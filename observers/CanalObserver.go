package observers

import (
	"log"
	esConfig "mulazim-api/configs/es"
	"mulazim-api/tools"
	"os"
	"time"

	"github.com/golang/protobuf/proto"
	"github.com/withlin/canal-go/client"
	pbe "github.com/withlin/canal-go/protocol/entry"
)

//BaseObserverInterface数组定义
var BaseObserverInterfaceArray []BaseObserverInterface

//
// startCanalOrderObserver
//  @Description: 启动canal观察者
//
func StartCanalObserver() {
	//TestObserver
	BaseObserverInterfaceArray = []BaseObserverInterface{
		OrderToElasticObserver{},
		OrderToChatCardObserver{},
		PayObserver{},
	}
	// tools.Logger.Info("StartCanalObserver init")
	connector := client.NewSimpleCanalConnector(esConfig.ElasticConfigOpt.CanalHost, 11111, "", "", "example", 60000, 60*60*1000)
	err := connector.Connect()
	if err != nil {
		tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
		os.Exit(1)
	}

	err = connector.Subscribe("mulazimpro.t_pay_lakala,mulazimpro.wechat,mulazimpro.t_order_today,mulazimpro.t_order,mulazimpro.t_order_extend")
	if err != nil {
		tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
		log.Println(err)
		os.Exit(1)
	}
	// tools.Logger.Info("StartCanalObserver start")
	for {
		message, err := connector.Get(100, nil, nil)
		if err != nil {
			tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
			os.Exit(1)
		}
		batchId := message.Id
		if batchId == -1 || len(message.Entries) <= 0 {
			// tools.Logger.Info("StartCanalObserver no entries sleep 3000")
			time.Sleep(3000 * time.Millisecond)
			continue
		}
		// tools.Logger.Info("StartCanalObserver processing %v",message)
		process(message.Entries)
	}
}

//
// processChanges
//  @Description: 处理相应的数据库变更
//  @param entrys
//
func process(entrys []pbe.Entry) {

	for _, entry := range entrys {
		if entry.GetEntryType() == pbe.EntryType_TRANSACTIONBEGIN || entry.GetEntryType() == pbe.EntryType_TRANSACTIONEND {
			continue
		}
		rowChange := new(pbe.RowChange)
		header := entry.GetHeader()
		//tools.Logger.Infof(fmt.Sprintf("================> binlog[%s : %d],name[%s,%s], eventType: %s", header.GetLogfileName(), header.GetLogfileOffset(), header.GetSchemaName(), header.GetTableName(), header.GetEventType()))

		err := proto.Unmarshal(entry.GetStoreValue(), rowChange)
		if err != nil {
			tools.Logger.Errorf("FATAL ERROR 数据解析错误: %s", err.Error())
			continue
		}
		if rowChange != nil {
			eventType := rowChange.GetEventType()

			for _, rowData := range rowChange.GetRowDatas() {
				for _, baseObserverInterface := range BaseObserverInterfaceArray {
					if tools.InArray(header.GetTableName(), baseObserverInterface.TableNames()) {
						baseObserverInterface.Process(eventType,header, rowData)
					}
				}
			}
		}
	}
}
