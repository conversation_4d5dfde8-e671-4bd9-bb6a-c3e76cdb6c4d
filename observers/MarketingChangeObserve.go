package observers

import (
	"encoding/json"
	"mulazim-api/models"
	"mulazim-api/tools"
	"reflect"
)

// MarketingChangeObserve
// @Description: 营销活动修改观察者
type MarketingChangeObserve struct {
	marketingOld *models.Marketing
	marketingNew *models.Marketing
}

// Observe
//
//	@Description: 观察者开始
//	@receiver o
//	@param marketingId 活动ID
func (o *MarketingChangeObserve) Observe(marketingId int) {
	db := tools.GetDB()
	db.Model(&o.marketingOld).Where("id = ?", marketingId).Unscoped().First(&o.marketingOld)

}

// SaveChanges
//
//	@Description: 保存修改的数据，保存观察到的信息
//	@receiver o
func (o *MarketingChangeObserve) SaveChanges(admin models.Admin) {

	if o.marketingOld.ID == 0 {
		tools.Logger.Error("FATAL ERROR: marketingOld is not exist")
		return
	}
	db := tools.GetDB()
	db.Model(&o.marketingNew).Where("id = ?", o.marketingOld.ID).Unscoped().First(&o.marketingNew)

	// 获取原始数据
	oldValues := reflect.ValueOf(*o.marketingOld)
	newValues := reflect.ValueOf(*o.marketingNew)

	// 比较字段差异并记录修改的字段
	changeValues := make([]models.MarketChangeLogChangeValues, 0)
	for i := 0; i < oldValues.NumField(); i++ {
		oldFieldValue := oldValues.Field(i).Interface()
		newFieldValue := newValues.Field(i).Interface()
		if !reflect.DeepEqual(oldFieldValue, newFieldValue) {
			fieldName := oldValues.Type().Field(i).Name
			if fieldName == "CreatedAt" || fieldName == "UpdatedAt" {
				continue
			}
			changeValues = append(changeValues, models.MarketChangeLogChangeValues{
				OldValue:  oldFieldValue,
				NewValue:  newFieldValue,
				FiledName: fieldName,
			})
		}
	}
	if len(changeValues) == 0 {
		return
	}
	strChangedValues, _ := json.Marshal(changeValues)
	strMarketingNew, _ := json.Marshal(o.marketingNew)
	strMarketingNew1 := string(strMarketingNew)
	strMarketingNew2, _ := tools.StringToMap(strMarketingNew1)
	delete(strMarketingNew2, "City")
	delete(strMarketingNew2, "Area")
	delete(strMarketingNew2, "MarketingOrderLog")
	delete(strMarketingNew2, "restaurant")
	delete(strMarketingNew2, "creator")
	// fmt.Println(strMarketingNew2)
	// delete(strMarketingNew, "city")
	// delete(strMarketingNew, "area")
	descUg, descZh := o.GetChangeDes(changeValues)
	marketingChangeLog := models.MarketChangeLog{
		GroupId:       o.marketingNew.GroupId,
		CityId:        o.marketingNew.CityID,
		AreaId:        o.marketingNew.AreaID,
		RestaurantId:  o.marketingNew.RestaurantID,
		MarketingID:   o.marketingNew.ID,
		CurrentData:   tools.MapToString(strMarketingNew2),
		ChangedFields: string(strChangedValues),
		AdminId:       admin.ID,
		Ip:            "",
		DescUg:        descUg,
		DescZh:        descZh,
	}
	db.Create(&marketingChangeLog)
}

func (o *MarketingChangeObserve) GetChangeDes(changeValues []models.MarketChangeLogChangeValues) (string, string) {
	// 修改状态:0：新建，1:启动，2：暂停，3：失效，4:删除
	if len(changeValues) == 1 {
		state := changeValues[0].NewValue
		if state == 1 {
			return "پائالىيەت قوزغۇتۇلدى", "活动已启动"
		}
		if state == 2 {
			return "پائالىيەت توختۇتۇلدى", "活动已暂停"
		}
		if state == 3 {
			return "پائالىيەت ئېنىۋەتسىز قىلىندى", "活已失效"
		}
		if state == 4 {
			return "پائالىيەت ئۆچۈرۈلدى", "活动已删除"
		}
	}
	return "پائالىيەت ئۆزگەرتىلدى", "活动内容已被修改"
}
