package observers

import (
	"mulazim-api/jobs"
	pbe "github.com/withlin/canal-go/protocol/entry"
	"mulazim-api/tools"
)

type PayObserver struct {
}

func (t PayObserver) TableNames() []string {
	return []string{
		"t_pay_lakala",
	}
}
func (t PayObserver) Process(eventType pbe.EventType, header *pbe.Header, rowData *pbe.RowData) {
	if eventType == pbe.EventType_DELETE {

	} else if eventType == pbe.EventType_INSERT {//插入操作时触发
		sendChangeMessage(header.GetTableName(), rowData.GetAfterColumns())
	}
}

func sendChangeMessage(tableName string, columns []*pbe.Column) {
	if tableName == "t_pay_lakala" {
		// isUpdated := false
		var id int
		var orderId int
		var outOrderNo string
		var payStatus int
		var orderStatus int
		
		for _, col := range columns {
			if col.GetName() == "id"  {
				// isUpdated = true
				id = tools.ToInt(col.GetValue())
			}
			if col.GetName() == "order_id"  {
				// isUpdated = true
				orderId = tools.ToInt(col.GetValue())
			}
			if col.GetName() == "out_order_no" {
				// isUpdated = true
				outOrderNo = tools.ToString(col.GetValue())
			}
			if col.GetName() == "pay_status" {
				// isUpdated = true
				payStatus = tools.ToInt(col.GetValue())
			}
			if col.GetName() == "order_status" {
				// isUpdated = true
				orderStatus = tools.ToInt(col.GetValue())
			}

		}
		
		if  payStatus ==0 || payStatus == 1001 || orderStatus == 0 || orderStatus == 1001 { //发送主动查询 任务 未支付,支付中
			jobs.SendLakalaQueryJob(id,orderId,outOrderNo,tableName)
		}
	}

}
