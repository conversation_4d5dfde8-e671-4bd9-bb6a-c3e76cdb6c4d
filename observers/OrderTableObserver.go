package observers

//  观察t_order_today,wechat 和t_pay_lakala 信息，如果有变动,则相应的处理,主要是添加到ElasticSearch ，方便后台查询

import (
	"context"
	"encoding/json"
	"fmt"
	esConfig "mulazim-api/configs/es"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
	pbe "github.com/withlin/canal-go/protocol/entry"
)


type OrderToElasticObserver struct {

}

func (t OrderToElasticObserver)TableNames() []string {
	return []string{
		"t_pay_lakala",
		"wechat",
		"t_order_today",
		"t_order"}
}
func (t OrderToElasticObserver)Process(eventType pbe.EventType,header *pbe.Header,rowData *pbe.RowData){
	orderId := ""
	columnKey := ""
	if header.GetTableName() == "t_order_today" || header.GetTableName() == "t_order" {
		columnKey = "id"
	}
	if header.GetTableName() == "t_pay_lakala" || header.GetTableName() == "wechat" {
		columnKey = "order_id"
	}

	if eventType == pbe.EventType_DELETE {
		if header.GetTableName() == "t_order_today" {
			for _, col := range rowData.GetBeforeColumns() {
				if col.GetName() == columnKey {
					orderId = col.GetValue()
					break
				}
			}
			deleteFromOrderToday(orderId)
		}
	} else if eventType == pbe.EventType_INSERT {
		for _, col := range rowData.GetAfterColumns() {
			if col.GetName() == columnKey {
				orderId = col.GetValue()
				break
			}
		}
		updateOrderInfo(orderId)
	} else {
		if header.GetTableName() == "t_pay_lakala" || header.GetTableName() == "wechat" {
			for _, col := range rowData.GetAfterColumns() {
				if col.GetName() == columnKey {
					orderId = col.GetValue()
					break
				}
			}
			updateOrderInfo(orderId)
		}
	}
}

//
// StartOrderObserver
//  @Description: 启动订单观察者
//  @param action string update_import_all_t_order 从t_order导入所有数据到Elastic，update_import_all_t_order_today 从t_order_today导入所有数据到Elastic，start_observe_order 启动订单观察者
//
func StartOrderObserver(action string) {
	// 创建连接
	initEs()
	if action == "update_import_all_t_order" {
		createOrderIndexIfNotExist("t_order")
		importOldOrderData("t_order")
		tools.Logger.Infof("t_order 到Elastic 数据导入结束")
		return
	}
	if action == "update_import_all_t_order_today" {
		createOrderIndexIfNotExist("t_order_today")
		importOldOrderData("t_order_today")
		tools.Logger.Infof("t_order_today 到Elastic 数据导入结束")
		return
	}
	if action == "start_observe_order" {
		tools.Logger.Infof("t_order_today 监听任务开始")
		StartCanalObserver()
		
		return
	}

}

// initEs
//
//	@Description: 初始化es
func initEs() *elasticsearch.Client {
	addresses := []string{esConfig.ElasticConfigOpt.Host}
	config := elasticsearch.Config{
		Addresses: addresses,
		Username:  "",
		Password:  "",
		CloudID:   "",
		APIKey:    "",
	}
	// new client
	es, err := elasticsearch.NewClient(config)
	if err != nil {
		tools.Logger.Error("FATAL ERROR: %s", err.Error())
	}
	Es = es
	return Es
}

var Es *elasticsearch.Client

// createOrderIndexIfNotExist
//
//	@Description: 创建订单索引
func createOrderIndexIfNotExist(indexName string) {

	// 创建或更新索引并应用映射
	_, err := Es.Indices.Delete([]string{indexName})
	// Index creates or updates a document in an index
	exists, err := Es.Indices.Exists([]string{indexName})
	if err != nil {
		tools.Logger.Error("FATAL ERROR: %s", err.Error())
	}
	if exists.StatusCode == 404 {

		// 定义映射
		mapping := `
    {
		 "settings":{
			"index.max_ngram_diff":2,
			"analysis":{
				"analyzer":{
					"my_analyzer":{
						"tokenizer":"my_tokenizer"
					}
				},
				"tokenizer":{
					"my_tokenizer":{
						"type":"ngram",
						"min_gram":4,
						"max_gram":6,
						"token_chars":[
							"letter",
							"digit"
						]
					}
				}
			}
		},
        "mappings": {
			"properties":{
				"building_id":{
					"type":"long"
				},
				"created_at":{
					"type":"date",
					"format": "yyyy-MM-dd HH:mm:ss||strict_date_optional_time ||epoch_millis"
				},
				
				"area_id":{
					"type":"long"
				},
				"user_mobile":{
					"type":"text",
					"analyzer":"my_analyzer",
					"fields":{
						"keyword":{
							"ignore_above":256,
							"type":"keyword"
						}
					}
				},
				"delivery_type":{
					"type":"long"
				},
				"id":{
					"type":"long"
				},
				"state":{
					"type":"long"
				},
				"terminal_id":{
					"type":"long"
				},
				"store_id":{
					"type":"long"
				},
				"shipment":{
					"type":"long"
				},
				"mobile":{
					"type":"text",
					"analyzer":"my_analyzer",
					"fields":{
						"keyword":{
							"ignore_above":256,
							"type":"keyword"
						}
					}
				},
				
				"lunch_box_fee":{
					"type":"long"
				},
				"name":{
					"type":"text",
					"fields":{
						"keyword":{
							"ignore_above":256,
							"type":"keyword"
						}
					}
				},
				"order_id":{
					"type":"text",
					"analyzer":"my_analyzer",
					"fields":{
						"keyword":{
							"ignore_above":256,
							"type":"keyword"
						}
					}
				},
				"city_id":{ 
					"type":"long"
				},
				"pay_channel_trade_no":{
					"type":"text",
					"analyzer":"my_analyzer",
					"fields":{
						"keyword":{
							"ignore_above":1024,
							"type":"keyword"
						}
					}
				},
				"wechat_pay_channel_trade_no":{
					"type":"text",
					"analyzer":"my_analyzer",
					"fields":{
						"keyword":{
							"ignore_above":1024,
							"type":"keyword"
						}
					}
				}

			}
		}
    }
    `

		req := esapi.IndicesCreateRequest{
			Index: indexName,
			Body:  strings.NewReader(mapping),
		}

		res, err := req.Do(context.Background(), Es)
		if err != nil {
			tools.Logger.Error("FATAL ERROR: %s", err.Error())
		}
		defer res.Body.Close()
		if res.StatusCode != 200 {
			tools.Logger.Error("FATAL ERROR: %s", "索引创建失败")
		} else {
			tools.Logger.Infof("索引不存在，创建成功")
		}
	} else {
		tools.Logger.Infof("索引已存在")
	}

}
//
// deleteFromOrderToday
//  @Description: 从t_order_today删除相应的数据，数据归档的时候删除相应的数据，不然荣誉数据变多
//  @param columns
//
func deleteFromOrderToday(orderId string) {
	if orderId != "" {
		req := esapi.DeleteRequest{
			Index:      "t_order_today",
			DocumentID: orderId,
		}
		res, err := req.Do(context.Background(), Es)
		if err != nil {
			tools.Logger.Errorf("Error getting response: %s", err)
			return
		}
		defer res.Body.Close()

		if res.IsError() {
			tools.Logger.Errorf("删除文档时发生错误: %s %s", res.Status(), orderId)
		}
	} else {
		tools.Logger.Errorf("FATAL ERROR: %s", "order_id为空")
	}
}
//
// updateOrderInfo
//  @Description: 更新订单信息，如果是t_pay_lakala或者wechat表的数据变动，更新订单信息
//  @param columns
//
func updateOrderInfo(orderId string) {
	if orderId != "" {
		var order OrderElasticModel
		db := tools.Db
		indexName := "t_order_today"
		db.Table(indexName+" o").Where("o.id = ? ", orderId).
			Select("o.id,o.building_id,o.created_at,o.area_id,u.mobile as user_mobile,o.delivery_type,o.state,o.terminal_id,o.store_id,o.shipment,o.mobile,o.lunch_box_fee,o.name,o.order_id,o.city_id," +
				"(select GROUP_CONCAT(lakala.pay_channel_trade_no ORDER BY lakala.id SEPARATOR ', ') from t_pay_lakala lakala where order_id = o.id and object_type = 'order') as pay_channel_trade_no," +
				"(select GROUP_CONCAT(we.transaction_id ORDER BY we.id SEPARATOR ', ') from wechat we where we.order_id = o.id) as wechat_pay_channel_trade_no").
			Joins("LEFT JOIN t_user u ON u.id = o.user_id").
			Scan(&order)
		if order.ID == 0 {
			indexName = "t_order"
			db.Table(indexName+" o").Where("o.id = ? ", orderId).
				Select("o.id,o.building_id,o.created_at,o.area_id,u.mobile as user_mobile,o.delivery_type,o.state,o.terminal_id,o.store_id,o.shipment,o.mobile,o.lunch_box_fee,o.name,o.order_id,o.city_id," +
					"(select GROUP_CONCAT(lakala.pay_channel_trade_no ORDER BY lakala.id SEPARATOR ', ') from t_pay_lakala lakala where order_id = o.id and object_type = 'order') as pay_channel_trade_no," +
					"(select GROUP_CONCAT(we.transaction_id ORDER BY we.id SEPARATOR ', ') from wechat we where we.order_id = o.id) as wechat_pay_channel_trade_no").
				Joins("LEFT JOIN t_user u ON u.id = o.user_id").
				Scan(&order)
		}
		if order.ID == 0 {
			// tools.Logger.Errorf("FATAL ERROR: %s %s", "原始数据不存在", orderId)
			return
		}
		// 准备批量索引的文档数据
		var buf strings.Builder

		indexLine := fmt.Sprintf(`{"index":{"_index":"%s","_type":"_doc","_id":%d}}`, indexName, order.ID)
		docBody, err := json.Marshal(order)
		if err != nil {
			tools.Logger.Infof("Fatal encoding document body: %s", err)
		}
		// 在文档数据之间添加换行符
		fmt.Fprintf(&buf, "%s\n", indexLine)
		fmt.Fprintf(&buf, "%s\n", docBody)

		// 创建批量请求
		req := esapi.BulkRequest{
			Body:         strings.NewReader(buf.String()),
			DocumentType: "_doc",
		}
		// 执行批量请求
		res, err := req.Do(context.Background(), Es)
		if err != nil {
			tools.Logger.Errorf("Error executing bulk request: %s", err)
		}
		defer res.Body.Close()

	} else {
		tools.Logger.Errorf("FATAL ERROR: %s %s", "order_id为空",orderId)
	}
}

//
//  OrderElasticModel
//  @Description: 订单Elastic模型
//
type OrderElasticModel struct {
	ID                      uint      `gorm:"column:id;primary_key" json:"id"`
	BuildingID              int64     `gorm:"column:building_id" json:"building_id"`
	CreatedAt               time.Time `gorm:"column:created_at" json:"created_at"`
	AreaID                  int64     `gorm:"column:area_id" json:"area_id"`
	UserMobile              string    `gorm:"column:user_mobile" json:"user_mobile"`
	DeliveryType            int64     `gorm:"column:delivery_type" json:"delivery_type"`
	State                   int64     `gorm:"column:state" json:"state"`
	TerminalID              int64     `gorm:"column:terminal_id" json:"terminal_id"`
	StoreID                 int64     `gorm:"column:store_id" json:"store_id"`
	Shipment                int64     `gorm:"column:shipment" json:"shipment"`
	Mobile                  string    `gorm:"column:mobile" json:"mobile"`
	LunchBoxFee             int64     `gorm:"column:lunch_box_fee" json:"lunch_box_fee"`
	Name                    string    `gorm:"column:name" json:"name"`
	OrderID                 string    `gorm:"column:order_id" json:"order_id"`
	CityID                  int64     `gorm:"column:city_id" json:"city_id"`
	PayChannelTradeNo       string    `gorm:"column:pay_channel_trade_no" json:"pay_channel_trade_no"`
	WechatPayChannelTradeNo string    `gorm:"column:wechat_pay_channel_trade_no" json:"wechat_pay_channel_trade_no"`
}
//
// importOldOrderData
//  @Description: 批量插入之前的所有数据，使用于t_order和t_order_today
//  @param indexName 使用于t_order和t_order_today
//
func importOldOrderData(indexName string) {
	//获取order的数组长度
	//select max(id) from t_order
	db := tools.Db
	var maxId *int
	err := db.Raw("select max(id) as max_id from " + indexName).Scan(&maxId).Error
	if err != nil {
		tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
	}
	if maxId == nil {
		tools.Logger.Errorf("FATAL ERROR: 无数据 %s", indexName)
		return
	}
	tools.Logger.Infof("数据导入开始，总共数据: %d", maxId)

	// 创建或更新索引并应用映射
	size := 10000
	for i := 7500000; i <= *maxId+size; i = i + size {
		//curStart := i
		var orders []OrderElasticModel
		db := tools.Db
		db.Table(indexName+" o").Where("o.id >= ? and o.id < ?", i, i+size).
			Select("o.id,o.building_id,o.created_at,o.area_id,u.mobile as user_mobile,o.delivery_type,o.state,o.terminal_id,o.store_id,o.shipment,o.mobile,o.lunch_box_fee,o.name,o.order_id,o.city_id," +
				"(select GROUP_CONCAT(lakala.pay_channel_trade_no ORDER BY lakala.id SEPARATOR ', ') from t_pay_lakala lakala where order_id = o.id) as pay_channel_trade_no," +
				"(select GROUP_CONCAT(we.transaction_id ORDER BY we.id SEPARATOR ', ') from wechat we where we.order_id = o.id) as wechat_pay_channel_trade_no").
			Joins("LEFT JOIN t_user u ON u.id = o.user_id").
			Scan(&orders)
		if len(orders) == 0 {
			continue
		}
		tools.Logger.Infof("从%d到%d插入%d", i, i+size, len(orders))
		// 准备批量索引的文档数据
		var buf strings.Builder
		for _, order := range orders {
			indexLine := fmt.Sprintf(`{"index":{"_index":"%s","_type":"_doc","_id":%d}}`, indexName, order.ID)
			docBody, err := json.Marshal(order)
			if err != nil {
				tools.Logger.Infof("Fatal encoding document body: %s", err)
			}
			// 在文档数据之间添加换行符
			fmt.Fprintf(&buf, "%s\n", indexLine)
			fmt.Fprintf(&buf, "%s\n", docBody)
		}
		// 创建批量请求
		req := esapi.BulkRequest{
			Body:         strings.NewReader(buf.String()),
			DocumentType: "_doc",
		}
		// 执行批量请求
		res, err := req.Do(context.Background(), Es)
		if err != nil {
			tools.Logger.Errorf("Error executing bulk request: %s", err)
		}
		res.Body.Close()
	}
	tools.Logger.Infof("数据导入结束，总共数据: %d", *maxId)
}
