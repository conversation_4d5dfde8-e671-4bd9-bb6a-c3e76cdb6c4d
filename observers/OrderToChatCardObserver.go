package observers

import (
	"encoding/json"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/configs/mq"
	"mulazim-api/jobs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/models/chat"
	"mulazim-api/tools"
	"time"

	"context"

	rmq_client "github.com/apache/rocketmq-clients/golang"
	"github.com/golang-module/carbon/v2"
	pbe "github.com/withlin/canal-go/protocol/entry"
)

type OrderToChatCardObserver struct {
}

func (t OrderToChatCardObserver) TableNames() []string {
	return []string{
		"t_order_today",
		"t_order_extend",
	}
}
func (t OrderToChatCardObserver) Process(eventType pbe.EventType, header *pbe.Header, rowData *pbe.RowData) {
	if eventType == pbe.EventType_DELETE {

	} else if eventType == pbe.EventType_INSERT {
		sendCardMessage(header.GetTableName(), rowData.GetAfterColumns())
	} else {
		sendCardMessage(header.GetTableName(), rowData.GetAfterColumns())
	}
}

func sendCardMessage(tableName string, columns []*pbe.Column) {
	if tableName == "t_order_today" {
		isUpdated := false
		isNewJoinRoom := false //是否需要创建room
		isNewShipper := false
		// isAdminAssingedShipper := false
		var state int64
		var cityId int64
		var areaId int64
		var id int64
		var bookingTime string
		for _, col := range columns {
			if col.GetName() == "state" && col.GetUpdated() {
				isUpdated = true
				state = tools.ToInt64(col.GetValue())
			}
			if col.GetName() == "shipper_id" && col.GetUpdated() && tools.ToInt64(col.GetValue()) > 0 {
				isNewShipper = true
			}
			if col.GetName() == "state" && col.GetUpdated() && tools.ToInt64(col.GetValue()) == 3 {
				isNewJoinRoom = true
			}
			if col.GetName() == "city_id" {
				cityId = tools.ToInt64(col.GetValue())
			}

			if col.GetName() == "area_id" {
				areaId = tools.ToInt64(col.GetValue())
			}
			if col.GetName() == "id" {
				id = tools.ToInt64(col.GetValue())
			}
			if col.GetName() == "booking_time" {
				bookingTime = col.GetValue()
			}
			// if col.GetName() == "json_info" && col.GetUpdated() {
			// 	isAdminAssingedShipper = true
			// }
		}
		if isUpdated && tools.InArray(state, []int64{3, 4, 5, 6, 7, 8, 9, 10}) {
			db := tools.GetDB()
			//OrderChatDetailCard 初始化 并序列化
			foodLists := []chat.OrderChatDetailCardFood{}
			var order models.OrderToday
			foodsReadyTime := 0 // 获取订单备餐时间
			db.Model(&models.OrderToday{}).Where("id = ?", id).Preload("Restaurant").Preload("OrderDetail.RestaurantFoods").Preload("Area").Find(&order)
			if order.ID == 0 { //订单不存在不用发送卡片
				return
			}
			for _, orderDetail := range order.OrderDetail {
				restaurantFoods := orderDetail.RestaurantFoods
				// 找出准备时间最大的时间作为该订单备餐时间
				if restaurantFoods.ReadyTime > foodsReadyTime {
					foodsReadyTime = restaurantFoods.ReadyTime
				}
				food := chat.OrderChatDetailCardFood{
					FoodNameUG: restaurantFoods.NameUg,
					FoodNameZH: restaurantFoods.NameZh,
					FoodPrice:  fmt.Sprintf("%.2f", float64(orderDetail.Price)/100),
					Number:     float64(orderDetail.Number),
				}
				foodLists = append(foodLists, food)
			}
			if state == 3 { //订单等级计算队列启动
				PushOrderToAutoDispatchForCalc(order)
			}
			//手动同步 auto_dispatch_history 中的 订单价格和状态 
			er0 :=db.Model(&models.AutoDispatchHistory{}).Where("order_id = ?",id).Updates(map[string]interface{}{
				"order_state": state,
				"order_price":  order.OrderPrice,
				// "updated_at":  carbon.Now(),
			}).Error
			if er0 != nil {
				tools.Logger.Errorf("ERROR，更新auto_dispatch_history时出现错误:%s", er0.Error())
			}
			// 如果状态4（餐厅接受订单），更新t_order_extend表中的餐厅备餐时间(foods_ready_time)
			if state == 4 {
				err := db.Model(&models.OrderExtend{}).Where("order_id =?", id).UpdateColumns(map[string]interface{}{
					"foods_ready_time": carbon.Now().AddMinutes(foodsReadyTime),
					"updated_at":       carbon.Now(),
				}).Error
				if err != nil {
					tools.Logger.Errorf("FATAL ERROR，更新餐厅出餐时间出餐: %s", err.Error())
				}
				if order.Area.AutoDispatchState == 1 && order.ShipperID == 0 {
					// 推送智能派单系统，并添加压单列表(redis)
					PushOrderToAutoDispatch(order)
				}
			}
			if state == 5 && order.ShipperID > 0 {
				tools.Logger.Infof("order_push_shipper_订单已备餐推送，订单: %d", order.ID)
				jobs.SendFoodReadyPush(order)
			}
			card := chat.OrderChatDetailCard{
				OrderState:      (state),
				OrderStateMsgUG: lang.Ug["order_state"].((map[int]string))[int(state)],
				OrderStateMsgZH: lang.Zh["order_state"].((map[int]string))[int(state)],
				FoodsList:       foodLists,
				Remark:          order.Description,
				DeliveryTime:    carbon.Parse(bookingTime).Format("Y-m-d H:i:s"),
				CreatedTime:     order.CreatedAt.Format("2006-01-02 15:04:05"),
			}
			serializedCard, err := json.Marshal(card)
			if err != nil {
				fmt.Println("Error encoding JSON:", err)
				return
			}
			chatMsg := chat.OrderChatDetail{
				CityID:            cityId,
				AreaID:            areaId,
				OrderID:           id,
				SenderType:        5,
				SenderID:          0,
				CardType:          tools.ToInt(state),
				ContentType:       3,
				CardContent:       string(serializedCard),
				Image:             "",
				Content:           "",
				CreatedAt:         time.Now(),
				UpdatedAt:         time.Now(),
				OrderSerialNumber: carbon.Now(configs.AsiaShanghai).Format("m-d") + " #" + tools.ToString(order.SerialNumber),
				OrderNo:           order.OrderID,
				ResNameUg:         order.Restaurant.NameUg,
				ResNameZh:         order.Restaurant.NameZh,
				CustomerPhone:     order.Mobile,
			}
			job := jobs.NewSendOrderChatDetailJob()
			dataStr, err := json.Marshal(chatMsg)
			if err != nil {
				tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
				return
			}
			dataMap := make(map[string]interface{})
			err = json.Unmarshal(dataStr, &dataMap)
			if err != nil {
				tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
				return
			}
			job.ProduceMessageToConsumer(dataMap)
		}
		if isUpdated && state >= 7 {
			db := tools.GetDB()
			db.Model(&models.MarketingOrderLog{}).Where("order_id = ?", id).Update("order_state", state)
		}
		if isNewJoinRoom {
			db := tools.Db
			var order models.OrderToday
			db.Model(&models.OrderToday{}).Preload("Restaurant").Where("id = ?", id).Preload("OrderDetail.RestaurantFoods").Find(&order)
			if order.ID == 0 {
				return
			}
			//创建room 添加用户
			join := chat.OrderChatJoin{
				CityID:            cityId,
				AreaID:            areaId,
				UserType:          1,
				UserID:            int64(order.UserID),
				OrderID:           int64(order.ID),
				ResID:             int64(order.StoreID),
				LastContent:       "",
				CreatedAt:         time.Now(),
				UpdatedAt:         time.Now(),
				OrderSerialNumber: carbon.Now(configs.AsiaShanghai).Format("m-d") + " #" + tools.ToString(order.SerialNumber),
				OrderNo:           order.OrderID,
				ResNameUg:         order.Restaurant.NameUg,
				ResNameZh:         order.Restaurant.NameZh,
				CustomerPhone:     order.Mobile,
			}
			if err := db.Create(&join).Error; err != nil {
				tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
				return
			}
			//创建room 添加商户
			join = chat.OrderChatJoin{
				CityID:            cityId,
				AreaID:            areaId,
				UserType:          2,
				UserID:            int64(order.StoreID),
				OrderID:           int64(order.ID),
				ResID:             int64(order.StoreID),
				LastContent:       "",
				CreatedAt:         time.Now(),
				UpdatedAt:         time.Now(),
				OrderSerialNumber: carbon.Now(configs.AsiaShanghai).Format("m-d") + " #" + tools.ToString(order.SerialNumber),
				OrderNo:           order.OrderID,
				ResNameUg:         order.Restaurant.NameUg,
				ResNameZh:         order.Restaurant.NameZh,
				CustomerPhone:     order.Mobile,
			}
			if err := db.Create(&join).Error; err != nil {
				tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
				return
			}
			//创建room 添加商户
			join = chat.OrderChatJoin{
				CityID:            cityId,
				AreaID:            areaId,
				UserType:          4,
				UserID:            0,
				ResID:             int64(order.StoreID),
				OrderID:           int64(order.ID),
				LastContent:       "",
				CreatedAt:         time.Now(),
				UpdatedAt:         time.Now(),
				OrderSerialNumber: carbon.Now(configs.AsiaShanghai).Format("m-d") + " #" + tools.ToString(order.SerialNumber),
				OrderNo:           order.OrderID,
				ResNameUg:         order.Restaurant.NameUg,
				ResNameZh:         order.Restaurant.NameZh,
				CustomerPhone:     order.Mobile,
			}
			if err := db.Create(&join).Error; err != nil {
				tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
				return
			}
		}
		if isNewShipper {
			db := tools.Db
			var order models.OrderToday
			db.Model(&models.OrderToday{}).Preload("Restaurant").Where("id = ?", id).Preload("OrderDetail.RestaurantFoods").Find(&order)
			db.Model(&models.MarketingOrderLog{}).Where("order_id = ?", id).Update("shipper_id", order.ShipperID)
			if order.ID == 0 {
				return
			}
			//创建room 添加用户
			joinCt :=int64(0)
			var oldShippers []int
			db.Model(&chat.OrderChatJoin{}).Where("user_type = ? and order_id = ?",3,order.ID).Pluck("user_id",&oldShippers)
			if len(oldShippers) > 0 { //已经有配送员加入该订单，切换配送员后要删除 原来的配送员，否则 聊天推送会发送到原来的配送员
				for _,shipperId := range oldShippers {
					db.Model(&chat.OrderChatJoin{}).Where("user_type = ? and user_id = ? and order_id = ?",3,shipperId,order.ID).Delete(&chat.OrderChatJoin{})
				}
			}
			db.Model(&chat.OrderChatJoin{}).Where("user_type = ? and user_id = ? and order_id = ?",3,order.ShipperID,order.ID).Count(&joinCt)
			if joinCt == 0 { //防止多次进入导致推送两次
				join := chat.OrderChatJoin{
					CityID:            cityId,
					AreaID:            areaId,
					UserType:          3,
					UserID:            int64(order.ShipperID),
					OrderID:           int64(order.ID),
					ResID:             int64(order.StoreID),
					LastContent:       "",
					CreatedAt:         time.Now(),
					UpdatedAt:         time.Now(),
					OrderSerialNumber: carbon.Now(configs.AsiaShanghai).Format("m-d") + " #" + tools.ToString(order.SerialNumber),
					OrderNo:           order.OrderID,
					ResNameUg:         order.Restaurant.NameUg,
					ResNameZh:         order.Restaurant.NameZh,
					CustomerPhone:     order.Mobile,
				}
				if err := db.Create(&join).Error; err != nil {
					tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
					return
				}
			}
			// if isAdminAssingedShipper {
			// 	tools.Logger.Infof("order_push_shipper_订单分配推送，订单: %d", order.ID)
			// 	jobs.SendOrderAssignedPush(order)
			// }
		}
	} else if tableName == "t_order_extend" {
		isUpdated := false
		var shipperOrderState int64
		var orderID int64
		for _, col := range columns {
			if col.GetName() == "shipper_order_state" && col.GetUpdated() {
				//需要发送卡片消息
				if col.GetName() == "shipper_order_state" && col.GetUpdated() {
					isUpdated = true
					shipperOrderState = tools.ToInt64(col.GetValue())
				}
			}
			if col.GetName() == "order_id" {
				orderID = tools.ToInt64(col.GetValue())
			}
		}
		if isUpdated && shipperOrderState > 0 {
			db := tools.GetDB()
			//OrderChatDetailCard 初始化 并序列化
			foodLists := []chat.OrderChatDetailCardFood{}
			var order models.OrderToday
			db.Model(&models.OrderToday{}).Preload("Restaurant").Where("id = ?", orderID).Preload("OrderDetail.RestaurantFoods").Find(&order)
			if order.ID == 0 { //订单不存在的话不用发送
				return
			}
			for _, orderDetail := range order.OrderDetail {
				food := chat.OrderChatDetailCardFood{
					FoodNameUG: orderDetail.RestaurantFoods.NameUg,
					FoodNameZH: orderDetail.RestaurantFoods.NameZh,
					FoodPrice:  fmt.Sprintf("%.2f", float64(orderDetail.Price)/100),
					Number:     float64(orderDetail.Number),
				}
				foodLists = append(foodLists, food)
			}
			card := chat.OrderChatDetailCard{
				OrderState:      shipperOrderState,
				OrderStateMsgUG: lang.Ug["shipper_order_state"].((map[int]string))[int(shipperOrderState)],
				OrderStateMsgZH: lang.Zh["shipper_order_state"].((map[int]string))[int(shipperOrderState)],
				FoodsList:       foodLists,
				Remark:          order.Description,
				DeliveryTime:    carbon.Parse(order.BookingTime).Format("Y-m-d H:i:s"),
				CreatedTime:     order.CreatedAt.Format("2006-01-02 15:04:05"),
			}
			serializedCard, err := json.Marshal(card)
			if err != nil {
				fmt.Println("Error encoding JSON:", err)
				return
			}

			chatMsg := chat.OrderChatDetail{
				CityID:            int64(order.CityID),
				AreaID:            int64(order.AreaID),
				OrderID:           orderID,
				SenderType:        5,
				SenderID:          0,
				CardType:          tools.ToInt(shipperOrderState),
				ContentType:       3,
				CardContent:       string(serializedCard),
				Image:             "",
				Content:           "",
				CreatedAt:         time.Now(),
				UpdatedAt:         time.Now(),
				OrderSerialNumber: carbon.Now(configs.AsiaShanghai).Format("m-d") + " #" + tools.ToString(order.SerialNumber),
				OrderNo:           order.OrderID,
				ResNameUg:         order.Restaurant.NameUg,
				ResNameZh:         order.Restaurant.NameZh,
				CustomerPhone:     order.Mobile,
			}

			job := jobs.NewSendOrderChatDetailJob()
			dataStr, err := json.Marshal(chatMsg)
			if err != nil {
				tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
				return
			}
			dataMap := make(map[string]interface{})
			err = json.Unmarshal(dataStr, &dataMap)
			if err != nil {
				tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
				return
			}
			job.ProduceMessageToConsumer(dataMap)
		}
	}

}


// PushOrderToAutoDispatch
//
// @Description: 订单推送智能派单系统
// @Author: Rixat
// @Time: 2024-08-09 17:43:17
// @receiver 
// @param c *gin.Context
func PushOrderToAutoDispatch(order models.OrderToday){
	// 添加智能派单系统队列
	tools.Logger.Infof("开始推送智能派单系统: area_id=%d,order_id=%d",order.AreaID,order.ID)
	AutoDispatchJobPushOrderData(order)
	tools.Logger.Infof("推送智能派单系统结束: area_id=%d,order_id=%d",order.AreaID,order.ID)
}

func PushOrderToAutoDispatchForCalc(order models.OrderToday){
	// 添加智能派单系统队列
	tools.Logger.Infof("开始推送智能派单系统-计算等级: area_id=%d,order_id=%d",order.AreaID,order.ID)
	AutoDispatchJobCalcOrderData(order)
	tools.Logger.Infof("推送智能派单系统结束-计算等级: area_id=%d,order_id=%d",order.AreaID,order.ID)
}


// AutoDispatchJobPushOrderData
//
// @Description: 添加智能派单系统队列
// @Author: Rixat
// @Time: 2024-08-30 10:41:51
// @receiver 
// @param c *gin.Context
func AutoDispatchJobPushOrderData(order models.OrderToday){
	// 设置HTTP协议客户端接入点，进入消息队列RocketMQ版控制台实例详情页面的接入点区域查看。
	endpoint := mq.MqConfigOpt.Endpoint
	// 请确保环境变量ALIBABA_CLOUD_ACCESS_KEY_ID、ALIBABA_CLOUD_ACCESS_KEY_SECRET已设置。
	// AccessKey ID，阿里云身份验证标识。
	accessKey := mq.MqConfigOpt.AccessKey
	// AccessKey Secret，阿里云身份验证密钥。
	secretKey := mq.MqConfigOpt.SecretKey

	topicName := "auto_dispatch_order_area_group_"+tools.ToString(order.Area.AutoDispatchMqGroup)

	topic := "pro_" + topicName
	if configs.CurrentEnvironment != "production" {
		topic = "dev_" + topicName
	}
	// b.currentReconnectCount++
	data := map[string]interface{}{
		"area_id":order.AreaID,
		"order_id":order.ID,
	}
	tag := "area_"+tools.ToString(order.AreaID)
	key := tools.ToString(order.SerialNumber)+"#"+order.OrderID
	byteJson, _ := json.Marshal(data)
	msg := &rmq_client.Message{
		Topic: topic,
		Body:  byteJson,
		Tag:   &tag,
	}
	msg.SetKeys(key)
	// msg.SetMessageGroup(tag)
	producer := jobs.GetProducer(endpoint, accessKey, secretKey, topic)
	_, err := producer.Send(context.TODO(), msg)
	if err != nil {
		tools.Logger.Errorf("rocketmq-publish-fail ---->\n\tError:%s  topic:%s  data:%s\n", err, topic, data)
	}
}

//智能派单计算 订单等级
func AutoDispatchJobCalcOrderData(order models.OrderToday){
	// 设置HTTP协议客户端接入点，进入消息队列RocketMQ版控制台实例详情页面的接入点区域查看。
	endpoint := mq.MqConfigOpt.Endpoint
	// 请确保环境变量ALIBABA_CLOUD_ACCESS_KEY_ID、ALIBABA_CLOUD_ACCESS_KEY_SECRET已设置。
	// AccessKey ID，阿里云身份验证标识。
	accessKey := mq.MqConfigOpt.AccessKey
	// AccessKey Secret，阿里云身份验证密钥。
	secretKey := mq.MqConfigOpt.SecretKey

	topicName := "auto_dispatch_calc_order_area_group"

	topic := "pro_" + topicName
	if configs.CurrentEnvironment != "production" {
		topic = "dev_" + topicName
	}
	// b.currentReconnectCount++
	data := map[string]interface{}{
		"area_id":order.AreaID,
		"order_id":order.ID,
	}
	tag := "area_"+tools.ToString(order.AreaID)
	key := tools.ToString(order.SerialNumber)+"#"+order.OrderID
	byteJson, _ := json.Marshal(data)
	msg := &rmq_client.Message{
		Topic: topic,
		Body:  byteJson,
		Tag:   &tag,
	}
	msg.SetKeys(key)
	// msg.SetMessageGroup(tag)
	producer := jobs.GetProducer(endpoint, accessKey, secretKey, topic)
	_, err := producer.Send(context.TODO(), msg)
	if err != nil {
		tools.Logger.Errorf("rocketmq-publish-fail ---->\n\tError:%s  topic:%s  data:%s\n", err, topic, data)
	}
}