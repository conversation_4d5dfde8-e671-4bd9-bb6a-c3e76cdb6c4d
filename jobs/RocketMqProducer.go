package jobs

import (
	"github.com/apache/rocketmq-clients/golang"
	rmq_client "github.com/apache/rocketmq-clients/golang"
	"github.com/apache/rocketmq-clients/golang/credentials"
	"mulazim-api/tools"
)


var RocketMqProducerMap map[string]golang.Producer

func init() {
	RocketMqProducerMap = make(map[string]golang.Producer)
}
func DeleteProducer(topic string)  {
	if _, ok := RocketMqProducerMap[topic]; ok {
		// 存在
		producer := RocketMqProducerMap[topic]
		producer.GracefulStop()
		delete(RocketMqProducerMap,topic)
	}
}
func GetProducer(endpoint string,accessKey string,secretKey string,topic string) golang.Producer {
	if _, ok := RocketMqProducerMap[topic]; ok {
		// 存在
		return RocketMqProducerMap[topic]
	}
	tools.Logger.Info("RocketMq重新初始化 topic:"+topic)
	producer, err := rmq_client.NewProducer(&rmq_client.Config{
		Endpoint: endpoint,
		Credentials: &credentials.SessionCredentials{
			AccessKey:    accessKey,
			AccessSecret: secretKey,
		},
	},
		rmq_client.WithTopics(topic),
	)
	if err != nil {
		tools.Logger.Error("connect rocketmq fail ---->\n\tError:%s\n", err)
	}

	err = producer.Start()
	if err != nil {
		tools.Logger.Error("rocketmq producer start fail ---->\n\tError:%s\n", err)
	}
	RocketMqProducerMap[topic] = producer
	return producer
}