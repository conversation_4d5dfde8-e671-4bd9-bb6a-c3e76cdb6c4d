package jobs

import (
	"encoding/json"
	"mulazim-api/models"
	"mulazim-api/services/merchant/lakala"
	"mulazim-api/tools"

	rmq_client "github.com/apache/rocketmq-clients/golang"
	"gorm.io/gorm"
)

// 拉卡拉退款Job
type RefundJob struct {
	BaseJob
}

func (b *RefundJob) MaxReconnectCount() int32 {
	return 3
}

func (b *RefundJob) TopicName() string {
	return "lakala_refund_topic"
}

func (b *RefundJob) GroupId() string {
	return "LAKALA_REFUND_GROUP"
}

func (b *RefundJob) MaxRetryCount() int32 {
	return 16
}

type OrderData struct {
	OrderId     int64 `form:"order_id" json:"order_id"`
	LakalaPayId int64 `form:"lakala_pay_id" json:"lakala_pay_id"`
	PartRefund     int64 `form:"part_refund" json:"part_refund"`
}

// 消费消息
//
// 返回： 结果、 延迟单位：如果失败会进入延迟执行 单位： 秒
func (b *RefundJob) HandleMessage(v *rmq_client.MessageView) (bool, int64) {

	orderData := OrderData{}
	lakalaMap := make(map[string]interface{})
	var dbResult *gorm.DB

	err := json.Unmarshal([]byte(v.GetBody()), &orderData)
	if err != nil {
		tools.Logger.Error("lakala-refund-job-json-unmarshal-error", err)
		return true, 0
	}
	var orderToday models.OrderToday
	// 重复支付订单， 根据t_pay_lakala表id 查找进行退款
	if orderData.LakalaPayId != 0 {
		tools.Logger.Info("lakala-refund-job: t_pay_lakala ID: ", orderData.LakalaPayId)
		dbResult = tools.Db.Table("t_pay_lakala").
			Where("id = ?", orderData.LakalaPayId).
			Scan(&lakalaMap)
		if lakalaMap["order_id"] != nil {
			dbResult = tools.Db.Table("t_order_today").Where("id = ?", lakalaMap["order_id"]).Scan(&orderToday)
			if dbResult.RowsAffected < 1 {
				dbResult = tools.Db.Table("t_order").Where("id = ?", lakalaMap["order_id"]).Scan(&orderToday)
			}
		}	
	} else if orderData.OrderId != 0 {
		// 普通退单重复退款处理
		
		dbResult = tools.Db.Table("t_order_today").Where("id = ?", orderData.OrderId).Scan(&orderToday)
		if dbResult.RowsAffected < 1 {
			dbResult = tools.Db.Table("t_order").Where("id = ?", orderData.OrderId).Scan(&orderToday)
		}

		// 订单未找到
		if dbResult.RowsAffected < 1 {
			tools.Logger.Error("lakala-refund-job-fail : 订单未找到 orderId : ", orderData.OrderId)
			return true, 0
		}

		// 订单状态不是已退款状态
		if orderData.PartRefund ==0 && !(orderToday.State == models.ORDER_STATE_CANCELED || orderToday.State == models.ORDER_STATE_RESTAURANT_REJECTED) {
			tools.Logger.Error("lakala-refund-job-fail : 订单状态不是退款 orderId: ", orderData.OrderId)
			return true, 0
		}
		dbResult = tools.Db.Table("t_pay_lakala").
			Where("object_type = ? ", models.PAY_LAKALA_OBJECT_TYPE_ORDER).
			Where("pay_status", 1004). //支付完成的记录中退款 避免未支付的记录进行退款
			Where("order_id = ?", orderToday.ID).
			Scan(&lakalaMap)
	} else { // 接受数据为空时退出处理并写日志
		tools.Logger.Error("lakala-refund-job-fail : 接受数据为空", v.GetMessageId(), v.GetBody())
		return true, 0
	}

	if dbResult.RowsAffected < 1 {
		tools.Logger.Error("lakala-refund-job-fail : 拉卡拉成功支付信息未找到 orderId: ", orderData.OrderId)
		return true, 0
	}
	var lakalaRefundCount int64
	tools.Db.Table("t_pay_lakala_refund").
		Where("pay_lakala_id = ?", lakalaMap["id"]).
		Where("order_status = ?", 2003).
		Count(&lakalaRefundCount)
	// 如表t_pay_lakala_refund 已有退款记录无需重复尝试退款
	var partRefunds models.OrderPartRefund
	//查询部分退款记录
	tools.Db.Model(&models.OrderPartRefund{}).Where("order_id = ? and part_refund_type = ?",orderData.OrderId,2).Find(&partRefunds) 	
	partRefundRequired :=false
	if partRefunds.ID > 0 { //部分退款 有部分退款记录
		refunded := orderToday.Refunded	
		if refunded > 0 {
			refundAmount := tools.ToInt64(lakalaMap["pay_amount"])-partRefunds.PartRefundAmount
			tools.Logger.Info("lakala-refund-job-part-refund : 拉卡拉部分退款[全额退款]， ", lakalaMap["id"],",orderId:",orderData.OrderId,",partRefunds.PartRefundAmount:",partRefunds.PartRefundAmount,",refundAmount:",refundAmount)
			lakalaMap["pay_amount"] = refundAmount
		}else{
			tools.Logger.Info("lakala-refund-job-part-refund : 拉卡拉部分退款[部分退款]， ", lakalaMap["id"],",orderId:",orderData.OrderId,",partRefunds.PartRefundAmount:",partRefunds.PartRefundAmount)
			lakalaMap["pay_amount"] = partRefunds.PartRefundAmount
		}
		partRefundRequired = true
	}
	if lakalaRefundCount > 0 && !partRefundRequired {
		tools.Logger.Info("lakala-refund-job-refuned : 拉卡拉已退款， 无需重复 ", lakalaMap["id"])
		return true, 0
	}
	isPartRefund := false
	if orderData.PartRefund > 0{
		isPartRefund = true
	}
	if _, err := lakala.GetLakalaService().RefundLakala("39.105.138.155", lakalaMap,isPartRefund); err != nil {
		tools.Logger.Error("lakala-refund-job-fail ", err)
		// 默认重试延迟1 小时
		delayTime := int64(3600)
		switch v.GetDeliveryAttempt() {
		case 1:
			// 第一次失败 10分钟后重试
			delayTime = 600
		case 2:
			// 第二次失败 20分钟后重试
			delayTime = 1200
		case 3:
			// 第二次失败 8小时候重试
			delayTime = 7 * 3600
		}
		tools.Logger.Info("lakala-refund-job-fail : 延迟队列： 订单ID", lakalaMap["id"], " 尝试次数：",
			v.GetDeliveryAttempt(), " 延迟时间：", delayTime)
		return false, delayTime
	} else {
		return true, 1
	}
}

func NewLakalaRefundJob() *RefundJob {
	job := RefundJob{}
	job.job = &job
	return &job
}
