package jobs

import (
	"context"
	"encoding/json"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/constants"

	"mulazim-api/models"
	"mulazim-api/models/chat"
	"mulazim-api/tools"
	"regexp"
	"time"

	rmq_client "github.com/apache/rocketmq-clients/golang"

	shipperResource "mulazim-api/resources/shipper"
	"gorm.io/gorm"
	"mulazim-api/lang"
	"github.com/golang-module/carbon/v2"
)

type PushData struct {
	UserId      int                     `json:"user_id"`

	UserType    string                  `json:"user_type"`
	PushContent PushContent             `json:"push_content"`
	Client      models.PushDeviceClient `json:"client"`
	//Params      map[string]interface{}  `json:"params"`
	Sound string `json:"sound"`
	ChannelType      int                `json:"channel_type"`
	AdminId int `json:"admin_id"`
}

type PushContent struct {
	TitleZh   string
	TitleUg   string
	ContentZh string
	ContentUg string
	Params    map[string]interface{}
}

// PushJob
// implement JobInterface
type PushJob struct {
	BaseJob
}

func (j *PushJob) MaxReconnectCount() int32 {
	return 1
}

func (j *PushJob) MaxRetryCount() int32 {
	return 2
}

func (j *PushJob) TopicName() string {
	return "push_job"
}

func (j *PushJob) GroupId() string {
	return "PUSH_MESSAGE"
}

func NewPushJob() *PushJob {
	job := PushJob{}
	job.job = &job
	return &job
}

func (b *PushJob) HandleMessage(v *rmq_client.MessageView) (bool, int64) {
	var pushData PushData
	err := json.Unmarshal([]byte(v.GetBody()), &pushData)
	if err != nil {
		tools.Logger.Error("push job json unmarshal fail", err)
		return true, 0
	}
	var devices []models.PushDevice

	var config configs.JiguangConfig
	
	if pushData.UserId > 0 {
		query :=tools.GetDB().Model(models.PushDevice{}).
			Where("user_id = ? and user_type = ?", pushData.UserId, pushData.UserType)
		if pushData.AdminId > 0 {
			query = query.Where("admin_id = ?",pushData.AdminId)
		}	
		query.Find(&devices)
		
		var adm models.Admin
		tools.GetDB().Model(&models.Admin{}).
			// Preload("ShipperAttendance", func(d *gorm.DB) *gorm.DB {
			// 	return d.Where("state in (1,2,3) and created_at > '"+carbon.Now().Format("Y-m-d")+" 00:00:00'").Order("created_at desc")
			// }).
			Where("id = ?",pushData.UserId).Where("state = ?",1).Find(&adm)
		for _, device := range devices {
			switch device.Client {
			case models.PushDeviceClientShipper:
				config = configs.MyApp.PushConfig.Shipper
				if device.DeviceAppVersion <= configs.MyApp.ShipperPushMinVersion { //推送开始版本大于318
					tools.Logger.Info("order_push_激光推送 配送端  跳过低版本 v:"+tools.ToString(device.DeviceAppVersion)+",admin_id:"+tools.ToString(pushData.AdminId))
					continue
				}
				//跳过 未打卡的配送员 和 推送类型 不是 默认推送的 包括全局通知 
				if (adm.Type == constants.ADMIN_TYPE_SHIPPER || adm.Type == constants.ADMIN_TYPE_SHIPPER_ADMIN) && pushData.ChannelType != constants.ChannelTypeShipperDefault{
					// //跳过今天 没有打卡上班的
					// if len(adm.ShipperAttendance)== 0{
					// 	tools.Logger.Info("order_push_激光推送 配送端  跳过未打卡 v:"+tools.ToString(device.DeviceAppVersion)+",admin_id:"+tools.ToString(pushData.AdminId))
					// 	return true,0
					// }
					// attendanceState :=adm.ShipperAttendance[0].Type	
					// if attendanceState != 1 {
					// 	tools.Logger.Info("order_push_激光推送 配送端  跳过未打卡 v:"+tools.ToString(device.DeviceAppVersion)+",admin_id:"+tools.ToString(pushData.AdminId))
					// 	return true,0
					// }
					attendanceState :=adm.AttendanceState	
					if attendanceState != 1 {
						tools.Logger.Info("order_push_激光推送 配送端  跳过未打卡 v:"+tools.ToString(device.DeviceAppVersion)+",admin_id:"+tools.ToString(pushData.AdminId))
						return true,0
					}
					// continue
				} 
			case models.PushDeviceClientMerchant:
				config = configs.MyApp.PushConfig.Merchant
				if device.DeviceAppVersion <= configs.MyApp.MerchantPushMinVersion { //推送开始版本大于242
					tools.Logger.Info("order_push_激光推送 商家端  跳过低版本 v:"+tools.ToString(device.DeviceAppVersion)+",admin_id:"+tools.ToString(pushData.AdminId))
					continue
				}
			case models.PushDeviceClientUser:
				config = configs.MyApp.PushConfig.User
			default:
				tools.Logger.Error("push job fail", pushData)
				continue
			}
			jpushMode :=configs.IsProduction()
			if len(configs.MyApp.JPushMode) > 0 {
				if configs.MyApp.JPushMode =="production" {
					jpushMode =true
				}else{
					jpushMode =false
				}
			}
			jpushClient := tools.NewJiguangPush(config.AppKey, config.AppSecret, jpushMode)
			title := ""
			content := ""
			sound :=""

			switch device.Lang {
				case models.PushDeviceLangZh:
					title = pushData.PushContent.TitleZh
					content = pushData.PushContent.ContentZh
					sound =pushData.Sound+"_zh"
				case models.PushDeviceLangUg:
					title = pushData.PushContent.TitleUg
					content = pushData.PushContent.ContentUg
					sound =pushData.Sound+"_ug"	
				default:
					continue 	
			}

			rs, er1 := jpushClient.Push(device.Rid, title, content, pushData.PushContent.Params, sound,pushData.ChannelType,device.AdminID,string(device.Lang))
			if er1 != nil {
				tools.Logger.Debugf("order_push_激光推送 推送失败： 用户ID: %d, 用户类型： %s, 消息ID: %s, 推送结果：%v,admin_id:%v,err:%v",
				pushData.UserId, pushData.UserType, v.GetMessageId(), rs,device.AdminID,er1)

			}else{
				tools.Logger.Debugf("order_push_激光推送 推送成功： 用户ID: %d, 用户类型： %s, 消息ID: %s, 推送结果：%v,admin_id:%v",
				pushData.UserId, pushData.UserType, v.GetMessageId(), rs,device.AdminID)
			}
			
		}
		return true, 0
	}
	return true, 0
}

// PushData 发送推送消息

func (b *PushJob) PushData(pushData PushData) {
	b.ProduceMessageToConsumer(map[string]interface{}{
		"user_id":      pushData.UserId,
		"user_type":    pushData.UserType,
		"push_content": pushData.PushContent,
		"sound":        pushData.Sound,
		"channel_type" :pushData.ChannelType,
		"admin_id"	   :pushData.AdminId,    
	})
}

func (b *PushJob) SendChatPush(orderChatDetail *chat.OrderChatDetail) {
	var chatUsers []chat.OrderChatJoin
	var pushContent = PushContent{}
	tools.GetDB().Model(chat.OrderChatJoin{}).
		Where("order_id = ? ", orderChatDetail.OrderID).
		Find(&chatUsers)
	tools.Logger.Info("order_push_聊天室push",orderChatDetail.OrderID)
	switch orderChatDetail.ContentType {
	case chat.CHAT_CONTENT_TYPE_TEXT:
		pushContent.ContentZh = tools.SubString(orderChatDetail.Content, 100)
		pushContent.ContentUg = tools.SubString(orderChatDetail.Content, 100)
	case chat.CHAT_CONTENT_TYPE_IMAGE:
		pushContent.ContentUg = "رەسىم"
		pushContent.ContentZh = "图片"
	case chat.CHAT_CONTENT_TYPE_ADDRESS:
		pushContent.ContentUg = "ئادرېس"
		pushContent.ContentZh = "地址"
	default:
		return
	}
	extraParams := map[string]interface{}{
		"page":     "chat_page",
		"order_id": tools.ToString(orderChatDetail.OrderID),
	}

	redisHelper :=tools.GetRedisHelper()
	redisKeyPrefix :="order_push_"
	logPrefix :="order_push_shipper_"
	// sendDuration :=60*time.Second

	for _, user := range chatUsers {
		// 忽略发送人推送消息
		if user.UserType == orderChatDetail.SenderType {
			continue
		}
		var userType string
		// 目前只针对配送员推送
		if !(user.UserType == 3 || user.UserType == 2 || user.UserType == 1) { //1,用户:2,商家,3:配送员,4:管理员;index,5:系统管理员
			continue
		}
		pd :=PushData{
			
		}
		sound :=constants.SoundDefault
		switch user.UserType {
		case 3://配送员
			userType = new(models.Admin).TableName()
			pd.Client = models.PushDeviceClientShipper
			pd.ChannelType = constants.ChannelTypeShipperChat //聊天室
		case 2://商家
			userType = new(models.Restaurant).TableName()
			pd.Client = models.PushDeviceClientMerchant
			pd.ChannelType = constants.ChannelTypeMerchantDefault
		case 1://用户端
			userType = new(models.User).TableName()
			pd.Client = models.PushDeviceClientUser
			pd.ChannelType = constants.ChannelTypeUserDefault	
		default:
			continue
		}
		reg, _ := regexp.Compile(`\d+-\d+\s+#(\d+)`)
		subTitle := reg.ReplaceAllString(orderChatDetail.OrderSerialNumber, "($1)")
		pushContent.TitleZh = fmt.Sprintf("%s %s", tools.SubString(user.ResNameZh, 39-len(subTitle)), subTitle)
		pushContent.TitleUg = fmt.Sprintf("%s %s", tools.SubString(user.ResNameUg, 39-len(subTitle)), subTitle)
		extraParams["restaurant_name_ug"] = pushContent.TitleUg
		extraParams["restaurant_name_zh"] = pushContent.TitleZh

		pushContent.Params = extraParams
		pd.UserType = userType
		pd.UserId = int(user.UserID)
		// pd.AdminId = int(user.UserID)
		pd.PushContent = pushContent
		pd.Sound = sound
		if user.UserType == 3 { //配送端 推送或者是socket 按情况确定
			
			orderId :=orderChatDetail.OrderID
			shipperId :=user.UserID
			onlineKey :=fmt.Sprintf(redisKeyPrefix+"online_%d",shipperId)
			
			exists1, _ := redisHelper.Exists(context.Background(),onlineKey).Result()
			tools.Logger.Info(logPrefix,"-给配送员分配订单推送 order_id:",orderId,",shipper_id:",shipperId,",-exists2=",exists1,",onlineKey=",onlineKey)
			// exists1 = 1 //强制设置 设备在线
			if exists1 != 0 { //商家用户在线
				tools.Logger.Info(logPrefix,"-给配送员分配订单推送 要发送socket order_id:",orderId,",shipper_id:",shipperId,",exists1=",exists1,",onlineKey=",onlineKey)
			
				job := NewOrderPushToShipper1Job()
				
				msgContentMap :=make(map[string]interface{})
				msgContentMap["title_ug"]=pushContent.TitleUg
				msgContentMap["title_zh"]=pushContent.TitleZh
				msgContentMap["content"]=pushContent.ContentZh
				msgContentMap["order_id"]=tools.ToString(orderChatDetail.OrderID)
				msgContent :=tools.MapToString(msgContentMap)

				job.ProduceMessageToConsumer(map[string]interface{}{
					"user_id": shipperId,
					"order_id": orderId,
					"type":     constants.SoundShipperSocketTypeChat,
					"content":msgContent,
				})
			}else{ //socket 不在线 继续用激光推送
				b.PushData(pd)
			}
		}else{
			b.PushData(pd)
		}
		

	}
}

// SendOrderAssignedPushByOrderID
//
// @Description: 根据订单ID发送订单分配推送消息
// @Author: Rixat
// @Time: 2024-08-26 17:23:27
// @receiver 
// @param c *gin.Context
func SendOrderAssignedPushByOrderID(orderID int,sendType int) {
	var order models.OrderToday
	tools.GetDB().Model(models.OrderToday{}).Where("id = ?", orderID).First(&order)
	if  order.ID == 0{
		return
	}
	SendOrderAssignedPush(order,sendType)
}
// SendOrderAssignedPush 管理员把订单分配给配送员时推送消息给配送员设备
func SendOrderAssignedPush(order models.OrderToday,sendType int) {
	redisHelper :=tools.GetRedisHelper()
	redisKeyPrefix :="order_push_"
	logPrefix :="order_push_shipper_"
	sendDuration :=60*time.Second
	onlineKey :=fmt.Sprintf(redisKeyPrefix+"online_%d",order.ShipperID)
	orderSendKey :=fmt.Sprintf(redisKeyPrefix+"shipper_%d_%d_%d", order.ID,order.ShipperID,constants.SoundShipperSocketTypeAdminAssignOrder)
	exists1, _ := redisHelper.Exists(context.Background(),onlineKey).Result()
	tools.Logger.Info(logPrefix,"-给配送员分配订单推送 order_id:",order.ID,",shipper_id:",order.ShipperID,",-exists2=",exists1,",onlineKey=",onlineKey)
	// exists1 = 1 //强制设置 设备在线
	if exists1 != 0 { //商家用户在线
		exists2, _ := redisHelper.Exists(context.Background(),orderSendKey).Result()
		if exists2 != 0 {
			//同样的请求发送过了 一分钟内不在发送
			return
		}
		tools.Logger.Info(logPrefix,"-给配送员分配订单推送 在线 要发送socket order_id:",order.ID,",shipper_id:",order.ShipperID,",exists1=",exists1,",onlineKey=",onlineKey)
		redisHelper.Set(context.Background(),orderSendKey,constants.ShipperOrderPushReady,sendDuration)	
		job := NewOrderPushToShipper1Job()
		job.ProduceMessageToConsumer(map[string]interface{}{
			"user_id": order.ShipperID,
			"order_id": order.ID,
			"type":     sendType,//管理员 分配订单
			
		})
		return
	}
	exists2, _ := redisHelper.Exists(context.Background(),orderSendKey).Result()
	if exists2 != 0 {
		//同样的请求发送过了 一分钟内不在发送
		return
	}
	redisHelper.Set(context.Background(),orderSendKey,constants.ShipperOrderPushReady,sendDuration)	
	tools.Logger.Info(logPrefix,"-给配送员分配订单推送 不在线 要发送极光推送 order_id:",order.ID,",shipper_id:",order.ShipperID)
	job := NewPushJob()
	subTitle := tools.ToString(order.SerialNumber)
	channelType :=constants.ChannelTypeShipperAssignOrder
	sound := constants.SoundShipperAssignOrder
	contentUg := constants.ContentShipperAssignOrderUg
	contentZh := constants.ContentShipperAssignOrderZh
	if sendType == constants.SoundShipperSocketTypeSystemAssignOrder { //智能派单推送声音
		channelType = constants.ChannelTypeShipperAutoDispatchAssignOrder
		sound =constants.SoundShipperAutoDispatchAssignOrder
		contentUg = constants.ContentShipperAutoDispatchAssignOrderUg
		contentZh = constants.ContentShipperAutoDispatchAssignOrderZh
	}
	job.PushData(PushData{
		UserId:   order.ShipperID,
		UserType: constants.PushUserTypeShipper,
		PushContent: PushContent{
			TitleUg:   fmt.Sprintf("%s %s", tools.SubString(order.Restaurant.NameUg, 39-len(subTitle)), subTitle),
			TitleZh:   fmt.Sprintf("%s %s", tools.SubString(order.Restaurant.NameZh, 39-len(subTitle)), subTitle),
			ContentUg: contentUg,
			ContentZh: contentZh,
			Params:    map[string]interface{}{},
		},
		Client: models.PushDeviceClientShipper,
		Sound:  sound,
		ChannelType: channelType,
		AdminId: order.ShipperID,
	})
}

// SendFoodReadyPush 商家已备餐通知
func SendFoodReadyPush(order models.OrderToday) {
	redisHelper :=tools.GetRedisHelper()
	redisKeyPrefix :="order_push_"
	logPrefix :="order_push_shipper_"
	sendDuration :=60*time.Second
	onlineKey :=fmt.Sprintf(redisKeyPrefix+"online_%d",order.ShipperID)
	orderSendKey :=fmt.Sprintf(redisKeyPrefix+"shipper_%d_%d_%d", order.ID,order.ShipperID,constants.SoundShipperSocketTypeFoodReady)
	exists1, _ := redisHelper.Exists(context.Background(),onlineKey).Result()
	tools.Logger.Info(logPrefix,"-订单美食已准备 推送 order_id:",order.ID,",shipper_id:",order.ShipperID,",-exists2=",exists1,",onlineKey=",onlineKey)
	// exists1 = 1 //强制设置 设备在线
	if exists1 != 0 { //商家用户在线
		tools.Logger.Info(logPrefix,"-给配送员分配订单推送 在线 要发送socket order_id:",order.ID,",shipper_id:",order.ShipperID,",exists1=",exists1,",onlineKey=",onlineKey)
		redisHelper.Set(context.Background(),orderSendKey,constants.ShipperOrderPushReady,sendDuration)
		job := NewOrderPushToShipper1Job()
		job.ProduceMessageToConsumer(map[string]interface{}{
			"user_id": order.ShipperID,
			"order_id": order.ID,
			"type":     constants.SoundShipperSocketTypeFoodReady,//管理员 订单已准备好
		})
		return
	}
	exists2, _ := redisHelper.Exists(context.Background(),orderSendKey).Result()
	if exists2 != 0 {
		//同样的请求发送过了 一分钟内不在发送
		return
	}
	redisHelper.Set(context.Background(),orderSendKey,constants.ShipperOrderPushReady,sendDuration)	
	tools.Logger.Info(logPrefix,"-订单美食已准备 不在线 发送激光推送 order_id:",order.ID,",shipper_id:",order.ShipperID)
	job := NewPushJob()
	subTitle := tools.ToString(order.SerialNumber)
	job.PushData(PushData{
		UserId:   order.ShipperID,
		UserType: constants.PushUserTypeShipper,
		PushContent: PushContent{
			TitleUg:   fmt.Sprintf("%s %s", tools.SubString(order.Restaurant.NameUg, 39-len(subTitle)), subTitle),
			TitleZh:   fmt.Sprintf("%s %s", tools.SubString(order.Restaurant.NameZh, 39-len(subTitle)), subTitle),
			ContentUg: constants.ContentShipperFoodReadyUg,
			ContentZh: constants.ContentShipperFoodReadyZh,
			Params:    map[string]interface{}{},
		},
		Client: models.PushDeviceClientShipper,
		Sound:  constants.SoundShipperFoodReady,
		ChannelType: constants.ChannelTypeShipperFoodReady,
		AdminId: order.ShipperID,
	})
}
//商家端  管理员取消订单推送
func SendAdminCancelPush(su constants.StoreUserOrder) {
	tools.Logger.Info(fmt.Sprintf("order_push_取消订单 商家 要发送激光推送order_id:"+tools.ToString(su.OrderId)+"_content:%v",su))
	job := NewPushJob()
	//  添加要播放的声音类型
	job.PushData(PushData{
		UserId: su.StoreId,
		UserType: constants.PushUserTypeMerchant,
		PushContent: PushContent{
			TitleUg:   constants.TitleMerchantAdminCancelOrderUg,
			TitleZh:   constants.TitleMerchantAdminCancelOrderZh,
			ContentUg: constants.ContentMerchantAdminCancelOrderUg,
			ContentZh: constants.ContentMerchantAdminCancelOrderZh,
			Params:    map[string]interface{}{},
		},
		Client: models.PushDeviceClientMerchant,
		Sound:  constants.SoundMerchantAdminCancelOrder,
		ChannelType: constants.ChannelTypeMerchantDefault,
		AdminId:su.UserId,
	})	

}

//配送员  配送员取消订单推送
func SendShipperCancelPush(order map[string]interface{},restaurant map[string]interface{},shipperId int,from string) {

	redisHelper :=tools.GetRedisHelper()
	redisKeyPrefix :="order_push_"
	logPrefix :="order_push_shipper_"
	sendDuration :=60*time.Second
	
	orderId := tools.ToString(order["id"])

	onlineKey :=fmt.Sprintf(redisKeyPrefix+"online_%d",shipperId)
	orderSendKey :=fmt.Sprintf(redisKeyPrefix+"shipper_%d_%d_%d", tools.ToInt(orderId),shipperId,constants.SoundShipperSocketTypeAdminCancelOrder)
	exists1, _ := redisHelper.Exists(context.Background(),onlineKey).Result()
	tools.Logger.Info(logPrefix,"-给配送员 取消 订单推送 order_id:",orderId,",shipper_id:",shipperId,",-exists2=",exists1,",onlineKey=",onlineKey)
	channelType :=constants.ChannelTypeShipperOrderCancelCms
	sound :=constants.SoundShipperCancelOrder
	socketSound :=constants.SoundShipperSocketTypeAdminCancelOrder
	switch(from){
		case "cms"://后台取消
			channelType = constants.ChannelTypeShipperOrderCancelCms
			sound =constants.SoundShipperCancelOrder
			socketSound =constants.SoundShipperSocketTypeAdminCancelOrder
		case "api"://商家取消
			channelType = constants.ChannelTypeShipperOrderCancelStore
			sound =constants.SoundShipperCancelOrderStore
			socketSound =constants.SoundShipperSocketTypeRestaurantCancelOrder
		case "mini"://用户取消
			channelType = constants.ChannelTypeShipperOrderCancelCustomer
			sound =constants.SoundShipperCancelOrderCustomer
			socketSound =constants.SoundShipperSocketTypeCustomerCancelOrder
	}
	// exists1 = 1 //强制设置 设备在线
	if exists1 != 0 { //商家用户在线
		tools.Logger.Info(logPrefix,"-给配送员 取消 订单推送 在线 要发送socket order_id:",orderId,",shipper_id:",shipperId,",exists1=",exists1,",onlineKey=",onlineKey)
		exists2, _ := redisHelper.Exists(context.Background(),orderSendKey).Result()
		if exists2 != 0 {
			//同样的请求发送过了 一分钟内不在发送
			return
		}
		redisHelper.Set(context.Background(),orderSendKey,constants.ShipperOrderPushReady,sendDuration)
		job := NewOrderPushToShipper1Job()
		job.ProduceMessageToConsumer(map[string]interface{}{
			"user_id": tools.ToInt(shipperId),
			"order_id": tools.ToInt(orderId),
			"type":     socketSound,
		})
		return
	}
	exists2, _ := redisHelper.Exists(context.Background(),orderSendKey).Result()
	if exists2 != 0 {
		//同样的请求发送过了 一分钟内不在发送
		return
	}
	tools.Logger.Info(logPrefix,"-给配送员 取消 不在线 发送激光推送 订单推送 order_id:",orderId,",shipper_id:",shipperId)
	subTitle := tools.ToString(order["serial_number"])
	
	var pushData = PushData{
		UserType: constants.PushUserTypeShipper,
		UserId:   tools.ToInt(shipperId),
		PushContent: PushContent{
			TitleZh:   fmt.Sprintf("%s %s", tools.SubString(tools.ToString(restaurant["name_zh"]), 39-len(subTitle)), subTitle),
			TitleUg:   fmt.Sprintf("%s %s", tools.SubString(tools.ToString(restaurant["name_ug"]), 39-len(subTitle)), subTitle),
			ContentZh: constants.ContentShipperOrderCancelZh,
			ContentUg: constants.ContentShipperOrderCancelUg,
			Params: map[string]interface{}{
				"page":     "chat",
				"order_id":tools.ToString(order["id"]),
			},
		},
		Sound: sound,
		ChannelType: channelType,
		AdminId: tools.ToInt(shipperId),
	}

	pushJob := NewPushJob()
	pushJob.PushData(pushData)
	
}



// SendNewOrderPush 配送端新订单通知
func  SendNewOrderPush(orderId int,shipperId int) {
	go func ()  { //异步执行
		defer func() {
			if err := recover(); err != nil {
				tools.Logger.Error("配送端新订单通知推送错误",err)
			}
		}()
		//格式化 订单格式和判断 是否可以推送
		content :=NewOrderFormat(orderId,shipperId)

		if len(content) == 0 {
			//内容为空 表示某个验证未通过 
			return 
		}
		redisHelper :=tools.GetRedisHelper()
		redisKeyPrefix :="order_push_"
		logPrefix :="order_push_shipper_"

		sendDuration :=60*time.Second
		onlineKey :=fmt.Sprintf(redisKeyPrefix+"online_%d",shipperId)
		orderSendKey :=fmt.Sprintf(redisKeyPrefix+"shipper_%d_%d", orderId,shipperId)
		exists1, _ := redisHelper.Exists(context.Background(),onlineKey).Result()
		tools.Logger.Info(logPrefix,"配送员 新订单推送 order_id:",orderId,",shipper_id:",shipperId,",-exists2=",exists1,",onlineKey=",onlineKey)
		// exists1 = 1 //强制设置 设备在线
		if exists1 != 0 { //商家用户在线
			tools.Logger.Info(logPrefix,"-配送员 在线 新订单推送 要发送socket order_id:",orderId,",shipper_id:",shipperId,",onlineKey=",onlineKey)
			redisHelper.Set(context.Background(),orderSendKey,constants.ShipperOrderPushReady,sendDuration)
			job := NewOrderPushToShipper1Job()
			job.ProduceMessageToConsumer(map[string]interface{}{
				"user_id": shipperId,
				"order_id": orderId,
				"type":     constants.SoundShipperSocketTypeNewOrder,//管理员 订单已准备好
				"content":content,
			})
			return
		}
		tools.Logger.Info(logPrefix,"配送员 不在线 新订单推送 激光推送 order_id:",orderId,",shipper_id:",shipperId,",onlineKey=",onlineKey)
		job := NewPushJob()
		job.PushData(PushData{
			UserId: shipperId,
			UserType: constants.PushUserTypeShipper,
			PushContent: PushContent{
				TitleUg:   constants.TitleShipperNewOrderUg,
				TitleZh:   constants.TitleShipperNewOrderZh,
				ContentUg: constants.ContentShipperNewOrderUg,
				ContentZh: constants.ContentShipperNewOrderZh,
				Params:    map[string]interface{}{},
			},
			Client: models.PushDeviceClientShipper,
			Sound:  constants.SoundShipperNewOrder,
			ChannelType: constants.ChannelTypeShipperNewOrder,
			AdminId:shipperId,
		})
		
	}()
}

//推送订单格式化和必要的判断
func NewOrderFormat(orderId int,shipperId int) string{

	db :=tools.ReadDb1
	logPrefix :="order_push_shipper_"

	var admin models.Admin
	db.Model(&models.Admin{}).
		Where("id =? and state = ? and type in (8,9)",shipperId,1).
		Find(&admin)

	if configs.MyApp.CheckForAttendance > 0 {
		attendanceState :=admin.AttendanceState	
		if attendanceState != 1 {
			tools.Logger.Info(logPrefix+"-shipper socket 推送 跳过 没有打卡上班的 ",shipperId,attendanceState)
			return ""
		}
	}	
	var pushDevice models.PushDevice	
	db.Model(&models.PushDevice{}).
		Where("user_id =? and user_type = ? ",shipperId,"t_admin").
		Find(&pushDevice)
	langNow :="ug"	
	if pushDevice.Lang != "ug" {
		langNow ="zh"	
	}
	l :=lang.LangUtil{
		Lang: langNow,
	}	
	
	var order models.OrderToday	
	db.Model(&models.OrderToday{}).Where("id= ?",orderId).
		Preload("AddressView", func(db *gorm.DB) *gorm.DB {
			return db.Select("address_view.city_name_" + langNow + " as city_name," +
				"address_view.area_name_" + langNow + " as area_name," +
				"address_view.building_name_" + langNow + " as building_name," +
				"address_view.street_name_" + langNow + " as street_name, " +
				"address_view.building_id as building_id,street_id")
		}).
		Preload("Restaurant", func(db *gorm.DB) *gorm.DB {
			return db.Select(
				"t_restaurant.name_" + langNow + " as name," +
					"t_restaurant.address_" + langNow + " as address," + "t_restaurant.id",
			)
		}).
		Preload("PushDetail", func(db *gorm.DB) *gorm.DB {
			return db.Where("t_shipper_order_push_detail.shipper_id = ?", shipperId)
		}).
		Preload("PayTypes", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "name_ug", "name_zh")
		}).
		Preload("OrderExtend").
		Find(&order)
		
	if order.PayType != 5 && admin.TakeCashOrder == 0{ //订单为现金订单 而且 配送员不能取 现金订单的话
		tools.Logger.Info(logPrefix+"-shipper 推送 跳过 不能取 现金订单 shipperId:",shipperId,",orderId:",orderId)
		return ""
	}
	if order.PushDetail.ID ==0 { //不是推送的配送员的话 跳过
		return ""
	}
	//已被抢单的话跳过
	if order.Taked == 1 {
		tools.Logger.Info(logPrefix+"-shipper 推送 跳过 已经被抢单的 ",orderId)
		return ""
	}
	// if order.MarketType == 2{ //特价活动订单只能由特价活动的 配送员配送
	// 	shipperIds := order.GetSpecialPriceOrderShipperIds()
	// 	if len(shipperIds) > 0 {
	// 		if !tools.InArray(shipperId, shipperIds) {//不是特价活动配送员的话跳过
	// 			tools.Logger.Info(logPrefix+"-shipper 推送 跳过 不是特价活动配送员的 ",orderId,",",shipperId)
	// 			return ""
	// 		}
	// 	}
	// }

	old :=order
	payType := tools.If(pushDevice.Lang == "ug", old.PayTypes.NameUg, old.PayTypes.NameZh)
	if old.PayType != 0 {
		if old.CashClearState == 1 && old.CashClearChannel == 3 {
			payType =l.T("pay_type_app_pay_desctription")
		}
		if old.CashClearState == 1 && old.CashClearChannel == 4 {
			payType = l.T("pay_type_qr_code_desctription")
		}
		arr := []int{1, 2}
		var inarr bool
		for _, v := range arr {
			if v == old.CashClearChannel {
				inarr = true
			}
		}
		if old.CashClearState == 1 && inarr {
			payType = l.T("pay_type_sms_desctription")
		}
	}
	item := shipperResource.NewOrderItems{
		ID:            old.ID,
		StoreID:       old.StoreID,
		Distance:      old.Distance,
		OrderAddress:  old.OrderAddress,
		OrderType:     old.OrderType,
		StoreName:     old.Restaurant.Name,
		StoreAddress:  old.Restaurant.Address,
		OrderState:    old.State,
		SerialNumber:  int(old.SerialNumber),
		Timezone:      old.Timezone,
		CityID:        old.CityID,
		CityName:      old.AddressView.CityName,
		AreaID:        old.AreaID,
		AreaName:      old.AddressView.AreaName,
		StreetID:      int(old.AddressView.StreetId),
		StreetName:    old.AddressView.StreetName,
		BuildingID:    int(old.AddressView.BuildingId),
		BuildingName:  old.AddressView.BuildingName,
		ShippingPrice: tools.ToPrice(tools.ToFloat64(old.PushDetail.EstimatedShippingPrice) / 100),
		PayType:       payType,
	}
	item.FoodsReadyTime = old.GetFoodsReadyTime()
	item.FoodsReadyRemainMinute = old.FoodsReadyRemainMinute()

	item.BookingTime = carbon.Parse(old.BookingTime).Format("H:i")
	item.BookingDateTime = carbon.Parse(old.BookingTime).Format("Y-m-d H:i:s")

	item.BookingRemainingMinute = int(carbon.Now().DiffInMinutes(carbon.Parse(item.BookingDateTime)))
	
	orderMapBytes,_ :=json.Marshal(&item)
	return string(orderMapBytes)
	
}