package jobs

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/silenceper/wechat/v2"

	"github.com/silenceper/wechat/v2/cache"
	"github.com/silenceper/wechat/v2/credential"
	miniConfig "github.com/silenceper/wechat/v2/miniprogram/config"
	subscribe2 "github.com/silenceper/wechat/v2/miniprogram/subscribe"

	rmq_client "github.com/apache/rocketmq-clients/golang"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
	"strings"
	"time"
)

// SendWechatMiniMessageJob
//
//	@Description: 发送微信模板消息
type SendWechatMiniMessageJob struct {
	BaseJob
}

func (b *SendWechatMiniMessageJob) MaxReconnectCount() int32 {
	return 1
}

func (b *SendWechatMiniMessageJob) MaxRetryCount() int32 {

	return 3
}

// TopicName
//
//	@Description: 返回Topic 阿里云后台配置
//	@author: Ali<PERSON>jan
//	@Time: 2023-09-09 16:53:25
//	@receiver b *SendWechatMiniMessageJob
//	@return string
func (b *SendWechatMiniMessageJob) TopicName() string {
	return "wechat_send_message"
}

// GroupId
//
//	@Description: 返回GroupID 阿里云后台配置
//	@author: Alimjan
//	@Time: 2023-09-09 16:53:12
//	@receiver b *SendWechatMiniMessageJob
//	@return string
func (b *SendWechatMiniMessageJob) GroupId() string {
	return "WECHAT_MESSAGE"
}

// NewSendWechatMiniMessageJob
//
//	@Description: 初始化队列
//	@author: Alimjan
//	@Time: 2023-09-09 16:53:05
//	@return *SendWechatMiniMessageJob
func NewSendWechatMiniMessageJob() *SendWechatMiniMessageJob {
	job := SendWechatMiniMessageJob{}
	job.job = &job
	return &job
}

const (
	ORDER_PAYED    = 1
	ORDER_RECEIVED = 2
	ORDER_CANCEL   = 3
)

// HandleMessage
//
//	@Description: 接收到消息时被调用
//	@author: Alimjan
//	@Time: 2023-09-09 16:52:47
//	@receiver b *SendWechatMiniMessageJob
//	@param v mq_http_sdk.ConsumeMessageEntry
//	@return bool
func (b *SendWechatMiniMessageJob) HandleMessage(v *rmq_client.MessageView) (bool, int64) {
	//超过两次，测不重复发送

	firstTime := *v.GetBornTimestamp()
	if time.Now().Sub(firstTime).Minutes() > 6 {
		tools.Logger.Error("消息已经超过6分钟，不再发送")
		return true, 0
	}
	tools.Logger.Info("开始发送微信订阅消息", string(v.GetBody()))

	OrderTypeData := OrderTypeData{}
	err := json.Unmarshal([]byte(v.GetBody()), &OrderTypeData)
	if err != nil {
		tools.Logger.Error("json.Unmarshal error", err)
		return true, 0
	}
	return b.sendMessageToUser(OrderTypeData.OrderId, OrderTypeData.Type)

}

type OrderTypeData struct {
	OrderId int64 `form:"order_id" binding:"required" json:"order_id"`
	Type    int64 `form:"type" binding:"required" json:"type"`
}

func (b *SendWechatMiniMessageJob) sendMessageToUser(orderId int64, optType int64) (bool, int64) {
	db := tools.Db
	var wechatSendMessage models.WechatSendMessage
	db.Model(&models.WechatSendMessage{}).Where("order_id = ?", orderId).Scan(&wechatSendMessage)
	var todayOrder models.OrderToday
	db.Model(&models.OrderToday{}).Where("id = ?", orderId).Scan(&todayOrder)

	if optType == ORDER_PAYED {
		if todayOrder.PayType == models.PAYMENT_CASH || todayOrder.PayType == models.PAYMENT_AGENT_WECHAT { //现金订单 不发送 
			return true, 0
		}
		if err := b.sendOrderPayed(todayOrder, wechatSendMessage); err != nil {
			if strings.Contains(err.Error(), "retry") {
				err = b.sendOrderPayed(todayOrder, wechatSendMessage)
			}
			if err != nil && strings.Contains(err.Error(), "43101") {
				//tools.Logger.Error("发送订阅消息->支付成功失败 用户拒绝 过一会再发一次","orderId",orderId,"optTypeStr",optType,err)
				return false, 30
			} else if err != nil {
				tools.Logger.Error("发送订阅消息->支付成功消息发送失败", "orderId", orderId, "optTypeStr", optType, err)
				return true, 0
			}
		}
		return true, 0
	} else if optType == ORDER_CANCEL {
		if err := b.sendOrderCancel(todayOrder, wechatSendMessage); err != nil {
			if strings.Contains(err.Error(), "retry") {
				err = b.sendOrderCancel(todayOrder, wechatSendMessage)
			}
			if err != nil {
				tools.Logger.Error("发送订阅消息->取消订单消息发送失败", "orderId", orderId, "optTypeStr", optType, err)
				return true, 0
			}
		}
		return true, 0
	} else if optType == ORDER_RECEIVED {
		if todayOrder.PayType == models.PAYMENT_CASH || todayOrder.PayType == models.PAYMENT_AGENT_WECHAT { //现金订单 不发送 
			return true, 0
		}
		if err := b.sendOrderReceived(todayOrder, wechatSendMessage); err != nil {
			if strings.Contains(err.Error(), "retry") {
				err = b.sendOrderReceived(todayOrder, wechatSendMessage)
			}
			if err != nil {
				tools.Logger.Error("发送订阅消息->接单消息发送失败", "orderId", orderId, "optTypeStr", optType, err)
				return true, 0
			}
		}
		return true, 0
	}

	return true, 0
}

// sendOrderPayed
//
//	@Description: 发送支付通知
//	@author: Alimjan
//	@Time: 2023-09-13 19:03:22
//	@receiver b *SendWechatMiniMessageJob
//	@param todayOrder models.OrderToday
//	@param wechatSendMessage models.WechatSendMessage
//	@return error
func (b *SendWechatMiniMessageJob) sendOrderPayed(todayOrder models.OrderToday, wechatSendMessage models.WechatSendMessage) error {
	wc := wechat.NewWechat()
	//获取context
	//设置缓存
	ctx, cancel := context.WithDeadline(context.Background(), time.Now().Add(30*time.Second))
	defer cancel()
	cfg := &miniConfig.Config{
		AppID:     configs.MyApp.WechatAppID,
		AppSecret: configs.MyApp.WechatAppSecret,
		Cache: cache.NewRedis(ctx, &cache.RedisOpts{
			Host:     configs.MyApp.RedisIp,
			Password: configs.MyApp.RedisPassword,
		}),
	}
	miniprogram := wc.GetMiniProgram(cfg)
	miniprogram.SetAccessTokenHandle(b)
	subscribe := miniprogram.GetSubscribe()

	payTime := carbon.Time2Carbon(todayOrder.PayTime).ToDateTimeString("Asia/Shanghai")

	bookingTime := carbon.Parse(todayOrder.BookingTime).ToDateTimeString("Asia/Shanghai")

	totalPrice := tools.ToPrice(float64(tools.ToInt(todayOrder.Price+todayOrder.Shipment)+todayOrder.LunchBoxFee) / 100.0)
	//tools.Logger.Info("total price",totalPrice)
	err := subscribe.Send(&subscribe2.Message{
		ToUser:     wechatSendMessage.OpenID,
		TemplateID: "d72MU_RIb3Qr-XNDqElJhf5VpaAKwwV6TztMa-XVsGc",
		Page:       fmt.Sprintf("pages/index/index?orderId=%d", todayOrder.ID),
		Data: map[string]*subscribe2.DataItem{
			"amount4": {
				Value: totalPrice,
			},
			"character_string1": {
				Value: tools.CutChineseString(todayOrder.OrderID, 40),
			},
			"thing6": {
				Value: tools.CutChineseString(wechatSendMessage.StoreName, 40),
			},
			"time7": {
				Value: bookingTime,
			},
			"time8": {
				Value: payTime,
			},
		},
	})
	if err != nil {
		if strings.Contains(err.Error(), "40001") ||
			strings.Contains(err.Error(), "40014") ||
			strings.Contains(err.Error(), "42001") {
			subscribe.Cache.Delete("access_token")
			return errors.New("retry")
		}
	}
	return err
}

// sendOrderCancel
//
//	@Description: 发送取消订单通知
//	@author: Alimjan
//	@Time: 2023-09-13 19:03:31
//	@receiver b *SendWechatMiniMessageJob
//	@param todayOrder models.OrderToday
//	@param wechatSendMessage models.WechatSendMessage
//	@return error
func (b *SendWechatMiniMessageJob) sendOrderCancel(todayOrder models.OrderToday, wechatSendMessage models.WechatSendMessage) error {
	wc := wechat.NewWechat()
	//获取context
	//设置缓存
	ctx, cancel := context.WithDeadline(context.Background(), time.Now().Add(30*time.Second))
	defer cancel()
	cfg := &miniConfig.Config{
		AppID:     configs.MyApp.WechatAppID,
		AppSecret: configs.MyApp.WechatAppSecret,
		Cache: cache.NewRedis(ctx, &cache.RedisOpts{
			Host:     configs.MyApp.RedisIp,
			Password: configs.MyApp.RedisPassword,
		}),
	}
	miniprogram := wc.GetMiniProgram(cfg)
	miniprogram.SetAccessTokenHandle(b)
	subscribe := miniprogram.GetSubscribe()
	things2 := "زاكاس بىكار قىلىنىدى"
	if wechatSendMessage.LangID == 2 {
		things2 = "订单已取消"
	}

	//1 表示实时订单自动被系统取消  2表示从后台取消 3表示商家端取消 4表示 用户端取消

	langUtil := lang.LangUtil{
		Lang: tools.If(wechatSendMessage.LangID == 1, "ug", "zh"),
	}
	things7 := langUtil.TArr("order_refund_channel")[todayOrder.RefundChanel]
	totalPrice := tools.ToPrice(float64(tools.ToInt(todayOrder.Price+todayOrder.Shipment)+todayOrder.LunchBoxFee) / 100.0)
	//tools.Logger.Info("total price",totalPrice)
	err := subscribe.Send(&subscribe2.Message{
		ToUser:     wechatSendMessage.OpenID,
		TemplateID: "vTnhpYUtUwRi_V0VGiTc_7Gqsf7zjJvjjp_8eUwDx40",
		Page:       fmt.Sprintf("pages/index/index?orderId=%d", todayOrder.ID),
		Data: map[string]*subscribe2.DataItem{
			"amount13": {
				Value: totalPrice,
			},
			"character_string3": {
				Value: tools.CutChineseString(todayOrder.OrderID, 40),
			},
			"thing1": {
				Value: tools.CutChineseString(wechatSendMessage.StoreName, 40),
			},
			"thing2": {
				Value: things2,
			},
			"thing7": {
				Value: tools.CutChineseString(things7, 40),
			},
		},
	})
	if err != nil {
		if strings.Contains(err.Error(), "40001") ||
			strings.Contains(err.Error(), "40014") ||
			strings.Contains(err.Error(), "42001") {
			subscribe.Cache.Delete("access_token")
			return errors.New("retry")
		}
	}
	return err
}

// sendOrderReceived
//
//	@Description: 发送接单通知
//	@author: Alimjan
//	@Time: 2023-09-13 19:03:44
//	@receiver b *SendWechatMiniMessageJob
//	@param todayOrder models.OrderToday
//	@param wechatSendMessage models.WechatSendMessage
//	@return error
func (b *SendWechatMiniMessageJob) sendOrderReceived(todayOrder models.OrderToday, wechatSendMessage models.WechatSendMessage) error {
	wc := wechat.NewWechat()
	//获取context
	//设置缓存
	ctx, cancel := context.WithDeadline(context.Background(), time.Now().Add(30*time.Second))
	defer cancel()
	cfg := &miniConfig.Config{
		AppID:     configs.MyApp.WechatAppID,
		AppSecret: configs.MyApp.WechatAppSecret,
		Cache: cache.NewRedis(ctx, &cache.RedisOpts{
			Host:     configs.MyApp.RedisIp,
			Password: configs.MyApp.RedisPassword,
		}),
	}
	miniprogram := wc.GetMiniProgram(cfg)
	miniprogram.SetAccessTokenHandle(b)
	subscribe := miniprogram.GetSubscribe()
	confirmTime := carbon.Time2Carbon(todayOrder.UpdatedAt).ToDateTimeString("Asia/Shanghai")

	totalPrice := tools.ToPrice(float64(todayOrder.ActualPaid) / 100.0)
	
	err := subscribe.Send(&subscribe2.Message{
		ToUser:     wechatSendMessage.OpenID,
		TemplateID: "hbTzTYxYC9wIFze2OEv51IS6c7AoqR2-6VakW0_lIgo",
		Page:       fmt.Sprintf("pages/index/index?orderId=%d", todayOrder.ID),
		Data: map[string]*subscribe2.DataItem{
			"amount2": {
				Value: totalPrice,
			},
			"character_string1": {
				Value: tools.CutChineseString(todayOrder.OrderID, 40),
			},
			"thing3": {
				Value: tools.CutChineseString(wechatSendMessage.StoreName, 40),
			},
			"time4": {
				Value: confirmTime,
			},
			"phone_number5": {
				Value: tools.CutChineseString(wechatSendMessage.StorePhone, 40),
			},
		},
	})
	if err != nil {
		if strings.Contains(err.Error(), "40001") ||
			strings.Contains(err.Error(), "40014") ||
			strings.Contains(err.Error(), "42001") {
			subscribe.Cache.Delete("access_token")
			return errors.New("retry")
		}
	}
	return err
}

// 缓存获取access token
const accessTokenURL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s"

// GetAccessToken
//
//	@Description: 缓存获取access token
//	@author: Alimjan
//	@Time: 2023-09-13 19:05:32
//	@receiver b *SendWechatMiniMessageJob
//	@return accessToken string
//	@return err error
func (b *SendWechatMiniMessageJob) GetAccessToken() (accessToken string, err error) {
	ctx, cancel := context.WithDeadline(context.Background(), time.Now().Add(30*time.Second))
	defer cancel()
	redis := tools.GetRedisHelper()
	accessToken, err = redis.Get(ctx, "access_token").Result()
	if err == nil {
		return accessToken, nil
	} else {
		res, err := credential.GetTokenFromServer(fmt.Sprintf(accessTokenURL, configs.MyApp.WechatAppID, configs.MyApp.WechatAppSecret))
		if err != nil {
			return "", err
		} else {
			redis.SetEX(ctx, "access_token", res.AccessToken, 5700*time.Second)
			return res.AccessToken, nil
		}
	}
}
