package jobs

import (
	"context"
	"encoding/json"
	
	rmq_client "github.com/apache/rocketmq-clients/golang"
	"github.com/apache/rocketmq-clients/golang/credentials"
	"mulazim-api/configs"
	"mulazim-api/configs/mq"
	"mulazim-api/tools"
	"time"
)

type JobInterface interface {
	HandleMessage(v *rmq_client.MessageView) (bool, int64)
	TopicName() string
	GroupId() string
	//
	// MaxRetryCount
	//  @Description: 重试次数
	//  @return int64
	//
	MaxRetryCount() int32
	MaxReconnectCount() int32
}
type BaseJob struct {
	job                   JobInterface
	currentReconnectCount int32
}

var (
	// maximum waiting time for receive func
	awaitDuration = time.Second * 5
	// maximum number of messages received at one time
	maxMessageNum int32 = 16
	// invisibleDuration should > 20s
	invisibleDuration = time.Second * 20
	// receive messages in a loop
)

func (b *BaseJob) StartConsumer() {
	endpoint := mq.MqConfigOpt.Endpoint
	// 请确保环境变量ALIBABA_CLOUD_ACCESS_KEY_ID、ALIBABA_CLOUD_ACCESS_KEY_SECRET已设置。
	// AccessKey ID，阿里云身份验证标识。
	accessKey := mq.MqConfigOpt.AccessKey
	// AccessKey Secret，阿里云身份验证密钥。
	secretKey := mq.MqConfigOpt.SecretKey

	// 消息所属的Topic，在消息队列RocketMQ版控制台创建。
	topic := "pro_" + b.job.TopicName()
	if configs.CurrentEnvironment != "production" {
		topic = "dev_" + b.job.TopicName()
	}

	// 您在控制台创建的Group ID。
	groupId := "GID_PRO_" + b.job.GroupId()
	if configs.CurrentEnvironment != "production" {
		groupId = "GID_DEV_" + b.job.GroupId()
	}
	simpleConsumer, err := rmq_client.NewSimpleConsumer(&rmq_client.Config{
		Endpoint:      endpoint,
		ConsumerGroup: groupId,
		Credentials: &credentials.SessionCredentials{
			AccessKey:    accessKey,
			AccessSecret: secretKey,
		},
	},
		rmq_client.WithAwaitDuration(awaitDuration),
		rmq_client.WithSubscriptionExpressions(map[string]*rmq_client.FilterExpression{
			topic: rmq_client.SUB_ALL,
		}),
	)
	if err != nil {
		//log.Fatal(err)
		tools.Logger.Errorf("FATAL rocketmq-consumer-fail ---->\n\tError:%s  topic:%s\n", err, topic)
		return
	}
	err = simpleConsumer.Start()
	if err != nil {
		tools.Logger.Errorf("FATAL %s",err)
		return
	}
	// graceful stop simpleConsumer
	defer simpleConsumer.GracefulStop()
	for {
		//fmt.Println("start receive message")
		mvs, err := simpleConsumer.Receive(context.TODO(), maxMessageNum, invisibleDuration)
		if err != nil {
			tools.Logger.Errorf(topic + " : " + err.Error())
			time.Sleep(time.Second * 3)
		}
		// ack message
		for _, mv := range mvs {
			if success, delayTime := b.job.HandleMessage(mv); success {
				simpleConsumer.Ack(context.TODO(), mv)
			} else {
				if b.job.MaxRetryCount() <= (mv.GetDeliveryAttempt()) {
					simpleConsumer.Ack(context.TODO(), mv)
				} else {
					simpleConsumer.ChangeInvisibleDurationAsync(mv, time.Duration(delayTime)*time.Second)
				}
			}
		}
	}
}

// ProduceMessageToConsumer
//
//	@Description: 给阿里rocket mq 发送消息
//	@author: Alimjan
//	@Time: 2023-09-09 15:51:29
//	@param topic string
//	@param data map[string]interface{}
func (b *BaseJob) ProduceMessageToConsumer(data map[string]interface{}) {
	// 设置HTTP协议客户端接入点，进入消息队列RocketMQ版控制台实例详情页面的接入点区域查看。
	endpoint := mq.MqConfigOpt.Endpoint
	// 请确保环境变量ALIBABA_CLOUD_ACCESS_KEY_ID、ALIBABA_CLOUD_ACCESS_KEY_SECRET已设置。
	// AccessKey ID，阿里云身份验证标识。
	accessKey := mq.MqConfigOpt.AccessKey
	// AccessKey Secret，阿里云身份验证密钥。
	secretKey := mq.MqConfigOpt.SecretKey

	topic := "pro_" + b.job.TopicName()
	if configs.CurrentEnvironment != "production" {
		topic = "dev_" + b.job.TopicName()
	}
	b.currentReconnectCount++

	byteJson, _ := json.Marshal(data)
	msg := &rmq_client.Message{
		Topic: topic,
		Body:  byteJson,
	}
	producer := GetProducer(endpoint, accessKey, secretKey, topic)
	// 设置消息自定义属性。
	//msg.StartDeliverTime = time.Now().Add(time.Second * 20).UnixMilli()
	_, err := producer.Send(context.TODO(), msg)
	if err != nil {
		tools.Logger.Errorf("rocketmq-publish-fail ---->\n\tError:%s  topic:%s  data:%s\n", err, topic, data)
		DeleteProducer(topic)
		if b.currentReconnectCount <= b.job.MaxReconnectCount() {
			b.ProduceMessageToConsumer(data)
		}
	}
}
