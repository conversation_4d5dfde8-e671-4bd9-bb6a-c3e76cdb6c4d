/***
 * @Author: rzbb
 * @Date: 2023-04-14 16:00:42
 * @LastEditors: rzbb
 * @LastEditTime: 2023-04-14 16:00:49
 * @FilePath: \job\yinshangBankJob.go
 * @Description:
 * @2023-04-14 16:00:42
 */
package jobs

import (
	"encoding/json"
	"fmt"
	"strings"

	"mulazim-api/services/merchant/selfsign"
	"mulazim-api/tools"
	"time"
)

/***
 * @Author: [rozimamat]
 * @description: 采集银联商务银行支行数据  获取原始数据
 * @Date: 2023-04-17 13:12:47
 * @param {bool} isOneBank  是否同步某一个银行的数据
 * @param {int} bankId
 * @param {bool} isOneCity  是否同步某一个城市的数据
 * @param {int} cityId
 */
func GetYinShangBankList(isOneBank bool, bankId int, isOneCity bool, cityId int) {

	db := tools.Db

	var collectionBank []map[string]interface{}
	if !isOneBank {
		db.Table("b_self_sign_bank").Where("state = 1").Select("id,name_zh").Scan(&collectionBank)
	} else {
		db.Table("b_self_sign_bank").Where("id = ?", bankId).Select("id,name_zh").Scan(&collectionBank)
	}

	var cities []map[string]interface{}

	if !isOneCity {
		db.Table("b_self_sign_area").Where("level =2 and p_code=65").Select("mlz_city_id,name_zh,code,p_code").Scan(&cities)
	} else {
		db.Table("b_self_sign_area").Where("id = ?", cityId).Select("mlz_city_id,name_zh,code,p_code").Scan(&cities)
	}

	umsService := selfsign.SelfSignFactory("ums")
	for _, city := range cities {

		cityCode := city["code"].(string)

		mlzCityId := tools.ToInt(city["mlz_city_id"])

		for _, v := range collectionBank {
			bankName := v["name_zh"].(string)
			bankId := tools.ToInt(v["id"])

			if strings.Contains(bankName, "合作") { //农村信用合作联社  要走每个县城
				var areas []map[string]interface{}
				if !isOneCity {
					db.Table("b_self_sign_area").Where("p_code = ?", cityCode).Select("mlz_city_id,mlz_area_id,name_zh,code,p_code").Scan(&areas)
				} else {
					db.Table("b_self_sign_area").Where("id = ?", cityId).Select("mlz_city_id,mlz_area_id,name_zh,code,p_code").Scan(&areas)
				}
				for _, area := range areas {

					areaName := area["name_zh"].(string)

					areaCode := area["code"].(string)

					mlzAreaId := tools.ToInt(city["mlz_area_id"])

					fmt.Println("areaName:" + areaName + ",areaCode:" + areaCode + "," + bankName)
					time.Sleep(time.Duration(5) * time.Second)

					banks := umsService.GetBankList(cityCode, bankName)

					fmt.Println(bankName, "banks", banks)

					brData, _ := json.Marshal(banks["branchBankList"])

					bankListData := make([]map[string]interface{}, 0)
					json.Unmarshal(brData, &bankListData)

					WriteBankDate(bankListData, mlzCityId, cityCode, bankId, mlzAreaId)

				}

				continue
			} //

			if bankName != "其他银行" {

				time.Sleep(time.Duration(5) * time.Second)

				banks := umsService.GetBankList(cityCode, bankName)

				fmt.Println(bankName, "banks", banks)

				brData, _ := json.Marshal(banks["branchBankList"])

				bankListData := make([]map[string]interface{}, 0)
				json.Unmarshal(brData, &bankListData)

				WriteBankDate(bankListData, mlzCityId, cityCode, bankId, 0)

			}
		}
	}

}

func WriteBankDate(bankListData []map[string]interface{}, mlzCityId int, cityCode string, bankId int, areaId int) {
	db := tools.Db
	if len(bankListData) > 0 {
		for _, b := range bankListData {
			branchMap := make(map[string]interface{}, 0)
			branchMap["mlz_city_id"] = mlzCityId
			bankBranchName := tools.ToString(b["bankBranchName"])
			bankBranchCode := tools.ToString(b["code"])
			branchMap["name_ug"] = bankBranchName
			branchMap["name_zh"] = bankBranchName
			branchMap["city_code"] = cityCode
			branchMap["bank_id"] = bankId
			branchMap["bank_code"] = bankBranchCode
			if areaId > 0 {
				branchMap["mlz_area_id"] = areaId
			}
			var ct int64
			if areaId > 0 {
				db.Table("b_self_sign_bank_branch").
					Where("mlz_city_id = ? and name_ug = ? and name_zh = ? and bank_code = ? and bank_id=? and mlz_area_id = ? ", mlzCityId, bankBranchName, bankBranchName, bankBranchCode, bankId, areaId).Count(&ct)
			} else {
				db.Table("b_self_sign_bank_branch").
					Where("mlz_city_id = ? and name_ug = ? and name_zh = ? and bank_code = ? and bank_id=? ", mlzCityId, bankBranchName, bankBranchName, bankBranchCode, bankId).Count(&ct)
			}

			if ct == 0 {

				db.Table("b_self_sign_bank_branch").Create(&branchMap)
			}

		}
	}
}
