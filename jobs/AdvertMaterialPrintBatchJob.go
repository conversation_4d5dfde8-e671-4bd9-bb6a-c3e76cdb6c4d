package jobs

import (
	"encoding/json"
	"fmt"
	rmq_client "github.com/apache/rocketmq-clients/golang"
	"mulazim-api/configs"
	"mulazim-api/models"
	"mulazim-api/tools"
	"time"
)

type AdvertMaterialPrintBatchJob struct {
	BaseJob
}

type AdvertMaterialPrintBatchJobData struct {
	BatchId int64 `json:"batch_id"`
}

func (job AdvertMaterialPrintBatchJob) MaxReconnectCount() int32 {
	return 3
}

func (job AdvertMaterialPrintBatchJob) TopicName() string {
	return "advert_material_print_batch"
}

func (job AdvertMaterialPrintBatchJob) GroupId() string {
	return "ADVERT_MATERIAL_PRINT_BATCH"
}

func (job AdvertMaterialPrintBatchJob) MaxRetryCount() int32 {
	return 16
}

func (job *AdvertMaterialPrintBatchJob) HandleMessage(v *rmq_client.MessageView) (bool, int64) {
	var (
		jobData      AdvertMaterialPrintBatchJobData
		batch        models.AdvertMaterialPrintBatch
		lastCode     models.AdvertMaterialCode
		lastInsertId int   = 0
		deleyTime    int64 = 100
		db                 = tools.Db
	)
	err := json.Unmarshal(v.GetBody(), &jobData)
	if err != nil {
		tools.Logger.Error("FATAL AdvertMaterialPrintBatchJobData 消息解析失败", err)
		return true, 0
	}
	fmt.Printf("AdvertMaterialPrintBatchJobStart : %d, time: %s \n", jobData.BatchId, time.Now().Format("2006-01-02 15:04:05"))

	rs := db.Model(&models.AdvertMaterialPrintBatch{}).Where("id = ?", jobData.BatchId).First(&batch)
	if rs.Error != nil {
		msg := fmt.Sprintf("FATAL AdvertMaterialPrintBatchJobData 批次不存在 batch id : %d ", jobData.BatchId)
		tools.Logger.Error(msg)
		return false, deleyTime

	}

	switch batch.State {
	case models.AdvertMaterialPrintBatchStateWait:
		rs = db.Model(&models.AdvertMaterialCode{}).Order("id desc").Find(&lastCode)
		if rs.Error != nil {
			msg := fmt.Sprintf("FATAL AdvertMaterialPrintBatchJobData 查询最后插入记录失败 批次ID : %d ", jobData.BatchId)
			tools.Logger.Error(msg)
			return false, deleyTime
		}
		break
	case models.AdvertMaterialPrintBatchStateGenerating:
		msg := fmt.Sprintf("FATAL AdvertMaterialPrintBatchJobData 批次序号生成中断 批次ID : %d ", jobData.BatchId)
		tools.AliDeveloperDingdingMsg(fmt.Sprintf("宣传材料批次打印中断,批次ID: %d ", jobData.BatchId))
		tools.Logger.Error(msg)
		return true, 0
	case models.AdvertMaterialPrintBatchStateGenerated:
		msg := fmt.Sprintf("FATAL AdvertMaterialPrintBatchJobData 批次已打印完成 批次ID : %d ", jobData.BatchId)
		tools.Logger.Error(msg)
		return true, 0
	default:
		msg := fmt.Sprintf("FATAL AdvertMaterialPrintBatchJobData 批次状态错误 批次ID: %d ", jobData.BatchId)
		tools.Logger.Error(msg)
		return true, 0
	}
	rs = db.Model(&models.AdvertMaterialPrintBatch{}).Where("id = ?", jobData.BatchId).Update("state", models.AdvertMaterialPrintBatchStateGenerating)
	if rs.Error != nil {
		tools.Logger.Error("FATAL AdvertMaterialPrintBatchJobData 更新批次状态失败 batch id : %d ", jobData.BatchId, rs.Error)
		return false, deleyTime
	}
	rs = db.Model(&models.AdvertMaterialCode{}).Order("id desc").Find(&lastCode)
	if lastCode.ID > 0 {
		lastInsertId = lastCode.ID
	}
	var codes []models.AdvertMaterialCode = make([]models.AdvertMaterialCode, 0)
	var startCode, endCode string
	for index := 1; index <= batch.Count; index++ {
		codeNo := fmt.Sprintf("%d", lastInsertId+index)
		if index == 1 {
			startCode = codeNo
		}
		if index == batch.Count {
			endCode = codeNo
		}
		code := models.AdvertMaterialCode{
			AdvertMaterialId:           batch.AdvertMaterialId,
			AdvertMaterialPrintBatchId: batch.ID,
			Code:                       fmt.Sprintf("%s", codeNo),
			QrLink:                     fmt.Sprintf("%s?c=%s", configs.MyApp.AdvertMaterialQrcodeUrl, codeNo),
			State:                      models.AdvertMaterialCodeStateWait,
		}
		codes = append(codes, code)
		if len(codes) >= 100 {
			rs = db.Create(&codes)
			if rs.Error != nil {
				tools.Logger.Error("FATAL AdvertMaterialPrintBatchJobData 插入记录失败 batch id : %d ", jobData.BatchId, rs.Error)
				return false, deleyTime
			}
			codes = make([]models.AdvertMaterialCode, 0)
		}
	}
	if len(codes) > 0 {
		rs = db.Create(&codes)
		if rs.Error != nil {
			tools.Logger.Error("FATAL AdvertMaterialPrintBatchJobData 插入记录失败 batch id : %d ", jobData.BatchId, rs.Error)
			return false, deleyTime
		}
		codes = make([]models.AdvertMaterialCode, 0)
	}
	db.Model(&models.AdvertMaterialPrintBatch{}).Where("id = ?", jobData.BatchId).Updates(map[string]interface{}{
		"state":      models.AdvertMaterialPrintBatchStateGenerated,
		"start_code": startCode,
		"end_code":   endCode,
	})

	fmt.Printf("AdvertMaterialPrintBatchJobEnd : %d time : %s \n", jobData.BatchId, time.Now().Format("2006-01-02 15:04:05"))
	return true, 0
}

// PushMessageToAdvertMaterialPrintBatchJob 发送消息
func PushMessageToAdvertMaterialPrintBatchJob(id int) {
	data := map[string]interface{}{
		"batch_id": id,
	}
	job := NewAdvertMaterialPrintBatchJob()
	job.ProduceMessageToConsumer(data)
}
func NewAdvertMaterialPrintBatchJob() *AdvertMaterialPrintBatchJob {
	job := AdvertMaterialPrintBatchJob{}
	job.job = &job
	return &job
}
