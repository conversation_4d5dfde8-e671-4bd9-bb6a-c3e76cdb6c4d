package jobs

import (

	// "mulazim-api/models"

	"context"
	"encoding/json"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/models"
	"mulazim-api/tools"
	"path/filepath"
	"strings"
	"time"

	rmq_client "github.com/apache/rocketmq-clients/golang"
	"github.com/golang-module/carbon/v2"
	// "github.com/golang-module/carbon/v2"
)

// 拉卡拉退款Job
type FoodImportJob struct {
	BaseJob
}

func (b *FoodImportJob) MaxReconnectCount() int32 {
	return 1
}

func (b *FoodImportJob) TopicName() string {
	return "food_import_topic"
}

func (b *FoodImportJob) GroupId() string {
	return "FOOD_IMPORT_GROUP"
}

func (b *FoodImportJob) MaxRetryCount() int32 {
	return 1
}

type FoodImportData struct {
	Key string `json:"key"`
	ResId     int `json:"res_id"`
	FoodItems []map[string]interface{} `json:"items"`
	FoodTypes map[string]interface{} `json:"food_types"`
	FoodGroups map[string]interface{} `json:"food_groups"`
	FoodLunchBoxes map[string]interface{} `json:"lunch_boxes"`
	FileType string  `json:"file_type"`
	AdminId int `json:"admin_id"`
}

// 消费消息
func (b *FoodImportJob) HandleMessage(v *rmq_client.MessageView) (bool, int64) {

	foodData := FoodImportData{}
	

	err := json.Unmarshal([]byte(v.GetBody()), &foodData)
	if err != nil {
		tools.Logger.Error("foods-import-json-unmarshal-error", err)
		return true, 0
	}
	if foodData.ResId == 0 {
		tools.Logger.Error("foods-import-res-id-error", foodData.ResId)
		return true, 0
	}
	//1.找出分类数据，分组数据 ，遍历items 查询是否存在相同的名称等 然后写入数据  失败的的话 
	////文件处理 状态 0：开始处理 1：处理中 2：处理完成 3：处理失败
	fileProcessKey :=fmt.Sprintf("mass_import_file_process_%s",foodData.Key)
	fileProcessResultKey :=fmt.Sprintf("mass_import_file_process_result_%s",foodData.Key)
	redisHelper := tools.GetRedisHelper()
	db :=tools.GetDB()
	tx  := db.Begin()
	defer  func() { 
		if r := recover(); r != nil { 
			tools.Logger.Error("批量导入美食异常",r)
			tx.Rollback()
		}
	}()

	var restaurant models.Restaurant
	tx.Model(&models.Restaurant{}).Where("id=?",foodData.ResId).Find(&restaurant)
	redisHelper.Set(context.Background(), fileProcessKey, 1, 10*time.Minute)
	redisHelper.Set(context.Background(), fileProcessResultKey, "", 10*time.Minute)
	
	var printer models.RestaurantPrinter
	tx.Model(&models.RestaurantPrinter{}).Where("restaurant_id=? and state = ?",foodData.ResId,1).Find(&printer)

	types :=foodData.FoodTypes
	groups :=foodData.FoodGroups
	lunchBoxes :=foodData.FoodLunchBoxes


	for k, v := range  types{
		mapContent := v.(map[string]interface{})
		var allFood  models.AllFoods
		tx.Model(&models.AllFoods{}).Where("name_zh=?",mapContent["zh"]).First(&allFood)
		if allFood.ID == 0 {
			//分类不存在  ?提示错误 
		}else{
			mapContent["all_foods_id"]=allFood.ID
			types[k]=mapContent
		}
	}
	for k, v := range  groups{
		mapContent := v.(map[string]interface{})
		var foodsGroup  models.FoodsGroup
		tx.Model(&models.FoodsGroup{}).Where("restaurant_id = ? and (name_zh=?  OR name_ug = ?)",foodData.ResId,mapContent["zh"],mapContent["ug"]).First(&foodsGroup)
		if foodsGroup.ID == 0 {
			foodsGroup=models.FoodsGroup{
				NameZh: tools.ToString(mapContent["zh"]),
				NameUg:tools.ToString(mapContent["ug"]),
				RestaurantID:foodData.ResId,
			}
			tx.Create(&foodsGroup)
			mapContent["foods_group_id"]=foodsGroup.ID
			groups[k]=mapContent

		}else{
			mapContent["foods_group_id"]=foodsGroup.ID
			groups[k]=mapContent
		}
		
	}
	for k, v := range  lunchBoxes{
		mapContent := v.(map[string]interface{})
		var foodLunchBox  models.LunchBox
		tx.Model(&models.LunchBox{}).Where("name_zh=?",mapContent["zh"]).First(&foodLunchBox)
		if foodLunchBox.ID == 0 {
			//饭盒不存在  ?创建 ?提示错误 
		}else{
			mapContent["lunch_box_id"]=foodLunchBox.ID
			mapContent["lunch_box_fee"]=foodLunchBox.UnitPrice
			lunchBoxes[k]=mapContent
		}

	}


	var foods []models.RestaurantFoods
	mpPercent :=float64(4.6)
	tx.Model(&models.Area{}).Where("id = ?",restaurant.AreaID).Pluck("mp_percent",&mpPercent)

	dealerPercent :=float64(0) //代理百分比 ?首次加入的怎么算
	now :=carbon.Now(configs.AsiaShanghai)
	
	for _, v := range foodData.FoodItems {
		foodItem :=v
		for kk, vv := range foodItem { 

			value :=tools.ToString(vv)

			if kk == "图片" && len(value) > 0 { //图片
				ext:=filepath.Ext(value)
				fileName :=tools.RandStr(6)+ext
				if len(ext)==0 && !(strings.HasSuffix(value,".jpg") || strings.HasSuffix(value,".jpeg") || strings.HasSuffix(value,".png")) { //图片
					//图片处理
					fileName=fileName+".png"
				}
				uploadDir :=strings.TrimRight(configs.MyApp.UploadRootDir,"/")+"/uploads/foods/"+fmt.Sprintf("%d",foodData.ResId)+now.Format("Ymdhis")+fileName
				uploadUrl :="/uploads/foods/"+fmt.Sprintf("%d",foodData.ResId)+now.Format("Ymdhis")+fileName

				if foodData.FileType == "excel" {
					tools.Logger.Info("图片地址=>>>>>",tools.ToString(foodItem["美食名称中文"]),",", value)
					if !(strings.Contains(value,"http") || strings.Contains(value,"https")) { //上传到cdn
						tools.FileCopy(value,uploadDir)
						foodItem["图片"]=uploadUrl
					}else{
						if !(strings.Contains(value,"almas")||strings.Contains(value,"mulazim")){ //是网络图片但是不是我们的cdn地址 
							//需要下载图片并上传到我们的服务器里面
							tools.DownloadFile(value,uploadDir)
							foodItem["图片"]=uploadUrl
						}
					}
				}else{ //zip
					tools.FileCopy(value,uploadDir)
					foodItem["图片"]=uploadUrl
				}
			}
			
			
			// //分类分组数据要正确处理
			// if kk == "美食分类中文" {
			// 	for _, vvv := range  types{
			// 		foodTypeContent :=vvv.(map[string]interface{})
			// 		if tools.ToString(foodTypeContent["zh"]) == tools.ToString(vv) {
			// 			foodItem["all_foods_id"]=foodTypeContent["all_foods_id"]
			// 		}
			// 	}
			// }

			if kk == "美食分组名称中文" {
				for _, vvv := range  groups{
					foodGroupContent :=vvv.(map[string]interface{})
					if tools.ToString(foodGroupContent["zh"]) == strings.TrimSpace(tools.ToString(vv)) {
						foodItem["foods_group_id"]=foodGroupContent["foods_group_id"]
					}
				}
				
			}
			//开始准备数据
			if kk == "饭盒名称中文" {
				for _, vvv := range  lunchBoxes{
					foodLunchBoxContent :=vvv.(map[string]interface{})
					if tools.ToString(foodLunchBoxContent["food_name_zh"]) == tools.ToString(vv) {
						foodItem["lunch_box_id"]=foodLunchBoxContent["lunch_box_id"]
						foodItem["lunch_box_fee"]=foodLunchBoxContent["lunch_box_fee"]
					}
				}
			}


		}
		isDistribution :=0 //表示是否配送（0表示可配送，1表示不配送，2可配送，可自取）
		if tools.ToString(foodItem["支持配送"]) == "是" && tools.ToString(foodItem["支持自取"]) == "是" {
			isDistribution =2 
		}else if tools.ToString(foodItem["支持配送"]) != "是" && tools.ToString(foodItem["支持自取"]) == "是" {
			isDistribution =1
		}

		dealerPercent = tools.ToFloat64(foodItem["配送百分比"])
		dealerPercent = dealerPercent-mpPercent
		if dealerPercent < 0 { 
			dealerPercent = 0
		}
		selfTakePercent := tools.ToFloat64(foodItem["自取百分比"])
		weight :=tools.ToInt(foodItem["排序"])

		foods = append(foods,models.RestaurantFoods{

			RestaurantID:foodData.ResId,
			// RestaurantPrinterID      :tools.ToString(foodItem[""]),
			AllFoodsID               :tools.ToInt(foodItem["all_foods_id"]),
			// Name                     :tools.ToInt(foodItem[""]),
			NameUg                   :tools.ToString(foodItem["美食名称维吾尔语"]),
			NameZh                   :tools.ToString(foodItem["美食名称中文"]),
			Image                    :tools.ToString(foodItem["图片"]),
			// Description              :tools.ToString(foodItem[""]),
			DescriptionUg            :tools.ToString(foodItem["美食简介中文"]),
			DescriptionZh            :tools.ToString(foodItem["美食简介中文"]),
			BeginTime                :tools.ToString(foodItem["出餐开始时间"]),
			EndTime                  :tools.ToString(foodItem["出餐结束时间"]),
			ReadyTime                :tools.ToInt(foodItem["备餐时间"]),
			IsDistribution           :isDistribution,
			StarAvg                  :"5",
			
			Price                    :uint(tools.ToFloat64(foodItem["价格"])*100),
			MpPercent                :mpPercent,
			DealerPercent            :dealerPercent,
			MpYourselfTakePercent    :selfTakePercent/2,
			DealerYourselfTakePercent:selfTakePercent/2,
			Weight                   :weight,
			LunchBoxID               :tools.ToInt(foodItem["lunch_box_id"]),
			LunchBoxAccommodate      :tools.ToInt(foodItem["每个饭盒美食数量"]),
			LunchBoxFee              :tools.ToInt(foodItem["lunch_box_fee"]),
			State                    :0,
			MinCount                 :tools.ToInt(foodItem["最低购买数量"]),
			FoodQuantity:             tools.ToFloat64(foodItem["food_quantity"]),
			FoodQuantityType:             uint8(tools.ToInt(foodItem["food_quantity_type"])),
			FoodsGroupId:             tools.ToInt(foodItem["foods_group_id"]),
			RestaurantPrinterID :int(printer.ID),

			
		})

	}
	er :=tx.Create(&foods).Error
	if er != nil {
		tx.Rollback()
		tools.Logger.Error("FoodImportJob.PushJobData.Create.Error:",er)
		redisHelper.Set(context.Background(), fileProcessKey, 3, 10*time.Minute)
		redisHelper.Set(context.Background(), fileProcessResultKey, fmt.Sprintf("数据存储失败 %v",err.Error()), 10*time.Minute)
		return true,0
	}
	tx.Commit()
	redisHelper.Set(context.Background(), fileProcessKey, 2, 10*time.Minute)
	redisHelper.Set(context.Background(), fileProcessResultKey, "", 10*time.Minute)

	return true,0
}

func NewFoodImportJob() *FoodImportJob {
	job := FoodImportJob{}
	job.job = &job
	return &job
}


func (b *FoodImportJob) PushJobData(foodData FoodImportData) {
	b.ProduceMessageToConsumer(map[string]interface{}{
		"key":foodData.Key,
		"res_id":      foodData.ResId,
		"items": foodData.FoodItems,
		"food_types": foodData.FoodTypes,
		"food_groups": foodData.FoodGroups,
		"lunch_boxes": foodData.FoodLunchBoxes,
		"admin_id":foodData.AdminId,
		"file_type":foodData.FileType,
	})
}

func SendFoodImportJob(key string,resId int,items []map[string]interface{},adminId int,types map[string]interface{},groups map[string]interface{},lunchBoxes map[string]interface{},fileType string) {
	job := NewFoodImportJob()
	job.PushJobData(FoodImportData{
		Key:key,
		ResId:     resId,
		FoodItems: items,
		AdminId:adminId,
		FoodTypes: types,
		FoodGroups: groups,
		FoodLunchBoxes: lunchBoxes,
		FileType:fileType,
		
		
	})
}
