package jobs

import (
	rmq_client "github.com/apache/rocketmq-clients/golang"
	"mulazim-api/tools"
)

// TemplateJob
//
//	 TemplateJob
//	 @Description: 模板队列，启动队列方式
//	 	job := jobs.NewTemplateJobJob()
//			job.StartConsumer()
//
// 发送消息  	job := jobs.NewSendWechatMiniMessageJob()
//
//	job.ProduceMessageToConsumer(map[string]interface{}{
//		"order_id":requestData.OrderId,
//		"type":requestData.Type,
//	})
type TemplateJob struct {
	BaseJob
}

func (b *TemplateJob) MaxReconnectCount() int32 {
	return 1
}

func (b *TemplateJob) MaxRetryCount() int32 {
	return 999
}

func (b *TemplateJob) TopicName() string {
	return "wechat_send_message"
}

func (b *TemplateJob) GroupId() string {
	return "WECHAT_MESSAGE"
}
func NewTemplateJobJob() *TemplateJob {
	job := TemplateJob{}
	job.job = &job
	return &job
}

func (b *TemplateJob) HandleMessage(v *rmq_client.MessageView) (bool, int64) {
	//超过两次，测不重复发送

	//firstTime := time.Unix(v.FirstConsumeTime/1000, 0)
	//if time.Now().Sub(firstTime).Minutes() > 6 {
	//	tools.Logger.Error("消息已经超过6分钟，不再发送")
	//	return true
	//}
	tools.Logger.Info("开始发送微信订阅消息", v.GetBody())
	return true, 0
}
