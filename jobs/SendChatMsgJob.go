package jobs

import (
	"encoding/json"
	"mulazim-api/models/chat"
	"mulazim-api/tools"
	"strings"
	"time"

	rmq_client "github.com/apache/rocketmq-clients/golang"
)

// SendorderChatDetailJob
//
//	 SendorderChatDetailJob
//	 @Description: 模板队列，启动队列方式
//	 	job := jobs.NewSendorderChatDetailJobJob()
//			job.StartConsumer()
//
// 发送消息  	job := jobs.NewSendWechatMiniMessageJob()
//
//	job.ProduceMessageToConsumer(map[string]interface{}{
//		"order_id":requestData.OrderId,
//		"type":requestData.Type,
//	})
type SendorderChatDetailJob struct {
	BaseJob
}

func (b *SendorderChatDetailJob) MaxReconnectCount() int32 {
	return 1
}

func (b *SendorderChatDetailJob) MaxRetryCount() int32 {
	return 999
}

func (b *SendorderChatDetailJob) TopicName() string {
	return "chat_send_msg_job"
}

func (b *SendorderChatDetailJob) GroupId() string {
	return "CHAT_MESSAGE"
}
func NewSendOrderChatDetailJob() *SendorderChatDetailJob {
	job := SendorderChatDetailJob{}
	job.job = &job
	return &job
}

func (b *SendorderChatDetailJob) HandleMessage(v *rmq_client.MessageView) (bool, int64) {
	orderChatDetail := chat.OrderChatDetail{}
	err := json.Unmarshal(v.GetBody(), &orderChatDetail)
	if err != nil {
		tools.Logger.Error("FATAL 消息解析失败", err)
		return true, 0
	}
	if len(orderChatDetail.OrderSerialNumber) > 0 && strings.Contains(orderChatDetail.OrderSerialNumber, "#") {
		str := strings.Split(orderChatDetail.OrderSerialNumber, "#")
		orderChatDetail.OrderSerialNumber = str[0] + " #" + str[1]
	}
	db := tools.Db
	if err := db.Create(&orderChatDetail).Error; err != nil {
		tools.Logger.Error("FATAL 创建数据失败", err)
		return true, 0
	}
	if orderChatDetail.ContentType == 1 {
		db.Model(&chat.OrderChatJoin{}).
			Where("order_id = ? ", orderChatDetail.OrderID).
			Select("last_content", "updated_at").
			Updates(map[string]interface{}{"last_content": orderChatDetail.Content, "updated_at": time.Now()})
	}

	var response chat.ChatMessage

	response.Id = orderChatDetail.ID
	response.MsgDateTime = orderChatDetail.CreatedAt.Format("2006-01-02 15:04:05")
	response.Rule = orderChatDetail.SenderType
	response.OrderId = orderChatDetail.OrderID

	switch orderChatDetail.ContentType {
	case chat.CHAT_CONTENT_TYPE_TEXT:
		response.Type = "text"
		response.Content = orderChatDetail.Content
	case chat.CHAT_CONTENT_TYPE_IMAGE:
		response.Type = "image"
		response.Content = tools.AddCdn(orderChatDetail.Image)
	case chat.CHAT_CONTENT_TYPE_CARD:
		response.Type = "card"
		response.CardType = orderChatDetail.ContentType
		response.CardContent = orderChatDetail.CardContent
		response.Content = orderChatDetail.CardContent
	case chat.CHAT_CONTENT_TYPE_REPORT:
		response.Type = "report"
		response.CardType = orderChatDetail.ContentType
		response.CardContent = orderChatDetail.CardContent
		response.Content = orderChatDetail.CardContent
	case chat.CHAT_CONTENT_TYPE_ADDRESS:
		response.Type = "address"
		response.CardType = orderChatDetail.ContentType
		response.CardContent = orderChatDetail.CardContent
		response.Content = orderChatDetail.Content
	}
	//发送信息人类型:1,用户:2,商家,3:配送员,4:管理员 5:系统
	switch orderChatDetail.SenderType {
	case 1:
		response.Avatar = tools.GetDefaultUserImage()
	case 2:
		response.Avatar = tools.GetDefaultStoreImage()
	case 3:
		response.Avatar = tools.GetDefaultShipperImage()
	case 4:
		response.Avatar = tools.GetDefaultAdminImage()
	case 5:
		response.Avatar = tools.GetDefaultSystemImage()
	}
	// 聊天室websocket 服务端发送消息
	tools.SendSocketNotify(response)
	tools.GroupMessageCountUpdate(response.OrderId, int(orderChatDetail.SenderType))
	pushJob := NewPushJob()
	pushJob.SendChatPush(&orderChatDetail)
	return true, 0
}
