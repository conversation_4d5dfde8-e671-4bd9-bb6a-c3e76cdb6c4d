package jobs

import (
	"context"
	"encoding/json"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/tools"
	"time"

	rmq_client "github.com/apache/rocketmq-clients/golang"
)

// OrderPushToMerchant1Job
//
//	 OrderPushToMerchant1Job
//	 @Description: 模板队列，启动队列方式
//	 	job := jobs.NewOrderPushToMerchant1Job()
//			job.StartConsumer()
//
// 发送消息  	job := jobs.NewSendWechatMiniMessageJob()
//
//	job.ProduceMessageToConsumer(map[string]interface{}{
//		"order_id":requestData.OrderId,
//		"type":requestData.Type,
//	})
type OrderPushToMerchant1Job struct {
	BaseJob
}

func (b *OrderPushToMerchant1Job) MaxReconnectCount() int32 {
	return 1
}

func (b *OrderPushToMerchant1Job) MaxRetryCount() int32 {
	return 1
}

func (b *OrderPushToMerchant1Job) TopicName() string {
	return "order_push_to_merchant1"
}

func (b *OrderPushToMerchant1Job) GroupId() string {
	return "order_push_to_merchant1_group"
}
func NewOrderPushToMerchant1Job() *OrderPushToMerchant1Job {
	job := OrderPushToMerchant1Job{}
	job.job = &job
	return &job
}

type OrderPushData struct {
	OrderId int `json:"order_id"`
	UserId  int `json:"user_id"`
	StoreId int `json:"store_id"`
	SerialNumber int `json:"serial_number"`
	Type    int   `json:"type"` 
}

func (b *OrderPushToMerchant1Job) HandleMessage(v *rmq_client.MessageView) (bool, int64) {
	//超过两次，测不重复发送

	var pushData OrderPushData
	err := json.Unmarshal([]byte(v.GetBody()), &pushData)
	if err != nil {
		tools.Logger.Error("push job json unmarshal fail", err)
		return true, 0
	}
	logPrefix :=fmt.Sprintf("order_push_%d_用户socket在线队列1_",pushData.OrderId)
	tools.Logger.Info(logPrefix+fmt.Sprintf("队列处理开始 %v", pushData))
	redisHelper :=tools.GetRedisHelper()
	redisKeyPrefix :="order_push_"
	
	
	orderSendKey :=fmt.Sprintf(redisKeyPrefix+"merchant_%d_%d", pushData.OrderId,pushData.UserId)

	exists2, _ := redisHelper.Exists(context.Background(),orderSendKey).Result()
	tools.Logger.Info(logPrefix+fmt.Sprint("orderSendKey=",orderSendKey,"-exists2=",exists2 ))

	send :=true //是否发送请求
	if exists2 != 0 { //商家用户在线
		state :=tools.ToInt(redisHelper.Get(context.Background(),orderSendKey).Val())
		tools.Logger.Info(logPrefix+fmt.Sprint("orderSendKey=",orderSendKey,"-exists2=",exists2 ,"-state=",state))

		if state == constants.MerchantOrderPushReady {
			send =true //是否发送请求
		}else{
			send = false
		}
	}
	tools.Logger.Info(logPrefix+fmt.Sprint("orderSendKey=",orderSendKey,"-exists2=",exists2 ,"-send=",send))
	if send {
		
		job := NewOrderPushToMerchant2Job()
		
		tools.Logger.Info(logPrefix+fmt.Sprint("发送延迟队列消息,orderSendKey=",orderSendKey,"-exists2=",exists2 ,"-send=",send))
		job.ProduceMessageToConsumer(map[string]interface{}{
			"user_id": pushData.UserId,
			"order_id": pushData.OrderId,
			"serial_number": pushData.SerialNumber,
			"store_id": pushData.StoreId,
			"type":     1,
		})
		
		data := make(map[string]interface{})
		data["order_id"]=pushData.OrderId
		data["user_id"]=pushData.UserId
		data["store_id"]=pushData.StoreId
		url :=configs.MyApp.MerchantVoiceUrl+"/order-info"
		tools.Logger.Info(logPrefix+fmt.Sprint("发送socket ,orderSendKey=",orderSendKey,"-exists2=",exists2 ,"-send=",send))
		rtn :=tools.HttpPost(url,data,5*time.Second)
		if len(rtn) == 0 {
			tools.Logger.Error(logPrefix+"voice_socket_发送失败",pushData.OrderId)
			return true,0
		}
		tools.Logger.Info(logPrefix+fmt.Sprint("发送socket 消息成功,orderSendKey=",orderSendKey,"-exists2=",exists2 ,"-send=",send,",socket地址=",url,",socket回复结果:",rtn))
		return true, 0
	}
	tools.Logger.Info(logPrefix+fmt.Sprintf("OrderPushToMerchant1Job --队列处理完毕 %v", pushData))
	return true, 0
}
