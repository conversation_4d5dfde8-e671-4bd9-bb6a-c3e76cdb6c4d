package jobs

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	// "mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/models"
	"mulazim-api/tools"

	// "time"

	rmq_client "github.com/apache/rocketmq-clients/golang"
)

// OrderPushToMerchant2Job
//
//	 OrderPushToMerchant2Job
//	 @Description: 模板队列，启动队列方式
//	 	job := jobs.NewOrderPushToMerchant2Job()
//			job.StartConsumer()
//
// 发送消息  	job := jobs.NewSendWechatMiniMessageJob()
//
//	job.ProduceMessageToConsumer(map[string]interface{}{
//		"order_id":requestData.OrderId,
//		"type":requestData.Type,
//	})
type OrderPushToMerchant2Job struct {
	BaseJob
}

func (b *OrderPushToMerchant2Job) MaxReconnectCount() int32 {
	return 2
}

func (b *OrderPushToMerchant2Job) MaxRetryCount() int32 {
	return 2
}

func (b *OrderPushToMerchant2Job) TopicName() string {
	return "order_push_to_merchant2"
}

func (b *OrderPushToMerchant2Job) GroupId() string {
	return "order_push_to_merchant2_group"
}
func NewOrderPushToMerchant2Job() *OrderPushToMerchant2Job {
	job := OrderPushToMerchant2Job{}
	job.job = &job
	return &job
}

type OrderSendData struct {
	OrderId int `json:"order_id"`
	UserId  int `json:"user_id"`
	StoreId int `json:"store_id"`
	SerialNumber int `json:"serial_number"`
	Type    int   `json:"type"` 
}

func (b *OrderPushToMerchant2Job) HandleMessage(v *rmq_client.MessageView) (bool, int64) {
	
	attempt :=v.GetDeliveryAttempt()
	if attempt == 1 {//第一次执行 强制跳过 要实现 延时15秒
		return false,15
	}
	var pushItem OrderPushData
	err := json.Unmarshal([]byte(v.GetBody()), &pushItem)
	if err != nil {
		tools.Logger.Error("push job json unmarshal fail", err)
		return true, 0
	}
	
	redisHelper :=tools.GetRedisHelper()
	db :=tools.GetDB()
	var ct int64 
	db.Model(&models.OrderToday{}).Where("id = ? and state = ?",pushItem.OrderId,3).Count(&ct)
	if ct == 0 { //订单已经被接收
		return true,0
	}

	redisKeyPrefix :="order_push_"
	logPrefix :=fmt.Sprintf("order_push_%d_延迟队列",pushItem.OrderId)
	sendDuration :=60*time.Second
 

	
	orderSendKey :=fmt.Sprintf(redisKeyPrefix+"merchant_%d_%d", pushItem.OrderId,pushItem.UserId)

	exists2, _ := redisHelper.Exists(context.Background(),orderSendKey).Result()
	tools.Logger.Info(logPrefix+fmt.Sprint("orderSendKey=",orderSendKey,"-exists2=",exists2 ))
	if exists2 != 0 {
		state :=tools.ToInt(redisHelper.Get(context.Background(),orderSendKey).Val())
		tools.Logger.Info(logPrefix+fmt.Sprint("orderSendKey=",orderSendKey,"-exists2=",exists2 ),"-state=",state)
		if state == constants.MerchantOrderPushSendComplete {
			//推送完成了 该做什么 
			//
			return true,0
		}
	}
	//通过jpush推送
	//通知 结束不用继续 推送
	// tools.Logger.Info(logPrefix+"OrderPushToMerchant2Job_send_jiguang",pushItem.OrderId)
	redisHelper.Set(context.Background(),orderSendKey,constants.MerchantOrderPushSendComplete,sendDuration)
	tools.Logger.Info(logPrefix+fmt.Sprint("-延迟队列socket不在线，或者未收到应答，要发送激光推送 set orderSendKey=",orderSendKey,"-exists2=",exists2 ))
	job := NewPushJob()
	// subTitle := string(order.SerialNumber)
	job.PushData(PushData{
		UserId:   pushItem.StoreId,
		UserType: constants.PushUserTypeMerchant,
		PushContent: PushContent{
			TitleUg:   constants.TitleMerchantNewOrderUg,
			TitleZh:   constants.TitleMerchantNewOrderZh,
			ContentUg: constants.ContentMerchantNewOrderUg,
			ContentZh: constants.ContentMerchantNewOrderZh,
			Params:    map[string]interface{}{},
		},
		Client: models.PushDeviceClientMerchant,
		Sound:  constants.SoundMerchantNewOrder,
		ChannelType: constants.ChannelTypeMerchantOrder,
		AdminId:pushItem.UserId,
	})

	
	tools.Logger.Info(fmt.Sprintf("OrderPushToMerchant2Job --队列处理完毕 %v", pushItem))
	return true, 0
}
