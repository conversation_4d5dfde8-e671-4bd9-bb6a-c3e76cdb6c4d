package jobs

import (
	"encoding/json"
	"mulazim-api/configs"

	// "mulazim-api/models"

	// "mulazim-api/models"
	"mulazim-api/services/merchant/lakala"
	"mulazim-api/tools"
	"time"

	rmq_client "github.com/apache/rocketmq-clients/golang"
	// "github.com/golang-module/carbon/v2"
)

// 拉卡拉退款Job
type LakalaQueryJob struct {
	BaseJob
}

func (b *LakalaQueryJob) MaxReconnectCount() int32 {
	return 3
}

func (b *LakalaQueryJob) TopicName() string {
	return "lakala_query_topic"
}

func (b *LakalaQueryJob) GroupId() string {
	return "LAKALA_QUERY_GROUP"
}

func (b *LakalaQueryJob) MaxRetryCount() int32 {
	return 8
}

type LakalaQueryData struct {
	OrderId     int64 `form:"order_id" json:"order_id"`
	LakalaPayId int64 `form:"lakala_pay_id" json:"lakala_pay_id"`
	OutOrderNo	string `form:"out_order_no" json:"out_order_no"`
	PaySource	string `form:"pay_source" json:"pay_source"`
}

// 消费消息
//
// 返回： 结果、 延迟单位：如果失败会进入延迟执行 单位： 秒
func (b *LakalaQueryJob) HandleMessage(v *rmq_client.MessageView) (bool, int64) {

	
	
	lakalaQueryData := LakalaQueryData{}
	// lakalaMap := make(map[string]interface{})
	

	err := json.Unmarshal([]byte(v.GetBody()), &lakalaQueryData)
	if err != nil {
		tools.Logger.Error("lakala-query-job-json-unmarshal-error", err)
		return true, 0
	}
	logPrefix :="lakala_query_job_"
	attempt :=v.GetDeliveryAttempt()
	delayTimes :=[]int64{0,2*60,3*60,4*60,10*60,15*60,30*60,60*60,120*60}
	//第一次进入后 3分钟后 开始查询
	if attempt > 8 {
		tools.Logger.Info(logPrefix+"支付未完成,尝试8次后退出",lakalaQueryData.LakalaPayId)
		return true,0
	}

	if attempt == 1 {//第一次执行 强制跳过 要实现 延时3*60秒  
		return false,delayTimes[attempt]
	}

	db :=tools.GetDB()

	qr,err:=lakala.GetLakalaService().QueryLakala(lakalaQueryData.OutOrderNo)
	if err != nil {
		tools.Logger.Info(logPrefix+"支付查询错误",err)
		return false, delayTimes[attempt]
	}
	//处理查询结果
	if qr.OrderStatus == 1001 || qr.OrderStatus == 1002 { //未支付   进行三次 重试
		tools.Logger.Info(logPrefix+"未支付",lakalaQueryData.LakalaPayId)
		return false,delayTimes[attempt]
	}else if qr.OrderStatus == 2003 { //已退款  跟本地数据进行比对 进行退款时的 操作 
		tools.Logger.Info(logPrefix+"已退款",lakalaQueryData.LakalaPayId)
		payLakalaMap :=make(map[string]interface{})	
		db.Table("t_pay_lakala").Where("id = ?",lakalaQueryData.LakalaPayId).Scan(&payLakalaMap)
		if payLakalaMap != nil && tools.ToInt(payLakalaMap["order_status"]) != qr.OrderStatus { //退款状态不一致 
			tools.Logger.Info(logPrefix+"退款状态不一致",lakalaQueryData.LakalaPayId)
		}
	}else if qr.OrderStatus == 1006 { //已支付  跟本地数据进行比对 进行支付时的 操作 
		tools.Logger.Info(logPrefix+"完成支付",lakalaQueryData.LakalaPayId)
			
		
		
		payStatus :=0
		paySeqNo :=""
		channelTradeNo :=""
		splitSeqNo :=""
		channelSeqNo :=""
		payChannelTradeNo :=""
		thirdPartyPayment :=""
		payTime :=""

		for _, v := range qr.PayList {
			payStatus = v.PayStatus	
			paySeqNo = v.PaySeqNo
			channelTradeNo = v.ChannelTradeNo
			channelSeqNo = v.ChannelSeqNo
			payChannelTradeNo = v.PayChannelTradeNo
			thirdPartyPayment = v.ThirdPartyPayment
			payTime = v.PayTime
			
			break
		}
		splitRuleResult :=make([]map[string]interface{},0)
		for _, v := range qr.SplitList {
			splitSeqNo =v.SplitSeqNo
			splitRuleResult = append(splitRuleResult,map[string]interface{}{
				"amount":v.Amount,
				"member_no":v.MemberIdNo,
			})
		}
		
		payLakalaMap :=make(map[string]interface{})	
		db.Table(lakalaQueryData.PaySource).Where("id = ?",lakalaQueryData.LakalaPayId).Scan(&payLakalaMap)
		if payLakalaMap != nil && (tools.ToInt(payLakalaMap["order_status"]) != qr.OrderStatus || tools.ToInt(payLakalaMap["pay_status"]) != payStatus) { //支付状态不一致 需要处理 接收支付通知一样的结果
			tools.Logger.Info(logPrefix+"支付状态不一致,开始执行支付通知逻辑",lakalaQueryData.LakalaPayId)
			data :=map[string]interface{}{
				"out_order_no":qr.OutOrderNo,
				"order_no":qr.OrderNo,
				"pay_status":payStatus,
				"order_status":qr.OrderStatus,
				"pay_seq_no":paySeqNo,
				"split_seq_no":splitSeqNo,
				"split_rule_result":splitRuleResult,
				"channel_trade_no":channelTradeNo,
				"channel_seq_no":channelSeqNo,
				"pay_channel_trade_no":payChannelTradeNo,
				"third_party_payment":thirdPartyPayment,
				"pay_time":payTime,
				"status":"OK",
				"notification_type":"local",//手动发送标志
				
			}
			// configs.MyApp.SwooleUrl
			url :=configs.MyApp.SwooleUrl+"/ug/v1/payment/lakala/pay/notify" //客户或配送员支付 	
			tools.AliDeveloperDingdingMsg("拉卡拉订单状态不同步,拉卡拉状态支付完成,数据库状态不对,t_pay_lakala:id:"+tools.ToString(lakalaQueryData.LakalaPayId))
			tools.Logger.Info(logPrefix+"手动发送拉卡拉通知"+url)
			rtn :=tools.HttpPost(url,data,5*time.Second)
			if len(rtn) == 0 {
				tools.Logger.Error(logPrefix+"发送失败",lakalaQueryData.OrderId)
				return true,delayTimes[attempt]
			}
			tools.Logger.Error(logPrefix+"发送结果,",rtn)
		}
		
		
	}
	
	return true,0
}

func NewLakalaQueryJob() *LakalaQueryJob {
	job := LakalaQueryJob{}
	job.job = &job
	return &job
}


func (b *LakalaQueryJob) PushJobData(lakalaQueryData LakalaQueryData) {
	b.ProduceMessageToConsumer(map[string]interface{}{
		"order_id":      lakalaQueryData.OrderId,
		"lakala_pay_id": lakalaQueryData.LakalaPayId,
		"out_order_no":  lakalaQueryData.OutOrderNo,
		"pay_source":lakalaQueryData.PaySource,
	})
}

func SendLakalaQueryJob(id int,orderId int,outOrderNo string,paySource string) {
	job := NewLakalaQueryJob()
	job.PushJobData(LakalaQueryData{
		OrderId:     int64(orderId),
		LakalaPayId: int64(id),
		OutOrderNo:  outOrderNo,
		PaySource: paySource,
	})
}
