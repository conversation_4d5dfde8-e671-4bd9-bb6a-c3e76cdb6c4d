package jobs

import (
	"context"
	"encoding/json"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/models"
	"strings"

	// "mulazim-api/constants"
	"mulazim-api/tools"
	"time"

	rmq_client "github.com/apache/rocketmq-clients/golang"
	"github.com/golang-module/carbon/v2"
	// "github.com/golang-module/carbon/v2"
	// "gorm.io/gorm"
)

// OrderPushToShipper1Job
//
//	 OrderPushToShipper1Job
//	 @Description: 模板队列，启动队列方式
//	 	job := jobs.NewOrderPushToShipper1Job()
//			job.StartConsumer()
//
// 发送消息  	job := jobs.NewSendWechatMiniMessageJob()
//
//	job.ProduceMessageToConsumer(map[string]interface{}{
//		"order_id":requestData.OrderId,
//		"type":requestData.Type,
//	})
type OrderPushToShipper1Job struct {
	BaseJob
}

func (b *OrderPushToShipper1Job) MaxReconnectCount() int32 {
	return 1
}

func (b *OrderPushToShipper1Job) MaxRetryCount() int32 {
	return 2
}

func (b *OrderPushToShipper1Job) TopicName() string {
	return "order_push_to_shipper1"
}

func (b *OrderPushToShipper1Job) GroupId() string {
	return "order_push_to_shipper1_group"
}
func NewOrderPushToShipper1Job() *OrderPushToShipper1Job {
	job := OrderPushToShipper1Job{}
	job.job = &job
	return &job
}

type OrderShipperPushData struct {
	OrderId int `json:"order_id"`
	UserId  int `json:"user_id"`
	SerialNumber int `json:"serial_number"`
	Type    int   `json:"type"` 
	Content string   `json:"content"` 
}

func (b *OrderPushToShipper1Job) HandleMessage(v *rmq_client.MessageView) (bool, int64) {
	

	var pushData OrderShipperPushData
	err := json.Unmarshal([]byte(v.GetBody()), &pushData)
	if err != nil {
		tools.Logger.Error("order_push_push job json unmarshal fail", err)
		return true, 0
	}

	var order models.OrderToday
	tools.GetDB().Model(&order).Where("id =? and state >? and state < ?", pushData.OrderId,3,7).Preload("Area").First(&order)
	if order.ID == 0 {
		tools.Logger.Error("order_push_push job 订单不存在", pushData.OrderId)
		return true, 0
	}
	// 过滤压单中的订单
	if order.Area.AutoDispatchState == 1 {
		nowTime := carbon.Now();
		waitEndTime := carbon.Parse(order.PrintedTime).AddSeconds(order.Area.AutoDispatchWait*60)
		diffSeconds := nowTime.DiffAbsInSeconds(waitEndTime)
		// 压单时间还没结束，压单时间结束后发送 通知
		if diffSeconds > 0 && order.ShipperID == 0 {
			return false, int64(tools.ToInt(diffSeconds))
		}
	}

	logPrefix :=fmt.Sprintf("order_push_%d_用户socket在线队列1_",pushData.UserId)
	tools.Logger.Info(logPrefix+fmt.Sprintf("队列处理开始 %v", pushData))
	redisHelper :=tools.GetRedisHelper()
	redisKeyPrefix :="order_push_"
	sendDuration :=time.Duration(20) * time.Second
	
	orderId :=pushData.OrderId
	shipperId :=pushData.UserId


	onlineKey :=fmt.Sprintf(redisKeyPrefix+"online_%d",shipperId)
			
	exists1, _ := redisHelper.Exists(context.Background(),onlineKey).Result()
	tools.Logger.Info(logPrefix,"-给配送员分配订单推送 order_id:",orderId,",shipper_id:",shipperId,",-exists2=",exists1,",onlineKey=",onlineKey)
	// exists1 = 1 //强制设置 设备在线
	if exists1 == 0 { //配送员 用户 不在线  跳过 
		tools.Logger.Info(logPrefix+"-shipper socket 不在线 跳过 ",onlineKey)
		return true,0
	}
	
	//同类型的通知 一分钟值发送一次
	orderSendBefore :=fmt.Sprintf(redisKeyPrefix+"shipper_pushed_%d_%d", pushData.UserId,pushData.Type)
	exists3, _ := redisHelper.Exists(context.Background(),orderSendBefore).Result()
	if exists3 != 0 && pushData.Type == constants.SoundShipperSocketTypeNewOrder{ //新订单推送 一分钟内不会重复发送
		//一分钟内有推送记录 要跳过
		tools.Logger.Info(logPrefix+"-shipper socket 推送 跳过 一分钟内的 同类型 通知:",orderSendBefore)
		return true,0
	}

	redisHelper.Set(context.Background(),orderSendBefore,1,sendDuration)
	
	if pushData.Type == constants.SoundShipperSocketTypeSystemAssignOrder{ 
		tools.Logger.Info("系统分配订单推送",pushData)
		redisHelper :=tools.GetRedisHelper()
		db :=tools.GetDB()
		var pushDevice models.PushDevice
		db.Model(&models.PushDevice{}).Where("`user_id` =? and `user_type` =? and `client` =?",pushData.UserId,constants.PushUserTypeShipper,"shipper").Find(&pushDevice)
		//订单属于的店铺有几个人登录就给他发送几个通知
		
		voiceUrl := "system_send_order_voice_url_"+pushDevice.Lang
		var printVoiceConfig models.AppConfig
		db.Model(&models.AppConfig{}).Where("`key` = ? and state = ?",voiceUrl,1).Find(&printVoiceConfig)
		printVoiceUrl := ""
		sendVoiceSocket := false
		
		printVoiceMinVersion := 339
		tools.Logger.Info("printVoiceMinVersion：", printVoiceConfig.ID > 0 , printVoiceConfig.Value != "" , printVoiceConfig.State == 1)
		if printVoiceConfig.ID > 0 && printVoiceConfig.Value != "" && printVoiceConfig.State == 1 {

			printVoiceMinVersion = tools.ToInt(strings.Replace(printVoiceConfig.Version,".","",-1))
			if len(printVoiceConfig.Value) > 0 && printVoiceMinVersion > 0 {
				sendVoiceSocket = true
				printVoiceUrl = printVoiceConfig.Value
			}
		}
		//监测 该订单的 推送历史 
		//onlineKey = "order_push_online_"+"店铺id"+"_"+"登录用户id"
		onlineKey :=fmt.Sprintf("order_push_online_%d",pushData.UserId)
		exists2, _ := redisHelper.Exists(context.Background(),onlineKey).Result()
		tools.Logger.Info(logPrefix,"-扫描所有的订单-筛选需要推送的订单-订单存在推送历史",order.ID,"-exists2=",exists2,"-send=","onlineKey=",onlineKey)
		// exists2 = 1 //强制设置 设备在线
		if exists2 != 0 { //商家用户在线
			//加入推送队列 
			if sendVoiceSocket && tools.ToInt( pushDevice.DeviceAppVersion) >= printVoiceMinVersion { //后台发送到店铺语音 客户端版本大于等于 配置的客户端版本
				data := make(map[string]interface{})
				data["order_id"]=pushData.OrderId
				data["user_id"]=pushData.UserId
				data["store_id"]=order.StoreID
				data["voice_url"]= printVoiceUrl
				url :=configs.MyApp.MerchantVoiceUrl+"/custom-info-shipper"
				rtn :=tools.HttpPost(url,data,5*time.Second)
				if len(rtn) == 0 {
					tools.Logger.Error(logPrefix+"系统分配-voice_socket_发送失败",order.ID)
					return true,0 

				}
				tools.Logger.Error(logPrefix+"系统分配-voice_socket_发送成功",order.ID)
				return true,0
			}
		}

	}


	data := make(map[string]interface{})
	data["order_id"]=pushData.OrderId
	data["user_id"]=pushData.UserId
	data["type"]=pushData.Type
	data["content"]=pushData.Content
	url :=configs.MyApp.ShipperVoiceUrl+"/shipper-order-change"
	tools.Logger.Info(logPrefix+fmt.Sprint(" shipper 发送socket ,orderSendKey=",orderSendBefore,",type:",pushData.Type,"-exists3=",exists3))
	rtn :=tools.HttpPost(url,data,5*time.Second)
	if len(rtn) == 0 {
		tools.Logger.Error(logPrefix+"voice_socket_发送失败",pushData.OrderId)
		return true,0
	}
	tools.Logger.Info(logPrefix+fmt.Sprint("shipper 发送socket 消息成功,orderSendKey=",orderSendBefore,"-exists2=",exists3 ,",socket地址=",url,",socket回复结果:",rtn))
	return true, 0
	
}
