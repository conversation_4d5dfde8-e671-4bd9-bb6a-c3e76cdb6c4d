package mygin

import (
	"io/ioutil"
	"mulazim-api/configs"
	"mulazim-api/inits"
	"mulazim-api/middlewares"
	"mulazim-api/routes"
	"os"
	"time"

	"github.com/gin-gonic/gin"
)

func InitGinWeb() *gin.Engine {

	configPath := inits.ConfigFilePath + "./configs"
	os.Setenv("ZONEINFO", configPath+"/data.zip")
	var cstZone = time.FixedZone("CST", 8*3600) // 东八
	time.Local = cstZone

	r := gin.New()
	if configs.CurrentEnvironment == "production" {
		gin.SetMode(gin.ReleaseMode)
		gin.DefaultWriter = ioutil.Discard
		r.Use(gin.CustomRecovery(func(c *gin.Context, err interface{}) {
			c.JSON(403, gin.H{
				"state": "403",
				"msg":   "出错了",
			})
		}))
	} else {
		gin.SetMode(gin.ReleaseMode)
		gin.DefaultWriter = ioutil.Discard
	}
	r.Use(middlewares.LangMiddleware())
	r.Use(gin.Logger())

	r.Use(middlewares.VildatorRecovery())
	r.Use(middlewares.MonitorMiddleware())


	// 记录日志(追加记录)
	routes.InitLangRoute(r)
	return r
}
