#!/bin/sh
set -e

cp -f config.json.example config.json
sed -i "s|\"mysql_dsn\":\".*\",|\"mysql_dsn\":\"${MYSQL_DSN}\",|" config.json
sed -i "s@\"environment\":\".*\",@\"environment\":\"dev\",@" config.json
sed -i "s@\"redis_ip\":\".*\",@\"redis_ip\":\"$REDIS_IP\",@" config.json
sed -i "s@\"redis_password\":\".*\",@\"redis_password\":\"$REDIS_PASS\",@" config.json
go mod tidy && go mod vendor

echo done

echo "$@"

exec "$@"