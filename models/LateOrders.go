package models

import (
	"mulazim-api/models/shipment"
	"time"
)

type LateOrders struct {
	ID            int    `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CityID        int    `gorm:"column:city_id;"`
	AreaID        int    `gorm:"column:area_id;"`
	ShipperID     int    `gorm:"column:shipper_id;"`
	OrderID       int    `gorm:"column:order_id;"`
	Shipment      int    `gorm:"column:shipment;"`
	BookingTime   string `gorm:"column:booking_time;"`
	DeliveredTime string `gorm:"column:delivered_time;"`
	LateMinute    int    `gorm:"column:late_minute;"`

	CreatedAt time.Time `gorm:"column:created_at"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"` // 修改时间

	PushDetail    ShipperOrderPushDetail   `gorm:"foreignkey:order_id;references:order_id"`
	ShipperIncome []shipment.ShipperIncome `gorm:"foreignkey:order_id;references:order_id"`
}

func (m *LateOrders) TableName() string {
	return "t_shipper_late_orders"
}
