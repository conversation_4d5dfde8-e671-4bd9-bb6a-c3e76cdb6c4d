/*
*
@author: captain
@since: 2022/9/12
@desc:
*
*/
package models

// Code generated by sql2gorm. DO NOT EDIT.

import (
	"mulazim-api/models/cms"
	"time"
)

type OrderStateLog struct {
	ID              int                 `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	OrderID         int                 `gorm:"column:order_id;NOT NULL"`        // 订单编号
	OrderStateID    int                 `gorm:"column:order_state_id;NOT NULL"`  // 状态编号
	FailReason      int                 `gorm:"column:fail_reason;default:null"` // 失败原因
	State           int                 `gorm:"column:state;default:1"`
	CreatedAt       time.Time           `gorm:"column:created_at"` // 创建时间
	UpdatedAt       time.Time           `gorm:"column:updated_at"`
	DeletedAt       time.Time           `gorm:"column:deleted_at;default:null"`
	OrderFailReason cms.OrderFailReason `gorm:"foreignkey:order_id;association_foreignkey:order_id"`
}

func (m *OrderStateLog) TableName() string {
	return "t_order_state_log"
}
