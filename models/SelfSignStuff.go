package models

import (
	"time"
)

type SelfSignStuff struct {
	Id                     int        `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	Type                   int        `gorm:"column:type"`                     // t_self_sign_merchant_info 表的 id
	MerInfoId              int        `gorm:"column:mer_info_id"`              // t_self_sign_merchant_info 表的 id
	StaffName              string     `gorm:"column:staff_name"`               // 员工姓名
	StaffIdCard            string     `gorm:"column:staff_idcard"`             // 员工姓名
	HealthCertificateStart *time.Time `gorm:"column:health_certificate_start"` // 健康证起效日期
	HealthCertificateEnd   *time.Time `gorm:"column:health_certificate_end"`   // 健康证失效日期
	HealthCertificateImage string     `gorm:"column:health_certificate_image"` // 健康证证件地址
	CreatedAt              time.Time  `gorm:"column:created_at"`               // 创建时间
	UpdatedAt              time.Time  `gorm:"column:updated_at"`               // 更新时间
	DeletedAt              *time.Time `gorm:"column:deleted_at"`               // 删除时间
}

func (m *SelfSignStuff) TableName() string {
	return "t_self_sign_stuff"
}
