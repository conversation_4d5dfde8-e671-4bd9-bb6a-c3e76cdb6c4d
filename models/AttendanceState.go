package models

import (
	"time"
)

type AttendanceState struct {
	Id        int32     `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL"`
	Name 	  string    `gorm:"column:name;NOT NULL"`
	NameUg    string    `gorm:"column:name_ug;NOT NULL;comment:'状态维文名称'"`
	NameZh    string    `gorm:"column:name_zh;NOT NULL;comment:'状态中文名称'"`
	NextState string    `gorm:"column:next_state;default:NULL;comment:'可选中的状态'"`
	CreatedAt time.Time `gorm:"column:created_at;NOT NULL;comment:'创建时间'"`
	UpdatedAt time.Time `gorm:"column:updated_at;default:NULL;comment:'修改时间'"`
	DeletedAt time.Time `gorm:"column:deleted_at;default:NULL;comment:'删除时间'"`
}

func (b *AttendanceState) TableName() string {
	return "b_attendance_state"
}