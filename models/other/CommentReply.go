package Other

import (
	"gorm.io/gorm"
	"time"
)

type CommentReply struct {
	ID        int       `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	CommentID int       `gorm:"column:comment_id;NOT NULL" json:"comment_id"`     // 评论ID
	Type      int       `gorm:"column:type;default:1;NOT NULL" json:"type"` // 评论类型 1 商家回复  2 平台回复
	Text      string    `gorm:"column:text" json:"text"`                    // 评论内容
	AdminID   int       `gorm:"column:admin_id;NOT NULL" json:"admin_id"`       // 评论的管理员
	CreatedAt time.Time `gorm:"column:created_at;type:time" json:"created_at"`              // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at"`              // 修改时间
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at" json:"-"`              // 删除时间
}
func (m *CommentReply) TableName() string {
	return "t_comment_reply"
}
