package Other

import (
	"gorm.io/gorm"
	"time"
)


// 商家头像信息审核
type MerchantProcess struct {
	ID             int       `gorm:"column:id;AUTO_INCREMENT;primary_key"`
	Type           int       `gorm:"column:type"`            // 1:头像,2:营业执照,3:许可证,4:餐厅照片
	CityID         int       `gorm:"column:city_id"`         // 城市编号
	AreaID         int       `gorm:"column:area_id"`         // 区域编号
	RestaurantID   int       `gorm:"column:restaurant_id"`   // 关联商家id
	RestaurantName string    `gorm:"column:restaurant_name"` // 商户名称
	OldValue       string    `gorm:"column:old_value"`       // 源数据
	NewValue       string    `gorm:"column:new_value"`       // 新数据
	State          int       `gorm:"column:state"`           // 状态（0审核不通过，1审核中，2审核通过）
	CreatedAt      time.Time `gorm:"column:created_at"`      // 创建时间
	UpdatedAt      time.Time `gorm:"column:updated_at"`      // 修改时间
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at"`      // 删除时间
}

func (t *MerchantProcess) TableName() string {
	return "t_merchant_process"
}