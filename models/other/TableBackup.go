package Other

import (
	"gorm.io/gorm"
	"time"
)

// 数据库表备份
type TableBackup struct {
	ID        int       `gorm:"column:id;AUTO_INCREMENT;primary_key"`
	TaskName  string    `gorm:"column:task_name"`           // 任务名称
	FromTable string    `gorm:"column:from_table;NOT NULL"` // 备份源
	ToTable   string    `gorm:"column:to_table;NOT NULL"`   // 备份处
	KeepDay   int       `gorm:"column:keep_day"`            // 备份源保留数据天数
	State     int       `gorm:"column:state;NOT NULL"`      // 是否运行
	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`
	DeletedAt time.Time `gorm:"column:deleted_at"`
}

func (m *TableBackup) TableName() string {
	return "table_backup"
}

// 任务执行记录
type TableBackupLog struct {
	ID            int       `gorm:"column:id;AUTO_INCREMENT;primary_key"`
	TableBackupID int       `gorm:"column:table_backup_id"` // 任务ID
	State         int       `gorm:"column:state"`           // 任务执行结果
	Desc          string    `gorm:"column:desc"`            // 执行结果说明
	CreatedAt     time.Time `gorm:"column:created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at"`
}

func (m *TableBackupLog) TableName() string {
	return "table_backup_log"
}
