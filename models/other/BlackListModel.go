package Other

import "time"
type TAppBlacklist struct {
	ID int64 `json:"id" gorm:"id"`
	AppName string `json:"app_name" gorm:"app_name"` // 应用名称
	PackageName string `json:"package_name" gorm:"package_name"` // 包名
	Platform string `json:"platform" gorm:"platform"` // 操作系统 android,ios
	State int8 `json:"state" gorm:"state"` // 状态: 1 开启 2 关闭
	CreatedAt time.Time `json:"created_at" gorm:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at" gorm:"updated_at"` // 修改时间
	DeletedAt time.Time `json:"deleted_at" gorm:"deleted_at"` // 删除时间
}

// TableName 表名称
func (*TAppBlacklist) TableName() string {
    return "t_app_blacklist"
}

type BlackListApp struct {
	ID 				int64 	`json:"id"`
	AppName 		string 	`json:"app_name"`
	PackageName 	string 	`json:"package_name"`
	Platform 		string	`json:"platform"`
}