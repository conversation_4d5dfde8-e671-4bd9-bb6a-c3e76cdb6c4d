package Other
import (
	"time"
)

// 商家资质证书图片信息表
type RestaurantLicense struct {
	ID            int       `gorm:"column:id;AUTO_INCREMENT;primary_key"` // 自增编号
	RestaurantID  int       `gorm:"column:restaurant_id;NOT NULL"`        // 餐厅编号
	LicenseTypeID int       `gorm:"column:license_type_id;NOT NULL"`      // 执照类型编号
	ImageUrl      string    `gorm:"column:image_url"`                     // 证件图片地址
	ExpiredTime   time.Time `gorm:"column:expired_time"`                  // 证件到期时间
	State         int       `gorm:"column:state"`                         // 证件状态（0表示待审核，1表示审核通过）
	CreatedAt     time.Time `gorm:"column:created_at"`                    // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at"`                    // 修改时间
	DeletedAt     time.Time `gorm:"column:deleted_at"`                    // 删除时间
}

func (m *RestaurantLicense) TableName() string {
	return "b_restaurant_license"
}