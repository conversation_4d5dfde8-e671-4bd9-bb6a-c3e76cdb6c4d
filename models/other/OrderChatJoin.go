package Other

import (
	"mulazim-api/models"
	"mulazim-api/models/chat"
	"time"

	"gorm.io/gorm"
)

// db.Set("gorm:table_options", "ENGINE=InnoDB COMMENT='每个订单推送的配送员'").AutoMigrate(&models.ShipperOrderPushDetail{})
// 区县信息表
type OrderChatJoin struct {
	ID          int64      `gorm:"column:id;COMMENT:自增ID;primary_key;AUTO_INCREMENT"`                                   // 自增编号
	CityID      int64      `gorm:"column:city_id;NOT NULL;COMMENT:城市编号;index"`                                          // 城市编号
	AreaID      int64      `gorm:"column:area_id;NOT NULL;COMMENT:区域编号;index"`                                          // 区域编号
	UserType    int8       `gorm:"column:user_type;NOT NULL;COMMENT:参加房间人类型:1,用户:2,商家,3:配送员,4:管理员;index,5:系统管理员;index"` // 发送信息人类型
	UserID      int64      `gorm:"column:user_id;NOT NULL;COMMENT:参加房间人ID;index"`                                       // 订单编号
	OrderID     int64      `gorm:"column:order_id;NOT NULL;COMMENT:订单编号;index"`                                         // 订单编号
	ResID       int64      `gorm:"column:res_id;NOT NULL;COMMENT:订单编号;index"`                                           // 订单编号
	LastContent string     `gorm:"column:last_content;COMMENT:最后的信息内容"`                                                 // 信息内容
	CreatedAt   time.Time  `gorm:"column:created_at;COMMENT:创建时间;index"`                                                // 创建时间
	UpdatedAt   time.Time  `gorm:"column:updated_at;COMMENT:修改时间;index"`                                                // 修改时间
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;COMMENT:删除时间"`                                                      // 删除时间
	OrderSerialNumber string     `gorm:"column:order_serial_number"`                                                 // 订单序列号
	OrderNo string     `gorm:"column:order_no"`    
	ResNameUg string `gorm:"column:res_name_ug"`    
	ResNameZh string 	`gorm:"column:res_name_zh"`    
	CustomerPhone	string `gorm:"column:customer_phone"`    
	Restaurant models.Restaurant   `gorm:"foreignkey:res_id;references:id"`
	ChatDetail chat.OrderChatDetail   `gorm:"foreignkey:order_id;references:order_id"`
	 
}

func (m *OrderChatJoin) TableName() string {
	return "t_order_chat_join"
}
