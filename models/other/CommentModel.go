package Other

import (
	"database/sql"
	"gorm.io/gorm"
	"mulazim-api/models"
	"time"
)

type CommentModel struct {
	ID            int            `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id,omitempty"`
	UserID        int            `gorm:"column:user_id;NOT NULL" json:"user_id,omitempty"`           // 用户ID
	OrderID       int            `gorm:"column:order_id;NOT NULL" json:"order_id,omitempty"`         // 订单编号
	Type          int            `gorm:"column:type;default:1;NOT NULL" json:"type,omitempty"`       // 评论类型 1 配送员 2 美食
	OrderDetailID int            `gorm:"column:order_detail_id" json:"order_detail_id,omitempty"`    // 订单详细中的ID
	FoodID        int            `gorm:"column:food_id" json:"food_id,omitempty"`                    // 美食ID
	ShipperID     int            `gorm:"column:shipper_id" json:"shipper_id,omitempty"`              // 配送员ID
	Star          uint           `gorm:"column:star;default:5;NOT NULL" json:"star,omitempty"`       // 评论星数 1-5
	FoodStar      uint           `gorm:"column:food_star;default:5" json:"food_star,omitempty"`      // 评论星数 1-5
	BoxStar       uint           `gorm:"column:box_star;default:5" json:"box_star,omitempty"`        // 评论星数 1-5
	Text          string         `gorm:"column:text" json:"text,omitempty"`                          // 评论内容
	IsAnonymous   int            `gorm:"column:is_anonymous;NOT NULL" json:"is_anonymous,omitempty"` // 是否匿名 1 匿名
	IsSatisfied   int            `gorm:"column:is_satisfied" json:"is_satisfied,omitempty"`          // 是否满意配送员1 满意
	RejectReson   string         `gorm:"column:reject_reson" json:"reject_reson,omitempty"`          // 拒绝原因
	ReviewTime    time.Time      `gorm:"column:review_time" json:"review_time"`                      // 审核时间
	State         int            `gorm:"column:state;default:0" json:"state,omitempty"`              // 状态 0 待审核 1已审核 2 拒绝审核 3 等待拒绝审核
	CreatedAt     time.Time      `gorm:"column:created_at" json:"created_at"`                        // 创建时间
	UpdatedAt     time.Time      `gorm:"column:updated_at" json:"updated_at"`                        // 修改时间
	DeletedAt     gorm.DeletedAt      `gorm:"column:deleted_at" json:"deleted_at"`                        // 删除时间
	CommentReply  []CommentReply `gorm:"foreignkey:comment_id;references:id"`                        //
	Images        []CommentImage `gorm:"foreignkey:comment_id;references:id"`
	Food          models.RestaurantFoods    `gorm:"foreignkey:id;references:food_id"`                          //
	User          models.User    `gorm:"foreignkey:id;references:user_id"`                          //
	UserName      string         `gorm:"column:user_name"`
	UserAvatar    string         `gorm:"column:user_avatar"`
	Order models.Order  `gorm:"foreignkey:id;references:order_id"`                        //

	OrderDetail *models.OrderDetail `gorm:"foreignKey:ID;references:OrderDetailID"`
}

func (m *CommentModel) TableName() string {
	return "t_comment"
}


type CommentImage struct {
	Id sql.NullInt32 `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CommentId int32 `gorm:"column:comment_id;NOT NULL;comment:'评论ID'"`
	Image string `gorm:"column:image;NOT NULL;comment:'评论照片'"`
	CreatedAt sql.NullString `gorm:"column:created_at;comment:'创建时间'"`
	UpdatedAt sql.NullString `gorm:"column:updated_at;comment:'修改时间'"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;comment:'删除时间'"`
}

func (t *CommentImage) TableName() string {
	return "t_comment_image"
}