package rank

import (
	"time"
)

type UserRank struct {
	ID                             int       `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	UserID                         int       `json:"user_id" gorm:"column:user_id;not null;comment:用户id"`
	Rank                           *int      `json:"rank,omitempty" gorm:"column:rank;comment:用户等级"`
	Score                          *float64  `json:"score,omitempty" gorm:"column:score;comment:用户等级分"`
	HistoryScore                   *float64  `json:"history_score,omitempty" gorm:"column:history_score;comment:用户历史分数"`
	RecentScore                    *float64  `json:"recent_score,omitempty" gorm:"column:recent_score;comment:用户最近分数"`

	RecentOrderCount               *int      `json:"recent_order_count,omitempty" gorm:"column:recent_order_count;comment:最近订单数量"`
	RecentTotalProfit              *float64  `json:"recent_total_profit,omitempty" gorm:"column:recent_total_profit;comment:最近订单总利益"`
	RecentAvgProfit                *float64  `json:"recent_avg_profit,omitempty" gorm:"column:recent_avg_profit;comment:最近订单平均利益"`
	RecentHighestProfit            *float64  `json:"recent_highest_profit,omitempty" gorm:"column:recent_highest_profit;comment:最近订单最高利益"`
	RecentLowestProfit             *float64  `json:"recent_lowest_profit,omitempty" gorm:"column:recent_lowest_profit;comment:最近订单最低利益"`

	RecentEarliestOrderTime        *time.Time `json:"recent_earliest_order_time,omitempty" gorm:"column:recent_earliest_order_time;comment:最近最早下单时间"`
	RecentRecentOrderTime          *time.Time `json:"recent_recent_order_time,omitempty" gorm:"column:recent_recent_order_time;comment:最近最新下单时间"`
	RecentActiveDays               *int      `json:"recent_active_days,omitempty" gorm:"column:recent_active_days;comment:最近活跃天数"`
	RecentDailyAvgOrderCount       *float64  `json:"recent_daily_avg_order_count,omitempty" gorm:"column:recent_daily_avg_order_count;comment:日均订单数量"`
	RecentDailyAvgProfit           *float64  `json:"recent_daily_avg_profit,omitempty" gorm:"column:recent_daily_avg_profit;comment:最近日均利益"`

	RecentOrderCountRank           *int      `json:"recent_order_count_rank,omitempty" gorm:"column:recent_order_count_rank;comment:最近订单数量排名"`
	RecentTotalProfitRank          *int      `json:"recent_total_profit_rank,omitempty" gorm:"column:recent_total_profit_rank;comment:最近总利益排名"`
	RecentAvgProfitRank            *int      `json:"recent_avg_profit_rank,omitempty" gorm:"column:recent_avg_profit_rank;comment:最近平均利益排名"`
	RecentDailyAvgOrderCountRank   *int      `json:"recent_daily_avg_order_count_rank,omitempty" gorm:"column:recent_daily_avg_order_count_rank;comment:最近日均订单数量排名"`
	RecentDailyAvgProfitRank       *int      `json:"recent_daily_avg_profit_rank,omitempty" gorm:"column:recent_daily_avg_profit_rank;comment:最近日均利益排名"`

	HistoryOrderCount              *int      `json:"history_order_count,omitempty" gorm:"column:history_order_count;comment:历史订单数量"`
	HistoryTotalProfit             *int      `json:"history_total_profit,omitempty" gorm:"column:history_total_profit;comment:历史订单总收益"`
	HistoryAvgProfit               *float64  `json:"history_avg_profit,omitempty" gorm:"column:history_avg_profit;comment:历史平均利益"`
	HistoryHighestProfit           *int      `json:"history_highest_profit,omitempty" gorm:"column:history_highest_profit;comment:历史最高利益"`
	HistoryLowestProfit            *int      `json:"history_lowest_profit,omitempty" gorm:"column:history_lowest_profit;comment:历史最低利益"`

	HistoryEarliestOrderTime       *time.Time `json:"history_earliest_order_time,omitempty" gorm:"column:history_earliest_order_time;comment:历史最早下单时间"`
	HistoryRecentOrderTime         *time.Time `json:"history_recent_order_time,omitempty" gorm:"column:history_recent_order_time;comment:历史最新下单时间"`
	HistoryActiveDays              *int      `json:"history_active_days,omitempty" gorm:"column:history_active_days;comment:历史活跃天数"`
	HistoryDailyAvgOrderCount      *float64  `json:"history_daily_avg_order_count,omitempty" gorm:"column:history_daily_avg_order_count;comment:历史日均订单量"`
	HistoryDailyAvgProfit          *float64  `json:"history_daily_avg_profit,omitempty" gorm:"column:history_daily_avg_profit;comment:历史日均利益"`

	HistoryOrderCountRank          *int      `json:"history_order_count_rank,omitempty" gorm:"column:history_order_count_rank;comment:历史订单数量排名"`
	HistoryTotalProfitRank         *int      `json:"history_total_profit_rank,omitempty" gorm:"column:history_total_profit_rank;comment:历史总利益排名"`
	HistoryAvgProfitRank           *int      `json:"history_avg_profit_rank,omitempty" gorm:"column:history_avg_profit_rank;comment:历史平均利益排名"`
	HistoryDailyAvgOrderCountRank  *int      `json:"history_daily_avg_order_count_rank,omitempty" gorm:"column:history_daily_avg_order_count_rank;comment:历史日均订单量排名"`
	HistoryDailyAvgProfitRank      *int      `json:"history_daily_avg_profit_rank,omitempty" gorm:"column:history_daily_avg_profit_rank;comment:历史日均利益排名"`

	CreatedAt                      *time.Time `json:"created_at,omitempty" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt                      *time.Time `json:"update_at,omitempty" gorm:"column:update_at;autoUpdateTime:milli;comment:更新时间"`
}

func (*UserRank) TableName() string {
	return "t_user_rank"
}