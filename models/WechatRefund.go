// Code generated by sql2gorm. DO NOT EDIT.
package models

import (
"time"
)

// 微信退款记录（可以部分退款）
type WechatRefund struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	OrderID       int       `gorm:"column:order_id;default:0;NOT NULL"`   // 网站商品订单编号（id）
	RefundFrom    int       `gorm:"column:refund_from"`                   // 1, 用户退单 2. 商家退单 3.后台退单 4. 系统自动退单
	TransactionID string    `gorm:"column:transaction_id"`                // 微信支付流水号
	OutTradeNo    string    `gorm:"column:out_trade_no"`                  // 网站商品订单唯一号
	NonceStr      string    `gorm:"column:nonce_str"`                     // 随机字符串
	RefundID      string    `gorm:"column:refund_id"`                     // 微信退款单号
	OutRefundNo   string    `gorm:"column:out_refund_no;NOT NULL"`        // 商户退款单号( 商户系统内部唯一)
	TotalFee      int      `gorm:"column:total_fee;default:0;NOT NULL"`  // 订单总金额，单位为分
	RefundFee     int       `gorm:"column:refund_fee;default:0;NOT NULL"` // 退款金额（可以部分退款, 单位为分）
	RefundDesc    string    `gorm:"column:refund_desc"`                   // 退款原因
	Refunded      int       `gorm:"column:refunded;default:0;NOT NULL"`   // 状态（0 未退款， 1已退款）
	CreatedAt     time.Time `gorm:"column:created_at"`                    // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at"`                    // 修改时间
	DeletedAt     *time.Time `gorm:"column:deleted_at;default:NULL"`                    // 删除时间
}

func (m *WechatRefund) TableName() string {
	return "wechat_refund"
}


