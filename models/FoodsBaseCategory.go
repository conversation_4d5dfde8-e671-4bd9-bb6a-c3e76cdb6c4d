package models

import (
	"gorm.io/gorm"
	"time"
)

type FoodBaseCategory struct {
	ID          int            `gorm:"primaryKey;autoIncrement;not null;column:id" json:"id"` // 主键
	NameZh      string         `gorm:"column:name_zh" json:"name_zh"`                         // 分类名称(国语)
	NameUg      string         `gorm:"column:name_ug" json:"name_ug"`                         // 分类名称(维语)
	Image       string         `gorm:"column:image" json:"image"`                             // 图片
	State       int            `gorm:"not null;default:0;column:state" json:"state"`          // 状态 0 关闭 1 开启
	CreateBy    int            `gorm:"default:0;column:create_by" json:"create_by"`           // 添加人ID
	UpdateBy    int            `gorm:"default:0;column:update_by" json:"update_by"`           // 修改人ID
	CreatedAt   *time.Time     `gorm:"column:created_at" json:"created_at"`                   // 创建时间
	UpdatedAt   *time.Time     `gorm:"autoUpdateTime;column:updated_at" json:"updated_at"`    // 修改时间，自动更新时间
	DeletedAt   gorm.DeletedAt `gorm:"index;column:deleted_at" json:"deleted_at"`             // 删除时间，支持软删除，添加索引
	CreateAdmin Admin          `gorm:"foreignKey:create_by;references:id" json:"create_admin"`
	UpdateAdmin Admin          `gorm:"foreignKey:update_by;references:id" json:"update_admin"`
}

// TableName
//
//  @Author: YaKupJan
//  @Date: 2024-09-14 11:49:52
//  @Description: 美食分类基本表 审核完成后添加
//  @receiver FoodBaseCategory
//  @return string
func (FoodBaseCategory) TableName() string {
	return "t_foods_base_category"
}
