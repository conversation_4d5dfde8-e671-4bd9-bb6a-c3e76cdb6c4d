package chat

import (
	"time"
)

type ChatForbiddenWords struct {
	Id        int       `gorm:"column:id"`         //
	Word      string    `gorm:"column:word"`       //
	State     int       `gorm:"column:state"`      //
	Hash      string    `gorm:"column:hash"`      //
	CreatedAt time.Time `gorm:"column:created_at"` //
	UpdatedAt time.Time `gorm:"column:updated_at"` //

}

func (m *ChatForbiddenWords) TableName() string {
	return "t_forbidden_words"
}
