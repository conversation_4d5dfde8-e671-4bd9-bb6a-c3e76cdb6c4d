package chat

import (
	"time"

	"gorm.io/gorm"
)

const (
	CHAT_SENDER_USER     = 1
	CHAT_SENDER_MERCHANT = 2
	CHAT_SENDER_SHIPPER  = 3
	CHAT_SENDER_ADMIN    = 4

	// Content Types
	CHAT_CONTENT_TYPE_TEXT     = 1 // 文字类型
	CHAT_CONTENT_TYPE_IMAGE    = 2 // 图片
	CHAT_CONTENT_TYPE_CARD     = 3
	CHAT_CONTENT_TYPE_REPORT    = 4
	CHAT_CONTENT_TYPE_REMINDER = 5
	CHAT_CONTENT_TYPE_ADDRESS  = 6 // 地址

	//Card Types
	CHAT_CARD_TYPE_MERCHANT_CLOSE   = 20
	CHAT_CARD_TYPE_REPORT_NOT_READY = 21
)

// 聊天室
type OrderChatDetail struct {
	ID          int64      `gorm:"column:id;COMMENT:自增ID;primary_key;AUTO_INCREMENT" json:"id"`                                // 自增编号
	OrderID     int64      `gorm:"column:order_id;NOT NULL;COMMENT:订单编号;index" json:"order_id"`                                // 订单编号
	CityID      int64      `gorm:"column:city_id;NOT NULL;COMMENT:城市编号;index" json:"city_id"`                                  // 城市编号
	AreaID      int64      `gorm:"column:area_id;NOT NULL;COMMENT:区域编号;index" json:"area_id"`                                  // 区域编号
	SenderType  int       `gorm:"column:sender_type;NOT NULL;COMMENT:发送信息人类型:1,用户:2,商家,3:配送员,4:管理员;index" json:"sender_type"` // 发送信息人类型
	SenderID    int64      `gorm:"column:sender_id;NOT NULL;COMMENT:发送信息人;index" json:"sender_id"`
	ContentType int       `gorm:"column:content_type;NOT NULL;COMMENT:信息内容类型： 1：文字、2：图片、3：卡片、4：上报情况、5： 催单;index" json:"content_type"`                                               // 信息内容类型
	Content     string     `gorm:"column:content;COMMENT:信息内容" json:"content"`                                                                                                       // 信息内容
	Image       string     `gorm:"column:image;COMMENT:图片路径" json:"image"`                                                                                                           // 图片路径
	CardType    int       `gorm:"column:card_type;COMMENT:卡片类型:4,已接单:5,订单已准备:6,订单配送中:7,配送完毕:8,订单已取消:9,餐厅拒绝接单:10,订单配送失败:11,配送员已到餐厅:12,配送员已取餐, 20: 餐厅关门、 21 ;index" json:"card_type"` // 卡片类型
	CardContent string     `gorm:"column:card_content;COMMENT:信息内容" json:"card_content"`                                                                                             // 信息内容
	CreatedAt   time.Time  `gorm:"column:created_at;COMMENT:创建时间;index" json:"created_at"`                                                                                           // 创建时间
	UpdatedAt   time.Time  `gorm:"column:updated_at;COMMENT:修改时间" json:"updated_at"`                                                                                                 // 修改时间
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;COMMENT:删除时间" json:"deleted_at"`      
	
	OrderSerialNumber string     `gorm:"column:order_serial_number"`                                                 // 订单序列号
	OrderNo string     `gorm:"column:order_no"`                                                 // 订单号
	ResNameUg string `gorm:"column:res_name_ug"`    
	ResNameZh string 	`gorm:"column:res_name_zh"`    
	CustomerPhone	string `gorm:"column:customer_phone"`    
}

func (m *OrderChatDetail) TableName() string {
	return "t_order_chat_detail"
}

// 发送消息
type ChatSendRequest struct {
	OrderID     int64  `form:"order_id" binding:"required" json:"order_id"`
	SenderType  int   `form:"sender_type" binding:"required,oneof=1 2 3 4" json:"sender_type"`
	ContentType int   `form:"content_type" binding:"required,oneof=1 2 3 4 5 6" json:"content_type"`
	Content     string `form:"content" json:"content"`
	Image       string `form:"image" json:"image"`
	CardType    int   `form:"card_type" json:"card_type"`
	CardContent string `form:"card_content" json:"card_content"`
}

// 聊天室返回数据的结构体
type ChatResponse struct {
	ResturantName   string        `json:"restaurant_name"`
	RestaurantImage string        `json:"restaurant_image"`
	ChatMessage     []ChatMessage `json:"messages"`
	OrderSerialNumber string `json:"order_serial_number"`
	OrderNo string `json:"order_no"`
	Images []string `json:"images"`
	TotalCount int64 `json:"total_count"`
}

// 聊天室每一条消息的结构体
type ChatMessage struct {
	Id          int64  `json:"id"`
	Type        string `json:"type"`
	Content     string `json:"content"`
	CardType    int   `json:"card_type"`
	CardContent string `json:"card_content"`
	Rule        int   `json:"rule"`
	Avatar      string `json:"avatar"`
	MsgDateTime string `json:"msg_datetime"`
	OrderId     int64  `json:"order_id"`
}

// 聊天室获取聊天详情的参数
type ChatDetailRequest struct {
	OrderID int64 `form:"order_id" binding:"required"`
}

// / 聊天列表数据
type ChatListResponse struct {
	OrderId         int64  `json:"order_id"`
	RestaurantName  string `json:"restaurant_name"`
	RestaurantImage string `json:"restaurant_image"`
	NewMsg          string `json:"new_msg"`
	MsgCount        int    `json:"msg_count"`
	MsgTime         string `json:"msg_time"`
	ShipperImage string `json:"shipper_image"`
	UserImage string `json:"user_image"`
	OrderSerialNumber string `json:"order_serial_number"`
	OrderNo string `json:"order_no"`
	Images []string `json:"images"`
	ResNameUg string `json:"res_name_ug"`    
	ResNameZh string 	`json:"res_name_zh"`    
	CustomerPhone	string `json:"customer_phone"`    

}

// 聊天室所显示的用户数据
type UserInfo struct {
	Avatar string `json:"avatar"`
}

type OrderInfo struct {
	OrderId           string      `json:"order_id"`
	OrderLog          []OrderLog  `json:"order_log" gorm:"-"`
	RestaurantName    string      `json:"restaurant_name"`
	RestaurantAddress string      `json:"restaurant_address"`
	RestaurantMobile  string      `json:"restaurant_mobile"`
	BuildingName      string      `json:"building_name"`
	AreaName          string      `json:"area_name"`
	StreetName        string      `json:"street_name"`
	UserName          string      `json:"user_name"`
	UserMobile        string      `json:"user_mobile"`
	Taked             int         `json:"taked"`
	Foods             []FoodsLog  `json:"foods" gorm:"-"`
	Shipper           ShipperInfo `json:"shipper" gorm:"-"`
	ShipperId         int         `json:"shipper_id"`

	BookingTime      string      `json:"booking_time"`
	DeliveryTakedTime string     `json:"delivery_taked_time"` 
	DeliveryStartTime string     `json:"delivery_start_time"`
	DeliveryEndTime string     `json:"delivery_end_time"`
	CreatedAt         string     `json:"created_at"`
	ShipperArrivedAtShop string  `json:"shipper_arrived_at_shop"`
	ShipperTakeFoodAt    string  `json:"shipper_take_food_at"`
	Description         string   `json:"description"`
	

}

type OrderLog struct {
	OrderStateID     int    `json:"order_state_id"`
	CreatedAt        string `json:"created_at"`
	BookingTime      string `json:"booking_time" gorm:"-"`
	DeleveryTime     string `json:"delevery_time" gorm:"-"`
	ShipperTakedTime string `json:"shipper_taked_time" gorm:"-"`
}

type FoodsLog struct {
	FoodName      string  `json:"foods_name"`
	LunchBoxCount int     `json:"lunch_box_count"`
	LunchBoxFee   float64 `json:"lunch_box_fee"`
	Number        int     `json:"number"`
	Price         float64 `json:"price"`
	StoreFoodsId  int     `json:"store_foods_id"`
}

type ShipperInfo struct {
	ShipperName   string `json:"shipper_name"`
	ShipperMobile string `json:"shipper_mobile"`
}
