package chat

type OrderChatDetailCardFood struct {
	FoodNameUG string `json:"food_name_ug"`
	FoodNameZH string `json:"food_name_zh"`
	FoodPrice  string `json:"food_price"`
	Number     float64    `json:"number"`
}

type OrderChatDetailCard struct {
	OrderState      int64                       `json:"order_state"`
	OrderStateMsgUG string                    `json:"order_state_msg_ug"`
	OrderStateMsgZH string                    `json:"order_state_msg_zh"`
	FoodsList       []OrderChatDetailCardFood `json:"foods_list"`
	Remark          string                    `json:"remark"`
	CreatedTime     string                    `json:"created_time"`
	DeliveryTime    string                    `json:"delivery_time"`
}
