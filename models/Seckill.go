﻿package models

import (
	"time"

	"gorm.io/gorm"
)

type Seckill struct {
	ID                         int64                        `json:"id" gorm:"id"`                                     // 编号
	Type                       int8                         `json:"type" gorm:"type"`                                 // 1:秒杀，2:特价
	MarketId                   int64                        `json:"market_id" gorm:"market_id"`                       // 抢购活动ID
	PriceMarkupID              int64                        `json:"price_markup_id" gorm:"price_markup_id"`           // 抢购活动ID
	AdminId                    int64                        `json:"admin_id" gorm:"admin_id"`                         // 操作人编号
	CityId                     int                          `json:"city_id" gorm:"city_id"`                           // 地区编号
	AreaId                     int                          `json:"area_id" gorm:"area_id"`                           // 县，区编号
	RestaurantId               int64                        `json:"restaurant_id" gorm:"restaurant_id"`               // 餐厅
	FoodId                     int64                        `json:"food_id" gorm:"food_id"`                           // 美食编号
	SpecID                     int                          `json:"spec_id" gorm:"spec_id"`                           // 规格ID
	TotalCount                 int64                        `json:"total_count" gorm:"total_count"`                   // 销售数量
	SaledCount                 int64                        `json:"saled_count" gorm:"saled_count"`                   // 已销售数量
	Price                      int64                        `json:"price" gorm:"price"`                               // 销售价格(分)
	OriginalPrice              int64                        `json:"original_price" gorm:"original_price"`           // 原价(分)
	State                      int                          `json:"state" gorm:"state"`                               // 0表示关闭，1表示开启
	ReviewState                int                          `json:"review_state" gorm:"review_state"`                 // 秒杀审核状态： 1 待审核、2 审核通过 3、被拒绝
	BeginTime                  string                       `json:"begin_time" gorm:"begin_time"`                     // 开始时间
	EndTime                    string                       `json:"end_time" gorm:"end_time"`                         // 结束时间
	OrderTime                  *time.Time                   `json:"order_time" gorm:"order_time"`                     // 下单时间限制,不设置 则按原来的时间下单
	UserMaxOrderCount          int64                        `json:"user_max_order_count" gorm:"user_max_order_count"` // 限制每个用户最多购买次数，0 为不限制
	PlatformServiceFee         int64                        `json:"platform_service_fee" gorm:"platform_service_fee"` // 平台服务费
	Order                      int64                        `json:"order" gorm:"order"`                               // 排序
	TakeTime                   int64                        `json:"take_time" gorm:"take_time"`                       // 配送时间长(分钟)
	CreatedAt                  time.Time                    `json:"created_at" gorm:"created_at"`                     // 创建时间
	UpdatedAt                  time.Time                    `json:"updated_at" gorm:"updated_at"`                     // 更新时间
	DeletedAt                  gorm.DeletedAt               `json:"deleted_at" gorm:"deleted_at"`                     // 删除时间
	Remark                     string                       `json:"remark" gorm:"remark"`                             // 审核备注
	SeckillMarket              SeckillMarket                `gorm:"foreignkey:market_id;references:id"`               // 订单推送时的数据
	RestaurantFoods            RestaurantFoods              `gorm:"foreignkey:food_id;references:id"`
	Area                       Area                         `gorm:"foreignkey:area_id;references:id"`
	City                       City                         `gorm:"foreignkey:city_id;references:id"`
	Admin                      Admin                        `gorm:"foreignkey:admin_id;references:id"`
	Restaurant                 Restaurant                   `gorm:"foreignkey:restaurant_id;references:id"`
	PriceMarkupSeckillPriceLog []PriceMarkupSeckillPriceLog `gorm:"foreignkey:seckill_id;"`
	SeckillLog                 []SeckillLog                 `gorm:"foreignkey:seckill_id"`
	SeckillLogGroupBySelect    SeckillLogGroupBySelect      `gorm:"foreignkey:seckill_id"`
	PriceMarkupFood            PriceMarkupFood              `gorm:"foreignkey:price_markup_id;references:id"`
	PriceMarkupFoodLogSum      PriceMarkupFoodLog   `gorm:"foreignkey:ActivityId;references:ID"`
	FoodType     uint8     `gorm:"column:food_type;default:0;not null"` // 美食类型 (0: 普通美食, 1: 规格, 2: 规格美食)
	SelectedSpec *FoodSpec `gorm:"foreignkey:id;references:spec_id"`    // 已选规格数据

	
}

func (m *Seckill) TableName() string {
	return "b_seckill"
}


type FoodsSeckill struct {
	Seckill
	UserSaledCountSumSeckillLogsaledCount int `json:"user_saled_count_sum_b_seckill_logsaled_count" gorm:"column:user_saled_count_sum_b_seckill_logsaled_count"`
}

func (m *FoodsSeckill) TableName() string {
	return "b_seckill"
}