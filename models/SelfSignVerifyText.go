package models
// Code generated by sql2gorm. DO NOT EDIT.

import (
"time"
)

type SelfSignVerifyText struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	Content       string    `gorm:"column:content"`    // 关联表(t_self_sign_merchant_info)
	State         string    `gorm:"column:state"`          // 0：未通过，1:已通过
	CreatedAt     time.Time `gorm:"column:created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at"`
	DeletedAt     *time.Time `gorm:"column:deleted_at"`
}

func (m *SelfSignVerifyText) TableName() string {
	return "t_self_sign_verify_text"
}

