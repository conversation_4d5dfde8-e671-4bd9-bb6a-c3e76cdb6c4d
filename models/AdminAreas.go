package models

import (
	"time"
)

// 管理员和区域关系表
type AdminAreas struct {
	AdminID   int        `gorm:"column:admin_id;primary_key"` // 管理员ID
	CityID    int        `gorm:"column:city_id;NOT NULL"`     // 城市ID
	AreaID    int        `gorm:"column:area_id;NOT NULL"`     // 区县ID
	CreatedAt time.Time  `gorm:"column:created_at"`           // 创建时间
	UpdatedAt time.Time  `gorm:"column:updated_at"`           // 修改时间
	DeletedAt *time.Time `gorm:"column:deleted_at"`           // 删除时间
}
