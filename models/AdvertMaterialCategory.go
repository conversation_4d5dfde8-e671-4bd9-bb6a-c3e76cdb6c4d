package models

import (
	"time"

	"gorm.io/gorm"
)

const AdvertMaterialCategoryStateOpen = 1  // 状态： 1 开启
const AdvertMaterialCategoryStateClose = 2 // 状态： 2 关闭

type AdvertMaterialCategory struct {
	ID int `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`

	NameUg    string         `gorm:"column:name_ug" json:"-"`    // 宣传材料分类维吾尔语名称
	NameZh    string         `gorm:"column:name_zh" json:"-"`    // 宣传材料分类中文名称
	State     int            `gorm:"column:state" json:"-"`      // 状态： 1 开启， 2 关闭
	CreatedAt time.Time      `gorm:"column:created_at" json:"-"` //
	UpdatedAt time.Time      `gorm:"column:updated_at" json:"-"` //
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at" json:"-"` //

}

func (m *AdvertMaterialCategory) TableName() string {
	return "t_advert_material_category"
}
