package models

import (
	"time"
)

// 区县信息表
type LakalaRequst struct {
	ID              int    `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	Api             string `gorm:"api" json:"api"`
	Ip              string `gorm:"column:ip" json:"ip"`
	Extra           string `gorm:"column:extra" json:"extra"`
	RequestContent  string `gorm:"request_content" json:"request_content"`
	ResponseContent string `gorm:"response_content" json:"response_content"`

	CreatedAt time.Time `gorm:"column:created_at" json:"created_at"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at"` // 修改时间

}

func (m *LakalaRequst) TableName() string {
	return "t_lakala_request"
}
