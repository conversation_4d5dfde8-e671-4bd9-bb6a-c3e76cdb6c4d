package models

import (
	"time"

	"gorm.io/gorm"
)

const (
	FoodsComboItemStateOff = 0
	FoodsComboItemStateOn  = 1

	// 这里单独规定，因为 ComboItem 中不能出现套餐美食
	FoodsComboItemFoodTypeNormal = 0
	FoodsComboItemFoodTypeSpec   = 1
)

// FoodsComboItem 套餐项结构体
type FoodsComboItem struct {
	ID        int            `gorm:"column:id;primaryKey;autoIncrement"`  // 主键ID
	ComboID   int            `gorm:"column:combo_id;not null"`            // 套餐编号
	FoodType  uint8          `gorm:"column:food_type;default:0;not null"` // 美食类型 (0: 普通美食, 1: 规格美食)
	FoodID    int            `gorm:"column:food_id;not null"`             // 美食编号
	SpecID    int            `gorm:"column:spec_id;null"`                 // 已选规格编号
	Count     int            `gorm:"column:count;default:1;not null"`     // 数量
	State     uint8          `gorm:"column:state;default:1;not null"`     // 状态 (0：关闭，1：开启)
	CreatedAt time.Time      `gorm:"column:created_at"`                   // 创建时间
	UpdatedAt time.Time      `gorm:"column:updated_at"`                   // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at"`                   // 删除时间

	RestaurantFood RestaurantFoods `gorm:"foreignkey:id;references:food_id"` // 加载普通美食
	//SpecOptions    []FoodSpecOption `gorm:"foreignkey:food_id;references:food_id"` // 如果是规格美食，加载规格信息
	SelectedSpec *FoodSpec `gorm:"foreignkey:id;references:spec_id"`
}

func (mdl *FoodsComboItem) TableName() string {
	return "t_foods_combo_items"
}


