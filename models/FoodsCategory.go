package models

import (
	"gorm.io/gorm"
	"time"
)

type FoodsCategory struct {
	ID                   int               `gorm:"primaryKey;autoIncrement;not null;column:id" json:"id"`                                                                                                         // 主键
	NameZh               string            `gorm:"column:name_zh" json:"name_zh"`                                                                                                                                 // 分类名称(国语)
	NameUg               string            `gorm:"column:name_ug" json:"name_ug"`                                                                                                                                 // 分类名称(维语)
	Image                string            `gorm:"column:image" json:"image"`                                                                                                                                     // 图片
	Weight               int               `gorm:"not null;default:0;column:weight" json:"weight"`                                                                                                                // 排序顺序
	State                int               `gorm:"not null;default:0;column:state" json:"state"`                                                                                                                  // 状态 0 待审核 1 审核通过 2 审核未通过 3 开启 4 关闭
	RestaurantID         int               `gorm:"not null;column:restaurant_id" json:"restaurant_id"`                                                                                                            // 餐厅ID
	CreateBy             int               `gorm:"column:create_by" json:"create_by"`                                                                                                                             // 创建人AdminID
	UpdateBy             int               `gorm:"column:update_by" json:"update_by"`                                                                                                                             // 修改人AdminID
	CreatedAt            *time.Time        `gorm:"column:created_at" json:"created_at"`                                                                                                                           // 创建时间
	UpdatedAt            *time.Time        `gorm:"autoUpdateTime;column:updated_at" json:"updated_at"`                                                                                                            // 修改时间
	DeletedAt            gorm.DeletedAt    `gorm:"index;column:deleted_at" json:"deleted_at"`                                                                                                                     // 删除时间，支持软删除
	RestaurantFoods      []RestaurantFoods `gorm:"many2many:t_restaurant_foods_category;foreignKey:ID;joinForeignKey:foods_category_id;References:ID;joinReferences:restaurant_foods_id" json:"restaurant_foods"` // 分类下面的美食列表
	Restaurant           Restaurant        `gorm:"foreignKey:restaurant_id;references:id" json:"restaurant"`                                                                                                      // 创建的餐厅
	CreateAdmin          Admin             `gorm:"foreignKey:create_by;references:id" json:"create_admin"`
	UpdateAdmin          Admin             `gorm:"foreignKey:update_by;references:id" json:"update_admin"`
	RestaurantFoodsCount int64 `gorm:"-"` // 美食数量
}

// TableName
//
//	@Author: YaKupJan
//	@Date: 2024-09-14 11:24:25
//	@Description: 美食分类表
//	@receiver FoodsCategory
//	@return string
func (FoodsCategory) TableName() string {
	return "t_foods_category"
}
