package models

import (
	"gorm.io/gorm"
	"time"
)

// MiniGame 小程序游戏表
type MiniGame struct {
	ID             int            `json:"id" gorm:"id"`
	NameUg         string         `json:"name_ug" gorm:"name_ug"`                   // 游戏名称 维文
	NameZh         string         `json:"name_zh" gorm:"name_zh"`                   // 游戏名称 中文
	RemarkUg       string         `json:"remark_ug" gorm:"remark_ug"`               // 游戏备注 维文
	RemarkZh       string         `json:"remark_zh" gorm:"remark_zh"`               // 游戏备注 中文
	Image          string         `json:"image" gorm:"image"`                       // 游戏图片
	GameCategoryId int            `json:"game_category_id" gorm:"game_category_id"` // 游戏类型ID
	CreatedAt      time.Time      `json:"created_at" gorm:"created_at"`             // 创建时间
	UpdatedAt      time.Time      `json:"updated_at" gorm:"updated_at"`             // 更新时间
	DeletedAt      gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"`             // 删除时间
	MiniGameCategory MiniGameCategory `gorm:"foreignKey:game_category_id;references:id"` // 类型
}

// TableName 表名称
func (*MiniGame) TableName() string {
	return "t_mini_game"
}
