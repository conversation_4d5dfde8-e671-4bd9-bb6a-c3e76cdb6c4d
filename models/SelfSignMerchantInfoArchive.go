package models

import (
	"time"
)

// SelfSignMerchantInfoArchive
//
//	@Description: 收集商家信息(微信，支付宝，银联商务，工商局)
type SelfSignMerchantInfoArchive struct {
	Id                           int        `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	RestaurantId                 int        `gorm:"column:restaurant_id"` // 店铺id
	CityId                       int        `gorm:"column:city_id"`
	AreaId                       int        `gorm:"column:area_id"`
	RegMerType                   string     `gorm:"column:reg_mer_type"`                          // 注册类型（00:企业，01:店铺：02：小微商户）
	ShopLicenseNum               string     `gorm:"column:shop_license_num"`                      // 社会信用统一代码/营业执照号
	ShopName                     string     `gorm:"column:shop_name"`                             // 营业执照店铺名称
	LegalName                    string     `gorm:"column:legal_name"`                            // 法人姓名
	RegAddress                   string     `gorm:"column:reg_address"`                           // 注册地址
	RegCapital                   string     `gorm:"column:reg_capital"`                           // 注册资金
	BusinessScope                string     `gorm:"column:business_scope"`                        // 营业范围
	ShopLicenseLimitedType       *int       `gorm:"column:shop_license_limited_type"`             // 营业执照有限期类型( 0：短期  1：长期)
	ShopLicenseStart             *time.Time `gorm:"column:shop_license_start;default:NULL"`       // 营业执照起效日期
	ShopLicenseEnd               *time.Time `gorm:"column:shop_license_end;default:NULL"`         // 营业执照失效时间
	MerIdcardName                string     `gorm:"column:mer_idcard_name"`                       // 经营者身份证姓名
	MerIdcardNum                 string     `gorm:"column:mer_idcard_num"`                        // 经营者身份证号(商户)
	MerIdcardStart               *time.Time `gorm:"column:mer_idcard_start"`                      // 经营者身份证生效时间
	MerIdcardEnd                 *time.Time `gorm:"column:mer_idcard_end"`                        // 经营者身份证照失效时间
	MerIsBnf                     *int       `gorm:"column:mer_is_bnf"`                            // 经营者是否受益人(0：否,1：是)
	MerMobile                    string     `gorm:"column:mer_mobile"`                            // 经营者手机号
	MerSex                       *int       `gorm:"column:mer_sex"`                               // 性别
	ShareholderName              string     `gorm:"column:shareholder_name"`                      // 控股股东姓名
	ShareholderIdcard            string     `gorm:"column:shareholder_idcard"`                    // 控股股东身份证号
	ShareholderIdcardLimitedType *int       `gorm:"column:shareholder_idcard_limited_type"`       // 控股股东执照有限期类型( 0：短期  1：长期)
	ShareholderIdcardStart       *time.Time `gorm:"column:shareholder_idcard_start;default:NULL"` // 控股股东身份证起效日期
	ShareholderIdcardEnd         *time.Time `gorm:"column:shareholder_idcard_end;default:NULL"`   // 控股股东身份证失效日期，如果长期：9999-12-31
	ShareholderAddress           string     `gorm:"column:shareholder_address"`                   // 控股股东家庭地址
	LegalmanHomeAddr             string     `gorm:"column:legal_home_address"`                    // 控股股东家庭地址
	BankAcctType                 *int       `gorm:"column:bank_acct_type"`                        // 银行账户类型(0:个人账户  1:公司账户)
	BankAcctNum                  string     `gorm:"column:bank_acct_num"`                         // 开户行帐号/银行卡号
	BankAcctName                 string     `gorm:"column:bank_acct_name"`                        // 开户行名称
	BankId                       int        `gorm:"column:bank_id"`                               // 银行名称
	BankProvinceId               int        `gorm:"column:bank_province_id"`                      // 开户银行省份
	BankCityId                   int        `gorm:"column:bank_city_id"`                          // 开户银行城市
	BankAreaId                   int        `gorm:"column:bank_area_id"`                          // 开户银行区/县
	BankBranchName               string     `gorm:"column:bank_branch_name"`                      // 开户银行分行名称
	BankBranchCode               string     `gorm:"column:bank_branch_code"`                      // 开户行号
	BankBindMobile               string     `gorm:"column:bank_bind_mobile"`                      // 银行卡绑定手机号
	ShopBusinessName             string     `gorm:"column:shop_business_name"`                    // 店铺门牌名称
	ShopBusinessProvinceID       string     `gorm:"column:shop_business_province_id"`             // 店铺省编号
	ShopBusinessCityID           string     `gorm:"column:shop_business_city_id"`                 // 店铺地区/城市
	ShopBusinessCountryID        string     `gorm:"column:shop_business_country_id"`              // 店铺经营区/县
	ShopBusinessAddress          string     `gorm:"column:shop_business_address"`                 // 店铺实际经营详细地址
	PayProduct                   string     `gorm:"column:pay_product"`                           // 开通的支付业务
	ShopCategoryCode             string     `gorm:"column:shop_category_code"`                    // 商家业务类别代码（比如：餐厅、超市等）
	ShopManagerType              int        `gorm:"column:shop_manager_type"`                     // 店铺管理员类型(1:法人，2:其他)
	ShopManagerName              string     `gorm:"column:shop_manager_name"`                     // 店铺管理员姓名
	ShopManagerIdcard            string     `gorm:"column:shop_manager_idcard"`                   // 店铺管理员身份证号
	ShopManagerMobile            string     `gorm:"column:shop_manager_mobile"`                   // 店铺管理员手机号
	ShopManagerEmail             string     `gorm:"column:shop_manager_email"`                    // 店铺管理员邮箱
	PermitStartDate              *time.Time `gorm:"column:permit_start_date"`                     // 许可证生效日期
	PermitEndDate                *time.Time `gorm:"column:permit_end_date"`                       // 许可证失效日期
	PermitShopName               string     `gorm:"column:permit_shop_name"`                      // 许可证上的店铺名称
	PermitLicNum                 string     `gorm:"column:permit_lic_num"`                        // 许可证编号
	PermitCreditCode             string     `gorm:"column:permit_credit_code"`                    // 社会信用代码
	PermitLegalName              string     `gorm:"column:permit_legal_name"`                     // 许可证法人姓名
	PermitShopAddr               string     `gorm:"column:permit_shop_addr"`                      // 许可证店铺地址
	PermitBusinessPremises       string     `gorm:"column:permit_business_premises"`              // 经营场所
	PermitState                  string     `gorm:"column:permit_state"`                          // 主体业态
	PermitBusinessType           string     `gorm:"column:permit_business_type"`                  // 许可证经营类型
	PermitSuperviseOrg           string     `gorm:"column:permit_supervise_org"`                  // 监督管理机构
	PermitSuperviseManagers      string     `gorm:"column:permit_supervise_managers"`             // 监督管理人员
	VerifyContent                string     `gorm:"column:verify_content"`                        // 后台审核内容json格式
	PermitIssuingAuthority       string     `gorm:"column:permit_issuing_authority"`              // 发证机关
	UmsRegId                     string     `gorm:"column:ums_reg_id"`                            // 发证机关
	State                        int        `gorm:"column:state"`                                 // 0:未提交,1:待审核 ,2:审核未通过 ,3:后台审核通过 ,4:待提交资料 ,5:已提交资料 ,6:资料未通过,7:对公账户待确认  ,8:待在线签约 ,9:待银商入网审核 ,10:银商入网成功,11:银商入网失败,
	WechatVerifyState            int        `gorm:"column:wechat_verify_state"`                   // 微信平台审核状态（0:审核未通过，1:审核中，2：审核通过）
	AlipayVerifyState            int        `gorm:"column:alipay_verify_state"`                   // 支付宝平台审核状态（0:审核未通过，1:审核中，2：审核通过）
	UmsVerifyState               string     `gorm:"column:ums_verify_state"`                      // 银联商务平台审核状态（0:审核未通过，1:审核中，2：审核通过）
	CreatedAt                    time.Time  `gorm:"column:created_at"`                            // 创建时间
	UpdatedAt                    time.Time  `gorm:"column:updated_at"`                            // 更新时间
	DeletedAt                    *time.Time `gorm:"column:deleted_at"`                            // 删除时间
	AlterState                   int        `gorm:"column:alter_state"`                           // 1: 营业执照和法人信息变更 2: 账户信息变更 3：管理员信息变更 4: 许可证 5：门店信息
	UmsMerNo                     string     `gorm:"column:ums_mer_no"`                            // 银商商户号
	UmsCompanyNo                 string     `gorm:"column:ums_company_no"`                        // 银商企业号
	//UmsReviewContent			 string       `gorm:"column:ums_review_content"`                 // 银商审核返回数据
	UmsTermAppNoList string `gorm:"column:ums_term_app_no_list"` // 多应用终端号列表
}

func (m *SelfSignMerchantInfoArchive) TableName() string {
	return "t_self_sign_merchant_info_archive"
}
