﻿package models

import (
	"time"
)

type Menus struct {
	ID          int            `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	Name        string         `gorm:"column:name;NOT NULL"`
	NameUg      string         `gorm:"column:name_ug"`
	NameZh      string         `gorm:"column:name_zh"`
	PermId      int            `gorm:"column:perm_id;default:NULL;"`
	ParentID    int            `gorm:"column:parent_id;default:NULL;"`
	Weight      int            `gorm:"column:weight;default:0;NOT NULL"`
	ActiveClass string         `gorm:"column:active_class"`
	IconClass   string         `gorm:"column:icon_class"`
	CreatedAt   time.Time      `gorm:"column:created_at"`
	UpdatedAt   time.Time      `gorm:"column:updated_at"`
}

func (m *Menus) TableName() string {
	return "menus"
}
