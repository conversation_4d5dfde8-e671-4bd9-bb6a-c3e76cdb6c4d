package models

import (
	"mulazim-api/models/cms"
	"time"

	"gorm.io/gorm"
)

type ShipperAttendanceLog struct {
	Id           int            `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL;comment:'自增ID'"`
	CityId       int            `gorm:"column:city_id;NOT NULL;comment:'下单城市编号'"`
	AreaId       int            `gorm:"column:area_id;NOT NULL;comment:'区域编号'"`
	City         cms.City       `gorm:"foreignKey:city_id;references:id"` // 地区
	Area         Area           `gorm:"foreignKey:area_id;references:id"` // 区域
	ShipperId    int            `gorm:"column:shipper_id;NOT NULL;comment:'配送员ID'"`
	Type    int            `gorm:"column:type;comment:'类型'"`
	Shipper      Admin          `gorm:"foreignKey:shipper_id;references:id"` // 地区
	AdminId      int            `gorm:"column:admin_id;comment:'管理员关闭话显示管理员ID'"`
	ReviewerID   int            `gorm:"column:reviewer_id;comment:'管理员关闭话显示管理员ID'"`
	Name         string         `gorm:"column:name;comment:'配送员姓名'"`
	Mobile       string         `gorm:"column:mobile;comment:'配送员手机号'"`
	Lat          string         `gorm:"column:lat;default:0;NOT NULL;comment:'大厦纬度'"`
	Lng          string         `gorm:"column:lng;default:0;NOT NULL;comment:'大厦经度'"`
	Position     string         `gorm:"column:position;default:NULL;comment:'打卡地点'"`
	StartTime    time.Time      `gorm:"column:start_time;default:NULL;comment:'请假开始时间/事故时间'"`
	EndTime      *time.Time     `gorm:"column:end_time;default:NULL;comment:'请假结束时间'"`
	Images       string         `gorm:"column:images;default:NULL;comment:'照片'"`
	ImageHash    string         `gorm:"column:image_hash;default:NULL;comment:'照片MD5哈希值'"`
	LeaveType    int            `gorm:"column:leave_type;default:NULL;comment:'请假类型// 1:事假 2:病假 3:婚嫁 4:产假/陪产假'"`
	Remark       string         `gorm:"column:remark;default:NULL;comment:'备注/原因'"`
	ReviewRemark string         `gorm:"column:review_remark;default:NULL;comment:'备注/原因'"`
	ReviewState  int            `gorm:"column:review_state;default:NULL;comment:'备注/原因'"`
	State        int            `gorm:"column:state;NOT NULL;comment:'1 上班\n2 下班\n3 休息\n4 请假\n5 事故\n6 管理员暂停配送员配送\n7 管理员开启配送员配送\n'"`
	CreatedAt    time.Time      `gorm:"column:created_at;NOT NULL;comment:'创建时间'"`
	UpdatedAt    time.Time      `gorm:"column:updated_at;default:NULL;comment:'修改时间'"`
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;default:NULL;comment:'删除时间'"`

	Creator  Admin `gorm:"foreignKey:admin_id;references:id"`    // 区域
	Reviewer Admin `gorm:"foreignKey:reviewer_id;references:id"` // 区域
}

func (t *ShipperAttendanceLog) TableName() string {
	return "t_shipper_attendance_log"
}
