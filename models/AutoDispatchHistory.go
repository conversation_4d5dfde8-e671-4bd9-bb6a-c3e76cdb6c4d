package models

import (
    "mulazim-api/models/Comments"
    "mulazim-api/models/shipment"
    "time"
	// "gorm.io/gorm"
)

type AutoDispatchHistory struct {
    ID                int             `gorm:"primaryKey;autoIncrement" json:"id"`
    CityID            int             `gorm:"column:city_id" json:"city_id"`
    AreaID            int             `gorm:"column:area_id" json:"area_id"`
    OrderID           int             `gorm:"column:order_id" json:"order_id"`
    OrderNo           string          `gorm:"column:order_no;type:text" json:"order_no"`
    OrderRank         int             `gorm:"column:order_rank" json:"order_rank"`
    OrderScore        float64         `gorm:"column:order_score" json:"order_score"`
    ShipperID         int             `gorm:"column:shipper_id" json:"shipper_id"`
    ShipperRank       int             `gorm:"column:shipper_rank" json:"shipper_rank"`
    ShipperScore      float64         `gorm:"column:shipper_score" json:"shipper_score"`
    ShipperLng        float64         `gorm:"column:shipper_lng" json:"shipper_lng"`
    ShipperLat        float64         `gorm:"column:shipper_lat" json:"shipper_lat"`
    ShipperBoxCapacity int            `gorm:"column:shipper_box_capacity" json:"shipper_box_capacity"`
    ShipperOrderInfo  string `gorm:"column:shipper_order_info;type:json" json:"shipper_order_info"`
    UserID            int             `gorm:"column:user_id" json:"user_id"`
    UserRank          int             `gorm:"column:user_rank" json:"user_rank"`
    UserScore         float64         `gorm:"column:user_score" json:"user_score"`
    DispatchType      int             `gorm:"column:dispatch_type" json:"dispatch_type"`
    Remark            string          `gorm:"column:remark;type:text" json:"remark"`
    RequestID         string          `gorm:"column:request_id;type:text" json:"request_id"`
    CreatedAt         time.Time       `gorm:"column:created_at" json:"created_at"`
    UpdatedAt         time.Time       `gorm:"column:updated_at" json:"updated_at"`

    PositiveReviewsScore   float64 `gorm:"column:positive_reviews_score" json:"positive_reviews_score"` // 好评获得的分数
    MildLatenessDeduct     float64 `gorm:"column:mild_lateness_deduct" json:"mild_lateness_deduct"` // 轻度超时扣分
    ModerateLatenessDeduct float64 `gorm:"column:moderate_lateness_deduct" json:"moderate_lateness_deduct"` // 中度超时扣分
    SevereLatenessDeduct   float64 `gorm:"column:severe_lateness_deduct" json:"severe_lateness_deduct"` // 严重超时扣分（10分钟以上）
    NegativeReviewsDeduct  float64 `gorm:"column:negative_reviews_deduct" json:"negative_reviews_deduct"` // 差评扣分
    ComplaintsDeduct       float64 `gorm:"column:complaints_deduct" json:"complaints_deduct"` // 投诉扣分
    EarlyDeliveryDeduct    float64 `gorm:"column:early_delivery_deduct" json:"early_delivery_deduct"` // 早送扣分
    OrderDeliverState      int `gorm:"column:order_deliver_state" json:"order_deliver_state"` // 订单配送状态 0:按时配送 1:迟到5分钟 2:迟到10分钟 3:重度迟到 4：早送

    City           	City           `gorm:"foreignkey:city_id;references:id" json:"city"`
    Area           	Area           `gorm:"foreignkey:area_id;references:id" json:"area"`
    Shipper         AdminBase           `gorm:"foreignkey:shipper_id;references:id" json:"shipper"`
    Order           Order           `gorm:"foreignkey:id;references:order_id"`
    OrderToday      OrderToday      `gorm:"foreignkey:id;references:order_id"`
    Comments        []Comments.Comment   `gorm:"foreignkey:order_id;references:id"`
    ShipperIncomes  []shipment.ShipperIncome   `gorm:"foreignkey:order_id;references:id"`
}

func (AutoDispatchHistory) TableName() string {
    return "t_auto_dispatch_history"
}