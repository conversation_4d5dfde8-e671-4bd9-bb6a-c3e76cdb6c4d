package models

import (
	"gorm.io/gorm"
	"time"
)

// MiniGameCategory 小程序游戏类型表
type MiniGameCategory struct {
	ID        int            `json:"id" gorm:"id"`
	NameUg    string         `json:"name_ug" gorm:"name_ug"`       // 游戏类型名称 维文
	NameZh    string         `json:"name_zh" gorm:"name_zh"`       // 游戏类型名称 中文
	State     int            `json:"state" gorm:"state"`           // 状态 1开启 2关闭
	CreatedAt time.Time      `json:"created_at" gorm:"created_at"` // 创建时间
	UpdatedAt time.Time      `json:"updated_at" gorm:"updated_at"` // 更新时间
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"` // 删除时间
}

// TableName 表名称
func (*MiniGameCategory) TableName() string {
	return "t_mini_game_category"
}
