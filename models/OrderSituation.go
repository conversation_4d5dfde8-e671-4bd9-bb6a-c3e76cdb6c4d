package models

import (
	"time"

	"gorm.io/gorm"
)

// OrderSituation
//
// @Description: 上报情况
// @Author: Rixat
// @Time: 2023-11-17 10:32:10
// @receiver
// @param c *gin.Context
type OrderSituation struct {
	ID           int            `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CityID       int            `gorm:"column:city_id"`                        // 地区ID
	AreaID       int            `gorm:"column:area_id"`                        // 区域ID
	OrderID      int            `gorm:"column:order_id"`                       // 订单ID
	OrderNo      string         `gorm:"column:order_no"`                       // 订单编号
	ShipperID    int            `gorm:"column:shipper_id"`                     // 配送员ID
	Shipper      Admin          `gorm:"foreignKey:shipper_id;references:id`    // 配送员ID
	RestaurantID int            `gorm:"column:restaurant_id"`                  // 餐厅ID
	Restaurant   Restaurant     `gorm:"foreignKey:restaurant_id;references:id` // 配送员ID
	ShipperName  string         `gorm:"column:shipper_name"`                   // 配送员名称
	Content      string         `gorm:"column:content"`                        // 上报情况描述
	Image        string         `gorm:"column:image"`                          // 商品情况照片
	CreatedAt    time.Time      `gorm:"column:created_at"`                     // 创建时间
	UpdatedAt    time.Time      `gorm:"column:updated_at"`                     // 更新时间
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at"`                     // 删除标记字段
}

func (m *OrderSituation) TableName() string {
	return "t_order_situation"
}
