package models

import (
	"gorm.io/gorm"
	"time"
)

const (
	LotteryCommentTypeLike    = 1
	LotteryCommentTypeDislike = 2
)

// LotteryComment 抽奖评论结构体
type LotteryComment struct {
	ID                uint           `gorm:"primaryKey;autoIncrement" json:"id"`                    // 主键ID
	CityID            *int           `gorm:"column:city_id" json:"city_id"`                         // 城市ID
	AreaID            *int           `gorm:"column:area_id" json:"area_id"`                         // 地区ID
	LotteryActivityID *int           `gorm:"column:lottery_activity_id" json:"lottery_activity_id"` // 抽奖活动ID
	UserID            int            `gorm:"column:user_id" json:"user_id"`                         // 用户ID
	Type              *int           `gorm:"column:type" json:"type"`                               // 类型：1. 喜欢 2. 不喜欢
	Content           string         `gorm:"column:content" json:"content"`                         // 评论内容
	CreatedAt         *time.Time     `gorm:"column:created_at" json:"created_at"`                   // 创建时间
	UpdatedAt         *time.Time     `gorm:"column:updated_at" json:"updated_at"`                   // 更新时间
	DeletedAt         gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`                   // 删除时间

	User User `gorm:"foreignKey:UserID;references:ID" json:"user"`
}

func (m *LotteryComment) TableName() string {
	return "t_lottery_comment"
}
