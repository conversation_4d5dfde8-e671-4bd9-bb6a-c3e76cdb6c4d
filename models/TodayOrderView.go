package models

// Code generated by http://www.gotool.top

import (
	"database/sql"
	"mulazim-api/models/cms"
	"time"
)

type TodayOrderView struct {
	Id                int32                 `gorm:"column:id;default:0;NOT NULL;comment:'自增编号'"`
	TerminalId        int32                 `gorm:"column:terminal_id;NOT NULL;comment:'终端编号'"`
	OrderId           string                `gorm:"column:order_id;NOT NULL;comment:'订单唯一编号'"`
	CategoryId        int32                 `gorm:"column:category_id;NOT NULL;comment:'商户类目编号'"`
	StoreId           int32                 `gorm:"column:store_id;NOT NULL;comment:'商户编号'"`
	UserId            int32                 `gorm:"column:user_id;NOT NULL;comment:'用户编号'"`
	RestaurantName    string                `gorm:"column:restaurant_name;default:NULL;comment:'餐厅维文名称'"`
	RestaurantNameUg  string                `gorm:"column:restaurant_name_ug;default:NULL;comment:'餐厅维文名称'"`
	RestaurantNameZh  string                `gorm:"column:restaurant_name_zh;default:NULL;comment:'餐厅中文名称'"`
	RestaurantTag     string                `gorm:"column:restaurant_tag;comment:'餐厅唯一标识（自动生成）'"`
	RestaurantLat     string                `gorm:"column:restaurant_lat;comment:'纬度'"`
	RestaurantLng     string                `gorm:"column:restaurant_lng;comment:'经度'"`
	LastQueryTime     time.Time             `gorm:"column:last_query_time;default:NULL;comment:'最终查询时间'"`
	CityId            int32                 `gorm:"column:city_id;default:0;comment:'自增编号'"`
	CityNameUg        string                `gorm:"column:city_name_ug;default:NULL"`
	CityNameZh        string                `gorm:"column:city_name_zh;default:NULL"`
	AreaId            int32                 `gorm:"column:area_id;default:0;comment:'自增编号'"`
	AreaName          sql.NullInt32         `gorm:"column:area_name;comment:'区域名称'"`
	AreaNameUg        string                `gorm:"column:area_name_ug;default:NULL;comment:'区域维文名称'"`
	AreaNameZh        string                `gorm:"column:area_name_zh;default:NULL;comment:'区域中文名称'"`
	StreetId          int32                 `gorm:"column:street_id;default:0;comment:'自增编号'"`
	StreetNameUg      string                `gorm:"column:street_name_ug;default:NULL"`
	StreetNameZh      string                `gorm:"column:street_name_zh;default:NULL"`
	BuildingId        int32                 `gorm:"column:building_id;NOT NULL;comment:'大厦编号'"`
	BuildingNameUg    string                `gorm:"column:building_name_ug;default:NULL"`
	BuildingNameZh    string                `gorm:"column:building_name_zh;default:NULL"`
	BuildingLat       string                `gorm:"column:building_lat;default:0;comment:'大厦纬度'"`
	BuildingLng       string                `gorm:"column:building_lng;default:0;comment:'大厦经度'"`
	TerminalNameUg    string                `gorm:"column:terminal_name_ug;comment:'终端名称'"`
	TerminalNameZh    string                `gorm:"column:terminal_name_zh;comment:'终端名称'"`
	OrderAddress      string                `gorm:"column:order_address;NOT NULL;comment:'收餐地址'"`
	Name              string                `gorm:"column:name;NOT NULL;comment:'收餐人名称'"`
	Mobile            string                `gorm:"column:mobile;NOT NULL;comment:'收餐人手机号'"`
	ConsumeType       int32                 `gorm:"column:consume_type;default:NULL;comment:'付费类型（0现金,1在线支付）'"`
	PayType           int32                 `gorm:"column:pay_type;default:NULL;comment:'用什么付费(1 现金, 2 餐币, 3 支付宝,  4 银联, 5 微信)'"`
	OriginalPrice     uint32                `gorm:"column:original_price;NOT NULL;comment:'订单原价格（单位：分）'"`
	Price             uint32                `gorm:"column:price;NOT NULL;comment:'实际价格（单位：分）'"`
	Shipment          uint32                `gorm:"column:shipment;default:NULL;comment:'配送费'"`
	OriginalShipment  uint                  `gorm:"column:original_shipment"` // 原始配送费
	LunchBoxFee       int32                 `gorm:"column:lunch_box_fee;default:0;comment:'总饭盒费（单位：分）'"`
	MpProfit          uint32                `gorm:"column:mp_profit;default:NULL;comment:'一个订单的属于平台的总利润'"`
	DealerProfit      uint32                `gorm:"column:dealer_profit;default:NULL;comment:'一个订单的属于代理商的总利润'"`
	Cash              uint32                `gorm:"column:cash;default:0;NOT NULL;comment:'现金（单位：分）'"`
	Coin              uint32                `gorm:"column:coin;default:0;NOT NULL;comment:'餐币（单位：分）'"`
	Consume           uint32                `gorm:"column:consume;default:0;NOT NULL;comment:'充值金额（单位：分）'"`
	Description       string                `gorm:"column:description;NOT NULL;comment:'订单备注'"`
	BookingTime       time.Time             `gorm:"column:booking_time;default:NULL;comment:'预订时间'"`
	PrintedTime       time.Time             `gorm:"column:printed_time;default:NULL;comment:'订单打印完成时间'"`
	PrintTime         time.Time             `gorm:"column:print_time;default:NULL;comment:'打印时间'"`
	Timezone          int8                  `gorm:"column:timezone;default:6;comment:'预定时间时区：6乌鲁木齐时间，8北京时间'"`
	PayTime           time.Time             `gorm:"column:pay_time;default:NULL;comment:'付费时间'"`
	DeliveryStartTime time.Time             `gorm:"column:delivery_start_time;default:NULL;comment:'开始配送时间'"`
	DeliveryEndTime   time.Time             `gorm:"column:delivery_end_time;default:NULL;comment:'订单结束时间'"`
	Taked             int8                  `gorm:"column:taked;default:0;comment:'表示是否已被抢单（0未抢单，1已抢单）'"`
	SerialNumber      uint32                `gorm:"column:serial_number;default:0;NOT NULL"`
	ShipperId         int32                 `gorm:"column:shipper_id;default:NULL;comment:'配送员编号'"`
	OrderType         int8                  `gorm:"column:order_type;default:1;comment:'0:表示预订单；1：表示实时订单'"`
	ShipperName       string                `gorm:"column:shipper_name;default:NULL;comment:'管理员实名'"`
	DeleteFlag        int8                  `gorm:"column:delete_flag;default:0;comment:'用户是否删除该订单（0正常，1用户已删除）'"`
	RefundChanel      int8                  `gorm:"column:refund_chanel;default:0;comment:'取消订单渠道 （0表示没取消，1 表示实时订单自动被系统取消  2表示从后台取消 3表示商家端取消 4表示 用户端取消）'"`
	State             int8                  `gorm:"column:state;NOT NULL;comment:'订单状态（跟b_order_state表关联）'"`
	CreatedAt         time.Time             `gorm:"column:created_at;default:NULL;comment:'创建时间'"`
	UpdatedAt         time.Time             `gorm:"column:updated_at;default:NULL;comment:'修改时间'"`
	DeletedAt         time.Time             `gorm:"column:deleted_at;default:NULL;comment:'删除时间'"`
	OrderDetail       []OrderDetail         `gorm:"foreignkey:order_id;references:id"`             //
	LunchBoxDetail    []LunchBoxDetail      `gorm:"foreignkey:order_id;references:id"`             //
	AddressView       AddressView           `gorm:"foreignkey:building_id;references:building_id"` //
	Restaurant        Restaurant            `gorm:"foreignkey:store_id;references:id"`             //
	PayTypes          PayType               `gorm:"foreignkey:pay_type;references:id"`             //
	LunchBox          []LunchBoxOrderDetail `gorm:"foreignkey:order_id;references:id"`
	MarketingList     []MarketingOrderLog   `gorm:"foreignkey:order_id;references:id"`
	Shipper           Admin                 `gorm:"foreignkey:id;references:shipper_id"`
	User              User                  `gorm:"foreignkey:id;references:user_id"`
	OrderState        cms.OrderState        `gorm:"foreignkey:state;references:id"`
	LoginTime         *RestaurantLastLogin  `gorm:"foreignkey:store_id;references:restaurant_id"`
	OrderStateLog     []OrderStateLog       `gorm:"foreignkey:order_id;references:id"`
	DeliveryType      int                   `gorm:"column:delivery_type" json:"delivery_type"`
	PayPlatform       int                   `gorm:"column:pay_platform" json:"pay_platform"`
}

const (
	REASON_UNSUBSCRIBE_ORDER = 7
	REASON_MULAZIM_GIFT      = 8
	REASON_ORDER_FAIL        = 9
	REASON_RECHARGE_FAIL     = 10
)

func (m *TodayOrderView) TableName() string {
	return "today_order_view"
}
