package models

import (
	"gorm.io/gorm"
	"time"
)

// UserBuilding represents the t_user_building table
type UserBuilding struct {
	ID         int       `gorm:"column:id;AUTO_INCREMENT;primary_key"`
	UserID     int       `gorm:"column:user_id;NOT NULL"`
	BuildingID int       `gorm:"column:building_id;NOT NULL"`
	Address    string    `gorm:"column:address;NOT NULL"`
	Tel        string    `gorm:"column:tel;NOT NULL"`
	Name       string    `gorm:"column:name;NOT NULL"`
	Gender     int       `gorm:"column:gender;default:1;NOT NULL"`
	State      int       `gorm:"column:state;default:0"` // 设置默认地址状态（0普通，1默认）
	CreatedAt  time.Time `gorm:"column:created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at"`
	Building           Building           `gorm:"foreignkey:building_id;references:id"`

}

func (m *UserBuilding) TableName() string {
	return "t_user_building"
}