package models

import (
	"time"

	"gorm.io/gorm"
)

// 订单满减记录表
type MarketingOrderLog struct {
	ID               int            `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 编号 编号
	Type             int            `gorm:"column:type;default:1"`                // 营销类型:1:满减
	CityID           int            `gorm:"column:city_id"`                       // 城市编号
	AreaID           int            `gorm:"column:area_id"`                       // 区县编号
	UserID           int            `gorm:"column:user_id"`                       // 下单用户ID
	ShipperId        int            `gorm:"column:shipper_id"`                    // 配送员id
	RestaurantID     int            `gorm:"column:restaurant_id;NOT NULL"`        // 餐厅编号 餐厅编号
	OrderID          int            `gorm:"column:order_id;NOT NULL"`             // 订单ID
	OrderNo          string         `gorm:"column:order_no;NOT NULL"`             // 订单编号
	GroupId          int            `gorm:"column:group_id"`                      // 活动组编号
	MarketingID      int            `gorm:"column:marketing_id;NOT NULL"`         // 满减活动编号 满减活动编号
	NameUg           string         `gorm:"column:name_ug"`                       // 营销活动名称
	NameZh           string         `gorm:"column:name_zh"`                       // 营销活动名称
	Name             string         `gorm:"column:name"`                          // 营销活动名称
	OrderPrice       int            `gorm:"column:order_price"`                   // 订单总金额（单位 分）
	StepReduce       int            `gorm:"column:step_reduce;NOT NULL"`          // 优惠金额（单位 分）
	MpServiceFee     int            `gorm:"column:mp_service_fee"`                // 平台技术服务费（单位 分）
	DealerServiceFee int            `gorm:"column:dealer_service_fee"`            // 代理技术服务费（单位 分）
	StepNumber       int            `gorm:"column:step_number"`                   // 阶梯号
	ReductionFee     int            `gorm:"column:reduction_fee"`
	OriginalShipment int            `gorm:"column:original_shipment"`                             // 原始配送费（单位 分）
	SeledCount       int            `gorm:"column:seled_count;default:1"`                         // 销售数量
	ResCost          int            `gorm:"column:res_cost"`                                      // 商家承担部分（单位 分）
	DealerCost       int            `gorm:"column:dealer_cost"`                                   // 代理承担部分（单位 分）
	Distance         float64        `gorm:"column:distance"`                                      // 配送距离（单位 米）
	CreatedAt        time.Time      `gorm:"column:created_at"`                                    // 创建时间 创建时间
	UpdatedAt        time.Time      `gorm:"column:updated_at"`                                    // 更新时间 更新时间
	DeletedAt        gorm.DeletedAt `gorm:"column:deleted_at"`                                    // 被删除时间 被删除时间
	Order            *Order         `gorm:"foreignKey:id;references:order_id" json:"order"`       // 归档订单
	OrderToday       *OrderToday    `gorm:"foreignKey:id;references:order_id" json:"order_today"` // 当天订单
	Restaurant       *Restaurant    `gorm:"foreignKey:id;references:restaurant_id" json:"-"`      // 餐厅
	OrderState       int            `gorm:"column:order_state"`                                   // 订单状态
	Shipper          *Admin         `gorm:"foreignkey:id;references:shipper_id"`
}

func (m *MarketingOrderLog) TableName() string {
	return "t_marketing_order_log"
}

type MarketingOrderLogAggs struct {
	MarketingID      int `gorm:"column:marketing_id" json:"marketing_id"`             // 满减活动编号 满减活动编号
	TotalOrderPrice  int `gorm:"column:total_order_price" json:"total_order_price"`   // 订单总金额（单位 分）
	OrderCount       int `gorm:"column:order_count" json:"order_count"`               // 订单总数
	TotalReducePrice int `gorm:"column:total_reduce_price" json:"total_reduce_price"` // 优惠金额（单位 分）
	TotalDealerCost  int `gorm:"column:total_dealer_cost" json:"total_dealer_cost"`   // 代理承担部分（单位 分）
	TotalResCost     int `gorm:"column:total_res_cost" json:"total_res_cost"`         // 商家承担部分（单位 分）
}

type MarketingOrderLogGroupAggs struct {
	GroupID          int `gorm:"column:group_id" json:"group_id"`                     // 满减活动编号 满减活动编号
	RestaurantId     int `gorm:"column:restaurant_id" json:"restaurant_id"`           // 满减活动编号 满减活动编号
	TotalOrderPrice  int `gorm:"column:total_order_price" json:"total_order_price"`   // 订单总金额（单位 分）
	OrderCount       int `gorm:"column:order_count" json:"order_count"`               // 订单总数
	TotalReducePrice int `gorm:"column:total_reduce_price" json:"total_reduce_price"` // 优惠金额（单位 分）
	TotalDealerCost  int `gorm:"column:total_dealer_cost" json:"total_dealer_cost"`   // 代理承担部分（单位 分）
	TotalResCost     int `gorm:"column:total_res_cost" json:"total_res_cost"`         // 商家承担部分（单位 分）
}

func (m *MarketingOrderLogGroupAggs) TableName() string {
	return "t_marketing_order_log"
}

func (m *MarketingOrderLogAggs) TableName() string {
	return "t_marketing_order_log"
}
