package models

import "time"

type YingshangFormStateLog struct {
	ID           int       `json:"id" gorm:"column:id"`
	RestaurantID int       `json:"restaurant_id" gorm:"column:restaurant_id"` // 店铺id
	FormID       int       `json:"form_id" gorm:"column:form_id"`             // 档案id
	OldState     int       `json:"old_state" gorm:"column:old_state"`         // 旧状态
	CurrentState int       `json:"current_state" gorm:"column:current_state"` // 新状态
	OldData      string    `json:"old_data" gorm:"column:old_data"`           // 更改前的数据
	CurrentData  string    `json:"current_data" gorm:"column:current_data"`   // 更改后的数据
	AdminID      int       `json:"admin_id" gorm:"column:admin_id"`           // 操作员id
	CreatedAt    time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"column:updated_at"`
	DeletedAt    string    `json:"deleted_at" gorm:"column:deleted_at"`
}

func (m *YingshangFormStateLog) TableName() string {
	return "yinshang_form_state_log"
}
