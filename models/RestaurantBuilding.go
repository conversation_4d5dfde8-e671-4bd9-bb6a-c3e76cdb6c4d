package models

import (
	"time"

	"gorm.io/gorm"
)

// 大厦信息表
type RestaurantBuilding struct {

	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`     // 自增编号
	RestaurantID  int       `gorm:"column:restaurant_id;NOT NULL"`            // 餐厅编号
	BuildingID    int       `gorm:"column:building_id;NOT NULL"`              // 大厦或小区什么的
	Distance      uint      `gorm:"column:distance;NOT NULL"`                 // 餐厅和大厦之间的距离(单位：米)
	FixedShipment uint      `gorm:"column:fixed_shipment;default:0;NOT NULL"` // 固定配送费
	Shipment      uint      `gorm:"column:shipment;NOT NULL"`                 // 最高配送费
	Time          uint      `gorm:"column:time;NOT NULL"`                     // 配送所需要的时间（单位为分钟）
	State         int       `gorm:"column:state;NOT NULL"`                    // 状态（0关闭，1开通）
	CreatedAt     time.Time `gorm:"column:created_at"`                        // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at"`                        // 修改时间
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at"`                        // 删除时间

	
}

func (m *RestaurantBuilding) TableName() string {
	return "t_restaurant_building"
}
