package models
// Code generated by sql2gorm. DO NOT EDIT.

import (
"time"
)

type SelfSignVerifyLog struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	MerInfoID     int       `gorm:"column:mer_info_id"`    // 关联表(t_self_sign_merchant_info)
	VarifyerID    int       `gorm:"column:varifyer_id"`    // 审核人
	StepNum       int       `gorm:"column:step_num"`       // 步骤，1：营业执照信息，2：账户信息，3：店铺信息
	PageName      string    `gorm:"column:page_name"`      // 步骤名称
	Remark        string    `gorm:"column:remark"`         // 审核备注
	OperationType int       `gorm:"column:operation_type"` // 1:提交审核，2：
	State         int       `gorm:"column:state"`          // 0：未通过，1:已通过
	CreatedAt     time.Time `gorm:"column:created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at"`
	DeletedAt     *time.Time `gorm:"column:deleted_at"`
	Verifyer     Admin `gorm:"foreignKey:varifyer_id;references:id"`  
}

func (m *SelfSignVerifyLog) TableName() string {
	return "t_self_sign_verify_log"
}

