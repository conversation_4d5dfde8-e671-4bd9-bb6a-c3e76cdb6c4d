package models

import (
	"time"
)

// 行业类别
type SelfSignMcc struct {
	ID        int       `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	Code      string    `gorm:"column:code" json:"code"`
	Category  string    `gorm:"column:category"  json:"category"`
	NameUg    string    `gorm:"column:name_ug"  json:"name_ug"`
	NameZh    string    `gorm:"column:name_zh"  json:"name_zh"`
	State     int       `gorm:"column:state;default:0;NOT NULL"  json:"state"`
	CreatedAt time.Time `gorm:"column:created_at"  json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at"`
	DeletedAt *time.Time `gorm:"column:deleted_at" json:"deleted_at"`
}

func (m *SelfSignMcc) TableName() string {
	return "b_self_sign_mcc"
}
