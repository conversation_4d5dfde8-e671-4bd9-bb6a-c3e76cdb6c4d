package models

import (
	"time"
)

// 代理营销类活动充值退款表
type AdminMarketRefundLog struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CityID        int       `gorm:"column:city_id;NOT NULL"`
	AreaID        int       `gorm:"column:area_id;NOT NULL"`
	RechargeID    int       `gorm:"column:recharge_id"`           // 充值id
	AdminID       int       `gorm:"column:admin_id;NOT NULL"`     // 代理（管理员）编号
	ItemID        int       `gorm:"column:item_id"`               // 优惠券id
	Type          int       `gorm:"column:type"`                  // 1表示充值优惠券的钱退款
	TotalAmount   int       `gorm:"column:total_amount;NOT NULL"` // 充值金额（单位为：分）
	RefundAmount  int       `gorm:"column:refund_amount"`         // 充值或消费后的余额
	OutTradeNo    string    `gorm:"column:out_trade_no"`          // 网站商品订单唯一号
	Refunded      int       `gorm:"column:refunded;default:0"`    // 状态（0 未退款， 1已退款）

	PaySeqNo    string    `gorm:"column:pay_seq_no"`          
	RefundRule    string    `gorm:"column:refund_rule"`       
	RefundParam    string    `gorm:"column:refund_param"`      
	Remark    string    `gorm:"column:remark"`          
	OrderStatus    int    `gorm:"column:order_status"`    
	RefundStatus    int    `gorm:"column:refund_status"`   
	ErrorMessage    string    `gorm:"column:error_message"`   
	RefundSeqNo    string    `gorm:"column:refund_seq_no"`   
	SplitRuleResult    string    `gorm:"column:split_rule_result"`
	QztChannelPayRequestNo    string    `gorm:"column:qzt_channel_pay_request_no"` 
	ChannelTradeNo    string    `gorm:"column:channel_trade_no"`       
	ChannelSeqNo    string    `gorm:"column:channel_seq_no"`        
	PayChannelTradeNo    string    `gorm:"column:pay_channel_trade_no"`  
	RefundTime  *time.Time `gorm:"column:refund_time"`            
	
	CreatedAt     time.Time `gorm:"column:created_at"`            // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at"`            // 修改时间
	DeletedAt     *time.Time `gorm:"column:deleted_at"`            // 删除时间
}

func (m *AdminMarketRefundLog) TableName() string {
	return "t_admin_market_refund_log"
}
