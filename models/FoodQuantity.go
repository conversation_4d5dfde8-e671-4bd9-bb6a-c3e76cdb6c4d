package models

import (
	"time"

	"gorm.io/gorm"
)


type FoodQuantity struct {
	ID          int            `gorm:"primaryKey;autoIncrement;not null;column:id" json:"id"` // 主键
	NameZh      string         `gorm:"column:name_zh" json:"name_zh"`                         // 分组名称(国语)
	NameUg      string         `gorm:"column:name_ug" json:"name_ug"`                         // 分组名称(维语)
	State       int            `gorm:"default:1;column:state" json:"state"`          		  // 1.开启 2.关闭
	CreatedAt   *time.Time     `gorm:"column:created_at" json:"created_at"`                   // 创建时间
	UpdatedAt   *time.Time     `gorm:"autoUpdateTime;column:updated_at" json:"updated_at"`    // 修改时间，自动更新时间
	DeletedAt   gorm.DeletedAt `gorm:"index;column:deleted_at" json:"deleted_at"`             // 删除时间，支持软删除，添加索引
}

// TableName
//
//  @Author: YaKupJan
//  @Date: 2024-09-14 11:50:42
//  @Description: 美食分组基本表 审核完成后添加
//  @receiver FoodsBaseGroup
//  @return string
func (FoodQuantity) TableName() string {
	return "b_food_quantity"
}
