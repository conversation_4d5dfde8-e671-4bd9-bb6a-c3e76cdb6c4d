package models

import (
	"time"

	"gorm.io/gorm"
)

type OrderDelayed struct {
	ID int64 `json:"id" gorm:"id"`
	RandomId string `json:"random_id" gorm:"random_id"` // 随机字符
	OrderId int64 `json:"order_id" gorm:"order_id"` // 订单编号
	ShipperId int64 `json:"shipper_id" gorm:"shipper_id"` // 配送员编号
	UserId int64 `json:"user_id" gorm:"user_id"` // 用户编号
	DelayedDuration int64 `json:"delayed_duration" gorm:"delayed_duration"` // 延时时长 （分钟）
	AgreeState int `json:"agree_state" gorm:"agree_state"` // 同意状态 ；1:待处理 、2:同意、3:不同意 
	ReasonId int `json:"reason_id" gorm:"reason_id"` // 延时原因id
	OriginalBookingTime time.Time `json:"original_booking_time" gorm:"original_booking_time"` // 原来的配送时间
	NewBookingTime time.Time `json:"new_booking_time" gorm:"new_booking_time"` // 新的配送时间
	ReasonDelay string `json:"reason_delay" gorm:"reason_delay"` // 延时原因
	CreatedAt time.Time `json:"created_at" gorm:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at" gorm:"updated_at"` // 修改时间
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"` // 删除时间
}

// TableName 表名称
func (*OrderDelayed) TableName() string {
    return "t_order_delayed"
}