package models

import (
	"time"
)

// 用户优惠券
type CouponUser struct {
	ID        int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	UserID    int       `gorm:"column:user_id"`         // 用户id
	CityID    *int       `gorm:"column:city_id"`         // 城市id
	AreaID    *int       `gorm:"column:area_id"`         // 区域id
	CouponID  int       `gorm:"column:coupon_id"`       // 优惠券id

	LotteryOrderId *int `gorm:"column:lottery_order_id"`

	State     int       `gorm:"column:state;default:0"` // 状态0 未使用 1:已使用
	AddTime   string `gorm:"column:add_time"`        // 领取时间
	NameUg    string    `gorm:"column:name_ug"`         // 优惠券名称 维吾尔语
	NameZh    string    `gorm:"column:name_zh"`         // 优惠券名称 汉语
	UseTime   *time.Time `gorm:"column:use_time"`        // 使用时间

	Price     int       `gorm:"column:price"`           // 优惠券金额
	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`
	
	CouponType int `gorm:"column:coupon_type"`
	StartUseTime   string `gorm:"column:start_use_time"`        
	EndUseTime   string `gorm:"column:end_use_time"`        // 
	
}

func (m *CouponUser) TableName() string {
	return "t_coupon_user"
}

