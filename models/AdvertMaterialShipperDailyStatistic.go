package models

import (
	"time"
)

type AdvertMaterialShipperDailyStatistic struct {
	ID                       int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	AdvertMaterialId         int       `gorm:"column:advert_material_id"`          // 推广材料id，非宣传单填写0
	AdvertMaterialCategoryId int       `gorm:"column:advert_material_category_id"` // 宣传材料分类ID
	AreaId                   int       `gorm:"column:area_id"`                     // 区域ID
	CityId                   int       `gorm:"column:city_id"`                     // 城市ID
	CreatedAt                time.Time `gorm:"column:created_at"`                  // 创建日期
	Date                     string    `gorm:"column:date"`                        // 日期
	InviteUserCount          int       `gorm:"column:invite_user_count"`           // 新用户数量
	InviteUserFee            int       `gorm:"column:invite_user_fee"`             // 新用户奖励
	MaterialInviteCount      int       `gorm:"column:material_invite_count"`       // 宣传单邀请客户数量
	OrderCount               int       `gorm:"column:order_count"`                 // 邀请的用户下单数
	QrInviteCount            int       `gorm:"column:qr_invite_count"`             // 配送员二维码客户数
	ShipperId                int       `gorm:"column:shipper_id"`                  // 配送员ID
	TotalOrderPrice          int       `gorm:"column:total_order_price"`           // 邀请的用户下单总额
	TotalOrderTipsFee        int       `gorm:"column:total_order_tips_fee"`        // 邀请的用户订单奖励总额
	UpdatedAt                time.Time `gorm:"column:updated_at"`                  // 更新日期
	Shipper   				Admin 		`gorm:"foreignkey:shipper_id;references:id"`

}

type AdvertMaterialShipperDailyStatisticForCmsInviteUserStatistic struct {
	ShipperID           int    `gorm:"column:shipper_id" json:"shipper_id"`
	ShipperName         string `gorm:"column:shipper_name" json:"shipper_name"`
	InviteUserCount     int    `gorm:"column:invite_user_count" json:"invite_user_count"`         // 新用户数量
	InviteUserFee       int    `gorm:"column:invite_user_fee" json:"invite_user_fee"`             // 新用户奖励
	MaterialInviteCount int    `gorm:"column:material_invite_count" json:"material_invite_count"` // 宣传单邀请客户数量
	OrderCount          int    `gorm:"column:order_count" json:"order_count"`                     // 邀请的用户下单数
	QrInviteCount       int    `gorm:"column:qr_invite_count" json:"qr_invite_count"`             // 配送员二维码客户数
	TotalOrderPrice     int    `gorm:"column:total_order_price" json:"total_order_price"`         // 邀请的用户下单总额
	TotalRewardFee      int    `gorm:"column:total_reward_fee" json:"total_reward_fee"`           // 邀请的用户订单奖励总额

}

type AdvertMaterialShipperDailyStatisticByArea struct {
	AreaID              int    `gorm:"column:area_id" json:"area_id"`
	AreaName            string `gorm:"column:area_name" json:"area_name"`
	InviteUserCount     int    `gorm:"column:invite_user_count" json:"invite_user_count"`         // 新用户数量
	InviteUserFee       int    `gorm:"column:invite_user_fee" json:"invite_user_fee"`             // 新用户奖励
	MaterialInviteCount int    `gorm:"column:material_invite_count" json:"material_invite_count"` // 宣传单邀请客户数量
	OrderCount          int    `gorm:"column:order_count" json:"order_count"`                     // 邀请的用户下单数
	QrInviteCount       int    `gorm:"column:qr_invite_count" json:"qr_invite_count"`             // 配送员二维码客户数
	TotalOrderPrice     int    `gorm:"column:total_order_price" json:"total_order_price"`         // 邀请的用户下单总额
	TotalRewardFee      int    `gorm:"column:total_reward_fee" json:"total_reward_fee"`           // 邀请的用户订单奖励总额

}

func (m *AdvertMaterialShipperDailyStatistic) TableName() string {
	return "t_advert_material_shipper_daily_statistic"
}

type AdvertMaterialShipperDailyStatisticPage struct {
	AdvertMaterialCategoryId int                    `gorm:"column:advert_material_category_id"` // 宣传材料分类ID
	InviteUserCount          int64                  `gorm:"column:invite_user_count"`           // 新用户数量
	OrderCount               int64                  `gorm:"column:order_count"`                 // 邀请的用户下单数
	AdvertMaterialCategory   AdvertMaterialCategory `gorm:"foreignkey:advert_material_category_id;references:id"`
	Date                     string                 `gorm:"column:date"`                 // 日期
	TotalOrderTipsFee        int                    `gorm:"column:total_order_tips_fee"` // 邀请的用户订单奖励总额
	OrderPrice               int                    `gorm:"column:total_order_price"`    // 邀请的用户订单总额

}

func (m *AdvertMaterialShipperDailyStatisticPage) TableName() string {
	return "t_advert_material_shipper_daily_statistic"
}
