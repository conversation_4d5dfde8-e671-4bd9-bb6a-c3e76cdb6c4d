package models

import (
	"time"

	"gorm.io/gorm"
)


type LotteryActivityLevel struct {
	ID             int           `gorm:"primaryKey;autoIncrement;column:id"`

	LotteryActivityID       int           `gorm:"column:lottery_activity_id" json:"lottery_activity_id"`
	LotteryActivityGroupCouponLevelID int       `gorm:"column:lottery_activity_group_coupon_id"`      // 奖品等级
	LotteryActivityGroupID  int           `gorm:"column:lottery_activity_group_coupon_id" json:"lottery_activity_group_coupon_id"`
	Level                   int          `gorm:"column:level" json:"level"`
	Price                   int            `gorm:"column:price" json:"price"`
	RuleUG                  string  `gorm:"column:rule_ug" json:"rule_ug"`
	RuleZH                  string  `gorm:"column:rule_zh" json:"rule_zh"`
	CreatedAt      time.Time      `gorm:"column:created_at"`                         // 创建时间
	UpdatedAt      time.Time      `gorm:"column:updated_at;autoUpdateTime"`          // 更新时间
	DeletedAt         gorm.DeletedAt `gorm:"type:timestamp" json:"deleted_at"`

	LotteryActivityLevelPrize []LotteryActivityLevelPrize `gorm:"foreignKey:lottery_activity_level_id;references:id"`
}

func (m *LotteryActivityLevel) TableName() string {
	return "t_lottery_activity_level"
}
