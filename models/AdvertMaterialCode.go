package models

import (
	"time"
)

const (
	AdvertMaterialCodeStateWait  = 1
	AdvertMaterialCodeStateTaked = 2
)

type AdvertMaterialCode struct {
	ID                         int    `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	AdvertMaterialId           int    `gorm:"column:advert_material_id"`             // 宣传材料ID
	AdvertMaterialPrintBatchId int    `gorm:"column:advert_material_print_batch_id"` // 批次号
	AdvertMaterialTakeId       int    `gorm:"column:advert_material_take_id"`        // 认领批次ID
	Code                       string `gorm:"column:code"`                           // 宣传单编号

	QrLink    string     `gorm:"column:qr_link"`    // 二维码地址
	ShipperId *int       `gorm:"column:shipper_id"` // 认领ID
	State     int        `gorm:"column:state"`      // 状态： 1 待认领， 2 已认领
	TakedAt   *time.Time `gorm:"column:taked_at"`   // 认领时间
	CreatedAt time.Time  `gorm:"column:created_at"` // 创建时间
	UpdatedAt time.Time  `gorm:"column:updated_at"` // 更新时间

	//关系

	AdvertMaterial AdvertMaterial `gorm:"foreignkey:advert_material_id;references:id"`
	AdvertMaterialTake AdvertMaterialTake `gorm:"foreignkey:advert_material_take_id;references:id"`
}

func (m *AdvertMaterialCode) TableName() string {
	return "t_advert_material_code"
}
