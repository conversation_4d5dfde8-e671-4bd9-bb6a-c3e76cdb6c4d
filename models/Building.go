/*
*
@author: captain
@since: 2022/9/11
@desc:
*
*/
package models

import (
	"time"
)

// 大厦信息表
type Building struct {
	ID           int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	CityID       int       `gorm:"column:city_id"`                       // 城市编号
	AreaID       int       `gorm:"column:area_id"`                       // 区域编号
	StreetID     int       `gorm:"column:street_id;NOT NULL"`            // 街道编号
	BuildingType int       `gorm:"column:building_type;NOT NULL"`        // 大厦类型
	Name         int       `gorm:"column:name;NOT NULL"`                 // 大厦名称
	NameUg       string    `gorm:"column:name_ug"`
	NameZh       string    `gorm:"column:name_zh"`
	PrefixUg     string    `gorm:"column:prefix_ug"`                 // 维文名称头字母
	PrefixZh     string    `gorm:"column:prefix_zh"`                 // 汉文名称头字母
	Lat          float64   `gorm:"column:lat;default:0;NOT NULL"`    // 大厦纬度
	Lng          float64   `gorm:"column:lng;default:0;NOT NULL"`    // 大厦经度
	Weight       int       `gorm:"column:weight;default:0;NOT NULL"` // 大厦排序
	State        int       `gorm:"column:state;default:0;NOT NULL"`  // 大厦状态（0关闭，1开通）
	CreatedAt    time.Time `gorm:"column:created_at"`                // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at"`                // 修改时间
	DeletedAt    time.Time `gorm:"column:deleted_at"`                // 删除时间

	Area           Area           `gorm:"foreignkey:area_id;references:id"`
	City           City           `gorm:"foreignkey:city_id;references:id"`
}

func (m *Building) TableName() string {
	return "t_building"
}
