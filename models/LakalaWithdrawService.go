package models

import (
	"time"
)

const (
	LAKALA_WITHDRAW_SERVICE_OPT_TYPE_MULAZIM string = "mulazim"
)

type LakalaWithdrawService struct {
	ID         int    `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"` // 自增编号
	AreaId     int    `gorm:"column:area_id" json:"area_id"`                  //
	CityId     int    `gorm:"column:city_id" json:"city_id"`                  //
	Type       int    `gorm:"column:type" json:"type"`
	OptType    string `gorm:"column:opt_type" json:"opt_type"`
	AdminId    int    `gorm:"column:admin_id" json:"admin_id"`
	ItemId     int    `gorm:"column:item_id" json:"item_id"`
	OptId      int    `gorm:"column:opt_id" json:"opt_id"`
	OutTradeNo string `gorm:"column:out_trade_no" json:"out_trade_no"`
	MemberNo   string `gorm:"column:member_no" json:"member_no"`
	BankCardId string `gorm:"column:bank_card_id" json:"bank_card_id"`
	Amount     int    `gorm:"column:amount" json:"amount"`
	DetailJson string `gorm:"column:detail_json" json:"detail_json"`
	Remark     string `gorm:"column:remark" json:"remark"`
	Logs       string `gorm:"column:logs" json:"logs"`
	State      int    `gorm:"state" json:"state" json:"state"`

	CreatedAt time.Time `gorm:"column:created_at" json:"created_at"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at"` // 修改时间
	Bills string `gorm:"column:bills" json:"bills"`

}

func (m *LakalaWithdrawService) TableName() string {
	return "t_lakala_withdraw_service"
}
