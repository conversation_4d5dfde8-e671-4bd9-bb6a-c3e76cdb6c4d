package models

import (
	"time"

	"gorm.io/gorm"
)

type AdvertMaterial struct {
	ID                       int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	AdvertMaterialCategoryId int       `gorm:"column:advert_material_category_id"` // 宣传材料分类ID
	Cover                    string    `gorm:"column:cover"`                       // 宣传材料封面图
	CreatedAt                time.Time `gorm:"column:created_at"`                  // 创建时间

	File      string         `gorm:"column:file"`       // 设计材料
	NameUg    string         `gorm:"column:name_ug"`    // 维吾尔语名称
	NameZh    string         `gorm:"column:name_zh"`    // 中文名称
	State     int            `gorm:"column:state"`      // 状态： 1 开启， 2 关闭
	UpdatedAt time.Time      `gorm:"column:updated_at"` // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at"` // 删除时间

	AdvertMaterialCategory AdvertMaterialCategory `gorm:"foreignkey:advert_material_category_id;references:id"`
}

func (m *AdvertMaterial) TableName() string {
	return "t_advert_material"
}

type AdvertMaterialWithTakeCount struct {
	AdvertMaterial
	TakedCount int `json:"taked_count"`
}
