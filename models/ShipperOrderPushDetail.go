// Code generated by sql2gorm. DO NOT EDIT.
package models

import (
	"time"
)

// db.Set("gorm:table_options", "ENGINE=InnoDB COMMENT='每个订单推送的配送员'").AutoMigrate(&models.ShipperOrderPushDetail{})
// 区县信息表
type ShipperOrderPushDetail struct {
	ID                          int64      `gorm:"column:id;COMMENT:自增ID;primary_key;AUTO_INCREMENT"`      // 自增编号
	BaseID                      int64      `gorm:"column:base_id;NOT NULL;COMMENT:推送信息;index"`             // 订单编号
	OrderID                     int64      `gorm:"column:order_id;NOT NULL;COMMENT:订单编号;index"`            // 订单编号
	ShipperID                   int64      `gorm:"column:shipper_id;NOT NULL;COMMENT:配送员ID;index"`         // 订单编号
	Distance                    float64    `gorm:"column:distance;NOT NULL;COMMENT:推送时配送员与餐厅距离"`           // 订单编号
	PushedTime                  time.Time  `gorm:"column:pushed_time;NOT NULL;COMMENT:推送的时间"`              // 开始推送时间
	CurrentLevel                int8       `gorm:"column:current_level;default:1;NOT NULL;COMMENT:当前推送级别"` // 当前推送级别
	EstimatedShippingPrice      int        `gorm:"column:estimated_shipping_price;default:0"`              // 推送时估计的配送费
	OrderShippingPrice          int        `gorm:"column:order_shipping_price;default:0"`                  // 订单配送费
	SpecialTimeShippingPrice    int        `gorm:"column:special_time_shipping_price;default:0"`           // 特殊时间配送费
	SpecialWeatherShippingPrice int        `gorm:"column:special_weather_shipping_price;default:0"`        // 特殊天气配送费
	CreatedAt                   time.Time  `gorm:"column:created_at;COMMENT:创建时间;index"`                   // 创建时间
	UpdatedAt                   time.Time  `gorm:"column:updated_at;COMMENT:修改时间"`                         // 修改时间
	DeletedAt                   *time.Time `gorm:"column:deleted_at;COMMENT:删除时间"`                         // 删除时间
}

func (m *ShipperOrderPushDetail) TableName() string {
	return "t_shipper_order_push_detail"
}
