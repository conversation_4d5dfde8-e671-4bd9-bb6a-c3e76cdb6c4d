package models
import (
	"gorm.io/gorm"
	"time"
)

// 平台关于信息表
type About struct {
	ID        int       `gorm:"column:id;AUTO_INCREMENT;primary_key"` // 自增编号
	Type      int       `gorm:"column:type;default:0;NOT NULL"`       // 类型（1表示关于平台，2表示使用协议，3表示餐币说明，4表示积分说明）
	Title     int       `gorm:"column:title"`                         // 标题
	TitleUg   string    `gorm:"column:title_ug"`
	TitleZh   string    `gorm:"column:title_zh"`
	Content   int       `gorm:"column:content"` // 内容
	ContentUg string    `gorm:"column:content_ug"`
	ContentZh string    `gorm:"column:content_zh"`
	State     int       `gorm:"column:state"`      // 状态（0表示待审核，1表示已审核）
	CreatedAt time.Time `gorm:"column:created_at"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"` // 修改时间
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at"` // 删除时间
}

func (m *About) TableName() string {
	return "b_about"
}

