package models

import "time"

// YinshangForm
type YinshangForm struct {
	Id                    int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	RestaurantId          int       `gorm:"column:restaurant_id"`          // 店铺id
	AccesserId            string    `gorm:"column:accesser_id"`            // 接入平台id
	RequestDate           string    `gorm:"column:request_date"`           // 请求时间
	RequestSeq            string    `gorm:"column:request_seq"`            // 请求流水号
	AccesserUserId        string    `gorm:"column:accesser_user_id"`       // 平台用户id
	RegMerType            string    `gorm:"column:reg_mer_type"`           // 注册类型（企业，店铺）
	LegalName             string    `gorm:"column:legal_name"`             // 法人身份证姓名
	LegalIdcardNo         string    `gorm:"column:legal_idcard_no"`        // 法人身份证号
	LegalMobile           string    `gorm:"column:legal_mobile"`           // 法人手机号
	LegalEmai             string    `gorm:"column:legal_emai"`             // 法人邮箱
	LegalCardDeadline     string    `gorm:"column:legal_card_deadline"`    // 法人代表证件截止日期
	LegalSex              int       `gorm:"column:legal_sex"`              // 法人性别
	LegalOccupation       int       `gorm:"column:legal_occupation"`       // 法人职业
	LegalmanCareerDesc    string    `gorm:"column:legalmanCareerDesc"`     // 法人职业详细描述
	ShopName              string    `gorm:"column:shop_name"`              // 商户营业名称
	BankNo                string    `gorm:"column:bank_no"`                // 开户行行号
	BankAcctType          string    `gorm:"column:bank_acct _type"`        // 账户类型
	BankAcctNo            string    `gorm:"column:bank_acct _no"`          // 开户行帐号
	BankAcctName          string    `gorm:"column:bank_acct _name"`        // 开户帐号名称
	ShopProvinceId        string    `gorm:"column:shop_province_id"`       // 营业省份 id
	ShopCityId            string    `gorm:"column:shop_city _id"`          // 营业市 id
	ShopCountryId         string    `gorm:"column:shop_country_id"`        // 营业区 id
	ShopAddrExt           string    `gorm:"column:shop_addr_ext"`          // 营业地址补充信息
	ShopLic               string    `gorm:"column:shop_lic"`               // 营业执照号
	MccCode               string    `gorm:"column:mccCode"`                // 行业类别编码
	Product               string    `gorm:"column:product"`                // 开通业务
	HavingFixedBusiAddr   int       `gorm:"column:having_fixed_busi_addr"` // 是否有营业场所
	ShareholderName       string    `gorm:"column:shareholderName"`        // 控股股东姓名
	ShareholderCertno     string    `gorm:"column:shareholderCertno"`      // 控股股东证件号
	ShareholderCertExpire string    `gorm:"column:shareholderCertExpire"`  // 控股股东证件有效期
	ShareholderCertType   string    `gorm:"column:shareholderCertType"`    // 控股股东证件证件类型
	Fax                   string    `gorm:"column:fax"`                    // 商户传真
	LastTerminalManager   string    `gorm:"column:lastTerminalManager"`    // 终端维护经理
	LastClientManager     string    `gorm:"column:lastClientManager"`      // 客户维护经理
	ServiceDistrict       string    `gorm:"column:serviceDistrict"`        // 所属服务区域
	DetailDistrict        string    `gorm:"column:detailDistrict"`         // 细分服务区域
	DevelopingDept        string    `gorm:"column:developingDept"`         // 发展部门
	DevelopingPersonID    string    `gorm:"column:developingPersonID"`     // 发展人
	BnfList               string    `gorm:"column:bnfList"`                // 受益人列表
	LegalmanHomeAddr      string    `gorm:"column:legalmanHomeAddr"`       // 法人家庭地址
	PicList               string    `gorm:"column:pic_list"`               // 上传图片列表
	Remark                string    `gorm:"column:remark"`                 // 备注
	UmsQrcodeList         string    `gorm:"column:ums_qrcode_list"`        // 二维码 id列表
	UmsRegId              string    `gorm:"column:ums_reg_id"`             // 自助签约平台流水号
	MerNo                 string    `gorm:"column:mer_no"`                 // 商户号
	CompanyNo             string    `gorm:"column:company_no"`             // 企业号码
	CompanyAccount        string    `gorm:"column:company_account"`        // 对公账号
	MappInfoList          string    `gorm:"column:mapp_info_list"`         // 商户多应用
	ShopAddrUg            string    `gorm:"column:shop_addr_ug"`           // 店铺地址 ug
	ShopAddrZh            string    `gorm:"column:shop_addr_zh"`           // 店铺地址 zh
	RegisterAmount        string    `gorm:"column:register_amount"`        // 注册资本
	RegisterDate          string    `gorm:"column:register_date"`          // 成立日期
	IsLegalPerson         int       `gorm:"column:is_legal_person"`        // 是否为收益所有人
	FoodCert              string    `gorm:"column:food_cert"`              // 食品经营许可证
	ShopUrl               string    `gorm:"column:shop_url"`               // 店铺网址
	ShopWebName           string    `gorm:"column:shop_web_name"`          // 店铺网址名称
	ShopWebIp             string    `gorm:"column:shop_web_ip"`            // 店铺网站ip
	ShopPostCode          string    `gorm:"column:shop_post_code"`         // 经营地址行政区规划代码
	ShopFrontImg          string    `gorm:"column:shop_front_img"`         // 店铺门头照片
	ShopInsideImg         string    `gorm:"column:shop_inside_img"`        // 店铺内部照片
	ShopAdminName         string    `gorm:"column:shop_admin_name"`        // 店铺管理员姓名
	ShopAdminIdcard       string    `gorm:"column:shop_admin_idcard"`      // 店铺管理员身份证号
	ShopAdminMobile       string    `gorm:"column:shop_admin_mobile"`      // 店铺管理员手机号
	ShopAdminEmail        string    `gorm:"column:shop_admin_email"`       // 店铺管理员邮箱
	RegisterAddress       string    `gorm:"column:register_address"`       // 注册地址
	RegisterType          string    `gorm:"column:register_type"`          // 登记类型
	State                 int       `gorm:"column:state"`                  // 结果状态
	AdminId               int       `gorm:"column:admin_id"`               // 管理员id
	CreatedAt             time.Time `gorm:"column:created_at"`
	UpdatedAt             time.Time `gorm:"column:updated_at"`
	DeletedAt             string    `gorm:"column:deleted_at"`
}

func (m *YinshangForm) TableName() string {
	return "yinshang_form"
}
