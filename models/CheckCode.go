package models

import (
	"database/sql"
	"gorm.io/gorm"
	"time"
)



type CheckCode struct {
	Id sql.NullInt32 `gorm:"column:id;primary_key;AUTO_INCREMENT;comment:'自增编号'"`
	Type string `gorm:"column:type;default:1;comment:'1为用户，2为管理员，商家'"`
	Mobile string `gorm:"column:mobile;NOT NULL;comment:'手机号'"`
	Code string `gorm:"column:code;NOT NULL;comment:'验证码'"`
	State int8 `gorm:"column:state;NOT NULL;comment:'状态(１可用，　０不可用)'"`
	IpAddr int64 `gorm:"column:ip_addr;default:0;comment:'用户IP地址'"`
	CreatedAt time.Time      `gorm:"column:created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index"`
}

func (t *CheckCode) TableName() string {
	return "t_check_code"
}