package models

import (
	"time"
)

type LakalaSplit struct {
	ID int `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	
	ServiceId int 	`gorm:"service_id"`

	ResId           int       `gorm:"res_id" json:"res_id"`
	FromMerno       string    `gorm:"from_merno" json:"from_merno"`
	ToMerno         string    `gorm:"to_merno" json:"to_merno"`
	Amount          int       `gorm:"amount" json:"amount"`
	OutOrderNo      string    `gorm:"out_order_no" json:"out_order_no"`
	AccountType     string    `gorm:"account_type" json:"account_type"`
	SplitRuleData   string    `gorm:"split_rule_data" json:"split_rule_data"`
	OrderDesc       string    `gorm:"order_desc" json:"order_desc"`
	Error        string    `gorm:"error" json:"error"`
	ErrorCode        string    `gorm:"error_code" json:"error_code"`
	OrderNo         string    `gorm:"order_no" json:"order_no"`
	SplitSeqNo      string    `gorm:"split_seq_no" json:"split_seq_no"`
	SplitRuleResult string    `gorm:"split_rule_result" json:"split_rule_result"`
	State           int       `gorm:"state" json:"state"`
	CreatedAt       time.Time `gorm:"column:created_at" json:"created_at"` // 创建时间
	UpdatedAt       time.Time `gorm:"column:updated_at" json:"updated_at"` // 修改时间

}

func (m *LakalaSplit) TableName() string {
	return "t_lakala_split"
}
