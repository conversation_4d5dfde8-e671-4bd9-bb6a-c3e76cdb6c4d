package models

import (
	"time"

	"gorm.io/gorm"
)

type LotteryActivityLevelCoupon struct {
	ID                        int           `gorm:"primaryKey;autoIncrement" json:"id"`
    LotteryActivityGroupCouponID int       `gorm:"index;comment:优惠券礼包ID" json:"lottery_activity_group_coupon_id"`
    LotteryID                 int           `gorm:"index;comment:活动id" json:"lottery_id"`
    LotteryActivityLevelID    int           `gorm:"index;comment:抽奖活动等级" json:"lottery_activity_level_id"`
    CouponID                  int           `gorm:"index;comment:优惠券id" json:"coupon_id"`
    TimeType                  int           `gorm:"default:1;comment:1,按天  2.截止日" json:"time_type"`
    ActiveDate                int           `gorm:"comment:优惠券有效期" json:"active_date"`
    StartDate                 time.Time     `gorm:"type:datetime;comment:优惠券开始使用时间" json:"start_date"`
    EndDate                   time.Time     `gorm:"type:datetime;comment:优惠券过期时间" json:"end_date"`
    State                     int           `gorm:"index;comment:状态" json:"state"`
    AdminID                   int           `gorm:"comment:创建人" json:"admin_id"`
    CreatedAt                 time.Time     `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
    UpdatedAt                 time.Time     `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
    DeletedAt         gorm.DeletedAt `gorm:"type:timestamp" json:"deleted_at"`
}

func (LotteryActivityLevelCoupon) TableName() string {
	return "t_lottery_activity_level_coupon"
}
