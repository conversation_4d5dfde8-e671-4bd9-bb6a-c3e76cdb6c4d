/***
 * @Author: rzbb <EMAIL>
 * @Date: 2023-07-31 17:31:42
 * @LastEditors: rzbb <EMAIL>
 * @LastEditTime: 2023-08-01 13:27:11
 * @FilePath: \mulazim-api-go\models\LakalaBankCard.go
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package models

import (
	"time"
)

type LakalaBankCard struct {
	ID int `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号

	ResId           int    `gorm:"column:res_id"`
	MemberNo        string `gorm:"column:member_no"`
	CardNo          string `gorm:"column:card_no"`
	CardId          string `gorm:"column:card_id"`
	BankCode        string `gorm:"column:bank_code"`
	BankName        string `gorm:"column:bank_name"`
	BindTime        string `gorm:"column:bind_time"`
	CardType        int    `gorm:"column:card_type"`
	BankAccountProp int    `gorm:"column:bank_account_prop"`
	BindChannelCode string `gorm:"column:bind_channel_code"`
	IsSettleCard    int    `gorm:"column:is_settle_card"`

	CreatedAt time.Time `gorm:"column:created_at" json:"created_at"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at"` // 修改时间

}

func (m *LakalaBankCard) TableName() string {
	return "t_lakala_bank_cards"
}
