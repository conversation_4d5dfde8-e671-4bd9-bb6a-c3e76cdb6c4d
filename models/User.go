// Code generated by sql2gorm. DO NOT EDIT.
package models

import (
	"time"

	"gorm.io/gorm"
)

type User struct {
	ID            int          `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	Name          string       `gorm:"column:name;NOT NULL"`             // 用户名称
	Mobile        string       `gorm:"column:mobile;NOT NULL"`           // 手机号
	Avatar        string       `gorm:"column:avatar"`                    // 用户头像
	Password      string       `gorm:"column:password"`                  // 密码（md5）
	RememberToken string       `gorm:"column:remember_token"`            // remember_token
	AuthToken     string       `gorm:"column:auth_token"`                // wap 验证用户身份
	SearialNumber string       `gorm:"column:searial_number"`            // 序列号
	Money         uint         `gorm:"column:money;default:0;NOT NULL"`  // 用户餐币(单位：分）
	Points        uint         `gorm:"column:points;default:0;NOT NULL"` // 用户积分
	PhoneBrand    string       `gorm:"column:phone_brand"`               // 手机品牌
	OsType        int          `gorm:"column:os_type"`                   // 操作系统类型(1，安卓 2，苹果，3windowphone，4 其他)
	TerminalID    int          `gorm:"column:terminal_id"`               // 终端编号
	OsVersion     string       `gorm:"column:os_version"`                // 操作系统版本
	Regtype       int          `gorm:"column:regtype"`                   // 注册类型 1 Android  2 iPhone 3 Web  4 wap  5 Other
	RegIP         string       `gorm:"column:reg_ip;NOT NULL"`           // 注册ip
	LoginTime     *time.Time    `gorm:"column:login_time"`                // 最后登录时间
	CityID        int          `gorm:"column:city_id"`                   // 城市编号
	BirthDay      *time.Time    `gorm:"column:birth_day"`
	Gender        int          `gorm:"column:gender;default:1;NOT NULL"`        // 性别（1表示男，2表示女）
	OpenID        string       `gorm:"column:open_id"`                          // 用户微信小程序openId
	State         int          `gorm:"column:state;NOT NULL"`                   // 状态（-1已注销、0未审核、1已审核、2已锁定）
	ShipperID     int          `gorm:"column:shipper_id"`                       // 推荐配送员的id
	CreatedAt     time.Time    `gorm:"column:created_at"`                       // 创建时间
	UpdatedAt     time.Time    `gorm:"column:updated_at"`                       // 修改时间
	DeletedAt     gorm.DeletedAt    `gorm:"column:deleted_at"`                       // 删除时间
	CityName      string       `gorm:"-"`                        // 用户微信小程序openId

	UserCityId   int `gorm:"column:user_city_id"`
	UserAreaId   int `gorm:"column:user_area_id"`
	Source        int          `gorm:"column:source"`        //来源

	PushDevices   []PushDevice `gorm:"polymorphic:User;polymorphicValue:t_user` // 激光设备
}

func (m *User) TableName() string {
	return "t_user"
}
