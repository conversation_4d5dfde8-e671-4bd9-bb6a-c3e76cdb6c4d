package models

import (
	"time"

	"gorm.io/gorm"
)

// 配送员意见和故障提交
type ShipperOpinionBug struct {
	ID        int            `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CityID    int            `gorm:"column:city_id;NOT NULL"`
	AreaID    int            `gorm:"column:area_id;NOT NULL"`
	ShipperID int            `gorm:"column:shipper_id;NOT NULL"`
	Type      int            `gorm:"column:type;NOT NULL"`
	Date      string         `gorm:"column:date;NOT NULL"`
	Content   string         `gorm:"column:content;NOT NULL"`
	Images    string         `gorm:"column:images"`
	State     int            `gorm:"column:state"`
	CreatedAt time.Time      `gorm:"column:created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at"`

	City    City  `gorm:"foreignKey:city_id;references:id"`    // 地区
	Area    Area  `gorm:"foreignKey:area_id;references:id"`    // 区域
	Shipper Admin `gorm:"foreignKey:shipper_id;references:id"` // 地区
}

func (m *ShipperOpinionBug) TableName() string {
	return "t_shipper_opinion_bug"
}
