package models

import (
	"time"
)

type LotteryActivityLevelWinner struct {
	ID                          int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	LotteryActivityID           int       `gorm:"column:lottery_activity_id;NOT NULL"`             // 抽奖活动名称
	LotteryActivityLevelID      int       `gorm:"column:lottery_activity_level_id;NOT NULL"`       // 抽奖活动等级ID
	LotteryActivityLevelPrizeID int       `gorm:"column:lottery_activity_level_prize_id;NOT NULL"` // 抽奖活动等级奖品ID
	LuckyUserNo                 int       `gorm:"column:lucky_user_no;NOT NULL"`                   // 预设中奖用户位次： 如果输入100， 会在90-至110之间选择N个位次预设中奖用户
	LuckyUserIndex              int       `gorm:"column:lucky_user_index;NOT NULL"`                // 中奖用户位次
	AdminID                     int       `gorm:"column:admin_id;NOT NULL"`                        // 创建用户ID
	State                       int       `gorm:"column:state;NOT NULL"`                           // 1 有效 2 关闭
	CreatedAt                   time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL"`
	UpdatedAt                   time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL"`
}

func (m *LotteryActivityLevelWinner) TableName() string {
	return "t_lottery_activity_level_winners"
}
