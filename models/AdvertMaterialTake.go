package models

import (
	"time"
)

type AdvertMaterialTake struct {
	ID                             int                            `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	AdvertMaterialCategoryId       int                            `gorm:"column:advert_material_category_id"`    // 宣传材料分类ID
	AdvertMaterialId               int                            `gorm:"column:advert_material_id"`             // 宣传材料ID
	AdvertMaterialPrintBatchId     int                            `gorm:"column:advert_material_print_batch_id"` // 批次号
	AreaId                         int                            `gorm:"column:area_id"`                        // 区域ID
	CityId                         int                            `gorm:"column:city_id"`                        // 城市ID
	CreatedAt                      time.Time                      `gorm:"column:created_at"`                     // 创建时间
	EndCode                        string                         `gorm:"column:end_code"`                       // 穿选单结束编码
	ShipperId                      int                            `gorm:"column:shipper_id"`                     // 配送员ID
	StartCode                      string                         `gorm:"column:start_code"`                     // 宣传单开始编码
	TakedAt                        time.Time                      `gorm:"column:taked_at"`                       // 领取时间
	TakedCount                     int                            `gorm:"column:taked_count"`                    // 领取数量
	UpdatedAt                      time.Time                      `gorm:"column:updated_at"`                     // 更新时间
	AdvertMaterial                 AdvertMaterial                 `gorm:"foreignkey:advert_material_id;references:id"`
	AdvertMaterialShipperUser      []AdvertMaterialShipperUser    `gorm:"foreignkey:advert_material_take_id;references:id"`
	AdvertMaterialShipperUserCount AdvertMaterialShipperUserCount `gorm:"foreignkey:advert_material_take_id;references:id"` // 客户数量数量
	Shipper                        Admin                          `gorm:"foreignkey:shipper_id;references:id"`
	Category                       AdvertMaterialCategory         `gorm:"foreignkey:advert_material_category_id;references:id"`
}

func (m *AdvertMaterialTake) TableName() string {
	return "t_advert_material_take"
}
