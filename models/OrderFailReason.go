package models

import (
	"time"
)

// 订单失败原因备注表
type OrderFailReason struct {
	ID        int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 订单失败原因编号
	Type      int       `gorm:"column:type;NOT NULL"`                 // 失败类型（4配送员能选的原因，）
	Reason    int       `gorm:"column:reason;NOT NULL"`               // 原因
	ReasonUg  string    `gorm:"column:reason_ug"`
	ReasonZh  string    `gorm:"column:reason_zh"`
	State     int       `gorm:"column:state"`      // 状态（0关闭，1开通）
	CreatedAt time.Time `gorm:"column:created_at"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"` // 修改时间
	DeletedAt time.Time `gorm:"column:deleted_at"` // 删除时间
}

func (m *OrderFailReason) TableName() string {
	return "b_order_fail_reason"
}