package models

import (
	"mulazim-api/models/Comments"
	"mulazim-api/models/cms"
	"mulazim-api/models/shipment"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/golang-module/carbon/v2"
)

const (
	ORDER_STATE_WAITING_FOR_PAY     = 1
	ORDER_STATE_CONFIRM             = 2
	ORDER_STATE_WAITING_ACCEPT      = 3
	ORDER_STATE_ACCEPTED            = 4
	ORDER_STATE_READY               = 5
	ORDER_STATE_IN_DELIVERY         = 6
	ORDER_STATE_DELIVERY_COMPLETED  = 7
	ORDER_STATE_CANCELED            = 8
	ORDER_STATE_RESTAURANT_REJECTED = 9
	ORDER_STATE_DELIVERY_FAIL       = 10

	PAY_PLATFORM_ORIGINAL = 0 // 原支付方式
	PAY_PLATFORM_LAKALA   = 1 // 拉卡拉支付
	PAY_PLATFORM_WECHAT_ECOMMERCE   = 4 // 拉卡拉支付


	PAYMENT_CASH = 1 // 现金
	PAYMENT_AGENT_WECHAT = 6 // 代理代付
	CONSUME_AGENT = 3
)

// 今日订单信息表
type OrderToday struct {
	ID                   int       `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"` // 自增编号
	RandomID             string    `gorm:"column:random_id" json:"random_id"`                     // 发给用户的随机ID
	TerminalID           int       `gorm:"column:terminal_id;NOT NULL" json:"terminal_id"`          // 终端编号
	OrderID              string    `gorm:"column:order_id;NOT NULL" json:"order_id"`             // 订单唯一编号
	CategoryID           int       `gorm:"column:category_id;NOT NULL" json:"category_id"`          // 商户类目编号
	CityID               int       `gorm:"column:city_id" json:"city_id"`                       // 下单城市编号
	AreaID               int       `gorm:"column:area_id" json:"area_id"`                       // 区域编号
	StoreID              int       `gorm:"column:store_id;NOT NULL" json:"store_id"`             // 商户编号
	UserID               int       `gorm:"column:user_id;NOT NULL" json:"user_id"`              // 用户编号
	BuildingID           int       `gorm:"column:building_id;NOT NULL" json:"building_id"`          // 大厦编号
	OrderAddress         string    `gorm:"column:order_address;NOT NULL" json:"order_address"`        // 收餐地址
	Name                 string    `gorm:"column:name;NOT NULL" json:"name"`                 // 收餐人名称
	Mobile               string    `gorm:"column:mobile;NOT NULL" json:"mobile"`               // 收餐人手机号
	ConsumeType          int       `gorm:"column:consume_type" json:"consume_type"`                  // 付费类型（0现金,1在线支付）
	PayType              int       `gorm:"column:pay_type" json:"pay_type"`                      // 用什么付费(1 现金, 2 餐币, 3 支付宝,  4 银联, 5 微信)
	OriginalPrice        uint      `gorm:"column:original_price;NOT NULL" json:"original_price"`       // 订单原价格（单位：分）
	OrderPrice           uint      `gorm:"column:order_price;NOT NULL" json:"order_price"`          // 实际价格（单位：分）
	Price                uint      `gorm:"column:price;NOT NULL" json:"price"`                // 实际价格（单位：分）
	Shipment             uint      `gorm:"column:shipment" json:"shipment"`                      // 配送费
	OriginalShipment     uint      `gorm:"column:original_shipment" json:"original_shipment"`             // 原始配送费
	LunchBoxFee          int       `gorm:"column:lunch_box_fee;default:0" json:"lunch_box_fee"`       // 总饭盒费（单位：分）
	MpProfit             uint      `gorm:"column:mp_profit" json:"mp_profit"`                     // 一个订单的属于平台的总利润
	DealerProfit         int       `gorm:"column:dealer_profit" json:"dealer_profit"`                 // 一个订单的属于代理商的总利润
	ResProfit            int      `gorm:"column:res_profit;default:0;NOT NULL" json:"res_profit"`    //店铺收入
	Cash                 uint      `gorm:"column:cash;default:0;NOT NULL" json:"cash"`       // 现金（单位：分）
	Coin                 uint      `gorm:"column:coin;default:0;NOT NULL" json:"coin"`       // 餐币（单位：分）
	Consume              uint      `gorm:"column:consume;default:0;NOT NULL" json:"consume"`    // 充值金额（单位：分）
	Description          string    `gorm:"column:description;NOT NULL" json:"description"`          // 订单备注
	Timezone             int       `gorm:"column:timezone;default:6" json:"timezone"`            // 预定时间时区：6乌鲁木齐时间，8北京时间
	BookingTime          string    `gorm:"column:booking_time" json:"booking_time"`                  // 预订时间
	PayTime              time.Time `gorm:"column:pay_time" json:"pay_time"`                      // 付费时间
	PrintTime            string    `gorm:"column:print_time" json:"print_time"`                    // 打印时间
	PrintedTime          string    `gorm:"column:printed_time" json:"printed_time"`                  // 订单打印完成时间
	DeliveryTakedTime    time.Time `gorm:"column:delivery_taked_time" json:"delivery_taked_time"`           // 抢订单时间
	DeliveryStartTime    time.Time `gorm:"column:delivery_start_time" json:"delivery_start_time"`           // 开始配送时间
	DeliveryEndTime      time.Time `gorm:"column:delivery_end_time" json:"delivery_end_time"`             // 订单结束时间
	RealShipment         int       `gorm:"column:real_shipment" json:"real_shipment"`                 // 订单配送完后配送员能得到的实际配送费
	DeductionFee         int       `gorm:"column:deduction_fee" json:"deduction_fee"`                 // 配送扣费金额
	Taked                int       `gorm:"column:taked;default:0" json:"taked"`               // 表示是否已被抢单（0未抢单，1已抢单）
	ShipperID            int       `gorm:"column:shipper_id" json:"shipper_id"`                    // 配送员编号
	SentSms              int       `gorm:"column:sent_sms;default:0" json:"sent_sms"`            // 是否发送过短信（0没发送，1已发送）
	DeleteFlag           int       `gorm:"column:delete_flag;default:0" json:"delete_flag"`         // 用户是否删除该订单（0正常，1用户已删除）
	RefundType           int       `gorm:"column:refund_type;default:0" json:"refund_type"`         // 1　金额返回美滋来餐币，2　金额返回用户付款账户，3表示退回给代理余额
	RefundChanel         int       `gorm:"column:refund_chanel;default:0" json:"refund_chanel"`       // 取消订单渠道 （0表示没取消，1 表示实时订单自动被系统取消  2表示从后台取消 3表示商家端取消 4表示 用户端取消）
	Refunded             int       `gorm:"column:refunded;default:0" json:"refunded"`            // 是否已退款（0 为退款， 1 已退款），只能取消的订单有用
	IsDouyin             int       `gorm:"column:is_douyin" json:"is_douyin"`                     // 是否从抖音支付
	SendNotify           int       `gorm:"column:send_notify;default:0" json:"send_notify"`         // 是否给商家发送推送
	SerialNumber         uint      `gorm:"column:serial_number;default:0;NOT NULL" json:"serial_number"`
	ShipperCompleteGrant uint      `gorm:"column:shipper_complete_grant;default:0" json:"shipper_complete_grant"`  // 配送员完成订单授权（1，没有权限、1有权限）
	IsLuckDrawed         int       `gorm:"column:is_luck_drawed;default:0;NOT NULL" json:"is_luck_drawed"` // 是否参加抽奖活动（0表示没有抽奖、2表示以抽奖）
	IsAutoRefund         int       `gorm:"column:is_auto_refund;default:0" json:"is_auto_refund"`          // 是否是自动退的订单（只有在实时订单有效1表示人工取消的，1表示系统自动取消的订单）
	IsCommented          int       `gorm:"column:is_commented;default:0" json:"is_commented"`            // 是否评论
	Distance             float64   `gorm:"column:distance;default:0.00" json:"distance"`             // 距离 （单位：公里）
	OrderType            int       `gorm:"column:order_type;default:1" json:"order_type"`              // 0:表示预订单；1：表示实时订单
	Called               uint      `gorm:"column:called;default:0;NOT NULL" json:"called"`
	ResIncome            int      `gorm:"column:res_income;default:0;NOT NULL" json:"res_income"`    //店铺收入
	ActualPaid           uint      `gorm:"column:actual_paid;default:0;" json:"actual_paid"`           //实际支付金额
	TotalDiscountAmount  uint      `gorm:"column:total_discount_amount;default:0;" json:"total_discount_amount"` //优惠金额  主要用于 抽奖和春节活动

	ShipperReward      int      `gorm:"column:shipper_reward;default:0;" json:"shipper_reward"`//配送员客户拓展奖励

	PriceMarkupInPrice int      `gorm:"column:price_markup_in_price;default:0;" json:"price_markup_in_price"` //成本价 合计
	OrderPriceRes int      `gorm:"column:order_price_res;default:0;" json:"order_price_res"` //餐厅靠单的价格

	State            int                      `gorm:"column:state;NOT NULL" json:"state"`                         // 订单状态（跟b_order_state表关联）
	CreatedAt        time.Time                `gorm:"column:created_at" json:"created_at"`                             // 创建时间
	UpdatedAt        time.Time                `gorm:"column:updated_at" json:"updated_at"`                             // 修改时间
	DeletedAt        time.Time                `gorm:"column:deleted_at" json:"deleted_at"`                             // 删除时间
	CashClearState   int                      `gorm:"column:cash_clear_state;default:0" json:"cash_clear_state"`             // 配送员是否缴纳现金订单款额
	CashClearTime    time.Time                `gorm:"column:cash_clear_time" json:"cash_clear_time"`                        // 配送员缴纳现金时间
	CashClearChannel int                      `gorm:"column:cash_clear_channel" json:"cash_clear_channel"`                     // 1:公众号，2.h5支付 3:配送员客户端
	CashClearAdmin   int                      `gorm:"column:cash_clear_admin" json:"cash_clear_admin"`                       // 配送员缴纳现金人员
	SeckillState     int                      `gorm:"column:seckill_state;default:0" json:"seckill_state"`                // 0 表示 普通订单 1 表示秒杀订单 2 表示秒杀退款
	OrderTime        int                      `gorm:"column:order_time" json:"order_time"`                             // 0 表示 普通订单 1 表示秒杀订单 2 表示秒杀退款
	OrderDetail      []OrderDetail            `gorm:"foreignkey:order_id;references:id" json:"order_detail"`             //
	LunchBoxDetail   []LunchBoxDetail         `gorm:"foreignkey:order_id;references:id" json:"lunch_box_detail"`             //
	AddressView      AddressView              `gorm:"foreignkey:building_id;references:building_id" json:"address_view"` //
	Restaurant       Restaurant               `gorm:"foreignkey:store_id;references:id" json:"restaurant"`             //
	PayTypes         PayType                  `gorm:"foreignkey:pay_type;references:id" json:"pay_types"`             //
	LunchBox         []LunchBoxOrderDetail    `gorm:"foreignkey:order_id;references:id" json:"lunch_box"`
	MarketingList    []MarketingOrderLog      `gorm:"foreignkey:order_id;references:id" json:"marketing_list"`
	Shipper          Admin                    `gorm:"foreignkey:shipper_id;references:id" json:"shipper"`
	DeliveryType     int                      `gorm:"column:delivery_type" json:"delivery_type"`
	SelfTakeNumber   int                      `gorm:"column:self_take_number" json:"self_take_number"`
	PayPlatform      int                      `gorm:"column:pay_platform" json:"pay_platform"`
	MarketType       int                      `gorm:"column:market_type" json:"market_type"`
	Comments         []Comments.Comment       `gorm:"foreignkey:order_id" json:"comments"`
	Coupon           CouponLogItem            `gorm:"foreignkey:order_id;references:id" json:"coupon"`
	User             User                     `gorm:"foreignkey:id;references:user_id" json:"user"`
	PayLakala        PayLakala                `gorm:"foreignkey:order_id;references:id" json:"pay_lakala"`
	OrderState       cms.OrderState           `gorm:"foreignkey:state;references:id" json:"order_state"`
	LoginTime        RestaurantLastLogin      `gorm:"foreignkey:store_id;references:restaurant_id" json:"login_time"`
	OrderStateLog    []OrderStateLog          `gorm:"foreignkey:order_id;references:id" json:"order_state_log"`
	JSONInfo         string                   `gorm:"column:json_info" json:"json_info"`
	OrderExtend      *OrderExtend             `gorm:"foreignkey:order_id;references:id" json:"order_extend"` // 订单扩展字段
	PushDetail       ShipperOrderPushDetail   `gorm:"foreignkey:order_id;references:id" json:"push_detail"` // 订单推送时的数据
	ShipperIncome    []shipment.ShipperIncome `gorm:"foreignkey:order_id;references:id" json:"shipper_income"` // 订单推送时的数据
	// ShipperIncome       int64  `gorm:"foreignkey:order_id;references:id" json:"shipper_income"` // 订单推送时的数据
	OrderOrigin        string             `gorm:"<-:false;column:order_origin;" json:"order_origin"` // 订单来源
	RestaurantBuilding RestaurantBuilding `gorm:"foreignkey:building_id;references:building_id" json:"restaurant_building"`
	Terminal           Terminal           `gorm:"foreignkey:terminal_id;references:id" json:"terminal"`
	Building           Building           `gorm:"foreignkey:building_id;references:id" json:"building"`

	AdminStore []AdminStore `gorm:"foreignKey:store_id;references:store_id" json:"admin_store"`
	PushDetails       []ShipperOrderPushDetail   `gorm:"foreignkey:order_id;references:id" json:"push_details"` // 订单推送时的数据

	Area           	Area           `gorm:"foreignkey:area_id;references:id" json:"area"`
	TakeOrder       TakeOrder           `gorm:"foreignkey:order_id;references:id" json:"take_order"`

	OrderDelay      OrderDelayed          `gorm:"foreignkey:order_id;references:id" json:"order_delay"`

	MiniGameUserLog      MiniGameUserLog          `gorm:"foreignkey:order_id;references:id" json:"mini_game_user_log"`
	OrderPartRefund 	[]OrderPartRefund `gorm:"foreignkey:order_id;references:id" json:"order_part_refund"`

}
type LunchBoxOrderDetail struct {
	LunchBoxCount int    `gorm:"column:count"`
	ID            int    `gorm:"column:id"`
	FoodID        int    `gorm:"column:food_id"`
	OriginalPrice int    `gorm:"column:original_price"`
	Price         int    `gorm:"column:price"`
	Name          string `gorm:"column:name"`
	OrderID       int    `gorm:"column:order_id"`
	Number        int    
	SumPrice      float64
}

func (m *LunchBoxOrderDetail) TableName() string {
	return "t_order_detail"
}
func (m *OrderToday) TableName() string {
	return "t_order_today"
}

// CanArrvivedShop 检查时候可以到店签到
func (o OrderToday) CanArrvivedShop() bool {
	if o.State == ORDER_STATE_ACCEPTED || o.State == ORDER_STATE_READY {
		return true
	}
	return false
}

type CouponLogItem struct {
	ID      int    `gorm:"column:id"`
	OrderID int    `gorm:"column:order_id"`
	Price   int    `gorm:"column:price"`
	Name    string `gorm:"column:name"`
	State   int    `gorm:"column:state"`
}

func (m *CouponLogItem) TableName() string {
	return "t_coupon_log"
}

type TimeOutOrders struct {
	Id              int                      `gorm:"id"`
	BookingTime     time.Time                `gorm:"booking_time"`
	DeliveryEndTime time.Time                `gorm:"delivery_end_time"`
	Shipment        int                      `gorm:"shipment"`
	ShipperId       int                      `gorm:"shipper_id"`
	OrderOrigin     string                   `gorm:"order_origin"`
	PushDetail      ShipperOrderPushDetail   `gorm:"foreignkey:order_id;references:id"`
	ShipperIncome   []shipment.ShipperIncome `gorm:"foreignkey:order_id;references:id"`
}

// GetFoodsReadyTime
//
// @Description: 获取餐厅准备美食时间
// @Author: Rixat
// @Time: 2023-12-01 09:02:13
// @receiver
// @param c *gin.Context
func (m OrderToday) GetFoodsReadyTime() string {
	if m.OrderExtend != nil && m.OrderExtend.FoodsReadyTime != nil {
		return tools.TimeFormatYmdHis(m.OrderExtend.FoodsReadyTime)
	}
	return ""
}
func (m OrderToday) FoodsReadyRemainMinute() int {
	if m.OrderExtend != nil && m.OrderExtend.FoodsReadyTime != nil {
		t := m.OrderExtend.FoodsReadyTime
		return int(carbon.Now().DiffAbsInMinutes(carbon.Time2Carbon(*t)))
	}
	return 0
}

func (m OrderToday) GetSpecialOrderShipperIncome() int {
	var price int
	if m.MarketType == 2 {
		var seckillLog SeckillLog
		tools.Db.Model(seckillLog).Where("order_id", m.ID).Preload("Seckill.SeckillMarket").Find(&seckillLog)
		if seckillLog.Seckill.SeckillMarket.ShipperIncome > 0 {
			price = seckillLog.Seckill.SeckillMarket.ShipperIncome
		} else {
			price = 0
		}
	}
	return price
}

// IsShipperSpecialPriceOrder
//
// @Description: 验证该特价活动订单是否属于配送员
// @Author: Rixat
// @Time: 2024-02-22 11:24:49
// @receiver 
// @param c *gin.Context
func (m OrderToday) IsShipperSpecialPriceOrder(shipper Admin) bool {
	var seckillLog SeckillLog
	tools.Db.Model(seckillLog).Where("order_id", m.ID).Preload("Seckill.SeckillMarket").Find(&seckillLog)
	specialShipperIds := seckillLog.Seckill.SeckillMarket.Shipper
	ids := strings.Split(specialShipperIds, ",")
	for _, id := range ids {
		if tools.ToInt(id) == shipper.ID {
			return true
		}
	}
	return false
}

/// GetSpecialPriceOrderShipperIds
//
// @Description: 获取特价配送员设置的配送员
// @Author: Rixat
// @Time: 2024-02-27 09:23:13
// @receiver 
// @param c *gin.Context
func (m OrderToday) GetSpecialPriceOrderShipperIds() []int64 {
	var shipperIds []int64
	if(m.MarketType==1 || m.MarketType==0){
		return shipperIds
	}
	var seckillLog SeckillLog
	tools.Db.Model(seckillLog).Where("order_id", m.ID).Preload("Seckill.SeckillMarket").Find(&seckillLog)
	specialShipperIds := seckillLog.Seckill.SeckillMarket.Shipper
	ids := strings.Split(specialShipperIds, ",")
	for _, id := range ids {
		if id != "" {
			shipperIds = append(shipperIds, tools.ToInt64(id))
		}
	}
	return shipperIds
}
