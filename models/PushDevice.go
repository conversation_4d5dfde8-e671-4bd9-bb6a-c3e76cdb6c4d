package models

import (
	"database/sql/driver"
	"time"
)

// PushDevicePlatform 终端类型： android 、 ios
type PushDevicePlatform string

// PushDeviceClient 客户端类型： user （用户端）、 shipper（配送端) 、 merchant (商家端）
type PushDeviceClient string

// PushDeviceLang 语言类型： zh 、 ug
type PushDeviceLang string

const (
	PushDevicePlatformAndroid PushDevicePlatform = "android"
	PushDevicePlatformIos     PushDevicePlatform = "ios"
)

func (st *PushDevicePlatform) Scan(value interface{}) error {
	b, ok := value.([]byte)
	if ok {
		*st = PushDevicePlatform(b)
	}
	return nil
}

func (st PushDevicePlatform) Value() (driver.Value, error) {
	return string(st), nil
}

const (
	PushDeviceClientUser     PushDeviceClient = "user"     // 用户端
	PushDeviceClientShipper  PushDeviceClient = "shipper"  // 配送端
	PushDeviceClientMerchant PushDeviceClient = "merchant" // 商家段
)

func (jdc *PushDeviceClient) Scan(value interface{}) error {
	b, ok := value.([]byte)
	if ok {
		*jdc = PushDeviceClient(b)
	}
	return nil
}

func (jdc PushDeviceClient) Value() (driver.Value, error) {
	return string(jdc), nil
}

const (
	PushDeviceLangZh PushDeviceLang = "zh"
	PushDeviceLangUg PushDeviceLang = "ug"
)

// PushDevice 激光设备信息
type PushDevice struct {
	ID        uint64             `gorm:"column:id;AUTO_INCREMENT;primary_key"`
	Rid       string             `gorm:"column:rid"`                                              // 激光设备ID
	Platform  PushDevicePlatform `gorm:"column:platform" sql:"enum('android', 'ios')"`            // 平台
	UserType  string             `gorm:"column:user_type"`                                        // 用户类型
	UserID    int                `gorm:"column:user_id"`                                          // 用户ID
	Client    PushDeviceClient   `gorm:"column:client" sql:"enum('user', 'shipper', 'merchant')"` // 客户端
	Lang      PushDeviceLang     `gorm:"column:lang" sql:"enum('zh', 'ug')"`                      // 语言: zh, ug
	CreatedAt time.Time          `gorm:"column:created_at"`
	UpdatedAt time.Time          `gorm:"column:updated_at"`
	AdminID    int                `gorm:"column:admin_id"`                                          // 用户ID
	DeviceAppVersion int64          `gorm:"column:device_app_version"`                               // 客户端发来的 版本号
	Brand  string             `gorm:"column:brand"`                                        // 品牌
	
}

func (m *PushDevice) TableName() string {
	return "t_push_device"
}
