package models

import (
	"time"
	"gorm.io/gorm"
)

const (
	PAY_LAKALA_OBJECT_TYPE_ORDER         = "order"
	PAY_LAKALA_OBJECT_TYPE_LOTTERY_ORDER = "lottery_order"

		/**
     * @link http://47.110.246.50:6524/docs/qzt/qzt-1ekbe0j5a5ard
     */
	 PAY_STATUS_READY = 1001; // 未支付
	 PAY_STATUS_PAYING = 1002; // 进行中
	 PAY_STATUS_PAYED = 1004; // 已付款
	 PAY_STATUS_CLOSED = 2002; // 关闭
	 PAY_STATUS_REFUND = 2003; // 已退款
 
	 /**
	  * order status
	  * @link http://47.110.246.50:6524/docs/qzt/qzt-1ekbdvs8bn5qi
	  */
	 ORDER_STATUS_READY = 1001; // 未支付
	 ORDER_STATUS_PARTIAL_PAYED = 1002; // 部分支付
	 ORDER_STATUS_PAYING = 1003; // 进行中
	 ORDER_STATUS_PAYED = 1004; // 已付款
	 ORDER_STATUS_PAY_COMPLETED = 1006; // 交易完成
	 ORDER_STATUS_PARTIAL_CONFIRM = 1007; // 部分确权
	 ORDER_STATUS_CLOSED = 2002; // 关闭（失败）
	 ORDER_STATUS_REFUNED = 2003; // 已退款
 
	
)


// 定义支付状态映射
var PAY_LAKALA_PAY_STATES = map[int]string{
	1001: "未支付",
	1002: "部分支付",
	1003: "进行中",
	1004: "已付款",
	1006: "交易完成",
	1007: "部分确权",
	2002: "关闭（失败）", // 修复括号未闭合的问题
	2003: "已退款",
}


// 定义退款状态映射
var PAY_LAKALA_REFUND_STATES = map[int]string{
	
	1001:"PROCESSING",
    1004:"SUCCESS",
    2002:"REFUNDCLOSE",
}

// 用户拉卡拉支付记录
type PayLakala struct {
	ID                     int64     `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	ObjectType             string     `gorm:"column:object_type;default:order;"`
	ObjectID                int64     `gorm:"column:object_id;NOT NULL"`
	OrderID                int64     `gorm:"column:order_id;NOT NULL"`
	OrderNo                string    `gorm:"column:order_no;NOT NULL"`         // 存管系统订单号
	PayerOpenid            string    `gorm:"column:payer_openid;NOT NULL"`     // 支付人员openid
	SellerMemberNo         string    `gorm:"column:seller_member_no;NOT NULL"` // 卖家会员标识
	OutOrderNo             string    `gorm:"column:out_order_no;NOT NULL"`     // 应用平台订单号
	Amount                 int64     `gorm:"column:amount;NOT NULL"`           // 商品详情
	PayAmount              int64     `gorm:"column:pay_amount;NOT NULL"`       // 订单金额
	TerminalIP             int64     `gorm:"column:terminal_ip;NOT NULL"`      // 用户IP
	OrderName              string    `gorm:"column:order_name;NOT NULL"`       // 订单名称
	SplitMode              int       `gorm:"column:split_mode;NOT NULL"`       // 分账模式：0：即时分账, 1：确权分账
	FrontUrl               string    `gorm:"column:front_url;NOT NULL"`
	BackUrl                string    `gorm:"column:back_url;NOT NULL"`
	PayMethod              string    `gorm:"column:pay_method;NOT NULL"`                 // 支付方式
	SplitRuleData          string    `gorm:"column:split_rule_data"`                     // 分账时必传，分账规则为空时，消费金额中所有钱默认分账给商户分账规则中分账方必须实名并且设置会员角色分账记录总金额=订单金额
	OutRequestNo           string    `gorm:"column:out_request_no;NOT NULL"`             // 平台支付请求号
	PayStatus              int       `gorm:"column:pay_status;default:0;NOT NULL"`       // 支付状态：1001 未支付，1002 进行中，1004 已付款，2002 关闭，2003 已退款，http://47.110.246.50:6524/docs/qzt/qzt-1ekbe0j5a5ard
	OrderStatus            int       `gorm:"column:order_status;default:0;NOT NULL"`     // 订单状态： 1001 未支付， 1002 部分支付， 1003 进行中， 1004 已付款， 1006 交易完成， 1007 部分确权， 2002 关闭（失败）， 2003 已退款， http://47.110.246.50:6524/docs/qzt/qzt-1ekbdvs8bn5qi
	ErrorMessage           string    `gorm:"column:error_message;NOT NULL"`              // 错误信息
	PaySeqNo               string    `gorm:"column:pay_seq_no;NOT NULL"`                 // 支付流水号
	SplitSeqNo             string    `gorm:"column:split_seq_no;NOT NULL"`               // 分账流水号
	SplitRuleResult        string    `gorm:"column:split_rule_result"`                   // 分账会员列表
	BuyerMemberNo          string    `gorm:"column:buyer_member_no;NOT NULL"`            // 买家会员标识
	PayInfo                string    `gorm:"column:pay_info"`                            // 支付信息，原生支付时返回
	QztChannelPayRequestNo string    `gorm:"column:qzt_channel_pay_request_no;NOT NULL"` // 钱帐通请求通道的流水号
	ChannelTradeNo         string    `gorm:"column:channel_trade_no;NOT NULL"`           // 渠道交易流水号（收单）
	ChannelSeqNo           string    `gorm:"column:channel_seq_no;NOT NULL"`             // 渠道支付流水号（收单）
	PayChannelTradeNo      string    `gorm:"column:pay_channel_trade_no;NOT NULL"`       // 支付通道交易流水号（支付宝、微信）
	ThirdPartyPayment      string    `gorm:"column:third_party_payment;NOT NULL"`        // 微信：WECHAT支付宝：ALIPAY
	OpenID                 string    `gorm:"column:open_id;NOT NULL"`                    // 微信/支付宝/云闪付 的openid
	SubOpenID              string    `gorm:"column:sub_open_id;NOT NULL"`                // 微信子appid的openid
	PayTime                *time.Time `gorm:"column:pay_time"`                            // 支付时间
	ConfirmUrl             string    `gorm:"column:confirm_url;NOT NULL"`                // 密码确认的URL地址
	IsConfirm              int       `gorm:"column:is_confirm;default:0;NOT NULL"`       // 是否需要确认
	ErrorCode              string    `gorm:"column:error_code;NOT NULL"`                 // 错误代码
	Message                string    `gorm:"column:message;NOT NULL"`                    // 错误信息
	PayerType              uint      `gorm:"column:payer_type;default:1;NOT NULL"`       // 支付人员类型： 1 :用户、 2 :配送员
	PayFrom                uint      `gorm:"column:pay_from;default:0;NOT NULL"`         // 支付终端来源：0 小程序直接下单支付， 1 公众号支付， 2 H5支付 ， 3: 配送员终端， 4： 用户端App下单支付
	CreatedAt              time.Time `gorm:"column:created_at"`
	UpdatedAt              time.Time `gorm:"column:updated_at"`
	DeletedAt              gorm.DeletedAt `gorm:"column:deleted_at"`
}

func (m *PayLakala) TableName() string {
	return "t_pay_lakala"
}
