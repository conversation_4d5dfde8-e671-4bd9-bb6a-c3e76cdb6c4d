package models

// Code generated by sql2gorm. DO NOT EDIT.

import (
	"time"
)

// 管理员和商家关系表
type AdminStore struct {
	AdminID   int       `gorm:"column:admin_id;NOT NULL"` // 管理员编号
	StoreID   int       `gorm:"column:store_id;NOT NULL"` // 商户编号
	CreatedAt time.Time `gorm:"column:created_at"`        // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"`        // 修改时间
	DeletedAt time.Time `gorm:"column:deleted_at"`        // 删除时间

	Admin Admin `gorm:"foreignKey:admin_id;references:id"`
}

func (m *AdminStore) TableName() string {
	return "b_admin_store"
}
