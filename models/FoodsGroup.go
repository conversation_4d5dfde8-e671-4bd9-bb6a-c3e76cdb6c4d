package models

import (
	"gorm.io/gorm"
	"time"
)

const FOODS_GROUP_REVIEW_STATE_PENDING = 1 // 待审核
const FOODS_GROUP_REVIEW_STATE_APPROVED = 2 // 审核通过
const FOODS_GROUP_REVIEW_STATE_REFUSE = 3 // 被拒绝

type FoodsGroup struct {
	ID              int               `gorm:"primaryKey;autoIncrement;NOT NULL" json:"id"`                     // 主键
	NameZh          string            `gorm:"NOT NULL" json:"name_zh"`                                         // 分组名称(国语)
	NameUg          string            `gorm:"NOT NULL" json:"name_ug"`                                         // 分组名称(维语)
	Weight          int               `gorm:"NOT NULL;default:0" json:"weight"`                                // 排序顺序
	State           int               `gorm:"NOT NULL;default:0" json:"state"`                                 // 状态 0 关闭 1 开启
	ReviewState     int               `gorm:"NOT NULL;default:1" json:"review_state"`                          // 审核状态： 1 待审核、2 审核通过 3、被拒绝
	RestaurantID    int               `gorm:"NOT NULL" json:"restaurant_id"`                                   // 餐厅ID
	RefuseReason    string            `gorm:"default:null" json:"refuse_reason"`                               // 拒绝理由
	ReviewBy        int               `gorm:"default:null" json:"review_by"`                                   // 审核员AdminID
	ReviewAt        *time.Time        `gorm:"default:null" json:"review_at"`                                   // 审核时间
	ReviewAdmin     Admin             `gorm:"foreignKey:review_by;references:id" json:"review_admin"`          // 审核员AdminID
	CreateBy        int               `gorm:"default:null" json:"create_by"`                                   // 创建人AdminID
	UpdateBy        int               `gorm:"default:null" json:"update_by"`                                   // 修改人AdminID
	CreatedAt       *time.Time        `gorm:"default:null" json:"created_at"`                                  // 创建时间
	UpdatedAt       *time.Time        `gorm:"default:CURRENT_TIMESTAMP;autoUpdateTime" json:"updated_at"`      // 修改时间
	DeletedAt       *gorm.DeletedAt   `gorm:"default:null" json:"deleted_at"`                                  // 删除时间，支持软删除
	Restaurant      Restaurant        `gorm:"foreignKey:restaurant_id;references:id" json:"restaurant"`        // 创建的餐厅
	RestaurantFoods []RestaurantFoods `gorm:"foreignKey:foods_group_id;references:id" json:"restaurant_foods"` // 美食列表
	CreateAdmin     Admin             `gorm:"foreignKey:create_by;references:id" json:"create_admin"`
	UpdateAdmin     Admin             `gorm:"foreignKey:update_by;references:id" json:"update_admin"`
}

// TableName
//
//	@Author: YaKupJan
//	@Date: 2024-09-14 11:22:27
//	@Description: 美食分组表
//	@receiver FoodsGroup
//	@return string
func (f *FoodsGroup) TableName() string {
	return "t_foods_group"
}
