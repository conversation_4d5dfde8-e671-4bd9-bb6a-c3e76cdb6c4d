package models

import (
	"time"
)

// 银行列表
type SelfSignBank struct {
	ID        int       `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	NameUg    string    `gorm:"column:name_ug" json:"name_ug"`
	NameZh    string    `gorm:"column:name_zh"  json:"name_zh"`
	State     int       `gorm:"column:state"  json:"state"`
	Index     int       `gorm:"column:index"  json:"index"`
	CreatedAt time.Time `gorm:"column:created_at"  json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"  json:"updated_at"`
	DeletedAt *time.Time `gorm:"column:deleted_at"  json:"deleted_at"`
}

func (m *SelfSignBank) TableName() string {
	return "b_self_sign_bank"
}
