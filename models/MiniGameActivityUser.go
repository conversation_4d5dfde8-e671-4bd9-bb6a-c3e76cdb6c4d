package models

import (
	"gorm.io/gorm"
	"time"
)

// MiniGameActivityUser 参加活动用户
type MiniGameActivityUser struct {
	ID         int            `json:"id" gorm:"id"`
	ActivityId int            `json:"activity_id" gorm:"activity_id"` // 活动ID
	CityId     int            `json:"city_id" gorm:"city_id"`         // 城市ID
	AreaId     int            `json:"area_id" gorm:"area_id"`         // 区县ID
	UserId     int            `json:"user_id" gorm:"user_id"`         // 活动ID
	AllAmount  int            `json:"all_amount" gorm:"all_amount"`   // 总金额
	LeftAmount int            `json:"left_amount" gorm:"left_amount"` // 剩余金额
	CreatedAt  time.Time      `json:"created_at" gorm:"created_at"`   // 创建时间
	UpdatedAt  time.Time      `json:"updated_at" gorm:"updated_at"`   // 更新时间
	DeletedAt  gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"`   // 删除时间
	User             User             `gorm:"foreignKey:user_id;references:id"`     // 用户
	Area             Area             `gorm:"foreignKey:area_id;references:id"`
	MiniGameActivity MiniGameActivity `gorm:"foreignKey:activity_id;references:id"`

}

// TableName 表名称
func (*MiniGameActivityUser) TableName() string {
	return "t_mini_game_activity_user"
}



// MiniGameActivityUser 参加活动用户
type MiniGameActivityUserForCmsStatics struct {
	ID         int            `json:"id" gorm:"id"`
	ActivityId int            `json:"activity_id" gorm:"activity_id"` // 活动ID
	CityId     int            `json:"city_id" gorm:"city_id"`         // 城市ID
	AreaId     int            `json:"area_id" gorm:"area_id"`         // 区县ID
	UserId     int            `json:"user_id" gorm:"user_id"`         // 活动ID
	AllAmount  int            `json:"all_amount" gorm:"all_amount"`   // 总金额
	LeftAmount int            `json:"left_amount" gorm:"left_amount"` // 剩余金额
	User             User             `gorm:"foreignKey:user_id;references:id"`     // 用户
	Area             Area             `gorm:"foreignKey:area_id;references:id"`
	MiniGameActivity MiniGameActivity `gorm:"foreignKey:activity_id;references:id"`

	GameCount float64 `gorm:"game_count"`
	SpendAmount float64 `gorm:"spend_amount"`
	TakeOrderCount float64 `gorm:"take_order_count"`
	NewUserCount int64 `gorm:"new_user_count"`
}

// TableName 表名称
func (*MiniGameActivityUserForCmsStatics) TableName() string {
	return "t_mini_game_activity_user"
}
