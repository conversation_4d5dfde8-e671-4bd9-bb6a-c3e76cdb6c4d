package models

import (
	"time"

	"gorm.io/gorm"
)

// AdvertMaterialPrintBatch 宣传材料打印批次

const AdvertMaterialPrintBatchStateWait = 1
const AdvertMaterialPrintBatchStateGenerating = 2
const AdvertMaterialPrintBatchStateGenerated = 3

type AdvertMaterialPrintBatch struct {
	ID               int       `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	AdvertMaterialId int       `gorm:"column:advert_material_id" json:"material_id"` // 宣传材料ID
	BatchNo          int       `gorm:"column:batch_no" json:"batch_no"`              // 批次号
	Count            int       `gorm:"column:count" json:"count"`                    // 打印数量
	CreatedAt        time.Time `gorm:"column:created_at" json:"created_at"`          // 创建时间
	CreatorId        int       `gorm:"column:creator_id" json:"creator_id"`          // 创建者

	EndCode        string         `gorm:"column:end_code" json:"end_code"`     // 结束编号
	StartCode      string         `gorm:"column:start_code" json:"start_code"` // 开始编号
	State          int            `gorm:"column:state" json:"state"`           // 状态： 1 待生成代码， 2 正在生成代码、3 已生成代码
	UpdatedAt      time.Time      `gorm:"column:updated_at" json:"updated_at"` // 更新时间
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at"`                   // 删除时间
	AdvertMaterial AdvertMaterial `gorm:"foreignKey:advert_material_id;references:id" json:"-"`
}

func (m *AdvertMaterialPrintBatch) TableName() string {
	return "t_advert_material_print_batch"
}
