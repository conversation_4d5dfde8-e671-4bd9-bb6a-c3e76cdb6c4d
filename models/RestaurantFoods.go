package models

import (
	"mulazim-api/resources"
	"mulazim-api/tools"
	"time"

	"gorm.io/gorm"
)

const (
	RESTAURANTFOODS_STATE_CLOSE   = 0 // 关闭
	RESTAURANTFOODS_STATE_OPEN    = 1 // 开通
	RESTAURANTFOODS_STATE_SOLDOUT = 2 // 已售完
	RESTAURANTFOODS_STATE_INPREVIEW = 3 // 待审核

	RestaurantFoodsTypeNormal = 0 // 普通美食
	RestaurantFoodsTypeSpec  = 1  // 规格美食
	RestaurantFoodsTypeCombo = 2  // 套餐美食
)

// 餐厅和美食关系表
type RestaurantFoods struct {
	ID                        int            `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`   // 自增编号
	RestaurantID              int            `gorm:"column:restaurant_id;NOT NULL" json:"restaurant_id"`          // 餐厅编号
	RestaurantPrinterID       int            `gorm:"column:restaurant_printer_id;default:0" json:"restaurant_printer_id"` // 餐厅打印机编号
	AllFoodsID                int            `gorm:"column:all_foods_id;NOT NULL" json:"all_foods_id"`
	Name                      int            `gorm:"column:name;NOT NULL" json:"name"` // 美食编号
	NameUg                    string         `gorm:"column:name_ug" json:"name_ug"`
	NameZh                    string         `gorm:"column:name_zh" json:"name_zh"`
	Image                     string         `gorm:"column:image" json:"image"`       // 美食图片
	Description               int            `gorm:"column:description" json:"description"` // 美食说明
	DescriptionUg             string         `gorm:"column:description_ug" json:"description_ug"`
	DescriptionZh             string         `gorm:"column:description_zh" json:"description_zh"`
	BeginTime                 string         `gorm:"column:begin_time;NOT NULL" json:"begin_time"`                               // 配送开始时间
	EndTime                   string         `gorm:"column:end_time;NOT NULL" json:"end_time"`                                 // 配送结束时间
	ReadyTime                 int            `gorm:"column:ready_time;NOT NULL" json:"ready_time"`                               // 准备时间，下订单到饭熟所耗的时间(单位为：分钟）
	IsDistribution            int            `gorm:"column:is_distribution;default:0;NOT NULL" json:"is_distribution"`                // 表示是否配送（0表示可配送，1表示不配送，2可配送，可自取）
	StarAvg                   string         `gorm:"column:star_avg;default:1;NOT NULL" json:"star_avg"`                       // 平均星数
	CommentCount              int            `gorm:"column:comment_count;NOT NULL" json:"comment_count"`                            // 评论次数
	MonthOrderCount           int            `gorm:"column:month_order_count;default:0;NOT NULL" json:"month_order_count"`              // 月订单量
	Price                     uint           `gorm:"column:price;default:0;NOT NULL" json:"price"`                          // 出口价（单位：分）
	OriginalPrice			  int			 `gorm:"column:original_price;default:0;" json:"original_price"`                 // 出口价（单位：分）
	MpPercent                 float64        `gorm:"column:mp_percent;default:0.0;NOT NULL" json:"mp_percent"`                   // 属于平台的百分比（平台负责配送）
	DealerPercent             float64        `gorm:"column:dealer_percent;default:0.0;NOT NULL" json:"dealer_percent"`               // 属于代理商的百分比（平台负责配送）
	MpYourselfTakePercent     float64        `gorm:"column:mp_yourself_take_percent;default:0.0;NOT NULL" json:"mp_yourself_take_percent"`     // 属于平台的百分比（用户自取）
	DealerYourselfTakePercent float64        `gorm:"column:dealer_yourself_take_percent;default:0.0;NOT NULL" json:"dealer_yourself_take_percent"` // 属于代理商的百分比（用户自取）
	Weight                    int            `gorm:"column:weight;default:0" json:"weight"`                                  // 排序
	LunchBoxID                int            `gorm:"column:lunch_box_id" json:"lunch_box_id"`                                      // 饭盒编号
	LunchBoxAccommodate       int            `gorm:"column:lunch_box_accommodate" json:"lunch_box_accommodate"`                             // 一个饭盒能装的美食数量
	LunchBoxFee               int            `gorm:"column:lunch_box_fee" json:"lunch_box_fee"`                                     // 饭盒单价（单位：分）
	FoodQuantity     float64 `gorm:"column:food_quantity;type:decimal(10,2);default:1.00" json:"food_quantity,omitempty"` // 美食量
	FoodQuantityType uint8   `gorm:"column:food_quantity_type;default:3" json:"food_quantity_type,omitempty"` // 美食量分类；例如：1表示份、2表示个、3表示克、4表示公斤
	State                     int            `gorm:"column:state;NOT NULL" json:"state"`                                    // 状态（0关闭，1开通，2已售完）
	MinCount    int `gorm:"column:min_count;NOT NULL" json:"min_count"` // 最低销售限制，比如馒头，最低两个
	IsRecommend int `gorm:"column:is_recommend;NOT NULL" json:"is_recommend"` // 是否推荐：0:否，1:是
	CreatedAt                 time.Time      `gorm:"column:created_at" json:"created_at"`                                        // 创建时间
	UpdatedAt                 time.Time      `gorm:"column:updated_at" json:"updated_at"`                                        // 修改时间
	DeletedAt                 gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`                                        // 删除时间
	Restaurant                Restaurant     `gorm:"foreignkey:restaurant_id;references:id" json:"restaurant"`
	Category                  AllFoods		 `gorm:"foreignkey:all_foods_id;references:id" json:"category"`
	FoodsGroupId			  int			 `gorm:"column:foods_group_id" json:"foods_group_id"` // 美食分组ID
	WeightInGroup			  int			 `gorm:"column:weight_in_group" json:"weight_in_group"` // 美食在分组里的顺序

	FoodsGroup 				  FoodsGroup 	 `gorm:"foreignKey:foods_group_id;references:id" json:"foods_group"`
	LunchBox                  LunchBox       `gorm:"foreignkey:lunch_box_id;references:id" json:"lunch_box"`
	
	// 多对多关联 美食分类表 商家自建的分类
	Categories []FoodsCategory `gorm:"many2many:t_restaurant_foods_category;foreignKey:ID;joinForeignKey:restaurant_foods_id;References:ID;joinReferences:foods_category_id" json:"categories"`

	// 美食是套餐时，不能是规格美食。
	// 美食是普通美食时，可以是规格美食。
	// 套餐中的美食不能是套餐美食。
	FoodType       uint8            `gorm:"column:food_type;default:0" json:"food_type"` // 美食类型：0:普通美食，1:规格，2:套餐美食
	ComboFoodItems []FoodsComboItem `gorm:"foreignKey:combo_id;references:id" json:"combo_food_items"`
	FoodSpecTypes  []FoodSpecType   `gorm:"foreignKey:food_id;references:id" json:"food_spec_types"`
}

type RestaurantFoodsHasPreferential struct {
	ID                        int            `gorm:"column:id;primary_key;AUTO_INCREMENT"`   // 自增编号
	FoodType uint8 `gorm:"column:food_type;default:0" json:"food_type"`                      // 美食类型 0) 普通美食 1) 规格美食 2) 套餐美食
	RestaurantID              int            `gorm:"column:restaurant_id;NOT NULL"`          // 餐厅编号
	RestaurantPrinterID       int            `gorm:"column:restaurant_printer_id;default:0"` // 餐厅打印机编号
	AllFoodsID                int            `gorm:"column:all_foods_id;NOT NULL"`
	Name                      int            `gorm:"column:name;NOT NULL"` // 美食编号
	NameUg                    string         `gorm:"column:name_ug"`
	NameZh                    string         `gorm:"column:name_zh"`
	Image                     string         `gorm:"column:image"`       // 美食图片
	Description               int            `gorm:"column:description"` // 美食说明
	DescriptionUg             string         `gorm:"column:description_ug"`
	DescriptionZh             string         `gorm:"column:description_zh"`
	BeginTime                 string         `gorm:"column:begin_time;NOT NULL"`                               // 配送开始时间
	EndTime                   string         `gorm:"column:end_time;NOT NULL"`                                 // 配送结束时间
	ReadyTime                 int            `gorm:"column:ready_time;NOT NULL"`                               // 准备时间，下订单到饭熟所耗的时间(单位为：分钟）
	IsDistribution            int            `gorm:"column:is_distribution;default:0;NOT NULL"`                // 表示是否配送（0表示可配送，1表示不配送，2可配送，可自取）
	StarAvg                   string         `gorm:"column:star_avg;default:1;NOT NULL"`                       // 平均星数
	CommentCount              int            `gorm:"column:comment_count;NOT NULL"`                            // 评论次数
	MonthOrderCount           int            `gorm:"column:month_order_count;default:0;NOT NULL"`              // 月订单量
	Price                     uint           `gorm:"column:price;default:0;NOT NULL"`                          // 出口价（单位：分）
	MpPercent                 float64        `gorm:"column:mp_percent;default:0.0;NOT NULL"`                   // 属于平台的百分比（平台负责配送）
	DealerPercent             float64        `gorm:"column:dealer_percent;default:0.0;NOT NULL"`               // 属于代理商的百分比（平台负责配送）
	MpYourselfTakePercent     float64        `gorm:"column:mp_yourself_take_percent;default:0.0;NOT NULL"`     // 属于平台的百分比（用户自取）
	DealerYourselfTakePercent float64        `gorm:"column:dealer_yourself_take_percent;default:0.0;NOT NULL"` // 属于代理商的百分比（用户自取）
	Weight                    int            `gorm:"column:weight;default:0"`                                  // 排序
	LunchBoxID                int            `gorm:"column:lunch_box_id"`                                      // 饭盒编号
	LunchBoxAccommodate       int            `gorm:"column:lunch_box_accommodate"`                             // 一个饭盒能装的美食数量
	LunchBoxFee               int            `gorm:"column:lunch_box_fee"`                                     // 饭盒单价（单位：分）
	FoodsGroupId			  int			 `gorm:"column:foods_group_id"` // 美食分组ID
	State                     int            `gorm:"column:state;NOT NULL"`                                    // 状态（0关闭，1开通，2已售完）
	CreatedAt                 time.Time      `gorm:"column:created_at"`                                        // 创建时间
	UpdatedAt                 time.Time      `gorm:"column:updated_at"`                                        // 修改时间
	DeletedAt                 gorm.DeletedAt `gorm:"column:deleted_at"`                                        // 删除时间
	Restaurant                Restaurant     `gorm:"foreignkey:restaurant_id;references:id"`
	FoodsPreferential         FoodsPreferential     `gorm:"foreignkey:restaurant_foods_id;references:id"`
}

func (m *RestaurantFoods) TableName() string {
	return "t_restaurant_foods"
}

func (m *RestaurantFoodsHasPreferential) TableName() string {
	return "t_restaurant_foods"
}

// 套餐信息
func (mdl *RestaurantFoods) GetComboItems(food RestaurantFoods,language string) []resources.ComboItems {
	comboItems := make([]resources.ComboItems, 0)
	for _, item := range food.ComboFoodItems {
		comboItem := resources.ComboItems{
			ID: item.ID,
			FoodID: item.FoodID,
			SpecID: item.SpecID,
			Count: item.Count,
			BeginTime: item.RestaurantFood.BeginTime,
			EndTime: item.RestaurantFood.EndTime,
			FoodType: tools.ToInt(item.FoodType),
			Image: tools.CdnUrl(item.RestaurantFood.Image),
			OriginalImage: item.RestaurantFood.Image,
			ReadyTime: item.RestaurantFood.ReadyTime,
			FoodName: tools.GetNameByLang(item.RestaurantFood,language),
			FoodNameUg: item.RestaurantFood.NameUg,
			FoodNameZh: item.RestaurantFood.NameZh,
			FoodImage: tools.CdnUrl(item.RestaurantFood.Image),
			Price: tools.ToInt(item.RestaurantFood.Price),
			LunchBoxID:item.RestaurantFood.LunchBoxID,
			LunchBoxFee:item.RestaurantFood.LunchBoxFee,
			LunchBoxAccommodate:item.RestaurantFood.LunchBoxAccommodate,
		}
		if item.SelectedSpec != nil {
			comboItem.SelectedSpec = item.SelectedSpec.GetOptions(*item.SelectedSpec,language)
			comboItem.Price = item.SelectedSpec.Price
		}
		comboItems = append(comboItems, comboItem)
	}
	return comboItems
}

