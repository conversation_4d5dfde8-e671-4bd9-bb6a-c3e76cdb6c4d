// Code generated by sql2gorm. DO NOT EDIT.
package models

import (
"time"
)

// 通用美食信息基本表
type ShipperUserChange struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	
	AreaId	int	`gorm:"column:area_id"`	// 区域ID	
	CityId	int	`gorm:"column:city_id"`	// 城市ID	
	OldShipperId	int	`gorm:"column:old_shipper_id"`	// 配送员ID	
	NewShipperId	int	`gorm:"column:new_shipper_id"`	// 配送员ID	
	Reason         string       `gorm:"column:reason"` // 原因
	State         int       `gorm:"column:state;NOT NULL"` // 状态（0关闭，1开通）
	CreatedAt     time.Time `gorm:"column:created_at"`     // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at"`     // 修改时间


	City                      City  `gorm:"foreignKey:city_id;references:id"` // 地区
	Area                      Area      `gorm:"foreignKey:area_id;references:id"` // 区域
	OldShipper                Admin      `gorm:"foreignKey:old_shipper_id;references:id"` //原来的配送员
	NewShipper                Admin      `gorm:"foreignKey:new_shipper_id;references:id"` //新的的配送员
	
}

func (m *ShipperUserChange) TableName() string {
	return "t_advert_material_shipper_change"
}

