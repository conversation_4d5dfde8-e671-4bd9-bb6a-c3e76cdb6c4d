package models
import (
	"time"
)

type RestaurantVoiceNotify struct {
	ID               int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	RestaurantID     int       `gorm:"column:restaurant_id;NOT NULL"` // 餐厅ID
	OrderID          int       `gorm:"column:order_id;NOT NULL"`      // 订单ID
	Price            int       `gorm:"column:price;NOT NULL"`         // 订单总额
	OrderType        int       `gorm:"column:order_type"`
	SendNotify       int       `gorm:"column:send_notify"`
	PrintTime        string `gorm:"column:print_time"`
	BookingTime      string `gorm:"column:booking_time;NOT NULL"` // 订单时间
	QueryNoticeBegin time.Time `gorm:"column:query_notice_begin"`    // 查询通知开始时间
	QueryNoticeEnd   time.Time `gorm:"column:query_notice_end"`      // 查询通知结束时间
	QueryLastTime    time.Time `gorm:"column:query_last_time"`       // 最后查询时间
	QueryTime        int       `gorm:"column:query_time"`            // 查询次数
	PushNoticeBegin  time.Time `gorm:"column:push_notice_begin"`     // 查询通知开始时间
	PushSend         int       `gorm:"column:push_send"`             // 推送已发送
	CallNoticeBegin  time.Time `gorm:"column:call_notice_begin"`     // 查询通知开始时间
	CallSend         int       `gorm:"column:call_send"`             // 已打电话
	State            int       `gorm:"column:state;default:1"`       // 不需要读了
	CreatedAt        time.Time `gorm:"column:created_at"`            // 创建时间
	UpdatedAt        time.Time `gorm:"column:updated_at"`            // 修改时间
	DeletedAt        time.Time `gorm:"column:deleted_at"`            // 删除时间
}

func (m *RestaurantVoiceNotify) TableName() string {
	return "t_restaurant_voice_notify"
}
