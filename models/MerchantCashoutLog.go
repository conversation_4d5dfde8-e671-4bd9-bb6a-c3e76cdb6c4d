package models
// Code generated by sql2gorm. DO NOT EDIT.

import (
"time"
)

type MerchantCashoutLog struct {
	ID                 int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	RestaurantID       int       `gorm:"column:restaurant_id;NOT NULL" json:"restaurant_id"` // 餐厅编号
	Mobile             string    `gorm:"column:mobile;NOT NULL"`
	RealName           string    `gorm:"column:real_name"`                  // 收款人真实姓名
	IDNumber           string    `gorm:"column:id_number"`                  // 收款人身份证号
	Openid             string    `gorm:"column:openid;NOT NULL"`            // 餐厅收款人OpenID号或银行卡卡号
	PayeeName          string    `gorm:"column:payee_name"`                 // 微信NickName或餐厅收款人姓名
	PartnerTradeNo     string    `gorm:"column:partner_trade_no;NOT NULL"`  // 商户订单号，需保持历史全局唯一性
	WechatPaymentNo    string    `gorm:"column:wechat_payment_no;NOT NULL"` // 微信付款单号
	CashoutAmount      string    `gorm:"column:cashout_amount;NOT NULL"`    // 提现金额q
	CashoutTime        time.Time `gorm:"column:cashout_time;NOT NULL"`      // 提现成功时间
	CashoutWxTax       int       `gorm:"column:cashout_wx_tax"`             // 转账到银行卡手续费
	CashoutWxPaymentNo string    `gorm:"column:cashout_wx_payment_no"`      // 转账到银行卡回复单号
	CreatedAt          time.Time `gorm:"column:created_at"`                 // 创建时间
	UpdatedAt          time.Time `gorm:"column:updated_at"`                 // 修改时间
	DeletedAt          *time.Time `gorm:"column:deleted_at"`                 // 删除时间
	
	CashPlatform  int `gorm:"column:cash_platform"` //
	WithdrawId		  int `gorm:"column:withdraw_id"` //


	CashoutDetails	   []*Mulazimpaytoresagent `gorm:"foreignKey:payment_no;references:wechat_payment_no"`
	LakalaCashoutDetails	   []*LakalaWithdrawService `gorm:"foreignKey:id;references:withdraw_id"`
	//CashoutDetails	   []Mulazimpaytoresagent `gorm:"foreignKey:res_id;references:restaurant_id"`
}


func (m *MerchantCashoutLog) TableName() string {
	return "t_merchant_cashout_log"
}

type MerchantCashoutLogLakala struct {
	ID                 int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	RestaurantID       int       `gorm:"column:restaurant_id;NOT NULL" json:"restaurant_id"` // 餐厅编号
	Mobile             string    `gorm:"column:mobile;NOT NULL"`
	RealName           string    `gorm:"column:real_name"`                  // 收款人真实姓名
	IDNumber           string    `gorm:"column:id_number"`                  // 收款人身份证号
	Openid             string    `gorm:"column:openid;NOT NULL"`            // 餐厅收款人OpenID号或银行卡卡号
	PayeeName          string    `gorm:"column:payee_name"`                 // 微信NickName或餐厅收款人姓名
	PartnerTradeNo     string    `gorm:"column:partner_trade_no;NOT NULL"`  // 商户订单号，需保持历史全局唯一性
	WechatPaymentNo    string    `gorm:"column:wechat_payment_no;NOT NULL"` // 微信付款单号
	CashoutAmount      string    `gorm:"column:cashout_amount;NOT NULL"`    // 提现金额
	CashoutTime        time.Time `gorm:"column:cashout_time;NOT NULL"`      // 提现成功时间
	CashoutWxTax       int       `gorm:"column:cashout_wx_tax"`             // 转账到银行卡手续费
	CashoutWxPaymentNo string    `gorm:"column:cashout_wx_payment_no"`      // 转账到银行卡回复单号
	CreatedAt          time.Time `gorm:"column:created_at"`                 // 创建时间
	UpdatedAt          time.Time `gorm:"column:updated_at"`                 // 修改时间
	DeletedAt          *time.Time `gorm:"column:deleted_at"`                 // 删除时间
	
	State  int `gorm:"column:state"` //

	CashPlatform  int `gorm:"column:cash_platform"` //
	
	DetailJson  string `gorm:"column:detail_json"`
	BankName string `gorm:"column:bank_name"`
	CardId  string `gorm:"column:card_id"`  
	CardNo   string `gorm:"column:card"`

	WithdrawTime  time.Time `gorm:"column:withdraw_time"`
	WithdrawState int `gorm:"column:withdraw_state"`
	BankLogo string `gorm:"column:bank_logo"`

 
}



