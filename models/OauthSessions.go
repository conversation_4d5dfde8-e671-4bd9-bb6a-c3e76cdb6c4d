package models
import (
	"time"
)

type OauthSessions struct {
	ID                int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	ClientID          string    `gorm:"column:client_id;NOT NULL"`
	OwnerType         string `gorm:"column:owner_type;default:user;NOT NULL"`
	OwnerID           string    `gorm:"column:owner_id;NOT NULL"`
	ClientRedirectUri string    `gorm:"column:client_redirect_uri"`
	CreatedAt         time.Time `gorm:"column:created_at"`
	UpdatedAt         time.Time `gorm:"column:updated_at"`
}
func (m *OauthSessions) TableName() string {
	return "oauth_sessions"
}
