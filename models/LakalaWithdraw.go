package models

import (
	"time"
)

type LakalaWithdraw struct {
	ID      int `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	ServiceId int `gorm:"column:service_id" json:"service_id"`      //
	SplitId int `gorm:"column:split_id" json:"split_id"`      //
	ResId   int `gorm:"res_id" json:"res_id"`

	Date string `gorm:"column:date" json:"date"`

	Amount       int    `gorm:"column:amount" json:"amount"`
	MemberNo     string `gorm:"column:member_no" json:"member_no"`
	OutOrderNo   string `gorm:"column:out_order_no" json:"out_order_no"`
	AccountType  string `gorm:"column:account_type" json:"account_type"`
	BankCardId   string `gorm:"column:bank_card_id" json:"bank_card_id"`
	OrderName    string `gorm:"order_name" json:"order_name"`
	WithdrawType string `gorm:"withdraw_type" json:"withdraw_type"`

	Exts string `gorm:"exts" json:"exts"`

	Response string `gorm:"response" json:"response"`
	OrderNo  string `gorm:"order_no" json:"order_no"`
	Error    string `gorm:"error" json:"error"`

	State     int       `gorm:"state" json:"state"`
	CreatedAt time.Time `gorm:"column:created_at" json:"created_at"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at"` // 修改时间

}

type LakalaWithdrawDetail struct {
	ID         int    `gorm:"column:id;primary_key;AUTO_INCREMENT"`  // 自增编号
	WithdrawId int    `gorm:"column:withdraw_id" json:"withdraw_id"` //
	BillId     int    `gorm:"column:bill_id" json:"bill_id"`         //
	Date       string `gorm:"column:date" json:"date"`               //
	CardId     string `gorm:"column:card_id" json:"card_id"`         //

	State     int       `gorm:"state" json:"state"`
	CreatedAt time.Time `gorm:"column:created_at" json:"created_at"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at"` // 修改时间

}

func (m *LakalaWithdrawDetail) TableName() string {
	return "t_lakala_withdraw_detail"
}

func (m *LakalaWithdraw) TableName() string {
	return "t_lakala_withdraw"
}
