package models


import (
	"time"
)

// 大小栏目信息表
type Category struct {
	ID        int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	ParentID  int       `gorm:"column:parent_id;default:0"`           // 父类型编号
	Name      int       `gorm:"column:name"`                          // 类目名称
	NameUg    string    `gorm:"column:name_ug"`
	NameZh    string    `gorm:"column:name_zh"`
	Icon      string    `gorm:"column:icon"`                      // 类目图标
	Rgb       string    `gorm:"column:rgb"`                       // 分类背景颜色
	PageType  int       `gorm:"column:page_type"`                 // 客户端上跳转页面的类型（1表示跳转餐厅商户列表，2表示非餐厅商户列表）
	LinkUrl   string    `gorm:"column:link_url"`                  // 链接URL（page_type等于0时有效）
	Weight    int       `gorm:"column:weight;default:0;NOT NULL"` // 排序
	Active    int       `gorm:"column:active;default:0"`          // 在客户端是否可用（０不可用，　１　可用）
	State     int       `gorm:"column:state"`                     // 类目状态（0关闭，1开通）
	CreatedAt time.Time `gorm:"column:created_at"`                // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"`                // 修改时间
	DeletedAt time.Time `gorm:"column:deleted_at"`                // 删除时间
}

func (m *Category) TableName() string {
	return "b_category"
}

