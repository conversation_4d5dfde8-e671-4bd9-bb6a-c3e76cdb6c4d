package models

import "time"

type SelfSignBnf struct {
	Id                   int        `gorm:"column:id;primary_key;AUTO_INCREMENT"`    // 自增ID
	MerInfoId            int        `gorm:"column:mer_info_id;NOT NULL"`             // t_self_sign_merchant_info 表的 id
	BnfName              string     `gorm:"column:bnf_name;NOT NULL"`                // 股东（受益人）名字
	BnfIdcardNum         string     `gorm:"column:bnf_idcard_num;NOT NULL"`          // 股东身份证号
	BnfAddress           string     `gorm:"column:bnf_address;NOT NULL"`             // 地址
	BnfIdcardLimitedType int        `gorm:"column:bnf_idcard_limited_type;NOT NULL"` // 控股股东执照有限期类型( 0：短期  1：长期)
	BnfIdcardStart       time.Time  `gorm:"column:bnf_idcard_start;NOT NULL"`        // 控股股东身份证起效日期
	BnfIdcardEnd         time.Time  `gorm:"column:bnf_idcard_end;NOT NULL"`          // 控股股东身份证失效日期，如果长期：9999-12-31
	CreatedAt            time.Time  `gorm:"column:created_at"`                       // 创建时间
	UpdatedAt            time.Time  `gorm:"column:updated_at"`                       // 更新时间
	DeletedAt            *time.Time `gorm:"column:deleted_at"`                       // 删除时间
}

func (m *SelfSignBnf) TableName() string {
	return "t_self_sign_bnf"
}
