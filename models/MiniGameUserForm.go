package models

import (
    
    "time"

    "gorm.io/gorm"
)

const (
    MiniGameActivityUserFormStatePending    = 0  // 待审核
    MiniGameActivityUserFormStateApproved   = 1  // 审核通过
    MiniGameActivityUserFormStateRefused    = 2  // 审核拒绝
)

type MiniGameActivityUserForm struct {
    ID            int       `gorm:"primaryKey;autoIncrement" json:"id"`
    Type          int       `gorm:"not null" json:"type"` // 1. 雪花游戏 2.情人节活动
    ActivityID    int       `gorm:"not null" json:"activity_id"`
    CityID        int      `gorm:"default:null" json:"city_id,omitempty"`
    AreaID        int      `gorm:"default:null" json:"area_id,omitempty"`
    UserID        int       `gorm:"not null" json:"user_id"`
    Image         *string   `gorm:"type:varchar(255);default:null" json:"image"`
    Content       *string   `gorm:"type:longtext;default:null" json:"content"`
    
    Gender        int       `gorm:"default:2" json:"gender"` // 0:女  1:男  2:没有选
    Mobile        string   `gorm:"type:varchar(255);default:null" json:"mobile,omitempty"`
    FavoriteCount int      `gorm:"default:null" json:"favorite_count,omitempty"`
    ShareCount    int      `gorm:"default:null" json:"share_count,omitempty"`
    ViewCount     int      `gorm:"default:null" json:"view_count,omitempty"`
    RefuseContent *string   `gorm:"type:text;default:null" json:"refuse_content,omitempty"`
    State         int      `gorm:"default:null" json:"state,omitempty"` // 0:待审核 1:审核通过  2:审核拒绝
    ReviewTime    *time.Time `gorm:"default:null" json:"review_time,omitempty"`
    AdminID       *int      `gorm:"default:null" json:"admin_id,omitempty"`
    CreatedAt     time.Time `gorm:"default:null" json:"created_at"`
    UpdatedAt     time.Time `gorm:"default:null;autoUpdateTime" json:"updated_at"`
    DeletedAt     gorm.DeletedAt `gorm:"index;default:null" json:"deleted_at,omitempty"`

    City     City             `gorm:"foreignKey:city_id;references:id"`
    Area     Area             `gorm:"foreignKey:area_id;references:id"`
    User     User             `gorm:"foreignKey:user_id;references:id"`
    Activity MiniGameActivity `gorm:"foreignKey:activity_id;references:id"`
    Admin    Admin            `gorm:"foreignKey:admin_id;references:id"`
    Topics   []Dictionary     `gorm:"-"`
}
// TableName 表名称
func (*MiniGameActivityUserForm) TableName() string {
	return "t_mini_game_activity_user_form"
}
