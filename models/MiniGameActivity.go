package models

import (
	"gorm.io/gorm"
	"time"
)

// MiniGameActivity 小程序游戏活动
type MiniGameActivity struct {
	ID              int            `json:"id" gorm:"id"`
	NameUg          string         `json:"name_ug" gorm:"name_ug"`                       // 名称维吾尔语
	NameZh          string         `json:"name_zh" gorm:"name_zh"`                       // 名称汉语
	RuleUg          string         `json:"rule_ug" gorm:"rule_ug"`                       // 活动规则维文
	RuleZh          string         `json:"rule_zh" gorm:"rule_zh"`                       // 活动规则中文
	GameRuleUg      string         `json:"game_rule_ug" gorm:"game_rule_ug"`             // 游戏规则维文
	GameRuleZh      string         `json:"game_rule_zh" gorm:"game_rule_zh"`             // 游戏规则中文
	UseAmountRuleUg string         `json:"use_amount_rule_ug" gorm:"use_amount_rule_ug"` // 优惠金额使用规则维文
	UseAmountRuleZh string         `json:"use_amount_rule_zh" gorm:"use_amount_rule_zh"` // 优惠金额使用规则中文
	StartTime       time.Time      `json:"start_time" gorm:"start_time"`                 // 开始时间
	EndTime         time.Time      `json:"end_time" gorm:"end_time"`                     // 结束时间
	StartUseTime    time.Time      `json:"start_use_time" gorm:"start_use_time"`         // 积分开始使用时间
	EndUseTime      time.Time      `json:"end_use_time" gorm:"end_use_time"`             // 积分结束使用时间
	State           int            `json:"state" gorm:"state"`                           // 状态 0:未开启 1:开启 2:暂停
	Type            int            `json:"type" gorm:"type"`                             // 1: 下雪游戏
	AdminId         int            `json:"admin_id" gorm:"admin_id"`                     // 创建人

	HomePageCoverImgUg string `json:"home_page_cover_img_ug" gorm:"home_page_cover_img_ug"` 
	HomePageCoverImgZh string `json:"home_page_cover_img_zh" gorm:"home_page_cover_img_zh"`
	MiniOrderDetailImgUg string `json:"mini_order_detail_img_ug" gorm:"mini_order_detail_img_ug"`
	MiniOrderDetailImgZh string `json:"mini_order_detail_img_zh" gorm:"mini_order_detail_img_zh"`
	RewardAmount int `json:"reward_amount" gorm:"reward_amount"`
	MiniAppLink string `json:"mini_app_link" gorm:"mini_app_link"`



	CreatedAt       time.Time      `json:"created_at" gorm:"created_at"`                 // 创建时间
	UpdatedAt       time.Time      `json:"updated_at" gorm:"updated_at"`                 // 更新时间
	DeletedAt       gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"`                 // 删除时间
}

// TableName 表名称
func (*MiniGameActivity) TableName() string {
	return "t_mini_game_activity"
}
