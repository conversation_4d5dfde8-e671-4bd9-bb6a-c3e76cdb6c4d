package models
type MyTest struct {
	Status int `json:"status"`
	Msg string `json:"msg"`
	Data struct {
		OrderList []struct {
			ID int `json:"id"`
			SerialNumber int `json:"serial_number"`
			Name string `json:"name"`
			Price int `json:"price"`
			OrderNo string `json:"order_no"`
			Mobile string `json:"mobile"`
			RealMobile string `json:"real_mobile"`
			OrderTime int `json:"order_time"`
			Timezone int `json:"timezone"`
			BookingTime string `json:"booking_time"`
			BookingDateTime string `json:"booking_date_time"`
			CityName string `json:"city_name"`
			AreaName string `json:"area_name"`
			StreetName string `json:"street_name"`
			BuildingName string `json:"building_name"`
			OrderAddress string `json:"order_address"`
			OrderNote string `json:"order_note"`
			SendNotify int `json:"send_notify"`
			OrderState int `json:"order_state"`
			OrderStateDes string `json:"order_state_des"`
			OrderType int `json:"order_type"`
			Now int `json:"now"`
			OrderTimeoutTime string `json:"order_timeout_time"`
			OrderTimeoutTimestamp int `json:"order_timeout_timestamp"`
			OrderTimeLeft int `json:"order_time_left"`
			OrderDetail []struct {
				FoodID int `json:"food_id"`
				OriginalPrice int `json:"original_price"`
				Price int `json:"price"`
				Count int `json:"count"`
				Name string `json:"name"`
				Image string `json:"image"`
			} `json:"order_detail"`
		} `json:"order_list"`
		OrderCount struct {
			NewOrderCount int `json:"newOrderCount"`
			ReceivedOrderCount int `json:"receivedOrderCount"`
			FinishedOrderCount int `json:"finishedOrderCount"`
			CanceledOrderCount int `json:"canceledOrderCount"`
		} `json:"order_count"`
		ServerTime int `json:"server_time"`
		VoiceOrderCount int `json:"voice_order_count"`
	} `json:"data"`
}