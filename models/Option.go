package models
import (
	"time"
)

type Option struct {
	Key         string    `gorm:"column:key;primary_key"`               // 常量关键字
	Value       string    `gorm:"column:value;NOT NULL"`                // 常量值
	Description string    `gorm:"column:description"`                   // 常量描述
	Model       string    `gorm:"column:model;NOT NULL"`                // 常量model
	CanModify   int       `gorm:"column:can_modify;default:0;NOT NULL"` // 是否允许更改（0允许，1不允许）
	Weight      int       `gorm:"column:weight"`                        // 排序
	State       int       `gorm:"column:state;default:0"`               // 状态（0关闭，1开通）
	CreatedAt   time.Time `gorm:"column:created_at"`                    // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at"`                    // 修改时间
	DeletedAt   time.Time `gorm:"column:deleted_at"`                    // 删除时间
}

func (m *Option) TableName() string {
	return "t_option"
}


