package models

import (
    "gorm.io/gorm"
    "time"
)

type RestaurantPrinter struct {
    ID                int64       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
    RestaurantID      uint      `gorm:"not null" json:"restaurant_id"`
    PrinterType       uint8     `gorm:"not null;default:1" json:"printer_type"`
    CloudPrinterType  uint8     `gorm:"default:1" json:"cloud_printer_type"`
    CloudPrinterLang  uint8     `gorm:"default:2" json:"cloud_printer_lang"`
    LocationName      *uint     `gorm:"index" json:"location_name,omitempty"`
    LocationNameUg    string    `gorm:"size:256;charset:utf8;collate:utf8_general_ci" json:"location_name_ug,omitempty"`
    LocationNameZh    string    `gorm:"size:256;charset:utf8;collate:utf8_general_ci" json:"location_name_zh,omitempty"`
    PrinterID         string    `gorm:"size:32;charset:utf8;collate:utf8_general_ci;default:''" json:"printer_id"`
    PrinterKey        string    `gorm:"size:32;charset:utf8;collate:utf8_general_ci;default:''" json:"printer_key"`
    PrintCount        uint8     `gorm:"default:1" json:"print_count"`
    SupportUighur     uint8     `gorm:"default:1" json:"support_uighur"`
    State             uint8     `gorm:"default:0;index" json:"state"`
    CreatedAt         time.Time `json:"created_at"`
    UpdatedAt         time.Time `gorm:"autoUpdateTime" json:"updated_at"`
    DeletedAt         gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

func (*RestaurantPrinter) TableName() string {
	return "t_restaurant_printer"
}