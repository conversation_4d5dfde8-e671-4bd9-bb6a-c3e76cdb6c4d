package models

import (
	"database/sql"
	"time"
)

type OrderExtend struct {
	OrderID              int        `gorm:"column:order_id"`                                               // 订单ID
	ShipperArrivedShopAt *time.Time `gorm:"column:shipper_arrived_shop_at" json:"shipper_arrived_shop_at"` // 配送员已到达店时间
	ShipperOrderState    int        `gorm:"column:shipper_order_state" json:"shipper_order_state"`
	ShipperTakeFoodAt    *time.Time `gorm:"column:shipper_take_food_at" json:"shipper_take_food_at"`
	FoodsReadyTime       *time.Time `gorm:"column:foods_ready_time" json:"foods_ready_time"`
	CustomerPosLat    sql.NullFloat64        `gorm:"column:customer_pos_lat" json:"customer_pos_lat"`
	CustomerPosLng    sql.NullFloat64        `gorm:"column:customer_pos_lng" json:"customer_pos_lng"`
	IsClosed    int        `gorm:"column:is_closed" json:"is_closed"` //是否已关闭 
	CreatedAt            time.Time  `gorm:"column:created_at"` // 创建时间
	UpdatedAt            time.Time  `gorm:"column:updated_at"` // 修改时间

	PartRefundRatio	float64	`gorm:"column:part_refund_ratio"`
	LastReadAt	time.Time	`gorm:"column:last_read_at"`

}

func (o *OrderExtend) TableName() string {
	return "t_order_extend"
}
