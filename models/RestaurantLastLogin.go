package models

import "time"

type RestaurantLastLogin struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	RestaurantID  int       `gorm:"column:restaurant_id;NOT NULL"`        // 餐厅编号
	LastQueryTime *time.Time `gorm:"column:last_query_time"`               // 最终查询时间
	DeviceToken   string    `gorm:"column:device_token"`                  // 极光推送ID
	//DeletedAt     gorm.DeletedAt `gorm:"index"`                    // 删除时间
}

func (m *RestaurantLastLogin) TableName() string {
	return "t_restaurant_last_login"
}
