package models

import (
	"time"

	"gorm.io/gorm"
)

const (
	LotteryChanceTypePurchase             = 1 // 支付 - t_lottery_order
	LotteryChanceTypeHighPriceOrder       = 2 // 高额下单 - t_order, t_order_today
	LotteryChanceTypeSharedHighPriceOrder = 3 // 分享后对方高额下单 - t_order, t_order_today
	LotteryChanceTypeSharedPurchase       = 4 // 分享后对方购买 - t_lottery_order
	LotteryChanceTypeActivity             = 5 // 订单排行活动 - t_order , t_order_today

	LotteryChanceStateUnDraw        = 1 // 未抽奖
	LotteryChanceStateDraw          = 2 // 已抽奖
	LotteryChanceStateExpired       = 3 // 已失效
	LotteryChanceStateSubmitAddress = 4 // 已提交地址（已兑换奖品的状态）

	LotteryChancePrizeOpenStateUnDraw  = 1 // 带抽奖
	LotteryChancePrizeOpenStateDraw    = 2 // 已中奖
	LotteryChancePrizeOpenStateExpired = 3 // 已中奖
)


type LotteryChance struct {
	ID             int           `gorm:"primaryKey;autoIncrement;column:id"`
 
	CityId       int           `gorm:"column:city_id" json:"city_id"`
	AreaId       int           `gorm:"column:area_id" json:"area_id"`
	BuildingId   int           `gorm:"column:building_id" json:"building_id"`

	LotteryActivityID       int           `gorm:"column:lottery_activity_id" json:"lottery_activity_id"`
	Type                    int           `gorm:"column:type" json:"type"`
	TypeID                  int           `gorm:"column:type_id" json:"type_id"`
	UserID                  int           `gorm:"column:user_id" json:"user_id"`
	SourceUserId          *int           `gorm:"column:source_user_id" json:"source_user_id"`
	DrawIndex               *int           `gorm:"column:draw_index" json:"draw_index"`
	State                   int          `gorm:"column:state" json:"state"`
	LotteryActivityLevelID  int           `gorm:"column:lottery_activity_level_id" json:"lottery_activity_level_id"`
	PrizeOpenTime           *time.Time      `gorm:"column:prize_open_time" json:"prize_open_time"`
	PrizeOpenState          *int          `gorm:"column:prize_open_state" json:"prize_open_state"`
	PrizeID                 *int           `gorm:"column:prize_id" json:"prize_id"`
	UserOrderPrice int `gorm:"column:user_order_price" json:"user_order_price"`
	
	CreatedAt      time.Time      `gorm:"column:created_at"`                         // 创建时间
	UpdatedAt      time.Time      `gorm:"column:updated_at;autoUpdateTime"`          // 更新时间
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at"`                         // 删除时间

	
	
	LotteryPrize          LotteryPrize `gorm:"foreignKey:prize_id;references:id"`
	LotteryActivity       LotteryActivity `gorm:"foreignKey:lottery_activity_id;references:id"`
	User                 User `gorm:"foreignKey:user_id;references:id"`
	City City `gorm:"foreignKey:city_id;references:id"`
	Area Area `gorm:"foreignKey:area_id;references:id"`
	LotteryActivityLevelPrize []LotteryActivityLevelPrize `gorm:"foreignKey:lottery_activity_level_id;references:lottery_activity_level_id"`
	UserBuilding UserBuilding `gorm:"foreignKey:BuildingId;references:ID"`
	LotteryOrder LotteryOrder `gorm:"foreignKey:TypeID;references:id"`
	CouponUser []CouponUser `gorm:"foreignkey:LotteryOrderId;references:ID"`

	Order                    Order        `gorm:"foreignKey:TypeID;references:id"`
	OrderToday               OrderToday   `gorm:"foreignKey:TypeID;references:id"`
}

func (m *LotteryChance) TableName() string {
	return "t_lottery_chance"
}
