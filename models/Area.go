// Code generated by sql2gorm. DO NOT EDIT.
package models

import (
	"mulazim-api/models/cms"
	"mulazim-api/models/shipment"
	"mulazim-api/tools"
	"time"

	"gorm.io/gorm"
)

// 区县信息表
type Area struct {
	ID                    int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`              // 自增编号
	CityID                int       `gorm:"column:city_id;NOT NULL"`                           // 城市编号
	Name                  int       `gorm:"column:name;NOT NULL"`                              // 区域名称
	NameUg                string    `gorm:"column:name_ug"`                                    // 区域维文名称
	NameZh                string    `gorm:"column:name_zh"`                                    // 区域中文名称
	PerKmShipment         uint      `gorm:"column:per_km_shipment;default:100;NOT NULL"`       // 每公里配送费（单位：分）
	MinShipment           uint      `gorm:"column:min_shipment;default:500;NOT NULL"`          // 最低配送费（单位：分）
	MpPercent             float64   `gorm:"column:mp_percent;default:0.0;NOT NULL"`            // 属于平台的百分比（平台负责配送）
	ServicePhone          string    `gorm:"column:service_phone"`                              // 服务电话
	AdminSmsPhone         string    `gorm:"column:admin_sms_phone"`                            // 管理员短信手机号
	Weight                int       `gorm:"column:weight;default:0;NOT NULL"`                  // 区域排序
	AgentFee              int       `gorm:"column:agent_fee"`                                  // 代理费
	AdvancePayment        int       `gorm:"column:advance_payment"`                            // 押金
	Lat                   float64   `gorm:"column:lat;NOT NULL"`                               // 纬度
	Lng                   float64   `gorm:"column:lng;NOT NULL"`                               // 经度
	OrderFinishDistance   float64   `gorm:"column:order_finish_distance;default:1000"`         // 配送员完成订单范围（单位：米）
	CashPaymentState      int       `gorm:"column:cash_payment_state;default:1"`               // 是否开启现金支付（0关闭，1开通）
	AreaType              int       `gorm:"column:area_type;default:2"`                        // 区域类型（1表示地市、2表示县城、3表示乡镇）
	ShowRestaurantContact int       `gorm:"column:show_restaurant_contact;default:0;NOT NULL"` // 是否向客户显示餐厅联系电话（0表示不显示，1表示显示）
	ShowCustomContact     int       `gorm:"column:show_custom_contact;default:0;NOT NULL"`     // 是否向餐厅显示客户联系电话（0表示不显示，1表示显示）
	RestingStartTime      time.Time `gorm:"column:resting_start_time"`                         // 区域休息开始时间
	RestingEndTime        time.Time `gorm:"column:resting_end_time"`                           // 区域休息结束时间
	RestingContentUg      string    `gorm:"column:resting_content_ug"`                         // 区域休息公告内容
	RestingContentZh      string    `gorm:"column:resting_content_zh"`                         // 区域休息公告内容
	ReceiveOrderTime      int       `gorm:"column:receive_order_time;default:3"`               // 该区域餐厅处理新订单时间限制（单位为：分钟）
	MaxOrderPerHour       int       `gorm:"column:max_order_per_hour;default:30"`              // 一个小时内能配送完成的最多订单数量
	RankState             int       `gorm:"column:rank_state;default:0;NOT NULL"`                   // 状态（0关闭，1开通，2休息）
	ShipperRankState             int       `gorm:"column:shipper_rank_state;default:0;NOT NULL"`                   // 状态（0关闭，1开通，2休息）
	State                 int       `gorm:"column:state;default:0;NOT NULL"`                   // 状态（0关闭，1开通，2休息）
	AutoDispatchState   int       `gorm:"column:auto_dispatch_state;default:0;NOT NULL"`                   // 是否开启智能派单：1:开启，2:关闭
	AutoDispatchWait   int       `gorm:"column:auto_dispatch_wait;default:0;NOT NULL"`                   // 压单分钟数
	AutoDispatchParallelDistance   int       `gorm:"column:auto_dispatch_parallel_distance;default:0;NOT NULL"`                   // 同方向单子最小平行距离，默认 500米
	AutoDispatchPeakState   int       `gorm:"column:auto_dispatch_peak_state;default:0;NOT NULL"`                   // 1:高峰期，2:均匀分配
	AutoDispatchPeakTime     *string       `gorm:"column:auto_dispatch_peak_time;NOT NULL"`                   // 高峰期时间端
	AutoDispatchDeliveryTime   int       `gorm:"column:auto_dispatch_delivery_time;default:0;NOT NULL"`                   // 最大配送时间(分钟)
	AutoDispatchTakeFoodTime   int       `gorm:"column:auto_dispatch_take_food_time;default:0;NOT NULL"`                   // 取餐时间(分钟)
	AutoDispatchShipperToRestaurantDistance   float64       `gorm:"column:auto_dispatch_shipper_to_restaurant_distance;NOT NULL"`                   // 配送员到餐厅最大距离（km）
	AutoDispatchShipperSendFoodCustomerTime   int       `gorm:"column:auto_dispatch_shipper_send_food_customer_time;default:0;NOT NULL"` // 配送员到达客户区域到送到门口时间(分钟)
	AutoDispatchSpecialOrderPeakState   int       `gorm:"column:auto_dispatch_special_order_peak_state;default:2;NOT NULL"` // 未接订单时间阈值(分钟)
	AutoDispatchNotTakeOrderThresholdTime   int       `gorm:"column:auto_dispatch_not_take_order_threshold_time;default:60;NOT NULL"` // 配送员到达客户区域到送到门口时间(分钟)
	AutoDispatchMqGroup   int       `gorm:"column:auto_dispatch_mq_group;default:1;NOT NULL"` // 配送员到达客户区域到送到门口时间(分钟)
	AutoDispatchSameBuildingRestaurantSeckillOrder   int       `gorm:"column:auto_dispatch_same_building_restaurant_seckill_order;default:1;NOT NULL"`                   // 智能派单秒杀订单同一个餐厅和建筑分配一个配送员配置
	AutoDispatchSameBuildingRestaurantSpecialPriceOrder   int       `gorm:"column:auto_dispatch_same_building_restaurant_special_price_order;default:1;NOT NULL"`        // 智能派单特价活动订单同一个餐厅和建筑分配一个配送员配置
	AutoDispatchSameBuildingRestaurantNormalOrder   int       `gorm:"column:auto_dispatch_same_building_restaurant_normal_order;default:0;NOT NULL"`                   // 智能派单普通订单同一个餐厅和建筑分配一个配送员配置
	AutoDispatchReturnOrderState   int       `gorm:"column:auto_dispatch_return_order_state;default:0;NOT NULL"`                   // 智能派单是否分配反方向路径的订单
	AutoDispatchReturnOrderDistance   int       `gorm:"column:auto_dispatch_return_order_distance;default:0;NOT NULL"`                   // 智能派单反向分配订单距离
	AutoDispatchEnableRank   int       `gorm:"column:auto_dispatch_enable_rank;default:0;NOT NULL"`                   // 智能派单是否开启排名

	CreatedAt             time.Time `gorm:"column:created_at"`                                 // 创建时间
	UpdatedAt             time.Time `gorm:"column:updated_at"`                                 // 修改时间
	DeletedAt             time.Time `gorm:"column:deleted_at"`                                 // 删除时间

	InsuranceEnable       int       `gorm:"column:insurance_enable;default:0"`                  // 是否开启保险 

	City                  City                     `gorm:"foreignKey:city_id;references:id"`
	SelfSignImages        []SelfSignImages         `gorm:"foreignKey:restaurant_id;references:id"`
	DeliveryRunningConfig shipment.DeliveryAreas   `gorm:"foreignKey:area_id;references:id;"` // 区域配送配置
	DeliveryAllConfigs    []shipment.DeliveryAreas `gorm:"foreignKey:area_id;references:id;where:type = 1"`                            // 区域配送配置
	BusinessStartTime     string                   `json:"business_start_time"`
    BusinessEndTime       string                   `json:"business_end_time"`
    BusinessTimeType      int                      `json:"business_time_type"`
}



func AreaScoreOffUser(adminType int,adminId int) func (db *gorm.DB) *gorm.DB {
	return func (db *gorm.DB) *gorm.DB {
		if adminType==1 {
			return db
		}
		dbTemp := tools.Db
		var areaIDs []int

		dbTemp.Model(cms.AdminAreas{}).
			Where("admin_id = ?", adminId).
			Group("area_id").
			Pluck("area_id", &areaIDs)
		return db.Where("id IN (?)", areaIDs)
	}
}


func (m *Area) TableName() string {
	return "b_area"
}
