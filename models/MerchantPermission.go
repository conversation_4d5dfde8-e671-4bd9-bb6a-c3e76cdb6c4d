package models

// 权限表
type MerchantPermission struct {
	ID   int       `gorm:"column:id;NOT NULL"`
	Ptype   string       `gorm:"column:ptype;NOT NULL"`
	V0 string `gorm:"column:v0"`
	V1 string `gorm:"column:v1"`
	V2 string `gorm:"column:v2"`
	V3 string `gorm:"column:v3"`
	V4 string `gorm:"column:v4"`
	V5 string `gorm:"column:v5"`
}

func (m *MerchantPermission) TableName() string {
	return "merchant_permission"
}
