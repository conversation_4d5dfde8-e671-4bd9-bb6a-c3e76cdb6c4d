package models

import (
	"time"

	"gorm.io/gorm"
)

const OrderDetailActivityTypeOriginPrice = 1
const OrderDetailActivityTypePreferential = 2
const OrderDetailActivityTypeSecKill = 3
const OrderDetailActivityTypeSpecialPrice = 4
const OrderDetailActivityTypePriceMarkupPreferential = 5
const OrderDetailActivityTypePriceMarkupSecKill = 6
const OrderDetailActivityTypePriceMarkupSpecialPrice = 7

type OrderDetail struct {
	ID              int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`         // 自增编号
	OrderID         int       `gorm:"column:order_id;NOT NULL"`                     // 订单编号
	StoreFoodsID    int       `gorm:"column:store_foods_id;NOT NULL"`               // 餐厅美食编号
	OriginalPrice   uint      `gorm:"column:original_price;NOT NULL"`               // 原价格（单位：分）
	Price           uint      `gorm:"column:price;NOT NULL"`                        // 实际价格（单位：分）
	MpPercent       float64   `gorm:"column:mp_percent;default:0.0;NOT NULL"`       // 当时属于平台的百分比
	DiscountPercent uint      `gorm:"column:discount_percent;default:100;NOT NULL"` // 打折百分比
	DealerPercent   float64   `gorm:"column:dealer_percent;default:0.0;NOT NULL"`   // 当时属于代理商的百分比
	MpProfit        uint      `gorm:"column:mp_profit;default:0;NOT NULL"`          // 当时属于平台的利润（公式：price * number*mp_percent）
	DealerProfit    int      `gorm:"column:dealer_profit;default:0;NOT NULL"`      // 当时属于代理商的利润（公式：price * number*dealer_percent）
	ResProfit    int      `gorm:"column:res_profit;default:0;NOT NULL"`      // 蒂娜普利润
	Number          uint      `gorm:"column:number;default:1;NOT NULL"`             // 数量
	LunchBoxID      int       `gorm:"column:lunch_box_id;default:0"`                // 跟美食关联的饭盒编号
	LunchBoxFee     int       `gorm:"column:lunch_box_fee;default:0"`               // 饭盒单价（单位：分）
	LunchBoxCount   int       `gorm:"column:lunch_box_count;default:0"`             // 饭盒数量
	SeckillID       int       `gorm:"column:seckill_id"`                            // 订单编号
	CreatedAt       time.Time `gorm:"column:created_at"`                            // 创建时间
	UpdatedAt       time.Time `gorm:"column:updated_at"`                            // 修改时间
	DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at"`                            // 删除时间

	PrefId             int    `gorm:"column:pref_id;"`                        // 实际价格（单位：分）
	PriceMarkupId     int     `gorm:"column:price_markup_id;"`                        // 实际价格（单位：分）
	PriceMarkupPrice  int     `gorm:"column:price_markup_price;"`                        // 实际价格（单位：分）
	RefundPrice int `gorm:"column:refund_price;"` //部分退款金额
	LunchBoxRefundPrice int 	`gorm:"column:lunch_box_refund_price;"` //饭盒部分退款金额
	LunchBoxGroupIndex int 	`gorm:"column:lunch_box_group_index;"` //饭盒部分退款金额
	// LunchBoxRefundPricePer int 	`gorm:"column:lunch_box_refund_price_per;"` //饭盒部分退款金额
	LunchBoxFoodCount int `gorm:"column:lunch_box_food_count;"`
	// LunchBoxRefundCount int 	`gorm:"column:lunch_box_refund_count;"` //饭盒部分退款数量
	
	ActivityType     int `gorm:"column:activity_type;"` 
	ActivityId     int `gorm:"column:activity_id;"`     


	RestaurantFoods    RestaurantFoods `gorm:"foreignkey:id;references:store_foods_id"`
	Order    Order `gorm:"foreignkey:id;references:order_id"`
	OrderToday    OrderToday `gorm:"foreignkey:id;references:order_id"`

	FoodType     uint8     `gorm:"column:food_type;default:0;not null"` // 美食类型 (0: 普通美食, 1: 规格, 2: 套餐)
	SpecID       int       `gorm:"column:spec_id;null"`                 // 已选规格编号
	SelectedSpec *FoodSpec `gorm:"foreignkey:id;references:spec_id"`    // 已选规格数据
	LunchBoxActualCount float64 	`gorm:"-"` //饭盒实际数量
	LunchBoxAccommodate float64 	`gorm:"-"` //饭盒实际数量
}

func (m *OrderDetail) TableName() string {
	return "t_order_detail"
}

