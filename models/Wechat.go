/**
@author: captain
@since: 2022/9/17
@desc:
**/

// Package models Code generated by sql2gorm. DO NOT EDIT.
package models

import (
	"time"
)

type Wechat struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	AppID         string    `gorm:"column:app_id"` // 终端ID
	MchID         string    `gorm:"column:mch_id"` // 商户号
	OpenID        string    `gorm:"column:open_id"`
	OrderID       int       `gorm:"column:order_id"`                     // 网站商品订单编号
	OutTradeNo    string    `gorm:"column:out_trade_no;NOT NULL"`        // 网站商品订单唯一号
	TransactionID string    `gorm:"column:transaction_id"`               // 支付宝流水号
	PrepayID      string    `gorm:"column:prepay_id"`                    // 预支付交易会话标识
	NonceStr      string    `gorm:"column:nonce_str"`                    // 卖家支付宝用户号
	TradeType     string    `gorm:"column:trade_type"`                   // 卖家支付宝账号
	TotalFee      uint      `gorm:"column:total_fee;default:0;NOT NULL"` // 交易金额
	Payed         int       `gorm:"column:payed;default:0"`              // 状态（0 未付款， 1已付款）
	Number        int       `gorm:"column:number;default:0"`
	Remark        string    `gorm:"column:remark"`                  // 备注
	ExpireTime    time.Time `gorm:"column:expire_time"`             // 过期时间
	CreatedAt     time.Time `gorm:"column:created_at"`              // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at"`              // 修改时间
	DeletedAt     time.Time `gorm:"column:deleted_at;default:NULL"` // 删除时间
}

func (m *Wechat) TableName() string {
	return "wechat"
}
