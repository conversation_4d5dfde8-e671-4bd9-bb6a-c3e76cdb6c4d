package models

import (
	"time"
)

// MarketChangeLogChangeValues
//
//	MarketChangeLogChangeValues
//	@Description: 修改的字段,JSON 存储
type MarketChangeLogChangeValues struct {
	OldValue  interface{}
	NewValue  interface{}
	FiledName string
}

// MarketChangeLog
//
//	@Description: 营销活动修改记录表
type MarketChangeLog struct {
	Id            int       `gorm:"column:id;type:int(11);primary_key;AUTO_INCREMENT" json:"id"`
	GroupId       *int       `gorm:"column:group_id;type:int(11);comment:模板ID" json:"group_id"`
	CityId        int       `gorm:"column:city_id;type:int(11);comment:地区ID" json:"city_id"`
	AreaId        int       `gorm:"column:area_id;type:int(11);comment:区域ID" json:"area_id"`
	RestaurantId  int       `gorm:"column:restaurant_id;type:int(11);comment:餐厅ID" json:"restaurant_id"`
	MarketingID   int       `gorm:"column:marketing_id;type:int(11);comment:活动ID" json:"marketing_id"`
	CurrentData   string    `gorm:"column:current_data;type:json;comment:最新数据" json:"current_data"`
	ChangedFields string    `gorm:"column:changed_fields;type:json;comment:修改的字段" json:"changed_fields"`
	AdminId       int       `gorm:"column:admin_id;type:int(11);comment:操作人员" json:"admin_id"`
	Ip            string    `gorm:"column:ip;type:varchar(255);comment:操作人IP" json:"ip"`
	DescUg        string    `gorm:"column:desc_ug;type:varchar(255);comment:描述" json:"desc_ug"`
	DescZh        string    `gorm:"column:desc_zh;type:varchar(255);comment:描述" json:"desc_zh"`
	CreatedAt     time.Time `gorm:"column:created_at;type:datetime;comment:创建时间" json:"created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at;type:datetime;comment:更新时间" json:"updated_at"`
}

// TableName
//
//	@Description: 表名
//	@receiver m
//	@return string
func (m *MarketChangeLog) TableName() string {
	return "t_market_change_log"
}
