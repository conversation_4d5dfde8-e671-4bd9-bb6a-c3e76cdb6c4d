package models

import (
	"time"
)

// Mulazim平台终端类型信息表
type Terminal struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`    // 自增编号
	TerminalType  int       `gorm:"column:terminal_type;default:1;NOT NULL"` // 终端类型（1用户客户端，2配送员客户端，3用户wap，4用户web , 5 商家客户端）
	Name          string    `gorm:"column:name;NOT NULL"`                    // 终端名称
	NameUg        string    `gorm:"column:name_ug;NOT NULL"`                 // 终端名称
	NameZh        string    `gorm:"column:name_zh;NOT NULL"`                 // 终端名称
	OsType        int       `gorm:"column:os_type;default:0;NOT NULL"`       // 终端类型(1表示Android、2表示iOS、3表示wap页面、4表示网页web页面)
	Icon          string    `gorm:"column:icon;NOT NULL"`                    // 终端图标
	Version       string    `gorm:"column:version;NOT NULL"`                 // 终端版本
	VersionCode   int       `gorm:"column:version_code;NOT NULL"`            // 终端版本号
	PackageName   string    `gorm:"column:package_name"`                     // 终端包名
	Url           string    `gorm:"column:url"`                              // 客户端下载地址
	Description   int       `gorm:"column:description"`                      // 终端说明
	DescriptionUg string    `gorm:"column:description_ug"`
	DescriptionZh string    `gorm:"column:description_zh"`
	ForceUpdate   int       `gorm:"column:force_update;default:0;NOT NULL"` // 是否强制更新
	State         int       `gorm:"column:state;NOT NULL"`                  // 状态（0关闭，1开通）
	CreatedAt     time.Time `gorm:"column:created_at"`                      // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at"`                      // 修改时间
	DeletedAt     time.Time `gorm:"column:deleted_at"`                      // 删除时间
}

func (m *Terminal) TableName() string {
	return "b_terminal"
}
