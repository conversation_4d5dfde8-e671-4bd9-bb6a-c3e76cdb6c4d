package models

import (
	"time"

	"gorm.io/gorm"
)


type LotteryShareBind struct {
	ID             int           `gorm:"primaryKey;autoIncrement;column:id"`
	
	LotteryActivityID       int           `gorm:"column:lottery_activity_id" json:"lottery_activity_id"`
	LotteryActivityLevelID  int           `gorm:"column:lottery_activity_level_id" json:"lottery_activity_level_id"`
	UserID                  int           `gorm:"column:user_id" json:"user_id"`
	InvitedUserID           int           `gorm:"column:invited_user_id" json:"invited_user_id"`
	
	 
	CreatedAt      time.Time      `gorm:"column:created_at"`                         // 创建时间
	UpdatedAt      time.Time      `gorm:"column:updated_at;autoUpdateTime"`          // 更新时间
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at"`                         // 删除时间

	User            User          `gorm:"foreignkey:user_id;references:id"`
	ShareUser            User          `gorm:"foreignkey:InvitedUserID;references:id"`

}

func (m *LotteryShareBind) TableName() string {
	return "t_lottery_share_bind"
}
