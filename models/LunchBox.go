package models

import (
	"time"

	"gorm.io/gorm"
)

const (
	LunchBoxStateOn  = 1
	LunchBoxStateOff = 0
)

// BLunchBox 定义饭盒模型
type LunchBox struct {
	ID        int            `gorm:"column:id;primaryKey;autoIncrement" json:"id"`     // 主键ID
	CityID    int            `gorm:"column:city_id" json:"city_id,omitempty"`          // 城市编号
	AreaID    int            `gorm:"column:area_id" json:"area_id,omitempty"`          // 区域编号
	NameUg    string         `gorm:"column:name_ug;size:255" json:"name_ug,omitempty"` // 维文名称
	NameZh    string         `gorm:"column:name_zh;size:64" json:"name_zh,omitempty"`  // 中文名称
	UnitPrice int            `gorm:"column:unit_price" json:"unit_price,omitempty"`    // 饭盒单价（单位：分）
	State     int8           `gorm:"column:state" json:"state,omitempty"`              // 状态（0：关闭、1：开启）
	CreatedAt time.Time      `gorm:"column:created_at" json:"created_at,omitempty"`    // 创建时间
	UpdatedAt time.Time      `gorm:"column:updated_at" json:"updated_at,omitempty"`    // 修改时间
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`                 // 删除时间（软删除）
}

// TableName 设置表名
func (LunchBox) TableName() string {
	return "b_lunch_box" // 数据库表名
}

