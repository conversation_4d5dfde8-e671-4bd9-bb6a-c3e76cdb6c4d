package shipment

import (
	"database/sql/driver"
	"encoding/binary"
	"fmt"
	"math"
	deliveryRequests "mulazim-api/requests/cms/delivery"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/paulmach/orb"
)

const (
	// Type
	DeliveryAreasTypeArea       = 1 // 区域类型
	DeliveryAreasTypeRestaurant = 2 // 餐厅类型
	// Option
	DeliveryAreasOptionArea   = 1 // 跟随区域
	DeliveryAreasOptionNormal = 2 // 普通范围
	DeliveryAreasOptionCircle = 3 // 圆形范围
)

// DeliveryAreas 配送配置表
type DeliveryAreas struct {
	ID               int            `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`                               // 自增ID
	CityID           int            `gorm:"column:city_id" json:"city_id"`                                                // 城市编号
	AreaID           int            `gorm:"column:area_id" json:"area_id"`                                                // 区域编号
	RestaurantID     *int           `gorm:"column:restaurant_id" json:"restaurant_id,omitempty"`                          // 餐厅编号（可为空）
	ParentID         *int           `gorm:"column:parent_id" json:"parent_id,omitempty"`                                  // 父ID
	Type             uint8          `gorm:"column:type" json:"type"`                                                      // 类型：1 区域数据 / 2 餐厅数据
	NameUg           string         `gorm:"column:name_ug" json:"name_ug,omitempty"`                                      // 该配置的名称 - 维吾尔文
	NameZh           string         `gorm:"column:name_zh" json:"name_zh,omitempty"`                                      // 该配置的名称 - 中文
	NoticeUg         string         `gorm:"column:notice_ug" json:"notice_ug,omitempty"`                                  // 特殊天气公告内容 - 维吾尔文
	NoticeZh         string         `gorm:"column:notice_zh" json:"notice_zh,omitempty"`                                  // 特殊天气公告内容 - 中文
	Option           uint8          `gorm:"column:option" json:"option"`                                                  // 地图范围选项 - 1 跟随区域 / 2 使用普通 / 3 使用圆形
	Polygon          *Polygon       `gorm:"column:polygon;type:polygon" json:"polygon,omitempty"`                         // 普通配送范围（Polygon类型），可为空
	MinLat           float64        `gorm:"column:min_lat" json:"min_lat"`                                                // 最小纬度
	MaxLat           float64        `gorm:"column:max_lat" json:"max_lat"`                                                // 最大纬度
	MinLng           float64        `gorm:"column:min_lng" json:"min_lng"`                                                // 最小经度
	MaxLng           float64        `gorm:"column:max_lng" json:"max_lng"`                                                // 最大经度
	Radius           uint           `gorm:"column:radius" json:"radius,omitempty"`                                        // 圆形区域半径长度（米）
	AreaRunningState uint8          `gorm:"column:area_running_state" json:"area_running_state"`                          // 区域状态：1 开启 / 0 关闭
	OrderAddTime     int            `gorm:"column:order_add_time" json:"order_add_time,omitempty"`                        // 配送时间加长（分钟）
	FeeState         uint8          `gorm:"column:fee_state" json:"fee_state,omitempty"`                                  // 是否使用配送费配置：2 不用 / 1 使用（可为空）
	UpdatedAdminID   int            `gorm:"column:updated_admin_id" json:"updated_admin_id"`                              // 更新者编号
	CreatedAt        time.Time      `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at,omitempty"`      // 创建时间，默认当前时间
	UpdatedAt        time.Time      `gorm:"column:updated_at;default:CURRENT_TIMESTAMP" json:"updated_at,omitempty"`      // 最后更新时间，默认当前时间
	AreaInfo         AreaInfo       `gorm:"foreignKey:area_id;references:id" json:"area,omitempty"`                       // 区域信息
	DeliveryFees     []DeliveryFees `gorm:"foreignKey:delivery_area_id;references:id" json:"delivery_fees,omitempty"`     // 区域信息
	DeliveryFeesMin  DeliveryFees   `gorm:"foreignKey:delivery_area_id;references:id" json:"delivery_fees_min,omitempty"` // 区域信息
}

type AreaInfo struct {
	ID      int      `gorm:"column:id;primary_key;AUTO_INCREMENT"`                      // 自增编号
	Polygon *Polygon `gorm:"column:area_polygon;type:polygon" json:"polygon,omitempty"` // 普通配送范围（Polygon类型），可为空
}

func (AreaInfo) TableName() string {
	return "b_area"
}

func (DeliveryAreas) TableName() string {
	return "t_delivery_areas"
}

// Polygon 包装 orb.Polygon 以支持数据库操作
type Polygon struct {
	orb.Polygon
}

// Scan 实现 sql.Scanner 接口
func (p *Polygon) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case string:
		return p.scanWKT(v)
	case []uint8:
		// Try WKT first
		if len(v) > 0 && v[0] == 'P' {
			return p.scanWKT(string(v))
		}
		// If not WKT, assume it's MySQL's binary format
		return p.scanBinary(v)
	default:
		return fmt.Errorf("expected string or []uint8, got %T", value)
	}
}

// scanWKT 解析WKT格式的polygon数据
func (p *Polygon) scanWKT(wkt string) error {
	if len(wkt) < 10 || wkt[:8] != "POLYGON(" || wkt[len(wkt)-2:] != "))" {
		return fmt.Errorf("invalid WKT polygon format: %s", wkt)
	}

	// 提取坐标点
	coords := wkt[9 : len(wkt)-2]
	points := make(orb.Ring, 0)

	// 解析坐标点
	for _, pointStr := range strings.Split(coords, ",") {
		var x, y float64
		_, err := fmt.Sscanf(strings.TrimSpace(pointStr), "%f %f", &x, &y)
		if err != nil {
			return fmt.Errorf("failed to parse WKT polygon coordinates: %v", err)
		}
		points = append(points, orb.Point{x, y})
	}

	// 创建多边形
	p.Polygon = orb.Polygon{points}
	return nil
}

// scanBinary 解析MySQL二进制格式的polygon数据
func (p *Polygon) scanBinary(data []uint8) error {
	if len(data) < 13 { // 至少需要头部信息
		return fmt.Errorf("binary polygon data too short")
	}

	// Skip header (4 bytes for SRID + 1 byte for byteOrder + 4 bytes for type + 4 bytes for numRings)
	pos := 13

	// Read number of points (4 bytes)
	if len(data) < pos+4 {
		return fmt.Errorf("binary polygon data truncated")
	}
	numPoints := int(data[pos]) | int(data[pos+1])<<8 | int(data[pos+2])<<16 | int(data[pos+3])<<24
	pos += 4

	points := make(orb.Ring, 0, numPoints)

	// Read points
	for i := 0; i < numPoints && pos+16 <= len(data); i++ {
		// MySQL stores coordinates as IEEE 754 double-precision floating-point
		x := math.Float64frombits(binary.LittleEndian.Uint64(data[pos : pos+8]))
		y := math.Float64frombits(binary.LittleEndian.Uint64(data[pos+8 : pos+16]))
		points = append(points, orb.Point{x, y})
		pos += 16
	}

	if len(points) < 3 {
		return fmt.Errorf("polygon must have at least 3 points")
	}

	p.Polygon = orb.Polygon{points}
	return nil
}

// CreateCirclePolygon
//
//	@Description: 根据中心点和半径创建圆形多边形
//	@Author: Salam
//	@Time: 2025-06-27 17:30:00
//	@Param centerLat float64 中心点纬度
//	@Param centerLng float64 中心点经度
//	@Param radiusMeters uint 半径（米）
//	@Param points int 多边形点数（默认12个点）
//	@return *Polygon
func (*Polygon) CreateCirclePolygon(centerLat, centerLng float64, radiusMeters uint, points int) *Polygon {
	if points <= 0 {
		points = 12 // 默认12个点
	}

	// 地球半径（米）
	const earthRadius = 6371000.0

	// 计算纬度增量（弧度）
	latDelta := float64(radiusMeters) / earthRadius

	// 计算经度增量（弧度）
	lngDelta := float64(radiusMeters) / (earthRadius * math.Cos(centerLat*math.Pi/180))

	// 转换为度
	latDeltaDeg := latDelta * 180 / math.Pi
	lngDeltaDeg := lngDelta * 180 / math.Pi

	// 创建圆形边界点
	ring := make(orb.Ring, points+1) // +1 是为了闭合多边形

	for i := 0; i <= points; i++ {
		angle := 2 * math.Pi * float64(i) / float64(points)

		// 计算偏移后的坐标
		lat := centerLat + latDeltaDeg*math.Cos(angle)
		lng := centerLng + lngDeltaDeg*math.Sin(angle)

		ring[i] = orb.Point{lng, lat} // orb.Point 使用 [经度, 纬度] 格式
	}

	// 确保多边形闭合
	if ring[0] != ring[len(ring)-1] {
		ring[len(ring)-1] = ring[0]
	}

	polygon := Polygon{Polygon: orb.Polygon{ring}}
	return &polygon
}

// ToOrbPolygon 将 DeliveryAreaPoint 数组转换为 orb.Polygon
func (*Polygon) ToOrbPolygon(points []deliveryRequests.DeliveryAreaPoint) *orb.Polygon {
	if len(points) < 3 {
		return nil
	}

	// 创建环
	ring := make(orb.Ring, len(points))
	for i, point := range points {
		// 注意：orb.Point 使用 [x, y] 格式，其中 x 是经度，y 是纬度
		ring[i] = orb.Point{point.Lng, point.Lat}
	}

	// TODO: is this necessary?
	// 确保多边形是闭合的
	if ring[0] != ring[len(ring)-1] {
		ring = append(ring, ring[0])
	}

	// 创建多边形
	polygon := orb.Polygon{ring}
	return &polygon
}

// FromOrbPolygon 从 orb.Polygon 创建 Polygon
func (*Polygon) FromOrbPolygon(p *orb.Polygon) *Polygon {
	if p == nil {
		return nil
	}
	return &Polygon{Polygon: *p}
}

// Value 实现 driver.Valuer 接口
func (p *Polygon) Value() (driver.Value, error) {
	if p == nil || len(p.Polygon) == 0 {
		return nil, nil
	}

	// 获取第一个环（外环）
	ring := p.Polygon[0]
	if len(ring) < 3 {
		return nil, fmt.Errorf("polygon must have at least 3 points")
	}

	// 确保多边形是闭合的
	if ring[0] != ring[len(ring)-1] {
		ring = append(ring, ring[0])
	}

	// 构建 MySQL POLYGON 格式的字符串
	var points []string
	for _, point := range ring {
		// 注意：MySQL 的 POLYGON 格式要求经度在前，纬度在后
		points = append(points, fmt.Sprintf("%f %f", point[0], point[1]))
	}

	// 构建完整的 POLYGON 字符串
	polygonStr := fmt.Sprintf("POLYGON((%s))", strings.Join(points, ","))
	return polygonStr, nil
}

// GetBounds 计算多边形的边界框，返回4个极值
// 返回值顺序：minLng, maxLng, minLat, maxLat
func (p *Polygon) GetBounds() (minLng, maxLng, minLat, maxLat float64) {
	if p == nil || len(p.Polygon) == 0 {
		return 0, 0, 0, 0
	}

	// 初始化极值为第一个点的坐标
	firstRing := p.Polygon[0]
	if len(firstRing) == 0 {
		return 0, 0, 0, 0
	}

	firstPoint := firstRing[0]
	minLng = firstPoint[0] // x 坐标（经度）
	maxLng = firstPoint[0]
	minLat = firstPoint[1] // y 坐标（纬度）
	maxLat = firstPoint[1]

	// 遍历所有环（rings）
	for _, ring := range p.Polygon {
		// 遍历环中的所有点
		for _, point := range ring {
			lng := point[0] // x 坐标（经度）
			lat := point[1] // y 坐标（纬度）

			// 更新最小/最大经度
			if lng < minLng {
				minLng = lng
			}
			if lng > maxLng {
				maxLng = lng
			}

			// 更新最小/最大纬度
			if lat < minLat {
				minLat = lat
			}
			if lat > maxLat {
				maxLat = lat
			}
		}
	}

	return tools.ToRound(minLng, 7), tools.ToRound(maxLng, 7), tools.ToRound(minLat, 7), tools.ToRound(maxLat, 7)
}
