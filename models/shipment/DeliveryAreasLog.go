package shipment

import (
	"time"
)

// DeliveryAreasLog 配送配置表
type DeliveryAreasLog struct {
	ID               int       `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`                                      // 自增ID
	CityID           int       `gorm:"column:city_id" json:"city_id"`                                                       // 城市编号
	AreaID           int       `gorm:"column:area_id" json:"area_id"`                                                       // 区域编号
	RestaurantID     *int      `gorm:"column:restaurant_id" json:"restaurant_id,omitempty"`                                 // 餐厅编号（可为空）
	ParentID         *int      `gorm:"column:parent_id" json:"parent_id,omitempty"`                                         // 父ID
	Type             uint8     `gorm:"column:type" json:"type"`                                                             // 类型：1 区域数据 / 2 餐厅数据
	NameUg           string    `gorm:"column:name_ug" json:"name_ug,omitempty"`                                             // 该配置的名称 - 维吾尔文
	NameZh           string    `gorm:"column:name_zh" json:"name_zh,omitempty"`                                             // 该配置的名称 - 中文
	NoticeUg         string    `gorm:"column:notice_ug" json:"notice_ug,omitempty"`                                         // 特殊天气公告内容 - 维吾尔文
	NoticeZh         string    `gorm:"column:notice_zh" json:"notice_zh,omitempty"`                                         // 特殊天气公告内容 - 中文
	Option           uint8     `gorm:"column:option" json:"option"`                                                         // 地图范围选项 - 1 跟随区域 / 2 使用普通 / 3 使用圆形
	Polygon          *Polygon  `gorm:"column:polygon;type:polygon" json:"polygon,omitempty"`                                // 普通配送范围（Polygon类型），可为空
	MinLat           float64   `gorm:"column:min_lat" json:"min_lat"`                                                       // 最小纬度
	MaxLat           float64   `gorm:"column:max_lat" json:"max_lat"`                                                       // 最大纬度
	MinLng           float64   `gorm:"column:min_lng" json:"min_lng"`                                                       // 最小经度
	MaxLng           float64   `gorm:"column:max_lng" json:"max_lng"`                                                       // 最大经度
	Radius           uint      `gorm:"column:radius" json:"radius,omitempty"`                                               // 圆形区域半径长度（米）
	AreaRunningState uint8     `gorm:"column:area_running_state" json:"area_running_state"`                                 // 区域状态：1 开启 / 0 关闭
	OrderAddTime     int       `gorm:"column:order_add_time" json:"order_add_time,omitempty"`                               // 配送时间加长（分钟）
	FeeState         uint8     `gorm:"column:fee_state" json:"fee_state,omitempty"`                                         // 是否使用配送费配置：2 不用 / 1 使用（可为空）
	UpdatedAdminID   int       `gorm:"column:updated_admin_id" json:"updated_admin_id"`                                     // 更新者编号
	ValidBeginTime   time.Time `gorm:"column:valid_begin_time;default:CURRENT_TIMESTAMP" json:"valid_begin_time,omitempty"` // 最后更新时间，默认当前时间
	ValidEndTime     time.Time `gorm:"column:valid_end_time;default:CURRENT_TIMESTAMP" json:"valid_end_time,omitempty"`     // 最后更新时间，默认当前时间
}

func (DeliveryAreasLog) TableName() string {
	return "t_delivery_areas_log"
}
