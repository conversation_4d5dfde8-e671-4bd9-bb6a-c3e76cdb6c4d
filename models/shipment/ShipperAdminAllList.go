package shipment

//
type ShipperAdminAll struct {
	ID                        int                            `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"` // 自增编号
	Avatar                    string                         `gorm:"column:avatar" json:"avatar"`                        // 用户头像
	Mobile                    string                         `gorm:"column:mobile;NOT NULL" json:"mobile"`
	RealName                  string                         `gorm:"column:real_name" json:"real_name"`
	SuccessOrderCount         int                            `gorm:"-" json:"success_order_count"`
	CurrentOrderCount	  int							  `gorm:"-" json:"current_order_count"`
	SuccessOrder  				[]ShipperAdminAllOrderToday  `gorm:"foreignKey:shipper_id;references:id" json:"-"`
	CurrentOrder  				[]ShipperAdminAllOrderToday  `gorm:"foreignKey:shipper_id;references:id" json:"-"`
}

func (m *ShipperAdminAll) TableName() string {
	return "t_admin"
}


type ShipperAdminAllOrderToday struct {
	ID                        int                            `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"` // 自增编号
	ShipperID                        int                            `gorm:"column:shipper_id" json:"shipper_id"` // 自增编号
}

func (m *ShipperAdminAllOrderToday) TableName() string {
	return "t_order_today"
}