package shipment


type ShipperIncomeSummary struct {
	CommentStar          float64
	OrderDeliveryMinute  float64
	WorkDay              int64
	TotalSalary          float64
	TotalOrderCount      int64
	ComplainCount        int64
	CountCommentBad      int64
	LateCount            int64
	AmountReward         float64
	AmountTips           float64
	AmountSpecialTime    float64
	AmountSpecialWeather float64
	AmountCommentGood    float64
	AmountCommentBad     float64
	AmountOrder          float64
	AmountComplain       float64
	AmountLate           float64
	AmountInviteUser     float64
	AmountOrderTips      float64
	AmountSpecialPriceOrder float64
	CountSpecialPriceOrder int64
	Punishment           float64
	AmountInsurance       float64
}
