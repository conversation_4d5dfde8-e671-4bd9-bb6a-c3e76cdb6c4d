package shipment

import (
	"time"
)

// 配送员收入记录表
type ShipperIncomeArchive struct {
	ID     int `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"` // 自增ID
	CityID int `gorm:"column:city_id" json:"city_id"`
	AreaID int `gorm:"column:area_id;NOT NULL" json:"area_id"`

	ShipperID  int    `gorm:"column:shipper_id;NOT NULL" json:"shipper_id"` // 配送员
	Date       string `gorm:"column:date;NOT NULL" json:"date"`             //
	Shipment   int    `gorm:"column:shipment;NOT NULL" json:"shipment"`     //
	Reward     int    `gorm:"column:reward;" json:"reward"`                 //
	Punishment int    `gorm:"column:punishment;" json:"punishment"`         //
	Other      int    `gorm:"column:other;" json:"other"`                   //

	ComplainCount int `gorm:"column:complain_count;" json:"complain_count"` //

	Success int `gorm:"column:success_count;" json:"success"` //
	Cancel  int `gorm:"column:cancel_count;" json:"cancel"`   //
	Fail    int `gorm:"column:fail_count;" json:"fail"`       //
	Late    int `gorm:"column:late_count;" json:"late"`       //

	ShipperName   string `gorm:"column:shipper_name" json:"shipper_name"`     //
	ShipperMobile string `gorm:"column:shipper_mobile" json:"shipper_mobile"` //

	DayTotal  int       `gorm:"<-:false;column:day_total;" json:"day_total"`             //
	CreatedAt time.Time `gorm:"column:created_at"   json:"created_at_original"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"`                              // 修改时间

	City City `gorm:"foreignKey:city_id;references:id"` // 地区
	Area Area `gorm:"foreignKey:area_id;references:id"` // 区域

	AmountOrder int `gorm:"amount_order"`
	AmountReward int `gorm:"amount_reward"`
	AmountTips int `gorm:"amount_tips"`
	AmountSpecialTime int `gorm:"amount_special_time"`
	AmountSpecialWeather int `gorm:"amount_special_weather"`
	AmountCommentGood int `gorm:"amount_comment_good"`
	AmountCommentBad int `gorm:"amount_comment_bad"`
	AmountComplain int `gorm:"amount_complain"`
	AmountLate int `gorm:"amount_late"`
	AmountCancel int `gorm:"amount_cancel"`
	AmountFail int `gorm:"amount_fail"`
	OrderDeliveryMinute int `gorm:"amount_fail"`
	CommentStar int `gorm:"amount_fail"`

	ChangeLog	 string       `gorm:"column:change_log" json:"change_log"`

	CountCommentBad int `gorm:"count_comment_bad"`
	CountComplain int `gorm:"count_complain"`

	AmountInviteUser int 	`gorm:"amount_invite_user"`
	AmountOrderTips	int 	`gorm:"amount_order_tips"`

	CountSpecialPriceOrder int `gorm:"count_special_price_order"`
	AmountSpecialPriceOrder int `gorm:"amount_special_price_order"`

	AmountInsurance	int `gorm:"amount_insurance"`	 

	CommentGoodCount int `gorm:"comment_good_count"`
}

func (m *ShipperIncomeArchive) TableName() string {
	return "t_shipper_income_archive"
}
