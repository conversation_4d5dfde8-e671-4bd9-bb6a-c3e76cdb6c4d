package shipment

import (
	"time"

	"gorm.io/gorm"
)

// ShipperRewardSetting
//
// @Description: 配送员奖励设置
// @Author: Rixat
// @Time: 2023-11-17 08:43:26
// @receiver
// @param c *gin.Context
type ShipperRewardSetting struct {
	// 第一段
	ID              int            `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CityID          int            `gorm:"column:city_id"`
	AreaID          int            `gorm:"column:area_id"`
	City            City           `gorm:"foreignKey:city_id;references:id"` // 地区
	Area            Area           `gorm:"foreignKey:area_id;references:id"` // 区域
	MonthRestDay    int            `gorm:"column:month_rest_day"`            // 每月休息天数
	DailyOrderLimit int            `gorm:"column:daily_order_limit"`         // 每天最少订单数量
	RewardAmount    int            `gorm:"column:reward_amount"`             // 奖励金额
	WorkTime        string         `gorm:"column:work_time"`                 // 状态
	State           int            `gorm:"column:state"`                     // 状态
	CreatedAt       time.Time      `gorm:"column:created_at"`                // 创建时间
	UpdatedAt       time.Time      `gorm:"column:updated_at"`                // 更新时间
	DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at"`                // 删除标记字段
}

// TableName 表名称

func (*ShipperRewardSetting) TableName() string {
	return "t_shipper_reward_setting"
}

type Area struct {
	ID     int    `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	NameUg string `gorm:"column:name_ug;"`
	NameZh string `gorm:"column:name_zh;"`
}

func (*Area) TableName() string {
	return "b_area"
}

type City struct {
	ID     int    `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	NameUg string `gorm:"column:name_ug;"`
	NameZh string `gorm:"column:name_zh;"`
}

func (*City) TableName() string {
	return "b_city"
}
