package shipment

import (
	"time"

	"gorm.io/gorm"
)

// 配送员收入记录表
type ShipperIncome struct {
	ID                  int            `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"` // 自增ID
	CityID              int            `gorm:"column:city_id;NOT NULL" json:"city_id"`         // 城市ID
	AreaID              int            `gorm:"column:area_id;NOT NULL"  json:"area_id"`        // 区县ID
	City                City           `gorm:"foreignKey:city_id;references:id"`               // 地区
	Area                Area           `gorm:"foreignKey:area_id;references:id"`               // 区域
	Date                string         `gorm:"column:date"  json:"date"`
	OperatorID          int            `gorm:"column:operator_id"  json:"operator_id"`       // 操作人员
	ShipperID           int            `gorm:"column:shipper_id;NOT NULL" json:"shipper_id"` // 配送员
	ShipperName         string         `gorm:"column:shipper_name" json:"shipper_name"`
	ShipperMobile       string         `gorm:"column:shipper_mobile" json:"shipper_mobile"`
	OrderNo             string         `gorm:"column:order_no" json:"order_no"`
	OrderID             int            `gorm:"column:order_id" json:"order_id"`                  // 订单编号
	Order               OrderJoin      `gorm:"foreignKey:order_id;references:id"`                // 订单编号
	OrderPrice          int            `gorm:"column:order_price" json:"order_price"`            // 订单价格
	TemplateID          int            `gorm:"column:template_id" json:"template_id"`            // 配送费模板ID
	Type                int            `gorm:"column:type;default:1;NOT NULL" json:"type"`       // 类型 1：订单收入，2:奖励收入，3:打赏收入，4.特殊时间，5.特殊天气，6.好评，7.差评，8.投诉，9:迟到
	TypeName            string         `gorm:"-"  json:"type_name"`                              // 类型名称
	ComplaintType       int            `gorm:"column:complaint_type;"  json:"complaint_type"`    // 1：客户投诉，2：餐厅投诉
	ComplaintTypeName   string         `gorm:"-"  json:"complaint_type_name"`                    // 1：客户投诉，2：餐厅投诉
	Amount              int            `gorm:"column:amount" json:"amount_original"`             // 金额
	AmountStr           string         `gorm:"-" json:"amount"`                                  // 金额
	Remark              string         `gorm:"column:remark"  json:"remark"`                     // 备注
	ArchiveId           int            `gorm:"column:archive_id;default:null" json:"archive_id"` // 归档id
	CreatedAt           time.Time      `gorm:"column:created_at"   json:"created_at_original"`   // 创建时间
	CreatedAtStr        string         `gorm:"-"   json:"created_at"`                            // 创建时间
	UpdatedAt           time.Time      `gorm:"column:updated_at"`                                // 修改时间
	DeletedAt           gorm.DeletedAt `gorm:"column:deleted_at"`                                // 删除时间
	OrderDeliveryMinute int            `gorm:"column:order_delivery_minute" json:"order_delivery_minute"`
	CommentStar         int            `gorm:"column:comment_star" json:"comment_star"`
	ChangeLog           string         `gorm:"column:change_log" json:"change_log"`
	OrderType           int        	   `gorm:"column:order_type" json:"order_type"`
}

func (m *ShipperIncome) TableName() string {
	return "t_shipper_income"
}

// 订单历史表
type OrderJoin struct {
	ID              int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	Name            string    `gorm:"column:name"`                          // 预配送时间          int `gorm:"column:created_at"`                    // 下单城市编号
	Mobile          string    `gorm:"column:mobile"`                        // 预配送时间          int `gorm:"column:created_at"`                    // 下单城市编号
	BookingTime     time.Time `gorm:"column:booking_time"`                  // 预配送时间          int `gorm:"column:created_at"`                    // 下单城市编号
	DeliveryEndTime time.Time `gorm:"column:delivery_end_time"`             // 配送员完成时间
	Restaurant       Restaurant     `gorm:"foreignkey:store_id;references:id"`
	RestaurantID     int            `gorm:"column:store_id"` // 餐厅编号
	CreatedAt       time.Time `gorm:"column:created_at"`                    // 订单创建时间
}

func (m *OrderJoin) TableName() string {
	return "t_order"
}

// 订单历史表
type Restaurant struct {
	ID              int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	NameUg            string    `gorm:"column:name_ug"`                          //
	NameZh            string    `gorm:"column:name_zh"`                          //
}

func (m *Restaurant) TableName() string {
	return "t_restaurant"
}
type ChangeLog struct {
	OriginalValue int
	NewValue      int
	OriginalDate  string
	ChangeDate    string
	Type          int
}
