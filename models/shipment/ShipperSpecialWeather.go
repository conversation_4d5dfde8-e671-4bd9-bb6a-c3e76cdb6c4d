package shipment

import (
	"time"

	"gorm.io/gorm"
)

const (
	SHIPPER_SPECIAL_WEATHER_STATE_OFF = 0
	SHIPPER_SPECIAL_WEATHER_SATE_ON   = 1
)

// 特殊天气
type ShipperSpecialWeather struct {
	ID          int             `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CityID      int             `gorm:"column:city_id"`
	AreaID      int             `gorm:"column:area_id"`
	City        City            `gorm:"foreignKey:city_id;references:id"` // 地区
	Area        Area            `gorm:"foreignKey:area_id;references:id"` // 区域
	NameUg      string          `gorm:"column:name_ug"`                   // 维文名称
	NameZh      string          `gorm:"column:name_zh"`                   // 中文名称
	StartTime   time.Time       `gorm:"column:start_time"`                // 开始时间
	EndTime     time.Time       `gorm:"column:end_time"`                  // 结束时间
	ShipmentFee int             `gorm:"column:shipment_fee"`              // 配送费
	State       int             `gorm:"column:state"`                     // 2:关闭,1:开启
	CreatedAt   time.Time       `gorm:"column:created_at"`
	UpdatedAt   time.Time       `gorm:"column:updated_at"`
	DeletedAt   gorm.DeletedAt  `gorm:"column:deleted_at"`
	Incomes     []ShipperIncome `gorm:"foreignKey:template_id;references:id"` // 记录信息
}

func (m *ShipperSpecialWeather) TableName() string {
	return "t_shipper_special_weather"
}
