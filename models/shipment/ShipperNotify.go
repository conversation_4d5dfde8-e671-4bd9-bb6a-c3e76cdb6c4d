package shipment

import (
	"time"

	"gorm.io/gorm"
)

const (
	ShipperNotifyStateClose  = 1 // 关闭
	ShipperNotifyStateSent   = 2 // 已发
	ShipperNotifyStateResent = 3 // 重发
)

type ShipperNotify struct {
	ID        int            `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CityID    int            `gorm:"column:city_id;"`
	AreaID    int            `gorm:"column:area_id;"`
	City      City           `gorm:"foreignKey:city_id;references:id"` // 地区
	Area      Area           `gorm:"foreignKey:area_id;references:id"` // 区域
	TitleUg   string         `gorm:"column:title_ug"`                  // 标题维文
	TitleZh   string         `gorm:"column:title_zh"`                  // 标题中文
	ContentUg string         `gorm:"column:content_ug"`                // 内容维文
	ContentZh string         `gorm:"column:content_zh"`                // 内容中文
	SendTime  *time.Time     `gorm:"column:send_time"`                 // 发送时间
	SendCount int            `gorm:"column:send_count"`                // 发送次数
	State     int            `gorm:"column:state"`                     // 1:新建,2:已发,3:重发
	CreatedBy int            `gorm:"column:created_by"`                // 创建者ID
	CreatedAt time.Time      `gorm:"column:created_at"`                // 创建时间
	UpdatedAt time.Time      `gorm:"column:updated_at"`                // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at"`                // 删除时间

	Creator ShipperNotifyAdmin `gorm:"foreignKey:created_by;references:id"`
}

func (m *ShipperNotify) TableName() string {
	return "t_shipper_notify"
}

// ShipperNotifyAdmin 管理员信息表
type ShipperNotifyAdmin struct {
	ID                         int64          `json:"id" gorm:"id"`                                   // 自增编号
	AdminCityId                int64          `json:"admin_city_id" gorm:"admin_city_id"`             // 配送员使用
	AdminAreaId                int64          `json:"admin_area_id" gorm:"admin_area_id"`             // 配送员使用
	Type                       int8           `json:"type" gorm:"type"`                               // 管理员类型（OWNER:1,ADMIN:2,DEALER:3,DEALER_SUB:4,RESTAURANT_ADMIN:5,RESTAURANT_ADMIN_SUB:6,CENTURION:7,SHIPPER_ADMIN:8,SHIPPER:9）
	MerchantType               int8           `json:"merchant_type" gorm:"merchant_type"`             // 1:老板，2:店铺管理员，3:财务管理员，4:订单管理员，5：客服
	Level                      int8           `json:"level" gorm:"level"`                             // 代理商级别：1市级,2区级
	ParentId                   string         `json:"parent_id" gorm:"parent_id"`                     // 父管理员编号
	Avatar                     string         `json:"avatar" gorm:"avatar"`                           // 用户头像
	Mobile                     string         `json:"mobile" gorm:"mobile"`                           // 联系电话
	Name                       string         `json:"name" gorm:"name"`                               // 管理员账号
	Password                   string         `json:"password" gorm:"password"`                       // 管理员密码
	StatisticsPassword         string         `json:"statistics_password" gorm:"statistics_password"` // 管理员密码
	RealName                   string         `json:"real_name" gorm:"real_name"`                     // 管理员实名
	Age                        int64          `json:"age" gorm:"age"`                                 // 年龄
	Sex                        int64          `json:"sex" gorm:"sex"`                                 // 性别 1:男 2:女
	Idcard                     string         `json:"idcard" gorm:"idcard"`                           // 身份证号码
	Description                string         `json:"description" gorm:"description"`                 // 描述
	RememberToken              string         `json:"remember_token" gorm:"remember_token"`
	AuthToken                  string         `json:"auth_token" gorm:"auth_token"`                                         // 验证token
	SearialNumber              string         `json:"searial_number" gorm:"searial_number"`                                 // 序列号
	Lat                        float64        `json:"lat" gorm:"lat"`                                                       // 维度
	Lng                        float64        `json:"lng" gorm:"lng"`                                                       // 经度
	Accuracy                   float64        `json:"accuracy" gorm:"accuracy"`                                             // 经纬度准确度
	AttendanceState            int8           `json:"attendance_state" gorm:"attendance_state"`                             // 配送员考勤状态\n\n1 上班\n2 下班\n3 休息\n4 请假\n5 事故\n6 管理员暂停配送员配送\n7 管理员开启配送员配送\n
	AttendRadius               int64          `json:"attend_radius" gorm:"attend_radius"`                                   // 打卡地点半径
	AttendLatLng               string         `json:"attend_lat_lng" gorm:"attend_lat_lng"`                                 // 打卡地点经纬度
	Balance                    int64          `json:"balance" gorm:"balance"`                                               // 代理余额
	MarketBalance              int64          `json:"market_balance" gorm:"market_balance"`                                 // 代理营销余额
	GrabOrderCount             int64          `json:"grab_order_count" gorm:"grab_order_count"`                             // 配送员最多能抢的订单数
	AutoDispatchGrabOrderCount int64          `json:"auto_dispatch_grab_order_count" gorm:"auto_dispatch_grab_order_count"` // 智能派单最多分配订单数量
	AutoDispatchRankOrderCount int64          `json:"auto_dispatch_rank_order_count" gorm:"auto_dispatch_rank_order_count"` // 按配送员等级智能派单最多能分配数量
	AutoDispatchRank           int64          `json:"auto_dispatch_rank" gorm:"auto_dispatch_rank"`                         // 智能派单配送员等级
	AutoDispatchFinalScore     float32        `json:"auto_dispatch_final_score" gorm:"auto_dispatch_final_score"`           // 智能派单配送员分数
	Openid                     string         `json:"openid" gorm:"openid"`                                                 // 用户微信小程序openId
	State                      int8           `json:"state" gorm:"state"`                                                   // 状态（0关闭，1开通）
	LoginTime                  time.Time      `json:"login_time" gorm:"login_time"`                                         // 登录时间
	ShipperDistance            int64          `json:"shipper_distance" gorm:"shipper_distance"`                             // 配送员走的总距离
	ShipperOnTimeDeliveryRate  int64          `json:"shipper_on_time_delivery_rate" gorm:"shipper_on_time_delivery_rate"`   // 配送员准时送达率
	ShipperCustomerRate        int64          `json:"shipper_customer_rate" gorm:"shipper_customer_rate"`                   // 配送员客户满意率
	ShipperDeliveryAvgTime     int64          `json:"shipper_delivery_avg_time" gorm:"shipper_delivery_avg_time"`           // 配送员平均送达时间
	ShipperIncomeTemplateId    int64          `json:"shipper_income_template_id" gorm:"shipper_income_template_id"`         // 配送费模板
	LastCommentReaded          time.Time      `json:"last_comment_readed" gorm:"last_comment_readed"`                       // 最后读取评论的时间
	TakeCashOrder              int8           `json:"take_cash_order" gorm:"take_cash_order"`                               // 表示配送员是否抢现金订单（0表示不能抢，1表示能抢）
	BackOrderTimeLimit         int64          `json:"back_order_time_limit" gorm:"back_order_time_limit"`                   // 退单时间限制（单位：分钟）
	RecommendQrcode            string         `json:"recommend_qrcode" gorm:"recommend_qrcode"`                             // 配送员发展新客户的小程序二维码
	GroupId                    int64          `json:"group_id" gorm:"group_id"`                                             // PK区域编号（1表示和田地区、2表示喀什地区、3表示第三区、4表示第四区）
	TeamId                     int64          `json:"team_id" gorm:"team_id"`                                               // PK队伍编号（1表示和田东部队、2表示和田西部队、3表示喀什南部队、4表示喀什北部队、5表示南北联盟队、6表示乡村联盟队）
	IsCaptain                  int8           `json:"is_captain" gorm:"is_captain"`                                         // 是否队长
	JwtSerialNumber            int64          `json:"jwt_serial_number" gorm:"jwt_serial_number"`                           // jwt序列号
	IsInsuranceBlack           int8           `json:"is_insurance_black" gorm:"is_insurance_black"`                         // 是否保险黑名单 0不是,1是
	CreatedAt                  time.Time      `json:"created_at" gorm:"created_at"`                                         // 创建时间
	UpdatedAt                  time.Time      `json:"updated_at" gorm:"updated_at"`                                         // 修改时间
	DeletedAt                  gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"`                                         // 删除时间
}

// TableName 表名称
func (*ShipperNotifyAdmin) TableName() string {
	return "t_admin"
}
