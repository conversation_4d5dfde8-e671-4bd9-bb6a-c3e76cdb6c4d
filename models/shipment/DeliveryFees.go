package shipment

import (
	delivery "mulazim-api/requests/cms/delivery"
	"time"
)

const (
	// Type
	DeliveryFeesTypeArea       = 1 // 区域类型
	DeliveryFeesTypeRestaurant = 2 // 餐厅类型
	// Option
	DeliveryFeesOptionArea       = 1 // 跟随区域
	DeliveryFeesOptionRestaurant = 2 // 普通范围
)

// DeliveryFees 配送配置表
type DeliveryFees struct {
	ID             int                    `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`                          // 自增ID
	CityID         int                    `gorm:"column:city_id" json:"city_id"`                                           // 城市编号
	AreaID         int                    `gorm:"column:area_id" json:"area_id"`                                           // 区域编号
	RestaurantID   *int                   `gorm:"column:restaurant_id" json:"restaurant_id,omitempty"`                     // 餐厅编号（可为空）
	ParentID       *int                   `gorm:"column:parent_id" json:"parent_id,omitempty"`                             // 父ID
	DeliveryAreaId int                    `gorm:"column:delivery_area_id" json:"delivery_area_id,omitempty"`               // 配送范围唯一 ID
	Type           uint8                  `gorm:"column:type" json:"type"`                                                 // 类型：1 餐厅数据 / 2 区域数据
	Option         uint8                  `gorm:"column:option" json:"option"`                                             // 配送费选项 - 1 跟随区域 / 2 使用餐厅配置
	Default        uint8                  `gorm:"column:default" json:"default"`                                           // 是否默认：1 默认 / 2 设置时间
	Stages         delivery.DeliveryFeeStages `gorm:"column:stages;type:json" json:"stages,omitempty"`                         // 存储价格阶梯数据（JSON格式）
	StageFeeMin    uint                   `gorm:"column:stage_fee_min" json:"stage_fee_min,omitempty"`                     // 配送费阶梯最小起始费用
	BeginTime      string                 `gorm:"column:begin_time" json:"begin_time,omitempty"`                           // 使用该配送费阶梯 - 开始时间
	EndTime        string                 `gorm:"column:end_time" json:"end_time,omitempty"`                               // 使用该配送费阶梯 - 结束时间
	StartFee       uint                   `gorm:"column:start_fee" json:"start_fee"`                                       // 起送价，默认0
	UpdatedAdminID int                    `gorm:"column:updated_admin_id" json:"updated_admin_id"`                         // 更新者编号
	CreatedAt      time.Time              `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at,omitempty"` // 创建时间，默认当前时间
	UpdatedAt      time.Time              `gorm:"column:updated_at;default:CURRENT_TIMESTAMP" json:"updated_at,omitempty"` // 最后更新时间，默认当前时间
}

func (DeliveryFees) TableName() string {
	return "t_delivery_fees"
}
