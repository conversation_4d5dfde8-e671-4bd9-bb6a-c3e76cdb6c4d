package shipment

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// TShipperIncomeTemplate 配送员收入模板
type ShipperIncomeTemplate struct {
	// 第一段
	ID                 int     `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	OperatorID         int     `gorm:"column:operator_id"`
	CityIdID           int     `gorm:"column:city_id"`
	AreaID             int     `gorm:"column:area_id"`
	NameUg             string  `gorm:"column:name_ug"`               // 模板维文名称
	NameZh             string  `gorm:"column:name_zh"`               // 模板中文名称
	BaseSalary         int     `gorm:"column:base_salary"`           // 基本工资
	RuleType           int     `gorm:"column:rule_type"`             // 规则类型:1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
	RuleContent        *string `gorm:"column:rule_content"`          // 规则内容
	RuleOrderCountType *int    `gorm:"column:rule_order_count_type"` // 按订单数量计算配送费类型：1:全部订单，2:超过部分
	// 第二段
	SpecialShipmentState   int     `gorm:"column:special_shipment_state"`   // 特殊配送费状态: 0:关闭,1:开启
	SpecialShipmentContent *string `gorm:"column:special_shipment_content"` // 特殊配送费内容
	// 第三段
	CommentDeductionFee int `gorm:"column:comment_deduction_fee"` // 差评扣款金额
	CommentIncrementFee int `gorm:"column:comment_increment_fee"` // 好评奖励金额

	ComplainDeductionFee int            `gorm:"column:complain_deduction_fee"` // 投诉扣款金额
	LateDeductionType    int            `gorm:"column:late_deduction_type"`    // 迟到扣款类型:1:按订单数量,2:迟到时间
	LateDeductionContent *string        `gorm:"column:late_deduction_content"` // 迟到扣款规则


	InviteUserFee int `gorm:"column:invite_user_fee"` //推广新用户奖励额度 
	InviteOldUserFee int    `gorm:"invite_old_user_fee" binding:""`         // 推广老客户奖励额度(没下单超过3个月)
	OrderPercent  int `gorm:"column:order_percent"`	  // 推广用户订单分成百分比，单位：万分之一	
	OrderOldUserPercent  int `gorm:"column:order_old_user_percent"`	  // 推广用户订单分成百分比，单位：万分之一	

	State                int            `gorm:"column:state"`                  // 1:创建，2：更新
	CreatedAt            time.Time      `gorm:"column:created_at"`             // 创建时间
	UpdatedAt            time.Time      `gorm:"column:updated_at"`             // 更新时间
	DeletedAt            gorm.DeletedAt `gorm:"column:deleted_at"`             // 删除标记字段
}

// TableName 表名称

func (*ShipperIncomeTemplate) TableName() string {
	return "t_shipper_income_template"
}

func (t *ShipperIncomeTemplate) GetSpecialShipmentPrice(timeStr string, distance int) (int, error) {
	if t.SpecialShipmentState != SpecialShipmentStateOpen {
		return 0, nil
	}
	var specialShipmentRules []SpecialShipmentFeeRule
	err := json.Unmarshal([]byte(*t.SpecialShipmentContent), &specialShipmentRules)
	if err != nil {
		msg := fmt.Sprintf("ID为%d的特殊配送费模板解析失败", t.ID, err)
		return 0, errors.New(msg)
	}
	for _, rule := range specialShipmentRules {
		ok, err := rule.inTimeRange(timeStr)
		if err != nil {
			return 0, err
		}
		if ok {
			return rule.getSpecialShipmentPriceFromStep(distance)
		}
	}
	return 0, nil
}

// GetFixShipmentFeeRules 获取固定配送费规则
func (t *ShipperIncomeTemplate) GetFixShipmentFeeRules() (*FixShipmentFeeRule, error) {
	if t.RuleType != RuleTypeFixed {
		msg := fmt.Sprintf("ID为%d的配送费模板不是固定配送费模板", t.ID)
		return nil, errors.New(msg)
	}
	var rules []FixShipmentFeeRule
	err := json.Unmarshal([]byte(*t.RuleContent), &rules)
	if err != nil {
		msg := fmt.Sprintf("ID为%d的配送费模板规则内容解析失败", t.ID)
		return nil, errors.New(msg)
	}
	return &rules[0], nil
}

// GetDistanceBaseShipmentFeeRules 获取按距离计算配送费规则
func (t *ShipperIncomeTemplate) GetDistanceBaseShipmentFeeRules() ([]DistanceBaseShipmentFeeRule, error) {
	if t.RuleType != RuleTypeDistance {
		msg := fmt.Sprintf("ID为%d的配送费模板不是按距离计算配送费模板", t.ID)
		return nil, errors.New(msg)
	}
	var rules []DistanceBaseShipmentFeeRule
	err := json.Unmarshal([]byte(*t.RuleContent), &rules)
	if err != nil {
		msg := fmt.Sprintf("ID为%d的配送费模板规则内容解析失败", t.ID)
		return nil, errors.New(msg)
	}
	return rules, nil
}

// GetTaxiBaseShipmentFeeRules 获取按出租车配送费计算规则
func (t *ShipperIncomeTemplate) GetTaxiBaseShipmentFeeRules() (*TaxiBaseShipmentFeeRule, error) {
	if t.RuleType != RuleTypeCar {
		msg := fmt.Sprintf("ID为%d的配送费模板不是按出租车配送费计算模板", t.ID)
		return nil, errors.New(msg)
	}
	var rules []TaxiBaseShipmentFeeRule
	err := json.Unmarshal([]byte(*t.RuleContent), &rules)
	if err != nil {
		msg := fmt.Sprintf("ID为%d的配送费模板规则内容解析失败", t.ID)
		return nil, errors.New(msg)
	}
	return &rules[0], nil
}

// GetOrderCountBaseShipmentFeeRules 获取按订单数量计算配送费规则
// return 按订单数量计算配送费规则
// return error
func (t *ShipperIncomeTemplate) GetOrderCountBaseShipmentFeeRules() ([]OrderCountBaseShipmentFeeRule, error) {
	if t.RuleType != RuleTypeOrder {
		msg := fmt.Sprintf("ID为%d的配送费模板不是按订单数量计算配送费模板", t.ID)
		return nil, errors.New(msg)
	}
	var rules []OrderCountBaseShipmentFeeRule
	err := json.Unmarshal([]byte(*t.RuleContent), &rules)
	if err != nil {
		msg := fmt.Sprintf("ID为%d的配送费模板规则内容解析失败", t.ID)
		return nil, errors.New(msg)
	}
	return rules, nil
}

func (t *ShipperIncomeTemplate) IsSpecialShipmentPriceActive() bool {
	return t.SpecialShipmentState == SpecialShipmentStateOpen
}

const (
	// 规则类型:1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
	RuleTypeFixed          = 1
	RuleTypeDistance       = 2
	RuleTypeCar            = 3
	RuleTypeOrder          = 4
	RuleTypeOrderCountAll  = 1 //从超过的部分开始 把以前 全部订单
	RuleTypeOrderCountOver = 1 //从超过的部分开始
)

const (
	// 特殊配送费状态: 0:关闭,1:开启
	SpecialShipmentStateClose = 0
	SpecialShipmentStateOpen  = 1
)

const (
	// 迟到扣款类型:1:按订单数量,2:迟到时间
	DeductionTypeOrder = 1
	DeductionTypeTime  = 2
)

// FixShipmentFeeRule 固定配送费规则
type FixShipmentFeeRule struct {
	FixShipmentFee int `json:"fix_shipment_fee"` // 固定配送费 单位分
}

// DistanceBaseShipmentFeeRule 按距离计算配送费规则
type DistanceBaseShipmentFeeRule struct {
	Distance    int `json:"distance"`     // 距离 单位米
	ShipmentFee int `json:"shipment_fee"` // 配送费	单位分
}

// TaxiBaseShimentFeeRule 按出租车配送费计算规则
type TaxiBaseShipmentFeeRule struct {
	FixedStartFee     int `json:"fixed_start_fee"`     // 基础配送费	单位分
	Distance          int `json:"distance"`            // 固定费用所包含的配送距离 单位米
	PricePerKilometer int `json:"price_per_kilometer"` // 超出最低配送距离部分按每公里配送价计算 单位分
}

// OrderCountBaseShipmentFeeRule 按订单数量计算配送费规则
type OrderCountBaseShipmentFeeRule struct {
	StartOrderCount int `json:"start_order_count"` // 订单数到这个值每个订单分配给配送员的配送费
	ShipmentFee     int `json:"shipment_fee"`      // 配送费	单位分
}

type SpecialShipmentFeeRule struct {
	StartTime    string                        `json:"start_time"`
	EndTime      string                        `json:"end_time"`
	DistanceStep []SpecialShipmentDistanceStep `json:"distance_step"`
}

func (ssf *SpecialShipmentFeeRule) inTimeRange(timeStr string) (bool, error) {
	var err error
	start, err := time.Parse("15:04", ssf.StartTime)
	if err != nil {
		return false, errors.New("开始时间错误")
	}
	end, err := time.Parse("15:04", ssf.EndTime)
	if err != nil {
		return false, errors.New("结束时间错误")
	}
	t, err := time.Parse("15:04", timeStr)
	if err != nil {
		return false, errors.New("给定的时间错误")
	}
	if t.Before(end) && t.After(start) {
		return true, nil
	}
	return false, nil
}

// getSpecialShipmentPriceFromStep 特殊时间计算配送费阶梯计算
func (ssf *SpecialShipmentFeeRule) getSpecialShipmentPriceFromStep(distance int) (int, error) {
	for _, step := range ssf.DistanceStep {
		if step.Distance >= distance {
			return step.ShipmentFee, nil
		}
	}
	return 0, errors.New("订单距离超出阶梯最大值")
}

// SpecialShipmentDistanceStep 特殊配送费距离价格阶梯
type SpecialShipmentDistanceStep struct {
	Distance    int `json:"distance"`
	ShipmentFee int `json:"shipment_fee"`
}
