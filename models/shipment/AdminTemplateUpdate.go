package shipment

import (
	"time"

	"gorm.io/gorm"
)

// TShipperIncomeTemplate 配送员收入模板
type AdminTemplateUpdate struct {
	// 第一段
	ID            int                   `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	OldTemplateID int                   `gorm:"column:old_template_id"` // 更新前在使用的模板ID
	TemplateID    int                   `gorm:"column:template_id"`     // 需要更新的模板ID
	ShipperID     int                   `gorm:"column:shipper_id"`      // 配送员ID
	CreatedAt     time.Time             `gorm:"column:created_at"`      // 创建时间
	UpdatedAt     time.Time             `gorm:"column:updated_at"`      // 更新时间
	DeletedAt     gorm.DeletedAt        `gorm:"column:deleted_at"`      // 删除标记字段
	Template      ShipperIncomeTemplate `gorm:"foreignKey:template_id;references:id"`
}

// TableName 表名称

func (m *AdminTemplateUpdate) TableName() string {
	return "t_admin_template_update"
}
