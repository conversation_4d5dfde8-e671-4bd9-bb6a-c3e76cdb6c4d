package models

import (
	"time"

	"gorm.io/gorm"
)

 

type MerchantAdver struct {
	ID             int           `gorm:"primaryKey;autoIncrement;column:id"`
	Type        int     `gorm:"column:type;default:1;comment:表示类型 1为 启动页面显示 2为弹窗形式显示 3 首页弹窗广告" json:"type"`
	AreaID      *int     `gorm:"column:area_id;default:null;comment:表示显示的区域" json:"area_id,omitempty"`
	LinkType    *int     `gorm:"column:link_type;default:null;comment:跳转类型 （0表示不跳转 1表示跳转wap页面 3 app内部页面 ）" json:"link_type,omitempty"`
	LinkURL     *string   `gorm:"column:link_url;type:varchar(255);default:null;comment:跳转url" json:"link_url,omitempty"`
	TitleUG     *string   `gorm:"column:title_ug;type:varchar(128);default:null;comment:通知标题" json:"title_ug,omitempty"`
	TitleZH     *string   `gorm:"column:title_zh;type:varchar(128);default:null;comment:通知标题" json:"title_zh,omitempty"`
	ContentUG   *string   `gorm:"column:content_ug;type:varchar(255);default:null;comment:内容" json:"content_ug,omitempty"`
	ContentZH   *string   `gorm:"column:content_zh;type:varchar(255);default:null;comment:内容" json:"content_zh,omitempty"`
	ImageUG     *string   `gorm:"column:image_ug;type:varchar(255);default:null;comment:图片" json:"image_ug,omitempty"`
	AppID       *int     `gorm:"default:null;comment:关联 app_id" json:"app_id,omitempty"`
	ImageZH     *string   `gorm:"type:varchar(255);default:null;comment:图片" json:"image_zh,omitempty"`
	ShowTime    int     `gorm:"default:5;comment:显示的时长 单位秒" json:"show_time"`
	StartTime   *time.Time `gorm:"default:null;comment:开始时间" json:"start_time,omitempty"`
	EndTime     *time.Time `gorm:"default:null;comment:结束时间" json:"end_time,omitempty"`
	State       int     `gorm:"default:1;comment:状态" json:"state"`
	CreatedAt   *time.Time `gorm:"default:null;comment:创建时间" json:"created_at,omitempty"`
	UpdatedAt   *time.Time `gorm:"default:null;onUpdate:CURRENT_TIMESTAMP;comment:修改时间" json:"updated_at,omitempty"`
	DeletedAt   gorm.DeletedAt `gorm:"default:null;comment:删除时间" json:"deleted_at,omitempty"`

}

func (m *MerchantAdver) TableName() string {
	return "t_merchant_adver"
}
