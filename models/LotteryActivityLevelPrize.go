package models

import (
	"time"

	"gorm.io/gorm"
)

const (
	LotteryActivityLevelPrizeStateOn  = 1
	LotteryActivityLevelPrizeStateOff = 0
)

// 抽奖活动详细 奖品 内容
type LotteryActivityLevelPrize struct {
	ID                     int       `gorm:"column:id;AUTO_INCREMENT;primary_key"`
	LotteryID              int       `gorm:"column:lottery_id"`                     // 活动id
	LotteryActivityLevelID int       `gorm:"column:lottery_activity_level_id"`      // 奖品等级
	Level                  int       `gorm:"column:level"`                          // 奖品内部等级
	PrizeID                int       `gorm:"column:prize_id"`                       // 奖品id
	Count                  int       `gorm:"column:count;NOT NULL"`                 // 奖品库存数量
	Probability            uint      `gorm:"column:probability;NOT NULL"`           // 获奖概率
	TakenCount             uint      `gorm:"column:taken_count;default:0;NOT NULL"` // 奖品已获奖数量
	LuckyUserIndex         int       `gorm:"column:lucky_user_index;NOT NULL"`      // 中奖用户位次
	State                  int       `gorm:"column:state"`                          // 状态
	AdminID                int       `gorm:"column:admin_id"`                       // 创建人
	CreatedAt              time.Time `gorm:"column:created_at"`                     // 创建时间
	UpdatedAt              time.Time `gorm:"column:updated_at"`                     // 更新时间
	DeletedAt             gorm.DeletedAt `gorm:"type:timestamp" json:"deleted_at"`
	LotteryPrize          LotteryPrize `gorm:"foreignKey:prize_id;references:id"`

	LotteryActivityLevelWinnersSet LotteryActivityLevelWinnersSet `gorm:"foreignKey:lottery_activity_level_prize_id;references:id"`


}

func (m *LotteryActivityLevelPrize) TableName() string {
	return "t_lottery_activity_level_prize"
}

