package models

import (
	"time"
)

// FoodsPreferentialLog 美食优惠信息表
type FoodsPreferentialLog struct {
	ID                int               `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	Type              int               `gorm:"column:type;NOT NULL"`                 // 优惠类型
	PriceMarkupID     int               `gorm:"column:price_markup_id;"`              //
	PrefID            int               `gorm:"column:pref_id;"`                      //
	UserId            int               `gorm:"column:user_id;"`                      //
	OrderId           int               `gorm:"column:order_id;"`                     //
	DetailId          int               `gorm:"column:detail_id;"`                    //
	SeckillId         int               `gorm:"column:seckill_id;"`                   //
	FoodId            int               `gorm:"column:food_id;"`                      //
	SaledCount        int     `gorm:"column:saled_count;"`                  //
	PrefPrice         int               `gorm:"column:pref_price;"`                   //
	State             int               `gorm:"column:state;NOT NULL"`                // 状态（0关闭，1开通）
	CreatedAt         time.Time         `gorm:"column:created_at"`                    // 创建时间
	UpdatedAt         time.Time         `gorm:"column:updated_at"`                    // 修改时间
	FoodsPreferential FoodsPreferential `gorm:"foreignkey:pref_id;references:id"`     // 订单推送时的数据
	User              User              `gorm:"foreignkey:user_id;references:id"`
	Food              RestaurantFoods   `gorm:"foreignkey:food_id;references:id"`
	OrderDetail       OrderDetail       `gorm:"foreignkey:detail_id;references:id"`
	Order             Order             `gorm:"foreignkey:id;references:order_id"`
	OrderToday        OrderToday        `gorm:"foreignkey:id;references:order_id"`
	PriceMarkupFood          PriceMarkupFood  `gorm:"foreignkey:price_markup_id;references:id"`
}

func (m *FoodsPreferentialLog) TableName() string {
	return "t_foods_preferential_log"
}



type FoodsPreferentialLogGroupBySelect struct {
	PrefID    int `gorm:"pref_id"`
	PriceMarkupID    int `gorm:"price_markup_id"`
	PrefPrice int `gorm:"pref_price"`
	BeginSellTime      time.Time               `gorm:"column:begin_sell_time;"`                  //
	EndSellTime        time.Time               `gorm:"column:end_sell_time;"`                  //
	SaledCount        int               `gorm:"column:saled_count;"`                  //
	State        int `gorm:"state"`             // 状态 1 成功 ，2 退款
	Type         int `gorm:"type"`               // 1:秒杀，2:特价 3:加价销售
}

func (m *FoodsPreferentialLogGroupBySelect) TableName() string {
	return "t_foods_preferential_log"
}
