package models

import (
	"time"
)

const (
	MarketingRestaurantIsHaveMarket         = 1
	MarketingRestaurantIsHaveShipmentReduce = 1
)

// 商家满减活动表
type MarketingRestaurantState struct {
	ID     int `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"` // 编号 编号
	CityID int `gorm:"column:city_id" json:"city_id"`
	AreaID int `gorm:"column:area_id;NOT NULL" json:"area_id"` // 地区编号

	RestaurantID int `gorm:"column:restaurant_id" json:"restaurant_id"` // 餐厅编号

	IsHaveMarket         int  `gorm:"column:is_have_market" json:"is_have_market"`                   // 餐厅编号
	IsHaveShipmentReduce *int `gorm:"column:is_have_shipment_reduce" json:"is_have_shipment_reduce"` // 餐厅编号

	CreatedAt *time.Time `gorm:"column:created_at" json:"created_at"` // 创建时间
	UpdatedAt *time.Time `gorm:"column:updated_at" json:"updated_at"` // 更新时间
	DeletedAt *time.Time `gorm:"column:deleted_at" json:"deleted_at"` // 被删除时间
}

func (m *MarketingRestaurantState) TableName() string {
	return "t_marketing_restaurant_state"
}
