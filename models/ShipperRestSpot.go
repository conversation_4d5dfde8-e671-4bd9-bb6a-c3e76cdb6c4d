package models

import (
    "time"
)

type ShipperRestSpot struct {
    ID        int           `gorm:"column:id" json:"id"`
    CityID    int          `gorm:"column:city_id" json:"city_id,omitempty"`
    AreaID    int          `gorm:"column:area_id" json:"area_id,omitempty"`
    NameUg    string       `gorm:"column:name_ug" json:"name_ug,omitempty"`
    NameZh    string       `gorm:"column:name_zh" json:"name_zh,omitempty"`
    DescUg    string        `gorm:"column:desc_ug" json:"desc_ug"`
    DescZh    string        `gorm:"column:desc_zh" json:"desc_zh"`
    Lng       string       `gorm:"column:lng" json:"lng,omitempty"`
    Lat       string       `gorm:"column:lat" json:"lat,omitempty"`
    AdminID   int          `gorm:"column:admin_id" json:"admin_id,omitempty"`
    State     int          `gorm:"column:state" json:"state,omitempty"`
    CreatedAt time.Time    `gorm:"column:created_at" json:"created_at,omitempty"`
    UpdatedAt time.Time    `gorm:"column:updated_at" json:"updated_at,omitempty"`
    City      City `gorm:"foreignkey:CityID;references:ID"`
    Area       Area `gorm:"foreignkey:AreaID;references:ID"`
    Admin       Admin `gorm:"foreignkey:AdminID;references:ID"`

}
func (ShipperRestSpot) TableName() string {
    return "t_shipper_rest_spot"
}