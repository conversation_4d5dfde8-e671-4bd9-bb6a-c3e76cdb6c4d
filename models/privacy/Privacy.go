package privacy

import (
	"time"
)

type Privacy struct {
	Id         int       `gorm:"column:id;primary_key;default:0"`
	Url        string    `gorm:"column:url"`                  // web页面连接地址
	NameUg     string    `gorm:"column:name_ug"`              // 标题维文名称
	NameZh     string    `gorm:"column:name_zh"`              // 标题中文名称
	TerminalId int       `gorm:"column:terminal_id;NOT NULL"` // 跳转终端： 1=>'微信小程序', 2=>'配送端', 3=>'商家端',4=>'用户端',
	State      int       `gorm:"column:state;default:0"`      // 状态（1开通，2关闭）
	CreatedAt  time.Time `gorm:"column:created_at"`           // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at"`           // 修改时间
	DeletedAt  time.Time `gorm:"column:deleted_at"`           // 删除时间
	Weight     int       `gorm:"column:weight"`               // 排序
}

func (m *Privacy) TableName() string {
	return "`t_web_view`"
}

type PrivacyList struct {
	Id    int    `json:"id"`
	Url   string `json:"url"`
	Title string `json:"title"`
}
