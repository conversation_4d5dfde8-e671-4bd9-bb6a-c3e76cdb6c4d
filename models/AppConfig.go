package models

import (
	"time"

	"gorm.io/gorm"
)

// AppConfig 代表 app_config 表中的记录
type AppConfig struct {
	ID         int       `gorm:"column:id;primary_key" json:"id"`
	Key        string    `gorm:"column:key;uniqueIndex;not null" json:"key"` // 配置名称
	Value      string    `gorm:"column:value" json:"value"`                 // 值
	OsType     string    `gorm:"column:os_type" json:"os_type"`             // 操作系统
	Version    string    `gorm:"column:version" json:"version"`             // 应用版本
	State      int      `gorm:"column:state" json:"state"`                 // 是否生效
	CreatedAt  time.Time `gorm:"column:created_at" json:"created_at"`       // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at" json:"updated_at"`       // 修改时间
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`       // 删除时间
}


func (m *AppConfig) TableName() string {
	return "app_config"
}
