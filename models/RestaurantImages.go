package models

import (
	"time"
)

// 商家内镜图信息表
type RestaurantImages struct {
	ID           int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	RestaurantID int       `gorm:"column:restaurant_id;NOT NULL"`        // 餐厅编号
	ImageUrl     string    `gorm:"column:image_url"`                     // 图片地址
	State        int       `gorm:"column:state"`                         // 证件状态（0表示待审核，1表示审核通过）
	CreatedAt    time.Time `gorm:"column:created_at"`                    // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at"`                    // 修改时间
	DeletedAt    time.Time `gorm:"column:deleted_at"`                    // 删除时间
}

func (m *RestaurantImages) TableName() string {
	return "b_restaurant_images"
}