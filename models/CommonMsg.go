package models
import (
	"time"
)

// 平台关于信息表
type CommonMsg struct {
	ID        int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"` // 自增编号
	Type      int       `gorm:"column:type;default:0;NOT NULL" json:"type"`     
    NameUG      string          `gorm:"column:name_ug;" json:"name_ug"`
    NameZH      string          `gorm:"column:name_zh;" json:"name_zh"`
    UseCount    int            `gorm:"column:use_count;default:0" json:"use_count"`
    State       int           `gorm:"column:state;default:1" json:"state"`
	
	CreatedAt time.Time `gorm:"column:created_at" json:"created_at"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at"` // 修改时间
}

func (m *CommonMsg) TableName() string {
	return "t_common_msg"
}

