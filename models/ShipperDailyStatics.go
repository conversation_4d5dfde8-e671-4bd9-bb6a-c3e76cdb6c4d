package models
// Code generated by sql2gorm. DO NOT EDIT.

import (
"time"
)

type ShipperDailyStatics struct {
	ShipperID          int       `gorm:"column:shipper_id;NOT NULL"`                           // 配送员ID
	AreaID             int       `gorm:"column:area_id;NOT NULL"`                              // 配送员所属区县ID
	AreaNameUg         string    `gorm:"column:area_name_ug"`                                  // 区县名称
	AreaNameZh         string    `gorm:"column:area_name_zh"`                                  // 区县名称
	RealName           string    `gorm:"column:real_name"`                                     // 配送员姓名
	FinishedOrderCount int       `gorm:"column:finished_order_count;default:0"`                // 完成订单数
	LateOrderCount     int       `gorm:"column:late_order_count;default:0"`                    // 迟到订单数
	TotalPrice         int       `gorm:"column:total_price;default:0"`                         // 所有订单总额
	CashTotalPrice     int       `gorm:"column:cash_total_price;default:0"`                    // 现金订单总额
	LateOrderFee       int       `gorm:"column:late_order_fee;default:0"`                      // 迟到订单总金额
	TotalLateSecond    int       `gorm:"column:total_late_second;default:0"`                   // 迟到订单总时间(秒)
	ReturnedOrderCount int       `gorm:"column:returned_order_count;default:0"`                // 退回订单数
	Distance           float64   `gorm:"column:distance;default:0.000"`                        // 配送员每日走的总路程
	TransDate          time.Time `gorm:"column:trans_date;primary_key"`                        // 交易日期
	CreatedAt          time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL"` // 创建时间
	DeliveryAvgTime    int       `gorm:"column:delivery_avg_time;default:20"`                  // 平均配送时间
}

func (m *ShipperDailyStatics) TableName() string {
	return "t_shipper_daily_statics"
}

