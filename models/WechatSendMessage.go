package models

import (
	"time"
)

type WechatSendMessage struct {
	ID          int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	OrderID     int       `gorm:"column:order_id;NOT NULL"` // 订单编号
	OrderNo     string    `gorm:"column:order_no"`
	StoreID     int       `gorm:"column:store_id"`           // 餐厅Id
	StoreName   string    `gorm:"column:store_name"`         // 餐厅名称
	StorePhone  string    `gorm:"column:store_phone"`        // 餐厅联系电话
	TotalFee    int       `gorm:"column:total_fee"`          // 订单金额（单位：分）
	BookingTime time.Time `gorm:"column:booking_time"`       // 配送时间
	OpenID      string    `gorm:"column:open_id;NOT NULL"`   // 小程序open_id
	PrepayID    string    `gorm:"column:prepay_id;NOT NULL"` // 订单微信支付预支付编号
	FormID1     string    `gorm:"column:form_id1"`           // formId
	FormID2     string    `gorm:"column:form_id2"`           // formId
	FormID3     string    `gorm:"column:form_id3"`           // formId
	LangID      int       `gorm:"column:lang_id;default:1"`  // 1:ug,2:zh
	CreatedAt   time.Time `gorm:"column:created_at"`         // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at"`         // 修改时间
	DeletedAt   *time.Time `gorm:"column:deleted_at"`         // 删除时间
}

func (m *WechatSendMessage) TableName() string {
	return "t_wechat_send_message"
}