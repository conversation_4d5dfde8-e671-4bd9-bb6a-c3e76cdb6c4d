﻿package models

import (
	"time"

	"gorm.io/gorm"
)

type PriceMarkupSeckillPriceLog struct {
	ID            int            `json:"id" gorm:"id;primaryKey;autoIncrement"`                           // 自增编号
	PriceMarkupID int            `json:"price_markup_id" gorm:"price_markup_id;"` // 自增编号
	SeckillID int            `json:"seckill_id" gorm:"seckill_id;"` // 自增编号
	CityID        int            `json:"city_id" gorm:"city_id"`                                          // 城市编号
	AreaID        int            `json:"area_id" gorm:"area_id"`                                          // 区域编号
	RestaurantID  int            `json:"restaurant_id" gorm:"restaurant_id;NOT NULL"`                     // 餐厅编号
	FoodID       int            `json:"food_id" gorm:"food_id;NOT NULL"`                     // 餐厅美食编号
	Price         int            `json:"price" gorm:"price"`                                              // 成本价
	Count         int            `json:"count" gorm:"count"`                                              // 成本价
	AdminID       int            `json:"admin_id" gorm:"admin_id"`                                        // 创建者ID
	State        int            `json:"state" gorm:"state"`                                        // 创建者ID
	CreatedAt     time.Time      `json:"created_at" gorm:"created_at"`                                    // 创建时间
	UpdatedAt     *time.Time     `json:"updated_at" gorm:"updated_at"`                                    // 修改时间
	DeletedAt     gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"`                                    // 删除时间
	SeckillLog          []SeckillLog  `gorm:"foreignkey:seckill_price_log_id;references:id"`                   // 删除时间
	PriceMarkupFoodLogSum      PriceMarkupFoodLog  `gorm:"foreignkey:seckill_price_log_id"` 
	PriceMarketSeckillPriceLogSum      PriceMarketSeckillPriceLogSum  `gorm:"foreignkey:seckill_price_log_id"` 
	SpecID int `json:"spec_id" gorm:"spec_id"` // 规格ID
}

type PriceMarketSeckillPriceLogSum struct {
	PriceMarkupFoodLog
	SeckillPriceLogID int `gorm:"seckill_price_log_id"`
	SaledCount      int  `gorm:"saled_count"`
	Price      int  `gorm:"price"`
	BeginSellTime      time.Time  `gorm:"begin_sell_time"`
	EndSellTime      time.Time  `gorm:"end_sell_time"`
}

func (m *PriceMarkupSeckillPriceLog) TableName() string {
	return "b_price_markup_seckill_price_log"
}
