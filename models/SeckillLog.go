﻿package models

import (
	"time"
)

type SeckillLog struct {
	ID       int64 `json:"id" gorm:"id"`
	UserId   int64 `json:"user_id" gorm:"user_id"`     // 用户ID
	OrderId  int64 `json:"order_id" gorm:"order_id"`   // 订单ID
	DetailId int64 `json:"detail_id" gorm:"detail_id"` // 订单详情ID

	Type                      int64           `json:"type" gorm:"type"`                                 // 1:秒杀，2:特价 3:加价销售
	PriceMarkupId             int64           `json:"price_markup_id" gorm:"price_markup_id"`           // 加价美食库id
	SeckillPriceLogId         int             `json:"seckill_price_log_id" gorm:"seckill_price_log_id"` // 加价美食库id
	SeckillId                 int64           `json:"seckill_id" gorm:"seckill_id"`                     // 秒杀ID
	FoodId                    int64           `json:"food_id" gorm:"food_id"`                           // 美食
	SaledCount                int64           `json:"saled_count" gorm:"saled_count"`                   // 购买数量
	SeckillPrice              int64           `json:"seckill_price" gorm:"seckill_price"`
	SeckillPlatformServiceFee int64           `json:"seckill_platform_service_fee" gorm:"seckill_platform_service_fee"`
	State                     int8            `json:"state" gorm:"state"` // 状态 1 成功 ，2 退款
	CreatedAt                 time.Time       `json:"created_at" gorm:"created_at"`
	UpdatedAt                 time.Time       `json:"updated_at" gorm:"updated_at"`
	Seckill                   Seckill         `gorm:"foreignkey:seckill_id;references:id"` // 订单推送时的数据
	User                      User            `gorm:"foreignkey:user_id;references:id"`
	Food                      RestaurantFoods `gorm:"foreignkey:food_id;references:id"`
	OrderDetail               OrderDetail     `gorm:"foreignkey:detail_id;references:id"`
	Order                     Order           `gorm:"foreignkey:id;references:order_id"`
	OrderToday                OrderToday      `gorm:"foreignkey:id;references:order_id"`
	FoodType                  int             `json:"food_type" gorm:"food_type"` // 规格ID
	SpecID                    int             `json:"spec_id" gorm:"spec_id"` // 规格ID
	RestaurantFoods           RestaurantFoods              `gorm:"foreignkey:food_id;references:id"`
	SelectedSpec          FoodSpec 		`gorm:"foreignkey:spec_id;references:id"`
}

func (m *SeckillLog) TableName() string {
	return "b_seckill_log"
}

type SeckillLogGroupBySelect struct {
	SeckillId    int `json:"seckill_id" gorm:"seckill_id"`
	PriceMarkupId    int `json:"price_markup_id" gorm:"seckill_id"`
	SeckillPriceLogId int `json:"seckill_price_log_id" gorm:"seckill_price_log_id"`
	SeckillPrice int `json:"seckill_price" gorm:"seckill_price"`
	StartSellTime time.Time `json:"start_sell_time"`
	EndSellTime  time.Time `json:"end_sell_time"`
	SaledCount   int `json:"saled_count" gorm:"saled_count"` // 购买数
	State        int `json:"state" gorm:"state"`             // 状态 1 成功 ，2 退款
	Type         int `json:"type" gorm:"type"`               // 1:秒杀，2:特价 3:加价销售
}

func (m *SeckillLogGroupBySelect) TableName() string {
	return "b_seckill_log"
}
