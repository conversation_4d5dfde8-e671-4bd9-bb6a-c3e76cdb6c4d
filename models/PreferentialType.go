package models

import (
	"time"
)

// 优惠类型信息表
type PreferentialType struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	Name          int       `gorm:"column:name;NOT NULL"`                 // 优惠类型名称
	NameUg        string    `gorm:"column:name_ug"`
	NameZh        string    `gorm:"column:name_zh"`
	Description   int       `gorm:"column:description"` // 优惠类型说明
	DescriptionUg string    `gorm:"column:description_ug"`
	DescriptionZh string    `gorm:"column:description_zh"`
	CreatedAt     time.Time `gorm:"column:created_at"` // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at"` // 修改时间
	DeletedAt     time.Time `gorm:"column:deleted_at"` // 删除时间
}

func (m *PreferentialType) TableName() string {
	return "b_preferential_type"
}
