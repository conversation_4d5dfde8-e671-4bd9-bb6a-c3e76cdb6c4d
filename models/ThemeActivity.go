package models

import (
	"time"

	"gorm.io/gorm"
)

// ThemeActivity 主题活动管理
type ThemeActivity struct {
	ID                    int             `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	CreaterID             int             `json:"creater_id" gorm:"column:creater_id"`
	NameUg                string          `json:"name_ug" gorm:"column:name_ug"`
	NameZh                string          `json:"name_zh" gorm:"column:name_zh"`
	DescUg                string          `json:"desc_ug" gorm:"column:desc_ug"`
	DescZh                string          `json:"desc_zh" gorm:"column:desc_zh"`
	BeginTime             *time.Time      `json:"begin_time" gorm:"column:begin_time"`
	EndTime               *time.Time      `json:"end_time" gorm:"column:end_time"`
	DiscountPercent       int             `json:"discount_percent" gorm:"column:discount_percent"`
	CoverUg               string          `json:"cover_ug" gorm:"column:cover_ug"`
	CoverZh               string          `json:"cover_zh" gorm:"column:cover_zh"`
	Color                 string          `json:"color" gorm:"column:color"`
	State                 int             `json:"state" gorm:"column:state"`
	OrderType             string          `json:"order_type" gorm:"column:order_type"`
	ShowPreferential      int             `json:"show_preferential" gorm:"column:show_preferential"`
	ShowSeckill           int             `json:"show_seckill" gorm:"column:show_seckill"`
	ShowFoodType          int             `json:"show_food_type" gorm:"column:show_food_type"`
	HasKeyword            int             `json:"has_keyword" gorm:"column:has_keyword"`
	KeywordUg             string          `json:"keyword_ug" gorm:"column:keyword_ug"`
	KeywordZh             string          `json:"keyword_zh" gorm:"column:keyword_zh"`
	ExcludeKeywordUG      string         `gorm:"column:exclude_keyword_ug" json:"exclude_keyword_ug,omitempty"`               // 不包含关键字维文
	ExcludeKeywordZH      string         `gorm:"column:exclude_keyword_zh" json:"exclude_keyword_zh,omitempty"`               // 不包含关键字中文
	ShareContentUg        string          `json:"share_content_ug" gorm:"column:share_content_ug"`
	ShareContentZh        string          `json:"share_content_zh" gorm:"column:share_content_zh"`
	ShareCoverUg          string          `json:"share_cover_ug" gorm:"column:share_cover_ug"`
	ShareCoverZh          string          `json:"share_cover_zh" gorm:"column:share_cover_zh"`
	ShareCoverToFriendUg  string          `json:"share_cover_to_friend_ug" gorm:"column:share_cover_to_friend_ug"`
	ShareCoverToFriendZh  string          `json:"share_cover_to_friend_zh" gorm:"column:share_cover_to_friend_zh"`
	CreatedAt             *time.Time      `json:"created_at" gorm:"column:created_at"`
	UpdatedAt             *time.Time      `json:"updated_at" gorm:"column:updated_at"`
	DeletedAt             *gorm.DeletedAt `json:"deleted_at" gorm:"column:deleted_at"`
	Creator               *Admin          `json:"creator" gorm:"foreignKey:CreaterID"`
}

// TableName 表名
func (ThemeActivity) TableName() string {
	return "theme_activity"
}
