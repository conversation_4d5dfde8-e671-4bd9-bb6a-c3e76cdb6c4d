package models

import (
	"time"

	"gorm.io/gorm"
)

// FoodsMultipleDiscountDetail 折扣规则表
type FoodsMultipleDiscountDetail struct {
	ID              int64          `json:"id" gorm:"id"`                             // 规则ID
	ActivityId      int64          `json:"activity_id" gorm:"activity_id"`           // 关联的活动ID
	DiscountIndex   int64          `json:"discount_index" gorm:"discount_index"`     // 第几份，从2开始
	DiscountPercent float64        `json:"discount_percent" gorm:"discount_percent"` // 折扣百分比，例如80表示80%
	OriginalPrice   int64          `json:"original_price" gorm:"original_price"`     // 原价
	DiscountPrice   int64          `json:"discount_price" gorm:"discount_price"`     // 优惠后的价格
	CreatedAt       time.Time      `json:"created_at" gorm:"created_at"`             // 创建时间
	UpdatedAt       time.Time      `json:"updated_at" gorm:"updated_at"`             // 更新时间
	DeletedAt       gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"`             // 删除时间
}

// TableName 表名称
func (*FoodsMultipleDiscountDetail) TableName() string {
	return "t_foods_multiple_discount_detail"
}

// 获取转换成元的优惠价格
func (f *FoodsMultipleDiscountDetail) GetDiscountPriceYuan() float64 {
	return float64(f.DiscountPrice) / 100
}

// 获取转换成元的原价
func (f *FoodsMultipleDiscountDetail) GetOriginalPriceYuan() float64 {
	return float64(f.OriginalPrice) / 100
}
