package models

import (
	"time"

	"gorm.io/gorm"
)

// 权限分类
type MerchantPermissionCategory struct {
	ID        int            `gorm:"column:id;NOT NULL"`
	Pid       int            `gorm:"column:pid;NOT NULL"`
	Name      string         `gorm:"column:name"`
	NameUg    string         `gorm:"column:name_ug"`
	NameZh    string         `gorm:"column:name_zh"`
	CreatedAt time.Time      `gorm:"column:created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at"`
	SubPermissions []MerchantPermissionCategory `gorm:"foreignKey:pid"`
}

func (m *MerchantPermissionCategory) TableName() string {
	return "merchant_permission_category"
}
