package cms
// Code generated by http://www.gotool.top

import (
"time"
)

type TOrderStateLog struct {
	Id int32 `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL"`
	OrderId int32 `gorm:"column:order_id;NOT NULL;comment:'订单编号'"`
	OrderStateId int32 `gorm:"column:order_state_id;NOT NULL;comment:'状态编号'"`
	FailReason int32 `gorm:"column:fail_reason;default:NULL;comment:'失败原因'"`
	State int8 `gorm:"column:state;default:1"`
	CreatedAt time.Time `gorm:"column:created_at;default:NULL;comment:'创建时间'"`
	UpdatedAt time.Time `gorm:"column:updated_at;default:NULL"`
	DeletedAt time.Time `gorm:"column:deleted_at;default:NULL"`
}

func (t *TOrderStateLog) TableName() string {
	return "t_order_state_log"
}
