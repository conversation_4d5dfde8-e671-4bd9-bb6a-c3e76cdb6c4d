package cms

import "time"

type CombinePayLog struct {
	ID        int        `gorm:"column:id;primary_key;auto_increment" json:"id"`
	OrderID   int        `gorm:"column:order_id" json:"order_id"`
	UserID    int        `gorm:"column:user_id" json:"user_id"`
	PayType   int        `gorm:"column:pay_type" json:"pay_type"`
	TotalFee  int        `gorm:"column:total_fee" json:"total_fee"`
	OnlinePay int        `gorm:"column:online_pay" json:"online_pay"`
	CoinPay   int        `gorm:"column:coin_pay" json:"coin_pay"`
	Refunded  int        `gorm:"column:refunded" json:"refunded"`
	CreatedAt time.Time  `gorm:"column:created_at" json:"created_at"`
	UpdatedAt time.Time  `gorm:"column:updated_at" json:"updated_at"`
	DeletedAt *time.Time `gorm:"column:deleted_at" json:"deleted_at"`
}

func (CombinePayLog) TableName() string {
	return "t_combine_pay_log"
}
