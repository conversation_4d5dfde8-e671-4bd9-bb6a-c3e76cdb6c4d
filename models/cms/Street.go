package cms
// Code generated by sql2gorm. DO NOT EDIT.

import (
	"gorm.io/gorm"
	"mulazim-api/tools"
	"time"
)

// 道路信息表
type Street struct {
	ID        int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	AreaID    uint      `gorm:"column:area_id;NOT NULL"`              // 区域编号
	Name      int       `gorm:"column:name;NOT NULL"`                 // 街道名称
	NameUg    string    `gorm:"column:name_ug"`
	NameZh    string    `gorm:"column:name_zh"`
	PrefixUg  string    `gorm:"column:prefix_ug"`                 // 维文名称头字母
	PrefixZh  string    `gorm:"column:prefix_zh"`                 // 汉文名称头字母
	Weight    int       `gorm:"column:weight;default:0;NOT NULL"` // 街道排序
	State     int       `gorm:"column:state;default:0;NOT NULL"`  // 街道状态（0关闭，1开通）
	CreatedAt time.Time `gorm:"column:created_at"`                // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"`                // 修改时间
	DeletedAt time.Time `gorm:"column:deleted_at"`                // 删除时间
}

func (m *Street) TableName() string {
	return "b_street"
}


func StreetScoreOffUser(adminType int,adminId int) func (db *gorm.DB) *gorm.DB {
	return func (db *gorm.DB) *gorm.DB {
		if adminType==1 {
			return db
		}
		dbTemp := tools.Db
		var areaIDs []int

		dbTemp.Model(AdminAreas{}).
			Where("admin_id = ?", adminId).
			Group("area_id").
			Pluck("area_id", &areaIDs)
		return db.Where("area_id IN (?)", areaIDs)
	}
}


