package cms
// Code generated by sql2gorm. DO NOT EDIT.

import (
	"time"
)

// 用户、角色关系表
type RoleUser struct {
	AdminID   int       `gorm:"column:admin_id;primary_key"`
	RoleID    int       `gorm:"column:role_id;NOT NULL;primary_key"`
	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`
	DeletedAt time.Time `gorm:"column:deleted_at"`
}

func (m *RoleUser) TableName() string {
	return "role_user"
}

