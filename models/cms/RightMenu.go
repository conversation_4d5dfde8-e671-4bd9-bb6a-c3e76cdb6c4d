package cms
// Code generated by sql2gorm. DO NOT EDIT.

import (
"time"
)

// 后台菜单表
type Menus struct {
	ID          int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	Name        string    `gorm:"column:name"`
	NameUg      string    `gorm:"column:name_ug"`
	NameZh      string    `gorm:"column:name_zh"`
	PermID      int       `gorm:"column:perm_id"`
	ParentID    int       `gorm:"column:parent_id"`
	Weight      int       `gorm:"column:weight;default:0"`
	ActiveClass string    `gorm:"column:active_class"`
	IconClass   string    `gorm:"column:icon_class"`
	CreatedAt   time.Time `gorm:"column:created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at"`
	Child<PERSON> []Menus        `gorm:"foreignKey:parent_id;references:id"`

	Perm Permissions    `gorm:"foreignKey:id;references:perm_id"`
}

func (m *Menus) TableName() string {
	return "menus"
}

