package cms
import (
"time"
)

// 权限管理信息表
type Permissions struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	ParentID      int       `gorm:"column:parent_id"`                     // 父亲
	Name          string    `gorm:"column:name;NOT NULL"`                 // 权限名称
	DisplayName   int       `gorm:"column:display_name"`                  // 显示名称
	DisplayNameUg string    `gorm:"column:display_name_ug"`
	DisplayNameZh string    `gorm:"column:display_name_zh"`
	Description   int       `gorm:"column:description"` // 说明
	DescriptionUg string    `gorm:"column:description_ug"`
	Url   		  string    `gorm:"column:url"`
	DescriptionZh string    `gorm:"column:description_zh"`
	State         int       `gorm:"column:state;default:1"` // 显示（０不显示，１显示）
	CreatedAt     time.Time `gorm:"column:created_at"`      // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at"`      // 修改时间
	DeletedAt     time.Time `gorm:"column:deleted_at"`      // 删除时间


}


func (m *Permissions) TableName() string {
	return "permissions"
}
