package cms


import (
	"time"
)

type TRestaurantLastLogin struct {
	Id int32 `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL;comment:'自增编号'"`
	RestaurantId int32 `gorm:"column:restaurant_id;NOT NULL;comment:'餐厅编号'"`
	LastQueryTime time.Time `gorm:"column:last_query_time;default:NULL;comment:'最终查询时间'"`
	DeviceToken string `gorm:"column:device_token;default:NULL;comment:'极光推送ID'"`
	CreatedAt time.Time `gorm:"column:created_at;default:NULL;comment:'创建时间'"`
	UpdatedAt time.Time `gorm:"column:updated_at;default:NULL;comment:'修改时间'"`
	DeletedAt time.Time `gorm:"column:deleted_at;default:NULL;comment:'删除时间'"`
}

func (t *TRestaurantLastLogin) TableName() string {
	return "t_restaurant_last_login"
}