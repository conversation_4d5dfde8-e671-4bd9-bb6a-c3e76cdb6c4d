package cms
// Code generated by sql2gorm. DO NOT EDIT.

import (
	"gorm.io/gorm"
	"time"
)

// 平台提示信息表
type Notification struct {
	ID        int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	Type      int       `gorm:"column:type;NOT NULL"`                 // 位置类型（1 餐厅加盟 2，商家更改美食信息,3.商家拒绝新订单 4. 商家拒绝已接订单,5 新的评论需要审核）
	AreaID    int       `gorm:"column:area_id;NOT NULL"`
	AdminID   int       `gorm:"column:admin_id"` // 商户编号（position_type为2时有效）
	ContentUg string    `gorm:"column:content_ug;NOT NULL"`
	ContentZh string    `gorm:"column:content_zh"`
	UpdateContent string    `gorm:"column:update_content"`
	Link      string    `gorm:"column:link"`                     // 链接
	State     int       `gorm:"column:state;default:0;NOT NULL"` // 状态（0未读，1已读）
	CreatedAt time.Time `gorm:"column:created_at"`               // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"`               // 修改时间
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at"`               // 删除时间
}

func (m *Notification) TableName() string {
	return "t_notification"
}

