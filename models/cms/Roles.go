package cms
// Code generated by sql2gorm. DO NOT EDIT.

import (
"time"
)

// 角色信息表
type Roles struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	Name          string    `gorm:"column:name;NOT NULL"` // 角色名称
	ParentID      int       `gorm:"column:parent_id;default:-1;NOT NULL"`
	DisplayName   int       `gorm:"column:display_name"` // 显示名称
	DisplayNameUg string    `gorm:"column:display_name_ug"`
	DisplayNameZh string    `gorm:"column:display_name_zh"`
	Description   int       `gorm:"column:description"` // 描述
	DescriptionUg string    `gorm:"column:description_ug"`
	DescriptionZh string    `gorm:"column:description_zh"`
	AdminID       int       `gorm:"column:admin_id;default:1"`
	CreatedAt     time.Time `gorm:"column:created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at"`
	DeletedAt     time.Time `gorm:"column:deleted_at"`
	Permissions   []Permissions  `gorm:"many2many:permission_role;joinforeignKey:role_id;joinReferences:permission_id"`
}

func (m *Roles) TableName() string {
	return "roles"
}

