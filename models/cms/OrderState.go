package cms

import (
"time"
)

// 订单状态信息表
type OrderState struct {
	ID          int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	Name        int       `gorm:"column:name"`                          // 订单状态名称
	NameUg      string    `gorm:"column:name_ug"`
	NameZh      string    `gorm:"column:name_zh"`
	Color       string    `gorm:"column:color"` // 状态颜色
	CurColor    string    `gorm:"column:cur_color"`
	Icon        string    `gorm:"column:icon"`        // 状态图标
	CurIcon     string    `gorm:"column:cur_icon"`    // 当前状态图标
	Description string    `gorm:"column:description"` // 状态描述
	CreatedAt   time.Time `gorm:"column:created_at"`  // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at"`  // 修改时间
	DeletedAt   time.Time `gorm:"column:deleted_at"`  // 删除时间
}

func (m *OrderState) TableName() string {
	return "b_order_state"
}


