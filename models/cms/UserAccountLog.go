package cms

import (
	"time"
)

type UserAccountLog struct {
	ID        int        `gorm:"primary_key;auto_increment" json:"id"`
	UserID    int        `gorm:"not null" json:"user_id"`
	PayTypeID *int       `gorm:"default:null;comment:'支付方式编号（充值时有效，表示：支付宝，微信，银联，等）'" json:"pay_type_id"`
	Balance   *int       `gorm:"default:null;comment:'充值或消费前余额（单位：分）'" json:"balance"`
	Amount    int        `gorm:"not null;comment:'充值或消费金额（单位：分，充值时正数，消费时负数）'" json:"amount"`
	TradeNo   string     `gorm:"type:varchar(100);charset:utf8;comment:'付费接口返回的流水号';default:null" json:"trade_no"`
	ReasonID  *int       `gorm:"default:null" json:"reason_id"`
	OrderID   *int       `gorm:"default:null;comment:'订单号'" json:"order_id"`
	State     int        `gorm:"not null;comment:'状态（0表示非正常，1表示正常）'" json:"state"`
	CreatedAt *time.Time `gorm:"comment:'创建时间'" json:"created_at"`
	UpdatedAt *time.Time `gorm:"comment:'修改时间';default:null" json:"updated_at"`
	DeletedAt *time.Time `gorm:"comment:'删除时间';default:null" json:"deleted_at"`
}

func (UserAccountLog) TableName() string {
	return "t_user_account_log"
}
