package cms
// Code generated by sql2gorm. DO NOT EDIT.

import (
"time"
)

// 角色和权限关系表
type PermissionRole struct {
	PermissionID int       `gorm:"column:permission_id;primary_key"` // 权限编号
	RoleID       int       `gorm:"column:role_id;NOT NULL"`          // 角色编号
	CreatedAt    time.Time `gorm:"column:created_at"`                // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at"`                // 修改时间
	DeletedAt    time.Time `gorm:"column:deleted_at"`                // 删除时间
}

func (m *PermissionRole) TableName() string {
	return "permission_role"
}

