// Code generated by sql2gorm. DO NOT EDIT.
package models

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"mulazim-api/models/cms"
	"mulazim-api/models/shipment"
	"mulazim-api/tools"
	"strconv"
	"strings"
	"time"
)

// 管理员信息表
type AdminBase struct {
	ID                        int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	Type                      int       `gorm:"column:type;NOT NULL"`                 // 管理员类型（OWNER:1,ADMIN:2,DEALER:3,DEALER_SUB:4,RESTAURANT_ADMIN:5,RESTAURANT_ADMIN_SUB:6,CENTURION:7,SHIPPER_ADMIN:8,SHIPPER:9）
	MerchantType              int       `gorm:"column:merchant_type;"`        // 1:老板，2:店铺管理员，3:财务管理员，4:订单管理员，5：客服
	Level                     int       `gorm:"column:level"`                         // 代理商级别：1市级,2区级
	ParentID                  string    `gorm:"column:parent_id"`                     // 父管理员编号
	Avatar                    string    `gorm:"column:avatar"`                        // 用户头像
	Mobile                    string    `gorm:"column:mobile;NOT NULL"`               // 联系电话
	Name                      string    `gorm:"column:name;NOT NULL"`                 // 管理员账号
	Password                  string    `gorm:"column:password;NOT NULL"`             // 管理员密码
	RealName                  string    `gorm:"column:real_name"`                     // 管理员实名
	Description               string    `gorm:"column:description"`                   // 描述
	RememberToken             string    `gorm:"column:remember_token"`
	AuthToken                 string    `gorm:"column:auth_token"`                                // 验证token
	SearialNumber             string    `gorm:"column:searial_number"`                            // 序列号
	Lat                       float64   `gorm:"column:lat"`                                       // 维度
	Lng                       float64   `gorm:"column:lng"`                                       // 经度
	Accuracy                  float64   `gorm:"column:accuracy"`                                  // 经纬度准确度
	Balance                   int       `gorm:"column:balance;default:0"`                         // 代理余额
	GrabOrderCount            int       `gorm:"column:grab_order_count;default:3"`                // 配送员最多能抢的订单数
	Openid                    string    `gorm:"column:openid"`                                    // 用户微信小程序openId
	State                     int       `gorm:"column:state"`                                     // 状态（0关闭，1开通）
	AttendanceState           int       `gorm:"column:attendance_state"`                          // 状态（0关闭，1开通）
	LoginTime                 time.Time `gorm:"column:login_time"`                                // 登录时间
	ShipperDistance           int       `gorm:"column:shipper_distance;default:0"`                // 配送员走的总距离
	ShipperOnTimeDeliveryRate int       `gorm:"column:shipper_on_time_delivery_rate;default:100"` // 配送员准时送达率
	ShipperCustomerRate       int       `gorm:"column:shipper_customer_rate;default:100"`         // 配送员客户满意率
	ShipperDeliveryAvgTime    int       `gorm:"column:shipper_delivery_avg_time;default:15"`      // 配送员平均送达时间
	LastCommentReaded         time.Time `gorm:"column:last_comment_readed"`                       // 最后读取评论的时间
	TakeCashOrder             uint      `gorm:"column:take_cash_order;default:1"`                 // 表示配送员是否抢现金订单（0表示不能抢，1表示能抢）
	BackOrderTimeLimit        int       `gorm:"column:back_order_time_limit;default:30"`          // 退单时间限制（单位：分钟）
	RecommendQrcode           string    `gorm:"column:recommend_qrcode"`                          // 配送员发展新客户的小程序二维码
	CreatedAt                 time.Time `gorm:"column:created_at"`                                // 创建时间
	UpdatedAt                 time.Time `gorm:"column:updated_at"`                                // 修改时间
	DeletedAt                 time.Time `gorm:"column:deleted_at"`                                // 删除时间
	AdminCityID               int       `gorm:"column:admin_city_id"`
	AdminAreaID               int       `gorm:"column:admin_area_id"`
	City                      cms.City  `gorm:"foreignKey:admin_city_id;references:id"` // 地区
	Area                      Area      `gorm:"foreignKey:admin_area_id;references:id"` // 区域
	// TemplateUpdate            shipment.AdminTemplateUpdate   `gorm:"foreignKey:id;references:admin_id"`                                     `gorm:"foreignKey:admin_area_id;references:id"` // 区域
	ShipperIncomeTemplateID int                            `gorm:"column:shipper_income_template_id"`
	ShipperIncomeTemplate   shipment.ShipperIncomeTemplate `gorm:"foreignKey:shipper_income_template_id;references:id"` // 配送费计算模板
	Restaurants             []Restaurant                   `gorm:"many2many:b_admin_store;joinReferences:store_id" json:"restaurants"`

	Roles []cms.Roles `gorm:"many2many:role_user;joinforeignKey:admin_id;joinReferences:role_id"`
	Areas []Area      `gorm:"many2many:admin_areas;joinforeignKey:admin_id;joinReferences:area_id"`

	ShipperAttendance []ShipperAttendanceLog `gorm:"foreignKey:shipper_id;references:id"`       // 地区
	SelfSignInfo      SelfSignMerchantInfo   `gorm:"foreignKey:restaurant_id;references:id"`    // 地区
	PushDevices       []PushDevice           `gorm:"polymorphic:User;polymorphicValue:t_admin"` // 激光设备

	TodayOrders         []OrderToday                 `gorm:"foreignKey:shipper_id;references:id"` // 地区
	AdminTemplateUpdate shipment.AdminTemplateUpdate `gorm:"foreignKey:shipper_id;references:id"` // 地区
	ShipperRank ShipperRank `gorm:"foreignKey:shipper_id;references:id"`
}
type Admin struct {
	ID                        int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	Type                      int       `gorm:"column:type;NOT NULL"`                 // 管理员类型（OWNER:1,ADMIN:2,DEALER:3,DEALER_SUB:4,RESTAURANT_ADMIN:5,RESTAURANT_ADMIN_SUB:6,CENTURION:7,SHIPPER_ADMIN:8,SHIPPER:9）
	MerchantType              int       `gorm:"column:merchant_type;"`        // 1:老板，2:店铺管理员，3:财务管理员，4:订单管理员，5：客服
	Level                     int       `gorm:"column:level"`                         // 代理商级别：1市级,2区级
	ParentID                  string    `gorm:"column:parent_id"`                     // 父管理员编号
	Avatar                    string    `gorm:"column:avatar"`                        // 用户头像
	Mobile                    string    `gorm:"column:mobile;NOT NULL"`               // 联系电话
	Name                      string    `gorm:"column:name;NOT NULL"`                 // 管理员账号
	Password                  string    `gorm:"column:password;NOT NULL"`             // 管理员密码
	RealName                  string    `gorm:"column:real_name"`                     // 管理员实名
	Description               string    `gorm:"column:description"`                   // 描述
	RememberToken             string    `gorm:"column:remember_token"`
	AuthToken                 string    `gorm:"column:auth_token"`                                // 验证token
	SearialNumber             string    `gorm:"column:searial_number"`                            // 序列号
	Lat                       float64   `gorm:"column:lat"`                                       // 维度
	Lng                       float64   `gorm:"column:lng"`                                       // 经度
	Speed                     float64   `gorm:"-"`                     							  // 速度
	Accuracy                  float64   `gorm:"column:accuracy"`                                  // 经纬度准确度
	Balance                   int       `gorm:"column:balance;default:0"`                         // 代理余额
	GrabOrderCount            int       `gorm:"column:grab_order_count;default:3"`                // 配送员最多能抢的订单数
	AutoDispatchRankOrderCount            int       `gorm:"column:auto_dispatch_rank_order_count;default:3"`      
	AutoDispatchRank            int       `gorm:"column:auto_dispatch_rank;default:2"`      
	AutoDispatchFinalScore    float64       `gorm:"column:auto_dispatch_final_score;default:60"`                // 
	Openid                    string    `gorm:"column:openid"`                                    // 用户微信小程序openId
	State                     int       `gorm:"column:state"`                                     // 状态（0关闭，1开通）
	AttendanceState           int       `gorm:"column:attendance_state"`                          // 状态（0关闭，1开通）
	LoginTime                 time.Time `gorm:"column:login_time"`                                // 登录时间
	ShipperDistance           int       `gorm:"column:shipper_distance;default:0"`                // 配送员走的总距离
	ShipperOnTimeDeliveryRate int       `gorm:"column:shipper_on_time_delivery_rate;default:100"` // 配送员准时送达率
	ShipperCustomerRate       int       `gorm:"column:shipper_customer_rate;default:100"`         // 配送员客户满意率
	ShipperDeliveryAvgTime    int       `gorm:"column:shipper_delivery_avg_time;default:15"`      // 配送员平均送达时间
	LastCommentReaded         time.Time `gorm:"column:last_comment_readed"`                       // 最后读取评论的时间
	TakeCashOrder             uint      `gorm:"column:take_cash_order;default:1"`                 // 表示配送员是否抢现金订单（0表示不能抢，1表示能抢）
	BackOrderTimeLimit        int       `gorm:"column:back_order_time_limit;default:30"`          // 退单时间限制（单位：分钟）
	RecommendQrcode           string    `gorm:"column:recommend_qrcode"`                          // 配送员发展新客户的小程序二维码
	CreatedAt                 time.Time `gorm:"column:created_at"`                                // 创建时间
	UpdatedAt                 time.Time `gorm:"column:updated_at"`                                // 修改时间
	DeletedAt                 time.Time `gorm:"column:deleted_at"`                                // 删除时间
	AreaName                  string    `gorm:"column:area_name"`
	AdminCityID               int       `gorm:"column:admin_city_id"`
	AdminAreaID               int       `gorm:"column:admin_area_id"`

	Age                   int       `gorm:"column:age;"`                         // 年龄
	Sex                   int       `gorm:"column:sex;"`                         // 性别
	IDCard                 string    `gorm:"column:idcard"`                     // 身份账号

	JwtSerialNumber    int        `gorm:"column:jwt_serial_number"`                     // jwt_serial_number

	City                      cms.City  `gorm:"foreignKey:admin_city_id;references:id"` // 地区
	Area                      Area      `gorm:"foreignKey:admin_area_id;references:id"` // 区域
	// TemplateUpdate            shipment.AdminTemplateUpdate   `gorm:"foreignKey:id;references:admin_id"`                                     `gorm:"foreignKey:admin_area_id;references:id"` // 区域
	ShipperIncomeTemplateID int                            `gorm:"column:shipper_income_template_id"`
	ShipperIncomeTemplate   shipment.ShipperIncomeTemplate `gorm:"foreignKey:shipper_income_template_id;references:id"` // 配送费计算模板
	Restaurants             []Restaurant                   `gorm:"many2many:b_admin_store;joinReferences:store_id" json:"restaurants"`

	Roles []cms.Roles `gorm:"many2many:role_user;joinforeignKey:admin_id;joinReferences:role_id"`
	Areas []Area      `gorm:"many2many:admin_areas;joinforeignKey:admin_id;joinReferences:area_id"`

	ShipperAttendance []ShipperAttendanceLog `gorm:"foreignKey:shipper_id;references:id"`       // 地区
	SelfSignInfo      SelfSignMerchantInfo   `gorm:"foreignKey:restaurant_id;references:id"`    // 地区
	PushDevices       []PushDevice           `gorm:"polymorphic:User;polymorphicValue:t_admin"` // 激光设备

	TodayOrders []OrderToday `gorm:"foreignKey:shipper_id;references:id"` // 地区
	OauthSessions OauthSessions `gorm:"foreignKey:owner_id;references:id"` //session
	OauthSessionsInt OauthSessions `gorm:"foreignKey:owner_id;references:str_id"` //session
	StrId string `gorm:"column:str_id;->"` // 只读字段：可以从数据库读取，但写入时会被忽略
	GroupId int `gorm:"column:group_id;default:null"`                                                         //PK区域编号（1表示和田地区、2表示喀什地区、3表示第三区、4表示第四区）
	TeamId int `gorm:"column:team_id;default:null"`                                                         //PK区域编号（1表示和田地区、2表示喀什地区、3表示第三区、4表示第四区）
	IsCaptain int `gorm:"column:is_captain;default:null"`													// 是否队长

	MerchantPermissions []MerchantPermission `gorm:"foreignKey:v0;references:id"`       // 商家权限
	
	IsInsuranceBlack int `gorm:"column:is_insurance_black;default:0"` //是不是 保险黑名单 

	PushDeviceRestaurant       PushDevice          `gorm:"foreignKey:admin_id;references:id"` // 商家设备
	ShipperRank ShipperRank `gorm:"foreignKey:shipper_id;references:id"`
}



// 管理员信息表 关联 实名认证使用
type AdminForSelfSign struct {
	ID                        int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	Type                      int       `gorm:"column:type;NOT NULL"`                 // 管理员类型（OWNER:1,ADMIN:2,DEALER:3,DEALER_SUB:4,RESTAURANT_ADMIN:5,RESTAURANT_ADMIN_SUB:6,CENTURION:7,SHIPPER_ADMIN:8,SHIPPER:9）
	MerchantType              int       `gorm:"column:merchant_type;"`        // 1:老板，2:店铺管理员，3:财务管理员，4:订单管理员，5：客服
	Level                     int       `gorm:"column:level"`                         // 代理商级别：1市级,2区级
	ParentID                  string    `gorm:"column:parent_id"`                     // 父管理员编号
	Avatar                    string    `gorm:"column:avatar"`                        // 用户头像
	Mobile                    string    `gorm:"column:mobile;NOT NULL"`               // 联系电话
	Name                      string    `gorm:"column:name;NOT NULL"`                 // 管理员账号
	Password                  string    `gorm:"column:password;NOT NULL"`             // 管理员密码
	RealName                  string    `gorm:"column:real_name"`                     // 管理员实名
	Description               string    `gorm:"column:description"`                   // 描述
	RememberToken             string    `gorm:"column:remember_token"`
	AuthToken                 string    `gorm:"column:auth_token"`                                // 验证token
	SearialNumber             string    `gorm:"column:searial_number"`                            // 序列号
	Lat                       float64   `gorm:"column:lat"`                                       // 维度
	Lng                       float64   `gorm:"column:lng"`                                       // 经度
	Accuracy                  float64   `gorm:"column:accuracy"`                                  // 经纬度准确度
	Balance                   int       `gorm:"column:balance;default:0"`                         // 代理余额
	GrabOrderCount            int       `gorm:"column:grab_order_count;default:3"`                // 配送员最多能抢的订单数
	Openid                    string    `gorm:"column:openid"`                                    // 用户微信小程序openId
	State                     int       `gorm:"column:state"`                                     // 状态（0关闭，1开通）
	AttendanceState           int       `gorm:"column:attendance_state"`                          // 状态（0关闭，1开通）
	LoginTime                 time.Time `gorm:"column:login_time"`                                // 登录时间
	ShipperDistance           int       `gorm:"column:shipper_distance;default:0"`                // 配送员走的总距离
	ShipperOnTimeDeliveryRate int       `gorm:"column:shipper_on_time_delivery_rate;default:100"` // 配送员准时送达率
	ShipperCustomerRate       int       `gorm:"column:shipper_customer_rate;default:100"`         // 配送员客户满意率
	ShipperDeliveryAvgTime    int       `gorm:"column:shipper_delivery_avg_time;default:15"`      // 配送员平均送达时间
	LastCommentReaded         time.Time `gorm:"column:last_comment_readed"`                       // 最后读取评论的时间
	TakeCashOrder             uint      `gorm:"column:take_cash_order;default:1"`                 // 表示配送员是否抢现金订单（0表示不能抢，1表示能抢）
	BackOrderTimeLimit        int       `gorm:"column:back_order_time_limit;default:30"`          // 退单时间限制（单位：分钟）
	RecommendQrcode           string    `gorm:"column:recommend_qrcode"`                          // 配送员发展新客户的小程序二维码
	CreatedAt                 time.Time `gorm:"column:created_at"`                                // 创建时间
	UpdatedAt                 time.Time `gorm:"column:updated_at"`                                // 修改时间
	DeletedAt                 *time.Time `gorm:"column:deleted_at"`                                // 删除时间
	AdminCityID               int       `gorm:"column:admin_city_id"`
	AdminAreaID               int       `gorm:"column:admin_area_id"`


}

const (
	AdminStateOk = 1
)
const (
	AdminTypeOwner              = 1
	AdminTypeAdmin              = 2
	AdminTypeDealer             = 3
	AdminTypeDealerSub          = 4
	AdminTypeRestaurantAdmin    = 5
	AdminTypeRestaurantAdminSub = 6
	AdminTypeShipperAdmin       = 8
	AdminTypeShipper            = 9
)

func (m *AdminBase) TableName() string {
	return "t_admin"
}

func (m *Admin) TableName() string {
	return "t_admin"
}

func (m *Admin) getResIdsKey() string {
	return "admin_api_rests_" + strconv.Itoa(m.ID)
}

func (m *AdminForSelfSign) TableName() string {
	return "t_admin"
}

// GetRestaurantIds
//
//	@Description: 获取分配管理员（配送员）的餐厅编号
//	@author: Captain
//	@Time: 2022-09-09 20:07:13
//	@receiver m *Admin
//	@param c *gin.Context
//	@param fromDB bool true时从数据库查询并把结果写入到Redis缓存，false时直接从Redis缓存查找（如果Redis里没有，则从数据库中查找并写入Redis）
//	@return []int
func (m *Admin) GetRestaurantIds(c *gin.Context, fromDB bool) []int {
	redisHelper := tools.GetRedisHelper()
	var key string = m.getResIdsKey()
	var restaurantIds []int
	tools.RedisDel(c, key)
	if fromDB {
		db := tools.Db
		db.Table("b_admin_store").Select("store_id").Where("admin_id = ?", m.ID).Find(&restaurantIds)
		for i := 0; i < len(restaurantIds); i++ {
			redisHelper.SAdd(c, key, restaurantIds[i])
		}
		redisHelper.Expire(c, key, time.Second*60*5)
		return restaurantIds
	} else {
		if exists, _ := redisHelper.Exists(c, key).Result(); exists == 1 {
			strArr, _ := redisHelper.SMembers(c, key).Result()
			restaurantIds = m.StringArray2IntArray(strArr)
		} else {
			db := tools.Db
			db.Table("b_admin_store").Select("store_id").Where("admin_id = ?", m.ID).Find(&restaurantIds)
			for i := 0; i < len(restaurantIds); i++ {
				redisHelper.SAdd(c, key, restaurantIds[i])
			}
			redisHelper.Expire(c, key, time.Second*60*5)
		}
		return restaurantIds
	}
}

// GetAdminRestaurant 获取商家管理员商家信息
func (m *Admin) GetAdminRestaurant() Restaurant {
	var restaurant Restaurant = Restaurant{}
	if !m.IsRestaurantAdmin() && !m.IsRestaurantAdminSub() {
		return restaurant
	}
	db := tools.Db
	rs := db.Model(Restaurant{}).
		Joins("join b_admin_store on b_admin_store.store_id = t_restaurant.id and admin_id = ? and t_restaurant.deleted_at is null", m.ID).
		First(&restaurant)
	if rs.RowsAffected < 0 {
		msg := fmt.Sprintf("查询商家管理员商家信息失败,用户ID: %d, 错误信息：%s", m.ID, rs.Error.Error())
		tools.Logger.Error(msg)
	}
	return restaurant
}

// StringArray2IntArray
//
//	@Description: 字符串数组转换成整型数组
//	@author: Captain
//	@Time: 2022-09-09 20:06:32
//	@receiver m *Admin
//	@param strArr []string
//	@return []int
func (m *Admin) StringArray2IntArray(strArr []string) []int {
	res := make([]int, len(strArr))
	for index, val := range strArr {
		res[index], _ = strconv.Atoi(val)
	}
	return res
}

func (m *Admin) GetParentId() string {
	var pID string
	if m.Type == 1 {
		if m.ID == 1 {
			pID = fmt.Sprintf("%s,4796,", strings.Trim(fmt.Sprintf("%d", m.ID), ","))
		} else {
			pID = fmt.Sprintf("1,%s,", strings.Trim(fmt.Sprintf("%d", m.ID), ","))
		}
	} else {
		pID = fmt.Sprintf("%s,%s,", strings.Trim(m.ParentID, ","), strings.Trim(fmt.Sprintf("%d", m.ID), ","))
	}

	return strings.TrimLeft(pID, ",")
}

func (m *Admin) HasResId(c *gin.Context, id int) bool {
	if m.Type == AdminTypeOwner {
		return true
	}
	ids := m.GetRestaurantIds(c, false)
	return tools.InArrayAll(id, ids)
}

func (m *Admin) IsOwner() bool {
	return m.Type == AdminTypeOwner
}

func (m *Admin) IsAdmin() bool {
	return m.Type == AdminTypeAdmin
}

func (m *Admin) IsDealer() bool {
	return m.Type == AdminTypeDealer
}

func (m *Admin) IsDealerSub() bool {
	return m.Type == AdminTypeDealerSub
}

func (m *Admin) IsRestaurantAdmin() bool {
	return m.Type == AdminTypeRestaurantAdmin
}

func (m *Admin) IsRestaurantAdminSub() bool {
	return m.Type == AdminTypeRestaurantAdminSub
}

func (m *Admin) IsShipper() bool {
	return m.Type == AdminTypeShipper
}
