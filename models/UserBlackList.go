package models

import (
	 "gorm.io/gorm"
	 "time"
)

// TUserBlacklist defines the structure for the t_user_blacklist table with GORM column definitions.
type UserBlacklist struct {
	ID            int            `gorm:"column:id;primaryKey;autoIncrement"` // 主键ID
	UserID        int            `gorm:"column:user_id"`                      // 用户编号
	Count         uint           `gorm:"column:count"`                        // 次数（用户进入黑名单的次数）
	OnlinePayCount uint           `gorm:"column:online_pay_count"`             // 用户的需要在线支付的次数（每次成功在线支付时要减一次，次数等于0时出黑名单并修改状态）
	State         bool           `gorm:"column:state"`                        // 状态（0表示进入黑名单，1表示出黑名单），这里使用bool类型更符合逻辑
	CreatedAt     *time.Time     `gorm:"column:created_at"`                   // 进入黑名单时间
	UpdatedAt     *time.Time     `gorm:"column:updated_at;autoUpdateTime"`    // 修改时间，使用GORM的自动更新时间特性
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at"`                   // 删除时间，使用GORM的软删除特性
}


func (m *UserBlacklist) TableName() string {
	return "t_user_blacklist"
}