package models

import (
	"mulazim-api/tools"
	"time"

	"gorm.io/gorm"
)

// 地市信息表
type City struct {
	ID        int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	Name      int       `gorm:"column:name;NOT NULL"`                 // 城市名称
	NameUg    string    `gorm:"column:name_ug"`
	NameZh    string    `gorm:"column:name_zh"`
	Weight    int       `gorm:"column:weight;default:0;NOT NULL"` // 排序
	State     int       `gorm:"column:state;default:0;NOT NULL"`  // 状态（0关闭，1开通，2休息）
	CreatedAt time.Time `gorm:"column:created_at"`                // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"`                // 修改时间
	DeletedAt time.Time `gorm:"column:deleted_at"`                // 删除时间
	Areas     []Area    `gorm:"foreignKey:CityID;references:ID"`
}

func (m *City) TableName() string {
	return "b_city"
}

// CityScopeOffUser
//
//	@Description:
//	@author: Alimjan
//	@Time: 2023-06-06 17:25:04
//	@param adminType int
//	@param adminId int
//	@return func (db *gorm.DB) *gorm.DB
func CityScopeOffUser(adminType int, adminId int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if adminType == 1 {
			return db
		}
		dbTemp := tools.Db
		var cityIDs []int

		dbTemp.Model(AdminAreas{}).
			Where("admin_id = ?", adminId).
			Group("city_id").
			Pluck("city_id", &cityIDs)
		return db.Where("id IN (?)", cityIDs)
	}
}
