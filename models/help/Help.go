package help

import "database/sql"

type THelp struct {
	Id                int          `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	Type              int          `gorm:"column:type;NOT NULL"`
	Title             int          `gorm:"column:title;NOT NULL"`
	TitleUg           string       `gorm:"column:title_ug"`
	TitleZh           string       `gorm:"column:title_zh"`
	Content           int          `gorm:"column:content;NOT NULL"`
	ContentUg         string       `gorm:"column:content_ug"`
	ContentZh         string       `gorm:"column:content_zh"`
	UnderstandCount   int          `gorm:"column:understand_count;default:0"`
	NoUnderstandCount int          `gorm:"column:no_understand_count;default:0"`
	State             int          `gorm:"column:state;NOT NULL"`
	CreatedAt         sql.NullTime `gorm:"column:created_at"`
	UpdatedAt         sql.NullTime `gorm:"column:updated_at"`
	DeletedAt         sql.NullTime `gorm:"column:deleted_at"`
}

func (m *THelp) TableName() string {
	return "t_help"
}

type HelpList struct {
	ID    int    `json:"id"`
	Title string `json:"title"`
}

type HelpDetail struct {
	ID      int    `json:"id"`
	Title   string `json:"title"`
	Content string `json:"content"`
}
