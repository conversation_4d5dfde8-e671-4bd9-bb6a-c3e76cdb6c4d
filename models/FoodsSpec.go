package models

import (
	"mulazim-api/resources"
	"mulazim-api/tools"
	"time"
)

const (
	FoodSpecOptionItemStateOn  = 1
	FoodSpecOptionItemStateOff = 0
)

// FoodSpec 定义美食规格模型
type FoodSpec struct {
	ID           int            `gorm:"column:id;primaryKey;autoIncrement" json:"id"`       // 主键ID
	RestaurantID int            `gorm:"column:restaurant_id;not null" json:"restaurant_id"` // 餐厅编号
	FoodID       int            `gorm:"column:food_id;not null" json:"food_id"`             // 美食编号
	Price       int            `gorm:"column:price;not null" json:"price"`             // 美食编号
	CreatedAt    time.Time      `gorm:"column:created_at" json:"created_at,omitempty"`      // 创建时间
	UpdatedAt    time.Time      `gorm:"column:updated_at" json:"updated_at,omitempty"`      // 更新时间
	IsDeleted    int            `gorm:"column:is_deleted;default:0;not null" json:"is_deleted"` // 是否删除（0：未删除；1：已删除）

	FoodSpecOptions []FoodSpecOption `gorm:"many2many:t_foods_spec_detail;References:ID;joinReferences:foods_spec_option_id;joinForeignKey:spec_id;foreignKey:ID" json:"food_spec_options"` // 分类下面的美食列表
}

// TableName 设置表名
func (FoodSpec) TableName() string {
	return "t_foods_spec" // 数据库表名
}

// FoodSpecDetail 定义美食规格详情模型
type FoodSpecDetail struct {
	ID                int            `gorm:"column:id;primaryKey;autoIncrement" json:"id"`                     // 主键ID
	RestaurantID      int            `gorm:"column:restaurant_id;not null" json:"restaurant_id"`               // 餐厅编号
	FoodID            int            `gorm:"column:food_id;not null" json:"food_id"`                           // 美食编号
	SpecID            int            `gorm:"column:spec_id;not null" json:"spec_id"`                           // 规格编号
	FoodsSpecOptionID int            `gorm:"column:foods_spec_option_id;not null" json:"foods_spec_option_id"` // 规格项编号
	CreatedAt         time.Time      `gorm:"column:created_at" json:"created_at,omitempty"`                    // 创建时间
	UpdatedAt         time.Time      `gorm:"column:updated_at" json:"updated_at,omitempty"`                    // 更新时间
	IsDeleted    int            `gorm:"column:is_deleted;default:0;not null" json:"is_deleted"` // 是否删除（0：未删除；1：已删除）
}

// TableName 设置表名
func (FoodSpecDetail) TableName() string {
	return "t_foods_spec_detail" // 数据库表名
}

// FoodSpecOption 定义美食规格选项模型
type FoodSpecOption struct {
	ID           int            `gorm:"column:id;primaryKey;autoIncrement" json:"id"`             // 主键ID
	RestaurantID int            `gorm:"column:restaurant_id;not null" json:"restaurant_id"`       // 餐厅编号
	FoodID       int            `gorm:"column:food_id;not null" json:"food_id"`                   // 美食编号
	SpecTypeID   int            `gorm:"column:spec_type_id;not null" json:"spec_type_id"`         // 规格组编号
	NameUg       string         `gorm:"column:name_ug;size:255;not null" json:"name_ug"`          // 维吾尔文名称
	NameZh       string         `gorm:"column:name_zh;size:255;not null" json:"name_zh"`          // 中文名称
	IsSelected   int            `gorm:"column:is_selected;default:0;not null" json:"is_selected"` // 已选状态（0：未选；1：已选）
	Price        int            `gorm:"column:price" json:"price,omitempty"`                      // 价格（可为空）
	State uint8 `gorm:"column:state" json:"state,omitempty"`                                      // 是否可选状态 0: 不可选 1: 可选
	Sort  int   `gorm:"column:sort" json:"sort,omitempty"`                                        // 排序 - 正序
	CreatedAt    time.Time      `gorm:"column:created_at" json:"created_at,omitempty"`            // 创建时间
	UpdatedAt    time.Time      `gorm:"column:updated_at" json:"updated_at,omitempty"`            // 更新时间
	IsDeleted    int            `gorm:"column:is_deleted;default:0;not null" json:"is_deleted"` // 是否删除（0：未删除；1：已删除）

	FoodSpecType FoodSpecType `gorm:"foreignKey:SpecTypeID;references:ID"`
}

// TableName 设置表名
func (FoodSpecOption) TableName() string {
	return "t_foods_spec_option" // 数据库表名
}

// FoodSpecType 定义美食规格类型模型
type FoodSpecType struct {
	ID           int            `gorm:"column:id;primaryKey;autoIncrement" json:"id"`       // 主键ID
	PriceType    int          `gorm:"column:price_type;default:0;not null" json:"price_type"`   // 价格类型（0：浮动类型；1：覆盖类型）
	RestaurantID int            `gorm:"column:restaurant_id;not null" json:"restaurant_id"` // 餐厅编号
	FoodID       int            `gorm:"column:food_id;not null" json:"food_id"`             // 美食编号
	NameUg       string         `gorm:"column:name_ug;size:255" json:"name_ug,omitempty"`   // 维吾尔文名称
	NameZh       string         `gorm:"column:name_zh;size:255" json:"name_zh,omitempty"`   // 中文名称
	State uint8 `gorm:"column:state;default:1;not null" json:"state"` // 是否可选状态 0: 不可选 1: 可选
	CreatedAt    time.Time      `gorm:"column:created_at" json:"created_at,omitempty"`      // 创建时间
	UpdatedAt    time.Time      `gorm:"column:updated_at" json:"updated_at,omitempty"`      // 更新时间
	IsDeleted    int            `gorm:"column:is_deleted;default:0;not null" json:"is_deleted"` // 是否删除（0：未删除；1：已删除）
	FoodSpecOptions []FoodSpecOption `gorm:"foreignKey:SpecTypeID;references:ID"`
}

// TableName 设置表名
func (FoodSpecType) TableName() string {
	return "t_foods_spec_type" // 数据库表名
}

// 获取绑定的规格项
func (f *FoodSpec) GetOptions(spec FoodSpec,language string) []resources.FoodSpecOption {
	specOptions := make([]resources.FoodSpecOption, 0)
	for _, option := range spec.FoodSpecOptions {
		specOption := resources.FoodSpecOption{
			ID:    option.ID,
			Name:  tools.If(language == "ug", option.NameUg, option.NameZh),
			NameUg:  option.NameUg,
			NameZh:  option.NameZh,
			Price: option.Price,
			SpecTypeID: option.SpecTypeID,
		}
		specOptions = append(specOptions, specOption)
	}
	return specOptions
}
