// Code generated by sql2gorm. DO NOT EDIT.
package models

import (
	"time"
)

// db.Set("gorm:table_options", "ENGINE=InnoDB COMMENT='每个订单的当前推送情况'").AutoMigrate(&models.ShipperOrderPushBase{})
const (
	StatePushing = 1
	StateEnd     = 2
)

// 区县信息表
type ShipperOrderPushBase struct {
	ID            int64                    `gorm:"column:id;COMMENT:自增ID;primary_key;AUTO_INCREMENT"`                                          // 自增编号
	CityID        int64                    `gorm:"column:city_id;COMMENT:城市ID;index"`                                                          // 自增编号
	AreaID        int64                    `gorm:"column:area_id;COMMENT:区县ID;index"`                                                          // 自增编号
	OrderID       int64                    `gorm:"column:order_id;NOT NULL;COMMENT:订单编号;index"`                                                // 订单编号
	StoreID       int64                    `gorm:"column:store_id;NOT NULL;COMMENT:餐厅编号;index"`                                                // 订单编号
	StartPushTime time.Time                `gorm:"column:start_push_time;NOT NULL;COMMENT:开始推送时间;index"`                                       // 开始推送时间
	NextPushTime  time.Time                `gorm:"column:next_push_time;NOT NULL;COMMENT:下次推送时间;index;index:idx_state_next,priority:2"`        // 开始推送时间
	CurrentLevel  int8                     `gorm:"column:current_level;default:1;NOT NULL;COMMENT:当前推送级别;index"`                               // 当前推送级别
	State         int8                     `gorm:"column:state;default:1;NOT NULL;COMMENT:1：正在推送，2推送结束;index;index:idx_state_next,priority:1"` // 当前推送级别 1：正在推送，2推送结束
	CreatedAt     time.Time                `gorm:"column:created_at;COMMENT:创建时间;index"`                                                       // 创建时间
	UpdatedAt     time.Time                `gorm:"column:updated_at;COMMENT:修改时间"`                                                             // 修改时间
	DeletedAt     *time.Time               `gorm:"column:deleted_at;COMMENT:删除时间"`                                                             // 删除时间
	Details       []ShipperOrderPushDetail `gorm:"foreignKey:base_id;references:id"`
	Order         OrderToday               `gorm:"foreignKey:id;references:order_id"` // 推送详情
}

func (m *ShipperOrderPushBase) TableName() string {
	return "t_shipper_order_push_base"
}
