﻿package models

import (
	"time"
)

const PriceMarkupFoodLogStateNotPaid = 1
const PriceMarkupFoodLogStateHavePaid = 2
const PriceMarkupFoodLogStateComplete = 3
const PriceMarkupFoodLogStateRefund = 4

const PriceMarkupFoodLogActivityTypeSecKill = 1
const PriceMarkupFoodLogActivityTypeSpecialPrice = 2
const PriceMarkupFoodLogActivityTypePreferential = 3

type PriceMarkupFoodLog struct {
	ID                int        `json:"id" gorm:"id"`                                     // 自增编号
	CityId            int        `json:"city_id" gorm:"city_id"`                           // 城市编号
	AreaId            int        `json:"area_id" gorm:"area_id"`                           // 区域编号
	PriceMarkupId     int        `json:"price_markup_id" gorm:"price_markup_id"`           // 加价活动ID
	ActivityType      int        `json:"activity_type" gorm:"activity_type"`               // 活动类型:1:秒杀，2:特价，3:优惠
	ActivityId        int        `json:"activity_id" gorm:"activity_id"`                   // 活动ID
	SeckillPriceLogID int        `json:"seckill_price_log_id" gorm:"seckill_price_log_id"` // 秒杀活动价格阶梯ID
	OrderId           int        `json:"order_id" gorm:"order_id"`                         // 订单ID
	RestaurantId      int        `json:"restaurant_id" gorm:"restaurant_id"`               // 餐厅编号
	FoodId            int        `json:"food_id" gorm:"food_id"`                           // 餐厅美食编号
	InPrice           int        `json:"in_price" gorm:"in_price"`                         // 进价
	Price             int        `json:"price" gorm:"price"`                               // 销售价格
	SaledCount        int        `json:"saled_count" gorm:"saled_count"`                   // 销售数量
	State             int        `json:"state" gorm:"state"`                               // 1:完成，2:退单
	CreatedAt         time.Time  `json:"created_at" gorm:"created_at"`                     // 创建时间
	UpdatedAt         *time.Time `json:"updated_at" gorm:"updated_at"`                     // 修改时间

	PriceMarkupFoodSaledTotalCount int64 `gorm:"price_markup_food_saled_total_count"` // 关联  t_price_markup_food_log 获取 sailed_count 的 总和

}

func (m *PriceMarkupFoodLog) TableName() string {
	return "t_price_markup_food_log"
}
