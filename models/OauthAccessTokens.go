package models

import (
"time"
)

type OauthAccessTokens struct {
	ID         string    `gorm:"column:id;primary_key"`
	SessionID  int       `gorm:"column:session_id;NOT NULL"`
	ExpireTime int       `gorm:"column:expire_time;NOT NULL"`
	CreatedAt  time.Time `gorm:"column:created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at"`
}

func (m *OauthAccessTokens) TableName() string {
	return "oauth_access_tokens"
}
