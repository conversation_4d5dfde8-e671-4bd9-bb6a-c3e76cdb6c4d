// /**
// @author: captain
// @since: 2022/9/1
// @desc:
// **/
package Comments

import (
	"time"
)

type CommentReply struct {
	ID        int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CommentID int       `gorm:"column:comment_id;NOT NULL"`     // 评论ID
	Type      int       `gorm:"column:type;default:1;NOT NULL"` // 评论类型 1 商家回复  2 平台回复
	Text      string    `gorm:"column:text"`                    // 评论内容
	AdminID   int       `gorm:"column:admin_id;NOT NULL"`       // 评论的管理员
	CreatedAt time.Time `gorm:"column:created_at"`              // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"`              // 修改时间
	DeletedAt time.Time `gorm:"column:deleted_at"`              // 删除时间
}

func (m *CommentReply) TableName() string {
	return "t_comment_reply"
}
