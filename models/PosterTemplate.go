package models

import (
	"gorm.io/gorm"
	"time"
)

const PosterTemplateRenderTypeDefault = 1
const PosterTemplateRenderTypeCanvas = 2

// PosterTemplate 海报模板
type PosterTemplate struct {
	ID         int64          `json:"id" gorm:"id"`
	TypeId     int64          `json:"type_id" gorm:"type_id"`         // 海报分类
	CoverUg    string         `json:"cover_ug" gorm:"cover_ug"`       // 示例图片
	CoverZh    string         `json:"cover_zh" gorm:"cover_zh"`       // 示例图片
	BaseUrl    string         `json:"base_url" gorm:"base_url"`       // 域名前缀
	Params     string         `json:"params" gorm:"params"`           // 页面相关参数
	Width      int64          `json:"width" gorm:"width"`             // 宽度
	Height     int64          `json:"height" gorm:"height"`           // 高度
	Order      int64          `json:"order" gorm:"order"`             // 排序
	State      int64          `json:"state" gorm:"state"`             // 状态
	Crop       int64          `json:"crop" gorm:"crop"`               // 是否抠图
	ShareUrl   string         `json:"share_url" gorm:"share_url"`     // 分享url
	RenderType int64          `json:"render_type" gorm:"render_type"` // 渲染方式 1默认 2:canvas
	ContentUg  string         `json:"content_ug" gorm:"content_ug"`   // json内容
	ContentZh  string         `json:"content_zh" gorm:"content_zh"`   // json内容
	CreatedAt  time.Time      `json:"created_at" gorm:"created_at"`   // 创建时间
	UpdatedAt  time.Time      `json:"updated_at" gorm:"updated_at"`   // 修改时间
	DeletedAt  gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"`   // 删除时间
}

// TableName 表名称
func (*PosterTemplate) TableName() string {
	return "t_poster_template"
}
