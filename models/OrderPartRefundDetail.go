package models

import (
	"time"

	"gorm.io/gorm"
)
const (
	PartRefundDetailTypeOriginal = 1 //原始记录 
	PartRefundDetailTypeRefund = 2   //本次退的记录
)

// OrderPartRefundDetail 订单部分退款和全额退款详情表
type OrderPartRefundDetail struct {
	ID               int64     `json:"id" gorm:"id"`                                 // 自增编号
	OrderDetailId  int64     `json:"order_detail_id" gorm:"order_detail_id"`
	Type     int64     `json:"type" gorm:"type"`									//数据类型 1:原始数据 2:退款数据
	PartRefundId     int64     `json:"part_refund_id" gorm:"part_refund_id"`
	OrderId          int64     `json:"order_id" gorm:"order_id"`                     // 订单编号
	ActivityType     int64     `json:"activity_type" gorm:"activity_type"`           // 活动类型 1：原价 2：优惠 3：秒杀 4：特价 5：加价优惠 6：加价秒杀 7：加价特价
	ActivityId       int64     `json:"activity_id" gorm:"activity_id"`               // 活动类型ID\n活动类型 1：原价 2：优惠,prefrential_id 3：秒杀,seckill_id 4：特价seckill_id 5：加价优惠 prefrential_id 6：加价秒杀 seckill_id 7：加价特价 seckill_id
	StoreFoodsId     int64     `json:"store_foods_id" gorm:"store_foods_id"`         // 餐厅美食编号
	OriginalPrice    int64     `json:"original_price" gorm:"original_price"`         // 原价格（单位：分）
	Price            int64     `json:"price" gorm:"price"`                           // 实际价格（单位：分）
	MpPercent        float64   `json:"mp_percent" gorm:"mp_percent"`                 // 当时属于平台的百分比
	DiscountPercent  int64     `json:"discount_percent" gorm:"discount_percent"`     // 打折百分比
	DealerPercent    float64   `json:"dealer_percent" gorm:"dealer_percent"`         // 当时属于代理商的百分比
	MpProfit         int64     `json:"mp_profit" gorm:"mp_profit"`                   // 当时属于平台的利润（公式：price * number*mp_percent）
	DealerProfit     int64     `json:"dealer_profit" gorm:"dealer_profit"`           // 当时属于代理商的利润（公式：price * number*dealer_percent）
	ResProfit        int64     `json:"res_profit" gorm:"res_profit"`                 // 餐厅利润
	Number           int64     `json:"number" gorm:"number"`                         // 数量
	LunchBoxId       int64     `json:"lunch_box_id" gorm:"lunch_box_id"`             // 跟美食关联的饭盒编号
	LunchBoxFee      int64     `json:"lunch_box_fee" gorm:"lunch_box_fee"`           // 饭盒单价（单位：分）
	LunchBoxCount    int64     `json:"lunch_box_count" gorm:"lunch_box_count"`       // 饭盒数量
	SeckillId        int64     `json:"seckill_id" gorm:"seckill_id"`                 // 秒杀ID
	PrefId           int64     `json:"pref_id" gorm:"pref_id"`                       // 优惠id
	PriceMarkupId    int64     `json:"price_markup_id" gorm:"price_markup_id"`       // 加价 id
	PriceMarkupPrice int64     `json:"price_markup_price" gorm:"price_markup_price"` // 加价 进价
	CreatedAt        time.Time `json:"created_at" gorm:"created_at"`                 // 创建时间
	UpdatedAt        time.Time `json:"updated_at" gorm:"updated_at"`                 // 修改时间
	DeletedAt        gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"`                 // 删除时间
	RefundPrice      int64     `json:"refund_price" gorm:"refund_price"`             // 部分退款金额
	LunchBoxFoodCount int `gorm:"column:lunch_box_food_count;"`
	RestaurantFoods    RestaurantFoods `gorm:"foreignkey:id;references:store_foods_id"`
	LunchBoxRefundPrice int 	`gorm:"column:lunch_box_refund_price;"` //饭盒部分退款金额
	// LunchBoxRefundCount int 	`gorm:"column:lunch_box_refund_count;"` //饭盒部分退款数量

	LunchBox LunchBox `gorm:"foreignkey:id;references:lunch_box_id"`

	FoodType     uint8    `gorm:"column:food_type;"`
	SpecID       int      `gorm:"column:spec_id;"`
	SelectedSpec FoodSpec `gorm:"foreignkey:id;references:spec_id"`
	LunchBoxGroupIndex int 	`gorm:"column:lunch_box_group_index;"` //饭盒部分退款金额
}

// TableName 表名称
func (*OrderPartRefundDetail) TableName() string {
	return "t_order_part_refund_detail"
}
