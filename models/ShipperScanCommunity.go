// Code generated by sql2gorm. DO NOT EDIT.
package models

import (
	"gorm.io/gorm"
	"time"
)

// 区县信息表
type ShipperScanCommunity struct {
	ID                    int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`              // 自增编号
	CityID                int       `gorm:"column:city_id;NOT NULL"`                           // 城市编号
	AreaID                int       `gorm:"column:area_id;NOT NULL"`                           // 城市编号
	NameUg                string    `gorm:"column:name_ug"`                                    // 区域维文名称
	NameZh                string    `gorm:"column:name_zh"`                                    // 区域中文名称
	Lat                   float64   `gorm:"column:lat;NOT NULL"`                               // 纬度
	Lng                   float64   `gorm:"column:lng;NOT NULL"`                               // 经度
	State                 int       `gorm:"column:state;default:0;NOT NULL"`                   // 状态（0关闭，1开通，2休息）
	CreatedAt             time.Time `gorm:"column:created_at"`                                 // 创建时间
	UpdatedAt             time.Time `gorm:"column:updated_at"`                                 // 修改时间
	DeletedAt             gorm.DeletedAt `gorm:"column:deleted_at"`                            // 删除时间
}

func (m *ShipperScanCommunity) TableName() string {
	return "b_shipper_scan_enter_community"
}
