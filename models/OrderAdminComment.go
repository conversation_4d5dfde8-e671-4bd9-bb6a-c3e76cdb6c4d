package models

import (
	"time"
)

type OrderAdminComment struct {
	ID          int       `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id,omitempty"`
	OrderID     int       `gorm:"column:order_id;NOT NULL" json:"user_id,omitempty"` // 用户ID
	AdminID     time.Time `gorm:"column:admin_id" json:"created_at"`                 // 创建时间
	SpeedStar   time.Time `gorm:"column:speed_star" json:"deleted_at"`               // 删除时间
	ServiceStar time.Time `gorm:"column:service_star" json:"deleted_at"`             // 删除时间
	CleanStar   time.Time `gorm:"column:clean_star" json:"deleted_at"`               // 删除时间
	CreatedAt   time.Time `gorm:"column:created_at" json:"deleted_at"`               // 删除时间
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"deleted_at"`               // 删除时间
	DeletedAt   time.Time `gorm:"column:deleted_at" json:"deleted_at"`               // 删除时间
}

func (m *OrderAdminComment) TableName() string {
	return "t_order_admin_comment"
}
