package models

type RestaurantFoodsCategory struct {
	FoodsCategoryID   int `gorm:"column:foods_category_id;not null;primaryKey" json:"foods_category_id"`     // t_foods_category 表的 id
	RestaurantFoodsID int `gorm:"column:restaurant_foods_id;not null;primaryKey" json:"restaurant_foods_id"` // t_restaurant_foods 表的 id
	Weight            int `gorm:"column:weight;not null;default:0" json:"weight"`                            // 美食在分类里面的顺序
}

// TableName sets the insert table name for this struct type
func (RestaurantFoodsCategory) TableName() string {
	return "t_restaurant_foods_category"
}
