package models

import (

"gorm.io/gorm"
"time"
)
type OrderSMS struct {
	ID         int            `gorm:"primary_key;auto_increment;column:id" json:"id"`
	CityID     int             `gorm:"column:city_id;" json:"city_id"`
	AreaID     int             `gorm:"column:area_id;" json:"area_id"`
	OrderID    int             `gorm:"column:order_id;" json:"order_id"`
	Type       int           `gorm:"column:type;" json:"type"`
	Resp       string          `gorm:"column:resp" json:"resp"`
	From       string          `gorm:"column:from" json:"from"`
	CreatedAt  time.Time       `gorm:"column:created_at;" json:"created_at"`
	UpdatedAt  time.Time       `gorm:"column:updated_at" json:"updated_at"`
	DeletedAt  gorm.DeletedAt  `gorm:"column:deleted_at;" json:"-"`
}

// TableName 表名
func (OrderSMS) TableName() string {
	return "t_order_sms"
}