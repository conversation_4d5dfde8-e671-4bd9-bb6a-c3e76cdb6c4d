package models

import (
	"time"
)

type PayLakalaRefund struct {
	ID           int          `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	ObjectID                int64     `gorm:"column:object_id;"`
	ObjectType             string     `gorm:"column:object_type;default:order;"`
	PayLakalaID  int          `gorm:"column:pay_lakala_id;NOT NULL"`
	OrderNo      string       `gorm:"column:order_no;NOT NULL"`
	OutRequestNo string       `gorm:"column:out_request_no"` // 应用平台请求号
	RefundAmount int          `gorm:"column:refund_amount;NOT NULL"`
	PaySeqNo     string       `gorm:"column:pay_seq_no;NOT NULL"`         // 支付流水号
	RefundRule   []RefundRule `gorm:"column:refund_rule;serializer:json"` // 退款规则，交易已分账时必填

	RefundParam            *string    `gorm:"column:refund_param"`                        // 退款参数
	Remark                 string     `gorm:"column:remark;NOT NULL"`                     // 备注
	OrderStatus            int        `gorm:"column:order_status;default:0;NOT NULL"`     // 订单状态
	RefundStatus           int        `gorm:"column:refund_status;default:0;NOT NULL"`    // 退款状态
	ErrorMessage           string     `gorm:"column:error_message"`                       // 错误信息
	RefundSeqNo            string     `gorm:"column:refund_seq_no;NOT NULL"`              // 退款流水号
	SplitRuleResult        *string    `gorm:"column:split_rule_result"`                   // 分账会员列表
	QztChannelPayRequestNo string     `gorm:"column:qzt_channel_pay_request_no;NOT NULL"` // 钱账通请求通道的流水号
	ChannelTradeNo         string     `gorm:"column:channel_trade_no;NOT NULL"`           // 渠道交易流水号（收单）
	ChannelSeqNo           string     `gorm:"column:channel_seq_no;NOT NULL"`             // 渠道支付流水号（收单）
	PayChannelTradeNo      string     `gorm:"column:pay_channel_trade_no"`                // 支付通道交易流水号（支付宝、微信）
	RefundTime             *time.Time `gorm:"column:refund_time"`                         // 退款时间
	CreatedAt              time.Time  `gorm:"column:created_at;NOT NULL"`
	UpdatedAt              time.Time  `gorm:"column:updated_at;NOT NULL"`
}

func (m *PayLakalaRefund) TableName() string {
	return "t_pay_lakala_refund"
}

type RefundRule struct {
	MemberNo      string `json:"member_no"`
	AccountTypeNo string `json:"account_type_no"`
	Amount        int    `json:"amount"`
}
