package models

import (
	"time"

	"gorm.io/gorm"
)

// LotteryOrder represents the t_lottery_order table structure with GORM annotations.
type LotteryOrder struct {
	ID                 uint           `gorm:"primaryKey;autoIncrement;column:id"`
	OrderID            string         `gorm:"column:order_id"`                               // 订单号
	LotteryActivityID  *int           `gorm:"column:lottery_activity_id"`                    // 抽奖活动id
	LotteryActivityLevelID *int          `gorm:"column:lottery_activity_level_id"`             // 抽奖活动等级id
	LotteryActivityLevel   *int         `gorm:"column:lottery_activity_level"`                  // 抽奖活动等级
	CityID              *int           `gorm:"column:city_id"`                                // 城市id
	AreaID              *int           `gorm:"column:area_id"`                                // 地区id
	UserID              *int           `gorm:"column:user_id"`                                // 用户id
	Mobile              string         `gorm:"column:mobile"`                                 // 用户手机号
	UserName            string         `gorm:"column:user_name"`                              // 用户姓名
	Price               *int           `gorm:"column:price"`                                  // 下单金额
	State               int          `gorm:"default:1;column:state"`                        // 订单状态
	PrizeOpenTime       *time.Time       `gorm:"column:prize_open_time"`                        // 抽奖时间
	PrizeOpenState      *int         `gorm:"default:1;column:prize_open_state"`             // 状态
	PrizeID             *uint          `gorm:"column:prize_id"`                               // 奖品ID
	ShipperID           *int           `gorm:"column:shipper_id"`                             // 配送员
	BuildingID          *int           `gorm:"column:building_id"`                             // 地址id
	CreatedAt           time.Time       `gorm:"column:created_at"`                             // 创建时间
	UpdatedAt           time.Time       `gorm:"column:updated_at"`                             // 更改时间
	DeletedAt           gorm.DeletedAt `gorm:"column:deleted_at"`                             // 删除时间

	LotteryActivity           LotteryActivity           `gorm:"foreignkey:lottery_activity_id;references:id"`
	
	
}

func (m *LotteryOrder) TableName() string {
	return "t_lottery_order"
}
