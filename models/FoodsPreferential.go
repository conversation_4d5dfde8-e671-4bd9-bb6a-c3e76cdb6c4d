package models

import (
	"time"

	"gorm.io/gorm"
)

const (
	FOODSPREFERENTIAL_ORDER_COUNT_PERDAY_UNLIMITED = 0
	FOODSPREFERENTIAL_ORDER_COUNT_PERDAY_ONCE      = 1

	FOODSPREFERENTIAL_STATE_OFF = 0 // 状态（0关闭，1开通）
	FOODSPREFERENTIAL_STATE_ON  = 1

	FOODSPREFERENTIAL_SHOW_HOME_OFF = 0 // 表示是否在小程序首页弹出（1表示在小程序首页显示，0表示不显示）
	FOODSPREFERENTIAL_SHOW_HOME_ON  = 1

	// 优惠类型
	FOODSPREFERENTIAL_TYPE_PRICE_DISCOUNT = 1 // 价格折扣
)

// FoodsPreferential 美食优惠信息表
type FoodsPreferential struct {
	ID                    int            `gorm:"column:id;primary_key;AUTO_INCREMENT"`    // 自增编号
	CityID                int            `gorm:"column:city_id"`                          // 城市编号
	AreaID                int            `gorm:"column:area_id"`                          // 区域编号
	RestaurantID          int            `gorm:"column:restaurant_id;NOT NULL"`           // 餐厅编号
	RestaurantFoodsID     int            `gorm:"column:restaurant_foods_id;NOT NULL"`     // 餐厅美食编号
	SpecID     			  int            `gorm:"column:spec_id;"`     					  // 美食规格
	PriceMarkupType       int            `gorm:"column:price_markup_type"`     			  // 是否加价销售 1:是 2否   数据库默认值 :2
	PriceMarkupId         int            `gorm:"column:price_markup_id"`     			  // 加价活动ID
	PriceMarkupCount      int            `gorm:"column:price_markup_count"`     		  // 加价美食数量
	PriceMarkupSaledCount int            `gorm:"column:price_markup_saled_count"`     	  // 加价美食销售数量
	GiftRestaurantFoodsID *int           `gorm:"column:gift_restaurant_foods_id"`         // 送了的东西的id
	Type                  int            `gorm:"column:type;NOT NULL"`                    // 优惠类型
	StartDateTime         time.Time      `gorm:"column:start_date_time"`                  // 活动的开始时间  比如:2015-10-15 06:00
	EndDateTime           time.Time      `gorm:"column:end_date_time"`                    // 活动的结束时间 比如:2015-11-15 23:00
	StartTime             string         `gorm:"column:start_time;NOT NULL"`              // 一天内的开始时间  15:00
	EndTime               string         `gorm:"column:end_time;NOT NULL"`                // 一天内的结束时间   20:00
	Level                 int            `gorm:"column:level;default:0;NOT NULL"`         // 优先级
	Percent               uint           `gorm:"column:percent;default:100"`              // 百分比
	OriginalPrice         uint           `gorm:"column:original_price;default:0"`         // 优惠价格（单位：分）
	DiscountPrice         uint           `gorm:"column:discount_price;default:0"`         // 优惠价格（单位：分）
	MaxOrderCount         *int           `gorm:"column:max_order_count;default:1"`        // 一个订单里最多能购买的数量
	OrderCountPerDay      int            `gorm:"column:order_count_per_day;default:0"`    // 一天内的订购次数（0表示能购买无数，1表示智能买一个）
	Count                 *uint          `gorm:"column:count"`                            // 购买的数量
	GiftCount             *uint          `gorm:"column:gift_count"`                       // 赠送数量
	SendPlatform          int            `gorm:"column:send_platform;default:0;NOT NULL"` // 是否发布到公众平台，0表示否，1表示是
	ShowHome              int            `gorm:"column:show_home;default:0"`              // 表示是否在小程序首页弹出（1表示在小程序首页显示，0表示不显示）
	State                 int            `gorm:"column:state;NOT NULL"`                   // 状态（0关闭，1开通）
	Weight                int            `gorm:"column:weight;default:0"`                // 权重
	CreatorID             *int           `gorm:"column:creator_id;NOT NULL"`              // 创建者编号
	CreatedAt             time.Time      `gorm:"column:created_at"`                       // 创建时间
	UpdatedAt             time.Time      `gorm:"column:updated_at"`                       // 修改时间
	DeletedAt             gorm.DeletedAt `gorm:"column:deleted_at"`                       // 删除时间

	RestaurantFood   RestaurantFoods  `gorm:"foreignKey:restaurant_foods_id;references:ID"`
	PreferentialType PreferentialType `gorm:"foreignKey:type;references:ID"`
	Creator          Admin            `gorm:"foreignKey:creator_id;references:ID"`
	GiftFood         RestaurantFoods  `gorm:"foreignKey:gift_restaurant_foods_id;references:id"`
	Restaurant       Restaurant       `gorm:"foreignKey:restaurant_id;references:id"`
	FoodsPreferential       []FoodsPreferentialLog       `gorm:"foreignKey:pref_id"`
	FoodsPreferentialLogGroupBySelect       FoodsPreferentialLogGroupBySelect       `gorm:"foreignKey:pref_id"`
	PriceMarkupFood            PriceMarkupFood              `gorm:"foreignkey:price_markup_id;references:id"`
	PriceMarkupFoodLogSum            PriceMarkupFoodLog              `gorm:"foreignkey:price_markup_id;references:price_markup_id"`

	FoodType     uint8     `gorm:"column:food_type;default:0;not null"` // 美食类型 (0: 普通美食, 1: 规格, 2: 规格美食)
	SelectedSpec *FoodSpec `gorm:"foreignkey:SpecID;references:ID"`    // 已选规格数据
}

func (m *FoodsPreferential) TableName() string {
	return "t_foods_preferential"
}
