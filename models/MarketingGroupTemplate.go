package models

import (
	"time"

	"gorm.io/gorm"
)

const (
	MarketingGroupTemplateTypeRestaurant = 1

	MarketingGroupTemplateStateNew   = 0
	MarketingGroupTemplateStateOpen  = 1
	MarketingGroupTemplateStatePause = 2
)

// 商家满减活动模板表
type MarketingGroupTemplate struct {
	ID            int `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`        // 编号 编号
	CreatorType   int `gorm:"column:creator_type;default:2" json:"creator_type"`     // 谁创建的 1.代理
	MarketingMode int `gorm:"column:marketing_mode;default:1" json:"marketing_mode"` // 店铺类型 (1 全部店铺 2:个别店铺)
	CreatorID     int `gorm:"column:creator_id" json:"creator_id"`                   // 创建人ID
	CityID        int `gorm:"column:city_id" json:"city_id"`
	AreaID        int `gorm:"column:area_id;NOT NULL" json:"area_id"`      // 地区编号
	MarketingType int `gorm:"column:marketing_type" json:"marketing_type"` // 营销类型  1：满减   2:减配送费 3：满赠活动 3:折扣美食 4: 5:优惠券 6:新客立减

	Type int `gorm:"column:type" json:"type"` // 活动类型   1：店铺活动，2：商品定向活动

	NameUg        string     `gorm:"column:name_ug" json:"name_ug"`
	NameZh        string     `gorm:"column:name_zh" json:"name_zh"`
	BeginDate     *time.Time `gorm:"column:begin_date;NOT NULL" json:"begin_date"`           // 开始日期
	EndDate       *time.Time `gorm:"column:end_date;NOT NULL" json:"end_date"`               // 结束日期
	FullWeekState int        `gorm:"column:full_week_state;NOT NULL" json:"full_week_state"` // 全星期  0：否（多选星期），1：是

	Day           int    `gorm:"column:day" json:"day"`                                        // 星期日
	FullTimeState int    `gorm:"column:full_time_state;NOT NULL" json:"full_time_state"`       // 全天     0：否（添加时间段），1：是
	Time1Start    string `gorm:"column:time1_start" json:"time1_start"`                        // 时间段1开始
	Time1End      string `gorm:"column:time1_end" json:"time1_end"`                            // 时间段1结束
	Time2Start    string `gorm:"column:time2_start" json:"time2_start"`                        // 时间段2开始
	Time2End      string `gorm:"column:time2_end" json:"time2_end"`                            // 时间段2结束
	Time3Start    string `gorm:"column:time3_start" json:"time3_start"`                        // 时间段3开始
	Time3End      string `gorm:"column:time3_end" json:"time3_end"`                            // 时间段3结束
	TimeCount     int    `gorm:"column:time_count;default:1" json:"time_count"`                // 记录几个时间段
	AutoContinue  int    `gorm:"column:auto_continue;default:0;NOT NULL" json:"auto_continue"` // 自动延续 0:  否，1：是
	ContinueCount int    `gorm:"column:continue_count;default:0" json:"continue_count"`        // 自动延续数量
	Steps         string `gorm:"type:json;default:null" json:"steps"`                          // 价格阶梯

	PriceMax       *int `gorm:"column:price_max" json:"price_max"`                         // 最高的满减阶梯开始
	PriceReduceMax *int `gorm:"column:price_reduce_max;default:0" json:"price_reduce_max"` // 最高的满减 减免金额
	PriceCount     *int `gorm:"column:price_count;default:0" json:"price_count"`           // 阶梯数量
	//Price1Type     *int `gorm:"column:price1_type" json:"price1_type"`                     // 减配送费 减免类别 （ 1:配送费的一般 2::配送费的全部）
	//Price2Type     *int `gorm:"column:price2_type" json:"price2_type"`                     // 减配送费 减免类别 （ 1:配送费的一般 2::配送费的全部）
	SendCount       int    `gorm:"column:send_count;default:0" json:"send_count"`       // 发送次数
	State           int    `gorm:"column:state;default:0" json:"state"`                 // 状态  0：新建，1:启动，2：暂停，3：失效
	CustomerType    int    `gorm:"column:customer_type;default:0" json:"customer_type"` // 对象用户类别(0:全部客户 1:新客户(平台) 2: 新客户(店铺) 3:老客户(平台)  4:老客户(店铺))
	MarketingNumber string `gorm:"column:marketing_number" json:"marketing_number"`     // 活动编号
	//Foods            string         `gorm:"column:foods" json:"foods"`                           // 美食
	MinDeliveryPrice int            `gorm:"column:min_delivery_price" json:"min_delivery_price"` // 最低起送价
	CreatedAt        *time.Time     `gorm:"column:created_at" json:"created_at"`                 // 创建时间
	UpdatedAt        *time.Time     `gorm:"column:updated_at" json:"updated_at"`                 // 更新时间
	DeletedAt        gorm.DeletedAt `gorm:"column:deleted_at"  json:"deleted_at"`                // 被删除时间

	Creator                          *Admin                             `gorm:"foreignKey:creator_id;references:id" json:"creator"` // 创建人
	City                             City                               `gorm:"foreignKey:city_id;references:id"`                   // 地区
	Area                             Area                               `gorm:"foreignKey:area_id;references:id"`                   // 区域
	MarketingGroupTemplateAttendance []MarketingGroupTemplateAttendance `gorm:"foreignKey:template_id;references:id"`               // 参与记录

}

type MarketingTemplateStaticWithOrderLog struct {
	MarketingGroupTemplate
	TotalOrderPrice  int `gorm:"column:total_order_price" json:"total_order_price"`   // 订单总金额（单位 分）
	OrderCount       int `gorm:"column:order_count" json:"order_count"`               // 订单总数
	TotalReducePrice int `gorm:"column:total_reduce_price" json:"total_reduce_price"` // 优惠金额（单位 分）
	InviteCount      int `gorm:"column:invite_count" json:"invite_count"`             // 邀请店铺的数量
	AcceptedCount    int `gorm:"column:accepted_count" json:"accepted_count"`         // 接收邀请的店铺的数量
}
type MarketingGroupTemplateAggs struct {
	MarketingGroupTemplate
	TotalOrderPrice  int `gorm:"column:total_order_price" json:"total_order_price"`   // 订单总金额（单位 分）
	OrderCount       int `gorm:"column:order_count" json:"order_count"`               // 订单总数
	TotalReducePrice int `gorm:"column:total_reduce_price" json:"total_reduce_price"` // 优惠金额（单位 分）
	TotalDealerCost  int `gorm:"column:total_dealer_cost" json:"total_dealer_cost"`   // 代理承担部分（单位 分）
	TotalResCost     int `gorm:"column:total_res_cost" json:"total_res_cost"`         // 商家承担部分（单位 分）
	InviteCount      int `gorm:"column:invite_count" json:"invite_count"`             // 邀请店铺的数量
	AcceptedCount    int `gorm:"column:accepted_count" json:"accepted_count"`         // 接收邀请的店铺的数量
}

// MarketingGroupTemplateAggsForStatistics Cms统计页面专用
type MarketingGroupTemplateAggsForStatistics struct {
	MarketingGroupTemplate
	TotalOrderPrice     int `gorm:"column:total_order_price" json:"total_order_price"`         // 订单总金额（单位 分）
	CompletedOrderCount int `gorm:"column:complated_order_count" json:"complated_order_count"` // 订单总数
	RejectedOrderCount  int `gorm:"column:rejected_order_count" json:"rejected_order_count"`   // 订单总数
	TotalReducePrice    int `gorm:"column:total_reduce_price" json:"total_reduce_price"`       // 优惠金额（单位 分）
	InviteCount         int `gorm:"column:invite_count" json:"invite_count"`                   // 邀请店铺的数量
	AcceptedCount       int `gorm:"column:accepted_count" json:"accepted_count"`               // 接收邀请的店铺的数量
}

func (m *MarketingGroupTemplate) TableName() string {
	return "t_marketing_group_template"
}

func (m *MarketingTemplateStaticWithOrderLog) TableName() string {
	return "t_marketing_group_template"
}
