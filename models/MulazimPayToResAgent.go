/**
@author: captain
@since: 2022/11/7
@desc: 商家每天的营业表和代理商的收益表
**/
package models

import (
	"time"
)

type Mulazimpaytoresagent struct {
	ID              int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 编号
	SxID            uint      `gorm:"column:sx_id;NOT NULL"`                // 市县代码
	SxMc            string    `gorm:"column:sx_mc;NOT NULL"`                // 市县名称
	ResID           int64     `gorm:"column:res_id"`                        // 餐厅ID
	ResNameUg       string    `gorm:"column:res_name_ug"`                   // 餐厅维文名称
	ResNameZh       string    `gorm:"column:res_name_zh"`                   // 餐厅汉文名称
	TransDate       time.Time `gorm:"column:trans_date;NOT NULL"`           // 交易日期
	TotalIncome     int       `gorm:"column:total_income"`                  // 当天的营业额+配送费
	CashIncome      int       `gorm:"column:cash_income"`                   // 当天的总交易量（现金部分）
	OnlineIncome    int       `gorm:"column:online_income"`                 // 当天的总交易量（网付部分）
	BusinessIncome  int       `gorm:"column:business_income"`               // 当天的营业额
	DeliverProfit   int       `gorm:"column:deliver_profit"`                // 当天的配送收入
	ResIncome       int       `gorm:"column:res_income"`                    // 当天的餐厅汇款金额
	AgentProfit     int       `gorm:"column:agent_profit"`                  // 当天的代理收入
	MpProfit        int       `gorm:"column:mp_profit"`                     // 当天的Mulazim平台收入
	OrderNumber     int       `gorm:"column:order_number"`                  // 当天的订单量
	PerTicketSales  int       `gorm:"column:per_ticket_sales"`              // 当天的客单价
	PerTicketProfit int       `gorm:"column:per_ticket_profit"`             // 当天的客单利润
	RemitTotal      int       `gorm:"column:remit_total"`                   // 餐厅汇款金额
	PaymentType     int       `gorm:"column:payment_type"`                  // Mulazim打款方式（1：企业付款到零钱，2：企业付款到银行卡）
	OpenID          string    `gorm:"column:open_id"`                       // 餐厅收款人OpenID号
	CardNumber      string    `gorm:"column:card_number"`                   // 银行卡卡号
	PayeeName       string    `gorm:"column:payee_name"`                    // 微信绑定的手机号或餐厅收款人姓名
	BankCode        uint      `gorm:"column:bank_code"`                     // 收款方开户行代码
	PartnerTradeNo  string    `gorm:"column:partner_trade_no"`              // 商户订单号，需保持历史全局唯一性
	PaymentNo       string    `gorm:"column:payment_no"`                    // 微信付款单号
	PaymentTime     time.Time `gorm:"column:payment_time"`                  // 付款成功时间
	PaymentState    int       `gorm:"column:payment_state"`                 // 0未付款，1已付款
	SettlementDate  time.Time `gorm:"column:settlement_date"`               // 结算日期（24小时模式下今日订单可能次日完成，所以这样的订单不能当日结算）
	WithdrawId     int       `gorm:"column:withdraw_id"`                   //
}

func (m *Mulazimpaytoresagent) TableName() string {
	return "t_mulazimpaytoresagent"
}
