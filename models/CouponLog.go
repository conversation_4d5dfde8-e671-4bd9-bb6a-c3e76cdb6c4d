package models

import (
	"time"
)

const (
	//状态 0 订单创建  1:订单完成 2:退款
	COUPON_LOG_STATE_OREDER   = 0 //0 订单创建
	COUPON_LOG_STATE_USED     = 1 //1:订单完成
	COUPON_LOG_STATE_REFUNDED = 2 //2:退款

)

// 优惠券使用记录
type CouponLog struct {
	ID        int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CityID    int       `gorm:"column:city_id"`            // 城市id
	AreaID    int       `gorm:"column:area_id"`            // 区域id
	ResID     int       `gorm:"column:res_id"`             // 店铺id
	OrderID   int       `gorm:"column:order_id;NOT NULL"`  // 订单id
	CouponID  int       `gorm:"column:coupon_id;NOT NULL"` // 优惠券id
	UserID    int       `gorm:"column:user_id;NOT NULL"`   // 用户id
	State     int       `gorm:"column:state;default:0"`    // 状态 0 订单创建  1:订单完成 2:退款
	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`

	UserCouponId int `gorm:"column:user_coupon_id;default:null"` 

	Name      string    `gorm:"-"`
	Price     int       `gorm:"-"`
	Coupon    Coupon    `gorm:"foreignkey:coupon_id;references:id"`
}

func (m *CouponLog) TableName() string {
	return "t_coupon_log"
}
