﻿package models

import (
	"time"

	"gorm.io/gorm"
)
const (
	LotteryPrizeStateEnable  = 1
	LotteryPrizeStateDisable = 0
)

const (
	LotteryPrizeStateOn  = 1 // 开启
	LotteryPrizeStateOff = 0 // 未开启

	LotteryPrizeTypeLottery  = 1 // 抽奖活动
	LotteryPrizeTypeActivity = 2 // 订单排行榜活动
)

// LotteryPrize represents the lottery prize table in the database.
type LotteryPrize struct {
	ID                uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	CityID int  `gorm:"type:int" json:"city_id"`
	AreaID int  `gorm:"type:int" json:"area_id"`
	// Type   uint `gorm:"type:tinyint;default:1" json:"type"`
	TitleImgUg        string         `gorm:"type:varchar(255);collation:utf8mb4_general_ci" json:"title_img_ug"`                     // 封面图片 维吾尔语
	TitleImgZh        string         `gorm:"type:varchar(255);collation:utf8mb4_general_ci" json:"title_img_zh"`                     // 封面图片 汉语
	SwiperImgUg       string         `gorm:"type:varchar(255);collation:utf8mb4_general_ci" json:"swiper_img_ug"`                    // 轮播图片 维吾尔语
	SwiperImgZh       string         `gorm:"type:varchar(255);collation:utf8mb4_general_ci" json:"swiper_img_zh"`                    // 轮播图片 汉语
	NameUg            string         `gorm:"type:varchar(255);collation:utf8mb4_general_ci" json:"name_ug"`                          // 名称维吾尔语
	NameZh            string         `gorm:"type:varchar(255);collation:utf8mb4_general_ci" json:"name_zh"`                          // 名称汉语
	Model             string         `gorm:"type:varchar(255);collation:utf8mb4_general_ci" json:"model"`                            // 型号
	Price             int            `gorm:"type:int" json:"price"`                                                                  // 价格
	State             int            `gorm:"type:tinyint;default:0" json:"state"`                                                    // 状态 0:未开启  1:开启
	AdminID           int            `gorm:"type:int" json:"admin_id"`                                                               // 创建人
	CreatedAt         time.Time      `gorm:"type:timestamp;default:CURRENT_TIMESTAMP" json:"created_at"`                             // 创建时间
	UpdatedAt         time.Time      `gorm:"type:timestamp;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updated_at"` // 更新时间
	DeletedAt         gorm.DeletedAt `gorm:"type:timestamp" json:"deleted_at"`

	City City `gorm:"foreignKey:CityID;references:id" json:"city"`
	Area Area `gorm:"foreignKey:AreaID;references:id" json:"area"`
}

// TableName sets the table name for the LotteryPrize model.
func (LotteryPrize) TableName() string {
	return "t_lottery_prize"
}
