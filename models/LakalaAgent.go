package models

import (
	"time"

)

// TPayAgentLakala represents the t_pay_agent_lakala table in the database.
type PayAgentLakala struct {
	

	ID                int   `gorm:"primaryKey;autoIncrement"` // 自增ID
	ChargeID          int   `gorm:"column:charge_id;not null"`          // 充值ID
	OrderNo           string `gorm:"column:order_no;not null"`           // 存管系统订单号
	PayerOpenID       string `gorm:"column:payer_openid;not null"`       // 支付人员openid
	SellerMemberNo    string `gorm:"column:seller_member_no;not null"`    // 卖家会员标识
	OutOrderNo        string `gorm:"column:out_order_no;not null"`        // 应用平台订单号
	Amount            int   `gorm:"column:amount;not null"`            // 商品详情
	PayAmount         int   `gorm:"column:pay_amount;not null"`         // 订单金额
	TerminalIP        int   `gorm:"column:terminal_ip;not null"`        // 用户IP
	OrderName         string `gorm:"column:order_name;not null"`         // 订单名称
	SplitMode         int  `gorm:"column:split_mode;not null"`         // 分账模式
	FrontURL          string `gorm:"column:front_url"`                  // 前端回调地址
	BackURL           string `gorm:"column:back_url"`                   // 后端回调地址
	PayMethod         string `gorm:"column:pay_method;type:text;not null"` // 支付方式
	SplitRuleData     string `gorm:"column:split_rule_data"`           // 分账规则数据
	OutRequestNo      string `gorm:"column:out_request_no;not null"`      // 平台支付请求号
	PayStatus         int    `gorm:"column:pay_status;default:0;not null"` // 支付状态
	OrderStatus       int    `gorm:"column:order_status;default:0;not null"` // 订单状态
	ErrorMessage      string `gorm:"column:error_message"`               // 错误信息
	PaySeqNo          string `gorm:"column:pay_seq_no"`                  // 支付流水号
	SplitSeqNo        string `gorm:"column:split_seq_no"`                // 分账流水号
	SplitRuleResult   string `gorm:"column:split_rule_result"`         // 分账会员列表
	BuyerMemberNo     string `gorm:"column:buyer_member_no"`             // 买家会员标识
	PayInfo           string `gorm:"column:pay_info"`                   // 支付信息
	QztChannelPayRequestNo string `gorm:"column:qzt_channel_pay_request_no"` // 钱帐通请求通道的流水号
	ChannelTradeNo    string `gorm:"column:channel_trade_no"`             // 渠道交易流水号（收单）
	ChannelSeqNo      string `gorm:"column:channel_seq_no"`               // 渠道支付流水号（收单）
	PayChannelTradeNo string `gorm:"column:pay_channel_trade_no"`         // 支付通道交易流水号（支付宝、微信）
	ThirdPartyPayment string `gorm:"column:third_party_payment"`          // 第三方支付类型
	OpenID            string `gorm:"column:open_id"`                      // openid
	SubOpenID         string `gorm:"column:sub_open_id"`                  // 微信子appid的openid
	PayTime           *time.Time `gorm:"column:pay_time"`                     // 支付时间
	ConfirmURL        string `gorm:"column:confirm_url"`                  // 密码确认的URL地址
	IsConfirm         int  `gorm:"column:is_confirm;default:0;not null"` // 是否需要确认
	ErrorCode         string `gorm:"column:error_code"`                   // 错误代码
	Message           string `gorm:"column:message"`                      // 错误信息
	Refunded          *bool  `gorm:"column:refunded"`                     // 退款标识
	OutRefundRequestNo string `gorm:"column:out_refund_request_no"`       // 退款请求号
	MerchantNo        string `gorm:"column:merchant_no"`                  // merchant_no
}

// TableName sets the table name for the TPayAgentLakala model.
func (m *PayAgentLakala) TableName() string {
	return "t_pay_agent_lakala"
}