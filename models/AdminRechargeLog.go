/**
@author: captain
@since: 2022/9/18
@desc: Code generated by sql2gorm. DO NOT EDIT.
**/

package models

import (
	"time"

	"gorm.io/gorm"
)

type AdminRechargeLog struct {
	ID             int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CityID         int       `gorm:"column:city_id;NOT NULL"`
	AreaID         int       `gorm:"column:area_id;NOT NULL"`
	RechargerID    int       `gorm:"column:recharger_id"`             // 充值人员（代理助手）编号
	AdminID        int       `gorm:"column:admin_id;NOT NULL"`        // 代理（管理员）编号
	Type           int       `gorm:"column:type"`                     // 1表示充值；2表示消费、3表示退款充值，4表示现金订单 客户 支付 ,5表示现金订单 配送员 支付,（4和5的时候 向代理退现金订单扣的钱）
	RechargeAmount int       `gorm:"column:recharge_amount;NOT NULL"` // 充值金额（单位为：分）
	Balance        int       `gorm:"column:balance"`                  // 充值或消费后的余额
	OutTradeNo     string    `gorm:"column:out_trade_no"`             // 网站商品订单唯一号
	TransactionID  string    `gorm:"column:transaction_id"`           // 支付宝流水号
	PrepayID       string    `gorm:"column:prepay_id"`                // 预支付交易会话标识
	NonceStr       string    `gorm:"column:nonce_str"`                // 卖家支付宝用户号
	TradeType      string    `gorm:"column:trade_type"`               // 卖家支付宝账号
	Payed          int       `gorm:"column:payed;default:0"`          // 状态（0 未付款， 1已付款）
	ExpireTime     time.Time `gorm:"column:expire_time;default:NULL"` // 过期时间
	CreatedAt      time.Time `gorm:"column:created_at"`               // 创建时间
	UpdatedAt      time.Time `gorm:"column:updated_at"`               // 修改时间
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;default:NULL"`  // 删除时间
}

func (m *AdminRechargeLog) TableName() string {
	return "t_admin_recharge_log"
}
