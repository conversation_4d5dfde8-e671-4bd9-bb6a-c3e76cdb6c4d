package models

import (
	"time"

	"gorm.io/gorm"
)

const DICTIONARY_TYPE_GROUP = 1 // 美食分组

const DICTINARY_SUB_TYPE_FOOD_SOLD_OUT = 1

const DICTIONARY_TYPE_PART_REFUND_REASON  = 2 // 部分退款原因
const DICTIONARY_TYPE_VALENTINES_DAY_TOPIC  = 3 // 情人节活动话题

const DictionaryStateOpen = 1
const DictionaryStateClose = 2


type Dictionary struct {
	ID          int            `gorm:"primaryKey;autoIncrement;not null;column:id" json:"id"` // 主键
	NameZh      string         `gorm:"column:name_zh" json:"name_zh"`                         // 分组名称(国语)
	NameUg      string         `gorm:"column:name_ug" json:"name_ug"`                         // 分组名称(维语)
	Type        int            `gorm:"not null;default:0;column:type" json:"type"`          // 1:分组
	SubType     int            `gorm:"not null;default:0;column:sub_type" json:"sub_type"`    // 1.商家客户端退款时美食售完 弹出
	State       int            `gorm:"default:1;column:state" json:"state"`          		  // 1.开启 2.关闭
	Weight      int            `gorm:"default:0;column:weight" json:"weight"`                // 排序
	CreatedAt   *time.Time     `gorm:"column:created_at" json:"created_at"`                   // 创建时间
	UpdatedAt   *time.Time     `gorm:"autoUpdateTime;column:updated_at" json:"updated_at"`    // 修改时间，自动更新时间
	DeletedAt   gorm.DeletedAt `gorm:"index;column:deleted_at" json:"deleted_at"`             // 删除时间，支持软删除，添加索引
}

// TableName
//
//  @Author: YaKupJan
//  @Date: 2024-09-14 11:50:42
//  @Description: 美食分组基本表 审核完成后添加
//  @receiver FoodsBaseGroup
//  @return string
func (Dictionary) TableName() string {
	return "b_dictionary"
}
