package models

import (
	"gorm.io/gorm"
	"time"
)

// Advert 广告信息表
type Advert struct {
	ID                  int64          `json:"id" gorm:"id"`                                         // 自增编号
	LangId              int64          `json:"lang_id" gorm:"lang_id"`                               // 语言类型
	AdverPositionId    int64          `json:"adver_position_id" gorm:"adver_position_id"`           // 广告位置编号
	Type                int64          `json:"type" gorm:"type"`                                     // 1：表示图片、2表示视频
	AreaId              int64          `json:"area_id" gorm:"area_id"`                               // 区域或县编号
	Content             string         `json:"content" gorm:"content"`                               // 文字内容
	ImageUrl            string         `json:"image_url" gorm:"image_url"`                           // 广告图片路径
	VideoUrl            string         `json:"video_url" gorm:"video_url"`                           // 视频广告url
	LinkType            int64          `json:"link_type" gorm:"link_type"`                           // 链接类型（0 wap页面，1 餐厅详细页面， 2 美食详细页面，3 商户详细页面，4 产品详细页面，99 表示广告图）
	LinkUrl             string         `json:"link_url" gorm:"link_url"`                             // 超链接地址
	LinkId              int64          `json:"link_id" gorm:"link_id"`                               // link_type不是超链接的时有效，是要链接的产品或商户编号
	StoreId             int64          `json:"store_id" gorm:"store_id"`                             // 商户编号
	MiniProgramId       string         `json:"mini_program_id" gorm:"mini_program_id"`               // 第三方小程序原始ID
	MiniProgramLinkPage string         `json:"mini_program_link_page" gorm:"mini_program_link_page"` // 第三方小程序页面路径
	StartTime           time.Time      `json:"start_time" gorm:"start_time"`                         // 开始时间
	EndTime             time.Time      `json:"end_time" gorm:"end_time"`                             // 结束时间
	TimeBegin           string         `json:"time_begin" gorm:"time_begin"`                         // 开始时间(小时)
	TimeEnd             string         `json:"time_end" gorm:"time_end"`                             // 结束时间(小时)
	Weight              int64          `json:"weight" gorm:"weight"`                                 // 排序
	State               int64          `json:"state" gorm:"state"`                                   // 状态（0关闭，1开通）
	CreatedAt           time.Time      `json:"created_at" gorm:"created_at"`                         // 创建时间
	UpdatedAt           time.Time      `json:"updated_at" gorm:"updated_at"`                         // 修改时间
	DeletedAt           gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"`                         // 删除时间

	Area           Area            `json:"area" gorm:"foreignKey:area_id;references:id"`
	AdvertPosition AdvertPosition  `json:"adver_position" gorm:"foreignKey:adver_position_id;references:id"`
	Restaurant     Restaurant      `json:"restaurant" gorm:"foreignKey:link_id;references:id"`
	RestaurantFood RestaurantFoods `json:"restaurant_food" gorm:"foreignKey:link_id;references:id"`
}

// TableName 表名称
func (*Advert) TableName() string {
	return "t_adver"
}
