package models

import (
	"fmt"
	"mulazim-api/tools"
	"time"

	"gorm.io/gorm"
)

const (
	FoodsMultipleDiscountStateClose = 0 // 关闭
	FoodsMultipleDiscountStateOpen  = 1 // 开启
	FoodsMultipleDiscountStatePause = 2 // 暂停
	FoodsMultipleDiscountStateExpired = 3 // 过期
)

// FoodsMultipleDiscount 多分打折
type FoodsMultipleDiscount struct {
	ID               int64          `json:"id" gorm:"id"`
	CityId           int64          `json:"city_id" gorm:"city_id"`                     // 城市id
	AreaId           int64          `json:"area_id" gorm:"area_id"`                     // 地区id
	RestaurantId     int64          `json:"restaurant_id" gorm:"restaurant_id"`         // 店铺id
	CreatorId        int64          `json:"creator_id" gorm:"creator_id"`               // 创建人
	CreatorType      int64          `json:"creator_type" gorm:"creator_type"`           // 创建人类型
	NameUg           string         `json:"name_ug" gorm:"name_ug"`                     // 名称
	NameZh           string         `json:"name_zh" gorm:"name_zh"`                     // 名称
	StartTime        time.Time      `json:"start_time" gorm:"start_time"`               // 开始时间
	EndTime          time.Time      `json:"end_time" gorm:"end_time"`                   // 结束时间
	FoodId           int64          `json:"food_id" gorm:"food_id"`                     // 美食id
	Discount2Percent float64        `json:"discount2_percent" gorm:"discount2_percent"` // 第2份优惠比例
	Discount3Percent float64        `json:"discount3_percent" gorm:"discount3_percent"` // 第3份优惠比例
	Discount4Percent float64        `json:"discount4_percent" gorm:"discount4_percent"` // 第4份优惠比例
	Discount5Percent float64        `json:"discount5_percent" gorm:"discount5_percent"` // 第5份优惠比例
	State            int            `json:"state" gorm:"state"`                         // 状态 0 新建 1 开启  2 暂停
	CreatedAt        time.Time      `json:"created_at" gorm:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at" gorm:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"`

	Details    []FoodsMultipleDiscountDetail `json:"details" gorm:"foreignKey:ActivityId;references:ID"`      // 优惠阶梯
	Food       RestaurantFoods               `json:"food" gorm:"foreignKey:FoodId;references:ID"`             // 美食
	Creator    Admin                         `json:"creator" gorm:"foreignKey:CreatorId;references:ID"`       // 创建人
	Restaurant Restaurant                    `json:"restaurant" gorm:"foreignKey:RestaurantId;references:ID"` // 店铺
	City       City                          `json:"city" gorm:"foreignKey:CityId;references:ID"`             // 城市
	Area       Area                          `json:"area" gorm:"foreignKey:AreaId;references:ID"`             // 地区
}


// TableName 表名称
func (*FoodsMultipleDiscount) TableName() string {
	return "t_foods_multiple_discount"
}

// IsOpen 状态是否开启
func (m *FoodsMultipleDiscount) IsOpen() bool {
	return m.State == FoodsMultipleDiscountStateOpen
}

// IsClose 状态是否关闭
func (m *FoodsMultipleDiscount) IsClose() bool {
	return m.State == FoodsMultipleDiscountStateClose
}

// IsPause 状态是否暂停
func (m *FoodsMultipleDiscount) IsPause() bool {
	return m.State == FoodsMultipleDiscountStatePause
}

// IsInEffect 是否在有效期内
func (m *FoodsMultipleDiscount) IsInEffect() bool {
	return m.StartTime.Before(time.Now()) && m.EndTime.After(time.Now())
}

// IsEnd 是否结束
func (m *FoodsMultipleDiscount) IsEnd() bool {
	return m.EndTime.Before(time.Now())
}

// IsStart 是否开始
func (m *FoodsMultipleDiscount) IsStart() bool {
	return m.StartTime.Before(time.Now())
}

// GetDiscountCount 获取优惠阶梯数量
func (m *FoodsMultipleDiscount) GetDiscountCount(db *gorm.DB) int64 {
	// 预加载 Details
	if err := db.Preload("Details").First(m, m.ID).Error; err != nil {
		tools.Logger.Error("加载数据失败: %v\n", err)
		return 0
	}

	// 计算当前模型中有几个百分比不为 0（代表启用了几级阶梯）
	var countInMainTable int64 = 0
	if m.Discount2Percent > 0 {
		countInMainTable++
	}
	if m.Discount3Percent > 0 {
		countInMainTable++
	}
	if m.Discount4Percent > 0 {
		countInMainTable++
	}
	if m.Discount5Percent > 0 {
		countInMainTable++
	}

	countInDetails := int64(len(m.Details))

	// 比较两个数量是否一致
	if countInDetails != countInMainTable {
		fmt.Printf("数据不一致：主表有 %d 阶梯，关联表有 %d 条记录\n", countInMainTable, countInDetails)
		return 0
	}

	// 返回实际阶梯数量（都一致）
	return countInMainTable
}

// 判断是不是使用多份打折下单
func (m *FoodsMultipleDiscount) HasMultipleDiscountOrder(db *gorm.DB) bool {
	query := db.Model(&FoodsMultipleDiscountLog{}).Where("activity_id = ?", m.ID)
	var count int64
	query.Count(&count)
	return count > 0
}

// FoodsMultipleDiscount 多分打折
type FoodsMultipleDiscountDetailQuery struct {
	ID               int64          `json:"id" gorm:"id"`
	CityId           int64          `json:"city_id" gorm:"city_id"`                     // 城市id
	AreaId           int64          `json:"area_id" gorm:"area_id"`                     // 地区id
	RestaurantId     int64          `json:"restaurant_id" gorm:"restaurant_id"`         // 店铺id
	CreatorId        int64          `json:"creator_id" gorm:"creator_id"`               // 创建人
	CreatorType      int64          `json:"creator_type" gorm:"creator_type"`           // 创建人类型
	NameUg           string         `json:"name_ug" gorm:"name_ug"`                     // 名称
	NameZh           string         `json:"name_zh" gorm:"name_zh"`                     // 名称
	StartTime        time.Time      `json:"start_time" gorm:"start_time"`               // 开始时间
	EndTime          time.Time      `json:"end_time" gorm:"end_time"`                   // 结束时间
	FoodId           int64          `json:"food_id" gorm:"food_id"`                     // 美食id
	Discount2Percent float64        `json:"discount2_percent" gorm:"discount2_percent"` // 第2份优惠比例
	Discount3Percent float64        `json:"discount3_percent" gorm:"discount3_percent"` // 第3份优惠比例
	Discount4Percent float64        `json:"discount4_percent" gorm:"discount4_percent"` // 第4份优惠比例
	Discount5Percent float64        `json:"discount5_percent" gorm:"discount5_percent"` // 第5份优惠比例
	State            int            `json:"state" gorm:"state"`                         // 状态 0 新建 1 开启  2 暂停
	CreatedAt        time.Time      `json:"created_at" gorm:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at" gorm:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"`

	Details    []FoodsMultipleDiscountDetail `json:"details" gorm:"foreignKey:ActivityId;references:ID"`      // 优惠阶梯
	Food       RestaurantFoods               `json:"food" gorm:"foreignKey:FoodId;references:ID"`             // 美食
	Creator    Admin                         `json:"creator" gorm:"foreignKey:CreatorId;references:ID"`       // 创建人
	Restaurant Restaurant                    `json:"restaurant" gorm:"foreignKey:RestaurantId;references:ID"` // 店铺
	City       City                          `json:"city" gorm:"foreignKey:CityId;references:ID"`             // 城市
	Area       Area                          `json:"area" gorm:"foreignKey:AreaId;references:ID"`             // 地区

	Discount1SaledCount  int64 `json:"discount1_saled_count"`
	Discount2SaledCount  int64 `json:"discount2_saled_count"`
	Discount3SaledCount  int64 `json:"discount3_saled_count"`
	Discount4SaledCount  int64 `json:"discount4_saled_count"`
	Discount5SaledCount  int64 `json:"discount5_saled_count"`
	TotalSaledCount      int64 `json:"total_saled_count"`
	Discount1SaledAmount int64 `json:"discount1_saled_amount"`
	Discount2SaledAmount int64 `json:"discount2_saled_amount"`
	Discount3SaledAmount int64 `json:"discount3_saled_amount"`
	Discount4SaledAmount int64 `json:"discount4_saled_amount"`
	Discount5SaledAmount int64 `json:"discount5_saled_amount"`
	TotalSaledAmount     int64 `json:"total_saled_amount"`
}

// TableName 表名称
func (*FoodsMultipleDiscountDetailQuery) TableName() string {
	return "t_foods_multiple_discount"
}
