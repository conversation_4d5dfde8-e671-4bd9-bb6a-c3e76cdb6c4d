/*
*
// Code generated by sql2gorm. DO NOT EDIT.
@author: captain
@since: 2022/11/2
@desc:
*
*/
package models

import (
	"time"
)

// 餐厅员工信息表
type MerchantStuff struct {
	ID           uint      `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CityID       int       `gorm:"column:city_id"`       // 地州编号
	AreaID       int       `gorm:"column:area_id"`       // 市县编号
	StreetID     int       `gorm:"column:street_id"`     // 街道编号
	RestaurantID int       `gorm:"column:restaurant_id"` // 餐厅编号
	StuffType    int       `gorm:"column:stuff_type"`    // 餐厅员工类型（1、餐厅管理员；2、餐厅收款员；3、餐厅订单管理员）
	AppID        string    `gorm:"column:app_id"`        // 微信公众平台（小程序）app_id
	Openid       string    `gorm:"column:openid"`        // 员工openId
	Unionid      string    `gorm:"column:unionid"`       // 员工UnionId
	SessionKey   string    `gorm:"column:session_key"`
	UserID       int       `gorm:"column:user_id"`
	Mobile       string    `gorm:"column:mobile"`
	Password     string    `gorm:"column:password"`        // 提现密码
	RealName     string    `gorm:"column:real_name"`       // 收款人真实姓名
	IDNumber     string    `gorm:"column:id_number"`       // 收款人身份证号
	CardNumber   string    `gorm:"column:card_number"`     // 收款银行卡号
	BankCode     int       `gorm:"column:bank_code"`       // 银行编号
	State        uint      `gorm:"column:state;default:1"` // 状态
	CreatedAt    time.Time `gorm:"column:created_at"`      // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at"`      // 修改时间
	DeletedAt    time.Time `gorm:"column:deleted_at"`      // 删除时间
}

func (m *MerchantStuff) TableName() string {
	return "t_merchant_stuff"
}
