package models

import (
	"time"
)

type ShipperTips struct {
	ID             int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	ShipperID      int       `gorm:"column:shipper_id;NOT NULL"`      // 配送员ID
	OrderID        int       `gorm:"column:order_id;NOT NULL"`        // 订单ID
	UserID         int       `gorm:"column:user_id;NOT NULL"`         // 赞赏用户ID
	Amount         int       `gorm:"column:amount;NOT NULL"`          // 赞赏金额
	State          int       `gorm:"column:state;default:0;NOT NULL"` // 0 表示还没支付 1表示已支付
	MchAppid       string    `gorm:"column:mch_appid"`                // 微信支付时APPID
	Mchid          string    `gorm:"column:mchid"`                    // 微信支付时商户号
	ResultCode     string    `gorm:"column:result_code"`              // 微信支付时 状态
	PartnerTradeNo string    `gorm:"column:partner_trade_no"`         // 微信支付时 商户上号
	PaymentNo      string    `gorm:"column:payment_no"`               // 微信支付时 微信商户号
	OutTradeNo     string    `gorm:"column:out_trade_no"`             // 微信支付时 微信商户号
	Openid         string    `gorm:"column:openid"`                   // 微信支付时 微信商户号
	PaymentTime    time.Time `gorm:"column:payment_time"`             // 支付时间
	NonceStr       string    `gorm:"column:nonce_str"`                // 微信支付时签名
	PrepayID       string    `gorm:"column:prepay_id"`                // 与支付ID
	TransactionID  string    `gorm:"column:transaction_id"`           // 微信支付系统生成的订单号
	TotalFee       int       `gorm:"column:total_fee"`                // 赞赏金额
	ExpiredAt      time.Time `gorm:"column:expired_at"`               // 支付过期时间
	PayToShipper   int       `gorm:"column:pay_to_shipper;default:0"` // 是否打款给配送员 0 表示未打款  1表示已打款
	CreatedAt      time.Time `gorm:"column:created_at"`               // 创建时间
	UpdatedAt      time.Time `gorm:"column:updated_at"`               // 修改时间
	DeletedAt      time.Time `gorm:"column:deleted_at"`               // 删除时间
	UserName       string    `gorm:"column:user_name"`                // 删除时间
	UserAvatar     string    `gorm:"column:user_avatar"`              // 删除时间
}

func (m *ShipperTips) TableName() string {
	return "t_shipper_tips"
}
