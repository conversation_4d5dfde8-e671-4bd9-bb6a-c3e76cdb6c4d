package models

import (
	"time"
)

// FoodsMultipleDiscountLog 多分打折 下单日志
type FoodsMultipleDiscountLog struct {
	ID            int64     `json:"id" gorm:"id"`
	OrderId       int64     `json:"order_id" gorm:"order_id"`             // 订单id
	ActivityId    int64     `json:"activity_id" gorm:"activity_id"`       // 多份打折id
	FoodId        int64     `json:"food_id" gorm:"food_id"`               // 美食id
	RestaurantId  int64     `json:"restaurant_id" gorm:"restaurant_id"`   // 店铺id
	OriginalPrice int64     `json:"original_price" gorm:"original_price"` // 美食原价
	Price         int64     `json:"price" gorm:"price"`                   // 打折后的价格
	DiscountIndex int64     `json:"discount_index" gorm:"discount_index"` // 多份打折第几次
	CreatedAt     time.Time `json:"created_at" gorm:"created_at"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"updated_at"`

	Order *Order `json:"order" gorm:"foreignKey:OrderId"`
	OrderToday *OrderToday `json:"order_today" gorm:"foreignKey:OrderId"`
}

type FoodsMultipleDiscountOrderLog struct {
	OrderId          int64     `json:"order_id" gorm:"order_id"`                     // 订单id
	Count           int64     `json:"count"`                     // 数量统计
	TotalPrice      int64     `json:"total_price"`              // 总价格
	TotalOriginalPrice int64  `json:"total_original_price"`     // 总原价

	Order      *Order      `json:"order" gorm:"foreignKey:OrderId"`       // 订单信息
	OrderToday *OrderToday `json:"order_today" gorm:"foreignKey:OrderId"` // 今日订单信息
}


// TableName 表名称
func (*FoodsMultipleDiscountLog) TableName() string {
	return "t_foods_multiple_discount_log"
}
