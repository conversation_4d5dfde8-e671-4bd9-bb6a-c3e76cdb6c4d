package models

import (
	"time"

	"gorm.io/gorm"
)

type LotteryActivityGroupCoupon struct {
	ID           int        `gorm:"primaryKey;column:id;autoIncrement"` // 主键，自增
	NameUg       string     `gorm:"column:name_ug;size:256"`            // 活动 ID
	NameZh       string     `gorm:"column:name_zh;size:256"`            // 抽奖活动等级
	TotalPrice   int        `gorm:"column:total_price"`                 // 总价（分）
	Count        int        `gorm:"column:count"`                       // 优惠券总数
	State        int8       `gorm:"column:state"`                       // 状态
	CouponDetail *string    `gorm:"column:coupon_detail;type:json"`     // 优惠券信息（JSON 类型）
	AdminID      *int       `gorm:"column:admin_id"`                    // 创建人
	CreatedAt    *time.Time `gorm:"column:created_at"`                  // 创建时间
	UpdatedAt    *time.Time `gorm:"column:updated_at;autoUpdateTime"`   // 更新时间
	DeletedAt         gorm.DeletedAt `gorm:"type:timestamp" json:"deleted_at"`
}

func (LotteryActivityGroupCoupon) TableName() string {
	return "t_lottery_activity_group_coupon"
}
