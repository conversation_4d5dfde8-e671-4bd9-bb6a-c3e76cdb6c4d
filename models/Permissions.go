﻿package models

import (
	"time"

	"gorm.io/gorm"
)

type Permissions struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	ParentID          int       `gorm:"column:parent_id"`
	Name          string       `gorm:"column:name;NOT NULL"`
	DisplayName   int    `gorm:"column:display_name"`
	DisplayNameUg string    `gorm:"column:display_name_ug"`
	DisplayNameZh string    `gorm:"column:display_name_zh"`
	Description   int       `gorm:"column:description;default:0;NOT NULL"`
	DescriptionUg string       `gorm:"column:description_ug;default:0;NOT NULL"`
	DescriptionZh string `gorm:"column:description_zh"`
	State        int `gorm:"column:state"`
	URL           string`gorm:"column:url"`
	CreatedAt     time.Time `gorm:"column:created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at"`
}

func (m *Permissions) TableName() string {
	return "permissions"
}
