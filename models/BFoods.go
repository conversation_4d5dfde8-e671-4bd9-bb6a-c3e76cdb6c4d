package models

import (
	"time"

	"gorm.io/gorm"
)

// 餐厅和美食关系表
type BFoods struct {
	ID                        int            `gorm:"column:id;primary_key;AUTO_INCREMENT"`   // 自增编号
	AllFoodsID                int            `gorm:"column:all_foods_id"` // 美食大类编号
	Image                     string         `gorm:"column:image"` // 美食图片
	Name                      string         `gorm:"column:name"` // 美食名称
	NameUg                    string         `gorm:"column:name_ug"` // 美食名称
	NameZh                    string         `gorm:"column:name_zh"` // 美食名称
	Description               string         `gorm:"column:description"` // 美食简介
	DescriptionUg             string         `gorm:"column:description_ug"` // 美食简介
	DescriptionZh             string         `gorm:"column:description_zh"` // 美食简介
	State                     int            `gorm:"column:state"` // 状态
	CreatedAt                 time.Time      `gorm:"column:created_at"` // 创建时间
	UpdatedAt                 time.Time      `gorm:"column:updated_at"` // 修改时间
	DeletedAt                 gorm.DeletedAt `gorm:"column:deleted_at"` // 删除时间
	AllFoods                  AllFoods       `gorm:"foreignKey:AllFoodsID;references:ID"`
}

func (m *BFoods) TableName() string {
	return "b_foods"
}
