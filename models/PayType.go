package models

import "time"

// 支付方式信息表
type PayType struct {
	ID        int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	Type      int       `gorm:"column:type"`                          // 支付方式类型（0表示现金，1表示餐币，2在线支付）
	Name      int       `gorm:"column:name;NOT NULL"`                 // 支付方式名称
	NameUg    string    `gorm:"column:name_ug"`
	NameZh    string    `gorm:"column:name_zh"`
	Icon      string    `gorm:"column:icon"`                      // 标志
	Weight    int       `gorm:"column:weight;default:0;NOT NULL"` // 排序
	State     int       `gorm:"column:state;default:0;NOT NULL"`  // 状态（0关闭，1开通）
	CreatedAt time.Time `gorm:"column:created_at"`                // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"`                // 修改时间
	DeletedAt time.Time `gorm:"column:deleted_at"`                // 删除时间
}

func (m *PayType) TableName() string {
	return "b_pay_type"
}
