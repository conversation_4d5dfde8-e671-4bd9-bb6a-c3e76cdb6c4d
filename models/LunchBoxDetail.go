package models


type LunchBoxDetail struct {
	ID              int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`         // 自增编号
	OrderID         int       `gorm:"column:order_id;NOT NULL"`                     // 订单编号
	StoreFoodsID    int       `gorm:"column:store_foods_id;NOT NULL"`               // 餐厅美食编号
	Price           uint      `gorm:"column:price;NOT NULL"`                        // 实际价格（单位：分）
	Number          uint      `gorm:"column:number;default:1;NOT NULL"`             // 数量
	LunchBoxID      int       `gorm:"column:lunch_box_id;default:0"`                // 跟美食关联的饭盒编号
	LunchBoxFee     int       `gorm:"column:lunch_box_fee;default:0"`               // 饭盒单价（单位：分）
	LunchBoxCount   int       `gorm:"column:count;default:0"`             // 饭盒数量
	FoodId   int       `gorm:"column:food_id"`             // 饭盒数量
	OriginalPrice   int       `gorm:"column:original_price"`             // 饭盒数量
	Name   string       `gorm:"column:name"`             // 饭盒数量
}

func (m *LunchBoxDetail) TableName() string {
	return "t_order_detail"
}

