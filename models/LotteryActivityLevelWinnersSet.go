package models

import (
	"gorm.io/gorm"
	"time"
)

// 中奖设置记录
type LotteryActivityLevelWinnersSet struct {
	ID                          int      `gorm:"column:id;AUTO_INCREMENT;primary_key"`
	LotteryActivityID           int       `gorm:"column:lottery_activity_id;NOT NULL"`             // 抽奖活动名称
	LotteryActivityLevelID      int      `gorm:"column:lottery_activity_level_id;NOT NULL"`       // 抽奖活动等级ID
	LotteryActivityLevelPrizeID int      `gorm:"column:lottery_activity_level_prize_id;NOT NULL"` // 抽奖活动等级奖品ID
	MinIndex                    int      `gorm:"column:min_index;NOT NULL"`                       // 中奖开始位置
	MaxIndex                    int      `gorm:"column:max_index;NOT NULL"`                       // 中奖结束位置
	PrizeCount                  int      `gorm:"column:prize_count;NOT NULL"`                     // 奖励次数
	CurrentCount                int      `gorm:"column:current_count;NOT NULL"`                   // 当前次数
	AdminID                     int      `gorm:"column:admin_id;NOT NULL"`                        // 创建用户ID
	State                       int      `gorm:"column:state;NOT NULL"`                           // 1 有效 2 关闭
	CreatedAt                   time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL"`
	UpdatedAt                   time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL"`
	DeletedAt                   gorm.DeletedAt `gorm:"column:deleted_at;default:CURRENT_TIMESTAMP"`
	Admin   		Admin `gorm:"foreignKey:id;references:admin_id"`
	LotteryActivity   		LotteryActivity `gorm:"foreignKey:id;references:lottery_activity_id"`

	Level int `gorm:"->" json:"level"` // 忽略创建、更新，只在查询时有效
	PrizeName string `gorm:"->" json:"prize_name"` // 忽略创建、更新，只在查询时有效

}

func (m *LotteryActivityLevelWinnersSet) TableName() string {
	return "t_lottery_activity_level_winners_set"
}
