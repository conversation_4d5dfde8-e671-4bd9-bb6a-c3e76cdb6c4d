package models

import (
	"mulazim-api/models/shipment"
	"time"
)

type TakeOrder struct {
	ID                     int        `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	AdminID                int        `gorm:"column:admin_id;NOT NULL"`     // 配送员编号
	OrderID                int        `gorm:"column:order_id;NOT NULL"`     // 订单编号
	OrderPrice             uint       `gorm:"column:order_price;NOT NULL"`  // 订单总价格（餐费+配送费， 单位：分）
	ConsumeType            uint       `gorm:"column:consume_type;NOT NULL"` // 支付方式（0现金，1在线支付）
	ReasonID               *int       `gorm:"column:reason_id;default:null"`
	IsClearing             int        `gorm:"column:is_clearing;default:0"`              // 是否把现金交给代理商或配送管理员（0未交给，1交完）
	State                  int        `gorm:"column:state;default:1"`                    // 状态（0退回订单，1抢单，2配送失败，3配送完毕，4,管理员后台取消订单，5表示商家拒绝订单 ）
	EstimatedShippingPrice int        `gorm:"column:estimated_shipping_price;default:0"` // 推送时估计的配送费
	CreatedAt              time.Time  `gorm:"column:created_at"`                         // 创建时间
	UpdatedAt              time.Time  `gorm:"column:updated_at"`                         // 修改时间
	DeletedAt              *time.Time `gorm:"column:deleted_at;default:null"`            // 删除时间
	StoreId                int        `gorm:"column:store_id;"`
	Distance               float64    `gorm:"column:distance;"`
	BuildingId             int        `gorm:"column:building_id;"`
	OrderAddress           string     `gorm:"column:order_address;"`
	SetChannel            int      `gorm:"column:set_channel;default:1;NOT NULL"`   // 分配通道：1:自己抢单，2:后台分配，3:智能派单，4:客户指定
	Restaurant  Restaurant  `gorm:"foreignkey:store_id;references:id"`             //
	AddressView AddressView `gorm:"foreignkey:building_id;references:building_id"` //

	PushDetail    ShipperOrderPushDetail   `gorm:"foreignkey:order_id;references:order_id"`
	ShipperIncome []shipment.ShipperIncome `gorm:"foreignkey:order_id;references:order_id"`
	Order Order `gorm:"foreignkey:id;references:order_id"`
}

const (
	TakeOrderStateBack = 0
	TakeOrderStateTake = 1
)
const (
	TakeOrderClearOff = 0 //未完成
	TakeOrderClearOK  = 1 //交完
)

func (m *TakeOrder) TableName() string {
	return "t_take_order"
}
