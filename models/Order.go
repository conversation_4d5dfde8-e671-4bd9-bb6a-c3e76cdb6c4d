package models

// Code generated by sql2gorm. DO NOT EDIT.

import (
	"mulazim-api/models/cms"
	"mulazim-api/models/shipment"
	"mulazim-api/tools"
	"time"
)

// 订单历史表
type Order struct {
	ID                   int        `gorm:"column:id;primary_key;AUTO_INCREMENT"`     // 自增编号
	RandomID             string     `gorm:"column:random_id"`                         // 发给用户的随机ID
	TerminalID           int        `gorm:"column:terminal_id;NOT NULL"`              // 终端编号
	OrderID              string     `gorm:"column:order_id;NOT NULL"`                 // 订单唯一编号
	CategoryID           int        `gorm:"column:category_id;NOT NULL"`              // 商户类目编号
	CityID               int        `gorm:"column:city_id"`                           // 下单城市编号
	AreaID               int        `gorm:"column:area_id"`                           // 区域编号
	StoreID              int        `gorm:"column:store_id;NOT NULL"`                 // 商户编号
	UserID               int        `gorm:"column:user_id;NOT NULL"`                  // 用户编号
	BuildingID           int        `gorm:"column:building_id;NOT NULL"`              // 大厦编号
	OrderAddress         string     `gorm:"column:order_address;NOT NULL"`            // 收餐地址
	Name                 string     `gorm:"column:name;NOT NULL"`                     // 收餐人名称
	Mobile               string     `gorm:"column:mobile;NOT NULL"`                   // 收餐人手机号
	ConsumeType          int        `gorm:"column:consume_type"`                      // 付费类型（0现金,1在线支付）
	PayType              int        `gorm:"column:pay_type"`                          // 用什么付费(1 现金, 2 餐币, 3 支付宝,  4 银联, 5 微信)
	OriginalPrice        uint       `gorm:"column:original_price;NOT NULL"`           // 订单原价格（单位：分）
	Price                uint       `gorm:"column:price;NOT NULL"`                    // 实际价格（单位：分）
	Shipment             uint       `gorm:"column:shipment"`                          // 配送费
	OriginalShipment     uint       `gorm:"column:original_shipment"`                 // 原始配送费
	LunchBoxFee          int        `gorm:"column:lunch_box_fee;default:0"`           // 总饭盒费（单位：分）
	MpProfit             uint       `gorm:"column:mp_profit"`                         // 一个订单的属于平台的总利润
	DealerProfit         int       `gorm:"column:dealer_profit"`                     // 一个订单的属于代理商的总利润
	Cash                 uint       `gorm:"column:cash;default:0;NOT NULL"`           // 现金（单位：分）
	Coin                 uint       `gorm:"column:coin;default:0;NOT NULL"`           // 餐币（单位：分）
	Consume              uint       `gorm:"column:consume;default:0;NOT NULL"`        // 充值金额（单位：分）
	Description          string     `gorm:"column:description;NOT NULL"`              // 订单备注
	Timezone             int        `gorm:"column:timezone;default:6"`                // 预定时间时区：6乌鲁木齐时间，8北京时间
	BookingTime          string     `gorm:"column:booking_time"`                      // 预订时间
	PrintTime            string     `gorm:"column:print_time"`                        // 打印时间
	PayTime              time.Time  `gorm:"column:pay_time"`                          // 付费时间
	DeliveryTakedTime    time.Time  `gorm:"column:delivery_taked_time"`               // 抢订单时间
	DeliveryStartTime    *time.Time `gorm:"column:delivery_start_time"`               // 开始配送时间
	DeliveryEndTime      *time.Time `gorm:"column:delivery_end_time"`                 // 订单结束时间
	RealShipment         int        `gorm:"column:real_shipment"`                     // 订单配送完后配送员能得到的实际配送费
	DeductionFee         int        `gorm:"column:deduction_fee"`                     // 配送扣费金额
	Taked                int        `gorm:"column:taked;default:0"`                   // 表示是否已被抢单（0未抢单，1已抢单）
	ShipperID            int        `gorm:"column:shipper_id;default:0"`              // 配送员编号
	SentSms              int        `gorm:"column:sent_sms;default:0"`                // 是否发送过短信（0没发送，1已发送）
	DeleteFlag           int        `gorm:"column:delete_flag;default:0"`             // 用户是否删除该订单（0正常，1用户已删除）
	RefundType           int        `gorm:"column:refund_type;default:0"`             // 1　金额返回美滋来餐币，2　金额返回用户付款账户
	RefundChanel         int        `gorm:"column:refund_chanel;default:0"`           // 取消订单渠道 （0表示没取消，1 表示实时订单自动被系统取消  2表示从后台取消 3表示商家端取消 4表示 用户端取消）
	Refunded             int        `gorm:"column:refunded;default:0"`                // 是否已退款（0 为退款， 1 已退款），只能取消的订单有用
	IsDouyin             int        `gorm:"column:is_douyin"`                         // 是否从抖音支付
	ShipperCompleteGrant uint       `gorm:"column:shipper_complete_grant;default:0"`  // 配送员完成订单授权（1，没有权限、1有权限）
	IsLuckDrawed         int        `gorm:"column:is_luck_drawed;default:0;NOT NULL"` // 是否参加抽奖活动（0表示没有抽奖、2表示以抽奖）
	IsAutoRefund         int        `gorm:"column:is_auto_refund"`                    // 是否是自动退的订单（只有在实时订单有效1表示人工取消的，1表示系统自动取消的订单）
	IsCommented          int        `gorm:"column:is_commented;default:0"`            // 是否评论
	Distance             float64    `gorm:"column:distance;default:0.00"`             // 距离
	OrderType            int        `gorm:"column:order_type"`                        // 0:表示预订单；1：表示实时订单
	Called               int        `gorm:"column:called"`                            // 表示是否打电话提醒过商家（0表示还没打电话提醒、2表示一打电话提醒
	PayPlatForm          int        `gorm:"column:pay_platform"`                      // 表示是否打电话提醒过商家（0表示还没打电话提醒、2表示一打电话提醒
	State                int        `gorm:"column:state;NOT NULL"`                    // 订单状态（跟b_order_state表关联）
	CreatedAt            time.Time  `gorm:"column:created_at"`                        // 创建时间
	UpdatedAt            time.Time  `gorm:"column:updated_at"`                        // 修改时间
	DeletedAt            time.Time  `gorm:"column:deleted_at"`                        // 删除时间

	OrderPrice          int  `gorm:"column:order_price"`                      // 订单总额
	ResIncome           uint `gorm:"column:res_income;default:0;NOT NULL"`    //店铺收入
	ActualPaid          uint `gorm:"column:actual_paid;default:0;"`           //实际支付金额
	TotalDiscountAmount uint `gorm:"column:total_discount_amount;default:0;"` //实际支付金额


	PriceMarkupInPrice int      `gorm:"column:price_markup_in_price;default:0;"` //成本价 合计
	OrderPriceRes int      `gorm:"column:order_price_res;default:0;"` //餐厅靠单的价格

	CashClearState   int                      `gorm:"column:cash_clear_state;default:0"` // 配送员是否缴纳现金订单款额
	CashClearTime    time.Time                `gorm:"column:cash_clear_time"`            // 配送员缴纳现金时间
	CashClearAdmin   int                      `gorm:"column:cash_clear_admin"`           // 配送员缴纳现金人员
	CashClearChannel int                      `gorm:"column:cash_clear_channel"`         // 1:公众号，2.h5支付 3:配送员客户端
	OrderDetail      []OrderDetail            `gorm:"foreignkey:order_id;references:id"`
	LunchBoxDetail   []LunchBoxDetail         `gorm:"foreignkey:order_id;references:id"` //
	LunchBox         []LunchBoxOrderDetail    `gorm:"foreignkey:order_id;references:id"`
	AddressView      AddressView              `gorm:"foreignkey:building_id;references:building_id"` //
	Restaurant       Restaurant               `gorm:"foreignkey:store_id;references:id"`             //
	PayTypes         PayType                  `gorm:"foreignkey:pay_type;references:id"`             //
	MarketingList    []MarketingOrderLog      `gorm:"foreignkey:order_id;references:id"`
	DeliveryType     int                      `gorm:"column:delivery_type" json:"delivery_type"`
	SelfTakeNumber   int                      `gorm:"column:self_take_number" json:"self_take_number"`
	MarketType       int                      `gorm:"column:market_type" json:"market_type"`
	OrderExtend      OrderExtend              `gorm:"foreignkey:order_id;references:id"` // 订单扩展字段
	PushDetail       ShipperOrderPushDetail   `gorm:"foreignkey:order_id;references:id"` // 订单推送时的数据
	ShipperIncome    []shipment.ShipperIncome `gorm:"foreignkey:order_id;references:id"` // 订单推送时的数据
	// ShipperIncome       int64  `gorm:"foreignkey:order_id;references:id"` // 订单推送时的数据
	OrderOrigin        string             `gorm:"<-:false;column:order_origin;"` // 订单来源
	OrderStateLog      []OrderStateLog    `gorm:"foreignkey:order_id;references:id"`
	RestaurantBuilding RestaurantBuilding `gorm:"foreignkey:building_id;references:id"`

	LateOrders LateOrders `gorm:"foreignkey:order_id;references:id"` // 迟到订单

	Shipper    Admin          `gorm:"foreignkey:shipper_id;references:id"`
	OrderState cms.OrderState `gorm:"foreignkey:state;references:id"`
	User       User           `gorm:"foreignkey:id;references:user_id"`
	Terminal   Terminal       `gorm:"foreignkey:terminal_id;references:id"`

	SerialNumber uint `gorm:"column:serial_number;default:0;NOT NULL"`
	ShipperReward int `gorm:"column:shipper_reward;default:0;NOT NULL"`
	OrderDelay      OrderDelayed          `gorm:"foreignkey:order_id;references:id"`

	MiniGameUserLog      MiniGameUserLog          `gorm:"foreignkey:order_id;references:id"`
	
}

func (m *Order) TableName() string {
	return "t_order"
}

func (m *Order) GetFoodsReadyTime() string {
	if m.OrderExtend.OrderID != 0 && m.OrderExtend.FoodsReadyTime != nil {
		return tools.TimeFormatYmdHis(m.OrderExtend.FoodsReadyTime)
	}
	return ""
}


func (m *Order) GetSpecialOrderShipperIncome() int {
	var price int
	if m.MarketType == 2 {
		var seckillLog SeckillLog
		tools.Db.Model(seckillLog).Where("order_id", m.ID).Preload("Seckill.SeckillMarket").Find(&seckillLog)
		if seckillLog.Seckill.SeckillMarket.ShipperIncome > 0 {
			price = seckillLog.Seckill.SeckillMarket.ShipperIncome
		} else {
			price = 0
		}
	}
	return price
}
