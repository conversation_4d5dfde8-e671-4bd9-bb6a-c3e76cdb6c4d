package models

import (
	"time"

	"gorm.io/gorm"
)

// 优惠券
type Coupon struct {
	ID           int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	NameUg       string    `gorm:"column:name_ug"`                      // 名称维吾尔语
	NameZh       string    `gorm:"column:name_zh"`                      // 名称汉语
	CityID       int       `gorm:"column:city_id"`                      // 城市
	AreaID       int       `gorm:"column:area_id"`                      // 区域
	ResID        int       `gorm:"column:res_id"`                       // 店铺id
	Type         int       `gorm:"column:type;default:1"`               // 类型 1:区域 2:店铺
	TypeObjectID         int       `gorm:"column:type_object_id"`       // type等于 3时 抽奖活动优惠券id
	AdminID      int       `gorm:"column:admin_id"`                     // 管理员id
	MinPrice     int       `gorm:"column:min_price;default:0;NOT NULL"` // 最低门槛
	Price        int       `gorm:"column:price;default:0;NOT NULL"`     // 优惠金额
	Count        int       `gorm:"column:count;default:0;NOT NULL"`     // 数量
	PayAmount    int       `gorm:"column:pay_amount"`                   // 需要支付的金额
	TakenCount   int       `gorm:"column:taken_count;default:0"`        // 领取数量
	SaledCount   int       `gorm:"column:saled_count;default:0"`        // 使用数量
	StartTime    time.Time `gorm:"column:start_time"`                   // 开始时间
	EndTime      time.Time `gorm:"column:end_time"`                     // 结束时间
	StartUseTime time.Time `gorm:"column:start_use_time"`               // 开始使用时间
	EndUseTime   time.Time `gorm:"column:end_use_time"`                 // 结束使用时间
	State        int       `gorm:"column:state;default:0"`              // 0:未开启 1:开启 2:停止 3:过期 4:库存没有了 5:彻底结束等待退剩余的钱 6:彻底结束已退剩余的钱
	PayStatus    int       `gorm:"column:pay_status;default:0"`         // 支付状态 0:未支付 1:已支付 3:已退款(过期后结算退款)
	ChargeID     int       `gorm:"column:charge_id"`                    // 充值id
	Refunded     int       `gorm:"column:refunded;default:0"`           // 是否退款
	CreatedAt    time.Time `gorm:"column:created_at"`                   // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at"`                   // 更新时间
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at"`                   // 删除时间
}

func (m *Coupon) TableName() string {
	return "t_coupon"
}

