package models

import (
	"time"
)

type ShipperRank struct {
	ID                        int64          `gorm:"primaryKey;autoIncrement;not null;column:id" json:"id"`                     // 主键
	AreaID                    int64          `gorm:"column:area_id" json:"area_id"`                                             // 区域ID
	CityID                    int64          `gorm:"column:city_id" json:"city_id"`                                             // 城市ID
	ShipperID                 int64          `gorm:"column:shipper_id" json:"shipper_id"`                                       // 配送员ID
	BaseScore                 int64          `gorm:"column:base_score" json:"base_score"`                                       // 基础分
	OrderTotalCount           int64          `gorm:"column:order_total_count" json:"order_total_count"`                         // 总配送订单个数
	DeliveredOnTimeOrderCount int64          `gorm:"column:delivered_on_time_order_count" json:"delivered_on_time_order_count"` // 准时送达订单个数
	DeliveredOnTime           float64        `gorm:"column:delivered_on_time" json:"delivered_on_time"`                         // 准时送达获得的分数
	PositiveReviewsCount      int64          `gorm:"column:positive_reviews_count" json:"positive_reviews_count"`               // 好评个数
	PositiveReviewsScore      float64        `gorm:"column:positive_reviews_score" json:"positive_reviews_score"`               // 好评获得的分数
	MildLatenessCount         int64          `gorm:"column:mild_lateness_count" json:"mild_lateness_count"`                     // 轻度迟到订单个数（5分钟之内）
	MildLatenessDeduct        float64        `gorm:"column:mild_lateness_deduct" json:"mild_lateness_deduct"`                   // 轻度超时扣分
	ModerateLatenessCount     int64          `gorm:"column:moderate_lateness_count" json:"moderate_lateness_count"`             // 中度超时订单个数（5-10分钟）
	ModerateLatenessDeduct    float64        `gorm:"column:moderate_lateness_deduct" json:"moderate_lateness_deduct"`           // 中度超时扣分
	SevereLatenessCount       int64          `gorm:"column:severe_lateness_count" json:"severe_lateness_count"`                 // 严重超时订单个数
	SevereLatenessDeduct      float64        `gorm:"column:severe_lateness_deduct" json:"severe_lateness_deduct"`               // 严重超时扣分（10分钟以上）
	NegativeReviewsCount      int64          `gorm:"column:negative_reviews_count" json:"negative_reviews_count"`               // 差评订单个数
	NegativeReviewsDeduct     float64        `gorm:"column:negative_reviews_deduct" json:"negative_reviews_deduct"`             // 差评扣分
	ComplaintsCount           int64          `gorm:"column:complaints_count" json:"complaints_count"`                           // 投诉订单个数
	ComplaintsDeduct          float64        `gorm:"column:complaints_deduct" json:"complaints_deduct"`                         // 投诉扣分
	EarlyDeliveryCount        int64          `gorm:"column:early_delivery_count" json:"early_delivery_count"`                   // 早送订单个数（20分钟）
	EarlyDeliveryDeduct       float64        `gorm:"column:early_delivery_deduct" json:"early_delivery_deduct"`                 // 早送扣分
	FinalScore                float64        `gorm:"column:final_score" json:"final_score"`                                     // 最终分数
	MaxOrders                 int64          `gorm:"column:max_orders" json:"max_orders"`                                       // 能分配的最多订单个数
	Rank                      int64          `gorm:"column:rank" json:"rank"`                                                   // 配送员等级
	CreatedAt                 time.Time      `gorm:"column:created_at" json:"created_at"`                                       // 创建时间
	UpdatedAt                 time.Time      `gorm:"autoUpdateTime;column:updated_at" json:"updated_at"`                        // 修改时间
}

func (ShipperRank) TableName() string {
	return "t_shipper_rank"
}
