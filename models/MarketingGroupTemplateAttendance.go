package models

import (
	"time"

	"gorm.io/gorm"
)

const (
	MarketingGroupTemplateAttendenceStateNew      = 1
	MarketingGroupTemplateAttendenceStateSend     = 2
	MarketingGroupTemplateAttendenceStateJoined   = 3
	MarketingGroupTemplateAttendenceStateRejected = 4
)

// 商家满减活动模板表
type MarketingGroupTemplateAttendance struct {
	ID           int        `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"` // 编号 编号
	TemplateId   int        `gorm:"column:template_id" json:"template_id"`          // 模板id
	MarketingId  int        `gorm:"column:marketing_id" json:"marketing_id"`        // 活动id
	RestaurantId int        `gorm:"column:restaurant_id" json:"restaurant_id"`      // 店铺id
	AdminId      int        `gorm:"column:admin_id" json:"admin_id"`                // 管理员id
	SendCount    int        `gorm:"column:send_count" json:"send_count"`            // 发送次数
	State        int        `gorm:"column:state" json:"state"`                      // 1：新建，2：已发送邀请，3：已参加，4：拒绝参加
	CreatedAt    *time.Time `gorm:"column:created_at" json:"created_at"`            // 创建时间
	UpdatedAt    *time.Time `gorm:"column:updated_at" json:"updated_at"`            // 更新时间
	DeleteAt    gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`

	Marketing Marketing              `gorm:"foreignKey:id;references:marketing_id"`
	Template  MarketingGroupTemplate `gorm:"foreignKey:template_id;references:id"`
	Restaurant  *Restaurant `gorm:"foreignKey:id;references:restaurant_id"`
}

type MarketingGroupTemplateAttendanceAggs struct {
	TemplateId    int `gorm:"column:template_id" json:"template_id"`       // 模板id
	TotalCount    int `gorm:"column:total_count" json:"total_count"`       // 总数
	AcceptedCount int `gorm:"column:accepted_count" json:"accepted_count"` // 已参加数
}

func (m *MarketingGroupTemplateAttendanceAggs) TableName() string {
	return "t_marketing_group_template_attendence"
}

func (m *MarketingGroupTemplateAttendance) TableName() string {
	return "t_marketing_group_template_attendence"
}
