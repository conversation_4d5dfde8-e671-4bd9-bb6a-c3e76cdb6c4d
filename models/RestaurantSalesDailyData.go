package models

import (
	"time"
)

// 餐厅营业日日账单
type RestaurantSalesDailyData struct {
	ID              int        `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CategoryID      int        `gorm:"column:category_id;default:0;NOT NULL"`
	CityID          int        `gorm:"column:city_id"` // 城市编号
	AreaID          int        `gorm:"column:area_id"` // 区域编号
	RestaurantID    int        `gorm:"column:restaurant_id;default:0;NOT NULL"`
	Date            time.Time  `gorm:"column:date;NOT NULL"`                  // 日期[年-月-日]
	OrderCount      int        `gorm:"column:order_count;default:0;NOT NULL"` // 订单量
	OrderCountMp    int        `gorm:"column:order_count_mp;default:0"`
	OrderCountSelf  int        `gorm:"column:order_count_self;default:0"`
	Cash            int        `gorm:"column:cash;default:0;NOT NULL"`     // 现金
	Coin            int        `gorm:"column:coin;default:0;NOT NULL"`     // 餐币
	Alipay          int        `gorm:"column:alipay;default:0;NOT NULL"`   // 支付宝
	Unionpay        int        `gorm:"column:unionpay;default:0;NOT NULL"` // 银联
	Yunshanfu       int        `gorm:"column:yunshanfu;default:0"`
	Wechat          int        `gorm:"column:wechat;default:0;NOT NULL"`        // 微信
	Shipment        int        `gorm:"column:shipment;NOT NULL"`                // 配送费
	LunchBoxFee     int        `gorm:"column:lunch_box_fee;default:0;NOT NULL"` // 饭盒费
	MpProfit        int        `gorm:"column:mp_profit;NOT NULL"`               // 一个订单的属于平台的总利润
	DealerProfit    int        `gorm:"column:dealer_profit;NOT NULL"`           // 一个订单的属于代理商的总利润
	AgentPay        int        `gorm:"column:agent_pay;default:0"`
	CustomerPay     int        `gorm:"column:customer_pay;default:0"`
	TotalMoneyMp    int        `gorm:"column:total_money_mp;default:0"`
	TotalMoneySelf  int        `gorm:"column:total_money_self;default:0"`
	CreatedAt       time.Time  `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL"`
	UpdatedAt       time.Time  `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL"`
	DeletedAt       *time.Time `gorm:"column:deleted_at"`
	TotalMoney      int        `gorm:"column:total_money;default:0"`
	IncorrectAmount int        `gorm:"column:incorrect_amount;default:0"`
}

func (m *RestaurantSalesDailyData) TableName() string {
	return "restaurant_sales_daily_data"
}
