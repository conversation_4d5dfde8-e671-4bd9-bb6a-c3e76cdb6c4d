﻿package models

import (
	"mulazim-api/tools"
	"time"

	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

const (
	PriceMarkupStateWaitForPayment         = 1
	PriceMarkupStateWaitForMerchantConfirm = 2
	PriceMarkupStateMerchantHasConfirmed   = 3
	PriceMarkupStateMerchantRefuse         = 4
	PriceMarkupStateRefunded               = 5

	PriceMarkupRunningStateNormal = 1
	PriceMarkupRunningStatePause  = 2

	PriceMarkupPayedNotPaid = 0
	PriceMarkupPayedIsPaid  = 1

	PriceMarkupRefundedNotRefunded = 0
	PriceMarkupRefundedIsRefunded  = 1
)

type PriceMarkupFood struct {
	ID                int             `json:"id" gorm:"id;primaryKey;autoIncrement"`                   // 自增编号
	CityID            int             `json:"city_id" gorm:"city_id"`                                  // 城市编号
	AreaID            int             `json:"area_id" gorm:"area_id"`                                  // 区域编号
	RestaurantID      int             `json:"restaurant_id" gorm:"restaurant_id;NOT NULL"`             // 餐厅编号
	RestaurantFoodsID int             `json:"restaurant_foods_id" gorm:"restaurant_foods_id;NOT NULL"` // 餐厅美食编号
	SpecID            int             `json:"spec_id" gorm:"spec_id"`                                  // 规格ID
	StartDate         *time.Time      `json:"start_date" gorm:"start_date"`                            // 活动的开始时间  比如:2015-10-15 06:00
	EndDate           *time.Time      `json:"end_date" gorm:"end_date"`                                // 活动的结束时间 比如:2015-11-15 23:00
	TotalCount        int             `json:"total_count" gorm:"total_count"`                          // 库存数量
	State             int             `json:"state" gorm:"state;NOT NULL"`                             // 1:待支付,可修改,2:带商家确认,可修改,3:商家已确认,不可修改,4:商家拒绝,可修改,5:已退款,不可修改
	RunningState      int             `json:"running_state" gorm:"running_state;NOT NULL"`             // 1:正常,2:暂停
	InPrice           int             `json:"in_price" gorm:"in_price"`                                // 成本价
	CreatorID         int             `json:"creator_id" gorm:"creator_id"`                            // 创建者ID
	CreatedAt         time.Time       `json:"created_at" gorm:"created_at"`                            // 创建时间
	PayTime           *time.Time      `json:"pay_time" gorm:"pay_time"`                                // 支付时间
	ReviewAt          *time.Time      `json:"review_at" gorm:"review_at"`                              // 审核时间
	Payed          	  int             `json:"payed" gorm:"payed"`                                      // 支付状态 0:未支付 1:已支付
	Refunded          int             `json:"refunded" gorm:"refunded"`                                // 退款状态 0未退款 1:已退款
	RefundTime      *time.Time      `json:"refund_time" gorm:"refund_time"`                           //退款时间
	StopTime       *time.Time      `json:"stop_time" gorm:"stop_time"`								   //活动暂停时间
	OperationLog  *string            `json:"operation_log" gorm:"operation_log"`                       //操作日志
	
	
	
	UpdatedAt         *time.Time      `json:"updated_at" gorm:"updated_at"`                            // 修改时间
	DeletedAt         gorm.DeletedAt  `json:"deleted_at" gorm:"deleted_at"`                            // 删除时间
	City              City            `gorm:"foreignkey:city_id;references:id"`
	Area              Area            `gorm:"foreignkey:area_id;references:id"`
	Restaurant        Restaurant      `gorm:"foreignkey:restaurant_id;references:id"`
	RestaurantFoods   RestaurantFoods `gorm:"foreignkey:restaurant_foods_id;references:id"`
	Seckill                 []Seckill              `gorm:"foreignkey:price_markup_id"`
	FoodsPreferential       []FoodsPreferential    `gorm:"foreignkey:price_markup_id"`
	Admin                   Admin                  `gorm:"foreignkey:creator_id;references:id"`
	FoodsPreferentialLog    []FoodsPreferentialLog `gorm:"foreignkey:price_markup_id;references:id"`
	SeckillLog              []SeckillLog           `gorm:"foreignkey:price_markup_id;references:id"`
	PriceMarkupFoodLog      []PriceMarkupFoodLog   `gorm:"foreignkey:price_markup_id;references:id"`
	SeckillLogGroupBySelect     SeckillLogGroupBySelect   `gorm:"foreignkey:price_markup_id;references:id"`
	FoodsPreferentialLogGroupBySelect     FoodsPreferentialLogGroupBySelect   `gorm:"foreignkey:price_markup_id;references:id"`
	PriceMarkupFoodLogSum     PriceMarkupFoodLog   `gorm:"foreignkey:price_markup_id;"`
	FoodSpec                  FoodSpec           `gorm:"foreignkey:spec_id;references:id"`

	FoodType     uint8     `gorm:"column:food_type;default:0;not null"` // 美食类型 (0: 普通美食, 1: 规格, 2: 规格美食)
	SelectedSpec *FoodSpec `gorm:"foreignkey:id;references:spec_id"`    // 已选规格数据
}

type PriceMarkupFoodDetailModel struct {
	PriceMarkupFood
	PreferentialSailedCount int `json:"preferential_sailed_count"`
	SecKillSailedCount      int `json:"sec_kill_sailed_count"`
	TotalSailedCount        int `json:"total_sailed_count"`
}
func (m *PriceMarkupFood) TableName() string {
	return "t_price_markup_food"
}


func (m *PriceMarkupFood) CanUseCount() int {
	// 1.还每结束的秒杀活动销售数量和库存 select sum(total_count),sum(saled_count) from b_seckill where price_markup_id = 18 and state = 1 and end_time > NOW() and deleted_at is NULL;
	var seckill Seckill
	tools.Db.Model(Seckill{}).
		Select("ifnull(sum(total_count),0) as total_count,ifnull(sum(saled_count),0) as saled_count").
		Where("price_markup_id = ?", m.ID).
		Where("state = ?", 1).
		Where("end_time > ?", carbon.Now()).
		Scan(&seckill)

	// 已结束的销售数量
	var saledCount int64
	tools.Db.Model(SeckillLog{}).
		Select("ifnull(sum(saled_count),0) as saled_count").
		Where("price_markup_id =?", m.ID).
		Where("state =?", 1).
		Scan(&saledCount)

	seckillUseCount := saledCount - seckill.SaledCount + seckill.TotalCount


	// 2. 查询 t_foods_preferential
	var totalPreferentialCount int64
	tools.Db.Model(FoodsPreferential{}).
		Select("IFNULL(SUM(CASE WHEN state = 1 AND CONCAT(DATE(end_date_time), ' ', end_time) > ? THEN price_markup_count ELSE price_markup_saled_count END),0) as totalPreferentialCount", time.Now()).
		Where("price_markup_id = ?", m.ID).
		Scan(&totalPreferentialCount)
	canUseCount := m.TotalCount - tools.ToInt(seckillUseCount+totalPreferentialCount)
	if canUseCount<0 {
		canUseCount = 0
	}
	return canUseCount
}