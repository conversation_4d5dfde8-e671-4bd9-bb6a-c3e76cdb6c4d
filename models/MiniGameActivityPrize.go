package models

import (
	"gorm.io/gorm"
	"time"
)

// MiniGameActivityPrize 小程序游戏活动奖品
type MiniGameActivityPrize struct {
	ID         int            `json:"id" gorm:"id"`
	ActivityId int            `json:"activity_id" gorm:"activity_id"` // 活动ID
	NameUg     string         `json:"name_ug" gorm:"name_ug"`         // 奖品名称维吾尔语
	NameZh     string         `json:"name_zh" gorm:"name_zh"`         // 奖品名称汉语
	ImageUg    string         `json:"image_ug" gorm:"image_ug"`       // 奖品图片维语
	ImageZh    string         `json:"image_zh" gorm:"image_zh"`       // 奖品图片汉语
	Level      int            `json:"level" gorm:"level"`             // 奖品等级
	Price      int            `json:"price" gorm:"price"`             // 奖品价格
	Model      string         `json:"model" gorm:"model"`             // 规格
	State      int            `json:"state" gorm:"state"`             // 状态 0:未开启 1:开启
	AdminId    int            `json:"admin_id" gorm:"admin_id"`       // 创建人
	CreatedAt  time.Time      `json:"created_at" gorm:"created_at"`   // 创建时间
	UpdatedAt  time.Time      `json:"updated_at" gorm:"updated_at"`   // 更新时间
	DeletedAt  gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"`   // 删除时间
}

// TableName 表名称
func (*MiniGameActivityPrize) TableName() string {
	return "t_mini_game_activity_prize"
}
