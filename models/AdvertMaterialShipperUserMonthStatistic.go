package models

import (
	"time"
)

type AdvertMaterialShipperUserMonthStatistic struct {
	ID                          int64                     `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	AdvertMaterialCategoryId    int64                     `gorm:"column:advert_material_category_id"`     // 推广材料分类
	AdvertMaterialShipperUserId int64                     `gorm:"column:advert_material_shipper_user_id"` // 邀请记录ID
	AreaId                      int64                     `gorm:"column:area_id"`                         // 区域ID
	CityId                      int64                     `gorm:"column:city_id"`                         // 城市ID
	CreatedAt                   time.Time                 `gorm:"column:created_at"`                      // 创建日期
	InviteUserFee               int64                     `gorm:"column:invite_user_fee"`                 // 新用户奖励
	Month                       string                    `gorm:"column:month"`                           // 日期
	OrderCount                  int64                     `gorm:"column:order_count"`                     // 邀请的用户下单数
	ShipperId                   int64                     `gorm:"column:shipper_id"`                      // 配送员ID
	TotalOrderPrice             int64                     `gorm:"column:total_order_price"`               // 邀请的用户下单总额
	TotalOrderTipsFee           int64                     `gorm:"column:total_order_tips_fee"`            // 邀请的用户订单奖励总额
	UpdatedAt                   time.Time                 `gorm:"column:updated_at"`                      // 更新日期
	UserId                      int64                     `gorm:"column:user_id"`                         // 邀请的用户ID
	User                        User                      `gorm:"foreignkey:id;references:user_id"`
	AdvertMaterialShipperUser   AdvertMaterialShipperUser `gorm:"foreignkey:id;references:advert_material_shipper_user_id"`
	AdvertMaterialCategory      AdvertMaterialCategory    `gorm:"foreignkey:id;references:advert_material_category_id"`
}

func (m *AdvertMaterialShipperUserMonthStatistic) TableName() string {
	return "t_advert_material_shipper_user_month_statistic"
}
