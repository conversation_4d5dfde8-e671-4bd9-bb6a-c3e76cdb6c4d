package models

import (
	"mulazim-api/models/cms"
	"mulazim-api/tools"
	"time"

	"gorm.io/gorm"
)


const RESTAURANT_STATE_OK = 1
const RESTAURANT_STATE_TIME_OUT = 2
const RESTAURANT_STATE_OFF = 0

type Restaurant struct {
	ID                int          `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	Tag               string       `gorm:"column:tag;NOT NULL"`                  // 餐厅唯一标识（自动生成）
	DealerID          int          `gorm:"column:dealer_id"`                     // 餐厅所属区域的代理编号
	CityID            int          `gorm:"column:city_id"`                       // 城市编号
	AreaID            int          `gorm:"column:area_id"`                       // 区域编号
	StreetID          int          `gorm:"column:street_id;NOT NULL"`            // 街道编号
	Address           string       `gorm:"column:address;NOT NULL"`              // 餐厅具体地址
	AddressUg         string       `gorm:"column:address_ug"`
	AddressZh         string       `gorm:"column:address_zh"`
	Name              string       `gorm:"column:name;NOT NULL"`        // 餐厅名称
	NameUg            string       `gorm:"column:name_ug"`              // 餐厅维文名称
	NameZh            string       `gorm:"column:name_zh"`              // 餐厅中文名称
	Description       int          `gorm:"column:description;NOT NULL"` // 说明
	DescriptionUg     string       `gorm:"column:description_ug"`
	DescriptionZh     string       `gorm:"column:description_zh"`
	FullNameZh        string       `gorm:"column:full_name_zh"`                         // 餐厅中文全名
	FullNameUg        string       `gorm:"column:full_name_ug"`                         // 餐厅维文全名
	Famous            int          `gorm:"column:famous;default:0;NOT NULL"`            // 是否著名餐厅(0,否， 1是)
	Logo              string       `gorm:"column:logo;NOT NULL"`                        // 餐厅logo
	Lat               float64      `gorm:"column:lat;NOT NULL"`                         // 纬度
	Lng               float64      `gorm:"column:lng;NOT NULL"`                         // 经度
	AdminTel          string       `gorm:"column:admin_tel"`                            // 订单处理人
	Tel               string       `gorm:"column:tel"`                                  // 订餐电话
	Tel2              string       `gorm:"column:tel2"`                                 // 包厢预订电话
	Tel3              string       `gorm:"column:tel3"`                                 // 住宿预订电话
	Tel4              string       `gorm:"column:tel4"`                                 // 主管联系电话
	Tel5              string       `gorm:"column:tel5"`                                 // 老板电话
	OpenTime          string       `gorm:"column:open_time;NOT NULL"`                   // 餐厅开业时间
	CloseTime         string       `gorm:"column:close_time;NOT NULL"`                  // 餐厅关闭时间
	BeginTime         string       `gorm:"column:begin_time;NOT NULL"`                  // 配送开始时间
	EndTime           string       `gorm:"column:end_time;NOT NULL"`                    // 配送结束时间
	HasStamp          int          `gorm:"column:has_stamp;default:2"`                  // 是否提供税票 ( 1 提供  2  不提供)
	Mark              int          `gorm:"column:mark"`                                 // 附近标志性建筑物
	ShipperAvg        float64      `gorm:"column:shipper_avg;NOT NULL"`                 // 配送员满意度
	MonthOrderCount   int          `gorm:"column:month_order_count;default:0;NOT NULL"` // 前30天订单量
	PrintLang         int          `gorm:"column:print_lang;default:1;NOT NULL"`        // 订单语言类型
	Weight            int          `gorm:"column:weight;default:0"`                     // 排序
	OpenID            string       `gorm:"column:open_id"`                              // 商家微信openId
	Type              int          `gorm:"column:type;default:1;NOT NULL"`              // 1表示餐厅、2表示超市、3表示便利店
	StarAvg           float64      `gorm:"column:star_avg;default:5.0"`
	FoodStarAvg       float64      `gorm:"column:food_star_avg;default:5.0"`
	BoxStarAvg        float64      `gorm:"column:box_star_avg;default:5.0"`
	CommentCount      int          `gorm:"column:comment_count;default:0;NOT NULL"`        // 评论次数（餐厅的有评论的订单总数）
	State             int          `gorm:"column:state;default:0;NOT NULL"`                // 状态（0关闭，1开通，2休息）
	CanSelfTake       int          `gorm:"column:can_self_take;default:0;NOT NULL"`        // 是否可以自取订单（0关闭，1开通）
	RestaurantType    int          `gorm:"column:restaurant_type;default:1;"`              // 餐厅详情页面的美食显示样式（1,2,3）
	StartBusiness     time.Time    `gorm:"column:start_business"`                          // 开始营业时间
	LastCommentReaded time.Time    `gorm:"column:last_comment_readed"`                     // 最后读取评论的时间
	IsCashouting      int          `gorm:"column:is_cashouting;default:0;NOT NULL"`        // 是否在提现
	SelfOfferLunchBox int          `gorm:"column:self_offer_lunch_box;default:1;NOT NULL"` // 是否提供饭盒（0表示代理提供，1表示餐厅提供）
	LastQueryTime     time.Time    `gorm:"column:last_query_time"`                         // 最终查询时间
	DeviceToken       string       `gorm:"column:device_token"`                            // 极光推送ID
	CreatedAt         time.Time    `gorm:"column:created_at"`                              // 创建时间
	UpdatedAt         time.Time    `gorm:"column:updated_at"`                              // 修改时间
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at"`                                    // 删除时间
	CashPlatform      int          `gorm:"-" json:"cash_platform" `                       //
	DisplayShipment   int          `gorm:"column:display_shipment"`
	DisplayMerchantProfit   int          `gorm:"column:display_merchant_profit"`
	PushDevices       []PushDevice `gorm:"polymorphic:User;polymorphicValue:t_restaurant"` // 激光设备
	City              cms.City     `gorm:"foreignKey:city_id;references:id"` // 地区
	Area              Area         `gorm:"foreignKey:area_id;references:id"` // 区域
	Foods             []RestaurantFoods `gorm:"foreignKey:RestaurantID;references:ID"`
	RestaurantPrinter           []RestaurantPrinter         `gorm:"foreignKey:RestaurantID;references:ID"`
	FoodsGroup   []FoodsGroup       `gorm:"foreignKey:RestaurantID;references:ID"`
}

func (m *Restaurant) TableName() string {
	return "t_restaurant"
}

func RestaurantScopeOffUser(adminType int, adminId int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if adminType == 1 {
			return db
		}
		dbTemp := tools.Db
		var storeIds []int

		dbTemp.Model(cms.AdminStore{}).
			Where("admin_id = ?", adminId).
			Group("store_id").
			Pluck("store_id", &storeIds)
		return db.Where("t_restaurant.id IN (?)", storeIds)
	}
}

func ActiveRestaurant() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("t_restaurant.state = 1")
	}
}

func ValidRestaurant() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("t_restaurant.state > 0")
	}
}
