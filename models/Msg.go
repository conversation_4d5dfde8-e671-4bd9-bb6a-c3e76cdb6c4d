package models

import (

"gorm.io/gorm"
"time"
)

// TMsg represents the table `t_msg` in Go with GORM annotations.
type Msg struct {
	ID            int           `gorm:"primaryKey;autoIncrement;column:id"`
	Mobiles       string         `gorm:"type:text;not null;comment:'用户手机号码';column:mobiles"`
	TerminalID    int         `gorm:"default:null;comment:'客户端编号';column:terminal_id"`
	Type          int          `gorm:"not null;comment:'1 是验证码，2 是订单通知 ，3是后台通知，4 是redis连接失败通知，5是订单无法打印';column:type"`
	Content       string         `gorm:"type:varchar(512);not null;comment:'短信内容';column:content"`
	ContentLength int            `gorm:"not null;comment:'短信长度';column:content_length"`
	Count         int           `gorm:"default:null;comment:'短信数量';column:count"`
	Price         float64       `gorm:"type:decimal(11,3);default:null;comment:'消费总额 单位：元';column:price"`
	State         int          `gorm:"default:1;comment:'0,失败的短信， 1 成功的短信';column:state"`
	Reason        string         `gorm:"type:varchar(256);default:'';comment:'失败原因（短信平台返回的原因）';column:reason"`
	CreatedAt     time.Time `gorm:"column:created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at"`
	DeletedAt     gorm.DeletedAt  `gorm:"column:deleted_at"`
}

func (m *Msg) TableName() string {
	return "t_msg"
}
