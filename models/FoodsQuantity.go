package models

import (
	"time"

	"gorm.io/gorm"
)

// 餐厅和美食关系表
type FoodsQuantity struct {
	ID                        int            `gorm:"column:id;primary_key;AUTO_INCREMENT"`   // 自增编号
	NameUg                    string         `gorm:"column:name_ug"` // 美食名称
	NameZh                    string         `gorm:"column:name_zh"` // 美食名称
	State                     int            `gorm:"column:state"` // 状态
	CreatedAt                 time.Time      `gorm:"column:created_at"` // 创建时间
	UpdatedAt                 time.Time      `gorm:"column:updated_at"` // 修改时间
	DeletedAt                 gorm.DeletedAt `gorm:"column:deleted_at"` // 删除时间
}

func (m *FoodsQuantity) TableName() string {
	return "b_food_quantity"
}
