package models

import (
	"time"

	"gorm.io/gorm"
)

//'状态 0:未开启 1:开启 2:暂停 3:过期'
const LOTTERY_ACTIVITY_STATE_CLOSED = 0
const LOTTERY_ACTIVITY_STATE_PAUSE = 2
const LOTTERY_ACTIVITY_STATE_OPEN = 1
const LOTTERY_ACTIVITY_STATE_EXPIRED = 3

const(
	LotteryActivityTypeLottery = 1
	LotteryActivityTypeOrderRanking = 2

	LotteryActivityStateClose = 0
	LotteryActivityStateOpen = 1
	LotteryActivityStatePause = 2
	LotteryActivityStateExpired = 3

	LotteryActivityIsPlatform = 1
	LotteryActivityIsNotPlatform = 0
)


type LotteryActivity struct {
	ID             int           `gorm:"primaryKey;autoIncrement;column:id"`
	NameUg         string         `gorm:"column:name_ug"`                           // 名称维吾尔语
	NameZh         string         `gorm:"column:name_zh"`                           // 名称汉语
	StartTime      *time.Time     `gorm:"column:start_time"`                        // 开始时间
	EndTime        *time.Time     `gorm:"column:end_time"`                          // 结束时间
	ShowPosition   *int         `gorm:"column:show_position"`                      // 显示位置
	State          int          `gorm:"default:0;column:state"`                    // 状态
	AdminID        *int           `gorm:"column:admin_id"`                           // 创建人
	MinPrizeOrderPrice	int `gorm:"column:min_prize_order_price"`
	MaxBuyCount  int `gorm:"column:max_buy_count"`
	ShareCount    int `gorm:"column:share_count"`
	ShareMinOrderPrice   int `gorm:"column:share_min_order_price"`
	ShareImage   string `gorm:"column:share_image"`
	
	CouponEndTimeType   int `gorm:"column:coupon_end_time_type"`
	CouponInvalidDate   int `gorm:"column:coupon_invalid_date"`
	CouponEndTime   *time.Time `gorm:"column:coupon_end_time"`
	
	AreaID         *int           `gorm:"column:area_id"`                           // 区域ID
	CityID         *int           `gorm:"column:city_id"`                           // 城市ID
	IsPlatformActivity *int `gorm:"column:is_platform_activity"`                    // 是否平台活动
	Type               int `gorm:"column:type"`                                    // 活动类型
	AnnounceBeginTime *time.Time `gorm:"column:announce_begin_time"` // 活动预告开始时间
	ResultShowEndTime *time.Time `gorm:"column:result_show_end_time"` // 结果展示结束时间
	ShareCoverImages      string `gorm:"column:share_cover_images"`              // 分享封面图片
	ShareWinnerImageUg    string `gorm:"column:share_winner_image_ug"`              // 中奖用户分享图片维文
	ShareWinnerImageZh    string `gorm:"column:share_winner_image_zh"`              // 中奖用户分享图片中文
	AnnounceEntranceImageUg string `gorm:"column:announce_entrance_image_ug"`       // 预告入口图片维文
	AnnounceEntranceImageZh string `gorm:"column:announce_entrance_image_zh"`       // 预告入口图片中文
	AnnouncePageImageUg   string `gorm:"column:announce_page_image_ug"`             // 预告页面图片维文
	AnnouncePageImageZh   string `gorm:"column:announce_page_image_zh"`             // 预告页面图片中文
	RuleUg               string `gorm:"column:rule_ug"`                             // 维文规则
	RuleZh               string `gorm:"column:rule_zh"`                             // 中文规则
	PageShareCount       int    `gorm:"column:page_share_count"`                    // 分享次数
	PageViewCount        int    `gorm:"column:page_view_count"`                     // 浏览次数

	CreatedAt      time.Time      `gorm:"column:created_at"`                         // 创建时间
	UpdatedAt      time.Time      `gorm:"column:updated_at;autoUpdateTime"`          // 更新时间
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at"`                         // 删除时间
	Admin   		Admin `gorm:"foreignKey:id;references:admin_id"`
	LotteryActivityGroupID  int           `gorm:"column:lottery_activity_group_coupon_id" json:"lottery_activity_group_coupon_id"`

	LotteryActivityLevel []LotteryActivityLevel `gorm:"foreignKey:lottery_activity_id;references:id"`
}

func (m *LotteryActivity) TableName() string {
	return "t_lottery_activity"
}



type LotteryActivityOrderRanking struct {
	ID             int           `gorm:"primaryKey;autoIncrement;column:id"`
	NameUg         string         `gorm:"column:name_ug"`                           // 名称维吾尔语
	NameZh         string         `gorm:"column:name_zh"`                           // 名称汉语
	StartTime      *time.Time     `gorm:"column:start_time"`                        // 开始时间
	EndTime        *time.Time     `gorm:"column:end_time"`                          // 结束时间
	ShowPosition   *int         `gorm:"column:show_position"`                      // 显示位置
	State          int          `gorm:"default:0;column:state"`                    // 状态
	AdminID        *int           `gorm:"column:admin_id"`                           // 创建人
	MinPrizeOrderPrice	int `gorm:"column:min_prize_order_price"`
	MaxBuyCount  int `gorm:"column:max_buy_count"`
	ShareCount    int `gorm:"column:share_count"`
	ShareMinOrderPrice   int `gorm:"column:share_min_order_price"`
	ShareImage   string `gorm:"column:share_image"`
	
	CouponEndTimeType   int `gorm:"column:coupon_end_time_type"`
	CouponInvalidDate   int `gorm:"column:coupon_invalid_date"`
	CouponEndTime   *time.Time `gorm:"column:coupon_end_time"`
	
	AreaID         *int           `gorm:"column:area_id"`                           // 区域ID
	CityID         *int           `gorm:"column:city_id"`                           // 城市ID
	IsPlatformActivity *int `gorm:"column:is_platform_activity"`                    // 是否平台活动
	Type               int `gorm:"column:type"`                                    // 活动类型
	AnnounceBeginTime *time.Time `gorm:"column:announce_begin_time"` // 活动预告开始时间
	ResultShowEndTime *time.Time `gorm:"column:result_show_end_time"` // 结果展示结束时间
	ShareCoverImages      string `gorm:"column:share_cover_images"`              // 分享封面图片
	ShareWinnerImageUg    string `gorm:"column:share_winner_image_ug"`              // 中奖用户分享图片维文
	ShareWinnerImageZh    string `gorm:"column:share_winner_image_zh"`              // 中奖用户分享图片中文
	AnnounceEntranceImageUg string `gorm:"column:announce_entrance_image_ug"`       // 预告入口图片维文
	AnnounceEntranceImageZh string `gorm:"column:announce_entrance_image_zh"`       // 预告入口图片中文
	AnnouncePageImageUg   string `gorm:"column:announce_page_image_ug"`             // 预告页面图片维文
	AnnouncePageImageZh   string `gorm:"column:announce_page_image_zh"`             // 预告页面图片中文
	RuleUg               string `gorm:"column:rule_ug"`                             // 维文规则
	RuleZh               string `gorm:"column:rule_zh"`                             // 中文规则
	PageShareCount       int    `gorm:"column:page_share_count"`                    // 分享次数
	PageViewCount        int    `gorm:"column:page_view_count"`                     // 浏览次数

	CreatedAt      time.Time      `gorm:"column:created_at"`                         // 创建时间
	UpdatedAt      time.Time      `gorm:"column:updated_at;autoUpdateTime"`          // 更新时间
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at"`                         // 删除时间
	Admin   		Admin `gorm:"foreignKey:id;references:admin_id"`
	LotteryActivityGroupID  int           `gorm:"column:lottery_activity_group_coupon_id" json:"lottery_activity_group_coupon_id"`

	LotteryActivityLevel []LotteryActivityLevel `gorm:"foreignKey:lottery_activity_id;references:id"`

	// 参加人数
	AttendCount int64 `gorm:"column:attend_count"`
	// 订单数量
	OrderCount int64 `gorm:"column:order_count"`
	// 订单总额
	TotalOrderAmount float64 `gorm:"column:total_order_amount"` // 订单总金额（元）
	// 奖品数量
	PrizeCount int64 `gorm:"column:prize_count"`
	// 已发出奖品数量
	SendPrizeCount int64 `gorm:"column:send_prize_count"`

	Area Area `gorm:"foreignKey:area_id;references:id"`
	
}


func (m *LotteryActivityOrderRanking) TableName() string {
	return "t_lottery_activity"
}
