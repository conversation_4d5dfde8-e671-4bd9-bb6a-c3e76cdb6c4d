package models // 你可以选择合适的包名

import (
	"time"

	"gorm.io/gorm"
)

// ShipperInsuranceLog represents the t_shipper_insurance_log table.
type ShipperInsuranceLog struct {
	ID             int       `gorm:"column:id;primary_key;autoIncrement" json:"id"`
	CityID      int      `gorm:"column:city_id;index:city_id" json:"city_id"`
	AreaID      int      `gorm:"column:area_id;index:area_id" json:"area_id"`
	ShipperID      int      `gorm:"column:shipper_id;index:shipper_id" json:"shipper_id"`
	InsuranceNameUG string    `gorm:"column:insurance_name_ug" json:"insurance_name_ug"`
	InsuranceNameZH string    `gorm:"column:insurance_name_zh" json:"insurance_name_zh"`
	Date           string `gorm:"column:date" json:"date"`
	StartTime      string `gorm:"column:start_time" json:"start_time"`
	EndTime        string `gorm:"column:end_time" json:"end_time"`
	UploadTime     *time.Time `gorm:"column:upload_time" json:"upload_time"`
	ConfirmTime    *time.Time `gorm:"column:confirm_time" json:"confirm_time"`
	Amount         int       `gorm:"column:amount" json:"amount"`
	InsuranceNumber string    `gorm:"column:insurance_number" json:"insurance_number"`
	InsuranceState string    `gorm:"column:insurance_state" json:"insurance_state"`
	State          int       `gorm:"column:state;default:0" json:"state"`
	FailReasonUg     string    `gorm:"column:fail_reason_ug" json:"fail_reason_ug"`
	FailReasonZh     string    `gorm:"column:fail_reason_zh" json:"fail_reason_zh"`
	CreatedAt      time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt      time.Time `gorm:"column:updated_at" json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`
	Admin Admin        `gorm:"foreignKey:shipper_id;references:id" json:"admin"`
	City  City        `gorm:"foreignKey:city_id;references:id" json:"city"`
	Area Area         `gorm:"foreignKey:area_id;references:id" json:"area"`
	RealNameInfo SelfSignMerchantInfo `gorm:"foreignKey:shipper_id;references:restaurant_id" json:"self_sign_merchant_info"`
	AdminForSelfSign   AdminForSelfSign `gorm:"foreignKey:id;references:shipper_id"`            // 配送员数据
}

// TableName returns the name of the table in the database.
func (ShipperInsuranceLog) TableName() string {
	return "t_shipper_insurance_log"
}


type ShipperInsuranceLogForSelfSign struct {
	ID             int       `gorm:"column:id;primary_key;autoIncrement" json:"id"`
	CityID      int      `gorm:"column:city_id;index:city_id" json:"city_id"`
	AreaID      int      `gorm:"column:area_id;index:area_id" json:"area_id"`
	ShipperID      int      `gorm:"column:shipper_id;index:shipper_id" json:"shipper_id"`
	InsuranceNameUG string    `gorm:"column:insurance_name_ug" json:"insurance_name_ug"`
	InsuranceNameZH string    `gorm:"column:insurance_name_zh" json:"insurance_name_zh"`
	Date           string `gorm:"column:date" json:"date"`
	StartTime      string `gorm:"column:start_time" json:"start_time"`
	EndTime        string `gorm:"column:end_time" json:"end_time"`
	UploadTime     *time.Time `gorm:"column:upload_time" json:"upload_time"`
	ConfirmTime    *time.Time `gorm:"column:confirm_time" json:"confirm_time"`
	Amount         int       `gorm:"column:amount" json:"amount"`
	InsuranceNumber string    `gorm:"column:insurance_number" json:"insurance_number"`
	InsuranceState string    `gorm:"column:insurance_state" json:"insurance_state"`
	State          int       `gorm:"column:state;default:0" json:"state"`
	FailReasonUg     string    `gorm:"column:fail_reason_ug" json:"fail_reason_ug"`
	FailReasonZh     string    `gorm:"column:fail_reason_zh" json:"fail_reason_zh"`
	CreatedAt      time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt      time.Time `gorm:"column:updated_at" json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`
	
	City  City        `gorm:"foreignKey:city_id;references:id" json:"city"`
	Area Area         `gorm:"foreignKey:area_id;references:id" json:"area"`
	
}


func (ShipperInsuranceLogForSelfSign) TableName() string {
	return "t_shipper_insurance_log"
}