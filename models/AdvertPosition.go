package models

import (
	"gorm.io/gorm"
	"time"
)

// AdvertPosition 广告位置
type AdvertPosition struct {
	ID            int64          `json:"id" gorm:"id"`                         // 自增编号
	TerminalId    int64          `json:"terminal_id" gorm:"terminal_id"`       // 终端编号
	Tag           string         `json:"tag" gorm:"tag"`                       // 位置标签
	Width         int64          `json:"width" gorm:"width"`                   // 位置宽度
	Height        int64          `json:"height" gorm:"height"`                 // 位置高度
	Description   string         `json:"description" gorm:"description"`       // 广告介绍
	DescriptionZh string         `json:"description_zh" gorm:"description_zh"` // 广告介绍
	State         int64          `json:"state" gorm:"state"`                   // 状态（0关闭，1开通）
	CreatedAt     time.Time      `json:"created_at" gorm:"created_at"`         // 创建时间
	UpdatedAt     time.Time      `json:"updated_at" gorm:"updated_at"`         // 修改时间
	DeletedAt     gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"`         // 删除时间
}

// TableName 表名称
func (*AdvertPosition) TableName() string {
	return "b_adver_position"
}
