// Code generated by sql2gorm. DO NOT EDIT.
package models

import (
"time"
)

type SelfSignImages struct {
	ID           int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	Type 		 int       `gorm:"column:type"` // 注册类型 1:餐厅，2:配送员，3:代理
	RestaurantID int       `gorm:"column:restaurant_id"` // 餐厅ID
	DocType      string    `gorm:"column:doc_type"`      // 图片类型
	DocTypeName  string    `gorm:"column:doc_type_name"`      						 // 图片类型
	MlzFilePath  string    `gorm:"column:mlz_file_path"` // 美滋来服务器保存的图片路径
	UmsFilePath  string    `gorm:"column:ums_file_path"` // 银商服务器返回的图片路径
	FileSize  	 string    `gorm:"column:file_size"` // 银商服务器返回的图片路径
	FileType     string    `gorm:"column:file_type"` // 银商服务器返回的图片路径
	CreatedAt    time.Time `gorm:"column:created_at"`    // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at"`    // 更新时间
	//DeletedAt    time.Time `gorm:"column:deleted_at"`    // 删除时间
}

func (m *SelfSignImages) TableName() string {
	return "t_self_sign_images"
}


