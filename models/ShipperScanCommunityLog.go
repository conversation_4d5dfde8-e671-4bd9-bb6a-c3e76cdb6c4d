// Code generated by sql2gorm. DO NOT EDIT.
package models

import (
	"gorm.io/gorm"
	"time"
)

// 区县信息表
type ShipperScanCommunityLog struct {
	ID                    int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`              // 自增编号
	CityID                int       `gorm:"column:city_id;NOT NULL"`                           // 城市编号
	AreaID               int       `gorm:"column:area_id;NOT NULL"`                           // 城市编号
	ShipperID            int    `gorm:"column:shipper_id"`                                    // 区域维文名称
	CommunityID                int    `gorm:"column:community_id"`                                    // 区域中文名称
	CreatedAt             time.Time `gorm:"column:created_at"`                                 // 创建时间
	UpdatedAt             time.Time `gorm:"column:updated_at"`                                 // 修改时间
	DeletedAt             gorm.DeletedAt `gorm:"column:deleted_at"`                                 // 删除时间
}

func (m *ShipperScanCommunityLog) TableName() string {
	return "t_shipper_scan_community_log"
}
