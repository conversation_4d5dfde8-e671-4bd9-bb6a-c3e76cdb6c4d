package models

import (
	"time"

	"gorm.io/gorm"
)

const MiniGameActivityUserLogTypeIncome = 1
const MiniGameActivityUserLogTypeOutcome = 2

type MiniGameUserLog struct {

	ID            uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	ActivityID    int            `gorm:"column:activity_id;not null" json:"activity_id"`
	CityID        int           `gorm:"column:city_id" json:"city_id"`
	AreaID        int           `gorm:"column:area_id" json:"area_id"`
	UserID        int            `gorm:"column:user_id;not null" json:"user_id"`
	Type          int            `gorm:"column:type;not null" json:"type"`
	ItemId        int            `gorm:"column:item_id;" json:"item_id"`

	FullAmount    int            `gorm:"column:full_amount;default:1;not null" json:"full_amount"`
	OriginalAmount int64         `gorm:"column:original_amount;default:0" json:"original_amount"`
	Amount        int64          `gorm:"column:amount;default:0" json:"amount"`
	LeftAmount    int64          `gorm:"column:left_amount;default:0" json:"left_amount"`
	OrderID       *int           `gorm:"column:order_id;default:0" json:"order_id"`
	OrderPercent  float64        `gorm:"column:order_percent;default:0" json:"order_percent"`
	

	CreatedAt time.Time `json:"created_at" gorm:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at" gorm:"updated_at"` // 修改时间
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"deleted_at"` // 删除时间

	Area             Area             `gorm:"foreignKey:area_id;references:id"`     // 区域
	City             City             `gorm:"foreignKey:city_id;references:id"`     // 城市
	User             User             `gorm:"foreignKey:user_id;references:id"`     // 用户
	MiniGameActivity MiniGameActivity `gorm:"foreignKey:activity_id;references:id"` // 游戏活动
	TOrder      TOrder      `gorm:"foreignKey:OrderID;references:ID"` // 订单
	TOrderToday TOrderToday `gorm:"foreignKey:OrderID;references:ID"` // 今日订单
}

// TableName 表名称
func (*MiniGameUserLog) TableName() string {
    return "t_mini_game_activity_user_log"
}



type MiniGameUserLogStatistic struct {
	MiniGameUserLog
	AllAmountSum     int64              `json:"all_amount_sum"`
	UseAmountSum     int64              `json:"use_amount_sum"`
	AttendCount     int64               `json:"attend_count"`
	TakeOrderCount     int64            `json:"take_order_count"`
}
func (*MiniGameUserLogStatistic) TableName() string {
	return "t_mini_game_activity_user_log"
}


// TOrder 订单历史表
type TOrder struct {
	ID int64 `json:"id" gorm:"id"` // 自增编号
	RandomId string `json:"random_id" gorm:"random_id"` // 发给用户的随机ID
	TerminalId int64 `json:"terminal_id" gorm:"terminal_id"` // 终端编号
	OrderId string `json:"order_id" gorm:"order_id"` // 订单唯一编号
	CategoryId int64 `json:"category_id" gorm:"category_id"` // 商户类目编号
	CityId int64 `json:"city_id" gorm:"city_id"` // 下单城市编号
	AreaId int64 `json:"area_id" gorm:"area_id"` // 区域编号
	StoreId int64 `json:"store_id" gorm:"store_id"` // 商户编号
	UserId int64 `json:"user_id" gorm:"user_id"` // 用户编号
	BuildingId int64 `json:"building_id" gorm:"building_id"` // 大厦编号
	OrderAddress string `json:"order_address" gorm:"order_address"` // 收餐地址
	Name string `json:"name" gorm:"name"` // 收餐人名称
	Mobile string `json:"mobile" gorm:"mobile"` // 收餐人手机号
	ConsumeType int64 `json:"consume_type" gorm:"consume_type"` // 付费类型（0现金,1在线支付）
	PayType int64 `json:"pay_type" gorm:"pay_type"` // 用什么付费(1 现金, 2 餐币, 3 支付宝,  4 银联, 5 微信)
	OriginalPrice int64 `json:"original_price" gorm:"original_price"` // 订单原价格（单位：分）
	Price int64 `json:"price" gorm:"price"` // 实际价格（单位：分）
	Shipment int64 `json:"shipment" gorm:"shipment"` // 配送费
	OriginalShipment int64 `json:"original_shipment" gorm:"original_shipment"` // 原始配送费
	FullReductionAmount int64 `json:"full_reduction_amount" gorm:"full_reduction_amount"` // 满减金额(单位：分钱)
	LunchBoxFee int64 `json:"lunch_box_fee" gorm:"lunch_box_fee"` // 总饭盒费（单位：分）
	MpProfit int64 `json:"mp_profit" gorm:"mp_profit"` // 一个订单的属于平台的总利润
	DealerProfit int64 `json:"dealer_profit" gorm:"dealer_profit"` // 一个订单的属于代理商的总利润
	ResProfit int64 `json:"res_profit" gorm:"res_profit"` // 一个订单的属于代商家的总利润
	CurrentMpProfit int64 `json:"current_mp_profit" gorm:"current_mp_profit"` // 一个订单的属于平台的分账金额(平台利润加上+代理暂扣的利润)
	CurrentDealerProfit int64 `json:"current_dealer_profit" gorm:"current_dealer_profit"` // 一个订单的属于代理商的总利润(代理利润-平台暂扣利润)
	DealerDepositToMp int64 `json:"dealer_deposit_to_mp" gorm:"dealer_deposit_to_mp"` // 平台暂扣的代理利润
	Cash int64 `json:"cash" gorm:"cash"` // 现金（单位：分）
	Coin int64 `json:"coin" gorm:"coin"` // 餐币（单位：分）
	Consume int64 `json:"consume" gorm:"consume"` // 充值金额（单位：分）
	Description string `json:"description" gorm:"description"` // 订单备注
	Timezone int8 `json:"timezone" gorm:"timezone"` // 预定时间时区：6乌鲁木齐时间，8北京时间
	BookingTime time.Time `json:"booking_time" gorm:"booking_time"` // 预订时间
	PayTime time.Time `json:"pay_time" gorm:"pay_time"` // 付费时间
	PrintTime time.Time `json:"print_time" gorm:"print_time"` // 打印时间
	PrintedTime time.Time `json:"printed_time" gorm:"printed_time"`
	SendNotify int8 `json:"send_notify" gorm:"send_notify"`
	SerialNumber int64 `json:"serial_number" gorm:"serial_number"`
	DeliveryTakedTime time.Time `json:"delivery_taked_time" gorm:"delivery_taked_time"` // 抢订单时间
	DeliveryStartTime time.Time `json:"delivery_start_time" gorm:"delivery_start_time"` // 开始配送时间
	DeliveryEndTime time.Time `json:"delivery_end_time" gorm:"delivery_end_time"` // 订单结束时间
	RealShipment int64 `json:"real_shipment" gorm:"real_shipment"` // 订单配送完后配送员能得到的实际配送费
	DeductionFee int64 `json:"deduction_fee" gorm:"deduction_fee"` // 配送扣费金额
	Taked int8 `json:"taked" gorm:"taked"` // 表示是否已被抢单（0未抢单，1已抢单）
	ShipperId int64 `json:"shipper_id" gorm:"shipper_id"` // 配送员编号
	SentSms int8 `json:"sent_sms" gorm:"sent_sms"` // 是否发送过短信（0没发送，1已发送）
	DeleteFlag int8 `json:"delete_flag" gorm:"delete_flag"` // 用户是否删除该订单（0正常，1用户已删除）
	RefundType int8 `json:"refund_type" gorm:"refund_type"` // 1　金额返回美滋来餐币，2　金额返回用户付款账户
	RefundChanel int8 `json:"refund_chanel" gorm:"refund_chanel"` // 取消订单渠道 （0表示没取消，1 表示实时订单自动被系统取消  2表示从后台取消 3表示商家端取消 4表示 用户端取消）
	Refunded int8 `json:"refunded" gorm:"refunded"` // 是否已退款（0 为退款， 1 已退款），只能取消的订单有用
	IsDouyin int8 `json:"is_douyin" gorm:"is_douyin"` // 是否从抖音支付
	ShipperCompleteGrant int8 `json:"shipper_complete_grant" gorm:"shipper_complete_grant"` // 配送员完成订单授权（1，没有权限、1有权限）
	IsLuckDrawed int8 `json:"is_luck_drawed" gorm:"is_luck_drawed"` // 是否参加抽奖活动（0表示没有抽奖、2表示以抽奖）
	IsAutoRefund int8 `json:"is_auto_refund" gorm:"is_auto_refund"` // 是否是自动退的订单（只有在实时订单有效1表示人工取消的，1表示系统自动取消的订单）
	IsCommented int8 `json:"is_commented" gorm:"is_commented"` // 是否评论
	Distance float32 `json:"distance" gorm:"distance"` // 距离
	OrderType int8 `json:"order_type" gorm:"order_type"` // 0:表示预订单；1：表示实时订单
	Called int8 `json:"called" gorm:"called"` // 表示是否打电话提醒过商家（0表示还没打电话提醒、2表示一打电话提醒
	State int8 `json:"state" gorm:"state"` // 订单状态（跟b_order_state表关联）
	CreatedAt time.Time `json:"created_at" gorm:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at" gorm:"updated_at"` // 修改时间
	DeletedAt time.Time `json:"deleted_at" gorm:"deleted_at"` // 删除时间
	CashClearState int8 `json:"cash_clear_state" gorm:"cash_clear_state"` // 配送员是否缴纳现金订单款额
	CashClearTime time.Time `json:"cash_clear_time" gorm:"cash_clear_time"` // 配送员缴纳现金时间
	CashClearChannel int8 `json:"cash_clear_channel" gorm:"cash_clear_channel"` // 1:公众号，2.h5支付 3:配送员客户端
	CashClearAdmin int64 `json:"cash_clear_admin" gorm:"cash_clear_admin"` // 配送员缴纳现金人员
	SeckillState int8 `json:"seckill_state" gorm:"seckill_state"` // 0 表示 普通订单 1 表示秒杀订单 2 表示秒杀退款
	PayChannel int8 `json:"pay_channel" gorm:"pay_channel"`
	PayPlatform int8 `json:"pay_platform" gorm:"pay_platform"` // 0 默认：公司账号直接支付， 1 拉卡拉平台支付\n\n3:拉卡拉收单商户\n4:微信收付通账号
	IsAutoFished int8 `json:"is_auto_fished" gorm:"is_auto_fished"` // 表示是否自动完成（0表示否、1表示是）
	SelfTakeNumber int64 `json:"self_take_number" gorm:"self_take_number"` // 自取编号
	DeliveryType int8 `json:"delivery_type" gorm:"delivery_type"` // 配送类型  1: 系统配送 2:自取
	ArchiveDate time.Time `json:"archive_date" gorm:"archive_date"`
	ActualPaid int64 `json:"actual_paid" gorm:"actual_paid"` // 实际支付
	OrderPrice int64 `json:"order_price" gorm:"order_price"` // 订单金额
	TotalDiscountAmount int64 `json:"total_discount_amount" gorm:"total_discount_amount"` // 总优惠金额
	MarketType int8 `json:"market_type" gorm:"market_type"`
	ShipperReward int64 `json:"shipper_reward" gorm:"shipper_reward"` // 配送员奖励
	SubsidyAmount int64 `json:"subsidy_amount" gorm:"subsidy_amount"` // 商家补贴
	PriceMarkupInPrice int64 `json:"price_markup_in_price" gorm:"price_markup_in_price"` // 加价 总进价
	OrderPriceRes int64 `json:"order_price_res" gorm:"order_price_res"` // 餐厅看到的订单价格
	Restaurant Restaurant  `gorm:"foreignKey:StoreId;references:ID"`

}

// TableName 表名称
func (*TOrder) TableName() string {
	return "t_order"
}

// TOrderToday 今日订单信息表
type TOrderToday struct {
	ID int64 `json:"id" gorm:"id"` // 自增编号
	RandomId string `json:"random_id" gorm:"random_id"` // 发给用户的随机ID
	TerminalId int64 `json:"terminal_id" gorm:"terminal_id"` // 终端编号
	OrderId string `json:"order_id" gorm:"order_id"` // 订单唯一编号
	CategoryId int64 `json:"category_id" gorm:"category_id"` // 商户类目编号
	CityId int64 `json:"city_id" gorm:"city_id"` // 下单城市编号
	AreaId int64 `json:"area_id" gorm:"area_id"` // 区域编号
	StoreId int64 `json:"store_id" gorm:"store_id"` // 商户编号
	UserId int64 `json:"user_id" gorm:"user_id"` // 用户编号
	BuildingId int64 `json:"building_id" gorm:"building_id"` // 大厦编号
	OrderAddress string `json:"order_address" gorm:"order_address"` // 收餐地址
	Name string `json:"name" gorm:"name"` // 收餐人名称
	Mobile string `json:"mobile" gorm:"mobile"` // 收餐人手机号
	ConsumeType int64 `json:"consume_type" gorm:"consume_type"` // 付费类型（0现金,1在线支付,3现金订单代理支付）
	PayType int64 `json:"pay_type" gorm:"pay_type"` // 用什么付费(1 现金, 2 餐币, 3 支付宝,  4 银联, 5 微信，6 代理支付)
	OriginalPrice int64 `json:"original_price" gorm:"original_price"` // 订单原价格（单位：分）
	Price int64 `json:"price" gorm:"price"` // 实际价格（单位：分）
	Shipment int64 `json:"shipment" gorm:"shipment"` // 配送费
	OriginalShipment int64 `json:"original_shipment" gorm:"original_shipment"` // 原始配送费
	LunchBoxFee int64 `json:"lunch_box_fee" gorm:"lunch_box_fee"` // 总饭盒费（单位：分）
	FullReductionAmount int64 `json:"full_reduction_amount" gorm:"full_reduction_amount"` // 满减金额(单位：分钱)
	MpProfit int64 `json:"mp_profit" gorm:"mp_profit"` // 一个订单的属于平台的总利润
	DealerProfit int64 `json:"dealer_profit" gorm:"dealer_profit"` // 一个订单的属于代理商的总利润
	ResProfit int64 `json:"res_profit" gorm:"res_profit"` // 一个订单的属于代商家的总利润
	CurrentMpProfit int64 `json:"current_mp_profit" gorm:"current_mp_profit"` // 一个订单的属于平台的分账金额(平台利润加上+代理暂扣的利润)
	CurrentDealerProfit int64 `json:"current_dealer_profit" gorm:"current_dealer_profit"` // 一个订单的属于代理商的总利润(代理利润-平台暂扣利润)
	DealerDepositToMp int64 `json:"dealer_deposit_to_mp" gorm:"dealer_deposit_to_mp"` // 平台暂扣的代理利润
	Cash int64 `json:"cash" gorm:"cash"` // 现金（单位：分）
	Coin int64 `json:"coin" gorm:"coin"` // 餐币（单位：分）
	Consume int64 `json:"consume" gorm:"consume"` // 充值金额（单位：分）
	Description string `json:"description" gorm:"description"` // 订单备注
	Timezone int8 `json:"timezone" gorm:"timezone"` // 预定时间时区：6乌鲁木齐时间，8北京时间
	BookingTime time.Time `json:"booking_time" gorm:"booking_time"` // 预订时间
	PayTime time.Time `json:"pay_time" gorm:"pay_time"` // 付费时间
	PrintTime time.Time `json:"print_time" gorm:"print_time"` // 打印时间
	PrintedTime time.Time `json:"printed_time" gorm:"printed_time"` // 订单打印完成时间
	DeliveryTakedTime time.Time `json:"delivery_taked_time" gorm:"delivery_taked_time"` // 抢订单时间
	DeliveryStartTime time.Time `json:"delivery_start_time" gorm:"delivery_start_time"` // 开始配送时间
	DeliveryEndTime time.Time `json:"delivery_end_time" gorm:"delivery_end_time"` // 订单结束时间
	RealShipment int64 `json:"real_shipment" gorm:"real_shipment"` // 订单配送完后配送员能得到的实际配送费
	DeductionFee int64 `json:"deduction_fee" gorm:"deduction_fee"` // 配送扣费金额
	Taked int8 `json:"taked" gorm:"taked"` // 表示是否已被抢单（0未抢单，1已抢单）
	ShipperId int64 `json:"shipper_id" gorm:"shipper_id"` // 配送员编号
	SentSms int8 `json:"sent_sms" gorm:"sent_sms"` // 是否发送过短信（0没发送，1已发送）
	DeleteFlag int8 `json:"delete_flag" gorm:"delete_flag"` // 用户是否删除该订单（0正常，1用户已删除）
	RefundType int8 `json:"refund_type" gorm:"refund_type"` // 1　金额返回美滋来餐币，2　金额返回用户付款账户，3表示退回给代理余额
	RefundChanel int8 `json:"refund_chanel" gorm:"refund_chanel"` // 取消订单渠道 （0表示没取消，1 表示实时订单自动被系统取消  2表示从后台取消 3表示商家端取消 4表示 用户端取消）
	Refunded int8 `json:"refunded" gorm:"refunded"` // 是否已退款（0 为退款， 1 已退款），只能取消的订单有用
	IsDouyin int8 `json:"is_douyin" gorm:"is_douyin"` // 是否从抖音支付
	SendNotify int8 `json:"send_notify" gorm:"send_notify"` // 是否给商家发送推送
	SerialNumber int64 `json:"serial_number" gorm:"serial_number"`
	ShipperCompleteGrant int8 `json:"shipper_complete_grant" gorm:"shipper_complete_grant"` // 配送员完成订单授权（1，没有权限、1有权限）
	IsLuckDrawed int8 `json:"is_luck_drawed" gorm:"is_luck_drawed"` // 是否参加抽奖活动（0表示没有抽奖、2表示以抽奖）
	IsAutoRefund int8 `json:"is_auto_refund" gorm:"is_auto_refund"` // 是否是自动退的订单（只有在实时订单有效1表示人工取消的，1表示系统自动取消的订单）
	IsCommented int8 `json:"is_commented" gorm:"is_commented"` // 是否评论
	Distance float32 `json:"distance" gorm:"distance"` // 距离
	OrderType int8 `json:"order_type" gorm:"order_type"` // 0:表示预订单；1：表示实时订单
	Called int8 `json:"called" gorm:"called"` // 表示是否打电话提醒过商家（0表示还没打电话提醒、2表示一打电话提醒
	PayChannel int8 `json:"pay_channel" gorm:"pay_channel"` // 1:微信，2:支付宝，3:云闪付
	PayPlatform int8 `json:"pay_platform" gorm:"pay_platform"` // 0 默认：走公司支付， 1 拉卡拉通道支付,\n3:拉卡拉收单商户\n4:微信收付通账号
	State int8 `json:"state" gorm:"state"` // 订单状态（跟b_order_state表关联）
	CreatedAt time.Time `json:"created_at" gorm:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at" gorm:"updated_at"` // 修改时间
	DeletedAt time.Time `json:"deleted_at" gorm:"deleted_at"` // 删除时间
	CashClearState int8 `json:"cash_clear_state" gorm:"cash_clear_state"` // 配送员是否缴纳现金订单款额
	CashClearTime time.Time `json:"cash_clear_time" gorm:"cash_clear_time"` // 配送员缴纳现金时间
	CashClearChannel int8 `json:"cash_clear_channel" gorm:"cash_clear_channel"` // 1:公众号，2.h5支付 3:配送员客户端
	CashClearAdmin int64 `json:"cash_clear_admin" gorm:"cash_clear_admin"` // 配送员缴纳现金人员
	SeckillState int8 `json:"seckill_state" gorm:"seckill_state"` // 0 表示 普通订单 1 表示秒杀订单 2 表示秒杀退款
	MarketType int8 `json:"market_type" gorm:"market_type"` // 0无秒杀和特价 1:秒杀，2:特价活动
	IsAutoFished int8 `json:"is_auto_fished" gorm:"is_auto_fished"` // 表示是否自动完成（0表示否、1表示是）
	SelfTakeNumber int64 `json:"self_take_number" gorm:"self_take_number"` // 自取编号
	DeliveryType int8 `json:"delivery_type" gorm:"delivery_type"` // 配送类型  1: 系统配送 2:自取
	ActualPaid int64 `json:"actual_paid" gorm:"actual_paid"` // 实际支付金额
	OrderPrice int64 `json:"order_price" gorm:"order_price"` // 订单总额
	TotalDiscountAmount int64 `json:"total_discount_amount" gorm:"total_discount_amount"` // 总优惠金额
	ShipperReward int64 `json:"shipper_reward" gorm:"shipper_reward"` // 配送员奖励
	SubsidyAmount int64 `json:"subsidy_amount" gorm:"subsidy_amount"` // 商家补贴金额
	PriceMarkupInPrice int64 `json:"price_markup_in_price" gorm:"price_markup_in_price"` // 加价 总进价
	OrderPriceRes int64 `json:"order_price_res" gorm:"order_price_res"` // 餐厅看到的订单价格
	Restaurant Restaurant  `gorm:"foreignKey:StoreId;references:ID"`
}

// TableName 表名称
func (*TOrderToday) TableName() string {
	return "t_order_today"
}