package models

import (
	"time"

	"gorm.io/gorm"
)

// AdminPacketRechargeLog defines the structure for the t_admin_packet_recharge_log table with GORM annotations.
type AdminPacketRechargeLog struct {
	ID            uint           `gorm:"primaryKey;column:id;autoIncrement"` // 主键ID
	CityID        int            `gorm:"column:city_id;not null"`             // 城市ID
	AreaID        int            `gorm:"column:area_id;not null"`             // 区域ID
	RechargerID   *int           `gorm:"column:recharger_id"`                 // 充值人员（代理助手）编号
	AdminID       int            `gorm:"column:admin_id;not null"`            // 代理（管理员）编号
	Type          *int         `gorm:"column:type"`                        // 类型：1表示充值；2表示发红包、3表示退款充值、4 秒杀服务费 5、秒杀退款
	RechargeAmount int            `gorm:"column:recharge_amount;not null"`     // 充值金额（单位为：分）
	Balance       *int           `gorm:"column:balance"`                     // 充值或消费后的余额
	OutTradeNo    string         `gorm:"column:out_trade_no"`                // 网站商品订单唯一号
	TransactionID string         `gorm:"column:transaction_id"`              // 支付宝流水号
	PrepayID      string         `gorm:"column:prepay_id"`                   // 预支付交易会话标识
	NonceStr      string         `gorm:"column:nonce_str"`                   // 卖家支付宝用户号
	TradeType     string         `gorm:"column:trade_type"`                  // 卖家支付宝账号
	Platform      int          `gorm:"column:platform;default:1"`           // 支付平台 1:微信 2:拉卡拉
	ItemID        *int           `gorm:"column:item_id"`                     // 优惠券 等活动的 id
	Payed         int          `gorm:"column:payed;default:0"`             // 状态（0 未付款， 1已付款）
	ExpireTime    *time.Time     `gorm:"column:expire_time"`                 // 过期时间
	CreatedAt     *time.Time     `gorm:"column:created_at"`                  // 创建时间
	UpdatedAt     *time.Time     `gorm:"column:updated_at;autoUpdateTime"`   // 修改时间
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at"`                  // 删除时间
}

func (m *AdminPacketRechargeLog) TableName() string {
	return "t_admin_packet_recharge_log"
}
