package models

import (
	"time"
)

type SelfSignArea struct {
	Id        int        `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	Platform  int        `gorm:"column:platform"`   // 创建时间
	Level     int        `gorm:"column:level"`      // 创建时间
	PCode     string     `gorm:"column:p_code"`     // 创建时间
	Code      string     `gorm:"column:code"`       // 创建时间
	NameUg    string     `gorm:"column:name_ug"`    // 创建时间
	NameZh    string     `gorm:"column:name_zh"`    // 创建时间
	CreatedAt time.Time  `gorm:"column:created_at"` // 创建时间
	UpdatedAt time.Time  `gorm:"column:updated_at"` // 更新时间
	DeletedAt *time.Time `gorm:"column:deleted_at"` // 删除时间
}

func (m *SelfSignArea) TableName() string {
	return "b_self_sign_area"
}
