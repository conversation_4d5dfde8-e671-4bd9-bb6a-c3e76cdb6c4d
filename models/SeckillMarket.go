﻿package models

import (
	"time"
)

type SeckillMarket struct {
	ID                      int       `json:"id" gorm:"id"`                                                   // ID
	Type                    int       `json:"type" gorm:"type"`                                               // 1.秒杀活动:2:特价活动
	CreatorId               int       `json:"creator_id" gorm:"creator_id"`                                   // 创建人ID
	CityId                  int       `json:"city_id" gorm:"city_id"`                                         // 城市ID
	AreaId                  int       `json:"area_id" gorm:"area_id"`                                         // 地区 id
	NameUg                  string    `json:"name_ug" gorm:"name_ug"`                                         // 维文名称
	NameZh                  string    `json:"name_zh" gorm:"name_zh"`                                         // 中文名称
	FoodCount               int       `json:"food_count" gorm:"food_count"`                                   // 美食数量
	Price                   int       `json:"price" gorm:"price"`                                             // 价格
	StartType               int       `json:"start_type" gorm:"start_type"`                                   // 1:立即开始，2：提前公布
	AnnouncedTime           time.Time `json:"announced_time" gorm:"announced_time"`                           // 公布时间
	BeginTime               time.Time `json:"begin_time" gorm:"begin_time"`                                   // 开始时间
	EndTime                 time.Time `json:"end_time" gorm:"end_time"`                                       // 结束时间
	EnableSelfTake          int       `json:"enable_self_take" gorm:"enable_self_take"`                       // 是否支持自取,0:否，1:是
	ShipmentType            int       `json:"shipment_type" gorm:"shipment_type"`                             // 是否设置固定配送费: 0:否，1:是
	ShipmentFee             int       `json:"shipment_fee" gorm:"shipment_fee"`                               // 固定配送费
	ShipperIncome           int       `json:"shipper_income" gorm:"shipper_income"`                           // 配送员收入
	Shipper                 string    `json:"shipper" gorm:"shipper"`                                         // 分配的配送员ID
	LateDeductRule          string    `json:"late_deduct_rule" gorm:"late_deduct_rule"`                       // 分配的配送员ID
	UserMarketMaxOrderCount int       `json:"user_market_max_order_count" gorm:"user_market_max_order_count"` // 用户活动最多购买次数限制
	RuleUg                  string    `json:"rule_ug" gorm:"rule_ug"`                                         // 维文规则
	RuleZh                  string    `json:"rule_zh" gorm:"rule_zh"`                                         // 中文规则
	State                   int       `json:"state" gorm:"state"`                                             // 0:停止，1:启动
	CreatedAt               time.Time `json:"created_at" gorm:"created_at"`                                   // 创建时间
	UpdatedAt               time.Time `json:"updated_at" gorm:"updated_at"`                                   // 更新时间
	DeletedAt               time.Time `json:"deleted_at" gorm:"deleted_at"`                                   // 删除时间
}

func (m *SeckillMarket) TableName() string {
	return "b_seckill_market"
}
