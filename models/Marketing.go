package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"
)

const (
	MarketingCreatorTypeDealer   = 1 // 被代理创建的活动
	MarketingCreatorTypeMerchant = 2 // 备商家创建的活动

	MarketingMarketingTypePriceDiscount  = 1 // 满减活动
	MarketingMarketingTypeShipmentReduce = 2 // 减配送费活动

	MarketingGroupTypeGroupActivity = 1 //  减配送费活动类型：团体活动
	MarketingGroupTypeMerchant      = 2 // 减配送费活动类型： 商家活动

	MarketingTypeShop  = 1 // 活动类型   1：定向店铺活动
	MarketingTypeFoods = 2 // 活动类型   2：定向商品活动

	MarketingStateNew     = 0 // 状态  0：新建
	MarketingStateActive  = 1 // 状态  1:启动
	MarketingStatePause   = 2 // 状态  2：暂停
	MarketingStateExpired = 3 // 状态  3：失效
	MarketingStateDelete  = 4 // 状态  4:删除

	MarketingMarketingModeAllRes  = 1 // 全部店铺
	MarketingMarketingModeSomeRes = 2 // 个别店铺
)

// Marketing 商家满减活动表
type Marketing struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`        // 编号 编号
	CreatorType   int       `gorm:"column:creator_type;default:2" json:"creator_type"`     // 谁创建的 1.代理 2:商家
	MarketingMode int       `gorm:"column:marketing_mode;default:2" json:"marketing_mode"` // 店铺类型 (1 全部店铺 2:个别店铺)
	CreatorID     int       `gorm:"column:creator_id" json:"creator_id"`                   // 创建人ID
	CityID        int       `gorm:"column:city_id" json:"city_id"`
	AreaID        int       `gorm:"column:area_id;NOT NULL" json:"area_id"`              // 地区编号
	GroupId       *int      `gorm:"column:group_id" json:"group_id"`                     // 集体活动ID(t_marketing_group_template表关联)
	GroupType     int       `gorm:"column:group_type" json:"group_type"`                 // 运费减免活动：1:团体活动、 2：商家活动、 其他: null
	MarketingType int       `gorm:"column:marketing_type" json:"marketing_type"`         // 营销类型  1：满减   2:减配送费 3：满赠活动 3:折扣美食 4: 5:优惠券 6:新客立减 7:二份立减
	RestaurantID  int       `gorm:"column:restaurant_id" json:"restaurant_id"`           // 餐厅编号
	Type          int       `gorm:"column:type" json:"type"`                             // 活动类型   1：店铺活动，2：商品定向活动
	RunningState  int       `gorm:"column:running_state;default:0" json:"running_state"` // 活动在当前时间进行状态  0:没进行  1:进行中
	NameUg        string    `gorm:"column:name_ug" json:"name_ug"`
	NameZh        string    `gorm:"column:name_zh" json:"name_zh"`
	BeginDate     time.Time `gorm:"column:begin_date;NOT NULL" json:"begin_date"`           // 开始日期
	EndDate       time.Time `gorm:"column:end_date;NOT NULL" json:"end_date"`               // 结束日期
	FullWeekState int       `gorm:"column:full_week_state;NOT NULL" json:"full_week_state"` // 全星期  0：否（多选星期），1：是

	Day           int    `gorm:"column:day" json:"day"`                                        // 星期日
	FullTimeState int    `gorm:"column:full_time_state;NOT NULL" json:"full_time_state"`       // 全天     0：否（添加时间段），1：是
	Time1Start    string `gorm:"column:time1_start" json:"time1_start"`                        // 时间段1开始
	Time1End      string `gorm:"column:time1_end" json:"time1_end"`                            // 时间段1结束
	Time2Start    string `gorm:"column:time2_start" json:"time2_start"`                        // 时间段2开始
	Time2End      string `gorm:"column:time2_end" json:"time2_end"`                            // 时间段2结束
	Time3Start    string `gorm:"column:time3_start" json:"time3_start"`                        // 时间段3开始
	Time3End      string `gorm:"column:time3_end" json:"time3_end"`                            // 时间段3结束
	TimeCount     int    `gorm:"column:time_count;default:1" json:"time_count"`                // 记录几个时间段
	AutoContinue  int    `gorm:"column:auto_continue;default:0;NOT NULL" json:"auto_continue"` // 自动延续 0:  否，1：是
	ContinueCount int    `gorm:"column:continue_count;default:0" json:"continue_count"`        // 自动延续数量
	Steps         string `gorm:"type:json;default:null" json:"steps"`                          // 价格阶梯

	PriceMax       *int `gorm:"column:price_max" json:"price_max"`                         // 最高的满减阶梯开始
	PriceReduceMax *int `gorm:"column:price_reduce_max;default:0" json:"price_reduce_max"` // 最高的满减 减免金额
	PriceCount     *int `gorm:"column:price_count;default:0" json:"price_count"`           // 阶梯数量

	State            int            `gorm:"column:state;default:0" json:"state"`                 // 状态  0：新建，1:启动，2：暂停，3：失效，4:删除
	CustomerType     int            `gorm:"column:customer_type;default:0" json:"customer_type"` // 对象用户类别(0:全部客户 1:新客户(平台) 2: 新客户(店铺) 3:老客户(平台)  4:老客户(店铺))
	MarketingNumber  string         `gorm:"column:marketing_number" json:"marketing_number"`     // 活动编号
	Foods            string         `gorm:"column:foods" json:"foods"`                           // 美食
	MinDeliveryPrice int            `gorm:"min_delivery_price" json:"min_delivery_price"`        // 运费减免活动： 起送价
	CreatedAt        time.Time      `gorm:"column:created_at" json:"created_at"`                 // 创建时间
	UpdatedAt        time.Time      `gorm:"column:updated_at" json:"updated_at"`                 // 更新时间
	DeletedAt        gorm.DeletedAt `gorm:"column:deleted_at"  json:"deleted_at"`                // 被删除时间

	FoodsJson FoodItemJsonSlice `gorm:"column:foods_json;type:json" json:"foods_json"`

	Creator                    *Admin                        `gorm:"foreignKey:creator_id;references:id" json:"creator"` // 创建人
	Restaurant                 *Restaurant                   `gorm:"foreignKey:restaurant_id;references:id" json:"restaurant"`
	City                       City                          `gorm:"foreignKey:city_id;references:id"`        // 地区
	Area                       Area                          `gorm:"foreignKey:area_id;references:id"`        // 区域
	MarketingOrderLog          []MarketingOrderLog           `gorm:"foreignKey:marketing_id;references:id"`   // 下单记录
	MarketingOrderLogForDetail *[]MarketingOrderLogGroupAggs `gorm:"foreignKey:group_id;references:group_id"` // 下单记录 团体活动
	// MarketingGroupTemplateAttendance MarketingGroupTemplateAttendance `gorm:"foreignKey:MarketingId;references:ID"`
}

// JSON 子项结构体
type FoodItemJson struct {
	FoodType uint8 `json:"food_type" binding:"required,oneof=0 1 2"`
	FoodId   int   `json:"food_id" binding:"required"`
	SpecId   int   `json:"spec_id"`
	OptionIds []int `json:"option_ids"`
}

// Add this new type to handle the slice
type FoodItemJsonSlice []FoodItemJson

// Implement scanner for the slice
func (fj *FoodItemJsonSlice) Scan(value interface{}) error {
	if value == nil {
		*fj = FoodItemJsonSlice{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid scan source for FoodItemJsonSlice")
	}

	return json.Unmarshal(bytes, fj)
}

// Implement valuer for the slice
func (fj FoodItemJsonSlice) Value() (driver.Value, error) {
	if fj == nil {
		return nil, nil
	}
	return json.Marshal(fj)
}

type MarketingStaticWithOrderLog struct {
	Marketing
	TotalOrderPrice     int `gorm:"column:total_order_price" json:"total_order_price"`         // 订单总金额（单位 分）
	OrderCount          int `gorm:"column:order_count" json:"order_count"`                     // 订单总数
	TotalReducePrice    int `gorm:"column:total_reduce_price" json:"total_reduce_price"`       // 优惠金额（单位 分）
	TotalCostRestaurant int `gorm:"column:total_cost_restaurant" json:"total_cost_restaurant"` // 优惠金额（单位 分） 店铺承担金额
	TotalCostDealer     int `gorm:"column:total_cost_dealer" json:"total_cost_dealer"`         // 优惠金额（单位 分） 代理承担金额
}

// MarketingStatisticsWithOrderLog 统计普通减配送费专用
type MarketingStatisticsWithOrderLog struct {
	Marketing
	TotalOrderPrice     int `gorm:"column:total_order_price" json:"total_order_price"`       // 订单总金额（单位 分）
	ComplatedOrderCount int `gorm:"column:order_count" json:"order_count"`                   // 完成订单总数
	RejectedOrderCount  int `gorm:"column:rejected_order_count" json:"rejected_order_count"` // 退单总数
	TotalReducePrice    int `gorm:"column:total_reduce_price" json:"total_reduce_price"`     // 优惠金额（单位 分）
}
type MarketingWithOrderLogCount struct {
	Marketing
	OrderLogCount int `gorm:"column:order_log_count" json:"order_log_count"` // 订单总数
}

func (m *Marketing) GroupTypeIsMerchant() bool {
	return m.GroupType == MarketingGroupTypeMerchant
}

func (m *Marketing) GroupTypeIsGroupActivity() bool {
	return m.GroupType == MarketingGroupTypeGroupActivity
}

func (m *Marketing) TypeIsShipmentReduce() bool {
	return m.MarketingType == MarketingMarketingTypeShipmentReduce
}

type MarketingParam struct {
	CreatorType   int    `form:"creator_type" json:"creator_type"`   // 谁创建的 1.代理 2:商家
	MarketingMode int    `form:"marketing_mode" json:"marketing_mode"` // 店铺类型 (1 全部店铺 2:个别店铺)
	CreatorID     int    `form:"creator_id" json:"creator_id"`     // 创建人ID
	CityID        int    `form:"city_id" json:"city_id"`
	GroupId       int    `form:"group_id" json:"group_id"`
	AreaID        int    `form:"area_id" json:"area_id"`                   // 地区编号
	MarketingType int    `form:"marketing_type" json:"marketing_type"`
	RestaurantID  int    `form:"restaurant_id" json:"restaurant_id"`       // 餐厅编号
	Type          int    `form:"type" json:"type"`                         // 活动类型   1：店铺活动，2：商品定向活动
	RunningState  int    `form:"running_state" json:"running_state"`       // 活动在当前时间进行状态  0:没进行  1:进行中
	NameUg        string `form:"name_ug" json:"name_ug"`
	NameZh        string `form:"name_zh" json:"name_zh"`
	BeginDate     string `form:"begin_date" json:"begin_date"`             // 开始日期
	EndDate       string `form:"end_date" json:"end_date"`                 // 结束日期
	FullWeekState int    `form:"full_week_state" json:"full_week_state"`   // 全星期  0：否（多选星期），1：是
	Day1          int    `form:"day1" json:"day1"`                         // 星期一
	Day2          int    `form:"day2" json:"day2"`                         // 星期二
	Day3          int    `form:"day3" json:"day3"`                         // 星期三
	Day4          int    `form:"day4" json:"day4"`                         // 星期四
	Day5          int    `form:"day5" json:"day5"`                         // 星期五
	Day6          int    `form:"day6" json:"day6"`                         // 星期六
	Day7          int    `form:"day7" json:"day7"`                         // 星期日
	FullTimeState int    `form:"full_time_state" json:"full_time_state"`   // 全天     0：否（添加时间段），1：是
	Time1Start    string `form:"time1_start" json:"time1_start"`           // 时间段1开始
	Time1End      string `form:"time1_end" json:"time1_end"`               // 时间段1结束
	Time2Start    string `form:"time2_start" json:"time2_start"`           // 时间段2开始
	Time2End      string `form:"time2_end" json:"time2_end"`               // 时间段2结束
	Time3Start    string `form:"time3_start" json:"time3_start"`           // 时间段3开始
	Time3End      string `form:"time3_end" json:"time3_end"`               // 时间段3结束
	TimeCount     int    `form:"time_count" json:"time_count"`             // 记录几个时间段
	AutoContinue  int    `form:"auto_continue" json:"auto_continue"`       // 自动延续 0:  否，1：是
	ContinueCount int    `form:"continue_count" json:"continue_count"`     // 自动延续数量
	Steps         string `form:"steps" json:"steps"`                       // 价格阶梯

	PriceMax       string `form:"price_max" json:"price_max"`                // 最高的满减阶梯开始
	PriceReduceMax string `form:"price_reduce_max" json:"price_reduce_max"`  // 最高的满减 减免金额
	PriceCount     int    `form:"price_count" json:"price_count"`           // 阶梯数量

	State           int    `form:"state" json:"state"`                       // 状态  0：新建，1:启动，2：暂停，3：失效
	CustomerType    int    `form:"customer_type" json:"customer_type"`       // 对象用户类别(0:全部客户 1:新客户(平台) 2: 新客户(店铺) 3:老客户(平台)  4:老客户(店铺))
	MarketingNumber string `form:"marketing_number" json:"marketing_number"` // 活动编号
	Foods           string `form:"foods" json:"foods"`                       // 美食

	FoodsJson FoodItemJsonSlice `json:"foods_json"` // 美食列表，包括 food_id 美食ID，food_type 美食类型，spec_id 美食规格ID
}

type MarketingMap struct {
	ID            int    `json:"id"`             // 编号 编号
	CreatorType   int    `json:"creator_type"`   // 谁创建的 1.代理 2:商家
	MarketingMode int    `json:"marketing_mode"` // 店铺类型 (1 全部店铺 2:个别店铺)
	CreatorID     int    `json:"creator_id"`     // 创建人ID
	CityID        int    `json:"city_id"`
	AreaID        int    `json:"area_id"` // 地区编号
	MarketingType int    `json:"marketing_type"`
	RestaurantID  int    `json:"restaurant_id"` // 餐厅编号
	Type          int    `json:"type"`          // 活动类型   1：店铺活动，2：商品定向活动
	RunningState  int    `json:"running_state"` // 活动在当前时间进行状态  0:没进行  1:进行中
	NameUg        string `json:"name_ug"`
	NameZh        string `json:"name_zh"`
	BeginDate     string `json:"begin_date"`      // 开始日期
	EndDate       string `json:"end_date"`        // 结束日期
	FullWeekState int    `json:"full_week_state"` // 全星期  0：否（多选星期），1：是
	Day1          int    `json:"day1"`            // 星期一
	Day2          int    `json:"day2"`            // 星期二
	Day3          int    `json:"day3"`            // 星期三
	Day4          int    `json:"day4"`            // 星期四
	Day5          int    `json:"day5"`            // 星期五
	Day6          int    `json:"day6"`            // 星期六
	Day7          int    `json:"day7"`            // 星期日
	Day           string `json:"day"`             // 星期日
	FullTimeState int    `json:"full_time_state"` // 全天     0：否（添加时间段），1：是
	Time1Start    string `json:"time1_start"`     // 时间段1开始
	Time1End      string `json:"time1_end"`       // 时间段1结束
	Time2Start    string `json:"time2_start"`     // 时间段2开始
	Time2End      string `json:"time2_end"`       // 时间段2结束
	Time3Start    string `json:"time3_start"`     // 时间段3开始
	Time3End      string `json:"time3_end"`       // 时间段3结束
	TimeCount     int    `json:"time_count"`      // 记录几个时间段
	AutoContinue  int    `json:"auto_continue"`   // 自动延续 0:  否，1：是
	ContinueCount int    `json:"continue_count"`  // 自动延续数量

	Steps string `  json:"steps"` // 价格阶梯

	StepItems []MarketingSteps `gorm:"-"  json:"step_items"` // 价格阶梯

	PriceMax       float64 `json:"price_max"`        // 最高的满减阶梯开始
	PriceReduceMax float64 `json:"price_reduce_max"` // 最高的满减 减免金额
	PriceCount     float64 `json:"price_count"`      // 阶梯数量

	State           int    `json:"state"`             // 状态  0：新建，1:启动，2：暂停，3：失效
	CustomerType    int    `json:"customer_type"`     // 对象用户类别(0:全部客户 1:新客户(平台) 2: 新客户(店铺) 3:老客户(平台)  4:老客户(店铺))
	MarketingNumber string `json:"marketing_number"`  // 活动编号
	Foods           string `json:"foods"`             // 美食
	StateName       string `json:"state_name"`        // 状态名称
	CreatedAt       string `json:"created_at"`        // 创建时间
	CreatorTypeName string `json:"creator_type_name"` // 创建人类型

	ViewCount    int         `gorm:"-" json:"view_count"`
	BuyCount     int         `gorm:"-" json:"buy_count"`
	OrderCount   int         `gorm:"-" json:"order_count"`
	ReduceAmount float64     `gorm:"-" json:"reduce_amount"`
	OrderPrice   float64     `gorm:"-" json:"order_price"`
	FoodItems    []FoodItems `gorm:"-" json:"food_items"`
	OrderSuccess int         `gorm:"-" json:"order_success"`
	OrderCancel  int         `gorm:"-" json:"order_cancel"`
}

type MarketingStaticMap struct {
	ID            int `json:"id"`             // 编号 编号
	CreatorType   int `json:"creator_type"`   // 谁创建的 1.代理 2:商家
	MarketingMode int `json:"marketing_mode"` // 店铺类型 (1 全部店铺 2:个别店铺)
	CreatorID     int `json:"creator_id"`     // 创建人ID

	State int `json:"state"` // 状态  0：新建，1:启动，2：暂停，3：失效

	Foods           string `json:"foods"`             // 美食
	StateName       string `json:"state_name"`        // 状态名称
	CreatedAt       string `json:"created_at"`        // 创建时间
	CreatorTypeName string `json:"creator_type_name"` // 创建人类型

	ViewCount    int     `gorm:"-" json:"view_count"`
	BuyCount     int     `gorm:"-" json:"buy_count"`
	OrderCount   int     `gorm:"-" json:"order_count"`
	ReduceAmount float64 `gorm:"-" json:"reduce_amount"`

	BeginDate string `json:"begin_date"`
	EndDate   string `json:"end_date"`
}

type MarketingSteps struct {
	PriceStart     float64 `gorm:"-" json:"price_start"`
	PriceReduce    float64 `gorm:"-" json:"price_reduce"`
	OrderCount     int     `gorm:"-" json:"order_count"`
	ReduceAmount   float64 `gorm:"-" json:"reduce_amount"`
	StepTitle      string  `gorm:"-" json:"step_title"`
	PriceAll       float64 `gorm:"-" json:"price_all"`
	PriceReduceAll float64 `gorm:"-" json:"price_reduce_all"`
}

type FoodItems struct {
	FoodName  string `gorm:"-" json:"name"`
	FoodImage string `gorm:"-" json:"image"`
	FoodPrice string `gorm:"-" json:"price"`
	FoodSold  int    `gorm:"-" json:"food_sold"`
}

type MarketingStatistics struct {
	ID int `json:"id"` // 编号 编号

	Name string `json:"name"`

	CreatorID int `json:"creator_id"` // 创建人ID

	State int `json:"state"` // 状态  0：新建，1:启动，2：暂停，3：失效

	StateName string `json:"state_name"` // 状态名称
	CreatedAt string `json:"created_at"` // 创建时间

	ViewCount    int     `gorm:"-" json:"view_count"`
	BuyCount     int     `gorm:"-" json:"buy_count"`
	OrderCount   int     `gorm:"-" json:"order_count"`
	ReduceAmount float64 `gorm:"-" json:"reduce_amount"`

	BeginDate  string `json:"begin_date"`
	EndDate    string `json:"end_date"`
	PriceCount string `json:"price_count"`
}

type MarketingStatisticsSteps struct {
	Steps       string  `json:"steps"`
	PriceStart  float64 `gorm:"-" json:"price_start"`
	PriceReduce float64 `gorm:"-" json:"price_reduce"`

	OrderCount int     `gorm:"-" json:"order_count"`
	ReduceAll  float64 `gorm:"-" json:"reduce_all"`
	PriceAll   float64 `gorm:"-" json:"price_all"`
}

func (m *Marketing) TableName() string {
	return "t_marketing"
}

func (m *Marketing) IsActive() bool {
	return m.State == MarketingStateActive
}

func (m *Marketing) IsShipmentReduce() bool {
	return m.MarketingType == MarketingMarketingTypeShipmentReduce
}

func (m *Marketing) IsGroupActivity() bool {
	return m.GroupType == MarketingGroupTypeGroupActivity
}

func (m *Marketing) IsMerchantActivity() bool {
	return m.GroupType == MarketingGroupTypeMerchant
}

func (m *MarketingMap) TableName() string {
	return "t_marketing"
}

func (m *MarketingStaticMap) TableName() string {
	return "t_marketing"
}

func (m *MarketingSteps) TableName() string {
	return "t_marketing"
}

func (m *FoodItems) TableName() string {
	return "t_marketing"
}
func (m *MarketingStatistics) TableName() string {
	return "t_marketing"
}
func (m *MarketingStatisticsSteps) TableName() string {
	return "t_marketing"
}

// type MarketingGroupAttendance struct {
// 	ID    int `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 编号 编号
// 	State int `gorm:"column:state;"`                        // 编号 编号
// }

// func (m *MarketingGroupAttendance) TableName() string {
// 	return "t_marketing_group_template_attendence"
// }

type StepStatistic struct {
	NameUg              string `json:"name_ug"`
	NameZh              string `json:"name_zh"`
	TotalPrice          int    `json:"total_price"`
	TotalOrderCount     int    `json:"total_order_count"`
	TotalReduceShipment int    `json:"total_reduce_shipment"`
}
