// Code generated by sql2gorm. DO NOT EDIT.
package models

import (
	"gorm.io/gorm"
	"time"
)

// 通用美食信息基本表
type AllFoods struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	Type          int       `gorm:"column:type;NOT NULL"`                 // 美食类型
	Name          string       `gorm:"column:name;NOT NULL"`                 // 美食名称
	NameUg        string    `gorm:"column:name_ug"`
	NameZh        string    `gorm:"column:name_zh"`
	Description   int       `gorm:"column:description;NOT NULL"` // 美食简介
	DescriptionUg string    `gorm:"column:description_ug"`
	DescriptionZh string    `gorm:"column:description_zh"`
	Image         string    `gorm:"column:image;NOT NULL"` // 美食图片
	State         int       `gorm:"column:state;NOT NULL"` // 状态（0关闭，1开通）
	CreatedAt     time.Time `gorm:"column:created_at"`     // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at"`     // 修改时间
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at"`     // 删除时间
	BFoodsType    BFoodsType  `gorm:"foreignKey:Type;references:ID"`
}

func (m *AllFoods) TableName() string {
	return "b_all_foods"
}

