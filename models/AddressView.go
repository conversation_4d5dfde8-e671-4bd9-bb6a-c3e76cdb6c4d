package models

import "time"

type AddressView struct {
	BuildingId           int64     `gorm:"column:building_id"`      // 父管理员编号
	StreetId             int64     `gorm:"column:street_id"`        // 父管理员编号
	CityName             string    `gorm:"column:city_name"`        // 父管理员编号
	CityNameUg           string    `gorm:"column:city_name_ug"`     // 父管理员编号
	CityNameZh           string    `gorm:"column:city_name_zh"`     // 父管理员编号
	AreaName             string    `gorm:"column:area_name"`        // 父管理员编号
	AreaNameUg           string    `gorm:"column:area_name_ug"`     // 父管理员编号
	AreaNameZh           string    `gorm:"column:area_name_zh"`     // 父管理员编号
	BuildingName         string    `gorm:"column:building_name"`    // 父管理员编号
	BuildingNameUg       string    `gorm:"column:building_name_ug"` // 父管理员编号
	BuildingNameZh       string    `gorm:"column:building_name_zh"` // 父管理员编号
	StreetName           string    `gorm:"column:street_name"`      // 父管理员编号
	StreetNameUg         string    `gorm:"column:street_name_ug"`   // 父管理员编号
	StreetNameZh         string    `gorm:"column:street_name_zh"`   // 父管理员编号
	CityId               int64     `gorm:"column:city_id"`          // 父管理员编号
	AreaId               int64     `gorm:"column:area_id"`          // 父管理员编号
	StreetPrefixUg       string    `gorm:"column:street_prefix_ug"`
	StreetPrefixZh       string    `gorm:"column:street_prefix_zh"`
	BuildingLat          float64   `gorm:"column:building_lat"`
	BuildingLng          float64   `gorm:"column:building_lng"`
	BuildingPrefixUg     string    `gorm:"column:building_prefix_ug"`
	BuildingPrefixZh     string    `gorm:"column:building_prefix_zh"`
	CityState            int       `gorm:"column:city_state"`
	PerKmShipment        int       `gorm:"column:per_km_shipment"`
	MinShipment          int       `gorm:"column:min_shipment"`
	ServicePhone         string    `gorm:"column:service_phone"`
	AreaWeight           int       `gorm:"column:area_weight"`
	AreaRestingStartTime time.Time `gorm:"column:area_resting_start_time"`
	AreaRestingEndTime   time.Time `gorm:"column:area_resting_end_time"`
	AreaRestingContentUg string    `gorm:"column:area_resting_content_ug"`
	AreaRestingContentZh string    `gorm:"column:area_resting_content_zh"`
	AreaState            int       `gorm:"column:area_state"`
	StreetWeight         int       `gorm:"column:street_weight"`
	StreetState          int       `gorm:"column:street_state"`
	BuildingType         int       `gorm:"column:building_type"`
	BuildingWeight       int       `gorm:"column:building_weight"`
	BuildingState        int       `gorm:"column:building_state"`
}

func (m *AddressView) TableName() string {
	return "address_view"
}
