package models
import (
    "time"

)

type AdvertMaterialShipperUser struct {
	ID           int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	AdvertMaterialCategoryId	int	`gorm:"column:advert_material_category_id"`	// 宣传材料分类ID	
	AdvertMaterialId	int	`gorm:"column:advert_material_id"`	// 宣传材料ID	
	AdvertMaterialPrintBatchId	int	`gorm:"column:advert_material_print_batch_id"`	// 批次号	
	AdvertMaterialTakeId	int	`gorm:"column:advert_material_take_id"`	// 领取批次	
	AdvertMaterialCodeId int `gorm:"column:advert_material_code_id"` // 宣传材料编码ID 
	AreaId	int	`gorm:"column:area_id"`	// 区域ID	
	CityId	int	`gorm:"column:city_id"`	// 城市ID	
	CreatedAt	time.Time	`gorm:"column:created_at"`	// 创建时间	
	ShipperId	int	`gorm:"column:shipper_id"`	// 配送员ID	
	State	int	`gorm:"column:state"`	// 状态： 1 有效， 2 无效 已绑定新的配送员	

	Lng	string	`gorm:"column:lng"`	// Lng
	Lat	string	`gorm:"column:lat"`	// Lat

	Reward	int	`gorm:"column:reward"`	// 奖励金额
	IsNewUser	int	`gorm:"is_new_user:reward"`	// 是否新注册用户

	UpdatedAt	time.Time	`gorm:"column:updated_at"`	// 更新时间
	UserId	int	`gorm:"column:user_id"`	// 用户ID	
	User  User    `gorm:"foreignkey:user_id;references:id"`
	AdvertMaterialCode AdvertMaterialCode `gorm:"foreignkey:advert_material_code_id;references:id"`
	
	AdvertMaterial 	 AdvertMaterial           `gorm:"foreignkey:id;references:advert_material_id"`

}

func (m *AdvertMaterialShipperUser) TableName() string {
    return "t_advert_material_shipper_user"
}
 

type AdvertMaterialShipperUserCount struct {
	ID           int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	CustomerCount	int	`gorm:"column:customer_count"`	// 用户数量
	AdvertMaterialTakeId	int	`gorm:"column:advert_material_take_id"`	// 领取批次	
}
func (m *AdvertMaterialShipperUserCount) TableName() string {
    return "t_advert_material_shipper_user"
}