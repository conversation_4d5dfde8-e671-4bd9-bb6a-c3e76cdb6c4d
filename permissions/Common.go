﻿package permissions

import (
	"errors"
	"mulazim-api/constants"
	"mulazim-api/models"
	"mulazim-api/tools"
	"strings"

	"github.com/gin-gonic/gin"
)

// IsAgent
//
// @Description: 验证是否代理
// @Author: Rixat
// @Time: 2023-12-02 07:43:04
// @receiver
// @param c *gin.Context
func IsAgent(admin models.Admin) bool {
	if admin.Type == constants.ADMIN_TYPE_DEALER || admin.Type == constants.ADMIN_TYPE_DEALER_SUB {
		return true
	}
	return false
}

// IsAgent
//
// @Description: 验证是否超级管理员
// @Author: Rixat
// @Time: 2023-12-02 07:43:04
// @receiver
// @param c *gin.Context
func IsOwner(admin models.Admin) bool {
	if admin.Type == constants.ADMIN_TYPE_OWNER {
		return true
	}
	return false
}

// IsAgent
//
// @Description: 验证是否平台管理员
// @Author: Rixat
// @Time: 2023-12-02 07:43:04
// @receiver
// @param c *gin.Context
func IsAdmin(admin models.Admin) bool {
	if admin.Type == constants.ADMIN_TYPE_ADMIN {
		return true
	}
	return false
}

// IsAgent
//
// @Description: 验证是否平台管理员
// @Author: Rixat
// @Time: 2023-12-02 07:43:04
// @receiver
// @param c *gin.Context
func IsRestaurantManager(admin models.Admin) bool {
	if admin.Type == constants.ADMIN_TYPE_RESTAURANT_ADMIN || admin.Type == constants.ADMIN_TYPE_RESTAURANT_ADMIN_SUB {
		return true
	}
	return false
}

// IsAgent
//
// @Description: 验证是否平台管理员
// @Author: Rixat
// @Time: 2023-12-02 07:43:04
// @receiver
// @param c *gin.Context
func IsShipper(admin models.Admin) bool {
	if admin.Type == constants.ADMIN_TYPE_SHIPPER_ADMIN || admin.Type == constants.ADMIN_TYPE_SHIPPER {
		return true
	}
	return false
}

// IsAgent
//
// @Description: 是否代理下的配送员
// @Author: Rixat
// @Time: 2023-12-02 07:43:04
// @receiver
// @param c *gin.Context
func AgentShipper(admin models.Admin, shipper models.Admin) bool {
	if IsAgent(admin) && IsShipper(shipper) && admin.AdminAreaID == shipper.AdminAreaID {
		return true
	}
	return false
}

// IsAgent
//
// @Description: 是否代理下的餐厅管理员
// @Author: Rixat
// @Time: 2023-12-02 07:43:04
// @receiver
// @param c *gin.Context
func AgentResManager(admin models.Admin, restaurantManager models.Admin) bool {
	if IsAgent(admin) && IsRestaurantManager(restaurantManager) && admin.AdminAreaID == restaurantManager.AdminAreaID {
		return true
	}
	return false
}

// BelongByParent
//
// @Description: 根据管理员ParentID验证是否下级管理员
// @Author: Rixat
// @Time: 2023-12-02 08:17:15
// @receiver
// @param c *gin.Context
func IsParentAdmin(admin models.Admin, child models.Admin) bool {
	// 该管理员下级管理员绑定的ParentID
	ParentID := admin.GetParentId()
	if strings.Contains(child.ParentID, ParentID) {
		return true
	}
	return false
}

// UpdateAreaParams
//
// @Description: 更新列表中的区域信息，如果代理操作，区域信息填充代理的区域
// @Author: Rixat
// @Time: 2023-12-02 04:47:45
// @receiver
// @param c *gin.Context
func GetAdminAreaInfo(c *gin.Context, cityId int, areaId int) (int, int) {
	admin := GetAdmin(c)
	if admin.ID > 0 && IsAgent(admin) && (cityId == 0 || areaId == 0) {
		return admin.AdminCityID, admin.AdminAreaID
	}
	return cityId, areaId
}

// GetAdmin
//
// @Description: 获取当前登录用户
// @Author: Rixat
// @Time: 2023-12-02 08:27:30
// @receiver
// @param c *gin.Context
func GetAdmin(c *gin.Context) models.Admin {
	var (
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
	)
	return admin
}

// GetAdmin
//
// @Description: 获取当前商家端登录用户属于的餐厅ID
// @Author: Rixat
// @Time: 2023-12-02 08:27:30
// @receiver
// @param c *gin.Context
func GetRestaurantIdByMerchantAdmin(admin models.Admin) (int, error) {
	// 验证当前用户是否餐厅管理员（管理员或者副管理员）
	if admin.ID > 0 || admin.Type == constants.ADMIN_TYPE_RESTAURANT_ADMIN || admin.Type == constants.ADMIN_TYPE_RESTAURANT_ADMIN_SUB {
		// 获取最新的状态开启的商家ID
		var adminStore models.AdminStore
		tools.Db.Model(adminStore).
			Joins("left join t_restaurant on t_restaurant.id = b_admin_store.store_id").
			Where("b_admin_store.admin_id = ? and t_restaurant.state > 0 and t_restaurant.deleted_at is NULL", admin.ID).
			Order("b_admin_store.created_at desc").
			First(&adminStore)
		if adminStore.StoreID > 0 {
			return adminStore.StoreID, nil
		}
	}
	return 0, errors.New("restaurant_not_found")
}


// GetAdminFirstParent
//
//  @Author: YaKupJan
//  @Date: 2024-10-25 13:23:46
//  @Description: 获取admin的第一个上级
//  @param admin
//  @param parentType
//  @return models.Admin
//  @return error
func GetAdminFirstParent(admin models.Admin, parentType int) (models.Admin, error) {
	db := tools.GetDB()
	// 获取当前的 type
	parentIdsStr := admin.ParentID
	parentIds := strings.Split(parentIdsStr, ",")
	var parentAdmin models.Admin
	err := db.Model(models.Admin{}).Where("id IN ?", parentIds).Where("type = ?", parentType).First(&parentAdmin).Error
	return parentAdmin,err
}

func GetRestaurantInfoByContent(c *gin.Context) (models.Restaurant, error) {
	admin := GetAdmin(c)
	if admin.Type != 5 && admin.Type != 6 {
		return models.Restaurant{}, errors.New("not_fund_merchant")
	}
	var res models.Restaurant
	resDB := tools.GetDB().Model(models.Restaurant{}).Select(
		"t_restaurant.*").
		Joins("INNER JOIN b_admin_store ON t_restaurant.id = b_admin_store.store_id").
		Joins("LEFT JOIN t_admin ON t_admin.id = b_admin_store.admin_id").
		Where("b_admin_store.admin_id=?", admin.ID).
		Where("t_admin.type in (5,6)"). //商家端登录 角色 5:店铺管理员 6:店铺副管理员
		Where("t_restaurant.deleted_at IS NULL")

	rs := resDB.First(&res)
	if rs.RowsAffected < 1 {
		return models.Restaurant{}, errors.New("not_fund_merchant")
	}
	return res, nil
}

// HasAccessToRestaurantInCityAndArea
//
//  @Author: YaKupJan
//  @Date: 2024-11-05 11:15:06
//  @Description: 检查用户是否有权限访问指定城市和区域内的餐厅。
//  @param restaurantId
//  @param cityId
//  @param areaId
//  @return bool
func HasAccessToRestaurantInCityAndArea(c *gin.Context, restaurantId, cityId, areaId int) bool {
	// 如果是 超管 或者 管理员 放行
	admin := GetAdmin(c)
	if admin.IsOwner() || admin.IsAdmin() {
		return true
	}
	// 如果当前餐厅不在该区域内 那就返回false
	db := tools.GetDB()
	var count int64
	db.Model(models.Restaurant{}).Select("id").
		Where("id = ?", restaurantId).
		Where("city_id = ?", cityId).
		Where("area_id = ?", areaId).
		Count(&count)
	return count > 0
}