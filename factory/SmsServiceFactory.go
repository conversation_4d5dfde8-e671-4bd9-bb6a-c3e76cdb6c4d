package factory

import (
	"fmt"
	"mulazim-api/models"
	"mulazim-api/services/sms"
	"mulazim-api/tools"
)

const (
	// 数据库中存储供应商配置的 key: aliyun/zhutong
	SmsSupplierKey = "sms_supplier"
	// 默认短信供应商
	DefaultSmsSupplier = sms.SupplierZhuTong

)

type SmsServiceFactory struct{}


// CreateSmsService 根据供应商名称创建对应的短信服务实例
func (f *SmsServiceFactory) CreateSmsService(supplier ...string) sms.SmsService {
	var supplierValue string
	var err error

	switch len(supplier) {
	case 0:
		// 如果未传入供应商名称，则从数据库中获取
		supplierValue, err = f.getSupplierFromDB()
		if err != nil {
			tools.Logger.Errorf("从数据库获取短信供应商失败，将使用默认供应商: %s, 错误: %v",DefaultSmsSupplier,err)
			supplierValue = DefaultSmsSupplier
		}
	case 1:
		// 如果传入一个供应商名称，则直接使用
		supplierValue = supplier[0]
	default:
		tools.Logger.Errorf("传入的供应商参数数量无效，必须为0或1，将使用默认供应商: %s, 参数数量: %d", DefaultSmsSupplier, len(supplier))
		supplierValue = DefaultSmsSupplier
	}

	// 根据供应商名称创建对应的服务实例
	switch supplierValue {
	case sms.SupplierZhuTong:
		return &sms.ZhuTongSmsService{}
	case sms.SupplierAliYun:
		return &sms.AliYunSmsService{}
	default:
		tools.Logger.Errorf("不支持的短信供应商: %s，将使用默认供应商: %s", supplierValue, DefaultSmsSupplier)
		return &sms.ZhuTongSmsService{}
	}
}

// getSupplierFromDB 从数据库中获取供应商配置
func (f *SmsServiceFactory) getSupplierFromDB() (string, error) {
	db := tools.GetDB()
	var appConfig models.AppConfig

	// 查询数据库中的供应商配置
	result := db.Model(&models.AppConfig{}).
		Where("`key` = ? AND state = ?", SmsSupplierKey, 1).
		First(&appConfig)

	// 检查查询是否出错
	if result.Error != nil {
		return "", fmt.Errorf("查询短信供应商配置失败: %w", result.Error)
	}

	// 检查是否有有效记录
	if appConfig.ID == 0 || appConfig.Value == "" {
		return "", fmt.Errorf("数据库中未配置短信供应商或配置为空")
	}

	// 返回供应商名称
	return appConfig.Value, nil
}