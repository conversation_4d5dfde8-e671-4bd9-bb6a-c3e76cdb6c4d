


const automator = require('miniprogram-automator')

var projectPath = "D:/PHP_PROJECT/mulazim/mulazimmini";



// 接收 payMethod 参数

console.log('args:', process.argv);
var argIndex = 2;
for(var i=0;i<process.argv.length;i++){
  if(process.argv[i]=='--'){
    argIndex = i+1;
  }
}
const args = process.argv.slice(argIndex);
const payMethod = args[0] || 'defaultPayMethod'; // 默认支付方式



describe('优惠订单下载', () => {
  let miniProgram
  let page

  beforeAll(async () => {
    miniProgram = await automator.launch({
      projectPath: projectPath,
      timeout: 300000
    })
    // await miniProgram.remote()
    page = await miniProgram.reLaunch('/pages/index/index')
    await page.waitFor(500)
  }, 300000)

  
  it('优惠订单下载', async () => {
    var element = await page.$('.discountTip--img-close')
    if(element){
      console.log(await element.attribute('class'))
      await page.waitFor(500)
      await element.tap()
      await page.waitFor(500)
    }
    

    const tabs = await page.$$('.getTabbar-index--menu-item')
    const tab = await tabs[2]
    tab.tap()
    await page.waitFor(500)

    const lists = await page.$$('.discount--discount-item')
    const list = await lists[0]
    list.tap()
    await page.waitFor(1500)

    //点击+号
    let foodListPage = await miniProgram.currentPage()
    const adds = await foodListPage.$$('.foodCard-index--icon .foodCard-index--addNumber')
    var add = await adds[0]
    add.tap()

    await page.waitFor(500)
    if (adds.length > 1) {
      add = await adds[1]
      add.tap()
      await page.waitFor(500)
    }


// 点击下单按钮  card--submit-form card--submit-form-block
    const confirm = await foodListPage.$('.card--submit-form')
    confirm.tap()
    await page.waitFor(1500)



    let orderCreatePage = await miniProgram.currentPage()
    const orderCreate = await orderCreatePage.$('.submit-form')
    orderCreate.tap()
    await page.waitFor(1500)

  
  })
  


  afterAll(async () => {
    await page.waitFor(1500)

    await miniProgram.close()
  })
})