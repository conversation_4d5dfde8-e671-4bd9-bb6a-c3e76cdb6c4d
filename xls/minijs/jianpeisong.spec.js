


const automator = require('miniprogram-automator')

var projectPath = "D:/PHP_PROJECT/mulazim/mulazimmini";


// 接收 payMethod 参数

// //console.log('args:', process.argv);
var argIndex = 2;
for(var i=0;i<process.argv.length;i++){
  if(process.argv[i]=='--'){
    argIndex = i+1;
  }
}
const args = process.argv.slice(argIndex);
const payMethod = args[0] || 'defaultPayMethod'; // 默认支付方式


describe('减配送费订单下载', () => {
  let miniProgram
  let page

  beforeAll(async () => {
    try{
      miniProgram = await automator.launch({
        projectPath: projectPath,
        timeout: 300000
      })
      // await miniProgram.remote()
      page = await miniProgram.reLaunch('/pages/index/index')
      await page.waitFor(500)
    }catch(e){console.log(e)}
    
  }, 300000)

  
  it('减配送费订单下载', async () => {
    var element = await page.$('.discountTip--img-close')
    if(element){
      //console.log(await element.attribute('class'))
      await page.waitFor(500)
      await element.tap()
      await page.waitFor(500)
    }
    

    const tabs = await page.$$('.home--foodclass-title')
    const tab = await tabs[2]
    tab.tap()
    await page.waitFor(500)

    const lists = await page.$$('.restaurant--restaurant-list')
    const list = await lists[0]
    list.tap()
    await page.waitFor(1500)

    //点击+号
    let foodListPage = await miniProgram.currentPage()
    const adds = await foodListPage.$$('.foodCard-index--icon .foodCard-index--addNumber')
    var add = await adds[0]
    for(var i = 0; i < 5; i++){
        add.tap()
        await page.waitFor(200)
    }

    await page.waitFor(500)
    if (adds.length > 1) {
      add = await adds[1]
      add.tap()
      await page.waitFor(500)
    }

    const confirm = await foodListPage.$('.card--submit-form')
    confirm.tap()
    await page.waitFor(2500)

    let orderCreatePage = await miniProgram.currentPage()
    await page.waitFor(3500)
    const orderCreate = await orderCreatePage.$('.submit-form')
    orderCreate.tap()
    await page.waitFor(4500)

    let orderPayPage = await miniProgram.currentPage()
    
    //pay-item
    // console.log('orderPayPage',orderPayPage);
    const payItems = await orderPayPage.$$('.pay .pay-item')
    
    // console.log('payItems',payItems);
    
    if(payMethod == "cash"){
      var item =await payItems[1]
      item.tap()

    }else{
      var item =await payItems[0]
      item.tap()
    }

    const payBtn = await orderPayPage.$$('.btn-box .submit-form')
    // console.log('payBtn',payBtn);
    try{

      var item =await payBtn[0]
      item.tap()
    }catch(e){
      console.log('error',e);
    }

    await page.waitFor(2500)
    
  
  }, 300000)
  


  afterAll(async () => {
    await page.waitFor(1500)

    try{
      await miniProgram.close()
    }catch(e){console.log(e)}
    
  })
})