const automator = require('miniprogram-automator')

var projectPath = "D:/PHP_PROJECT/mulazim/mulazimmini";


// 接收 payMethod 参数

// //console.log('args:', process.argv);
var argIndex = 2;
for(var i=0;i<process.argv.length;i++){
  if(process.argv[i]=='--'){
    argIndex = i+1;
  }
}
const args = process.argv.slice(argIndex);
const payMethod = args[0] || 'defaultPayMethod'; // 默认支付方式

describe('加价订单下单', () => {
  let miniProgram
  let page

  beforeAll(async () => {
    try{
      miniProgram = await automator.launch({
        projectPath: projectPath,
        timeout: 300000
      })
      page = await miniProgram.reLaunch('/pages/index/index')
      await page.waitFor(500)
    }catch(e){
      console.log(e);
    }
    
  }, 300000)

  it('加价订单下单', async () => {
    // 使用 payMethod 参数
    // //console.log('支付方式:', payMethod);

    var element = await page.$('.discountTip--img-close')
    if (element) {
      await element.attribute('class')
      await page.waitFor(500)
      await element.tap()
      await page.waitFor(500)
    }

    const lists = await page.$$('.restaurant--enclosure-item')
    var resId = 7978;
    for (var i = 0; i < lists.length; i++) {
      element = await lists[i];
      try {
        var rid = parseInt(await element.attribute('data-id'))
        if (rid == resId) {
          await element.tap();
          break;
        }
        // //console.log('id-data=', await element.attribute('data-id'))
      } catch (e) {
        // //console.log('id-data=error', e)
      }
    }
    await page.waitFor(1500)
    let foodListPage = await miniProgram.currentPage()
    await foodListPage.waitFor(1500)

    //foodLists--foods-scroll
    var scroll = await foodListPage.$('.foodLists--foods-scroll')
    const y = (await scroll.scrollHeight()) - 250
    await scroll.scrollTo(0, y)
    await foodListPage.waitFor(1500)
    // await miniProgram.pageScrollTo(y)

    // await foodListPage.waitFor(10500)
    // return

    var adds = await foodListPage.$$('.foodCard-index--icon .foodCard-index--addNumber')
    ////console.log('adds', adds);
    if (adds.length==0){
      adds = await foodListPage.$$('.foodCard-index--icon')
    }

    var foodsIds = [
      271416,
      271414,
      271415,
      271421,
      367749
    ];
    for (var i = 0; i < adds.length; i++) {
      element = await adds[i];
      try {
        var rid = parseInt(await element.attribute('data-id'))
        for(var j =0 ; j<foodsIds.length;j++){

          if (rid == foodsIds[j]) {
            for (var k = 0; k < 2; k++) {
              element.tap()
              await page.waitFor(200)
            }
            // break;
          }
        }
        // //console.log('id-data=', await element.attribute('data-id'))
      } catch (e) {
        // //console.log('id-data=error', e)
      }
    }

    ////console.log('adds', adds);
    // var add = await adds[0]
    // for (var i = 0; i < 5; i++) {
    //   add.tap()
    //   await page.waitFor(200)
    // }

    await page.waitFor(500)
    // if (adds.length > 1) {
    //   add = await adds[1]
    //   add.tap()
    //   await page.waitFor(500)
    // }

    const confirm = await foodListPage.$('.card--submit-form')
    confirm.tap()
    await page.waitFor(2500)

    let orderCreatePage = await miniProgram.currentPage()
    await page.waitFor(3500)
    const orderCreate = await orderCreatePage.$('.submit-form')
    orderCreate.tap()
    await page.waitFor(4500)

    let orderPayPage = await miniProgram.currentPage()
    
    //pay-item
    // console.log('orderPayPage',orderPayPage);
    const payItems = await orderPayPage.$$('.pay .pay-item')
    
    // console.log('payItems',payItems);
    
    if(payMethod == "cash"){
      var item =await payItems[1]
      item.tap()

    }else{
      var item =await payItems[0]
      item.tap()
    }

    const payBtn = await orderPayPage.$$('.btn-box .submit-form')
    // console.log('payBtn',payBtn);
    try{

      var item =await payBtn[0]
      item.tap()
    }catch(e){
      console.log('error',e);
    }

    await page.waitFor(2500)

    // let orderDetailPage = await miniProgram.currentPage()

    // await page.waitFor(30000)
    

  }, 300000)

  afterAll(async () => {
    await page.waitFor(1500)
    try{
      await miniProgram.close()
    }catch(e){

    }
    
  })
})