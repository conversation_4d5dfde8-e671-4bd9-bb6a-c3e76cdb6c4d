package shipper

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"mime/multipart"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/errors"
	"mulazim-api/factory"
	"mulazim-api/lang"
	"mulazim-api/models"
	shipperService "mulazim-api/services/shipperv2"
	"mulazim-api/tools"
	"strconv"
	"strings"
	"time"

	"mulazim-api/resources/ocrEntity"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type InsuranceController struct {
	controllers.BaseController
}


// 获取 资料审核信息
// 0:未提交,1:待审核 ,2:审核未通过 ,3:后台审核通过 ,4:待提交资料 ,5:已提交资料 ,6:资料未通过,7:对公账户待确认  ,8:待在线签约 ,9:待银商入网审核 ,10:银商入网成功,11:银商入网失败,
func (s InsuranceController) GetCheckState(c *gin.Context) {

	var (
		service      = shipperService.NewShipperService(c)
		l, _        = c.Get("lang_util")
		lang        = l.(lang.LangUtil)
	)
	
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	// 查询 入驻信息
	info, errMsg := service.CheckInsuranceState(admin.ID, admin.AdminCityID, admin.AdminAreaID)
	if len(errMsg) > 0 {

		s.Fail(c, errMsg, -1000)
		return
	}

	state := info["state"].(int)
	
	info["state_name"] = s.TransArr(c,"real_name_states")[state]

	info["hide_gps_permission_next_time"] = service.GetHideGpsPermissionNextTime(c)
	info["insurance_guide_url"] = service.BaseService.GetAppConfig("insurance_guide_url_"+lang.Lang)
	s.Success(c, info, "success", 200)

}


// PostSmsCode
//
//	获取短信验证码
//
//	mobile 手机号
func (s InsuranceController) PostSmsCode(c *gin.Context) {
	type Params struct {
		Mobile string `form:"mobile" binding:"required"` // 手机号
	}
	var (
		redisHelper = tools.GetRedisHelper()
		params      Params
		err         = c.ShouldBind(&params)
	)

	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	if !(admin.Type == 8 || admin.Type ==9) { //不是配送 
		s.Fail(c, "admin_is_not_shipper", -1000)
		return
	}

	if !tools.VerifyMobileFormat(params.Mobile) { //手机号 格式判断
		s.Fail(c, "mobile_incorrect", -1000)
		return
	}

	cacheKey := fmt.Sprintf("send_mobile_%s", params.Mobile)
	//2分钟 能获取一次
	exists, _ := redisHelper.Exists(c, cacheKey).Result()
	if exists == 0 {
		redisHelper.Set(c, cacheKey, params.Mobile, 2*time.Minute)
	} else {
		s.Fail(c, "sms_in_two_minutes", -1000)
		return
	}

	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))
	code := fmt.Sprintf("%06v", rnd.Int31n(1000000))

	cacheKey = fmt.Sprintf("shipper_insurance_mobile_%s", params.Mobile)

	redisHelper.Set(c, cacheKey, code, 30*time.Minute) //验证码保存 30 分钟

	//contentMap := map[string]string{
	//	"code": code,
	//}
	//dataType, _ := json.Marshal(contentMap)
	//content := string(dataType)
	//
	//err = tools.AliSmsSend(params.Mobile, string(content))
	//if err != nil {
	//	panic(err)
	//	return
	//}
	// 发送验证码
	smsServiceFactory := &factory.SmsServiceFactory{}
	smsService := smsServiceFactory.CreateSmsService()
	codeResp, err := smsService.SendVerificationCode(params.Mobile, code)
	if err != nil {
		tools.Logger.Errorf("发送验证码失败: %v \n",err)
		panic(err)
		return
	}
	tools.Logger.Infof("发送验证码成功 结果: %v \n",codeResp)
	s.Ok(c)
}

//实名认证信息获取
func (s InsuranceController) GetInfo(c *gin.Context) {
	var (
		service      = shipperService.NewShipperService(c)
		// transformer = merchantTransformer.NewCollectionInfoTransformer(c)
	)
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	stuffs := service.GetInsuranceInfo(admin.ID)

	state := stuffs["state"].(int)
	editable := false
	if state == 0 || state == 2 || state == 10 {
		editable = true
	}
	stuffs["editable"] = editable
	stuffs["state_name"] = s.TransArr(c,"real_name_states")[state]
	s.Success(c, stuffs, "msg", 200)
}





// PostIdCardInfo
//
//	@Time 2023-01-09 13:59:34
//	<AUTHOR>
//	@Description: 第2页 营业环境 Business environment
//	@receiver s SelfSignController
//	@param c
func (s InsuranceController) PostIdCardInfo(c *gin.Context) {
	type Params struct {
		IdcardImg         string `form:"idcard_img" binding:"required"`
		MerIdcardName     string `form:"mer_idcard_name" binding:"required"`       // 身份证姓名
		MerIdcardNum      string `form:"mer_idcard_num" binding:"required,len=18"` // 身份证号
		MerIdcardStart    string `form:"mer_idcard_start" binding:"required"`      // 身份证有效期开始时间
		MerIdcardEnd      string `form:"mer_idcard_end" binding:"required"`        // 身份证有效
		// MerMobile         string `form:"mer_mobile" `                              // 手机号
		MerIdcardTimeType string `form:"mer_idcard_time_type" binding:"required"`  // 身份证有效期类型
		MerAddress  string `form:"mer_address" `                      // 住址
		MerSex            int    `form:"mer_sex" `                                 //性别

		// Captcha           string `form:"captcha" binding:"required"`              // 验证码
	}
	var (
		params   Params
		err      = c.ShouldBind(&params)
		
		service = shipperService.NewShipperService(c)
		// redis    = tools.GetRedisHelper()
	)
	if err != nil {
		panic(err)
		return
	}

	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	
	state, i := service.CheckColMerInfoState(admin.ID)
	if !state {
		panic(errors.CustomError{Msg: "merInfoaudit" + strconv.Itoa(i)})
	}

	

	if  params.MerSex == 0 { //小微商户
		s.Fail(c, "mini_shop_must_fill_gender", -1000)
		return
	}
	ok, er := service.SaveIdCardInfo(admin.ID, params.IdcardImg, params.MerIdcardName, params.MerIdcardNum, params.MerIdcardStart, params.MerIdcardEnd,   params.MerIdcardTimeType,  params.MerSex,params.MerAddress)
	if ok {
		s.Ok(c)
	} else {
		s.Fail(c, er, -1000)
	}
}

// PostAccountInfo
//
//	@Time 2023-01-11 19:01:59
//	<AUTHOR>
//	@Description: 第3页 账户信息 Account information
//	@receiver s SelfSignController
//	@param c
func (s InsuranceController) PostAccountInfo(c *gin.Context) {
	type Params struct {
		AcctImg        string `form:"acct_img" binding:"required"`
		// BankAcctType   *int   `form:"bank_acct_type" binding:"required"`          // 银行账户类型
		BankAcctNum    string `form:"bank_acct_num" binding:"required"`           // 银行账户号
		BankId         int    `form:"bank_id" binding:"required"`                 // 开户银行名称
		BankProvinceId int    `form:"bank_province_id" binding:"required"`        // 开户银行所在省份
		BankCityId     int    `form:"bank_city_id" binding:"required"`            // 开户银行所在市
		BankAreaId     int    `form:"bank_area_id" binding:"required"`            // 开户银行所在区
		BankBranchName string `form:"bank_branch_name" binding:"required"`        // 开户银行支行名称
		BankBranchCode string `form:"bank_branch_code" binding:"required"`        // 开户银行支行联行号
		BankBindMobile string `form:"bank_bind_mobile" binding:"required,len=11"` // 银行预留手机号
	}
	var (
		params   Params
		err      = c.ShouldBind(&params)
		
		service = shipperService.NewShipperService(c)
		redis    = tools.GetRedisHelper()
	)
	if err != nil {
		tools.Logger.Info("参数绑定失败!", err)
		panic(err)
		return
	}
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	
	state, i := service.CheckColMerInfoState(admin.ID)
	if !state {
		panic(errors.CustomError{Msg: "merInfoaudit" + strconv.Itoa(i)})
	}
	idCardInfo, _ := service.GetIdCardInfo(admin.ID)
	if len(params.BankBindMobile) > 0 {

		Captcha := c.PostForm("captcha")

		if configs.CurrentEnvironment == "production" { //正式环境
			if idCardInfo.BankBindMobile != params.BankBindMobile {

				//手机号不同的话需要要求验证码

				// 验证码
				cacheKey := fmt.Sprintf("shipper_insurance_mobile_%s", params.BankBindMobile)

				if len(Captcha) == 0 {
					s.Fail(c, "captcha_requiered", -1000)
					return
				}
				get := redis.Get(c, cacheKey)
				if get.Val() != Captcha {
					s.Fail(c, "captcha_incorrect", -1000)
					return
				}

			}
		} else {
			// 测试过程中 验证码 999999 通过
			if Captcha != "999999" {

				if idCardInfo.BankBindMobile != params.BankBindMobile {

					//手机号不同的话需要要求验证码

					// 验证码
					cacheKey := fmt.Sprintf("shipper_insurance_mobile_%s", params.BankBindMobile)

					if len(Captcha) == 0 {
						s.Fail(c, "captcha_requiered", -1000)
						return
					}
					get := redis.Get(c, cacheKey)
					if get.Val() != Captcha {
						s.Fail(c, "captcha_incorrect", -1000)
						return
					}

				}
			}
		}
	}
	
	ok, msg := service.SaveAccountInfo(admin.ID, params.AcctImg, 0, params.BankAcctNum, params.BankId, params.BankCityId, params.BankAreaId, params.BankBranchName, params.BankBranchCode, params.BankProvinceId, params.BankBindMobile)
	if ok {
		s.Ok(c)
	} else {
		m := "error_happend"
		if len(msg) > 0 {
			m = msg
		}
		s.Fail(c, m, -1000)
	}
}







//
//	@Time 2023-01-10 13:00:52
//	<AUTHOR>
//	@Description: 上传文件
//	@receiver s SelfSignController
//	@param c
func (s InsuranceController) PostUploadFile(c *gin.Context) {
	type Params struct {
		File         *multipart.FileHeader `form:"file" binding:"required"`          // 营业执照
		DocType      string                `form:"doc_type" binding:"required"`      // 证件类型
		// RestaurantId string                `form:"restaurant_id" binding:"required"` // 餐厅id
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = shipperService.NewShipperService(c)
	)
	if err != nil {
		panic(err)
		return
	}

	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	var fileMap map[string]string
	
	fileMap, errMsg := service.UploadFile(c, params.File, params.DocType,tools.ToString(admin.ID))
	fileFullPath := fileMap["fileFullPath"]
	fileUrl := configs.MyApp.CdnUrl + fileMap["fileUrl"]

	tools.Logger.Info("上传文件", fileFullPath)
	if errMsg != "" {
		tools.Logger.Error("上传文件错误", errMsg)
		s.Fail(c, errMsg, -1000)
		return
	} else {
		//图片类型
		// 0001	法人身份证
		// 0011	身份证反面
		// 0002	商户营业执照
		// 0003	商户税务登记证
		// 0004	组织机构代码证
		// 0005	开户许可证
		// 0007	手持身份证自拍照
		// 0008	辅助证明材料
		// 0013	辅助证明材料 1
		// 0014	辅助证明材料 2
		// 0015	室内照片
		// 0099	其他材料
		// 0025	银行卡正面照
		// 0026	银行卡背面照
		// 0032	民办非登记证

		//1001  食品经营许可证
		//1002  股东身份证前面
		//1003	股东身份证背面

		switch params.DocType {
		case "0001", "1002", "1005":
			{
				var idF ocrEntity.IDFront
				rs := tools.ShumaiOcr(1, fileFullPath)
				json.Unmarshal([]byte(rs), &idF)

				frontInfo := idF.Data.FrontInfo

				frontInfo.Image = fileUrl
				s.Success(c, frontInfo, "success", 200)
				return

			}
		case "0011", "1003", "1006":
			{
				var idF ocrEntity.IDBack
				rs := tools.ShumaiOcr(1, fileFullPath)

				json.Unmarshal([]byte(rs), &idF)

				info := idF.Data.Info
				info.Image = fileUrl

				//日期格式化
				//carbon.ParseByFormat(ShareholderIdcardStart, "Y-m-d")
				if len(info.Timelimit) > 0 {
					tms := strings.Split(info.Timelimit, "-")

					startTime := tms[0]

					endTime := tms[1]

					err1 := carbon.Parse(startTime).Error
					if err1 == nil {
						info.StartTime = carbon.Parse(startTime).ToFormatString("Y-m-d")
					}
					err2 := carbon.Parse(endTime).Error
					if err2 == nil {
						info.EndTime = carbon.Parse(endTime).ToFormatString("Y-m-d")
					} else {
						if endTime != "长期" {
							info.EndTime = ""
						}
					}

				}

				s.Success(c, info, "success", 200)
				return

			}
	
		case "0025":
			//银行卡
			var bc ocrEntity.BankCardEntity
			rs := tools.ShumaiOcr(2, fileFullPath)

			json.Unmarshal([]byte(rs), &bc)

			info := bc.Data

			db := tools.GetDB()
			if len(info.CardNumber) > 0 {
				info.IsBankCard = tools.CheckBankCardNumber(info.CardNumber)
				if info.IsBankCard {
					// merService := merchantService.NewMerchantService(c)
					// resData1 := merService.GetRestaurant(params.RestaurantId)

					cityCode := ""
					areaCode := ""

					// if resData.ID > 0 {
						cityMap := make(map[string]interface{}, 0)
						db.Table("b_self_sign_area").Where("mlz_city_id = ?", admin.AdminCityID).Select("level,name_zh,code,p_code").Scan(&cityMap)
						if cityMap != nil && cityMap["code"] != nil {
							cityCode = tools.ToString(cityMap["code"])
						}
						areaMap := make(map[string]interface{}, 0)
						db.Table("b_self_sign_area").Where("mlz_area_id = ?", admin.AdminAreaID).Select("level,name_zh,code,p_code").Scan(&areaMap)
						if areaMap != nil && areaMap["code"] != nil {
							areaCode = tools.ToString(areaMap["code"])
						}

					// }
					bankNameMap := make(map[string]interface{}, 0)
					er11 := db.Table("self_sign_bank_card_log").Where("card_number = ?", info.CardNumber).Select("bank_id,province_code,city_code,area_code").Scan(&bankNameMap).Error
					if er11 == nil && (bankNameMap != nil && bankNameMap["bank_id"] != nil) {
						info.BankId = tools.ToInt(bankNameMap["bank_id"])
						info.ProvinceCode = tools.ToInt(bankNameMap["province_code"])
						info.CityCode = tools.ToInt(bankNameMap["city_code"])
						info.AreaCode = tools.ToInt(bankNameMap["area_code"])
					} else {
						rs2, er := tools.BankRegion(info.CardNumber)
						if er == nil {
							var bcr ocrEntity.BankCardRegionEntity
							json.Unmarshal([]byte(rs2), &bcr)
							info2 := bcr.Data
							if len(info2.Bank) > 0 {

								bankId := 0

								if info2.Bank == "乌鲁木齐市商业银行" {
									info2.Bank = "乌鲁木齐银行"
								}
								var bank models.SelfSignBank
								er4 := db.Where("name_zh like ?", info2.Bank+"%").Select("id").First(&bank).Error
								if er4 == nil && bank.ID > 0 {
									bankId = bank.ID
								} else {
									//不存在的银行写一个数据
									bk := models.SelfSignBank{
										NameUg:    info2.Bank,
										NameZh:    info2.Bank,
										State:     1,
										Index:     1,
										CreatedAt: carbon.Now().Carbon2Time(),
									}
									db.Table("b_self_sign_bank").Create(&bk)
									bankId = bk.ID
									tools.SendDingDingMsg("新增银行数据:" + info2.Bank)

								}
								info.BankId = bankId

								if len(info2.City) > 0 {

									city2Map := make(map[string]interface{}, 0)
									if strings.Contains(info2.City, "地区") {
										info2.City = strings.Split(info2.City, "地区")[0]
									} else if strings.Contains(info2.City, "市") {
										info2.City = strings.Split(info2.City, "市")[0]
									} else if strings.Contains(info2.City, "州") {
										info2.City = strings.Split(info2.City, "州")[0]
									}
									db.Table("b_self_sign_area").Where("level = 2 and name_zh like ?", info2.City+"%").Select("level,name_zh,code,p_code").Order("id asc").Limit(1).Scan(&city2Map)
									if city2Map != nil && city2Map["code"] != nil {

										cityCode2 := tools.ToString(city2Map["code"])
										if cityCode == cityCode2 {
											info.CityCode = tools.ToInt(cityCode)
											info.AreaCode = tools.ToInt(areaCode)
										} else {
											info.CityCode = tools.ToInt(cityCode2)
										}
									}

									if len(info2.Province) > 0 {
										provinceMap := make(map[string]interface{}, 0)
										if strings.Contains(info2.Province, "新疆") {
											info2.Province = "新疆"
										}
										db.Table("b_self_sign_area").Where("level = 1 and name_zh like ?", info2.Province+"%").Select("level,name_zh,code,p_code").Scan(&provinceMap)
										if provinceMap != nil && provinceMap["code"] != nil {
											info.ProvinceCode = tools.ToInt(provinceMap["code"])
										}
									}

									err3 := db.Table("self_sign_bank_card_log").Create(&map[string]interface{}{
										"card_number":   info.CardNumber,
										"bank_name":     info2.Bank,
										"bank_id":       bankId,
										"province_code": info.ProvinceCode,
										"city_code":     info.CityCode,
										"area_code":     info.AreaCode,
										"created_at":    carbon.Now().ToDateTimeString(),
									}).Error
									if err3 != nil {
										tools.Log(err3.Error())
									}

								}

							}
						}
					}

				}
			}

			info.Image = fileUrl
			s.Success(c, info, "success", 200)
			return
 

		}
		info := map[string]string{
			"image": fileUrl,
		}
		s.Success(c, info, "success", 200)

	}

}

//购买保险 
func (s InsuranceController) PostInsuranceBuy(c *gin.Context) {
	var (
		service      = shipperService.NewShipperService(c)
	)
	
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	// 查询 入驻信息
	err := service.InsuranceBuy(admin.ID,admin.AdminCityID,admin.AdminAreaID)
	if err!= nil {

		s.Fail(c, err.Error(), -1000)
		return
	}	
	s.Success(c, nil, "success", 200)
}


//购买保险 
func (s InsuranceController) GetInsuranceLog(c *gin.Context) {
	var (
		service      = shipperService.NewShipperService(c)
	)
	
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	// 查询 入驻信息
	startDate :=c.DefaultQuery("start_date","")
	endDate :=c.DefaultQuery("end_date","")
	result,err := service.InsuranceLog(c,admin.ID,startDate,endDate)
	if err!= nil {

		s.Fail(c, err.Error(), -1000)
		return
	}	
	s.Success(c, result, "success", 200)
}


//保险 状态获取
func (s InsuranceController) GetInsuranceStatus(c *gin.Context) {
	var (
		service      = shipperService.NewShipperService(c)
		l, _        = c.Get("lang_util")
		lang        = l.(lang.LangUtil)
	)
	
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	// 查询 入驻信息
	result,err := service.InsuranceStatus(admin.ID)
	if err!= nil {

		s.Fail(c, err.Error(), -1000)
		return
	}
	result["insurance_guide_url"] = service.BaseService.GetAppConfig("insurance_guide_url_"+lang.Lang)	
	s.Success(c, result, "success", 200)
}


//保险 取消
func (s InsuranceController) PostInsuranceCancel(c *gin.Context) {
	var (
		service      = shipperService.NewShipperService(c)
	)
	
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	// 查询 入驻信息
	reject :=tools.ToInt(c.DefaultPostForm("reject","0"))
	err := service.InsuranceCancel(admin.ID,admin.AdminCityID,admin.AdminAreaID,reject)
	if err!= nil {

		s.Fail(c, err.Error(), -1000)
		return
	}	
	s.Success(c, nil, "success", 200)
}