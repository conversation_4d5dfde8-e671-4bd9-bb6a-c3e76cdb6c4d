package order

import (
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/models/chat"
	"mulazim-api/services"
	"mulazim-api/tools"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ReportController struct {
	controllers.BaseController
}

type ReportRequestData struct {
	Type    int    `form:"type" binding:"required,oneof=20 21"`
	Image   string `form:"image" binding:"required"`
	Content string `form:"content"`
	Ip      string
}

type UriData struct {
	OrderId int `uri:"order_id" biding:"required,"`
}

// Store godoc
// @Route /order/{id}/reports [post]
// @Summary 订单上报情况
// @Accept json
// @Produce json
// @Param order_id 订单ID
// <AUTHOR> Kirem
// @Date 2023-10-10
func (r *ReportController) Store(c *gin.Context) {
	var (
		requestData ReportRequestData
		order       models.OrderToday
		uriData     UriData
		err         = c.ShouldBind<PERSON>ri(&uriData)
		admin       models.Admin
		l, _        = c.Get("lang_util")
		lang        = l.(lang.LangUtil)
	)
	if err != nil {
		panic(err)
		return
	}
	// 验证数据
	if err := c.ShouldBind(&requestData); err != nil {
		panic(err)
		return
	}

	anyAdmin, _ := c.Get("admin")
	admin = anyAdmin.(models.Admin)

	result := tools.GetDB().First(&order, uriData.OrderId)
	// 判断订单是否存在并是否该配送员所有
	if result.RowsAffected <= 0 || order.ShipperID != admin.ID {
		r.Fail(c, "order_not_found", 404)
		return
	}
	requestData.Ip = c.ClientIP()

	cardContent := map[string]interface{}{
		"title_ug":     lang.TUg("reports"),
		"title_zh":     lang.TZh("reports"),
		"type_ug":      lang.TArrUg("report_types")[requestData.Type],
		"type_zh":      lang.TArrZh("report_types")[requestData.Type],
		"image":        tools.GetImageURLs(requestData.Image),
		"content":      requestData.Content,
		"created_time": carbon.Now().Format("Y-m-d H:i"),
	}
	var request = chat.ChatSendRequest{
		OrderID:     int64(order.ID),
		SenderType:  chat.CHAT_SENDER_SHIPPER,
		ContentType: chat.CHAT_CONTENT_TYPE_REPORT,
		Content:     requestData.Content,
		Image:       requestData.Image,
		CardType:    requestData.Type,
		CardContent: tools.MapToString(cardContent),
	}
	chatService := services.NewChatService(c)
	_, err = chatService.SendMessage(request, int64(admin.ID))
	// 保存上报情况记录
	err = chatService.SaveReport(admin, order, request.Content, request.Image)
	if err != nil {
		tools.Logger.Error("shipper-report-fail", err.Error())
		r.Fail(c, "failed", http.StatusBadRequest)
		return
	}

	r.Success(c, nil, "msg", 200)
}

// GetReportTypes
//
// @Description: 上报情况类别
// @Author: Rixat
// @Time: 2023-12-16 04:10:58
// @receiver
// @param c *gin.Context
func (r *ReportController) GetReportTypes(c *gin.Context) {
	var (
		l, _ = c.Get("lang_util")
		lang = l.(lang.LangUtil)
	)
	// 20 商户关门、21： 商户没有准备好订单
	leaveTypes := lang.TArr("report_types")
	resMap := make([]map[string]interface{}, 0)
	for key, value := range leaveTypes {
		resMap = append(resMap, map[string]interface{}{"id": key, "name": value})
	}
	r.Success(c, resMap, "", 200)
}
