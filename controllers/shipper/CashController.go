/**
@author: captain
@since: 2022/9/13
@desc: 配送客户端微信支付控制器类
**/

package shipper

import (
	"fmt"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	cashService "mulazim-api/services/shipper"
	"mulazim-api/tools"
	cashTransformer "mulazim-api/transformers/shipper"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type CashController struct {
	controllers.BaseController
}

// GetNotPayedOrderList
//
//	@Description: 获取配送员未缴纳订单列表
//	@author: Captain
//	@Time: 2022-09-20 12:49:07
//	@receiver cash *CashController
//	@param c *gin.Context
func (cash *CashController) GetNotPayedOrderList(c *gin.Context) {
	//validate
	type NOtPayedOrderListRequest struct {
		Limit     int    `form:"limit" binding:""`
		Page      int    `form:"page" binding:"required"`
		StartDate string `form:"start_date" binding:"date"`
		EndDate   string `form:"end_date" binding:"date"`
		Month     string `form:"month" binding:""`
		Day       string `form:"day" binding:"date"`
		CashClearState int    `form:"cash_clear_state" `  //现金订单是否 上缴 完成 
	}
	var request NOtPayedOrderListRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	var (
		l, _            = c.Get("lang_util")
		lang            = l.(lang.LangUtil)
		cashService     = cashService.NewCashService(c)
		cashTransformer = cashTransformer.NewCashTransformer(c)
	)

	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	if request.Limit == 0 {
		request.Limit = 50
	}
	list := cashService.GetShipperNotPayedOrders(admin, request.StartDate, request.EndDate, request.Month, request.Day, request.Page, request.Limit,request.CashClearState)
	header := cashService.GetShipperNotPayedOrderStatisticsHeader(admin, request.StartDate, request.EndDate, request.Month, request.Day,request.CashClearState)
	data := cashTransformer.FormatNotPayedOrderList(list, header, request.Page, request.Limit,request.CashClearState)
	c.JSON(200, gin.H{
		"status": 200,
		"msg":    lang.T("msg"),
		"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		"data":   data,
	})
}

// PostPay
//
//	@Description: 配送员现金订单支付（pay_type=1时客户扫平时客户端的订单付款二维码，pay_type=2时配送员通过app支付在线支付现金订单的金额）
//	@author: Captain
//	@Time: 2022-09-16 18:30:39
//	@receiver cash *CashController
//	@param c *gin.Context
func (cash *CashController) PostPay(c *gin.Context) {
	var (
		l, _        = c.Get("lang_util")
		lang        = l.(lang.LangUtil)
		orderID, _  = strconv.Atoi(c.PostForm("order_id"))
		payType, _  = strconv.Atoi(c.PostForm("pay_type"))
		cashService = cashService.NewCashService(c)
	)
	//fmt.Println(orderID, payType)
	//anyAdmin, _ := c.Get("admin")
	//admin := anyAdmin.(models.Admin)
	statusCode, result := cashService.Pay(c, orderID, payType)
	var data = map[string]interface{}{}
	fmt.Println("v3 pay params:", result)
	if statusCode == 200 {
		c.JSON(200, gin.H{
			"status": 200,
			"msg":    lang.T("msg"),
			"data":   result,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	} else {
		c.JSON(200, gin.H{
			"status": statusCode,
			"msg":    result["return_msg"],
			"data":   data,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	}

}

// GetPayQuery
//
//	@Description: 查询现金订单是否支付
//	@author: Captain
//	@Time: 2022-09-16 18:29:25
//	@receiver cash *CashController
//	@param c *gin.Context
func (cash *CashController) GetPayQuery(c *gin.Context) {
	var (
		l, _        = c.Get("lang_util")
		lang        = l.(lang.LangUtil)
		orderID, _  = strconv.Atoi(c.Query("order_id"))
		cashService = cashService.NewCashService(c)
	)
	result := cashService.PayQuery(c, orderID)
	var data = make([]map[string]interface{}, 0)
	if result {
		c.JSON(200, gin.H{
			"status": 200,
			"msg":    lang.T("msg"),
			"data":   data,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	} else {
		c.JSON(200, gin.H{
			"status": 301,
			"msg":    "",
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	}
}

// Notify
//
//	@Description: 微信支付异步通知接口
//	@author: Captain
//	@Time: 2022-09-17 12:07:44
//	@receiver cash *CashController
//	@param context *gin.Context
func (cash *CashController) Notify(context *gin.Context) {
	fmt.Println("微信支付异步通知接口被调用")
	cashService := cashService.NewCashService(context)
	rs, msg := cashService.Notify(context)
	if rs {
		context.JSON(200, gin.H{
			"code":    "SUCCESS",
			"message": msg,
		})
	} else {
		context.JSON(401, gin.H{
			"code":    "FAIL",
			"message": msg,
		})
	}
}

/***
 * @Author: [rozimamat]
 * @description: 统一支付
 * @Date: 2023-08-26 11:31:07
 * @param {*gin.Context} c
 */
func (cash *CashController) PostUnionPay(c *gin.Context) {
	var (
		l, _        = c.Get("lang_util")
		lang        = l.(lang.LangUtil)
		orderID, _  = strconv.Atoi(c.PostForm("order_id"))
		payPage     = c.PostForm("pay_page") //order_page  订单详情页面客户要付款  cash_page  配送员现金订单 要付款
		cashService = cashService.NewCashService(c)
	)
	statusCode, result := cashService.UnionPay(c, orderID, payPage)
	var data = map[string]interface{}{}
	tools.Logger.Info("union pay params:", result)
	if statusCode == 200 {
		c.JSON(200, gin.H{
			"status": 200,
			"msg":    lang.T("msg"),
			"data":   result,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	} else {
		c.JSON(200, gin.H{
			"status": statusCode,
			"msg":    result["return_msg"],
			"data":   data,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	}

}

// GetPayQuery
//
//	@Description: 查询现金订单是否支付
//	@author: Captain
//	@Time: 2022-09-16 18:29:25
//	@receiver cash *CashController
//	@param c *gin.Context
func (cash *CashController) GetPayQueryV2(c *gin.Context) {
	type Params struct {
		OrderID int `form:"order_id" binding:"required"`
	}
	var (
		params      Params
		cashService = cashService.NewCashService(c)
	)
	if err := c.ShouldBind(&params); err != nil {
		panic(err)
	}
	result := cashService.CheckCachClearState(params.OrderID)
	if result {
		cash.Ok(c)
		return
	}
	cash.Fail(c, "", 301)
}
