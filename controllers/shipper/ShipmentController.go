package shipper

import (
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	shipperService "mulazim-api/services/shipperv2"
	"mulazim-api/tools"

	shipperTransformer "mulazim-api/transformers/shipper"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ShipmentController struct {
	controllers.BaseController
}

// ShipperNewOrderList 获取配送员新订单列表
//
//	@Description:
//	@receiver shipper
//	@param c
func (shipper ShipmentController) GetShipperInfo(c *gin.Context) {
	var (
		l, _     = c.Get("lang_util")
		lang     = l.(lang.LangUtil)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
	
		shipperService = shipperService.NewShipperService(c)
	)

	data, er := shipperService.GetUserCenterStatistic(admin)
	shipperService.UpdateShowAdImageByAppConfig(c,&data)
	if er == nil {
		shipper.Success(c, data, "", 200)
	} else {
		shipper.Fail(c, lang.T("error_happend"), 301)
	}

}

// ShipperNewOrderList 获取配送员新订单列表
//
//	@Description:
//	@receiver shipper
//	@param c
func (shipper ShipmentController) GetIncomeStatistic(c *gin.Context) {
	type Params struct {
		Month string `form:"month" binding:"required"`
	}
	var (
		l, _     = c.Get("lang_util")
		lang     = l.(lang.LangUtil)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)

		shipperService = shipperService.NewShipperService(c)
	)
	var req Params
	if err := c.ShouldBind(&req); err != nil {
		panic(err)
	}
	data, er := shipperService.GetIncomeStatistic(admin, req.Month)
	if er == nil {
		shipper.Success(c, data, "", 200)
	} else {
		shipper.Fail(c, lang.T("error_happend"), 301)
	}

}

// ShipperNewOrderList 获取配送员订单统计
//
//	@Description:
//	@receiver shipper
//	@param c
func (shipper ShipmentController) GetOrderStatistic(c *gin.Context) {

	type Params struct {
		Month string `form:"month" binding:"required"`
	}
	var (
		l, _     = c.Get("lang_util")
		lang     = l.(lang.LangUtil)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)

		shipperService = shipperService.NewShipperService(c)
	)
	var req Params
	if err := c.ShouldBind(&req); err != nil {
		panic(err)
	}
	data, er := shipperService.GetOrderStatistic(admin, req.Month)
	if er == nil {
		shipper.Success(c, data, "", 200)
	} else {
		shipper.Fail(c, lang.T("error_happend"), 301)
	}

}

// PostAttendance
//
// @Description:创建事故和请假
// @Author: Rixat
// @Time: 2023-10-11 10:55:00
// @receiver
// @param c *gin.Context
func (shipper ShipmentController) PostAttendanceSave(c *gin.Context) {
	type Params struct {
		StartTime string `form:"start_time" binding:"required"` // 请假/事故
		EndTime   string `form:"end_time"`                      // 请假
		Type      int    `form:"type" binding:"required"`       // 2:请假,3:事故
		Images    string `form:"images" binding:""`             // 请假/事故
		LeaveType int    `form:"leave_type"`                    // 请假
		Remark    string `form:"remark" binding:"required"`     // 请假/事故
	}
	var (
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		params   Params
		err      = c.ShouldBind(&params)
		// service  = shipperService.NewShipperService(c)
	)
	if err != nil {
		panic(err)
	}
	tools.Logger.Info("attendance-params:", params)
	// 获取配送员的城市跟区域
	db := tools.Db
	var areas map[string]interface{}
	db.Table("admin_areas").Select("city_id,area_id").Where("admin_id = ?", admin.ID).Scan(&areas)
	//写到日志表
	attendanceLog := models.ShipperAttendanceLog{
		CityId:    tools.ToInt(areas["city_id"]),
		AreaId:    tools.ToInt(areas["area_id"]),
		ShipperId: admin.ID,
		Name:      admin.RealName,
		Mobile:    admin.Mobile,
		StartTime: carbon.Parse(params.StartTime).Carbon2Time(),
		Images:    params.Images,
		Remark:    params.Remark,
	}
	endTime := carbon.Parse(params.EndTime).Carbon2Time()
	if params.Type == 2 {
		attendanceLog.State = 4
		attendanceLog.EndTime = &endTime
		attendanceLog.LeaveType = params.LeaveType
	}
	if params.Type == 3 {
		attendanceLog.State = 5
	}
	save := db.Save(&attendanceLog)
	if save.Error != nil {
		shipper.Fail(c, save.Error.Error(), -1000)
	} else {
		shipper.Ok(c)
	}
}

// GetAttendanceList
//
// @Description: 获取请假/事故列表
// @Author: Rixat
// @Time: 2023-10-11 10:54:31
// @receiver
// @param c *gin.Context
func (shipper ShipmentController) GetAttendanceList(c *gin.Context) {
	type Params struct {
		Type int    `form:"type"` // 1.打卡 2:请假  3:事故
		Date string `form:"date"` // 位置
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		shipperService = shipperService.NewShipperService(c)
		transformer    = shipperTransformer.NewShipperTransformer(c)
	)
	if err != nil {
		panic(err)
	}
	admin := permissions.GetAdmin(c)
	// 获取配送员的城市跟区域
	attendanceList := shipperService.GetAttendanceListCommon(admin, params.Type, params.Date)
	result := transformer.FormatAttendanceCommonList(admin, attendanceList, params.Type)
	shipper.Success(c, result, "", 200)
}

// GetAttendanceList
//
// @Description: 获取请假/事故详情
// @Author: Rixat
// @Time: 2023-10-11 10:54:31
// @receiver
// @param c *gin.Context
func (shipper ShipmentController) GetAttendanceDetail(c *gin.Context) {
	type Params struct {
		ID   int `form:"id"`   // 考勤ID
		Type int `form:"type"` // 1.打卡 2:请假  3:事故
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		shipperService = shipperService.NewShipperService(c)
		transformer    = shipperTransformer.NewShipperTransformer(c)
	)
	if err != nil {
		panic(err)
	}
	admin := permissions.GetAdmin(c)
	// 获取配送员的城市跟区域
	attendance := shipperService.GetAttendanceDetailCommon(admin, params.Type, params.ID)
	if attendance.Id == 0 {
		shipper.Fail(c, "not_found", 200)
		return
	}
	result := transformer.FormatAttendanceCommonDetail(admin, attendance, params.Type)
	shipper.Success(c, result, "", 200)
}

func (shipper ShipmentController) GetLeaveTypes(c *gin.Context) {
	var (
		l, _ = c.Get("lang_util")
		lang = l.(lang.LangUtil)
	)
	// 1:事假 2:病假 3:婚嫁 4:产假/陪产假
	leaveTypes := lang.TArr("leave_types")
	resMap := make([]map[string]interface{}, 0)
	for key, value := range leaveTypes {
		resMap = append(resMap, map[string]interface{}{"id": key, "name": value})
	}
	shipper.Success(c, resMap, "", 200)
}
//意见和故障
func (shipper ShipmentController) OpinionSave(c *gin.Context) {
	type Params struct {
		Type      int    `form:"type" binding:"required,oneof=1 2"`       // 1:bug,2:意见
		Images    string `form:"images" binding:""`             
		Content    string `form:"content" binding:"required"`             
	}
	var (
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		params   Params
		err      = c.ShouldBind(&params)
		// service  = shipperService.NewShipperService(c)
	)
	if err != nil {
		panic(err)
	}
	tools.Logger.Info("opinion-params:", params)
	// 获取配送员的城市跟区域
	db := tools.Db
	var areas map[string]interface{}
	db.Table("admin_areas").Select("city_id,area_id").Where("admin_id = ?", admin.ID).Scan(&areas)
	//写到意见和故障
	opinion := models.ShipperOpinionBug{
		CityID:    tools.ToInt(areas["city_id"]),
		AreaID:    tools.ToInt(areas["area_id"]),
		ShipperID: admin.ID,
		Type: params.Type,
		Date: carbon.Now(configs.AsiaShanghai).Format("Y-m-d"),
		Content: params.Content,
		Images: params.Images,
		CreatedAt: carbon.Now(configs.AsiaShanghai).Carbon2Time(),
	}
	
	save := db.Save(&opinion)
	tools.AliDeveloperDingdingMsg(fmt.Sprintf("骑手发送了意见反馈，手机号：%s,内容:%s", admin.Mobile, params.Content))

	if save.Error != nil {
		shipper.Fail(c, save.Error.Error(), -1000)
	} else {
		shipper.Ok(c)
	}
}



// 描述：获取配送员收入详情
// 作者：Qurbanjan
// 文件：ShipmentController.go
// 修改时间：2024/07/09 12:34

func (shipper ShipmentController) GetIncomeStatisticDetail(c *gin.Context) {
	type Params struct {
		Month string `form:"month" binding:"required"`
		Type  int    `form:"type" binding:""`
	}

	var (
		l, _           = c.Get("lang_util")
		lang           = l.(lang.LangUtil)
		value, _       = c.Get("admin")
		admin          = value.(models.Admin)
		shipperService = shipperService.NewShipperService(c)
		transformer    = shipperTransformer.NewShipperTransformer(c)
	)
	var request Params
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}
	_, items, err := shipperService.GetIncomeDetail(c, admin, request.Month, request.Type)
	if err != nil {
		shipper.Fail(c, lang.T("error_happend"), -1000)
		tools.Logger.Error("配送员收入详情列表查询错误:", err)
		return
	}
	list := transformer.FormatShipperIncomeList(items)
	shipper.Success(c, list, lang.T("success"), 200)
}

// 描述：工资收入和扣款标签
// 作者：Qurbanjan
// 文件：ShipmentController.go
// 修改时间：2024/07/11 17:28
func (shipper ShipmentController) GetIncomeTags(c *gin.Context){
	type Params struct {
		Month string `form:"month" binding:"required"`
	}

	var (
		l, _           = c.Get("lang_util")
		lang           = l.(lang.LangUtil)
		value, _       = c.Get("admin")
		admin          = value.(models.Admin)
		shipperService = shipperService.NewShipperService(c)
		transformer    = shipperTransformer.NewShipperTransformer(c)
	)

	var request Params
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	detailHeader, err := shipperService.DetailHeader(admin.ID, request.Month)
	if err != nil {
		shipper.Fail(c, err.Error(), -1000)
		tools.Logger.Error("配送员收入详情标签查询错误:", err)
		return
	}
	head,rtn := transformer.FormatShipperIncomeTagDetailHeader(detailHeader)

	res := map[string]interface{}{
		"head":head,
		"tag_list":rtn,
	}
	shipper.Success(c, res, lang.T("success"), 200)
}

//配送员的等级统计 
func (shipper ShipmentController) GetRankStatistic(c *gin.Context) {
	type Params struct {
		Month string `form:"month" binding:"required"`
		

	}
	var (
		l, _     = c.Get("lang_util")
		lang     = l.(lang.LangUtil)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)

		shipperService = shipperService.NewShipperService(c)
	)
	var req Params
	if err := c.ShouldBind(&req); err != nil {
		panic(err)
	}
	data, er := shipperService.GetRankStatistic(admin, req.Month,lang)
	if er == nil {
		shipper.Success(c, data, "", 200)
	} else {
		shipper.Fail(c, lang.T("error_happend"), 301)
	}

}
