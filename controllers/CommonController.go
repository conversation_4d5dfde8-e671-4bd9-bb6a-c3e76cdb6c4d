package controllers

import (
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"
	cmsTransformer "mulazim-api/transformers/cms"

	"github.com/gin-gonic/gin"
)

type CommonController struct {
	BaseController
}

// 
//
// @Description: 部分退款页面原因列表
// @Author: Rixat
// @Time: 2024-11-27 11:06:21
// @receiver 
// @param c *gin.Context
func (common CommonController) GetPartRefundReasonList(c *gin.Context) {
	var (
		service     = cmsService.NewPartRefundService(c)
		transformer = cmsTransformer.NewPartRefundTransformer(c)
	)
	pagination := tools.GetPagination(c)
	// 获取退款原因列表
	data, total := service.RefundReasonList(pagination,1)
	// 格式化退款原因列表
	format := transformer.RefundReasonListFormat(data, total)
	common.Success(c, format, "msg", 200)
}
