package payment

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources"
	"mulazim-api/resources/LakalaEntity"
	"mulazim-api/services"
	"mulazim-api/services/merchant/lakala"
	"mulazim-api/services/payment"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"encoding/base64"
	"github.com/elliotchance/phpserialize"
)

type PaymentController struct {
	controllers.BaseController
}

const TYPE_USER = 1           // 用户退单
const TYPE_ADMIN_CMS = 2      // 后台退单
const TYPE_ADMIN_MERCHANT = 3 // 商家退单
const TYPE_AUTO_REJECT = 4    // 自动退单

// 统一退单接口(用户端(小程序,安卓,WAP,IOS),后台,商家端商家,商家端自动)
func (payC *PaymentController) CancelOrder(c *gin.Context) {
	tools.Logger.Info("开始取消订单")
	type Params struct {
		OrderId    int `form:"order_id" binding:"required"` //订单id
		UserId     int `form:"user_id" binding:""`          //操作人id
		TerminalId int `form:"terminal_id" binding:""`      //操作终端
		UserType   int `form:"user_type" binding:""`        //1:普通用户 2:管理员或商家 3:商家端
		From       string
	}
	var (
		params     Params
		paramError = c.ShouldBind(&params)
		service    = payment.PaymentFactory("ums")
	)
	params.From = c.GetHeader("From")
	reasonId := c.DefaultPostForm("reason_id", "")
	tools.Logger.Info("请求参数reasonId:", reasonId, "orderId:", params.OrderId)
	//通过redis 禁止重复提交
	forbiddenMultiTimeKey := fmt.Sprintf("cancel_order_%d", params.OrderId)
	setResult, err := tools.GetRedisHelper().SetNX(c, forbiddenMultiTimeKey, 1, 5*time.Second).Result()
	if err != nil {
		tools.Logger.Errorf("redis setnx error:%s order_id:%s", err.Error(), params.OrderId)
		return
	}
	if !setResult {
		tools.Logger.Error("订单重复提交，订单号:", params.OrderId)
		payC.Fail(c, "retry_after_3_second", -1000)
		return
	}else{
		tools.Logger.Info("订单不用限流，订单号:", params.OrderId)
	}


	payC.initLang(c)
	if payC.isMerchant(params.From) {
		if admin, err := payC.GetMerchant(c); err != nil {
			tools.Logger.Error("商家判断错误，订单号:", params.OrderId, err.Error())
			payC.Fail(c, err.Error(), -1000)
			return
		} else {
			params.UserId = admin.ID
			params.UserType = TYPE_ADMIN_MERCHANT
		}
		reasonId = "2"
	}
	if payC.isCustomer(params.From) {
		if admin, err := payC.GetCustomer(c); err != nil {
			tools.Logger.Error("客户错误，订单号:", params.OrderId, err.Error())
			payC.Fail(c, err.Error(), -1000)
			return
		} else {
			params.UserId = admin.ID
			params.UserType = TYPE_USER
		}
	}
	if payC.isAutoReject(params.From) {
		params.UserType = TYPE_AUTO_REJECT
		reasonId = "2"
	}
	tools.Logger.Info("请求参数", tools.StructToMap(params))
	if paramError != nil {
		tools.Logger.Error("参数错误:", paramError)
		panic(paramError)
	}

	params.UserType = payC.getUserType(params.From)
	// 验证能否取消订单
	tools.Logger.Info("开始验证订单能否取消，订单号：", params.OrderId)
	reason := tools.ToInt(reasonId)
	order, err := service.CheckCancelOrder(params.OrderId, params.UserId, reason, params.From, params.UserType)
	if err != nil {
		tools.Logger.Error("取消订单验证不通过订单号:", params.OrderId, err.Error())
		payC.Fail(c, err.Error(), -1000)
		return
	}
	// 取消订单
	tools.Logger.Info("开始订单取消逻辑")
	if payC.isAutoReject(params.From) {
		order["refund_chanel"] = 1
	}
	ip :=tools.GetRealIp(c)
	err = service.CancelOrder(ip, order, params.OrderId, params.UserId, reason, params.TerminalId, params.UserType, params.From)
	if err != nil {
		payC.Fail(c, err.Error(), -1000)
		return
	}
	service.PushCancelNotify(params.From, order)
	// 订单排行中奖信息更新(只用户取消订单时更新)
	if params.From == "mini" {
		service.UpdateLotteryChanceInfo(order)
	}
	tools.Logger.Info("订单取消成功订单号:", params.OrderId,",from:",params.From)
	payC.Success(c, nil, "msg", 200)
}

func (payC *PaymentController) GetMerchant(c *gin.Context,id ...int) (models.Admin, error) {

	db := tools.GetDB()
	var admin models.Admin
	if len(id) >0 && id[0] > 0{ //直接发送id获取用户的 测试用户
		db.Table("t_admin").
			//Joins("INNER JOIN `oauth_sessions` ON `oauth_sessions`.`owner_id` = `t_admin`.`id`").Joins("INNER JOIN `oauth_access_tokens` ON `oauth_sessions`.`id` = `oauth_access_tokens`.`session_id`").
			Where("`id`=? AND `state`=1 AND `deleted_at` IS NULL", id[0]).
			Limit(1).
			Scan(&admin)
			return admin,nil
	}
	if token := c.GetHeader("authorization"); token == "" {
		fmt.Printf("%s token 为空\n", carbon.Now().ToDateTimeString())
		return models.Admin{}, errors.New("not_fund_merchant")
	} else {
		serialNumber := c.GetHeader("searialnumber")
		db := tools.GetDB()
		
		if strings.Contains(token,".") {
			tokenString := token[7:]
			adminID,jwtSerialNumber,err := tools.NewJWTTools().ParseTokenAndGetIDAndJWTSerialNumber(tokenString)
			if err==nil {
				db.Table("t_admin").
					Select("t_admin.id,t_admin.type,t_admin.level,t_admin.mobile,t_admin.name,t_admin.real_name,t_admin.grab_order_count,t_admin.recommend_qrcode,t_admin.state,t_admin.created_at,take_cash_order,back_order_time_limit,last_comment_readed").
					Where("t_admin.id = ? and jwt_serial_number = ? and `t_admin`.`deleted_at` IS NULL ",adminID, jwtSerialNumber).
					Limit(1).
					Scan(&admin)
			}else{
				tools.Logger.Errorf("FATAL 查询管理员失败 %s",tokenString)
			}
		}else{
			db.Table("t_admin").
				Select("t_admin.id,t_admin.type,t_admin.level,t_admin.mobile,t_admin.name,t_admin.real_name,t_admin.grab_order_count,t_admin.recommend_qrcode,t_admin.state,t_admin.created_at,take_cash_order,back_order_time_limit,last_comment_readed").
				Joins("INNER JOIN `oauth_sessions` ON `oauth_sessions`.`owner_id` = `t_admin`.`id`").Joins("INNER JOIN `oauth_access_tokens` ON `oauth_sessions`.`id` = `oauth_access_tokens`.`session_id`").
				Where("`oauth_access_tokens`.`id`=? AND `t_admin`.`deleted_at` IS NULL AND `t_admin`.`searial_number`=? ", token[7:], serialNumber).
				Limit(1).
				Scan(&admin)
		}

		if admin.ID == 0 {
			fmt.Printf("%s  admin 查询失败 token=%s,serialNumber=%s\n", carbon.Now().ToDateTimeString(), token, serialNumber)
			return models.Admin{}, errors.New("not_fund_merchant")
		} else {
			return admin, nil
		}
	}
}

func (payC *PaymentController) GetCustomer(c *gin.Context,id ...int) (models.Admin, error) {

	db := tools.GetDB()
	var admin models.Admin
	if len(id) >0 && id[0] > 0{ //直接发送id获取用户的 测试用户
		db.Table("t_user").
			//Joins("INNER JOIN `oauth_sessions` ON `oauth_sessions`.`owner_id` = `t_admin`.`id`").Joins("INNER JOIN `oauth_access_tokens` ON `oauth_sessions`.`id` = `oauth_access_tokens`.`session_id`").
			Where("`id`=? AND `state`=1 AND `deleted_at` IS NULL", id[0]).
			Limit(1).
			Scan(&admin)
			return admin,nil
	}
	if token := c.GetHeader("authorization"); token == "" {
		fmt.Printf("%s token 为空\n", carbon.Now().ToDateTimeString())
		return models.Admin{}, errors.New("not_found_customer")
	} else {
		serialNumber := c.GetHeader("searialnumber")
		tools.Logger.Info("serialNumber:", serialNumber)
		db := tools.GetDB()
		var admin models.Admin
		db.Table("t_user").
			Select("t_user.id").
			Joins("INNER JOIN `oauth_sessions` ON `oauth_sessions`.`owner_id` = `t_user`.`id`").Joins("INNER JOIN `oauth_access_tokens` ON `oauth_sessions`.`id` = `oauth_access_tokens`.`session_id`").
			Where("`oauth_access_tokens`.`id`=? AND `t_user`.`deleted_at` IS NULL ", token[7:]).
			Limit(1).
			Scan(&admin)
		if admin.ID == 0 {
			fmt.Printf("%s  admin 查询失败 token=%s,serialNumber=%s\n", carbon.Now().ToDateTimeString(), token, serialNumber)
			tools.Logger.Info("%s  admin 查询失败 token=%s,serialNumber=%s\n", carbon.Now().ToDateTimeString(), token, serialNumber)
			return models.Admin{}, errors.New("not_found_customer")
		} else {
			return admin, nil
		}
	}
}

//获取后台用户 
func (payC *PaymentController) GetAdmin(c *gin.Context,id ...int) (models.Admin, error) {
	
	fields :="id,"
		fields +="admin_city_id,"
		fields +="admin_area_id,"
		fields +="type,"
		fields +="level,"
		fields +="mobile,"
		fields +="name,"
		fields +="real_name,"
		fields +="grab_order_count,"
		fields +="recommend_qrcode,"
		fields +="state,"
		fields +="created_at,"
		fields +="take_cash_order,"
		fields +="back_order_time_limit,"
		fields +="last_comment_readed,"
		fields +="parent_id,"
		fields +="group_id,"
		fields +="team_id,"
		fields +="is_captain"
	var admin models.Admin

	db := tools.GetDB()
	if len(id) >0 && id[0] > 0{ //直接发送id获取用户的 测试用户
		db.Table("t_admin").
			Select(fields).
			//Joins("INNER JOIN `oauth_sessions` ON `oauth_sessions`.`owner_id` = `t_admin`.`id`").Joins("INNER JOIN `oauth_access_tokens` ON `oauth_sessions`.`id` = `oauth_access_tokens`.`session_id`").
			Where("`id`=? AND `state`=1 AND `deleted_at` IS NULL", id[0]).
			Limit(1).
			Scan(&admin)
			return admin,nil
	}

	redis := tools.GetRedisCmsHelper()
	// 从 cookie 获取 登录信息
	cookie, err := c.Cookie("mulazim_two_point_zero")
	
	if err != nil {
		tools.Logger.Error("cms 认证 cookie 获取失败")
		
		return admin,fmt.Errorf("not_authorized")
	}
	// 解码cookie
	cipher := configs.MyApp.CmsCipher
	key, err := base64.StdEncoding.DecodeString(configs.MyApp.CmsKey)
	if err != nil {
		tools.Logger.Error("cms key 错误")
		return admin,fmt.Errorf("not_authorized")
	}
	// 解码cookie
	encrypter := tools.NewEncrypter(key, cipher)
	out, err := encrypter.Decrypt(cookie)
	if err != nil {
		tools.Logger.Error("cookie 解密失败")
		return admin,fmt.Errorf("not_authorized")
	}

	var sessionId string
	// 解析 获取session id
	if err = phpserialize.Unmarshal(out, &sessionId); err != nil {
		tools.Logger.Error("cookie key unsereialize fail")
		return admin,fmt.Errorf("not_authorized")
	}
	// 从redis 获取用户session信息
	var session_key = "laravel:" + sessionId
	redisData := redis.Get(c, session_key)
	//var sessionData SessionData
	var sessionStr string
	if err = phpserialize.Unmarshal([]byte(redisData.Val()), &sessionStr); err != nil {
		tools.Logger.Error("解析session data 失败")
		return admin,fmt.Errorf("not_authorized")
	}
	// session信息反序列化
	var sessionMap map[interface{}]interface{}
	if sessionMap, err = phpserialize.UnmarshalAssociativeArray([]byte(sessionStr)); err != nil {
		tools.Logger.Error("解析session data 失败")
		return admin,fmt.Errorf("not_authorized")
	}

	// 获取 adminId
	adminId, keyExist := sessionMap["login_dashboard_59ba36addc2b2f9401580f014c7f58ea4e30989d"]
	if keyExist {
		
		
		
		
		db.Table("t_admin").
			Select(fields).
			//Joins("INNER JOIN `oauth_sessions` ON `oauth_sessions`.`owner_id` = `t_admin`.`id`").Joins("INNER JOIN `oauth_access_tokens` ON `oauth_sessions`.`id` = `oauth_access_tokens`.`session_id`").
			Where("`id`=? AND `state`=1 AND `deleted_at` IS NULL", adminId).
			Limit(1).
			Scan(&admin)
	} else {
		tools.Logger.Error("用户ID 不存在")
		return admin,fmt.Errorf("not_authorized")
	}
	return admin,nil
	
}

func (payC *PaymentController) isMerchant(From string) bool {
	return From == "api"
}
func (payC *PaymentController) isCustomer(From string) bool {
	return From == "mini"
}
func (payC *PaymentController) isAutoReject(From string) bool {
	return From == "auto-reject"
}

func (payC *PaymentController) isCMS(From string) bool {
	return From == "cms"
}

func (payC *PaymentController) initLang(c *gin.Context) {
	path := c.FullPath()
	if strings.HasPrefix(path, "/zh/") {
		c.Set("lang", "zh")
		c.Set("lang_util", lang.LangUtil{
			Lang: "zh",
		})
	} else {
		c.Set("lang", "ug")
		c.Set("lang_util", lang.LangUtil{
			Lang: "ug",
		})
	}
}

// getUserType
//
//	@Description: 获取用户类型
//	@author: Alimjan
//	@Time: 2023-08-12 10:46:55
//	@receiver payC *PaymentController
//	@param from string
//	@return int
func (payC *PaymentController) getUserType(from string) int {
	switch from {
	case "api":
		return services.TYPE_ADMIN_MERCHANT
	case "auto-reject":
		return services.TYPE_AUTO_REJECT
	case "cms":
		return services.TYPE_ADMIN_CMS
	case "mini":
		return services.TYPE_USER
	}
	return 0
}

// DownloadOrderFile
//
//	@Description: 下载订单文件,并对张
//	@author: Alimjan
//	@Time: 2023-08-12 10:46:45
//	@receiver payC *PaymentController
//	@param context *gin.Context
func (payC *PaymentController) DownloadOrderFile(context *gin.Context) {
	type Params struct {
		BillDate string `form:"bill_date" binding:"date"` //订单id
	}
	var (
		params     Params
		paramError = context.ShouldBind(&params)
	)
	if paramError != nil {
		panic(paramError)
	}
	if params.BillDate == "" {
		params.BillDate = carbon.Now().SubDay().ToDateString()
	}

	lakalaService := lakala.NewLakalaService(context)

	lakalaService.CheckDownload(context, params.BillDate)
	lakalaService.ArchiveZipFile(params.BillDate)
	lakalaService.CSVOrderCheck(context, params.BillDate)
	lakalaService.CSVDetailCheck(context, params.BillDate)
	payC.Success(context, nil, "msg", 200)
}

func (payC *PaymentController) CheckOrderPayStateHourly(context *gin.Context) {
	service := payment.PaymentFactory("lakala")
	service.CheckOrderPayStateHourly(context)
	service.CheckDupilicatedPayedOrder(context)

}

/***
 * @Author: [rozimamat]
 * @description: 检查商户余额
 * @Date: 2023-08-12 20:37:39
 * @param {*gin.Context} context
 */
func (payC *PaymentController) CheckResCash(context *gin.Context) {
	service := payment.PaymentFactory("lakala")
	service.CheckResCash(context)

}

func (payC *PaymentController) CheckMulazimBalance(context *gin.Context) {
	la := lakala.GetLakalaService()
	//tools.Logger.Info("商户余额账户余额错误:商户id:", v.RestaurantId)
	//检查佣金余额
	res, err := la.AccountBalanceQuery(context, "7076471907119063040", "S005")
	if err != nil {
		tools.Logger.Error("商户余额账户余额错误:", err.Error())
		return
	}

	var mq LakalaEntity.LakalaResult
	err = json.Unmarshal([]byte(res), &mq)
	if err != nil {
		tools.Logger.Error("商户余额账户余额错误:", err.Error())
		return
	}
	var balanceResult LakalaEntity.BalanceResultResponse
	err = json.Unmarshal([]byte(mq.Response), &balanceResult)
	if err != nil {
		tools.Logger.Error("商户余额账户余额错误:", err.Error())
		return
	}
	tools.Logger.Info("Mulazim平台商户余额账户余额:", tools.FormatFen2TYuanPrice(balanceResult.Result.TotalAmount),
		",可用余额：", tools.FormatFen2TYuanPrice(balanceResult.Result.AvailableAmount),
		",冻结余额：", tools.FormatFen2TYuanPrice(balanceResult.Result.FreezeAmount))
	context.JSON(200, gin.H{
		"code":   200,
		"msg":    "success",
		"result": balanceResult.Result,
	})
}

func (payC *PaymentController) CheckTakeOrderError(context *gin.Context) {
	db := tools.Db
	var orderIds []string
	db.Table("t_take_order").
		Select("order_id").
		Where("created_at > ? AND state = 1 AND order_id IN (SELECT order_id FROM t_take_order WHERE created_at > ? AND state = 3)", tools.Today("Asia/Shanghai"), tools.Today("Asia/Shanghai")).
		Find(&orderIds)

	if len(orderIds) > 0 {
		tools.AliDeveloperDingdingMsg("take order 状态错误 orderIds:" + strings.Join(orderIds, ","))
		tools.Logger.Info("take order 状态错误 orderIds:" + strings.Join(orderIds, ","))
	}

	type OrderToday struct {
		ID        int
		CreatedAt time.Time
	}
	var orders []string
	subQuery := db.Table("t_take_order").
		Select("order_id").
		Where("created_at > ?", tools.Today("Asia/Shanghai")).
		Where("state IN ?", []int{1, 2, 3})

	db.Table("t_order_today").
		Select("id").
		Where("created_at > ?", tools.Today("Asia/Shanghai")).
		Where("shipper_id IS NULL").
		Where("id IN (?)", subQuery).
		Find(&orders)
	if len(orders) > 0 {
		tools.AliDeveloperDingdingMsg("order 状态错误 shipper id null:" + strings.Join(orders, ","))
		tools.Logger.Info("order 状态错误 shipper id null:" + strings.Join(orders, ","))
	}
	tools.Logger.Info("配送员抢单情况检查结束")
}

// OrderBackToUser
//
//	@Description: 退款因账号没有金额 导致的无法退款的订单
//	@author: Alimjan
//	@Time: 2023-09-04 18:10:49
//	@receiver payC *PaymentController
//	@param c *gin.Context
func (payC *PaymentController) OrderBackToUser(c *gin.Context) {

	type Params struct {
		OrderId int `form:"order_id" binding:"required"` //订单id
	}
	var (
		params     Params
		paramError = c.ShouldBind(&params)
	)
	if paramError != nil {
		tools.Logger.Error("参数错误:", paramError)
		panic(paramError)
	}
	order := make(map[string]interface{})
	tools.Db.Table("t_order_today").Where("id = ?", params.OrderId).Scan(&order)
	lakalaMap := make(map[string]interface{})
	tools.Db.Table("t_pay_lakala").
		Where("object_type = ? ", models.PAY_LAKALA_OBJECT_TYPE_ORDER).
		Where("order_id = ?", tools.ToInt(order["id"])).
		Where("pay_status", 2003).Scan(&lakalaMap)

	_, err := lakala.GetLakalaService().RefundLakala(tools.GetRealIp(c), lakalaMap,false)
	if err != nil {
		tools.Logger.Error("退款失败:", err)
		c.JSON(200, gin.H{
			"code":   500,
			"msg":    "退款失败",
			"result": err.Error(),
		})
		return
	} else {
		c.JSON(200, gin.H{
			"code":   200,
			"msg":    "退款成功",
			"result": "",
		})
		return
	}

}

// CouponRefund
//
//	@Description: 优惠券结束后 剩余的钱退还给代理
//	@author: rozimamat
//	@Time: 2023-09-13 18:10:49
//	@receiver payC *PaymentController
//	@param c *gin.Context
func (payC *PaymentController) CouponRefund(c *gin.Context) {

	type Params struct {
		CouponId int `form:"coupon_id" binding:"required"` //优惠券id

	}
	var (
		params     Params
		paramError = c.ShouldBind(&params)
	)
	if paramError != nil {
		tools.Logger.Error("参数错误:", paramError)
		panic(paramError)
	}

	_, err := lakala.NewLakalaService(c).CouponRefund(c, params.CouponId)
	if err != nil {
		tools.Logger.Error("退款失败:", err)
		c.JSON(200, gin.H{
			"code":   500,
			"msg":    err.Error(),
			"result": err.Error(),
		})
		return
	} else {
		c.JSON(200, gin.H{
			"code":   200,
			"msg":    "退款成功",
			"result": "",
		})
		return
	}


}
//获取微信 小程序和用户端APP 支付参数 
func (payC *PaymentController) GetPayParam(c *gin.Context) {
	//context 复制一遍 一遍两次读取
	body, _ := io.ReadAll(c.Request.Body)	
	c.Request.Body.Close() // 关闭请求体，避免后续读取问题
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body)) // 重置请求体以便后续可能的使用

	payId :=tools.ToInt(c.DefaultQuery("pay_id", "0"))
	orderId :=tools.ToInt(c.DefaultQuery("order_id", "0"))
	openId :=c.DefaultQuery("openid", "")
	payerType :=tools.ToInt(c.DefaultQuery("payer_type", "1"))
	payFrom :=tools.ToInt(c.DefaultQuery("pay_from", "0"))
	appId :=c.DefaultQuery("appid", "")

	terminalId      := tools.ToInt(c.GetHeader("terminalId"))
	userId      := tools.ToInt(c.GetHeader("user-id"))
	isLakalaMini :=0
	tools.Logger.Info("支付参数:query:", c.Request.URL.RawQuery)
	if c.Request.Method == "POST" { //拉卡拉 支付 小程序 来的 
		type PostParams struct {

			OrderId string `form:"order_id" json:"order_id"` 
			Appid string  `form:"appid" json:"appid"`  
			OpenId string   `form:"openid" json:"openid"`
			PayFrom string   `form:"pay_from" json:"pay_from"`
			PayerType string   `form:"payer_type" json:"payer_type"`
		}
		var params PostParams
		json.Unmarshal(body, &params)

		orderId 	=	tools.ToInt(params.OrderId)
		openId 		=	params.OpenId
		payerType 	=	tools.ToInt(params.PayerType)
		payFrom 	=	tools.ToInt(params.PayFrom)
		appId 		=	params.Appid
		terminalId = 8
		isLakalaMini =1
		tools.Logger.Info("支付参数:post:", string(body))

	}
	if   orderId == 0 {
		tools.Logger.Error("参数错误:order_id 未发送")
		panic("参数错误")
		return
	}
	if terminalId == 0 {
		terminalId =tools.ToInt(c.DefaultQuery("terminalId", "0"))
	}
	if userId == 0 {
		userId =tools.ToInt(c.DefaultQuery("user-id", "0"))
	}
	if payId > 0 && !tools.InArray(payId,[]int{5,6}){ //不是在线支付 
		payC.Success(c,nil,"",200)
		return
	}
	realIpStr :=tools.GetRealIp(c)
	realIpInt :=tools.GetClientIP(c.Request)
	realIpMap :=map[string]interface{}{
		"real_ip":realIpStr,
		"real_ip_int":realIpInt,
	}
	
	service,err:= payment.PaymentFactoryByOrderID(orderId)
	if err != nil {
		tools.Logger.Error("小程序和用户端APP支付参数失败:", err)
		payC.Fail(c,err.Error(),301)
		return
	}
	result:=make(map[string]interface{})
	language, _ := c.Get("lang")
	js_api_param, err := service.GetPayParam(tools.ToString(language),orderId,userId, payId,openId,realIpMap,terminalId,payerType,payFrom,appId)
	if err != nil {
		tools.Logger.Error("小程序和用户端APP支付参数失败:", err)
		payC.Fail(c,err.Error(),301)
		return
	}
	if terminalId == 8 { //小程序
		if isLakalaMini == 1 {
			result = js_api_param	
		}else{
			result["js_api_param"] = js_api_param	
		}
	}else{ //APP 
		result = js_api_param
	}
	payC.Success(c,result,"",200)
}

//获取微信 小程序和用户端APP 支付参数  代理支付
func (payC *PaymentController) GetAgentPayParam(c *gin.Context) {
	
	
	id :=tools.ToInt(c.DefaultQuery("id", "0"))
	openId :=c.DefaultQuery("open_id", "")

	terminalId      := 8
	if   id == 0 {
		tools.Logger.Error("参数错误:")
		panic("参数错误")
		return
	}
	if terminalId == 0 {
		terminalId =tools.ToInt(c.DefaultQuery("terminalId", "0"))
	}
	realIpStr :=tools.GetRealIp(c)
	realIpInt :=tools.GetClientIP(c.Request)
	realIpMap :=map[string]interface{}{
		"real_ip":realIpStr,
		"real_ip_int":realIpInt,
	}
	tools.Logger.Info("支付参数:query:", c.Request.URL.RawQuery)
	service := payment.PaymentFactoryByPlatformID(models.PAY_PLATFORM_LAKALA)
	result:=make(map[string]interface{})
	language, _ := c.Get("lang")
	js_api_param, err := service.GetAgentPayParam(tools.ToString(language),id,openId,realIpMap,terminalId)
	if err != nil {
		tools.Logger.Error("小程序和用户端APP支付参数失败:", err)
		payC.Fail(c,err.Error(),301)
		return
	}
	if terminalId == 8 { //小程序
		result = js_api_param	
	}else{ //APP 
		result = js_api_param
	}
	payC.Success(c,result,"",200)
}

//微信支付通知
func (payC *PaymentController) MiniNotify(c *gin.Context) {

	tools.Logger.Info("微信支付异步通知接口被调用")
	service := payment.PaymentFactoryByPlatformID(models.PAY_PLATFORM_ORIGINAL)
	ok, msg,version := service.MiniNotify(c)
	if ok {
		if version == 3 {	
			c.JSON(200, gin.H{
				"code":    "SUCCESS",
				"message": msg,
			})
		}else{//v2 xml 
			c.XML(200, gin.H{
				"return_code": "SUCCESS",
				"return_msg":  "OK",
			})
		}
	} else {
		if version == 3 {	
			c.JSON(401, gin.H{
				"code":    "FAIL",
				"message": msg,
			})
		}else{//v2 xml 
			c.XML(401, gin.H{
				"return_code": "FAIL",
				"return_msg":  "FAIL",
			})
		}
		
	}
}

//抽奖活动 获取微信 小程序和用户端APP 支付参数 
func (payC *PaymentController) GetLotteryPayParam(c *gin.Context) {
	//context 复制一遍 一遍两次读取
	body, _ := io.ReadAll(c.Request.Body)	
	c.Request.Body.Close() // 关闭请求体，避免后续读取问题
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body)) // 重置请求体以便后续可能的使用

	payId :=tools.ToInt(c.DefaultQuery("pay_id", "0"))
	orderId :=tools.ToInt(c.DefaultQuery("order_id", "0"))
	openId :=c.DefaultQuery("openid", "")
	payerType :=tools.ToInt(c.DefaultQuery("payer_type", "1"))
	payFrom :=tools.ToInt(c.DefaultQuery("pay_from", "0"))
	appId :=c.DefaultQuery("appid", "")

	terminalId      := tools.ToInt(c.GetHeader("terminalId"))
	userId      := tools.ToInt(c.GetHeader("user-id"))
	isLakalaMini :=0
	tools.Logger.Info("支付参数:query:", c.Request.URL.RawQuery)
	if c.Request.Method == "POST" { //拉卡拉 支付 小程序 来的 
		type PostParams struct {

			OrderId string `form:"order_id" json:"order_id"` 
			Appid string  `form:"appid" json:"appid"`  
			OpenId string   `form:"openid" json:"openid"`
			PayFrom string   `form:"pay_from" json:"pay_from"`
			PayerType string   `form:"payer_type" json:"payer_type"`
		}
		var params PostParams
		json.Unmarshal(body, &params)

		orderId 	=	tools.ToInt(params.OrderId)
		openId 		=	params.OpenId
		payerType 	=	tools.ToInt(params.PayerType)
		payFrom 	=	tools.ToInt(params.PayFrom)
		appId 		=	params.Appid
		terminalId = 8
		isLakalaMini =1
		tools.Logger.Info("支付参数:post:", string(body))

	}
	if   orderId == 0 {
		tools.Logger.Error("参数错误:order_id 未发送")
		panic("参数错误")
		return
	}
	if terminalId == 0 {
		terminalId =tools.ToInt(c.DefaultQuery("terminalId", "0"))
	}
	if userId == 0 {
		userId =tools.ToInt(c.DefaultQuery("user-id", "0"))
	}
	if payId > 0 && !tools.InArray(payId,[]int{5,6}){ //不是在线支付 
		payC.Success(c,nil,"",200)
		return
	}
	realIpStr :=tools.GetRealIp(c)
	realIpInt :=tools.GetClientIP(c.Request)
	realIpMap :=map[string]interface{}{
		"real_ip":realIpStr,
		"real_ip_int":realIpInt,
	}
	
	service,err:= payment.PaymentFactoryByOrderID(orderId)
	if err != nil {
		tools.Logger.Error("小程序和用户端APP支付参数失败:", err)
		payC.Fail(c,err.Error(),301)
		return
	}
	result:=make(map[string]interface{})
	language, _ := c.Get("lang")
	js_api_param, err := service.GetLotteryPayParam(tools.ToString(language),orderId,userId, payId,openId,realIpMap,terminalId,payerType,payFrom,appId)
	if err != nil {
		tools.Logger.Error("小程序和用户端APP支付参数失败:", err)
		payC.Fail(c,err.Error(),301)
		return
	}
	if terminalId == 8 { //小程序
		if isLakalaMini == 1 {
			result = js_api_param	
		}else{
			result["js_api_param"] = js_api_param	
		}
	}else{ //APP 
		result = js_api_param
	}
	payC.Success(c,result,"",200)
}



//获取微信 小程序和用户端APP 支付参数  代理 加价销售 支付 二维码获取
func (payC *PaymentController) GetAgentPriceUpPayCode(c *gin.Context) {
	
	
	id :=tools.ToInt(c.DefaultQuery("id", "0"))

	if   id == 0 {
		tools.Logger.Error("参数错误:")
		panic("参数错误")
		return
	}
	tools.Logger.Info("支付参数:query:", c.Request.URL.RawQuery)
	service := payment.PaymentFactoryByPlatformID(models.PAY_PLATFORM_LAKALA)
	
	js_api_param, err := service.GetAgentPriceUpPayCode(id)
	if err != nil {
		tools.Logger.Error("小程序和用户端APP支付参数失败:", err)
		payC.Fail(c,err.Error(),301)
		return
	}
	
	payC.Success(c,js_api_param,"",200)
}



//获取微信 小程序和用户端APP 支付参数  代理 加价销售 支付
func (payC *PaymentController) GetAgentPriceUpPayParam(c *gin.Context) {
	
	
	id :=tools.ToInt(c.DefaultQuery("id", "0"))
	openId :=c.DefaultQuery("open_id", "")
	appId :=c.DefaultQuery("appid", "")

	terminalId      := 8
	if   id == 0 {
		tools.Logger.Error("参数错误:")
		panic("参数错误")
		return
	}
	realIpStr :=tools.GetRealIp(c)
	realIpInt :=tools.GetClientIP(c.Request)
	realIpMap :=map[string]interface{}{
		"real_ip":realIpStr,
		"real_ip_int":realIpInt,
	}
	tools.Logger.Info("支付参数:query:", c.Request.URL.RawQuery)
	service := payment.PaymentFactoryByPlatformID(models.PAY_PLATFORM_LAKALA)
	
	language, _ := c.Get("lang")
	js_api_param, err := service.GetAgentPriceUpPayParam(tools.ToString(language),id,openId,realIpMap,terminalId,appId)
	if err != nil {
		tools.Logger.Error("小程序和用户端APP支付参数失败:", err)
		payC.Fail(c,err.Error(),301)
		return
	}
	payC.Success(c,js_api_param,"",200)
}

//支付查询
func (payC *PaymentController) GetAgentPriceUpCheck(c *gin.Context) {
	
	
	id :=tools.ToInt(c.DefaultQuery("id", "0"))

	if   id == 0 {
		tools.Logger.Error("参数错误:")
		panic("参数错误")
		return
	}
	
	service := payment.PaymentFactoryByPlatformID(models.PAY_PLATFORM_LAKALA)
	
	js_api_param, err := service.GetAgentPriceUpCheck(id)
	if err != nil {
		tools.Logger.Error("小程序和用户端APP支付查询失败:", err)
		payC.Fail(c,err.Error(),301)
		return
	}
	payC.Success(c,js_api_param,"",200)
}


//加价 美食退款
func (payC *PaymentController) GetAgentPriceUpRefund(c *gin.Context) {
	
	
	id :=tools.ToInt(c.DefaultQuery("id", "0"))

	if   id == 0 {
		tools.Logger.Error("参数错误:")
		panic("参数错误")
		return
	}
	
	service := payment.PaymentFactoryByPlatformID(models.PAY_PLATFORM_LAKALA)
	
	js_api_param, err := service.GetAgentPriceUpRefund(c,id)
	if err != nil {
		tools.Logger.Error("小程序和用户端APP支付查询失败:id:",id,",err:", err)
		payC.Fail(c,err.Error(),301)
		return
	}
	payC.Success(c,js_api_param,"",200)
}



//部分退款
func (payC *PaymentController) PostPartRefund(c *gin.Context) {
	
	var (
		params resources.PartRefund
		paramError = c.ShouldBind(&params)
		service    = payment.PaymentFactory("ums")
	)
	payC.initLang(c)
	l, _:= c.Get("lang_util")
	langUtils           := l.(lang.LangUtil)
	
	if paramError != nil {
		tools.Logger.Error("参数错误:", paramError)
		payC.Fail(c, paramError.Error(), -1000)
		return
	}
	params.From = c.GetHeader("From")

	refundTestUserId :=tools.ToInt(c.GetHeader("refund_test_user")) //测试专用用户
	
	//通过redis 禁止重复提交
	forbiddenMultiTimeKey := fmt.Sprintf("part_refund_order_%d", params.OrderId)
	expireTime :=15*time.Second
	setResult, err := tools.GetRedisHelper().SetNX(c, forbiddenMultiTimeKey, 1, expireTime).Result()
	if err != nil {
		tools.Logger.Errorf("redis setnx error:%s order_id:%s", err.Error(), params.OrderId)
		payC.Fail(c,fmt.Sprintf(langUtils.T("retry_after_second"), 15), -1000)
		return
	}
	if !setResult {
		tools.Logger.Error("订单重复提交，订单号:", params.OrderId)
		payC.Fail(c,fmt.Sprintf(langUtils.T("retry_after_second"), 15), -1000)
		return
	}else{
		tools.Logger.Info("订单不用限流，订单号:", params.OrderId)
	}

	
	var admin models.Admin
	if payC.isMerchant(params.From) {
		admin, err = payC.GetMerchant(c,refundTestUserId)
		tools.Logger.Info("商家退单，订单号:", params.OrderId,",id:",admin.ID)
		if  err != nil {
			tools.Logger.Error("商家判断错误，订单号:", params.OrderId, err.Error())
			payC.Fail(c, err.Error(), -1000)
			return
		} else {
			params.UserId = admin.ID
			params.UserType = TYPE_ADMIN_MERCHANT
		}
		if params.ReasonId == 0 {
			params.ReasonId = 2
		}
		if params.RefundType == 2  { //部分退款 
			newApp :=true
			for _, v := range params.FoodDetails {
				if v.LunchBoxGroupIndex == 0 {
					newApp =false
				}
			}
			for _, v := range params.LunchBoxDetails {
				if v.LunchBoxGroupIndex == 0 {
					newApp =false
				}
			}
			if !newApp {
				payC.Fail(c, langUtils.T("res_manager_app_is_old"), -1000)
				return
			}
		}
		
	}else if payC.isCustomer(params.From) {
		admin, err = payC.GetCustomer(c,refundTestUserId)
		tools.Logger.Info("客户退单，订单号:", params.OrderId,",id:",admin.ID)
		if  err != nil {
			tools.Logger.Error("客户错误，订单号:", params.OrderId, err.Error())
			payC.Fail(c, err.Error(), -1000)
			return
		} else {
			params.UserId = admin.ID
			params.UserType = TYPE_USER
		}
	}else if payC.isAutoReject(params.From) {
		params.UserType = TYPE_AUTO_REJECT
		if params.ReasonId == 0 {
			params.ReasonId = 2
		}
	}else{ //后台管理员
		admin, err = payC.GetAdmin(c,refundTestUserId)
		tools.Logger.Info("管理员退单，订单号:", params.OrderId,",id:",admin.ID)
		if  err != nil {
			tools.Logger.Error("管理员判断错误，订单号:", params.OrderId, err.Error())
			payC.Fail(c, err.Error(), -1000)
			return
		} else {
			params.UserId = admin.ID
			params.UserType = 2
		}
	}
	
	
	params.UserType = payC.getUserType(params.From)
	
	pr,_:=json.Marshal(params)
	tools.Logger.Info("请求参数:", string(pr))
	partRefundCount :=int64(0)
	if params.RefundType == 2 { //部分退款 
		partRefundCount,err=service.PartRefundCheck(params)
		if err != nil {
			tools.Logger.Error("参数错误:", err)
			payC.Fail(c, err.Error(), -1000)
			return
		}
	}
	
	if partRefundCount > 0 || params.RefundType == 1 || len(params.FoodDetails) == 0{ //退款过一次的话直接 转发到 全额退款 
		tools.Logger.Info("订单已退款过一次，订单号:", params.OrderId,",即将进行全额退款")
		if partRefundCount > 0 && params.RefundType == 2 { //部分退款进行过一次 ，第二次还是要进行部分退款 
			payC.Fail(c, "can_not_part_refund_twice", -1000)
			return
		}
		if len(params.FoodDetails) == 0 && params.RefundType == 2 { //是部分退款 但是 退款内容为空 
			payC.Fail(c, "missing_parameter", -1000)
			return
		}
		reason := params.ReasonId
		if reason == 0 {
		 	reason = tools.ToInt(c.Request.Form.Get("reason_id"))
		}
		order, err := service.CheckCancelOrder(params.OrderId, params.UserId, reason, params.From, params.UserType)
		if err != nil {
			tools.Logger.Error("取消订单验证不通过订单号:", params.OrderId, err.Error())
			payC.Fail(c, err.Error(), -1000)
			return
		}
		// 取消订单
		tools.Logger.Info("开始订单取消逻辑")
		if payC.isAutoReject(params.From) {
			order["refund_chanel"] = 1
		}
		ip :=tools.GetRealIp(c)
		err = service.CancelOrder(ip, order, params.OrderId, params.UserId, reason, params.TerminalId, params.UserType, params.From)
		if err != nil {
			payC.Fail(c, err.Error(), -1000)
			return
		}
		service.PushCancelNotify(params.From, order)

		// 订单排行中奖信息更新(只用户取消订单时更新)
		if params.From == "mini" {
			service.UpdateLotteryChanceInfo(order)
		}
		tools.Logger.Info("订单取消成功订单号:", params.OrderId,",from:",params.From)
		payC.Success(c, nil, "msg", 200)

		return
	}
	ip :=tools.GetRealIp(c)
	err = service.PostPartRefund(ip,params)
	if err != nil {
		rs,_:=json.Marshal(params)
		tools.Logger.Error("FATAL部分退款失败失败:order_id:",params.OrderId,"params:",string(rs),",err:",err)
		payC.Fail(c,err.Error(),-1000)
		return
	}
	payC.Success(c,nil,"msg",200)
}
// 拉卡拉 订单状态查询
func (payC *PaymentController) GetOrderStatus(c *gin.Context) {
	orderId :=tools.ToInt(c.DefaultQuery("order_id", "0"))
	if orderId == 0 {
		payC.Fail(c,"not_found",-1000)
		return
	}
	service    := payment.PaymentFactory("ums")
	orderStatus,err := service.GetOrderStatus(orderId)
	if err != nil {
		payC.Fail(c,err.Error(),-1000)
		return
	}
	payC.Success(c,orderStatus,"msg",200)

}