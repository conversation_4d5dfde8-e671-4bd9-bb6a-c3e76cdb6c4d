package client

import (
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/models/chat"
	"mulazim-api/services"
	"mulazim-api/tools"
	"net/http"

	"github.com/gin-gonic/gin"
)

type ClientChatController struct {
	controllers.BaseController
}

// 描述：发送消息
// 作者：Qurbanjan
// 文件：ClientChatController.go
// 修改时间：2023/11/15 13:16
func (cmsChat ClientChatController) PostSendMessage(c *gin.Context) {
	var request chat.ChatSendRequest
	var (
		anyAdmin, _ = c.Get("user")
		user        = anyAdmin.(models.User)
	)
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}
	chatService := services.NewChatService(c)
	data, err := chatService.SendMessage(request, int64(user.ID))
	if err != nil {
		cmsChat.Fail(c, err.Error(), http.StatusBadRequest)
		return
	}
	cmsChat.Success(c, data, "msg", 200)
}

// 描述：获取聊天室聊天列表
// 作者：Qurbanjan
// 文件：ClientChatController.go
// 修改时间：2023/11/15 13:16
func (s ClientChatController) GetChatList(c *gin.Context) {
	var (
		anyAdmin, _ = c.Get("user")
		user        = anyAdmin.(models.User)
	)
	kw :=c.DefaultQuery("kw","")
	startDate :=c.DefaultQuery("start_date","")
	endDate :=c.DefaultQuery("end_date","")
	resId :=tools.ToInt(c.DefaultQuery("res_id","0"))
	shipperId :=tools.ToInt(c.DefaultQuery("shipper_id","0"))
	chatService := services.NewChatService(c)
	list, err := chatService.GetChatList(c, int64(user.ID), 99,0,0,kw,startDate,endDate,resId,shipperId)
	if err != nil {
		panic(err)
	}
	s.Success(c, list, "获取成功", http.StatusOK)

}

// 描述：获取聊天详情
// 作者：Qurbanjan
// 文件：ClientChatController.go
// 修改时间：2023/11/15 10:54
func (s ClientChatController) GetChatDetail(c *gin.Context) {
	var request chat.ChatDetailRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	var (
		anyUser, _ = c.Get("user")
		user       = anyUser.(models.User)
	)

	chatService := services.NewChatService(c)
	data, err := chatService.ChatDetail(c, request.OrderID, int64(user.ID), 99)
	if err != nil {
		panic(err)
	}
	chatService.UpdateLastReadAt(request.OrderID)

	s.Success(c, data, "msg", 200)
}
