package controllers

import (
	"io/ioutil"
	"mulazim-api/inits"
	"mulazim-api/models"
	"mulazim-api/services"
	"mulazim-api/tools"
	"mulazim-api/transformers"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

type TerminalController struct {
	BaseController
}

// GetShow
//
//	@Description: 获取客户端最新版本号
//	@author: Alimjan
//	@Time: 2022-09-06 13:08:58
//	@receiver shipper TerminalController
//	@param c *gin.Context
func (shipper TerminalController) GetShow(c *gin.Context) {

	var (
		osType      = c.GetHeader("osType")
		transformer = transformers.NewTerminalTransformer(c)
		service     = services.NewTerminalService(c)
	)
	terminal := service.Show(osType)
	data := transformer.FormatShow(terminal)

	deviceAppVersion := tools.ToInt64(0)
	//  获取当前客户端版本号
	if c.<PERSON>Header("Version-Name") != ""{
		version2 :=tools.ToInt64(tools.ReplaceString(c.<PERSON>eader("Version-Name"),".",""))
		if version2 > 0 {
			deviceAppVersion = version2
		}
	}
	// 只更新设置的版本号 低版本的 配送客户端
	minAppUpgradeVersion := service.BaseService.GetAppConfig("upgrade_shipper_app_min_version") //商家端 最小版本 
	if deviceAppVersion > 0 &&  len(minAppUpgradeVersion) > 0 &&  deviceAppVersion <=  tools.ToInt64(minAppUpgradeVersion){
		data.ForceUpdate = 1
	}else{
		data.ForceUpdate = 0
	}
	if tools.ToInt(osType) == 2 {
		data.ForceUpdate = 0
	}
	shipper.Success(c, data, "msg", 200) //

}
//
// GetAppShow
//  @Description: 用户端App
//  @receiver shipper
//  @param c
//
func (shipper TerminalController) GetAppShow(c *gin.Context) {

	var (
		osType,_      = c.GetQuery("type")
		transformer = transformers.NewTerminalTransformer(c)
		service     = services.NewTerminalService(c)
	)
	terminal := service.AppShow(osType)
	data := transformer.FormatShow(terminal)
	shipper.Success(c, data, "msg", 200) //

}
// flutter App 版本获取
func (shipper TerminalController) GetNewAppShow(c *gin.Context) {
	var (
		osType      = c.GetHeader("osType")
		transformer = transformers.NewTerminalTransformer(c)
		service     = services.NewTerminalService(c)
	)
	terminal := service.NewAppShow(osType,5)//终端类型（1用户客户端，2配送员客户端，3用户wap，4用户web , 5 商家客户端）
	data := transformer.FormatShow(terminal)
	shipper.Success(c, data, "msg", 200) //
}

func (shipper TerminalController) GetAboutDetail(c *gin.Context) {
	type Request struct {
		Type int `form:"type" binding:"required"`
	}
	var request Request
	var (
		language, _ = c.Get("lang")
	)
	err         := c.ShouldBind(&request)
	if err != nil {
		panic(err)
		return
	}
	//  $list = AboutModel::select('id','title', 'title_ug','title_zh', 'content', 'content_ug', 'content_zh')
	//                            ->where('type', $type)
	//                            ->where('state', AboutModel::STATE_OK)
	//                            ->first();
	var about models.About
	db := tools.Db
	db.Where("type = ? and state = ?", 1, 1).First(&about)

	htmlFilePath := "data/about.html"
	htmlContent, err := ioutil.ReadFile(inits.ConfigFilePath+htmlFilePath)
	if err != nil {
		tools.Logger.Infof("Fatal to read HTML file: %v", err)
	}

	// Convert the HTML content to a string
	htmlString := string(htmlContent)

	// Replace the placeholders with actual values
	lang := language.(string)
	htmlString = strings.Replace(htmlString, "{{Title}}", tools.If(lang =="ug",about.TitleUg,about.TitleZh), -1)
	htmlString = strings.Replace(htmlString, "{{Lang}}", language.(string), -1)
	htmlString = strings.Replace(htmlString, "{{Content}}", tools.If(lang =="ug",about.ContentUg,about.ContentZh), -1)

	// Return the modified HTML content
	c.Data(http.StatusOK, "text/html; charset=utf-8", []byte(htmlString))

}
