package lakala

import (
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/services/merchant/lakala"

	"github.com/gin-gonic/gin"
)

type WithdrawController struct {
	controllers.BaseController
}

func (withdraw *WithdrawController) Store(c *gin.Context) {
	type RequestData struct {
		Amount  int `form:"amount" binding:"required,gte=1"`
		AdminId int `form:"admin_id" binding:"required,gte=1"`
	}
	var requestData RequestData
	if err := c.ShouldBind(&requestData); err != nil {
		panic(err)
		return
	}
	lakaService := lakala.NewLakalaService(c)
	amount := requestData.Amount
	adminId := requestData.AdminId
	memberNo := configs.MyApp.LakalaConfig.SystemMerNo
	cardId := configs.MyApp.LakalaConfig.MulazimWithdrawCartId
	var bills []int

	result, msg, model := lakaService.SplitAndWithdraw(
		c,
		models.LAKALA_WITHDRAW_SERVICE_OPT_TYPE_MULAZIM,
		amount,
		adminId,
		1, // 美滋来提现默认设置为1， captain
		memberNo,
		cardId,
		bills)
	if result {
		withdraw.Success(c, model, "msg", 200)
		return
	} else {
		withdraw.Fail(c, msg, 400)
		return
	}
}

/***
 * @Author: [rozimamat]
 * @description: 转到余额
 * @Date: 2023-09-04 15:34:31
 */
func (withdraw *WithdrawController) ToBalance(c *gin.Context) {
	type RequestData struct {
		Amount  int `form:"amount" binding:"required,gte=1"`
		AdminId int `form:"admin_id" binding:"required,gte=1"`
	}
	var requestData RequestData
	if err := c.ShouldBind(&requestData); err != nil {
		panic(err)
		return
	}
	lakaService := lakala.NewLakalaService(c)
	amount := requestData.Amount
	adminId := requestData.AdminId
	memberNo := configs.MyApp.LakalaConfig.SystemMerNo

	result, msg, model := lakaService.ToBalance(
		c,
		models.LAKALA_WITHDRAW_SERVICE_OPT_TYPE_MULAZIM,
		amount,
		adminId,
		1, // 美滋来提现默认设置为1， captain
		memberNo)
	if result {
		withdraw.Success(c, model, "msg", 200)
		return
	} else {
		withdraw.Fail(c, msg, 400)
		return
	}
}
