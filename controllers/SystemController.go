package controllers

import (
	"context"
	"fmt"
	"mulazim-api/constants"
	"mulazim-api/jobs"
	"mulazim-api/models"
	"mulazim-api/services"
	"mulazim-api/tools"
	"net/url"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type SystemController struct{
	BaseController
}

/***
 * @Author: [rozimamat]
 * @description: 数据库查询 一遍系统是否正常
 * @Date: 2023-03-16 17:53:02
 * @param {*gin.Context} c
 */
func (sys SystemController) Check(c *gin.Context) {

	success := true
	var rs map[string]interface{}
	err := tools.Db.Table("t_comment").
		Select("t_comment.id").
		Joins("LEFT JOIN `t_admin` ON `t_admin`.`id` = `t_comment`.`shipper_id`").
		Joins("LEFT JOIN `t_user` ON `t_user`.`id` = `t_comment`.`user_id`").
		Joins("LEFT JOIN `admin_areas` ON `admin_areas`.`admin_id` = `t_comment`.`shipper_id`").
		Joins("LEFT JOIN `b_area` ON `b_area`.`id` = `admin_areas`.`area_id`").
		Joins("LEFT JOIN `t_order_detail` ON `t_order_detail`.`id` = `t_comment`.`order_detail_id`").
		Joins("LEFT JOIN `t_restaurant_foods` ON `t_restaurant_foods`.`id` = `t_comment`.`food_id`").
		Order("t_comment.created_at ASC").Limit(1).Scan(&rs).Error
	msg := ""
	if err != nil {
		fmt.Println("数据库错误", err)
		msg = err.Error()
		success = false
	}
	c.JSON(200, gin.H{
		"msg":     msg,
		"success": success,
		"time":    carbon.Now("Asia/Shanghai").ToDateTimeString(),
	})
}

func (sys SystemController) SyncBankBranch(c *gin.Context) {

	bank_id := c.DefaultQuery("bank_id", "")
	city_id := c.DefaultQuery("city_id", "")

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Printf("error: %s\n", err)
			}
		}()
		if len(bank_id) > 0 && len(city_id) == 0 {
			jobs.GetYinShangBankList(true, tools.ToInt(bank_id), false, 0)
		} else if len(bank_id) > 0 && len(city_id) > 0 {
			jobs.GetYinShangBankList(true, tools.ToInt(bank_id), true, tools.ToInt(city_id))
		} else if len(bank_id) == 0 && len(city_id) > 0 {
			jobs.GetYinShangBankList(false, 0, true, tools.ToInt(city_id))
		} else {
			jobs.GetYinShangBankList(false, 0, false, 0)
		}

	}()

	c.JSON(200, gin.H{
		"msg":     "ok",
		"success": true,
		"time":    carbon.Now("Asia/Shanghai").ToDateTimeString(),
	})

}
//推送订单 自动打印 订单推送
func (sys SystemController) OrderPush(c *gin.Context) {

	orderId := tools.ToInt(c.DefaultQuery("order_id", ""))
	
	if orderId == 0 {
		c.JSON(200, gin.H{
			"msg":     "ok",
			"success": false,
			"time":    carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
		return
	}
	var p services.BaseService
	storeUsers :=p.GetMerchantUsersByOrder("id = "+tools.ToString(orderId))
	success :=false
	logPrefix :="order_push_"
	//发送socket 请求 让它 发出声音
	//启动队列
	storeJiguang :=make([]int,0)//已发送激光推送列表 避免重复推送
	//订单属于的店铺有几个人登录就给他发送几个通知
	for _, su := range storeUsers {
		
		if !tools.InArray(su.UserId,storeJiguang){
			storeJiguang = append(storeJiguang,su.UserId)
		}else{ //已经发送过激光推送了 不会再发送了，另一个循环 启动后再次发送
			continue
		}
		// 通过jpush推送
		// 通知 结束不用继续 推送 
		job := jobs.NewPushJob()
		// subTitle := string(order.SerialNumber)
		//  添加要播放的声音类型
		tools.Logger.Info(logPrefix,"-自动打印推送订单order_id:",su.OrderId,",admin_id:",su.UserId)
		job.PushData(jobs.PushData{
			UserId: su.StoreId,
			UserType: constants.PushUserTypeMerchant,
			PushContent: jobs.PushContent{
				TitleUg:   constants.TitleMerchantNewOrderUg,
				TitleZh:   constants.TitleMerchantNewOrderZh,
				ContentUg: constants.ContentMerchantNewOrderUg,
				ContentZh: constants.ContentMerchantNewOrderZh,
				Params:    map[string]interface{}{},
			},
			Client: models.PushDeviceClientMerchant,
			Sound:  constants.SoundMerchantNewOrder,
			ChannelType: constants.ChannelTypeMerchantOrder,
			AdminId:su.UserId,
		})
		success = true
	}

	c.JSON(200, gin.H{
		"msg":     "ok",
		"success": success,
		"time":    carbon.Now("Asia/Shanghai").ToDateTimeString(),
	})

}



//推送订单 自动打印 订单推送
func (sys SystemController) OrderShipperPush(c *gin.Context) {

		//配送端 socket 声音分类
		 //1.有新的订单要抢 
		//2.管理员分配订单 
		//3.管理员取消订单 
		//4.店铺取消订单 
		//5.店铺取消订单 
		//6.聊天室 
		//7.餐厅准备美食 
		//8.客户催单
		//9. 有会议 要参加
		//10. 特殊情况
		//11. 管理员切换配送员

	orderId := tools.ToInt(c.DefaultQuery("order_id", ""))
	shipperId := tools.ToInt(c.DefaultQuery("shipper_id", ""))
	typeId := tools.ToInt(c.DefaultQuery("type", ""))
	
	if orderId == 0 || shipperId ==0 || typeId ==0 {
		c.JSON(200, gin.H{
			"msg":     "ok",
			"success": false,
			"time":    carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
		return
	}
	//取消发送 observer 和 退单接口直接 触发 不用再次发送
	db :=tools.GetDB()
	var order models.OrderToday
	db.Model(&order).Where("id = ?",orderId).Find(&order)
	// redisKeyPrefix :="order_push_"
	logPrefix :="order_push_"
	// orderSendKey :=fmt.Sprintf(redisKeyPrefix+"shipper_%d_%d", order.ID,shipperId)
	
	//配送端 socket 声音分类
	//1.有新的订单要抢 socket 
	//2.管理员分配订单 socket 和 激光 
	//3.管理员取消订单 socket 和 激光
	//4.店铺取消订单   socket 和 激光
	//5.客户取消订单   socket 
	//6.聊天室        socket 和 激光 
	//7.餐厅准备美食   socket 和 激光
	//8.客户催单      socket 和 激光
	//9. 有会议 要参加 socket 激光? 
	//10. 特殊情况      socket 激光? 
	//11. 管理员切换配送员  socket 激光? 
	tools.Logger.Info(logPrefix+"给配送员 后台发送的 推送消息： 订单ID： "+tools.ToString(order.ID)+", 配送员"+tools.ToString(shipperId))
	switch(typeId){
		case 2:// "管理员分配"
			jobs.SendOrderAssignedPush(order,constants.SoundShipperSocketTypeAdminAssignOrder)
		case 3,4,5,11:	// "管理员取消订单了"
			
			from :="cms" //
			switch(typeId){
				case 3:
					from ="cms" //后台
				case 4:
					from ="api" //商家
				case 5:
					from ="customer" //客户
			}
			orderMap :=make(map[string]interface{})
			orderMap["id"]=orderId
			orderMap["serial_number"]=order.SerialNumber


			shipperId :=tools.ToInt(shipperId)
			tools.Logger.Info(logPrefix+"给配送员 取消 退单推送消息： 订单ID： "+tools.ToString(order.ID)+", 配送员"+tools.ToString(shipperId))
			if shipperId > 0 {
				var restaurant models.Restaurant
				rs := tools.GetDB().Model(models.Restaurant{}).Where("id = ?", tools.ToString(order.StoreID)).Scan(&restaurant)
				if rs.RowsAffected > 0 {
					resMap :=make(map[string]interface{})
					resMap["name_ug"] = restaurant.NameUg
					resMap["name_zh"] = restaurant.NameZh
					jobs.SendShipperCancelPush(orderMap,resMap,tools.ToInt(shipperId),from)
					
				}
			}


		default: //其他的 用socket 来继续发送 
			job := jobs.NewOrderPushToShipper1Job()
			job.ProduceMessageToConsumer(map[string]interface{}{
				"user_id": shipperId,
				"order_id": orderId,
				"type":     typeId,
			})

	}

	
	success :=true
	c.JSON(200, gin.H{
		"msg":     "ok",
		"success": success,
		"time":    carbon.Now("Asia/Shanghai").ToDateTimeString(),
	})

}
//给商家发送新订单推送 后台发送
func (sys SystemController) OrderPushToMerchant(c *gin.Context) {

	redisKeyPrefix :="order_push_"
	logPrefix :="order_push_"
	go func ()  {
		defer func() {
			if err := recover(); err != nil {
				tools.Logger.Info(logPrefix,"OrderPushToMerchant",err)
			}
		}()	
		
		orderId := tools.ToInt(c.DefaultQuery("order_id", ""))
		if orderId <=0 {
			return
		}
		redisHelper :=tools.GetRedisHelper()
		
		var p services.BaseService	
			
		where :="state = 4 and id = "+tools.ToString(orderId)
		storeOrderUsers :=p.GetMerchantUsersByOrder(where)
		var storeUsers []constants.StoreUserOrder
		orderSendKey :=""
		for _, storeUser := range storeOrderUsers {
			orderSendKey = storeUser.OrderSendKey
			exists1, _ := redisHelper.Exists(context.Background(),storeUser.OrderSendKey).Result()
			send :=true //是否需要发送
			tools.Logger.Info(logPrefix,"-扫描所有的订单-筛选需要推送的订单-订单存在推送历史",storeUser.OrderId,"---",storeUser.OrderSendKey,"-exists1=",exists1)
			if exists1 != 0 { //订单存在推送历史

				state :=tools.ToInt(redisHelper.Get(context.Background(),orderSendKey).Val())
				tools.Logger.Info(logPrefix,"-扫描所有的订单-筛选需要推送的订单-订单存在推送历史",storeUser.OrderId,"-exists1=",exists1,"-state=",state)
				if state == constants.MerchantOrderPushSendComplete { //1:加入队列 2:发送socket 3:客户端有响应 表示完成
					send = false
					//推送额完成了
					//不用推送 进行下一个了
				}
			}
			tools.Logger.Info(logPrefix,"-扫描所有的订单-筛选需要推送的订单-订单存在推送历史",storeUser.OrderId,"-exists1=",exists1,"-send=",send)
			if send {
				exists :=false
				for _, stu := range storeUsers {
					if stu.UserId == storeUser.UserId {
						exists = true
					}
				}
				if !exists {
					storeUsers = append(storeUsers, storeUser)
				}
			}
		}
		//发送socket 请求 让它 发出声音
		p.SendSocketToMerchant(logPrefix,redisKeyPrefix,storeUsers,true)

	}()

	c.JSON(200, gin.H{
		"msg":     "ok",
		"success": true,
		"time":    carbon.Now("Asia/Shanghai").ToDateTimeString(),
	})
}


//获取电信优惠券
func (sys SystemController) GetTelecomCoupon(c *gin.Context) {
	//1.检查用户 
	//2.创建用户 
	//3.分配优惠券 
	//4.返回结果 
	encryptedText :=c.Query("sign")
	mobile :=c.Query("mobile")
	rawParam, err := url.QueryUnescape(encryptedText)
	if err != nil {
		tools.Logger.Error("GetTelecomCoupon参数解析失败",err)
        sys.Fail(c,"参数解析失败",201)
        return
    }
	service :=services.NewCommonService(c)
	errStr,status :=service.GetTelecomCoupon(mobile,rawParam)
	if status != 200{
		sys.Fail(c,errStr,status)
		return
	}
	sys.Success(c,nil,errStr,200)
}
