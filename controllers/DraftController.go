package controllers

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/configs"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/tools"
	"net/http"
	"time"
)

type DraftController struct {
	BaseController
}

// GetKey
//
// @Description: 获取草稿数据 Redis key
// @Author: Salam
// @Time: 2025-07-10 18:16:21
// @receiver
// @param from int
// @param user models.Admin
// @param key string
func (ctr *DraftController) GetKey(from int, admin models.Admin, key string) string {
	return fmt.Sprintf("draft_%d_%d_%s", from, admin.ID, key)
}

// Save
//
// @Description: 保存草稿通用接口
// @Author: Salam
// @Time: 2025-07-10 17:18:21
// @receiver
// @param ctx *gin.Context
// @param from int
func (ctr *DraftController) Save(ctx *gin.Context, from int) {
	// 定义并解析请求体
	var params struct {
		Key   string `json:"key" binding:"required,min=4"`    // 要存的用户域下的唯一值
		Value string `json:"value" binding:"required,min=10"` // 要存的数据（前端自己定，建议 JSON 字符串）
		TTL   string `json:"ttl"`                             // 生存周期 - 1s / 5m / 24h
	}
	if err := ctx.ShouldBind(&params); err != nil {
		panic(err)
	}

	// 初始化变量
	var (
		ttl         = 15 * 24 * time.Hour // 默认存15天
		timeNow     = carbon.Now(configs.AsiaShanghai)
		redisHelper = tools.GetRedisHelper()
		admin       = permissions.GetAdmin(ctx)
		redisKey    = ctr.GetKey(from, admin, params.Key)
	)

	// 处理 TTL
	if params.TTL != "" {
		_ttl, err := time.ParseDuration(params.TTL)
		if err != nil {
			ctr.Fail(ctx, err.Error(), http.StatusBadRequest)
			return
		}
		ttl = _ttl
	}
	// - 草稿生存时间不得超过30天
	if ttl > 30*24*time.Hour || ttl < 1 {
		ctr.Fail(ctx, "草稿生存周期需要大于0，并且不能超过30天", http.StatusBadRequest)
		return
	}
	timeExpires := timeNow.AddSeconds(tools.ToInt(ttl.Seconds())) // 过期时间

	// 保存
	redisHelper.Set(ctx, redisKey, params.Value, ttl)

	// 返回
	ctr.Success(ctx, map[string]any{
		"key": params.Key,
		//"value": params.Value,
		"expires_at": timeExpires.Format("Y-m-d H:i:s"),
	}, "msg", http.StatusOK)
}

// Get
//
// @Description: 获取草稿通用接口
// @Author: Salam
// @Time: 2025-07-10 18:21:21
// @receiver
// @param ctx *gin.Context
// @param from int
func (ctr *DraftController) Get(ctx *gin.Context, from int) {
	// 定义并解析请求体
	var params struct {
		Key string `uri:"key" binding:"required,min=4"` // 要获取的用户域下的唯一值
	}
	if err := ctx.ShouldBindUri(&params); err != nil {
		panic(err)
	}

	// 初始化变量
	var (
		redisHelper = tools.GetRedisHelper()
		admin       = permissions.GetAdmin(ctx)
		redisKey    = ctr.GetKey(from, admin, params.Key)
	)

	// 保存
	res := redisHelper.Get(ctx, redisKey).Val()

	// 返回
	ctr.Success(ctx, map[string]any{
		"key":   params.Key,
		"value": res,
	}, "msg", http.StatusOK)
}

// Delete
//
// @Description: 删除草稿通用接口
// @Author: Salam
// @Time: 2025-07-10 18:24:21
// @receiver
// @param ctx *gin.Context
// @param from int
func (ctr *DraftController) Delete(ctx *gin.Context, from int) {
	// 定义并解析请求体
	var params struct {
		Key string `uri:"key" binding:"required,min=4"` // 要删除的用户域下的唯一值
	}
	if err := ctx.ShouldBindUri(&params); err != nil {
		panic(err)
	}

	// 初始化变量
	var (
		redisHelper = tools.GetRedisHelper()
		admin       = permissions.GetAdmin(ctx)
		redisKey    = ctr.GetKey(from, admin, params.Key)
	)

	// 保存
	res := redisHelper.Del(ctx, redisKey).Val()

	// 返回
	ctr.Success(ctx, map[string]any{
		"key":   params.Key,
		"res": res,
	}, "msg", http.StatusOK)
}
