package controllers

import (
	"fmt"
	"math"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/models"
	"mulazim-api/permissions"
	restaurantRequest "mulazim-api/requests/RestaurantRequest"
	"mulazim-api/services"
	"mulazim-api/tools"
	"mulazim-api/transformers"
	cmsTransformer "mulazim-api/transformers/cms"
	"net/http"

	"mulazim-api/lang"
	"path/filepath"

	"github.com/gin-gonic/gin"
)

type RestaurantFoodsController struct {
	BaseController
}

// GetList
//
// @Description: 美食列表接口
// @Author: Rixat
// @Time: 2024-10-08 16:56:53
// @receiver
// @param c *gin.Context
func (r *RestaurantFoodsController) GetList(c *gin.Context, fromType int) {
	var (
		params         restaurantRequest.RestaurantListRequest
		err     = c.ShouldBind(&params)
		service = services.NewRestaurantFoodsService(c)
		cmsTransformer = cmsTransformer.NewRestaurantFoodsTransformer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	if fromType == constants.FromMerchantClient {
		// 检查权限
		restaurant, err := permissions.GetRestaurantInfoByContent(c)
		if err != nil {
			r.Fail(c, "fail", -1000)
			return
		}
		// 强制返回，有权限获取的餐厅数据
		params.RestaurantID = restaurant.ID
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	// 获取信息
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	totalCount, foodsList := service.GetList(params,fromType)
	// 格式化结果
	seckillListFormat := cmsTransformer.FormatRestaurantFoodsList(foodsList)
	r.Success(c, map[string]interface{}{
		"total": totalCount,
		"items": seckillListFormat,
	}, "success", 200)
}

// GetDetail
//
// @Description: 美食详情接口
// @Author: Rixat
// @Time: 2024-10-08 16:56:53
// @receiver
// @param c *gin.Context
func (ctr *RestaurantFoodsController) GetDetail(ctx *gin.Context) {
	var params struct {
		ID int `uri:"id" binding:"required,min=1"`
	}
	if err := ctx.ShouldBindUri(&params); err != nil {
		panic(err)
	}
	var (
		foodSpecService = services.NewRestaurantFoodsSpecService(ctx)
		foodSpecTransformer = transformers.NewFoodSpecTransformer(ctx)
	)

	// 调用餐厅美食服务
	_restaurantFoodsSvc := services.NewRestaurantFoodsService(ctx)
	food, err := _restaurantFoodsSvc.GetFoodsByID(params.ID)
	if food == nil || food.ID == 0 || err != nil {
		ctr.Fail(ctx, "fail", -1000)
		return
	}
	
	_restaurantFoodsTrns := cmsTransformer.NewRestaurantFoodsTransformer(ctx)
	formattedFood := _restaurantFoodsTrns.FormatRestaurantFoodDetail(food)
	if food.FoodType == models.RestaurantFoodsTypeSpec {
		specList := foodSpecService.GetFoodSpecList(food.ID)
		resSpec := foodSpecTransformer.FormatFoodSpecList(specList)
		formattedFood.FoodSpecTypes = resSpec
	}
	
	// 获取待审核美食的元数据
	if food.State == 3 {
		formattedFood.OldFoodInfo = _restaurantFoodsSvc.GetOldFoodInfo(*food)
	}
	ctr.Success(ctx, formattedFood, "msg", http.StatusOK)
}

// PostCreate 创建餐厅美食
// @Summary 创建餐厅美食
// @Description 创建餐厅美食
// @Tags 餐厅美食
// @Accept json
// @Produce json
// @Param data body restaurantRequest.RestaurantFoodsCreateRequest true "创建餐厅美食请求"
// @Success 200 {object} utils.Response
// @Router /ug/cms/v2/restaurant/foods [post]
func (ctr *RestaurantFoodsController) PostCreate(ctx *gin.Context,fromType int) {
	var (
		_restaurantSvc = services.NewRestaurantService(ctx)
		_restaurantFoodsSvc = services.NewRestaurantFoodsService(ctx)
		body restaurantRequest.RestaurantFoodsCreateBody
		err = ctx.ShouldBind(&body)
		l, _ = ctx.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	if err != nil {
		panic(err)
		return
	}

	// 检查权限
	admin := permissions.GetAdmin(ctx)
	if !admin.HasResId(ctx, body.RestaurantID) {
		ctr.Fail(ctx, "forbidden", http.StatusForbidden)
		return
	}

	// 检查餐厅是否存在
	restaurant, err := _restaurantSvc.GetByID(body.RestaurantID)
	if restaurant == nil || restaurant.ID == 0 || err != nil {
		ctr.Fail(ctx, "restaurant_not_exist", -1000)
		return
	}

	// 从商家端发出的请求，初始化分润比例 / 状态等
	if fromType == constants.FromMerchantClient {
		mpPercent := restaurant.Area.MpPercent
		maxDealerPercent := _restaurantFoodsSvc.GetMaxDealerPercent(*restaurant)
		body.DistributionPercent = mpPercent+maxDealerPercent
		body.YourSelfTakePercent = 20
		body.State = models.RESTAURANTFOODS_STATE_INPREVIEW
	}

	// 验证24小时营业：美食出餐时间段是否在餐厅出餐范围内
	if body.FoodType != models.RestaurantFoodsTypeCombo && !tools.TimeLineInTimeLine(body.BeginTime+":00", body.EndTime+":00", restaurant.BeginTime, restaurant.EndTime) {
		tools.Logger.Error(fmt.Sprintf("美食的出餐时间段 %s - %s", body.BeginTime, body.EndTime),
			fmt.Sprintf("美食的出餐时间段不在餐厅的出餐范围内 (%s - %s)", restaurant.BeginTime, restaurant.EndTime))
		ctr.Fail(ctx, "time_confilict_with_res", -1000)
		return
	}

	// 如果是套餐，那么必须得有子美食
	if body.FoodType == models.RestaurantFoodsTypeCombo{
		if body.ComboFoodItems == nil || len(body.ComboFoodItems) == 0 {
			ctr.Fail(ctx, "foods_combo_items_empty", -1000)
			return
		}
		if body.OriginalPrice < body.Price {
			ctr.Fail(ctx, "combo_price_need_le_items_price", -1000)
			return
		}
	}
	
	
	foodCfg := configs.MyApp.Food
	// 美食利润百分比范围验证
	if body.FoodType != models.RestaurantFoodsTypeCombo && (body.DistributionPercent < foodCfg.Percent.MPDealerMin || body.DistributionPercent > foodCfg.Percent.MPDealerMax) {
		ctr.Fail(ctx, fmt.Sprintf("%s (%.2f - %.2f)", langUtil.T( "food_percent_range"), foodCfg.Percent.MPDealerMin, foodCfg.Percent.MPDealerMax), -1000)
		return
	}

	// 自取订单利润百分比范围验证
	if restaurant.CanSelfTake == 1 { // 店铺开启自取
		if body.FoodType != models.RestaurantFoodsTypeCombo && (body.YourSelfTakePercent < foodCfg.SelfPercent.MPDealerMin || body.YourSelfTakePercent > foodCfg.SelfPercent.MPDealerMax) {
			ctr.Fail(ctx, fmt.Sprintf("%s (%.2f - %.2f)", langUtil.T( "self_take_percent_range"), foodCfg.SelfPercent.MPDealerMin, foodCfg.SelfPercent.MPDealerMax), -1000)
			return
		}
	}
	
	ok, errStr, food := _restaurantFoodsSvc.CreateRestaurantFoods(body, *restaurant)
	if !ok {
		tools.Logger.Error(errStr)
		ctr.Fail(ctx, errStr, -1000)
		return
	}

	// 如果是套餐，那么创建与子美食的关联关系
	if body.FoodType == models.RestaurantFoodsTypeCombo {
		_foodsComboSvc := services.NewFoodsComboService(ctx)
		comboFoodItems, err := _foodsComboSvc.CreateFoodsComboItems(*food, body.ComboFoodItems)
		if err != nil {
			tools.Logger.Error(err.Error())
			ctr.Fail(ctx, err.Error(), -1000)
			return
		}
		// 保存套餐美食
		food.ComboFoodItems = comboFoodItems
		// 第一次创建套餐，创建组
		food.FoodsGroupId = _foodsComboSvc.CreateFoodsComboGroup(*food)
		// 饭盒计算
		totalLunchBoxFee := 0
		totalFoodsPrice := 0
		oldMpProfit := 0.0
		oldDealerProfit := 0.0
		oldDealerYourSelfTakeProfit := 0.0
		oldMpYourSelfTakeProfit := 0.0
		var comboItems []models.FoodsComboItem
		tools.Db.Model(models.FoodsComboItem{}).Where("combo_id",food.ID).Preload("RestaurantFood.LunchBox").Preload("SelectedSpec").Find(&comboItems)
		for _,item := range comboItems {
			if item.RestaurantFood.LunchBoxAccommodate > 0 {
				totalLunchBoxFee += int(math.Ceil(float64(item.RestaurantFood.LunchBoxFee*item.Count) / float64(item.RestaurantFood.LunchBoxAccommodate)))
			}
			foodPrice := item.RestaurantFood.Price
			if item.RestaurantFood.FoodType == 1 {
				foodPrice = uint(item.SelectedSpec.Price)
			}
			totalFoodsPrice += tools.ToInt(foodPrice)*item.Count
			oldMpProfit += float64(foodPrice*uint(item.Count)) * item.RestaurantFood.MpPercent
			oldDealerProfit += float64(foodPrice*uint(item.Count)) * item.RestaurantFood.DealerPercent
			oldDealerYourSelfTakeProfit += float64(foodPrice*uint(item.Count)) * item.RestaurantFood.DealerYourselfTakePercent
			oldMpYourSelfTakeProfit += float64(foodPrice*uint(item.Count)) * item.RestaurantFood.MpYourselfTakePercent
		}
		// 套餐原价和套餐价比例
		comboPercent := float64(body.Price) / float64(totalFoodsPrice)
		// 代理比例
		dealerProfit := oldDealerProfit*comboPercent
		comboDealerPercent :=  float64(dealerProfit) / float64( body.Price)
		// 平台比例
		mpProfit := oldMpProfit*comboPercent
		comboMpPercent :=  float64(mpProfit) / float64( body.Price)
		// 代理自取比例
		dealerYourSelfTakeProfit := oldDealerYourSelfTakeProfit*comboPercent
		comboDealerYourSelfTakePercent :=  float64(dealerYourSelfTakeProfit) / float64( body.Price)
		// 平台自取比例
		mpYourSelfTakeProfit := oldMpYourSelfTakeProfit*comboPercent
		comboMpYourSelfTakePercent :=  float64(mpYourSelfTakeProfit) / float64( body.Price)
		tools.Db.Model(models.RestaurantFoods{}).Where("id",food.ID).UpdateColumns(map[string]interface{}{
			"lunch_box_fee": totalLunchBoxFee,
			"lunch_box_accommodate": 1,
			"lunch_box_id": 500,
			"dealer_percent": comboDealerPercent,
			"mp_percent": comboMpPercent,
			"dealer_yourself_take_percent": comboDealerYourSelfTakePercent,
			"mp_yourself_take_percent": comboMpYourSelfTakePercent,
			"foods_group_id":food.FoodsGroupId,
		})
		
	}
	// 商家端
	if fromType == constants.FromMerchantClient {
		// 发送通知
		_restaurantFoodsSvc.AddNotificationCreate(*food, *restaurant)
	}

	fromStr := tools.If(fromType == constants.FromMerchantClient, "merchant", "cms")

	// 记录日志
	tools.Logger.Info("重要日志", map[string]any{
		"请求参数":  body,
		"file":      "RestaurantFoodsController.go",
		"line": 135,
		"project":       "mulazim-api-go",
		"admin_id":      admin.ID,
		"object_id": food.ID,
		"restaurant_id": body.RestaurantID,
		"type": tools.If(body.FoodType == models.RestaurantFoodsTypeCombo,
			fmt.Sprintf("%s_combo_create", fromStr),
			fmt.Sprintf("%s_food_create", fromStr),
		),
	})
	ctr.Success(ctx, map[string]interface{}{
		"id": food.ID,
	}, "msg", http.StatusOK)
}

// PostEdit 编辑餐厅美食
// @Summary 编辑餐厅美食
// @Description 编辑餐厅美食
// @Tags 餐厅美食
// @Accept json
// @Produce json	
// @Param data body restaurantRequest.RestaurantFoodsEditRequest true "编辑餐厅美食请求"
// @Success 200 {object} utils.Response
// @Router /ug/cms/v2/restaurant/foods/edit [post]
func (ctr *RestaurantFoodsController) PostEdit(ctx *gin.Context,fromType int) {
	var (
		_restaurantSvc = services.NewRestaurantService(ctx)
		_restaurantFoodsSvc = services.NewRestaurantFoodsService(ctx)
		body restaurantRequest.RestaurantFoodsCreateBody
		err = ctx.ShouldBind(&body)
		l, _ = ctx.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	if err != nil {
		panic(err)
		return
	}
	 type ID struct {
		ID int `uri:"id" binding:"required,min=1"`
	}
	var id ID
	if err := ctx.ShouldBindUri(&id); err != nil {
		panic(err)
		return
	}
	// 检查权限
	admin := permissions.GetAdmin(ctx)
	if !admin.HasResId(ctx, body.RestaurantID) {
		ctr.Fail(ctx, "forbidden", http.StatusForbidden)
		return
	}

	// 检查餐厅是否存在
	restaurant, err := _restaurantSvc.GetByID(body.RestaurantID)
	if restaurant == nil || restaurant.ID == 0 || err != nil {
		ctr.Fail(ctx, "restaurant_not_exist", -1000)
		return
	}

	// 验证24小时营业：美食出餐时间段是否在餐厅出餐范围内
	if !tools.TimeLineInTimeLine(body.BeginTime+":00", body.EndTime+":00", restaurant.BeginTime, restaurant.EndTime) {
		tools.Logger.Error(fmt.Sprintf("%s %s - %s", langUtil.T("foods_time_not_in_restaurant_time_range"), body.BeginTime, body.EndTime))
		ctr.Fail(ctx, fmt.Sprintf("%s (%s - %s)", langUtil.T("foods_time_not_in_restaurant_time_range"), restaurant.BeginTime, restaurant.EndTime), -1000)
		return
	}

	// 如果是套餐，那么必须得有子美食
	// 如果是套餐，那么必须得有子美食
	if body.FoodType == models.RestaurantFoodsTypeCombo{
		if body.ComboFoodItems == nil || len(body.ComboFoodItems) == 0 {
			ctr.Fail(ctx, "foods_combo_items_empty", -1000)
			return
		}
		if body.OriginalPrice < body.Price {
			ctr.Fail(ctx, "combo_price_need_le_items_price", -1000)
			return
		}
	}
	
	oldFood, err := _restaurantFoodsSvc.GetFoodsByID(id.ID)
	if err != nil {
		ctr.Fail(ctx, err.Error(), -1000)
		return
	}

	// 验证套餐
	if oldFood.State != 1 && body.State == 1 {
		err = _restaurantFoodsSvc.ValidCombo(oldFood.ID)
		if err != nil {
			ctr.Fail(ctx, err.Error(), -1000)
			return
		}
	}
	// 从商家端发出的请求，初始化分润比例 / 状态等
	if fromType == constants.FromMerchantClient {
		mpPercent := restaurant.Area.MpPercent
		maxDealerPercent := _restaurantFoodsSvc.GetMaxDealerPercent(*restaurant)
		body.DistributionPercent = mpPercent + maxDealerPercent
		body.YourSelfTakePercent = 20
		body.State = models.RESTAURANTFOODS_STATE_INPREVIEW
	}
	// 商家端
	if fromType == constants.FromMerchantClient {
		if _restaurantFoodsSvc.IsReviewEditFood(body, *oldFood) {
			body.State = models.RESTAURANTFOODS_STATE_INPREVIEW
		}
	}
	// 调用服务编辑美食
	ok, errStr, food := _restaurantFoodsSvc.EditRestaurantFoods(id.ID, body, *restaurant, fromType)
	if !ok {
		tools.Logger.Error(errStr)
		ctr.Fail(ctx,errStr, -1000)
		return
	}

	// 如果是套餐，那么创建与子美食的关联关系
	if body.FoodType == models.RestaurantFoodsTypeCombo {
		_foodsComboSvc := services.NewFoodsComboService(ctx)
		tools.Db.Model(models.FoodsComboItem{}).Where("combo_id",food.ID).Delete(&models.FoodsComboItem{})
		comboFoodItems, err := _foodsComboSvc.CreateFoodsComboItems(*food, body.ComboFoodItems)
		if err != nil {
			tools.Logger.Error(err.Error())
			ctr.Fail(ctx, err.Error(), -1000)
			return
		}
		food.ComboFoodItems = comboFoodItems
		// 饭盒计算
		totalLunchBoxFee := 0
		totalFoodsPrice := 0
		oldMpProfit := 0.0
		oldDealerProfit := 0.0
		oldDealerYourSelfTakeProfit := 0.0
		oldMpYourSelfTakeProfit := 0.0
		var comboItems []models.FoodsComboItem
		tools.Db.Model(models.FoodsComboItem{}).Where("combo_id",food.ID).Preload("RestaurantFood.LunchBox").Preload("SelectedSpec").Find(&comboItems)
		for _,item := range comboItems {
			if item.RestaurantFood.LunchBoxAccommodate > 0 {
				totalLunchBoxFee += int(math.Ceil(float64(item.RestaurantFood.LunchBoxFee*item.Count) / float64(item.RestaurantFood.LunchBoxAccommodate)))
			}
			foodPrice := item.RestaurantFood.Price
			if item.RestaurantFood.FoodType == 1 {
				foodPrice = uint(item.SelectedSpec.Price)
			}
			totalFoodsPrice += tools.ToInt(foodPrice)*item.Count
			oldMpProfit += float64(foodPrice*uint(item.Count)) * item.RestaurantFood.MpPercent
			oldDealerProfit += float64(foodPrice*uint(item.Count)) * item.RestaurantFood.DealerPercent
			oldDealerYourSelfTakeProfit += float64(foodPrice*uint(item.Count)) * item.RestaurantFood.DealerYourselfTakePercent
			oldMpYourSelfTakeProfit += float64(foodPrice*uint(item.Count)) * item.RestaurantFood.MpYourselfTakePercent
		}
		// 套餐原价和套餐价比例
		comboPercent := float64(body.Price) / float64(totalFoodsPrice)
		// 代理比例
		dealerProfit := oldDealerProfit*comboPercent
		comboDealerPercent :=  float64(dealerProfit) / float64( body.Price)
		// 平台比例
		mpProfit := oldMpProfit*comboPercent
		comboMpPercent :=  float64(mpProfit) / float64( body.Price)
		// 代理自取比例
		dealerYourSelfTakeProfit := oldDealerYourSelfTakeProfit*comboPercent
		comboDealerYourSelfTakePercent :=  float64(dealerYourSelfTakeProfit) / float64( body.Price)
		// 平台自取比例
		mpYourSelfTakeProfit := oldMpYourSelfTakeProfit*comboPercent
		comboMpYourSelfTakePercent :=  float64(mpYourSelfTakeProfit) / float64( body.Price)
		tools.Db.Model(models.RestaurantFoods{}).Where("id",food.ID).UpdateColumns(map[string]interface{}{
			"lunch_box_fee": totalLunchBoxFee,
			"lunch_box_accommodate": 1,
			"lunch_box_id": 500,
			"dealer_percent": comboDealerPercent,
			"mp_percent": comboMpPercent,
			"dealer_yourself_take_percent": comboDealerYourSelfTakePercent,
			"mp_yourself_take_percent": comboMpYourSelfTakePercent,
		})
	}

	if fromType == constants.FromMerchantClient && _restaurantFoodsSvc.IsReviewEditFood(body, *oldFood) {
		_restaurantFoodsSvc.AddNotificationEdit(*oldFood, *food, *restaurant)
	}

	fromStr := tools.If(fromType == constants.FromMerchantClient, "merchant", "cms")

	tools.Logger.Info("重要日志", map[string]any{
		"请求参数":  body,
		"file":      "RestaurantFoodsController.go",
		"line": 218,
		"project":       "mulazim-api-go",
		"admin_id":      admin.ID,
		"object_id": food.ID,
		"restaurant_id": body.RestaurantID,
		"type": tools.If(body.FoodType == models.RestaurantFoodsTypeCombo,
			fmt.Sprintf("%s_combo_edit", fromStr),
			fmt.Sprintf("%s_food_edit", fromStr),
		),
	})
	ctr.Ok(ctx)
}



// GetOne 获取创建餐厅美食
// @Summary 获取创建餐厅美食
// @Description 获取创建餐厅美食
// @Tags 餐厅美食
// @Accept json
// @Produce json
// @Param param uri int true "获取餐厅美食请求"
// @Success 200 {object} utils.Response
// @Router /ug/cms/v2/restaurant/foods/:id [get]
func (ctr *RestaurantFoodsController) GetOne(ctx *gin.Context) {
	var params struct {
		ID int `uri:"id" binding:"required,min=1"`
	}
	if err := ctx.ShouldBindUri(&params); err != nil {
		panic(err)
	}

	// 调用餐厅美食服务
	_restaurantFoodsSvc := services.NewRestaurantFoodsService(ctx)
	food, err := _restaurantFoodsSvc.GetComboFoodsByID(params.ID)
	if food == nil || food.ID == 0 || err != nil {
		ctr.Fail(ctx, "fail", -1000)
		return
	}
	_restaurantFoodsTrns := cmsTransformer.NewRestaurantFoodsTransformer(ctx)
	formattedFood := _restaurantFoodsTrns.FormatRestaurantComboFoods(food)
	ctr.Success(ctx, formattedFood, "msg", http.StatusOK)
}

// DeleteOne 删除创建餐厅美食
// @Summary 删除 创建餐厅美食
// @Description 删除创建餐厅美食，只会做一次状态的关闭
// @Tags 餐厅美食
// @Accept json
// @Produce json
// @Param param uri int true "删除餐厅美食请求"
// @Success 200 {object} utils.Response
// @Router /ug/cms/v2/restaurant/foods/:id [delete]
func (ctr *RestaurantFoodsController) DeleteOne(ctx *gin.Context) {
	var params struct {
		ID int `uri:"id" binding:"required,min=1"`
	}
	if err := ctx.ShouldBindUri(&params); err != nil {
		panic(err)
	}

	// 检查各类尽心中的活动是否有该餐厅美食，有的话不能删除
	// TODO:

	// 通过后以上检查后，设置该餐厅美食的状态为关闭
	// 调用餐厅美食服务
	_restaurantFoodsSvc := services.NewRestaurantFoodsService(ctx)
	if err := _restaurantFoodsSvc.DeleteRestaurantFood(params.ID); err != nil {
		ctr.Fail(ctx, "fail", -1000)
		return
	}

	// 调用服务创建美食
	ctr.Success(ctx, nil, "msg", http.StatusOK)
}


// GetQuantityList 获取美食规格列表
// @Summary 获取美食规格列表
// @Description 获取美食规格列表
// @Tags 餐厅美食
// @Accept json

func (ctr *RestaurantFoodsController) GetQuantityList(ctx *gin.Context) {
	_restaurantFoodsSvc := services.NewRestaurantFoodsService(ctx)
	quantityList := _restaurantFoodsSvc.GetQuantityList()
	_restaurantFoodsTrns := cmsTransformer.NewRestaurantFoodsTransformer(ctx)
	formattedQuantityList := _restaurantFoodsTrns.FormatRestaurantFoodsQuantityList(quantityList)
	ctr.Success(ctx, formattedQuantityList, "msg", http.StatusOK)
}

func (ctr *RestaurantFoodsController) GetFoodTypes(ctx *gin.Context) {
	var params struct {
		Keyword string `form:"keyword" binding:""`
	}
	if err := ctx.ShouldBind(&params); err != nil {
		panic(err)
		return
	}
	_restaurantFoodsSvc := services.NewRestaurantFoodsService(ctx)
	foodTypesList := _restaurantFoodsSvc.GetFoodTypes(params.Keyword)
	_restaurantFoodsTrns := cmsTransformer.NewRestaurantFoodsTransformer(ctx)
	formattedFoodTypesList := _restaurantFoodsTrns.FormatRestaurantFoodsTypeList(foodTypesList)
	ctr.Success(ctx, formattedFoodTypesList, "msg", http.StatusOK)
}

// GetListForSelectTable 获取美食列表用于选择表
// @Summary 获取美食列表用于选择表
// @Description 获取美食列表用于选择表
// @Tags 餐厅美食
// @Accept json
func (ctr *RestaurantFoodsController) GetListForSelectTable(ctx *gin.Context) {
	var params struct {
		AllFoodID int `form:"all_food_id" binding:""`
		Kw string `form:"kw" binding:""`
		Page int `form:"page" binding:""`
		Limit int `form:"limit" binding:""`
	}
	if err := ctx.ShouldBind(&params); err != nil {
		panic(err)
		return
	}
	_restaurantFoodsSvc := services.NewRestaurantFoodsService(ctx)
	foodList,totalCount := _restaurantFoodsSvc.GetListForSelectTable(params.AllFoodID,params.Kw,params.Page,params.Limit)
	_restaurantFoodsTrns := cmsTransformer.NewRestaurantFoodsTransformer(ctx)
	formattedFoodList := _restaurantFoodsTrns.FormatRestaurantFoodsListForSelectTable(foodList)
	ctr.Success(ctx, gin.H{
		"items": formattedFoodList,
		"total": totalCount,
	}, "msg", http.StatusOK)
}


func (ctr *RestaurantFoodsController) GetComboFoodTime(ctx *gin.Context) {
	var params struct {
		FoodIds string `form:"food_ids" binding:""`
	}
	if err := ctx.ShouldBind(&params); err != nil {
		panic(err)
		return
	}
	_restaurantFoodsSvc := services.NewRestaurantFoodsService(ctx)
	beginTime,endTime,err := _restaurantFoodsSvc.GetComboFoodTime(params.FoodIds)
	if err != nil {
		ctr.Fail(ctx, err.Error(), -1000)
		return
	}
	ctr.Success(ctx, gin.H{
		"begin_time": beginTime,
		"end_time": endTime,
	}, "msg", http.StatusOK)
}



// MassImportExcelUpload

func (r *RestaurantFoodsController) MassImport(c *gin.Context) {
	
	var (
	
		service  = services.NewRestaurantFoodsService(c)
		admin             = permissions.GetAdmin(c)
		langUtil, _ = c.Get("lang_util")
		lg = langUtil.(lang.LangUtil)
	)

	

	var allowTypes []string = []string{".xlsx",".zip"}
	typeName := "food-import-file"
	file, err := c.FormFile("file")
	if err != nil {
		r.Fail(c, "FileNotFound", http.StatusBadRequest)
		return
	}
	resId :=tools.ToInt(c.PostForm("res_id"))
	ext := filepath.Ext(file.Filename)

	filePath,_,_,err :=service.BaseService.Upload(typeName, file, ext, allowTypes)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	rs,err:=service.MassImportFoodProcess(lg,"",admin.ID,resId,filePath,false)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	r.Success(c, rs, "msg", 200)


	
}


func (r *RestaurantFoodsController) MassImportConfirm(c *gin.Context) {
	type Params struct {
		Key        string    `form:"key" binding:"required"`  //
		ResId      int    `form:"res_id" binding:"required"`
	}
	var (
		params         Params
		err           = c.ShouldBind(&params)
		admin             = permissions.GetAdmin(c)
		service  = services.NewRestaurantFoodsService(c)
		langUtil, _ = c.Get("lang_util")
		lg = langUtil.(lang.LangUtil)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}	

	rs,err:=service.MassImportFoodProcess(lg,params.Key,admin.ID,params.ResId,params.Key,true)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	r.Success(c, rs, "msg", 200)


}


func (r *RestaurantFoodsController) MassImportResultQuery(c *gin.Context) {
	type Params struct {
		Key        string    `json:"key" form:"key" binding:"required"`  //
		ResId      int    `json:"res_id" form:"res_id" binding:"required"`
	}
	var (
		params         Params
		err           = c.ShouldBind(&params)
		service  = services.NewRestaurantFoodsService(c)
		langUtil, _ = c.Get("lang_util")
		lg = langUtil.(lang.LangUtil)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}	

	rs,err:=service.MassImportResultQuery(lg,params.ResId,params.Key)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	r.Success(c, rs, "msg", 200)


}