package cmd

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/services/marketing"
	"mulazim-api/tools"
)

type MarketingOrderLogController struct {
	controllers.BaseController
}

// Sync 同步活动历史订单状态和配送员ID
// @param type 1 一天内， 2 一周内, 3 一个月内
func (m MarketingOrderLogController) Sync(c *gin.Context) {
	var (
		action = tools.ToInt(c.Query("type"))
	)
	service := marketing.OrderLogService{}
	err := service.SyncOrderStateAndShipperId(action)
	if err != nil {
		m.Fail(c, "fail", 400)
		return
	}
	m.Success(c, nil, "msg", 200)
}
