package cmd

import (
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/controllers"
	"mulazim-api/tools"
	"regexp"
)

type InsertOrderUserTwoTUser struct {
	controllers.BaseController
}
type Order struct {
	ID     int    `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	CityID int    `gorm:"column:city_id"`                       // 下单城市编号
	AreaID int    `gorm:"column:area_id"`                       // 区域编号
	Mobile string `gorm:"column:mobile"`                        // 区域编号
}

type NewUser struct {
	ID            int    `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
	CityID        int    `gorm:"column:user_city_id"`                  // 下单城市编号
	AreaID        int    `gorm:"column:user_area_id"`                  // 区域编号
	State         int    `gorm:"column:state"`                         // 区域编号
	Gender        int    `gorm:"column:gender"`                        // 区域编号
	OrderRegisted int    `gorm:"column:order_registed"`                // 区域编号
	Mobile        string `gorm:"column:mobile"`                        // 区域编号
	Name          string `gorm:"column:name"`                          // 区域编号
	RegIP         string `gorm:"column:reg_ip"`                        // 区域编号
	CreatedAt     string `gorm:"column:created_at"`                    // 区域编号
	UpdatedAt     string `gorm:"column:updated_at"`                    // 区域编号
}

func (u InsertOrderUserTwoTUser) InsertToTUser(context *gin.Context) {
	db := tools.Db
	var mobiles []string
	//select DISTINCT(mobile) from t_order where mobile not in (select t_user.mobile from t_user)
	db.Select("DISTINCT(mobile)").Table("t_order").Where("mobile not in (select t_user.mobile from t_user)").Scan(&mobiles)
	index := 0
	for _, mobile := range mobiles {
		index++
		if u.isMobile(mobile) {
			var order Order
			db.Table("t_order").Where("mobile = ?", mobile).Order("id desc").Scan(&order)
			if order.ID != 0 {
				db.Table("t_user").Create(&NewUser{
					CityID:        order.CityID,
					AreaID:        order.AreaID,
					Mobile:        order.Mobile,
					RegIP:         "0",
					State:         1,
					OrderRegisted: 1,
					Gender:        1,
					Name:          "微信用户",
					CreatedAt:     carbon.Now("Asia/Shanghai").ToDateTimeString(),
					UpdatedAt:     carbon.Now("Asia/Shanghai").ToDateTimeString(),
				})
			}
		}
		if index%1000 == 0 {
			tools.Logger.Infof("已处理%d/%d个数据，", index, len(mobiles))
		}

	}
	tools.Logger.Infof("结束了 %d/%d", index, len(mobiles))
}

// isMobile
//
//	@Description: 判断是否是手机号
//	@author: Alimjan
//	@Time: 2023-07-31 16:35:36
//	@receiver u InsertOrderUserTwoTUser
//	@param mobile string
//	@return bool
func (u InsertOrderUserTwoTUser) isMobile(mobile string) bool {
	if len(mobile) != 11 {
		return false
	}
	// 匹配规则
	// ^1第一位为一
	// [345789]{1} 后接一位345789 的数字
	// \\d \d的转义 表示数字 {9} 接9位
	// $ 结束符
	regRuler := "^1[345789]{1}\\d{9}$"

	// 正则调用规则
	reg := regexp.MustCompile(regRuler)
	// 返回 MatchString 是否匹配
	return reg.MatchString(mobile)
}
