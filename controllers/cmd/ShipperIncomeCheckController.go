package cmd

import (
	"database/sql"
	"mulazim-api/tools"
)

type DateRange struct {
	StartDate string
	EndDate   string
}
func CheckRule01()  {

	type OrderCount struct {
		OrderCount sql.NullInt64 `gorm:"column:order_count" json:"orderData"`
		OrderIncome sql.NullInt64 `gorm:"column:order_income" json:"orderIncome"`
	}
	var orderData OrderCount
	dateRange := DateRange{StartDate: "2023-12-19", EndDate: "2023-12-20"}
	db := tools.Db
	db.Table("t_order").
		Select("count(1) as order_count, sum(JSON_EXTRACT(t_shipper_income_template.rule_content, '$[0].fix_shipment_fee')) as order_income").
		Joins("LEFT JOIN t_admin ON t_admin.id = t_order.shipper_id").
		Joins("LEFT JOIN t_shipper_income_template ON t_admin.shipper_income_template_id = t_shipper_income_template.id").
		Where("t_order.created_at BETWEEN ? AND ?", dateRange.StartDate, dateRange.EndDate).
		Where("t_order.state IN (7,10) AND delivery_type = 1").
		Where("t_shipper_income_template.rule_type = 1").
		Scan(&orderData)



	var shipperIncomeData OrderCount
	db.Table("t_shipper_income as si").
		Select("count(1) as order_count, sum(si.amount) as order_income").
		Joins("LEFT JOIN t_admin as a ON a.id = si.shipper_id").
		Joins("LEFT JOIN t_shipper_income_template ON a.shipper_income_template_id = t_shipper_income_template.id").
		Where("si.date BETWEEN ? AND ?", dateRange.StartDate, dateRange.EndDate).
		Where("si.type = 1").
		Where("t_shipper_income_template.rule_type = 1").
		Scan(&shipperIncomeData)



	var shipperIncomeArchiveData OrderCount
	db.Table("t_shipper_income_archive as sia").
		Select("sum(sia.success_count) as order_count, sum(sia.amount_order) as order_income").
		Joins("LEFT JOIN t_admin a ON a.id = sia.shipper_id").
		Joins("LEFT JOIN t_shipper_income_template ON a.shipper_income_template_id = t_shipper_income_template.id").
		Where("sia.date BETWEEN ? AND ?", dateRange.StartDate, dateRange.EndDate).
		Where("t_shipper_income_template.rule_type = 1").
		Scan(&shipperIncomeArchiveData)

	if orderData.OrderCount.Int64  != shipperIncomeData.OrderCount.Int64 ||
		shipperIncomeData.OrderCount.Int64 != shipperIncomeArchiveData.OrderCount.Int64{
		//打印条件中的所有参数
		tools.Logger.Infof("配送员收入模板1,订单数对账失败,0001,orderData: %d, orderIncome: %d, orderArchive:%d ",
			orderData.OrderCount.Int64,
			shipperIncomeData.OrderCount.Int64,
			shipperIncomeArchiveData.OrderCount.Int64)
	}

	if orderData.OrderIncome.Int64  != shipperIncomeData.OrderIncome.Int64 ||
		shipperIncomeData.OrderIncome.Int64 != shipperIncomeArchiveData.OrderIncome.Int64{
		//打印条件中的所有参数
		tools.Logger.Infof("配送员收入模板1,订单数对账失败,0001,orderData: %d, orderIncome: %d, orderArchive:%d ",
			orderData.OrderIncome.Int64,
			shipperIncomeData.OrderIncome.Int64,
			shipperIncomeArchiveData.OrderIncome.Int64)
	}
}