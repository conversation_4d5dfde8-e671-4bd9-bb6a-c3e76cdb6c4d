package cmd

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/tools"
	"net/http"
	"time"
)
// ClearPayInfo 清理t_pay_lakala中的多余字段
type ClearPayInfo struct {
	controllers.BaseController
}

type ClearPayInfoRequest struct {
	Type       string    `form:"type"`                  // 地区ID
}
// ClearPayInfo 清理t_pay_lakala中的多余字段
func (f ClearPayInfo) ClearPayInfo(c *gin.Context) {

	var (
		params        ClearPayInfoRequest
		err           = c.ShouldBind(&params)
	)

	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	var maxId int64
	db := tools.Db

	if params.Type == "all" {
		tools.Logger.Info("开始处理t_pay_lakala表，type == all")
		const batchSize = 1000
		cutoffDate := time.Now().AddDate(0, 0, -3)
		db.Table("t_pay_lakala").Where("created_at < ?",cutoffDate).Select("max(id)").Find(&maxId)
		for i := int64(1); i < maxId; i=i+batchSize {
			to := i+batchSize
			if to > maxId {
				to = maxId
			}
			err := db.Exec(`
			UPDATE t_pay_lakala 
			SET pay_info = NULL, split_rule_result = NULL, split_rule_data = NULL,front_url = NULL,back_url = NULL,pay_method = NULL
			WHERE id between ? and ? `, i,to).Error
			if err != nil {
				tools.Logger.Infof("FATAL 开始处理t_pay_lakala表，所有信息处理,%f",err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"error":  "Failed to update records",
					"detail": err.Error(),
				})
				return
			}
		}
	}else{
		tools.Logger.Infof("开始处理t_pay_lakala表，前天信息处理,%s",tools.Today("Asia/Shanghai").SubDays(2).ToDateString())
		err := db.Exec(`
			UPDATE t_pay_lakala 
			SET pay_info = NULL, split_rule_result = NULL, split_rule_data = NULL,front_url = NULL,back_url = NULL,pay_method = NULL
			WHERE created_at between ? and ? `,
			tools.Today("Asia/Shanghai").SubDays(2).ToDateString(),
			tools.Today("Asia/Shanghai").SubDays(1).ToDateString(),
		).Error
		if err != nil {
			tools.Logger.Infof("FATAL 开始处理t_pay_lakala表，前天信息处理,%f",err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":  "Failed to update records",
				"detail": err.Error(),
			})
			return
		}
	}

	tools.Logger.Info("结束处理t_pay_lakala表，所有信息处理")
	// Respond with success
	f.Success(c, nil, "", http.StatusOK)

}