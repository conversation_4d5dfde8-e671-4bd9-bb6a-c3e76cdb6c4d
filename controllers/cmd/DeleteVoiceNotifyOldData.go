package cmd

import (
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

const (
	currentTimeZoneName = "Asia/Shanghai"
)

type DeleteVoiceNotifyOldData struct {
	controllers.BaseController
}

// DeleteOldData
//
//	@Description: 删除voice notify 中的历史数据
//	@author: Alimjan
//	@Time: 2023-08-26 13:20:15
//	@receiver d DeleteVoiceNotifyOldData
//	@param context *gin.Context
func (d DeleteVoiceNotifyOldData) DeleteOldData(context *gin.Context) {
	date := context.DefaultQuery("date", tools.Yesterday(currentTimeZoneName).SubDays(1).ToDateString())
	tools.Logger.Info("正在删除", date, "之前的数据")
	count := 0
	for {
		db := tools.Db
		var datas []models.RestaurantVoiceNotify
		db.Model(&models.RestaurantVoiceNotify{}).Where("push_notice_begin <?", carbon.Parse(date).AddDay().ToDateTimeString()).Limit(100).Scan(&datas)
		count += len(datas)
		if len(datas) == 0 {
			break
		}
		db.Delete(&datas)
		//println(len(datas))
		//break
	}
	tools.Logger.Info("删除", date, "之前的数据已完成,共删除", count, "条数据")
	context.JSON(200, gin.H{
		"code": 200,
		"msg":  "success",
	})
}
