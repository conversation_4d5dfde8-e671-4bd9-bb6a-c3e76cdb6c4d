package cmd

import (
	"context"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/services"
	"mulazim-api/tools"
	"time"

	"github.com/golang-module/carbon/v2"
	
)

func OrderPushToMerchant1() {
	logPrefix :="order_push_"
	defer func() {
		if err := recover(); err != nil {
			tools.Logger.Error(logPrefix+"job1_error-------", err)
		}
	}()
	
	redisHelper :=tools.GetRedisHelper()
	redisKeyPrefix :="order_push_"
	
	var p services.BaseService
	where :=""

	
	for {
		//监测 待 接单订单
		tools.Logger.Info(logPrefix,"-扫描所有的订单-开始新一轮")

		currentTimeFormat := carbon.Now("Asia/Shanghai").AddMinutes(15).Format("Y-m-d H:i:s")
		
		
		where ="state = 3 and ((`print_time`<='"+currentTimeFormat+"') OR (`print_time`>='"+currentTimeFormat+"' AND `send_notify`=1) OR `order_type`=1)"	
		where+=" and created_at > '"+carbon.Now(configs.AsiaShanghai).AddDays(-1).Format("Y-m-d 00:00:00")+"'"
		storeOrderUsers :=p.GetMerchantUsersByOrder(where)
		var storeUsers []constants.StoreUserOrder
		orderSendKey :=""
		for _, storeUser := range storeOrderUsers {
			orderSendKey = storeUser.OrderSendKey
			exists1, _ := redisHelper.Exists(context.Background(),storeUser.OrderSendKey).Result()
			send :=true //是否需要发送
			tools.Logger.Info(logPrefix,"-扫描所有的订单-筛选需要推送的订单-订单存在推送历史",storeUser.OrderId,"---",storeUser.OrderSendKey,"-exists1=",exists1)
			if exists1 != 0 { //订单存在推送历史

				state :=tools.ToInt(redisHelper.Get(context.Background(),orderSendKey).Val())
				tools.Logger.Info(logPrefix,"-扫描所有的订单-筛选需要推送的订单-订单存在推送历史",storeUser.OrderId,"-exists1=",exists1,"-state=",state)
				if state == constants.MerchantOrderPushSendComplete { //1:加入队列 2:发送socket 3:客户端有响应 表示完成
					send = false
					//推送额完成了
					//不用推送 进行下一个了
				}
			}
			tools.Logger.Info(logPrefix,"-扫描所有的订单-筛选需要推送的订单-订单存在推送历史",storeUser.OrderId,"-exists1=",exists1,"-send=",send)
			if send {
				userExists :=false
				for _, stu := range storeUsers {
					if stu.UserId == storeUser.UserId {
						userExists = true
					}
				}
				if !userExists {
					storeUsers = append(storeUsers, storeUser)
				}
			}
		}
		
		//发送socket 请求 让它 发出声音
		p.SendSocketToMerchant(logPrefix,redisKeyPrefix,storeUsers,false)
		
		tools.Logger.Info(logPrefix,"-扫描所有的订单-结束新一轮")
		time.Sleep(60 * time.Second)
		
	}

	//}
}
