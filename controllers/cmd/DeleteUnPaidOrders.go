package cmd

import (
	"math"
	"mulazim-api/controllers"
	"mulazim-api/models"

	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type DeleteUnPaidOrders struct {
	controllers.BaseController
}
/***
 * @LastEditTime: Do not edit
 * @Author: [rozimamat]
 * @description: 删除 t_order_today 里面的创建日期小于3天前的未支付的订单
 * @Date: 2023-10-24 13:09:19
 */
func (f DeleteUnPaidOrders) DeleteUnPaidOrders(c *gin.Context) {
	count := int64(0)
	pageSize := 100
	date := carbon.Now(currentTimeZoneName).SubDays(3)
	tools.Logger.Info("正在删除", date.Format("Y-m-d"), "之前的未支付的订单数据")
	db := tools.Db
	createdAt := date.Format("Y-m-d")
	items := db.Model(models.OrderToday{}).Where("state < ? and created_at < ?", 3, createdAt)
	items.Count(&count)
	if count > 0 {
		tools.Logger.Info("要删除 ", count, "个未支付的订单数据")
	}
	pages := math.Ceil(tools.ToFloat64(count) / tools.ToFloat64(pageSize))
	for i := 0; i < tools.ToInt(pages); i++ {
		var orders []models.OrderToday
		db.Model(&models.OrderToday{}).Where("state < ? and created_at < ?", 3, createdAt).Limit(pageSize).Scan(&orders)
		err := db.Delete(&orders).Error
		if err != nil {
			tools.Logger.Error("定时删除未支付的订单出错 请及时处理", err)
			break
		}
	}

	f.Success(c, gin.H{}, "success", 200)

}
