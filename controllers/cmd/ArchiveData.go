package cmd

import (
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ArchiveData struct {
	controllers.BaseController
}

// DeleteOldData
//
//	@Description: 删除历史推送记录
//	@author: Alim<PERSON>
//	@Time: 2023-08-26 13:20:15
//	@receiver d DeleteVoiceNotifyOldData
//	@param context *gin.Context
func (d ArchiveData) UpdateShipperAttendanceState(context *gin.Context) {
	curTime := carbon.Now().Format("H:i")
	if( curTime != "06:30" ){
		d.<PERSON>ail(context, "当前时间不是06:30", 403)
		return
	}
	db :=tools.GetDB()
	var admins []models.Admin
	db.Model(&models.Admin{}).
		Where("attendance_state <> 2 and `type` in(8,9) and deleted_at is null").Scan(&admins)
	// 获取所有没有下班的配送员
	err := db.Model(&models.Admin{}).
		Where("attendance_state <> 2 and `type` in(8,9)").
		UpdateColumn("attendance_state", 2).Error
	if err != nil {
		d.Fail(context, "更新失败", -1000)
		return
	}
	var adminIds []int64
	for _, admin := range admins {
		adminIds = append(adminIds, int64(admin.ID))
	}
	var adminAreas []models.AdminAreas
	if len(adminIds) > 0 {
		db.Model(&models.AdminAreas{}).Where("admin_id in (?)", adminIds).Scan(&adminAreas)
	}

	for _, admin := range admins {
		
		// 获取配送员的城市跟区域
		cityId :=0
		areaId :=0
		for _, v := range adminAreas {
			if v.AdminID == admin.ID {
				cityId = v.CityID
				areaId = v.AreaID
			}
		}
		//写到日志表
		attendanceLog := models.ShipperAttendanceLog{
			CityId:    cityId,
			AreaId:    areaId,
			ShipperId: admin.ID,
			Name:      admin.Name,
			Mobile:    admin.Mobile,
			Lat:       "0.0",
			Lng:       "0.0",
			Position:  "系统自动强制下班",
			State:     2,
			AdminId:   0,
			Images:    "",
			Type:      2,//系统自动 下班 
		}
		db.Save(&attendanceLog)
	}

	d.Ok(context)
}
