package cmd

import (
	"context"
	"encoding/json"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/elastic/go-elasticsearch/v7/esapi"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ElasticOrderSync struct {
	controllers.BaseController
}


//
//  OrderElasticModel
//  @Description: 订单Elastic模型
//
type OrderElasticModel struct {
	ID                      uint      `gorm:"column:id;primary_key" json:"id"`
	BuildingID              int64     `gorm:"column:building_id" json:"building_id"`
	CreatedAt               time.Time `gorm:"column:created_at" json:"created_at"`
	AreaID                  int64     `gorm:"column:area_id" json:"area_id"`
	UserMobile              string    `gorm:"column:user_mobile" json:"user_mobile"`
	DeliveryType            int64     `gorm:"column:delivery_type" json:"delivery_type"`
	State                   int64     `gorm:"column:state" json:"state"`
	TerminalID              int64     `gorm:"column:terminal_id" json:"terminal_id"`
	StoreID                 int64     `gorm:"column:store_id" json:"store_id"`
	Shipment                int64     `gorm:"column:shipment" json:"shipment"`
	Mobile                  string    `gorm:"column:mobile" json:"mobile"`
	LunchBoxFee             int64     `gorm:"column:lunch_box_fee" json:"lunch_box_fee"`
	Name                    string    `gorm:"column:name" json:"name"`
	OrderID                 string    `gorm:"column:order_id" json:"order_id"`
	CityID                  int64     `gorm:"column:city_id" json:"city_id"`
	PayChannelTradeNo       string    `gorm:"column:pay_channel_trade_no" json:"pay_channel_trade_no"`
	WechatPayChannelTradeNo string    `gorm:"column:wechat_pay_channel_trade_no" json:"wechat_pay_channel_trade_no"`
}
//导入数据到es
func (e ElasticOrderSync)ImportOldOrderData(startId int,endId int, indexName string) {
	//获取order的数组长度
	//select max(id) from t_order
	if startId==0 || endId == 0 {
		tools.Logger.Infof("输入参数有误")
		return
	}
	db := tools.Db

	// 创建或更新索引并应用映射
	size := 1000
	total := 0
	shouldWait := 0
	for i := startId; i <= endId+size; i = i + size {
		//curStart := i
		var orders []OrderElasticModel
		db.Table(indexName+" o").Where("o.id >= ? and o.id < ?", i, i+size).
			Select("o.id,o.building_id,o.created_at,o.area_id,u.mobile as user_mobile,o.delivery_type,o.state,o.terminal_id,o.store_id,o.shipment,o.mobile,o.lunch_box_fee,o.name,o.order_id,o.city_id," +
				"(select GROUP_CONCAT(lakala.pay_channel_trade_no ORDER BY lakala.id SEPARATOR ', ') from t_pay_lakala lakala where order_id = o.id) as pay_channel_trade_no," +
				"(select GROUP_CONCAT(we.transaction_id ORDER BY we.id SEPARATOR ', ') from wechat we where we.order_id = o.id) as wechat_pay_channel_trade_no").
			Joins("LEFT JOIN t_user u ON u.id = o.user_id").
			Scan(&orders)
		if len(orders) == 0 {
			continue
		}
						
		total += len(orders)
		tools.Logger.Infof("从%d到%d插入%d %d   %d", i, i+size, len(orders),startId,endId)
		// 准备批量索引的文档数据
		var buf strings.Builder
		for _, order := range orders {
			indexLine := fmt.Sprintf(`{"index":{"_index":"%s","_type":"_doc","_id":%d}}`, indexName, order.ID)
			docBody, err := json.Marshal(order)
			if err != nil {
				tools.Logger.Infof("Fatal encoding document body: %s", err)
			}
			// 在文档数据之间添加换行符
			fmt.Fprintf(&buf, "%s\n", indexLine)
			fmt.Fprintf(&buf, "%s\n", docBody)
		}
		// 创建批量请求
		req := esapi.BulkRequest{
			Body:         strings.NewReader(buf.String()),
			DocumentType: "_doc",
		}
		// 执行批量请求
		res, err := req.Do(context.Background(), tools.Es)
		if err != nil {
			tools.Logger.Errorf("Error executing bulk request: %s", err)
		}
		res.Body.Close()
		shouldWait++
		if shouldWait>15 {
			shouldWait = 0
			time.Sleep(time.Minute*1)	
		}else{
			time.Sleep(time.Second*1)
		}
		
	}
	tools.Logger.Infof("数据导入结束，总共数据: %d", total)
}


//同步今日订单
func (e ElasticOrderSync)SyncOrderToday(c *gin.Context) {
	type Params struct {
		//action = 1 ,删除 t_order_today t_order_today 数据
		//action = 2 ,删除t_order 索引，并且创建新的
		//action = 3, 同步昨日订单数据到elastic search
		//action = 4, 则同步开始日期到结束日期中的所有的数据
		Action      string    `form:"action" binding:"required"`                
		StartDate   string    `form:"start_date"`
		EndDate     string 	  `form:"end_date"` 
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
		return
	}  
	//action = 1 ,删除t_order_today 索引，并且新建，同步 t_order_today 数据
	if params.Action == "1" {
		indexName := "t_order_today"
		tools.DeleteOrderIndex(indexName)
		tools.CreateOrderIndex(indexName)
		db := tools.Db
		var endId *int
		var startId *int
		db.Raw("select max(id) as max_id from " + indexName).Scan(&endId)
		db.Raw("select min(id) as max_id from " + indexName).Scan(&startId)
		e.ImportOldOrderData(*startId,*endId,indexName)
		e.Success(c,nil,"action = 1 ,删除t_order_today 索引，并且新建，同步 t_order_today 数据,删除索引成功",200)
		return
	}
	//action = 2 ,删除t_order 索引，并且创建新的
	if params.Action == "2" {
		indexName := "t_order"
		tools.DeleteOrderIndex(indexName)
		tools.CreateOrderIndex(indexName)
		e.Success(c,nil,"action = 2 ,删除t_order 索引，并且创建新的,删除索引成功",200)
		return
	}
	//action = 3, 同步昨日订单数据到elastic search
	if params.Action == "3" {
		indexName := "t_order"
		var endId *int
		var startId *int
		db := tools.Db
		yesterday := carbon.Yesterday().ToDateString(configs.AsiaShanghai)
		db.Table(indexName).Select("max(id)").Where("created_at > ?",yesterday+" 00:00:00").Where("created_at < ?",yesterday+" 23:59:59").Scan(&endId)
		db.Table(indexName).Select("min(id)").Where("created_at > ?",yesterday+" 00:00:00").Where("created_at < ?",yesterday+" 23:59:59").Scan(&startId)

		if (startId!=nil ||endId!=nil) {
			e.ImportOldOrderData(*startId,*endId,indexName)
		}

		e.Success(c,nil,"action = 3, 同步昨日订单数据到elastic search,插入昨日数据成功:"+yesterday,200)
		return
	}
	//action = 4, 则同步开始日期到结束日期中的所有的数据
	if params.Action == "4" {
		indexName := "t_order"
		var endId *int
		var startId *int
		db := tools.Db
		db.Table(indexName).Select("max(id)").Where("created_at > ?",params.StartDate+" 00:00:00").Where("created_at < ?",params.EndDate+" 23:59:59").Scan(&endId)
		db.Table(indexName).Select("min(id)").Where("created_at > ?",params.StartDate+" 00:00:00").Where("created_at < ?",params.EndDate+" 23:59:59").Scan(&startId)

		if (startId!=nil ||endId!=nil) {
			e.ImportOldOrderData(*startId,*endId,indexName)
		}

		e.Success(c,nil,"action = 4, 则同步开始日期到结束日期中的所有的数据,插入昨日数据成功:"+params.StartDate+":"+params.EndDate,200)
		return
	}
}
