package cmd

import (
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/tools"

	shipperService "mulazim-api/services/shipper"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ShipperIncomeCalculate struct {
	controllers.BaseController
}

//
//	@Description: 配送员收入计算
//	@author: rozimamat
//	@Time: 2023-11-24 16:10
//	@receiver d CorrectData
//	@param context *gin.Context
func (d ShipperIncomeCalculate) IncomeArchive(c *gin.Context) {

	day :=carbon.Now(configs.AsiaShanghai).Format("Y-m-d")
	incomeArchiveService :=shipperService.NewIncomeArchiveService()
	//计算评论收入
	incomeArchiveService.ArchiveIncomeByDay(day,0,0,0)
	//分别计算一天的收入
	incomeArchiveService.ArchiveIncomeByComment(day,0,0)
	
	//归档一天的数据
	incomeArchiveService.ArchiveIncomeAll(day,0,0)
	//对账
	incomeArchiveService.IncomeCheck(day)
	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "配送员收入写入成功",
	})

}
// 全勤奖计算
func (d ShipperIncomeCalculate) IncomeFullAttendance(c *gin.Context) {
	day :=carbon.Now(configs.AsiaShanghai).Format("Y-m-d")
	incomeArchiveService :=shipperService.NewIncomeArchiveService()
	incomeArchiveService.ArchiveFullAttendanceByDay(day,0,0,0)
	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "配送员收入写入成功",
	})

}
// 按月批量计算配送员收入
func (d ShipperIncomeCalculate) IncomeArchiveAll(c *gin.Context) {

	type DailySaleDataCorrectionRequest struct {
		StartMonth string `form:"start_month" binding:"required"`
		ShipperId int `form:"shipper_id"`
		Date string `form:"date"`
		OldTemplateId int `form:"old_template_id"`
		AreaId int `form:"area_id"`
	}
	var request DailySaleDataCorrectionRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}
	
	incomeArchiveService :=shipperService.NewIncomeArchiveService()
	
	monthStart := carbon.Parse(carbon.ParseByFormat(request.StartMonth, "Y-m").Format("Y-m-d"))
	monthEnd := carbon.Parse(carbon.ParseByFormat(request.StartMonth, "Y-m").AddMonth().Format("Y-m-d"))

	monthLength := monthEnd.DiffAbsInDays(monthStart)
	
	if request.ShipperId > 0 && len(request.Date) > 0{ //某个配送员的 某天的 数据归档 
		
		
		day :=carbon.Parse(request.Date,configs.AsiaShanghai).AddDays(+1).Format("Y-m-d")
		tools.Logger.Info("开始 "+request.Date+" 的 订单基本信息的 归档")
		// tools.Logger.Info(monthLength)
		tools.Logger.Info("开始 "+request.Date+" 的 订单基本信息的 归档")
		incomeArchiveService.ArchiveIncomeByDay(day,request.ShipperId,request.OldTemplateId,request.AreaId)
		tools.Logger.Info("结束 "+request.Date+" 的 订单基本信息的 归档")
		tools.Logger.Info("开始 "+request.Date+" 的 评论 归档")
		incomeArchiveService.ArchiveIncomeByComment(day,request.ShipperId,request.OldTemplateId)
		tools.Logger.Info("结束 "+request.Date+" 的 评论 归档")
		tools.Logger.Info("开始 "+request.Date+" 的 总结的 归档")
		incomeArchiveService.ArchiveIncomeAll(day,request.ShipperId,request.OldTemplateId)
		tools.Logger.Info("结束 "+request.Date+" 的 总结的 归档")

		c.JSON(200, gin.H{
			"code": 200,
			"msg":  "配送员收入写入成功",
		})

		return
	}
	for i := int(monthLength); i > -1; i-- { //某给月份的数据全部归档
		day := monthEnd.AddDays(-i).Format("Y-m-d")
		tools.Logger.Info("开始 "+day+" 的 订单基本信息的 归档")
		incomeArchiveService.ArchiveIncomeByDay(day,0,0,request.AreaId)
		tools.Logger.Info("结束 "+day+" 的 订单基本信息的 归档")
		tools.Logger.Info("开始 "+day+" 的 评论 归档")
		incomeArchiveService.ArchiveIncomeByComment(day,0,0)
		tools.Logger.Info("结束 "+day+" 的 评论 归档")
		tools.Logger.Info("开始 "+day+" 的 总结的 归档")
		incomeArchiveService.ArchiveIncomeAll(day,0,0)
		tools.Logger.Info("结束 "+day+" 的 总结的 归档")
	}

	

	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "配送员收入写入成功",
	})

}

//配送员 收入对账
func (d ShipperIncomeCalculate) IncomeCheck(c *gin.Context) {
	type DailySaleDataCorrectionRequest struct {
		Day string `form:"day" binding:"required"`
	}
	var request DailySaleDataCorrectionRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}
	incomeArchiveService :=shipperService.NewIncomeArchiveService()
	incomeArchiveService.IncomeCheck(request.Day)
}
// 修复跨月时的数据错误问题
func (d ShipperIncomeCalculate) FixLastDayError(c *gin.Context){

	

	type ShipperAndTemplate struct{
		ShipperId int `gorm:"column:shipper_id"`
		TemplateId int `gorm:"column:template_id"`
	}
	incomeArchiveService :=shipperService.NewIncomeArchiveService()
	date :="2023-12-31"
	var errorItems []ShipperAndTemplate
	db :=tools.GetDB()
	db.Table("t_shipper_income").
	Select("shipper_id,template_id").
	Where("amount = ? and type =? and date = ?",0,1,date).
	Group("shipper_id").
	Order("id asc").
	Scan(&errorItems)
	for _, v := range errorItems {

		day :=carbon.Parse(date,configs.AsiaShanghai).AddDays(+1).Format("Y-m-d")
		tools.Logger.Info("开始 "+date+" 的 订单基本信息的 归档")
		tools.Logger.Info("开始 "+date+" 的 订单基本信息的 归档")
		incomeArchiveService.ArchiveIncomeByDay(day,v.ShipperId,v.TemplateId,0)
		tools.Logger.Info("结束 "+date+" 的 订单基本信息的 归档")
		tools.Logger.Info("开始 "+date+" 的 评论 归档")
		incomeArchiveService.ArchiveIncomeByComment(day,v.ShipperId,v.TemplateId)
		tools.Logger.Info("结束 "+date+" 的 评论 归档")
		tools.Logger.Info("开始 "+date+" 的 总结的 归档")
		incomeArchiveService.ArchiveIncomeAll(day,v.ShipperId,v.TemplateId)
		tools.Logger.Info("结束 "+date+" 的 总结的 归档")

		
	}
	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "配送员2023-12-31日数据更改写入成功",
	})

}

// 修复跨月时的数据错误问题
func (d ShipperIncomeCalculate) FixSpecialOrderShipperPriceError(c *gin.Context){

	
	incomeArchiveService :=shipperService.NewIncomeArchiveService()
	type ShipperAndTemplate struct{
		ShipperId int `gorm:"column:shipper_id"`
		TemplateId int `gorm:"column:template_id"`
		Date string `gorm:"column:date"`
	}
	var errorItems []ShipperAndTemplate
	db :=tools.GetDB()
	// db.Table("t_shipper_income").Select("shipper_id,date,template_id,concat(shipper_id,'_',date,'_',template_id) as group_key").Where(" date > ? and type=1 and order_type=2 and amount=0","2024-03-08").Group("group_key").Debug().Scan(&errorItems)
	// db.Table("t_shipper_income").Select("shipper_id,date,template_id,concat(shipper_id,'_',date,'_',template_id) as group_key").Where(" date >= ? and type=1 and  shipper_id = 12820","2024-03-01").Group("group_key").Debug().Scan(&errorItems)
	db.Table("t_shipper_income").Select("shipper_id,date,template_id,concat(shipper_id,'_',date,'_',template_id) as group_key").Where(" date >= ? and date < ? and type=1 and order_type=2","2024-01-01","2024-03-10").Group("group_key").Scan(&errorItems)
	// db.Table("t_shipper_income").Select("shipper_id,date,template_id,concat(shipper_id,'_',date,'_',template_id) as group_key").Where("shipper_id = 11620 and date >= ? and date < ? and type=1 and order_type=2","2024-03-01","2024-03-05").Group("group_key").Debug().Scan(&errorItems)
	tools.Logger.Info("总数量："+tools.ToString(len(errorItems)))
	for index, v := range errorItems {
		day :=carbon.Parse(v.Date,configs.AsiaShanghai).AddDays(+1).Format("Y-m-d")
		tools.Logger.Info("第："+tools.ToString(index+1)+"次,配送员:"+tools.ToString(v.ShipperId)+",处理日期:"+day)
		// tools.Logger.Info("开始 "+day+" 的 订单基本信息的 归档")
		// tools.Logger.Info("开始 "+day+" 的 订单基本信息的 归档")
		incomeArchiveService.ArchiveIncomeByDay(day,v.ShipperId,v.TemplateId,0)
		// tools.Logger.Info("结束 "+day+" 的 订单基本信息的 归档")
		// tools.Logger.Info("开始 "+day+" 的 评论 归档")
		// incomeArchiveService.ArchiveIncomeByComment(day,v.ShipperId,v.TemplateId)
		// tools.Logger.Info("结束 "+day+" 的 评论 归档")
		// tools.Logger.Info("开始 "+day+" 的 总结的 归档")
		incomeArchiveService.ArchiveIncomeAll(day,v.ShipperId,v.TemplateId)
		// tools.Logger.Info("结束 "+day+" 的 总结的 归档")
	}
	tools.Logger.Info("修复完成~~")
	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "配送员特价活动设置配送员时，但订单更新配送收入时没有记录金额数据更新完成",
	})
}

// 修复 按订单数量 超过某个数量的部分 计算工资的模板数据
func (d ShipperIncomeCalculate) FixSalaryOrderCountByOverLimit(c *gin.Context) {


	type DailySaleDataCorrectionRequest struct {
		StartMonth string `form:"start_month" binding:"required"`
	}
	var request DailySaleDataCorrectionRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}
	//收到影响的配送员
	type Shippers struct {
		ShipperId int `gorm:"column:shipper_id"`
		TemplateId int `gorm:"column:template_id"`
	}
	incomeArchiveService := shipperService.NewIncomeArchiveService()

	dateStart := carbon.Parse(carbon.ParseByFormat(request.StartMonth, "Y-m").Format("Y-m-d"))
	dateEnd := carbon.Parse(carbon.ParseByFormat(request.StartMonth, "Y-m").AddMonth().Format("Y-m-d"))

	var errorItems []Shippers
	db := tools.GetDB()
	db.Table("t_shipper_income").
		Select("shipper_id,template_id").
		Where("template_id in ( select id from t_shipper_income_template where rule_type = 4 and rule_order_count_type =2 and deleted_at is null )").
		Where("date between ? and ?",dateStart,dateEnd).
		Group("shipper_id").
		Scan(&errorItems)
	days := tools.GetDaysOfMonth(dateStart.Format("Y-m-d"))	
	for _, v := range errorItems {

		for _, date := range days {
			
			day := carbon.Parse(date, configs.AsiaShanghai).AddDays(+1).Format("Y-m-d")
			tools.Logger.Info("开始 日期:" + date + " shipper_id:",v.ShipperId," template_id:", v.TemplateId," 订单基本信息的 归档")
			tools.Logger.Info("开始 " + date + " 的 订单基本信息的 归档")
			incomeArchiveService.ArchiveIncomeByDay(day, v.ShipperId, v.TemplateId,0)
			tools.Logger.Info("结束 日期:" + date + " shipper_id:",v.ShipperId," template_id:", v.TemplateId,"  订单基本信息的 归档")
			tools.Logger.Info("开始 日期:" + date + " shipper_id:",v.ShipperId," template_id:", v.TemplateId,"  评论 归档")
			incomeArchiveService.ArchiveIncomeByComment(day, v.ShipperId, v.TemplateId)
			tools.Logger.Info("结束 日期:" + date + " shipper_id:",v.ShipperId," template_id:", v.TemplateId,"  评论 归档")
			tools.Logger.Info("开始 日期:" + date + " shipper_id:",v.ShipperId," template_id:", v.TemplateId,"  总结的 归档")
			incomeArchiveService.ArchiveIncomeAll(day, v.ShipperId, v.TemplateId)
			tools.Logger.Info("结束 日期:" + date + " shipper_id:",v.ShipperId," template_id:", v.TemplateId,"  总结的 归档")
		}		
	}
	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "配送员2023-12-31日数据更改写入成功",
	})

}




// 按月计算配送员收入
func (d ShipperIncomeCalculate) IncomeArchiveByDay(c *gin.Context) {

	type DailySaleDataCorrectionRequest struct {
		
		Date string `form:"date"`
	}
	var request DailySaleDataCorrectionRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}
	
	incomeArchiveService :=shipperService.NewIncomeArchiveService()
	
	if  len(request.Date) > 0{ //某个区域的 某天的数据
		day :=carbon.Parse(request.Date,configs.AsiaShanghai).AddDays(+1).Format("Y-m-d")
		tools.Logger.Info("开始 "+request.Date+" 的 订单基本信息的 归档")
		incomeArchiveService.ArchiveIncomeByDay(day,0,0,0)
		tools.Logger.Info("结束  "+request.Date+" 的 订单基本信息的 归档")
		// tools.Logger.Info("开始  "+request.Date+" 的 评论 归档")
		// incomeArchiveService.ArchiveIncomeByComment(day,0,0)
		// tools.Logger.Info("结束  "+request.Date+" 的 评论 归档")
		tools.Logger.Info("开始  "+request.Date+" 的 总结的 归档")
		incomeArchiveService.ArchiveIncomeAll(day,0,0)
		tools.Logger.Info("结束  "+request.Date+" 的 总结的 归档")
	}

	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "配送员收入写入成功",
	})
}