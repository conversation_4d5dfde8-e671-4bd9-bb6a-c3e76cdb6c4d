package cmd

import (
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/tools"
)

type DeleteShipperPushOldData struct {
	controllers.BaseController
}

// DeleteOldData
//
//	@Description: 删除历史推送记录
//	@author: Alim<PERSON>
//	@Time: 2023-08-26 13:20:15
//	@receiver d DeleteVoiceNotifyOldData
//	@param context *gin.Context
func (d DeleteShipperPushOldData) DeleteOldData(context *gin.Context) {
	date := context.DefaultQuery("date", tools.Yesterday(currentTimeZoneName).SubDays(1).ToDateString())
	tools.Logger.Info("正在删除", date, "之前的数据")
	count := 0
	for {
		db := tools.Db
		var pushBase []models.ShipperOrderPushBase
		db.Model(&models.ShipperOrderPushBase{}).Where("created_at <?", carbon.Parse(date).AddDay().ToDateTimeString()).Limit(100).Scan(&pushBase)
		count += len(pushBase)
		if len(pushBase) == 0 {
			break
		}
		db.Delete(&pushBase)
		//println(len(pushBase))
		//break
	}
	tools.Logger.Info("删除", date, "之前的数据已完成,共删除", count, "条数据")
	count = 0
	for {
		db := tools.Db
		var pushDetail []models.ShipperOrderPushDetail
		db.Model(&models.ShipperOrderPushDetail{}).Where("created_at <?", carbon.Parse(date).AddDay().ToDateTimeString()).Limit(100).Scan(&pushDetail)
		count += len(pushDetail)
		if len(pushDetail) == 0 {
			break
		}
		db.Delete(&pushDetail)
		//println(len(pushBase))
		//break
	}
	tools.Logger.Info("删除", date, "之前的数据已完成,共删除", count, "条数据")
	context.JSON(200, gin.H{
		"code": 200,
		"msg":  "success",
	})
}
