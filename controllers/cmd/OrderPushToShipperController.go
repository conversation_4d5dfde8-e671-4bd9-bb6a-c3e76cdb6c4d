package cmd

import (
	// "context"
	// "fmt"
	 
	"mulazim-api/jobs"

	// "mulazim-api/jobs"
	// "mulazim-api/models"
	"mulazim-api/services"
	"mulazim-api/tools"
	"time"

	
)

func OrderPushToShipper1() {
	//每分钟扫描一次 如果已经接单 但是没有配送员 分配的或接单的话给配送员推送 
	logPrefix :="order_push_shipper_"
	defer func() {
		if err := recover(); err != nil {
			tools.Logger.Error(logPrefix+"shipper_job1_error-------", err)
		}
	}()
	
	var p services.BaseService
	

	// sendDuration :=60*time.Second
	
	for {
		//监测 待 接单订单
		tools.Logger.Info(logPrefix,"-扫描所有的订单-给配送员推送-开始新一轮")

		storeOrderUsers :=p.GetShipperUsersByOrder()
		
		for _, storeUser := range storeOrderUsers {
			jobs.SendNewOrderPush(storeUser.OrderId,storeUser.UserId)
		}
		
		tools.Logger.Info(logPrefix,"-扫描所有的订单-结束新一轮")
		time.Sleep(60 * time.Second)
		
	}

}
