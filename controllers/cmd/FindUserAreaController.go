package cmd

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/tools"
	"sync"
)

type FindUserAreaController struct {
	controllers.BaseController
}
type User struct {
	ID                   int       `gorm:"column:id;primary_key;AUTO_INCREMENT"`     // 自增编号
	CityID               int       `gorm:"column:user_city_id"`                           // 下单城市编号
	AreaID               int       `gorm:"column:user_area_id"`                           // 区域编号
	Mobile               string       `gorm:"column:mobile"`                           // 区域编号
}
//
// FindUserArea
//  @Description: 填充t_user表的user_city_id,user_area_id
//  @author: Alimjan
//  @Time: 2023-07-27 17:59:08
//  @receiver f FindUserAreaController
//  @param c *gin.Context
//
func (f FindUserAreaController) FindUserArea(c *gin.Context) {
	maxRoutineCountStr,_ := c.GetQuery("max_routine")
	maxRoutineCount := tools.ToInt(maxRoutineCountStr)
	if maxRoutineCount ==0{
		maxRoutineCount = 4
	}
	var wg sync.WaitGroup
	sem := make(chan struct{}, maxRoutineCount)

	db :=tools.Db
	var users []User
	db.Model(&models.User{}).Select("id,mobile,user_city_id,user_area_id").Where("user_city_id is null").Scan(&users)
	i := 0
	for _,user := range users {
		i++
		tools.Logger.Infof("--------- 当前处理 %d",i)
		wg.Add(1)
		// ScanRows 方法用于将一行记录扫描至结构体
		sem <- struct{}{}
		go func() {
			updateUser(&wg,user,sem)
		}()
	}
	tools.Logger.Infof("--------- 等待信号")
	wg.Wait()
	tools.Logger.Infof("--------- 结束了")

	if true {
		f.Success(c, gin.H{

		}, "success", 200)
	}
}
func updateUser(wg *sync.WaitGroup,user User, sem chan struct{}){

	defer func() { <-sem }()
	db := tools.Db
	var order models.Order
	db.Model(&models.Order{}).Where("user_id = ?", user.ID).Order("id desc").Scan(&order)
	if order.ID!= 0 {
		db.Table("t_user").Model(&user).Select("user_city_id", "user_area_id").Updates(User{
			CityID: order.CityID,
			AreaID: order.AreaID,
		})
	}else{
		db.Model(&models.Order{}).Where("mobile = ?", user.Mobile).Order("id desc").Scan(&order)
		if order.ID!= 0 {
			db.Table("t_user").Model(&user).Select("user_city_id", "user_area_id").Updates(User{
				CityID: order.CityID,
				AreaID: order.AreaID,
			})
		}else{
			//tools.Logger.Infof("这个用户没有下单，找不到 %s",user.Mobile)
		}
	}
	wg.Done()
}