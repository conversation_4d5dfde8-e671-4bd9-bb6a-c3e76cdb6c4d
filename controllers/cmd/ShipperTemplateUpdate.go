package cmd

import (
	"mulazim-api/controllers"
	"mulazim-api/models"
	shipmentModels "mulazim-api/models/shipment"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type ShipperTemplateUpdate struct {
	controllers.BaseController
}

// UpdateShipperTemplates
//
// @Description: 配送员工资模板更新
// @Author: Rixat
// @Time: 2024-02-07 13:26:46
// @receiver 
// @param c *gin.Context
func (s ShipperTemplateUpdate) UpdateShipperTemplates(c *gin.Context) {
	var updateTemplates []shipmentModels.AdminTemplateUpdate
	tools.Db.Model(updateTemplates).Scan(&updateTemplates)
	var failShipperIds []int
	var successShipperIds []int
	for _, v := range updateTemplates {
		err := tools.Db.Model(models.Admin{}).Where("id", v.ShipperID).UpdateColumn("shipper_income_template_id", v.TemplateID).Error
		if err != nil {
			tools.Logger.Errorf("配送员[%d]模板[%d]跟新失败", v.ShipperID, v.TemplateID)
			failShipperIds = append(failShipperIds, v.ShipperID)
		} else {
			successShipperIds = append(successShipperIds, v.ShipperID)
		}
	}
	tools.Db.Model(updateTemplates).Delete(&updateTemplates)
	if len(failShipperIds) > 0 {
		tools.Logger.Error("FATAL 配送员模板跟新失败：", failShipperIds)
	}
	tools.Logger.Info("配送员模板跟新成功：", successShipperIds)
	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "配送员新模板更新成功",
	})

}
