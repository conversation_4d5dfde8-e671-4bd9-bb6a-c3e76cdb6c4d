﻿package cmd

import (
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)
type InviteUserController struct {
	controllers.BaseController
}

// 更新订单表中的配送员拓展用户分给的金额
//
// @Description: 
// @Author: Rixat
// @Time: 2024-04-07 18:14:53
// @receiver 
// @param c *gin.Context
func (s InviteUserController) UpdateOrderShipperReward(c *gin.Context) {
   
    var monthStatistic []models.AdvertMaterialShipperUserMonthStatistic
	tools.Db.Model(monthStatistic).Where("total_order_tips_fee>0").Preload("AdvertMaterialShipperUser").Find(&monthStatistic)
    tools.Logger.Info("开始更新配送员订单中拓展用户金额：",len(monthStatistic));

    updateType := c.Query("type")
    // 更新今日订单
    if(updateType=="today"){
        orderTodayCountSuccess := 0;
        orderTodayCountFail := 0;
        for _, item := range monthStatistic {
            tools.Logger.Info("更新今日订单，月份:",item.Month);
            userAddTime := item.AdvertMaterialShipperUser.CreatedAt
            orderPercent := tools.ToFloat64(item.TotalOrderTipsFee) / tools.ToFloat64(item.TotalOrderPrice)
            startDay,endDay  := tools.GetMonthStartAndEndWithParam(carbon.Parse(item.Month).Format("Y-m-d"), "Y-m-d")
            startDayCarbon,endDayCarbon := carbon.Parse(startDay+" 00:00:00").AddDay().Format("Y-m-d H:i:s"),carbon.Parse(endDay+"  23:59:59").AddDay().Format("Y-m-d H:i:s")
            var todayOrders []models.OrderToday
            tools.Db.Model(&models.OrderToday{}).Where("created_at between ? and ?", startDayCarbon,endDayCarbon).Where("user_id=? and state = 7", item.UserId).Where("created_at>?",userAddTime).Find(&todayOrders)
            tools.Logger.Info("更新今日订单数量:",len(todayOrders));
            for _,order := range todayOrders {
                shipperReward := tools.ToInt(tools.ToFloat64(order.OrderPrice) * orderPercent)
                err := tools.Db.Model(&order).Update("shipper_reward", shipperReward).Error
                if err!= nil {
                    tools.Logger.Info("更新失败，订单号:",order.ID);
                    orderTodayCountFail = orderTodayCountFail+1
                }else{
                    orderTodayCountSuccess = orderTodayCountSuccess+1
                }
            }
        }
        tools.Logger.Info("更新今日订单完成[成功,失败]:",orderTodayCountSuccess,orderTodayCountFail);
    }
    // 更新历史订单
    if(updateType=="history"){
        orderCountSuccess := 0;
        orderCountFail := 0;
        for _, item := range monthStatistic {
            tools.Logger.Info("更新历史订单，月份:",item.Month);
            userAddTime := item.AdvertMaterialShipperUser.CreatedAt
            orderPercent := tools.ToFloat64(item.TotalOrderTipsFee) / tools.ToFloat64(item.TotalOrderPrice)
            startDay,endDay  := tools.GetMonthStartAndEndWithParam(carbon.Parse(item.Month).Format("Y-m-d"), "Y-m-d")
            startDayCarbon,endDayCarbon := carbon.Parse(startDay+" 00:00:00").AddDay().Format("Y-m-d H:i:s"),carbon.Parse(endDay+"  23:59:59").AddDay().Format("Y-m-d H:i:s")
            var orders []models.Order
            tools.Db.Model(&models.Order{}).Where("archive_date between ? and ?", startDayCarbon,endDayCarbon).Where("user_id=? and state = 7", item.UserId).Where("archive_date>?",userAddTime).Find(&orders)
            tools.Logger.Info("更新今日订单数量:",len(orders));
            for _,order := range orders {
                shipperReward := tools.ToInt(tools.ToFloat64(order.OrderPrice) * orderPercent)
                err := tools.Db.Model(&order).Update("shipper_reward", shipperReward).Error
                if err!= nil {
                    tools.Logger.Info("更新失败，订单号:",order.ID);
                    orderCountFail = orderCountFail+1
                }else{
                    orderCountSuccess = orderCountSuccess+1
                }
            }
        }
        tools.Logger.Info("更新历史订单完成[成功,失败]:",orderCountSuccess,orderCountFail);
    }

    s.Ok(c)
}
