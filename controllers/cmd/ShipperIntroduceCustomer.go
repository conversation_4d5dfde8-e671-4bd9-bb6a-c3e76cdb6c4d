package cmd

import (
	// "fmt"

	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/models"

	// "github.com/golang-module/carbon/v2"
	"mulazim-api/configs"
	"mulazim-api/controllers"

	"mulazim-api/tools"
	// "sort"
)

type ShipperIntroduceCustomer struct {
	controllers.BaseController
}

func (s ShipperIntroduceCustomer) dailyStatisticMinute(shipperID int64,adminAreaId int) {
	if shipperID == 0 {
		return
	}
	tools.Logger.Infof("开始计算 配送员拓展用户 每日数据")
	db :=tools.GetDB()
	now :=carbon.Now(configs.AsiaShanghai)
	from :=now.Format("Y-m-d")
	end :=now.AddDays(1).Format("Y-m-d")
	sql :=`
		with invite_user_count_table (advert_material_id,invite_user_count) as (

		SELECT
				temp.advert_material_id,count( 1 ) invite_user_count
			FROM
				t_advert_material_shipper_user temp 
			WHERE
				temp.shipper_id = ?
				AND temp.state = 1 
				AND temp.created_at BETWEEN ? 
				AND ? 
				GROUP BY temp.advert_material_id
		),
		invite_user_fee_table (advert_material_id,invite_user_fee) as (
				SELECT
					advert_material_id,sum( temp.reward ) as invite_user_fee
				FROM
					t_advert_material_shipper_user temp 
				WHERE
					temp.state = 1 
					AND temp.shipper_id = ?
					AND temp.created_at BETWEEN ? 
					AND ? 
					group by  temp.advert_material_id
		
		),
		qr_invite_count_table (advert_material_id,qr_invite_count,material_invite_count) as (
				SELECT
					advert_material_id,
					sum( if(temp.advert_material_category_id = 1,1,0 )) as qr_invite_count,
					sum( if(temp.advert_material_category_id != 1,1,0 )) as material_invite_count
				FROM
					t_advert_material_shipper_user temp 
				WHERE
					temp.state = 1 
					AND temp.shipper_id = ?
					AND temp.created_at BETWEEN ?
					AND ? 
					group by  temp.advert_material_id
		
		),
		order_count_table (advert_material_id,order_count,total_order_price,total_order_tips_fee) as (
			SELECT
			advert_material_id,
				sum((
					SELECT
						count( 1 ) 
					FROM
						t_order_today 
					WHERE
						created_at BETWEEN ? AND ?
						AND t_order_today.user_id = temp.user_id 
						AND t_order_today.area_id = ?
						AND state = 7 
						AND temp.created_at <= t_order_today.created_at 
					))  order_count,
				sum((
					SELECT
						sum( price + shipment + lunch_box_fee ) 
					FROM
						t_order_today 
					WHERE
						created_at BETWEEN ? AND ? 
						AND t_order_today.user_id = temp.user_id 
						AND t_order_today.area_id = ?
						AND state = 7 
						AND temp.created_at <= t_order_today.created_at 
					))  total_order_price,
				sum((
					SELECT
					sum( shipper_reward ) 
						
					FROM
						t_order_today 
					WHERE
						created_at BETWEEN ? AND ? 
						AND t_order_today.user_id = temp.user_id 
						AND t_order_today.area_id = ?
						AND state = 7 
						AND temp.created_at <= t_order_today.created_at 
					))  total_order_tips_fee
					
					
			FROM
				t_advert_material_shipper_user AS temp 
			WHERE
				temp.shipper_id = ?
				AND temp.state = 1 
				group by  temp.advert_material_id
		)
		SELECT
			? AS date,
			tamsu.advert_material_category_id,
			tamsu.advert_material_id,
			a.admin_city_id AS city_id,
			a.admin_area_id AS area_id,
			tamsu.shipper_id,
			IFNULL(invite_user_count_table.invite_user_count,0) invite_user_count,
			IFNULL(invite_user_fee_table.invite_user_fee,0) invite_user_fee,
			IFNULL(qr_invite_count_table.qr_invite_count,0) qr_invite_count,
			IFNULL(qr_invite_count_table.material_invite_count,0) material_invite_count,
			IFNULL(order_count_table.order_count,0) order_count,
				IFNULL(order_count_table.total_order_price,0) total_order_price,
				IFNULL(order_count_table.total_order_tips_fee,0) total_order_tips_fee,
			now() AS created_at,
			now() AS updated_at 
		FROM
			t_advert_material_shipper_user AS tamsu
			LEFT JOIN t_admin AS a ON a.id = tamsu.shipper_id
			LEFT JOIN t_shipper_income_template AS sit ON sit.id = a.shipper_income_template_id 
			LEFT JOIN invite_user_count_table on invite_user_count_table.advert_material_id  = tamsu.advert_material_id 
			LEFT JOIN invite_user_fee_table on invite_user_fee_table.advert_material_id  = tamsu.advert_material_id 
			LEFT JOIN qr_invite_count_table on qr_invite_count_table.advert_material_id  = tamsu.advert_material_id 
			LEFT JOIN order_count_table on order_count_table.advert_material_id  = tamsu.advert_material_id 
		WHERE
			tamsu.state = 1 
			AND tamsu.shipper_id = ? 
		GROUP BY
			tamsu.shipper_id,
			tamsu.advert_material_id
	`
	dbRead1 := tools.ReadDb1
	
	var results []map[string]interface{}
	if err := dbRead1.Raw(sql,
		shipperID,
		from,
		end,

		shipperID,
		from,
		end,

		shipperID,
		from,
		end,

		from,
		end,
		adminAreaId,

		from,
		end,
		adminAreaId,

		from,
		end,
		adminAreaId,

		shipperID,

		from,

		shipperID,

		).Debug().Scan(&results).Error; err != nil {
		tools.Logger.Errorf("计算 配送员拓展用户 每日数据 失败 %s", err.Error())
		panic(err)
	}
	// INSERT操作，在db中写入
	for _, result := range results {
		insertSQL := `
            INSERT INTO t_advert_material_shipper_daily_statistic (
                date, advert_material_category_id, advert_material_id, city_id, area_id, shipper_id,
                invite_user_count, invite_user_fee, qr_invite_count, material_invite_count,
                order_count, total_order_price, total_order_tips_fee, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
            ON DUPLICATE KEY UPDATE
                invite_user_count = VALUES(invite_user_count),
                invite_user_fee = VALUES(invite_user_fee),
                qr_invite_count = VALUES(qr_invite_count),
                material_invite_count = VALUES(material_invite_count),
                order_count = VALUES(order_count),
                total_order_price = VALUES(total_order_price),
                total_order_tips_fee = VALUES(total_order_tips_fee),
                updated_at = NOW()
        `
		if err := db.Exec(insertSQL,
			result["date"],
			result["advert_material_category_id"],
			result["advert_material_id"],
			result["city_id"],
			result["area_id"],
			result["shipper_id"],
			result["invite_user_count"],
			result["invite_user_fee"],
			result["qr_invite_count"],
			result["material_invite_count"],
			result["order_count"],
			result["total_order_price"],
			result["total_order_tips_fee"],
		).Error; err != nil {
			tools.Logger.Errorf("计算 配送员拓展用户 每日数据 失败 %s", err.Error())
			panic(err)
		}
	}

	tools.Logger.Infof("结束计算 配送员拓展用户 每日数据")
}
//每5分钟运行一次 计算配送员 拓展用户 数据
func (s ShipperIntroduceCustomer) DailyStatisticMinute(c *gin.Context) {
	s.dailyStatisticMinute(0,0)
	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "配送员拓展用户 每日数据 写入成功",
	})
}


//每天运行一次 计算配送员 拓展用户 数据 订单归档后的数据
func (s ShipperIntroduceCustomer) DailyStatisticDay(c *gin.Context) {
	tools.Logger.Infof("开始计算 配送员拓展用户 每日数据 订单归档后的数据")
	
	startDay :=c.Query("start_day")
	startMonth :=c.Query("start_month")
	if startMonth != "" { //按月运行

		month := carbon.Parse(startMonth, configs.AsiaShanghai).Format("Y-m")
		monthStart := carbon.Parse(carbon.ParseByFormat(month, "Y-m").Format("Y-m-d"))
		monthEnd := carbon.Parse(carbon.ParseByFormat(month, "Y-m").AddMonth().Format("Y-m-d"))

		monthLength := monthEnd.DiffAbsInDays(monthStart)
		for i := int(monthLength); i > -1; i-- { //某给月份的数据全部归档
			day := monthEnd.AddDays(-i).Format("Y-m-d")
			s.DailyStatisticDailyRun(day)	
		}

	}else{
		s.DailyStatisticDailyRun(startDay)
	}

	
	tools.Logger.Infof("结束计算 配送员拓展用户 每日数据 订单归档后的数据")
	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "送员拓展用户 每日数据 订单归档后的数据 写入成功",
	})
}

func (s ShipperIntroduceCustomer) monthStatistic(startMonth string,shipperID int64) {
	tools.Logger.Infof("开始计算 配送员拓展用户 每个月数据 订单归档后的数据")
	db :=tools.GetDB()
	now :=carbon.Now(configs.AsiaShanghai)
	start :=carbon.Parse(now.Format("Y-m-01"))


	dbRead1 := tools.ReadDb1

	if startMonth != "" {
		start = carbon.Parse(startMonth, configs.AsiaShanghai)
	}

	from := start.Format("Y-m-d")
	end := start.AddMonth().Format("Y-m-d")
	archivedFrom := start.AddDay().Format("Y-m-d")
	archivedEnd := start.AddMonth().AddDay().Format("Y-m-d")

	// 大的SELECT查询，从dbRead1获取数据
	sql :=``
	sql +=" select shipper_id,user_id, advert_material_shipper_user_id, advert_material_category_id, month, city_id, area_id, invite_user_fee, today_order_count + history_order_count as order_count, "

	sql +=" today_order_price+ history_order_price as total_order_price, (today_shipper_reward+ history_shipper_reward) as total_order_tips_fee,"
	sql +=" now() as created_at,now() as updated_at "
	sql +=" from (select tamsu.shipper_id,tamsu.user_id,tamsu.id as advert_material_shipper_user_id,tamsu.advert_material_category_id  , "

	sql +=" '"+from+"' as month,a.admin_city_id as city_id, a.admin_area_id as area_id,if(tamsu.created_at BETWEEN '"+from+"' and '"+end+"',tamsu.reward, 0) AS invite_user_fee,"
	sql +=" (select count(1) from t_order_today where created_at between '"+from+"' and '"+end+"' and state = 7 and t_order_today.area_id = a.admin_area_id and t_order_today.user_id = tamsu.user_id and t_order_today.created_at > tamsu.created_at) as today_order_count, "
	sql +=" (select count(1) from t_order where archive_date between '"+archivedFrom+"' and '"+archivedEnd+"' and state = 7 and t_order.area_id = a.admin_area_id and t_order.user_id = tamsu.user_id and t_order.created_at > tamsu.created_at) as history_order_count, "
	sql +=" ifnull((select sum(shipment+lunch_box_fee+price) from t_order_today where created_at between '"+from+"' and '"+end+"' and t_order_today.created_at > tamsu.created_at and state = 7 and t_order_today.area_id = a.admin_area_id and t_order_today.user_id = tamsu.user_id) ,0) as today_order_price, "
	sql +=" ifnull((select sum(shipment+lunch_box_fee+price) from t_order where archive_date between '"+archivedFrom+"' and '"+archivedEnd+"' and t_order.created_at > tamsu.created_at and state = 7 and t_order.area_id = a.admin_area_id and t_order.user_id = tamsu.user_id ),0) as history_order_price, "

	sql +=" ifnull((select sum(shipper_reward) from t_order_today where created_at between '"+from+"' and '"+end+"' and t_order_today.created_at > tamsu.created_at and state = 7 and t_order_today.area_id = a.admin_area_id and t_order_today.user_id = tamsu.user_id) ,0) as today_shipper_reward,"
	sql +=" ifnull((select sum(shipper_reward) from t_order where archive_date between '"+archivedFrom+"' and '"+archivedEnd+"' and t_order.created_at > tamsu.created_at and state = 7 and t_order.area_id = a.admin_area_id and t_order.user_id = tamsu.user_id ),0) as history_shipper_reward "


	sql +="  from t_advert_material_shipper_user as tamsu "
	sql +="  left join t_admin a on a.id = tamsu.shipper_id "

	sql +=" where tamsu.state = 1 "
	if shipperID > 0 {
		sql += " and tamsu.shipper_id = " + fmt.Sprintf("%d ",shipperID)
	}
	sql +=" and a.state = 1 group by tamsu.shipper_id,tamsu.user_id) as Z "

	var results []map[string]interface{}
	if err := dbRead1.Raw(sql).Debug().Scan(&results).Error; err != nil {
		tools.Logger.Errorf("计算 配送员拓展用户 每个月数据 订单归档后的数据 失败 %s", err.Error())
		panic(err)
	}

	// INSERT操作，在db中写入
	for _, result := range results {
		insertSQL := `
            INSERT INTO t_advert_material_shipper_user_month_statistic (
                shipper_id, user_id, advert_material_shipper_user_id, advert_material_category_id, month,
                city_id, area_id, invite_user_fee, order_count, total_order_price, total_order_tips_fee,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ON DUPLICATE KEY UPDATE
                invite_user_fee = VALUES(invite_user_fee),
                order_count = VALUES(order_count),
                total_order_price = VALUES(total_order_price),
                total_order_tips_fee = VALUES(total_order_tips_fee),
                updated_at = NOW()
        `
		if err := db.Exec(insertSQL,
			result["shipper_id"],
			result["user_id"],
			result["advert_material_shipper_user_id"],
			result["advert_material_category_id"],
			result["month"],
			result["city_id"],
			result["area_id"],
			result["invite_user_fee"],
			tools.ToInt64(result["order_count"]),
			tools.ToFloat64(result["total_order_price"]),
			tools.ToFloat64(result["total_order_tips_fee"]),
		).Error; err != nil {
			tools.Logger.Errorf("计算 配送员拓展用户 每个月数据 订单归档后的数据 失败 %s", err.Error())
			panic(err)
		}
	}

	tools.Logger.Infof("结束计算 配送员拓展用户 每个月数据 订单归档后的数据")
}
//每个月运行一次 计算配送员 拓展用户 数据 订单归档后的数据
func (s ShipperIntroduceCustomer) MonthStatistic(c *gin.Context) {
	startMonth :=c.Query("start_month")
	s.monthStatistic(startMonth,0)
	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "配送员拓展用户 每月数据 订单归档后的数据 写入成功",
	})
}

func (s ShipperIntroduceCustomer) DailyStatisticDailyRun(startDay string) {

	db :=tools.GetDB()

	now :=carbon.Now(configs.AsiaShanghai)
	
	if (startDay !=""){
		now = carbon.Parse(startDay, configs.AsiaShanghai)
	}


	archiveStart :=now.Format("Y-m-d")
	archiveEnd :=now.AddDays(1).Format("Y-m-d")
	

	from :=now.AddDays(-1).Format("Y-m-d")
	end :=now.Format("Y-m-d")

	
	sql :=``
	sql +=" insert into t_advert_material_shipper_daily_statistic "
	sql +=" (date,advert_material_category_id,advert_material_id,city_id,area_id,shipper_id,invite_user_count,invite_user_fee,qr_invite_count,material_invite_count,order_count,total_order_price,total_order_tips_fee,created_at, updated_at) "
	
	// sql +=" select '"+from+"' as date,tamsu.advert_material_category_id, tamsu.advert_material_id, a.admin_city_id as city_id, a.admin_area_id as area_id, tamsu.shipper_id, "
	// sql +=" (select count(1) from t_advert_material_shipper_user temp where temp.shipper_id = tamsu.shipper_id and temp.state=1 and temp.created_at between '"+from+"' and '"+end+"') as invite_user_count, "
	
	// sql +=" ifnull((select sum(reward) from t_advert_material_shipper_user temp where temp.state=1 and temp.shipper_id = tamsu.shipper_id and temp.created_at between '"+from+"' and '"+end+"' and temp.advert_material_id = tamsu.advert_material_id ),0) as invite_user_fee, "
	// sql +=" (select count(1) from t_advert_material_shipper_user temp where temp.shipper_id = tamsu.shipper_id  and temp.state=1 and temp.created_at between '"+from+"' and '"+end+"' and temp.advert_material_category_id = 1 and temp.advert_material_id = tamsu.advert_material_id ) as qr_invite_count, "
	// sql +=" (select count(1) from t_advert_material_shipper_user temp where temp.state=1 and temp.shipper_id = tamsu.shipper_id and temp.created_at between '"+from+"' and '"+end+"' and temp.advert_material_category_id != 1 and temp.advert_material_id = tamsu.advert_material_id ) as material_invite_count, "
	// sql +=" (select sum(( select count(1) from t_order where archive_date between '"+archiveStart+"' and '"+archiveEnd+"' and t_order.user_id = temp.user_id and t_order.area_id = a.admin_area_id and state = 7 and temp.created_at <= t_order.created_at)) from t_advert_material_shipper_user as temp where temp.shipper_id = tamsu.shipper_id and temp.advert_material_id = tamsu.advert_material_id and temp.state = 1) as order_count, "
	// sql +=" ifnull((select sum(( select sum(price+shipment+lunch_box_fee) from t_order where archive_date between '"+archiveStart+"' and '"+archiveEnd+"' and t_order.user_id = temp.user_id and t_order.area_id = a.admin_area_id and state = 7 and temp.created_at <= t_order.created_at))  from t_advert_material_shipper_user as temp where temp.shipper_id = tamsu.shipper_id and temp.advert_material_id = tamsu.advert_material_id and temp.state = 1),0) as total_order_price, "
	
	// sql +=" ifnull((select sum(( select sum(shipper_reward) from t_order where archive_date between '"+archiveStart+"' and '"+archiveEnd+"' and t_order.user_id = temp.user_id and t_order.area_id = a.admin_area_id and state = 7 and temp.created_at <= t_order.created_at)) from t_advert_material_shipper_user as temp where temp.shipper_id = tamsu.shipper_id and temp.advert_material_id = tamsu.advert_material_id and temp.state = 1),0) as total_order_tips_fee, "
	// sql +=" now() as created_at, "
	// sql +=" now() as updated_at "
	// sql +=" from t_advert_material_shipper_user as tamsu "
	// sql +=" left join t_admin as a on a.id = tamsu.shipper_id "
	
	// sql +=" where tamsu.state = 1 "
	// sql +=" group by tamsu.shipper_id, tamsu.advert_material_id "
	sql +=" WITH invite_data AS ( "
    sql +=" SELECT shipper_id, advert_material_id, COUNT(1) AS invite_user_count,"
    sql +=" SUM(CASE WHEN advert_material_category_id = 1 THEN 1 ELSE 0 END) AS qr_invite_count,"
    sql +=" SUM(CASE WHEN advert_material_category_id != 1 THEN 1 ELSE 0 END) AS material_invite_count,"
    sql +=" SUM(reward) AS invite_user_fee"
    sql +=" FROM t_advert_material_shipper_user" 
    sql +=" WHERE state = 1 AND created_at BETWEEN '"+from+"' AND '"+end+"' "
    sql +=" GROUP BY shipper_id, advert_material_id ),"
	sql +=" order_data AS ( "
    sql +=" SELECT "
    sql +=" temp.shipper_id,"
	sql +=" temp.advert_material_id,"
	sql +=" COUNT(DISTINCT t_order.user_id) AS order_count,"
	sql +=" SUM(price + shipment + lunch_box_fee) AS total_order_price,"
	sql +=" SUM(shipper_reward) AS total_order_tips_fee"
    sql +=" FROM t_advert_material_shipper_user AS temp "
    sql +=" JOIN " 
	sql +=" t_order ON t_order.user_id = temp.user_id "
	sql +=" AND t_order.archive_date BETWEEN '"+archiveStart+"' AND '"+archiveEnd+"' "
	sql +=" AND t_order.area_id = (SELECT admin_area_id FROM t_admin WHERE id = temp.shipper_id) "
	sql +=" AND t_order.state = 7 "
	sql +=" AND temp.created_at <= t_order.created_at "
    sql +=" WHERE "
    sql +=" temp.state = 1 "
    sql +=" GROUP BY "
    sql +=" temp.shipper_id, "
    sql +=" temp.advert_material_id "
	sql +=" ) "
	sql +=" SELECT "
    sql +=" '"+from+"' AS date, "
    sql +=" tamsu.advert_material_category_id, "
    sql +=" tamsu.advert_material_id, "
    sql +=" a.admin_city_id AS city_id, "
    sql +=" a.admin_area_id AS area_id, "
    sql +=" tamsu.shipper_id, "
    sql +=" COALESCE(i.invite_user_count, 0) AS invite_user_count, "
    sql +=" COALESCE(i.invite_user_fee, 0) AS invite_user_fee, "
    sql +=" COALESCE(i.qr_invite_count, 0) AS qr_invite_count, "
    sql +=" COALESCE(i.material_invite_count, 0) AS material_invite_count, "
    sql +=" COALESCE(o.order_count, 0) AS order_count, "
    sql +=" COALESCE(o.total_order_price, 0) AS total_order_price, "
    sql +=" COALESCE(o.total_order_tips_fee, 0) AS total_order_tips_fee, "
    sql +=" NOW() AS created_at, "
    sql +=" NOW() AS updated_at "
	sql +=" FROM "
	sql +=" t_advert_material_shipper_user AS tamsu "
	sql +=" LEFT JOIN "
	sql +=" t_admin AS a ON a.id = tamsu.shipper_id "
	sql +=" LEFT JOIN "
	sql +=" invite_data AS i ON i.shipper_id = tamsu.shipper_id AND i.advert_material_id = tamsu.advert_material_id "
	sql +=" LEFT JOIN "
	sql +=" order_data AS o ON o.shipper_id = tamsu.shipper_id AND o.advert_material_id = tamsu.advert_material_id "
	sql +=" WHERE "
	sql +=" tamsu.state = 1 "
	sql +=" and tamsu.created_at < '"+end+"' "
	sql +=" GROUP BY "
    sql +=" tamsu.shipper_id,"
    sql +=" tamsu.advert_material_id "



	sql +=" ON DUPLICATE KEY UPDATE "
	sql +=" invite_user_count = values(invite_user_count), "
	sql +=" invite_user_fee = values(invite_user_fee), "
	sql +=" qr_invite_count = values(qr_invite_count), "
	sql +=" material_invite_count = values(material_invite_count), "
	sql +=" order_count = values(order_count), "
	sql +=" total_order_price = values(total_order_price), "
	sql +=" total_order_tips_fee = values(total_order_tips_fee), "
	sql +=" updated_at = now() "
	// tools.Logger.Info(sql)
	result :=db.Exec(sql)
		if result.Error != nil {
			tools.Logger.Errorf("FATAL 计算 配送员拓展用户 每日数据 订单归档后的数据 失败 %s",result.Error.Error())
			panic(result.Error)
		}
	

}
//
// ShipperIncomeUpdateByShipperID
//  @Description: 客户扫码时使用,swoole 发送请求
//  @receiver s
//  @param context
//
func (s ShipperIntroduceCustomer) ShipperIncomeUpdateByShipperID(context *gin.Context) {
   go func() {
	   shipperIDStr :=context.Query("shipper_id")
	   userIDStr :=context.Query("user_id")

	   userID := tools.ToInt64(userIDStr)
	   shipperID := tools.ToInt64(shipperIDStr)
	   adminAreaId := int64(0)
	   if userID > 0{
		   var shipperUser models.AdvertMaterialShipperUser
		   tools.Db.Model(&shipperUser).Where("state = 1 and user_id = ?",userID).Find(&shipperUser)
		   if shipperUser.ID>0 {
			   shipperID = int64(shipperUser.ShipperId)
			   tools.Db.Model(&models.Admin{}).Where("id = ?", shipperUser.ShipperId).Pluck("admin_area_id",&adminAreaId)
		   }
	   }

	   if shipperID > 0{
		   s.monthStatistic("",shipperID)
		   s.dailyStatisticMinute(shipperID,int(adminAreaId))
	   }
   }()
	context.JSON(200, gin.H{
		"code": 200,
		"msg":  "配送员拓展用户 每个月数据 订单归档后的数据 写入成功",
	})
}