package cmd

import (
	"mulazim-api/controllers"
	

	"github.com/golang-module/carbon/v2"

	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type LakalaWithdrawDataCorrection struct {
	controllers.BaseController
}

// CorrectData
//
//	@Description: 修复拉卡拉提现不记录提现编号问题
//	@author: rozimamat
//	@Time: 2023-10-07 19:09
//	@receiver d CorrectData
//	@param context *gin.Context
func (d LakalaWithdrawDataCorrection) CorrectData(c *gin.Context) {

	type LakalaWithdrawDataCorrectionRequest struct {
		StartDate string `form:"start_date" binding:"required"`
	}
	var request LakalaWithdrawDataCorrectionRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	d.UpdateData(request.StartDate, "")

	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "开始更新店铺销售数据补充的数据",
	})

}

// 更新拉卡拉提现后的不记录partner_trade_no 问题
func (d LakalaWithdrawDataCorrection) UpdateData(start_date string, end_date string) {

	db := tools.GetDB()
	type  UpdateDataStruct struct{
		Id int `gorm:"column:id"`
		OrderNo string `gorm:"column:order_no"`
		AgentId string `gorm:"column:agent_id"`
		
	}
	var dailyData []UpdateDataStruct

	end_date = carbon.Parse(start_date, "Asia/Shanghai").EndOfMonth().ToDateTimeString()

	db.Table("t_merchant_cashout_log").
		Where("t_merchant_cashout_log.created_at between ? and ? ", start_date, end_date).
		Select("t_merchant_cashout_log.id,t_lakala_withdraw.order_no ,t_mulazimpaytoresagent.id as agent_id ").
		Joins("LEFT JOIN t_lakala_withdraw_service ON t_merchant_cashout_log.withdraw_id = t_lakala_withdraw_service.id").
		Joins("LEFT JOIN t_lakala_withdraw ON t_lakala_withdraw.service_id = t_lakala_withdraw_service.id").
		Joins("LEFT JOIN t_mulazimpaytoresagent  ON t_merchant_cashout_log.withdraw_id = t_mulazimpaytoresagent.withdraw_id").
		Where("t_lakala_withdraw_service.state = ? AND length( t_merchant_cashout_log.partner_trade_no ) = ? AND t_merchant_cashout_log.cash_platform = ? AND length( t_lakala_withdraw.order_no ) > ? and t_lakala_withdraw_service.opt_type = ?",5,0,2,0,"merchant").
		Scan(&dailyData)

	tools.Logger.Info("更新拉卡拉提现后的不记录partner_trade_no 问题 数量:", len(dailyData))
	updateMerchantCashoutLog := make([]map[string]interface{}, 0) //更新 t_merchant_cashout_log
	updateMulazimpaytoresagent := make([]map[string]interface{}, 0) //更新 t_mulazimpaytoresagent
	for _, v := range dailyData {

		updateData := make(map[string]interface{})
		updateData["id"] = v.Id
		updateData["partner_trade_no"] = v.OrderNo
		updateMerchantCashoutLog = append(updateMerchantCashoutLog, updateData)

		updateData2 := make(map[string]interface{})
		updateData2["id"] = v.AgentId
		updateData2["partner_trade_no"] = v.OrderNo
		updateMulazimpaytoresagent= append(updateMulazimpaytoresagent, updateData2)
		db.Table("t_merchant_cashout_log").Where("id = ?",v.Id).Updates(&map[string]interface{}{
			"partner_trade_no":v.OrderNo,
		})

		db.Table("t_mulazimpaytoresagent").Where("id = ?",v.AgentId).Updates(&map[string]interface{}{
			"partner_trade_no":v.OrderNo,
		})

	}
	if len(updateMerchantCashoutLog) > 0 {
		tools.Logger.Info("批量更新开始")
		sql := tools.GenerateBatchUpdateSql("t_merchant_cashout_log", updateMerchantCashoutLog)
		tools.Logger.Info(sql)

		sql2 := tools.GenerateBatchUpdateSql("t_mulazimpaytoresagent", updateMulazimpaytoresagent)
		tools.Logger.Info(sql2)
		
		// db.Exec(sql)
		tools.Logger.Info("批量更新结束")

	}

}
