package cmd

import (
	"github.com/golang-module/carbon/v2"
	"mulazim-api/controllers"
	"mulazim-api/models"

	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type DailySaleDataCorrection struct {
	controllers.BaseController
}

// CorrectData
//
//	@Description: 下载微信支付统计数据
//	@author: rozimamat
//	@Time: 2023-09-21 18:10
//	@receiver d CorrectData
//	@param context *gin.Context
func (d DailySaleDataCorrection) CorrectData(c *gin.Context) {

	type DailySaleDataCorrectionRequest struct {
		StartDate string `form:"start_date" binding:"required"`
	}
	var request DailySaleDataCorrectionRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	d.UpdateData(request.StartDate, "")

	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "开始更新店铺销售数据补充的数据",
	})

}

// 更新店铺销售数据补充的数据
func (d DailySaleDataCorrection) UpdateData(start_date string, end_date string) {

	db := tools.GetDB()
	var dailyData []models.RestaurantSalesDailyData

	end_date = carbon.Parse(start_date, "Asia/Shanghai").EndOfMonth().ToDateTimeString()

	db.Table("restaurant_sales_daily_data").
		Where("date between ? and ? ", start_date, end_date).
		Select("(cash + coin + wechat + alipay + unionpay + yunshanfu -total_money_mp-total_money_self) AS incorrect_amount, (cash + coin + wechat + alipay + unionpay + yunshanfu)  as total_money,restaurant_sales_daily_data.*").
		Having("incorrect_amount >  0").Scan(&dailyData)

	tools.Logger.Info("更新店铺销售数据补充的数据 数量:", len(dailyData))
	updateDataMap := make([]map[string]interface{}, 0)
	for _, v := range dailyData {

		totalMoneyMp := v.Coin + v.Cash + v.Alipay + v.Wechat + v.Unionpay + v.Yunshanfu
		incorrectAmount := v.TotalMoney - v.TotalMoneyMp - v.TotalMoneySelf

		if incorrectAmount > 0 { //订单总额对不上的记录要更改
			updateData := make(map[string]interface{})
			orderCountMp := v.OrderCount // 配送订单数量  默认取总订单数

			if v.TotalMoneySelf > 0 && v.TotalMoneyMp == 0 { //有自取订单 但是没有记录配送订单的总金额
				totalMoneyMp = v.TotalMoney - v.TotalMoneySelf //配送的金额=订单总额-自取订单金额
				orderCountMp = orderCountMp - v.OrderCountSelf //配送订单数量=订单总数-自取订单数量
			}
			updateData["id"] = v.ID
			updateData["total_money_mp"] = totalMoneyMp
			updateData["order_count_mp"] = orderCountMp
			updateDataMap = append(updateDataMap, updateData)

		}

		// er1 :=db.Table("restaurant_sales_daily_data").Where("id = ?",v.ID).Updates(&updateMap).Error
		// if er1 !=nil {
		// 	tools.Logger.Error("更新店铺销售数据补充的数据 出错 id:",er1.Error())
		// 	continue
		// }

	}
	if len(updateDataMap) > 0 {
		sql := tools.GenerateBatchUpdateSql("restaurant_sales_daily_data", updateDataMap)
		//tools.Logger.Info(sql)
		tools.Logger.Info("批量更新开始")
		db.Exec(sql)
		tools.Logger.Info("批量更新结束")

	}

}
