package cmd

import (
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/controllers"
	"mulazim-api/jobs"
	"mulazim-api/models"
	"mulazim-api/tools"
	"mulazim-api/services/merchant/lakala"
)

type LakalaRefundDoublePayment struct {
	controllers.BaseController
}

// CheckDoublePayment 处理拉卡拉重复支付
func (l LakalaRefundDoublePayment) CheckDoublePayment(c *gin.Context) {

	type Record struct {
		OrderCount int   `gorm:"column:order_count"`
		OrderId    int64 `gorm:"column:order_id"`
	}
	var records []Record
	db := tools.GetDB()
	result := db.Table("t_pay_lakala").
		Select("count(order_id) as order_count, order_id").
		Where("created_at > ?", carbon.Now().SubHours(1).ToDateTimeString()).
		Where("order_status = ?", "1006").
		Where("object_type = ? ", models.PAY_LAKALA_OBJECT_TYPE_ORDER).
		Group("order_id").
		Having("count(order_id) > 1").
		Scan(&records)
	if result.RowsAffected <= 0 {
		l.Success(c, nil, "msg", 200)
		return
	}
	for _, record := range records {
		tools.Logger.Info(record)
		l.refundDoublePayment(record.OrderId)
	}
	l.Ok(c)
}

// refundDoublePayment
func (l LakalaRefundDoublePayment) refundDoublePayment(orderId int64) {
	var lakalaMapArr []map[string]interface{}
	tools.Db.Table("t_pay_lakala").
		Select("id, order_id").
		Where("object_type = ? ", models.PAY_LAKALA_OBJECT_TYPE_ORDER).
		Where("order_id = ?", orderId).
		Where("order_status = ?", 1006).
		Order("id ASC").
		Scan(&lakalaMapArr)
	if len(lakalaMapArr) < 2 {
		return
	}
	for index, lakalaMap := range lakalaMapArr {
		if index == 0 {
			continue
		}
		tools.Logger.Info("lakala-refund-double-payment: t_pay_lakala id: ", lakalaMap["id"])
		job := jobs.NewLakalaRefundJob()
		job.ProduceMessageToConsumer(map[string]interface{}{
			"lakala_pay_id": lakalaMap["id"],
		})
		tools.Logger.Error("拉卡拉重复支付订单退款中, 拉卡拉支付ID: " + tools.ToString(lakalaMap["id"]))
	}
}

//手动处理 退款
func (l LakalaRefundDoublePayment) RefundManual(c *gin.Context) {
	orderId :=tools.ToInt(c.Query("order_id"))
	if orderId ==0 {
		l.Ok(c)
		return 
	}
	db :=tools.GetDB()
	var lakalaMap map[string]interface{}
	db.Table("t_pay_lakala").
		Select("id, order_id").
		Where("object_type = ? ", models.PAY_LAKALA_OBJECT_TYPE_ORDER).
		Where("order_id = ?", orderId).
		Where("order_status = ?", 1006).
		Order("id DESC").
		Scan(&lakalaMap)
	if lakalaMap == nil{
		l.Ok(c)
		return
	}
	var orderMap map[string]interface{}
	db.Table("t_order_today").
		Select("id, order_id,state").
		Where("id = ?", orderId).
		// Where("refunded = ?", 1).
		Scan(&orderMap)
	if orderMap == nil {
		l.Ok(c)
		return
	}
	state :=tools.ToInt(orderMap["state"])	

	if !(state == 8 || state == 9) { //不是退款订单 不能处理 
		l.Ok(c) //默认回复 ok
		return
	}
	
	tools.Logger.Info("lakala-refund: t_pay_lakala id: ", lakalaMap["id"])
	job := jobs.NewLakalaRefundJob()
	job.ProduceMessageToConsumer(map[string]interface{}{
		"lakala_pay_id": lakalaMap["id"],
	})
	tools.Logger.Info("拉卡拉订单退款中, 拉卡拉支付ID: " + tools.ToString(lakalaMap["id"]))
	
	l.Ok(c)
}

//查询拉卡拉支付状态
func (l LakalaRefundDoublePayment) CheckPayment(c *gin.Context) {
	 outOrderNo :=c.Query("out_order_no")
	 rs,_:=lakala.GetLakalaService().QueryLakala(outOrderNo)
	 l.Success(c, rs, "msg", 200)
}

//提现修复
func (l LakalaRefundDoublePayment) FixLakalaWithDraw(c *gin.Context){

	rs,er:=lakala.GetLakalaService().WithdrawFix()
	if er!=nil{
		l.Fail(c, er.Error(), -1000)
		return
	}
	 l.Success(c, rs, "msg", 200)
}