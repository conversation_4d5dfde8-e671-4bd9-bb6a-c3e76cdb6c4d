package cmd

import (
	"context"
	"encoding/json"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/jobs"
	
	"mulazim-api/models"
	"mulazim-api/services/shipper"
	"mulazim-api/tools"
	"sort"
	"time"

	
	clause2 "gorm.io/gorm/clause"

	

	"github.com/golang-module/carbon/v2"
)

const (
	timeZoneName = "Asia/Shanghai"
)

type RestaurantAdminDistance struct {
	ResID    int
	AdminID  int
	Distance float64
}

// BeginReceivedOrderToPushTable
//
//	@Description: 开始将订单推送到推送表中
//	@author: Alimjan
//	@Time: 2023-08-24 18:10:05
func BeginReceivedOrderToPushTable() {
	defer func() {
		if err := recover(); err != nil {
			tools.Logger.Error("第一次推送时，出错了请及时处理", err)
		}
	}()
	redisHelper :=tools.GetRedisHelper()
	for {

		//tools.Logger.Info("正在开始插入订单到开始推送订单列表中")
		db := tools.Db
		// 1. 查找已经接单还没开始推送的订单
		subQuery := db.Model(&models.ShipperOrderPushBase{}).
			Select("order_id").
			Where("created_at > ?", tools.Yesterday(timeZoneName))
		var orders []models.OrderToday
		query := db.Model(&models.OrderToday{}).
			Select("id,store_id,city_id,area_id,distance,booking_time,market_type").
			Where("state > ?", 3).Where("state < ?", 10).
			// Where("shipper_id is null").
			Where("delivery_type = 1").
			Where("id NOT IN (?)", subQuery)
		if configs.MyApp.ShipperPushAll != 1 {
			query.Where("area_id in (?)", configs.MyApp.ShipperPushArea)
		}
		tools.Logger.Info("第一次推送查询开始")
		query.Scan(&orders)
		tools.Logger.Info("第一次推送查询结束")
		var orderBases []models.ShipperOrderPushBase
		// 2. 查找每个订单的第一次推送的配送员，并插入到推送表中
		for _, order := range orders {
			var orderPushDetails []models.ShipperOrderPushDetail
			base := models.ShipperOrderPushBase{
				OrderID:       int64(order.ID),
				StoreID:       int64(order.StoreID),
				CityID:        int64(order.CityID),
				AreaID:        int64(order.AreaID),
				StartPushTime: carbon.Now(timeZoneName).Carbon2Time(),
				/*下次根据配置文件之后的时间推送*/
				NextPushTime: carbon.Now(timeZoneName).AddMinutes(configs.MyApp.ShipperPushOrderTimeDelta[0]).Carbon2Time(),
				State:        models.StatePushing, //
				CurrentLevel: 1,
				CreatedAt:    carbon.Now(timeZoneName).Carbon2Time(),
				UpdatedAt:    carbon.Now(timeZoneName).Carbon2Time(),
				Details:      orderPushDetails,
			}

			var restaurant models.Restaurant
			db.Model(&models.Restaurant{}).Select("id,lat,lng").Where("id = ?", order.StoreID).First(&restaurant)
			if restaurant.ID > 0 {
				//tools.Logger.Info(restaurant.Lat, restaurant.Lng)
				var adminStores []models.AdminStore
				// 3. 查找订单可配送的配送员
				adminStoreQuery := db.Model(&models.AdminStore{}).Preload("Admin", "type in (8,9) and state = 1 and deleted_at is null").Where("store_id = ?", order.StoreID)
				// 如果该订单特价活动订单，并指定过指定配送员，则只查询指定的配送员
				specialShipperIds := order.GetSpecialPriceOrderShipperIds()
				if len(specialShipperIds)>0{
					adminStoreQuery.Where("admin_id in(?)",specialShipperIds)
				}
				adminStoreQuery.Find(&adminStores)
				// 缓存计算的距离 如果没有适合的配送员，则下一步推送给前三个配送员
				restaurantAdminDistances := make([]RestaurantAdminDistance, 0)
				for _, admin := range adminStores {
					if admin.Admin.ID != 0 {
						var lat float64
						var lng float64
						exists, _ := redisHelper.Exists(context.Background(),fmt.Sprintf("shipper_%d", admin.Admin.ID)).Result()
						if exists != 0 {
							shipperLocation,_ :=redisHelper.Get(context.Background(),fmt.Sprintf("shipper_%d", admin.Admin.ID)).Result()
							locationMap :=make(map[string]interface{})
							json.Unmarshal([]byte(shipperLocation),&locationMap)
							if locationMap != nil {
								lat = tools.ToFloat64(locationMap["lat"])
								lng = tools.ToFloat64(locationMap["lng"])
							}
						}
						distance := tools.CalculateLatitudeLongitudeDistance(restaurant.Lng, restaurant.Lat, lng, lat)
						restaurantAdminDistance := RestaurantAdminDistance{
							ResID:    restaurant.ID,
							AdminID:  admin.Admin.ID,
							Distance: distance,
						}
						restaurantAdminDistances = append(restaurantAdminDistances, restaurantAdminDistance)
						var shipperUser models.Admin
						rs := db.Model(&models.Admin{}).
							Preload("ShipperIncomeTemplate").
							Where("id = ?", admin.Admin.ID).
							First(&shipperUser)
						var orderShippingPrice, specialTimeShippingPrice, specialWeatherShippingPrice int
						if rs.RowsAffected > 0 && shipperUser.ID > 0 && shipperUser.ShipperIncomeTemplate.ID > 0 && order.ID > 0 {
							month := carbon.Now(configs.AsiaShanghai).Format("Y-m")
							orderCount := (new(shipper.ShipperFeeService)).GetShipperMonthOrderBeforeToday(admin.Admin.ID, month,carbon.Now(configs.AsiaShanghai).AddDays(-1).Format("Y-m-d"),false)
							todayOrderCount := int64(0)
							db.Model(&models.OrderToday{}).Where("shipper_id = ? and state = ?", admin.Admin.ID, 7).Count(&todayOrderCount)
							nthOrder := int(orderCount) + int(todayOrderCount)
							orderShippingPrice, specialTimeShippingPrice, specialWeatherShippingPrice, _ = (new(shipper.ShipperFeeService)).CalculateShipperFee(shipperUser.ID, shipperUser.ShipperIncomeTemplate, order, nthOrder)
						}
						// 4. 查找是否在可配送的已设置的范围内
						if distance < float64(configs.MyApp.ShipperPushOrderDistance[0]) {
							detail := models.ShipperOrderPushDetail{
								OrderID:                     int64(order.ID),
								ShipperID:                   int64(admin.AdminID),
								Distance:                    distance,
								PushedTime:                  carbon.Now(timeZoneName).Carbon2Time(),
								CurrentLevel:                1,
								EstimatedShippingPrice:      orderShippingPrice + specialTimeShippingPrice + specialWeatherShippingPrice,
								OrderShippingPrice:          orderShippingPrice,
								SpecialTimeShippingPrice:    specialTimeShippingPrice,
								SpecialWeatherShippingPrice: specialWeatherShippingPrice,
								CreatedAt:                   carbon.Now(timeZoneName).Carbon2Time(),
								UpdatedAt:                   carbon.Now(timeZoneName).Carbon2Time(),
							}
							orderPushDetails = append(orderPushDetails, detail)
						}
					}
				}
				if len(orderPushDetails) == 0 {
					sort.Slice(restaurantAdminDistances, func(i, j int) bool {
						return restaurantAdminDistances[i].Distance < restaurantAdminDistances[j].Distance
					})
					for index, restaurantAdminDistance := range restaurantAdminDistances {
						if index > 3 {
							break
						}

						var admin models.Admin

						rs := db.Model(&models.Admin{}).
							Preload("ShipperIncomeTemplate").
							Where("id = ?", restaurantAdminDistance.AdminID).
							First(&admin)
						var orderShippingPrice, specialTimeShippingPrice, specialWeatherShippingPrice int
						var err error
						if rs.RowsAffected > 0 && admin.ID > 0 && admin.ShipperIncomeTemplate.ID > 0 && order.ID > 0 {

							//配送员 这个月的 总订单数量
							//配送员 这个月的 总订单数量 不包括今天的
							month := carbon.Now(configs.AsiaShanghai).Format("Y-m")
							orderCountBefore := (new(shipper.ShipperFeeService)).GetShipperMonthOrderBeforeToday(admin.ID, month,carbon.Now(configs.AsiaShanghai).AddDays(-1).Format("Y-m-d"),false)
							//今天的成功的订单数量
							orderCountToday := (new(shipper.ShipperFeeService)).GetShipperOrderCountToday(admin.ID)
							nthOrder := orderCountBefore + orderCountToday + 1 //这个月的第几个订单 加1

							orderShippingPrice, specialTimeShippingPrice, specialWeatherShippingPrice, err = (new(shipper.ShipperFeeService)).CalculateShipperFee(admin.ID, admin.ShipperIncomeTemplate, order, nthOrder)
							if err != nil {
								tools.Logger.Error("FATAL 订单:", order.ID, " 计算配送费失败", err)
							}
						}
						detail := models.ShipperOrderPushDetail{
							OrderID:                     int64(order.ID),
							ShipperID:                   int64(restaurantAdminDistance.AdminID),
							Distance:                    restaurantAdminDistance.Distance,
							PushedTime:                  carbon.Now(timeZoneName).Carbon2Time(),
							CurrentLevel:                1,
							EstimatedShippingPrice:      orderShippingPrice + specialTimeShippingPrice + specialWeatherShippingPrice,
							OrderShippingPrice:          orderShippingPrice,
							SpecialTimeShippingPrice:    specialTimeShippingPrice,
							SpecialWeatherShippingPrice: specialWeatherShippingPrice,
							CreatedAt:                   carbon.Now(timeZoneName).Carbon2Time(),
							UpdatedAt:                   carbon.Now(timeZoneName).Carbon2Time(),
						}
						orderPushDetails = append(orderPushDetails, detail)
					}
					//tools.Logger.Info("第一次没能推送，可能配送员距离太远,推送了:",len(orderPushDetails),"个配送员")
				}
			}
			base.Details = orderPushDetails
			orderBases = append(orderBases, base)

		}
		if len(orderBases) > 0 {
			tools.Logger.Info("第一次推送insert开始")
			db.Create(&orderBases)
			//推送 
			for _, bb := range orderBases {
				
				for _, v := range bb.Details {
					if bb.Order.State == 4 &&  bb.Order.Taked == 0 { //已被抢单的话跳过
						jobs.SendNewOrderPush(tools.ToInt(v.OrderID),tools.ToInt(v.ShipperID))
					}
				}
			}
			tools.Logger.Info("第一次推送insert结束")
		}
		tools.Logger.Info("第一次推送数据到订单列表完成")
		tools.Logger.Infof("更新t_take_order 未记录的预计配送费 开始")
		UpdateTakeOrderShippingPrice()
		tools.Logger.Infof("更新t_take_order 未记录的预计配送费 结束")
		time.Sleep(5 * time.Second)
	}

	//}
}

// PushOrderSecondTime
//
//	@Description: 第二次，第三次，推送订单
//	@author: Alimjan
//	@Time: 2023-08-28 17:46:50
func PushOrderSecondTime() {
	defer func() {
		if err := recover(); err != nil {
			tools.Logger.Error("第二次推送时，出错了请及时处理", err)
		}
	}()
	redisHelper :=tools.GetRedisHelper()
	for true {
		//tools.Logger.Info("继续推送数据到订单列表")
		var orderBases []models.ShipperOrderPushBase
		db := tools.Db
		// 1. 查找可以第二次 第三次 推送的订单
		tools.Logger.Info("第二次推送查询开始")
		db.Model(&models.ShipperOrderPushBase{}).
			Preload("Details").
			Preload("Order").
			Where("next_push_time < ?", carbon.Now("Asia/Shanghai").
				ToDateTimeString()).
			Where("state = ?", 1).
			Find(&orderBases)
		tools.Logger.Info("第二次推送查询结束")
		for _, base := range orderBases {
			if base.CurrentLevel < 3 {
				base.NextPushTime = base.StartPushTime.Add(time.Duration(configs.MyApp.ShipperPushOrderTimeDelta[base.CurrentLevel]) * time.Minute)
			}
			var restaurant models.Restaurant
			db.Model(&models.Restaurant{}).Select("id,lat,lng").Where("id = ?", base.StoreID).First(&restaurant)
			if restaurant.ID > 0 {
				//tools.Logger.Info(restaurant.Lat, restaurant.Lng)
				var adminStores []models.AdminStore
				query := db.Model(&models.AdminStore{})
				adminIds := getAdminIds(base.Details)
				if len(adminIds) > 0 {
					query.Where("admin_id not in (?)", adminIds)
				}
				// 重要：如果特价活动，只获取设置的配送员
				if base.Order.MarketType == 2{
					shipperIds := base.Order.GetSpecialPriceOrderShipperIds()
					if len(shipperIds) > 0 {
						query.Where("admin_id in (?)", shipperIds)
					}
				}
				query.
					Preload("Admin", "type in (8,9) and state = 1 and deleted_at is null").
					Preload("Admin.ShipperIncomeTemplate").
					Where("store_id = ?", base.StoreID).Find(&adminStores)
				var orderPushDetails []models.ShipperOrderPushDetail
				type OrderShipper struct {
					OrderId int64
					ShipperId int64
					State int64
					Taked int
				}
				var orderShippers []OrderShipper
				for _, admin := range adminStores {

					if admin.Admin.ID != 0 {
						var lat float64
						var lng float64
						exists, _ := redisHelper.Exists(context.Background(),fmt.Sprintf("shipper_%d", admin.Admin.ID)).Result()
						if exists != 0 {
							shipperLocation,_ :=redisHelper.Get(context.Background(),fmt.Sprintf("shipper_%d", admin.Admin.ID)).Result()
							locationMap :=make(map[string]interface{})
							json.Unmarshal([]byte(shipperLocation),&locationMap)
							if locationMap != nil {
								lat = tools.ToFloat64(locationMap["lat"])
								lng = tools.ToFloat64(locationMap["lng"])
							}
						}
						distance := tools.CalculateLatitudeLongitudeDistance(restaurant.Lng, restaurant.Lat, lng, lat)
						maxDistance := 9999999
						if base.CurrentLevel < 3 {
							maxDistance = configs.MyApp.ShipperPushOrderDistance[base.CurrentLevel]
						}
						if distance < float64(maxDistance) {
							//tools.Logger.Info(admin.AdminID, ",", admin.Admin.Mobile, ",", distance, maxDistance)

							var orderShippingPrice, specialTimeShippingPrice, specialWeatherShippingPrice int
							var err error
							if admin.Admin.ShipperIncomeTemplate.ID > 0 {
								shipperId := admin.Admin.ID

								//配送员 这个月的 总订单数量 不包括今天的
								month := carbon.Now(configs.AsiaShanghai).Format("Y-m")
								orderCountBefore := (new(shipper.ShipperFeeService)).GetShipperMonthOrderBeforeToday(shipperId, month,carbon.Now(configs.AsiaShanghai).AddDays(-1).Format("Y-m-d"),false)
								//今天的成功的订单数量
								orderCountToday := (new(shipper.ShipperFeeService)).GetShipperOrderCountToday(shipperId)
								nthOrder := orderCountBefore + orderCountToday + 1 //这个月的第几个订单 加1

								orderShippingPrice, specialTimeShippingPrice, specialWeatherShippingPrice, err = (new(shipper.ShipperFeeService)).
									CalculateShipperFee(shipperId, admin.Admin.ShipperIncomeTemplate, base.Order, nthOrder)
								if err != nil {
									tools.Logger.Error("FATAL 订单:", base.Order.ID, " 计算配送费失败", err)
								}
							}
							if base.Order.MarketType == 2{ //特价活动 不能加入 特殊时间段和特殊天气的钱  新的规则
								tools.Logger.Info("新的规则特价活动不能加入 特殊时间段和特殊天气的钱 订单:", base.Order.ID, " 原来的值 特殊时间费用:", specialTimeShippingPrice,",特殊天气费用:", specialWeatherShippingPrice)
								specialTimeShippingPrice = 0
								specialWeatherShippingPrice = 0
							}
							detail := models.ShipperOrderPushDetail{
								BaseID:                      base.ID,
								OrderID:                     int64(base.OrderID),
								ShipperID:                   int64(admin.AdminID),
								Distance:                    distance,
								PushedTime:                  carbon.Now(timeZoneName).Carbon2Time(),
								CurrentLevel:                base.CurrentLevel + 1,
								EstimatedShippingPrice:      orderShippingPrice + specialTimeShippingPrice + specialWeatherShippingPrice,
								OrderShippingPrice:          orderShippingPrice,
								SpecialTimeShippingPrice:    specialTimeShippingPrice,
								SpecialWeatherShippingPrice: specialWeatherShippingPrice,
								CreatedAt:                   carbon.Now(timeZoneName).Carbon2Time(),
								UpdatedAt:                   carbon.Now(timeZoneName).Carbon2Time(),
							}
							orderPushDetails = append(orderPushDetails, detail)
							orderShippers = append(orderShippers, OrderShipper{OrderId: base.OrderID, ShipperId: int64(admin.AdminID),State:int64(base.Order.State) ,Taked:base.Order.Taked})
						}
					}
				}
				if len(orderPushDetails) > 0 {
					db.Create(&orderPushDetails)
					//推送 
					
					for _, v := range orderShippers {
						if  v.State == 4  &&  v.Taked == 0 { //已被抢单的话跳过
							jobs.SendNewOrderPush(int(v.OrderId),int(v.ShipperId))
						}
					}	
				}
			}
			base.CurrentLevel++
			if base.CurrentLevel > 3 {
				base.State = 2
			}
			db.Model(&base).Omit(clause2.Associations).Updates(map[string]interface{}{
				"next_push_time": base.NextPushTime,
				"state":          base.State,
				"current_level":  base.CurrentLevel,
				"updated_at":     carbon.Now(timeZoneName).Carbon2Time(),
			})
		}
		//tools.Logger.Info("第二次推送完成")
		time.Sleep(5 * time.Second)
	}
}

// getAdminIds
//
//	@Description: 获取配送员id
//	@author: Alimjan
//	@Time: 2023-08-28 19:30:57
//	@param details []models.ShipperOrderPushDetail
//	@return []int64
func getAdminIds(details []models.ShipperOrderPushDetail) []int64 {
	var ids []int64
	for _, d := range details {
		ids = append(ids, d.ShipperID)
	}
	return ids
}

// PushOrderSecondTime
//
//	@Description: 更新t_take_order 预计配送费
//	@author: rozimamamt
//	@Time: 2023-11-21 13:11
func UpdateTakeOrderShippingPrice() {
	defer func() {
		if err := recover(); err != nil {
			tools.Logger.Error("更新t_take_order 预计配送费，出错了请及时处理", err)
		}
	}()
	redisHelper :=tools.GetRedisHelper()
	// for {

		var takeOrders []models.TakeOrder
		db := tools.Db
		db.Model(&models.TakeOrder{}).
			Where("created_at between ? and ?", carbon.Now(configs.AsiaShanghai).AddDays(-1).Format("Y-m-d H:i:s"), carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s")).
			Where("(estimated_shipping_price is null or estimated_shipping_price = 0)").
			Find(&takeOrders)
		
		for _, take := range takeOrders {
			var tk models.ShipperOrderPushDetail
			db.Model(&models.ShipperOrderPushDetail{}).
				Where("order_id = ? and shipper_id = ? ", take.OrderID, take.AdminID).
				Find(&tk)
			if tk.ID > 0 {
				if tk.EstimatedShippingPrice == 0 { 
					continue
				}
				db.Model(&models.TakeOrder{}).
					Where("id = ?", take.ID).
					Updates(&map[string]interface{}{
						"estimated_shipping_price": tk.EstimatedShippingPrice,
					})
			}else{
				var admin models.Admin

				rs := db.Model(&models.Admin{}).
					Preload("ShipperIncomeTemplate").
					Where("id = ?", take.AdminID).
					First(&admin)
				var orderShippingPrice, specialTimeShippingPrice, specialWeatherShippingPrice int
				var order models.OrderToday
				db.Model(&models.OrderToday{}).
					Where("id = ?", take.OrderID).
					First(&order)
				var restaurant models.Restaurant
				db.Model(&models.Restaurant{}).Select("id,lat,lng").Where("id = ?", order.StoreID).First(&restaurant)

				var err error
				if rs.RowsAffected > 0 && admin.ID > 0 && admin.ShipperIncomeTemplate.ID > 0 && order.ID > 0 {

					//配送员 这个月的 总订单数量
					//配送员 这个月的 总订单数量 不包括今天的
					month := carbon.Now(configs.AsiaShanghai).Format("Y-m")
					orderCountBefore := (new(shipper.ShipperFeeService)).GetShipperMonthOrderBeforeToday(admin.ID, month,carbon.Now(configs.AsiaShanghai).AddDays(-1).Format("Y-m-d"),false)
					//今天的成功的订单数量
					orderCountToday := (new(shipper.ShipperFeeService)).GetShipperOrderCountToday(admin.ID)
					nthOrder := orderCountBefore + orderCountToday + 1 //这个月的第几个订单 加1

					orderShippingPrice, specialTimeShippingPrice, specialWeatherShippingPrice, err = (new(shipper.ShipperFeeService)).CalculateShipperFee(admin.ID, admin.ShipperIncomeTemplate, order, nthOrder)
					if err != nil {
						tools.Logger.Error("FATAL 订单:", order.ID, " 计算配送费失败", err)
					}
				
				var lat float64
				var lng float64
				exists, _ := redisHelper.Exists(context.Background(),fmt.Sprintf("shipper_%d", take.AdminID)).Result()
				if exists != 0 {
					shipperLocation,_ :=redisHelper.Get(context.Background(),fmt.Sprintf("shipper_%d", take.AdminID)).Result()
					locationMap :=make(map[string]interface{})
					json.Unmarshal([]byte(shipperLocation),&locationMap)
					if locationMap != nil {
						lat = tools.ToFloat64(locationMap["lat"])
						lng = tools.ToFloat64(locationMap["lng"])
					}
				}
				distance := tools.CalculateLatitudeLongitudeDistance(restaurant.Lng, restaurant.Lat, lng, lat)
				var base models.ShipperOrderPushBase	
				db.Model(&models.ShipperOrderPushBase{}).Where("order_id = ?",take.OrderID).Select("id").Find(&base)

				if order.MarketType == 2 { //特价活动 不能加入 特殊时间段和特殊天气的钱  新的规则
					tools.Logger.Info("新的规则特价活动不能加入 特殊时间段和特殊天气的钱 订单:", order.ID, " 原来的值 特殊时间费用:", specialTimeShippingPrice,",特殊天气费用:", specialWeatherShippingPrice)
					specialTimeShippingPrice = 0
					specialWeatherShippingPrice = 0
				}

				detail := models.ShipperOrderPushDetail{
					BaseID: base.ID,
					OrderID:                     int64(take.OrderID),
					ShipperID:                   int64(take.AdminID),
					Distance:                    distance,
					PushedTime:                  carbon.Now(timeZoneName).Carbon2Time(),
					CurrentLevel:                1,
					EstimatedShippingPrice:      orderShippingPrice + specialTimeShippingPrice + specialWeatherShippingPrice,
					OrderShippingPrice:          orderShippingPrice,
					SpecialTimeShippingPrice:    specialTimeShippingPrice,
					SpecialWeatherShippingPrice: specialWeatherShippingPrice,
					CreatedAt:                   carbon.Now(timeZoneName).Carbon2Time(),
					UpdatedAt:                   carbon.Now(timeZoneName).Carbon2Time(),
				}
				db.Create(&detail)
				db.Model(&models.TakeOrder{}).
					Where("id = ?", take.ID).
					Updates(&map[string]interface{}{
						"estimated_shipping_price": orderShippingPrice + specialTimeShippingPrice + specialWeatherShippingPrice,
					})
				}
			}
		}
		// time.Sleep(5 * time.Second)
	// }
}
