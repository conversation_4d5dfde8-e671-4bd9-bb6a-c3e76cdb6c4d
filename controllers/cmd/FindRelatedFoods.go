package cmd

import (
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/tools"
	"sort"
)

type FindRelatedFoods struct {
	controllers.BaseController
}

func (f FindRelatedFoods) FindFoods(context *gin.Context) {
	tools.Logger.Infof("开始计算关联美食")
	//beginDate := "2023-09-01"
	beginDate := carbon.Now().SubMonths(3).ToDateString()

	//select store_foods_id  from t_order_detail created_at > '2021-09-01' group by store_foods_id
	//models.Order{}
	orders := make([]models.Order, 0)
	db := tools.Db

	var res []models.Restaurant
	db.Model(&models.Restaurant{}).Select("id,name_ug").Where("state in (1,2) and deleted_at is null").Find(&res)
	for iii, restaurant := range res {
		db.Select("id,order_id,store_id").
			Preload("OrderDetail").
			Model(&models.Order{}).
			Where("created_at > ?",beginDate).
			Where("state in (7,10)").
			Where("store_id = ?", restaurant.ID).
			Find(&orders)
		topFoods := make([]TopFood, 0)
		for _, order := range orders {
			if len(order.OrderDetail)>1 {
				storeFoodIds := make([]int, 0)
				for _, orderDetail := range order.OrderDetail {
					storeFoodIds = append(storeFoodIds, orderDetail.StoreFoodsID)
				}
				for _, storeFoodId1 := range storeFoodIds {
					for _, storeFoodId2 := range storeFoodIds {
						if storeFoodId1 != storeFoodId2 {
							topFood := f.getCurrentTopFood(&topFoods, storeFoodId1)
							f.AddCount2RelatedItem(topFood.RelatedFoods, storeFoodId2)
						}
					}
				}
			}
		}
		for _, topFood := range topFoods {
			ids := make([]int, 0)
			for _, relatedFood := range *topFood.RelatedFoods {
				ids = append(ids, relatedFood.StoreFoodsID)
			}
		}

		for _, topFood := range topFoods {
			if len(*topFood.RelatedFoods) > 0 {
				//排序
				sort.Sort(ByCount(*topFood.RelatedFoods))
				//取前3个
				updatesMap := make(map[string]interface{})
				if len(*topFood.RelatedFoods)>1 {
					if (*topFood.RelatedFoods)[0].Count>0 {
						updatesMap["related_foods_id1"] = (*topFood.RelatedFoods)[0].StoreFoodsID
					}else{
						updatesMap["related_foods_id1"] = 0
					}
					db.Model(&models.RestaurantFoods{}).Where("id = ?", topFood.StoreFoodsID).Updates(updatesMap)
				}
				//if len(*topFood.RelatedFoods)>2 {
				//	//updatesMap["related_foods_id2"] = (*topFood.RelatedFoods)[1].StoreFoodsID
				//	updatesMap["related_foods_id2"] = 0
				//}else{
				//	updatesMap["related_foods_id2"] = 0
				//}
				//if len(*topFood.RelatedFoods)>3 {
				//	//updatesMap["related_foods_id3"] = (*topFood.RelatedFoods)[2].StoreFoodsID
				//	updatesMap["related_foods_id3"] = 0
				//}else{
				//	updatesMap["related_foods_id3"] = 0
				//}

			}
		}
		println(iii,"/", len(res),"店铺id:", restaurant.ID, "店铺名称:", restaurant.NameUg)
	}
	tools.Logger.Infof("结束计算关联美食")
}

func (f FindRelatedFoods) getCurrentTopFood(topFoods *[]TopFood, StoreFoodsID int) *TopFood {
	for _, topFood := range *topFoods {
		if topFood.StoreFoodsID == StoreFoodsID {
			return &topFood
		}
	}
	newTopFood := TopFood{
		StoreFoodsID: StoreFoodsID,
		Count:        1,
		RelatedFoods: nil,
	}
	newTopFood.RelatedFoods = new([]TopFood)
	*topFoods = append(*topFoods, newTopFood)
	return &newTopFood
}

func (f FindRelatedFoods) AddCount2RelatedItem(relatedFoods *[]TopFood, storeFoodId2 int) {
	for i, food := range *relatedFoods {
		if food.StoreFoodsID == storeFoodId2 {
			(*relatedFoods)[i].Count++
			return

		}
	}
	*relatedFoods = append(*relatedFoods, TopFood{
		StoreFoodsID: storeFoodId2,
		Count:        1,
	})
}

type TopFood struct {
	StoreFoodsID int `json:"store_foods_id"`
	Count int `json:"count"`
	RelatedFoods *[]TopFood `json:"related_foods"`
}

type ByCount []TopFood

func (s ByCount) Len() int {
	return len(s)
}

func (s ByCount) Less(i, j int) bool {
	//无法定位的排到最后
	return s[i].Count>s[j].Count
}
func (s ByCount) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}
