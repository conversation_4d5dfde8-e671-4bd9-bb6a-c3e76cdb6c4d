package cmd

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/tools"
)

type DeleteOrderCreateRedisKey struct {
	controllers.BaseController
}
//
// DeleteKey
//  @Description: 删除订单创建redis key 用于重建订单创建队列数据 
//  @author: <PERSON><PERSON>jan
//  @Time: 2023-08-26 13:39:43
//  @receiver d DeleteOrderCreateRedisKey
//  @param context *gin.Context
//
func (d DeleteOrderCreateRedisKey) DeleteKey(context *gin.Context) {
	maxIdStr := context.DefaultQuery("max_id","880000")
	minIdStr := context.DefaultQuery("min_id","0")
	maxId := tools.ToInt(maxIdStr)
	minId := tools.ToInt(minIdStr)

	for i := minId; i < maxId; i++ {
		tools.RedisDel(context, "lumen_database_coj_"+tools.ToString(i))
	}
	context.JSON(200, gin.H{
		"code": 200,
		"msg":  "success",
	})
}