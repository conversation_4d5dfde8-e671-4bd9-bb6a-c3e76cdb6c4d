package cmd

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"math"
	"mulazim-api/controllers"
	"mulazim-api/models"
	lakalaService "mulazim-api/services/merchant/lakala"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

type DownloadCmsStaticsCSV struct {
	controllers.BaseController
}

// DownloadWechatPayStatics
//
//	@Description: 下载微信支付统计数据
//	@author: Alimjan
//	@Time: 2023-08-26 17:09:49
//	@receiver d DownloadCmsStaticsCSV
//	@param context *gin.Context
func (d DownloadCmsStaticsCSV) DownloadWechatPayStatics(c *gin.Context) {

	//validate
	type WechatPayRequest struct {
		StartDate string `form:"start_date" binding:"required"`
		EndDate   string `form:"end_date" binding:"required"`
	}
	var request WechatPayRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}
	// 开始时间和结束时间不能超过3天
	startTime := carbon.Parse(request.StartDate)
	endTime := carbon.Parse(request.EndDate)
	if startTime.DiffInDays(endTime) > 3 {
		c.JSON(200, gin.H{
			"code": 400,
			"msg":  "开始时间和结束时间不能超过3天",
		})
		return
	}

	// 写入CSV数据到内存缓冲区
	writer := csv.NewWriter(transform.NewWriter(c.Writer, simplifiedchinese.GBK.NewEncoder()))

	data := [][]string{
		{"ID", "订单ID", "平台ID", "微信交易ID", "微信预支付ID", "交易类型", "总金额", "已支付金额", "数量", "备注", "创建时间", "更新时间"},
	}
	db := tools.Db
	var wechatPayList []models.Wechat
	db.Model(&models.Wechat{}).Where("created_at >= ? AND created_at <= ?", request.StartDate, request.EndDate).Find(&wechatPayList)
	for _, wechatPay := range wechatPayList {
		data = append(data, []string{
			convertToCsvStringText(wechatPay.ID),
			convertToCsvStringText(wechatPay.OrderID),
			convertToCsvStringText(wechatPay.OutTradeNo),
			convertToCsvStringText(wechatPay.TransactionID),
			convertToCsvStringText(wechatPay.PrepayID),
			convertToCsvStringText(wechatPay.TradeType),
			convertToCsvStringText(wechatPay.TotalFee),
			convertToCsvStringText(wechatPay.Payed),
			convertToCsvStringText(wechatPay.Number),
			convertToCsvStringText(wechatPay.Remark),
			wechatPay.CreatedAt.Format("2006-01-02 15:04:05"),
			wechatPay.UpdatedAt.Format("2006-01-02 15:04:05"),
		})

	}

	// 设置响应头，告诉浏览器下载文件
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=微信支付mulazim平台原始记录(%s到%s).csv", request.StartDate, request.EndDate))
	c.Header("Content-Type", "text/csv; charset=gbk")

	writer.WriteAll(data)
	writer.Flush()

}

// LakalaPayStatics
//
//	@Description: 下载拉卡拉支付统计数据(mulazim平台上的)
//	@author: Alimjan
//	@Time: 2023-08-26 17:27:59
//	@receiver d DownloadCmsStaticsCSV
//	@param c *gin.Context
func (d DownloadCmsStaticsCSV) DownloadLakalaPayStatics(c *gin.Context) {

	//validate
	type WechatPayRequest struct {
		StartDate string `form:"start_date" binding:"required"`
		EndDate   string `form:"end_date" binding:"required"`
	}

	var request WechatPayRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}
	// 开始时间和结束时间不能超过3天
	startTime := carbon.Parse(request.StartDate)
	endTime := carbon.Parse(request.EndDate)
	if startTime.DiffInDays(endTime) > 3 {
		c.JSON(200, gin.H{
			"code": 400,
			"msg":  "开始时间和结束时间不能超过3天",
		})
		return
	}

	// 写入CSV数据到内存缓冲区
	writer := csv.NewWriter(transform.NewWriter(c.Writer, simplifiedchinese.GBK.NewEncoder()))
	data := [][]string{
		{"ID", "订单ID", "订单号", "付款人openid", "商户会员号", "外部订单号", "金额", "支付金额", "外部请求号", "支付状态", "订单状态", "错误信息", "支付流水号", "分账流水号", "第三方支付", "支付时间", "支付来源", "创建时间", "更新时间"},
	}
	db := tools.Db
	var lakalaPayList []models.PayLakala
	db.Model(&models.PayLakala{}).Where("created_at >= ? AND created_at <= ?", request.StartDate, request.EndDate).Find(&lakalaPayList)
	for _, lakalaPay := range lakalaPayList {
		data = append(data, []string{
			convertToCsvStringText(lakalaPay.ID),
			convertToCsvStringText(lakalaPay.OrderID),
			convertToCsvStringText(lakalaPay.OrderNo),
			convertToCsvStringText(lakalaPay.PayerOpenid),
			convertToCsvStringText(lakalaPay.SellerMemberNo),
			convertToCsvStringText(lakalaPay.OutOrderNo),
			convertToCsvStringText(lakalaPay.Amount),
			convertToCsvStringText(lakalaPay.PayAmount),
			convertToCsvStringText(lakalaPay.OutRequestNo),
			convertToCsvStringText(lakalaPay.PayStatus),
			convertToCsvStringText(lakalaPay.OrderStatus),
			convertToCsvStringText(lakalaPay.ErrorMessage),
			convertToCsvStringText(lakalaPay.PaySeqNo),
			convertToCsvStringText(lakalaPay.SplitSeqNo),
			convertToCsvStringText(lakalaPay.ThirdPartyPayment),
			convertToCsvStringText(lakalaPay.PayTime),
			convertToCsvStringText(lakalaPay.PayFrom),
			lakalaPay.CreatedAt.Format("2006-01-02 15:04:05"),
			lakalaPay.UpdatedAt.Format("2006-01-02 15:04:05"),
		})

	}
	// 设置响应头，告诉浏览器下载文件
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=拉卡拉支付mulazim平台原始记录(%s到%s).csv", request.StartDate, request.EndDate))
	c.Header("Content-Type", "text/csv; charset=gbk")

	writer.WriteAll(data)
	writer.Flush()
}
func convertToCsvStringText(item interface{}) string {
	return fmt.Sprintf("`%s", tools.ToString(item)) // 用反引号包裹，防止出现科学计数法
}

// LakalaFee
//
//	@Description: 拉卡拉手续费记录下载
//	@author: rozimamat
//	@Time: 2023-09-02 16:12:00
//	@receiver d DownloadLakalaFee
//	@param c *gin.Context
func (d DownloadCmsStaticsCSV) DownloadLakalaFee(c *gin.Context) {
	var (
		lakaService = lakalaService.NewLakalaService(c)
	)
	//validate
	type WechatPayRequest struct {
		StartDate string `form:"start_date" binding:"required"`
		EndDate   string `form:"end_date" binding:"required"`
	}

	var request WechatPayRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}
	// 开始时间和结束时间不能超过31天
	startTime := carbon.Parse(request.StartDate)
	endTime := carbon.Parse(request.EndDate)
	if startTime.DiffInDays(endTime) > 31 {
		c.JSON(200, gin.H{
			"code": 400,
			"msg":  "开始时间和结束时间不能超过31天",
		})
		return
	}

	// 写入CSV数据到内存缓冲区
	writer := csv.NewWriter(transform.NewWriter(c.Writer, simplifiedchinese.GBK.NewEncoder()))
	data := [][]string{
		{
			"序号",
			"手续费金额",
			"手续费产生类型",
			"备注",
			"手续费产生日期",
			"收取状态",
			"收取方式",
			"收取时间",
			// "收取账户",
			// "收取专用账户",
		},
	}

	type FeeListResultList struct {
		Number      int
		Fee         int    `json:"fee"`
		FeeType     int    `json:"fee_type"`
		Status      int    `json:"status"`
		Remark      string `json:"remark"`
		FeeDate     string `json:"fee_date"`
		CollectType int    `json:"collect_type"`
		CollectTime string `json:"collect_time"`
		// AccountType string `json:"account_type"`
	}
	type FeeListResult struct {
		List         []FeeListResultList `json:"list"`
		TotalCount   int                 `json:"total_count"`
		CurrentCount int                 `json:"current_count"`
	}
	type FeeListResponse struct {
		Status string        `json:"status"`
		Result FeeListResult `json:"result"`
	}
	type LakalaResponse struct {
		Sign     string `json:"sign"`
		Response string `json:"response"`
	}

	var items []FeeListResultList

	pageLimit := 20

	startIndex := 0
	endIndex := 20

	var lakalaResponse LakalaResponse

	var feeListResponse FeeListResponse

	res, er := lakaService.FeeList(c, startTime.Format("Y-m-d"), endTime.Format("Y-m-d"), startIndex, endIndex)
	if er != nil {
		c.JSON(200, gin.H{
			"code": 400,
			"msg":  "手续费接口出现错误",
		})
		return
	}
	json.Unmarshal([]byte(res), &lakalaResponse)

	json.Unmarshal([]byte(lakalaResponse.Response), &feeListResponse)

	items = append(items, feeListResponse.Result.List...)

	totalItems := feeListResponse.Result.TotalCount
	currentItems := feeListResponse.Result.CurrentCount

	if totalItems > pageLimit {

		pages := math.Ceil(tools.ToFloat64(totalItems) / tools.ToFloat64(pageLimit))

		for i := 1; i < tools.ToInt(pages); i++ {
			startIndex = currentItems
			endIndex = startIndex + pageLimit

			res, _ = lakaService.FeeList(c, startTime.Format("Y-m-d"), endTime.Format("Y-m-d"), startIndex, endIndex)

			json.Unmarshal([]byte(res), &lakalaResponse)

			json.Unmarshal([]byte(lakalaResponse.Response), &feeListResponse)

			items = append(items, feeListResponse.Result.List...)

			currentItems = feeListResponse.Result.CurrentCount

		}

	}

	//手续费产生类型
	//1：收单  4：提现  5：转账  6：分账  8：税筹  9：返利
	feeTypes := map[int]interface{}{
		1: "收单",
		4: "提现",
		5: "转账",
		6: "分账",
		8: "税筹",
		9: "返利",
	}
	//收取状态
	//1：欠款  2：已收取  4：无需收取
	feeStatus := map[int]interface{}{
		1: "欠款",
		2: "已收取",
		4: "无需收取",
	}
	//收取方式
	//1：自动收取 2：结算收取 3：定时收取
	feeCollectType := map[int]interface{}{
		1: "自动收取",
		2: "结算收取",
		3: "定时收取",
	}

	number := 0
	for _, lakalaPay := range items {
		number++
		data = append(data, []string{
			convertToCsvStringText(number),
			convertToCsvStringText(tools.ToFloat64(lakalaPay.Fee) / 100),
			convertToCsvStringText(feeTypes[lakalaPay.FeeType]),
			convertToCsvStringText(lakalaPay.Remark),
			convertToCsvStringText(lakalaPay.FeeDate),
			convertToCsvStringText(feeStatus[lakalaPay.Status]),
			convertToCsvStringText(feeCollectType[lakalaPay.CollectType]),
			convertToCsvStringText(lakalaPay.CollectTime),
			// convertToCsvStringText(lakalaPay.AccountType),
		})

	}
	// 设置响应头，告诉浏览器下载文件
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=拉卡拉支付mulazim平台手续费(%s到%s).csv", request.StartDate, request.EndDate))
	c.Header("Content-Type", "text/csv; charset=gbk")

	writer.WriteAll(data)
	writer.Flush()
}
