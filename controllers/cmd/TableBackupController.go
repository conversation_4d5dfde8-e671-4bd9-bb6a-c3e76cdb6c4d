package cmd

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	Other "mulazim-api/models/other"
	"mulazim-api/tools"
	"time"
)
//
//  TableBackupController
//  @Description: 归档过期数据
//
type TableBackupController struct {
	controllers.BaseController
}
//
// BackupTask
//  @Description: 备份任务
//  @receiver c
//  @param context
//
func (c TableBackupController) BackupTask(context *gin.Context) {
	var tasks []Other.TableBackup
	db := tools.Db
	db.Where("state = ?", 1).Find(&tasks)
	for _, task := range tasks {
		c.createBackupLog(task.ID, 1, fmt.Sprintf("开始备份数据，从  %s 到 %s,保留 %d 天", task.FromTable, task.ToTable, task.KeepDay))
		err := c.backupTable(task.FromTable, task.ToTable, task.KeepDay)
		if err!=nil {
			c.createBackupLog(task.ID, 0, fmt.Sprintf("数据迁移的时候出错了 %s to %s: %s", task.FromTable, task.ToTable, err))
			return
		}else{
			c.createBackupLog(task.ID, 1, fmt.Sprintf("数据备份成功，从  %s 到 %s,保留 %d 天", task.FromTable, task.ToTable, task.KeepDay))
		}
		page,err := c.cleanTable(task.FromTable, task.KeepDay)
		if err!=nil {
			c.createBackupLog(task.ID, 0, fmt.Sprintf("数据删除的时候出错了 %s to %s: %s", task.FromTable, task.ToTable, err))
			return
		}else{
			c.createBackupLog(task.ID, 1, fmt.Sprintf("数据删除成功，从  %s 到 %s,保留 %d 天，共删除%d页", task.FromTable, task.ToTable, task.KeepDay,page))
		}
	}
	tools.Logger.Infof("备份结束")
	c.Success(context, nil,"msg",200)
}
//
// backupTable
//  @Description: 备份数据
//  @receiver c
//  @param fromTable
//  @param toTable
//  @param keepDay
//  @return error
//
func (c TableBackupController) backupTable(fromTable string, toTable string, keepDay int) error {
	cutoffDate := time.Now().AddDate(0, 0, -keepDay).Truncate(24 * time.Hour)
	query := fmt.Sprintf("INSERT INTO %s.%s SELECT * FROM %s.%s WHERE created_at < ?", "mulazimpro_backup", toTable, "mulazimpro", fromTable)
	db := tools.Db
	if err := db.Exec(query, cutoffDate).Error; err != nil {
		return err
	}
	return nil
}
//
// cleanTable
//  @Description: 清理表数据
//  @receiver c
//  @param fromTable
//  @param keepDay
//  @return int
//  @return error
//
func (c TableBackupController) cleanTable(fromTable string, keepDay int) (int,error){
	cutoffDate := time.Now().AddDate(0, 0, -keepDay).Truncate(24 * time.Hour)
	db := tools.Db
	page := 1
	for {
		deleteQuery := fmt.Sprintf("DELETE FROM %s WHERE created_at < ? LIMIT 1000", fromTable)
		res := db.Exec(deleteQuery, cutoffDate)
		if err := res.Error; err != nil {
			return 0,err
		}
		rowsAffected := res.RowsAffected
		page = page + 1
		if rowsAffected == 0 {
			break // 没有更多行需要删除
		}
	}
	return page,nil
}
//
// createBackupLog
//  @Description: 创建备份日志
//  @receiver c
//  @param backupId
//  @param state
//  @param desc
//
func (c TableBackupController) createBackupLog(backupId int,state int,desc string){
	db := tools.Db
	backupLog := Other.TableBackupLog{
		TableBackupID: backupId,
		State: state,
		Desc: desc,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	db.Create(&backupLog)
}