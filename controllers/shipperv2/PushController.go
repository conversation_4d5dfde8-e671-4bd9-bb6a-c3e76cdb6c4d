package shipperv2

import (
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/services"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type PushController struct {
	controllers.BaseController
}

func (push *PushController) Register(c *gin.Context) {
	type RegisterRequest struct {
		Rid      string                    `form:"rid" binding:"required"`
		Platform models.PushDevicePlatform `form:"platform" binding:"required,oneof=android ios"`
		Lang     models.PushDeviceLang     `form:"lang" binding:"required,oneof=zh ug"`
	}
	var request RegisterRequest
	if err := c.ShouldBind(&request); err != nil {
		tools.Logger.Error("参数错误:", err)
		panic(err)
		return
	}
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	deviceAppVersion :=configs.MyApp.MerchantPushMinVersion //商家端 最小版本 
	if c.<PERSON>Header("Version-Name") != ""{
		version2 :=tools.ToInt64(tools.ReplaceString(c.GetHeader("Version-Name"),".",""))
		if version2 > 0 {
			deviceAppVersion = version2
		}
	}
	Brand :=""
	if c.GetHeader("Device-Brande") != ""{
		Brand =tools.ToString(c.GetHeader("Device-Brande"))
	}
	_, err := services.PushDeviceServiceInst.CreateAdminDevice(admin, request.Rid, request.Platform, models.PushDeviceClientShipper, request.Lang,admin.ID,deviceAppVersion,Brand)
	if err != nil {
		tools.Logger.Error("创建设备失败:", err)
		push.Fail(c, "failed", 200)
		return
	}
	push.Success(c, nil, "msg", 200)
}

// 注销
func (push *PushController) UnRegister(c *gin.Context) {
	type UnRegisterRequest struct {
		Rid string `form:"rid" binding:"required"`
	}
	var request UnRegisterRequest
	if err := c.ShouldBind(&request); err != nil {
		tools.Logger.Error("参数错误:", err)
		panic(err)
		return
	}
	err := services.PushDeviceServiceInst.DeleteAdminDevice(request.Rid, models.PushDeviceClientShipper)
	if err != nil {
		tools.Logger.Error("注销设备失败:", err)
		push.Fail(c, "failed", 200)
		return
	}
	push.Success(c, nil, "msg", 200)
}
