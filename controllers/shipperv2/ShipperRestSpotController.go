package shipperv2

import (
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"

	"mulazim-api/services"

	"github.com/gin-gonic/gin"
)

type ShipperRestSpotController struct {
	controllers.BaseController
}

// Desc: List
// Author: rozi mamat
// Date: 2025-07-07
func (m *ShipperRestSpotController) List(c *gin.Context) {

	type Params struct {
		Lat  string `form:"lat" query:"lat"	 json:"lat" binding:"required"`
		Lng  string `form:"lng" query:"lng"	 json:"lng" binding:"required"`

		
		AreaID int    `form:"area_id" query:"area_id" json:"area_id" `
		CityID int    `form:"city_id" query:"city_id" json:"city_id" `
		KW     string `form:"kw" query:"kw" json:"kw" `
	}
	var (
		l, _            = c.Get("lang_util")
		langUtil        = l.(lang.LangUtil)
		service = services.NewShipperRestSpotService(c)
	)
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	var params Params
	if err := c.ShouldBind(&params); err != nil {
		panic(err)
		return
	}
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID = admin.AdminCityID
	params.AreaID = admin.AdminAreaID
	
	// 获取信息
	result := service.List(admin, 1, 100, params.CityID, params.AreaID, 1,params.KW,langUtil.Lang,true,params.Lat,params.Lng)
	m.Success(c, result, "msg", 200)

}
