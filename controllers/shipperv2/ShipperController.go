package shipperv2

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/controllers"
	"mulazim-api/factory"
	"mulazim-api/jobs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/ShipperRequest"
	shipperResources "mulazim-api/resources/shipper"
	shipperService "mulazim-api/services/shipperv2"
	"mulazim-api/tools"
	shipperTransformer "mulazim-api/transformers/shipperv2"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ShipperController struct {
	controllers.BaseController
}

// ShipperNewOrderList 获取配送员新订单列表
//
//	@Description:
//	@receiver shipper
//	@param c
func (shipper ShipperController) ShipperNewOrderList(c *gin.Context) {
	var (
		l, _               = c.Get("lang_util")
		lang               = l.(lang.LangUtil)
		admin              models.Admin
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
		streetId           = c.Query("street_id")
		list               []models.OrderToday
	)
	anyAdmin, _ := c.Get("admin")
	admin = anyAdmin.(models.Admin)
	//获取新订单列表
	list = shipperService.GetList(c, admin, streetId)
	//获取角标
	count := shipperService.GetTabCount(c, admin)
	// 过滤压单订单
	list = shipperService.AutoDispatchFilterOrder(admin.AdminAreaID,list)
	//格式化返回数据
	data := shipperTransformer.FormatOrderList(list, count, streetId, admin)
	c.JSON(200, gin.H{
		"status": 200,
		"msg":    lang.T("msg"),
		"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		"data":   data,
	})

}

// ShipperMyOrderList
//
//	@Description: 获取我的订单列表
//	@author: Alimjan
//	@Time: 2022-09-08 15:31:32
//	@receiver shipper ShipperController
//	@param c *gin.Context
func (shipper ShipperController) ShipperMyOrderList(c *gin.Context) {
	var (
		admin              models.Admin
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
	)
	anyAdmin, _ := c.Get("admin")
	admin = anyAdmin.(models.Admin)
	list := shipperService.GetMyOrderList(admin, 1)

	//获取角标
	count := shipperService.GetTabCount(c, admin)

	data := shipperTransformer.FormatMyOrderList(list, count)

	shipper.Success(c, data, "msg", 200)
}

// ShipperFinishedOrder
//
//	@Description: 完成订单列表
//	@author: Alimjan
//	@Time: 2022-09-09 17:38:04
//	@receiver shipper ShipperController
//	@param context *gin.Context
func (shipper ShipperController) ShipperFinishedOrder(c *gin.Context) {
	var (
		admin              models.Admin
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
	)
	anyAdmin, _ := c.Get("admin")
	admin = anyAdmin.(models.Admin)
	list := shipperService.GetFinishedOrderList(admin, 1)

	//获取角标
	count := shipperService.GetTabCount(c, admin)

	data := shipperTransformer.FormatMyOrderList(list, count)

	shipper.Success(c, data, "msg", 200)
}

// ShipperFailOrder
//
//	@Description: 失败订单列表
//	@author: Alimjan
//	@Time: 2022-09-09 17:37:52
//	@receiver shipper ShipperController
//	@param context *gin.Context
func (shipper ShipperController) ShipperFailOrder(c *gin.Context) {
	var (
		admin              models.Admin
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
	)
	anyAdmin, _ := c.Get("admin")
	admin = anyAdmin.(models.Admin)
	list := shipperService.GetFailedOrderList(admin, 1)

	//获取角标
	count := shipperService.GetTabCount(c, admin)

	data := shipperTransformer.FormatFailedList(list, count, 1)

	shipper.Success(c, data, "msg", 200)
}

// Notes: 配送员发展新客户统计
//User: yakup`
//DateTime: 2022年8月31日

func (shipper ShipperController) RecommendList(c *gin.Context) {
	//validate
	type RecommandRequest struct {
		Limit     int    `form:"limit" binding:""`
		Page      int    `form:"page" binding:"required"`
		StartDate string `form:"start_date" binding:"date"`
		EndDate   string `form:"end_date" binding:"date"`
		Month     string `form:"month" binding:""`
		Day       string `form:"day" binding:"date"`
	}
	var req RecommandRequest
	if err := c.ShouldBind(&req); err != nil {
		panic(err)
	}
	// ...
	var (
		l, _               = c.Get("lang_util")
		lang               = l.(lang.LangUtil)
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
	)

	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	// 获取配送员新新增客户列表（分页）和 数量
	list := shipperService.RecommendList(admin, req.StartDate, req.EndDate, req.Month, req.Day, req.Limit, req.Page)
	//格式化返回数据
	data := shipperTransformer.FormatRecommendList(list)
	c.JSON(200, gin.H{
		"status": 200,
		"msg":    lang.T("msg"),
		"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		"data":   data,
	})

}

// GetQrRecommend
//
//	@Description: 获取小程序推荐二维码(配送员发展新客户)
//	@author: Captain
//	@Time: 2022-09-05 13:17:38
//	@receiver shipper ShipperController
//	@param c *gin.Context
func (shipper ShipperController) GetQrRecommend(c *gin.Context) {
	var (
		l, _           = c.Get("lang_util")
		lang           = l.(lang.LangUtil)
		shipperService = shipperService.NewShipperService(c)
		//shipperTransformer = shipperTransformer.NewShipperTransformer(c)
	)
	c.JSON(200, gin.H{
		"status": 200,
		"msg":    lang.T("msg"),
		"data":   tools.AddCdn("wechat_mini/img/shipperIntroduce/shipper_indicator_"+lang.Lang+".png"),
		"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
	})
	return
	// 获取配送员ID
	anyAdmin, _ := c.Get("admin")
	shipperer := anyAdmin.(models.Admin)
	// 获取二维码
	if shipperer.RecommendQrcode != "" {
		c.JSON(200, gin.H{
			"status": 200,
			"msg":    lang.T("msg"),
			"data":   configs.MyApp.CdnUrl + shipperer.RecommendQrcode,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	} else {
		result, qrCodePath := shipperService.SaveQrCode(c, shipperer.ID)
		if result {
			c.JSON(200, gin.H{
				"status": 200,
				"msg":    lang.T("msg"),
				"data":   configs.MyApp.CdnUrl + qrCodePath,
				"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
			})
		} else {
			log.Println(qrCodePath)
			shipper.Fail(c, lang.T("error_happend"), 301)
		}

	}
}

// PostScanCommunityCode
//
//	@Description: 配送员进入小区扫二维码
//	@author: rixat
//	@Time: 2024-12-18 13:17:38
//	@receiver shipper ShipperController
//	@param c *gin.Context
func (shipper ShipperController) PostScanCommunityCode(c *gin.Context) {
	type Params struct {
		Code string `form:"code"  binding:"required"` // 考勤id
	}
	var (
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		params   = Params{}
		err      = c.ShouldBind(&params)
		service  = shipperService.NewShipperService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取扫码成功页面信息
	res := service.PostScanCommunityCode(admin,params.Code)
	shipper.Success(c, res, "msg", 200)
}

// GetTip 配送员赞赏列表
func (shipper ShipperController) GetTip(c *gin.Context) {
	var (
		l, _               = c.Get("lang_util")
		lang               = l.(lang.LangUtil)
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
		limit, _           = strconv.Atoi(c.DefaultQuery("limit", "50"))
		page, _            = strconv.Atoi(c.DefaultQuery("page", "1"))
	)
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	tip := shipperService.GetTip(limit, page, admin)
	tipsEntity := shipperTransformer.GetTip(tip)
	if tipsEntity.Tips == nil {
		tipsEntity.Tips = make([]shipperResources.Tips, 0)
	}
	c.JSON(200, gin.H{
		"status": 200,
		"msg":    lang.T("msg"),
		"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		"data": gin.H{
			"items": tipsEntity.Tips,
			"page": gin.H{
				"per_page":     limit,
				"current_page": page,
				"last_page":    page - 1,
			},
		},
	})
}

// PostUpdateReadTime 更新评论读取时间
func (shipper ShipperController) PostUpdateReadTime(c *gin.Context) {
	var (
		l, _           = c.Get("lang_util")
		lang           = l.(lang.LangUtil)
		shipperService = shipperService.NewShipperService(c)
	)
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	shipperService.UpdateReadTime(c, admin)
	c.JSON(200, gin.H{
		"status": 200,
		"msg":    lang.T("msg"),
		"data":   []interface{}{},
		"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
	})
}

// GetShipperCommentList
// 获取客户对配送员的评论列表
// User: Captain
// DateTime: 2022年09月01日 17:31
// /*
func (shipper ShipperController) GetShipperCommentList(c *gin.Context) {
	var (
		l, _               = c.Get("lang_util")
		lang               = l.(lang.LangUtil)
		commentType, _     = strconv.Atoi(c.Query("type"))
		limit, _           = strconv.Atoi(c.DefaultQuery("limit", "10"))
		page, _            = strconv.Atoi(c.DefaultQuery("page", "1"))
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
	)
	// 获取配送员ID
	anyAdmin, _ := c.Get("admin")
	shipperID := anyAdmin.(models.Admin).ID
	shipperInfo := shipperService.GetShipperInfo(shipperID)
	list := shipperService.GetShipperCommentList(shipperID, commentType, limit, page)
	counts := shipperService.GetShipperCommentsCountByType(c, shipperID)
	data := shipperTransformer.FormatCommentList(shipperInfo, list, counts)
	c.JSON(200, gin.H{
		"status": 200,
		"msg":    lang.T("msg"),
		"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		"data":   data,
	})
}

// PostRebindWechat
//
//	@Time 2022-09-03 15:45:00
//	<AUTHOR>
//	@Description: 配送员绑定微信号
//	@receiver shipper
//	@param c
func (shipper ShipperController) PostRebindWechat(c *gin.Context) {
	var (
		l, _           = c.Get("lang_util")
		lang           = l.(lang.LangUtil)
		shipperService = shipperService.NewShipperService(c)
	)
	value, _ := c.Get("admin")
	admin := value.(models.Admin)
	isRebinded, username := shipperService.RebindWechat(c, admin)
	if isRebinded {
		c.JSON(200, gin.H{
			"status": 200,
			"msg":    "باغلانغان ئەزانىڭ ئىسمى:" + username,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	} else {
		shipper.Fail(c, lang.T("rebind_error"), -1000)
	}

}

// PostChangePassword
//
//	@Time 2022-09-03 15:44:49
//	<AUTHOR>
//	@Description: 配送员修改密码
//	@receiver shipper
//	@param c
func (shipper ShipperController) PostChangePassword(c *gin.Context) {
	var (
		l, _           = c.Get("lang_util")
		lang           = l.(lang.LangUtil)
		old_password   = c.PostForm("old_password")
		password       = c.PostForm("password")
		shipperService = shipperService.NewShipperService(c)
	)
	value, _ := c.Get("admin")
	admin := value.(models.Admin)
	changePassword := shipperService.AdminChangePassword(admin, password, old_password)
	if changePassword {
		c.JSON(200, gin.H{
			"status": 200,
			"msg":    lang.T("msg"),
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	} else {
		c.JSON(200, gin.H{
			"status": 200,
			"msg":    lang.T("password_error"),
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	}

}

// GetGrabOrderCount
//
//	@Time 2022-09-03 15:44:38
//	<AUTHOR>
//	@Description: 获取派送员最大能抢的订单数量
//	@receiver shipper
//	@param c
func (shipper ShipperController) GetGrabOrderCount(c *gin.Context) {
	value, _ := c.Get("admin")
	admin := value.(models.Admin)
	grabOrderCount := admin.GrabOrderCount
	data := gin.H{
		"grab_order_count": grabOrderCount,
	}
	shipper.Success(c, data, "msg", 200)
}

// PostTakeOrder
//
//	@Description: 配送员抢订单
//	@author: Captain
//	@Time: 2022-09-08 11:23:12
//	@receiver shipper ShipperController
//	@param c *gin.Context
func (shipper ShipperController) PostTakeOrder(c *gin.Context) {
	var (
		l, _           = c.Get("lang_util")
		lang           = l.(lang.LangUtil)
		OrderID, _     = strconv.Atoi(c.PostForm("order_id"))
		shipperService = shipperService.NewShipperService(c)
	)

	anyAdmin, _ := c.Get("admin")
	shpperer := anyAdmin.(models.Admin)
	rs, msg := shipperService.TakeOrder(c, shpperer, OrderID)
	var data = make([]map[string]interface{}, 0)
	if rs {
		c.JSON(200, gin.H{
			"status": 200,
			"msg":    lang.T("msg"),
			"data":   data,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	} else {
		c.JSON(200, gin.H{
			"status": -1000,
			"msg":    msg,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	}

}

// GetOrderListByStreet
//
//	@Time 2022-09-05 16:24:19
//	<AUTHOR>
//	@Description: 按街道获取订单列表
//	@receiver shipper ShipperController
//	@param c
func (shipper ShipperController) GetOrderListByStreet(c *gin.Context) {
	var (
		shipperService = shipperService.NewShipperService(c)
	)
	value, _ := c.Get("admin")
	admin := value.(models.Admin)
	listByStreet := shipperService.GetListByStreet(c, admin)
	//if tipsEntity.Tips == nil {
	//	tipsEntity.Tips = make([]shipperResources.Tips, 0)
	//}
	if listByStreet == nil {
		listByStreet = []shipperResources.ListByStreet{}
	}
	shipper.Success(c, listByStreet, "msg", 200)

}

// OrderDetail
//
//	@Time 2022-09-07 19:30:00
//	<AUTHOR>
//	@Description: 订单详情
//	@receiver shipper ShipperController
//	@param c
func (shipper ShipperController) OrderDetail(c *gin.Context) {
	var (
		orderId            = c.Query("order_id")
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
	)
	isFounded, orderDetail := shipperService.GetOrderDetail(orderId)
	if isFounded {
		orderDetails := shipperTransformer.FormatOrderDetail(orderDetail)
		shipper.Success(c, orderDetails, "msg", 200)
		return
	} else {
		shipper.Fail(c, "order_not_found", -1000)
		return
	}

}

// GetStatistics
//
//	@Time 2022-09-10 13:43:59
//	<AUTHOR>
//	@Description: 配送员统计
//	@receiver shipper ShipperController
//	@param c
func (shipper ShipperController) GetStatistics(c *gin.Context) {
	var (
		startDate          = c.Query("start_date")
		endDate            = c.Query("end_date")
		month              = c.Query("month")
		day                = c.Query("day")
		value, _           = c.Get("admin")
		admin              = value.(models.Admin)
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
	)
	if startDate == "" && endDate == "" && month == "" && day == "" {
		shipper.Fail(c, "failed", 200)
		return
	} else {
		list := shipperService.OrderStatistics(admin, startDate, endDate, month, day)
		data := shipperTransformer.FormatStatistics(list)
		shipper.Success(c, data, "msg", 200)
	}

}

// PostChangeOrderState
//
//	@Description: 配送员更改订单状态接口
//	@author: Captain
//	@Time: 2022-09-12 13:54:41
//	@receiver shipper ShipperController
//	@param c *gin.Context
func (shipper ShipperController) PostChangeOrderState(c *gin.Context) {
	var (
		// l, _           = c.Get("lang_util")
		// lang           = l.(lang.LangUtil)
		OrderID, _     = strconv.Atoi(c.PostForm("order_id"))
		lat, _         = strconv.ParseFloat(c.DefaultPostForm("lat", "0"), 10)
		lng, _         = strconv.ParseFloat(c.DefaultPostForm("lng", "0"), 10)
		shipperService = shipperService.NewShipperService(c)
	)

	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	if lat == 0 || lng == 0 {
		lat = admin.Lat
		lng = admin.Lng
	}
	rs, msg := shipperService.ChangeOrderState(c, admin.ID, OrderID, admin.Mobile, lat, lng)
	var data = make([]map[string]interface{}, 0)
	if rs {
		c.JSON(200, gin.H{
			"status": 200,
			"msg":    msg,
			"data":   data,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	} else {
		c.JSON(200, gin.H{
			"status": -1000,
			"msg":    msg,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	}
}

// PostBackOrder
//
//	@Description: 配送员退单接口
//	@author: Captain
//	@Time: 2022-09-13 12:54:04
//	@receiver shipper ShipperController
//	@param c *gin.Context
func (shipper ShipperController) PostBackOrder(c *gin.Context) {
	var (
		l, _           = c.Get("lang_util")
		lang           = l.(lang.LangUtil)
		orderID, _     = strconv.Atoi(c.PostForm("order_id"))
		reasonID, _    = strconv.Atoi(c.PostForm("reason_id"))
		shipperService = shipperService.NewShipperService(c)
	)
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	rs, msg := shipperService.BackOrder(c, admin.ID, orderID, reasonID, admin.BackOrderTimeLimit)
	var data = make([]map[string]interface{}, 0)
	if rs {
		c.JSON(200, gin.H{
			"status": 200,
			"msg":    lang.T("msg"),
			"data":   data,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	} else {
		c.JSON(200, gin.H{
			"status": -1000,
			"msg":    msg,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	}
}

// Login
//
//	@Description: 配送员登录
//	@author: Alimjan
//	@Time: 2022-09-23 16:04:44
//	@receiver shipper ShipperController
//	@param c *gin.Context
func (shipper ShipperController) Login(c *gin.Context) {
	var (
		shipperService = shipperService.NewShipperService(c)
		name           = c.PostForm("name")
		password       = c.PostForm("password")
		clientId       = c.PostForm("client_id")
		clientSecret   = c.PostForm("client_secret")
		grantType      = c.PostForm("grant_type")
		searialNumber  = c.GetHeader("searialNumber")
	)
	// 前后清空
	name = strings.TrimSpace(name)
	success, admin, errMsg := shipperService.LoginCheck(name, password, clientId, clientSecret, grantType)
	if success {
		//tokenMap := shipperService.GenerateToken(admin, clientId, searialNumber)
		//token := tokenMap["access_token"].(string)
		token,jwtSerialNumber := tools.NewJWTTools().GenerateToken(map[string]interface{}{
			"id": admin.ID,
			"name": admin.Name,
			"mobile": admin.Mobile,
			"type": admin.Type,
			"grant_type": grantType,
			"searialNumber": searialNumber,
		})
		tools.Db.Model(&models.Admin{}).Where("id = ?", admin.ID).Update("jwt_serial_number", jwtSerialNumber)
		shipperType := "SHIPPER"
		if admin.Type == 8 {
			shipperType = "SHIPPER_ADMIN"
		}
		avatar := admin.Avatar
		if len(avatar) > 0 {
			avatar = strings.TrimRight(configs.MyApp.CdnUrl, "/") + admin.Avatar
		}
		loginEntity := shipperResources.ShipperLoginEntity{
			Tokens: shipperResources.ShipperLoginEntityTokens{
				AccessToken:  token,
				TokenType:    "Bearer",
				ExpiresIn:    31536000,
				RefreshToken: "",
			},
			Admin: shipperResources.ShipperLoginEntityAdmin{
				ID:     admin.ID,
				Name:   admin.Name,
				Mobile: admin.Mobile,
				Type:   shipperType,
				Avatar: avatar,
				AttendanceState: shipperService.GetShipperAttendanceState(admin.ID),
			},
		}
		shipper.Success(c, loginEntity, "msg", 200)
	} else {
		shipper.Fail(c, errMsg, -1000)
	}
	//shipper.Success(c,[],"msg",)
}

// GetUpdateProfile
//
//	@Description: 获取配送员详细信息
//	@author: Alimjan
//	@Time: 2022-09-16 11:12:24
//	@receiver shipper ShipperController
//	@param context *gin.Context
func (shipper ShipperController) GetUpdateProfile(c *gin.Context) {
	var (
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
	)
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	info := shipperService.GetShipperProfile(admin)
	data := shipperTransformer.GetShipperProfile(info)
	shipper.Success(c, data, "msg", 200)
}

// PostUpdateProfile
//
//	@Description: 更新配送员资料
//	@author: Alimjan
//	@Time: 2022-09-16 11:38:22
//	@receiver shipper ShipperController
//	@param context *gin.Context
func (shipper ShipperController) PostUpdateProfile(c *gin.Context) {
	var (
		shipperService = shipperService.NewShipperService(c)
		realName       = c.PostForm("real_name")
		avatarFile, _  = c.FormFile("avatar")
	)

	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	_, err, avatar := shipperService.SaveShipperAvatar(c, avatarFile)
	if err != nil {
		panic("出错了")
	}
	shipperService.SaveInfo(admin, realName, avatar)
	avatarUrl := configs.MyApp.CdnUrl + avatar
	shipper.Success(c, map[string]interface{}{"avatar_url": avatarUrl, "avatar_path": avatar}, "msg", 200)
}

// RankList
//
//	@Description: 获取配送员排行榜 web 页面使用
//	@author: Alimjan
//	@Time: 2022-09-16 16:29:01
//	@receiver shipper ShipperController
//	@param c *gin.Context
func (shipper ShipperController) RankList(c *gin.Context) {
	//validate
	type RankRequest struct {
		Type   int `form:"type" binding:"required"`
		UserId int `form:"user_id" binding:""`
	}
	// ...
	var (
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
	)
	var req RankRequest
	if err := c.ShouldBind(&req); err != nil {
		panic(err)
	}
	cacheDuration := 15 * time.Minute
	var cacheKey = fmt.Sprintf("shipper_rank_%d", req.Type)
	rankListJson := tools.Remember(c, cacheKey, cacheDuration, func() interface{} {
		rankList := shipperService.GetRankList(req.Type)
		return rankList
	})
	var rankList []map[string]interface{}
	_ = json.Unmarshal([]byte(rankListJson), &rankList)
	var cacheChampionKey = "shipper_rank_chaption_all"
	chaptionJson := tools.Remember(c, cacheChampionKey, cacheDuration, func() interface{} {
		return shipperService.GetChampion()
	})
	var chaption map[string]interface{}
	_ = json.Unmarshal([]byte(chaptionJson), &chaption)
	result := shipperTransformer.Transform(int64(req.UserId), rankList, chaption)
	shipper.Success(c, result, "msg", 200)
}

// GetStatisticDetail
//
//	@Time 2022-09-13 00:21:49
//	<AUTHOR>
//	@Description: 订单统计详细
//	@receiver shipper ShipperController
//	@param c
func (shipper ShipperController) GetStatisticDetail(c *gin.Context) {
	var (
		startDate          = c.Query("start_date")
		endDate            = c.Query("end_date")
		month              = c.Query("month")
		day                = c.Query("day")
		state              = c.Query("state")
		consumeType        = c.Query("consume_type")
		limit, _           = strconv.Atoi(c.DefaultQuery("limit", "10"))
		page, _            = strconv.Atoi(c.DefaultQuery("page", "1"))
		value, _           = c.Get("admin")
		admin              = value.(models.Admin)
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
	)
	detail := shipperService.StatisticsDetail(admin, 1, startDate, endDate, month, day, state, consumeType, limit, page)
	details := shipperTransformer.FormatStatisticsDetails(detail, page, limit)
	shipper.Success(c, details, "msg", 200)
}

// GetAdminOrderList
//
//	@Time 2022-09-14 22:53:48
//	<AUTHOR>
//	@Description: 显示配送员订单列表
//	@receiver shipper ShipperController
//	@param c
func (shipper ShipperController) GetAdminOrderList(c *gin.Context) {
	var (
		adminId, _         = strconv.Atoi(c.Query("admin_id"))
		value, _           = c.Get("admin")
		admin              = value.(models.Admin)
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
	)
	list := shipperService.GetAdminOrderList(adminId)
	orderList := shipperTransformer.FormatAdminOrderList(list, admin)
	shipper.Success(c, orderList, "msg", 200)
}

// CloseAccount
//
//	@Time 2022-09-16 17:48:50
//	<AUTHOR>
//	@Description: 更新商家阅读评论时间
//	@receiver shipper ShipperController
//	@param c
func (shipper ShipperController) CloseAccount(c *gin.Context) {
	var (
		shipperService = shipperService.NewShipperService(c)
	)
	admin := permissions.GetAdmin(c)
	shipperService.CloseAccount(admin)
	shipper.Success(c, "", "msg", 200)
}

// GetCheckOut
//
//	@Time 2022-09-21 10:10:23
//	<AUTHOR>
//	@Description:
//	@receiver shipper ShipperController
//	@param c
func (shipper ShipperController) GetCheckOut(c *gin.Context) {
	var (
		date               = c.Query("date")
		value, _           = c.Get("admin")
		admin              = value.(models.Admin)
		shipperService     = shipperService.NewShipperService(c)
		shipperTransformer = shipperTransformer.NewShipperTransformer(c)
	)
	list := shipperService.GetCheckOut(date, admin.ID)
	data := shipperTransformer.FormatCheckOut(list, date)
	shipper.Success(c, data, "msg", 200)

}

// GetAttendanceInfo
//
//	@Time 2022-12-31 15:25:21
//	<AUTHOR>
//	@Description: 获取考勤信息
//	@receiver shipper ShipperController
//	@param c
func (shipper ShipperController) GetAttendanceInfo(c *gin.Context) {
	var (
		value, _    = c.Get("admin")
		admin       = value.(models.Admin)
		service     = shipperService.NewShipperService(c)
		transformer = shipperTransformer.NewShipperTransformer(c)
	)
	currentState, nextState, todayHistoryStates := service.GetAttendanceInfo(admin)
	data := transformer.FormatAttendanceInfo(currentState, nextState, todayHistoryStates)
	shipper.Success(c, data, "msg", 200)
}

// PostAttendance
//
//	@Time 2022-12-31 18:00:32
//	<AUTHOR>
//	@Description: 配送员打卡
//	@receiver shipper ShipperController
//	@param c
func (shipper ShipperController) PostAttendance(c *gin.Context) {
	type Params struct {
		AttendanceId string `form:"attendance_id"  binding:"required"` // 考勤id
		Lng          string `form:"lng"`           // 经度
		Lat          string `form:"lat"`           // 纬度
		Position     string `form:"position" binding:"required"`       // 位置
		Image     string `form:"image" binding:"required"`       // 位置
		AdminId      int    `  form:"admin_id" `                       // 配送员id
	}
	var (
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		params   = Params{}
		err      = c.ShouldBind(&params)
		service  = shipperService.NewShipperService(c)
	)
	//判断经纬度是否为空
	if params.Lng == "" || params.Lat == "" {
		shipper.Fail(c, "lng_or_lat_is_empty", -1000)
		return
	}
	if err != nil {
		panic(err)
	}
	ok, msg, _ := service.PostAttendance(admin, params.AttendanceId, params.Lat, params.Lng, params.Position, params.AdminId,params.Image)
	if ok {
		// 打卡成功
		shipper.Ok(c)
	} else {
		//打卡失败
		shipper.Fail(c, msg, -1000)
	}
}

// GetAttendanceList
//
//	@Time 2022-12-31 19:22:19
//	<AUTHOR>
//	@Description: 获取考勤列表
//	@receiver shipper ShipperController
//	@param c
func (shipper ShipperController) GetAttendanceList(c *gin.Context) {
	var (
		value, _    = c.Get("admin")
		admin       = value.(models.Admin)
		service     = shipperService.NewShipperService(c)
		transformer = shipperTransformer.NewShipperTransformer(c)
	)
	data, currentEndDate, nextEndDate := service.GetAttendanceList(c, admin)
	result := transformer.FormatAttendanceList(c, data, currentEndDate, nextEndDate)
	shipper.Success(c, result, "msg", 200)
}

// PostGrabOrder
//
//	@Time 2023-09-06 12:17:00
//	<AUTHOR>
//	@Description: 配送员抢单 新版
//	@receiver shipper ShipperController
//	@param c
func (shipper ShipperController) PostGrabOrder(c *gin.Context) {
	var (
		l, _           = c.Get("lang_util")
		lang           = l.(lang.LangUtil)
		OrderID, _     = strconv.Atoi(c.PostForm("order_id"))
		shipperService = shipperService.NewShipperService(c)
	)

	anyAdmin, _ := c.Get("admin")
	shpperer := anyAdmin.(models.Admin)
	rs, msg := shipperService.GrabOrder(c, shpperer, OrderID,constants.TakeOrderChannelShipper)
	var data = make([]map[string]interface{}, 0)
	if 200 == rs {
		c.JSON(200, gin.H{
			"status": 200,
			"msg":    lang.T("msg"),
			"data":   data,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	} else {
		c.JSON(200, gin.H{
			"status": rs,
			"msg":    msg,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	}

}

// AutoDispatchSetShipper
//
// @Description: 智能派单订单设置配送员
// @Author: Rixat
// @Time: 2024-08-19 12:41:51
// @receiver 
// @param c *gin.Context
func (shipper ShipperController) AutoDispatchSetShipper(c *gin.Context) {
	type Params struct {
		OrderID        int    `form:"order_id" binding:"required"`  // 订单号
		ShipperID      int    `form:"shipper_id" binding:"required"`  // 配送员ID
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		shipperService = shipperService.NewShipperService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	var shipperModel models.Admin
	tools.Db.Model(shipperModel).Where("id=? and state=1 and deleted_at is null",params.ShipperID).Scan(&shipperModel)
	rs, msg := shipperService.GrabOrder(c, shipperModel, params.OrderID,constants.TakeOrderChannelAutoDispatch)
	if rs != 200 {
		shipper.Fail(c,msg,rs)
		return
		
	}
	// 配送端发送分配推送推送
	jobs.SendOrderAssignedPushByOrderID(params.OrderID,constants.SoundShipperSocketTypeSystemAssignOrder)

	shipper.Ok(c)
}


// 描述：获取配送员取消的订单历史
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2023/09/25 17:54
func (shipper ShipperController) GetCanceledOrder(c *gin.Context) {
	var (
		l, _        = c.Get("lang_util")
		lang        = l.(lang.LangUtil)
		anyAdmin, _ = c.Get("admin")
		shpperer    = anyAdmin.(models.Admin)
	)

	type Params struct {
		BeginTime string `form:"begin_time" binding:"required"`
		EndTime   string `form:"end_time" binding:"required"`
	}

	var request Params

	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	shipperService := shipperService.NewShipperService(c)
	transformer := shipperTransformer.NewShipperTransformer(c)
	data, count, lessAmount, err := shipperService.CanceledOrder(c, shpperer, request.BeginTime, request.EndTime)
	orderList := transformer.FormatCanceledOrder(data)
	if err != nil {
		shipper.Fail(c, err.Error(), http.StatusBadRequest)
		return
	}
	shipper.Success(c, gin.H{
		"count": count,
		"money": lessAmount,
		"list":  orderList,
	}, lang.T("msg"), 200)
}

//检查服务可用性
func (s ShipperController) CheckServer(c *gin.Context) {
	s.Success(c,nil,"",200)
}

// 描述：发送短信验证码、ps：世界
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2023/09/27 16:17
func (s ShipperController) PostSmsCode(c *gin.Context) {
	type Params struct {
		Mobile string `form:"mobile" binding:"required"`
	}
	var (
		redisHelper = tools.GetRedisHelper()
		params      Params
		err         = c.ShouldBind(&params)
	)

	if !tools.VerifyMobileFormat(params.Mobile) {
		s.Fail(c, "mobile_incorrect", -1000)
		return
	}

	service := shipperService.NewShipperService(c)
	_, err = service.CheckSMSLogin(params.Mobile)
	if err != nil {
		s.Fail(c, err.Error(), -1000)
		return
	}

	cacheKey := fmt.Sprintf("send_mobile_%s", params.Mobile)
	//2分钟 能获取一次
	exists, _ := redisHelper.Exists(c, cacheKey).Result()
	if exists == 0 {
		redisHelper.Set(c, cacheKey, params.Mobile, 2*time.Minute)
	} else {
		s.Fail(c, "sms_in_two_minutes", -1000)
		return
	}

	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))
	code := fmt.Sprintf("%04v", rnd.Int31n(9999))

	cacheKey = fmt.Sprintf("shipper_mobile_%s", params.Mobile)

	redisHelper.Set(c, cacheKey, code, 30*time.Minute) //验证码保存 30 分钟

	//contentMap := map[string]string{
	//	"code": code,
	//}
	//dataType, _ := json.Marshal(contentMap)
	//content := string(dataType)
	//
	//err = tools.AliSmsSend(params.Mobile, string(content))
	//if err != nil {
	//	panic(err)
	//}
	// 发送验证码
	smsServiceFactory := &factory.SmsServiceFactory{}
	smsService := smsServiceFactory.CreateSmsService()
	codeResp, err := smsService.SendVerificationCode(params.Mobile, code)
	if err != nil {
		tools.Logger.Errorf("发送验证码失败: %v \n",err)
		panic(err)
		return
	}
	tools.Logger.Infof("发送验证码成功 结果: %v \n",codeResp)
	s.Ok(c)
}

// 描述：验证码登录
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2023/09/27 17:29
func (s ShipperController) PostSMSLogin(c *gin.Context) {
	type Params struct {
		Mobile        string `form:"mobile" binding:"required"`
		SMSCode       string `form:"code" binding:"required"`
		ClientId      string `form:"client_id" binding:"required"`
		SearialNumber string `form:"searial_number" binding:"required"`
	}
	var (
		redisHelper = tools.GetRedisHelper()
		params      Params
		err         = c.ShouldBind(&params)
	)

	if err != nil {
		panic(err)
	}

	if !tools.VerifyMobileFormat(params.Mobile) {
		s.Fail(c, "mobile_incorrect", -1000)
		return
	}
	cacheKey := fmt.Sprintf("shipper_mobile_%s", params.Mobile)
	get := redisHelper.Get(c, cacheKey)
	if get.Val() != params.SMSCode {
		s.Fail(c, "captcha_incorrect", -1000)
		return
	}

	service := shipperService.NewShipperService(c)
	admin, err := service.CheckSMSLogin(params.Mobile)
	if err != nil {
		s.Fail(c, err.Error(), -1000)
		return
	}

	tokenStr,jwtSerialNumber := tools.NewJWTTools().GenerateToken(map[string]interface{}{
		"id": admin.ID,
		"name": admin.Name,
		"mobile": admin.Mobile,
		"type": admin.Type,
		//"grant_type": grantType,
		"searialNumber": params.SearialNumber,
		"area_id": admin.AdminAreaID,
	})
	tools.Db.Model(&models.Admin{}).Where("id = ?", admin.ID).Debug().Update("jwt_serial_number", jwtSerialNumber)


	shipperType := "SHIPPER"
	if admin.Type == 8 {
		shipperType = "SHIPPER_ADMIN"
	}

	loginEntity := shipperResources.ShipperLoginEntity{
		Tokens: shipperResources.ShipperLoginEntityTokens{
			AccessToken:  tokenStr,
			TokenType:    "Bearer",
			ExpiresIn:    31536000,
			RefreshToken: "",
		},
		Admin: shipperResources.ShipperLoginEntityAdmin{
			ID:     admin.ID,
			Name:   admin.Name,
			Mobile: admin.Mobile,
			Type:   shipperType,
		},
	}
	s.Success(c, loginEntity, "msg", 200)
}

// 描述：验证手机验证
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2023/09/28 14:02
func (s ShipperController) VerifySMSCode(c *gin.Context) {
	type Params struct {
		Mobile  string `form:"mobile" binding:"required,len=11"`
		SMSCode string `form:"code" binding:"required,len=4"`
	}
	var (
		redisHelper = tools.GetRedisHelper()
		params      Params
		err         = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
	}

	if !tools.VerifyMobileFormat(params.Mobile) {
		s.Fail(c, "mobile_incorrect", -1000)
		return
	}

	cacheKey := fmt.Sprintf("shipper_mobile_%s", params.Mobile)
	get := redisHelper.Get(c, cacheKey)
	if get.Val() != params.SMSCode {
		s.Fail(c, "captcha_incorrect", -1000)
		return
	}
	s.Success(c, nil, "msg", http.StatusOK)
}

// 描述：配送员找回密码
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2023/09/28 11:20
func (s ShipperController) RecoverPassword(c *gin.Context) {
	type RecoverParams struct {
		Mobile      string `form:"mobile" binding:"required,len=11"`
		NewPassword string `form:"new_password" binding:"required,gte=8"`
	}

	var request RecoverParams
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	service := shipperService.NewShipperService(c)
	if err := service.RecoverPassword(request.Mobile, request.NewPassword); err != nil {
		s.Fail(c, err.Error(), -1000)
		return
	}

	s.Success(c, nil, "msg", 200)
}



// 描述：获取超时的订单列表
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2023/10/07 17:59
func (s ShipperController) GetOverTimeOrders(c *gin.Context) {
	var (
		l, _        = c.Get("lang_util")
		lang        = l.(lang.LangUtil)
		anyAdmin, _ = c.Get("admin")
		shipper     = anyAdmin.(models.Admin)
	)

	type Params struct {
		BeginTime string `form:"begin_time" binding:"required"`
		EndTime   string `form:"end_time" binding:"required"`
	}

	var request Params

	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	shipperService := shipperService.NewShipperService(c)
	data, count, amount, err := shipperService.OverTimeOrders(c, shipper, request.BeginTime, request.EndTime)
	shipperTransformer := shipperTransformer.NewShipperTransformer(c)
	list := shipperTransformer.FormatOverTimeOrder(data)

	if err != nil {
		s.Fail(c, err.Error(), http.StatusBadRequest)
		return
	}
	s.Success(c, gin.H{
		"count": count,
		"money": amount,
		"list":  list,
	}, lang.T("msg"), 200)

}

// 描述：帮助列表
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2023/10/10 13:04
func (s ShipperController) GetHelpList(c *gin.Context) {
	shipperService := shipperService.NewShipperService(c)
	list, err := shipperService.HelpList(c)
	if err != nil {
		s.Fail(c, err.Error(), http.StatusBadRequest)
		return
	}
	s.Success(c, list, "msg", http.StatusOK)
}

// 描述：获取帮助详情
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2023/10/10 13:18
func (s ShipperController) GetHelpDetail(c *gin.Context) {
	type Params struct {
		ID int64 `form:"id" binding:"required"`
	}

	var request Params

	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	shipperService := shipperService.NewShipperService(c)
	detail, err := shipperService.HelpDetail(request.ID)
	if err != nil {
		s.Fail(c, err.Error(), http.StatusBadRequest)
		return
	}
	s.Success(c, detail, "msg", http.StatusOK)
}

// 描述：隐私协议列表
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2023/10/10 18:04
func (s ShipperController) GetPrivacyList(c *gin.Context) {
	shipperService := shipperService.NewShipperService(c)
	list, err := shipperService.PrivacyList(c, 2)
	if err != nil {
		s.Fail(c, err.Error(), http.StatusBadRequest)
		return
	}
	s.Success(c, list, "msg", http.StatusOK)
}

// GetNotify
//
// @Description: 配送端首页通知信息
// @Author: Rixat
// @Time: 2023-10-23 10:34:00
// @receiver
// @param c *gin.Context
func (s ShipperController) GetNotify(c *gin.Context) {
	var (
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
	)
	shipperService := shipperService.NewShipperService(c)
	res := shipperService.GetHomeNotify(admin)
	s.Success(c, res, "msg", 200)
}

// PostUploadImage
//
// @Description: 配送端通用上传图片
// @Author: Rixat
// @Time: 2023-10-23 10:34:00
// @receiver
// @param c *gin.Context
func (s ShipperController) PostUploadImage(c *gin.Context) {
	typeName := c.PostForm("type_name")
	file, err := c.FormFile("image")
	if err != nil {
		s.Fail(c, "无法获取上传文件", http.StatusBadRequest)
		return
	}

	ext := filepath.Ext(file.Filename)

	if ext != ".jpg" && ext != ".jpeg" && ext != ".png" {
		s.Fail(c, "只允许jpg|jpeg|png类型图片", http.StatusBadRequest)
		return
	}

	// 临时的
	fileName := s.generateRandomFilename() + ext
	rootDir := configs.MyApp.UploadRootDir
	imageDir := "uploads/" + typeName
	if _, err := os.Stat(rootDir + imageDir); os.IsNotExist(err) {
		// 目录不存在，创建目录
		err := os.MkdirAll(rootDir+imageDir, 0755)
		if err != nil {
			// 创建目录失败
			fmt.Println("目录创建失败:", err)
			return
		}
	}
	imagePath := imageDir + "/" + fileName
	fullPath := rootDir + imagePath
	if err := c.SaveUploadedFile(file, fullPath); err != nil {
		s.Fail(c, "上传保存失败", http.StatusBadRequest)
		return
	}

	fileUrl := configs.MyApp.CdnUrl + imagePath

	s.Success(c, map[string]interface{}{
		"url":        fileUrl,
		"image_path": imagePath,
	}, "上传成功", http.StatusOK)
}

// 生成随机图片名称
func (s ShipperController) generateRandomFilename() string {
	// 生成当前时间的字符串形式
	timestamp := time.Now().Format("20060102150405")
	// 生成五位随机数字（00000 - 99999）
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Intn(100000)
	randomString := strconv.Itoa(randomNumber)
	paddedRandomString := fmt.Sprintf("%05s", randomString)
	// 将随机数字追加到时间戳后面
	return timestamp + paddedRandomString
}

// ShipperArrivedShop 已到餐厅
func (shipper ShipperController) ShipperArrivedShop(c *gin.Context) {
	var (
		l, _           = c.Get("lang_util")
		lang           = l.(lang.LangUtil)
		OrderID, _     = strconv.Atoi(c.PostForm("order_id"))
		lat, _         = strconv.ParseFloat(c.DefaultPostForm("lat", "0"), 64)
		lng, _         = strconv.ParseFloat(c.DefaultPostForm("lng", "0"), 64)
		shipperService = shipperService.NewShipperService(c)
		opType         = c.DefaultPostForm("op_type", "come")
	)

	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	if lat == 0 || lng == 0 {
		lat = admin.Lat
		lng = admin.Lng
	}
	rs, msg := shipperService.ShipperArrivedAtShop(c, admin.ID, OrderID, lat, lng, opType)
	var data = make([]map[string]interface{}, 0)
	if rs {
		c.JSON(200, gin.H{
			"status": 200,
			"msg":    lang.T("msg"),
			"data":   data,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	} else {
		c.JSON(200, gin.H{
			"status": -1000,
			"msg":    msg,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	}
}

// 描述：获取配送管理员手下的配送员列表
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2023/10/10 18:04
func (s ShipperController) GetAdminList(c *gin.Context) {
	orderId := c.Query("order_id")
	if orderId == "" {
		s.Fail(c, "order_id 不能为空", http.StatusBadRequest)
		return
	}

	orderID, err := strconv.ParseInt(orderId, 10, 64)
	if err != nil {
		s.Fail(c, "order_id 必须为数字", http.StatusBadRequest)
		return
	}

	var admin models.Admin
	anyAdmin, _ := c.Get("admin")
	admin = anyAdmin.(models.Admin)

	shipperService := shipperService.NewShipperService(c)
	admin_list, err := shipperService.GetAdminList(admin, orderID)
	shipperTransformer := shipperTransformer.NewShipperTransformer(c)
	list := shipperTransformer.FormatAdminList(admin_list)
	if err != nil {
		s.Fail(c, err.Error(), http.StatusBadRequest)
		return
	}

	s.Success(c, list, "msg", http.StatusOK)

}

// GetAllShipperList
//
//	@Description: 获取配送管理员手下的配送员列表
//	@receiver shipper
//	@param context
func (s ShipperController) GetAllShipperList(c *gin.Context) {
	var admin models.Admin
	anyAdmin, _ := c.Get("admin")
	admin = anyAdmin.(models.Admin)

	shipperService := shipperService.NewShipperService(c)
	admin_list, err := shipperService.GetAllShipperList(admin)
	if err != nil {
		s.Fail(c, err.Error(), http.StatusBadRequest)
		tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
		return
	}
	shipperTransformer := shipperTransformer.NewShipperTransformer(c)
	list := shipperTransformer.FormatAllShipperList(admin_list)

	s.Success(c, list, "msg", http.StatusOK)
}

// 描述：检查App黑名单
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2024/07/16 16:10
func (s ShipperController) CheckAppBlackList(c *gin.Context) {
	type Params struct {
		Packages string `form:"packages" `
	}
	var request Params
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}
	var admin models.Admin
	anyAdmin, _ := c.Get("admin")
	admin = anyAdmin.(models.Admin)

	shipperService := shipperService.NewShipperService(c)
	appList,packageList, err := shipperService.GetAppBlackList(admin,request.Packages)
	if err != nil {
		s.Fail(c, err.Error(), -1000)
		return
	}
	// 如果存在不合规app就把app名称添加到数组里、然后返回所有的不合规app名称
	if len(packageList) > 0 {
		var packageNames []string
		for _, pkg := range packageList {
			packageNames = append(packageNames, pkg.AppName)
		}
		tools.ImportantLog("shipper-black-list-app",admin.ID,admin.ID,0,fmt.Sprintf("查询APP黑名单: userid :%d , 出发事件的应用列表:%s", admin.ID, strings.Join(packageNames, ", ")))
	}

	s.Success(c,appList,"msg", http.StatusOK)
}


type TTSToken struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int64  `json:"expires_in"`
}

type MtResponse struct {
	RequestId string `json:"requestId"`
	Code      int    `json:"code"`
	Message   string `json:"message"`
	Value     string `json:"value"`
	Success   bool   `json:"success"`
}

// 定义请求接口
const (
	tokenURL     = "https://open.xjguoyu.cn/api/auth/oauth/token"
	translateURL = "https://open.xjguoyu.cn/api/core/1.0/mt?appKey=6wHJwrPupa9oU9MsHUXsuRnUYOrcazTc"
	ttsURL       = "https://open.xjguoyu.cn/api/core/1.0/tts?appKey=8TrbcyVuUoh1iQfK52bChAMhxdHKwYGx"
)

// 描述：文本转换音频文件
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2024/07/18 18:58
func (s ShipperController) TextToSpeech(c *gin.Context) {
	type Params struct {
		Text string `form:"text" binding:"required"`
	}
	var request Params
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}
	// 获取 三剑客开放平台 API Token
	// 网址：https://open.xjguoyu.cn/#/docs/openApi
	token, err := s.getTTSToken(c)
	if err != nil {
		s.Fail(c, "无法获取token", -1000)
		tools.Logger.Error("tts:语音转换token 获取失败", err)
		return
	}

	// 翻译中文见到维吾尔文
	text, err := s.translateText(token, request.Text)
	if err != nil || text == "" {
		s.Fail(c, "翻译文本出错了", -1000)
		tools.Logger.Error("tts:翻译文本出错了", err)
		return
	}

	// 维吾尔文转换音频文件
	voice, err := s.tts(token, text)
	if err != nil || voice == "" {
		s.Fail(c, "文本转换语音出错了", -1000)
		tools.Logger.Error("tts:文本转换语音出错了", err)
		return
	}

	s.Success(c, voice, "请求成功", http.StatusOK)
}

// 描述：获取api接口token
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2024/07/18 18:57
func (s ShipperController) getTTSToken(c *gin.Context) (string, error) {
	redisHelper :=tools.GetRedisHelper()
	// 如果缓存中存在token直接返回token
	token , _ := redisHelper.Get(context.Background() ,"tts_access_token").Result()
	if token != ""{
		return token, nil
	}
	reqURL := fmt.Sprintf("%s?grant_type=client_credentials&client_id=93c3b0e24a4c5335aadc35df64fae8f0&secret=OWsUAhrBOMZonfGqnLchIAPAcKSYGIZz", tokenURL)
	resp, err := s.httpClient().Get(reqURL)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var tokens TTSToken
	if err := json.NewDecoder(resp.Body).Decode(&tokens); err != nil {
		return "", err
	}

	// 缓存token 2小时
	redisHelper.Set(context.Background(),"tts_access_token", tokens.AccessToken,60 * 60 * 2 * time.Second)
	
	return tokens.AccessToken, nil
}

// 描述：翻译中文文本到维吾尔语
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2024/07/18 18:56
func (s ShipperController) translateText(token string, text string) (string, error) {
	reqBody := map[string]interface{}{
		"text": text,
		"src":  "cn",
		"to":   "ug",
	}
	return s.PostRequest(translateURL, token, reqBody)
}

// 描述：文本转换语音文件
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2024/07/18 18:56
func (s ShipperController) tts(token string, text string) (string, error) {
	reqBody := map[string]interface{}{
		"text":    text,
		"lang":    "ug",
		"speaker": "xiringul",
		"format":  "wav",
	}
	return s.PostRequest(ttsURL, token, reqBody)
}

// 描述：发送post请求
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2024/07/18 18:55
func (s ShipperController) PostRequest(url string, token string, body interface{}) (string, error) {
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return "", err
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	resp, err := s.httpClient().Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var data MtResponse
	if err := json.NewDecoder(resp.Body).Decode(&data); err != nil {
		return "", err
	}

	return data.Value, nil
}
//创建一个http客户端
func (s ShipperController) httpClient() *http.Client {
	return &http.Client{}
}

// 描述：获取所有黑名单中的app
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2024/07/18 18:11
func (s ShipperController) GetBlackListApps(c *gin.Context){
	shipperService := shipperService.NewShipperService(c)
	appList, err := shipperService.GetBlackListApps()
	if err != nil {
		s.Fail(c, err.Error(), -1000)
		return
	}
	s.Success(c,appList,"msg", http.StatusOK)
}
//
// CustomerAddress
//  @Description: 获取客户地址
//  @receiver shipper
//  @param c
//
func (shipper ShipperController) CustomerAddress(c *gin.Context) {
	request := ShipperRequest.OrderCustomerAddRequest{}
	err     := c.ShouldBind(&request)
	if err != nil {
		panic(err)
		return
	}
	shipperService := shipperService.NewShipperService(c)
	shipperTransformer := shipperTransformer.NewShipperTransformer(c)
	order,userBuildings, err := shipperService.GetCustomerAddress(request.OrderId)
	customerAddressList := shipperTransformer.GetCustomerAddress(order,userBuildings)
	if err != nil {
		shipper.Fail(c, err.Error(), -1000)
		return
	}
	shipper.Success(c, customerAddressList, "msg", http.StatusOK)
}

// 描述：配送员申请修改订单配送时间
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2024/08/26 18:05
func(shipper *ShipperController) PostApplyModifyBookingTime(c *gin.Context){
	type Params struct {
		OrderID 		string 	`form:"order_id" binding:"required"`
		DelayedDuration int 	`form:"delayed_duration" binding:"required"`
		ReasonDelay		string 	`form:"reason_delay" binding:"required"`
		ReasonId int 	`form:"reason_id" `
	}
	var (
		request Params
		admin models.Admin
	) 	
	anyAdmin, _ := c.Get("admin")
	admin = anyAdmin.(models.Admin)

	if err := c.ShouldBind(&request); err != nil {
		shipper.Fail(c,err.Error(), -1000)
		return
	}

	shipperService := shipperService.NewShipperService(c)
	err := shipperService.ApplyModifyBookingTime(admin, request.OrderID, request.DelayedDuration, request.ReasonDelay,request.ReasonId)
	if err != nil {
		shipper.Fail(c,err.Error(), -1000)
		return
	}
	shipper.Success(c, nil, "msg",200)
}
//获取配送时间延长记录
func(shipper *ShipperController) GetApplyModifyBookingTime(c *gin.Context){
	type Params struct {
		OrderID 		string 	`form:"order_id" binding:"required"`
	}
	var (
		request Params
		admin models.Admin
	) 	
	anyAdmin, _ := c.Get("admin")
	admin = anyAdmin.(models.Admin)

	if err := c.ShouldBind(&request); err != nil {
		shipper.Fail(c,err.Error(), -1000)
		return
	}

	shipperService := shipperService.NewShipperService(c)
	msg,err := shipperService.GetModifyBookingTime(admin, request.OrderID)
	if err != nil {
		shipper.Fail(c,err.Error(), -1000)
		return
	}
	shipper.Success(c, msg, "msg",200)
}

// 描述：延时理由列表
// 作者：Qurbanjan
// 文件：ShipperController.go
// 修改时间：2024/08/27 15:44
func (shipper *ShipperController) GetReasonDelayTags(c *gin.Context){
	var (
		l, _           = c.Get("lang_util")
		lang           = l.(lang.LangUtil)
	)
	shipper.Success(c, lang.TArrMap("delay_reason_tags"),"msg", 200)
}
//获取配置
func (shipper ShipperController) GetConfig(c *gin.Context) {

	shipperService := shipperService.NewShipperService(c)
	keys,ok := c.GetQuery("keys")
	brand :=""
	if c.GetHeader("Device-Brande") != ""{
		brand =tools.ToString(c.GetHeader("Device-Brande"))
	}
	deviceAppVersion := int64(0)
	if c.GetHeader("Version-Name") != ""{
		version2 :=tools.ToInt64(tools.ReplaceString(c.GetHeader("Version-Name"),".",""))
		if version2 > 0 {
			deviceAppVersion = version2
		}
	}
	if ok {
		info := shipperService.GetConfig(keys,brand,deviceAppVersion)
		shipper.Success(c, info , "msg", 200)
	}else{
		shipper.Success(c, map[int]int{}, "msg", 200)
	}

}