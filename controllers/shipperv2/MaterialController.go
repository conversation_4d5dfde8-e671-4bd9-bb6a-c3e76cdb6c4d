package shipperv2

import (
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"

	"mulazim-api/services"
	"mulazim-api/transformers/shipperv2"

	"github.com/gin-gonic/gin"
)

type MaterialController struct {
	controllers.BaseController
}

// Desc: 领取 宣传单
// Author: rozi mamat
// Date: 2024-02-20
func (m *MaterialController) Take(c *gin.Context) {

	type ScanRequest struct {
		StartCode string `form:"start_code" json:"start_code" binding:"required"`
		EndCode   string `form:"end_code" json:"end_code"`
	}
	var (
		l, _            = c.Get("lang_util")
		langUtil        = l.(lang.LangUtil)
		materialService = services.NewMaterialService(langUtil, langUtil.Lang)
	)
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	var request ScanRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
		return
	}
	_, err := materialService.MaterialTake(admin, request.StartCode, request.EndCode)
	if err != nil {
		m.Fail(c, err.Error(), -1000)
		return
	}
	m.Success(c, nil, "msg", 200)

}

// Desc:  宣传单 内容获取
// Author: rozi mamat
// Date: 2024-02-20
func (m *MaterialController) Scan(c *gin.Context) {

	type ScanRequest struct {
		StartCode string `form:"start_code" json:"start_code" binding:"required"`
		EndCode   string `form:"end_code" json:"end_code"`
	}
	var (
		l, _            = c.Get("lang_util")
		langUtil        = l.(lang.LangUtil)
		materialService = services.NewMaterialService(langUtil, langUtil.Lang)
	)
	var request ScanRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
		return
	}
	result, err := materialService.MaterialInfo(request.StartCode, request.EndCode)
	if err != nil {
		m.Fail(c, err.Error(), -1000)
		return
	}
	m.Success(c, result, "msg", 200)

}

// GetCustomerStatistic
//
//	@Description: 获取客户统计
//	@receiver o
//	@param context
func (o *MaterialController) GetCustomerStatistic(c *gin.Context) {
	type Params struct {
		Month      string `form:"month" binding:"required,datetime=2006-01-02"`
		SortColumn string `form:"sort_column" binding:"required,oneof=total_order_tips_fee order_count total_order_price"`
		SortType   string `form:"sort_type" binding:"required,oneof=asc desc"`
		CategoryID int    `form:"category_id"`
		Page       int    `form:"page" binding:"required"`  // 当前页数
		Limit      int    `form:"limit" binding:"required"` // 每页显示数量
	}
	var (
		l, _               = c.Get("lang_util")
		langUtil           = l.(lang.LangUtil)
		materialService    = services.NewMaterialService(langUtil, langUtil.Lang)
		materialTransfomer = shipperv2.NewMaterialTransformer(c)
	)
	var req Params
	if err := c.ShouldBind(&req); err != nil {
		panic(err)
	}
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	data, err := materialService.GetCustomerStatistic(admin, req.Month, req.CategoryID, req.Page, req.Limit, req.SortColumn, req.SortType)
	totalCustomerCount, totalOrderCount := materialService.GetCustomerStatisticHeader(admin, req.Month, req.CategoryID, req.Page, req.Limit)
	categoryName := materialService.GetCategoryName(req.CategoryID)
	rtn := materialTransfomer.GetCustomerStatistic(
		data,
		totalCustomerCount,
		totalOrderCount,
		categoryName,
		req.CategoryID,
		req.SortColumn,
		req.SortType)
	if err == nil {
		o.Success(c, rtn, "", 200)
	} else {
		o.Fail(c, langUtil.T("error_happend"), 301)
	}
}

//
// GetMaterialCategory
//  @Description: 获取宣传材料分类
//  @receiver m
//  @param c
//
func (m *MaterialController) GetMaterialCategory(c *gin.Context) {

	var (
		l, _            = c.Get("lang_util")
		langUtil        = l.(lang.LangUtil)
		materialService = services.NewMaterialService(langUtil, langUtil.Lang)
	)
	page :=c.Query("page")
	data, err := materialService.GetMaterialCategory(page)
	if err == nil {
		m.Success(c, data, "", 200)
	} else {
		m.Fail(c, langUtil.T("error_happend"), 301)
	}
}

// Desc:  宣传单 列表
// Author: rozi mamat
// Date: 2024-02-21
func (m *MaterialController) MaterialList(c *gin.Context) {

	type ScanRequest struct {
		Month string `form:"month" json:"month" binding:"required"`
		CategoryId int `form:"category_id" json:"category_id"`
	}
	var (
		l, _            = c.Get("lang_util")
		langUtil        = l.(lang.LangUtil)
		materialService = services.NewMaterialService(langUtil, langUtil.Lang)
	)
	var request ScanRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
		return
	}

	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)

	result, err := materialService.MaterialList(admin,request.Month,request.CategoryId)
	if err != nil {
		m.Fail(c, err.Error(), -1000)
		return
	}
	m.Success(c, result, "msg", 200)

}


// Desc:  宣传单 列表
// Author: rozi mamat
// Date: 2024-02-21
func (m *MaterialController) MaterialTakeDetail(c *gin.Context) {

	type ScanRequest struct {
		TakeId int `form:"take_id" json:"take_id" binding:"required"`
	}
	var (
		l, _            = c.Get("lang_util")
		langUtil        = l.(lang.LangUtil)
		materialService = services.NewMaterialService(langUtil, langUtil.Lang)
	)
	var request ScanRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
		return
	}
	
	result, err := materialService.MaterialTakeDetail(request.TakeId)
	if err != nil {
		m.Fail(c, err.Error(), -1000)
		return
	}
	m.Success(c, result, "msg", 200)

}



// Desc:  宣传 中心
// Author: rozi mamat
// Date: 2024-02-22
func (m *MaterialController) MaterialCenter(c *gin.Context) {

	
	var (
		l, _            = c.Get("lang_util")
		langUtil        = l.(lang.LangUtil)
		materialService = services.NewMaterialService(langUtil, langUtil.Lang)
	)
	

	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)

	result, err := materialService.MaterialCenter(admin)
	if err != nil {
		m.Fail(c, err.Error(), -1000)
		return
	}
	m.Success(c, result, "msg", 200)

}

// 宣传材料 总统计
func (o *MaterialController) GetMaterialStatistic(c *gin.Context) {
	type Params struct {
		Month      string `form:"month" binding:"required,datetime=2006-01-02"`
	}
	var (
		l, _               = c.Get("lang_util")
		langUtil           = l.(lang.LangUtil)
		materialService    = services.NewMaterialService(langUtil, langUtil.Lang)
		materialTransfomer = shipperv2.NewMaterialTransformer(c)
	)
	var req Params
	if err := c.ShouldBind(&req); err != nil {
		panic(err)
	}
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	data,dataDiagram, err := materialService.GetMaterialStatistic(admin, req.Month)
	totalCustomerCount, totalOrderCount,totalOrderPrice,totalOrderTipsFee := materialService.GetMaterialStatisticHeader(admin, req.Month)
	
	rtn := materialTransfomer.GetMaterialStatistic(
		data,
		dataDiagram,
		totalCustomerCount,
		totalOrderCount,
		totalOrderPrice,
		totalOrderTipsFee,
	)
	if err == nil {
		o.Success(c, rtn, "", 200)
	} else {
		o.Fail(c, langUtil.T("error_happend"), 301)
	}
}


// Desc:  宣传单 扫码记录
// Author: rozi mamat
// Date: 2024-02-20
func (m *MaterialController) ScanLog(c *gin.Context) {

	
	var (
		l, _            = c.Get("lang_util")
		langUtil        = l.(lang.LangUtil)
		materialService = services.NewMaterialService(langUtil, langUtil.Lang)
	)
	
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	page := tools.ToInt(c.DefaultQuery("page","1"))
	limit := tools.ToInt(c.DefaultQuery("limit","10"))
	result, _ := materialService.GetMaterialScanLog(admin.ID,page,limit)
	
	m.Success(c, result, "msg", 200)

}