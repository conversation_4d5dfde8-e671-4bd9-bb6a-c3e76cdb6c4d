package controllers

import (
	"mulazim-api/lang"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type BaseController struct{}

const (
	SUCCESS = 200
	FAIL    = -1000
)

// Success
//
//	@Time 2022-09-04 00:57:53
//	<AUTHOR>
//	@Description: 请求数据成功时调用的方法
//	@receiver con BaseController
//	@param c *gin.Context
//	@param data 返回的数据map或者gin.H类型
//	@param msg 国际化key值
//	@param status 返回的状态码 200
func (con BaseController) Success(c *gin.Context, data interface{}, msg string, status int) {
	var (
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	c.JSON(status, gin.H{
		"status": status,
		"data":   data,
		"msg":    langUtil.T(msg),
		"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
	})
}

// Fail
//
//	@Time 2022-09-04 00:55:20
//	<AUTHOR>
//	@Description: 请求数据失败时调用的方法
//	@receiver con BaseController
//	@param c *gin.Context
//	@param msg 国际化key值
//	@param status 返回的状态码 -1000
func (con BaseController) Fail(c *gin.Context, msg string, status int) {
	var (
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	c.JSON(status, gin.H{
		"status": status,
		"msg":    langUtil.T(msg),
		"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
	})
}

// Ok
//
//	@Description: 没有参数的返回成功响应
//	<AUTHOR>
//	@Time 2023-01-12 11:37:25
//	@receiver con BaseController
//	@param c *gin.Context
func (con BaseController) Ok(c *gin.Context) {
	var (
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	c.JSON(SUCCESS, gin.H{
		"status": SUCCESS,
		"msg":    langUtil.T("msg"),
		"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
	})
}

// 翻译语言包
func (con BaseController) Trans(c *gin.Context, msgKey string) (content string) {
	var (
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	return langUtil.T(msgKey)
}

func (con BaseController) TransArr(c *gin.Context, msgKey string) (map[int]string) {
	var (
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	return langUtil.TArr(msgKey)
}


