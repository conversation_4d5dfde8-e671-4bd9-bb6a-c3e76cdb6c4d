package controllers

import (
	"mulazim-api/models"
	restaurantRequest "mulazim-api/requests/RestaurantRequest"
	"mulazim-api/services"
	"mulazim-api/tools"
	"mulazim-api/transformers"

	"github.com/gin-gonic/gin"
)

type RestaurantFoodsSpecController struct {
	BaseController
}

func (ctr *RestaurantFoodsSpecController) GetSpecList(ctx *gin.Context) {
	// 从URI中获取food_id参数
	var params struct {
		FoodID int `uri:"food_id" binding:"required,min=1"`
	}
	var (
		foodSpecService = services.NewRestaurantFoodsSpecService(ctx)
		foodSpecTransformer = transformers.NewFoodSpecTransformer(ctx)
	)
	if err := ctx.ShouldBindUri(&params); err != nil {
		ctr.Fail(ctx, "美食ID参数错误", -1000)
		return
	}

	// 获取美食规格列表
	specList := foodSpecService.GetFoodSpecList(params.FoodID)
	resSpec := foodSpecTransformer.FormatFoodSpecList(specList)
	ctr.Success(ctx, resSpec, "msg", 200)


}

// PostCreateSpec 创建美食规格
// @Summary 创建美食规格
// @Description 创建美食规格
// @Tags 餐厅美食
// @Accept json
// @Produce json
// @Param data body restaurantRequest.RestaurantFoodsSpecCreateRequest true "创建美食规格请求"
// @Success 200 {object} utils.Response
// @Router /ug/cms/v2/restaurant/foods/spec [post]
func (ctr *RestaurantFoodsSpecController) CreateSpec(ctx *gin.Context) {
	var body restaurantRequest.FoodsSpecBody
	if err := ctx.ShouldBindJSON(&body); err != nil {
		panic(err)
		return
	}
	if len(body.FoodIds) == 0 {
		ctr.Fail(ctx, "food_list_cannot_be_empty", -1000)
		return
	}

	// 美食多时，组必须一个
	if len(body.FoodIds) > 1 && len(body.RestaurantFoodsSpecTypes) > 1 {
		ctr.Fail(ctx, "multiple_foods_cannot_create_multiple_spec_groups", -1000)
		return
	}

	// 验证给个规格组
	if len(body.RestaurantFoodsSpecTypes) == 0 {
		ctr.Fail(ctx, "spec_group_cannot_be_empty", -1000)
		return
	}
	// 验证给个规格组
	if len(body.RestaurantFoodsSpecTypes) > 6 {
		ctr.Fail(ctx, "spec_type_count_max_6", -1000)
		return
	}
	// 验证规格组改价，只要一个组改价，其他组不能改价
	priceTypeCount := 0
	for _, specType := range body.RestaurantFoodsSpecTypes {
		if len(specType.RestaurantFoodsSpecOption) == 0 {
			ctr.Fail(ctx, "spec_option_cannot_be_empty", -1000)
			return
		}
		if specType.PriceType == 1 {
			priceTypeCount++
		}

		// 验证规格项只能一个选中
		isSelectedCount := 0
		for _, specOption := range specType.RestaurantFoodsSpecOption {
			if specType.PriceType == 1 && specOption.Price == 0 {
				ctr.Fail(ctx, "price_type_group_price_error", -1000)
				return
			}
			if specOption.IsSelected == 1 {
				isSelectedCount++
			}
		}
		if isSelectedCount != 1 {
			ctr.Fail(ctx, "spec_option_must_selected", -1000)
			return
		}
	}

	_restaurantFoodsSvc := services.NewRestaurantFoodsService(ctx)
	_foodsSpecSvc := services.NewRestaurantFoodsSpecService(ctx)
	// 创建规格
	for _, foodId := range body.FoodIds {
		if foodId == 0 {
			ctr.Fail(ctx, "food_id_cannot_be_empty", -1000)
			return
		}
		food, _ := _restaurantFoodsSvc.GetFoodsByID(foodId)
		if food.FoodType == models.RestaurantFoodsTypeCombo {
			ctr.Fail(ctx, "combo_not_able_create_spec", -1000)
			return
		}
		// 创建规格
		err := _foodsSpecSvc.SaveFoodSpec(*food, body.RestaurantFoodsSpecTypes)
		if err != nil {
			tools.Logger.Error("创建规格错误:",err.Error())
			ctr.Fail(ctx, err.Error(), -1000)
			return
		}
	}
	ctr.Ok(ctx)
}

// PutUpdateSpec 更新美食规格
// @Summary 更新美食规格
// @Description 更新美食规格
// @Tags 餐厅美食
// @Accept json
// @Produce json
// @Param data body restaurantRequest.RestaurantFoodsSpecUpdateRequest true "更新美食规格请求"
// @Success 200 {object} utils.Response
// @Router /ug/cms/v2/restaurant/foods/spec/:id [put]
func (ctr *RestaurantFoodsSpecController) UpdateSpec(ctx *gin.Context) {
	var body []restaurantRequest.RestaurantFoodsSpecTypes
	if err := ctx.ShouldBindJSON(&body); err != nil {
		panic(err)
		return
	}
	// 验证美食
	// 从URI中获取food_id参数
	var params struct {
		FoodID int `uri:"food_id" binding:"required,min=1"`
	}
	if err := ctx.ShouldBindUri(&params); err != nil {
		ctr.Fail(ctx, "food_id_cannot_be_empty", -1000)
		return
	}

	// 调用服务验证美食是否存在
	_restaurantFoodsSvc := services.NewRestaurantFoodsService(ctx)
	food, err := _restaurantFoodsSvc.GetFoodsByID(params.FoodID)
	if food == nil || food.ID == 0 || err != nil {
		ctr.Fail(ctx, "food_not_found", -1000)
		return
	}

	// 找出该美食是否设置过秒杀，特价，加价，优惠，满减，如果设置过提示关闭所有活动 ok
	err = _restaurantFoodsSvc.HasActivity(params.FoodID)
	if err != nil {
		ctr.Fail(ctx, err.Error(), -1000)
		return
	}

	/**
	* 1.(组)找出新加的组直接创建
	* 2.(组)找出被移除的组直接删除（is_deleted = 0）
	* 3.(规格项)找出内容更新的规格项，把更新的都删除，然后跟当前组新加的规格项和更新后的规格项创建
	*/
	_foodsSpecSvc := services.NewRestaurantFoodsSpecService(ctx)
	oldSpecTypes := _foodsSpecSvc.GetFoodSpecList(params.FoodID)
	// 1.创建新建组
	addSpecTypes := _foodsSpecSvc.GetEditSpecAddSpecTypes(body)
	if len(addSpecTypes) > 0 {
		_foodsSpecSvc.SaveFoodSpec(*food, addSpecTypes)
	}
	// 2.删除移除的组
	deleteSpecTypeIds := _foodsSpecSvc.GetEditSpecDeleteIds(oldSpecTypes,body)
	if len(deleteSpecTypeIds) > 0 {
		_foodsSpecSvc.DeleteFoodsSpecBySpecTypeIds(params.FoodID,deleteSpecTypeIds)
	}
	// 3.获取组内需要删除的规格项(被修改项+移除项)
	deleteSpecOptionIds := _foodsSpecSvc.GetEditSpecDeleteOptionItems(body,oldSpecTypes)
	if len(deleteSpecOptionIds) > 0 {
		_foodsSpecSvc.DeleteFoodsSpecOptions(deleteSpecOptionIds)
	}
	// 4.获取组内需要添加的组(修改后+没有ID) , 并跟新ID有的
	addSpecOptions := _foodsSpecSvc.GetEditSpecAddOptionItems(body,oldSpecTypes)
	if len(addSpecOptions) > 0 {
		_foodsSpecSvc.SaveFoodSpecOptions(*food,addSpecOptions)
	}
	
	// 5.更新内容更改的组名称
	err = _foodsSpecSvc.EditSpecTypesName(body)
	if err != nil {
		ctr.Fail(ctx, err.Error(), -1000)
		return
	}

	// 更新规格项排序
	err = _foodsSpecSvc.UpdateFoodSpecOptionsIsSelectedAndSort(body)
	if err != nil {
		ctr.Fail(ctx, err.Error(), -1000)
		return
	}
	// 更新美食价格
	err = _foodsSpecSvc.UpdateFoodPriceBySpec(params.FoodID,body)
	if err != nil {
		ctr.Fail(ctx, err.Error(), -1000)
		return
	}
	// 如果删除所有规格时，美食类型修改普通美食
	err = _foodsSpecSvc.DeleteAllSpecOptionUpdateFoodType(params.FoodID)
	if err != nil {
		ctr.Fail(ctx, err.Error(), -1000)
		return
	}
	ctr.Ok(ctx)
}



// DeleteDeleteSpec 删除美食规格
// @Summary 删除美食规格
// @Description 删除美食规格
// @Tags 餐厅美食
// @Accept json
// @Produce json
// @Param id path int true "美食规格ID"
// @Success 200 {object} utils.Response
// @Router /ug/cms/v2/restaurant/foods/spec/:id [delete]
func (ctr *RestaurantFoodsSpecController) DeleteSpec(ctx *gin.Context) {
	// 从URI中获取food_id参数
	var params struct {
		FoodID int `uri:"food_id" binding:"required,min=1"`
	}
	if err := ctx.ShouldBindUri(&params); err != nil {
		ctr.Fail(ctx, "美食ID参数错误", -1000)
		return
	}

	// 调用服务验证美食是否存在
	_restaurantFoodsSvc := services.NewRestaurantFoodsService(ctx)
	food, err := _restaurantFoodsSvc.GetFoodsByID(params.FoodID)
	if food == nil || food.ID == 0 || err != nil {
		ctr.Fail(ctx, "food_not_found", -1000)
		return
	}

	// 找出该美食是否设置过秒杀，特价，加价，优惠，满减，如果设置过提示关闭所有活动 ok
	err = _restaurantFoodsSvc.HasActivity(params.FoodID)
	if err != nil {
		ctr.Fail(ctx, err.Error(), -1000)
		return
	}

	// 调用服务验证美食是否存在
	_foodsSpecSvc := services.NewRestaurantFoodsSpecService(ctx)
	// 删除规格
	err = _foodsSpecSvc.DeleteFoodsSpec(params.FoodID)
	if err != nil {
		ctr.Fail(ctx, "美食不存在", -1000)
		return
	}
	ctr.Ok(ctx)
}
