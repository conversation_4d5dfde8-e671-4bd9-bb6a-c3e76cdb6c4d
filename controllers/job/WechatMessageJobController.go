package job

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/jobs"
)

type WechatMessageJobController struct {
	controllers.BaseController
}

func (w WechatMessageJobController) SendMessage(c *gin.Context) {
	type RequestData struct {
		OrderId int64 `form:"order_id" binding:"required"`
		Type    int64 `form:"type" binding:"required"`
	}
	var requestData RequestData
	if err := c.ShouldBind(&requestData); err != nil {
		panic(err)
		return
	}
	job := jobs.NewSendWechatMiniMessageJob()
	job.ProduceMessageToConsumer(map[string]interface{}{
		"order_id": requestData.OrderId,
		"type":     requestData.Type,
	})

	w.Success(c, nil, "msg", 200)
}
