package cms

import (
	"mulazim-api/configs"
	"mulazim-api/controllers"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type LotteryWinnersController struct {
	controllers.BaseController
}

// GetCurrentInfo 获取中奖列表
//
//	@receiver cms
//	@param c
func (cms *LotteryWinnersController) GetList(c *gin.Context) {
	type Params struct {
		ActivityID int `form:"activity_id" json:"activity_id" `
		CityId     int `form:"city_id" json:"city_id" `
		AreaId     int `form:"area_id" json:"area_id" `
		KW         string `form:"kw" json:"kw" `
		Sort       string `form:"sort" json:"sort" `
		Page       int    `form:"page" json:"page" `
		Limit      int    `form:"limit" json:"limit" `
		Level      int    `form:"level" json:"level" `
	}
	var (
		params                  Params
		err                     = c.ShouldBind(&params)
		lotteryWinnerService = cmsService.NewLotteryWinnersService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if params.Page < 1 {
		params.Page = 1
	}
	if params.Limit < 1 {
		params.Limit = 10
	}

	info := lotteryWinnerService.List(params.CityId,params.AreaId,params.ActivityID,params.KW,params.Sort,params.Page,params.Limit,0,params.Level,true,false,false)
	cms.Success(c, info, "success", 200)
}


// GetCurrentInfo 获取中奖列表
//
//	@receiver cms
//	@param c
func (cms *LotteryWinnersController) GetDetail(c *gin.Context) {
	type Params struct {
		Id int `form:"id" json:"id" binding:"required"`
	}
	var (
		params                  Params
		err                     = c.ShouldBind(&params)
		lotteryWinnerService = cmsService.NewLotteryWinnersService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	info := lotteryWinnerService.Detail(params.Id)
	cms.Success(c, info, "success", 200)
}
 

//支付订单列表
func (cms *LotteryWinnersController) GetOrders(c *gin.Context) {
	type Params struct {
		ActivityID int `form:"activity_id" json:"activity_id" `
		CityId     int `form:"city_id" json:"city_id" `
		AreaId     int `form:"area_id" json:"area_id" `
		KW         string `form:"kw" json:"kw" `
		Sort       string `form:"sort" json:"sort" `
		Page       int    `form:"page" json:"page" `
		Limit      int    `form:"limit" json:"limit" `
	}
	var (
		params                  Params
		err                     = c.ShouldBind(&params)
		lotteryWinnerService = cmsService.NewLotteryWinnersService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if params.Page < 1 {
		params.Page = 1
	}
	if params.Limit < 1 {
		params.Limit = 10
	}

	info := lotteryWinnerService.List(params.CityId,params.AreaId,params.ActivityID,params.KW,params.Sort,params.Page,params.Limit,0,0,false,false,false)
	cms.Success(c, info, "success", 200)
}

//机会列表
func (cms *LotteryWinnersController) GetChanceList(c *gin.Context) {
	type Params struct {
		ActivityID int `form:"activity_id" json:"activity_id" `
		CityId     int `form:"city_id" json:"city_id" `
		AreaId     int `form:"area_id" json:"area_id" `
		KW         string `form:"kw" json:"kw" `
		Sort       string `form:"sort" json:"sort" `
		Page       int    `form:"page" json:"page" `
		Limit      int    `form:"limit" json:"limit" `
		Tp      int    `form:"type" json:"type" `
	}
	var (
		params                  Params
		err                     = c.ShouldBind(&params)
		lotteryWinnerService = cmsService.NewLotteryWinnersService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if params.Page < 1 {
		params.Page = 1
	}
	if params.Limit < 1 {
		params.Limit = 10
	}

	info := lotteryWinnerService.List(params.CityId,params.AreaId,params.ActivityID,params.KW,params.Sort,params.Page,params.Limit,params.Tp,0,false,true,false)
	cms.Success(c, info, "success", 200)
}

//退款
func (cms *LotteryWinnersController) PostRefund(c *gin.Context) {
	type Params struct {
		OutOrderNo string `form:"out_order_no" json:"out_order_no" `
		TransactionId string `form:"transaction_id" json:"transaction_id" `
		Force int `form:"force" json:"force" ` //1强制退款
	}
	var (
		params                  Params
		err                     = c.ShouldBind(&params)
		lotteryWinnerService = cmsService.NewLotteryWinnersService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	
	ok,info := lotteryWinnerService.Refund(params.OutOrderNo,params.TransactionId,params.Force)
	if !ok{
		cms.Fail(c, info, -1000)
		return
	}
	cms.Success(c, info, "success", 200)
}



// GetCurrentInfo 获取中奖列表
//
//	@receiver cms
//	@param c
func (cms *LotteryWinnersController) GetOrderDetail(c *gin.Context) {
	type Params struct {
		OutOrderNo string `form:"out_order_no" json:"out_order_no" `
		TransactionId string `form:"transaction_id" json:"transaction_id" `
		
	}
	var (
		params                  Params
		err                     = c.ShouldBind(&params)
		lotteryWinnerService = cmsService.NewLotteryWinnersService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	ok,msg,info := lotteryWinnerService.OrderDetail(params.OutOrderNo,params.TransactionId)
	if !ok{
		cms.Fail(c, msg, -1000)
		return
	}
	cms.Success(c, info, "success", 200)
}
 

//excel 导出
func (cms *LotteryWinnersController) GetListExport(c *gin.Context) {
	type Params struct {
		ActivityID int `form:"activity_id" json:"activity_id" `
		CityId     int `form:"city_id" json:"city_id" `
		AreaId     int `form:"area_id" json:"area_id" `
		KW         string `form:"kw" json:"kw" `
		Sort       string `form:"sort" json:"sort" `
		Page       int    `form:"page" json:"page" `
		Limit      int    `form:"limit" json:"limit" `
		Level      int    `form:"level" json:"level" `
	}
	var (
		params                  Params
		err                     = c.ShouldBind(&params)
		lotteryWinnerService = cmsService.NewLotteryWinnersService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if params.Page < 1 {
		params.Page = 1
	}
	if params.Limit < 1 {
		params.Limit = 10
	}

	info := lotteryWinnerService.List(params.CityId,params.AreaId,params.ActivityID,params.KW,params.Sort,params.Page,params.Limit,0,params.Level,true,false,true)

	//开始excel导出
	fileName := carbon.Now(configs.AsiaShanghai).Format("YmdHis") + tools.RandStr(4)
	cols := []string{
		"序号",
		"城市",
		"地区",
		"姓名",
		"手机号",
		"奖品等级",
		"奖品名称",
		"收货地址-姓名",
		"收货地址-手机号",
		"收货地址-详细地址",
		"抽奖时间",
		"状态",
	}
	excelData :=info["data"].([]map[string]interface{})
	excelFile := tools.ExcelExport("中奖列表", cols, excelData, fileName)

	
	downLoadFileName := "中奖列表-" + carbon.Now(configs.AsiaShanghai).Format("Ymd") + ".xlsx"
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	err1 := excelFile.Write(c.Writer)
	if err1 != nil {
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}
// GetChanceListByArea 按照区域统计
//  @receiver cms
//  @param c
//
func (cms *LotteryWinnersController) GetChanceListByArea(c *gin.Context) {
	type Params struct {
		ActivityID int `form:"activity_id" json:"activity_id" `
		Sort       string `form:"sort" json:"sort" `
	}
	var (
		params                  Params
		err                     = c.ShouldBind(&params)
		lotteryWinnerService = cmsService.NewLotteryWinnersService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	info := lotteryWinnerService.ListByArea(params.ActivityID,params.Sort)
	cms.Success(c, info, "success", 200)
}
