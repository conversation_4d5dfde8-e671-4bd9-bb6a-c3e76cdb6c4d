package cms

import (
	"fmt"
	"math/rand"
	"mulazim-api/controllers"
	"mulazim-api/factory"
	"mulazim-api/models"
	"mulazim-api/permissions"
	shipperResource "mulazim-api/resources/cms/shipper"
	cmsService "mulazim-api/services/cms"
	merchantService "mulazim-api/services/merchant"
	"mulazim-api/services/shipperv2"
	"mulazim-api/tools"
	cmsTransformer "mulazim-api/transformers/cms"
	"time"

	"github.com/gin-gonic/gin"
	shipperRequest "mulazim-api/requests/ShipperRequest"
)

type ShipperController struct {
	controllers.BaseController
}

// PostAccountInfo
//
// @Description: 提交配送员账户信息
// @Author: Rixat
// @Time: 2023-10-24 03:01:49
// @receiver
// @param c *gin.Context
func (cms ShipperController) PostAccountInfo(c *gin.Context) {
	var (
		params         shipperResource.ShipperAccountInfo
		err            = c.ShouldBind(&params)
		shipperService = cmsService.NewShipperService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	// 创建
	result, err := shipperService.CreateAccountInfo(admin, params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, result, "msg", 200)
}

// PostUpdateAccountInfo
//
// @Description: 更新配送员账户信息
// @Author: Rixat
// @Time: 2023-11-04 10:02:59
// @receiver
// @param c *gin.Context
func (cms ShipperController) PostUpdateAccountInfo(c *gin.Context) {
	var (
		params         shipperResource.ShipperAccountInfo
		err            = c.ShouldBind(&params)
		shipperService = cmsService.NewShipperService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	// 更新
	result, err := shipperService.UpdateAccountInfo(admin, params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, result, "msg", 200)
}

// PostIDCardInfo
//
// @Description: 提交身份证信息
// @Author: Rixat
// @Time: 2023-10-24 03:01:49
// @receiver
// @param c *gin.Context
func (cms ShipperController) PostIDCardInfo(c *gin.Context) {

	var (
		params         shipperResource.ShipperIDCardInfo
		err            = c.ShouldBind(&params)
		shipperService = cmsService.NewShipperService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if params.SelfSignID == 0 {
		cms.Fail(c, "submit_account_info", -1000)
		return
	}
	// 创建
	err = shipperService.CreateIDCardInfo(params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// PostCreate
//
// @Description: 提交银行卡信息
// @Author: Rixat
// @Time: 2023-10-24 03:01:49
// @receiver
// @param c *gin.Context
func (cms ShipperController) PostBankInfo(c *gin.Context) {
	var (
		params         shipperResource.ShipperBankInfo
		err            = c.ShouldBind(&params)
		shipperService = cmsService.NewShipperService(c)
		redisHelper    = tools.GetRedisHelper()
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if params.SelfSignID == 0 {
		cms.Fail(c, "submit_account_info", -1000)
		return
	}
	cacheKey := fmt.Sprintf("send_mobile_%s", params.BankBindMobile)
	//2分钟 能获取一次
	exists, _ := redisHelper.Exists(c, cacheKey).Result()
	if exists == 0 {
		cms.Fail(c, "captcha_time_out", -1000)
		return
	} else {
		code := redisHelper.Get(c, cacheKey).Val()
		if code != params.VerifyCode {
			cms.Fail(c, "captcha_incorrect", -1000)
			return
		}
	}
	// // 创建
	err = shipperService.CreateBankCardInfo(params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// PostHealthInfo
//
// @Description: 提交配送员健康证书信息
// @Author: Rixat
// @Time: 2023-10-28 07:48:25
// @receiver
// @param c *gin.Context
func (cms ShipperController) PostHealthInfo(c *gin.Context) {
	var (
		params         shipperResource.ShipperHealthInfo
		err            = c.ShouldBind(&params)
		shipperService = cmsService.NewShipperService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if params.SelfSignID == 0 {
		cms.Fail(c, "submit_account_info", -1000)
		return
	}
	// 创建
	err = shipperService.CreateHealthInfo(params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// GetShipperInfo
//
// @Description: 获取配送员详情
// @Author: Rixat
// @Time: 2023-10-28 07:48:48
// @receiver
// @param c *gin.Context
func (cms ShipperController) GetShipperInfo(c *gin.Context) {
	type Params struct {
		ShipperID int `form:"shipper_id" binding:"required"` // 代理区域ID
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		shipperService = cmsService.NewShipperService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取信息
	result := shipperService.GetShipperInfo(params.ShipperID)
	cms.Success(c, result, "msg", 200)
}

// PostDelete
//
// @Description: 删除配送员
// @Author: Rixat
// @Time: 2023-10-28 07:49:14
// @receiver
// @param c *gin.Context
func (cms ShipperController) PostDelete(c *gin.Context) {
	type Params struct {
		ShipperID int `form:"shipper_id" json:"shipper_id" binding:"required"` // 代理区域ID
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		shipperService = cmsService.NewShipperService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 删除配送员
	err = shipperService.PostDeleteShipper(params.ShipperID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// PostChangeState
//
// @Description: 修改配送员状态(支持配送，支持现金订单)
// @Author: Rixat
// @Time: 2023-10-28 07:49:32
// @receiver
// @param c *gin.Context
func (cms ShipperController) PostChangeState(c *gin.Context) {
	type Params struct {
		ShipperID int `form:"shipper_id" json:"shipper_id" binding:"required"` // 代理区域ID
		State     int `form:"state" json:"state" binding:""`                   // 代理区域ID
		Type      int `form:"type" json:"type" binding:"required"`             // 1:配送状态 2：现金订单状态
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		shipperService = cmsService.NewShipperService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取信息
	err = shipperService.ChangeShipperState(params.ShipperID, params.State, params.Type)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// 获取配送列表
//
// @Description:
// @Author: Rixat
// @Time: 2023-10-28 07:50:36
// @receiver
// @param c *gin.Context
func (cms ShipperController) GetShipperList(c *gin.Context) {
	type Params struct {
		Page           int    `form:"page" binding:"required"`     // 当前页数
		Limit          int    `form:"limit" binding:"required"`    // 每页显示数量
		CityID         int    `form:"city_id" binding:""`          // 地区ID
		AreaID         int    `form:"area_id" binding:""`          // 区域ID
		State          string `form:"state" binding:""`            // 状态（0:关闭，1:开启）
		TemplateID     int    `form:"template_id" binding:""`      // 当前模板ID
		NextTemplateID int    `form:"next_template_id" binding:""` // 下一个模板ID
		Kw             string `form:"kw" binding:""`               // 关键字(姓名或手机号)
		SortColumns    string `form:"sort_columns"`
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		shipperService = cmsService.NewShipperService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取排序参数
	sort := tools.SortColumnParam(params.SortColumns)
	// 获取配送员列表信息
	result := shipperService.ShipperList(params.Page, params.Limit, params.CityID, params.AreaID, params.State, params.TemplateID, params.NextTemplateID, params.Kw, sort,c)
	cms.Success(c, result, "msg", 200)
}

// 获取配送列表
//
// @Description:
// @Author: Rixat
// @Time: 2023-10-28 07:50:36
// @receiver
// @param c *gin.Context
func (cms ShipperController) GetBankList(c *gin.Context) {
	var cService = merchantService.NewCollectionInfoService(c)
	bankList := cService.GetBankList()
	cms.Success(c, bankList, "", 200)
}

// 获取配送列表
//
// @Description:
// @Author: Rixat
// @Time: 2023-10-28 07:50:36
// @receiver
// @param c *gin.Context
func (cms ShipperController) GetBankAreas(c *gin.Context) {
	type Params struct {
		Level int    `form:"level" binding:"required"` // 当前页数
		PCode string `form:"p_code" binding:""`        // 当前页数
	}
	var (
		params Params
		err    = c.ShouldBind(&params)
		// lang, _ = c.Get("lang")
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	var selfSignAreas []models.SelfSignArea
	query := tools.Db.Model(selfSignAreas).Where("level = ?", params.Level)
	if len(params.PCode) > 0 && params.Level > 1 {
		query.Where("p_code = ?", params.PCode)
	}
	query.Order("created_at desc").Find(&selfSignAreas)

	areaMap := make([]map[string]interface{}, 0)
	for _, value := range selfSignAreas {
		areaMap = append(areaMap, map[string]interface{}{
			"id":      value.Id,
			"p_code":  value.PCode,
			"code":    value.Code,
			"name":    value.NameUg,
			"name_ug": value.NameUg,
			"name_zh": value.NameZh,
		})
	}
	cms.Success(c, map[string]interface{}{"items": areaMap}, "msg", 200)
}

// 获取配送列表
//
// @Description:
// @Author: Rixat
// @Time: 2023-10-28 07:50:36
// @receiver
// @param c *gin.Context
func (cms ShipperController) GetBranchBank(c *gin.Context) {
	type Params struct {
		AreaCode string `form:"areaCode" binding:"required"` // 地区code（省2位，城市4位，区县6位）
		Kw       string `form:"key" `                        // 请求的关键字，支持多个关键字查询，用"/"分割，需要符合前后顺序。建议使用支行关键字查询
		BankId   string `form:"bank_id" `
	}
	var (
		params   Params
		err      = c.ShouldBind(&params)
		cService = merchantService.NewCollectionInfoService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	page := c.DefaultQuery("page", "1")
	bankList := cService.BankBranchShumai(params.AreaCode, params.Kw, params.BankId, page)
	cms.Success(c, bankList, "", 200)
}

// 获取配送列表
//
// @Description:
// @Author: Rixat
// @Time: 2023-10-28 07:50:36
// @receiver
// @param c *gin.Context
func (cms ShipperController) PostSendCode(c *gin.Context) {
	type Params struct {
		Mobile string `form:"mobile" binding:"required"` // 手机号
	}
	var (
		redisHelper = tools.GetRedisHelper()
		params      Params
		err         = c.ShouldBind(&params)
	)

	if !tools.VerifyMobileFormat(params.Mobile) { //手机号 格式判断
		cms.Fail(c, "mobile_incorrect", -1000)
		return
	}

	// cacheKey := fmt.Sprintf("send_mobile_%s", params.Mobile)
	//2分钟 能获取一次
	// exists, _ := redisHelper.Exists(c, cacheKey).Result()
	// if exists == 0 {
	// 	redisHelper.Set(c, cacheKey, params.Mobile, 2*time.Minute)
	// } else {
	// 	cms.Fail(c, "sms_in_two_minutes", -1000)
	// 	return
	// }

	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))
	code := fmt.Sprintf("%06v", rnd.Int31n(1000000))

	cacheKey := fmt.Sprintf("send_mobile_%s", params.Mobile)

	redisHelper.Set(c, cacheKey, code, 2*time.Minute) //验证码保存 30 分钟

	//contentMap := map[string]string{
	//	"code": code,
	//}
	//dataType, _ := json.Marshal(contentMap)
	//content := string(dataType)

	//err = tools.AliSmsSend(params.Mobile, string(content))
	// 发送验证码
	smsServiceFactory := &factory.SmsServiceFactory{}
	smsService := smsServiceFactory.CreateSmsService()
	codeResp, err := smsService.SendVerificationCode(params.Mobile, code)
	if err != nil {
		tools.Logger.Errorf("发送验证码失败: %v \n",err)
		panic(err)
		return
	}
	tools.Logger.Infof("发送验证码成功 结果: %v \n",codeResp)
	//if err != nil {
	//	panic(err)
	//	return
	//}

	cms.Ok(c)
}

// BatchUpdateShipperTemplate 批量更新配送员模板
func (cms ShipperController) BatchUpdateShipperTemplate(c *gin.Context) {
	type Params struct {
		CityId     int   `form:"city_id" json:"city_id"`                            // 城市ID
		AreaId     int   `form:"area_id" json:"area_id"`                            // 区域ID
		ShipperIds []int `form:"shipper_ids" json:"shipper_ids" binding:"required"` // 配送员ID
		TemplateID int   `form:"template_id" json:"template_id" binding:"required"` // 当前模板ID
	}
	var (
		params         Params
		err            = c.ShouldBindJSON(&params)
		shipperService = cmsService.NewShipperService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	switch {
	case admin.IsOwner() || admin.IsAdmin():
		if params.CityId == 0 || params.AreaId == 0 {
			cms.Fail(c, "city_id or area_id", 422)
		}
	case admin.IsDealer() || admin.IsDealerSub():
		params.CityId = admin.AdminCityID
		params.AreaId = admin.AdminAreaID
	default:
		cms.Fail(c, "no_permission", 403)
	}
	// 更新
	err = shipperService.BatchUpdateShipperTemplate(params.CityId, params.AreaId, params.TemplateID, params.ShipperIds)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, nil, "msg", 200)
}


// 获取 待更新的 配送员信息 
//
// @Description:
// @Author: Rozimamat
// @Time: 2024-03-26 11:50:36
// @receiver
// @param c *gin.Context
func (cms ShipperController) GetNeedAlert(c *gin.Context) {
	var (
		shipperService = cmsService.NewShipperService(c)
	)
	var areaId int
	admin := permissions.GetAdmin(c)
	switch {
	case admin.IsDealer() || admin.IsDealerSub():
		areaId = admin.AdminAreaID
	default:
		cms.Fail(c, "no_permission", 403)
	}
	
	res :=shipperService.GetNeedAlert(areaId)
	cms.Success(c,res,"",200)
}


// 获取 待更新的 配送员信息 
//
// @Description:
// @Author: Rozimamat
// @Time: 2024-03-26 11:50:36
// @receiver
// @param c *gin.Context
func (cms ShipperController) GetShipperUpdateInfo(c *gin.Context) {
	var (
		shipperService = cmsService.NewShipperService(c)
	)
	var areaId int
	admin := permissions.GetAdmin(c)
	switch {
	case admin.IsDealer() || admin.IsDealerSub():
		areaId = admin.AdminAreaID
	default:
		cms.Fail(c, "no_permission", 403)
	}
	
	res :=shipperService.GetShipperUpdateInfo(areaId)
	cms.Success(c,res,"",200)
}


// 获取更新性别和年龄
//
// @Description:
// @Author: Rozimamat
// @Time: 2024-03-26 11:50:36
// @receiver
// @param c *gin.Context
func (cms ShipperController) PostUpdateInfo(c *gin.Context) {
	
	type Params struct {
		ShipperInfo       []shipperRequest.ShipperAgeAndSex ` json:"shipper_info" binding:"required"`                           // 减配送费阶梯阶梯
	}
	var (
		params      Params
		err         = c.ShouldBind(&params)
		shipperService = cmsService.NewShipperService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	shipperService.PostShipperUpdateInfo(params.ShipperInfo)
	  
	cms.Ok(c)
}

// 配送员排行榜
func (s ShipperController) ShipperRankDetail(c *gin.Context) {
	var (
		params shipperResource.ShipperRankDetailRequest
		err    = c.ShouldBind(&params)
		shipperService = cmsService.NewShipperService(c)
		shipperTransformer = cmsTransformer.NewShipperTransformer(c)
		shipperv = shipperv2.NewShipperService(c)
	)

	if err != nil {
		panic(err)
		return
	}
	shipper,thisWeekHistory,lastWeekHistory,orderList,total := shipperService.ShipperRankDetail(params)
	shipperAdmin := models.Admin{ID: shipper.ID}
	newRank, rankGrowthRate, newScore, scoreGrowthRate, startTime, endTime := shipperv.GetShipperRankGrowthRate(&shipperAdmin)
	// 固定的分数
	thisWeekHistory.FinalScore = newScore
	thisWeekHistory.Rank = tools.ToFloat64(newRank)
	response := shipperTransformer.ShipperRankDetailFormat(shipper, thisWeekHistory, lastWeekHistory, orderList, total,startTime,endTime,rankGrowthRate,scoreGrowthRate)
	s.Success(c, response, "msg", 200)
}