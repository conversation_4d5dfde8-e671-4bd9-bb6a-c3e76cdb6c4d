package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	cmsRequests "mulazim-api/requests/cms"
	cmsService "mulazim-api/services/cms"
	cmsTransformer "mulazim-api/transformers/cms"

	"github.com/gin-gonic/gin"
)

type AreaController struct {
	controllers.BaseController
}

// GetList
//
// @Description: 秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 16:13:45
// @receiver
// @param c *gin.Context
func (c AreaController) GetList(g *gin.Context) {
	var (
		params        cmsRequests.AreaList
		err           = g.ShouldBind(&params)
		seckillService = cmsService.NewAreaService(g)
		areaTransformer = cmsTransformer.NewAreaTransformer(g)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(g, params.CityID, params.AreaID)
	// 获取信息
	totalCount,areaList := seckillService.GetAreaList(params)
	// 格式化结果
	areaListFormat := areaTransformer.FormatAreaList(areaList)
	c.Success(g, map[string]interface{}{
		"total":totalCount,
		"items":areaListFormat,
	}, "success", 200)
}

func (c AreaController) GetDetail(g *gin.Context) {
	var (
		params        cmsRequests.AreaDetail
		err           = g.ShouldBind(&params)
		areaService = cmsService.NewAreaService(g)
		areaTransformer = cmsTransformer.NewAreaTransformer(g)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(g, params.CityID, params.AreaID)
	// 获取信息
	area,selfSignInfo,err := areaService.GetAreaDetail(params)
	if err!= nil {
		c.Fail(g,err.Error(),-1000)
		return
	}
	// 格式化结果
	areaFormat := areaTransformer.FormatAreaDetail(area,selfSignInfo)
	c.Success(g,areaFormat,"success",200)
}

func (c AreaController) PostShopLicenseInfo(g *gin.Context) {
	var (
		params        cmsRequests.AreaShopLicenseInfo
		err           = g.ShouldBind(&params)
		areaService = cmsService.NewAreaService(g)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(g, params.CityID, params.AreaID)
	// 获取信息
	err = areaService.SubmitShopLicenseInfo(params)
	if err!= nil {
		c.Fail(g,err.Error(),-1000)
		return 
	}
	c.Ok(g)
}

//修改营业时间
func (c AreaController) PostChangeBusinessTime(g *gin.Context) {
	
	var (
		params        cmsRequests.AreaBusinessTime
		err           = g.ShouldBind(&params)
		areaService = cmsService.NewAreaService(g)
	)
	
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
    // Call service to change business time
    err = areaService.ChangeBusinessTime(params.AreaID, params.BusinessStartTime, params.BusinessEndTime, params.BusinessTimeType)
    if err != nil {
        c.Fail(g,err.Error(),-1000)
        return
    }

    c.Ok(g)
}