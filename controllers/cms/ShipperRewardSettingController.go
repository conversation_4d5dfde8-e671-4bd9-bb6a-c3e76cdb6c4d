package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	shipmentResource "mulazim-api/resources/cms/shipment"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type ShipperRewardSettingController struct {
	controllers.BaseController
}

// PostCreate
//
// @Description: 创建奖励设置
// @Author: Rixat
// @Time: 2023-11-07 03:11:52
// @receiver
// @param c *gin.Context
func (cms ShipperRewardSettingController) PostSave(c *gin.Context) {
	var (
		params        shipmentResource.ShipperRewardSetting
		err           = c.ShouldBind(&params)
		rewardService = cmsService.NewShipperRewardSettingService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	// 创建
	err = rewardService.Save(admin, params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// PostChangeState
//
// @Description: 修改状态
// @Author: Rixat
// @Time: 2023-11-07 03:11:52
// @receiver
// @param c *gin.Context
func (cms ShipperRewardSettingController) PostChangeState(c *gin.Context) {
	type Params struct {
		ID    int `form:"id" binding:""`            // ID
		State int `form:"state" binding:"required"` // 状态
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		rewardService = cmsService.NewShipperRewardSettingService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 修改状态
	admin := permissions.GetAdmin(c)
	err = rewardService.ChangeState(admin, params.ID, params.State)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// GetDetail
//
// @Description: 配送员奖励设置信息
// @Author: Rixat
// @Time: 2023-11-07 02:31:50
// @receiver
// @param c *gin.Context
func (cms ShipperRewardSettingController) GetList(c *gin.Context) {
	type Params struct {
		Page        int    `json:"page" form:"page" binding:"required"`   // 当前页数
		Limit       int    `json:"limit" form:"limit" binding:"required"` // 每页显示数量
		CityId      int    `json:"city_id" form:"city_id" binding:""`     // 地区ID
		AreaID      int    `json:"area_id" form:"area_id" binding:""`     // 区域ID
		State       int    `json:"state" form:"state" binding:""`         // 状态
		SortColumns string `form:"sort_columns"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		rewardService = cmsService.NewShipperRewardSettingService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	params.CityId, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityId, params.AreaID)
	sort := tools.SortColumnParam(params.SortColumns)
	// 获取列表
	result := rewardService.List(params.Page, params.Limit, params.CityId, params.AreaID, params.State, sort)
	cms.Success(c, result, "msg", 200)
}

// GetDetail
//
// @Description: 配送员奖励设置信息
// @Author: Rixat
// @Time: 2023-11-07 02:31:50
// @receiver
// @param c *gin.Context
func (cms ShipperRewardSettingController) GetDetail(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		rewardService = cmsService.NewShipperRewardSettingService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取详情信息
	result, err := rewardService.Detail(params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, result, "msg", 200)
}
