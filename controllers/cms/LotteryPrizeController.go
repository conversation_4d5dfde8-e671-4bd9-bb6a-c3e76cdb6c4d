﻿package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/tools"

	lotteryRequest "mulazim-api/requests/cms/lottery"
	cmsService "mulazim-api/services/cms"
	cmsTransformer "mulazim-api/transformers/cms"

	"github.com/gin-gonic/gin"
)

type LotteryPrizeController struct {
	controllers.BaseController
}

// / PostCreate
//
// @Description: 配送员收入列表
// @Author: Rixat
// @Time: 2023-11-07 02:31:34
// @receiver
// @param c *gin.Context
func (cms *LotteryPrizeController) PostCreate(c *gin.Context) {
	var (
		params        lotteryRequest.LotteryPrizeCreateRequest
		err           = c.ShouldBind(&params)
		lotteryService = cmsService.NewLotteryPrizeService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	admin := permissions.GetAdmin(c)
	if err := checkPrizeType(c, admin, params.CityID, params.AreaID); err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}

	// 创建
	err = lotteryService.Create(admin, params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, "", "success", 200)
}

func (cms *LotteryPrizeController) PostEdit(c *gin.Context) {
	var (
		params        lotteryRequest.LotteryPrizeEditRequest
		err           = c.ShouldBind(&params)
		lotteryService = cmsService.NewLotteryPrizeService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	if err := checkPrizeType(c, admin, params.CityID, params.AreaID); err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}

	// 以下逻辑已被实现（使用是否有绑定过的LotteryActivityLevelPrize记录）
	//// 活动开始后不能修改 或者 删除
	//if err := checkModifyOrDeletePrize(c, params.ID); err != nil {
	//	cms.Fail(c, err.Error(), -1000)
	//	return
	//}

	// 修改
	err = lotteryService.Edit(admin, params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, "", "success", 200)
}

// GetDetail
//
// @Description: 配送员收入详情
// @Author: Rixat
// @Time: 2023-11-07 02:31:50
// @receiver
// @param c *gin.Context
func (cms *LotteryPrizeController) GetDetail(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		lotteryService = cmsService.NewLotteryPrizeService(c)
		cmsTransformer = cmsTransformer.NewLotteryTransformer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取详情信息
	result, err := lotteryService.Detail(params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	resFormat := cmsTransformer.FormatLotteryPrizeDetail(result)
	cms.Success(c, resFormat, "success", 200)
}

// GetList
//
// @Description: 配送员收入列表
// @Author: Rixat
// @Time: 2023-11-07 02:32:03
// @receiver
// @param c *gin.Context
func (cms *LotteryPrizeController) GetList(c *gin.Context) {
	type Params struct {
		Kw          string `form:"kw" binding:""`                 // 关键字(姓名或手机号)
		Page        int `form:"page" binding:""`                 // 关键字(姓名或手机号)
		Limit       int `form:"limit" binding:""`                 // 关键字(姓名或手机号)
		Type uint `form:"type" binding:"omitempty,oneof=1 2"`
		CityID int  `form:"city_id" binding:""`
		AreaID int  `form:"area_id" binding:""` 
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		lotteryService = cmsService.NewLotteryPrizeService(c)
		cmsTransformer = cmsTransformer.NewLotteryTransformer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	admin := permissions.GetAdmin(c)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	if err := checkPrizeType(c, admin, params.CityID, params.AreaID); err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}

	// 获取信息
	total, result := lotteryService.List(params.Page, params.Limit, params.Kw,
		params.Type, params.CityID, params.AreaID)
	resFormat := cmsTransformer.FormatLotteryPrizeList(result)
	cms.Success(c, map[string]interface{}{
		"total": total,
		"items":  resFormat,
	}, "success", 200)
}



func (cms *LotteryPrizeController) PostDelete(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		lotteryService = cmsService.NewLotteryPrizeService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 以下逻辑已被实现（使用是否有绑定过的LotteryActivityLevelPrize记录）
	//// 活动开始后不能修改 或者 删除
	//if err := checkModifyOrDeletePrize(c, params.ID); err != nil {
	//	cms.Fail(c, err.Error(), -1000)
	//	return
	//}

	// 获取详情信息
	err = lotteryService.PostDelete(params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}



func (cms *LotteryPrizeController) PostChangeState(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"`
		State string `form:"state" binding:"required,oneof=0 1"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		lotteryService = cmsService.NewLotteryPrizeService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取详情信息
	err = lotteryService.ChangeState(params.ID,tools.ToInt(params.State))
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// checkPrizeType 方法 - 在创建/编辑奖品时，检查奖品类型：
// - 如果奖品类型为 订单排行榜活动奖品，则不允许 让非管理员用户创建平台级别奖品
// - 如果奖品类型为 抽奖活动奖品，则不允许让 指定区域、城市ID
func checkPrizeType(ctx *gin.Context, admin models.Admin, cityID, areaID int) error {
	// if lpType == models.LotteryPrizeTypeActivity {
	// 	// 非管理员用户禁止创建平台级别奖品，只能创建自己区域的奖品
	// 	if !permissions.IsAdmin(admin) {
	// 		_cityID, _areaID := permissions.GetAdminAreaInfo(ctx, cityID, areaID)
	// 		if _cityID != cityID || _areaID != areaID {
	// 			return errors.New("请选择已绑定该账号的城市和区域")
	// 		}
	// 	}
	// } else if lpType == models.LotteryPrizeTypeLottery {
	// 	// 抽奖活动的奖品不能有区域、城市维度的数据
	// 	if cityID != 0 || areaID != 0 {
	// 		return errors.New("无法指定抽奖活动奖品至城市或区域")
	// 	}
	// }

	return nil
}

//// checkModifyOrDeletePrize - 检查奖品是否可以被更改 或者 删除
//// - 取决于绑定该奖品的活动是否已经开始，已开始的活动的奖品是不能被修改或者删除的。
//func checkModifyOrDeletePrize(ctx *gin.Context, prizeID int) error {
//	_lotteryActSvc := cmsService.NewLotteryActivityService(ctx)
//	if startedActivities, err := _lotteryActSvc.GetStartedActivityByPrizeID(prizeID); err != nil {
//		return err
//	} else {
//		if len(startedActivities) > 0 {
//			return errors.New("奖品已被绑定的已开始活动关联，无法修改/删除奖品")
//		}
//	}
//	return nil
//}