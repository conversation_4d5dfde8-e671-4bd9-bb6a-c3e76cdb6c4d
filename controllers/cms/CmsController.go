package cms

import (
	"encoding/json"
	"fmt"
	"github.com/golang-module/carbon/v2"
	"math/rand"
	"mime/multipart"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/models"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"
	cmsTransformer "mulazim-api/transformers/cms"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type CmsController struct {
	controllers.BaseController
}

var limitGoroutine chan int

func init() {
	limitGoroutine = make(chan int, 1)
}

const ORDER_NEW = 1             //新订单
const ORDER_CONFIRM = 2         //确认订单
const ORDER_WAITING_RECEIVE = 3 //等待接收订单
const ORDER_RECEIVE = 4         //已接受订单
const ORDER_READY_FOR_SEND = 5  //订单已准备
const ORDER_SENDING = 6         //订单配送中
const ORDER_SENDING_FINISH = 7  //订单配送完成
const ORDER_CANCEL = 8          //取消订单
const ORDER_RECEIVE_REFUSE = 9  //餐厅拒绝接单
const ORDER_SENDING_FAIL = 10   //订单配送失败
// GetMenuList
//
//	@Description: 获取菜单，按照不同的管理员权限获取不同的参数
//	@author: Alimjan
//	@Time: 2023-06-05 12:59:10
//	@receiver cms CmsController
//	@param c *gin.Context
func (cms CmsController) GetMenuList(c *gin.Context) {
	var service = cmsService.NewCmsService(c)
	var transformer = cmsTransformer.NewCmsTransformer(c)
	//获取用户及权限
	//根据用户权限初始化项目
	anyAdmin, _ := c.Get("admin")
	adminMiddleware := anyAdmin.(models.Admin)
	lang, ok := c.Get("lang")
	if !ok {
		lang = "ug"
	}

	var cacheKey = fmt.Sprintf("a_m_l_%d_%s", adminMiddleware.ID, lang)
	tools.GetRedisHelper().Del(c, cacheKey)
	//获取菜单,权限,用户信息
	menuJson := tools.Remember(c, cacheKey, 1*time.Second, func() interface{} {
		adminData := service.GetAdminAndRole(c, adminMiddleware.ID)
		menuList := service.GetMenuList(c)
		badge := service.NotiBadgeNumber(c, adminMiddleware)
		menu, permissions, user := transformer.FormatMenu(adminData, menuList, badge, lang.(string))
		data := make(map[string]interface{})
		data["menus"] = menu
		data["permissions"] = permissions
		data["user"] = user
		return data
	})
	var data map[string]interface{}
	json.Unmarshal([]byte(menuJson), &data)
	cms.Success(c, data, "", 200)
}

// 获取新订单
func (s CmsController) GetNewOrderCount(c *gin.Context) {

	var (
		service = cmsService.NewCmsService(c)
	)
	anyAdmin, _ := c.Get("admin")
	adminMidlleWare := anyAdmin.(models.Admin)

	// 获取管理员ID
	adminId := adminMidlleWare.ID
	// 获取管理员分配的餐厅编号
	restaurantIds := service.GetRestaurantIds(c, adminId)
	// 获取新订单列表和数量
	orders, count := service.GetToDayOrder(adminId, restaurantIds, []int{2, 3})
	//获取订单数量
	failed, canceled, refused, waiting, sending := service.OrderCountAllState(adminId, restaurantIds)
	countByOrderState := make(map[string]interface{}, 0)

	countByOrderState["failed"] = failed
	countByOrderState["canceled"] = canceled
	countByOrderState["refused"] = refused
	countByOrderState["waiting"] = waiting
	countByOrderState["sending"] = sending

	shipperCanceledCount := service.ShipperCanceledOrderCount(adminId, restaurantIds)

	// 计算当前时间 + 5 分钟
	currentTime := carbon.Now("Asia/Shanghai").AddMinutes(5)

    // 根据订单信息判断是否需要播放语音提示
    playVoice := false
    for _, order := range orders {
        orderType := tools.ToInt(order["order_type"])
        printTimeStr, ok := order["print_time"].(string)
        if !ok {
            continue
        }

        // 使用 carbon 来解析时间
        printTime := carbon.Parse(printTimeStr)

        if orderType == 1 {
            // 实时订单：直接播放语音
            playVoice = true
            break
        } else if orderType == 0 {
            if currentTime.DiffInMinutes(printTime) < 0 {  
                playVoice = true
                break
            }
        }
    }

	// 返回结果
	c.JSON(200, gin.H{
		"count":                count,
		"order":                orders,
		"countByOrderState":    countByOrderState,
		"shipperCanceledCount": shipperCanceledCount,
		"play_voice":           playVoice,
		"status":               200,
		"msg":                  "获取成功",
	})
}

// GetCityList
//
//	@Description: 获取城市列表
//	@author: Alimjan
//	@Time: 2023-06-05 16:30:26
//	@receiver s CmsController
//	@param c *gin.Context
func (s CmsController) GetCityList(c *gin.Context) {

	var service = cmsService.NewCmsService(c)
	var tranformer = cmsTransformer.NewCmsTransformer(c)
	anyAdmin, _ := c.Get("admin")
	adminMidlleWare := anyAdmin.(models.Admin)
	cities := service.GetCityList(c, adminMidlleWare)
	data := tranformer.FormatCities(cities)
	s.Success(c, data, "", 200)
}

// GetAreaList
//
//	@Description: 获取区域列表
//	@author: Alimjan
//	@Time: 2023-06-05 18:28:17
//	@receiver s CmsController
//	@param c *gin.Context
func (s CmsController) GetAreaList(c *gin.Context) {
	type AreaListRequest struct {
		CityId int `form:"city_id" binding:"required"`
	}
	var request AreaListRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	var service = cmsService.NewCmsService(c)
	var tranformer = cmsTransformer.NewCmsTransformer(c)
	anyAdmin, _ := c.Get("admin")
	adminMidlleWare := anyAdmin.(models.Admin)
	areas := service.GetAreaList(c, adminMidlleWare, request.CityId)
	data := tranformer.FormatAreas(areas)
	s.Success(c, data, "", 200)
}

// GetOrderStateList
//
//	@Description: 获取所有状态
//	@author: Alimjan
//	@Time: 2023-06-06 15:37:07
//	@receiver s CmsController
//	@param c *gin.Context
func (s CmsController) GetOrderStateList(c *gin.Context) {
	var service = cmsService.NewCmsService(c)
	var tranformer = cmsTransformer.NewCmsTransformer(c)
	stateList := service.GetStateList(c)
	data := tranformer.FormatStateList(stateList)
	s.Success(c, data, "", 200)
}

// GetCategoryList
//
//	@Description: 获取业务分类
//	@author: Alimjan
//	@Time: 2023-06-06 16:10:11
//	@receiver s CmsController
//	@param c *gin.Context
func (s CmsController) GetCategoryList(c *gin.Context) {
	var service = cmsService.NewCmsService(c)
	var tranformer = cmsTransformer.NewCmsTransformer(c)
	categoryList := service.GetCategoryList(c)
	data := tranformer.FormatCategoryList(categoryList)
	s.Success(c, data, "", 200)
}

// GetOrderCancelReasonList
//
//	@Description: 获取取消订单原因列表
//	@author: Alimjan
//	@Time: 2023-06-06 16:21:24
//	@receiver s CmsController
//	@param context *gin.Context
func (s CmsController) GetOrderCancelReasonList(context *gin.Context) {
	var service = cmsService.NewCmsService(context)
	var tranformer = cmsTransformer.NewCmsTransformer(context)
	reasonList := service.GetOrderCancelReasonList(context)
	data := tranformer.FormatOrderCancelReasonList(reasonList)
	s.Success(context, data, "", 200)
}

// GetStreetList
//
//	@Description: 获取街道列表
//	@author: Alimjan
//	@Time: 2023-06-06 16:30:38
//	@receiver s CmsController
//	@param context *gin.Context
func (s CmsController) GetStreetList(context *gin.Context) {
	type StreetListRequest struct {
		AreaId int `form:"area_id" binding:"required"`
	}
	var request StreetListRequest
	if err := context.ShouldBind(&request); err != nil {
		panic(err)
	}
	var service = cmsService.NewCmsService(context)
	var tranformer = cmsTransformer.NewCmsTransformer(context)
	anyAdmin, _ := context.Get("admin")
	adminMidlleWare := anyAdmin.(models.Admin)
	streetList := service.GetStreetList(context, adminMidlleWare, request.AreaId)
	data := tranformer.FormatStreetList(streetList)
	s.Success(context, data, "", 200)
}

// GetRestaurantList
//
//	@Description: 获取餐厅列表
//	@author: Alimjan
//	@Time: 2023-06-06 17:41:25
//	@receiver s CmsController
//	@param context *gin.Context
func (s CmsController) GetRestaurantList(context *gin.Context) {
	type StreetListRequest struct {
		CityId   int `form:"city_id" binding:""`
		AreaId   int `form:"area_id" binding:""`
		StreetId int `form:"street_id" binding:""`
	}
	var request StreetListRequest
	if err := context.ShouldBind(&request); err != nil {
		panic(err)
	}
	var service = cmsService.NewCmsService(context)
	var tranformer = cmsTransformer.NewCmsTransformer(context)
	anyAdmin, _ := context.Get("admin")
	adminMidlleWare := anyAdmin.(models.Admin)
	resList := service.GetRestaurantList(context, adminMidlleWare, request.CityId, request.AreaId, request.StreetId)
	data := tranformer.FormatRestaurantList(resList)
	s.Success(context, data, "", 200)
}

// GetShipperList
//
//	@Description: 获取骑手列表
//	@author: Alimjan
//	@Time: 2023-06-06 18:04:55
//	@receiver s CmsController
//	@param context *gin.Context
func (s CmsController) GetShipperList(context *gin.Context) {
	type ShipperListRequest struct {
		ResId int `uri:"res_id" binding:""`
	}
	var request ShipperListRequest
	if err := context.ShouldBindUri(&request); err != nil {
		panic(err)
	}

	var service = cmsService.NewCmsService(context)
	var tranformer = cmsTransformer.NewCmsTransformer(context)
	anyAdmin, _ := context.Get("admin")
	adminMidlleWare := anyAdmin.(models.Admin)
	shipperList, adminOrderCount := service.GetShipperList(context, adminMidlleWare, request.ResId)
	data := tranformer.FormatShipperList(shipperList, adminOrderCount)
	s.Success(context, data, "", 200)
}

// PostGrantComplete
//
//	@Description: 给配送员完成订单权限
//	@author: Alimjan
//	@Time: 2023-06-07 11:45:05
//	@receiver s CmsController
//	@param context *gin.Context
func (s CmsController) PostGrantComplete(context *gin.Context) {
	type GrantCompleteRequest struct {
		OrderId int `form:"id" binding:"required"`
	}

	var request GrantCompleteRequest
	if err := context.ShouldBind(&request); err != nil {
		panic(err)
	}
	var service = cmsService.NewCmsService(context)
	err := service.GrantComplete(context, request.OrderId)
	if err != nil {
		s.Fail(context, err.Error(), 400)
		return
	}
	s.Success(context, nil, "", 200)
}

// PostSetShipper
//
//	@Description: 订单分配配送员
//	@author: Alimjan
//	@Time: 2023-06-07 12:26:12
//	@receiver s CmsController
//	@param context *gin.Context
func (s CmsController) PostSetShipper(context *gin.Context) {
	type SetShipperRequest struct {
		OrderId   int `form:"order_id" binding:"required"`
		ShipperId int `form:"admin_id" binding:"required"`
	}

	var request SetShipperRequest
	if err := context.ShouldBind(&request); err != nil {
		panic(err)
	}
	var service = cmsService.NewCmsService(context)
	err := service.SetShipper(context, request.OrderId, request.ShipperId)
	if err != nil {
		s.Fail(context, err.Error(), 400)
		return
	}
	s.Success(context, nil, "", 200)
}

// GetOrderList
//
//	@Description: 获取订单列表
//	@author: Alimjan
//	@Time: 2023-06-07 17:04:05
//	@receiver s CmsController
//	@param context *gin.Context
func (s CmsController) GetOrderList(context *gin.Context) {
	type OrderListRequest struct {
		CityId       int    `form:"city_id" binding:""`
		AreaId       int    `form:"area_id" binding:""`
		StreetId     int    `form:"street_id" binding:""`
		ResId        int    `form:"restuarant_id" binding:""`
		State        int    `form:"state" binding:""`
		TimeFrom     string `form:"time_from" binding:""`
		Search       string `form:"sSearch" binding:""`
		TimeTo       string `form:"time_to" binding:""`
		IsTimeOut    int    `form:"is_time_out" binding:""`
		AdminId      int    `form:"admin_id" binding:""`
		CategoryId   int    `form:"category_id" binding:""`
		OrderType    int    `form:"order_type" binding:""`
		Page         int    `form:"page" binding:""`
		PageSize     int    `form:"page_size" binding:""`
		SortCol      int    `form:"iSortCol_0" binding:""`
		SortDir      string `form:"sSortDir_0" binding:""`
		DeliveryType int    `form:"delivery_type" binding:""`
	}
	var request OrderListRequest
	if err := context.ShouldBind(&request); err != nil {
		panic(err)
	}
	var SortColName string
	if request.SortCol > 1 {
		SortColName = context.Query("mDataProp_" + strconv.Itoa(request.SortCol))
	}
	var service = cmsService.NewCmsService(context)
	var tranformer = cmsTransformer.NewCmsTransformer(context)
	anyAdmin, _ := context.Get("admin")
	adminMidlleWare := anyAdmin.(models.Admin)
	header, list := service.GetOrderList(context,
		adminMidlleWare,
		request.CityId,
		request.AreaId,
		request.StreetId,
		request.ResId,
		request.State,
		request.TimeFrom,
		request.TimeTo,
		request.IsTimeOut,
		request.AdminId,
		request.CategoryId,
		request.OrderType,
		request.Page,
		request.PageSize,
		request.Search,
		request.SortCol,
		request.SortDir,
		SortColName,
		request.DeliveryType,
	)

	data := tranformer.FormatOrderList(header, list)
	context.JSON(200, data)
	//s.Success(context,data,"",200)
}

// 描述：订单重新分配配送员
// 作者：Qurbanjan
// 文件：CmsController.go
// 修改时间：2023/08/18 19:08

func (cms CmsController) PostEditShipper(context *gin.Context) {
	type PostEditShipperRequest struct {
		NewShipperId int `form:"admin_id" binding:"required"`
		OldShipperId int `form:"old_shipper_id" binding:"required"`
		OrderId      int `form:"order_id" binding:"required"`
	}
	var request PostEditShipperRequest
	if err := context.ShouldBind(&request); err != nil {
		panic(err)
	}

	anyAdmin, _ := context.Get("admin")
	adminMidlleWare := anyAdmin.(models.Admin)

	var service = cmsService.NewCmsService(context)
	err := service.EditShipper(request.OldShipperId, request.NewShipperId, request.OrderId, adminMidlleWare)

	if err != nil {
		cms.Fail(context, err.Error(), 400)
		return
	}

	cms.Success(context, nil, "success", 200)
}

func (cms CmsController) GetOrderDetail(c *gin.Context) {
	type Params struct {
		OrderID int `form:"order_id" binding:"required"`
	}
	var (
		params       Params
		err          = c.ShouldBind(&params)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	var (
		orderService = cmsService.NewCmsService(c)
		orderTrans   = cmsTransformer.NewOrderTransformer(c)
		value, _     = c.Get("admin")
		admin        = value.(models.Admin)
	)

	// 获取详情内容
	orderDetail := orderService.OrderDetail(admin, params.OrderID)
	// 格式化
	result := orderTrans.FormatOrderInfo(orderDetail)
	cms.Success(c, result, "msg", 200)
}

// UploadImage
//
// @Description: 上传图片
// @Author: Rixat
// @Time: 2023-10-26 02:37:00
// @receiver
// @param c *gin.Context
func (cms CmsController) UploadImage(c *gin.Context) {
	var allowTypes []string = []string{".jpg", ".jpeg", ".png"}
	typeName := c.PostForm("type_name")
	file, err := c.FormFile("image")
	if err != nil {
		cms.Fail(c, "无法获取上传文件", http.StatusBadRequest)
		return
	}

	ext := filepath.Ext(file.Filename)

	if ext != ".jpg" && ext != ".jpeg" && ext != ".png" {
		cms.Fail(c, "只允许jpg|jpeg|png类型图片", http.StatusBadRequest)
		return
	}

	cms.upload(typeName, file, ext, allowTypes, c)
}

// UploadZipFile 文件上传
func (cms CmsController) UploadAdvertMaterialFile(c *gin.Context) {
	var allowTypes []string = []string{".zip"}
	typeName := "advert-material-file"
	file, err := c.FormFile("file")
	if err != nil {
		cms.Fail(c, "无法获取上传文件", http.StatusBadRequest)
		return
	}

	ext := filepath.Ext(file.Filename)

	cms.upload(typeName, file, ext, allowTypes, c)
}

// 上传文件
func (cms CmsController) upload(typeName string, file *multipart.FileHeader, ext string, allowTypes []string, c *gin.Context) {
	if !tools.InArray(ext, allowTypes) {
		str := strings.Join(allowTypes, "，")
		cms.Fail(c, "只允许"+str+"类型文件", http.StatusBadRequest)
		return
	}
	// 临时的
	fileName := cms.generateRandomFilename() + ext
	rootDir := configs.MyApp.UploadRootDir
	imageDir := "uploads/" + typeName
	if _, err := os.Stat(rootDir + imageDir); os.IsNotExist(err) {
		// 目录不存在，创建目录
		err := os.MkdirAll(rootDir+imageDir, 0755)
		if err != nil {
			// 创建目录失败
			fmt.Println("目录创建失败:", err)
			return
		}
	}
	imagePath := imageDir + "/" + fileName
	fullPath := rootDir + imagePath
	if err := c.SaveUploadedFile(file, fullPath); err != nil {
		cms.Fail(c, "上传保存失败", http.StatusBadRequest)
		return
	}

	fileUrl := configs.MyApp.CdnUrl + imagePath

	cms.Success(c, map[string]interface{}{
		"url":        fileUrl,
		"image_path": imagePath,
	}, "上传成功", http.StatusOK)
}

// 生成随机图片名称
func (cms CmsController) generateRandomFilename() string {
	// 生成当前时间的字符串形式
	timestamp := time.Now().Format("20060102150405")
	// 生成五位随机数字（00000 - 99999）
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Intn(100000)
	randomString := strconv.Itoa(randomNumber)
	paddedRandomString := fmt.Sprintf("%05s", randomString)
	// 将随机数字追加到时间戳后面
	return timestamp + paddedRandomString
}

//获取 代理 意愿 列表
func (cms CmsController) GetAgentForm(c *gin.Context) {
	
	var (
		
		orderService = cmsService.NewCmsService(c)
		
	)
	page :=tools.ToInt(c.DefaultQuery("page","1"))
	limit :=tools.ToInt(c.DefaultQuery("page_size","10"))
	sort :=c.DefaultQuery("sort","created_at desc")
	
	// 获取详情内容
	result := orderService.AgentForm(c,page,limit,sort)
	cms.Success(c, result, "msg", 200)
}
