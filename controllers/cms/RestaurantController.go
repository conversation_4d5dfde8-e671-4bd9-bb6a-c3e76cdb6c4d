package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/permissions"
	"mulazim-api/requests/RestaurantRequest"
	"mulazim-api/resources"
	"mulazim-api/resources/cms"
	"mulazim-api/services"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type RestaurantController struct {
	controllers.BaseController
}

func (r *RestaurantController) GetByAreaId(c *gin.Context) {
	var (
		restaurantService = services.NewRestaurantService(c)
		request           RestaurantRequest.GetRestaurantByIdRequest
		err               = c.ShouldBindQuery(&request)
		admin             = permissions.GetAdmin(c)
		areaId            = tools.ToInt(c.Query("area_id"))
		langUtil          = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}

	if admin.IsAdmin() || admin.IsOwner() {
		if 0 == areaId {
			r.Fail(c, "area_id_can_not_be_empty", 422)
			return
		}
	} else if admin.IsDealer() || admin.IsDealerSub() {
		areaId = admin.AdminAreaID
	} else {
		r.Fail(c, "forbidden", 403)
		return
	}
	groupId := tools.ToInt(c.Query("template_id"))
	restaurants, total := restaurantService.GetOpenRestaurantsByAreaIdPaginate(areaId, request.Keyword, request.State, request.Page, request.Limit, groupId,request.OpenStates)

	r.Success(c, cms.NewRestaurantListForMarketingSearchResponse(cms.NewRestaurantListForMarketingSearchCollection(restaurants, langUtil), total, request.Page, request.Limit), "msg", 200)
}


//批量创建 配送区域
func (r *RestaurantController) CreateShippingArea(c *gin.Context) {
	
	 
	type Params struct {
		Ids string `form:"ids" json:"ids"`
		Mode int `form:"mode" json:"mode"`
		Steps []resources.BuildingShippingAreaSteps `form:"steps" json:"steps"`
	}
	
	var (
		restaurantService = services.NewRestaurantService(c)
		params Params
		err               = c.ShouldBind(&params)
		// admin             = permissions.GetAdmin(c)
		// langUtil          = lang.LangUtil{
		// 	Lang: c.Param("locale"),
		// }
	)
	if err != nil {
		panic(err)
		return
	}
	
	data,err := restaurantService.CreateShippingArea(params.Ids,params.Mode,params.Steps)
	if err !=nil {
		r.Fail(c,err.Error(),-1000)
		return
	}
	r.Success(c, data, "msg", 200)
}


//批量创建 配送区域
func (r *RestaurantController) CreateShippingAreaCheck(c *gin.Context) {
	
	 
	
	
	var (
		restaurantService = services.NewRestaurantService(c)
	)
	
	key :=c.Query("key")
	
	data := restaurantService.CreateShippingAreaCheck(key)
	r.Success(c, data, "msg", 200)
}