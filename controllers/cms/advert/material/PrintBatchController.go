package material

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"mulazim-api/controllers"
	"mulazim-api/jobs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/cms/advert/materialRequest/batchRequest"
	"mulazim-api/resources/cms/advert/materialResource"
	"mulazim-api/services"
	"mulazim-api/tools"
	"net/http"
)

// 宣传材料批次管理控制器

type PrintBatchController struct {
	controllers.BaseController
}

// Create 创建
func (c *PrintBatchController) Create(ctx *gin.Context) {
	var (
		request    batchRequest.CreateRequest
		material   models.AdvertMaterial
		materialId = tools.ToInt(ctx.Param("materialId"))

		err     = ctx.ShouldBindJSON(&request)
		service services.AdvertMaterialPrintBatchService
	)
	if err != nil {
		panic(err)
		return
	}
	rs := tools.GetDB().First(&material, materialId)
	admin := permissions.GetAdmin(ctx)
	if rs.Error != nil {
		msg := fmt.Sprintf("宣传材料打印批次创建： 没有找到相关宣传材料: Material ID: %d, %s", materialId, rs.Error.Error())
		tools.Logger.Error(msg)
		c.Fail(ctx, "failed", 400)
		return
	}
	if rs.RowsAffected == 0 {
		c.Fail(ctx, "advert_material_not_found", 400)
		return
	}
	batch, err := service.Create(admin, material, request)
	if err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	jobs.PushMessageToAdvertMaterialPrintBatchJob(batch.ID)
	c.Success(ctx, batch, "msg", 200)
}

// List
func (c *PrintBatchController) List(ctx *gin.Context) {
	var (
		materialId = tools.ToInt(ctx.Param("materialId"))
		material   models.AdvertMaterial
		service    services.AdvertMaterialPrintBatchService
		page       = tools.ToInt(ctx.DefaultQuery("page", "1"))
		limit      = tools.ToInt(ctx.DefaultQuery("limit", "10"))
		state      = tools.ToInt(ctx.DefaultQuery("state", "0"))
		langUtil   = lang.LangUtil{
			Lang: ctx.Param("locale"),
		}
	)
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	if state < 0 || state > 3 {
		state = 0
	}
	rs := tools.GetDB().First(&material, materialId)
	if rs.Error != nil {
		msg := fmt.Sprintf("宣传材料打印批次列表： 没有找到相关宣传材料: Material ID: %d, %s", materialId, rs.Error.Error())
		tools.Logger.Error(msg)
		c.Fail(ctx, "failed", 400)
		return
	}
	if rs.RowsAffected == 0 {
		c.Fail(ctx, "advert_material_not_found", 400)
		return
	}
	batchs, total, err := service.List(material, page, limit, state)
	if err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	c.Success(ctx, materialResource.NewPrintBatchListResource(batchs, total, langUtil), "msg", 200)
}

// Download 下载
func (c *PrintBatchController) Download(ctx *gin.Context) {
	var (
		batchId = tools.ToInt(ctx.Param("batchId"))
		service services.AdvertMaterialPrintBatchService
	)
	batch, err := service.FindByIdWithMaterial(batchId)
	if err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	if batch.ID == 0 {
		c.Fail(ctx, "advert_material_print_batch_not_found", 400)
		return
	}

	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	// 创建一个工作表
	sheet := "Sheet1"
	index, err := f.NewSheet(sheet)
	if err != nil {
		fmt.Println(err)
		return
	}
	f.SetActiveSheet(index)

	headers := map[string]string{
		"A1": "二维码编码",
		"B1": "二维码地址",
	}
	for k, v := range headers {
		f.SetCellValue(sheet, k, v)
	}

	var codes []models.AdvertMaterialCode = make([]models.AdvertMaterialCode, 0)
	index = 0
	var lastId int = 0
	for true {
		tools.GetDB().
			Model(models.AdvertMaterialCode{}).
			Where("advert_material_print_batch_id = ?", batch.ID).
			Order("id asc").
			Where("id > ?", lastId).
			Limit(1000).
			Find(&codes)
		if len(codes) == 0 {
			break
		}
		for _, code := range codes {
			f.SetCellValue(sheet, fmt.Sprintf("A%d", index+2), code.Code)
			f.SetCellValue(sheet, fmt.Sprintf("B%d", index+2), code.QrLink)
			index++
			lastId = code.ID
		}
	}
	// 返回csv格式的文件
	downLoadFileName := fmt.Sprintf("%s批次%d.xlsx", batch.AdvertMaterial.NameZh, batch.BatchNo)
	// 设置响应头
	ctx.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Access-Control-Expose-Headers", "Content-Disposition")
	// 写入到响应
	if err := f.Write(ctx.Writer); err != nil {
		ctx.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}
