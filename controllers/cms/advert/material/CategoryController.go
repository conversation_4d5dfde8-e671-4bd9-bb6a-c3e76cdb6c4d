package material

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	lang2 "mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/requests/cms/advert/materialRequest/categoryRequest"
	"mulazim-api/resources/cms/advert/materialResource"
	"mulazim-api/services"
	"mulazim-api/tools"
)

type CategoryController struct {
	controllers.BaseController
}

// Create 创建
func (c *CategoryController) Create(ctx *gin.Context) {
	var (
		request categoryRequest.CreateRequest
		err     = ctx.ShouldBindJSON(&request)
		service = services.AdvertMaterialCategoryService{}
	)
	if err != nil {
		panic(err)
		return
	}
	category, err := service.Create(request)
	if err != nil {
		c.Fail(ctx, "failed", 400)
		return
	}
	c.Success(ctx, materialResource.NewCategoryResource(category), "msg", 200)
}

// Update 更新
func (c *CategoryController) Update(ctx *gin.Context) {
	var (
		request  categoryRequest.CreateRequest
		err          = ctx.ShouldBindJSON(&request)
		service      = services.AdvertMaterialCategoryService{}
		id       int = tools.ToInt(ctx.Param("id"))
		category models.AdvertMaterialCategory
	)
	if err != nil {
		panic(err)
		return
	}
	rs := tools.GetDB().First(&category, id)
	if rs.Error != nil {
		c.Fail(ctx, "failed", 400)
	}
	if 0 == rs.RowsAffected {
		c.Fail(ctx, "not_found", 404)
	}
	category, err = service.Update(category, request)
	if err != nil {
		c.Fail(ctx, "failed", 400)
		return
	}
	c.Success(ctx, materialResource.NewCategoryResource(category), "msg", 200)
}

// Detail 查看
func (c *CategoryController) Detail(ctx *gin.Context) {
	var (
		service      = services.AdvertMaterialCategoryService{}
		id       int = tools.ToInt(ctx.Param("id"))
		category models.AdvertMaterialCategory
	)
	category, err := service.FindById(id)
	if err != nil {
		c.Fail(ctx, "failed", 400)
	}
	if 0 == category.ID {
		c.Fail(ctx, "not_found", 404)
	}
	c.Success(ctx, materialResource.NewCategoryResource(category), "msg", 200)
}

// List 列表
func (c *CategoryController) List(ctx *gin.Context) {
	var (
		service = services.AdvertMaterialCategoryService{}
		page    = tools.ToInt(ctx.DefaultQuery("page", "1"))
		limit   = tools.ToInt(ctx.DefaultQuery("limit", "10"))
		state   = tools.ToInt(ctx.DefaultQuery("state", "0"))
		lang    = lang2.LangUtil{
			Lang: ctx.Param("locale"),
		}
	)
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	categories, total, err := service.FindAll(state, page, limit)
	if err != nil {
		c.Fail(ctx, "failed", 400)
		return
	}
	c.Success(ctx, materialResource.NewCategoryCollectionResource(categories, total, lang), "msg", 200)
}

// UpdateState 更新状态
func (c *CategoryController) UpdateState(ctx *gin.Context) {
	var (
		service      = services.AdvertMaterialCategoryService{}
		id       int = tools.ToInt(ctx.Param("id"))
		state    int = tools.ToInt(ctx.Query("state"))
		category models.AdvertMaterialCategory
	)
	if id == 1 {
		c.Fail(ctx, "no_permission", 403)
		return
	}
	if state < 1 || state > 2 {
		c.Fail(ctx, "state_error", 422)
		return
	}
	rs := tools.GetDB().First(&category, id)
	if rs.Error != nil {
		c.Fail(ctx, "failed", 400)
	}
	if 0 == rs.RowsAffected {
		c.Fail(ctx, "not_found", 404)
	}
	category, err := service.UpdateState(category, state)
	if err != nil {
		c.Fail(ctx, "failed", 400)
		return
	}
	c.Success(ctx, materialResource.NewCategoryResource(category), "msg", 200)
}

// Delete 删除
func (c *CategoryController) Delete(ctx *gin.Context) {
	var (
		service     = services.AdvertMaterialCategoryService{}
		id      int = tools.ToInt(ctx.Param("id"))
	)
	if id <= 1 {
		c.Fail(ctx, "no_permission", 403)
		return
	}
	err := service.Delete(id)
	if err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	c.Success(ctx, nil, "msg", 200)
}
