package advert

import (
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/cms/advert/advert"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"
	cmsTransformer "mulazim-api/transformers/cms"

	"github.com/gin-gonic/gin"
)

type AdvertController struct {
	controllers.BaseController
}

// 获取广告列表
func (a *AdvertController) List(c *gin.Context) {
	var (
		request           advert.AdvertListRequest                 // 定义请求参数变量
		advertService     = cmsService.NewAdvertService(c)         // 初始化 Service
		advertTransformer = cmsTransformer.NewAdvertTransformer(c) // 初始化 Transformer
	)

	// 绑定 URL 查询参数到 request 结构体
	if err := c.ShouldBindQuery(&request); err != nil {
		panic(err)
		return
	}

	// 获取分页信息
	pagination := tools.GetPagination(c)
	request.Page = pagination.Page
	request.Limit = pagination.Limit

	// 获取当前管理员区域信息
	request.CityId, request.AreaId = permissions.GetAdminAreaInfo(c, request.CityId, request.AreaId)

	// 获取广告列表
	list, total, err := advertService.GetAdvertList(request)

	if err != nil {
		a.Fail(c, "fail", -1000)
		return
	}

	// 格式化数据
	advertList := advertTransformer.FormatAdvertList(list)
	a.Success(c, gin.H{
		"items": advertList,
		"total": total,
	}, "success", 200)
}

// 创建广告
func (a *AdvertController) Create(c *gin.Context) {

	var (
		request       advert.AdvertCreateRequest       // 定义请求参数变量
		advertService = cmsService.NewAdvertService(c) // 初始化 Service
		user, _ = c.Get("admin")
		admin = user.(models.Admin)
	)

	if err := c.ShouldBindJSON(&request); err != nil {
		panic(err)
		return
	}
	_, areaId := permissions.GetAdminAreaInfo(c, 0, 0)
	if areaId != 0 {
		request.AreaIds = []int64{int64(areaId)}
	}
	// 获取广告列表
	err := advertService.CreateAdvert(request,admin)

	if err != nil {
		a.Fail(c, err.Error(), -1000)
		return
	}

	a.Success(c, []int{}, "success", 200)

}

// 修改广告状态
func (a *AdvertController) ChangeState(c *gin.Context) {
	var (
		request       advert.AdvertChangeStateRequest  // 定义请求参数变量
		advertService = cmsService.NewAdvertService(c) // 初始化 Service
		user, _ = c.Get("admin")
		admin = user.(models.Admin)
	)
	if err := c.ShouldBindJSON(&request); err != nil {
		panic(err)
		return
	}
	err := advertService.ChangeState(request,admin)
	if err != nil {
		a.Fail(c, err.Error(), -1000)
		return
	}
	a.Success(c, request, "success", 200)
}

// 编辑广告
func (a *AdvertController) Update(c *gin.Context) {
	var (
		request       advert.AdvertEditRequest         // 定义请求参数变量
		advertService = cmsService.NewAdvertService(c) // 初始化 Service
		user, _ = c.Get("admin")
		admin = user.(models.Admin)
	)

	if err := c.ShouldBindJSON(&request); err != nil {
		panic(err)
		return
	}

	// 编辑广告
	err := advertService.UpdateAdvert(request,admin)

	if err != nil {
		a.Fail(c, err.Error(), -1000)
		return
	}

	a.Success(c, []int{}, "success", 200)
}

// 删除广告
func (a *AdvertController) Delete(c *gin.Context) {
	var (
		request       advert.AdvertDeleteRequest       // 定义请求参数变量
		advertService = cmsService.NewAdvertService(c) // 初始化 Service
	)

	if err := c.ShouldBindJSON(&request); err != nil {
		panic(err)
		return
	}

	// 删除广告
	err := advertService.DeleteAdvert(request.Ids)

	if err != nil {
		a.Fail(c, err.Error(), -1000)
		return
	}

	a.Success(c, []int{}, "success", 200)
}

// 获取广告位置
func (a *AdvertController) AdvertPosition(c *gin.Context) {
	var (
		advertService = cmsService.NewAdvertService(c) // 初始化 Service
	)

	// 获取当前管理员类型
	user, _ := c.Get("admin")
	admin := user.(models.Admin)
	adminType := admin.Type

	// 获取广告位置列表
	list, err := advertService.GetPosition(adminType)

	if err != nil {
		a.Fail(c, err.Error(), -1000)
		return
	}
	linkType :=  a.TransArr(c,"adv_link_type")
	if !(adminType ==1 || adminType == 2){ //不是超级管理员和管理员 
		linkType =  a.TransArr(c,"adv_link_type_dealer")
	}
	a.Success(c, gin.H{
		"advert_position":list,
		"adv_link_type":linkType,
	}, "success", 200)
}


func (a *AdvertController) CityAreaTree(c *gin.Context) {
	var (
		advertService = cmsService.NewAdvertService(c) // 初始化 Service
		areaTransformer = cmsTransformer.NewAreaTransformer(c) // 初始化 Transformer
	)
	// 获取当前管理员区域信息
	cityId, areaId := permissions.GetAdminAreaInfo(c, 0, 0)
	// 获取城市区域树
	city,_ := advertService.GetCityAreaTree(cityId,areaId)
	
	// 使用Transformer转换数据格式
	formattedCity := areaTransformer.FormatCityAreaTree(city)

	a.Success(c, formattedCity, "success", 200)
}

func (a *AdvertController) GetCanvasTemplate(c *gin.Context) {
	var (
		advertService     = cmsService.NewAdvertService(c)         // 初始化 Service
		advertTransformer = cmsTransformer.NewAdvertTransformer(c) // 初始化 Transformer
	)
	pagination := tools.GetPagination(c)
	templates, total, err := advertService.GetCanvasTemplate(pagination)
	if err != nil {
		a.Fail(c, err.Error(), -1000)
		return
	}
	items := advertTransformer.FormatCanvasTemplate(templates)

	a.Success(c, gin.H{
		"items": items,
		"total": total,
	}, "success", 200)
}

func (a *AdvertController) Detail(c *gin.Context) {
	var (
		request       advert.AdvertDetailRequest         // 定义请求参数变量
		advertService = cmsService.NewAdvertService(c)         // 初始化 Service
		advertTransformer = cmsTransformer.NewAdvertTransformer(c) // 初始化 Transformer
	)
	if err := c.ShouldBindQuery(&request); err != nil {
		panic(err)
		return
	}
	
	advert, err := advertService.GetAdvertDetail(request.Id)
	if err != nil {
		a.Fail(c, err.Error(), -1000)
		return
	}

	advertDetail := advertTransformer.FormatAdvertDetail(advert)

	a.Success(c, advertDetail, "success", 200)

	
	


}
