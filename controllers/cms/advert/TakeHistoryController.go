package advert

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/cms/advert/materialRequest/takeRequest"
	"mulazim-api/resources/cms/advert/materialResource"
	"mulazim-api/services"
	"mulazim-api/tools"
)

type TakeHistoryController struct {
	controllers.BaseController
}

// Statistic 统计
func (m *TakeHistoryController) Statistic(c *gin.Context) {
	var (
		request takeRequest.ListRequest
		err     = c.ShouldBind(&request)
		admin   = permissions.GetAdmin(c)
		service = services.NewMaterialService(lang.LangUtil{
			Lang: c.Param("locale"),
		},
			c.<PERSON>m("locale"),
		)
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(admin); err != nil {
		m.Fail(c, err.Error(), 400)
		return
	}
	shipperCount, totalTakedCount, newUserCount, totalOrderCount, totalOrderPrice, totalShipperIncome, categories, err := service.CmsTakeStatistic(request)
	if err != nil {
		m.Fail(c, err.Error(), 400)
		return
	}
	m.Success(c, gin.H{
		"shipper_count":        shipperCount,
		"total_taked_count":    totalTakedCount,
		"new_user_count":       newUserCount,
		"total_order_count":    totalOrderCount,
		"total_order_price":    totalOrderPrice,
		"total_shipper_income": totalShipperIncome,
		"categories":           categories,
	}, "msg", 200)
}

// List 列表
func (m *TakeHistoryController) List(c *gin.Context) {
	var (
		request  takeRequest.ListRequest
		err      = c.ShouldBind(&request)
		admin    = permissions.GetAdmin(c)
		language = c.Param("locale")
		langUtil = lang.LangUtil{
			Lang: language,
		}
		service     = services.NewMaterialService(langUtil, language)
		page    int = tools.ToInt(c.DefaultQuery("page", "1"))
		limit   int = tools.ToInt(c.DefaultQuery("limit", "10"))
	)
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(admin); err != nil {
		m.Fail(c, err.Error(), 400)
		return
	}
	takes, total, err := service.CmsTakeList(request, page, limit)
	if err != nil {
		m.Fail(c, err.Error(), 400)
		return
	}

	m.Success(c, materialResource.NewTakeListCollectionResource(takes, total, page, limit, langUtil), "msg", 200)
}

// Detail 详情
func (m *TakeHistoryController) Detail(c *gin.Context) {
	var (
		takeId   int = tools.ToInt(c.Param("takeId"))
		admin        = permissions.GetAdmin(c)
		language     = c.Param("locale")
		langUtil     = lang.LangUtil{
			Lang: language,
		}
		service        = services.NewMaterialService(langUtil, language)
		db             = tools.GetDB()
		totalReward    int
		totalUserCount int
	)

	take, err := service.CmsTakeDetail(takeId)
	if err != nil {
		m.Fail(c, err.Error(), 400)
		return
	}
	if take.ID == 0 {
		m.Fail(c, "not_found", 404)
		return
	}
	switch {
	case admin.IsOwner() || admin.IsAdmin():
	case admin.IsDealerSub() || admin.IsDealer():
		if take.AreaId != admin.AdminAreaID {
			m.Fail(c, "没有权限", 400)
			return
		}
	default:
		m.Fail(c, "没有权限", 400)
		return
	}

	var mapData map[string]interface{}
	fields := "SUM(reward) as total_reward,"
	fields += "count(id) as total_user_count"
	rs := db.Model(&models.AdvertMaterialShipperUser{}).
		Select(fields).
		Where("advert_material_take_id = ?", takeId).Scan(&mapData)
	if rs.Error != nil {
		m.Fail(c, "failed", 400)
		return
	}
	totalReward = tools.ToInt(mapData["total_reward"])
	totalUserCount = tools.ToInt(mapData["total_user_count"])
	m.Success(c, materialResource.NewTakeDetailResource(take, totalReward, totalUserCount, langUtil), "msg", 200)
}

// TakeDetailList 详情列表
func (m *TakeHistoryController) TakeDetailList(c *gin.Context) {
	var (
		take     models.AdvertMaterialTake
		takeId   int = tools.ToInt(c.Param("takeId"))
		admin        = permissions.GetAdmin(c)
		page     int = tools.ToInt(c.DefaultQuery("page", "1"))
		limit    int = tools.ToInt(c.DefaultQuery("limit", "10"))
		language     = c.Param("locale")
		langUtil     = lang.LangUtil{
			Lang: language,
		}
		service = services.NewMaterialService(langUtil, language)
	)
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	take, err := service.FindTakeById(takeId)
	if err != nil {
		m.Fail(c, err.Error(), 400)
		return
	}
	if take.ID == 0 {
		m.Fail(c, "not_found", 404)
		return
	}
	switch {
	case admin.IsOwner() || admin.IsAdmin():
	case admin.IsDealerSub() || admin.IsDealer():
		if take.AreaId != admin.AdminAreaID {
			m.Fail(c, "没有权限", 403)
			return
		}
	default:
		m.Fail(c, "没有权限", 403)
	}
	shipperUser, total, err := service.CmsTakeNewUserList(takeId, page, limit)
	m.Success(c, materialResource.NewShipperUserCollectionResource(shipperUser, total, page, limit), "msg", 200)
}
