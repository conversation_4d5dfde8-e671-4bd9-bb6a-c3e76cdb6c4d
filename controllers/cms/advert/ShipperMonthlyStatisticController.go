package advert

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/resources/cms/advert/materialResource"
	"mulazim-api/services"
	"mulazim-api/tools"
	"time"
)

type ShipperMonthlyStatisticController struct {
	controllers.BaseController
}

// Head 头部统计
func (c *ShipperMonthlyStatisticController) Head(ctx *gin.Context) {
	var (
		shipper      models.Admin
		admin        = permissions.GetAdmin(ctx)
		shipperId    = ctx.Param("shipperId")
		month        = ctx.Query("month")
		language     = ctx.Param("locale")
		langUtil     = lang.LangUtil{Lang: language}
		service      = services.NewMaterialService(langUtil, language)
		adminService = services.NewAdminService()
		categoryId   = tools.ToInt(ctx.DefaultQuery("category_id", "0"))
	)
	m, _ := time.Parse("2006-01", month)
	if month != m.Format("2006-01") {
		c.Fail(ctx, "month_error", 400)
		return
	}
	shipper = adminService.GetAdmin(tools.ToInt(shipperId))
	if !shipper.IsShipper() {
		c.Fail(ctx, "shipper_not_exist", 404)
		return
	}
	switch {
	case admin.IsAdmin() || admin.IsOwner():
	case admin.IsDealerSub() || admin.IsDealer():
		if admin.AdminAreaID != shipper.AdminAreaID {
			c.Fail(ctx, "shipper_not_exist", 404)
			return
		}
	default:
		c.Fail(ctx, "no_permission", 403)
	}
	data, err := service.GetCmsShipperUserMonthlyStatistic(shipper, month, categoryId, langUtil)
	if err != nil {
		c.Fail(ctx, err.Error(), 500)
		return
	}
	avatar := shipper.Avatar
	if avatar == "" {
		avatar = "/images/default/chat_default_shipper.png"
	}
	data["shipper_id"] = shipper.ID
	data["shipper_name"] = shipper.RealName
	data["shipper_avatar"] = tools.CdnUrl(avatar)
	c.Success(ctx, data, "msg", 200)
}

// List 列表
func (c *ShipperMonthlyStatisticController) List(ctx *gin.Context) {
	var (
		admin        = permissions.GetAdmin(ctx)
		shipperId    = tools.ToInt(ctx.Param("shipperId"))
		month        = ctx.Query("month")
		language     = ctx.Param("locale")
		langUtil     = lang.LangUtil{Lang: language}
		service      = services.NewMaterialService(langUtil, language)
		adminService = services.NewAdminService()
		page         = tools.ToInt(ctx.DefaultQuery("page", "1"))
		limit        = tools.ToInt(ctx.DefaultQuery("limit", "10"))
		categoryId   = tools.ToInt(ctx.DefaultQuery("category_id", "0"))
		sortColumn   = ctx.DefaultQuery("sort_column", "id")
		sortType     = ctx.DefaultQuery("sort_type", "desc")
	)

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	m, _ := time.Parse("2006-01", month)
	if month != m.Format("2006-01") {
		c.Fail(ctx, "month_error", 400)
		return
	}
	if sortType != "desc" {
		sortType = "asc"
	}
	switch {
	case sortColumn == "order_count":
	case sortColumn == "total_order_price":
	case sortColumn == "invite_user_fee":
	case sortColumn == "total_reward_fee":
		sortColumn = "(invite_user_fee + total_order_tips_fee)"
	default:
		sortColumn = "order_count"
	}
	shipper := adminService.GetAdmin(tools.ToInt(shipperId))
	if !shipper.IsShipper() {
		c.Fail(ctx, "shipper_not_exist", 404)
		return
	}
	switch {
	case admin.IsAdmin() || admin.IsOwner():
	case admin.IsDealerSub() || admin.IsDealer():
		if admin.AdminAreaID != shipper.AdminAreaID {
			c.Fail(ctx, "shipper_not_exist", 404)
			return
		}
	default:
		c.Fail(ctx, "no_permission", 403)
	}
	data, total, err := service.GetCmsShipperUserMonthlyStatisticList(shipper, month, categoryId, page, limit, sortColumn, sortType)
	if err != nil {
		c.Fail(ctx, err.Error(), 500)
		return
	}
	c.Success(ctx, materialResource.NewShipperUserMonthlyStatisticListResource(data, total, page, limit, shipperId, categoryId, langUtil), "msg", 200)
}
