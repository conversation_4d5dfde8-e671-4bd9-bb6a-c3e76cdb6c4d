package advert

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/requests/cms/advert/materialRequest"
	"mulazim-api/resources/cms/advert/materialResource"
	"mulazim-api/services"
	"mulazim-api/tools"
)

type MaterialController struct {
	controllers.BaseController
}

// Create 创建
func (c *MaterialController) Create(ctx *gin.Context) {
	var (
		request  materialRequest.CreateRequest
		material models.AdvertMaterial
		service  = services.NewMaterialService(lang.LangUtil{
			Lang: ctx.Param("locale"),
		}, ctx.Param("locale"))
		err = ctx.ShouldBindJSON(&request)
	)
	if err != nil {
		panic(err)
		return
	}
	material, err = service.Create(request)
	if err != nil {
		c.Fail(ctx, "failed", 400)
		return
	}
	c.Success(ctx, materialResource.NewMaterialResource(material), "msg", 200)
}

// List 列表
func (c *MaterialController) List(ctx *gin.Context) {
	var (
		request  materialRequest.ListRequest
		err      = ctx.ShouldBindQuery(&request)
		langUtil = lang.LangUtil{
			Lang: ctx.Param("locale"),
		}
		service = services.NewMaterialService(langUtil, ctx.Param("locale"))
	)
	if err != nil {
		c.Fail(ctx, "failed", 400)
		return
	}
	materials, total, err := service.Paginate(request.Keyword, request.State, request.CategoryId, request.Page, request.Limit)
	if err != nil {
		c.Fail(ctx, "failed", 400)
		return
	}
	c.Success(ctx, materialResource.NewMaterialCollectionResource(materials, total, langUtil), "msg", 200)
	//c.Success(ctx, materialResource.NewMaterialCollectionResource(collection.Items, collection.Total), "msg", 200)
}

// Update 更新
func (c *MaterialController) Update(ctx *gin.Context) {
	var (
		request  materialRequest.CreateRequest
		material models.AdvertMaterial
		service  = services.NewMaterialService(lang.LangUtil{
			Lang: ctx.Param("locale"),
		}, ctx.Param("locale"))
		err = ctx.ShouldBindJSON(&request)
		id  = tools.ToInt(ctx.Param("materialId"))
	)
	if id == 1 {
		c.Fail(ctx, "no_permission", 403)
		return
	}
	if err != nil {
		panic(err)
		return
	}
	material, err = service.Find(id)
	if err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	if material.ID == 0 {
		c.Fail(ctx, "not_found", 404)
		return
	}
	material, err = service.Update(material, request)
	if err != nil {
		c.Fail(ctx, "failed", 400)
		return
	}
	c.Success(ctx, materialResource.NewMaterialResource(material), "msg", 200)
}

// UpdateState 更新状态
func (c *MaterialController) UpdateState(ctx *gin.Context) {
	var (
		service = services.NewMaterialService(lang.LangUtil{
			Lang: ctx.Param("locale"),
		}, ctx.Param("locale"))
		id    = tools.ToInt(ctx.Param("materialId"))
		state = tools.ToInt(ctx.Query("state"))
	)
	if state < 1 || state > 2 {
		c.Fail(ctx, "state_error", 400)
		return
	}
	material, err := service.Find(id)
	if err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	if material.ID == 0 {
		c.Fail(ctx, "not_found", 404)
		return
	}
	material, err = service.UpdateState(material, state)
	if err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	c.Success(ctx, materialResource.NewMaterialResource(material), "msg", 200)
}

// Detail 详情
func (c *MaterialController) Detail(ctx *gin.Context) {
	var (
		service = services.NewMaterialService(lang.LangUtil{
			Lang: ctx.Param("locale"),
		}, ctx.Param("locale"))
		id = tools.ToInt(ctx.Param("materialId"))
	)
	material, err := service.Find(id)
	if err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	if material.ID == 0 {
		c.Fail(ctx, "not_found", 404)
		return
	}
	c.Success(ctx, materialResource.NewMaterialResource(material), "msg", 200)
}

// Delete 删除
func (c *MaterialController) Delete(ctx *gin.Context) {
	var (
		service = services.NewMaterialService(lang.LangUtil{
			Lang: ctx.Param("locale"),
		}, ctx.Param("locale"))
		id = tools.ToInt(ctx.Param("materialId"))
	)
	if id == 1 {
		c.Fail(ctx, "no_permission", 403)
		return
	}
	material, err := service.Find(id)
	if err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	if material.ID == 0 {
		c.Fail(ctx, "not_found", 404)
		return
	}
	err = service.Delete(material)
	if err != nil {
		c.Fail(ctx, "failed", 400)
		return
	}
	c.Success(ctx, "success", "msg", 200)
}
