package advert

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/cms/advert/InviteUserStatisticRequest"
	"mulazim-api/services"
	"mulazim-api/tools"
	"net/http"
)

type InviteUserStatisticController struct {
	controllers.BaseController
}

// Area 邀请用户统计
func (c *InviteUserStatisticController) ByShipper(ctx *gin.Context) {
	var (
		request  InviteUserStatisticRequest.ShipperStatisticRequest
		err      = ctx.ShouldBindQuery(&request)
		admin    = permissions.GetAdmin(ctx)
		language = ctx.Param("locale")
		langUtil = lang.LangUtil{Lang: language}
		service  = services.NewMaterialService(langUtil, language)
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(admin); err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	data, err := service.CmsInviteStatisticList(request)
	if err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	c.Success(ctx, data, "msg", 200)
}

func (c *InviteUserStatisticController) ByShipperDownload(ctx *gin.Context) {
	var (
		request  InviteUserStatisticRequest.ShipperStatisticRequest
		err      = ctx.ShouldBindQuery(&request)
		admin    = permissions.GetAdmin(ctx)
		language = ctx.Param("locale")
		langUtil = lang.LangUtil{Lang: language}
		service  = services.NewMaterialService(langUtil, language)
		area     models.Area
		city     models.City
		db       = tools.GetDB()
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(admin); err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	db.Model(models.Area{}).Where("id = ?", request.AreaID).First(&area)
	db.Model(models.City{}).Where("id = ?", request.CityID).First(&city)
	items, err := service.CmsInviteStatisticList(request)
	if err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}

	// 创建一个xlsx文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	// 创建一个工作表
	sheet := fmt.Sprintf("%s-%s", request.BeginDate, request.EndDate)
	index, err := f.NewSheet(sheet)
	if err != nil {
		fmt.Println(err)
		return
	}
	f.SetActiveSheet(index)

	headers := map[string]string{
		"A1": "配送员名称",
		"B1": "新用户数",
		"C1": "配送员二维码邀请用户数",
		"D1": "宣传二维码邀请用户数",
		"E1": "总订单数",
		"F1": "订单总额",
		"G1": "新用户奖励",
		"H1": "总奖励",
	}
	for k, v := range headers {
		f.SetCellValue(sheet, k, v)
	}
	for index, items := range items {

		f.SetCellValue(sheet, fmt.Sprintf("A%d", index+2), items.ShipperName)
		f.SetCellValue(sheet, fmt.Sprintf("B%d", index+2), items.InviteUserCount)
		f.SetCellValue(sheet, fmt.Sprintf("C%d", index+2), items.QrInviteCount)
		f.SetCellValue(sheet, fmt.Sprintf("D%d", index+2), items.MaterialInviteCount)
		f.SetCellValue(sheet, fmt.Sprintf("E%d", index+2), items.OrderCount)
		f.SetCellValue(sheet, fmt.Sprintf("F%d", index+2), fmt.Sprintf("%.2f", float64(items.TotalOrderPrice)/100))
		f.SetCellValue(sheet, fmt.Sprintf("G%d", index+2), fmt.Sprintf("%.2f", float64(items.InviteUserFee)/100))
		f.SetCellValue(sheet, fmt.Sprintf("H%d", index+2), fmt.Sprintf("%.2f", float64(items.TotalRewardFee)/100))

	}
	// 返回csv格式的文件
	downLoadFileName := fmt.Sprintf("%s-%s-日期-%s-%s.xlsx", city.NameZh, area.NameZh, request.BeginDate, request.EndDate)
	// 设置响应头
	ctx.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Access-Control-Expose-Headers", "Content-Disposition")
	// 写入到响应
	if err := f.Write(ctx.Writer); err != nil {
		ctx.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}

// ByArea 邀请用户统计
func (c *InviteUserStatisticController) ByArea(ctx *gin.Context) {
	var (
		request  InviteUserStatisticRequest.AreaStatisticRequest
		err      = ctx.ShouldBindQuery(&request)
		admin    = permissions.GetAdmin(ctx)
		language = ctx.Param("locale")
		langUtil = lang.LangUtil{Lang: language}
		service  = services.NewMaterialService(langUtil, language)
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	if !admin.IsAdmin() && !admin.IsOwner() {
		c.Fail(ctx, "no_permission", 403)
		return
	}
	data, err := service.CmsInviteStatisticByArea(request.BeginDate, request.EndDate, langUtil)
	if err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	c.Success(ctx, data, "msg", 200)
}

// ByArea 邀请用户统计
func (c *InviteUserStatisticController) ByAreaDownload(ctx *gin.Context) {
	var (
		request  InviteUserStatisticRequest.AreaStatisticRequest
		err      = ctx.ShouldBindQuery(&request)
		admin    = permissions.GetAdmin(ctx)
		language = "zh"
		langUtil = lang.LangUtil{Lang: language}
		service  = services.NewMaterialService(langUtil, language)
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	if !admin.IsAdmin() && !admin.IsOwner() {
		c.Fail(ctx, "no_permission", 403)
		return
	}
	items, err := service.CmsInviteStatisticByArea(request.BeginDate, request.EndDate, langUtil)
	if err != nil {
		c.Fail(ctx, err.Error(), 400)
		return
	}
	// 创建一个xlsx文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	// 创建一个工作表
	sheet := fmt.Sprintf("%s-%s", request.BeginDate, request.EndDate)
	index, err := f.NewSheet(sheet)
	if err != nil {
		fmt.Println(err)
		return
	}
	f.SetActiveSheet(index)

	headers := map[string]string{
		"A1": "区域名称",
		"B1": "新用户数",
		"C1": "配送员二维码邀请用户数",
		"D1": "宣传二维码邀请用户数",
		"E1": "总订单数",
		"F1": "订单总额",
		"G1": "新用户奖励",
		"H1": "总奖励",
	}
	for k, v := range headers {
		f.SetCellValue(sheet, k, v)
	}
	for index, item := range items {

		f.SetCellValue(sheet, fmt.Sprintf("A%d", index+2), item.AreaName)
		f.SetCellValue(sheet, fmt.Sprintf("B%d", index+2), item.InviteUserCount)
		f.SetCellValue(sheet, fmt.Sprintf("C%d", index+2), item.QrInviteCount)
		f.SetCellValue(sheet, fmt.Sprintf("D%d", index+2), item.MaterialInviteCount)
		f.SetCellValue(sheet, fmt.Sprintf("E%d", index+2), item.OrderCount)
		f.SetCellValue(sheet, fmt.Sprintf("F%d", index+2), fmt.Sprintf("%.2f", float64(item.TotalOrderPrice)/100))
		f.SetCellValue(sheet, fmt.Sprintf("G%d", index+2), fmt.Sprintf("%.2f", float64(item.InviteUserFee)/100))
		f.SetCellValue(sheet, fmt.Sprintf("H%d", index+2), fmt.Sprintf("%.2f", float64(item.TotalRewardFee)/100))

	}
	// 返回csv格式的文件
	downLoadFileName := fmt.Sprintf("区域宣传统计-%s-%s.xlsx", request.BeginDate, request.EndDate)
	// 设置响应头
	ctx.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Access-Control-Expose-Headers", "Content-Disposition")
	// 写入到响应
	if err := f.Write(ctx.Writer); err != nil {
		ctx.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}
