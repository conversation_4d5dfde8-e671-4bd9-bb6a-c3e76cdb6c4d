package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	markupRequests "mulazim-api/requests/cms/markup"
	cmsService "mulazim-api/services/cms"
	cmsTransformer "mulazim-api/transformers/cms"

	"github.com/gin-gonic/gin"
)

type PriceMarkupController struct {
	controllers.BaseController
}

// GetList
//
// @Description: 获取加价列表
// @Author: Rixat
// @Time: 2024-10-22 13:17:22
// @receiver
// @param c *gin.Context
func (markup PriceMarkupController) GetList(c *gin.Context) {
	var (
		params         markupRequests.CmsPriceMarkupList
		err            = c.ShouldBind(&params)
		markupService  = cmsService.NewPriceMarkupService(c)
		cmsTransformer = cmsTransformer.NewPriceMarkupTransformer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取信息
	totalCount, markupList := markupService.GetPriceMarkupList(params)
	// 格式化结果
	markupListFormat := cmsTransformer.FormatPriceMarkupList(markupList)
	markup.Success(c, map[string]interface{}{
		"total":   totalCount,
		"items": markupListFormat,
	}, "success", 200)
}

// PostCreate
//
// @Description: 创建加价记录
// @Author: Rixat
// @Time: 2024-10-22 13:16:09
// @receiver
// @param c *gin.Context
func (markup PriceMarkupController) PostCreate(c *gin.Context) {
	var (
		params        markupRequests.CmsPriceMarkupCreate
		err           = c.ShouldBindJSON(&params)
		markupService = cmsService.NewPriceMarkupService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 创建加价活动美食库
	admin := permissions.GetAdmin(c)
	ID,err := markupService.Create(params, admin)
	if err != nil {
		markup.Fail(c, err.Error(), -1000)
		return
	}
	markup.Success(c,map[string]interface{}{
		"id": ID,
	}, "success", 200)
}

// PostEdit
//
// @Description: 编辑加价
// @Author: Rixat
// @Time: 2024-10-22 13:16:32
// @receiver
// @param c *gin.Context
func (markup PriceMarkupController) PostEdit(c *gin.Context) {
	var (
		params        markupRequests.CmsPriceMarkupEdit
		err           = c.ShouldBindJSON(&params)
		markupService = cmsService.NewPriceMarkupService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 创建活动
	admin := permissions.GetAdmin(c)
	err = markupService.Edit(params, admin)
	if err != nil {
		markup.Fail(c, err.Error(), -1000)
		return
	}
	markup.Ok(c)
}

// GetDetail
//
// @Description: 获取加价详情
// @Author: Rixat
// @Time: 2024-10-22 13:16:54
// @receiver
// @param c *gin.Context
func (markup PriceMarkupController) GetDetail(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"` // 加价美食ID
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		markupService  = cmsService.NewPriceMarkupService(c)
		cmsTransformer = cmsTransformer.NewPriceMarkupTransformer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 创建活动
	markupInfo := markupService.Detail(params.ID)
	// 格式化结果
	markupDetailFormat := cmsTransformer.FormatPriceMarkupDetail(markupInfo)
	markup.Success(c, markupDetailFormat, "success", 200)
}

// PostChangeState
//
// @Description:
// @Author: Rixat
// @Time: 2024-10-22 13:17:04
// @receiver
// @param c *gin.Context
func (markup PriceMarkupController) PostChangeState(c *gin.Context) {
	type Params struct {
		ID    int `form:"id" binding:"required"`     // 加价美食ID
		State int `form:"state" binding:"oneof=1 2"` // 执行状态：0:关闭:1：开启
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		markupService = cmsService.NewPriceMarkupService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 修改状态
	err = markupService.ChangeState(params.ID, params.State)
	if err != nil {
		markup.Fail(c, err.Error(), -1000)
		return
	}
	markup.Ok(c)
}

// GetLogs
//
// @Description: 明细列表（秒杀，特价，优惠）
// @Author: Rixat
// @Time: 2024-10-25 12:43:46
// @receiver 
// @param c *gin.Context
func (markup PriceMarkupController) GetLogs(c *gin.Context) {
	type Params struct {
		ID    int `form:"id" binding:"required"`     // 加价美食ID
		Type int `form:"type" binding:"oneof=1 2 3"` // 执行状态：1:秒杀，2：特价，3：优惠
		Page int `form:"page" binding:"required"`
		Limit int `form:"limit" binding:"required"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		markupService = cmsService.NewPriceMarkupService(c)
		cmsTransformer = cmsTransformer.NewPriceMarkupTransformer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 修改状态
	if params.Type == 1  || params.Type == 2 {
		totalCount,res,err := markupService.GetSeckillLogs(params.ID, params.Type,params.Page,params.Limit)
		if err!= nil {
			markup.Fail(c, err.Error(), -1000)
			return
		}
		headCount := markupService.GetSeckillLogsHeader(params.ID)
		resMap := cmsTransformer.FormatPriceMarkupSeckillLog(res)
		markup.Success(c,map[string]interface{}{
			"items": resMap,
			"total": totalCount,
			"head_count": headCount,
		}, "success", 200)
	}else{
		totalCount,res,err := markupService.GetPreferentialLogs(params.ID, params.Type)
		if err!= nil {
			markup.Fail(c, err.Error(), -1000)
			return
		}
		headCount := markupService.GetSeckillLogsHeader(params.ID)
		resMap := cmsTransformer.FormatPriceMarkupPrefLog(res)
		markup.Success(c,map[string]interface{}{
			"items": resMap,
			"total": totalCount,
			"head_count": headCount,
		}, "success", 200)
	}
	

}


// GetLogs
//
// @Description: 明细列表（秒杀，特价，优惠）
// @Author: Rixat
// @Time: 2024-10-25 12:43:46
// @receiver 
// @param c *gin.Context
func (markup PriceMarkupController) GetLogsDetail(c *gin.Context) {
	type Params struct {
		ID    int `form:"id" binding:"required"`     // 加价美食ID
		Type int `form:"type" binding:"oneof=1 2 3"` // 执行状态：1:秒杀，2：特价，3：优惠
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		markupService = cmsService.NewPriceMarkupService(c)
		cmsTransformer = cmsTransformer.NewPriceMarkupTransformer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	var resMap map[string]interface{}
	// 修改状态
	if params.Type == 1  || params.Type == 2 {
		res,err := markupService.GetSeckillDetail(params.ID, params.Type)
		if err!= nil {
			markup.Fail(c, err.Error(), -1000)
			return
		}
		resMap = cmsTransformer.FormatPriceMarkupSeckillDetail(res)
	}else{
		res,err := markupService.GetPrefDetail(params.ID, params.Type)
		if err!= nil {
			markup.Fail(c, err.Error(), -1000)
			return
		}
		resMap = cmsTransformer.FormatPriceMarkupPrefDetail(res)
	}
	markup.Success(c,resMap, "success", 200)

}


// GetLogs
//
// @Description: 明细列表（秒杀，特价，优惠）
// @Author: Rixat
// @Time: 2024-10-25 12:43:46
// @receiver 
// @param c *gin.Context
func (markup PriceMarkupController) GetLogsDetailList(c *gin.Context) {
	type Params struct {
		Page    int `form:"page" binding:"required"`     // 加价美食ID
		Limit    int `form:"limit" binding:"required"`     // 加价美食ID
		ID    int `form:"id" binding:"required"`     // 加价美食ID
		Type int `form:"type" binding:"oneof=1 2 3"` // 执行状态：1:秒杀，2：特价，3：优惠
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		markupService = cmsService.NewPriceMarkupService(c)
		cmsTransformer = cmsTransformer.NewPriceMarkupTransformer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	var resMap []map[string]interface{}
	// 修改状态
	if params.Type == 1  || params.Type == 2 {
		totalCount,res,err := markupService.GetSeckillLogsList(params.ID, params.Type,params.Page,params.Limit)
		if err!= nil {
			markup.Fail(c, err.Error(), -1000)
			return
		}
		resMap = cmsTransformer.FormatPriceMarkupSeckillLogList(res)
		markup.Success(c,map[string]interface{}{
			"total":totalCount,
			"items":resMap,
		}, "success", 200)
		return
	}else{
		totalCount,res,err := markupService.GetPrefLogsList(params.ID, params.Type,params.Page,params.Limit)
		if err!= nil {
			markup.Fail(c, err.Error(), -1000)
			return
		}
		resMap = cmsTransformer.FormatPriceMarkupPrefLogList(res)
		markup.Success(c,map[string]interface{}{
			"total":totalCount,
			"items":resMap,
		}, "success", 200)
		return
	}

}
