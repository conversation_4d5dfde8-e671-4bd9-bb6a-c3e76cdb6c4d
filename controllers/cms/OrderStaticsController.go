package cms

import (
	"mulazim-api/constants"
	"mulazim-api/controllers"

	// "mulazim-api/lang"
	"mulazim-api/models"
	cmsService "mulazim-api/services/cms"

	"github.com/gin-gonic/gin"
	// "github.com/golang-module/carbon/v2"
)

type OrderStaticsController struct {
	controllers.BaseController
}

//
// GetTodayOrderStatics
//  @receiver c
//  @param context
//	<AUTHOR> 2024-04-03 12:00:00
//
func (m OrderStaticsController) GetTodayOrderStatics(c *gin.Context) {
	type Params struct {
		StartDate   string `form:"start_date" binding:"required,datetime=2006-01-02"`
	}
	var (
		params        Params
		err                = c.ShouldBind(&params)
		orderStaticsService     = cmsService.NewOrderStaticsService(c)
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
		// l, _     = c.Get("lang_util")
		// lang     = l.(lang.LangUtil)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	areaId :=0
	if admin.Type == constants.ADMIN_TYPE_DEALER || admin.Type == constants.ADMIN_TYPE_DEALER_SUB {
		areaId = admin.AdminAreaID
	}
	
	res := orderStaticsService.GetOrderStaticsList(c,admin,areaId,params.StartDate)
	

	c.JSON(200, res)
	// m.Success(c, res, "msg", 200)
}