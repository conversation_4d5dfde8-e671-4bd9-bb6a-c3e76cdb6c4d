package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	shipmentResource "mulazim-api/resources/cms/shipment"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type ShipperAttendanceController struct {
	controllers.BaseController
}

// PostCreate
//
// @Description: 创建(请假，事故)
// @Author: Rixat
// @Time: 2023-11-07 07:36:38
// @receiver
// @param c *gin.Context
func (cms ShipperAttendanceController) PostCreate(c *gin.Context) {
	var (
		params            shipmentResource.ShipperAttendance
		err               = c.ShouldBind(&params)
		attendanceService = cmsService.NewShipperAttendanceService(c)
		value, _          = c.Get("admin")
		admin             = value.(models.Admin)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if params.CityID == 0 && params.AreaID == 0 {
		params.CityID = admin.AdminCityID
		params.AreaID = admin.AdminAreaID
	}
	// 创建
	err = attendanceService.Create(admin, params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// GetList
//
// @Description: 获取列表(请假，事故)
// @Author: Rixat
// @Time: 2023-11-07 07:37:06
// @receiver
// @param c *gin.Context
func (cms ShipperAttendanceController) GetList(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" binding:"required"`  // 当前页数
		Limit       int    `form:"limit" binding:"required"` // 每页显示数量
		Type        int    `form:"type" binding:"required"`  // 1:打卡 2:请假 3:事故
		CityID      int    `form:"city_id" binding:""`       // 地区ID
		AreaID      int    `form:"area_id" binding:""`       // 区域ID
		State       int    `form:"state" binding:""`         // 状态
		ShipperID   int    `form:"shipper_id" binding:""`    // 关键字(姓名或手机号)
		SortColumns string `form:"sort_columns"`
	}
	var (
		params            Params
		err               = c.ShouldBind(&params)
		attendanceService = cmsService.NewShipperAttendanceService(c)
		value, _          = c.Get("admin")
		admin             = value.(models.Admin)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取排序参数
	sort := tools.SortColumnParam(params.SortColumns)
	// 获取信息
	result := attendanceService.List(admin, params.Page, params.Limit, params.Type, params.CityID, params.AreaID, params.State, params.ShipperID, sort)
	cms.Success(c, result, "msg", 200)
}

// GetListClock
//
// @Description: 打卡列表
// @Author: Rixat
// @Time: 2023-12-05 08:12:38
// @receiver
// @param c *gin.Context
func (cms ShipperAttendanceController) GetListClock(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" binding:"required"`  // 当前页数
		Limit       int    `form:"limit" binding:"required"` // 每页显示数量
		CityID      int    `form:"city_id" binding:""`       // 地区ID
		AreaID      int    `form:"area_id" binding:""`       // 区域ID
		State       int    `form:"state" binding:""`         // 状态
		ShipperID   int    `form:"shipper_id" binding:""`    // 关键字(姓名或手机号)
		SortColumns string `form:"sort_columns"`
	}
	var (
		params            Params
		err               = c.ShouldBind(&params)
		attendanceService = cmsService.NewShipperAttendanceService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取排序参数
	sort := tools.SortColumnParam(params.SortColumns)
	// 获取信息
	result := attendanceService.GetListClock(params.Page, params.Limit, params.CityID, params.AreaID, params.State, params.ShipperID, sort)
	cms.Success(c, result, "msg", 200)
}

func (cms ShipperAttendanceController) GetClockDetail(c *gin.Context) {
	type Params struct {
		Page      int    `form:"page" binding:"required"`       // 当前页数
		Limit     int    `form:"limit" binding:"required"`      // 每页显示数量
		State     int    `form:"state" binding:""`              // 状态
		ShipperID int    `form:"shipper_id" binding:"required"` // 关键字(姓名或手机号)
		StartDate string `form:"start_date" binding:"required"` // 关键字(姓名或手机号)
		EndDate   string `form:"end_date" binding:"required"`   // 关键字(姓名或手机号)
	}
	var (
		params            Params
		err               = c.ShouldBind(&params)
		attendanceService = cmsService.NewShipperAttendanceService(c)
		value, _          = c.Get("admin")
		admin             = value.(models.Admin)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if len(params.EndDate)!=0 {
		params.EndDate = params.EndDate + " 23:59:59"
	}
	// 获取信息
	header := attendanceService.GetClockHeader(admin, params.Page, params.Limit, params.ShipperID, params.StartDate, params.EndDate)
	result := attendanceService.GetClockDetailList(admin, params.Page, params.Limit, params.ShipperID, params.StartDate, params.EndDate)
	cms.Success(c, map[string]interface{}{
		"header": header,
		"list":   result,
	}, "msg", 200)
}

// GetDetail
//
// @Description: 详情
// @Author: Rixat
// @Time: 2023-11-07 07:51:10
// @receiver
// @param c *gin.Context
func (cms ShipperAttendanceController) GetDetail(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"` // ID
	}
	var (
		params            Params
		err               = c.ShouldBind(&params)
		attendanceService = cmsService.NewShipperAttendanceService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取信息
	result, err := attendanceService.Detail(params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, result, "msg", 200)
}

// PostChangeReviewState
//
// @Description: 审核请假/审核
// @Author: Rixat
// @Time: 2023-12-05 08:32:02
// @receiver
// @param c *gin.Context
func (cms ShipperAttendanceController) PostChangeReviewState(c *gin.Context) {
	type Params struct {
		ID            int    `form:"id" binding:"required"`           // ID
		ReviewState   int    `form:"review_state" binding:"required"` // 审核状态
		ReviewContent string `form:"review_remark" binding:""`        // 审核内容
	}
	var (
		params            Params
		err               = c.ShouldBind(&params)
		attendanceService = cmsService.NewShipperAttendanceService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 审核
	admin := permissions.GetAdmin(c)
	err = attendanceService.ChangeReviewState(admin, params.ID, params.ReviewState, params.ReviewContent)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// GetLeaveTypes
//
// @Description: 获取请假类型
// @Author: Rixat
// @Time: 2023-12-05 08:33:32
// @receiver
// @param c *gin.Context
func (cms ShipperAttendanceController) GetLeaveTypes(c *gin.Context) {
	var (
		l, _ = c.Get("lang_util")
		lang = l.(lang.LangUtil)
	)
	// 1:事假 2:病假 3:婚嫁 4:产假/陪产假
	// 1:事假 2:病假 3:婚嫁 4:产假/陪产假
	leaveTypes := lang.TArr("leave_types")
	resMap := make([]map[string]interface{}, 0)
	for key, value := range leaveTypes {
		resMap = append(resMap, map[string]interface{}{"id": key, "name": value})
	}
	cms.Success(c, resMap, "", 200)
}

// PostSaveClock
//
// @Description: 后台打卡
// @Author: Rixat
// @Time: 2023-12-05 08:33:46
// @receiver
// @param c *gin.Context
func (cms ShipperAttendanceController) PostSaveClock(c *gin.Context) {
	type Params struct {
		ShipperID int `form:"shipper_id" binding:"required"` // ID
		State     int `form:"state" binding:"required"`      // 1 上班  2 下班 3 休息
	}
	var (
		params            Params
		err               = c.ShouldBind(&params)
		anyAdmin, _       = c.Get("admin")
		admin             = anyAdmin.(models.Admin)
		attendanceService = cmsService.NewShipperAttendanceService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	err = attendanceService.SaveClock(admin, params.ShipperID, params.State)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}
