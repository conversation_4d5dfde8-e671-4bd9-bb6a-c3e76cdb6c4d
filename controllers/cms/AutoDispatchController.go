package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	cmsResource "mulazim-api/resources/cms"

	cmsService "mulazim-api/services/cms"

	"github.com/gin-gonic/gin"
)

type AutoDispatchController struct {
	controllers.BaseController
}


func (cms AutoDispatchController) GetList(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" binding:"required"`  // 当前页数
		Limit       int    `form:"limit" binding:"required"` // 每页显示数量
		CityID      int    `form:"city_id" binding:""`       // 地区ID
		AreaID      int    `form:"area_id" binding:""`       // 区域ID
		AutoDispatchState      int    `form:"auto_dispatch_state" binding:""`       // 是否开启智能派单：1:开启，2:关闭
		AutoDispatchPeakState      int    `form:"auto_dispatch_peak_state" binding:""`       // 1:高峰期，2:均匀分配
		Kw          string `form:"kw" binding:""`            // 关键字(模板名称)
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		service = cmsService.NewAutoDispatchService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取列表
	result := service.List(params.Page, params.Limit, params.CityID, params.AreaID,params.Kw,params.AutoDispatchState,params.AutoDispatchPeakState)
	cms.Success(c, result, "msg", 200)
}

// GetDetail
//
// @Description: 详情
// @Author: Rixat
// @Time: 2024-08-13 18:12:27
// @receiver 
// @param c *gin.Context
func (cms AutoDispatchController) GetDetail(c *gin.Context) {
	type Params struct {
		ID        int    `form:"id" binding:"required"`  // 当前页数
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		service = cmsService.NewAutoDispatchService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	result,err := service.Detail(params.ID)
	if err != nil {
		cms.Fail(c,err.Error(),-1000)
		return
	}
	cms.Success(c, result, "msg", 200)
}



// GetDetail
//
// @Description: 详情
// @Author: Rixat
// @Time: 2024-08-13 18:12:27
// @receiver 
// @param c *gin.Context
func (cms AutoDispatchController) PostEdit(c *gin.Context) {
	var (
		params          cmsResource.AutoDispatchParam
		err             = c.ShouldBind(&params)
		service = cmsService.NewAutoDispatchService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	err = service.Edit(params)
	if err != nil {
		cms.Fail(c,err.Error(),-1000)
		return
	}
	cms.Ok(c)
}

// PostChangeAutoDispatchState
//
// @Description: 智能派单修改状态
// @Author: Rixat
// @Time: 2024-08-16 12:25:00
// @receiver 
// @param c *gin.Context
func (cms AutoDispatchController) PostChangeAutoDispatchState(c *gin.Context) {
	type Params struct {
		ID        int    `form:"id" binding:"required"`  // 当前页数
		AutoDispatchState        int    `form:"auto_dispatch_state" binding:"oneof=1 2"`  // 是否开启智能派单：1:开启，2:关闭
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		service = cmsService.NewAutoDispatchService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	err = service.ChangeAutoDispatchState(params.ID,params.AutoDispatchState)
	if err != nil {
		cms.Fail(c,err.Error(),-1000)
		return
	}
	cms.Ok(c)
}


// PostChangeAutoDispatchState
//
// @Description: 智能派单高峰期状态修改
// @Author: Rixat
// @Time: 2024-08-16 12:25:00
// @receiver 
// @param c *gin.Context
func (cms AutoDispatchController) PostChangeAutoDispatchPeakState(c *gin.Context) {
	type Params struct {
		ID        int    `form:"id" binding:"required"`  // 当前页数
		AutoDispatchPeakState        int    `form:"auto_dispatch_peak_state" binding:"oneof=1 2"`  // 是否开启智能派单：1:开启，2:关闭
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		service = cmsService.NewAutoDispatchService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	err = service.ChangeAutoDispatchPeakState(params.ID,params.AutoDispatchPeakState)
	if err != nil {
		cms.Fail(c,err.Error(),-1000)
		return
	}
	cms.Ok(c)
}



// PostChangeAutoDispatchState
//
// @Description: 智能派单特价活动订单均匀分配状态修改
// @Author: Rixat
// @Time: 2024-08-16 12:25:00
// @receiver 
// @param c *gin.Context
func (cms AutoDispatchController) PostChangeAutoDispatchSpecialOrderPeakState(c *gin.Context) {
	type Params struct {
		ID        int    `form:"id" binding:"required"`  // 当前页数
		AutoDispatchSpecialOrderPeakState        int    `form:"auto_dispatch_special_order_peak_state" binding:"oneof=1 2"`  // 是否开启智能派单：1:开启，2:关闭
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		service = cmsService.NewAutoDispatchService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	err = service.ChangeAutoDispatchSpecialOrderPeakState(params.ID,params.AutoDispatchSpecialOrderPeakState)
	if err != nil {
		cms.Fail(c,err.Error(),-1000)
		return
	}
	cms.Ok(c)
}



// 历史列表
func (cms AutoDispatchController) GetHistoryList(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" `  // 当前页数
		Limit       int    `form:"limit" ` // 每页显示数量
		CityID      int    `form:"city_id" binding:""`       // 地区ID
		AreaID      int    `form:"area_id" binding:""`       // 区域ID
		OrderNo     string `form:"order_no" binding:""`
		ShipperMobile string    `form:"shipper_mobile" binding:""`
		StartDate   string `form:"start_date" binding:""`
		EndDate     string `form:"end_date" binding:""`
		ShipperId   int `form:"shipper_id" binding:""`
		
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		service = cmsService.NewAutoDispatchService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取列表
	result := service.HistoryList(params.Page, params.Limit, params.CityID, params.AreaID,params.OrderNo,params.ShipperMobile,params.StartDate,params.EndDate,params.ShipperId)
	cms.Success(c, result, "msg", 200)
}
