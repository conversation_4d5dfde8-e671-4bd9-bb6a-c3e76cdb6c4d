package cms

import (
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ShipperSalaryController struct {
	controllers.BaseController
}

// GetList
//
// @Description: 配送员工资列表
// @Author: rozimamat
// @Time: 2023-11-21 16:32
// @receiver
// @param c *gin.Context
func (cms ShipperSalaryController) GetList(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" binding:"required"`  // 当前页数
		Limit       int    `form:"limit" binding:"required"` // 每页显示数量
		CityID      int    `form:"city_id" `                 // 地区ID
		AreaID      int    `form:"area_id" `                 // 区域ID
		Month       string `form:"month" binding:"required"` // 月
		Kw          string `form:"kw" binding:""`            // 关键字(姓名或手机号)
		SortColumns string `form:"sort_columns"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		salaryService = cmsService.NewShipperSalaryService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取排序参数
	sort := tools.SortColumnParam(params.SortColumns)
	// 获取信息
	totalCount, items, _, err := salaryService.List(admin, params.Page, params.Limit, params.CityID, params.AreaID, params.Kw, params.Month, sort, false)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
	}
	result := make(map[string]interface{})
	result["total"] = totalCount
	result["items"] = items
	//头部信息
	result["head"] = salaryService.ListHead(admin, params.CityID, params.AreaID, params.Month)

	cms.Success(c, result, "success", 200)
}

// GetDetail
//
// @Description: 配送员工资详情
// @Author: rozimamat
// @Time: 2023-11-21 16:33
// @receiver
// @param c *gin.Context
func (cms ShipperSalaryController) GetDetail(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" binding:"required"`
		Limit       int    `form:"limit" binding:"required"`
		ShipperID   int    `form:"shipper_id" binding:"required"` // 配送员ID
		Type        int    `form:"type" binding:""`               // 收入类型
		OrderType   int    `form:"order_type" binding:""`         // 订单类型：1：普通订单，2：特价活动订单
		Month       string `form:"month" binding:"required"`      // 月份
		Kw          string `form:"kw" binding:""`                 // 关键字
		SortColumns string `form:"sort_columns"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		salaryService = cmsService.NewShipperSalaryService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取详情信息
	detailHeader, err := salaryService.DetailHeader(params.ShipperID, params.Month)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	sort := tools.SortColumnParam(params.SortColumns)
	totalCount, items, _ := salaryService.DetailList(params.ShipperID, params.Page, params.Limit, params.Type,params.OrderType, params.Month, params.Kw, sort, false)

	res := map[string]interface{}{
		"total": totalCount,
		"items": items,
	}
	

	cms.Success(c, map[string]interface{}{
		"income": res,
		"header": detailHeader,
	}, "success", 200)
}

// 导出excel
func (cms ShipperSalaryController) GetListExport(c *gin.Context) {
	type Params struct {
		CityID      int    `form:"city_id" `                 // 地区ID
		AreaID      int    `form:"area_id" `                 // 区域ID
		Month       string `form:"month" binding:"required"` // 月
		Kw          string `form:"kw" binding:""`            // 关键字(姓名或手机号)
		SortColumns string `form:"sort_columns"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		salaryService = cmsService.NewShipperSalaryService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取排序参数
	sort := tools.SortColumnParam(params.SortColumns)
	// 获取信息
	_, _, excelData, err := salaryService.List(admin, 0, 0, params.CityID, params.AreaID, params.Kw, params.Month, sort, true)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
	}

	//开始excel导出
	fileName := carbon.Now(configs.AsiaShanghai).Format("YmdHis") + tools.RandStr(4)
	cols := []string{
		"ID",
		"城市",
		"区域",
		"订单数量",
		"配送收入",
		"投诉数量",
		"其他",
		"惩罚",
		"迟到数量",
		"配送员姓名",
		"配送员手机",
		"日期",
		"订单收入",
		"奖励收入",
		"打赏收入",
		"特殊时间收入",
		"特殊天气收入",
		"好评收入",
		"差评惩罚",
		"投诉惩罚",
		"迟到惩罚",
		"客户介绍收入",
		"客户下单收入",
		"保险扣费",
		"最终工资",
	}
	excelFile := tools.ExcelExport("配送员工资", cols, excelData, fileName)

	downLoadFileName := "配送员工资统计-" + params.Month + "-" + carbon.Now(configs.AsiaShanghai).Format("Ymd") + ".xlsx"
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	// 写入 Excel 文件到响应体
	err1 := excelFile.Write(c.Writer)
	if err1 != nil {
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}

func (cms ShipperSalaryController) GetDetailExport(c *gin.Context) {
	type Params struct {
		ShipperID   int    `form:"shipper_id" binding:""` // 配送员ID
		Type        int    `form:"type" binding:""`       // 收入类型
		OrderType   int    `form:"order_type" binding:""`       // 收入类型
		Month       string `form:"month" binding:""`      // 月份
		Kw          string `form:"kw" binding:""`         // 关键字
		SortColumns string `form:"sort_columns"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		salaryService = cmsService.NewShipperSalaryService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取详情信息

	sort := tools.SortColumnParam(params.SortColumns)
	_, _, excelData := salaryService.DetailList(params.ShipperID, 0, 0, params.Type,params.OrderType, params.Month, params.Kw, sort, true)

	//开始excel导出
	fileName := carbon.Now(configs.AsiaShanghai).Format("YmdHis") + tools.RandStr(4)
	cols := []string{
		"ID",
		"类型",
		"餐厅名称", // 用户名称
		"客户名称", // 用户名称
		"订单ID", // 订单ID
		"订单号",  // 订单编号
		"订单价格", // 订单金额
		"配送收入", // 配送费收入

		"投诉类型",   // 投诉类型：1：客户投诉，2:商家投诉
		"是否投诉",   // 是否投诉
		"是否迟到",   // 是否迟到
		"配送完毕时间", // 配送完毕时间
		"下单时间",   // 下单时间

	}
	excelFile := tools.ExcelExport("配送员工资", cols, excelData, fileName)
	downLoadFileName := "配送员工资统计-" + params.Month + "-" + carbon.Now(configs.AsiaShanghai).Format("Ymd") + ".xlsx"
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	// 写入 Excel 文件到响应体
	err1 := excelFile.Write(c.Writer)
	if err1 != nil {
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}

}
