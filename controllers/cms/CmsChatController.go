package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/models/chat"
	"mulazim-api/services"
	"mulazim-api/tools"
	"mulazim-api/transformers"
	"net/http"

	"github.com/gin-gonic/gin"
)

type CmsChatController struct {
	controllers.BaseController
}

// 描述：发送消息
// 作者：Qurbanjan
// 文件：CmsChatController.go
// 修改时间：2023/11/15 13:16
func (cmsChat CmsChatController) PostSendMessage(c *gin.Context) {
	var request chat.ChatSendRequest
	var (
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
	)
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}
	chatService := services.NewChatService(c)
	data, err := chatService.SendMessage(request, int64(admin.ID))
	if err != nil {
		cmsChat.Fail(c, err.Error(), http.StatusBadRequest)
		return
	}
	cmsChat.Success(c, data, "msg", 200)
}

// 描述：获取聊天室聊天列表
// 作者：Qurbanjan
// 文件：CmsChatController.go
// 修改时间：2023/11/15 13:16
func (s CmsChatController) GetChatList(c *gin.Context) {
	var (
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
	)
	cityId :=tools.ToInt(c.DefaultQuery("city_id","0"))
	areaId :=tools.ToInt(c.DefaultQuery("area_id","0"))
	kw :=c.DefaultQuery("kw","")
	startDate :=c.DefaultQuery("start_date","")
	endDate :=c.DefaultQuery("end_date","")
	resId :=tools.ToInt(c.DefaultQuery("res_id","0"))
	shipperId :=tools.ToInt(c.DefaultQuery("shipper_id","0"))
	chatService := services.NewChatService(c)
	list, err := chatService.GetChatList(c, int64(admin.ID), admin.Type,cityId,areaId,kw,startDate,endDate,resId,shipperId)
	if err != nil {
		s.Fail(c,err.Error(),-1000)
		return
	}
	s.Success(c, list, "获取成功", http.StatusOK)

}

// 描述：获取聊天详情
// 作者：Qurbanjan
// 文件：CmsChatController.go
// 修改时间：2023/11/15 10:54
func (s CmsChatController) GetChatDetail(c *gin.Context) {
	var request chat.ChatDetailRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	var (
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
	)

	chatService := services.NewChatService(c)
	data, err := chatService.ChatDetail(c, request.OrderID, int64(admin.ID), admin.Type)
	if err != nil {
		s.Fail(c,err.Error(),-1000)
		return
	}
	info, err := chatService.GetOrderInfo(request.OrderID)
	if err != nil {
		s.Fail(c,err.Error(),-1000)
		return
	}

	transformers := transformers.NewChatTransformer(c)
	orderInfo := transformers.FormatChatDetailOrderInfo(info)
	s.Success(c, gin.H{
		"data":       data,
		"order_info": orderInfo,
	}, "msg", 200)
}
