package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/requests/cms/themeActivity"
	"mulazim-api/services/cms"
	"mulazim-api/transformers"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ThemeActivityController struct {
	controllers.BaseController
}

// Create 创建主题活动
func (t ThemeActivityController) Create(c *gin.Context) {
	var (
		params themeActivity.CreateRequest
		err    error
	)

	if err = c.ShouldBindJSON(&params); err != nil {
		t.Fail(c, err.Error(), -1)
		return
	}

	service := cms.NewThemeActivityService(c)
	id, err := service.Create(c, params)
	if err != nil {
		t.Fail(c, err.Error(), -1)
		return
	}

	t.Success(c, gin.H{
		"id": id,
	}, "success", 200)
}

// Update 更新主题活动
func (t ThemeActivityController) Update(c *gin.Context) {
	var (
		params themeActivity.UpdateRequest
		err    error
	)

	if err = c.ShouldBindJSON(&params); err != nil {
		t.Fail(c, err.Error(), -1)
		return
	}

	// 从URL获取ID并验证
	id := c.Param("id")
	if id == "" {
		t.Fail(c, "id is required", -1)
		return
	}

	idInt, err := strconv.Atoi(id)
	if err != nil {
		t.Fail(c, "invalid id", -1)
		return
	}
	params.ID = idInt

	service := cms.NewThemeActivityService(c)
	if err = service.Update(c, params); err != nil {
		t.Fail(c, err.Error(), -1)
		return
	}

	t.Success(c, gin.H{}, "success", 200)
}

// ChangeState 修改主题活动状态
func (t ThemeActivityController) ChangeState(c *gin.Context) {
	var (
		params themeActivity.ChangeStateRequest
		err    error
	)

	if err = c.ShouldBindJSON(&params); err != nil {
		t.Fail(c, err.Error(), -1)
		return
	}

	// 从URL获取ID并验证
	id := c.Param("id")
	if id == "" {
		t.Fail(c, "id is required", -1)
		return
	}

	idInt, err := strconv.Atoi(id)
	if err != nil {
		t.Fail(c, "invalid id", -1)
		return
	}
	params.ID = idInt

	service := cms.NewThemeActivityService(c)
	if err = service.ChangeState(c, params); err != nil {
		t.Fail(c, err.Error(), -1)
		return
	}

	t.Success(c, gin.H{}, "success", 200)
}

// Delete 删除主题活动
func (t ThemeActivityController) Delete(c *gin.Context) {
	id := c.Param("id")
	idInt, _ := strconv.Atoi(id)

	service := cms.NewThemeActivityService(c)
	if err := service.Delete(c, themeActivity.DeleteRequest{ID: idInt}); err != nil {
		t.Fail(c, err.Error(), -1)
		return
	}

	t.Success(c, gin.H{}, "success", 200)
}

// List 获取主题活动列表
func (t ThemeActivityController) List(c *gin.Context) {
	var (
		params themeActivity.ListRequest
		err    error
	)

	if err = c.ShouldBindQuery(&params); err != nil {
		t.Fail(c, err.Error(), -1)
		return
	}

	service := cms.NewThemeActivityService(c)
	activities, total, err := service.List(c, params)
	if err != nil {
		t.Fail(c, err.Error(), -1)
		return
	}

	// 转换数据
	var transformedActivities []transformers.ThemeActivityTransformer
	langUtil := t.GetLangUtil(c)
	for _, activity := range activities {
		transformedActivities = append(transformedActivities, transformers.NewThemeActivityTransformer(activity, langUtil))
	}

	t.Success(c, gin.H{
		"list":  transformedActivities,
		"total": total,
	}, "success", 200)
}

// Detail 获取主题活动详情
func (t ThemeActivityController) Detail(c *gin.Context) {
	// 从URL参数获取ID
	id := c.Param("id")
	if id == "" {
		t.Fail(c, t.GetLangUtil(c).T("id_required"), -1)
		return
	}

	idInt, err := strconv.Atoi(id)
	if err != nil {
		t.Fail(c, t.GetLangUtil(c).T("invalid_id"), -1)
		return
	}

	service := cms.NewThemeActivityService(c)
	activity, err := service.Detail(c, idInt)
	if err != nil {
		t.Fail(c, err.Error(), -1)
		return
	}

	// 转换数据
	transformer := transformers.NewThemeActivityTransformer(activity, t.GetLangUtil(c))

	t.Success(c, transformer, "success", 200)
}

// GetLangUtil 获取语言工具
func (t ThemeActivityController) GetLangUtil(c *gin.Context) *lang.LangUtil {
	l, _ := c.Get("lang_util")
	langUtil := l.(lang.LangUtil)
	return &langUtil
}
