package cms

import (
	"crypto/md5"
	"encoding/json"
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"

	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/permissions"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"

	"time"
)

type CustomerStaticsController struct {
	controllers.BaseController
}

//
// GetShipperMapList
//  @Description:  获取客户统计信息
//  @receiver c
//  @param context
//
func (m CustomerStaticsController) GetCustomerStatics(c *gin.Context) {
	type Params struct {
		CityID      int    `form:"city_id"`            // 地区ID
		AreaID      int    `form:"area_id"`            // 区域ID
		SortColumns string `form:"sort_columns,oneof=order_count order_price dealer_profit" binding:"required,oneof=order_count order_price dealer_profit"`
		SortType string `form:"sort_type,oneof=asc desc" binding:"required,oneof=asc desc"`
		StartDate   string `form:"start_date" binding:"required,datetime=2006-01-02"`
		EndDate     string `form:"end_date" binding:"required,datetime=2006-01-02"`
		Page 	  int    `form:"page"  `
		Limit 	  int    `form:"limit" `
		Lang 	  string `json:"lang"`
		OnlyCustomer  int    `form:"only_customer" `
	}
	var (
		params        Params
		err                = c.ShouldBind(&params)
		customerStaticsService     = cmsService.NewCustomerStaticsService(c)
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if carbon.Parse(params.StartDate).AddMonths(3).Lt(carbon.Parse(params.EndDate)) {
		m.Fail(c, "time_range_error_3_month", 400)
		return
	}
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.Lang = c.GetString("lang")
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	cacheKeyStr,_ := json.Marshal(params)
	hashStr := md5.Sum(cacheKeyStr)
	cacheKey := fmt.Sprintf("customer_statics_3_%x", hashStr)
	duration :=30*time.Minute
	cachedString := tools.Remember(c,cacheKey, duration, func() interface{} {
		result,_  := customerStaticsService.GetCustomerStaticsList(
			c,params.CityID,
			params.AreaID ,
			params.SortColumns,
			params.SortType,
			params.StartDate,
			params.EndDate,
			params.Page,
			params.Limit,
			params.OnlyCustomer,
		)
		if  len(result) == 0{
			return map[string]interface{}{
				"items": make([]interface{}, 0),
				"total_count": 0,
			}
		}
		for i, r := range result {

			if r.AdminType > 0 {
				tp :=tools.ToInt(r.AdminType)
				result[i].AdminTypeName = langUtil.TArr("admin_types")[tp]
			}else{
				result[i].AdminTypeName = langUtil.T("customer")
			}
			
		}
		return map[string]interface{}{
			"items": result,
			"total_count": len(result),
		}
	})
	cachedObject := make(map[string]interface{})
	json.Unmarshal([]byte(cachedString), &cachedObject)
	m.Success(c, cachedObject, "msg", 200)
}
// GetCustomerStaticsByStore 客户统计 按店铺
func (m CustomerStaticsController) GetCustomerStaticsByStore(c *gin.Context) {
	type Params struct {
		CustomerID      int    `form:"customer_id"`            //
		
		SortColumns string `form:"sort_columns,oneof=order_count order_price dealer_profit" binding:"required,oneof=order_count order_price dealer_profit"`
		SortType string `form:"sort_type,oneof=asc desc" binding:"required,oneof=asc desc"`
		StartDate   string `form:"start_date" binding:"required,datetime=2006-01-02"`
		EndDate     string `form:"end_date" binding:"required,datetime=2006-01-02"`
		Page 	  int    `form:"page"  `
		Limit 	  int    `form:"limit" `
		Lang 	  string `json:"lang"`
	}
	var (
		params        Params
		err                = c.ShouldBind(&params)
		customerStaticsService     = cmsService.NewCustomerStaticsService(c)
		// l, _     = c.Get("lang_util")
		// langUtil = l.(lang.LangUtil)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if carbon.Parse(params.StartDate).AddMonths(3).Lt(carbon.Parse(params.EndDate)) {
		m.Fail(c, "time_range_error_3_month", 400)
		return
	}
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.Lang = c.GetString("lang")
	cacheKeyStr,_ := json.Marshal(params)
	hashStr := md5.Sum(cacheKeyStr)
	cacheKey := fmt.Sprintf("customer_store_statics_%x", hashStr)
	duration :=30*time.Minute

	cachedString := tools.Remember(c,cacheKey, duration, func() interface{} {
		result,_  := customerStaticsService.GetCustomerStoreStaticsList(
			c,params.CustomerID,
			params.SortColumns,
			params.SortType,
			params.StartDate,
			params.EndDate,
			params.Page,
			params.Limit,
		)
		if  len(result) == 0{
			return map[string]interface{}{
				"items": make([]interface{}, 0),
				"total_count": 0,
			}
		}
		
		return map[string]interface{}{
			"items": result,
			"total_count": len(result),
		}
	})
	cachedObject := make(map[string]interface{})
	json.Unmarshal([]byte(cachedString), &cachedObject)
	m.Success(c, cachedObject, "msg", 200)
}

// GetCustomerStaticsByFood 客户统计 按美食数量和金额
func (m CustomerStaticsController) GetCustomerStaticsByFood(c *gin.Context) {
	type Params struct {
		CustomerID      int    `form:"customer_id"`            //
		
		SortColumns string `form:"sort_columns,oneof=food_count food_price" binding:"required,oneof=food_count food_price"`
		SortType string `form:"sort_type,oneof=asc desc" binding:"required,oneof=asc desc"`
		StartDate   string `form:"start_date" binding:"required,datetime=2006-01-02"`
		EndDate     string `form:"end_date" binding:"required,datetime=2006-01-02"`
		Page 	  int    `form:"page"  `
		Limit 	  int    `form:"limit" `
		Lang 	  string `json:"lang"`
	}
	var (
		params        Params
		err                = c.ShouldBind(&params)
		customerStaticsService     = cmsService.NewCustomerStaticsService(c)
		// l, _     = c.Get("lang_util")
		// langUtil = l.(lang.LangUtil)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if carbon.Parse(params.StartDate).AddMonths(3).Lt(carbon.Parse(params.EndDate)) {
		m.Fail(c, "time_range_error_3_month", 400)
		return
	}
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.Lang = c.GetString("lang")
	cacheKeyStr,_ := json.Marshal(params)
	hashStr := md5.Sum(cacheKeyStr)
	cacheKey := fmt.Sprintf("customer_food_statics_%x", hashStr)
	duration :=30*time.Minute
	cachedString := tools.Remember(c,cacheKey, duration, func() interface{} {
		result,_  := customerStaticsService.GetCustomerFoodStaticsList(
			c,params.CustomerID,
			params.SortColumns,
			params.SortType,
			params.StartDate,
			params.EndDate,
			params.Page,
			params.Limit,
		)
		if  len(result) == 0{
			return map[string]interface{}{
				"items": make([]interface{}, 0),
				"total_count": 0,
			}
		}
		
		return map[string]interface{}{
			"items": result,
			"total_count": len(result),
		}
	})
	cachedObject := make(map[string]interface{})
	json.Unmarshal([]byte(cachedString), &cachedObject)
	m.Success(c, cachedObject, "msg", 200)
}

//新客户(平台)统计
func (m CustomerStaticsController) GetNewCustomerStatics(c *gin.Context) {
	type Params struct {
		CityID      int    `form:"city_id"`            // 地区ID
		AreaID      int    `form:"area_id"`            // 区域ID
		// SortColumns string `form:"sort_columns,oneof=order_count order_price dealer_profit" binding:"required,oneof=order_count order_price dealer_profit"`
		// SortType string `form:"sort_type,oneof=asc desc" binding:"required,oneof=asc desc"`
		StartDate   string `form:"start_date" `
		EndDate     string `form:"end_date" `
		Mode 	  int    `form:"mode"  `
		// Page 	  int    `form:"page"  `
		// Limit 	  int    `form:"limit" `
		Lang 	  string `json:"lang"`
	}
	var (
		params        Params
		err                = c.ShouldBind(&params)
		customerStaticsService     = cmsService.NewCustomerStaticsService(c)
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	params.Lang = langUtil.Lang
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	// params.Lang = c.GetString("lang")
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	cacheKeyStr,_ := json.Marshal(params)
	hashStr := md5.Sum(cacheKeyStr)
	cacheKey := fmt.Sprintf("new_customer_statics_3_%x", hashStr)
	duration :=60*2*time.Minute
	// duration :=30*time.Second
	cachedString := tools.Remember(c,cacheKey, duration, func() interface{} {
		result,city,area,total,cityTotal,areaTotal,unknownTotal  := customerStaticsService.GetNewCustomerStaticsList(
			c,params.CityID,
			params.AreaID ,
			params.StartDate,
			params.EndDate,
			params.Mode,
		)
		if  len(result) == 0{
			return map[string]interface{}{
				"items": make([]interface{}, 0),
				"city": make([]interface{}, 0),
				"area": make([]interface{}, 0),
				"city_total": 0,
				"area_total": 0,
				"total_count": 0,
				"unknownTotal":0,
			}
		}
		
		return map[string]interface{}{
			"items": result,
			"city": city,
			"area": area,
			"city_total": cityTotal,
			"area_total": areaTotal,
			"total_count": total,
			"unknownTotal":unknownTotal,
		}
	})
	cachedObject := make(map[string]interface{})
	json.Unmarshal([]byte(cachedString), &cachedObject)
	m.Success(c, cachedObject, "msg", 200)
}