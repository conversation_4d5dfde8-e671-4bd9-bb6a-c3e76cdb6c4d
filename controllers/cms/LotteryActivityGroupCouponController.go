﻿package cms

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	"mulazim-api/requests/cms/lottery"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"
)

type LotteryActivityGroupCouponController struct {
	controllers.BaseController
}

// Create
//
//	@Author: Yakup
//	@Date: 2024-09-05 12:35:55
//	@Description: 创建优惠券
//	@receiver cms
//	@param c
func (cms *LotteryActivityGroupCouponController) Create(c *gin.Context) {
	var (
		req     = lottery.CreateLotteryActivityGroupCouponRequest{}
		service = cmsService.NewLotteryActivityGroupCouponService(c)
	)
	if err := c.ShouldBindJSON(&req); err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	if err := service.Create(admin, req); err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, "", "success", 200)
}

// Update
//
//	@Author: Yakup
//	@Date: 2024-09-05 12:39:45
//	@Description: 修改优惠卷
//	@receiver cms
//	@param c
func (cms *LotteryActivityGroupCouponController) Update(c *gin.Context) {
	var (
		req     = lottery.UpdateLotteryActivityGroupCouponRequest{}
		service = cmsService.NewLotteryActivityGroupCouponService(c)
	)
	if err := c.ShouldBindJSON(&req); err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	if err := service.Update(admin, req); err != nil {
		cms.Fail(c,err.Error(),-1000)
		return
	}
	cms.Success(c, "", "success", 200)
}

// Delete
//
//	@Author: Yakup
//	@Date: 2024-09-05 13:10:08
//	@Description: 删除优惠卷
//	@receiver cms
//	@param c
func (cms *LotteryActivityGroupCouponController) Delete(c *gin.Context) {
	var (
		req struct {
			ID int `json:"id" binding:"required"`
		}
		service = cmsService.NewLotteryActivityGroupCouponService(c)
	)
	if err := c.ShouldBindJSON(&req); err != nil {
		panic(err)
		return
	}
	if err := service.Delete(req.ID); err != nil {
		cms.Fail(c,err.Error(),-1000)
		return
	}
	cms.Success(c, "", "success", 200)
}

// List
//
//	@Author: Yakup
//	@Date: 2024-09-05 17:26:31
//	@Description: 优惠券列表
//	@receiver cms
//	@param c
func (cms *LotteryActivityGroupCouponController) List(c *gin.Context) {
	var (
		searchKey = c.Query("searchKey")
		service   = cmsService.NewLotteryActivityGroupCouponService(c)
		page      = tools.ToInt(c.DefaultQuery("page", "1"))
		limit     = tools.ToInt(c.DefaultQuery("limit", "10"))
	)
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	total, list := service.List(searchKey, page, limit)
	cms.Success(c, gin.H{
		"total": total,
		"items": list,
	}, "success", 200)
}

// Detail
//
//	@Author: Yakup
//	@Date: 2024-09-05 17:26:38
//	@Description: 优惠券详情
//	@receiver cms
//	@param c
func (cms *LotteryActivityGroupCouponController) Detail(c *gin.Context) {
	var (
		req struct {
			ID int `form:"id" binding:"required"`
		}
		service = cmsService.NewLotteryActivityGroupCouponService(c)
	)
	if err := c.ShouldBindQuery(&req); err != nil {
		panic(err)
		return
	}
	item, err := service.Detail(req.ID)
	if err != nil {
		panic(err)
		return
	}
	cms.Success(c, item, "success", 200)
}
