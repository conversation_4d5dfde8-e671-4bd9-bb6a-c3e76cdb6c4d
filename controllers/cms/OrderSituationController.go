package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type OrderSituationController struct {
	controllers.BaseController
}

// GetList
//
// @Description: 上报情况列表
// @Author: Rixat
// @Time: 2023-11-07 02:31:50
// @receiver
// @param c *gin.Context
func (cms OrderSituationController) GetList(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" binding:"required"`       // 当前页数
		Limit       int    `form:"limit" binding:"required"`      // 每页显示数量
		CityID      int    `form:"city_id" binding:""`            // 地区ID
		AreaID      int    `form:"area_id" binding:""`            // 区域ID
		ShipperID   int    `form:"shipper_id" binding:""`         // 配送员ID
		StartDate   string `form:"start_date" binding:"required"` // 开始日期
		EndDate     string `form:"end_date" binding:"required"`   // 结束日期
		SortColumns string `form:"sort_columns"`
	}
	var (
		params           Params
		err              = c.ShouldBind(&params)
		situationService = cmsService.NewOrderSituationService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	sort := tools.SortColumnParam(params.SortColumns)
	// 获取信息
	result := situationService.List(params.Page, params.Limit, params.CityID, params.AreaID, params.ShipperID, params.StartDate, params.EndDate, sort)
	cms.Success(c, result, "msg", 200)
}

// GetDetail
//
// @Description: 上报情况详情
// @Author: Rixat
// @Time: 2023-11-07 02:31:50
// @receiver
// @param c *gin.Context
func (cms OrderSituationController) GetDetail(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"`
	}
	var (
		params           Params
		err              = c.ShouldBind(&params)
		situationService = cmsService.NewOrderSituationService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取详情信息
	result, err := situationService.Detail(params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, result, "msg", 200)
}
