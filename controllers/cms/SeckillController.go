package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	seckillRequests "mulazim-api/requests/cms/seckill"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"
	cmsTransformer "mulazim-api/transformers/cms"

	"github.com/gin-gonic/gin"
)

type SeckillController struct {
	controllers.BaseController
}

// GetList
//
// @Description: 秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 16:13:45
// @receiver
// @param c *gin.Context
func (sec SeckillController) GetList(c *gin.Context) {
	var (
		params        seckillRequests.CmsSeckillList
		err           = c.ShouldBind(&params)
		seckillService = cmsService.NewSeckillService(c)
		cmsTransformer = cmsTransformer.NewSeckillTransformer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取信息
	totalCount,seckillList := seckillService.GetSeckillList(params)
	// 格式化结果
	seckillListFormat := cmsTransformer.FormatSeckillList(seckillList)
	sec.Success(c, map[string]interface{}{
		"total":totalCount,
		"seckills":seckillListFormat,
	}, "success", 200)
}

// GetSeckillLog
//
// @Description: 获取秒杀日志
// @Author: Rixat
// @Time: 2024-10-09 10:32:09
// @receiver 
// @param c *gin.Context
func (sec SeckillController) GetSeckillLog(c *gin.Context) {
	var (
		params        seckillRequests.CmsSeckillLogList
		err           = c.ShouldBind(&params)
		seckillService = cmsService.NewSeckillService(c)
		cmsTransformer = cmsTransformer.NewSeckillTransformer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取信息
	totalCount,seckillList := seckillService.GetSeckillLogList(params)
	header := seckillService.GetSeckillLogHeader(params)
	// 格式化结果
	seckillLogListFormat := cmsTransformer.FormatSeckillLogList(seckillList)
	sec.Success(c, map[string]interface{}{
		"header":header,
		"total":totalCount,
		"items":seckillLogListFormat,
	}, "success", 200)
}

// PostCreate
//
// @Description: 创建活动
// @Author: Rixat
// @Time: 2024-08-26 16:13:45
// @receiver
// @param c *gin.Context
func (sec SeckillController) GetDetail(c *gin.Context) {
	type Params struct {
		ID       int `form:"id" binding:"required"`      // 秒杀ID
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		seckillService = cmsService.NewSeckillService(c)
		cmsTransformer = cmsTransformer.NewSeckillTransformer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 创建活动
	seckill := seckillService.Detail(params.ID)
	// 格式化结果
	seckillDetailFormat := cmsTransformer.FormatSeckillDetail(seckill)
	sec.Success(c, seckillDetailFormat, "success", 200)
}


func (sec SeckillController) PostDelete(c *gin.Context) {
	type Params struct {
		ID       int `form:"id" binding:"required"`      // 秒杀ID
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		seckillService = cmsService.NewSeckillService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 创建活动
	err = seckillService.Delete(params.ID)
	if err!= nil {
		sec.Fail(c, err.Error(), -1000)
		return
	}

	// 格式化结果
	sec.Ok(c)
}

// GetList
//
// @Description: 状态修改
// @Author: Rixat
// @Time: 2024-08-26 16:13:45
// @receiver
// @param c *gin.Context
func (sec SeckillController) PostChangeState(c *gin.Context) {
	type Params struct {
		ID       int `form:"id" binding:"required"`      // 秒杀ID
		State    string `form:"state" binding:"oneof=0 1"`      // 执行状态：0:关闭:1：开启
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		seckillService = cmsService.NewSeckillService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 修改状态
	err = seckillService.ChangeState(params.ID,tools.ToInt(params.State))
	if err != nil {
		sec.Fail(c, err.Error(), -1000)
		return
	}
	sec.Ok(c)
}


// GetList
//
// @Description: 设置循序
// @Author: Rixat
// @Time: 2024-08-26 16:13:45
// @receiver
// @param c *gin.Context
func (sec SeckillController) PostSetOrder(c *gin.Context) {
	type Params struct {
		ID       int `form:"id" binding:"required"`      // 秒杀ID
		Order    string `form:"order" binding:"required"`      // 循序
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		seckillService = cmsService.NewSeckillService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 修改状态
	err = seckillService.SetOrder(params.ID,tools.ToInt(params.Order))
	if err != nil {
		sec.Fail(c, err.Error(), -1000)
		return
	}
	sec.Ok(c)
}


// PostCreate
//
// @Description: 创建活动
// @Author: Rixat
// @Time: 2024-08-26 16:13:45
// @receiver
// @param c *gin.Context
func (sec SeckillController) PostCreate(c *gin.Context) {
	var (
		params        seckillRequests.CmsSeckillItemRequest
		err           = c.ShouldBindJSON(&params)
		seckillService = cmsService.NewSeckillService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if len(params.Items) == 0{
		sec.Fail(c, "no_seckill_params", -1000)
		return
	}
	// 验证秒杀活动
	if params.Type == 1 {  // 1：同一个美食，2：多个美食
		err = seckillService.ValidSeckillSameFoods(params.Items)
	}else{
		err = seckillService.ValidSeckillDifferentFoods(params.Items)
	}
	if err != nil {
		sec.Fail(c, err.Error(), -1000)
		return
	}
	// 创建活动
	admin := permissions.GetAdmin(c)
	err = seckillService.Create(params.Items,admin)
	if err != nil {
		sec.Fail(c, err.Error(), -1000)
		return
	}
	sec.Ok(c)
}


// PostCreate
//
// @Description: 创建活动
// @Author: Rixat
// @Time: 2024-08-26 16:13:45
// @receiver
// @param c *gin.Context
func (sec SeckillController) PostEdit(c *gin.Context) {
	var (
		params        seckillRequests.CmsSeckillEditRequest
		err           = c.ShouldBindJSON(&params)
		seckillService = cmsService.NewSeckillService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 验证秒杀活动
	err = seckillService.ValidEditSeckillParams(params)
	if err != nil {
		sec.Fail(c, err.Error(), -1000)
		return
	}
	// 创建活动
	admin := permissions.GetAdmin(c)
	err = seckillService.Edit(params,admin)
	if err != nil {
		sec.Fail(c, err.Error(), -1000)
		return
	}
	sec.Ok(c)
}

func (sec SeckillController) PostSetMarkupPrice(c *gin.Context) {
	type Params struct {
		SeckillID       int `form:"seckill_id" binding:"required"`      // 秒杀ID
		Price    int `form:"price" binding:"required"`      // 循序
		Count    int `form:"count" binding:"required"`      // 循序
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		seckillService = cmsService.NewSeckillService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	
	// 创建活动
	admin := permissions.GetAdmin(c)
	err = seckillService.SetMarkupPrice(admin,params.SeckillID,params.Price,params.Count)
	if err != nil {
		sec.Fail(c, err.Error(), -1000)
		return
	}
	sec.Ok(c)
}

func (sec SeckillController) GetMarkupStepInfo(c *gin.Context) {
	type Params struct {
		SeckillID       int `form:"seckill_id" binding:"required"`      // 秒杀ID
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		seckillService = cmsService.NewSeckillService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	
	// 创建活动
	res ,err := seckillService.GetMarkupStepInfo(params.SeckillID)
	if err != nil {
		sec.Fail(c, err.Error(), -1000)
		return
	}
	sec.Success(c, res, "success", 200)

}
