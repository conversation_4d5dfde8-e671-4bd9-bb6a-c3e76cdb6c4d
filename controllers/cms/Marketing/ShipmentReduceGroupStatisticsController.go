package Marketing

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/marketingRequest"
	"mulazim-api/requests/marketingTemplateRequest"
	"mulazim-api/resources/cms/marketing/shipmentReduceResource"
	"mulazim-api/services"
	"mulazim-api/services/marketing"
	"mulazim-api/tools"
	"strings"
)

type ShipmentReduceGroupStatisticsController struct {
	controllers.BaseController
}

// Group 集团减配送费活动统计列表
func (s *ShipmentReduceGroupStatisticsController) Groups(c *gin.Context) {
	var (
		request   marketingTemplateRequest.GroupStatisticsListRequest
		err       = c.ShouldBindQuery(&request)
		admin     = permissions.GetAdmin(c)
		service   = marketing.GroupTemplateService{}
		langUtils = lang.LangUtil{
			Lang: c.<PERSON>m("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err = request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	if admin.IsOwner() || admin.IsAdmin() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		request.AreaID = admin.AdminAreaID
		request.CityID = admin.AdminCityID
		if request.RestaurantID > 0 {
			var restaurant = models.Restaurant{}
			if err = tools.GetDB().Model(models.Restaurant{}).Where("id = ?", request.RestaurantID).First(&restaurant).Error; err != nil {
				s.Fail(c, "restaurant_not_found", 422)
				return
			}
			if restaurant.ID == 0 || restaurant.AreaID != admin.AdminAreaID {
				s.Fail(c, "restaurant_not_found", 422)
				return
			}
		}
	} else {
		s.Fail(c, "no_permission", 403)
		return
	}
	sorts := ""
	if request.SortBy != "" {
		if request.SortBy == "created_at" {
			sorts = "id "
		} else {
			sorts = request.SortBy + " "
		}
		if request.SortType == "asc" {
			sorts += "asc"
		} else {
			sorts += "desc"
		}
	}
	templates, total := service.ListForStatistics(
		request.CityID,
		request.AreaID,
		request.RestaurantID,
		request.BeginDate,
		request.EndDate,
		request.Page,
		request.Limit,
		sorts,
		strings.TrimSpace(request.Keyword))
	s.Success(c, shipmentReduceResource.NewTemplateStatisticsResourceCollection(templates, total, request.CityID, request.AreaID, request.RestaurantID, request.BeginDate, request.EndDate, request.Page, request.Limit, langUtils), "success", 200)
}

func (s *ShipmentReduceGroupStatisticsController) GroupStatis(c *gin.Context) {
	var (
		request                  marketingRequest.ShipmentReduceStatisticsRestaurantStatisticsRequest
		err                      = c.ShouldBindQuery(&request)
		admin                    = permissions.GetAdmin(c)
		marketingOrderLogService marketing.OrderLogService
		orderService             services.OrderService
	)
	if err != nil {
		panic(err)
		return
	}
	if err = request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	if admin.IsOwner() || admin.IsAdmin() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		request.AreaID = admin.AdminAreaID
		request.CityID = admin.AdminCityID
		if request.RestaurantID > 0 {
			var restaurant = models.Restaurant{}
			if err = tools.GetDB().Model(models.Restaurant{}).Where("id = ?", request.RestaurantID).First(&restaurant).Error; err != nil {
				s.Fail(c, "restaurant_not_found", 422)
				return
			}
			if restaurant.ID == 0 || restaurant.AreaID != admin.AdminAreaID {
				s.Fail(c, "restaurant_not_found", 422)
				return
			}
		}
	} else {
		s.Fail(c, "no_permission", 403)
		return
	}
	groupIds := []int{}
	query := tools.GetDB().Model(models.MarketingGroupTemplate{}).
		Where("marketing_type = ?", models.MarketingMarketingTypeShipmentReduce)
	if request.AreaID > 0 {
		query = query.Where("area_id = ?", request.AreaID)
	} else if request.CityID > 0 {
		query = query.Where("city_id = ?", request.CityID)
	}
	keyword := strings.TrimSpace(request.Keyword)
	if keyword != "" {
		query.Where("(name_zh like ? name_ug like ? )", "%"+keyword+"%", "%"+keyword+"%")
	}
	if err = query.Pluck("id", &groupIds).Error; err != nil {
		s.Fail(c, "group_template_not_found", 422)
		return
	}
	orderCount, err2 := orderService.CountComplatedOrder(request.CityID, request.AreaID, request.RestaurantID, request.BeginDate, request.EndDate)

	if 0 == len(groupIds) {
		s.Success(c, shipmentReduceResource.NewShipmentReduceStatisticsOrderLogStatisticsResource(models.MarketingOrderLogGroupAggs{}, orderCount), "msg", 200)
		return
	}
	orderLogStatistics, err1 := marketingOrderLogService.StatisticsMerchantShipmentReduce(
		request.CityID, request.AreaID, request.RestaurantID, request.BeginDate, request.EndDate, true,
		groupIds,
	)

	if err1 != nil {
		s.Fail(c, err1.Error(), 400)
		return
	}
	if err2 != nil {
		s.Fail(c, err1.Error(), 400)
		return

	}
	s.Success(c, shipmentReduceResource.NewShipmentReduceStatisticsOrderLogStatisticsResource(orderLogStatistics, orderCount), "msg", 200)
}
