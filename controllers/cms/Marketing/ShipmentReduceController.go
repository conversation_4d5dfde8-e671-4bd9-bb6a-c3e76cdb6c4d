package Marketing

import (
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/marketingRequest"
	"mulazim-api/resources/cms"
	"mulazim-api/resources/cms/marketing/shipmentReduceResource"
	"mulazim-api/services"
	"mulazim-api/services/marketing"
	"mulazim-api/tools"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"github.com/xuri/excelize/v2"
)

type ShipmentReduceController struct {
	controllers.BaseController
}

// Create 减配送费活动创建
func (s *ShipmentReduceController) Create(c *gin.Context) {
	admin := permissions.GetAdmin(c)
	var (
		requestData           marketingRequest.CmsMarketingCreateRequest
		restaurantService     services.RestaurantService
		shipmentReduceService marketing.ShipmentReduceService
		language              = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err := c.ShouldBind<PERSON>(&requestData); err != nil {
		panic(err)
	}

	if ok, err := requestData.ValidateData(language); !ok {
		s.Fail(c, err.Error(), 422)
		return
	}

	if admin.IsAdmin() || admin.IsOwner() {
		if requestData.AreaId == nil {
			s.Fail(c, "area_id_can_not_be_empty", 422)
			return
		}
	} else if admin.IsDealer() || admin.IsDealerSub() {
		var areadId = admin.AdminAreaID
		requestData.AreaId = &areadId
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}

	var restaurants []models.Restaurant
	if *requestData.IsAllRestaurant == 1 {
		restaurants = restaurantService.GetOpenRestaurantsByAreaId(*requestData.AreaId)
	} else {
		restaurants = restaurantService.GetAreaRestaurantByIds(*requestData.AreaId, requestData.RestaurantIds)
	}
	if len(restaurants) == 0 {
		s.Fail(c, "restaurant_not_found", 404)
		return

	}
	shipmentReduceService.CreateRestaurantMarketingBatch(requestData, admin, restaurants)
	s.Success(c, nil, "msg", 200)
	return
}

// Update 更新减配送费活动
func (s *ShipmentReduceController) Update(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		marketingId           = tools.ToInt(c.Param("id"))
		requestData           marketingRequest.CmsMarketingUpdateRequest
		validateErr           = c.ShouldBindJSON(&requestData)
		marketing             models.Marketing
		lang                  = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if marketingId == 0 {
		s.Fail(c, "not_found", 404)
		return
	}
	if validateErr != nil {
		panic(validateErr)
		return
	}
	if ok, err := requestData.ValidateData(lang); !ok {
		s.Fail(c, err.Error(), 422)
		return
	}
	tools.Db.Model(marketing).Where("id=?", marketingId).First(&marketing)
	if marketing.ID == 0 || !marketing.IsShipmentReduce() {
		s.Fail(c, "not_found", 404)
		return
	}
	admin := permissions.GetAdmin(c)

	// 判断是否有权限： 管理员有权限、 代理和子代理需要判断活动是否在该代理所属下
	if admin.IsAdmin() || admin.IsOwner() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		if marketing.AreaID != admin.AdminAreaID {
			s.Fail(c, "forbidden", 403)
			return
		}
		if marketing.CreatorType != models.MarketingCreatorTypeDealer {
			msg := fmt.Sprintf(lang.T("marketing_create_by_merchant_not_editable"), marketing.ID)
			s.Fail(c, msg, 403)
			return
		}
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}
	// 如果不是减配送费活动或不是商家减配送费活动禁止更新
	if !marketing.TypeIsShipmentReduce() || !marketing.GroupTypeIsMerchant() {
		s.Fail(c, "forbidden", 403)
		return
	}
	// 开启活动检查在活动时间段是否存在冲突活动
	if marketing.IsActive() {
		if _, err := shipmentReduceService.FindConflictMarketing(marketing); err == nil {
			s.Fail(c, "conflict_marketing_found", 422)
		}
	}
	err := shipmentReduceService.UpdateRestaurantMarketing(marketing, requestData, admin)
	if err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	s.Success(c, marketingId, "msg", 200)
}

// DetailForEdit 减配送费活动详情
func (s *ShipmentReduceController) DetailForEdit(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		marketingId           = tools.ToInt(c.Param("id"))
		marketing             models.Marketing
		admin                 = permissions.GetAdmin(c)
	)
	if marketingId == 0 {
		s.Fail(c, "not_found", 404)
		return
	}
	marketing = shipmentReduceService.FindWithCreatorAndAreaAndCityAndRestaurant(marketingId)
	if marketing.ID == 0 || !marketing.IsShipmentReduce() {
		s.Fail(c, "not_found", 404)
		return
	}
	if !marketing.IsMerchantActivity() {
		s.Fail(c, "marketing_only_edit_merchant_activity", 400)
	}
	if admin.IsAdmin() || admin.IsOwner() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		if marketing.AreaID != admin.AdminAreaID {
			s.Fail(c, "forbidden", 403)
			return
		}
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}

	s.Success(c, cms.NewMarketingDetailResource(marketing), "msg", 200)
}

// Detail 减配送费活动详情
func (s *ShipmentReduceController) Detail(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		marketingId           = tools.ToInt(c.Param("id"))
		marketing             models.Marketing
		admin                 = permissions.GetAdmin(c)
	)
	if marketingId == 0 {
		s.Fail(c, "not_found", 404)
		return
	}
	marketing = shipmentReduceService.FindWithCreatorAndRestaurant(marketingId)
	if marketing.ID == 0 || !marketing.IsShipmentReduce() {
		s.Fail(c, "not_found", 404)
		return
	}
	if admin.IsAdmin() || admin.IsOwner() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		if marketing.AreaID != admin.AdminAreaID {
			s.Fail(c, "forbidden", 403)
			return
		}
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}

	orderLogAggs := shipmentReduceService.GetMarketingOrderLogAgg(marketingId)

	s.Success(c, cms.NewMarketingDetailWithAggsResource(marketing, orderLogAggs), "msg", 200)
}

// Orders 减配送费活动订单列表
func (s *ShipmentReduceController) Orders(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		marketingId           = tools.ToInt(c.Param("id"))
		pageInfo              marketingRequest.MarketingOrderPageInfo
		err                   = c.ShouldBindQuery(&pageInfo)
		marketing             models.Marketing
		admin                 = permissions.GetAdmin(c)
		langUtils             = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := pageInfo.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	if marketingId == 0 {
		s.Fail(c, "not_found", 404)
		return
	}
	marketing = shipmentReduceService.FindWithCreator(marketingId)
	if marketing.ID == 0 || !marketing.IsShipmentReduce() {
		s.Fail(c, "not_found", 404)
		return
	}
	if admin.IsAdmin() || admin.IsOwner() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		if marketing.AreaID != admin.AdminAreaID {
			s.Fail(c, "forbidden", 403)
			return
		}
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}

	orderLogs, total := shipmentReduceService.GetMarketingOrdersPaginate(marketing, pageInfo)
	pageInfo.Total = total

	s.Success(c, cms.NewMarketingOrderLogListResourceCollection(orderLogs, pageInfo, langUtils), "msg", 200)
}

// ChangeState 减配送费活动状态修改
func (s *ShipmentReduceController) ChangeState(c *gin.Context) {

	var (
		request               marketingRequest.CmsMarketingChangeStateRequest
		err                   = c.ShouldBindJSON(&request)
		shipmentReduceService marketing.ShipmentReduceService
		admin                 = permissions.GetAdmin(c)
		lang                  = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if admin.IsAdmin() || admin.IsOwner() {

	} else if admin.IsDealer() || admin.IsDealerSub() {

	} else {
		s.Fail(c, "forbidden", 403)
		return
	}
	successCount, failCount, failReasons, err := shipmentReduceService.ChangeState(request.Ids, request.State, admin, lang)
	if err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	s.Success(c, shipmentReduceResource.NewBatchActionResource(successCount, failCount, failReasons), "msg", 200)
}

func (s *ShipmentReduceController) OrdersDownload(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		marketingId           = tools.ToInt(c.Param("id"))
		pageInfo              marketingRequest.MarketingOrdersDownloadRequest
		err                   = c.ShouldBindQuery(&pageInfo)
		marketing             models.Marketing
		admin                 = permissions.GetAdmin(c)
		langUtils             = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := pageInfo.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	if marketingId == 0 {
		s.Fail(c, "not_found", 404)
		return
	}
	marketing = shipmentReduceService.FindWithCreator(marketingId)
	if marketing.ID == 0 || !marketing.IsShipmentReduce() {
		s.Fail(c, "not_found", 404)
		return
	}
	if admin.IsAdmin() || admin.IsOwner() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		if marketing.AreaID != admin.AdminAreaID {
			s.Fail(c, "forbidden", 403)
			return
		}
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}

	orderLogs := shipmentReduceService.GetMarketingOrders(marketing, pageInfo)

	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	// 创建一个工作表
	sheet := "Sheet1"
	index, err := f.NewSheet(sheet)
	if err != nil {
		fmt.Println(err)
		return
	}
	f.SetActiveSheet(index)
	langUtils.Lang = "zh"
	headers := map[string]string{
		"A1": langUtils.T("OrderNo"),
		"B1": langUtils.T("Receiver"),
		"C1": langUtils.T("Mobile"),
		"D1": langUtils.T("OrderPrice"),
		"E1": langUtils.T("OriginalShipmentPrice"),
		"F1": langUtils.T("DiscountShipmentPrice"),
		"G1": langUtils.T("Address"),
		"H1": langUtils.T("OrderTime"),
		"I1": langUtils.T("Shipper"),
		"J1": langUtils.T("OrderState"),
	}
	for k, v := range headers {
		f.SetCellValue(sheet, k, v)
	}
	for index, orderLog := range orderLogs {
		mobile := ""
		shipper := ""
		orderTime := ""
		address := ""
		receiver := ""

		if orderLog.Order != nil {
			mobile = tools.MaskMobile(orderLog.Order.Mobile)

			f.SetCellValue(sheet, fmt.Sprintf("I%d", index+2), langUtils.TArr("order_state")[orderLog.Order.State])
			receiver = orderLog.Order.Name
			orderTime = orderLog.Order.CreatedAt.Format("2006/01/02 15:04:05")
			address = orderLog.Order.OrderAddress
			if orderLog.Order.Shipper.ID != 0 {
				shipper = orderLog.Order.Shipper.RealName
			}
		} else if orderLog.OrderToday != nil {
			mobile = tools.MaskMobile(orderLog.OrderToday.Mobile)
			f.SetCellValue(sheet, fmt.Sprintf("I%d", index+2), langUtils.TArr("order_state")[orderLog.OrderToday.State])
			receiver = orderLog.OrderToday.Name
			orderTime = orderLog.OrderToday.CreatedAt.Format("2006/01/02 15:04:05")
			address = orderLog.OrderToday.OrderAddress
			if orderLog.OrderToday.Shipper.ID != 0 {
				shipper = orderLog.OrderToday.Shipper.RealName
			}
		}
		orderState := langUtils.TArr("order_state")[orderLog.OrderState]
		f.SetCellValue(sheet, fmt.Sprintf("A%d", index+2), orderLog.OrderNo)
		f.SetCellValue(sheet, fmt.Sprintf("B%d", index+2), receiver)
		f.SetCellValue(sheet, fmt.Sprintf("C%d", index+2), mobile)
		f.SetCellValue(sheet, fmt.Sprintf("D%d", index+2), fmt.Sprintf("%0.2f￥", float64(orderLog.OrderPrice)/100))
		f.SetCellValue(sheet, fmt.Sprintf("E%d", index+2), fmt.Sprintf("%0.2f￥", float64(orderLog.OriginalShipment)/100))
		f.SetCellValue(sheet, fmt.Sprintf("F%d", index+2), fmt.Sprintf("%0.2f￥", float64(orderLog.ReductionFee)/100))
		f.SetCellValue(sheet, fmt.Sprintf("G%d", index+2), address)
		f.SetCellValue(sheet, fmt.Sprintf("H%d", index+2), orderTime)
		f.SetCellValue(sheet, fmt.Sprintf("I%d", index+2), shipper)
		f.SetCellValue(sheet, fmt.Sprintf("J%d", index+2), orderState)
	}

	downLoadFileName := fmt.Sprintf("%s-订单明细-%s.xlsx", marketing.NameZh, carbon.Now().Format("Ymd"))
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	// 写入 Excel 文件到响应体
	err1 := f.Write(c.Writer)
	if err1 != nil {
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}

// Index 减配送费活动列表
func (s *ShipmentReduceController) Index(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		request               marketingRequest.CmsMarketingIndexRequest
		err                   = c.ShouldBindQuery(&request)
		admin                 = permissions.GetAdmin(c)
		langUtils             = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	if admin.IsAdmin() || admin.IsOwner() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		request.CityId = 0
		request.AreaId = admin.AdminAreaID
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}
	sorts := ""
	if request.SortBy != "" {
		if request.SortBy == "created_at" {
			sorts = "id "
		} else {
			sorts = request.SortBy + " "
		}
		if request.SortType == "asc" {
			sorts += "asc"
		} else {
			sorts += "desc"
		}
	}

	marketingList, total := shipmentReduceService.GetMarketingPaginate(
		request.CityId,
		request.AreaId,
		request.RestaurantId,
		request.State,
		request.BeginDate,
		request.EndDate,
		request.Page,
		request.Limit,
		request.Keyword,
		sorts)
	s.Success(c, cms.NewMarketingListResourceCollection(
		marketingList,
		total,
		request.CityId,
		request.AreaId,
		request.RestaurantId,
		request.State,
		request.BeginDate,
		request.EndDate,
		request.Page,
		request.Limit,
		request.Keyword,
		langUtils,
	), "msg", 200)
}

// Download 减配送费活动列表
func (s *ShipmentReduceController) Download(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		request               marketingRequest.CmsMarketingIndexRequest
		err                   = c.ShouldBindQuery(&request)
		admin                 = permissions.GetAdmin(c)
		langUtils             = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	if admin.IsAdmin() || admin.IsOwner() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		request.CityId = 0
		request.AreaId = admin.AdminAreaID
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}
	langUtils.Lang = "zh"
	excelData, cols, err := shipmentReduceService.GetMarketingDownload(
		request.CityId,
		request.AreaId,
		request.RestaurantId,
		request.State,
		request.BeginDate,
		request.EndDate,
		request.Keyword, langUtils)
	if err != nil {
		s.Fail(c, err.Error(), -1000)
	}
	//开始excel导出
	excelFile := tools.ExcelExport("减配送费", cols, excelData, "")
	downLoadFileName := "减配送费-" + "-" + carbon.Now(configs.AsiaShanghai).Format("Ymd") + ".xlsx"
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	// 写入 Excel 文件到响应体
	err1 := excelFile.Write(c.Writer)
	if err1 != nil {
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}

// Delete 减配送费活动 删除
func (s *ShipmentReduceController) Delete(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		request               marketingRequest.CmsMarketingDeleteRequest
		err                   = c.ShouldBindJSON(&request)
		admin                 = permissions.GetAdmin(c)
		langUtils             = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	successCount, failCount, failResions, err := shipmentReduceService.MarketingDelete(admin, request.Ids, langUtils)
	if err != nil {
		s.Fail(c, err.Error(), 422)
	}
	s.Success(c, shipmentReduceResource.NewBatchActionResource(successCount, failCount, failResions), "msg", 200)

}
