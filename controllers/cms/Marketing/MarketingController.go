package Marketing

import (
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/permissions"
	"mulazim-api/requests/marketingRequest"
	// "mulazim-api/resources/cms/marketing/marketingResource"
	marketingService "mulazim-api/services/marketing" 


	"github.com/gin-gonic/gin"
)

type MarketingController struct {
	controllers.BaseController
}

// UpadateMarketingState 满减活动状态修改
func (s *MarketingController) UpdateState(c *gin.Context) {

	var (
		request               marketingRequest.CmsMarketChangeStateRequest
		err                   = c.ShouldBind(&request)
		
		admin                 = permissions.GetAdmin(c)
		lang                  = lang.LangUtil{
			Lang: c.Param("locale"),
		}
		
	)
	if err != nil {
		panic(err)
		return
	}
	marketService :=marketingService.NewMarketingService(lang, c.Param("locale"))
	
	msg := marketService.UpadateMarketingState(request.Ids, request.State, admin, lang)
	s.Success(c, msg, "msg", 200)
}
