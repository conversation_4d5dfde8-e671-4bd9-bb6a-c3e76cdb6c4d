package Marketing

import (
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/observers"
	"mulazim-api/requests/marketingTemplateRequest"
	"mulazim-api/services"
	"mulazim-api/tools"
	"net/http"

	"github.com/golang-module/carbon/v2"

	"mulazim-api/permissions"
	"mulazim-api/requests/marketingRequest"
	"mulazim-api/resources/cms"
	"mulazim-api/resources/cms/marketing/shipmentReduceResource"
	"mulazim-api/services/marketing"

	"github.com/gin-gonic/gin"
)

type ShipmentReduceGroupController struct {
	controllers.BaseController
}

// Create 创建团体减配送费活动
func (g *ShipmentReduceGroupController) Create(c *gin.Context) {
	admin := permissions.GetAdmin(c)
	var (
		requestData       marketingTemplateRequest.CreateRequest
		restaurantService services.RestaurantService
		area              models.Area
		service           marketing.GroupTemplateService
		lang              = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	db := tools.GetDB()
	if err := c.ShouldBindJSON(&requestData); err != nil {
		panic(err)
	}

	if ok, err := requestData.ValidateData(lang); !ok {
		g.Fail(c, err.Error(), 422)
		return
	}

	if admin.IsAdmin() || admin.IsOwner() {
		if requestData.AreaId == nil {
			g.Fail(c, "area_id_can_not_be_empty", 422)
			return
		}
	} else if admin.IsDealer() || admin.IsDealerSub() {
		var areadId = admin.AdminAreaID
		requestData.AreaId = &areadId
	} else {
		g.Fail(c, "forbidden", 403)
		return
	}
	rs := db.Model(models.Area{}).Where("id = ?", *requestData.AreaId).First(&area)
	if rs.RowsAffected != 1 {
		g.Fail(c, "area_not_found", 404)
		return
	}
	var restaurants []models.Restaurant
	if *requestData.IsAllRestaurant == 1 {
		restaurants = restaurantService.GetOpenRestaurantsByAreaId(*requestData.AreaId)
	} else {
		restaurants = restaurantService.GetAreaRestaurantByIds(*requestData.AreaId, requestData.RestaurantIds)
	}
	if len(restaurants) == 0 {
		g.Fail(c, "restaurant_not_found", 404)
		return
	}
	template, err := service.Create(requestData, restaurants, admin, area)
	if err != nil {
		g.Fail(c, err.Error(), 400)
		return
	}
	g.Success(c, cms.NewMarketingGroupTemplateCreateResource(template), "success", 200)
}

func (g *ShipmentReduceGroupController) DetailForUpdate(c *gin.Context) {
	var (
		id      = tools.ToInt(c.Param("id"))
		service marketing.GroupTemplateService
		admin   = permissions.GetAdmin(c)
	)

	template, err := service.Find(id)
	if err != nil || template.ID == 0 {
		g.Fail(c, err.Error(), 404)
		return

	}
	// 权限检查
	if admin.IsAdmin() || admin.IsOwner() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		if template.AreaID != admin.AdminAreaID {
			g.Fail(c, "forbidden", 404)
			return
		}
	} else {
		g.Fail(c, "forbidden", 403)
		return
	}
	g.Success(c, cms.NewShipmentReduceTemplateDetailForUpdateResource(template), "success", 200)
}

// Update 更新团体减配送费活动
func (g *ShipmentReduceGroupController) Update(c *gin.Context) {
	var (
		service     = marketing.GroupTemplateService{}
		requestData marketingRequest.CmsMarketingUpdateRequest
		validateErr = c.ShouldBindJSON(&requestData)
		id          = tools.ToInt(c.Param("id"))
		admin       = permissions.GetAdmin(c)
		template    = models.MarketingGroupTemplate{}
		db          = tools.GetDB()
		lang        = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if validateErr != nil {
		panic(validateErr)
		return
	}
	if ok, err := requestData.ValidateData(lang); !ok {
		g.Fail(c, err.Error(), 422)
		return
	}
	db.Model(models.MarketingGroupTemplate{}).Where("id = ?", id).First(&template)
	if template.ID == 0 {
		g.Fail(c, "not_found", 404)
		return
	}
	// 权限检查
	if admin.IsAdmin() || admin.IsOwner() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		if template.AreaID != admin.AdminAreaID {
			g.Fail(c, "forbidden", 403)
			return
		}
	} else {
		g.Fail(c, "forbidden", 403)
		return
	}
	template, err := service.Update(template, requestData)
	if err != nil {
		g.Fail(c, err.Error(), 400)
		return
	}
	g.Success(c, cms.NewShipmentReduceTemplateDetailForUpdateResource(template), "success", 200)
}

// Index 减配送费 团体活动列表
func (s *ShipmentReduceGroupController) Index(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		request               marketingRequest.CmsMarketingIndexRequest
		err                   = c.ShouldBindQuery(&request)
		admin                 = permissions.GetAdmin(c)
		langUtils             = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	if admin.IsAdmin() || admin.IsOwner() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		request.CityId = 0
		request.AreaId = admin.AdminAreaID
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}
	sorts := ""
	if request.SortBy != "" {
		if request.SortBy == "created_at" {
			sorts = "id "
		} else {
			sorts = request.SortBy + " "
		}
		if request.SortType == "asc" {
			sorts += "asc"
		} else {
			sorts += "desc"
		}
	}
	marketingList, total := shipmentReduceService.GetMarketingGroupPaginate(
		request.CityId,
		request.AreaId,
		request.State,
		request.BeginDate,
		request.EndDate,
		request.Page,
		request.Limit,
		request.Keyword,
		sorts,
	)
	s.Success(c, cms.NewGroupMarketingListResourceCollection(
		marketingList,
		total,
		request.CityId,
		request.AreaId,
		request.State,
		request.BeginDate,
		request.EndDate,
		request.Page,
		request.Limit,
		request.Keyword,
		langUtils,
	), "msg", 200)
}

// Download 减配送费活动 团体活动下载
func (s *ShipmentReduceGroupController) Download(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		request               marketingRequest.CmsMarketingIndexRequest
		err                   = c.ShouldBindQuery(&request)
		admin                 = permissions.GetAdmin(c)
		langUtils             = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	if admin.IsAdmin() || admin.IsOwner() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		request.CityId = 0
		request.AreaId = admin.AdminAreaID
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}
	sorts := ""
	if request.SortBy != "" {
		if request.SortBy == "created_at" {
			sorts = "id "
		} else {
			sorts = request.SortBy + " "
		}
		if request.SortType == "asc" {
			sorts += "asc"
		} else {
			sorts += "desc"
		}
	}
	excelData, cols, err := shipmentReduceService.GetMarketingGroupDownload(
		request.CityId,
		request.AreaId,
		request.RestaurantId,
		request.State,
		request.BeginDate,
		request.EndDate,
		request.Keyword, langUtils, sorts)
	if err != nil {
		s.Fail(c, err.Error(), -1000)
	}
	//开始excel导出
	excelFile := tools.ExcelExport("团体减配送费", cols, excelData, "")
	downLoadFileName := "团体减配送费-" + "-" + carbon.Now(configs.AsiaShanghai).Format("Ymd") + ".xlsx"
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	// 写入 Excel 文件到响应体
	err1 := excelFile.Write(c.Writer)
	if err1 != nil {
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}

// Delete 减配送费 团体活动 删除
func (s *ShipmentReduceGroupController) Delete(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		request               marketingRequest.CmsMarketingDeleteRequest
		err                   = c.ShouldBind(&request)
		admin                 = permissions.GetAdmin(c)
		langUtils             = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	rs := shipmentReduceService.MarketingGroupDelete(admin, request.Ids, langUtils)
	s.Success(c, rs, "msg", 200)
}

// Detail 减配送费 团体活动 详情
func (s *ShipmentReduceGroupController) Detail(c *gin.Context) {
	var (
		groupService marketing.GroupTemplateService
		//orderLogService        marketing.OrderLogService
		admin     = permissions.GetAdmin(c)
		groupId   = tools.ToInt(c.Param("id"))
		langUtils = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	groupTemplate, err := groupService.FindWithStatistics(groupId)
	if err != nil || groupTemplate.ID == 0 {
		s.Fail(c, "not_found", 404)
		return
	}
	// 权限检查
	if admin.IsAdmin() || admin.IsOwner() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		if groupTemplate.AreaID != admin.AdminAreaID {
			s.Fail(c, "forbidden", 403)
			return
		}
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}
	s.Success(c, cms.NewShipmentReduceGroupTemplateDetailResource(groupTemplate, langUtils), "success", 200)
}

func (s *ShipmentReduceGroupController) On(c *gin.Context) {
	var id int = tools.ToInt(c.Param("id"))
	var template models.MarketingGroupTemplate
	tools.GetDB().Model(models.MarketingGroupTemplate{}).Where("id = ?", id).First(&template)
	if template.ID == 0 {
		s.Fail(c, "not_found", 404)
		return
	}
	admin := permissions.GetAdmin(c)
	if admin.IsOwner() || admin.IsAdmin() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		if template.AreaID != admin.AdminAreaID {
			s.Fail(c, "forbidden", 403)
			return
		}
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}
	if template.State == models.MarketingGroupTemplateStateOpen {
		s.Fail(c, "marketing_group_template_already_active", 400)
		return
	}
	if template.State != models.MarketingGroupTemplateStateNew {
		s.Fail(c, "marketing_group_template_only_active_new_template", 400)
		return
	}
	tools.GetDB().Model(models.MarketingGroupTemplate{}).Where("id = ?", id).Update("state", models.MarketingGroupTemplateStateOpen)
	s.Success(c, nil, "success", 200)
}

// Pause 暂停活动
func (s *ShipmentReduceGroupController) Pause(c *gin.Context) {
	var id int = tools.ToInt(c.Param("id"))
	var template models.MarketingGroupTemplate
	db := tools.GetDB()
	db.Model(models.MarketingGroupTemplate{}).Where("id = ?", id).First(&template)
	if template.ID == 0 {
		s.Fail(c, "not_found", 404)
		return
	}
	admin := permissions.GetAdmin(c)
	if admin.IsOwner() || admin.IsAdmin() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		if template.AreaID != admin.AdminAreaID {
			s.Fail(c, "forbidden", 403)
			return
		}
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}
	if template.State == models.MarketingGroupTemplateStatePause {
		s.Fail(c, "marketing_group_template_already_pause", 400)
		return
	}
	if template.State >= models.MarketingGroupTemplateStatePause {
		s.Fail(c, "marketing_group_template_only_pause_new_or_active_state", 400)
		return
	}
	var marketings []models.Marketing
	db.Model(models.Marketing{}).Where("group_id = ?", template.ID).Where("state in ?", []int{models.MarketingStateNew, models.MarketingStateActive}).Find(&marketings)
	for _, marketing := range marketings {
		if marketing.State == models.MarketingStateNew || marketing.State == models.MarketingStateActive {
			observe := observers.MarketingChangeObserve{}
			//开始观察
			observe.Observe(marketing.ID)
			err := db.Model(models.Marketing{}).Where("id = ?", marketing.ID).Update("state", models.MarketingStatePause).Error
			if err != nil {
				msg := fmt.Sprintf("团体活动：%d 暂停失败, 错误信息：%s", template.ID, err.Error())
				tools.Logger.Error(msg)
				s.Fail(c, "marketing_group_template_pause_failed", 400)
				return
			}
			observe.SaveChanges(admin)
		}
	}

	tools.GetDB().Model(models.MarketingGroupTemplate{}).Where("id = ?", id).Update("state", models.MarketingGroupTemplateStatePause)
	s.Success(c, nil, "success", 200)
}

// SendJoinPush 减配送费 团体活动 发送参与活动消息
func (s *ShipmentReduceGroupController) SendJoinPush(c *gin.Context) {
	var (
		request      marketingRequest.CmsMarketingGroupSendJoinActivityMessageRequest
		err              = c.ShouldBindJSON(&request)
		admin            = permissions.GetAdmin(c)
		areaId       int = 0
		groupService     = marketing.GroupTemplateService{}
	)
	if err != nil {
		panic(err)
		return
	}
	if admin.IsAdmin() || admin.IsOwner() {
		areaId = 0
	} else if admin.IsDealer() || admin.IsDealerSub() {
		areaId = admin.AdminAreaID
	} else {
		s.Fail(c, "forbidden", 403)
		return
	}
	if len(request.Ids) > 100 {
		s.Fail(c, "marketing_group_template_too_many_ids_received", 422)
		return
	}
	ok, err := groupService.SendJoinPush(areaId, request.Ids)
	if err != nil {
		s.Fail(c, err.Error(), 400)
		return
	}
	if !ok {
		s.Fail(c, "marketing_group_template_send_join_activity_message_failed", 400)
		return
	}
	s.Success(c, nil, "success", 200)
}

// DetailRestaurant 减配送费 团体活动 详情 商家列表
func (s *ShipmentReduceGroupController) DetailRestaurant(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		request               marketingRequest.CmsMarketingGroupDetailRestaurantRequest
		err                   = c.ShouldBind(&request)
		// admin                 = permissions.GetAdmin(c)
		langUtils = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	sorts := ""
	if request.SortBy != "" {
		if request.SortBy == "created_at" {
			sorts = "state "
		} else {
			sorts = request.SortBy + " "
		}
		if request.SortType == "asc" {
			sorts += "asc"
		} else {
			sorts += "desc"
		}
	}
	result := shipmentReduceService.MarketingDetailRestaurant(request.Id, request.BeginDate, request.EndDate, request.Page, request.Limit, request.Keyword, langUtils, sorts, request.State)
	s.Success(c, result, "msg", 200)
}

// DownloadDetailRestaurant 减配送费 团体活动 详情 商家列表
func (s *ShipmentReduceGroupController) DownloadDetailRestaurant(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		request               marketingRequest.CmsMarketingGroupDetailRestaurantRequest
		err                   = c.ShouldBind(&request)
		// admin                 = permissions.GetAdmin(c)
		langUtils = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	sorts := ""
	if request.SortBy != "" {
		if request.SortBy == "created_at" {
			sorts = "state "
		} else {
			sorts = request.SortBy + " "
		}
		if request.SortType == "asc" {
			sorts += "asc"
		} else {
			sorts += "desc"
		}
	}
	excelData, cols, err := shipmentReduceService.DownloadMarketingDetailRestaurant(request.Id, request.BeginDate, request.EndDate, request.Keyword, langUtils, sorts, request.State)

	if err != nil {
		s.Fail(c, err.Error(), -1000)
	}
	//开始excel导出
	excelFile := tools.ExcelExport("团体减配送费详情店铺", cols, excelData, "")
	downLoadFileName := "团体减配送费详情店铺-" + "-" + carbon.Now(configs.AsiaShanghai).Format("Ymd") + ".xlsx"
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	// 写入 Excel 文件到响应体
	err1 := excelFile.Write(c.Writer)
	if err1 != nil {
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}

// DetailOrder 减配送费 团体活动 详情 订单列表
func (s *ShipmentReduceGroupController) DetailOrder(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		request               marketingRequest.CmsMarketingGroupDetailOrderRequest
		err                   = c.ShouldBind(&request)
		// admin                 = permissions.GetAdmin(c)
		langUtils = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	sorts := ""
	if request.SortBy != "" {
		if request.SortBy == "created_at" {
			sorts = "id "
		} else {
			sorts = request.SortBy + " "
		}
		if request.SortType == "asc" {
			sorts += "asc"
		} else {
			sorts += "desc"
		}
	}
	result := shipmentReduceService.MarketingDetailOrder(
		request.Id, request.BeginDate, request.EndDate, request.Page, request.Limit, request.Keyword,
		request.ResturantId, request.OrderState, langUtils, sorts)
	s.Success(c, result, "msg", 200)
}

// DownloadDetailOrder 减配送费 团体活动 详情 订单列表
func (s *ShipmentReduceGroupController) DownloadDetailOrder(c *gin.Context) {
	var (
		shipmentReduceService marketing.ShipmentReduceService
		request               marketingRequest.CmsMarketingGroupDetailOrderRequest
		err                   = c.ShouldBind(&request)
		// admin                 = permissions.GetAdmin(c)
		langUtils = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}

	excelData, cols, err := shipmentReduceService.DownloadMarketingDetailOrder(request.Id, request.BeginDate, request.EndDate, request.Page, request.Limit, request.Keyword, request.ResturantId, request.OrderState, langUtils)
	if err != nil {
		s.Fail(c, err.Error(), -1000)
	}
	//开始excel导出
	excelFile := tools.ExcelExport("团体减配送费详情订单", cols, excelData, "")
	downLoadFileName := "团体减配送费详情店铺-" + "-" + carbon.Now(configs.AsiaShanghai).Format("Ymd") + ".xlsx"
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	// 写入 Excel 文件到响应体
	err1 := excelFile.Write(c.Writer)
	if err1 != nil {
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}

// DetailRestaurantAdd 减配送费 团体活动 详情 商家列表 添加新的店铺
func (s *ShipmentReduceGroupController) DetailRestaurantAdd(c *gin.Context) {
	var (
		shipmentReduceService marketing.GroupTemplateService
		request               marketingRequest.CmsMarketingGroupDetailRestaurantAddRequest
		err                   = c.ShouldBind(&request)
		admin                 = permissions.GetAdmin(c)
		langUtils             = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}

	successCount, failCount, failReasons, err := shipmentReduceService.MarketingDetailRestaurantAdd(request.Id, request.RestaurantIds, langUtils, admin)
	if err != nil {
		s.Fail(c, err.Error(), 422)
	}
	s.Success(c, shipmentReduceResource.NewBatchActionResource(successCount, failCount, failReasons), "msg", 200)
}

// SendJoinPushOne 减配送费 团体活动 发送参与活动消息 一个一个发送
func (s *ShipmentReduceGroupController) SendJoinPushOne(c *gin.Context) {
	var (
		request marketingRequest.CmsMarketingGroupSendJoinActivityMessageRequestOne
		err     = c.ShouldBindJSON(&request)

		groupService = marketing.GroupTemplateService{}
	)
	if err != nil {
		panic(err)
		return
	}
	ok, err := groupService.SendJoinPushOne(request.Id)
	if err != nil {
		s.Fail(c, err.Error(), 400)
		return
	}
	if !ok {
		s.Fail(c, "marketing_group_template_send_join_activity_message_failed", 400)
		return
	}
	s.Success(c, nil, "success", 200)
}
