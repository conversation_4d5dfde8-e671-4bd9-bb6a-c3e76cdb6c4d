package Marketing

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/marketingRequest"
	"mulazim-api/resources/cms/marketing/shipmentReduceResource"
	"mulazim-api/services"
	"mulazim-api/services/marketing"

	"mulazim-api/tools"
)

type ShipmentReduceStatisticsController struct {
	controllers.BaseController
}

// Restaurant 餐厅减配送费活动统计
func (s *ShipmentReduceStatisticsController) Restaurant(c *gin.Context) {
	var (
		request marketingRequest.ShipmentReduceStatisticsRestaurantRequest
		err     = c.ShouldBindQuery(&request)
		admin   = permissions.GetAdmin(c)
		//orderService services.OrderService
		shipmentReduceService marketing.ShipmentReduceService
		//marketingOrderLogService marketing.OrderLogService
		langUtil = lang.LangUtil{
			Lang: c.<PERSON>m("locale"),
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err = request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	if admin.IsOwner() || admin.IsAdmin() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		request.AreaID = admin.AdminAreaID
		request.CityID = admin.AdminCityID
		if request.RestaurantID > 0 {
			var restaurant = models.Restaurant{}
			if err = tools.GetDB().Model(models.Restaurant{}).Where("id = ?", request.RestaurantID).First(&restaurant).Error; err != nil {
				s.Fail(c, "restaurant_not_found", 422)
				return
			}
			if restaurant.ID == 0 || restaurant.AreaID != admin.AdminAreaID {
				s.Fail(c, "restaurant_not_found", 422)
				return
			}
		}
	} else {
		s.Fail(c, "no_permission", 403)
		return
	}
	sorts := ""
	if request.SortBy != "" {
		if request.SortBy == "created_at" {
			sorts = "id "
		} else {
			sorts = request.SortBy + " "
		}
		if request.SortType == "asc" {
			sorts += "asc"
		} else {
			sorts += "desc"
		}
	}
	marketingList, total := shipmentReduceService.GetStatisticsPaginate(request.CityID, request.AreaID, request.RestaurantID, request.BeginDate, request.EndDate, request.Page, request.Limit, sorts)

	s.Success(c,
		shipmentReduceResource.NewMerchantStatisticsResourceCollection(
			marketingList,
			total,
			request.CityID,
			request.AreaID,
			request.RestaurantID,
			request.BeginDate,
			request.EndDate,
			request.Page,
			request.Limit,
			langUtil,
		),
		"msg", 200)
}

func (s *ShipmentReduceStatisticsController) RestaurantStatis(c *gin.Context) {
	var (
		request                  marketingRequest.ShipmentReduceStatisticsRestaurantStatisticsRequest
		err                      = c.ShouldBindQuery(&request)
		admin                    = permissions.GetAdmin(c)
		marketingOrderLogService marketing.OrderLogService
		orderService             services.OrderService
	)
	if err != nil {
		panic(err)
		return
	}
	if err = request.Validate(); err != nil {
		s.Fail(c, err.Error(), 422)
		return
	}
	if admin.IsOwner() || admin.IsAdmin() {
	} else if admin.IsDealer() || admin.IsDealerSub() {
		request.AreaID = admin.AdminAreaID
		request.CityID = admin.AdminCityID
		if request.RestaurantID > 0 {
			var restaurant = models.Restaurant{}
			if err = tools.GetDB().Model(models.Restaurant{}).Where("id = ?", request.RestaurantID).First(&restaurant).Error; err != nil {
				s.Fail(c, "restaurant_not_found", 422)
				return
			}
			if restaurant.ID == 0 || restaurant.AreaID != admin.AdminAreaID {
				s.Fail(c, "restaurant_not_found", 422)
				return
			}
		}
	} else {
		s.Fail(c, "no_permission", 403)
		return
	}
	orderLogStatistics, err1 := marketingOrderLogService.StatisticsMerchantShipmentReduce(
		request.CityID, request.AreaID, request.RestaurantID, request.BeginDate, request.EndDate, false, []int{})
	orderCount, err2 := orderService.CountComplatedOrder(request.CityID, request.AreaID, request.RestaurantID, request.BeginDate, request.EndDate)
	if err1 != nil {
		s.Fail(c, err1.Error(), 400)
		return
	}
	if err2 != nil {
		s.Fail(c, err1.Error(), 400)
		return

	}
	s.Success(c, shipmentReduceResource.NewShipmentReduceStatisticsOrderLogStatisticsResource(orderLogStatistics, orderCount), "msg", 200)
}
