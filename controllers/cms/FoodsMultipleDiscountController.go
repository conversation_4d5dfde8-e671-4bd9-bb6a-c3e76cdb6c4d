package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	"mulazim-api/requests/foodsMultipleDiscount"
	"mulazim-api/services"
	"mulazim-api/tools"
	"mulazim-api/transformers"

	"github.com/gin-gonic/gin"
)

// FoodsMultipleDiscountController 多份打折活动控制器
type FoodsMultipleDiscountController struct {
	controllers.BaseController
}

// @Summary 创建多份打折活动
// @Description 创建一个新的多份打折活动
// @Tags 多份打折活动管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param request body foodsMultipleDiscount.FoodsMultipleDiscountRequest true "多份打折活动信息"
// @Success 200 {object} controllers.Response "操作成功"
// @Router /api/cms/foods-multiple-discount [post]
func (f *FoodsMultipleDiscountController) Create(c *gin.Context) {
	// 获取请求参数
	var (
		request foodsMultipleDiscount.FoodsMultipleDiscountCreateRequest
		service = services.NewFoodsMultipleDiscountService(c)
	)

	// 绑定请求参数
	if err := c.ShouldBindJSON(&request); err != nil {
		panic(err)
	}

	// 获取当前管理员
	admin := permissions.GetAdmin(c)
	restaurant, _ := permissions.GetRestaurantInfoByContent(c)
	// 创建多份打折活动
	err := service.Create(request, admin ,restaurant)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}

	// 返回成功响应
	f.Success(c, make([]int, 0), "success", 200)
}

// @Summary 获取多份打折活动列表
// @Description 获取多份打折活动的分页列表
// @Tags 多份打折活动管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param page query int false "页码，默认1"
// @Param limit query int false "每页数量，默认20"
// @Param request body foodsMultipleDiscount.FoodsMultipleDiscountListRequest true "查询条件"
// @Success 200 {object} []foodsMultipleDiscount.FoodsMultipleDiscountResponse "操作成功"
// @Router /api/cms/foods-multiple-discount/list [post]
func (f *FoodsMultipleDiscountController) List(c *gin.Context) {
	// 获取请求参数
	var (
		request     foodsMultipleDiscount.FoodsMultipleDiscountListRequest
		service     = services.NewFoodsMultipleDiscountService(c)
		transformer = transformers.NewFoodsMultipleDiscountTransformer(c)
	)

	// 绑定请求参数
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	// 获取分页信息
	pagination := tools.GetPagination(c)
	request.Page = pagination.Page
	request.Limit = pagination.Limit

	// 获取管理员区域信息
	request.CityId, request.AreaId = permissions.GetAdminAreaInfo(c, request.CityId, request.AreaId)
	// 获取多份打折活动列表
	list, total, err := service.GetCmsFoodsMultipleDiscountListList(request)

	// 转换多份打折活动列表
	response := transformer.FormatCmsFoodsMultipleDiscountList(list)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}

	// 返回成功响应
	f.Success(c, gin.H{
		"items": response,
		"total": total,
	}, "success", 200)

}

// @Summary 修改多份打折活动状态
// @Description 修改多份打折活动状态
// @Tags 多份打折活动管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param request body foodsMultipleDiscount.FoodsMultipleDiscountChangeStateRequest true "多份打折活动状态信息"
// @Success 200 {object} int "操作成功"
// @Router /api/cms/foods-multiple-discount/change-state [post]
func (f *FoodsMultipleDiscountController) ChangeState(c *gin.Context) {
	// 获取请求参数
	var (
		request foodsMultipleDiscount.FoodsMultipleDiscountChangeStateRequest
		service = services.NewFoodsMultipleDiscountService(c)
	)

	// 绑定请求参数
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	admin := permissions.GetAdmin(c)
	// 修改多份打折活动状态
	err, state := service.ChangeState(request, admin)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}

	// 返回成功响应
	f.Success(c, state, "success", 200)
}

// @Summary 获取多份打折活动详情
// @Description 获取多份打折活动的详情
// @Tags 多份打折活动管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param request body foodsMultipleDiscount.FoodsMultipleDiscountDetailRequest true "多份打折活动详情信息"
// @Success 200 {object} foodsMultipleDiscount.FoodsMultipleDiscountDetailResponse "操作成功"
// @Router /api/cms/foods-multiple-discount/detail [post]
func (f *FoodsMultipleDiscountController) Detail(c *gin.Context) {
	// 获取请求参数
	var (
		request     foodsMultipleDiscount.FoodsMultipleDiscountDetailRequest
		service     = services.NewFoodsMultipleDiscountService(c)
		transformer = transformers.NewFoodsMultipleDiscountTransformer(c)
	)

	// 绑定请求参数
	if err := c.ShouldBindQuery(&request); err != nil {
		panic(err)
	}

	// 获取多份打折活动详情
	detail, err := service.GetCmsFoodsMultipleDiscountDetail(request)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}

	// 转换多份打折活动详情
	response := transformer.FormatCmsFoodsMultipleDiscountDetail(detail)

	// 返回成功响应
	f.Success(c, response, "success", 200)
}


// @Summary 获取多份打折活动详情
// @Description 获取多份打折活动的详情
// @Tags 多份打折活动管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param request body foodsMultipleDiscount.FoodsMultipleDiscountDetailRequest true "多份打折活动详情信息"
// @Success 200 {object} foodsMultipleDiscount.FoodsMultipleDiscountDetailResponse "操作成功"
// @Router /api/cms/foods-multiple-discount/detail-view [post]
func (f *FoodsMultipleDiscountController) DetailView(c *gin.Context) {
	// 获取请求参数
	var (
		request     foodsMultipleDiscount.FoodsMultipleDiscountDetailRequest
		service     = services.NewFoodsMultipleDiscountService(c)
		transformer = transformers.NewFoodsMultipleDiscountTransformer(c)
	)

	// 绑定请求参数
	if err := c.ShouldBindQuery(&request); err != nil {
		panic(err)
	}

	// 获取多份打折活动详情
	detail, err := service.GetCmsFoodsMultipleDiscountDetailView(request)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}

	var res foodsMultipleDiscount.FoodsMultipleDiscountDetailMerchantViweResponse
	res.Header = service.GetCmsFoodsMultipleDiscountDetailViewHeader(request.ID)
	// 格式化
	res.Info = transformer.FormatMerchantFoodsMultipleDiscountDetailView(detail)
	
	// 返回成功响应
	f.Success(c, res, "success", 200)
	// 返回成功响应
}

// @Summary 获取多份打折活动列表头
// @Description 获取多份打折活动列表头
// @Tags 多份打折活动管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
func (f *FoodsMultipleDiscountController) ListHeader(c *gin.Context) {
	// 获取请求参数
	var (
		request foodsMultipleDiscount.FoodsMultipleDiscountListHeaderRequest
		service = services.NewFoodsMultipleDiscountService(c)
	)
	// 绑定请求参数
	if err := c.ShouldBindQuery(&request); err != nil {
		panic(err)
	}
	request.CityID, request.AreaID = permissions.GetAdminAreaInfo(c, request.CityID, request.AreaID)
	header := service.GetCmsFoodsMultipleDiscountListHeader(request)
	f.Success(c, header, "success", 200)
}

// @Summary 获取多份打折活动订单列表
// @Description 获取多份打折活动订单列表
// @Tags 多份打折活动管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
func (f *FoodsMultipleDiscountController) OrderList(c *gin.Context) {
	// 获取请求参数
	var (
		request foodsMultipleDiscount.FoodsMultipleDiscountOrderListRequest
		service = services.NewFoodsMultipleDiscountService(c)
		transformer = transformers.NewFoodsMultipleDiscountTransformer(c)
	)
	// 绑定请求参数
	if err := c.ShouldBindQuery(&request); err != nil {
		panic(err)
	}
	var res foodsMultipleDiscount.FoodsMultipleDiscountOrderListResponse
	// 获取多份打折活动详情
	orderList,total := service.GetCmsFoodsMultipleDiscountOrderList(request)
	// 格式化
	res.Items = transformer.FormatMerchantFoodsMultipleDiscountOrderList(orderList)
	res.Total = total
	// 返回成功响应
	f.Success(c, res, "success", 200)
}

// @Summary 更新多份打折活动
// @Description 更新多份打折活动
// @Tags 多份打折活动管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param request body foodsMultipleDiscount.FoodsMultipleDiscountUpdateRequest true "多份打折活动更新信息"
// @Success 200 {object} int "操作成功"
// @Router /api/cms/foods-multiple-discount/update [post]
func (f *FoodsMultipleDiscountController) Update(c *gin.Context) {
	// 获取请求参数
	var (
		request foodsMultipleDiscount.FoodsMultipleDiscountUpdateRequest
		service = services.NewFoodsMultipleDiscountService(c)
	)

	// 绑定请求参数
	if err := c.ShouldBindJSON(&request); err != nil {
		panic(err)
	}

	// 获取当前管理员
	admin := permissions.GetAdmin(c)

	// 更新多份打折活动
	err := service.Update(request, admin)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}

	// 返回成功响应
	f.Success(c, make([]int, 0), "success", 200)
}

// @Summary 删除多份打折活动
// @Description 删除多份打折活动
// @Tags 多份打折活动管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param request body foodsMultipleDiscount.FoodsMultipleDiscountDeleteRequest true "多份打折活动删除信息"
// @Success 200 {object} int "操作成功"
// @Router /api/cms/foods-multiple-discount/delete [post]
func (f *FoodsMultipleDiscountController) Delete(c *gin.Context) {
	// 获取请求参数
	var (
		request foodsMultipleDiscount.FoodsMultipleDiscountDeleteRequest
		service = services.NewFoodsMultipleDiscountService(c)
	)

	// 绑定请求参数
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	// 获取当前管理员
	admin := permissions.GetAdmin(c)

	// 删除多份打折活动
	err := service.Delete(request, admin)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}

	// 返回成功响应
	f.Success(c, make([]int, 0), "success", 200)
}
