package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	shipmentResource "mulazim-api/resources/cms/shipment"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type ShipperNotifyController struct {
	controllers.BaseController
}

// PostCreate
//
// @Description: 创建配送员通知
// @Author: Rixat
// @Time: 2023-11-07 03:11:52
// @receiver
// @param c *gin.Context
func (cms ShipperNotifyController) PostCreate(c *gin.Context) {
	var (
		params        shipmentResource.ShipperNotify
		err           = c.ShouldBindJSON(&params)
		notifyService = cmsService.NewShipperNotifyService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)

	// 创建
	err = notifyService.Create(admin, params)
	if err != nil {
		cms.Fail(c, err.<PERSON>rror(), -1000)
		return
	}
	cms.Ok(c)
}

// GetList
//
// @Description: 配送员通知列表
// @Author: Rixat
// @Time: 2023-11-07 03:21:36
// @receiver
// @param c *gin.Context
func (cms ShipperNotifyController) GetList(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" binding:"required"`       // 当前页数
		Limit       int    `form:"limit" binding:"required"`      // 每页显示数量
		CityID      int    `form:"city_id" binding:""`            // 地区ID
		AreaID      int    `form:"area_id" binding:""`            // 区域ID
		Kw          string `form:"kw" binding:""`                 // 关键字
		State       int    `form:"state" binding:""`              // 状态
		SortColumns string `form:"sort_columns"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		notifyService = cmsService.NewShipperNotifyService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取排序参数
	sort := tools.SortColumnParam(params.SortColumns)
	// 获取信息
	result, err := notifyService.List(params.Page, params.Limit, params.CityID, params.AreaID, params.Kw, params.State, sort)
	cms.Success(c, result, "msg", 200)
}

// PostChangeState
//
// @Description: 配送员通知修改状态
// @Author: Rixat
// @Time: 2023-11-07 03:27:47
// @receiver
// @param c *gin.Context
func (cms ShipperNotifyController) PostChangeState(c *gin.Context) {
	type Params struct {
		ID    int `form:"id" binding:"required"`
		State int `form:"state" binding:"required"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		notifyService = cmsService.NewShipperNotifyService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 修改状态
	err = notifyService.ChangeState(params.ID, params.State)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// PostDelete
//
// @Description: 删除
// @Author: Rixat
// @Time: 2023-11-07 03:35:52
// @receiver
// @param c *gin.Context
func (cms ShipperNotifyController) PostDelete(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		notifyService = cmsService.NewShipperNotifyService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 创建
	err = notifyService.Delete(params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, "", "success", 200)
}
