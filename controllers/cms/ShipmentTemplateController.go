package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/permissions"
	shipmentResource "mulazim-api/resources/cms/shipment"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type ShipperIncomeTemplateController struct {
	controllers.BaseController
}

// PostCreate
//
// @Description: 提交配送费基本信息
// @Author: Rixat
// @Time: 2023-10-24 03:01:49
// @receiver
// @param c *gin.Context
func (cms ShipperIncomeTemplateController) PostCreate(c *gin.Context) {
	var (
		params          shipmentResource.ShipmentBaseInfo
		value, _        = c.Get("admin")
		admin           = value.(models.Admin)
		err             = c.ShouldBind(&params)
		templateService = cmsService.NewShipperIncomeTemplateService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 创建
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	result, err := templateService.CreateBaseInfo(admin, params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, result, "msg", 200)
}

// GetDetail
//
// @Description: 配送费模板详情
// @Author: Rixat
// @Time: 2023-11-03 10:28:57
// @receiver
// @param c *gin.Context
func (cms ShipperIncomeTemplateController) GetDetail(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"`
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		templateService = cmsService.NewShipperIncomeTemplateService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取详情内容
	admin := permissions.GetAdmin(c)
	result, err := templateService.Detail(admin, params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, result, "msg", 200)
}

// GetList
//
// @Description: 配送费模板列表
// @Author: Rixat
// @Time: 2023-11-03 10:31:14
// @receiver
// @param c *gin.Context
func (cms ShipperIncomeTemplateController) GetList(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" binding:"required"`  // 当前页数
		Limit       int    `form:"limit" binding:"required"` // 每页显示数量
		CityID      int    `form:"city_id" binding:""`       // 地区ID
		AreaID      int    `form:"area_id" binding:""`       // 区域ID
		Kw          string `form:"kw" binding:""`            // 关键字(模板名称)
		SortColumns string `form:"sort_columns"`
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		templateService = cmsService.NewShipperIncomeTemplateService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取排序参数
	sort := tools.SortColumnParam(params.SortColumns)
	// 获取列表
	result := templateService.List(params.Page, params.Limit, params.CityID, params.AreaID, params.Kw, sort)
	cms.Success(c, result, "msg", 200)
}

// PostDelete
//
// @Description: 配送员模板
// @Author: Rixat
// @Time: 2023-11-03 09:01:18
// @receiver
// @param c *gin.Context
func (cms ShipperIncomeTemplateController) PostDelete(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"`
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		templateService = cmsService.NewShipperIncomeTemplateService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 删除模板
	admin := permissions.GetAdmin(c)
	err = templateService.Delete(admin, params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}
