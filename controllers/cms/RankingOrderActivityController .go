package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	rankingorderactivity "mulazim-api/requests/cms/rankingOrderActivity"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"
	cmsTransformer "mulazim-api/transformers/cms"

	"github.com/gin-gonic/gin"
)

type RankingOrderActivityController struct {
	controllers.BaseController
}

// CreateRankingOrderActivity
//
//	@Description: 创建排行订单活动
//	@Time: 2025-02-27 15:04:05
//	@param c *gin.Context
func (r *RankingOrderActivityController) CreateRankingOrderActivity(c *gin.Context) {
	// 实现创建排行订单活动逻辑
	var (
		rankingOrderActivityService = cmsService.NewRankingOrderActivityService(c)
		rankingOrderActivityCreateRequest rankingorderactivity.RankingOrderActivityCreateRequest
	)

	if err := c.ShouldBindJSON(&rankingOrderActivityCreateRequest); err != nil {
		panic(err)
	}
	admin := permissions.GetAdmin(c)
	IsAgent:= permissions.IsAgent(admin)
	err := rankingOrderActivityService.CreateRankingOrderActivity(rankingOrderActivityCreateRequest,admin, IsAgent)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	r.Success(c, make([]int, 0), "msg", 200)
}

// UpdateRankingOrderActivity
//
//	@Description: 修改排行订单活动
//	@Time: 2025-02-27 15:04:05
//	@param c *gin.Context
func (r *RankingOrderActivityController) UpdateRankingOrderActivity(c *gin.Context) {
	var (
		rankingOrderActivityService = cmsService.NewRankingOrderActivityService(c)
		params rankingorderactivity.RankingOrderActivityUpdateRequest
	)

	if err := c.ShouldBindJSON(&params); err != nil {
		panic(err)
	}
	admin := permissions.GetAdmin(c)
	IsAgent:= permissions.IsAgent(admin)
	err := rankingOrderActivityService.UpdateRankingOrderActivity(params,admin, IsAgent)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	r.Success(c, make([]int, 0), "msg", 200)
}	

// ListRankingOrderActivity
//
//	@Description: 获取排行订单活动列表
//	@Time: 2025-02-27 15:04:05
//	@param c *gin.Context
func (r *RankingOrderActivityController) ListRankingOrderActivity(c *gin.Context) {
	var (
		params rankingorderactivity.RankingOrderActivityListRequest
		rankingOrderActivityService = cmsService.NewRankingOrderActivityService(c)
		rankingOrderActivityTransformer = cmsTransformer.NewRankingOrderActivityTransformer(c)
	)

	if err := c.ShouldBindQuery(&params); err != nil {
		panic(err)
	}
	pagination := tools.GetPagination(c)
	params.Page = pagination.Page
	params.Limit = pagination.Limit

	admin := permissions.GetAdmin(c)
	IsAgent:= permissions.IsAgent(admin)
	if IsAgent {
		params.CityId = &admin.AdminCityID
		params.AreaId = &admin.AdminAreaID
	}

	list, total, err := rankingOrderActivityService.ListRankingOrderActivity(params,IsAgent)
	if err != nil {
		r.Fail(c, "failed", -1000)
		return
	}

	rankingOrderActivityListResponse := rankingOrderActivityTransformer.FormatListRankingOrderActivity(list)
	r.Success(c, gin.H{
		"items":rankingOrderActivityListResponse,
		"total":total,
	}, "msg", 200)
}	


// DetailRankingOrderActivity
//
//	@Description: 获取排行订单活动详情
//	@Time: 2025-02-27 15:04:05
//	@param c *gin.Context
func (r *RankingOrderActivityController) DetailRankingOrderActivity(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required,gt=0"`
	}
	var (
		params Params
		rankingOrderActivityService = cmsService.NewRankingOrderActivityService(c)
		rankingOrderActivityTransformer = cmsTransformer.NewRankingOrderActivityTransformer(c)
	)
	if err := c.ShouldBindQuery(&params); err != nil {
		panic(err)
	}
	rankingOrderActivity, err := rankingOrderActivityService.DetailRankingOrderActivity(params.ID)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	likeCount, dislikeCount, err := rankingOrderActivityService.GetRankingOrderActivityLikeCount(params.ID)
	if err != nil {
		r.Fail(c, "failed", -1000)
		return
	}
	rankingOrderActivityResponse := rankingOrderActivityTransformer.FormatDetailRankingOrderActivity(rankingOrderActivity)
	rankingOrderActivityResponse.LikeCount = likeCount
	rankingOrderActivityResponse.DislikeCount = dislikeCount
	r.Success(c, rankingOrderActivityResponse, "msg", 200)
}


// WinnerList
//
//	@Description: 获取活动中奖列表
//	@Time: 2025-02-27 15:04:05
//	@param c *gin.Context
func (r *RankingOrderActivityController) WinnerList(c *gin.Context) {
	
	var (
		params rankingorderactivity.RankingOrderActivityWinnerListRequest
		rankingOrderActivityService = cmsService.NewRankingOrderActivityService(c)
		rankingOrderActivityTransformer = cmsTransformer.NewRankingOrderActivityTransformer(c)
	)
	if err := c.ShouldBindQuery(&params); err != nil {
		panic(err)
	}
	pagination := tools.GetPagination(c)
	params.Page = pagination.Page
	params.Limit = pagination.Limit
	list, total, err := rankingOrderActivityService.WinnerList(params)
	if err != nil {
		r.Fail(c, "failed", -1000)
		return
	}
	rankingOrderActivityWinnerListResponse := rankingOrderActivityTransformer.FormatWinnerList(list)
	r.Success(c, gin.H{
		"items":rankingOrderActivityWinnerListResponse,
		"total":total,
	}, "msg", 200)
}


// ChangeState
//
//	@Description: 修改活动状态
//	@Time: 2025-02-27 15:04:05
//	@param c *gin.Context
func (r *RankingOrderActivityController) ChangeState(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required,gt=0"`
		State int `form:"state" binding:"required,gt=0,lt=3,oneof=1 2"`
	}
	var (
		params Params
		rankingOrderActivityService = cmsService.NewRankingOrderActivityService(c)
	)
	if err := c.ShouldBindJSON(&params); err != nil {
		panic(err)
	}
	err := rankingOrderActivityService.ChangeState(params.ID, params.State)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	r.Success(c, make([]int, 0), "msg", 200)
}	


// DeleteRankingOrderActivity
//
//	@Description: 删除活动
//	@Time: 2025-02-27 15:04:05
//	@param c *gin.Context
func (r *RankingOrderActivityController) DeleteRankingOrderActivity(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required,gt=0"`
	}
	var (
		params Params
		rankingOrderActivityService = cmsService.NewRankingOrderActivityService(c)
	)
	if err := c.ShouldBind(&params); err != nil {
		panic(err)
	}
	err := rankingOrderActivityService.DeleteRankingOrderActivity(params.ID)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	r.Success(c, make([]int, 0), "msg", 200)
}

// UpdateDetail
//
//	@Description: 获取修改的详细
//	@Time: 2025-02-27 15:04:05
//	@param c *gin.Context
func (r *RankingOrderActivityController) UpdateDetail(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required,gt=0"`
	}
	var (
		params Params
		rankingOrderActivityService = cmsService.NewRankingOrderActivityService(c)
		rankingOrderActivityTransformer = cmsTransformer.NewRankingOrderActivityTransformer(c)
	)
	if err := c.ShouldBindQuery(&params); err != nil {
		panic(err)
	}
	rankingOrderActivity, err := rankingOrderActivityService.GetUpdateDetail(params.ID)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	rankingOrderActivityResponse := rankingOrderActivityTransformer.FormatUpdateDetail(rankingOrderActivity)
	r.Success(c, rankingOrderActivityResponse, "msg", 200)
}
