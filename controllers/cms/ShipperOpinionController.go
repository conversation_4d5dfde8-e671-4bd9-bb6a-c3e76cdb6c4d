package cms

import (
	"mulazim-api/controllers"

	"mulazim-api/permissions"

	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type ShipperOpinionController struct {
	controllers.BaseController
}

// GetList
func (cms ShipperOpinionController) GetList(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" binding:"required"`       // 当前页数
		Limit       int    `form:"limit" binding:"required"`      // 每页显示数量
		Type        int    `form:"type" binding:""`               // 1:故障 2:意见
		CityID      int    `form:"city_id" binding:""`            // 地区ID
		AreaID      int    `form:"area_id" binding:""`            // 区域ID
		StartDate   string `form:"start_date" binding:"required"` // 开始日期
		EndDate     string `form:"end_date" binding:"required"`   // 结束日期
		State       int    `form:"state" binding:""`              // 状态
		ShipperID   int    `form:"shipper_id" binding:""`         //
		SortColumns string `form:"sort_columns"`
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		opinionService = cmsService.NewShipperOpinionService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取排序参数
	sort := tools.SortColumnParam(params.SortColumns)
	// 获取信息
	result := opinionService.List(params.Page, params.Limit, params.Type, params.CityID, params.AreaID, params.State, params.ShipperID, params.StartDate, params.EndDate, sort)
	cms.Success(c, result, "msg", 200)
}

// GetDetail
func (cms ShipperOpinionController) GetDetail(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"` // ID
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		opinionService = cmsService.NewShipperOpinionService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取信息
	result, err := opinionService.Detail(params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, result, "msg", 200)
}
