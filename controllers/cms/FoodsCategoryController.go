package cms

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"math/rand"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"
	cmsTransformer "mulazim-api/transformers/cms"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"
)

type FoodsCategoryController struct {
	controllers.BaseController
}

// List
//
//	@Author: YaKupJan
//	@Date: 2024-09-19 17:10:47
//	@Description: 美食分类列表
//	@receiver f
//	@param c
func (f FoodsCategoryController) List(c *gin.Context) {
	type Params struct {
		Search      string `form:"search"`
		SortColumns string `form:"sort_columns" binding:""`
	}
	var (
		params      Params
		err         = c.ShouldBindQuery(&params)
		service     = cmsService.NewFoodsCategoryService(c)
		transformer = cmsTransformer.NewFoodsCategoryTransformer(c)
	)
	if err != nil {
		panic(err)
		return
	}
	// 分页
	pagination := tools.GetPagination(c)
	list, total := service.List(pagination,params.Search, params.SortColumns)
	items := transformer.ListFormat(list)
	f.Success(c, gin.H{
		"items": items,
		"total": total,
	}, "success", 200)

}

// Create
//
//	@Author: YaKupJan
//	@Date: 2024-09-19 17:11:13
//	@Description: 新增美食分类
//	@receiver f
//	@param c
func (f FoodsCategoryController) Create(c *gin.Context) {
	type Params struct {
		NameZh string `json:"name_zh" binding:"required"`
		NameUg string `json:"name_ug" binding:"required"`
		Image  string `json:"image" binding:"required"`
		Weight int    `json:"weight" binding:""`
		State  int    `json:"state" binding:"oneof=0 1"`
	}
	var (
		params  Params
		err     = c.ShouldBindJSON(&params)
		service = cmsService.NewFoodsCategoryService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	createErr := service.Create(admin, params.NameZh, params.NameUg, params.Image, params.Weight, params.State)
	if createErr != nil {
		f.Fail(c, createErr.Error(), -1000)
		return
	}
	f.Ok(c)
}

// Detail
//
//	@Author: YaKupJan
//	@Date: 2024-09-19 17:11:32
//	@Description: 美食分类详情
//	@receiver f
//	@param c
func (f FoodsCategoryController) Detail(c *gin.Context) {
	type Params struct {
		Id int `form:"id" binding:"required"`
	}
	var (
		params      Params
		err         = c.ShouldBindQuery(&params)
		service     = cmsService.NewFoodsCategoryService(c)
		transformer = cmsTransformer.NewFoodsCategoryTransformer(c)
	)
	if err != nil {
		panic(err)
		return
	}
	detailErr, detail := service.Detail(params.Id)
	if detailErr != nil {
		f.Fail(c, detailErr.Error(), -1000)
		return
	}
	detailFormat := transformer.DetailFormat(detail)
	f.Success(c, detailFormat, "success", 200)
}
func (f FoodsCategoryController) generateRandomFilename() string {
	// 生成当前时间的字符串形式
	timestamp := time.Now().Format("20060102150405")
	// 生成五位随机数字（00000 - 99999）
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Intn(100000)
	randomString := strconv.Itoa(randomNumber)
	paddedRandomString := fmt.Sprintf("%05s", randomString)
	// 将随机数字追加到时间戳后面
	return timestamp + paddedRandomString
}

// UploadImage
//
//	@Author: YaKupJan
//	@Date: 2024-09-19 17:11:57
//	@Description: 上传美食分类图片
//	@receiver f
//	@param c
func (f FoodsCategoryController) UploadImage(c *gin.Context) {
	typeName := c.PostForm("type_name")
	file, err := c.FormFile("image")
	if err != nil {
		f.Fail(c, "无法获取上传文件", http.StatusBadRequest)
		return
	}

	ext := filepath.Ext(file.Filename)

	if ext != ".jpg" && ext != ".jpeg" && ext != ".png" {
		f.Fail(c, "只允许jpg|jpeg|png类型图片", http.StatusBadRequest)
		return
	}

	// 临时的
	fileName := f.generateRandomFilename() + ext
	rootDir := configs.MyApp.UploadRootDir
	imageDir := "uploads/" + typeName
	if _, err := os.Stat(rootDir + imageDir); os.IsNotExist(err) {
		// 目录不存在，创建目录
		err := os.MkdirAll(rootDir+imageDir, 0755)
		if err != nil {
			// 创建目录失败
			fmt.Println("目录创建失败:", err)
			return
		}
	}
	imagePath := imageDir + "/" + fileName
	fullPath := rootDir + imagePath
	if err := c.SaveUploadedFile(file, fullPath); err != nil {
		f.Fail(c, "上传保存失败", http.StatusBadRequest)
		return
	}

	fileUrl := configs.MyApp.CdnUrl + imagePath

	f.Success(c, map[string]interface{}{
		"url":        fileUrl,
		"image_path": imagePath,
	}, "上传成功", http.StatusOK)
}

// Edit
//
//	@Author: YaKupJan
//	@Date: 2024-09-19 17:12:10
//	@Description: 编辑美食分类
//	@receiver f
//	@param c
func (f FoodsCategoryController) Edit(c *gin.Context) {
	type Params struct {
		Id     int    `json:"id" binding:"required"`
		NameZh string `json:"name_zh" binding:"required"`
		NameUg string `json:"name_ug" binding:"required"`
		Image  string `json:"image" binding:"required"`
		Weight int    `json:"weight" binding:""`
		State  int    `json:"state" binding:"oneof=0 1"`
	}
	var (
		params  Params
		err     = c.ShouldBindJSON(&params)
		service = cmsService.NewFoodsCategoryService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	editErr := service.Edit(admin, params.Id, params.NameZh, params.NameUg, params.Image, params.Weight, params.State)
	if editErr != nil {
		f.Fail(c, editErr.Error(), -1000)
		return
	}
	f.Ok(c)
}

// FoodsList
//
//  @Author: YaKupJan
//  @Date: 2024-09-20 11:04:37
//  @Description: 获取分组内美食的列表
//  @receiver f
//  @param c
func (f FoodsCategoryController) FoodsList(c *gin.Context) {
	type Params struct {
		FoodsCategoryId int    `form:"foods_category_id" binding:"required"`
		Search          string `form:"search" binding:""`
	}
	var (
		params      Params
		service     = cmsService.NewFoodsCategoryService(c)
		transformer = cmsTransformer.NewFoodsCategoryTransformer(c)
		err         = c.ShouldBindQuery(&params)
		pagination  = tools.GetPagination(c)
	)
	if err != nil {
		panic(err)
		return
	}
	err,foodsList, total := service.FoodsList(c,pagination, params.FoodsCategoryId, params.Search)
	if err!= nil {
		f.Fail(c, err.Error(), -1000)
		return
	}
	foodsItems := transformer.FoodsListFormat(foodsList)
	f.Success(c, gin.H{
		"items": foodsItems,
		"total":      total,
	}, "success", 200)
}
