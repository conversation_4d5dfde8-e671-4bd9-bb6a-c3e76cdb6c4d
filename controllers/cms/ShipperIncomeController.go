package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/permissions"
	shipmentResource "mulazim-api/resources/cms/shipment"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type ShipperIncomeController struct {
	controllers.BaseController
}

// / PostCreate
//
// @Description: 配送员收入列表
// @Author: Rixat
// @Time: 2023-11-07 02:31:34
// @receiver
// @param c *gin.Context
func (cms ShipperIncomeController) PostCreate(c *gin.Context) {
	var (
		params        shipmentResource.ShipperIncome
		err           = c.ShouldBind(&params)
		incomeService = cmsService.NewShipperIncomeService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	// 创建
	err = incomeService.Create(admin, params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, "", "success", 200)
}

// GetDetail
//
// @Description: 配送员收入详情
// @Author: Rixat
// @Time: 2023-11-07 02:31:50
// @receiver
// @param c *gin.Context
func (cms ShipperIncomeController) GetDetail(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		incomeService = cmsService.NewShipperIncomeService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取详情信息
	result, err := incomeService.Detail(params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, result, "success", 200)
}

// GetList
//
// @Description: 配送员收入列表
// @Author: Rixat
// @Time: 2023-11-07 02:32:03
// @receiver
// @param c *gin.Context
func (cms ShipperIncomeController) GetList(c *gin.Context) {
	type Params struct {
		Type        int    `form:"type" binding:"required"`       // 收入类型：类型 1：订单收入，2:奖励收入，3:打赏收入，4.特殊时间，5.特殊天气，6.好评，7.差评，8.投诉，9:迟到
		Page        int    `form:"page" binding:"required"`       // 当前页数
		Limit       int    `form:"limit" binding:"required"`      // 每页显示数量
		CityID      int    `form:"city_id" binding:""`            // 地区ID
		AreaID      int    `form:"area_id" binding:""`            // 区域ID
		Kw          string `form:"kw" binding:""`                 // 关键字(姓名或手机号)
		StartDate   string `form:"start_date" binding:"required"` // 关键字(姓名或手机号)
		EndDate     string `form:"end_date" binding:"required"`   // 关键字(姓名或手机号)
		SortColumns string `form:"sort_columns"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		incomeService = cmsService.NewShipperIncomeService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取排序参数
	sort := tools.SortColumnParam(params.SortColumns)
	// 获取信息
	result := incomeService.List(params.Type, params.Page, params.Limit, params.CityID, params.AreaID, params.Kw, params.StartDate, params.EndDate, sort)
	// 统计
	result["header"] = incomeService.Header(params.Type, params.CityID, params.AreaID, params.StartDate, params.EndDate)
	cms.Success(c, result, "success", 200)
}

// GetIncomeTypes
//
// @Description: 获取收入类型
// @Author: Rixat
// @Time: 2023-12-05 07:54:55
// @receiver
// @param c *gin.Context
func (cms ShipperIncomeController) GetIncomeTypes(c *gin.Context) {
	var (
		l, _ = c.Get("lang_util")
		lang = l.(lang.LangUtil)
	)
	// 1:事假 2:病假 3:婚嫁 4:产假/陪产假
	incomeTypes := lang.TArr("income_types")
	resMap := make([]map[string]interface{}, 0)
	for key, value := range incomeTypes {
		resMap = append(resMap, map[string]interface{}{"id": key, "name": value})
	}
	cms.Success(c, resMap, "", 200)
}

// GetOrderList
//
// @Description: 订单列表
// @Author: Rixat
// @Time: 2023-12-05 07:55:13
// @receiver
// @param c *gin.Context
func (cms ShipperIncomeController) GetOrderList(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" binding:"required"`       // 当前页数
		Limit       int    `form:"limit" binding:"required"`      // 每页显示数量
		CityID      int    `form:"city_id" binding:""`            // 地区ID
		AreaID      int    `form:"area_id" binding:""`            // 区域ID
		ShipperID   int    `form:"shipper_id" binding:""`         // 区域ID
		Kw          string `form:"kw" binding:""`                 // 关键字(姓名或手机号)
		StartDate   string `form:"start_date" binding:"required"` //
		EndDate     string `form:"end_date" binding:"required"`   //
		SortColumns string `form:"sort_columns"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		incomeService = cmsService.NewShipperIncomeService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	sort := tools.SortColumnParam(params.SortColumns)
	// 获取信息
	result := incomeService.GetOrderList(params.Page, params.Limit, params.CityID, params.AreaID, params.Kw, params.ShipperID, params.StartDate, params.EndDate, sort)
	cms.Success(c, result, "success", 200)
}

func (cms ShipperIncomeController) GetShipperList(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" binding:"required"`  // 当前页数
		Limit       int    `form:"limit" binding:"required"` // 每页显示数量
		CityID      int    `form:"city_id" binding:""`       // 地区ID
		AreaID      int    `form:"area_id" binding:""`       // 区域ID
		Kw          string `form:"kw" binding:""`            // 关键字(姓名或手机号)
		SortColumns string `form:"sort_columns"`
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		incomeService = cmsService.NewShipperIncomeService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	admin := permissions.GetAdmin(c)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	sort := tools.SortColumnParam(params.SortColumns)
	disabled :=tools.ToInt(c.DefaultQuery("disabled","0"))
	// 获取信息
	result := incomeService.GetShipperList(admin, params.Page, params.Limit, params.CityID, params.AreaID, params.Kw, sort,disabled)
	cms.Success(c, result, "success", 200)
}
