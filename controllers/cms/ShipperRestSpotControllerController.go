package cms

import (
	"encoding/json"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/tools"

	"mulazim-api/models"
	"mulazim-api/permissions"
	resource "mulazim-api/resources"
	service "mulazim-api/services"

	"github.com/gin-gonic/gin"
)

type ShipperRestSpotController struct {
	controllers.BaseController
}

// PostCreate
func (cms ShipperRestSpotController) PostCreate(c *gin.Context) {
	var (
		params            resource.ShipperRestSpotEntity
		err               = c.ShouldBind(&params)
		attendanceService = service.NewShipperRestSpotService(c)
		value, _          = c.Get("admin")
		admin             = value.(models.Admin)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if params.CityId == 0 && params.AreaId == 0 {
		params.CityId = admin.AdminCityID
		params.AreaId = admin.AdminAreaID
	}
	// 创建
	err = attendanceService.Create(admin, params)
	if err != nil {
		cms.Fail(c, err.<PERSON>rror(), -1000)
		return
	}
	cms.Ok(c)
}

// List
// @param c *gin.Context
func (cms ShipperRestSpotController) GetList(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" query:"page" `  // 当前页数
		Limit       int    `form:"limit" query:"limit" ` // 每页显示数量
		
		CityID      int    `form:"city_id" query:"city_id" binding:""`       // 地区ID
		AreaID      int    `form:"area_id" query:"area_id" binding:""`       // 区域ID
		State       int    `form:"state" query:"state" binding:""`         // 状态
		KW          string `form:"kw" query:"kw" binding:""`
		
	}
	var (
		params            Params
		err               = c.ShouldBind(&params)
		attendanceService = service.NewShipperRestSpotService(c)
		value, _          = c.Get("admin")
		admin             = value.(models.Admin)
		langUtil          = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	
	if params.Page == 0 {
		params.Page = 1
	}
	if params.Limit == 0 {
		params.Limit = 10
	}
	
	// 获取信息
	result := attendanceService.ListForCms(admin, params.Page, params.Limit, params.CityID, params.AreaID, params.State,params.KW,langUtil.Lang)
	cms.Success(c, result, "msg", 200)
}



// GetDetail
// @param c *gin.Context
func (cms ShipperRestSpotController) GetDetail(c *gin.Context) {
	type Params struct {
		ID int `form:"id" query:"id" binding:"required"` // ID
	}
	var (
		params            Params
		err               = c.ShouldBind(&params)
		attendanceService = service.NewShipperRestSpotService(c)
		langUtil          = lang.LangUtil{
			Lang: c.Param("locale"),
		}
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取信息
	result, err := attendanceService.Detail(params.ID,langUtil.Lang)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, result, "msg", 200)
}

// PostChangeState
// @param c *gin.Context
func (cms ShipperRestSpotController) PostChangeState(c *gin.Context) {
	type Params struct {
		ID            int    `form:"id" json:"id" binding:"required"`           // ID
		State   json.RawMessage    `form:"state" json:"state" binding:"required"` // 审核状态
	}
	var (
		params            Params
		err               = c.ShouldBind(&params)
		attendanceService = service.NewShipperRestSpotService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 审核
	// admin := permissions.GetAdmin(c)
	// Parse ID as either string or number
	var stateStr interface{}
	json.Unmarshal(params.State, &stateStr)
	state :=tools.ToInt(stateStr)
	err = attendanceService.ChangeState(params.ID, state)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

func (cms ShipperRestSpotController) PostDelete(c *gin.Context) { 
	type Params struct {
		ID int `form:"id" binding:"required"`
	}
	var (
		params            Params
		err               = c.ShouldBind(&params)
		attendanceService = service.NewShipperRestSpotService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	err = attendanceService.Delete(params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)

}
func (cms ShipperRestSpotController) PostUpdate(c *gin.Context) { 
	var (
		params         resource.ShipperRestSpotUpdateEntity
		err            = c.ShouldBind(&params)
		attendanceService = service.NewShipperRestSpotService(c)
		value, _          = c.Get("admin")
		admin             = value.(models.Admin)
	)
	if err != nil {
		panic(err)
		return
	}
	err = attendanceService.Update(admin,params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}