package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	partRefundRequest "mulazim-api/requests/cms/partRefund"
	cmsService "mulazim-api/services/cms"
	merchantService "mulazim-api/services/merchant"
	"mulazim-api/tools"
	cmsTransformer "mulazim-api/transformers/cms"
	merchantTransformer "mulazim-api/transformers/merchant"

	"github.com/gin-gonic/gin"
)

type PartRefundController struct {
	controllers.BaseController
}

// RefundReasonList
//
//	@Author: YaKupJan
//	@Date: 2024-11-26 18:40:36
//	@Description: 退款原因列表
//	@receiver p
//	@param c
func (p PartRefundController) RefundReasonList(c *gin.Context) {
	var (
		service     = cmsService.NewPartRefundService(c)
		transformer = cmsTransformer.NewPartRefundTransformer(c)
	)
	pagination := tools.GetPagination(c)
	// 获取退款原因列表
	data, total := service.RefundReasonList(pagination,0)
	// 格式化退款原因列表
	format := transformer.RefundReasonListFormat(data, total)
	p.Success(c, format, "msg", 200)
}

// CreateRefundReason
//
//	@Author: YaKupJan
//	@Date: 2024-11-27 10:20:41
//	@Description: 添加退款原因
//	@receiver p
//	@param c
func (p PartRefundController) CreateRefundReason(c *gin.Context) {
	var (
		params  partRefundRequest.PartRefundReasonCreateRequest
		service = cmsService.NewPartRefundService(c)
	)
	if err := c.ShouldBindJSON(&params); err != nil {
		panic(err)
	}
	service.CreateRefundReason(params.NameZh, params.NameUg, params.State, params.Weight,params.SubType)
	p.Ok(c)
}

// UpdateRefundReason
//
//  @Author: YaKupJan
//  @Date: 2024-11-27 12:21:46
//  @Description: 修改退款原因
//  @receiver p
//  @param c
func (p PartRefundController) UpdateRefundReason(c *gin.Context) {
	var (
		params  partRefundRequest.PartRefundReasonUpdateRequest
		service = cmsService.NewPartRefundService(c)
	)
	if err := c.ShouldBindJSON(&params); err != nil {
		panic(err)
	}
	// 修改退款原因
	service.UpdateRefundReason(params.ID,params.NameZh, params.NameUg, params.State, params.Weight,params.SubType)
	p.Ok(c)
}

// DeleteRefundReason
//
//  @Author: YaKupJan
//  @Date: 2024-11-27 16:10:52
//  @Description: 删除退款原因
//  @receiver p
//  @param c
func (p PartRefundController) DeleteRefundReason(c *gin.Context) {
	type Params struct {
		ID     int    `json:"id" binding:"required"`
	}
	var (
		params  Params
		service = cmsService.NewPartRefundService(c)
	)
	if err := c.ShouldBindJSON(&params); err != nil {
		panic(err)
	}
	// 删除退款原因
	service.DeleteRefundReason(params.ID)
	p.Ok(c)
}

// RefundList
//
//  @Author: YaKupJan
//  @Date: 2024-11-29 10:48:11
//  @Description: 退款列表
//  @receiver p
//  @param c
func (p PartRefundController) RefundList(c *gin.Context) {
	var (
		service     = cmsService.NewPartRefundService(c)
		transformer = cmsTransformer.NewPartRefundTransformer(c)
	)
	pagination := tools.GetPagination(c)
	// 获取退款原因列表
	data, total := service.RefundList(pagination)
	// 格式化退款原因列表
	format := transformer.RefundListFormat(data, total)
	p.Success(c, format, "msg", 200)
}




// @Title 部分退款列表
// @Summary 部分退款列表
// @Description 部分退款列表
// @Tags 部分退款
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param page query int false "当前页数" default(1)
// @Param limit query int false "每页数量" default(10)
// @Param city_id query int false "城市ID"
// @Param area_id query int false "区域ID"
// @Param restaurant_id query int false "餐厅ID"
// @Param state query int false "状态"
// @Param part_refund_creator_id query int false "部分退款创建者ID"
// @Param part_refund_type query int false "部分退款类型"
// @Param begin_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Param kw query string false "关键字"
// @Success 200 {object} response.OrderListItemsResponse "成功响应"
// @Failure 400 {object} map[string]interface{} "请求参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /smart/v2/part-refund/refund-order-list [get]
// <AUTHOR>
func (p PartRefundController) GetPartRefundOrderList(c *gin.Context) {
	var (
		params  partRefundRequest.PartRefundOrderListRequest
		service = cmsService.NewPartRefundService(c)
		transformer = cmsTransformer.NewPartRefundTransformer(c)
	)
	if err := c.ShouldBind(&params); err != nil {
		panic(err)
		return
	}
	pagination := tools.GetPagination(c)
	params.CityID,params.AreaID = permissions.GetAdminAreaInfo(c,params.CityID,params.AreaID)
	partRefundList,total := service.GetPartRefundOrderList(params,pagination)
	format := transformer.PartRefundOrderListFormat(partRefundList,total)
	p.Success(c,format,"msg",200)
}

// @Title 部分退款订单详情
// @Summary 部分退款订单详情
// @Description 获取指定ID的部分退款订单的详细信息
// @Tags 部分退款
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param id query int true "部分退款订单ID"
// @Success 200 {object} response.PartRefundOrderDetailResponse "成功响应"
// @Failure 400 {object} map[string]interface{} "请求参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /smart/v2/part-refund/refund-order-detail [get]
// <AUTHOR>
func (p PartRefundController) GetPartRefundOrderDetail(c *gin.Context) {
	var (
		params  partRefundRequest.PartRefundOrderDetailRequest
		service = cmsService.NewPartRefundService(c)
		transformer = cmsTransformer.NewPartRefundTransformer(c)
	)
	if err := c.ShouldBind(&params); err != nil {
		panic(err)
		return
	}
	partRefund := service.GetPartRefundOrderDetail(params.ID)
	format := transformer.PartRefundOrderDetailFormat(partRefund)
	p.Success(c,format,"msg",200)
}

// PartRefundList
//
//  @Author: YaKupJan
//  @Date: 2024-11-29 17:47:59
//  @Description: 部分退款返回信息
//  @receiver merchant
//  @param c
func (merchant *PartRefundController) PartRefundInfo(c *gin.Context) {
	type Params struct {
		OrderID  int `form:"order_id" binding:"required"`  // 排序字段
	}
	var (
		params Params
		service = merchantService.NewMerchantService(c)
		transformer = merchantTransformer.NewMerchantTransformer(c)
		cmsService1 = cmsService.NewPartRefundService(c)

	)
	if err := c.ShouldBindQuery(&params); err != nil {
		panic(err)
		return
	}
	pagination := tools.GetPagination(c)
	pagination.Limit = 100
	// 部分退款返回信息
	orderToday := service.GetPartRefundList(params.OrderID)
	// 原因列表
	reasonList, _ := cmsService1.RefundReasonList(pagination,1)
	// 部分退款返回信息 格式化
	data := transformer.PartRefundListFormat(orderToday,reasonList)
	merchant.Success(c,data,"msg",200)
}