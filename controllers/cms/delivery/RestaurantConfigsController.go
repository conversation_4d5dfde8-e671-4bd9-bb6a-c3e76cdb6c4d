package delivery

import (
	"errors"
	"mulazim-api/controllers"
	shipmentModels "mulazim-api/models/shipment"
	"mulazim-api/permissions"
	deliveryRequests "mulazim-api/requests/cms/delivery"
	"mulazim-api/services"
	cmsServices "mulazim-api/services/cms"
	"mulazim-api/tools"
	cmsTransformers "mulazim-api/transformers/cms"
	"time"

	"gorm.io/gorm"

	"net/http"

	"github.com/gin-gonic/gin"
)

type RestaurantConfigsController struct {
	controllers.BaseController
}

// GetRestaurantList
//
//	@Description: 餐厅配送设置信息
//	@Author: Rixat
//	@Time: 2025-06-12 13:21:10
//	@param ctx *gin.Context
func (ctr *RestaurantConfigsController) GetRestaurantList(ctx *gin.Context) {
	var (
		params               deliveryRequests.GetListDeliveryConfigsRequest
		err                  = ctx.ShouldBind(&params)
		_deliveryAreaSvc     = cmsServices.NewDeliveryAreaService(ctx)
		_deliveryConfigsTrns = cmsTransformers.NewDeliveryConfigsTransformer(ctx)
	)
	// 验证参数
	if err != nil {
		panic(err)
	}
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	// 获取信息
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(ctx, params.CityID, params.AreaID)
	totalCount, items := _deliveryAreaSvc.GetDeliveryRestaurantList(params)
	// 格式化结果
	areaConfig, _ := _deliveryAreaSvc.GetDeliveryAreaById(params.ParentID)
	if areaConfig == nil || areaConfig.ID == 0 {
		areaConfigRunning := _deliveryAreaSvc.GetRunningDeliveryConfig(params.AreaID)
		if areaConfigRunning.ID > 0 {
			areaConfig = &areaConfigRunning
		}
	}
	if areaConfig == nil {
		ctr.Fail(ctx, "not_area_config", -1000)
		return
	}
	listFormat := _deliveryConfigsTrns.FormatRestaurantList(items,areaConfig)
	ctr.Success(ctx, map[string]interface{}{
		"total": totalCount,
		"items": listFormat,
	}, "success", 200)
}

// GetRestaurantDetail
//
//	@Description: 获取餐厅配送范围详情
//	@Author: Rixat
//	@Time: 2025-07-09 11:01:10
//	@receiver ctr *RestaurantConfigsController
//	@param ctx *gin.Context
func (ctr *RestaurantConfigsController) GetRestaurantDetail(ctx *gin.Context) {
	type Params struct {
		RestaurantID int `form:"restaurant_id" binding:"required,min=1"`
		ParentID     int `form:"parent_id" binding:"required,min=1"`
	}
	var (
		params               Params
		err                  = ctx.ShouldBind(&params)
		_deliveryAreaSvc     = cmsServices.NewDeliveryAreaService(ctx)
		_deliveryConfigsTrns = cmsTransformers.NewDeliveryConfigsTransformer(ctx)
	)
	// 验证参数
	if err != nil {
		panic(err)
	}
	// 调用服务
	// model := _deliveryAreaSvc.GetDeliveryConfigsById(params.RestaurantID, params.ParentID)
	model := _deliveryAreaSvc.GetDeliveryConfigsRestaurantById(params.RestaurantID, params.ParentID)
	if model.ID == 0 {
		model = _deliveryAreaSvc.GetDeliveryAreaConfigByID(params.ParentID)
	} else if model.FeeState == 2 || model.DeliveryFees == nil || len(model.DeliveryFees) == 0 {
		areaConfig := _deliveryAreaSvc.GetDeliveryAreaConfigByID(params.ParentID)
		model.DeliveryFees = areaConfig.DeliveryFees
	}

	// modelFee := _deliveryAreaSvc.GetDeliveryFeeById(params.RestaurantID, params.ParentID)
	// 格式化结果
	detailFormat := _deliveryConfigsTrns.FormatRestaurantDetail(model)
	detailFeeFormat := _deliveryConfigsTrns.FormatDeliveryFee(model.DeliveryFees)
	ctr.Success(ctx, map[string]interface{}{
		"detail": detailFormat,
		"fee":    detailFeeFormat,
	}, "success", 200)
}

// SaveDeliveryArea
//
//	@Description: 创建餐厅配送范围
//	@Author: Salam
//	@Time: 2025-07-01 11:01:10
//	@receiver ctr *RestaurantConfigsController
//	@param ctx *gin.Context
func (ctr *RestaurantConfigsController) SaveDeliveryArea(ctx *gin.Context) {
	var params deliveryRequests.CreateRestaurantAreaPolygonRequest
	if err := ctx.ShouldBind(&params); err != nil {
		panic(err)
	}

	// 权限验证
	admin := permissions.GetAdmin(ctx)
	params.CityId, params.AreaId = permissions.GetAdminAreaInfo(ctx, params.CityId, params.AreaId)

	// 数据校验部分
	if len(params.RestaurantIds) == 0 || len(params.RestaurantIds) > 20 {
		ctr.Fail(ctx, "餐厅ID数量不能超过20，不能留空", -1000)
		return
	}
	if params.Option == shipmentModels.DeliveryAreasOptionNormal && len(params.AreaPoints) == 0 {
		ctr.Fail(ctx, "地图数据不能为空", -1000)
		return
	}
	if params.Option == shipmentModels.DeliveryAreasOptionCircle && params.Radius == 0 {
		ctr.Fail(ctx, "地图距离数据不能为空", -1000)
		return
	}
	//if params.Option == shipmentModels.DeliveryAreasOptionArea {
	//	ctr.Fail(ctx, "应使用 /option 接口", -1000)
	//	return
	//}

	// 创建服务实例
	type RestaurantMapItem struct {
		RestaurantID int
		Lat          float64
		Lng          float64
	}
	var (
		_deliveryAreaSvc     = cmsServices.NewDeliveryAreaService(ctx)
		_deliveryConfigsTrns = cmsTransformers.NewDeliveryConfigsTransformer(ctx)
		_restaurantSvc       = services.NewRestaurantService(ctx)
		restaurantMapData    = make(map[int]RestaurantMapItem)
	)

	// 准备餐厅数据
	if params.Option == shipmentModels.DeliveryAreasOptionCircle {
		// - 获取餐厅地图/圆点数据
		restaurantList := _restaurantSvc.GetAreaValidRestaurantByIds(params.AreaId, params.RestaurantIds)
		for _, restaurant := range restaurantList {
			restaurantMapData[restaurant.ID] = RestaurantMapItem{
				RestaurantID: restaurant.ID,
				Lat:          restaurant.Lat,
				Lng:          restaurant.Lng,
			}
		}
	}

	// 服务调用
	areaList := make([]shipmentModels.DeliveryAreas, 0)
	for _, resId := range params.RestaurantIds {
		// 检查是否已存在
		existingArea, err := _deliveryAreaSvc.GetByRestaurantId(params.ParentID, resId)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			ctr.Fail(ctx, "failed", -1000)
			tools.Logger.Error(err.Error())
			return
		}

		// 准备多边形数据
		var _polygon = &shipmentModels.Polygon{}
		if params.Option == shipmentModels.DeliveryAreasOptionCircle {
			resMap, ok := restaurantMapData[resId]
			if !ok || resMap.Lat == 0 || resMap.Lng == 0 {
				ctr.Fail(ctx, "无法获取到餐厅地图位置数据", -1000)
				return
			}
			// - 圆形参数转换12边形
			_polygon = _polygon.CreateCirclePolygon(resMap.Lat, resMap.Lng, params.Radius, 12)
		} else if params.Option == shipmentModels.DeliveryAreasOptionNormal {
			// - 多点转换多边形
			orbPolygon := _polygon.ToOrbPolygon(params.AreaPoints)
			if orbPolygon != nil {
				_polygon = _polygon.FromOrbPolygon(orbPolygon)
			}
		}

		if (params.Option == shipmentModels.DeliveryAreasOptionCircle ||
			params.Option == shipmentModels.DeliveryAreasOptionNormal) && _polygon == nil {
			ctr.Fail(ctx, "范围数据有误", -1000)
			return
		}

		var area *shipmentModels.DeliveryAreas
		if existingArea != nil {
			// 更新现有记录
			area, err = _deliveryAreaSvc.Update(deliveryRequests.UpdateDeliveryConfigsRequest{
				ID: existingArea.ID,
				CreateDeliveryConfigsRequest: deliveryRequests.CreateDeliveryConfigsRequest{
					// 基础字段
					CityId:       params.CityId,
					AreaId:       params.AreaId,
					Type:         shipmentModels.DeliveryAreasTypeRestaurant, // Type 等于餐厅
					RestaurantId: resId,
					ParentID:     params.ParentID,
					// 配送范围
					AreaPoints:   params.AreaPoints,
					Option:       params.Option,
					Radius:       params.Radius,
					OrderAddTime: params.OrderAddTime,
				},
			}, _polygon, admin)
		} else {
			// 创建新记录
			area, err = _deliveryAreaSvc.Create(deliveryRequests.CreateDeliveryConfigsRequest{
				// 基础字段
				CityId:       params.CityId,
				AreaId:       params.AreaId,
				Type:         shipmentModels.DeliveryAreasTypeRestaurant, // Type 等于餐厅
				RestaurantId: resId,
				ParentID:     params.ParentID,
				// 配送范围
				AreaPoints:   params.AreaPoints,
				Option:       params.Option,
				Radius:       params.Radius,
				OrderAddTime: params.OrderAddTime,
			}, _polygon, admin)
		}

		if err != nil {
			ctr.Fail(ctx, "failed", -1000)
			tools.Logger.Error(err.Error())
			return
		}
		areaList = append(areaList, *area)
	}

	// 格式化
	formattedRes := _deliveryConfigsTrns.FormatDeliveryAreaList(areaList)

	// 返回
	ctr.Success(ctx, formattedRes, "msg", http.StatusOK)
}

// SaveDeliveryFee
//
//	@Description: 创建餐厅配送费阶梯
//	@Author: Salam
//	@Time: 2025-07-01 11:03:10
//	@receiver ctr *RestaurantConfigsController
//	@param ctx *gin.Context
func (ctr *RestaurantConfigsController) SaveDeliveryFee(ctx *gin.Context) {
	var params deliveryRequests.CreateRestaurantFeeStagesRequest
	if err := ctx.ShouldBind(&params); err != nil {
		panic(err)
	}
	if err := params.Validate(); err != nil {
		ctr.Fail(ctx, err.Error(), -1000)
		return
	}

	// 权限验证
	admin := permissions.GetAdmin(ctx)
	params.CityId, params.AreaId = permissions.GetAdminAreaInfo(ctx, params.CityId, params.AreaId)

	// 数据校验部分
	if len(params.RestaurantIds) == 0 || len(params.RestaurantIds) > 20 {
		ctr.Fail(ctx, "餐厅ID数量不能超过20，不能留空", -1000)
		return
	}
	if len(params.FeeConfigs) == 0 {
		ctr.Fail(ctx, "餐厅配送费阶梯数据不能为空", -1000)
		return
	}

	// 创建服务实例
	var (
		_deliveryFeeSvc      = cmsServices.NewDeliveryFeeService(ctx)
		_deliveryAreaSvc     = cmsServices.NewDeliveryAreaService(ctx)
		_deliveryConfigsTrns = cmsTransformers.NewDeliveryConfigsTransformer(ctx)
	)

	// 写入更新前的数据日志
	deliveryFeeList, err := _deliveryFeeSvc.GetDeliveryFeeListByRestaurantIds(params.ParentID, params.RestaurantIds)
	if err == nil && len(deliveryFeeList) > 0 {
		_deliveryFeeSvc.RecordDeliveryFeeListLog(deliveryFeeList)
	}

	// 获取 - 餐厅地图数据 （主要关联使用，option 字段会被使用，其他字段可以空着）
	areaList, err := _deliveryAreaSvc.GetDeliveryAreaByRestaurantIds(params.ParentID, params.RestaurantIds)
	if err != nil {
		ctr.Fail(ctx, "failed", -1000)
		return
	}
	areaMap := make(map[int]*shipmentModels.DeliveryAreas)
	for idx, area := range areaList {
		areaMap[*(area.RestaurantID)] = &areaList[idx]
	}
	// 创建 fee
	feeLists := make([][]shipmentModels.DeliveryFees, 0)
	for _, resId := range params.RestaurantIds {
		// 对于餐厅配送费，直接删除之前的然后配置新的
		if err := _deliveryFeeSvc.DeleteByRestaurantId(params.ParentID, resId); err != nil {
			ctr.Fail(ctx, "failed", -1000)
			tools.Logger.Error(err.Error())
			return
		}
		// 处理 - 地图数据
		area, ok := areaMap[resId]
		if ok {
			if area.FeeState != 1 {
				err = _deliveryAreaSvc.UpdateStatus(deliveryRequests.UpdateDeliveryConfigStatus{
					ID:               area.ID,
					AreaRunningState: 1,
					CityId:           params.CityId,
					AreaId:           params.AreaId,
					Type:             shipmentModels.DeliveryAreasTypeRestaurant,
					ParentId:         params.ParentID,
					RestaurantId:     resId,
				}, admin)
			}
		} else {
			area, err = _deliveryAreaSvc.Create(deliveryRequests.CreateDeliveryConfigsRequest{
				CityId:       params.CityId,
				AreaId:       params.AreaId,
				Type:         shipmentModels.DeliveryAreasTypeRestaurant,
				ParentID:     params.ParentID,
				RestaurantId: resId,
			}, nil, admin)
		}
		// - 处理更新/创建出现的错误
		if err != nil {
			ctr.Fail(ctx, "failed", -1000)
			tools.Logger.Error(err.Error())
			return
		}
		// 创建 - 配送费数据
		feeList, err := _deliveryFeeSvc.Create(deliveryRequests.CreateDeliveryConfigsRequest{
			// 基础字段
			CityId:       params.CityId,
			AreaId:       params.AreaId,
			Type:         2, // Type 等于餐厅
			RestaurantId: resId,
			ParentID:     params.ParentID,
			// 配送费列表
			Option:     shipmentModels.DeliveryFeesOptionRestaurant,
			FeeConfigs: params.FeeConfigs,
		}, admin, area.ID) // 创建餐厅配送费，不与配送范围记录相关联，只与区域配置相关联。
		if err != nil {
			ctr.Fail(ctx, "failed", -1000)
			tools.Logger.Error(err.Error())
			return
		}

		feeLists = append(feeLists, feeList)
	}

	// 格式化
	formattedRes := _deliveryConfigsTrns.FormatDeliveryFeeStagesList(feeLists)

	// 返回
	ctr.Success(ctx, formattedRes, "msg", http.StatusOK)
}

// UpdateOption
//
//	@Description: 更新餐厅配送范围-按区域更新
//	@Author: Salam
//	@Time: 2025-07-11 16:33:10
//	@receiver ctr *RestaurantConfigsController
//	@param ctx *gin.Context
func (ctr *RestaurantConfigsController) UpdateOption(ctx *gin.Context) {
	var params deliveryRequests.UpdateRestaurantDeliveryConfigs
	if err := ctx.ShouldBind(&params); err != nil {
		panic(err)
	}
	for _, resId := range params.RestaurantIds {
		if resId < 1 {
			ctr.Fail(ctx, "餐厅编号需要大于 0", -1000)
			return
		}
	}

	// 权限验证
	admin := permissions.GetAdmin(ctx)
	params.CityId, params.AreaId = permissions.GetAdminAreaInfo(ctx, params.CityId, params.AreaId)

	// 服务初始化
	var (
		_deliveryAreaSvc = cmsServices.NewDeliveryAreaService(ctx)
		_deliveryFeeSvc  = cmsServices.NewDeliveryFeeService(ctx)
		timeNow          = time.Now()
	)
	// - 配送范围
	if params.OptionType == 1 || params.OptionType == 2 {
		err := _deliveryAreaSvc.UpdateMapByRestaurantIds(map[string]any{
			"option":             shipmentModels.DeliveryAreasOptionArea,
			"area_running_state": 1,
			"updated_admin_id":   admin.ID,
			"updated_at":         timeNow,
		}, params)
		if err != nil {
			ctr.Fail(ctx, "failed", -1000)
			tools.Logger.Error(err.Error())
			return
		}
	}
	// - 配送费
	if params.OptionType == 1 || params.OptionType == 3 {
		err := _deliveryFeeSvc.UpdateMapByRestaurantIds(map[string]any{
			"option":           shipmentModels.DeliveryFeesOptionArea,
			"updated_admin_id": admin.ID,
			"updated_at":       timeNow,
		}, params)
		if err != nil {
			ctr.Fail(ctx, "failed", -1000)
			tools.Logger.Error(err.Error())
			return
		}
	}

	// 返回
	ctr.Success(ctx, nil, "msg", http.StatusOK)
}
