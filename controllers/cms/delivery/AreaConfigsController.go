package delivery

import (
	"mulazim-api/controllers"
	shipmentModels "mulazim-api/models/shipment"
	"mulazim-api/permissions"
	cmsRequests "mulazim-api/requests/cms/delivery"
	cmsServices "mulazim-api/services/cms"
	"mulazim-api/tools"
	cmsTransformers "mulazim-api/transformers/cms"

	"net/http"

	"github.com/gin-gonic/gin"
)

type AreaConfigsController struct {
	controllers.BaseController
}

// GetAreaList
//
//	@Description: 区域配送设置信息
//	@Author: Rixat
//	@Time: 2025-06-12 13:21:10
//	@param ctx *gin.Context
func (ctr *AreaConfigsController) GetAreaList(ctx *gin.Context) {
	var (
		params               cmsRequests.GetListDeliveryConfigsRequest
		err                  = ctx.ShouldBind(&params)
		_deliveryAreaSvc     = cmsServices.NewDeliveryAreaService(ctx)
		_deliveryConfigsTrns = cmsTransformers.NewDeliveryConfigsTransformer(ctx)
	)
	// 验证参数
	if err != nil {
		panic(err)
	}
	// 获取区域信息
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(ctx, params.CityID, params.AreaID)
	totalCount, items := _deliveryAreaSvc.GetDeliveryAreaList(params)
	// 格式化结果
	listFormat := _deliveryConfigsTrns.FormatAreaList(items)
	ctr.Success(ctx, map[string]interface{}{
		"total": totalCount,
		"items": listFormat,
	}, "success", 200)
}

// GetConfigList
//
//	@Description: 区域配送设置列表
//	@Author: Rixat
//	@Time: 2025-06-12 13:21:10
//	@param ctx *gin.Context
func (ctr *AreaConfigsController) GetConfigList(ctx *gin.Context) {
	var (
		params               cmsRequests.GetDeliveryConfigsRequest
		err                  = ctx.ShouldBind(&params)
		_deliveryConfigsSvc  = cmsServices.NewDeliveryAreaService(ctx)
		_deliveryConfigsTrns = cmsTransformers.NewDeliveryConfigsTransformer(ctx)
	)
	// 验证参数
	if err != nil {
		panic(err)
	}
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	// 获取信息
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(ctx, params.CityID, params.AreaID)
	totalCount, items := _deliveryConfigsSvc.GetDeliveryAreaConfigList(params)
	// 格式化结果
	listFormat := _deliveryConfigsTrns.FormatAreaConfigList(items)
	ctr.Success(ctx, map[string]interface{}{
		"total": totalCount,
		"items": listFormat,
	}, "success", 200)
}

// Create
//
//	@Description: 创建区域配送配置
//	@Author: Salam
//	@Time: 2025-06-12 13:21:10
//	@receiver ctr *AreaConfigsController
//	@param ctx *gin.Context
func (ctr *AreaConfigsController) Create(ctx *gin.Context) {
	var params cmsRequests.CreateDeliveryConfigsRequest
	if err := ctx.ShouldBind(&params); err != nil {
		panic(err)
	}

	// 数据校验通用部分
	if len(params.AreaPoints) < 3 {
		ctr.Fail(ctx, "地图数据不能少于三个坐标点", -1000)
		return
	}
	if len(params.FeeConfigs) == 0 {
		ctr.Fail(ctx, "配送费阶梯数据不能为空", -1000)
		return
	}
	if err := params.Validate(); err != nil {
		ctr.Fail(ctx, err.Error(), -1000)
		return
	}

	// 准备多边形数据
	var _polygon = &shipmentModels.Polygon{}
	// - 多点转换多边形
	orbPolygon := _polygon.ToOrbPolygon(params.AreaPoints)
	if orbPolygon != nil {
		_polygon = _polygon.FromOrbPolygon(orbPolygon)
	}
	if _polygon == nil {
		ctr.Fail(ctx, "范围数据有误", -1000)
		return
	}

	// 区域数据（避免误写入错误数据）
	params.Type = shipmentModels.DeliveryAreasTypeArea
	params.RestaurantId = 0
	params.ParentID = 0

	// 权限验证
	admin := permissions.GetAdmin(ctx)
	params.CityId, params.AreaId = permissions.GetAdminAreaInfo(ctx, params.CityId, params.AreaId)

	// 创建服务实例
	var (
		_deliveryAreaSvc     = cmsServices.NewDeliveryAreaService(ctx)
		_deliveryFeeSvc      = cmsServices.NewDeliveryFeeService(ctx)
		_deliveryConfigsTrns = cmsTransformers.NewDeliveryConfigsTransformer(ctx)
	)

	// 服务调用
	// - 创建范围
	area, err := _deliveryAreaSvc.Create(params, _polygon, admin)
	if err != nil {
		ctr.Fail(ctx, "failed", -1000)
		tools.Logger.Error(err.Error())
		return
	}
	// - 创建配送费
	feeList, err := _deliveryFeeSvc.Create(params, admin, area.ID)
	if err != nil {
		ctr.Fail(ctx, "failed", -1000)
		tools.Logger.Error(err.Error())
		return
	}
	// - 应用于所有餐厅
	if params.Status == 1 {
		err = _deliveryAreaSvc.UpdateStatus(cmsRequests.UpdateDeliveryConfigStatus{
			ID:               area.ID,
			AreaRunningState: 1,
			CityId:           params.CityId,
			AreaId:           params.AreaId,
			Type:             params.Type,
		}, admin)
		if err != nil {
			ctr.Fail(ctx, "failed", -1000)
			tools.Logger.Error(err.Error())
			return
		}
	}

	// 格式化
	area.DeliveryFees = feeList
	formattedRes := _deliveryConfigsTrns.FormatDeliveryConfigsDetail(*area)

	// 返回
	ctr.Success(ctx, formattedRes, "msg", http.StatusOK)
}

// Update
//
//	@Description: 更新区域配送配置
//	@Author: Salam
//	@Time: 2025-06-30 12:43:10
//	@receiver ctr *AreaConfigsController
//	@param ctx *gin.Context
func (ctr *AreaConfigsController) Update(ctx *gin.Context) {
	var params cmsRequests.UpdateDeliveryConfigsRequest
	if err := ctx.ShouldBind(&params); err != nil {
		panic(err)
	}

	// 数据校验通用部分
	if len(params.AreaPoints) < 3 {
		ctr.Fail(ctx, "地图数据不能少于三个坐标点", -1000)
		return
	}
	if len(params.FeeConfigs) == 0 {
		ctr.Fail(ctx, "配送费阶梯数据不能为空", -1000)
		return
	}
	if err := params.Validate(); err != nil {
		ctr.Fail(ctx, err.Error(), -1000)
		return
	}

	// 区域数据（避免误写入错误数据）
	params.Type = 1
	params.RestaurantId = 0
	params.ParentID = 0

	// 权限验证
	admin := permissions.GetAdmin(ctx)
	params.CityId, params.AreaId = permissions.GetAdminAreaInfo(ctx, params.CityId, params.AreaId)

	// 准备多边形数据
	var _polygon = &shipmentModels.Polygon{}
	// - 多点转换多边形
	orbPolygon := _polygon.ToOrbPolygon(params.AreaPoints)
	if orbPolygon != nil {
		_polygon = _polygon.FromOrbPolygon(orbPolygon)
	}
	if _polygon == nil {
		ctr.Fail(ctx, "范围数据有误", -1000)
		return
	}

	// 创建服务实例
	var (
		_deliveryAreaSvc     = cmsServices.NewDeliveryAreaService(ctx)
		_deliveryFeeSvc      = cmsServices.NewDeliveryFeeService(ctx)
		_deliveryConfigsTrns = cmsTransformers.NewDeliveryConfigsTransformer(ctx)
	)

	// 服务调用
	// - 保存范围
	area, err := _deliveryAreaSvc.Update(params, _polygon, admin)
	if err != nil {
		ctr.Fail(ctx, "failed", -1000)
		tools.Logger.Error(err.Error())
		return
	}
	// - 保存配送费
	params.DeliveryAreaId = area.ID
	feeList, err := _deliveryFeeSvc.Update(params, admin)
	if err != nil {
		ctr.Fail(ctx, "failed", -1000)
		tools.Logger.Error(err.Error())
		return
	}
	// - 应用于所有餐厅
	if params.Status == 1 {
		err = _deliveryAreaSvc.UpdateStatus(cmsRequests.UpdateDeliveryConfigStatus{
			ID:               area.ID,
			AreaRunningState: 1,
			CityId:           params.CityId,
			AreaId:           params.AreaId,
			Type:             params.Type,
		}, admin)
		if err != nil {
			ctr.Fail(ctx, "failed", -1000)
			tools.Logger.Error(err.Error())
			return
		}
	}

	// 格式化
	area.DeliveryFees = feeList
	formattedRes := _deliveryConfigsTrns.FormatDeliveryConfigsDetail(*area)

	// 返回
	ctr.Success(ctx, formattedRes, "msg", http.StatusOK)
}

// UpdateStatus
//
//	@Description: 更新区域配送配置
//	@Author: Salam
//	@Time: 2025-07-01 10:38:10
//	@receiver ctr *AreaConfigsController
//	@param ctx *gin.Context
func (ctr *AreaConfigsController) UpdateStatus(ctx *gin.Context) {
	var params cmsRequests.UpdateDeliveryConfigStatus
	if err := ctx.ShouldBind(&params); err != nil {
		panic(err)
	}

	// 权限验证
	admin := permissions.GetAdmin(ctx)
	params.CityId, params.AreaId = permissions.GetAdminAreaInfo(ctx, params.CityId, params.AreaId)

	// 创建服务实例
	var (
		_deliveryAreaSvc = cmsServices.NewDeliveryAreaService(ctx)
	)

	// 服务调用
	err := _deliveryAreaSvc.UpdateStatus(params, admin)
	if err != nil {
		ctr.Fail(ctx, "failed", -1000)
		tools.Logger.Error(err.Error())
		return
	}

	// 返回
	ctr.Success(ctx, nil, "msg", http.StatusOK)
}

// UpdateNotice
//
//	@Description: 更新区域公告信息
//	@Author: Salam
//	@Time: 2025-07-09 11:01:10
//	@receiver ctr *AreaConfigsController
//	@param ctx *gin.Context
func (ctr *AreaConfigsController) UpdateNotice(ctx *gin.Context) {
	var params cmsRequests.UpdateDeliveryConfigNotice
	if err := ctx.ShouldBind(&params); err != nil {
		panic(err)
	}

	// 权限验证
	admin := permissions.GetAdmin(ctx)
	params.CityId, params.AreaId = permissions.GetAdminAreaInfo(ctx, params.CityId, params.AreaId)

	// 创建服务实例
	var (
		_deliveryAreaSvc = cmsServices.NewDeliveryAreaService(ctx)
	)

	// 服务调用
	err := _deliveryAreaSvc.UpdateNotice(params, admin)
	if err != nil {
		ctr.Fail(ctx, "failed", -1000)
		tools.Logger.Error(err.Error())
		return
	}

	// 返回
	ctr.Success(ctx, nil, "msg", http.StatusOK)
}

// GetAreaConfigDetail
//
//	@Description: 获取配送配置详情
//	@Author: Salam
//	@Time: 2025-06-12 13:21:10
//	@receiver ctr *AreaConfigsController
//	@param ctx *gin.Context
func (ctr *AreaConfigsController) GetAreaConfigDetail(ctx *gin.Context) {
	type Params struct {
		Id int `uri:"id" binding:"required,min=1"`
	}
	var params Params
	if err := ctx.ShouldBindUri(&params); err != nil {
		panic(err)
	}

	// 调用服务
	_deliveryConfigsSvc := cmsServices.NewDeliveryAreaService(ctx)
	model := _deliveryConfigsSvc.GetDeliveryConfigsById(params.Id)
	// 格式化
	_deliveryConfigsTrns := cmsTransformers.NewDeliveryConfigsTransformer(ctx)
	formattedRes := _deliveryConfigsTrns.FormatDeliveryConfigsDetail(model)

	// 返回
	ctr.Success(ctx, formattedRes, "msg", http.StatusOK)
}

// GetDefaultPolygon
//
//	@Description: 获取区域默认服务区域范围
//	@Author: Rixat
//	@Time: 2025-07-09 11:01:10
//	@receiver ctr *AreaConfigsController
//	@param ctx *gin.Context
func (ctr *AreaConfigsController) GetDefaultPolygon(ctx *gin.Context) {
	type Params struct {
		CityID   int `form:"city_id" binding:""`
		AreaID   int `form:"area_id" binding:""`
		ParentID int `form:"parent_id" binding:""`
	}
	var (
		params               Params
		err                  = ctx.ShouldBind(&params)
		_deliveryAreaSvc     = cmsServices.NewDeliveryAreaService(ctx)
		_deliveryConfigsTrns = cmsTransformers.NewDeliveryConfigsTransformer(ctx)
	)
	// 验证参数
	if err != nil {
		panic(err)
	}
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(ctx, params.CityID, params.AreaID)

	// 调用服务
	if params.ParentID == 0 {
		runningDeliveryConfig := _deliveryAreaSvc.GetRunningDeliveryConfig(params.AreaID)
		params.ParentID = runningDeliveryConfig.ID
	}
	deliveryAreaConfig := _deliveryAreaSvc.GetDeliveryAreaConfigByID(params.ParentID)
	area := _deliveryAreaSvc.GetAreaDefaultPolygon(params.AreaID)
	if area.Polygon == nil {
		ctr.Fail(ctx, "not_area_server_config", -1000)
		return
	}
	// 格式化结果
	detailFormat := _deliveryConfigsTrns.FormatAreaDefaultPolygon(area)
	detailFormat.RunningDeliveryAreaConfig = _deliveryConfigsTrns.FormatRunningDeliveryConfig(deliveryAreaConfig)
	ctr.Success(ctx, detailFormat, "success", 200)
}
