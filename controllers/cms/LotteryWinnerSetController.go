package cms

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/permissions"
	lotteryRequest "mulazim-api/requests/cms/lottery"
	cmsService "mulazim-api/services/cms"
	cmsTransfomer "mulazim-api/transformers/cms"
)

type LotteryWinnerSetController struct {
	controllers.BaseController
}

// GetCurrentInfo 获取活动当前状态
//
//	@receiver cms
//	@param c
func (cms *LotteryWinnerSetController) GetCurrentInfo(c *gin.Context) {
	type Params struct {
		ActivityID int `form:"activity_id" binding:"required"` // 收入类型：类型 1：订单收入，2:奖励收入，3:打赏收入，4.特殊时间，5.特殊天气，6.好评，7.差评，8.投诉，9:迟到
	}
	var (
		params                  Params
		err                     = c.ShouldBind(&params)
		lotteryWinnerSetService = cmsService.NewLotteryWinnerSetService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	_, _, info := lotteryWinnerSetService.GetCurrentInfo(params.ActivityID)
	cms.Success(c, info, "success", 200)
}

// PostSet 设置抽奖
//
//	@receiver cms
//	@param context
func (cms *LotteryWinnerSetController) PostSet(c *gin.Context) {

	var (
		params                  lotteryRequest.LotteryWinnerSetRequest
		err                     = c.ShouldBindJSON(&params)
		lotteryWinnerSetService = cmsService.NewLotteryWinnerSetService(c)
		language, _             = c.Get("lang")
		langUtil                = lang.LangUtil{
			Lang: language.(string),
		}
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	success, msg := params.Validate(langUtil)
	if success == false {
		cms.Fail(c, msg, -1000)

		return
	}

	lotteryWinnerSetService.PostSet(params, admin.ID)
	cms.Success(c, nil, "success", 200)
}

// GetList
//
// @Description: 抽奖列表获取
// @Author: Rixat
// @Time: 2023-11-07 02:32:03
// @receiver
// @param c *gin.Context
func (cms *LotteryWinnerSetController) GetList(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" binding:"required"`  // 当前页数
		Limit       int    `form:"limit" binding:"required"` // 每页显示数量
		Kw          string `form:"kw" binding:""`            // 关键字(姓名或手机号)
		SortColumns string `form:"sort_columns"`
	}
	var (
		params                 Params
		err                    = c.ShouldBind(&params)
		lotteryActivityService = cmsService.NewLotteryWinnerSetService(c)
		cmsTransfomer          = cmsTransfomer.NewLotteryWinnerSetTransfomer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	totalCount, list := lotteryActivityService.List(params.Page, params.Limit, params.Kw, params.SortColumns)
	result := cmsTransfomer.FormatList(totalCount, list)
	cms.Success(c, result, "success", 200)
}
