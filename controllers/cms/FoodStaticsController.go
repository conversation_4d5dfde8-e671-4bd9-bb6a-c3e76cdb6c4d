package cms

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"
	"net/http"
	"time"
)

type FoodStaticsController struct {
	controllers.BaseController
}

//
// GetShipperMapList
//  @Description: 配送员列表包括经纬度，从Redis获取
//  @receiver c
//  @param context
//
func (m FoodStaticsController) GetFoodStatics(c *gin.Context) {
	type Params struct {
		CityID      int    `form:"city_id"`            // 地区ID
		AreaID      int    `form:"area_id"`            // 区域ID
		StoreID	 	int    `form:"store_id"`           // 店铺ID
		SortColumns string `form:"sort_columns,oneof=total_count price" binding:"required,oneof=total_count price"`
		SortType string `form:"sort_type,oneof=asc desc" binding:"required,oneof=asc desc"`
		StartDate   string `form:"start_date" binding:"required,datetime=2006-01-02"`
		EndDate     string `form:"end_date" binding:"required,datetime=2006-01-02"`
		Page 	  int    `form:"page" binding:"required,min=1"`
		Limit 	  int    `form:"limit" binding:"required,min=10,max=50"`
		Lang 	  string `json:"lang"`
	}
	var (
		params        Params
		err                = c.ShouldBind(&params)
		foodStaticsService     = cmsService.NewFoodStaticsService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if carbon.Parse(params.StartDate).AddMonths(3).Lt(carbon.Parse(params.EndDate)) {
		m.Fail(c, "time_range_error_3_month", 400)
		return
	}
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.Lang = c.GetString("lang")
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	cacheKeyStr,_ := json.Marshal(params)
	hashStr := md5.Sum(cacheKeyStr)
	cacheKey := fmt.Sprintf("food_statics_%x", hashStr)
	cachedString := tools.Remember(c,cacheKey, 2*time.Hour, func() interface{} {
		result,totalCount  := foodStaticsService.GetFoodStaticsList(
			c,params.CityID,
			params.AreaID ,
			params.StoreID ,
			params.SortColumns,
			params.SortType,
			params.StartDate,
			params.EndDate,
			params.Page,
			params.Limit)
		if result == nil || len(result) == 0{
			return map[string]interface{}{
				"items": make([]interface{}, 0),
				"total_count": 0,
			}
		}
		return map[string]interface{}{
			"items": result,
			"total_count": totalCount,
		}
	})
	cachedObject := make(map[string]interface{})
	json.Unmarshal([]byte(cachedString), &cachedObject)
	m.Success(c, cachedObject, "msg", 200)
}
//
// GetRestaurantStatics
//  @Description: 餐厅销售统计
//  @receiver m
//  @param c
//
func (m FoodStaticsController) GetRestaurantStatics(c *gin.Context) {
	type Params struct {
		CityID      int    `form:"city_id"`            // 地区ID
		AreaID      int    `form:"area_id"`            // 区域ID
		StoreID	 	int    `form:"store_id"`           // 店铺ID
		SortColumns string `form:"sort_columns,oneof=per_order_price per_order_profit total_cash_pay_price total_dealer_profit total_food_price total_lunch_box_price total_online_pay_price total_order_count total_order_price total_shipment_price" binding:"required,oneof=per_order_price per_order_profit total_cash_pay_price total_dealer_profit total_food_price total_lunch_box_price total_online_pay_price total_order_count total_order_price total_shipment_price total_self_take_deliver_price total_mp_deliver_price  total_order_price total_shipment_price"`
		SortType string `form:"sort_type,oneof=asc desc" binding:"required,oneof=asc desc"`
		StartDate   string `form:"start_date" binding:"required,datetime=2006-01-02"`
		EndDate     string `form:"end_date" binding:"required,datetime=2006-01-02"`
		Page 	  int    `form:"page" binding:"required,min=1"`
		Limit 	  int    `form:"limit" binding:"required,min=10,max=50"`
		Lang 	  string `json:"lang"`
	}
	var (
		params        Params
		err                = c.ShouldBind(&params)
		foodStaticsService     = cmsService.NewFoodStaticsService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if carbon.Parse(params.StartDate).AddMonths(3).Lt(carbon.Parse(params.EndDate)) {
		m.Fail(c, "time_range_error_3_month", 400)
		return
	}
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.Lang = c.GetString("lang")
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	cacheKeyStr,_ := json.Marshal(params)
	hashStr := md5.Sum(cacheKeyStr)
	cacheKey := fmt.Sprintf("restaurant_statics1_%x", hashStr)
	cachedString := tools.Remember(c,cacheKey, 2*time.Hour, func() interface{} {
		result,totalCount  := foodStaticsService.GetRestaurantStaticsList(
			c,params.CityID,
			params.AreaID ,
			params.StoreID ,
			params.SortColumns,
			params.SortType,
			params.StartDate,
			params.EndDate,
			params.Page,
			params.Limit,false)
		if result == nil || len(result) == 0{
			return map[string]interface{}{
				"items": make([]interface{}, 0),
				"total_count": 0,
			}
		}
		return map[string]interface{}{
			"items": result,
			"total_count": totalCount,
		}
	})
	cachedObject := make(map[string]interface{})
	json.Unmarshal([]byte(cachedString), &cachedObject)
	m.Success(c, cachedObject, "msg", 200)
}
//
// GetRestaurantStaticsExport
//  @Description: 餐厅统计导出Excel
//  @receiver m
//  @param c
//
func (m FoodStaticsController) GetRestaurantStaticsExport(c *gin.Context) {
	type Params struct {
		CityID      int    `form:"city_id"`            // 地区ID
		AreaID      int    `form:"area_id"`            // 区域ID
		StoreID	 	int    `form:"store_id"`           // 店铺ID
		SortColumns string `form:"sort_columns,oneof=per_order_price per_order_profit total_cash_pay_price total_dealer_profit total_food_price total_lunch_box_price total_online_pay_price total_order_count total_order_price total_shipment_price" binding:"required,oneof=per_order_price per_order_profit total_cash_pay_price total_dealer_profit total_food_price total_lunch_box_price total_online_pay_price total_order_count total_order_price total_shipment_price total_self_take_deliver_price total_mp_deliver_price  total_order_price total_shipment_price"`
		SortType string `form:"sort_type,oneof=asc desc" binding:"required,oneof=asc desc"`
		StartDate   string `form:"start_date" binding:"required,datetime=2006-01-02"`
		EndDate     string `form:"end_date" binding:"required,datetime=2006-01-02"`
		Page 	  int    `form:"page" binding:"omitempty,min=1"`
		Limit 	  int    `form:"limit" binding:"omitempty,min=10,max=50"`
		Lang 	  string `json:"lang"`
	}
	var (
		params        Params
		err                = c.ShouldBind(&params)
		foodStaticsService     = cmsService.NewFoodStaticsService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	if carbon.Parse(params.StartDate).AddMonths(3).Lt(carbon.Parse(params.EndDate)) {
		m.Fail(c, "time_range_error_3_month", 400)
		return
	}
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.Lang = c.GetString("lang")
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	result,_  := foodStaticsService.GetRestaurantStaticsList(
		c,params.CityID,
		params.AreaID ,
		params.StoreID ,
		params.SortColumns,
		params.SortType,
		params.StartDate,
		params.EndDate,
		params.Page,
		params.Limit,
		true)


	//开始excel导出
	fileName := carbon.Now(configs.AsiaShanghai).Format("YmdHis") + tools.RandStr(4)
	cols := []string{
		"区县名称",
		"时段",
		"ئاشخانا نامى",
		"ئاشخانا ئادرېسى",
		"ئومۇمىي سوممىسى",
		"يەتكۈزۈلگەن زاكاز سوممىسى",
		"ئېلىپ كەتكەن زاكاز سوممىسى",
		"توردا تۆلەنگىنى",
		"ۋاكالەتچى تۆلىگىنى",
		"تىجارەت سوممىسى",
		"يەتكۈزۈش ھەققى",
		"قاچا ھەققى",
		"ۋاكالەتچى پايدىسى",
		"زاكاز سانى",
		"يەككە زاكاز سوممىسى",
		"يەككە زاكاز پايدىسى",
	}
	excelItems := make([]map[string]interface{}, 0)
	for _, item := range result {
		excelItems = append(excelItems, map[string]interface{}{
			"区县名称":   item.CityName+"-"+item.AreaName,
			"时段":   params.StartDate + "-" + params.EndDate,
			"ئاشخانا نامى":   item.RestaurantName,
			"ئاشخانا ئادرېسى":   item.RestaurantAddress,
			"ئومۇمىي سوممىسى":tools.FormatFen2TYuanPrice(int(100*(item.TotalOrderPrice))),
			"يەتكۈزۈلگەن زاكاز سوممىسى":tools.FormatFen2TYuanPrice(int(100*(item.TotalMpDeliverPrice))),
			"ئېلىپ كەتكەن زاكاز سوممىسى":tools.FormatFen2TYuanPrice(int(100*(item.TotalSelfTakeDeliverPrice))),
			"توردا تۆلەنگىنى" : tools.FormatFen2TYuanPrice(int(100*(item.TotalOnlinePayPrice))),
			"ۋاكالەتچى تۆلىگىنى" : tools.FormatFen2TYuanPrice(int(100*(item.TotalCashPayPrice))),
			"تىجارەت سوممىسى" : tools.FormatFen2TYuanPrice(int(100*(item.TotalFoodPrice))),
			"يەتكۈزۈش ھەققى": tools.FormatFen2TYuanPrice(int(100*(item.TotalShipmentPrice))),
			"قاچا ھەققى": tools.FormatFen2TYuanPrice(int(100*(item.TotalLunchBoxPrice))),
			"ۋاكالەتچى پايدىسى" :tools.FormatFen2TYuanPrice(int(100*(item.TotalDealerProfit))),
			"زاكاز سانى" :item.TotalOrderCount,
			"يەككە زاكاز سوممىسى":tools.FormatFen2TYuanPrice(int(100*(item.PerOrderPrice))),
			"يەككە زاكاز پايدىسى":tools.FormatFen2TYuanPrice(int(100*(item.PerOrderProfit))),
		})
	}
	excelFile := tools.ExcelExport("餐厅营业统计", cols, excelItems, fileName)
	downLoadFileName := "餐厅营业统计-" + params.StartDate + "-" + params.EndDate+ "-" + carbon.Now(configs.AsiaShanghai).Format("Ymd") + ".xlsx"
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	// 写入 Excel 文件到响应体
	err1 := excelFile.Write(c.Writer)
	if err1 != nil {
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}
