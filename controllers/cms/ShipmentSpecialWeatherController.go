package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	shipmentResource "mulazim-api/resources/cms/shipment"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type ShipmentSpecialWeatherController struct {
	controllers.BaseController
}

// PostCreate
//
// @Description: 创建特殊天气
// @Author: Rixat
// @Time: 2023-11-06 09:55:55
// @receiver
// @param c *gin.Context
func (cms ShipmentSpecialWeatherController) PostCreate(c *gin.Context) {
	var (
		params         shipmentResource.SpecialWeather
		err            = c.ShouldBind(&params)
		weatherService = cmsService.NewShipmentSpecialWeather(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 创建
	admin := permissions.GetAdmin(c)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	err = weatherService.Create(admin, params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// PostUpdate
//
// @Description: 更新内容
// @Author: Rixat
// @Time: 2023-11-06 09:56:01
// @receiver
// @param c *gin.Context
func (cms ShipmentSpecialWeatherController) PostUpdate(c *gin.Context) {
	var (
		params         shipmentResource.SpecialWeather
		err            = c.ShouldBind(&params)
		weatherService = cmsService.NewShipmentSpecialWeather(c)
	)
	// 验证参数
	ID := tools.ToInt(c.PostForm("id"))
	if ID == 0 {
		cms.Fail(c, "not_id", -1000)
		return
	}
	if err != nil {
		panic(err)
		return
	}
	// 更新
	err = weatherService.Update(ID, params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// PostChangeState
//
// @Description: 修改状态
// @Author: Rixat
// @Time: 2023-11-06 09:56:05
// @receiver
// @param c *gin.Context
func (cms ShipmentSpecialWeatherController) PostChangeState(c *gin.Context) {
	type Params struct {
		ID    int `form:"id" binding:"required"`
		State int `form:"state" binding:"required"`
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		weatherService = cmsService.NewShipmentSpecialWeather(c)
	)
	if err != nil {
		panic(err)
		return
	}
	// 更新状态
	admin := permissions.GetAdmin(c)
	err = weatherService.ChangeState(admin, params.ID, params.State)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// GetDetail
//
// @Description: 特殊天气详情
// @Author: Rixat
// @Time: 2023-11-06 09:56:08
// @receiver
// @param c *gin.Context
func (cms ShipmentSpecialWeatherController) GetDetail(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"`
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		weatherService = cmsService.NewShipmentSpecialWeather(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取详情内容
	result, err := weatherService.Detail(params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Success(c, result, "msg", 200)
}

// GetList
//
// @Description: 获取特殊天气列表
// @Author: Rixat
// @Time: 2023-11-06 09:56:13
// @receiver
// @param c *gin.Context
func (cms ShipmentSpecialWeatherController) GetList(c *gin.Context) {
	type Params struct {
		Page        int    `form:"page" binding:"required"`  // 当前页数
		Limit       int    `form:"limit" binding:"required"` // 每页显示数量
		CityID      int    `form:"city_id" binding:""`       // 地区ID
		AreaID      int    `form:"area_id" binding:""`       // 区域ID
		Kw          string `form:"kw" binding:""`            // 关键字(名称)
		State       int    `form:"state" binding:""`
		SortColumns string `form:"sort_columns"`
	}

	var (
		params         Params
		err            = c.ShouldBind(&params)
		weatherService = cmsService.NewShipmentSpecialWeather(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	// 获取排序参数
	sort := tools.SortColumnParam(params.SortColumns)

	// 获取列表数据
	result := weatherService.List(params.Page, params.Limit, params.CityID, params.AreaID, params.Kw, params.State, sort)
	cms.Success(c, result, "msg", 200)
}

// / PostDelete
//
// @Description: 删除特殊天气
// @Author: Rixat
// @Time: 2023-11-06 09:56:16
// @receiver
// @param c *gin.Context
func (cms ShipmentSpecialWeatherController) PostDelete(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"`
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		weatherService = cmsService.NewShipmentSpecialWeather(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 删除
	err = weatherService.Delete(params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}
