package cms

import (
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	"mulazim-api/tools"

	// "mulazim-api/lang"
	cmsService "mulazim-api/services/cms"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	// "github.com/golang-module/carbon/v2"
)

type ShipperUserChangeController struct {
	controllers.BaseController
}

//
// GetTodayOrderStatics
//  @receiver c
//  @param context
//	<AUTHOR> 2024-05-06 13:00:00
//
func (r ShipperUserChangeController) GetList(c *gin.Context) {
	var (
		
		
		admin             = permissions.GetAdmin(c)
		shipperUserChangeService     = cmsService.NewShipperUserChangeService(c)
		 
	)
	cityId :=tools.ToInt(c.DefaultQuery("city_id","0"))
	areaId :=tools.ToInt(c.<PERSON>fa<PERSON>("area_id","0")) 
	startDate :=c.Default<PERSON>("start_date",carbon.Now(configs.AsiaShanghai).AddMonths(-6).Format("Y-m-d")) //默认查询6个月内的数据

	if admin.IsDealer() || admin.IsDealerSub() {
		cityId = admin.AdminCityID
		areaId = admin.AdminAreaID
	}
	page :=tools.ToInt(c.DefaultQuery("page","1"))
	limit :=tools.ToInt(c.DefaultQuery("limit","10"))
	res := shipperUserChangeService.GetShipperChangeList(cityId,areaId,startDate,page,limit)
	
	r.Success(c, res, "msg", 200)
}

//创建 个人二维码替换关系 
func (r ShipperUserChangeController) Create(c *gin.Context) {
	type Params struct {
		OldShipperId int `form:"old_shipper_id" json:"old_shipper_id" binding:"required"`
		NewShipperId int `form:"new_shipper_id" json:"new_shipper_id" binding:"required"`
		Reason       string `form:"reason" json:"reason" `
		
	}
	var (
		
		params        Params
		err                = c.ShouldBind(&params)
		// admin             = permissions.GetAdmin(c)
		shipperUserChangeService     = cmsService.NewShipperUserChangeService(c)
		 
	)
	if err != nil {
		panic(err)
		return
	}
	
	ok,res := shipperUserChangeService.CreateShipperChange(params.OldShipperId,params.NewShipperId,params.Reason)
	if !ok {
		r.Fail(c,res,-1000)
		return
	}
	
	r.Success(c, res, "msg", 200)
}


// 配送员个人二维码替换关系删除
func (r ShipperUserChangeController) Delete(c *gin.Context) {
	type Params struct {
		Id int `form:"id" json:"id" binding:"required"`
	}
	var (
		
		params        Params
		err                = c.ShouldBind(&params)
		// admin             = permissions.GetAdmin(c)
		shipperUserChangeService     = cmsService.NewShipperUserChangeService(c)
		 
	)
	if err != nil {
		panic(err)
		return
	}
	
	ok,res := shipperUserChangeService.DeleteShipperChange(params.Id)
	if !ok {
		r.Fail(c,res,-1000)
		return
	}
	
	r.Success(c, res, "msg", 200)
}