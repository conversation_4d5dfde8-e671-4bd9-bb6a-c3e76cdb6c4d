package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"

	"mulazim-api/services"
	"mulazim-api/tools"

	"net/http"

	"github.com/gin-gonic/gin"
	"mulazim-api/permissions"
)

type CmsOrderRankController struct {
	controllers.BaseController
}

//订单等级统计
func (s CmsOrderRankController) GetOrderRankStat(c *gin.Context) {
	var (
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	cityId :=tools.ToInt(c.Default<PERSON>uery("city_id","0"))
	areaId :=tools.ToInt(c.DefaultQuery("area_id","0"))

	cityId, areaId = permissions.GetAdminAreaInfo(c, cityId, areaId)

	kw :=c.<PERSON>("kw","")
	startDate :=c.<PERSON>("start_date","")
	endDate :=c.<PERSON>("end_date","")
	
	chatService := services.NewOrderRankService(c)
	list, err := chatService.GetOrderRankStat(c,langUtil, int64(admin.ID), admin.Type,cityId,areaId,kw,startDate,endDate)
	if err != nil {
		s.Fail(c,err.Error(),-1000)
		return
	}
	s.Success(c, list, "获取成功", http.StatusOK)

}
