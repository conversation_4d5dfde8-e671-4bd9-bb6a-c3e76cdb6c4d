package cms

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/permissions"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"
	cmsTransformer "mulazim-api/transformers/cms"
)

type FoodsGroupController struct {
	controllers.BaseController
}

// RestaurantList
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 12:16:56
//	@Description: 餐厅列表
//	@receiver f
//	@param c
func (f FoodsGroupController) RestaurantList(c *gin.Context) {
	type Params struct {
		CityId      int    `form:"city_id" binding:""`
		AreaId      int    `form:"area_id" binding:""`
		Search      string `form:"search" binding:""`
		CanSelfTake string `form:"can_self_take" binding:""`
		State       string `form:"state" binding:""`
		SortColumns string `form:"sort_columns" binding:""`
	}
	pagination := tools.GetPagination(c)
	var (
		params      Params
		service     = cmsService.NewFoodsGroupService(c)
		transformer = cmsTransformer.NewFoodsGroupTransformer(c)
	)
	if err := c.ShouldBindQuery(&params); err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}
	params.CityId, params.AreaId = permissions.GetAdminAreaInfo(c, params.CityId, params.AreaId)
	restaurantList, total := service.RestaurantList(pagination, params.CityId, params.AreaId, params.Search, params.SortColumns, params.CanSelfTake, params.State)
	format := transformer.RestaurantListFormat(restaurantList)
	f.Success(c, gin.H{"items": format, "total": total}, "success", 200)

}

// Create
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 12:17:08
//	@Description: 新增美食分组
//	@receiver f
//	@param c
func (f FoodsGroupController) Create(c *gin.Context) {
	type Params struct {
		RestaurantId int    `json:"restaurant_id" binding:"required"`
		NameUg       string `json:"name_ug" binding:"required"`
		NameZh       string `json:"name_zh" binding:"required"`
		Weight       int    `json:"weight" binding:""`
		State        string `json:"state" binding:""`
	}
	var (
		params  Params
		service = cmsService.NewFoodsGroupService(c)
		err     = c.ShouldBindJSON(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	// 创建美食分组的时候判断是不是该代理所在地区下面的餐厅
	cityId, areaId := permissions.GetAdminAreaInfo(c, 0, 0)
	ok := permissions.HasAccessToRestaurantInCityAndArea(c,params.RestaurantId,cityId, areaId)
	if !ok {
		f.Fail(c,"forbidden",-1000)
		return
	}
	if err := service.Create(c, params.RestaurantId, params.NameUg, params.NameZh, params.Weight, params.State); err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	} else {
		f.Ok(c)
		return
	}

}

// Edit
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 12:17:16
//	@Description: 编辑美食分组
//	@receiver f
//	@param c
func (f FoodsGroupController) Edit(c *gin.Context) {
	type Params struct {
		Id           int    `json:"id" binding:"required"`
		NameUg       string `json:"name_ug" binding:"required"`
		NameZh       string `json:"name_zh" binding:"required"`
		Weight       int    `json:"weight" binding:"gte=0"`
		State        string `json:"state" binding:"oneof=0 1"`
		RestaurantId int    `json:"restaurant_id" binding:"required"`
	}
	var (
		params  Params
		service = cmsService.NewFoodsGroupService(c)
		err     = c.ShouldBindJSON(&params)
	)
	if err != nil {
		panic(err)
		return
	}

	if err := service.Edit(c, params.Id, params.NameUg, params.NameZh, params.Weight, params.State,params.RestaurantId); err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	} else {
		f.Ok(c)
		return
	}
}

// Delete
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 12:17:48
//	@Description: 删除美食分组
//	@receiver f
//	@param c
func (f FoodsGroupController) Delete(c *gin.Context) {
	type Params struct {
		Id       int `json:"id" binding:"required"`
		IsDelete int `json:"is_delete" binding:"oneof=0 1"`
	}
	var (
		params  Params
		service = cmsService.NewFoodsGroupService(c)
		err     = c.ShouldBindJSON(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	err = service.Delete(params.Id, params.IsDelete)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	} else {
		f.Ok(c)
	}
}

// List
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 12:17:57
//	@Description: 美食分组列表
//	@receiver f
//	@param c
func (f FoodsGroupController) List(c *gin.Context) {
	type Params struct {
		RestaurantId int `form:"restaurant_id" binding:"required"`
	}
	var (
		params      Params
		service     = cmsService.NewFoodsGroupService(c)
		transformer = cmsTransformer.NewFoodsGroupTransformer(c)
		err         = c.ShouldBindQuery(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	// 根据餐厅ID查询分组列表的时候先查一下是不是这个代理所在的区域里的餐厅
	cityId, areaId := permissions.GetAdminAreaInfo(c, 0, 0)
	ok := permissions.HasAccessToRestaurantInCityAndArea(c,params.RestaurantId,cityId, areaId)
	if !ok {
		f.Fail(c,"not_found",-1000)
		return
	}
	foodsGroupList := service.List(params.RestaurantId)
	items := transformer.ListFormat(foodsGroupList)
	f.Success(c, items, "success", 200)
}

// FoodsList
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 12:18:10
//	@Description: 根据条件获取美食列表
//	@receiver f
//	@param c
func (f FoodsGroupController) FoodsList(c *gin.Context) {
	type Params struct {
		RestaurantId int    `form:"restaurant_id" binding:"required"`
		FoodsGroupId int    `form:"foods_group_id" binding:""`
		State   string `form:"state" binding:""`
		Search       string `form:"search" binding:""`
		FoodTypes []int `form:"food_types[]" binding:""` // food_types[] 是为了适配前端
	}
	var (
		params      Params
		service     = cmsService.NewFoodsGroupService(c)
		transformer = cmsTransformer.NewFoodsGroupTransformer(c)
		err = c.ShouldBind(&params)
		pagination  = tools.GetPagination(c)
	)
	if err != nil {
		panic(err)
		return
	}
	// 根据餐厅ID查询分组里面的美食列表的时候先查一下是不是这个代理所在的区域里的餐厅
	cityId, areaId := permissions.GetAdminAreaInfo(c, 0, 0)
	ok := permissions.HasAccessToRestaurantInCityAndArea(c,params.RestaurantId,cityId, areaId)
	if !ok {
		f.Fail(c,"not_found",-1000)
		return
	}
	foodsList, counts := service.FoodsList(params.RestaurantId, params.FoodsGroupId, pagination, params.State,
		params.Search, params.FoodTypes)
	foodsItems := transformer.FoodsListFormat(foodsList)
	f.Success(c, gin.H{
		"foods_state_count": counts,
		"foods":             foodsItems,
	}, "success", 200)
}

// EditFoodsGroupWeights
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:46:35
//	@Description: 修改美食分组顺序
//	@receiver f
//	@param c
func (f FoodsGroupController) EditFoodsGroupWeights(c *gin.Context) {
	type Params struct {
		RestaurantId int                 `json:"restaurant_id" binding:"required"`
		Weights      []models.FoodsGroup `json:"weights" binding:"required"`
	}
	var (
		params  Params
		service = cmsService.NewFoodsGroupService(c)
		err     = c.ShouldBindJSON(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	if err := service.EditFoodsGroupWeights(params.RestaurantId, params.Weights); err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	} else {
		f.Ok(c)
		return
	}

}

// EditFoodsGroups
//
//	@Author: YaKupJan
//	@Date: 2024-09-20 11:23:46
//	@Description: 批量修改美食的分组
//	@receiver f
//	@param c
func (f FoodsGroupController) EditFoodsGroups(c *gin.Context) {
	type Params struct {
		FoodsGroupId int    `json:"foods_group_id" binding:"required"`
		FoodsIds     []int  `json:"foods_ids" binding:"required"`
	}
	var (
		params  Params
		service = cmsService.NewFoodsGroupService(c)
		err     = c.ShouldBindJSON(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	if err := service.EditFoodsGroups(params.FoodsGroupId, params.FoodsIds); err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	} else {
		f.Ok(c)
		return
	}
}

// EditFoodsState
//
//	@Author: YaKupJan
//	@Date: 2024-09-20 11:23:46
//	@Description: 批量修改美食的分组
//	@receiver f
//	@param c
func (f FoodsGroupController) EditFoodsState(c *gin.Context) {
	type Params struct {
		State    string `json:"state" binding:"required"`
		FoodsIds []int  `json:"foods_ids" binding:"required"`
	}
	var (
		params  Params
		service = cmsService.NewFoodsGroupService(c)
		err     = c.ShouldBindJSON(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	if err := service.EditFoodsState( params.FoodsIds,params.State); err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	} else {
		f.Ok(c)
		return
	}
}

// EditFoodsInGroupWeights
//
//	@Author: YaKupJan
//	@Date: 2024-10-14 22:05:34
//	@Description: 修改美食分组中的美食顺序
//	@receiver f
//	@param c
func (f FoodsGroupController) EditFoodsInGroupWeights(c *gin.Context) {
	type Weight struct {
		FoodsId       int `json:"foods_id" binding:"required"`
		WeightInGroup int `json:"weight_in_group" binding:"gte=0"`
	}
	type Params struct {
		FoodsGroupId int      `json:"foods_group_id" binding:"required"`
		Weights      []Weight `json:"weights" binding:"required"`
	}
	var (
		params  Params
		service = cmsService.NewFoodsGroupService(c)
		err     = c.ShouldBindJSON(&params)
	)
	if err != nil {
		panic(err)
		return
	}

	param := make([]map[string]interface{}, len(params.Weights))

	// 将每个 Weight 转换为 interface{}
	for i, w := range params.Weights {
		param[i] = tools.Struct2Map(w)
	}
	if err := service.EditFoodsInGroupWeights(params.FoodsGroupId,param); err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	} else {
		f.Ok(c)
		return
	}
}

// ReviewList
//
//  @Author: YaKupJan
//  @Date: 2024-10-21 16:51:46
//  @Description: 审核美食分组审核列表
//  @receiver f
//  @param c
func (f FoodsGroupController) ReviewList(c *gin.Context) {
	type Params struct {
		CityId       int    `form:"city_id" binding:""`
		AreaId       int    `form:"area_id" binding:""`
		RestaurantId int    `form:"restaurant_id" binding:""`
		ReviewState  int    `form:"review_state" binding:""`
		Search       string `form:"search" binding:""`
		SortColumns  string `form:"sort_columns" binding:""`
	}
	// 定义
	var (
		params  Params
		// service
		service = cmsService.NewFoodsGroupService(c)
		// transformer
		transformer = cmsTransformer.NewFoodsGroupTransformer(c)
		err     = c.ShouldBindQuery(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取分页信息
	pagination := tools.GetPagination(c)
	// 获取列表信息
	reviewList, total := service.ReviewList(params.CityId, params.AreaId, params.RestaurantId, params.ReviewState, params.Search, pagination,params.SortColumns)
	// 格式化
	formattedList := transformer.ReviewListFormat(reviewList)
	// 返回格式化数据
	f.Success(c, gin.H{
		"items": formattedList,
		"total": total,
	}, "success", 200)
}

// Review
//
//  @Author: YaKupJan
//  @Date: 2024-10-21 18:32:37
//  @Description: 审核美食分组
//  @receiver f
//  @param c
func (f FoodsGroupController) Review(c *gin.Context) {
	type Params struct {
		Id           int    `form:"id" binding:"required"`
		ReviewState  int    `form:"review_state" binding:"required,oneof=2 3"`
		RefuseReason string `form:"refuse_reason" binding:""`
		NameZh       string `form:"name_zh" binding:"required"`
		NameUg       string `form:"name_ug" binding:"required"`
	}
	var (
		params  Params
		service = cmsService.NewFoodsGroupService(c)
		err     = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	if err := service.Review(params.Id, params.ReviewState, params.RefuseReason,admin,params.NameUg,params.NameZh); err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	} else {
		f.Ok(c)
		return
	}
}

// UpdateGroupName
//
//  @Author: YaKupJan
//  @Date: 2024-10-21 20:46:15
//  @Description: 修改美食分组名称
//  @receiver f
//  @param c
func (f FoodsGroupController) UpdateGroupName(c *gin.Context) {
	type Params struct {
		Id     int    `form:"id" binding:"required"`
		NameZh string `form:"name_zh" binding:"required"`
		NameUg string `form:"name_ug" binding:"required"`
	}
	var (
		params  Params
		service = cmsService.NewFoodsGroupService(c)
		err     = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	if err := service.UpdateGroupName(params.Id, params.NameZh, params.NameUg,admin); err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	} else {
		f.Ok(c)
		return
	}
}


// GetRecommendGroup
//
//  @Author: YaKupJan
//  @Date: 2024-10-22 20:57:01
//  @Description: 推荐分组
//  @receiver f
//  @param c
func (f FoodsGroupController) GetRecommendGroup(c *gin.Context) {
	type Params struct {
		Kw     string    `form:"kw" binding:""`
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = cmsService.NewFoodsGroupService(c)
		transformer = cmsTransformer.NewFoodsGroupTransformer(c)
	)
	if err != nil {
		panic(err)
		return
	}
	// 推荐分组
	foodsGroupList := service.GetRecommendGroup(params.Kw)
	// 格式化
	items := transformer.ListRecommendFormat(foodsGroupList)
	f.Success(c, items, "success", 200)
}
