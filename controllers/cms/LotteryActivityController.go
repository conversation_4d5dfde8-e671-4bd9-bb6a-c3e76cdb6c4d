﻿package cms

import (
	"encoding/csv"
	"fmt"
	"github.com/golang-module/carbon/v2"
	"io"
	"mime"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/permissions"
	lotteryRequest "mulazim-api/requests/cms/lottery"
	resLottery "mulazim-api/resources/cms/lottery"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"
	cmsTransformer "mulazim-api/transformers/cms"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

type LotteryActivityController struct {
	controllers.BaseController
}

// GetList
//
// @Description: 抽奖列表获取
// @Author: Rixat
// @Time: 2023-11-07 02:32:03
// @receiver
// @param c *gin.Context
func (cms *LotteryActivityController) GetList(c *gin.Context) {
	type Params struct {
		State *int   `form:"state"`                    // 收入类型：类型 1：订单收入，2:奖励收入，3:打赏收入，4.特殊时间，5.特殊天气，6.好评，7.差评，8.投诉，9:迟到
		Page  int    `form:"page" binding:"required"`  // 当前页数
		Limit int    `form:"limit" binding:"required"` // 每页显示数量
		Kw    string `form:"kw" binding:""`            // 关键字(姓名或手机号)
		SortColumns string `form:"sort_columns"`
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		lotteryActivityService = cmsService.NewLotteryActivityService(c)
		cmsTransformer = cmsTransformer.NewLotteryActivityTransformer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	totalCount, list := lotteryActivityService.List(params.State, params.Page, params.Limit, params.Kw, params.SortColumns)
	result := cmsTransformer.FormatList(totalCount, list)
	cms.Success(c, result, "success", 200)
}

// GetList
//
// @Description: 抽奖列表获取
// @Author: Rixat
// @Time: 2023-11-07 02:32:03
// @receiver
// @param c *gin.Context
func (cms *LotteryActivityController) GetDetail(c *gin.Context) {
	type Params struct {
		ID int `form:"id"` // 收入类型：类型 1：订单收入，2:奖励收入，3:打赏收入，4.特殊时间，5.特殊天气，6.好评，7.差评，8.投诉，9:迟到
	}
	var (
		params         Params
		err            = c.ShouldBind(&params)
		lotteryActivityService = cmsService.NewLotteryActivityService(c)
		cmsTransformer = cmsTransformer.NewLotteryActivityTransformer(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	res := lotteryActivityService.Detail(params.ID)
	result := cmsTransformer.FormatDetail(res)
	cms.Success(c, result, "success", 200)
}

// GetList
//
// @Description: 抽奖列表获取
// @Author: Rixat
// @Time: 2023-11-07 02:32:03
// @receiver
// @param c *gin.Context
func (cms *LotteryActivityController) PostChangeState(c *gin.Context) {
	type Params struct {
		ID    int    `form:"id"`
		State string `form:"state" binding:"required,oneof=0 1"`
	}
	var (
		params Params
		err    = c.ShouldBind(&params)
		lotteryActivityService = cmsService.NewLotteryActivityService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	err = lotteryActivityService.ChangeState(params.ID, tools.ToInt(params.State))
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// GetList
//
// @Description: 抽奖列表获取
// @Author: Rixat
// @Time: 2023-11-07 02:32:03
// @receiver
// @param c *gin.Context
func (cms *LotteryActivityController) PostDelete(c *gin.Context) {
	type Params struct {
		ID int `form:"id"`
	}
	var (
		params Params
		err    = c.ShouldBind(&params)
		lotteryActivityService = cmsService.NewLotteryActivityService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	err = lotteryActivityService.Delete(params.ID)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// GetList
//
// @Description: 抽奖列表获取
// @Author: Rixat
// @Time: 2023-11-07 02:32:03
// @receiver
// @param c *gin.Context
func (cms *LotteryActivityController) PostCreate(c *gin.Context) {
	var (
		params lotteryRequest.LotteryActivityCreateRequest
		err    = c.ShouldBindJSON(&params)
		lotteryActivityService = cmsService.NewLotteryActivityService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	err = lotteryActivityService.Create(admin, params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// GetList
//
// @Description: 抽奖列表获取
// @Author: Rixat
// @Time: 2023-11-07 02:32:03
// @receiver
// @param c *gin.Context
func (cms *LotteryActivityController) PostEdit(c *gin.Context) {
	var (
		params lotteryRequest.LotteryActivityEditRequest
		err    = c.ShouldBindJSON(&params)
		lotteryActivityService = cmsService.NewLotteryActivityService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	err = lotteryActivityService.Edit(admin, params)
	if err != nil {
		cms.Fail(c, err.Error(), -1000)
		return
	}
	cms.Ok(c)
}

// GetComments
//
// @Description: 获取活动评价列表
// @Author: Salam
// @Time: 2025-02-27 11:17:03
// @receiver
// @param c *gin.Context
func (ctr *LotteryActivityController) GetComments(ctx *gin.Context) {
	var (
		params             lotteryRequest.LotteryCommentRequestParams
		err                = ctx.ShouldBind(&params)
		lotteryCommentSvc  = cmsService.NewLotteryCommentService(ctx)
		lotteryTransformer = cmsTransformer.NewLotteryTransformer(ctx)
		pagination         = tools.GetPagination(ctx)
	)

	if err != nil {
		panic(err)
		return
	}

	admin := permissions.GetAdmin(ctx)
	if !admin.IsAdmin() {
		// TODO: 是否需要改成如果是代理？
		// 如果不是管理员，只能获取代理所在地区相关的数据
		params.CityID, params.AreaID = permissions.GetAdminAreaInfo(ctx, 0, 0)
	}

	totalCount, list, err := lotteryCommentSvc.GetCommentsWithUserInfo(
		pagination, params.LotteryActivityID, params.CityID, params.AreaID, params.Type, params.Query,
		params.BeginTime, params.EndTime)
	if err != nil {
		tools.Logger.Error(err.Error())
		ctr.Fail(ctx, "fail", -1000)
		return
	}
	items := lotteryTransformer.FormatCommentsWithUserInfo(list)

	res := resLottery.LotteryCommentResponse{
		Items: items,
		Total: totalCount,
	}

	ctr.Success(ctx, res, "msg", http.StatusOK)
}

// PostExcelToPrize
//
// @Description: 根据 xlsx 文件内容，获取活动中奖配置数据
// @Author: Salam
// @Time: 2025-02-27 16:50:08
// @receiver
// @param c *gin.Context
func (ctr *LotteryActivityController) PostExcelToPrize(ctx *gin.Context) {
	// 1. 获取上传的文件
	file, header, err := ctx.Request.FormFile("file")
	if err != nil {
		ctr.Fail(ctx, "excel_file_empty", -1000) // 请上传有效的文件
		return
	}
	defer file.Close()

	// 2. 检查文件的实际 MIME 类型
	mimeType := mime.TypeByExtension(header.Filename)
	if mimeType == "" {
		buffer := make([]byte, 512)
		_, err = file.Read(buffer)
		if err != nil && err != io.EOF {
			ctr.Fail(ctx, "excel_file_empty", -1000) // 读取文件头部失败
			return
		}
		file.Seek(0, 0) // 重置文件指针
		mimeType = http.DetectContentType(buffer)
	}

	// 3. 如果 MIME 类型不是 CSV，返回错误
	if len(header.Filename) <= 5 || header.Filename[len(header.Filename)-5:] != ".xlsx" {
		ctr.Fail(ctx, "only_support_xlsx", -1000) // 只支持 xlsx 文件类型
		return
	}

	// 4. 直接从文件读取内容并解析 CSV
	reader := csv.NewReader(file)
	reader.Comma = '\t'
	rows, err := reader.ReadAll()
	if err != nil {
		tools.Logger.Error(err.Error())
		ctr.Fail(ctx, "excel_file_empty", -1000) // excel_file_empty
		return
	}

	// 5. 处理 CSV 数据
	mapPrizeIdx := make(map[uint][]int)
	contentStarted := false    // 内容区域已开始读取
	for _, row := range rows { // 遍历行
		if strings.Contains(row[0], "奖品ID") {
			contentStarted = true
			continue
		}

		if len(strings.Trim(row[0], " ")) == 0 || len(strings.Trim(row[1], " ")) == 0 ||
			contentStarted == false { // 内容无效
			continue
		}

		_prizeId := tools.ToInt(row[0])
		prizeId := uint(_prizeId)
		orderIdx := tools.ToInt(row[1])
		mapPrizeIdx[prizeId] = append(mapPrizeIdx[prizeId], orderIdx)
	}

	// 6. 获取 prize list (by prizeID list)
	var prizeIDs []uint
	for _prizeID := range mapPrizeIdx { // 唯一 prizeID
		prizeIDs = append(prizeIDs, _prizeID)
	}
	if len(prizeIDs) == 0 {
		ctr.Fail(ctx, "lottery_activity_prize_list_required", -1000) // 无任何奖品 ID 被检测到
		return
	}

	_lotterySvc := cmsService.NewLotteryPrizeService(ctx)
	var prizeList []models.LotteryPrize
	if len(prizeIDs) == 1 {
		if prizeItem, err := _lotterySvc.Detail(tools.ToInt(prizeIDs[0])); err != nil {
			tools.Logger.Error(err.Error())
			ctr.Fail(ctx, "lottery_activity_prize_get_valid_data_failed", -1000) // 获取奖品详情失败
			return
		} else {
			if prizeItem.State != models.LotteryPrizeStateOn || !prizeItem.DeletedAt.Valid {
				ctr.Fail(ctx, "lottery_activity_prize_get_valid_data_failed", -1000) // 有无效奖品 ID
				return
			}
			prizeList = append(prizeList, prizeItem)
		}
	} else {
		prizeList, err = _lotterySvc.GetValidListByIDs(prizeIDs)
		if err != nil {
			tools.Logger.Error(err.Error())
			ctr.Fail(ctx, "lottery_activity_prize_get_valid_data_failed", -1000) // 获取奖品列表失败
			return
		}
		if len(prizeList) < len(prizeIDs) {
			ctr.Fail(ctx, "lottery_activity_prize_get_valid_data_failed", -1000) // 有无效奖品 ID
			return
		}
	}
	_lotteryTrn := cmsTransformer.NewLotteryTransformer(ctx)
	prizeListTransformed := _lotteryTrn.FormatPrizeList(prizeList)

	prizeMap := make(map[uint]resLottery.LotteryPrizeResponse)
	for _, prize := range prizeListTransformed {
		prizeMap[prize.ID] = prize
	}

	// 7. 组装成需要的数据结构
	var result []resLottery.LotteryActivityExcelPrizeResposne
	for prizeId, orderIdxList := range mapPrizeIdx {
		result = append(result, resLottery.LotteryActivityExcelPrizeResposne{
			PrizeID:        prizeId,
			PrizeData:      prizeMap[prizeId],
			OrderIndexList: orderIdxList,
		})
	}

	// 8. 返回处理结果
	ctr.Success(ctx, result, "msg", http.StatusOK)
}

// UserPrizeExcel
//
// @Description: 获取活动中奖用户信息，并导出成 Excel
// @Author: Salam
// @Time: 2025-02-28 10:08:08
// @receiver
// @param c *gin.Context
func (ctr *LotteryActivityController) UserPrizeExcel(ctx *gin.Context) {
	var (
		params lotteryRequest.GetUserPrizeExcelParams
		err    = ctx.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
		return
	}

	// 1. 检查权限
	cityID, areaID := 0, 0
	// TODO: 优化成只取一次 admin 数据
	admin := permissions.GetAdmin(ctx)
	if !admin.IsAdmin() {
		// 如果不是管理员，只能获取代理所在地区相关的数据
		cityID, areaID = permissions.GetAdminAreaInfo(ctx, 0, 0)
	}

	// 2. 获取数据
	_lotteryChanceSvc := cmsService.NewLotteryChanceService(ctx)
	list, err := _lotteryChanceSvc.GetValidListByActivityID(params.LotteryActivityID, cityID, areaID)
	if err != nil {
		tools.Logger.Error(err.Error())
		ctr.Fail(ctx, "get_prize_user_list_failed", -1000) // 获取中奖用户列表失败
		return
	}

	if list == nil || len(list) == 0 {
		ctr.Fail(ctx, "empty_prize_user_list", -1000) // 获取中奖用户列表失败
		return
	}

	_lotChanceTrsf := cmsTransformer.NewLotteryChanceTransformer(ctx)
	result := _lotChanceTrsf.FormatList(list)

	// 3. 准备 Excel 文件名和列标题
	fileName := carbon.Now(configs.AsiaShanghai).Format("YmdHis") + tools.RandStr(4)
	cols := []string{
		"订单序号",
		"奖品",
		"奖品金额",
		"订单号",
		"收货地址",
		"地区",
		"用户名称",
		"手机号",
		"下单时间",
	}
	excelData := []map[string]any{}

	// - 构建 Excel 数据
	for _, item := range result {
		excelData = append(excelData, map[string]any{
			"订单序号": *item.DrawIndex,
			"奖品":     item.LotteryPrizeName,
			"奖品金额": item.LotteryPrizePrice,
			"订单号":   item.OrderID,
			"收货地址": item.BuildingAddress,
			"地区":     item.AreaName,
			"用户名称": item.UserName,
			"手机号":   item.UserMobile,
			"下单时间": item.OrderTime,
		})
	}

	// - 添加活动 ID 和下载人 ID 到注释行
	footer := []map[string]any{
		{"订单序号": "", "奖品": ""},                             // 空行
		{"订单序号": "活动ID", "奖品": "下载人ID"},               // 注释列名
		{"订单序号": params.LotteryActivityID, "奖品": admin.ID}, // 注释值
	}
	excelData = append(excelData, footer...)
	excelFile := tools.ExcelExport("获奖数据", cols, excelData, fileName)

	// 4. 设置响应头，告诉浏览器这是一个 Excel 文件
	ctx.Writer.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	// - 写入 Excel 文件到响应体
	if err := excelFile.Write(ctx.Writer); err != nil {
		ctx.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}
