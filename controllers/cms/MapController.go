package cms

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/permissions"
	cmsService "mulazim-api/services/cms"
	cmsTransformer "mulazim-api/transformers/cms"
)

type MapController struct {
	controllers.BaseController
}

//
// GetShipperMapList
//  @Description: 配送员列表包括经纬度，从Redis获取
//  @receiver c
//  @param context
//
func (m MapController) GetShipperMapList(c *gin.Context) {
	type Params struct {
		CityID      int    `form:"city_id"`            // 地区ID
		AreaID      int    `form:"area_id"`            // 区域ID
		ShipperID      int    `form:"shipper_id" binding:""`            // 区域ID
		OrderID	int `form:"order_id" binding:""` // 订单ID
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		mapService = cmsService.NewMapService(c)
		mapTransformer = cmsTransformer.NewMapShipperTransformer(c)
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 更新区域信息(如果代理操作，区信息默认填充代理区域信息)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	if params.CityID == 0 || params.AreaID == 0 {
		m.Fail(c, langUtil.T("area_id_is_required"), 400)
		return
	}

	list, orderToday := mapService.GetShipperMapList(c,params.CityID, params.AreaID, params.ShipperID,params.OrderID)
	newOrderCount := mapService.GetNewReceivedOrderListCount(c,params.CityID, params.AreaID)
	result := mapTransformer.TransformList(list,orderToday)
	resultSorted := mapTransformer.SortResult(result,orderToday)
	rtnMap := make(map[string]interface{})
	rtnMap["shipper_list"] = resultSorted
	rtnMap["new_order_count"] = newOrderCount
	m.Success(c, rtnMap, "success", 200)
}
//
// GetNewReceivedOrderList
//  @Description: 获取新接单列表
//  @receiver m
//  @param c
//
func (m MapController) GetNewReceivedOrderList(c *gin.Context) {
	type Params struct {
		CityID      int    `form:"city_id"`            // 地区ID
		AreaID      int    `form:"area_id"`            // 区域ID
	}
	var (
		params        Params
		err           = c.ShouldBind(&params)
		mapService = cmsService.NewMapService(c)
		mapTransformer = cmsTransformer.NewMapShipperTransformer(c)
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	if params.CityID == 0 || params.AreaID == 0 {
		m.Fail(c, langUtil.T("area_id_is_required"), 400)
		return
	}
	list := mapService.GetNewReceivedOrderList(c,params.CityID, params.AreaID)
	result := mapTransformer.TransformNewReceivedOrderList(list)
	m.Success(c, result, "success", 200)
}
