package cms

import (
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/permissions"
	"mulazim-api/tools"
	"net/http"
	"path/filepath"

	// "mulazim-api/lang"
	cmsService "mulazim-api/services/cms"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	// "github.com/golang-module/carbon/v2"
)

type ShipperInsuranceController struct {
	controllers.BaseController
}

//
// RealName 实名认证列表
//  @receiver c
//  @param context
//	<AUTHOR> 2024-05-06 13:00:00
//
func (r ShipperInsuranceController) RealName(c *gin.Context) {
	var (
		
		
		admin             = permissions.GetAdmin(c)
		shipperInsuranceService     = cmsService.NewShipperInsuranceService(c)
		// selfSignTransformer = transformers.NewSelfSignTransformer(c)
		 
	)
	cityId :=tools.ToInt(c.DefaultQuery("city_id","0"))
	areaId :=tools.ToInt(c.DefaultQuery("area_id","0")) 
	startDate :=c.DefaultQuery("start_date","")
	endDate :=c.DefaultQuery("end_date","")
	searchField :=c.DefaultQuery("key","")
	state :=tools.ToInt(c.DefaultQuery("state","-1")) 

	if admin.IsDealer() || admin.IsDealerSub() {
		cityId = admin.AdminCityID
		areaId = admin.AdminAreaID
	}
	page :=tools.ToInt(c.DefaultQuery("page","1"))
	limit :=tools.ToInt(c.DefaultQuery("limit","10"))
	sort :=c.DefaultQuery("sort","")
	totalCount,infoList,headers := shipperInsuranceService.GetShipperRealNameList(cityId,areaId,startDate,endDate,searchField,state,page,limit,sort)
	
	r.Success(c, map[string]interface{}{
		"headers":headers,
		"total":totalCount,
		"items":infoList,
	}, "msg", 200)
}
//实名认证详情 
func (r ShipperInsuranceController) RealNameDetail(c *gin.Context) {
	type Params struct {
		ID        int    `form:"id" binding:"required"`
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		shipperService     = cmsService.NewShipperInsuranceService(c)
	)

	if err != nil {
		panic(err)
		return
	}

	detailInfo := shipperService.GetShipperRealNameDetail(params.ID)
	
	r.Success(c, detailInfo, "msg", 200)
}
//实名认证 关键词推荐
func (r ShipperInsuranceController)  GetVerifyText(c *gin.Context) {
	type Params struct {
		Kw        string    `form:"key_word" binding:"required"`
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		shipperService     = cmsService.NewShipperInsuranceService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	// 获取推荐审核建议，最多10条
	res := shipperService.GetVerifyRecommendText(params.Kw)
	r.Success(c, res, "msg", 200)
}
//实名认证 审核 拒绝
func (r ShipperInsuranceController)  RefuseReview(c *gin.Context) {
	type Params struct {
		MerInfoID        int    `form:"mer_info_id" binding:"required"`
		VerifyContent        string    `form:"verify_content" binding:"required"`
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		shipperService     = cmsService.NewShipperInsuranceService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	res := shipperService.RefuseRealName(admin.ID,params.MerInfoID,params.VerifyContent)
	r.Success(c, res, "msg", 200)
}


// 实名认证 审核 通过
func (r ShipperInsuranceController)  ApprovedReview(c *gin.Context) {
	type Params struct {
		MerInfoID        int    `form:"mer_info_id" binding:"required"`
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		shipperService     = cmsService.NewShipperInsuranceService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	res := shipperService.ApproveRealName(admin.ID,params.MerInfoID)
	if res !=nil{
		r.Fail(c, res.Error(), -1000)
		return
	}
	r.Success(c, res, "msg", 200)
}

//保险列表 
func (r ShipperInsuranceController) List(c *gin.Context) {
	var (
		
		
		admin             = permissions.GetAdmin(c)
		shipperInsuranceService     = cmsService.NewShipperInsuranceService(c)
		// selfSignTransformer = transformers.NewSelfSignTransformer(c)
		 
	)
	cityId :=tools.ToInt(c.DefaultQuery("city_id","0"))
	areaId :=tools.ToInt(c.DefaultQuery("area_id","0")) 
	startDate :=c.DefaultQuery("start_date",carbon.Now(configs.AsiaShanghai).Format("Y-m-d")) //默认查询6个月内的数据
	endDate :=c.DefaultQuery("end_date",carbon.Now(configs.AsiaShanghai).Format("Y-m-d")) //默认查询6个月内的数据
	searchField :=c.DefaultQuery("key","")
	st :=c.DefaultQuery("state","-1")
	state :=tools.ToInt(st) 
	sort :=c.DefaultQuery("sort","")
	
	if admin.IsDealer() || admin.IsDealerSub() {
		cityId = admin.AdminCityID
		areaId = admin.AdminAreaID
	}

	page :=tools.ToInt(c.DefaultQuery("page","1"))
	limit :=tools.ToInt(c.DefaultQuery("limit","10"))
	totalCount,infoList,headers := shipperInsuranceService.GetList(admin,cityId,areaId,startDate,endDate,searchField,state,page,limit,sort,false,0)
	
	r.Success(c, map[string]interface{}{
		"headers":headers,
		"total":totalCount,
		"items":infoList,
	}, "msg", 200)
}



// 导出excel
func (r ShipperInsuranceController) GetExcelExport(c *gin.Context) {
	var (
		
		
		admin             = permissions.GetAdmin(c)
		shipperInsuranceService     = cmsService.NewShipperInsuranceService(c)
		// selfSignTransformer = transformers.NewSelfSignTransformer(c)
		 
	)
	cityId :=tools.ToInt(c.DefaultQuery("city_id","0"))
	areaId :=tools.ToInt(c.DefaultQuery("area_id","0")) 
	startDate :=c.DefaultQuery("start_date",carbon.Now(configs.AsiaShanghai).Format("Y-m-d")) //默认查询6个月内的数据
	endDate :=c.DefaultQuery("end_date",carbon.Now(configs.AsiaShanghai).Format("Y-m-d")) //默认查询6个月内的数据
	searchField :=c.DefaultQuery("key","")
	st :=c.DefaultQuery("state","-1")
	state :=tools.ToInt(st) 
	sort :=c.DefaultQuery("sort","")
	// if admin.IsDealer() || admin.IsDealerSub() {
	// 	cityId = admin.AdminCityID
	// 	areaId = admin.AdminAreaID
	// }
	page :=tools.ToInt(c.DefaultQuery("page","1"))
	limit :=tools.ToInt(c.DefaultQuery("limit","10"))
	_,excelData,_ := shipperInsuranceService.GetList(admin,cityId,areaId,startDate,endDate,searchField,state,page,limit,sort,true,0)
	
	//开始excel导出
	fileName := carbon.Now(configs.AsiaShanghai).Format("YmdHis") + tools.RandStr(4)
	cols := []string{
		// "ID",
		// "城市",
		// "区域",
		// "姓名",
		// "身份证号",
		// "手机号",
		// "日期",
		// "有效时间开始",
		// "有效时间结束",
		// "银行卡号",
		"序号(*)",
		"姓名(*)",
		"证件类型(*#)",
		"证件号(*)",
		"证件有效期（起）",
		"证件有效期（止）",
		"性别(*#)",
		"生日(*)",
		"保障层级(*)",
		"职业代码(*#)",
		"手机",
		"省(#)",
		"市(#)",
		"县(#)",
		"地址",
		"办公电话区号",
		"办公电话号码",
		"办公电话分机",
		"家庭电话区号",
		"家庭电话号码",
		"加保申请日期(*)",
		"国家地区(#)",
	}
	excelFile := tools.ExcelExport("配送员保险", cols, excelData, fileName)

	downLoadFileName := "配送员保险-" + carbon.Now(configs.AsiaShanghai).Format("Ymd") + ".xlsx"
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	err1 := excelFile.Write(c.Writer)
	if err1 != nil {
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}
//保险审核 
func (r ShipperInsuranceController) PostConfirm(c *gin.Context) {

	type Params struct {
		Id         string  `form:"id" json:"id"`
		Confirm         int  `form:"confirm" json:"confirm"`
		ReasonUg         string  `form:"reason_ug" json:"reason_ug"`
		ReasonZh         string  `form:"reason_zh" json:"reason_zh"`
		BlackList         int  `form:"black_list" json:"black_list"`
	}
	var (
		admin             = permissions.GetAdmin(c)
		params        Params
		err           = c.ShouldBind(&params)
		shipperInsuranceService     = cmsService.NewShipperInsuranceService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	err = shipperInsuranceService.ProcessInsurance(admin.ID,params.Id,params.Confirm,params.ReasonUg,params.ReasonZh,params.BlackList)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	r.Success(c, nil, "msg", 200)
}


//保险 默认数据创建
func (r ShipperInsuranceController) CreateShipperInsurance(c *gin.Context) {
	var (
	
		shipperInsuranceService     = cmsService.NewShipperInsuranceService(c)
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	check :=tools.ToInt(c.DefaultQuery("check","0"))
	if check == 1 {//忘记点击保险购买的人数 
		count := shipperInsuranceService.GetInsuranceForgot()
		data :=map[string]interface{}{
			"count":count,
		}	
		r.Success(c, data, "msg", 200)
		return
	}else if check == 2 { //需要确认保险的人数
		count := shipperInsuranceService.GetInsuranceNeedBuy()
		data :=map[string]interface{}{
			"count":count,
		}	
		r.Success(c, data, "msg", 200)
		return
	}else{//忘记购买的人员手动登记
		now :=carbon.Now(configs.AsiaShanghai)
		insuranceStopTime :=shipperInsuranceService.BaseService.GetAppConfig("insurance_stop_time")
		originalStopTime :=configs.MyApp.InsuranceStopTime
		newTime :=originalStopTime
		if len(insuranceStopTime) > 0 {
			newTime =insuranceStopTime
		}
		stopTime :=carbon.Parse(now.Format("Y-m-d")+" "+newTime,configs.AsiaShanghai)
		if now.Lt(stopTime) { //保险停止时间
			tools.Logger.Info("保险停止时间还没到",stopTime)
			stTime :=stopTime.Format("Y-m-d H:i:s")
			if langUtil.Lang == "ug" {
				stTime =stopTime.Format("d-m-Y H:i:s")
			}
			r.Fail(c,fmt.Sprintf(langUtil.T("try_after_some_time"),stTime),-1000)
			return 
		}
		go func ()  {
			shipperInsuranceService.CreateInsurance(true)
		}()
	} 
	r.Success(c, nil, "msg", 200)
}



//保险记录
func (r ShipperInsuranceController) Log(c *gin.Context) {
	var (
		
		
		admin             = permissions.GetAdmin(c)
		shipperInsuranceService     = cmsService.NewShipperInsuranceService(c)
		// selfSignTransformer = transformers.NewSelfSignTransformer(c)
		 
	)
	startDate :=c.DefaultQuery("start_date",carbon.Now(configs.AsiaShanghai).Format("Y-m-d")) //默认查询6个月内的数据
	endDate :=c.DefaultQuery("end_date",carbon.Now(configs.AsiaShanghai).Format("Y-m-d")) //默认查询6个月内的数据

	page :=tools.ToInt(c.DefaultQuery("page","1"))
	shipperId :=tools.ToInt(c.DefaultQuery("shipper_id","0"))
	limit :=tools.ToInt(c.DefaultQuery("limit","10"))
	sort :=c.DefaultQuery("sort","")
	totalCount,infoList,headers := shipperInsuranceService.GetList(admin,0,0,startDate,endDate,"",-1,page,limit,sort,false,shipperId)
	
	r.Success(c, map[string]interface{}{
		"headers":headers,
		"total":totalCount,
		"items":infoList,
	}, "msg", 200)
}


//保险记录 excel 导出 
func (r ShipperInsuranceController) LogExport(c *gin.Context) {
	var (
		
		
		admin             = permissions.GetAdmin(c)
		shipperInsuranceService     = cmsService.NewShipperInsuranceService(c)
		// selfSignTransformer = transformers.NewSelfSignTransformer(c)
		 
	)
	startDate :=c.DefaultQuery("start_date",carbon.Now(configs.AsiaShanghai).Format("Y-m-d")) //默认查询6个月内的数据
	endDate :=c.DefaultQuery("end_date",carbon.Now(configs.AsiaShanghai).Format("Y-m-d")) //默认查询6个月内的数据

	page :=tools.ToInt(c.DefaultQuery("page","1"))
	shipperId :=tools.ToInt(c.DefaultQuery("shipper_id","0"))
	limit :=tools.ToInt(c.DefaultQuery("limit","10"))
	_,excelData,_ := shipperInsuranceService.GetList(admin,0,0,startDate,endDate,"",-1,page,limit,"",true,shipperId)
	//开始excel导出
	fileName := carbon.Now(configs.AsiaShanghai).Format("YmdHis") + tools.RandStr(4)
	cols := []string{
		// "ID",
		"城市",
		"区域",
		"姓名",
		"身份证号",
		"手机号",
		"日期",
		"银行卡号",
		"保险名称",
		"保险金额",
		"保险状态",
		"上传时间",
		"确认时间",
		"金额",
	}
	excelFile := tools.ExcelExport("配送员保险", cols, excelData, fileName)

	downLoadFileName := "配送员保险-" + carbon.Now(configs.AsiaShanghai).Format("Ymd") + ".xlsx"
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	err1 := excelFile.Write(c.Writer)
	if err1 != nil {
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}

//管理员替配送员开启购买保险状态
func (r ShipperInsuranceController) PostInsuranceEnableManually(c *gin.Context) {

	type Params struct {
		Id         string  `form:"id" json:"id"`
	}
	var (
		admin             = permissions.GetAdmin(c)
		params        Params
		err           = c.ShouldBind(&params)
		shipperInsuranceService     = cmsService.NewShipperInsuranceService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	err = shipperInsuranceService.InsuranceEnableManually(admin,params.Id)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	r.Success(c, nil, "msg", 200)
}

//强制创建保险记录 只处理 必须走保险的区域
func (r ShipperInsuranceController) CreateShipperInsuranceForce(c *gin.Context) {

	var (
	
		shipperInsuranceService     = cmsService.NewShipperInsuranceService(c)
		
	)
	err :=shipperInsuranceService.CreateInsurance(false)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	r.Success(c, nil, "msg", 200)
}

//上传保险已确认的数据
func (r ShipperInsuranceController) PostUploadConfirmFile(c *gin.Context) {

	var (
	
		shipperInsuranceService     = cmsService.NewShipperInsuranceService(c)
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)		
	)
	now :=carbon.Now(configs.AsiaShanghai)
	insuranceStopTime :=shipperInsuranceService.BaseService.GetAppConfig("insurance_stop_time")
	originalStopTime :=configs.MyApp.InsuranceStopTime
	newTime :=originalStopTime
	if len(insuranceStopTime) > 0 {
		newTime =insuranceStopTime
	}
	stopTime :=carbon.Parse(now.Format("Y-m-d")+" "+newTime,configs.AsiaShanghai)
	if now.Lt(stopTime) { //保险停止时间
		tools.Logger.Info("保险停止时间还没到",stopTime)
		stTime :=stopTime.Format("Y-m-d H:i:s")
		if langUtil.Lang == "ug" {
			stTime =stopTime.Format("d-m-Y H:i:s")
		}
		r.Fail(c,fmt.Sprintf(langUtil.T("try_after_some_time"),stTime),-1000)
		return 
	}
	admin := permissions.GetAdmin(c)
	var allowTypes []string = []string{".xlsx"}
	typeName := "insurance-file"
	file, err := c.FormFile("file")
	if err != nil {
		r.Fail(c, "FileNotFound", http.StatusBadRequest)
		return
	}

	ext := filepath.Ext(file.Filename)

	filePath,_,_,err :=shipperInsuranceService.BaseService.Upload(typeName, file, ext, allowTypes)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	res,err := shipperInsuranceService.UploadConfirm(admin,filePath,false)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	r.Success(c, res, "msg", 200)
	
}

//上传保险已确认的数据 二次确认
func (r ShipperInsuranceController) PostUploadConfirmConfirm(c *gin.Context) {

	
	type Params struct {
		FileKey        string    `form:"file_key" binding:"required"`
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		shipperInsuranceService     = cmsService.NewShipperInsuranceService(c)
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)		
	)


	if err != nil {
		panic(err)
		return
	}
	now :=carbon.Now(configs.AsiaShanghai)
	insuranceStopTime :=shipperInsuranceService.BaseService.GetAppConfig("insurance_stop_time")
	originalStopTime :=configs.MyApp.InsuranceStopTime
	newTime :=originalStopTime
	if len(insuranceStopTime) > 0 {
		newTime =insuranceStopTime
	}
	stopTime :=carbon.Parse(now.Format("Y-m-d")+" "+newTime,configs.AsiaShanghai)
	if now.Lt(stopTime) { //保险停止时间
		tools.Logger.Info("保险停止时间还没到",stopTime)
		stTime :=stopTime.Format("Y-m-d H:i:s")
		if langUtil.Lang == "ug" {
			stTime =stopTime.Format("d-m-Y H:i:s")
		}
		r.Fail(c,fmt.Sprintf(langUtil.T("try_after_some_time"),stTime),-1000)
		return 
	}
	admin := permissions.GetAdmin(c)
	
	res,err := shipperInsuranceService.UploadConfirm(admin,params.FileKey,true)
	if err != nil {
		r.Fail(c, err.Error(), -1000)
		return
	}
	r.Success(c, res, "msg", 200)
	
}