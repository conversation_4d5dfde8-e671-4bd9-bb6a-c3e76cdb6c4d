package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"

	// cmsService "mulazim-api/services/cms"
	services "mulazim-api/services"
	"mulazim-api/tools"
	cmsTransformer "mulazim-api/transformers/cms"
	"reflect"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type FoodsPreferentialController struct {
	controllers.BaseController
}

// DiscountList
//
//	@Author: YaKupJan
//	@Date: 2024-09-10 12:47:03
//	@Description: 查询 美食优惠信息表
//	@receiver fpc
//	@param c
func (fpc FoodsPreferentialController) DiscountList(c *gin.Context) {
	admin := permissions.GetAdmin(c)
	type Params struct {
		Page         int    `form:"page"`
		Limit        int    `form:"limit"`
		CityID       int    `form:"city_id"`
		AreaID       int    `form:"area_id"`
		RestaurantID int    `form:"restaurant_id"`
		SortColumns  string `form:"sort_columns"`
		Search       string `form:"search"`
		StartDate    string `form:"start_date"`
		EndDate      string `form:"end_date"`
		StateType    int    `form:"type"`
	}
	var (
		params      Params
		service     = services.NewFoodsPreferentialService(c)
		transformer = cmsTransformer.NewFoodsPreferentialTransformer(c)
	)
	if err := c.ShouldBindQuery(&params); err != nil {
		panic(err)
	}
	if params.Page == 0 {
		params.Page = 1
	}
	if params.Limit == 0 {
		params.Limit = 10
	}
	result, total := service.GetFoodsPreferentialList(c, admin, params.Limit, params.Page, params.SortColumns, params.Search, params.RestaurantID, params.CityID, params.AreaID, params.StartDate, params.EndDate,params.StateType)
	data := transformer.FoodsPreferentialListTransform(result)
	fpc.Success(c, gin.H{
		"items": data,
		"total": total,
	}, "msg", 200)
}

// PreferentialTypeList
//
//	@Author: YaKupJan
//	@Date: 2024-09-10 13:02:17
//	@Description: 优惠类型列表
//	@receiver fpc
//	@param c
func (fpc FoodsPreferentialController) PreferentialTypeList(c *gin.Context) {
	var (
		service = services.NewFoodsPreferentialService(c)
	)
	list := service.PreferentialTypeList()
	fpc.Success(c, list, "msg", 200)
}

// HasPreferential
//
//	@Author: YaKupJan
//	@Date: 2024-09-10 13:24:08
//	@Description: 判断是否有优惠
//	@receiver fpc
//	@param c
func (fpc FoodsPreferentialController) HasPreferential(c *gin.Context) {
	type Params struct {
		RestaurantID int `form:"restaurant_id" binding:"required"`
	}
	var (
		params  Params
		service = services.NewFoodsPreferentialService(c)
	)
	if err := c.ShouldBindQuery(&params); err != nil {
		panic(err)
	}
	hasPreferential := service.HasPreferential(params.RestaurantID)
	fpc.Success(c, hasPreferential, "msg", 200)
}

// CreatePreferential
//
//	@Author: YaKupJan
//	@Date: 2024-09-10 18:57:59
//	@Description: 创建优惠
//	@receiver fpc
//	@param c
func (fpc FoodsPreferentialController) CreatePreferential(c *gin.Context) {
	type Params struct {
		RestaurantID           int    `json:"restaurant_id" binding:"required"`
		Type                   int    `json:"type" binding:"required"`
		PreferentialStyle      int    `json:"preferential_style" binding:"required,oneof=1 2 3"`
		RestaurantFoodsID      int    `json:"restaurant_foods_id"`
		OriginPrice            *uint  `json:"origin_price"`
		DiscountPrice          *uint  `json:"discount_price"`
		PreferentialPercentage *uint  `json:"preferential_percentage"`
		MaxOrderCount          *int   `json:"max_order_count" binding:"required,gt=0"`
		OrderCountPerDay       *int   `json:"order_count_per_day" binding:"required,oneof=0 1"`
		StartDateTime          string `json:"start_date_time" binding:"required"`
		EndDateTime            string `json:"end_date_time" binding:"required"`
		StartTime              string `json:"start_time" binding:"required"`
		EndTime                string `json:"end_time" binding:"required"`
		SendPlatform           *int   `json:"send_platform" binding:"required,oneof=0 1"`
		State                  *int   `json:"state" binding:"required,oneof=0 1"`
		PriceMarkupType        int    `json:"price_markup_type" binding:""`  // 1 是 加价销售 2  不是
		PriceMarkupId          int    `json:"price_markup_id" binding:""`  // 加价ID
		PriceMarkupCount       int    `json:"price_markup_count" binding:""`  // 加价数量
		OptionIds       []int    `json:"option_ids" binding:""`  // 规格ID
		FoodType       int    `json:"food_type" binding:""`  // 美食类型
		Weight       int    `json:"weight" binding:""`  // 权重
	}

	var (
		params  Params
		service = services.NewFoodsPreferentialService(c)
	)
	if err := c.ShouldBind(&params); err != nil {
		panic(err)
	}
	if params.PreferentialStyle == 1 {
		if params.RestaurantFoodsID == 0 {
			fpc.failMsg(c, "餐厅ID不能为空", "رېستۇران ID قۇرۇق بولسا بولمايدۇ")
			return
		}
		if params.OriginPrice == nil {
			fpc.failMsg(c, "原始价格必须填写", "ئەسلىدىكى باھانى چوقۇم تولدۇرۇش كېرەك")
			return
		}
		if params.DiscountPrice == nil {
			fpc.failMsg(c, "优惠价格必须填写", "ئىتىبار باھانى چوقۇم تولدۇرۇش كېرەك ")
			return
		}
	}
	if params.PreferentialStyle == 2 {
		if params.PreferentialPercentage == nil {
			fpc.failMsg(c, "优惠百分比必须填写", "ئېتىبار پىرسەنتى چوقۇم تولدۇرۇش كېرەك ")
			return
		}
	}

	


	admin := permissions.GetAdmin(c)
	foodsPreferential := models.FoodsPreferential{
		RestaurantID:      params.RestaurantID,
		RestaurantFoodsID: params.RestaurantFoodsID,
		Type:              params.Type,
		StartDateTime:     carbon.ParseByFormat(params.StartDateTime, "Y-m-d").Carbon2Time(),
		EndDateTime:       carbon.ParseByFormat(params.EndDateTime, "Y-m-d").Carbon2Time(),
		StartTime:         carbon.ParseByFormat(params.StartTime, "H:i").ToTimeString(),
		EndTime:           carbon.ParseByFormat(params.EndTime, "H:i").ToTimeString(),
		DiscountPrice:     *params.DiscountPrice,
		MaxOrderCount:     params.MaxOrderCount,
		OrderCountPerDay:  *params.OrderCountPerDay,
		SendPlatform:      *params.SendPlatform,
		State:             *params.State,
		CreatorID:         &admin.ID,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
		PriceMarkupType:   params.PriceMarkupType,
		PriceMarkupId:     params.PriceMarkupId,
		PriceMarkupCount:  params.PriceMarkupCount,
		Weight:            params.Weight,
	}
	
	startDate := carbon.ParseByFormat(params.StartDateTime, "Y-m-d")
	endDate := carbon.ParseByFormat(params.EndDateTime, "Y-m-d")

	// 判断日期
	if startDate.Gt(endDate) {
		fpc.failMsg(c, "开始日期不能晚于结束日期", "باشلىنىدىغان چىسلا ئاخىرلىشىدىغان چىسلادىن كېچىكىپ كەتسە بولمايدۇ ")
		return
	}
	// 单个美食 创建优惠
	if params.PreferentialStyle == 1 {
		food := service.GetFoodById(params.RestaurantFoodsID)
		if food.ID == 0 || food.RestaurantID != params.RestaurantID {
			fpc.Fail(c,"not_found", -1000)
			return
		}
		if  params.FoodType == 1 && len(params.OptionIds) == 0 {
			fpc.Fail(c,"spec_foods_type_error",-1000)
			return
		}
		// 规格
		if  len(params.OptionIds) > 0 && food.FoodType != models.FoodsComboItemFoodTypeSpec{
			fpc.Fail(c,"spec_foods_type_error",-1000)
			return
		}
		
		foodsPreferential.OriginalPrice = food.Price
		foodsPreferential.FoodType = food.FoodType
		foodsPreferential.FoodType = food.FoodType
		// 规格活动
		if food.FoodType == models.RestaurantFoodsTypeSpec {
			SpecID, err := service.SaveFoodSpec(params.RestaurantID, params.RestaurantFoodsID, params.OptionIds)
			if err != nil {
				fpc.Fail(c,"faild", -1000)
				return 
			}
			foodsPreferential.SpecID = SpecID
			spec := service.GetFoodSpecByID(SpecID)
			foodsPreferential.OriginalPrice = uint(spec.Price)
		}
		ok, ugMsg,zhMsg := service.CreateFoodsPreferential(foodsPreferential, params.OriginPrice, params.DiscountPrice,params.OptionIds)
		if ok {
			tools.Logger.Info("重要日志：优惠活动创建成功(单个)", fpc.structToMapWithDereference(params))
			fpc.Ok(c)
			return
		} else {
			fpc.failMsg(c, zhMsg, ugMsg)
			return
		}
	}
	// 批量美食 创建优惠
	if params.PreferentialStyle == 2 {
		if service.RestaurantHasSpecFoods(params.RestaurantID) {
			fpc.Fail(c,"this_has_spec_foods",-1000)
			return
		}
		foodsPreferential.PriceMarkupType = 2
		ok, ugMsg,zhMsg := service.CreateFoodsPreferentialForAllFoods(foodsPreferential, params.PreferentialPercentage)
		if ok {
			tools.Logger.Info("重要日志：优惠活动创建成功(批量)", fpc.structToMapWithDereference(params))
			fpc.Ok(c)
			return
		} else {
			fpc.failMsg(c, zhMsg, ugMsg)
			return
		}
	}
	// 加价美食添加优惠销售
	if params.PreferentialStyle == 3 {
		if params.PriceMarkupCount == 0 || params.PriceMarkupId == 0 || params.PriceMarkupType == 0 {
			fpc.Fail(c, "missing_parameter",-1000)
			return
		}
		// 如果最大销售数量 大于加价销售数量
		if *params.MaxOrderCount > params.PriceMarkupCount   {
			fpc.Fail(c,"max_order_count_cannot_exceed_markup_count",-1000)
			return
		}

		// 规格活动
		if params.FoodType == models.RestaurantFoodsTypeSpec {
			SpecID , err := service.SaveFoodSpec(params.RestaurantFoodsID, params.RestaurantFoodsID,params.OptionIds)
			if err != nil {
				fpc.Fail(c,"faild", -1000)
				return 
			}
			foodsPreferential.SpecID = SpecID
			foodsPreferential.FoodType = uint8(params.FoodType)
			spec := service.GetFoodSpecByID(SpecID)
			foodsPreferential.OriginalPrice = uint(spec.Price)
		}

		// 加价优惠的类型设置为 1
		foodsPreferential.PriceMarkupType = 1
		// 检查是不是有足够的加价美食
		service.CheckPriceMarkupTotalCountIsEnough(params.PriceMarkupId,params.PriceMarkupCount)
		// 获取加价美食的信息
		markupFood := service.GetPriceMarkupFoodInPrice(params.PriceMarkupId)
		// 判断 优惠活动是不是在加价活动时间范围内
		service.CheekFoodsPreferentialTimeIsInPriceMarkupFoodTime(params.StartDateTime, params.EndDateTime, params.StartTime, params.EndTime, markupFood.StartDate, markupFood.EndDate)
		inPrice := uint(markupFood.InPrice)
		ok, ugMsg ,zhMsg := service.CreateFoodsPreferential(foodsPreferential, &inPrice, params.DiscountPrice,params.OptionIds)
		if ok {
			tools.Logger.Info("重要日志：加价优惠活动创建成功(单个)", fpc.structToMapWithDereference(params))
			fpc.Ok(c)
			return
		} else {
			fpc.failMsg(c, zhMsg, ugMsg)
			return
		}
	}

}

// failMsg
//
//	@Author: YaKupJan
//	@Date: 2024-09-10 19:22:20
//	@Description: 失败返回
//	@receiver fpc
//	@param c
//	@param msgZh
//	@param msgUg
func (fpc FoodsPreferentialController) failMsg(c *gin.Context, msgZh string, msgUg string) {
	var (
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	msg := ""
	if langUtil.Lang == "zh" {
		msg = langUtil.TZh(msgZh)
	} else {
		msg = langUtil.TUg(msgUg)
	}
	fpc.Fail(c, msg, -1000)
	return
}

// ChangePreferentialState
//
//	@Author: YaKupJan
//	@Date: 2024-09-11 15:47:25
//	@Description: 修改优惠状态
//	@receiver fpc
//	@param c
func (fpc FoodsPreferentialController) ChangePreferentialState(c *gin.Context) {
	type Params struct {
		Id    []int `json:"id" binding:"required"`
		State *int  `json:"state" binding:"required,oneof=0 1"`
	}
	var (
		params  Params
		service = services.NewFoodsPreferentialService(c)
	)
	if err := c.ShouldBindJSON(&params); err != nil {
		panic(err)
	}
	if params.State == nil {
		fpc.failMsg(c, "状态不能为空", "ھالەت قۇرۇق بولسا بولمايدۇ ")
		return
	}
	if len(params.Id) == 0 {
		fpc.failMsg(c, "请选择要操作的优惠", "مەشغۇلات قىلىدىغان ئېتىبارنى تاللاڭ")
		return
	}
	ok, ugMsg, zhMsg := service.ChangePreferentialState(params.Id, params.State)
	if ok {
		fpc.Ok(c)
		return
	} else {
		fpc.failMsg(c, zhMsg, ugMsg)
		return
	}
}

// 更新优惠排序
func (fpc FoodsPreferentialController) UpdateWeight(c *gin.Context) {
	type Params struct {
		Id    int `form:"id" binding:"required"`
		Weight *int  `form:"weight" binding:"required"`
	}
	var (
		params  Params
		service = services.NewFoodsPreferentialService(c)
	)
	if err := c.ShouldBind(&params); err != nil {
		panic(err)
	}
	err := service.UpdateWeight(params.Id, params.Weight)
	if err != nil {
		fpc.Fail(c,err.Error(),-1000)
		return
	}
	fpc.Ok(c)
}

// EditPreferential
//
//	@Author: YaKupJan
//	@Date: 2024-09-11 15:47:15
//	@Description: 编辑优惠
//	@receiver fpc
//	@param c
func (fpc FoodsPreferentialController) EditPreferential(c *gin.Context) {
	type Params struct {
		Id                int    `json:"id" binding:"required"`
		RestaurantID      int    `json:"restaurant_id" binding:"required"`
		Type              int    `json:"type" binding:"required"`
		RestaurantFoodsID int    `json:"restaurant_foods_id" binding:"required"`
		OriginPrice       *uint  `json:"origin_price" binding:"required"`
		DiscountPrice     *uint  `json:"discount_price" binding:"required"`
		MaxOrderCount     *int   `json:"max_order_count" binding:"required,gt=0"`
		OrderCountPerDay  *int   `json:"order_count_per_day" binding:"required,oneof=0 1"`
		StartDateTime     string `json:"start_date_time" binding:"required"`
		EndDateTime       string `json:"end_date_time" binding:"required"`
		StartTime         string `json:"start_time" binding:"required"`
		EndTime           string `json:"end_time" binding:"required"`
		SendPlatform      *int   `json:"send_platform" binding:"required,oneof=0 1"`
		State             *int   `json:"state" binding:"required,oneof=0 1"`
		PriceMarkupType   int    `json:"price_markup_type" binding:""`  // 1 是 加价销售 2  不是
		PriceMarkupId     int    `json:"price_markup_id" binding:""`  // 加价ID
		PriceMarkupCount  int    `json:"price_markup_count" binding:""`  // 加价数量
		SpecID            int    `json:"spec_id" binding:""`  // 规格ID
		FoodType          int    `json:"food_type" binding:""`  // 美食类型
		Weight            int    `json:"weight" binding:""`  // 权重
	}

	var (
		params  Params
		service = services.NewFoodsPreferentialService(c)
	)
	if err := c.ShouldBind(&params); err != nil {
		panic(err)
	}
	foodsPreferential := models.FoodsPreferential{
		ID:                params.Id,
		RestaurantID:      params.RestaurantID,
		RestaurantFoodsID: params.RestaurantFoodsID,
		Type:              params.Type,
		StartDateTime:     carbon.ParseByFormat(params.StartDateTime, "Y-m-d").Carbon2Time(),
		EndDateTime:       carbon.ParseByFormat(params.EndDateTime, "Y-m-d").Carbon2Time(),
		StartTime:         carbon.ParseByFormat(params.StartTime, "H:i").ToTimeString(),
		EndTime:           carbon.ParseByFormat(params.EndTime, "H:i").ToTimeString(),
		DiscountPrice:     *params.DiscountPrice,
		MaxOrderCount:     params.MaxOrderCount,
		OrderCountPerDay:  *params.OrderCountPerDay,
		SendPlatform:      *params.SendPlatform,
		State:             *params.State,
		UpdatedAt:         time.Now(),
		PriceMarkupType:   params.PriceMarkupType,
		PriceMarkupId:     params.PriceMarkupId,
		PriceMarkupCount:  params.PriceMarkupCount,
		Weight:            params.Weight,
	}
	// 判断是不是加价美食
	originPrice := params.OriginPrice
	if params.PriceMarkupId != 0 {
		// 如果最大销售数量 大于加价销售数量
		if *params.MaxOrderCount > params.PriceMarkupCount   {
			fpc.Fail(c,"max_order_count_cannot_exceed_markup_count",-1000)
			return
		}
		service.CheckPriceMarkupTotalCountIsEnough(params.PriceMarkupId,params.PriceMarkupCount)
		markupFoodInPrice := service.GetPriceMarkupFoodInPrice(params.PriceMarkupId)
		// 判断 优惠活动是不是在加价活动时间范围内
		service.CheekFoodsPreferentialTimeIsInPriceMarkupFoodTime(params.StartDateTime, params.EndDateTime, params.StartTime, params.EndTime, markupFoodInPrice.StartDate, markupFoodInPrice.EndDate)
		inPrice := uint(markupFoodInPrice.InPrice)
		originPrice = &inPrice
	}
	ok, ugMsg, zhMsg := service.EditPreferential(foodsPreferential, originPrice, params.DiscountPrice)
	if ok {
		tools.Logger.Info("重要日志：优惠活动修改成功", fpc.structToMapWithDereference(params))
		fpc.Ok(c)
		return
	} else {
		fpc.failMsg(c, zhMsg, ugMsg)
		return
	}
}

// structToMapWithDereference
//
//	@Author: YaKupJan
//	@Date: 2024-09-11 15:47:02
//	@Description:   将结构体转换为map
//	@receiver fpc
//	@param data
//	@return map[string]interface{}
func (fpc FoodsPreferentialController) structToMapWithDereference(data interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	val := reflect.ValueOf(data)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	// 遍历结构体字段
	for i := 0; i < val.NumField(); i++ {
		field := val.Type().Field(i)
		fieldName := field.Name
		fieldValue := val.Field(i)

		// 检查是否是指针类型，解引用指针
		if fieldValue.Kind() == reflect.Ptr {
			if !fieldValue.IsNil() {
				result[fieldName] = fieldValue.Elem().Interface()
			} else {
				result[fieldName] = nil // 如果指针为空
			}
		} else {
			result[fieldName] = fieldValue.Interface()
		}
	}
	return result
}

// DeletePreferential
//
//  @Author: YaKupJan
//  @Date: 2024-09-11 15:55:32
//  @Description: 删除优惠
//  @receiver fpc
//  @param c
func (fpc FoodsPreferentialController) DeletePreferential(c *gin.Context) {
	type Params struct {
		Id []int `json:"id" binding:"required"`
	}
	admin := permissions.GetAdmin(c)
	var (
		params  Params
		service = services.NewFoodsPreferentialService(c)
	)
	if err := c.ShouldBindJSON(&params); err != nil {
		panic(err)
	}
	if len(params.Id) == 0 {
		fpc.failMsg(c, "请选择要删除的优惠", "مەشغۇلات قىلىدىغان ئېتىبارنى تاللاڭ")
		return
	}
	ok, msg := service.DeletePreferential(params.Id)
	if ok {
		fpc.Ok(c)
		tools.Logger.Info("重要日志：优惠活动删除成功", map[string]interface{}{
			"id": params.Id,
			"admin_id":admin.ID,
		})
		return
	} else {
		fpc.failMsg(c, msg, msg)
		return
	}
}
