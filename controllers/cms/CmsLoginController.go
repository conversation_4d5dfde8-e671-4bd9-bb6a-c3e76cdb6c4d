package cms

import (
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/services/cms"
	"mulazim-api/tools"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// CmsLoginController CMS登录控制器
type CmsLoginController struct {
	controllers.BaseController
}

// Login CMS登录接口
// @Summary CMS登录接口
// @Description CMS系统登录接口，验证用户名和密码，生成JWT token，并设置cookie
// @Tags CMS登录
// @Accept multipart/form-data
// @Produce application/json
// @Param name formData string true "用户名"
// @Param password formData string true "密码"
// @Success 200 {object} map[string]interface{} "登录成功"
// @Failure 401 {object} map[string]interface{} "登录失败"
// @Router /cms/v2/login [post]
func (c *CmsLoginController) Login(ctx *gin.Context) {
	type Params struct {
		Name string `form:"name" binding:"required"` // 用户名
		Password string `form:"password" binding:"required"` // 密码
		Token string `form:"token" binding:"required"` // token
	}
	var (
		params          Params
		err             = ctx.ShouldBind(&params)
		loginService = cms.NewLoginService(ctx)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	
	success, admin,_,cookieValue, errMsg := loginService.Login(
		strings.TrimSpace(params.Name),
		strings.TrimSpace(params.Password),
		strings.TrimSpace(params.Token),
		ctx)
	if !success {
		langUtil, _ := ctx.Get("lang_util")
		lang := langUtil.(lang.LangUtil)
		c.Fail(ctx,lang.T(errMsg),http.StatusUnauthorized)
		return
	}else{
	// 设置cookie
		ctx.SetCookie(
			"mulazim_two_point_zero", 
			cookieValue, 
			24*60*60,  // 过期时间
			"/",       // 路径
			"",        // 域名
			false,     // 是否只在HTTPS下传输
			true,      // 是否只允许HTTP访问
		)
		c.Success(ctx,gin.H{
			// "token": token,
			"cookie_value": cookieValue,
			"admin": gin.H{
				"id":            admin.ID,
				"name":          admin.Name,
				"mobile":        admin.Mobile,
				"type":          admin.Type,
				"admin_city_id": admin.AdminCityID,
				"admin_area_id": admin.AdminAreaID,
				"real_name":     admin.RealName,
				"avatar":        tools.AddCdn(admin.Avatar),
			},
		},"msg",http.StatusOK)
	}
}
