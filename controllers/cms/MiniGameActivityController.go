package cms

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	errors2 "mulazim-api/errors"
	"mulazim-api/permissions"
	cmsResources "mulazim-api/resources/cms"
	cmsService "mulazim-api/services/cms"
	"mulazim-api/tools"
	cmsTransformer "mulazim-api/transformers/cms"
	"net/http"
	"time"
)

type MiniGameActivityController struct {
	controllers.BaseController
}

// ActivityList
//
//	@Author: YaKupJan
//	@Date: 2024-11-13 10:11:03
//	@Description: 游戏活动列表
//	@receiver m
//	@param c
func (m MiniGameActivityController) ActivityList(c *gin.Context) {
	type Params struct {
		Search string `form:"search"`
	}
	var (
		params      Params
		err         = c.ShouldBindQuery(&params)
		service     = cmsService.NewMiniGameActivityService(c)
		transformer = cmsTransformer.NewMiniGameActivityTransformer(c)
	)
	if err != nil {
		panic(err)
		return
	}
	pagination := tools.GetPagination(c)
	// 获取游戏活动列表
	list, total := service.ActivityList(pagination, params.Search)
	// 格式化游戏活动列表
	listFormat := transformer.ActivityListFormat(list)
	m.Success(c, gin.H{
		"items": listFormat,
		"total": total,
	}, "msg", 200)
}

// ToggleActivityState
//
//	@Author: YaKupJan
//	@Date: 2024-11-13 15:36:27
//	@Description: 切换游戏活动状态
//	@receiver m
//	@param c
func (m MiniGameActivityController) ToggleActivityState(c *gin.Context) {
	type Params struct {
		Id string `form:"id"`
	}
	var (
		params  Params
		err     = c.ShouldBindQuery(&params)
		service = cmsService.NewMiniGameActivityService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	// 切换游戏活动状态
	err1 := service.ToggleActivityState(params.Id)
	if err1 != nil {
		panic(err1)
		return
	}
	m.Ok(c)
}

// CreateActivity
//
//	@Author: YaKupJan
//	@Date: 2024-11-13 16:28:32
//	@Description: 创建新的游戏活动
//	@receiver m
//	@param c
func (m MiniGameActivityController) CreateActivity(c *gin.Context) {
	var (
		params  cmsResources.CreateMiniGameActivityParams
		service = cmsService.NewMiniGameActivityService(c)
	)

	if err := c.ShouldBind(&params); err != nil {
		panic(err)
		return
	}
	// 创建新的游戏活动
	err := service.CreateActivity(params)
	if err != nil {
		m.Fail(c,err.Error(),-1000)
		return
	}

	m.Ok(c)
}

// UpdateActivity
//
//	@Author: YaKupJan
//	@Date: 2024-11-13 16:32:25
//	@Description: 修改游戏活动
//	@receiver m
//	@param c
func (m MiniGameActivityController) UpdateActivity(c *gin.Context) {
	var (
		params  cmsResources.UpdateMiniGameActivityParams
		service = cmsService.NewMiniGameActivityService(c)
	)

	if err := c.ShouldBind(&params); err != nil {
		panic(err)
		return
	}
	// 修改游戏活动
	if err := service.UpdateActivity(params); err != nil {
		m.Fail(c,err.Error(),-1000)
		return
	}

	m.Ok(c)
}

// DeleteActivity
//
//  @Author: YaKupJan
//  @Date: 2024-11-13 17:21:21
//  @Description: 删除游戏活动
//  @receiver m
//  @param c
func (m MiniGameActivityController) DeleteActivity(c *gin.Context) {
	type Params struct {
		ID int `json:"id" binding:"required"` // 活动ID
	}

	var params Params
	if err := c.ShouldBind(&params); err != nil {
		panic(err)
		return
	}

	// 创建服务实例并调用 DeleteActivity 方法
	service := cmsService.NewMiniGameActivityService(c)
	admin := permissions.GetAdmin(c)
	if !admin.IsOwner() {
		m.Fail(c,"forbidden",-1000)
		return
	}
	if err := service.DeleteActivity(params.ID); err != nil {
		m.Fail(c,err.Error(),-1000)
		return
	}

	// 成功响应
	m.Ok(c)
}

// ActivityDetail
//
//  @Author: YaKupJan
//  @Date: 2024-11-13 17:56:20
//  @Description: 活动详细
//  @receiver m
//  @param c
func (m MiniGameActivityController) ActivityDetail(c *gin.Context) {
	// 定义请求结构体用于接收活动ID
	type Params struct {
		ID int `form:"id" binding:"required"` // 活动ID
	}

	var params Params
	if err := c.ShouldBindQuery(&params); err != nil {
		panic(err)
		return
	}

	// 创建服务实例并调用 GetActivityDetails 方法
	service := cmsService.NewMiniGameActivityService(c)
	activity, err := service.ActivityDetails(params.ID)
	if err != nil {
		m.Fail(c,err.Error(),-1000)
		return
	}
	transformer := cmsTransformer.NewMiniGameActivityTransformer(c)
	detailFormat := transformer.ActivityDetailFormat(activity)
	// 成功响应，返回活动详细信息
	m.Success(c,detailFormat,"msg",200)
}

// ActivityStatistics
//
//  @Author: YaKupJan
//  @Date: 2024-11-22 11:37:06
//  @Description: 活动统计
//  @receiver m
//  @param c
func (m MiniGameActivityController) ActivityStatistics(c *gin.Context) {
	type Params struct {
		AreaID      int    `form:"area_id" binding:""`      // 区域ID
		CityID      int    `form:"city_id" binding:""`      // 城市ID
		ActivityID  int    `form:"activity_id" binding:"required"`  // 城市ID
		Mobile      string `form:"mobile" binding:""`       // 手机号
		SortColumns string `form:"sort_columns" binding:""` // 排序字段
	}
	var (
		params Params
		service = cmsService.NewMiniGameActivityService(c)
		transformer = cmsTransformer.NewMiniGameActivityTransformer(c)
	)

	if err := c.ShouldBindQuery(&params); err != nil {
		if params.ActivityID == 0 {
			m.Fail(c,"please_select_an_activity",-1000)
			return
		}
		panic(err)
		return
	}
	pagination := tools.GetPagination(c)
	admin := permissions.GetAdmin(c)
	if admin.IsDealer() || admin.IsDealerSub() {
		params.CityID = admin.AdminCityID
		params.AreaID = admin.AdminAreaID
	}
	statistics, allAttendCount, sendAmount, usedAmount, allPlayCount, allTakeOrderCount := service.ActivityStatistics(pagination,params.ActivityID, params.CityID, params.AreaID, params.Mobile,params.SortColumns)
	var format = transformer.ActivityStatisticFormat(statistics, pagination, allAttendCount, sendAmount, usedAmount,allPlayCount,allTakeOrderCount)
	m.Success(c,format,"msg",200)
}

// CreateMiniGame
//
//  @Author: YaKupJan
//  @Date: 2024-11-15 16:14:53
//  @Description: 创建游戏
//  @receiver m
//  @param c
func (m MiniGameActivityController) CreateMiniGame(c *gin.Context) {
	type Params struct {
		NameUg         string `json:"name_ug" binding:"required"`          // 名称维语
		NameZh         string `json:"name_zh" binding:"required"`          // 名称汉语
		RemarkUg       string `json:"remark_ug" binding:"required"`        // 备注维文
		RemarkZh       string `json:"remark_zh" binding:"required"`        // 备注中文
		Image          string `json:"image" binding:"required"`            // 图片
		GameCategoryId int    `json:"game_category_id" binding:"required"` // 游戏类型ID文
	}
	var (
		params  Params
		service = cmsService.NewMiniGameActivityService(c)
	)

	if err := c.ShouldBindJSON(&params); err != nil {
		panic(err)
		return
	}
	// 创建游戏
	service.CreateMiniGame(params.NameZh,params.NameUg,params.RemarkZh,params.RemarkUg,params.Image,params.GameCategoryId)
	m.Ok(c)
}
// UpdateMiniGame
//
//  @Author: YaKupJan
//  @Date: 2024-11-15 16:33:36
//  @Description: 修改游戏
//  @receiver m
//  @param c
func (m MiniGameActivityController) UpdateMiniGame(c *gin.Context) {
	type Params struct {
		ID             int    `json:"id" binding:"required"`               // 游戏ID
		NameUg         string `json:"name_ug" binding:"required"`          // 名称维语
		NameZh         string `json:"name_zh" binding:"required"`          // 名称汉语
		RemarkUg       string `json:"remark_ug" binding:"required"`        // 备注维文
		RemarkZh       string `json:"remark_zh" binding:"required"`        // 备注中文
		Image          string `json:"image" binding:"required"`            // 图片
		GameCategoryId int    `json:"game_category_id" binding:"required"` // 游戏类型ID
	}
	var (
		params  Params
		service = cmsService.NewMiniGameActivityService(c)
	)

	if err := c.ShouldBindJSON(&params); err != nil {
		customError := errors2.CustomError{
			Msg: err.Error(),
		}
		panic(customError)
		return
	}
	// 修改游戏
	service.UpdateMiniGame(params.ID, params.NameZh, params.NameUg, params.RemarkZh, params.RemarkUg, params.Image, params.GameCategoryId)
	m.Ok(c)
}

// DeleteMiniGame
//
//  @Author: YaKupJan
//  @Date: 2024-11-15 17:31:06
//  @Description: 删除游戏
//  @receiver m
//  @param c
func (m MiniGameActivityController) DeleteMiniGame(c *gin.Context) {
	type Params struct {
		ID int `json:"id" binding:"required"` // 游戏ID
	}

	var (
		params  Params
		service = cmsService.NewMiniGameActivityService(c)
	)

	if err := c.ShouldBindJSON(&params); err != nil {
		panic(err)
		return
	}

	// 删除游戏
	service.DeleteMiniGame(params.ID)

	m.Ok(c)
}

// ListMiniGame
//
//  @Author: YaKupJan
//  @Date: 2024-11-15 18:40:20
//  @Description: 游戏列表
//  @receiver m
//  @param c
func (m MiniGameActivityController) ListMiniGame(c *gin.Context) {
	//type Params struct {
	//	GameCategory int    `json:"game_category,omitempty"`          // 游戏类型（可选）
	//	Name         string `json:"name,omitempty"`                  // 游戏名称（模糊搜索，可选）
	//}

	var (
		//params  Params
		service = cmsService.NewMiniGameActivityService(c)
		transformer = cmsTransformer.NewMiniGameActivityTransformer(c)
	)

	//if err := c.ShouldBindJSON(&params); err != nil {
	//	panic(err)
	//	return
	//}
	pagination := tools.GetPagination(c)
	// 获取游戏列表
	data, total := service.ListMiniGame(pagination)
	format := transformer.ListMiniGameFormat(data)
	m.Success(c, gin.H{
		"total": total,
		"items": format,
	}, "msg", 200)
}

// UseDiscountStatistics
//
//  @Author: YaKupJan
//  @Date: 2024-11-18 11:59:00
//  @Description: 游戏优惠使用统计
//  @receiver m
//  @param c
func (m MiniGameActivityController) UseDiscountStatistics(c *gin.Context) {
	type Params struct {
		AreaID       int    `form:"area_id" binding:""`       // 区域ID
		CityID       int    `form:"city_id" binding:""`       // 城市ID
		RestaurantID int    `form:"restaurant_id" binding:""` // 餐厅ID
		ActivityID   int    `form:"activity_id" binding:"required"`   // 城市ID
		StartTime    string `form:"start_time" binding:""`    // 开始时间
		EndTime      string `form:"end_time" binding:""`      // 结束时间
		SortColumns  string `form:"sort_columns" binding:""`  // 排序字段
	}
	var (
		params Params
		service = cmsService.NewMiniGameActivityService(c)
		transformer = cmsTransformer.NewMiniGameActivityTransformer(c)
	)

	if err := c.ShouldBindQuery(&params); err != nil {
		if params.ActivityID == 0 {
			m.Fail(c,"please_select_an_activity",-1000)
			return
		}
		panic(err)
		return
	}
	pagination := tools.GetPagination(c)
	admin := permissions.GetAdmin(c)
	if admin.IsDealer() || admin.IsDealerSub() {
		params.CityID = admin.AdminCityID
		params.AreaID = admin.AdminAreaID
	}
	data, numbers := service.UseDiscountStatistics(pagination,params.ActivityID, params.CityID, params.AreaID, params.RestaurantID,params.StartTime,params.EndTime,params.SortColumns)
	format := transformer.UseDiscountStatisticsFormat(data,numbers)
	//data["items"] = tools.If(format==nil,[]map[string]interface{}{},format)
	m.Success(c,format,"msg",200)
}

// UseDiscountStatisticsExport
//
//  @Author: YaKupJan
//  @Date: 2024-11-22 17:08:02
//  @Description: 游戏优惠使用统计Excel导出
//  @receiver m
//  @param c
func (m MiniGameActivityController) UseDiscountStatisticsExport(c *gin.Context) {
	type Params struct {
		AreaID       int    `form:"area_id" binding:""`             // 区域ID
		CityID       int    `form:"city_id" binding:""`             // 城市ID
		RestaurantID int    `form:"restaurant_id" binding:""`       // 餐厅ID
		ActivityID   int    `form:"activity_id" binding:"required"` // 城市ID
		StartTime    string `form:"start_time" binding:""`          // 开始时间
		EndTime      string `form:"end_time" binding:""`            // 结束时间
		SortColumns  string `form:"sort_columns" binding:""`        // 排序字段
	}
	var (
		params      Params
		service     = cmsService.NewMiniGameActivityService(c)
		transformer = cmsTransformer.NewMiniGameActivityTransformer(c)
	)

	if err := c.ShouldBindQuery(&params); err != nil {
		if params.ActivityID == 0 {
			m.Fail(c, "please_select_an_activity", -1000)
			return
		}
		panic(err)
		return
	}
	pagination := tools.GetPagination(c)
	admin := permissions.GetAdmin(c)
	if admin.IsDealer() || admin.IsDealerSub() {
		params.CityID = admin.AdminCityID
		params.AreaID = admin.AdminAreaID
	}
	data, numbers := service.UseDiscountStatistics(pagination, params.ActivityID, params.CityID, params.AreaID, params.RestaurantID, params.StartTime, params.EndTime, params.SortColumns)
	format := transformer.UseDiscountStatisticsFormat(data, numbers)
	//开始excel导出
	fileName := carbon.Now(configs.AsiaShanghai).Format("YmdHis") + tools.RandStr(4)
	cols := []string{
		"序号",
		"地区",
		"餐厅",
		"订单编号",
		"订单原价",
		"优惠比例(%)",
		"优惠价格",
		"实际支付",
		"下单时间",
	}
	var result []map[string]interface{}
	for index, item := range format.Items {

		m := map[string]interface{}{
			"序号":        index+1,
			"地区":        item.AreaName,
			"餐厅":        item.RestaurantName,
			"订单编号":    item.OrderNo,
			"订单原价":    fmt.Sprintf("%.2f", item.OriginalPrice/100),
			"优惠比例(%)": item.OrderPercent,
			"优惠价格":    fmt.Sprintf("%.2f", item.Discount/100),
			"实际支付":    tools.FormatFen2TYuanPrice(int(item.ActualPaid)),
			"下单时间":    item.CreatedAt,
		}
		result = append(result, m)
	}
	excelFile := tools.ExcelExport("游戏活动优惠使用信息", cols, result, fileName)
	downLoadFileName := "游戏活动优惠使用信息-" + carbon.Now(configs.AsiaShanghai).Format("Ymd") + ".xlsx"
	// 设置响应头，告诉浏览器这是一个 Excel 文件
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+downLoadFileName)
	err1 := excelFile.Write(c.Writer)
	if err1 != nil {
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}

// ActivityStatisticsByArea
//
//  @Author: YaKupJan
//  @Date: 2024-11-19 09:53:39
//  @Description: 活动统计 按区域
//  @receiver m
//  @param c
func (m MiniGameActivityController) ActivityStatisticsByArea(c *gin.Context) {
	type Params struct {
		ActivityID  int    `form:"activity_id" binding:"required"`  // 活动ID
		SortColumns string `form:"sort_columns" binding:""` // 排序字段
		CityID      int    `form:"city_id"`            // 地区ID
		AreaID      int    `form:"area_id"`            // 区域ID
		Lang 	  string `json:"lang"`
	}

	var (
		params Params
		service = cmsService.NewMiniGameActivityService(c)
		transformer = cmsTransformer.NewMiniGameActivityTransformer(c)
	)
	if err := c.ShouldBindQuery(&params); err != nil {
		if params.ActivityID == 0 {
			m.Fail(c,"please_select_an_activity",-1000)
			return
		}
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)

	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	params.Lang = c.GetString("lang")


	cacheKeyStr,_ := json.Marshal(params)
	hashStr := md5.Sum(cacheKeyStr)
	cacheKey := fmt.Sprintf("mini_game_attends_2_%x", hashStr)
	duration :=30*time.Minute
	cachedString := tools.Remember(c,cacheKey, duration, func() interface{} {
		items, total := service.ActivityStatisticsByArea(params.ActivityID,params.SortColumns,admin)
		if  len(items) == 0{
			return map[string]interface{}{
				"items": make([]interface{}, 0),
				"total": 0,
			}
		}
		format := transformer.ActivityStatisticsByAreaFormat(items)
		return map[string]interface{}{
			"items": format,
			"total": total,
		}
	})
	cachedObject := make(map[string]interface{})
	json.Unmarshal([]byte(cachedString), &cachedObject)

	m.Success(c,cachedObject,"msg",200)
}


// UseDiscountStatisticsByArea
//
//  @Author: YaKupJan
//  @Date: 2024-11-20 11:23:01
//  @Description: 游戏优惠使用统计 按区域
//  @receiver m
//  @param c
func (m MiniGameActivityController) UseDiscountStatisticsByArea(c *gin.Context) {
	type Params struct {
		ActivityID  int    `form:"activity_id" binding:"required"`  // 活动ID
		SortColumns string `form:"sort_columns" binding:""` // 排序字段
		CityID      int    `form:"city_id"`            // 地区ID
		AreaID      int    `form:"area_id"`            // 区域ID
		Lang 	  string `json:"lang"`
	}

	var (
		params Params
		service = cmsService.NewMiniGameActivityService(c)
		transformer = cmsTransformer.NewMiniGameActivityTransformer(c)

	)
	if err := c.ShouldBindQuery(&params); err != nil {
		if params.ActivityID == 0 {
			m.Fail(c,"please_select_an_activity",-1000)
			return
		}
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)
	params.Lang = c.GetString("lang")

	cacheKeyStr,_ := json.Marshal(params)
	hashStr := md5.Sum(cacheKeyStr)
	cacheKey := fmt.Sprintf("mini_game_discount_2_%x", hashStr)
	duration :=30*time.Minute
	cachedString := tools.Remember(c,cacheKey, duration, func() interface{} {
		items, total := service.UseDiscountStatisticsByArea(params.ActivityID,params.SortColumns,admin)
		if  len(items) == 0{
			return map[string]interface{}{
				"items": make([]interface{}, 0),
				"total": 0,
			}
		}
		format := transformer.UseDiscountStatisticsByAreaFormat(items)
		return map[string]interface{}{
			"items": format,
			"total": total,
		}
	})
	cachedObject := make(map[string]interface{})
	json.Unmarshal([]byte(cachedString), &cachedObject)

	m.Success(c,cachedObject,"msg",200)
}



// ListContentFromUser
//
// Author: YaKupJan
// Date: 2025-02-12 16:11:58
// Description: 获取用户提交的活动内容列表
// receiver m
// param c
func (m MiniGameActivityController) ListContentFromUser(c *gin.Context) {
	var (
		// 定义请求参数变量
		params cmsResources.MiniGameActivityListContentFromUserRequest
		// 初始化 MiniGameActivityService 服务层对象
		service = cmsService.NewMiniGameActivityService(c)
		// 初始化数据转换器
		transformer = cmsTransformer.NewMiniGameActivityTransformer(c)
	)

	// 绑定 URL 查询参数到 params 结构体
	if err := c.ShouldBindQuery(&params); err != nil {
		// 如果没有选择 ActivityID，则返回错误提示
		if params.ActivityID == 0 {
			m.Fail(c, "please_select_an_activity", -1000)
			return
		}
		// 绑定失败则直接抛出错误（可能是参数格式问题）
		panic(err)
		return
	}

	// 获取分页信息
	pagination := tools.GetPagination(c)

	// 获取管理员的区域信息，确保数据权限控制
	params.CityID, params.AreaID = permissions.GetAdminAreaInfo(c, params.CityID, params.AreaID)

	// 调用服务层方法查询用户提交的活动内容
	listContentFromUser, total, err := service.ListContentFromUser(params, pagination)
	if err != nil {
		// 查询失败，返回错误信息
		m.Fail(c, "failed", -1000)
		return
	}

	// 格式化 获取用户提交的情人节活动内容列表
	listContentFromUserFormat := transformer.ListContentFromUserFormat(listContentFromUser)

	// 返回成功的 JSON 响应，包括数据项和总数
	m.Success(c, gin.H{
		"items": listContentFromUserFormat, // 格式化后的数据列表
		"total": total,                     // 总记录数
	}, "msg", 200)
}

// OperateContentFromUser
//
// Author: YaKupJan
// Date: 2025-02-12 19:10:04
// Description: 操作用户的活动内容
// receiver m
// param c
func (m MiniGameActivityController) OperateContentFromUser(c *gin.Context) {

	var (
		// 定义请求参数变量
		params cmsResources.MiniGameActivityOperateContentFromUserRequest
		// 初始化 MiniGameActivityService 服务层对象
		service = cmsService.NewMiniGameActivityService(c)
	)

	// 绑定 URL 查询参数到 params 结构体
	if err := c.ShouldBind(&params); err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	err := service.OperateContentFromUser(params, admin)
	if err !=nil {
		m.Fail(c,"failed",-1000)
		return
	}
	m.Ok(c)
}
