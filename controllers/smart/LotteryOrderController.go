package smart

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/jobs"
	"mulazim-api/models"
	"mulazim-api/tools"
)

type LotteryOrderController struct {
	controllers.BaseController
}

// Refund 退单
func (lo *LotteryOrderController) Refund(c *gin.Context) {

	type RequestData struct {
		ID      int    `form:"id" binding:"required"`
		OrderNo string `form:"order_no" binding:"required"`
	}
	var request RequestData
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
		return
	}
	var lakala models.PayLakala
	result := tools.GetDB().
		Model(&models.PayLakala{}).
		Where("id = ? ", request.ID).
		Where("order_no = ? ", request.OrderNo).
		First(&lakala)
	if result.RowsAffected < 1 {
		lo.Fail(c, "not_found", 404)
		return
	}
	job := jobs.NewLakalaRefundJob()
	job.ProduceMessageToConsumer(map[string]interface{}{
		"lakala_pay_id": lakala.ID,
	})
	lo.Success(c, nil, "msg", 200)
	return
}
