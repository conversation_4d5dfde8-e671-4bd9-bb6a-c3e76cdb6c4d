package controllers

import (
	"fmt"
	"math/rand"

	"mulazim-api/models"
	"mulazim-api/models/chat"
	"mulazim-api/services"
	"mulazim-api/tools"
	"net/http"
	"path/filepath"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type ChatController struct {
	BaseController
}

// 描述：发送聊天信息
// 作者：Qurbanjan
// 文件：ChatController.go
// 修改时间：2023/09/18 19:33
func (s ChatController) PostSend(c *gin.Context) {
	var request chat.ChatSendRequest
	var (
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
	)
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	chatService := services.NewChatService(c)
	data, err := chatService.SendMessage(request, int64(admin.ID))
	if err != nil {
		s.Fail(c, err.Error(), http.StatusBadRequest)
		return
	}

	s.Success(c, data, "msg", 200)
}

// 描述：获取聊天详情
// 作者：Qurbanjan
// 文件：ChatController.go
// 修改时间：2023/09/19 10:54
func (s ChatController) GetChatDetail(c *gin.Context) {
	var request chat.ChatDetailRequest
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	var (
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
	)

	chatService := services.NewChatService(c)
	data, err := chatService.ChatDetail(c, request.OrderID, int64(admin.ID), admin.Type)
	if err != nil {
		panic(err)
	}
	s.Success(c, data, "msg", 200)
}

// 描述：聊天室的图片上传
// 作者：Qurbanjan
// 文件：ChatController.go
// 修改时间：2023/09/19 12:40
func (s ChatController) Upload(c *gin.Context) {
	file, err := c.FormFile("image")
	if err != nil {
		s.Fail(c, "无法获取上传文件", http.StatusBadRequest)
		return
	}

	ext := filepath.Ext(file.Filename)

	if ext != ".jpg" && ext != ".jpeg" && ext != ".png" {
		s.Fail(c, "只允许jpg|jpeg|png类型图片", http.StatusBadRequest)
		return
	}

	// 临时的
	fileHost := "http://" + c.Request.Host
	fileName := generateRandomFilename() + ext
	if err := c.SaveUploadedFile(file, "./uploads/"+fileName); err != nil {
		s.Fail(c, "上传保存失败", http.StatusBadRequest)
		return
	}

	fileUrl := fileHost + "/uploads/" + fileName
	s.Success(c, gin.H{"url": fileUrl}, "上传成功", http.StatusOK)

}

// 描述：获取聊天室聊天列表
// 作者：Qurbanjan
// 文件：ChatController.go
// 修改时间：2023/09/20 17:16
func (s ChatController) GetChatList(c *gin.Context) {
	var (
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
	)
	cityId :=tools.ToInt(c.DefaultQuery("city_id","0"))
	areaId :=tools.ToInt(c.DefaultQuery("area_id","0"))
	startDate :=c.DefaultQuery("start_date","")
	endDate :=c.DefaultQuery("end_date","")
	kw :=c.DefaultQuery("kw","")
	resId :=tools.ToInt(c.DefaultQuery("res_id","0"))
	shipperId :=tools.ToInt(c.DefaultQuery("shipper_id","0"))
	chatService := services.NewChatService(c)
	list, err := chatService.GetChatList(c, int64(admin.ID), admin.Type,cityId,areaId,kw,startDate,endDate,resId,shipperId)
	if err != nil {
		s.Fail(c,err.Error(),-1000)
		return
	}
	s.Success(c, list, "获取成功", http.StatusOK)

}

// 生成随机图片名称
func generateRandomFilename() string {
	// 生成当前时间的字符串形式
	timestamp := time.Now().Format("20060102150405")
	// 生成五位随机数字（00000 - 99999）
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Intn(100000)
	randomString := strconv.Itoa(randomNumber)
	paddedRandomString := fmt.Sprintf("%05s", randomString)
	// 将随机数字追加到时间戳后面
	return timestamp + paddedRandomString
}

func (s ChatController) PostClear(c *gin.Context) {
	type Request struct {
		OrderId    int `form:"order_id" json:"order_id"`
		SenderType int `form:"sender_type" json:"sender_type"`
	}
	var req Request
	if err := c.ShouldBind(&req); err != nil {
		panic(err)
	}
	groupPrefix := "group_"
	adminType := ""
	switch req.SenderType {
	case 1, 2, 3,4:
		adminType = "_admin_count"
	case 5,6:
		adminType = "_restaurant_count"
	case 8, 9:
		adminType = "_shipper_count"
	case 99:
		adminType = "_user_count"
	}
	key := groupPrefix + tools.ToString(req.OrderId) + adminType
	tools.RedisKeyValueUpdate(key, 0)
	s.Success(c, nil, "msg", http.StatusOK)

}
// CreateForbiddenWord
//
//  @Author: YaKupJan
//  @Date: 2024-10-11 11:01:52
//  @Description: 添加违禁词
//  @receiver s
//  @param c
func (s ChatController) CreateForbiddenWord(c *gin.Context) {
	type Params struct {
		Word    string `form:"word" json:"word"`
	}
	var (
		params Params
		service = services.NewChatService(c)
	)
	if err := c.ShouldBind(&params); err != nil {
		panic(err)
	}
	service.CreateForbiddenWord(params.Word)
	s.Success(c, nil, "msg", http.StatusOK)
}

// EncryptAndUpdateStateZeroWords
//
//  @Author: YaKupJan
//  @Date: 2024-10-11 11:03:00
//  @Description: 加密并更新状态为0的违禁词
//  @receiver s
//  @param c
func (s ChatController) EncryptAndUpdateStateZeroWords(c *gin.Context) {
	var (
		service = services.NewChatService(c)
	)
	service.EncryptAndUpdateStateZeroWords()
	s.Success(c, nil, "msg", http.StatusOK)
}