package merchant

import (
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/marketingRequest"
	"mulazim-api/requests/merchantRequest/food"
	merchantService "mulazim-api/services/merchant"
	"mulazim-api/tools"
	merchantTransformer "mulazim-api/transformers/merchant"

	"github.com/gin-gonic/gin"
)

type RestaurantFoodsController struct {
	controllers.BaseController
}



// GetRestaurantFoods
//
// @Description: 获取商家美食列表
// @Author: Rixat
// @Time: 2024-06-21 16:26:42
// @receiver 
// @param c *gin.Context
func (merchant *RestaurantFoodsController) GetRestaurantFoods(c *gin.Context) {
	type Params struct {
		ResID   int `form:"restaurant_id" binding:"required"`
		FoodsCategoryID   int `form:"foods_category_id" binding:"required"`
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service         = merchantService.NewRestaurantFoodsService(c)
		transformer     = merchantTransformer.NewRestaurantFoodsTransformer(c)
	)
	
	if err != nil {
		panic(err)
		return
	}

	restaurantFoods := service.GetRestaurantFoodsByCategory(params.ResID, params.FoodsCategoryID)
	foods := transformer.FormatRestaurantFoods(restaurantFoods)
	merchant.Success(c, foods, "msg", 200)
}


// GetFoodsCategory
//
// @Description: 获取美食分类
// @Author: Rixat
// @Time: 2024-06-21 16:27:08
// @receiver 
// @param c *gin.Context
func (merchant *RestaurantFoodsController) GetFoodsCategory(c *gin.Context) {
	type Params struct {
		ResID   int `form:"restaurant_id" binding:"required"`  // 餐厅ID
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service      = merchantService.NewRestaurantFoodsService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	categoryByRestaurant := service.GetFoodsCategoryByRestaurant(params.ResID)
	merchant.Success(c, categoryByRestaurant, "msg", 200)
}

// GetFoodsInformation
//
// @Description: 获取美食信息
// @Author: Rixat
// @Time: 2024-06-21 16:27:26
// @receiver 
// @param c *gin.Context
func (merchant *RestaurantFoodsController) GetFoodsInformation(c *gin.Context) {
	type Params struct {
		FoodId   int `form:"id" binding:"required"`  // 餐厅ID
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		value, _    = c.Get("admin")
		admin       = value.(models.Admin)
		service      = merchantService.NewRestaurantFoodsService(c)
		transformer     = merchantTransformer.NewRestaurantFoodsTransformer(c)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取美食信息
	foodInfo,err := service.GetRestaurantFoodInfoById(params.FoodId)
	if  err != nil {
		merchant.Fail(c,err.Error(),-1000)
		return 
	}
	// 验证是否属于该餐厅
	resId,  err:= permissions.GetRestaurantIdByMerchantAdmin(admin)
	if err != nil {
		merchant.Fail(c,"not_found",-1000)
		return 
	}
	if foodInfo.ID == 0 || foodInfo.Restaurant.ID != resId {
		merchant.Fail(c,"not_found",-1000)
		return 
	}
	// 格式化美食数据
	res := transformer.FormatFoodsInformation(foodInfo)
	merchant.Success(c, res, "msg", 200)
}


// PostCreateFoodsNew
//
// @Description: 创建美食新
// @Author: Rixat
// @Time: 2024-09-26 13:06:37
// @receiver 
// @param c *gin.Context
func (merchant *RestaurantFoodsController) PostCreateFoodsNew(c *gin.Context){
	var (
		merchantService = merchantService.NewMerchantService(c)
		request = food.FoodCreateRequestNew{}
		err     = c.ShouldBind(&request)
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
		adminRes = admin.GetAdminRestaurant()
	)
	if err != nil {
		panic(err)
		return
	}
	request.RestaurantID = int64(adminRes.ID)
	//  验证营业时间与美食时间是否冲突
	if code,msg := request.ValidateOpenTime(); code != marketingRequest.SUCCESS {
		if code == marketingRequest.TIME_CONFILICT {
			merchant.Fail(c, "time_confilict_with_res", -1000)
			return
		}else{
			merchant.Fail(c, msg, -1000)
			return
		}
	}
	// 添加美食
	suc, msg, createdFood := merchantService.PostCreateFoodNew(request)
	if suc {
		merchant.Success(c, []int{}, msg, 200)
		tools.ImportantLog(
			"merchant_food_update",
			admin.ID,
			createdFood.ID,
			adminRes.ID,
			request,
		)
		return
	}
	merchant.Fail(c, msg, -1000)
}