package merchant

import (
	"fmt"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	merchantService "mulazim-api/services/merchant"
	"mulazim-api/tools"
	merchantTransformer "mulazim-api/transformers/merchant"
	"time"

	"github.com/gin-gonic/gin"
)

type OrderV2Controller struct {
	controllers.BaseController
}

// GetNewList
//
//	@Description: 商家端获取新订单列表
//	@author: Salam
//	@Time: 2025-05-29 11:15:35
//	@receiver merchant *OrderV2Controller
//	@param c *gin.Context
func (merchant *OrderV2Controller) GetNewList(ctx *gin.Context) {
	var (
		redisHelper        = tools.GetRedisHelper()
		l, _               = ctx.Get("lang_util")
		lang               = l.(lang.LangUtil)
		restaurantId       = ctx.Query("restaurant_id")
		orderType          = ctx.DefaultQuery("order_type", "2")
		merchantService    = merchantService.NewMerchantService(ctx)
		orderV2Transformer = merchantTransformer.NewOrderV2Transformer(ctx)
		businessType       = 1
		orderToday         []models.OrderToday
		resAreaRedisKey    = "rark_" + restaurantId
		receiveOrderTime   int64
	)

	//  获取餐厅信息
	if exists, _ := redisHelper.Exists(ctx, resAreaRedisKey).Result(); exists == 0 {
		restaurant := merchantService.GetRestaurant(restaurantId)
		area := merchantService.GetArea(fmt.Sprintf("%d", restaurant.AreaID))
		redisHelper.Set(ctx, resAreaRedisKey, area.ReceiveOrderTime, 15*time.Minute)
	}
	receiveOrderTime, _ = redisHelper.Get(ctx, resAreaRedisKey).Int64()

	// 获取新订单
	orderToday = merchantService.GetNewOrders(restaurantId, int(receiveOrderTime), orderType, ctx)
	// 获取用户信息
	showCustomerInfo := merchantService.GetShowCustomerInfo(ctx, restaurantId)
	// 格式化返回数据
	formattedOrderToday := orderV2Transformer.FormatNewOrder(ctx, orderToday, int(receiveOrderTime), showCustomerInfo)
	// 获取新订单，已接收订单数量，失败订单，已完成订单，
	merchantService.GetOrderCountForHeader(restaurantId, businessType, &formattedOrderToday)
	// 获取是否有新的订单需要播放声音
	notifications := merchantService.GetVoiceOrderCount(restaurantId, businessType, &formattedOrderToday)

	merchantService.UpdateResLoginTimeAndOrderTime(restaurantId, ctx.Query("device_token"), notifications)
	ctx.JSON(200, gin.H{
		"data":   formattedOrderToday,
		"msg":    lang.T("msg"),
		"status": 200,
	})
}

// GetReceivedList
//
//	@Time 2025-05-29 11:15:44
//	<AUTHOR>
//	@Description: 获取商家已接单订单列表接口
//	@receiver merchant *OrderV2Controller
//	@param c
func (merchant *OrderV2Controller) GetReceivedList(ctx *gin.Context) {
	var (
		restaurantId = ctx.Query("restaurant_id")
		service      = merchantService.NewMerchantService(ctx)
		transformer  = merchantTransformer.NewOrderV2Transformer(ctx)
		redisHelper      = tools.GetRedisHelper()
		resAreaRedisKey  = "rark_" + restaurantId
		receiveOrderTime int64
	)
	list := service.GetRestaurantOrderListByState(ctx, restaurantId, []int{4, 5})

	// 获取餐厅信息
	if exists, _ := redisHelper.Exists(ctx, resAreaRedisKey).Result(); exists == 0 {
		restaurant := service.GetRestaurant(restaurantId)
		area := service.GetArea(fmt.Sprintf("%d", restaurant.AreaID))
		redisHelper.Set(ctx, resAreaRedisKey, area.ReceiveOrderTime, 15*time.Minute)
	}
	receiveOrderTime, _ = redisHelper.Get(ctx, resAreaRedisKey).Int64()

	// 获取用户信息
	showCustomerInfo := service.GetShowCustomerInfo(ctx, restaurantId)

	// 格式化
	data := transformer.FormatRestaurantOrderList(ctx, list, receiveOrderTime, showCustomerInfo, "Received")
	service.GetOrderCountForHeader(restaurantId, 1, &data)
	merchant.Success(ctx, data, "msg", 200)
}

// GetCompletedList
//
//	@Time 2025-05-29 11:15:54
//	<AUTHOR>
//	@Description: 获取餐厅当日已完成的订单列表（包括正在配送和已送完的订单）
//	@receiver merchant *OrderV2Controller
//	@param context
func (merchant *OrderV2Controller) GetCompletedList(ctx *gin.Context) {
	var (
		restaurantId = ctx.Query("restaurant_id")
		service      = merchantService.NewMerchantService(ctx)
		transformer  = merchantTransformer.NewOrderV2Transformer(ctx)

		redisHelper      = tools.GetRedisHelper()
		resAreaRedisKey  = "rark_" + restaurantId
		receiveOrderTime int64
	)
	list := service.GetRestaurantOrderListByState(ctx, restaurantId, []int{6, 7, 10})

	// 获取餐厅信息
	if exists, _ := redisHelper.Exists(ctx, resAreaRedisKey).Result(); exists == 0 {
		restaurant := service.GetRestaurant(restaurantId)
		area := service.GetArea(fmt.Sprintf("%d", restaurant.AreaID))
		redisHelper.Set(ctx, resAreaRedisKey, area.ReceiveOrderTime, 15*time.Minute)
	}
	receiveOrderTime, _ = redisHelper.Get(ctx, resAreaRedisKey).Int64()

	// 获取用户信息
	showCustomerInfo := service.GetShowCustomerInfo(ctx, restaurantId)

	// 格式化
	data := transformer.FormatRestaurantOrderList(ctx, list, receiveOrderTime, showCustomerInfo, "Completed")
	service.GetOrderCountForHeader(restaurantId, 1, &data)
	merchant.Success(ctx, data, "msg", 200)

}

// GetCanceledList
//
//	@Time 2025-05-29 11:15:35
//	<AUTHOR>
//	@Description: 获取餐厅当日已取消的订单列表（包括客户取消的和餐厅拒绝的订单）
//	@receiver merchant *OrderV2Controller
//	@param context
func (merchant *OrderV2Controller) GetCanceledList(ctx *gin.Context) {
	var (
		restaurantId = ctx.Query("restaurant_id")
		service      = merchantService.NewMerchantService(ctx)
		transformer  = merchantTransformer.NewOrderV2Transformer(ctx)
		redisHelper      = tools.GetRedisHelper()
		resAreaRedisKey  = "rark_" + restaurantId
		receiveOrderTime int64
	)
	list := service.GetRestaurantOrderListByState(ctx, restaurantId, []int{8, 9})

	// 获取餐厅信息
	if exists, _ := redisHelper.Exists(ctx, resAreaRedisKey).Result(); exists == 0 {
		restaurant := service.GetRestaurant(restaurantId)
		area := service.GetArea(fmt.Sprintf("%d", restaurant.AreaID))
		redisHelper.Set(ctx, resAreaRedisKey, area.ReceiveOrderTime, 15*time.Minute)
	}
	receiveOrderTime, _ = redisHelper.Get(ctx, resAreaRedisKey).Int64()

	// 获取用户信息
	showCustomerInfo := service.GetShowCustomerInfo(ctx, restaurantId)

	// 格式化
	data := transformer.FormatRestaurantOrderList(ctx, list, receiveOrderTime, showCustomerInfo, "Canceled")
	service.GetOrderCountForHeader(restaurantId, 1, &data)
	merchant.Success(ctx, data, "msg", 200)
}
