package merchant

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	"mulazim-api/requests/foodsMultipleDiscount"
	"mulazim-api/services"
	"mulazim-api/tools"
	"mulazim-api/transformers"
)

// FoodsMultipleDiscountController 多份打折活动控制器
type FoodsMultipleDiscountController struct {
	controllers.BaseController
}

// GetFoodsMultipleDiscount 获取多份打折活动
// @Summary 获取多份打折活动列表
// @Description 获取商家多份打折活动的列表数据
// @Tags 商家-多份打折活动
// @Accept json
// @Produce json
// @Param request query merchantRequests.FoodsMultipleDiscountListRequest false "查询参数"
// @Success 200 {object} controllers.Response{data=gin.H{items=[]merchantRequests.FoodsMultipleDiscountResponse,total=int}} "成功"
// @Router /merchant/foods-multiple-discount/list [get]
func (f *FoodsMultipleDiscountController) List(c *gin.Context) {

	var (
		request     foodsMultipleDiscount.FoodsMultipleDiscountListRequest
		service     = services.NewFoodsMultipleDiscountService(c)
		transformer = transformers.NewFoodsMultipleDiscountTransformer(c)
	)

	// 验证参数
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	// 获取当前登录的用户
	admin := permissions.GetAdmin(c)

	// 获取餐厅信息
	restaurantId, _ := permissions.GetRestaurantIdByMerchantAdmin(admin)

	// 获取分页信息
	pagination := tools.GetPagination(c)

	// 调用Service 获取数据
	list, total := service.GetMerchantFoodsMultipleDiscountList(request, pagination, restaurantId)

	// 调用Transformer 格式化数据
	items := transformer.FormatMerchantFoodsMultipleDiscountList(list)

	// 返回响应数据
	f.Success(c, gin.H{
		"items": items,
		"total": total,
	}, "success", 200)
}


// @Summary 获取多份打折活动列表
// @Description 获取商家多份打折活动的列表数据
// @Tags 商家-多份打折活动
// @Accept json
// @Produce json
// @Param request query merchantRequests.FoodsMultipleDiscountListRequest false "查询参数"
// @Success 200 {object} controllers.Response{data=gin.H{items=[]merchantRequests.FoodsMultipleDiscountResponse,total=int}} "成功"
// @Router /merchant/foods-multiple-discount/list [get]

func (f *FoodsMultipleDiscountController) Create(c *gin.Context) {
	// 获取请求参数
	var (
		request foodsMultipleDiscount.FoodsMultipleDiscountCreateRequest
		service = services.NewFoodsMultipleDiscountService(c)
	)

	// 绑定请求参数
	if err := c.ShouldBindJSON(&request); err != nil {
		panic(err)
	}

	// 获取当前管理员
	admin := permissions.GetAdmin(c)
	restaurant, _ := permissions.GetRestaurantInfoByContent(c)

	// 创建多份打折活动
	err := service.Create(request, admin, restaurant,true)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}

	// 返回成功响应
	f.Success(c, make([]int, 0), "success", 200)
}

// @Summary 创建多份打折活动
// @Description 创建商家多份打折活动
// @Tags 商家-多份打折活动
// @Accept json
// @Produce json
// @Param request body merchantRequests.FoodsMultipleDiscountCreateRequest true "创建参数"
// @Success 200 {object} controllers.Response{data=[]int} "成功"
// @Router /merchant/foods-multiple-discount/create [post]

func (f *FoodsMultipleDiscountController) ChangeState(c *gin.Context) {
	// 获取请求参数
	var (
		request foodsMultipleDiscount.FoodsMultipleDiscountChangeStateRequest
		service = services.NewFoodsMultipleDiscountService(c)
	)

	// 绑定请求参数
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	// 获取当前管理员
	admin := permissions.GetAdmin(c)

	// 更改活动状态
	err,state := service.ChangeState(request, admin)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}

	// 返回成功响应
	f.Success(c, state, "success", 200)
}

// @Summary 更改活动状态
// @Description 更改商家多份打折活动状态
// @Tags 商家-多份打折活动
// @Accept json
// @Produce json
// @Param request body merchantRequests.FoodsMultipleDiscountChangeStateRequest true "更改状态参数"
// @Success 200 {object} controllers.Response{data=int} "成功"
// @Router /merchant/foods-multiple-discount/change-state [get]

func (f *FoodsMultipleDiscountController) Detail(c *gin.Context) {
	// 获取请求参数 
	var (
		request foodsMultipleDiscount.FoodsMultipleDiscountDetailRequest
		service = services.NewFoodsMultipleDiscountService(c)
		transformer = transformers.NewFoodsMultipleDiscountTransformer(c)
	)
	restaurant, err := permissions.GetRestaurantInfoByContent(c)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}
	// 绑定请求参数
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
		return
	}
	// 获取多份打折活动详情
	detail, err, hasOrder := service.GetMerchantFoodsMultipleDiscountDetail(request,restaurant.ID)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}
	// 格式化
	formatDetail := transformer.FormatMerchantFoodsMultipleDiscountDetail(detail,hasOrder)
	// 返回成功响应
	f.Success(c, formatDetail, "success", 200)
}

// @Summary 获取多份打折活动详情
// @Description 获取商家多份打折活动详情
// @Tags 商家-多份打折活动
// @Accept json
// @Produce json
// @Param request query merchantRequests.FoodsMultipleDiscountDetailRequest true "获取详情参数"
// @Success 200 {object} controllers.Response{data=merchantRequests.FoodsMultipleDiscountDetailResponse} "成功"
// @Router /merchant/foods-multiple-discount/detail [post]

func (f *FoodsMultipleDiscountController) Delete(c *gin.Context) {
	// 获取请求参数
	var (
		request foodsMultipleDiscount.FoodsMultipleDiscountDeleteRequest
		service = services.NewFoodsMultipleDiscountService(c)
	)

	// 绑定请求参数
	if err := c.ShouldBind(&request); err != nil {
		panic(err)
	}

	// 获取当前管理员
	admin := permissions.GetAdmin(c)

	// 删除多份打折活动
	err := service.Delete(request, admin)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}

	// 返回成功响应
	f.Success(c, make([]int, 0), "success", 200)
}

// @Summary 删除多份打折活动
// @Description 删除商家多份打折活动
// @Tags 商家-多份打折活动
// @Accept json
// @Produce json
// @Param request body merchantRequests.FoodsMultipleDiscountDeleteRequest true "删除参数"
// @Success 200 {object} controllers.Response{data=[]int} "成功"
// @Router /merchant/foods-multiple-discount/delete [post]

func (f *FoodsMultipleDiscountController) Update(c *gin.Context) {
	// 获取请求参数
	var (
		request foodsMultipleDiscount.FoodsMultipleDiscountUpdateRequest
		service = services.NewFoodsMultipleDiscountService(c)
	)

	// 绑定请求参数
	if err := c.ShouldBindJSON(&request); err != nil {
		panic(err)
	}

	// 获取当前管理员
	admin := permissions.GetAdmin(c)

	// 更新多份打折活动
	err := service.Update(request, admin)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}

	// 返回成功响应
	f.Success(c, make([]int, 0), "success", 200)
}
