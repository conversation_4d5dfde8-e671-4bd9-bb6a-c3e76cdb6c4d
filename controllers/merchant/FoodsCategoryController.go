package merchant

import (
	"mulazim-api/controllers"
	merchantService "mulazim-api/services/merchant"
	merchantTransformer "mulazim-api/transformers/merchant"

	"github.com/gin-gonic/gin"
)

type FoodsCategoryController struct {
	controllers.BaseController
}

// List
//
// @Description: 分类列表
// @Author: Rixat
// @Time: 2024-09-25 13:21:51
// @receiver 
// @param c *gin.Context
func (f FoodsCategoryController) List(c *gin.Context) {
	type Params struct {
		Kw      string    `form:"kw" binding:""`
		State   *int    `form:"state" binding:""`
	}
	var (
		params      Params
		err 		= c.ShouldBind(&params)
		service     = merchantService.NewFoodsCategoryService(c)
		transformer = merchantTransformer.NewFoodsCategoryTransformer(c)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取分类列表
	categoryList := service.List(params.Kw,params.State)
	// 格式化分类列表
	format := transformer.ListFormat(categoryList)
	f.Success(c, format, "success", 200)
}
