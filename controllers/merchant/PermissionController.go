package merchant

import (
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/services/merchant"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type PermissionController struct {
	controllers.BaseController
}


// RoleList
//
// @Description: 获取角色列表
// @Author: Rixat
// @Time: 2024-05-16 13:26:33
// @receiver 
// @param c *gin.Context
func (s *PermissionController) RoleList(c *gin.Context) {
	var (
		permissionService = merchant.NewPermissionService(c)
	)

	admin := permissions.GetAdmin(c)
	if admin.MerchantType != 1 {
		s.Fail(c, "no_permission", 403)
		return
	}
	// 获取角色列表
	res := permissionService.GetStaffRules(admin)
	s.Success(c, res, "msg", 200)
}

// RoleTypes
//
// @Description: 角色类别
// @Author: Rixat
// @Time: 2024-05-16 13:26:45
// @receiver 
// @param c *gin.Context
func (s *PermissionController) RoleTypes(c *gin.Context) {
	var (
		permissionService = merchant.NewPermissionService(c)
	)
	admin := permissions.GetAdmin(c)
	if admin.MerchantType != 1 {
		s.Fail(c, "no_permission", 403)
		return
	}
	// 获取角色类型和所有权限
	ruleTypes := permissionService.GetRuleTypes()
	permissions := permissionService.GetPermissions()
	res := map[string]interface{}{
		"rule_type": ruleTypes,
		"permissions": permissions,
	}
	s.Success(c, res, "msg", 200)
}

// MerchantList
//
// @Description: 获取员工列表
// @Author: Rixat
// @Time: 2024-05-16 13:27:23
// @receiver 
// @param c *gin.Context
func (s *PermissionController) StaffList(c *gin.Context) {
	type Params struct {
		MerchantType int `form:"type" binding:"required"` // 员工类型
	}
	var (
		params Params
		err    = c.ShouldBind(&params)
		permissionService = merchant.NewPermissionService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	admin := permissions.GetAdmin(c)
	if admin.MerchantType != 1 {
		s.Fail(c, "no_permission", 403)
		return
	}
	// 获取员工商户
	staffs := permissionService.GetStaffsByAdmin(admin, params.MerchantType)
	var resMap []map[string]interface{}
	for _, value := range staffs {
		resMap = append(resMap, map[string]interface{}{
			"id":        value.ID,
			"avatar": value.Avatar,
			"real_name": value.RealName,
			"name": value.Name,
			"mobile":    value.Mobile,
			"state":     value.State,
		})
	}
	s.Success(c, resMap, "msg", 200)
}

// ChangeState
//
// @Description: 员工状态修改
// @Author: Rixat
// @Time: 2024-05-16 13:27:54
// @receiver 
// @param c *gin.Context
func (s *PermissionController) ChangeState(c *gin.Context) {
	type Params struct {
		ID    int    `form:"id" binding:"required"`    // 员工类型
		State string `form:"state" binding:"required"` // 员工类型
	}
	var (
		params Params
		err    = c.ShouldBind(&params)
		permissionService = merchant.NewPermissionService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}

	admin := permissions.GetAdmin(c)
	if admin.MerchantType != 1 {
		s.Fail(c, "no_permission", 403)
		return
	}

	var subMerchants []models.Admin
	err = tools.Db.Model(subMerchants).Where("id=?", params.ID).Update("state", params.State).Error
	if err != nil {
		s.Fail(c, "fail", -1000)
		return
	}
	// 清理登录token
	permissionService.ClearMerchantToken(params.ID)
	s.Ok(c)
}

// 格式化权限
//
// @Description: 
// @Author: Rixat
// @Time: 2024-05-16 13:28:18
// @receiver 
// @param c *gin.Context
func (s *PermissionController) FormatPermission(permissions []models.MerchantPermissionCategory,merchantPermissions []string) []map[string]interface{} {
	res := make([]map[string]interface{}, 0)
	for _, perm := range permissions {
		hasPermission := 0
		if tools.InArray(perm.Name, merchantPermissions){
			hasPermission = 1
		}
		res = append(res, map[string]interface{}{
			"id":    perm.ID,
			"name":  perm.NameUg,
			"state": hasPermission,
			"permission": perm.Name,
		})
	}
	return res
}

// MerchantInfo
//
// @Description: 获取员工信息
// @Author: Rixat
// @Time: 2024-05-16 13:28:36
// @receiver 
// @param c *gin.Context
func (s *PermissionController) StaffInfo(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"` // 员工类型
	}
	var (
		params Params
		err    = c.ShouldBind(&params)
		permissionService = merchant.NewPermissionService(c)
	)
	// 验证参数
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	if admin.MerchantType != 1 {
		s.Fail(c, "no_permission", 403)
		return
	}
	var staff models.Admin
	tools.Db.Model(staff).Where("id=?", params.ID).Find(&staff)
	if staff.ID == 0{
		s.Fail(c, "not_found", -1000)
		return
	}
	res := permissionService.GetStaffInfo(staff)
	s.Success(c, res, "", 200)
}



// Create
//
// @Description: 创建员工
// @Author: Rixat
// @Time: 2024-05-16 13:29:00
// @receiver 
// @param c *gin.Context
func (s *PermissionController) Create(c *gin.Context) {

	type Params struct {
		MerchantType int    `form:"merchant_type" json:"begin_time" binding:"required"`
		Name         string `form:"name" json:"name" binding:"required"`
		Mobile       string `form:"mobile" json:"mobile" `
		Password     string `form:"password" json:"password" binding:"required"`
		Permissions  string `form:"permissions" json:"permissions" binding:"required"`
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		permissionService = merchant.NewPermissionService(c)
	)
	if err != nil {
		panic(err)
		return
	}

	checkAdmin := permissionService.CheckAdminNameAndMobile(params.Name,params.Mobile)
	if checkAdmin.ID > 0{
		if params.Name == checkAdmin.Name{
			s.Fail(c,"name_already_exist",-1000)
		}else{
			s.Fail(c,"mobile_already_exist",-1000)
		}
        return;
        return;
	}
	// 验证密码
	checkPwd := permissionService.ValidatePassword(params.Password)
	if !checkPwd{
		s.Fail(c,"invalid_password",-1000)
		return
	}
	// 创建员工
	err = permissionService.StaffCreate(c,params.MerchantType,params.Name,params.Mobile,params.Password,params.Permissions)
	if err != nil {
		s.Fail(c,err.Error(),-1000)
        return;
	}
	s.Ok(c)

}

// Update
//
// @Description: 更新员工
// @Author: Rixat
// @Time: 2024-05-16 13:31:59
// @receiver 
// @param c *gin.Context
func (s *PermissionController) Update(c *gin.Context) {
	type Params struct {
		ID int    `form:"id" json:"id" binding:"required"`
		MerchantType int    `form:"merchant_type" json:"begin_time" binding:"required"`
		Name         string `form:"name" json:"name" binding:"required"`
		Mobile       string `form:"mobile" json:"mobile" `
		Password     string `form:"password" json:"password"`
		Permissions  string `form:"permissions" json:"permissions" binding:"required"`
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		permissionService = merchant.NewPermissionService(c)
	)
	if err != nil {
		panic(err)
		return
	}

	var admin models.Admin
	tools.Db.Model(models.Admin{}).Where("id",params.ID).First(&admin)
	if admin.ID == 0{
		s.Fail(c,"not_found",-1000)
		return;
	}

	// 验证更新的手机和名称是否存在
	checkAdmin := permissionService.CheckAdminNameAndMobile(params.Name,params.Mobile)
	if checkAdmin.ID > 0 && checkAdmin.ID != admin.ID{
		if params.Name == checkAdmin.Name{
			s.Fail(c,"name_already_exist",-1000)
		}else{
			s.Fail(c,"mobile_already_exist",-1000)
		}
        return;
	}
	// 验证密码
	if len(params.Password)>0 && !permissionService.ValidatePassword(params.Password){
		s.Fail(c,"invalid_password",-1000)
		return
	}

	// 创建员工
	err = permissionService.StaffUpdate(c,params.ID,params.MerchantType,params.Name,params.Mobile,params.Password,params.Permissions)
	if err != nil {
		s.Fail(c,err.Error(),-1000)
        return;
	}
	s.Ok(c)
}

// Delete
//
// @Description: 删除员工
// @Author: Rixat
// @Time: 2024-05-16 13:33:49
// @receiver 
// @param c *gin.Context
func (s *PermissionController) Delete(c *gin.Context) {

	type Params struct {
		Id int `form:"id" json:"id" binding:"required"`
	}
	var (
		params          Params
		err             = c.ShouldBind(&params)
		permissionService = merchant.NewPermissionService(c)
	)
	if err != nil {
		panic(err)
		return
	}

	// err = permissionService.CheckCanDelete(params.Id);
	// if err != nil {
	// 	s.Fail(c,err.Error(),-1000)
	// 	return
	// }

	// 删除员工
	err = permissionService.DeleteStaff(params.Id)
	if err != nil {
		s.Fail(c,err.Error(),-1000)
		return
	}
	s.Ok(c)
}
