package merchant

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"mime/multipart"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/errors"
	"mulazim-api/factory"
	"mulazim-api/models"
	"mulazim-api/resources/ocrEntity"
	merchantService "mulazim-api/services/merchant"
	lakalaService "mulazim-api/services/merchant/lakala"
	"mulazim-api/services/merchant/selfsign"
	"mulazim-api/tools"
	merchantTransformer "mulazim-api/transformers/merchant"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type CollectionInfoController struct {
	controllers.BaseController
}

var limitGoroutine chan int

func init() {
	limitGoroutine = make(chan int, 1)
}

// PostUploadFile
//
//	@Time 2023-01-10 13:00:52
//	<AUTHOR>
//	@Description: 上传文件
//	@receiver s SelfSignController
//	@param c
func (s CollectionInfoController) PostUploadFile(c *gin.Context) {
	type Params struct {
		File         *multipart.FileHeader `form:"file" binding:"required"`          // 营业执照
		DocType      string                `form:"doc_type" binding:"required"`      // 证件类型
		RestaurantId string                `form:"restaurant_id" binding:"required"` // 餐厅id
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchantService.NewCollectionInfoService(c)
	)
	if err != nil {
		panic(err)
		return
	}

	var fileMap map[string]string

	fileMap, errMsg := service.UploadFile(c, params.File, params.DocType, params.RestaurantId)
	fileFullPath := fileMap["fileFullPath"]
	fileUrl := configs.MyApp.CdnUrl + fileMap["fileUrl"]

	tools.Logger.Info("上传文件", fileFullPath)
	if errMsg != "" {
		tools.Logger.Error("上传文件错误", errMsg)
		s.Fail(c, errMsg, -1000)
		return
	} else {
		//图片类型
		// 0001	法人身份证
		// 0011	身份证反面
		// 0002	商户营业执照
		// 0003	商户税务登记证
		// 0004	组织机构代码证
		// 0005	开户许可证
		// 0007	手持身份证自拍照
		// 0008	辅助证明材料
		// 0013	辅助证明材料 1
		// 0014	辅助证明材料 2
		// 0015	室内照片
		// 0099	其他材料
		// 0025	银行卡正面照
		// 0026	银行卡背面照
		// 0032	民办非登记证

		//1001  食品经营许可证
		//1002  股东身份证前面
		//1003	股东身份证背面

		switch params.DocType {
		case "0001", "1002", "1005":
			{
				var idF ocrEntity.IDFront
				rs := tools.ShumaiOcr(1, fileFullPath)
				json.Unmarshal([]byte(rs), &idF)

				frontInfo := idF.Data.FrontInfo

				frontInfo.Image = fileUrl
				s.Success(c, frontInfo, "success", 200)
				return

			}
		case "0011", "1003", "1006":
			{
				var idF ocrEntity.IDBack
				rs := tools.ShumaiOcr(1, fileFullPath)

				json.Unmarshal([]byte(rs), &idF)

				info := idF.Data.Info
				info.Image = fileUrl

				//日期格式化
				//carbon.ParseByFormat(ShareholderIdcardStart, "Y-m-d")
				if len(info.Timelimit) > 0 {
					tms := strings.Split(info.Timelimit, "-")

					startTime := tms[0]

					endTime := tms[1]

					err1 := carbon.Parse(startTime).Error
					if err1 == nil {
						info.StartTime = carbon.Parse(startTime).ToFormatString("Y-m-d")
					}
					err2 := carbon.Parse(endTime).Error
					if err2 == nil {
						info.EndTime = carbon.Parse(endTime).ToFormatString("Y-m-d")
					} else {
						if endTime != "长期" {
							info.EndTime = ""
						}
					}

				}

				s.Success(c, info, "success", 200)
				return

			}
		case "0002":

			//注册类型  根据注册类型 判断 获取哪个数据
			reg_mer_type := c.DefaultPostForm("reg_mer_type", "01") //，默认 设置店铺 识别营业执照OCR
			//商户营业执照

			//企业:00  01:店铺
			if reg_mer_type == "00" {

				var bl ocrEntity.BusinessLicense

				rs := tools.ShumaiOcr(3, fileFullPath) //真实OCR

				db := tools.GetDB()
				json.Unmarshal([]byte(rs), &bl)
				rs2 := ""
				if len(bl.Data.RegNum) > 0 {
					bankNameMap := make(map[string]interface{}, 0)
					er11 := db.Table("self_sign_license_log").Where("reg_num = ?", bl.Data.RegNum).Select("content").Scan(&bankNameMap).Error
					if er11 == nil && (bankNameMap != nil && bankNameMap["content"] != nil) {
						rs2 = tools.ToString(bankNameMap["content"])
					} else {
						rs2 = tools.ShumaiCheckCompanyInfo(bl.Data.RegNum) //正式 天眼查数据
						var ci ocrEntity.CompanyInfo
						json.Unmarshal([]byte(rs2), &ci)
						if ci.Code == 200 {
							err3 := db.Table("self_sign_license_log").Create(&map[string]interface{}{
								"reg_num":    bl.Data.RegNum,
								"content":    rs2,
								"created_at": carbon.Now().ToDateTimeString(),
							}).Error
							if err3 != nil {
								tools.Log(err3.Error())
							}
						}

					}
				}

				var ci ocrEntity.CompanyInfo

				json.Unmarshal([]byte(rs2), &ci)

				type PartnerInfo struct {
					BnfName string `json:"bnf_name"`
				}
				type BusinessLic struct {
					RegisterAddress  string `json:"register_address"`
					RegisterNumber   string `json:"shop_license_num"`
					ShopName         string `json:"shop_name"`
					ShopLicenseStart string `json:"shop_license_start"`
					ShopLicenseEnd   string `json:"shop_license_end"`
					LegalName        string `json:"legal_name"`
					RegisterAmount   string `json:"reg_capital"`
					BusinessScope    string `json:"business_scope"`

					ShareholderName string `json:"shareholder_name"`

					ShareHolders []PartnerInfo `json:"shareholders"`
					Image        string        `json:"image"`
				}

				var bs BusinessLic

				baseInfo := ci.Data.Data.Base

				partners := ci.Data.Data.Partners

				bs.RegisterAddress = baseInfo.CompanyAddress
				bs.RegisterNumber = baseInfo.CreditNo
				bs.ShopName = baseInfo.CompanyName

				startTime := baseInfo.EstablishDate
				if len(startTime) > 0 {
					err1 := carbon.Parse(startTime).Error
					if err1 == nil {
						startTime = carbon.Parse(startTime).ToFormatString("Y-m-d")
					} else {
						startTime = ""
					}
				}
				bs.ShopLicenseStart = startTime
				bs.LegalName = baseInfo.LegalPerson
				bs.RegisterAmount = baseInfo.Capital

				validDate := bl.Data.ValidDate
				if len(validDate) > 0 {
					err1 := carbon.Parse(validDate).Error
					if err1 == nil {
						validDate = carbon.Parse(validDate).ToFormatString("Y-m-d")
					} else {
						if validDate != "长期" {
							validDate = ""
						}
					}
				}
				bs.ShopLicenseEnd = validDate

				bs.BusinessScope = baseInfo.BusinessScope

				var pis []PartnerInfo

				for i := 0; i < len(partners); i++ {
					var pi PartnerInfo
					pi.BnfName = partners[i].StockName
					pis = append(pis, pi)
				}
				bs.ShareHolders = pis
				bs.Image = fileUrl

				s.Success(c, bs, "success", 200)

				return

			} else {
				//01:店铺

				// rs :=tools.ShumaiOcr(3,fileFullPath) //真实OCR

				var bl ocrEntity.BusinessLicense
				rs := tools.ShumaiOcr(3, fileFullPath) //真实OCR

				json.Unmarshal([]byte(rs), &bl)

				info := bl.Data
				//营业执照数据
				type BusinessLic struct {
					RegisterAddress  string `json:"register_address"`
					RegisterNumber   string `json:"shop_license_num"`
					ShopName         string `json:"shop_name"`
					ShopLicenseStart string `json:"shop_license_start"`
					ShopLicenseEnd   string `json:"shop_license_end"`
					LegalName        string `json:"legal_name"`
					RegisterAmount   string `json:"reg_capital"`
					BusinessScope    string `json:"business_scope"`
					Image            string `json:"image"`
				}

				var bs BusinessLic

				bs.RegisterAddress = info.Address
				bs.RegisterNumber = info.RegNum
				bs.ShopName = info.Company
				startTime := info.EstablishDate
				if len(startTime) > 0 {
					err1 := carbon.Parse(startTime).Error
					if err1 == nil {
						startTime = carbon.Parse(startTime).ToFormatString("Y-m-d")
					} else {
						startTime = ""
					}

				}
				bs.ShopLicenseStart = startTime
				bs.LegalName = info.LegalPerson
				bs.RegisterAmount = info.Capital
				bs.BusinessScope = info.Business
				validDate := info.ValidDate
				if len(validDate) > 0 {
					err1 := carbon.Parse(validDate).Error
					if err1 == nil {
						validDate = carbon.Parse(validDate).ToFormatString("Y-m-d")
					} else {
						if validDate != "长期" {
							validDate = ""
						}
					}
				}
				bs.ShopLicenseEnd = validDate

				bs.Image = fileUrl
				s.Success(c, bs, "success", 200)

				return

			}
		case "0025":
			//银行卡
			var bc ocrEntity.BankCardEntity
			rs := tools.ShumaiOcr(2, fileFullPath)

			json.Unmarshal([]byte(rs), &bc)

			info := bc.Data

			db := tools.GetDB()
			if len(info.CardNumber) > 0 {
				info.IsBankCard = tools.CheckBankCardNumber(info.CardNumber)
				if info.IsBankCard {
					merService := merchantService.NewMerchantService(c)
					resData := merService.GetRestaurant(params.RestaurantId)

					cityCode := ""
					areaCode := ""

					if resData.ID > 0 {
						cityMap := make(map[string]interface{}, 0)
						db.Table("b_self_sign_area").Where("mlz_city_id = ?", resData.CityID).Select("level,name_zh,code,p_code").Scan(&cityMap)
						if cityMap != nil && cityMap["code"] != nil {
							cityCode = tools.ToString(cityMap["code"])
						}
						areaMap := make(map[string]interface{}, 0)
						db.Table("b_self_sign_area").Where("mlz_area_id = ?", resData.AreaID).Select("level,name_zh,code,p_code").Scan(&areaMap)
						if areaMap != nil && areaMap["code"] != nil {
							areaCode = tools.ToString(areaMap["code"])
						}

					}
					bankNameMap := make(map[string]interface{}, 0)
					er11 := db.Table("self_sign_bank_card_log").Where("card_number = ?", info.CardNumber).Select("bank_id,province_code,city_code,area_code").Scan(&bankNameMap).Error
					if er11 == nil && (bankNameMap != nil && bankNameMap["bank_id"] != nil) {
						info.BankId = tools.ToInt(bankNameMap["bank_id"])
						info.ProvinceCode = tools.ToInt(bankNameMap["province_code"])
						info.CityCode = tools.ToInt(bankNameMap["city_code"])
						info.AreaCode = tools.ToInt(bankNameMap["area_code"])
					} else {
						rs2, er := tools.BankRegion(info.CardNumber)
						if er == nil {
							var bcr ocrEntity.BankCardRegionEntity
							json.Unmarshal([]byte(rs2), &bcr)
							info2 := bcr.Data
							if len(info2.Bank) > 0 {

								bankId := 0

								if info2.Bank == "乌鲁木齐市商业银行" {
									info2.Bank = "乌鲁木齐银行"
								}
								var bank models.SelfSignBank
								er4 := db.Where("name_zh like ?", info2.Bank+"%").Select("id").First(&bank).Error
								if er4 == nil && bank.ID > 0 {
									bankId = bank.ID
								} else {
									//不存在的银行写一个数据
									bk := models.SelfSignBank{
										NameUg:    info2.Bank,
										NameZh:    info2.Bank,
										State:     1,
										Index:     1,
										CreatedAt: carbon.Now().Carbon2Time(),
									}
									db.Table("b_self_sign_bank").Create(&bk)
									bankId = bk.ID
									tools.SendDingDingMsg("新增银行数据:" + info2.Bank)

								}
								info.BankId = bankId

								if len(info2.City) > 0 {

									city2Map := make(map[string]interface{}, 0)
									if strings.Contains(info2.City, "地区") {
										info2.City = strings.Split(info2.City, "地区")[0]
									} else if strings.Contains(info2.City, "市") {
										info2.City = strings.Split(info2.City, "市")[0]
									} else if strings.Contains(info2.City, "州") {
										info2.City = strings.Split(info2.City, "州")[0]
									}
									db.Table("b_self_sign_area").Where("level = 2 and name_zh like ?", info2.City+"%").Select("level,name_zh,code,p_code").Order("id asc").Limit(1).Scan(&city2Map)
									if city2Map != nil && city2Map["code"] != nil {

										cityCode2 := tools.ToString(city2Map["code"])
										if cityCode == cityCode2 {
											info.CityCode = tools.ToInt(cityCode)
											info.AreaCode = tools.ToInt(areaCode)
										} else {
											info.CityCode = tools.ToInt(cityCode2)
										}
									}

									if len(info2.Province) > 0 {
										provinceMap := make(map[string]interface{}, 0)
										if strings.Contains(info2.Province, "新疆") {
											info2.Province = "新疆"
										}
										db.Table("b_self_sign_area").Where("level = 1 and name_zh like ?", info2.Province+"%").Select("level,name_zh,code,p_code").Scan(&provinceMap)
										if provinceMap != nil && provinceMap["code"] != nil {
											info.ProvinceCode = tools.ToInt(provinceMap["code"])
										}
									}

									err3 := db.Table("self_sign_bank_card_log").Create(&map[string]interface{}{
										"card_number":   info.CardNumber,
										"bank_name":     info2.Bank,
										"bank_id":       bankId,
										"province_code": info.ProvinceCode,
										"city_code":     info.CityCode,
										"area_code":     info.AreaCode,
										"created_at":    carbon.Now().ToDateTimeString(),
									}).Error
									if err3 != nil {
										tools.Log(err3.Error())
									}

								}

							}
						}
					}

				}
			}

			info.Image = fileUrl
			s.Success(c, info, "success", 200)
			return

		case "1001":
			//1001  食品经营许可证

			rs := tools.AliOcrFoodLicense(fileFullPath)

			var result map[string]interface{}
			json.Unmarshal([]byte(rs), &result)

			spo := 0
			if result != nil {
				spo = len(result["食品经营许可证识别状态"].(string))
			}

			if spo == 0 {

				result2 := map[string]string{
					"status":        "",
					"main_status":   "",
					"address":       "",
					"cert_office":   "",
					"daily_manager": "",
					"daily_office":  "",
					"cert_end":      "",
					"legal_person":  "",
					"cert_number":   "",
					"cert_person":   "",
					"real_address":  "",
					"run_person":    "",
					"permit_number": "",
				}

				result2["image"] = fileUrl

				s.Success(c, result2, "success", 200)

				return

			} else {

				baseInfo := result["食品经营许可证实体信息"].(map[string]interface{})

				result2 := map[string]string{
					"status":        result["食品经营许可证识别状态"].(string),
					"main_status":   baseInfo["主体业态"].(string),
					"address":       baseInfo["住所"].(string),
					"cert_office":   baseInfo["发证机关"].(string),
					"daily_manager": baseInfo["日常监督管理人员"].(string),
					"daily_office":  baseInfo["日常监督管理机构"].(string),
					"cert_end":      baseInfo["有效期限至"].(string),
					"legal_person":  baseInfo["法定代表人（负责人）"].(string),
					"cert_number":   baseInfo["社会信用代码（身份证号码）"].(string),
					"cert_person":   baseInfo["签发人"].(string),
					"real_address":  baseInfo["经营场所"].(string),
					"run_person":    baseInfo["经营者名称"].(string),
					"permit_number": baseInfo["许可证编号"].(string),
				}

				result2["image"] = fileUrl

				s.Success(c, result2, "success", 200)

				return
			}

		}
		info := map[string]string{
			"image": fileUrl,
		}
		s.Success(c, info, "success", 200)

	}

}

// PostShopLicenseInfo
//
//	@Time 2023-01-09 13:59:04
//	<AUTHOR>
//	@Description:  第1页  店铺信息 Shop information
//	@receiver s SelfSignController
//	@param c
func (s CollectionInfoController) PostShopLicenseInfo(c *gin.Context) {
	var (
		value, _   = c.Get("admin")
		regMerType = c.PostForm("reg_mer_type")
		admin      = value.(models.Admin)
		mService   = merchantService.NewMerchantService(c)
		cService   = merchantService.NewCollectionInfoService(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	tools.Logger.Info("第一页提交信息resInfo", resInfo)
	restId := resInfo["id"].(int)
	areaId := resInfo["area_id"].(int)
	cityId := resInfo["city_id"].(int)
	state, i := cService.CheckColMerInfoState(restId)
	if !state {
		panic(errors.CustomError{Msg: "merInfoaudit" + strconv.Itoa(i)})
	}
	if regMerType == "00" { //企业
		type ParamsOfCompany struct {
			LicenseImg                   string `form:"license_img" binding:"required"`
			RegMerType                   string `form:"reg_mer_type" binding:"required"`                     // 注册商户类型
			ShopLicenseNum               string `form:"shop_license_num" binding:"required"`                 // 营业执照号
			ShopName                     string `form:"shop_name" binding:"required"`                        // 店铺名称
			LegalName                    string `form:"legal_name" binding:"required"`                       // 法人姓名
			RegAddress                   string `form:"reg_address" binding:"required"`                      // 注册地址
			RegCapital                   string `form:"reg_capital"`                                         // 注册资本
			BusinessScope                string `form:"business_scope" `                                     // 经营范围
			ShopLicenseLimitedType       string `form:"shop_license_limited_type" binding:"required"`        // 有限责任公司类型
			ShopLicenseStart             string `form:"shop_license_start" binding:"required"`               // 营业执照有效期开始时间
			ShopLicenseEnd               string `form:"shop_license_end" binding:"required"`                 // 营业执照有效期结束时间
			ShareholderName              string `form:"shareholder_name" binding:"required" `                // 股东姓名
			ShareholderIdcard            string `form:"shareholder_idcard" binding:"required" `              // 股东身份证号
			ShareholderAddress           string `form:"shareholder_address" binding:"required"`              // 控股股东家庭地址
			ShareholderIdcardLimitedType string `form:"shareholder_idcard_limited_type" binding:"required" ` // 控股股东身份证有限期类型( 0：短期 1：长期)
			ShareholderIdcardStart       string `form:"shareholder_idcard_start" binding:"required"`         // 控股股东身份证起效日期
			ShareholderIdcardEnd         string `form:"shareholder_idcard_end" binding:"required" `          // 控股股东身份证失效日期，如果长期：9999-12-31
			Bnf                          string `form:"bnf" binding:"required" `                             // 受益人信息
		}
		var params ParamsOfCompany
		err := c.ShouldBind(&params)
		println(params.Bnf)
		if err != nil {
			tools.Logger.Error("数据绑定失败", err.Error())
			panic(err)
			return
		}
		ok := cService.SaveCompanyLicenseInfo(restId, cityId, areaId, params.LicenseImg, params.RegMerType, params.ShopLicenseNum, params.ShopName, params.LegalName, params.RegAddress, params.RegCapital, params.BusinessScope, params.ShopLicenseLimitedType, params.ShopLicenseStart, params.ShopLicenseEnd, params.ShareholderName, params.ShareholderIdcard, params.ShareholderAddress, params.ShareholderIdcardLimitedType, params.ShareholderIdcardStart, params.ShareholderIdcardEnd, params.Bnf)
		if ok {
			s.Ok(c)
		} else {
			s.Fail(c, "error_happend", -1000)
		}
		return
	} else if regMerType == "01" { //个体户
		type ParamsOfShop struct {
			LicenseImg             string `form:"license_img" binding:"required"`
			RegMerType             string `form:"reg_mer_type" binding:"required"`              // 注册商户类型
			ShopLicenseNum         string `form:"shop_license_num" binding:"required"`          // 营业执照号
			ShopName               string `form:"shop_name" binding:"required"`                 // 店铺名称
			LegalName              string `form:"legal_name" binding:"required"`                // 法人姓名
			RegAddress             string `form:"reg_address" binding:"required"`               // 注册地址
			RegCapital             string `form:"reg_capital"`                                  // 注册资本
			BusinessScope          string `form:"business_scope"`                               // 经营范围
			ShopLicenseLimitedType string `form:"shop_license_limited_type" binding:"required"` // 有限责任公司类型
			ShopLicenseStart       string `form:"shop_license_start" binding:"required"`        // 营业执照有效期开始时间
			ShopLicenseEnd         string `form:"shop_license_end" binding:"required"`          // 营业执照有效期结束时间
		}
		var params ParamsOfShop
		err := c.ShouldBind(&params)
		if err != nil {
			panic(err)
			return
		}
		isOK, er := cService.SaveShopLicenseInfo(restId, cityId, areaId, params.LicenseImg, params.RegMerType, params.ShopLicenseNum, params.ShopName, params.LegalName, params.RegAddress, params.RegCapital, params.BusinessScope, params.ShopLicenseLimitedType, params.ShopLicenseStart, params.ShopLicenseEnd)
		if isOK {
			s.Ok(c)
		} else {
			s.Fail(c, er, -1000)
		}
		return
	} else if regMerType == "02" { // 微商户
		type ParamsOfShop struct {
			LicenseImg string `form:"license_img" binding:"required"`  //
			RegMerType string `form:"reg_mer_type" binding:"required"` // 注册商户类型
			ShopName   string `form:"shop_name" binding:"required"`    // 店铺名称
		}
		var params ParamsOfShop
		err := c.ShouldBind(&params)
		if err != nil {
			panic(err)
			return
		}
		isOK, er := cService.SaveShopLicenseInfo(restId, cityId, areaId, params.LicenseImg, params.RegMerType, "", params.ShopName, "", "", "", "", "", "", "")
		if isOK {
			s.Ok(c)
		} else {
			s.Fail(c, er, -1000)
		}
		return
	} else {
		s.Fail(c, "error_happend", -1000)
	}
	return

	//cService.SaveShopLicenseInfo(resInfo["id"].(int), params.RegMerType, params.ShopLicenseNum, params.ShopName, params.LegalName, params.RegAddress, params.RegCapital, params.BusinessScope, params.ShopLicenseLimitedType, params.ShopLicenseStart, params.ShopLicenseEnd, params.ShareholderName, params.ShareholderIdcard, params.ShareholderAddress, params.ShareholderIdcardLimitedType, params.ShareholderIdcardStart, params.ShareholderIdcardEnd, params.Bnf)
	//s.Ok(c)

}

// PostIdCardInfo
//
//	@Time 2023-01-09 13:59:34
//	<AUTHOR>
//	@Description: 第2页 营业环境 Business environment
//	@receiver s SelfSignController
//	@param c
func (s CollectionInfoController) PostIdCardInfo(c *gin.Context) {
	type Params struct {
		IdcardImg         string `form:"idcard_img" binding:"required"`
		MerIdcardName     string `form:"mer_idcard_name" binding:"required"`       // 身份证姓名
		MerIdcardNum      string `form:"mer_idcard_num" binding:"required,len=18"` // 身份证号
		MerIdcardStart    string `form:"mer_idcard_start" binding:"required"`      // 身份证有效期开始时间
		MerIdcardEnd      string `form:"mer_idcard_end" binding:"required"`        // 身份证有效期结束时间
		MerIsBnf          string `form:"mer_is_bnf" `                              // 是否受益人
		MerMobile         string `form:"mer_mobile" `                              // 手机号
		MerIdcardTimeType string `form:"mer_idcard_time_type" binding:"required"`  // 身份证有效期类型
		LegalHomeAddress  string `form:"legal_home_address" `                      // 验证码
		MerSex            int    `form:"mer_sex" `                                 //性别

		// Captcha           string `form:"captcha" binding:"required"`              // 验证码
	}
	var (
		params   Params
		err      = c.ShouldBind(&params)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		mService = merchantService.NewMerchantService(c)
		cService = merchantService.NewCollectionInfoService(c)
		redis    = tools.GetRedisHelper()
	)
	if err != nil {
		panic(err)
		return
	}

	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)

	idCardInfo, _ := cService.GetIdCardInfo(resInfo["id"].(int))
	state, i := cService.CheckColMerInfoState(resInfo["id"].(int))
	if !state {
		panic(errors.CustomError{Msg: "merInfoaudit" + strconv.Itoa(i)})
	}

	if len(params.MerMobile) > 0 {

		Captcha := c.PostForm("captcha")

		if configs.CurrentEnvironment == "production" { //正式环境
			if idCardInfo.MerMobile != params.MerMobile {

				//手机号不同的话需要要求验证码

				// 验证码
				cacheKey := fmt.Sprintf("shop_mobile_%s", params.MerMobile)

				if len(Captcha) == 0 {
					s.Fail(c, "captcha_requiered", -1000)
					return
				}
				get := redis.Get(c, cacheKey)
				if get.Val() != Captcha {
					s.Fail(c, "captcha_incorrect", -1000)
					return
				}

			}
		} else {
			// 测试过程中 验证码 999999 通过
			if Captcha != "999999" {

				if idCardInfo.MerMobile != params.MerMobile {

					//手机号不同的话需要要求验证码

					// 验证码
					cacheKey := fmt.Sprintf("shop_mobile_%s", params.MerMobile)

					if len(Captcha) == 0 {
						s.Fail(c, "captcha_requiered", -1000)
						return
					}
					get := redis.Get(c, cacheKey)
					if get.Val() != Captcha {
						s.Fail(c, "captcha_incorrect", -1000)
						return
					}

				}
			}
		}
	}

	if idCardInfo.RegMerType == "02" && params.MerSex == 0 { //小微商户
		s.Fail(c, "mini_shop_must_fill_gender", -1000)
		return
	}
	ok, er := cService.SaveIdCardInfo(resInfo["id"].(int), params.IdcardImg, params.MerIdcardName, params.MerIdcardNum, params.MerIdcardStart, params.MerIdcardEnd, params.MerIsBnf, params.MerMobile, params.MerIdcardTimeType, params.LegalHomeAddress, params.MerSex)
	if ok {
		s.Ok(c)
	} else {
		s.Fail(c, er, -1000)
	}
}

// PostAccountInfo
//
//	@Time 2023-01-11 19:01:59
//	<AUTHOR>
//	@Description: 第3页 账户信息 Account information
//	@receiver s SelfSignController
//	@param c
func (s CollectionInfoController) PostAccountInfo(c *gin.Context) {
	type Params struct {
		AcctImg        string `form:"acct_img" binding:"required"`
		BankAcctType   *int   `form:"bank_acct_type" binding:"required"`          // 银行账户类型
		BankAcctNum    string `form:"bank_acct_num" binding:"required"`           // 银行账户号
		BankId         int    `form:"bank_id" binding:"required"`                 // 开户银行名称
		BankProvinceId int    `form:"bank_province_id" binding:"required"`        // 开户银行所在省份
		BankCityId     int    `form:"bank_city_id" binding:"required"`            // 开户银行所在市
		BankAreaId     int    `form:"bank_area_id" binding:"required"`            // 开户银行所在区
		BankBranchName string `form:"bank_branch_name" binding:"required"`        // 开户银行支行名称
		BankBranchCode string `form:"bank_branch_code" binding:"required"`        // 开户银行支行联行号
		BankBindMobile string `form:"bank_bind_mobile" binding:"required,len=11"` // 银行预留手机号
	}
	var (
		params   Params
		err      = c.ShouldBind(&params)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		mService = merchantService.NewMerchantService(c)
		cService = merchantService.NewCollectionInfoService(c)
		redis    = tools.GetRedisHelper()
	)
	if err != nil {
		tools.Logger.Info("参数绑定失败!", err)
		panic(err)
		return
	}
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	state, i := cService.CheckColMerInfoState(resInfo["id"].(int))
	if !state {
		panic(errors.CustomError{Msg: "merInfoaudit" + strconv.Itoa(i)})
	}
	idCardInfo, _ := cService.GetIdCardInfo(resInfo["id"].(int))
	if len(params.BankBindMobile) > 0 {

		Captcha := c.PostForm("captcha")

		if configs.CurrentEnvironment == "production" { //正式环境
			if idCardInfo.BankBindMobile != params.BankBindMobile {

				//手机号不同的话需要要求验证码

				// 验证码
				cacheKey := fmt.Sprintf("shop_mobile_%s", params.BankBindMobile)

				if len(Captcha) == 0 {
					s.Fail(c, "captcha_requiered", -1000)
					return
				}
				get := redis.Get(c, cacheKey)
				if get.Val() != Captcha {
					s.Fail(c, "captcha_incorrect", -1000)
					return
				}

			}
		} else {
			// 测试过程中 验证码 999999 通过
			if Captcha != "999999" {

				if idCardInfo.BankBindMobile != params.BankBindMobile {

					//手机号不同的话需要要求验证码

					// 验证码
					cacheKey := fmt.Sprintf("shop_mobile_%s", params.BankBindMobile)

					if len(Captcha) == 0 {
						s.Fail(c, "captcha_requiered", -1000)
						return
					}
					get := redis.Get(c, cacheKey)
					if get.Val() != Captcha {
						s.Fail(c, "captcha_incorrect", -1000)
						return
					}

				}
			}
		}
	}
	ok, msg := cService.SaveAccountInfo(resInfo["id"].(int), params.AcctImg, *params.BankAcctType, params.BankAcctNum, params.BankId, params.BankCityId, params.BankAreaId, params.BankBranchName, params.BankBranchCode, params.BankProvinceId, params.BankBindMobile)
	if ok {
		s.Ok(c)
	} else {
		m := "error_happend"
		if len(msg) > 0 {
			m = msg
		}
		s.Fail(c, m, -1000)
	}
}

// PostShopInfo
//
//	@Time 2023-01-11 19:02:10
//	<AUTHOR>
//	@Description: 第4页 店铺信息 Shop information
//	@receiver s SelfSignController
//	@param c
func (s CollectionInfoController) PostShopInfo(c *gin.Context) {
	type Params struct {
		ShopImg                string `form:"shop_img" binding:"required"`
		ShopBusinessName       string `form:"shop_business_name" binding:"required"`         // 店铺名称
		ShopBusinessCity       int    `form:"shop_business_city" binding:"required"`         // 店铺所在市
		ShopBusinessArea       int    `form:"shop_business_area" binding:"required"`         // 店铺所在区
		ShopBusinessAddress    string `form:"shop_business_address" binding:"required"`      // 店铺详细地址
		ShopCategoryCode       string `form:"shop_category_code" binding:"required"`         // 支付产品
		ShopBusinessProvinceId string `form:"shop_business_province_id"  binding:"required"` // 省份id
	}
	var (
		params   Params
		err      = c.ShouldBind(&params)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		mService = merchantService.NewMerchantService(c)
		cService = merchantService.NewCollectionInfoService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	state, i := cService.CheckColMerInfoState(resInfo["id"].(int))
	if !state {
		panic(errors.CustomError{Msg: "merInfoaudit" + strconv.Itoa(i)})
	}
	tools.Logger.Info("第4页 店铺信息 ", params)
	ok := cService.SaveShopInfo(resInfo["id"].(int), params.ShopImg, params.ShopBusinessName, params.ShopBusinessCity, params.ShopBusinessArea, params.ShopBusinessAddress, params.ShopCategoryCode, params.ShopBusinessProvinceId)
	if ok {
		s.Ok(c)
	} else {
		s.Fail(c, "error_happend", -1000)
	}
}

// GetShopLicenseInfo
//
//	@Time 2023-01-12 11:17:11
//	<AUTHOR>
//	@Description: 获取第一页的信息
//	@receiver s CollectionInfoController
//	@param c
func (s CollectionInfoController) GetShopLicenseInfo(c *gin.Context) {
	var (
		value, _    = c.Get("admin")
		admin       = value.(models.Admin)
		mService    = merchantService.NewMerchantService(c)
		cService    = merchantService.NewCollectionInfoService(c)
		transformer = merchantTransformer.NewCollectionInfoTransformer(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	shopLicenseInfo, collectionMerchantBnf, licenseImg := cService.GetShopLicenseInfo(resInfo["id"].(int))
	data := transformer.FormatShopLicenseInfo(shopLicenseInfo, collectionMerchantBnf, licenseImg)
	s.Success(c, data, "msg", 200)
}

// GetIdCardInfo
//
//	@Time 2023-01-12 16:39:31
//	<AUTHOR>
//	@Description: 获取第二页的信息
//	@receiver s CollectionInfoController
//	@param c
func (s CollectionInfoController) GetIdCardInfo(c *gin.Context) {
	var (
		value, _    = c.Get("admin")
		admin       = value.(models.Admin)
		mService    = merchantService.NewMerchantService(c)
		cService    = merchantService.NewCollectionInfoService(c)
		transformer = merchantTransformer.NewCollectionInfoTransformer(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	idCardInfo, idcardsImg := cService.GetIdCardInfo(resInfo["id"].(int))
	data := transformer.FormatIdCardInfo(idCardInfo, idcardsImg)
	s.Success(c, data, "msg", 200)
}

// GetAccountInfo
//
//	@Time 2023-01-12 16:39:36
//	<AUTHOR>
//	@Description: 获取第三页的信息
//	@receiver s CollectionInfoController
//	@param c
func (s CollectionInfoController) GetAccountInfo(c *gin.Context) {
	var (
		value, _    = c.Get("admin")
		admin       = value.(models.Admin)
		mService    = merchantService.NewMerchantService(c)
		cService    = merchantService.NewCollectionInfoService(c)
		transformer = merchantTransformer.NewCollectionInfoTransformer(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	accountInfo, bankCardsImg, bankNames := cService.GetAccountInfo(resInfo["id"].(int), resInfo["area_id"].(int))
	var data = transformer.FormatAccountInfo(accountInfo, bankCardsImg, bankNames)
	s.Success(c, data, "msg", 200)
}

// GetShopInfo
//
//	@Time 2023-01-12 18:16:39
//	<AUTHOR>
//	@Description: 获取第四页的信息
//	@receiver s CollectionInfoController
//	@param c
func (s CollectionInfoController) GetShopInfo(c *gin.Context) {
	var (
		value, _    = c.Get("admin")
		admin       = value.(models.Admin)
		mService    = merchantService.NewMerchantService(c)
		cService    = merchantService.NewCollectionInfoService(c)
		transformer = merchantTransformer.NewCollectionInfoTransformer(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	if resInfo == nil {
		//店铺数据不存在
		s.Fail(c, "restaurant_not_found", -1000)
		return

	}
	shopInfo, shopImages, categoryList := cService.GetShopInfo(resInfo["id"].(int))
	data := transformer.FormatShopInfo(shopInfo, shopImages, categoryList)
	s.Success(c, data, "msg", 200)
}

// PostAdministrator
//
//	@Time 2023-01-12 19:07:23
//	<AUTHOR>
//	@Description: 第5页 管理员信息 Administrator information
//	@receiver s CollectionInfoController
//	@param c
func (s CollectionInfoController) PostAdministrator(c *gin.Context) {
	shopManagerType := c.PostForm("shop_manager_type")
	if shopManagerType == "1" { // 法人
		type Params struct {
			ShopManagerType   int    `form:"shop_manager_type" binding:"required"`          // 管理员类型
			ShopManagerName   string `form:"shop_manager_name" binding:"required"`          // 管理员姓名
			ShopManagerIdcard string `form:"shop_manager_idcard" binding:"required,len=18"` // 管理员身份证号
			ShopManagerMobile string `form:"shop_manager_mobile" binding:"required,len=11"` // 管理员手机号
			ShopManagerEmail  string `form:"shop_manager_email" `                           // 管理员邮箱
			AdminImg          string `form:"admin_img" binding:"required" `                 // 管理员身份证照片
		}
		var (
			params   Params
			err      = c.ShouldBind(&params)
			value, _ = c.Get("admin")
			admin    = value.(models.Admin)
			mService = merchantService.NewMerchantService(c)
			cService = merchantService.NewCollectionInfoService(c)
		)
		if err != nil {
			panic(err)
			return
		}
		resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
		state, i := cService.CheckColMerInfoState(resInfo["id"].(int))
		if !state {
			panic(errors.CustomError{Msg: "merInfoaudit" + strconv.Itoa(i)})
		}
		ok := cService.SaveAdministrator(resInfo["id"].(int), params.AdminImg, params.ShopManagerType, params.ShopManagerName, params.ShopManagerIdcard, params.ShopManagerMobile, params.ShopManagerEmail)
		if ok {
			s.Ok(c)
		} else {
			s.Fail(c, "error_happen", -1000)
		}
	} else if shopManagerType == "2" { // 其他
		type Params struct {
			ShopManagerType   int    `form:"shop_manager_type" binding:"required"`          // 管理员类型
			ShopManagerName   string `form:"shop_manager_name" binding:"required"`          // 管理员姓名
			ShopManagerIdcard string `form:"shop_manager_idcard" binding:"required,len=18"` // 管理员身份证号
			ShopManagerMobile string `form:"shop_manager_mobile" binding:"required,len=11"` // 管理员手机号
			ShopManagerEmail  string `form:"shop_manager_email" `                           // 管理员邮箱
			AdminImg          string `form:"admin_img" binding:"required"`                  // 管理员身份证照片
		}
		var (
			params   Params
			err      = c.ShouldBind(&params)
			value, _ = c.Get("admin")
			admin    = value.(models.Admin)
			mService = merchantService.NewMerchantService(c)
			cService = merchantService.NewCollectionInfoService(c)
		)
		if err != nil {
			panic(err)
			return
		}
		resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
		ok := cService.SaveAdministrator(resInfo["id"].(int), params.AdminImg, params.ShopManagerType, params.ShopManagerName, params.ShopManagerIdcard, params.ShopManagerMobile, params.ShopManagerEmail)
		if ok {
			s.Ok(c)
		} else {
			s.Fail(c, "error_happen", -1000)
		}
	}

}

// PostBusinessLicense
//
//	@Time 2023-01-13 10:04:24
//	<AUTHOR>
//	@Description: 第6页  食品经营许可证 Business license
//	@receiver s CollectionInfoController
//	@param c
func (s CollectionInfoController) PostBusinessLicense(c *gin.Context) {
	type Params struct {
		PermitStartDate         string `form:"permit_start_date" binding:"required"`         // 食品经营许可证有效期开始时间
		PermitEndDate           string `form:"permit_end_date" binding:"required"`           // 食品经营许可证有效期结束时间
		PermitShopName          string `form:"permit_shop_name" binding:"required"`          // 食品经营许可证上的名称
		PermitLicNum            string `form:"permit_lic_num" binding:"required"`            // 食品经营许可证编号
		PermitCreditCode        string `form:"permit_credit_code" binding:"required"`        // 统一社会信用代码
		PermitLegalName         string `form:"permit_legal_name" binding:"required"`         // 法定代表人姓名
		PermitShopAddr          string `form:"permit_shop_addr" binding:"required"`          // 营业执照地址
		PermitBusinessPremises  string `form:"permit_business_premises" binding:"required"`  // 营业执照经营场所
		PermitState             string `form:"permit_state" binding:"required"`              // 主体业态
		PermitBusinessType      string `form:"permit_business_type" binding:"required"`      // 经营业态
		PermitSuperviseOrg      string `form:"permit_supervise_org" binding:"required"`      // 监管机构
		PermitSuperviseManagers string `form:"permit_supervise_managers" binding:"required"` // 监管人员
		PermitIssuingAuthority  string `form:"permit_issuing_authority" binding:"required"`  // 发证机关
		ImgSrc                  string `form:"img_src" binding:"required"`                   // 图片地址
	}
	var (
		params   Params
		err      = c.ShouldBind(&params)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		mService = merchantService.NewMerchantService(c)
		cService = merchantService.NewCollectionInfoService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	state, i := cService.CheckColMerInfoState(resInfo["id"].(int))
	if !state {
		panic(errors.CustomError{Msg: "merInfoaudit" + strconv.Itoa(i)})
		return
	}
	result,msg := cService.SaveBusinessLicense(resInfo["id"].(int), params.ImgSrc, params.PermitStartDate, params.PermitEndDate, params.PermitShopName, params.PermitLicNum, params.PermitCreditCode, params.PermitLegalName, params.PermitShopAddr, params.PermitBusinessPremises, params.PermitState, params.PermitBusinessType, params.PermitSuperviseOrg, params.PermitSuperviseManagers, params.PermitIssuingAuthority)
	if result {
		s.Ok(c)
	}else{
		s.Fail(c,msg,-1000)
	}

}

// GetAdministrator
//
//	@Time 2023-01-13 10:05:25
//	<AUTHOR>
//	@Description: 获取第5页的信息
//	@receiver s CollectionInfoController
//	@param c
func (s CollectionInfoController) GetAdministrator(c *gin.Context) {
	var (
		value, _    = c.Get("admin")
		admin       = value.(models.Admin)
		mService    = merchantService.NewMerchantService(c)
		cService    = merchantService.NewCollectionInfoService(c)
		transformer = merchantTransformer.NewCollectionInfoTransformer(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	shopInfo, shopImages := cService.GetAdministrator(resInfo["id"].(int))
	data := transformer.FormatAdministratorInfo(shopInfo, shopImages)

	accInfo := cService.GetAccountInfoOne(resInfo["id"].(int))
	dt := data.(map[string]interface{})
	dt["admin"] = accInfo

	s.Success(c, dt, "msg", 200)
}

// GetBusinessLicense
//
//	@Time 2023-01-13 10:21:12
//	<AUTHOR>
//	@Description: 获取第6页的信息
//	@receiver s CollectionInfoController
//	@param c
func (s CollectionInfoController) GetBusinessLicense(c *gin.Context) {
	var (
		value, _    = c.Get("admin")
		admin       = value.(models.Admin)
		mService    = merchantService.NewMerchantService(c)
		cService    = merchantService.NewCollectionInfoService(c)
		transformer = merchantTransformer.NewCollectionInfoTransformer(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	shopInfo, shopImages := cService.GetBusinessLicense(resInfo["id"].(int))
	data := transformer.FormatBusinessLicenseInfo(shopInfo, shopImages)
	s.Success(c, data, "msg", 200)
}

// PostSmsCode
//
//	获取短信验证码
//
//	mobile 手机号
func (s CollectionInfoController) PostSmsCode(c *gin.Context) {
	type Params struct {
		Mobile string `form:"mobile" binding:"required"` // 手机号
	}
	var (
		redisHelper = tools.GetRedisHelper()
		params      Params
		err         = c.ShouldBind(&params)
	)

	if !tools.VerifyMobileFormat(params.Mobile) { //手机号 格式判断
		s.Fail(c, "mobile_incorrect", -1000)
		return
	}

	cacheKey := fmt.Sprintf("send_mobile_%s", params.Mobile)
	//2分钟 能获取一次
	exists, _ := redisHelper.Exists(c, cacheKey).Result()
	if exists == 0 {
		redisHelper.Set(c, cacheKey, params.Mobile, 2*time.Minute)
	} else {
		s.Fail(c, "sms_in_two_minutes", -1000)
		return
	}

	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))
	code := fmt.Sprintf("%06v", rnd.Int31n(1000000))

	cacheKey = fmt.Sprintf("shop_mobile_%s", params.Mobile)

	redisHelper.Set(c, cacheKey, code, 30*time.Minute) //验证码保存 30 分钟

	//contentMap := map[string]string{
	//	"code": code,
	//}
	//dataType, _ := json.Marshal(contentMap)
	//content := string(dataType)
	//
	//err = tools.AliSmsSend(params.Mobile, string(content))
	// 发送验证码
	smsServiceFactory := &factory.SmsServiceFactory{}
	smsService := smsServiceFactory.CreateSmsService()
	codeResp, err := smsService.SendVerificationCode(params.Mobile, code)
	if err != nil {
		tools.Logger.Errorf("发送验证码失败: %v \n",err)
		panic(err)
		return
	}
	tools.Logger.Infof("发送验证码成功 结果: %v \n",codeResp)
	//if err != nil {
	//	panic(err)
	//	return
	//}

	s.Ok(c)
}

// GetPayProduct
//
//	业务类型
func (s CollectionInfoController) GetPayProduct(c *gin.Context) {

	var (
		service = merchantService.NewCollectionInfoService(c)
	)
	data := service.GetPayProduct()
	s.Success(c, data, "msg", 200)
}

// GetBankList
//
//	银行列表
// func (s CollectionInfoController) GetBankList(c *gin.Context) {

// 	var (
// 		service = merchantService.NewCollectionInfoService(c)
// 	)
// 	data := service.GetBankList()
// 	s.Success(c, data, "msg", 200)
// }

// GetBranchBankList

func (s CollectionInfoController) GetBranchBankList(c *gin.Context) {
	type Params struct {
		AreaCode string `form:"areaCode" binding:"required"` // 地区code（省2位，城市4位，区县6位）
		Kw       string `form:"key" binding:"required"`      // 请求的关键字，支持多个关键字查询，用"/"分割，需要符合前后顺序。建议使用支行关键字查询
	}
	var (
		params     Params
		err        = c.ShouldBind(&params)
		umsService = selfsign.SelfSignFactory("ums")
	)
	if err != nil {
		panic(err)
		return
	}

	// 根据地区码和关键字查询支行列表
	bankList := umsService.GetBankList(params.AreaCode, params.Kw)
	s.Success(c, bankList["branchBankList"], "", 200)
}

// @Description: 根据地区码和关键字查询支行列表
// <AUTHOR>
// @Time 2023-01-12 11:50:25
// @receiver s CollectionInfoController
// @param c
func (s CollectionInfoController) GetBranchBankList2(c *gin.Context) {
	type Params struct {
		AreaCode string `form:"areaCode" binding:"required"` // 地区code（省2位，城市4位，区县6位）
		Kw       string `form:"key" `                        // 请求的关键字，支持多个关键字查询，用"/"分割，需要符合前后顺序。建议使用支行关键字查询
		BankId   string `form:"bank_id" `
	}
	var (
		params   Params
		err      = c.ShouldBind(&params)
		cService = merchantService.NewCollectionInfoService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	page := c.DefaultQuery("page", "1")
	bankList := cService.BankBranchShumai(params.AreaCode, params.Kw, params.BankId, page)
	s.Success(c, bankList, "", 200)
}

// GetBankList
//
//	@Description: 获取银行名称列表
//	<AUTHOR>
//	@Time 2023-01-12 13:31:46
//	@receiver s CollectionInfoController
//	@param c
func (s CollectionInfoController) GetBankList(c *gin.Context) {
	var cService = merchantService.NewCollectionInfoService(c)
	bankList := cService.GetBankList()
	s.Success(c, bankList, "", 200)
}

// PostUmsVerifyAccount
//
//	@Description: 对公账户认证
//	<AUTHOR>
//	@Time 2023-01-12 13:31:46
//	@receiver s CollectionInfoController
//	@param c
func (s CollectionInfoController) PostUmsVerifyAccount(c *gin.Context) {
	type Params struct {
		VerifyAmount string `form:"verify_amount" binding:"required"` // 验证金额
	}
	var (
		params   Params
		err      = c.ShouldBind(&params)
		cService = selfsign.SelfSignFactory("ums")
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		mService = merchantService.NewMerchantService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	// 验证当前餐厅审核状态
	//var merchantInfo models.CollectionMerchantInfo
	//db.Model(merchantInfo).Where("restaurant_id = ?", resInfo["id"]).Scan(&merchantInfo)
	merchantInfo := cService.GetMerchantInfoByResId(resInfo["id"].(int))
	if err != nil {
		s.Fail(c, "未找到对应数据", -1000)
		return
	}
	verifyAmount := params.VerifyAmount

	res := cService.CompanyAccountVerify(merchantInfo.UmsRegId, fmt.Sprintf("%0.0f", tools.ToFloat64(verifyAmount)*100), merchantInfo.BankAcctNum)
	tools.Logger.Info("对公账户确认结果", res)
	if res["res_code"] == "0000" || res["res_code"] == "1446" {
		tools.Logger.Info("打款认证成功")
		merchantInfo = cService.GetMerchantInfoByResId(resInfo["id"].(int))
		cService.UpdateMerchantState(resInfo["id"].(int), 8, "对公账户打款验证", "对公账户打款验证成功")
		// 返回电子签约链接
		res = cService.GetAgreementSign(merchantInfo.UmsRegId)
		tools.Logger.Info("电子签约链接：")
		tools.Logger.Info(res)
		s.Success(c, res, "", 200)
	} else {
		s.Fail(c, res["res_msg"].(string), -1000)
		return
	}
}

// GetUmsApplyQuery
//
//	@Description: 查询入网状态
//	<AUTHOR>
//	@Time 2023-01-14 17:53:47
//	@receiver s CollectionInfoController
//	@param context
func (s CollectionInfoController) GetUmsApplyQuery(c *gin.Context) {
	var (
		cService    = selfsign.SelfSignFactory("ums")
		value, _    = c.Get("admin")
		admin       = value.(models.Admin)
		mService    = merchantService.NewMerchantService(c)
		infoService = merchantService.NewCollectionInfoService(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)

	resId := resInfo["id"].(int)
	// 验证当前餐厅审核状态
	//db := tools.Db
	//var UmsRegId string

	merchantInfo := cService.GetMerchantInfoByResId(resId)
	if merchantInfo.Id == 0 {
		s.Fail(c, "未找到对应数据", -1000)
		return
	}
	res, _ := infoService.GetApplyStateAndDoSomeUpdate(merchantInfo, resId)
	s.Success(c, res, "", 200)
}

// PostStaffInfo
//
//	@Time 2023-01-14 16:47:03
//	<AUTHOR>
//	@Description: 添加员工信息
//	@receiver s CollectionInfoController
//	@param c
func (s CollectionInfoController) PostStaffInfo(c *gin.Context) {
	type Parame struct {
		Staffs string `form:"staffs" binding:"required"`
	}
	var (
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		staffs   Parame
		err      = c.ShouldBind(&staffs)
		mService = merchantService.NewMerchantService(c)
		cService = merchantService.NewCollectionInfoService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	state, i := cService.CheckColMerInfoState(resInfo["id"].(int))
	if !state {
		panic(errors.CustomError{Msg: "merInfoaudit" + strconv.Itoa(i)})
	}
	cService.SaveStaffInfo(resInfo["id"].(int), staffs.Staffs)
	s.Ok(c)

}

// 修改 员工 健康证信息
func (s CollectionInfoController) PostUpdateStaffInfo(c *gin.Context) {
	type Params struct {
		Id                     string `form:"id" binding:"required"`                        // 员工id
		StaffName              string `form:"staff_name" binding:"required"`                // 员工姓名
		StaffIdcard            string `form:"staff_idcard" binding:"required"`              // 员工身份证号
		HealthCertificateImage string `form:"health_certificate_image"  binding:"required"` //员工健康证 图片
		HealthCertificateStart string `form:"health_certificate_start" binding:"required"`  // 员工健康证有效期开始时间
		HealthCertificateEnd   string `form:"health_certificate_end" binding:"required"`    // 员工健康证有效期结束时间
	}

	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchantService.NewCollectionInfoService(c)
	)
	if err != nil {
		panic(err)
		return
	}

	errMsg := service.UpdateMerStaff(params.Id, params.StaffName, params.StaffIdcard, params.HealthCertificateImage, params.HealthCertificateStart, params.HealthCertificateEnd)
	if len(errMsg) > 0 {

		s.Fail(c, errMsg, -1000)
		return
	}

	info := map[string]string{}
	s.Success(c, info, "success", 200)

}

// 删除员工健康证信息
func (s CollectionInfoController) PostDelStaffInfo(c *gin.Context) {
	type Params struct {
		Id string `form:"id" ` // 员工ID
	}

	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchantService.NewCollectionInfoService(c)
	)
	if err != nil {
		panic(err)
		return
	}

	tools.Logger.Info("删除员工健康证信息 id:", params.Id)
	errMsg := service.DeleteMerStaff(params.Id)
	if len(errMsg) > 0 {

		s.Fail(c, errMsg, -1000)
		return
	}

	info := map[string]string{}
	s.Success(c, info, "success", 200)

}

// 获取 资料审核信息
// 0:未提交,1:待审核 ,2:审核未通过 ,3:后台审核通过 ,4:待提交资料 ,5:已提交资料 ,6:资料未通过,7:对公账户待确认  ,8:待在线签约 ,9:待银商入网审核 ,10:银商入网成功,11:银商入网失败,
func (s CollectionInfoController) GetCheckState(c *gin.Context) {

	var (
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		// cService = selfsign.SelfSignFactory("ums")
		lakalService = lakalaService.NewLakalaService(c)
		mService     = merchantService.NewMerchantService(c)
		service      = merchantService.NewCollectionInfoService(c)
		// transformer = merchantTransformer.NewCollectionInfoTransformer(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	if resInfo["id"] == nil { //防止出现   interface {} is nil, not int  异常
		s.Fail(c, "data_not_found", -1000)
		return
	}

	if resInfo["res_count"] != nil {
		if tools.ToInt(resInfo["res_count"]) > 1 { //检查一个管理员属于多个店铺导致的店铺选错问题
			s.Fail(c, s.Trans(c, "this_admin_has_more_than_one_store")+" "+tools.ToString(resInfo["res_names"])+" "+s.Trans(c, "multi_admin_error"), -1000)
			return
		}
	}
	verify := c.DefaultQuery("verify", "")
	if len(verify) > 0 { //签约页面完成后点击签约完成了按钮
		//查询入网状态
		// merchantInfo := cService.GetMerchantInfoByResId(resInfo["id"].(int))
		// service.GetApplyStateAndDoSomeUpdate(merchantInfo, resInfo["id"].(int)) //银商接口查询并更新数据
		lakalService.MemberQueryInternal(c, tools.ToString(resInfo["id"])) //拉卡拉状态查询 查询一下就行不管结果

	}
	// 查询 入驻信息
	info, errMsg := service.MerCheckState(resInfo["id"].(int), resInfo["city_id"].(int), resInfo["area_id"].(int))
	if len(errMsg) > 0 {

		s.Fail(c, errMsg, -1000)
		return
	}
	//0:未提交,1:待审核 ,2:审核未通过 ,3:后台审核通过 ,4:待提交资料 ,5:已提交资料 ,6:资料未通过,7:对公账户待确认  ,8:待在线签约 ,9:待银商入网审核 ,10:银商入网成功,11:银商入网失败,
	// 注册类型（00:企业，01:店铺：02：小微商户）
	//注册类型 00
	// data["submit_type"]=inf.SubmitType
	// data["member_no"]=inf.MemberNo
	// data["lakala_verify_state"]=inf.LakalaVerifyState
	// if  tools.ToInt(info["submit_type"])==2 && tools.ToInt(info["lakala_verify_state"]) ==1 { //拉卡拉 发送入驻 返回签约地址
	if tools.ToInt(info["state"]) != 2 && tools.ToInt(info["lakala_verify_state"]) == 1 { //拉卡拉 发送入驻 返回签约地址
		//查询入网状态
		// merchantInfo := cService.GetMerchantInfoByResId(resInfo["id"].(int))
		// service.GetApplyStateAndDoSomeUpdate(merchantInfo, resInfo["id"].(int))
		//再次查询状态
		// info, errMsg = service.MerCheckState(resInfo["id"].(int), resInfo["city_id"].(int), resInfo["area_id"].(int))
		// if len(errMsg) > 0 {

		// 	s.Fail(c, errMsg, -1000)
		// 	return
		// }
		//银商的数据迁移到拉卡拉
		sendMap, err := lakalService.MigrateUMS(c, tools.ToString(info["information_id"]), tools.ToString(info["lakala_verify_mobile"]))
		if err != nil {
			s.Fail(c, err.Error(), -1000)
			return
		}
		info["verify_url"] = tools.ToString(sendMap["url"])
		info["state"] = 8                                                        //待签约
		if info["submit_type"] != nil && tools.ToInt(info["submit_type"]) == 2 { //拉卡拉
			resId := tools.ToString(resInfo["id"])
			//获取可以复制的内容
			info["copy_content"] = lakalService.GetCopyContent(c, resId)
		}

		//再查后 状态还是 8 待签名 则返回 签名地址
		// if info["state"] == 8 {
		// 	chinaumsService := selfsign.SelfSignFactory("ums")
		// 	mp := chinaumsService.GetAgreementSign(info["ums_reg_id"].(string))
		// 	tools.Logger.Info("签名url", mp)
		// 	info["verify_url"] = mp["url"] //签约地址
		// }
	}
	state := info["state"].(int)
	//客户端显示的时候 通过美滋来后台的时候还要显示 审核中 避免 客户端以为审核通过了
	if state == 3 || state == 5 {
		state = 4
	}
	st := strconv.Itoa(state)
	info["state_name"] = s.Trans(c, "state_"+st)
	s.Success(c, info, "success", 200)

}

// GetStaffInfo
//
//	@Time 2023-01-16 19:50:36
//	<AUTHOR>
//	@Description: 获取员工信息
//	@receiver s CollectionInfoController
//	@param c
func (s CollectionInfoController) GetStaffInfo(c *gin.Context) {
	var (
		value, _    = c.Get("admin")
		admin       = value.(models.Admin)
		mService    = merchantService.NewMerchantService(c)
		cService    = merchantService.NewCollectionInfoService(c)
		transformer = merchantTransformer.NewCollectionInfoTransformer(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	stuffs := cService.GetStuffInfo(resInfo["id"].(int))
	data := transformer.FormatStuffInfo(stuffs)
	s.Success(c, data, "msg", 200)
}

// 审核通过后的资料
func (s CollectionInfoController) GetRestaurantInfo(c *gin.Context) {
	var (
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		mService = merchantService.NewMerchantService(c)
		cService = merchantService.NewCollectionInfoService(c)
		// transformer = merchantTransformer.NewCollectionInfoTransformer(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	stuffs := cService.GetRestaurantInfo(resInfo["id"].(int))

	state := stuffs["state"].(int)
	editable := false
	if state == 0 || state == 2 || state == 10 {
		editable = true
	}
	stuffs["editable"] = editable
	st := strconv.Itoa(state)
	stuffs["state_name"] = s.Trans(c, "state_"+st)
	s.Success(c, stuffs, "msg", 200)
}

// 审核失败后再次提交
func (s CollectionInfoController) SubmitAgain(c *gin.Context) {
	var (
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		mService = merchantService.NewMerchantService(c)
		service  = merchantService.NewCollectionInfoService(c)
		// transformer = merchantTransformer.NewCollectionInfoTransformer(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	state, i := service.CheckColMerInfoState(resInfo["id"].(int))
	if !state {
		panic(errors.CustomError{Msg: "merInfoaudit" + strconv.Itoa(i)})
		return
	}
	errMsg := service.UpdateInfoState(resInfo["id"].(int))
	if len(errMsg) > 0 {

		s.Fail(c, errMsg, -1000)
		return
	}

	info := map[string]string{}
	s.Success(c, info, "success", 200)
}

// GetStaffInfo
//  @Time 2023-02-7
//  <AUTHOR>
//  @Description: 处理银联商务入网状态
//  @receiver s CollectionInfoController
//  @param c
func (s CollectionInfoController) Test(c *gin.Context) {
	s.Ok(c)
}
func (s CollectionInfoController) PostNotify(c *gin.Context) {
	tools.Log("银联商务通知进入。。。。。。。。。。。。。。。。")
	println("银联商务通知进入。。。。。。。。。。。。。。。。")
	var (
		infoService = merchantService.NewCollectionInfoService(c)
	)
	buf, _ := c.GetRawData()
	notifyStr := string(buf)
	tools.Log("银联商务审核通知内容：" + notifyStr)
	println("银联商务审核通知内容：" + notifyStr)

	//notifyStr := `accesser_id=2d9081bc7fe01be5017fea0d34925da8&sign_data=bcd8d81f4cc8c0a9b552140de958dac3b62e0d6e75eb9a56c67369ee2062b3a3&json_data=9618416b5c742901a6ba7795c259a8442378997545a32854eff68b6cbf861b6214e9f1014cf360614b76ab668ae680bd9dfec9825df440f7bdb0d6c4e8019d9aced11d834bcd943a89b40fa314444a458b377321f99e56bdcff1265bfc19e06d34a283e98949fb6904ff5b01cd5cb35487b7f8120e628877e20f7cc04df725f3753c3bf85833664ba84b7e21427d9ec449e101f0ac6b4963877b8253652b206ed571e84991b706b27ff3ccffa997367530904e288336cbc7e42ea06ce7a4d69dd5138ee04daeea365a14d0b254fdf241767b905889f8f7837cc88787aab3d0a69e7cd3d0235fddfcd37eff8ac782b344a1e2b50c31d16ab94e789926eba5c0fdf17ef13237a7e1210d928bd0cd203b41156f75e2bc46cb8bd2d4de2cb4099557c852e0398201214b7fca8946f38004a130f7c152d8ea91e89af7f81837f7abe9344ce4907eb403221c32e243210cf49b67f5c5bef3acb066d11430220794e3c3fe981f4158e9e4ae67fd64465531ad3f1e11c2572b52e35b90484a37a8a7e901ea2022bed188455c0129e9345c972863fce70d1bb84e2e2d8b4a2061db3b1f98836a0c368cb47741f280ad6abfdd9858b7f65969a8a4b7ae03566a1b67c4e3bce39fbb13efc35f1006a8eeb4e38dad69feb03044cf50d5e9c34877d298121db7ee9e3666170e95934b76ab668ae680bd9dfec9825df440f7004b3bf896ab6ca120bf2b55963e13dcb45d81c8396bcb39dad342443df2c990bff14579de314f1e`
	notifyIndex := strings.LastIndex(notifyStr, "json_data=")

	// accesser_id := c.PostForm("sign_data")
	// sign_data := c.PostForm("sign_data")

	json_data := notifyStr[notifyIndex+10:]
	tools.Logger.Info("json_data: ", json_data)
	// tools.ChinaUmsDES3Encrypt(jsonStr, configs.UmsConfig.Key)
	decryptStr := tools.ChinaUmsDES3Decrypt(json_data, configs.UmsConfig.Key)
	tools.Log("通知解析结果：" + decryptStr)
	println("通知解析结果：" + decryptStr)

	resMap, _ := tools.StringToMap(decryptStr)

	merchantInfo := infoService.GetSelfSignInfoByRegId(resMap["ums_reg_id"].(string))
	if merchantInfo.Id == 0 {
		s.Fail(c, "未找到对应数据", -1000)
		tools.Log(" 未找到对应数据")
		println(" 未找到对应数据")
		c.JSON(200, gin.H{
			"res_code": "0000",
		})
		return
	}
	println(" 成功")
	infoService.GetApplyStateAndDoSomeUpdate(merchantInfo, merchantInfo.RestaurantId)
	c.JSON(200, gin.H{
		"res_code": "0000",
	})
}

// 提交
func (s CollectionInfoController) Submit(c *gin.Context) {
	var (
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		mService = merchantService.NewMerchantService(c)
		service  = merchantService.NewCollectionInfoService(c)
		// transformer = merchantTransformer.NewCollectionInfoTransformer(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	state, i := service.CheckColMerInfoState(resInfo["id"].(int))
	if !state {
		panic(errors.CustomError{Msg: "merInfoaudit" + strconv.Itoa(i)})
		return
	}
	errMsg := service.Submit(resInfo["id"].(int))
	if len(errMsg) > 0 {

		s.Fail(c, errMsg, -1000)
		return
	}

	info := map[string]string{}
	s.Success(c, info, "success", 200)
}

// 清空数据
func (s CollectionInfoController) Clear(c *gin.Context) {
	var (
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		mService = merchantService.NewMerchantService(c)
		service  = merchantService.NewCollectionInfoService(c)
	)
	resInfo, _, _ := mService.GetRestaurantByAdmin(c, admin)
	newType := c.DefaultPostForm("reg_mer_type", "01")
	errMsg := service.Clear(resInfo["id"].(int), resInfo["city_id"].(int), resInfo["area_id"].(int), newType)
	if len(errMsg) > 0 {

		s.Fail(c, errMsg, -1000)
		return
	}

	info := map[string]string{}
	s.Success(c, info, "success", 200)
}

// GetUmsUploadDoc
//
//	@Description: 给第三方打包发送所有数据
//	@author: Alimjan
//	@Time: 2023-01-13 15:59:18
//	@receiver s CollectionInfoController
//	@param context *gin.Context
func (s CollectionInfoController) GetUploadDoc(c *gin.Context) {
	type Params struct {
		ResId string `form:"res_id" binding:"required"` // 营业执照有效期开始时间
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = selfsign.SelfSignFactory("ums")
	)
	if err != nil {
		panic(err)
		return
	}
	resId := tools.ToInt64(params.ResId)
	//限流 异步发送  每次只发送一个
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Printf("error: %s\n", err)
				service.UpdateMerchantState(int(resId), 4, "上传资料", "发送照片失败")
				<-limitGoroutine
			}
		}()

		limitGoroutine <- 0
		err = service.UploadDoc(resId)
		<-limitGoroutine
	}()
	if err != nil {
		s.Success(c, nil, err.Error(), -1000)
		return
	}
	s.Success(c, nil, "msg", 200)
}

// GetUmsApplyQueryFromCms
//
//	@Description: 从后台查询状态 不需要认证  里边的逻辑代码基本一致
//	@author: Alimjan
//	@Time: 2023-01-31 17:14:29
//	@receiver s CmsController
//	@param context *gin.Context
func (s CollectionInfoController) GetUmsApplyQueryFromCms(c *gin.Context) {
	var (
		cService    = selfsign.SelfSignFactory("ums")
		resId, _    = strconv.Atoi(c.Query("res_id"))
		infoService = merchantService.NewCollectionInfoService(c)
	)

	// 验证当前餐厅审核状态
	//db := tools.Db
	//var UmsRegId string

	merchantInfo := cService.GetMerchantInfoByResId(resId)
	if merchantInfo.Id == 0 {
		s.Fail(c, "未找到对应数据", -1000)
		return
	}
	res, _ := infoService.GetApplyStateAndDoSomeUpdate(merchantInfo, resId)
	s.Success(c, res, "", 200)
}

// GetUmsUploadDoc
//
//	@Description: 给第三方发送账户信息
//	@author: Alimjan
//	@Time: 2023-01-13 15:59:18
//	@receiver s CollectionInfoController
//	@param context *gin.Context
func (s CollectionInfoController) GetAlterAccountInfo(c *gin.Context) {
	type Params struct {
		ResId int `form:"res_id" binding:"required"` // 营业执照有效期开始时间
	}

	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = selfsign.SelfSignFactory("ums")
	)

	if err != nil {
		panic(err)
		return
	}
	resId := params.ResId
	err = service.AlterAccountInfo(resId)

	if err != nil {
		s.Success(c, nil, err.Error(), -1000)
		return
	}
	s.Success(c, nil, "msg", 200)
}
