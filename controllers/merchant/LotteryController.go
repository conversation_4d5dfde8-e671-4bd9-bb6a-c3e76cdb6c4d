package merchant

import (
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"

	merchant "mulazim-api/services/merchant"

	"github.com/gin-gonic/gin"
)

type LotteryController struct {
	controllers.BaseController
}

/***
 * @Author: [rozimamat]
 * @description: 活动首页
 * @Date: 2023-03-04 16:51:36
 * @param {*gin.Context} c
 */
func (marketing *LotteryController) Marketing(c *gin.Context) {

	var service = merchant.NewMarketingService(c)

	marketingList := service.GetMarketing(c)
	marketing.Success(c, marketingList, "msg", 200)
}

 //抽奖活动 优惠券日志
func (marketing *LotteryController) LotteryCouponLog(c *gin.Context) {
	
	var (
		l, _     = c.Get("lang_util")
		lang     = l.(lang.LangUtil)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		service  = merchant.NewMerchantService(c)
	)
	resInfo,_:=service .GetResByAdmin(admin)	

	marketingList := service.LotteryCouponLog(resInfo.ID,lang.Lang)
	marketing.Success(c, marketingList, "msg", 200)
}
