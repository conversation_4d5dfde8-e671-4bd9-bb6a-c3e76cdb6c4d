package merchant

// 优惠商品

import (
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/merchantRequest/foodsPreferentRequest"
	merchantResource "mulazim-api/resources/merchant"
	"mulazim-api/services"
	"mulazim-api/tools"
	"slices"
	"time"

	"github.com/gin-gonic/gin"
)

type FoodsPreferentialController struct {
	controllers.BaseController
}

// List 列表
func (c *FoodsPreferentialController) List(ctx *gin.Context) {
	var (
		State     int = tools.ToInt(ctx.DefaultQuery("state", "-1"))
		Page      int = tools.ToInt(ctx.DefaultQuery("page", "1"))
		Limit     int = tools.ToInt(ctx.DefaultQuery("limit", "10"))
		admin         = permissions.GetAdmin(ctx)
		service       = new(services.FoodsPreferentialService)
		language      = ctx.Param("locale")
		langUtils     = lang.LangUtil{
			Lang: language,
		}
	)
	if State < 0 || State > 1 {
		State = -1
	}
	if Page < 1 {
		Page = 1
	}
	if Limit < 1 || Limit > 100 {
		Limit = 10
	}
	restaurant := admin.GetAdminRestaurant()
	if restaurant.ID == 0 {
		c.Fail(ctx, "restaurant_not_found", -1000)
	}
	foods, total, err := service.List(restaurant, State, Page, Limit)
	if err != nil {
		c.Fail(ctx, err.Error(), -1000)
	}
	c.Success(ctx, merchantResource.NewFoodsPreferentialListResource(foods, total, Page, Limit, langUtils), "msg", 200)
}

// Create 创建
func (c *FoodsPreferentialController) Create(ctx *gin.Context) {
	var (
		request = foodsPreferentRequest.CreateRequest{}
		err     = ctx.ShouldBindJSON(&request)
		admin   = permissions.GetAdmin(ctx)
		service =services.NewFoodsPreferentialService(ctx)
		foodInfo models.RestaurantFoods
		db      = tools.GetDB()
		language      = ctx.Param("locale")
		langUtils     = lang.LangUtil{
			Lang: language,
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		c.Fail(ctx, err.Error(), -1000)
		return
	}
	restaurant := admin.GetAdminRestaurant()
	if restaurant.ID == 0 {
		c.Fail(ctx, "restaurant_not_found", -1000)
		return
	}
	if tools.InArray(restaurant.AreaID,configs.MyApp.SeckillAndPrefStopArea) {//墨玉县活动 暂停
		c.Fail(ctx, "promotion_create_disabled", -1000)
		return
	}
	// 单商品优惠
	if request.PreferentialStyle == foodsPreferentRequest.PreferentialStyleSingle {
		db.Model(&models.RestaurantFoods{}).
			Where("id = ? AND restaurant_id = ?", request.FoodID, restaurant.ID).
			Preload("FoodSpecTypes.FoodSpecOptions").
			First(&foodInfo)
		if foodInfo.ID == 0 {
			c.Fail(ctx, "not_found", -1000)
			return
		}

		// 美食价格 - 如果是规格美食，需要从已选的规格子项中获取最终价格
		foodPrice := int(foodInfo.Price)
		if request.FoodType == models.RestaurantFoodsTypeSpec {
			if len(request.OptionIds) == 0 {
				c.Fail(ctx, "option_ids_must_not_empty", -1000)
				return
			}

			_foodPrice := 0
			for _, fType := range foodInfo.FoodSpecTypes {
				for _, _specOption := range fType.FoodSpecOptions {
					if slices.Contains(request.OptionIds, _specOption.ID) {
						_foodPrice += _specOption.Price
					}
				}
			}
			if _foodPrice <= 0 {
				c.Fail(ctx, "discount_price_must_less_than_price", -1000) // TODO: 应该是规格项价格不对的提示
				return
			}
			foodPrice = _foodPrice
		}

		// 检查优惠价格是否大于美食价格
		if int(request.DiscountPrice) >= foodPrice {
			c.Fail(ctx, "discount_price_must_less_than_price", -1000)
			return
		}
		// 检查美食是否属于当前商家
		if foodInfo.RestaurantID != restaurant.ID {
			msg := fmt.Sprintf("商家ID: %d, 美食ID: %d 尝试添加优惠", restaurant.ID, foodInfo.ID)
			tools.Logger.Error(msg)
			c.Fail(ctx, "not_found", -1000)
			return
		}
		// 检查时间是否在美食出餐时间内
		if !tools.TimeLineInTimeLine(request.StartTime, request.EndTime, foodInfo.BeginTime, foodInfo.EndTime) {
			c.Fail(ctx, fmt.Sprintf(langUtils.T("food_not_in_activity_range"), foodInfo.BeginTime, foodInfo.EndTime), -1000)
			return
		}
		// 创建美食优惠
		err = service.CreateSingleFood(restaurant, admin, foodInfo, request)
		if err != nil {
			c.Fail(ctx, err.Error(), -1000)
			return
		}
	} else {
		// 验证是否存在规格美食
		if service.RestaurantHasSpecFoods(restaurant.ID) {
			c.Fail(ctx,"this_has_spec_foods",-1000)
			return
		}
		// 检查优惠时间段是否在商家营业时间内
		if !tools.TimeLineInTimeLine(request.StartTime, request.EndTime, restaurant.BeginTime, restaurant.EndTime) {
			c.Fail(ctx, "discount_timeline_not_in_restaurant_open_timeline", -1000)
			return
		}
		err = service.CreateRestaurantFoodsPreferential(restaurant, admin, request)
		if err != nil {
			c.Fail(ctx, err.Error(), -1000)
			return
		}
	}

	c.Success(ctx, nil, "msg", 200)
}

// Update 更新
func (c *FoodsPreferentialController) Update(ctx *gin.Context) {
	var (
		request           = foodsPreferentRequest.UpdateRequest{}
		err               = ctx.ShouldBindJSON(&request)
		admin             = permissions.GetAdmin(ctx)
		service           = services.NewFoodsPreferentialService(ctx)
		foodsPreferential models.FoodsPreferential
		db                = tools.GetDB()
		language          = ctx.Param("locale")
		langUtils         = lang.LangUtil{
			Lang: language,
		}
	)
	if err != nil {
		panic(err)
		return
	}
	if err := request.Validate(); err != nil {
		c.Fail(ctx, err.Error(), -1000)
		return
	}
	restaurant := admin.GetAdminRestaurant()
	if restaurant.ID == 0 {
		c.Fail(ctx, "restaurant_not_found", -1000)
		return
	}
	if tools.InArray(restaurant.AreaID,configs.MyApp.SeckillAndPrefStopArea) {//墨玉县活动 暂停
		c.Fail(ctx, "promotion_update_disabled", -1000)
		return
	}

	db.Model(&models.FoodsPreferential{}).
		Preload("RestaurantFood").
		Where("id = ?", request.Id).
		First(&foodsPreferential)
	if foodsPreferential.ID == 0 {
		c.Fail(ctx, "not_found", -1000)
		return
	}
	if foodsPreferential.PriceMarkupId != 0 {
		c.Fail(ctx, "price_markup_seckill_not_allow_edit", -1000)
		return
	}
	// 检查优惠价格是否大于美食价格
	if request.DiscountPrice > foodsPreferential.RestaurantFood.Price {
		c.Fail(ctx, "discount_price_must_less_than_price", -1000)
		return
	}
	// 检查美食是否属于当前商家
	if foodsPreferential.RestaurantID != restaurant.ID {
		msg := fmt.Sprintf("更新美食优惠失败：美食优惠ID: %d, 不属于管理员ID: %d 尝试添加优惠", request.Id, admin.ID)
		tools.Logger.Error(msg)
		c.Fail(ctx, "not_found", -1000)
		return
	}
	// 检查时间是否在美食出餐时间内
	if !tools.TimeLineInTimeLine(request.StartTime, request.EndTime, foodsPreferential.RestaurantFood.BeginTime, foodsPreferential.RestaurantFood.EndTime) {
		tools.Logger.Infof("活动开始时间与美食时间验证： 美食ID: %d, 开始时间: %s, 结束时间: %s, 美食开始时间: %s, 美食结束时间: %s", foodsPreferential.RestaurantFood.ID, request.StartTime, request.EndTime, foodsPreferential.RestaurantFood.BeginTime, foodsPreferential.RestaurantFood.EndTime)
		c.Fail(ctx, "discount_timeline_not_in_restaurant_open_timeline", -1000)
		return
	}
	// 结束的优惠活动无法进行修改
	if foodsPreferential.EndDateTime.Before(time.Now()) {
		c.Fail(ctx, "can_not_update_expired_foods_preferential", -1000)
		return
	}
	// 创建美食优惠
	foodsPreferential, err = service.Update(foodsPreferential, request.DiscountPrice, request.MaxOrderCount, *request.OrderCountPerDay,
		request.StartTime, request.EndTime, request.StartDateTime, request.EndDateTime, *request.State)
	if err != nil {
		c.Fail(ctx, err.Error(), -1000)
		return
	}
	db.Model(models.FoodsPreferential{}).
		Preload("PreferentialType").
		Preload("RestaurantFood.ComboFoodItems.SelectedSpec.FoodSpecOptions"). // 套餐子美食，以及子美食已选规格
		Preload("SelectedSpec.FoodSpecOptions"). // 已选规格
		Find(&foodsPreferential, request.Id)
	c.Success(ctx, merchantResource.NewFoodsPreferentialListItemResource(foodsPreferential, langUtils), "msg", 200)
}

// Detail 详情
func (c *FoodsPreferentialController) Detail(ctx *gin.Context) {
	var (
		foodsPreferential models.FoodsPreferential
		admin             = permissions.GetAdmin(ctx)
		db                = tools.GetDB()
		language          = ctx.Param("locale")
		langUtils         = lang.LangUtil{
			Lang: language,
		}
	)
	type DetailParam struct {
		Id uint `json:"id" binding:"required,min=1"`
	}
	var request DetailParam
	request.Id = uint(tools.ToInt(ctx.DefaultQuery("id", "0")))

	if request.Id <= 0 {
		c.Fail(ctx, "not_found", -1000)
		return
	}

	restaurant := admin.GetAdminRestaurant()
	if restaurant.ID == 0 {
		c.Fail(ctx, "restaurant_not_found", -1000)
		return
	}

	db.Model(&models.FoodsPreferential{}).
		Preload("PreferentialType").
		Preload("RestaurantFood.ComboFoodItems.RestaurantFood"). // 套餐子美食
		Preload("RestaurantFood.ComboFoodItems.SelectedSpec.FoodSpecOptions"). // 套餐子美食，以及子美食已选规格
		Preload("SelectedSpec.FoodSpecOptions"). // 已选规格
		Where("id = ?", request.Id).
		First(&foodsPreferential)
	if foodsPreferential.ID == 0 {
		c.Fail(ctx, "not_found", -1000)
		return
	}

	// 检查美食是否属于当前商家
	if foodsPreferential.RestaurantID != restaurant.ID {
		msg := fmt.Sprintf("更新美食优惠失败：美食优惠ID: %d, 不属于管理员ID: %d 尝试添加优惠", request.Id, admin.ID)
		tools.Logger.Error(msg)
		c.Fail(ctx, "not_found", -1000)
		return
	}
	c.Success(ctx, merchantResource.NewFoodsPreferentialListItemResource(foodsPreferential, langUtils), "msg", 200)
}

// UpdateState 更新状态
func (c *FoodsPreferentialController) UpdateState(ctx *gin.Context) {
	var (
		request           = foodsPreferentRequest.UpdateStateRequest{}
		err               = ctx.ShouldBindJSON(&request)
		admin             = permissions.GetAdmin(ctx)
		service           = services.NewFoodsPreferentialService(ctx)
		foodsPreferential models.FoodsPreferential
		db                = tools.GetDB()
	)
	if err != nil {
		panic(err)
		return
	}
	restaurant := admin.GetAdminRestaurant()
	if restaurant.ID == 0 {
		c.Fail(ctx, "restaurant_not_found", -1000)
		return
	}
	if tools.InArray(restaurant.AreaID,configs.MyApp.SeckillAndPrefStopArea){//墨玉县活动 暂停
		c.Fail(ctx, "promotion_update_disabled", -1000)
		return
	}

	db.Model(&models.FoodsPreferential{}).
		Preload("RestaurantFood").
		Where("id = ?", request.Id).
		First(&foodsPreferential)
	if foodsPreferential.ID == 0 {
		c.Fail(ctx, "not_found", -1000)
		return
	}
	if foodsPreferential.PriceMarkupId != 0 {
		c.Fail(ctx, "price_markup_seckill_not_allow_edit", -1000)
		return
	}
	// 检查美食是否属于当前商家
	if foodsPreferential.RestaurantID != restaurant.ID {
		msg := fmt.Sprintf("更新美食优惠失败：美食优惠ID: %d, 不属于管理员ID: %d 尝试添加优惠", request.Id, admin.ID)
		tools.Logger.Error(msg)
		c.Fail(ctx, "not_found", -1000)
		return
	}

	// 结束的优惠活动无法进行修改
	if foodsPreferential.EndDateTime.Before(time.Now()) {
		c.Fail(ctx, "can_not_update_expired_foods_preferential", -1000)
		return
	}
	if *request.State != foodsPreferential.State {
		// 更新状态
		foodsPreferential, err = service.UpdateState(foodsPreferential, *request.State)
		if err != nil {
			c.Fail(ctx, err.Error(), -1000)
			return
		}
	}

	c.Success(ctx, map[string]int{
		"state": foodsPreferential.State,
	}, "msg", 200)
}

// Delete 删除
func (c *FoodsPreferentialController) Delete(ctx *gin.Context) {
	var (
		admin             = permissions.GetAdmin(ctx)
		service           = new(services.FoodsPreferentialService)
		foodsPreferential models.FoodsPreferential
		db                = tools.GetDB()
	)
	type DetailParam struct {
		Id uint `json:"id" binding:"required,min=1"`
	}
	var request DetailParam
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		panic(err)
		return
	}
	if request.Id <= 0 {
		c.Fail(ctx, "not_found", -1000)
		return
	}
	restaurant := admin.GetAdminRestaurant()
	if restaurant.ID == 0 {
		c.Fail(ctx, "restaurant_not_found", -1000)
		return
	}

	db.Model(&models.FoodsPreferential{}).
		Where("id = ?", request.Id).
		First(&foodsPreferential)
	if foodsPreferential.ID == 0 {
		c.Fail(ctx, "not_found", -1000)
		return
	}
	if foodsPreferential.PriceMarkupId != 0 {
		c.Fail(ctx, "price_markup_seckill_not_allow_edit", -1000)
		return
	}
	// 检查美食是否属于当前商家
	if foodsPreferential.RestaurantID != restaurant.ID {
		msg := fmt.Sprintf("更新美食优惠失败：美食优惠ID: %d, 不属于管理员ID: %d 尝试添加优惠", request.Id, admin.ID)
		tools.Logger.Error(msg)
		c.Fail(ctx, "not_found", -1000)
		return
	}
	// 结束的优惠活动无法进行修改
	if foodsPreferential.EndDateTime.Before(time.Now()) {
		c.Fail(ctx, "can_not_update_expired_foods_preferential", -1000)
		return
	}
	err = service.Delete(foodsPreferential)
	if err != nil {
		c.Fail(ctx, err.Error(), -1000)
		return
	}
	c.Success(ctx, nil, "msg", 200)
}
