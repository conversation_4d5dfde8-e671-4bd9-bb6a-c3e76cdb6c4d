package merchant

import (
	"mulazim-api/controllers"
	"mulazim-api/models"
	merchantSeckillRequest "mulazim-api/requests/merchantRequest/Seckill"
	"mulazim-api/services/merchant"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type SeckillController struct {
	controllers.BaseController
}



// List
//
// @Description: 列表
// @Author: Rozimamat
// @Time: 2024-03-12 10:36:12
// @receiver
// @param c *gin.Context
func (s *SeckillController) List(c *gin.Context) {
	type Params struct {
		
		State         int `form:"state"`                             
		Page          int `form:"page"`                              
		Limit         int `form:"limit"`                             
		Deleted         int `form:"deleted"`                             
		startDate      string `form:"start_date"`
		endDate      string `form:"end_date"`
	}
	var (
		params  Params
		service = merchant.NewSeckillService(c)
		value, _   = c.Get("admin")
		admin      = value.(models.Admin)
		merchantService = merchant.NewMerchantService(c)
	)
	params.State  = tools.ToInt(c.DefaultQuery("state","0"))
	params.Page  = tools.ToInt(c.DefaultQuery("page","1"))
	params.Limit  = tools.ToInt(c.DefaultQuery("limit","10"))
	params.Deleted  = tools.ToInt(c.DefaultQuery("deleted","0"))
	params.startDate  = c.DefaultQuery("start_date","")
	params.endDate  = c.DefaultQuery("end_date","")

 
	resInfo,_:=merchantService.GetResByAdmin(admin)
	
	marketingList := service.GetSeckillList(admin.ID,resInfo.ID,  params.State, params.Page, params.Limit,params.Deleted,params.startDate,params.endDate)
	s.Success(c, marketingList, "msg", 200)
}



/***
 * @Author: [rozimamat]
 * @description: 创建
 * @Date: 2024-03-12 11:40:03
 * @param {*gin.Context} c
 */
func (s *SeckillController) Create(c *gin.Context) {
	var (
		params merchantSeckillRequest.SeckillCreateParams
		err     = c.ShouldBind(&params)
		service = merchant.NewSeckillService(c)
		value, _   = c.Get("admin")
		admin      = value.(models.Admin)
		merchantService = merchant.NewMerchantService(c)
	)
	if err != nil {
		panic(err)
	}
	resInfo,_:=merchantService.GetResByAdmin(admin)
	success, msg := service.Create(resInfo.ID, params, admin.ID)
	if success {
		s.Success(c, nil, msg, 200)
	} else {
		s.Success(c, nil, msg, -1000)
	}

}


/***
 * @Author: [rozimamat]
 * @description: 编辑
 * @Date: 2024-03-13 11:40:03
 * @param {*gin.Context} c
 */
func (s *SeckillController) Edit(c *gin.Context) {

	type Params struct {
		Id             int         `form:"id" json:"id" binding:"required"`
		
	}
	var (
		params  Params
		// err     = c.ShouldBind(&params)
		service = merchant.NewSeckillService(c)
		value, _   = c.Get("admin")
		admin      = value.(models.Admin)
		merchantService = merchant.NewMerchantService(c)
	)
	// if err != nil {
	// 	panic(err)
	// }
	params.Id  = tools.ToInt(c.DefaultQuery("id","0"))
	resInfo,_:=merchantService.GetResByAdmin(admin)
	result, msg := service.Detail(resInfo.ID, params.Id)
	if msg == "" {
		s.Success(c, result,msg, 200)
	} else {
		s.Success(c, nil, msg, -1000)
	}

}

/***
 * @Author: [Salam]
 * @description: 详情
 * @Date: 2025-04-10 18:19:03
 * @param {*gin.Context} ctx
 */
func (ctr *SeckillController) Detail(ctx *gin.Context) {
	type Params struct {
		ID int `uri:"id" binding:"required"`
	}
	var params Params
	if err := ctx.ShouldBindUri(&params); err != nil {
		panic(err)
	}

	var (
		service         = merchant.NewSeckillService(ctx)
		value, _        = ctx.Get("admin")
		admin           = value.(models.Admin)
		merchantService = merchant.NewMerchantService(ctx)
	)

	resInfo, _ := merchantService.GetResByAdmin(admin)
	result, msg := service.Detail(resInfo.ID, params.ID)
	if len(msg) > 0 {
		ctr.Fail(ctx, msg, -1000)
		return
	}

	ctr.Success(ctx, result, msg, 200)
}



/***
 * @Author: [rozimamat]
 * @description: 编辑 状态
 * @Date: 2024-03-13 11:40:03
 * @param {*gin.Context} c
 */
 func (s *SeckillController) UpdateState(c *gin.Context) {

	type Params struct {
		Id             int         `form:"id" json:"id" binding:"required"`
		State          string         `form:"state" json:"state" binding:"required"`
		
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchant.NewSeckillService(c)
		value, _   = c.Get("admin")
		admin      = value.(models.Admin)
		merchantService = merchant.NewMerchantService(c)
	)
	if err != nil {
		panic(err)
	}
	resInfo,_:=merchantService.GetResByAdmin(admin)
	result,msg := service.UpdateState(admin.ID,resInfo.ID,params.Id,tools.ToInt(params.State))
	if result {
		s.Success(c, result,msg, 200)
	} else {
		s.Success(c, nil, msg, -1000)
	}

}



/***
 * @Author: [rozimamat]
 * @description: 删除
 * @Date: 2024-03-13 11:40:03
 * @param {*gin.Context} c
 */
 func (s *SeckillController) Delete(c *gin.Context) {

	type Params struct {
		Id             int         `form:"id" json:"id" binding:"required"`
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchant.NewSeckillService(c)
		value, _   = c.Get("admin")
		admin      = value.(models.Admin)
		merchantService = merchant.NewMerchantService(c)
	)
	if err != nil {
		panic(err)
	}
	resInfo,_:=merchantService.GetResByAdmin(admin)
	result,msg := service.Delete(admin.ID,resInfo.ID,params.Id)
	if msg == "" {
		s.Success(c, result,msg, 200)
	} else {
		s.Success(c, nil, msg, -1000)
	}

}



/***
 * @Author: [rozimamat]
 * @description: 记录
 * @Date: 2024-03-14 16:40:03
 * @param {*gin.Context} c
 */
 func (s *SeckillController) Log(c *gin.Context) {

	type Params struct {
		Id             int         `form:"id" json:"id" binding:"required"`
		
	}
	var (
		params  Params
		// err     = c.ShouldBind(&params)
		service = merchant.NewSeckillService(c)
	)
	params.Id  = tools.ToInt(c.DefaultQuery("id","0"))
	// if err != nil {
	// 	panic(err)
	// }
	result,suc,msg := service.Log(params.Id)
	if suc {
		s.Success(c, result,msg, 200)
	} else {
		s.Success(c, nil, msg, -1000)
	}

}
