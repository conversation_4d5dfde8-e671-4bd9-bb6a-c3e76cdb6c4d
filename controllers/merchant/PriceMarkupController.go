package merchant

import (
	"fmt"
	"mulazim-api/controllers"
	"mulazim-api/permissions"
	merchantService "mulazim-api/services/merchant"
	"mulazim-api/tools"
	merchantTransformer "mulazim-api/transformers/merchant"

	"github.com/gin-gonic/gin"
)

type PriceMarkupController struct {
	controllers.BaseController
}

// HomeShowList
//
//  @Author: YaKupJan
//  @Date: 2024-10-22 19:10:16
//  @Description: 首页显示加价信息列表
//  @receiver p
//  @param c
func (p PriceMarkupController) HomeShowList(c *gin.Context) {
	// 获取登录的账号信息
	admin := permissions.GetAdmin(c)
	// 获取登录的账号的餐厅信息
	restaurant := admin.GetAdminRestaurant()
	var (
		service  = merchantService.NewPriceMarkupService(c)
		transformer = merchantTransformer.NewPriceMarkupTransformer(c)
	)
	// 查询 加价信息列表
	show, total := service.HomeShowList(restaurant.ID)
	// 获取admin手机号
	adminMobile := service.GetRestaurantAdminMobile(admin)
	// 格式化 加价信息列表
	listFormat := transformer.HomeShowListFormat(show)
	// 返回数据
	p.Success(c, gin.H{
		"items": listFormat,
		"total": total,
		"admin_mobile": adminMobile,
	}, "msg", 200)

}

// Handle
//
//  @Author: YaKupJan
//  @Date: 2024-10-23 12:23:56
//  @Description: 商家加价活动处理 1 同意 2 拒绝
//  @receiver p
//  @param c
func (p PriceMarkupController) Handle(c *gin.Context) {
	// 参数
	type Params struct {
		Id      int    `form:"id" binding:"required"`
		Agree   int    `form:"agree" binding:"required,oneof=1 2"` // 1 同意 2 拒绝
		Mobile  string `form:"mobile" binding:"required"`
		SmsCode string `form:"sms_code" binding:"required"`
	}
	var (
		params Params
		service  = merchantService.NewPriceMarkupService(c)
		redisHelper = tools.GetRedisHelper()
	)
	// 绑定参数
	err := c.ShouldBind(&params)
	if err != nil {
		panic(err)
	}
	// 验证手机号
	if !tools.VerifyMobileFormat(params.Mobile) {
		p.Fail(c, "mobile_incorrect", -1000)
		return
	}
	// 获取缓存的key并判断验证码是否正确
	cacheKey := fmt.Sprintf("merchant_mobile_%s", params.Mobile)
	get := redisHelper.Get(c, cacheKey)
	if get.Val() != params.SmsCode {
		p.Fail(c, "captcha_incorrect", -1000)
		return
	}
	// 获取登录的账号信息
	admin := permissions.GetAdmin(c)
	// 获取登录的账号的餐厅信息
	restaurant := admin.GetAdminRestaurant()
	// 商家处理加价活动
	err = service.Handle(params.Id,params.Agree,restaurant.ID)
	if err != nil{
		p.Fail(c,err.Error(),-1000)
		return
	}
	// 删除掉之前的验证码
	redisHelper.Del(c,cacheKey)
	// 返回数据
	p.Ok(c)
}

// SendSMSCode
//
//  @Author: YaKupJan
//  @Date: 2024-10-23 12:45:34
//  @Description: 发送验证码
//  @receiver p
//  @param c
func (p PriceMarkupController) SendSMSCode(c *gin.Context) {
	// 参数
	type Params struct {
		Mobile string `form:"mobile" binding:"required"`
	}
	var (
		params Params
		err    = c.ShouldBind(&params)
		service = merchantService.NewPriceMarkupService(c)
	)
	// 绑定参数
	if err != nil {
		p.Fail(c, err.Error(), -1000)
		return
	}
	// 检查手机号码合法性
	if !tools.VerifyMobileFormat(params.Mobile) {
		p.Fail(c, "mobile_incorrect", -1000)
		return
	}
	// 发送短信验证码
	err = service.SendSMSCode(c, params.Mobile)
	if err != nil {
		p.Fail(c, err.Error(), -1000)
		return
	}
	p.Ok(c)
}

// DetailList
//
//  @Author: YaKupJan
//  @Date: 2024-10-24 16:24:14
//  @Description: 加价活动详细列表
//  @receiver p
//  @param c
func (p PriceMarkupController) DetailList(c *gin.Context) {
	// 获取登录的账号信息
	admin := permissions.GetAdmin(c)
	// 获取登录的账号的餐厅信息
	restaurant := admin.GetAdminRestaurant()
	var (
		service  = merchantService.NewPriceMarkupService(c)
		transformer = merchantTransformer.NewPriceMarkupTransformer(c)
	)
	pagination := tools.GetPagination(c)
	// 查询 加价信息列表
	data, total := service.DetailList(restaurant.ID,pagination)
	// 格式化 加价信息列表
	listFormat := transformer.DetailListFormat(data)
	// 返回数据
	p.Success(c, gin.H{
		"items": listFormat,
		"total": total,
	}, "msg", 200)
}

// StatisticSailedMarket
//
//  @Author: YaKupJan
//  @Date: 2024-10-24 21:53:21
//  @Description: 统计页面显示美食销售标签
//  @receiver p
//  @param c
func (p PriceMarkupController) StatisticSailedMarket(c *gin.Context) {
	type Params struct {
		FoodsId   int    `form:"foods_id" json:"foods_id"  binding:"required" `
		StartDate string `form:"start_date" json:"start_date"  binding:"required" `
		EndDate   string `form:"end_date" json:"end_date"  binding:"required" `
	}
	var (
		params Params
		service  = merchantService.NewPriceMarkupService(c)
		//transformer = merchantTransformer.NewPriceMarkupTransformer(c)
	)
	if err := c.ShouldBindQuery(&params); err != nil {
		panic(err)
	}
	admin := permissions.GetAdmin(c)
	restaurant := admin.GetAdminRestaurant()
	statisticSailedMarket := service.StatisticSailedMarket(restaurant.ID,params.FoodsId, params.StartDate, params.EndDate)
	p.Success(c,statisticSailedMarket,"msg",200)
}

// StatisticSailedMarketDetail
//
//  @Author: YaKupJan
//  @Date: 2024-10-25 10:30:19
//  @Description: 统计页面美食加价销售明细
//  @receiver p
//  @param c
func (p PriceMarkupController) StatisticSailedMarketDetail(c *gin.Context) {
	type Params struct {
		FoodsId   int    `form:"foods_id" json:"foods_id"  binding:"required" `
		StartDate string `form:"start_date" json:"start_date"  binding:"required" `
		EndDate   string `form:"end_date" json:"end_date"  binding:"required" `
	}
	var (
		params Params
		service  = merchantService.NewPriceMarkupService(c)
		transformer = merchantTransformer.NewPriceMarkupTransformer(c)
	)
	if err := c.ShouldBindQuery(&params); err != nil {
		panic(err)
	}
	// 获取登录的账号信息
	admin := permissions.GetAdmin(c)
	// 获取登录的账号的餐厅信息
	restaurant := admin.GetAdminRestaurant()
	pagination := tools.GetPagination(c)
	detail, total := service.StatisticSailedMarketDetail(pagination,params.FoodsId, restaurant.ID,params.StartDate,params.EndDate)
	listFormat := transformer.StatisticSailedMarketDetailFormat(detail)
	p.Success(c, gin.H{
		"items": listFormat,
		"total": total,
	}, "msg", 200)
}


