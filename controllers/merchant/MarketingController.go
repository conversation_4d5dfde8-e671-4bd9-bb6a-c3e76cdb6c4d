package merchant

import (
	"mulazim-api/controllers"
	"mulazim-api/models"
	merchant "mulazim-api/services/merchant"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type MarketingController struct {
	controllers.BaseController
}

/***
 * @Author: [rozimamat]
 * @description: 活动首页
 * @Date: 2023-03-04 16:51:36
 * @param {*gin.Context} c
 */
func (marketing *MarketingController) Marketing(c *gin.Context) {

	var service = merchant.NewMarketingService(c)

	marketingList := service.GetMarketing(c)
	marketing.Success(c, marketingList, "msg", 200)
}

// List
//
// @Description: 获取营销活动列表
// @Author: Rixat
// @Time: 2023-03-02 10:36:12
// @receiver
// @param c *gin.Context
func (marketing *MarketingController) List(c *gin.Context) {
	type Params struct {
		ResId         int `form:"restaurant_id" binding:"required"`  // 餐厅ID
		State         int `form:"state"`                             // 餐厅ID
		Page          int `form:"page"`                              // 餐厅ID
		Limit         int `form:"limit"`                             // 餐厅ID
		MarketingType int `form:"marketing_type" binding:"required"` // 餐厅ID
		Type          int `form:"type"`                              //
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchant.NewMarketingService(c)
	)
	if err != nil {
		panic(err)
	}
	// 获取满减活动列表(分页)
	marketingList := service.GetMarketingList(params.ResId, params.MarketingType, params.State, params.Type, params.Page, params.Limit)
	marketing.Success(c, marketingList, "msg", 200)
}

/***
 * @Author: [rozimamat]
 * @description: 活动创建
 * @Date: 2023-03-04 11:40:03
 * @param {*gin.Context} c
 */
func (marketing *MarketingController) Create(c *gin.Context) {

	type Params struct {
		ResId         string `form:"restaurant_id" binding:"required"` // 店铺id
		MarketingType string `form:"marketing_type" binding:"required"`
		Type          string `form:"type" binding:"required"`
		BeginDate     string `form:"begin_date" binding:"required"`
		EndDate       string `form:"end_date" binding:"required"`
		FullWeekState string `form:"full_week_state"  binding:"required"`
		FullTimeState string `form:"full_time_state"  binding:"required"`
		AutoContinue  string `form:"auto_continue" binding:"required"`
		NameUg        string `form:"name_ug" binding:"required"`
		NameZh        string `form:"name_zh" binding:"required"`
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchant.NewMarketingService(c)
	)
	if err != nil {
		panic(err)
	}

	var allParams models.MarketingParam
	err = c.ShouldBind(&allParams)
	if err != nil {
		panic(err)
	}

	success, msg := service.CreateMarketing(allParams, 1)
	if success {
		marketing.Success(c, nil, "msg", 200)
	} else {
		marketing.Success(c, nil, msg, -1000)
	}

}

/***
 * @Author: [rozimamat]
 * @description: 活动编辑
 * @Date: 2023-03-04 11:39:51
 * @param {*gin.Context} c
 */
func (marketing *MarketingController) Edit(c *gin.Context) {

	type Params struct {
		Id int `form:"id" binding:"required"`
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchant.NewMarketingService(c)
	)
	if err != nil {
		panic(err)
	}

	success, msg, rs := service.EditMarketing(params.Id)

	if success {
		marketing.Success(c, rs, "msg", 200)
	} else {
		marketing.Success(c, nil, msg, -1000)
	}

}

// Update
//
// @Description: 更新满减活动信息
// @Author: Rixat
// @Time: 2023-03-03 16:29:10
// @receiver
// @param c *gin.Context
func (marketing *MarketingController) Update(c *gin.Context) {
	type Params struct {
		Id            int       `form:"id" binding:"required"`
		restaurantId  int       `form:"restaurant_id" binding:"required"`
		marketingType int       `form:"marketing_type" binding:"required"`
		beginDate     time.Time `form:"begin_date" binding:"required"`
		endDate       time.Time `form:"end_date" binding:"required"`
		fullWeekState int       `form:"full_week_state" binding:"required"`
		fullTimeState int       `form:"full_time_state" binding:"required"`
		autoContinue  int       `form:"auto_continue" binding:"required"`
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchant.NewMarketingService(c)
	)
	if err != nil {
		panic(err)
	}
	// 绑定客户端发送活动信息
	var allParams models.MarketingParam
	err = c.ShouldBind(&allParams)
	if err != nil {
		panic(err)
	}
	// 更新活动信息
	err = service.UpdateMarketing(allParams, params.Id, 1)
	if err != nil {
		marketing.Fail(c, err.Error(), -1000)
		return
	}
	marketing.Ok(c)
}

// UpdateState
//
// @Description: 修改满减活动状态
// @Author: Rixat
// @Time: 2023-03-03 16:49:23
// @receiver
// @param c *gin.Context
func (marketing *MarketingController) UpdateState(c *gin.Context) {
	type Params struct {
		Id    int    `form:"id" binding:"required"`
		State string `form:"state" binding:"required"`
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchant.NewMarketingService(c)
	)
	if err != nil {
		panic(err)
	}
	// 更新状态
	state, _ := strconv.Atoi(params.State)
	err = service.UpadateMarketingState(params.Id, state)
	if err != nil {
		marketing.Fail(c, err.Error(), -1000)
		return
	}
	marketing.Ok(c)
}

// Delete
//
// @Description: 删除满减活动
// @Author: Rixat
// @Time: 2023-03-03 16:49:40
// @receiver
// @param c *gin.Context
func (marketing *MarketingController) Delete(c *gin.Context) {
	type Params struct {
		MarketId int `form:"id"` // 餐厅ID
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchant.NewMarketingService(c)
	)
	if err != nil {
		panic(err)
	}
	// 删除活动
	err = service.DeleteMarketing(params.MarketId)
	if err != nil {
		marketing.Fail(c, err.Error(), -1000)
		return
	}
	marketing.Ok(c)
}

/***
 * @Author: [rozimamat]
 * @description: 活动详细页面统计
 * @Date: 2023-03-04 11:39:37
 * @param {*gin.Context} c
 */
func (marketing *MarketingController) Detail(c *gin.Context) {

	type Params struct {
		Id int `form:"id" binding:"required"`
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchant.NewMarketingService(c)
	)
	if err != nil {
		panic(err)
	}
	success, msg, m := service.MarketingDetail(params.Id)
	if success {
		marketing.Success(c, m, "msg", 200)
	} else {
		marketing.Success(c, nil, msg, -1000)
	}

}

/***
 * @Author: [rozimamat]
 * @description: 活动 统计
 * @Date: 2023-03-04 11:39:23
 * @param {*gin.Context} c
 */
func (marketing *MarketingController) Statistics(c *gin.Context) {

	type Params struct {
		StartDate string `form:"start_date" binding:"required"`
		EndDate   string `form:"end_date" binding:"required"`
		ResId     int    `form:"restaurant_id" binding:"required"`
		Target    int    `form:"target" `
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchant.NewMarketingService(c)
	)
	if err != nil {
		panic(err)
	}
	if params.Target == 0 {
		params.Target = 1
	}
	success, msg, m := service.MarketingStatistics(params.StartDate, params.EndDate, params.ResId, params.Target)
	if success {
		marketing.Success(c, m, "msg", 200)
	} else {
		marketing.Success(c, nil, msg, -1000)
	}

}

/***
 * @Author: [rozimamat]
 * @description: 参与代理组织的活动
 * @Date: 2023-03-04 11:40:42
 * @param {*gin.Context} c
 */
func (marketing *MarketingController) Join(c *gin.Context) {

	type Params struct {
		GoupId       int    `form:"group_id" binding:"required"`
		RestaurantId int    `form:"restaurant_id" binding:"required"`
		AdminId      int    `form:"admin_id" binding:"required"`
		PassWord     string `form:"password" binding:"required"`
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchant.NewMarketingService(c)
	)
	if err != nil {
		panic(err)
	}
	success, msg := service.MarketingJoin(params.GoupId, params.AdminId, params.RestaurantId, params.PassWord)
	if success {
		marketing.Success(c, nil, "msg", 200)
	} else {
		marketing.Success(c, nil, msg, -1000)
	}

}
