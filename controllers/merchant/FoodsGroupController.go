package merchant

import (
	"encoding/json"
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/marketingRequest"
	"mulazim-api/requests/merchantRequest/food"
	merchantService "mulazim-api/services/merchant"
	"mulazim-api/tools"
	merchantTransformer "mulazim-api/transformers/merchant"

	"github.com/gin-gonic/gin"
)

type FoodsGroupController struct {
	controllers.BaseController
}

// Create
//
// @Description: 创建美食分组
// @Author: Rixat
// @Time: 2024-09-25 10:51:35
// @receiver 
// @param c *gin.Context
func (f *FoodsGroupController) Create(c *gin.Context) {
	type Params struct {
		NameUg       string `form:"name_ug" binding:"required"`
		NameZh       string `form:"name_zh" binding:"required"`
		State        *int `form:"state" binding:"oneof=0 1"`
	}
	var (
		params  Params
		service = merchantService.NewFoodsGroupService(c)
		err     = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取登录用户属于餐厅
	resInfo,err := permissions.GetRestaurantInfoByContent(c)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}
	// 验证分组
	if err := service.ValidateCreateParams(resInfo.ID, 0,params.NameUg, params.NameZh,*params.State); err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}
	// 创建分组
	admin := permissions.GetAdmin(c)
	if err := service.CreateGroup(admin, resInfo.ID, params.NameUg, params.NameZh, *params.State); err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}
	f.Ok(c)
}

// Edit
//
// @Description: 编辑美食分组
// @Author: Rixat
// @Time: 2024-09-25 12:43:19
// @receiver 
// @param c *gin.Context
func (f FoodsGroupController) Edit(c *gin.Context) {
	type Params struct {
		Id     int    `form:"id" binding:"required"`
		NameUg string `form:"name_ug" binding:"required"`
		NameZh string `form:"name_zh" binding:"required"`
		State  *int    `form:"state" binding:"oneof=0 1"`
	}
	var (
		params  Params
		service = merchantService.NewFoodsGroupService(c)
		err     = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取登录用户属于餐厅
	resInfo,err := permissions.GetRestaurantInfoByContent(c)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}
	// 验证分组
	if err := service.ValidateCreateParams(resInfo.ID,params.Id, params.NameUg, params.NameZh,*params.State); err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}
	// 编辑分组
	admin := permissions.GetAdmin(c)
	if err := service.Edit(admin,params.Id, params.NameUg, params.NameZh,*params.State); err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}
	f.Ok(c)
}

// Delete
// 删除分组
// @Description: 
// @Author: Rixat
// @Time: 2024-09-25 12:59:19
// @receiver 
// @param c *gin.Context
func (f FoodsGroupController) Delete(c *gin.Context) {
	type Params struct {
		Id     int    `form:"id" binding:"required"`
	}
	var (
		params  Params
		service = merchantService.NewFoodsGroupService(c)
		err     = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	// 删除
	err = service.Delete(params.Id)
	if err!=nil{
		f.Fail(c, err.Error(), -1000)
		return
	}
	f.Ok(c)
}

// List
//
// @Description: 分组列表
// @Author: Rixat
// @Time: 2024-09-25 12:59:39
// @receiver 
// @param c *gin.Context
func (f FoodsGroupController) List(c *gin.Context) {
	type Params struct {
		State     *int    `form:"state" binding:""`
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchantService.NewFoodsGroupService(c)
		transformer = merchantTransformer.NewFoodsGroupTransformer(c)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取登录用户属于餐厅的分组列表
	resInfo, _ := permissions.GetRestaurantInfoByContent(c)
	foodsGroupList := service.List(resInfo.ID,params.State)
	// 格式化
	items := transformer.ListFormat(foodsGroupList)
	f.Success(c, items, "success", 200)
}


// List
//
// @Description: 分组列表
// @Author: Rixat
// @Time: 2024-09-25 12:59:39
// @receiver 
// @param c *gin.Context
func (f FoodsGroupController) GetRecommendGroup(c *gin.Context) {
	type Params struct {
		Kw     string    `form:"kw" binding:""`
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		service = merchantService.NewFoodsGroupService(c)
		transformer = merchantTransformer.NewFoodsGroupTransformer(c)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取登录用户属于餐厅的分组列表
	foodsGroupList := service.GetRecommendGroup(params.Kw)
	// 格式化
	items := transformer.ListRecommendFormat(foodsGroupList)
	f.Success(c, items, "success", 200)
}


// Detail
//
// @Description: 分组详情
// @Author: Rixat
// @Time: 2024-09-25 13:06:08
// @receiver 
// @param c *gin.Context
func (f FoodsGroupController) Detail(c *gin.Context) {
	type Params struct {
		ID int    `form:"id" binding:"required"`
	}
	var (
		params  Params
		service = merchantService.NewFoodsGroupService(c)
		transformer = merchantTransformer.NewFoodsGroupTransformer(c)
		err     = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取分组详情
	detail := service.Detail(params.ID)
	if detail.ID == 0 {
		f.Fail(c, "not_found", -1000)
		return
	}
	// 格式化
	items := transformer.DetailFormat(detail)
	f.Success(c, items, "success", 200)
}

// FoodsListByGroupId
//
// @Description: 根据分组ID获取美食列表
// @Author: Rixat
// @Time: 2024-09-25 13:06:46
// @receiver 
// @param c *gin.Context
func (f FoodsGroupController) FoodsListByGroupId(c *gin.Context) {
	type Params struct {
		GroupID int `form:"group_id" binding:""`
		Kw string `form:"kw" binding:""`
		FoodTypes []int `form:"food_types" binding:""`
	}
	var (
		params  Params
		service = merchantService.NewFoodsGroupService(c)
		transformer = merchantTransformer.NewFoodsGroupTransformer(c)
		err     = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取分组列表信息
	pagination := tools.GetPagination(c)
	resInfo,_ := permissions.GetRestaurantInfoByContent(c)
	foodsList, totalCount := service.FoodsListByGroupId(resInfo.ID, params.GroupID, params.Kw, pagination, params.FoodTypes)
	// 格式化
	items := transformer.FoodsListByGroupIdFormat(foodsList)
	f.Success(c, map[string]interface{}{
		"items": items,
		"total": totalCount,
	}, "success", 200)
}

// PostSetGroupOrder
//
// @Description: 设置分组排序
// @Author: Rixat
// @Time: 2024-09-25 13:17:26
// @receiver 
// @param c *gin.Context
func (f FoodsGroupController) PostSetGroupOrder(c *gin.Context) {
	type Params struct {
		Items string `form:"items" binding:"required"`
	}
	var (
		params  Params
		service = merchantService.NewFoodsGroupService(c)
		err     = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	// 解析分组参数
	var groups []models.FoodsGroup
	err = json.Unmarshal([]byte(params.Items), &groups)
	if err != nil {
		panic(err)
		return
	}
	// 更新分组排序
	resInfo,_ := permissions.GetRestaurantInfoByContent(c)
	err = service.EditFoodsGroupWeights(resInfo.ID,groups)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return 
	}
	f.Ok(c)
}

// PostCreateFoods
//
// @Description: 添加美食
// @Author: Rixat
// @Time: 2024-09-25 13:19:31
// @receiver 
// @param c *gin.Context
func (f FoodsGroupController) PostCreateFoods(c *gin.Context) {
	var (
		merchantService = merchantService.NewMerchantService(c)
		request = food.FoodCreateRequestNew{}
		err     = c.ShouldBind(&request)
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
		adminRes = admin.GetAdminRestaurant()
	)
	if err != nil {
		panic(err)
		return
	}
	request.RestaurantID = int64(adminRes.ID)
	//  验证营业时间与美食时间是否冲突
	if code,msg := request.ValidateOpenTime(); code != marketingRequest.SUCCESS {
		if code == marketingRequest.TIME_CONFILICT {
			f.Fail(c, "time_confilict_with_res", -1000)
			return
		}else{
			f.Fail(c, msg, -1000)
			return
		}
	}
	// 添加美食
	suc, msg, createdFood := merchantService.PostCreateFoodNew(request)
	if suc {
		f.Success(c, []int{}, msg, 200)
		tools.ImportantLog(
			"merchant_food_update",
			admin.ID,
			createdFood.ID,
			adminRes.ID,
			request,
		)
		return
	}
	f.Fail(c, msg, -1000)
}


// PostUpdateFoods
//
// @Description: 更新美食
// @Author: Rixat
// @Time: 2024-09-25 13:20:30
// @receiver 
// @param c *gin.Context
func (f FoodsGroupController) PostUpdateFoods(c *gin.Context) {
	var (
		merchantService = merchantService.NewMerchantService(c)
		request = food.FoodUpdateRequestNew{}
		err     = c.ShouldBind(&request)
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
		adminRes = admin.GetAdminRestaurant()
	)
	if err != nil {
		panic(err)
		return
	}
	request.RestaurantID = int64(adminRes.ID)
	//  验证营业时间与美食时间是否冲突
	if code,msg := request.ValidateOpenTime(); code != marketingRequest.SUCCESS {
		if code == marketingRequest.TIME_CONFILICT {
			f.Fail(c, "time_confilict_with_res", -1000)
			return
		}else{
			f.Fail(c, msg, -1000)
			return
		}
	}
	// 更新美食信息
	suc, msg := merchantService.PostUpdateFoodNew(request)
	if suc {
		f.Success(c, []int{}, msg, 200)
		tools.ImportantLog(
			"merchant_food_update",
			admin.ID,
			request.ID,
			adminRes.ID,
			request,
		)
		return
	}
	f.Fail(c, msg, -1000)
}


// GetDetail
//
// @Description: 美食详情
// @Author: Rixat
// @Time: 2024-09-25 13:21:05
// @receiver 
// @param c *gin.Context
func (f FoodsGroupController) GetDetail(c *gin.Context) {
	type Params struct {
		FoodId   int `form:"id" binding:"required"`  // 美食ID
	}
	var (
		params  Params
		err     = c.ShouldBind(&params)
		value, _    = c.Get("admin")
		admin       = value.(models.Admin)
		service      = merchantService.NewRestaurantFoodsService(c)
		transformer     = merchantTransformer.NewRestaurantFoodsTransformer(c)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取美食信息
	foodInfo,err := service.GetRestaurantFoodInfoById(params.FoodId)
	if  err != nil {
		f.Fail(c,err.Error(),-1000)
		return 
	}
	// 验证是否属于该餐厅
	resId,  err:= permissions.GetRestaurantIdByMerchantAdmin(admin)
	if err != nil {
		f.Fail(c,"not_found",-1000)
		return 
	}
	if foodInfo.ID == 0 || foodInfo.RestaurantID != resId {
		f.Fail(c,"not_found",-1000)
		return 
	}
	// 格式化美食数据
	res := transformer.FormatFoodsInformation(foodInfo)
	f.Success(c, res, "msg", 200)
	
}

// GetDetailNew
//
// @Description: 美食详情
// @Author: Rixat
// @Time: 2024-09-25 13:21:05
// @receiver
// @param c *gin.Context
func (f FoodsGroupController) GetDetailNew(c *gin.Context) {
	type Params struct {
		FoodId int `form:"id" binding:"required"` // 美食ID
	}
	var (
		params      Params
		err         = c.ShouldBind(&params)
		value, _    = c.Get("admin")
		admin       = value.(models.Admin)
		service     = merchantService.NewRestaurantFoodsService(c)
		transformer = merchantTransformer.NewRestaurantFoodsTransformer(c)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取美食信息
	foodInfo, err := service.GetRestaurantFoodInfoById(params.FoodId)
	if err != nil {
		f.Fail(c, err.Error(), -1000)
		return
	}
	// 验证是否属于该餐厅
	resId, err := permissions.GetRestaurantIdByMerchantAdmin(admin)
	if err != nil {
		f.Fail(c, "not_found", -1000)
		return
	}
	if foodInfo.ID == 0 || foodInfo.RestaurantID != resId {
		f.Fail(c, "not_found", -1000)
		return
	}

	// 格式化美食数据
	res := transformer.FormatFoodsInformationNew(foodInfo)
	f.Success(c, res, "msg", 200)
}
