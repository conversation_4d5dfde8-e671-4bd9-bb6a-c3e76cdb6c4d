package merchant

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/errors"
	"mulazim-api/lang"
	"mulazim-api/middlewares"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/marketingRequest"
	"mulazim-api/requests/merchantRequest/food"
	merchantFoodRequest "mulazim-api/requests/merchantRequest/food"
	"mulazim-api/requests/merchantRequest/user"
	merchantResources "mulazim-api/resources/merchant"
	"mulazim-api/services"
	cmsService "mulazim-api/services/cms"
	merchantService "mulazim-api/services/merchant"
	lakalaService "mulazim-api/services/merchant/lakala"
	"mulazim-api/tools"
	engine "mulazim-api/tools"
	merchantTransfomer "mulazim-api/transformers/merchant"
	merchantTransformer "mulazim-api/transformers/merchant"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type MerchantController struct {
	controllers.BaseController
}

// MerchantNewOrderList
//
//	@Description: 商家端获取新订单列表
//	@author: Alimjan
//	@Time: 2022-10-15 17:40:35
//	@receiver merchant *MerchantController
//	@param c *gin.Context
func (merchant *MerchantController) MerchantNewOrderList(c *gin.Context) {
	var (
		redisHelper         = engine.GetRedisHelper()
		l, _                = c.Get("lang_util")
		lang                = l.(lang.LangUtil)
		restaurantId        = c.Query("restaurant_id")
		orderType           = c.DefaultQuery("order_type", "2")
		merchantService     = merchantService.NewMerchantService(c)
		merchantTransformer = merchantTransformer.NewMerchantTransformer(c)
		businessType        = 1
		orderToday          []models.OrderToday
		resAreaRedisKey     = "rark_" + restaurantId
		receiveOrderTime    int64
	)

	//  获取餐厅信息

	if exists, _ := redisHelper.Exists(c, resAreaRedisKey).Result(); exists == 0 {
		restaurant := merchantService.GetRestaurant(restaurantId)
		area := merchantService.GetArea(fmt.Sprintf("%d", restaurant.AreaID))
		redisHelper.Set(c, resAreaRedisKey, area.ReceiveOrderTime, 15*time.Minute)
	}
	receiveOrderTime, _ = redisHelper.Get(c, resAreaRedisKey).Int64()


	 

	//  获取新订单
	orderToday = merchantService.GetNewOrders(restaurantId, int(receiveOrderTime), orderType, c)

	showCustomerInfo := merchantService.GetShowCustomerInfo(c, restaurantId)

	//  格式化返回数据
	formattedOrderToday := merchantTransformer.FormatNewOrder(c, orderToday, int(receiveOrderTime), showCustomerInfo)

	//  获取新订单，已接收订单数量，失败订单，已完成订单，
	merchantService.GetOrderCountForHeader(restaurantId, businessType, &formattedOrderToday)
	//  获取是否有新的订单需要播放声音
	notifications := merchantService.GetVoiceOrderCount(restaurantId, businessType, &formattedOrderToday)
	
	
	formattedOrderToday.VoiceOrderCount = 0 //旧版本 兼容 不然他发出 两次声音
	

	merchantService.UpdateResLoginTimeAndOrderTime(restaurantId, c.Query("device_token"), notifications)
	c.JSON(200, gin.H{
		"data":   formattedOrderToday,
		"msg":    lang.T("msg"),
		"status": 200,
	})
}

// GetRestaurantInfo 获取餐厅信息
//
//	@Description:
//	@receiver merchant
//	@param context
func (merchant *MerchantController) GetRestaurantInfo(c *gin.Context) {
	var (
		l, _                = c.Get("lang_util")
		lang                = l.(lang.LangUtil)
		restaurantId        = c.Query("restaurant_id")
		merchantService     = merchantService.NewMerchantService(c)
		merchantTransformer = merchantTransformer.NewMerchantTransformer(c)
	)
	// 通过登录商家信息获取餐厅ID
	admin := permissions.GetAdmin(c)
	resId,_ := permissions.GetRestaurantIdByMerchantAdmin(admin)
	restaurantId = tools.ToString(resId)
	//获取餐厅信息
	result := merchantService.RestaurantPersonal(restaurantId)
	//获取餐厅新订单数
	newCommentCount := merchantService.GetCommentCountByResIdAndTime(result)
	//格式化返回数据
	data := merchantTransformer.FormatRestaurantPersonal(result, newCommentCount)
	c.JSON(200, gin.H{
		"msg":    lang.T("msg"),
		"status": 200,
		"data":   data,
	})
}

// Login
//
//	@Description: 商家端登录
//	@author: Alimjan
//	@Time: 2022-09-23 17:09:30
//	@receiver merchant *MerchantController
//	@param context *gin.Context
func (merchant *MerchantController) Login(c *gin.Context) {
	var (
		merchantService     = merchantService.NewMerchantService(c)
		name                = c.PostForm("name")
		password            = c.PostForm("password")
		clientId            = c.PostForm("client_id")
		clientSecret        = c.PostForm("client_secret")
		grantType           = c.PostForm("grant_type")
		searialNumber       = c.GetHeader("searialNumber")
		merchantTransformer = merchantTransfomer.NewMerchantTransformer(c)
	)
	// 前后清空
	name = strings.TrimSpace(name)
	success, admin, errMsg := merchantService.LoginCheck(name, password, clientId, clientSecret, grantType, searialNumber)
	if success {
		// tokenMap := merchantService.GenerateToken(admin, clientId, searialNumber)
		//token := tokenMap["access_token"].(string)
		err := merchantService.CleanOldSessionAndToken(admin)
		if err != nil {
			tools.Logger.Error("无法删除session和token", err)
		}
		resInfo, dealerInfo, areaInfo := merchantService.GetRestaurantByAdmin(c, admin)
		token,jwtSerialNumber := tools.NewJWTTools().GenerateToken(map[string]interface{}{
			"id": admin.ID,
			"name": admin.Name,
			"mobile": admin.Mobile,
			"type": admin.Type,
			"grant_type": grantType,
			"searialNumber": searialNumber,
			"area_id": admin.AdminAreaID,
			"restaurant_id":tools.ToInt64(resInfo["id"]),
		})
		//更新t_admin的jwt_serial_number 字段
		tools.Db.Model(&models.Admin{}).Where("id = ?", admin.ID).Update("jwt_serial_number", jwtSerialNumber)
		if resInfo == nil {
			merchant.Fail(c, "merchant_not_found", -1000)
            return
		}
		if tools.ToInt(resInfo["state"])  ==  0{
			merchant.Fail(c, "restaurant_state_close", -1000)
            return
		}
		var loginEntity merchantResources.MerchantLoginEntity
		loginEntity = merchantTransformer.Login(token, admin, resInfo, dealerInfo, areaInfo)

		merchant.Success(c, loginEntity, "msg", 200)
	} else {
		merchant.Fail(c, errMsg, -1000)
	}
}

// GetSmartAppCode
//
//	@Description: 生成采集餐厅员工微信信息的二维码（小程序吗）
//	@author: Captain
//	@Time: 2022-11-03 10:28:48
//	@receiver merchant *MerchantController
//	@param c *gin.Context
func (merchant *MerchantController) GetSmartAppCode(c *gin.Context) {
	var (
		l, _            = c.Get("lang_util")
		lang            = l.(lang.LangUtil)
		restaurantId, _ = strconv.Atoi(c.Query("restaurant_id"))
		roleId, _       = strconv.Atoi(c.Query("role_id"))
		adminId, _      = strconv.Atoi(c.Query("admin_id"))
		password        = c.Query("password")
		merchantService = merchantService.NewMerchantService(c)
	)
	if true {
		c.JSON(200, gin.H{
			"msg":    lang.T("function_maintenance"),
			"status": 200,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
		return
	}
	if roleId < 1 || roleId > 3 {
		c.JSON(-1000, gin.H{
			"status": -1000,
			"msg":    lang.T("merchant_role_id_error"),
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
		return
	}
	rs, msg := merchantService.ValidateSmartAppCodeParams(adminId, password, restaurantId, roleId)
	if rs {
		result, qrCode := merchantService.GetQrCode(c, restaurantId, roleId, lang.Lang)
		if result {
			c.JSON(200, gin.H{
				"msg":    lang.T("msg"),
				"status": 200,
				"data":   configs.MyApp.CdnUrl + qrCode,
				"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
			})
		} else {
			c.JSON(-1000, gin.H{
				"msg":    lang.T(msg),
				"status": -1000,
				"data":   qrCode,
				"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
			})
		}
	} else {
		c.JSON(-1000, gin.H{
			"status": 1000,
			"msg":    lang.T(msg),
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	}

}

// GetNotCashedList
//
//	@Description: 获取商家未提现账单列表
//	@author: Captain
//	@Time: 2022-11-07 10:02:07
//	@receiver merchant MerchantController
//	@param c *gin.Context
func (merchant MerchantController) GetNotCashedList(c *gin.Context) {
	var (
		l, _                = c.Get("lang_util")
		lang                = l.(lang.LangUtil)
		restaurantId, _     = strconv.Atoi(c.Query("restaurant_id"))
		merchantService     = merchantService.NewMerchantService(c)
		merchantTransformer = merchantTransfomer.NewMerchantTransformer(c)
	)
	rs, totalAmount, list := merchantService.GetNoMontionList(restaurantId)
	if rs {

		NotCashedList := merchantTransformer.FormatNotCashedList(totalAmount, list)
		c.JSON(200, gin.H{
			"msg":    lang.T("msg"),
			"status": 200,
			"data":   NotCashedList,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	} else {
		c.JSON(-1000, gin.H{
			"status": 1000,
			"msg":    lang.T("failed"),
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	}
}

// GetReceivedOrderList
//
//	@Time 2022-09-23 11:28:44
//	<AUTHOR>
//	@Description: 获取商家已接单订单列表接口
//	@receiver merchant *MerchantController
//	@param c
func (merchant *MerchantController) GetReceivedOrderList(c *gin.Context) {
	var (
		restaurantId     = c.Query("restaurant_id")
		service          = merchantService.NewMerchantService(c)
		transformer      = merchantTransformer.NewMerchantTransformer(c)
		redisHelper      = engine.GetRedisHelper()
		resAreaRedisKey  = "rark_" + restaurantId
		receiveOrderTime int64
	)
	list := service.GetRestaurantOrderListByState(c, restaurantId, []int{4, 5})
	//  获取餐厅信息

	if exists, _ := redisHelper.Exists(c, resAreaRedisKey).Result(); exists == 0 {
		restaurant := service.GetRestaurant(restaurantId)
		area := service.GetArea(fmt.Sprintf("%d", restaurant.AreaID))
		redisHelper.Set(c, resAreaRedisKey, area.ReceiveOrderTime, 15*time.Minute)
	}
	receiveOrderTime, _ = redisHelper.Get(c, resAreaRedisKey).Int64()

	showCustomerInfo := service.GetShowCustomerInfo(c, restaurantId)
	data := transformer.FormatRestaurantOrderList(c, list, receiveOrderTime, showCustomerInfo, "Received")
	service.GetOrderCountForHeader(restaurantId, 1, &data)
	merchant.Success(c, data, "msg", 200)
}

// GetCompletedOrderList
//
//	@Time 2022-09-24 15:58:54
//	<AUTHOR>
//	@Description: 获取餐厅当日已完成的订单列表（包括正在配送和已送完的订单）
//	@receiver merchant *MerchantController
//	@param context
func (merchant *MerchantController) GetCompletedOrderList(c *gin.Context) {
	var (
		restaurantId = c.Query("restaurant_id")
		service      = merchantService.NewMerchantService(c)
		transformer  = merchantTransformer.NewMerchantTransformer(c)

		redisHelper      = engine.GetRedisHelper()
		resAreaRedisKey  = "rark_" + restaurantId
		receiveOrderTime int64
	)
	list := service.GetRestaurantOrderListByState(c, restaurantId, []int{6, 7, 10})

	//  获取餐厅信息

	if exists, _ := redisHelper.Exists(c, resAreaRedisKey).Result(); exists == 0 {
		restaurant := service.GetRestaurant(restaurantId)
		area := service.GetArea(fmt.Sprintf("%d", restaurant.AreaID))
		redisHelper.Set(c, resAreaRedisKey, area.ReceiveOrderTime, 15*time.Minute)
	}

	receiveOrderTime, _ = redisHelper.Get(c, resAreaRedisKey).Int64()
	showCustomerInfo := service.GetShowCustomerInfo(c, restaurantId)

	data := transformer.FormatRestaurantOrderList(c, list, receiveOrderTime, showCustomerInfo, "Completed")
	service.GetOrderCountForHeader(restaurantId, 1, &data)
	merchant.Success(c, data, "msg", 200)

}

// GetCanceledOrderList
//
//	@Time 2022-09-24 16:00:35
//	<AUTHOR>
//	@Description: 获取餐厅当日已取消的订单列表（包括客户取消的和餐厅拒绝的订单）
//	@receiver merchant *MerchantController
//	@param context
func (merchant *MerchantController) GetCanceledOrderList(c *gin.Context) {
	var (
		restaurantId     = c.Query("restaurant_id")
		service          = merchantService.NewMerchantService(c)
		transformer      = merchantTransformer.NewMerchantTransformer(c)
		redisHelper      = engine.GetRedisHelper()
		resAreaRedisKey  = "rark_" + restaurantId
		receiveOrderTime int64
	)
	list := service.GetRestaurantOrderListByState(c, restaurantId, []int{8, 9})

	//  获取餐厅信息

	if exists, _ := redisHelper.Exists(c, resAreaRedisKey).Result(); exists == 0 {
		restaurant := service.GetRestaurant(restaurantId)
		area := service.GetArea(fmt.Sprintf("%d", restaurant.AreaID))
		redisHelper.Set(c, resAreaRedisKey, area.ReceiveOrderTime, 15*time.Minute)
	}
	receiveOrderTime, _ = redisHelper.Get(c, resAreaRedisKey).Int64()
	showCustomerInfo := service.GetShowCustomerInfo(c, restaurantId)
	data := transformer.FormatRestaurantOrderList(c, list, receiveOrderTime, showCustomerInfo, "Canceled")
	service.GetOrderCountForHeader(restaurantId, 1, &data)
	merchant.Success(c, data, "msg", 200)
}

// GetBusinessStatistics
//
//	@Time 2022-09-30 12:28:57
//	<AUTHOR>
//	@Description: 餐厅营业统计接口
//	@receiver merchant *MerchantController
//	@param context
func (merchant *MerchantController) GetBusinessStatistics(c *gin.Context) {
	var (
		restaurantId = c.Query("restaurant_id")
		startDate    = c.Query("start_date")
		endDate      = c.Query("end_date")
		service      = merchantService.NewMerchantService(c)
		transformer  = merchantTransformer.NewMerchantTransformer(c)
	)
	statistics := service.BusinessStatistics(restaurantId, startDate, endDate)
	restaurantStatistics := transformer.FormatRestaurantStatistics(statistics)
	merchant.Success(c, restaurantStatistics, "msg", 200)
}


// GetFoodsCategoryList
//
//	@Time 2022-09-30 12:31:18
//	<AUTHOR>
//	@Description: 获取美食分类列表
//	@receiver merchant *MerchantController
//	@param c
func (merchant *MerchantController) GetFoodsCategoryList(c *gin.Context) {
	var (
		keyword = c.Query("keyword")
		service = merchantService.NewMerchantService(c)
	)
	resInfo, err := permissions.GetRestaurantInfoByContent(c)
	if err!= nil {
		merchant.Fail(c, "restaurant_not_found", -1000)
		return
	}
	list := service.SearchCategory(c, keyword,resInfo.ID)
	merchant.Success(c, list, "msg", 200)
}


// PostCashOut
//
//	@Description: 商家提现接口
//	@author: Captain
//	@Time: 2022-11-07 16:49:49
//	@receiver merchant MerchantController
//	@param c *gin.Context
func (merchant MerchantController) PostCashOut(c *gin.Context) {
	var (
		l, _            = c.Get("lang_util")
		lang            = l.(lang.LangUtil)
		restaurantId, _ = strconv.Atoi(c.PostForm("restaurant_id"))
		// presentationPassword = string(c.PostForm("presentation_password"))
		withdrawalAmount, _ = strconv.Atoi(c.PostForm("withdrawal_amount"))
		adminId, _          = strconv.Atoi(c.PostForm("admin_id"))
		cardId              = c.PostForm("card_id")
		billIds             = engine.ReadIntArrayParam(c)
		merchantService     = merchantService.NewMerchantService(c)
		lakaService         = lakalaService.NewLakalaService(c)
		//	//merchantTransformer = merchantTransfomer.NewMerchantTransformer(c)
	)
	//提现平台查询
	isLakala := merchantService.IsLakalaCashOut(restaurantId)
	rs := false
	msg := ""
	if isLakala { //拉卡拉提现
		if len(strings.TrimSpace(cardId)) == 0 {
			c.JSON(-1000, gin.H{
				"status": -1000,
				"msg":    lang.T("res_manager_app_is_old"), //卡号未传递 表示客户端版本过期，提示更新版本
				"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
			})
			return
		}
		rs, msg = lakaService.CashOutLakala(c, adminId, restaurantId, withdrawalAmount, billIds, lang.Lang, cardId)

	} else { // 普通提现 微信
		//停止微信提现
		// rs, msg = merchantService.CashOut(restaurantId, presentationPassword, withdrawalAmount, billIds, lang.Lang)
		rs = false
		msg = "wechat_withdraw_stop_msg"
	}
	if rs {
		var result map[string]interface{}
		json.Unmarshal([]byte(msg), &result)
		var data = make([]map[string]interface{}, 0)
		c.JSON(200, gin.H{
			"status": result["code"],
			"msg":    result["message"],
			"data":   data,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	} else {
		c.JSON(-1000, gin.H{
			"status": -1000,
			"msg":    lang.T(msg),
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
	}
}

// GetMerchantStuff
//
//	@Time 2022-10-02 22:21:21
//	<AUTHOR>
//	@Description: 获取餐厅员工绑定信息
//	@receiver merchant *MerchantController
//	@param c
func (merchant *MerchantController) GetMerchantStuff(c *gin.Context) {
	var (
		restaurantId = c.Query("restaurant_id")
		service      = merchantService.NewMerchantService(c)
	)
	stuff := service.GetRestaurantStuff(restaurantId)
	if stuff == nil {
		merchant.Success(c, []int{}, "msg", 200)
	} else {
		merchant.Success(c, stuff, "msg", 200)
	}

}

// PostDeleteReply
//
//	@Time 2022-10-02 22:21:35
//	<AUTHOR>
//	@Description: 删除商家评论
//	@receiver merchant *MerchantController
//	@param c
func (merchant *MerchantController) PostDeleteReply(c *gin.Context) {
	var (
		replyId = c.Query("reply_id")
		service = merchantService.NewMerchantService(c)
	)
	service.DeleteReply(replyId)
	merchant.Success(c, []int{}, "msg", 200)

}

// PostUpdateCommentTime
//
//	@Time 2022-10-02 22:21:47
//	<AUTHOR>
//	@Description: 更新商家阅读评论时间
//	@receiver merchant *MerchantController
//	@param c
func (merchant *MerchantController) PostUpdateCommentTime(c *gin.Context) {
	var (
		l, _         = c.Get("lang_util")
		lang         = l.(lang.LangUtil)
		restaurantId = c.Query("restaurant_id")
		service      = merchantService.NewMerchantService(c)
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
	)
	adminRes := admin.GetAdminRestaurant()

	if adminRes.ID==0 {
		merchant.Fail(c, "admin_belong_merchant_not_found", -1000)
		return
	}
	restaurantId = fmt.Sprintf("%d", int64(adminRes.ID))

	service.UpdateCommentTime(restaurantId)
	c.JSON(200, gin.H{
		"status": 200,
		"msg":    lang.T("msg"),
		"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
	})
}

// PostUpdateFoodState
//
//	@Time 2022-12-15 18:51:17
//	<AUTHOR>
//	@Description: 更新美食状态
//	@receiver merchant *MerchantController
//	@param c
func (merchant *MerchantController) PostUpdateFoodState(c *gin.Context) {
	var (
		l, _     = c.Get("lang_util")
		lang     = l.(lang.LangUtil)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		id       = c.PostForm("id")
		service  = merchantService.NewMerchantService(c)
	)
	//  适配flutter 客户端发送的错误代码
	if id=="" {
		id = c.Query("id")
	}
	restaurant := service.GetRestaurantByAdmin1(admin)
	restaurantId := restaurant.ID
	isOk, msg,foodState := service.UpdateFoodState(id, restaurantId)
	if isOk == true {
		merchant.Success(c, foodState, lang.T("msg"), 200)
		tools.ImportantLog(
			"merchant_food_state_update",
			admin.ID,
			restaurantId,
			restaurantId,
			foodState,
		)

	} else {
		merchant.Fail(c, lang.T(msg), -1000)
	}

}

// ChangeFoodState
//
//	@Time 2024-8-14 13:51:17
//	<AUTHOR>
//	@Description: 将美食的state设为2或1或0，如果美食的状态本来就是0会报错
//	@receiver merchant *MerchantController
//	@param c
func (merchant *MerchantController) ChangeFoodState(c *gin.Context) {
	type FoodInfo struct {
		ID   string `form:"id" binding:"required"`
		State string `form:"food_state" binding:"required"` //如果用int，当state为空时go会默认设置state为0
	}
	var (
		l, _     = c.Get("lang_util")
		lang     = l.(lang.LangUtil)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
		foodInfo = FoodInfo{}
		err      =c.ShouldBind(&foodInfo)
		service  = merchantService.NewMerchantService(c)
		_restaurantFoodsSvc = services.NewRestaurantFoodsService(c)
	)
	if err != nil {
		panic(err)
		return
	}
	//  适配flutter 客户端发送的错误代码
	if foodInfo.ID=="" {
		foodInfo.ID = c.Query("id")
	}

	if !regexp.MustCompile(`^[0-9]+$`).MatchString(foodInfo.State) { //确保state仅由数字构成
		merchant.Fail(c, lang.T("food_state_incorrect"), -1000)
		return
	}
	// string 转 int
	stateInt, err := strconv.Atoi(foodInfo.State)
	if err != nil {
		tools.Logger.Error("ChangeFoodState food_state(string) 转 int失败")
		merchant.Fail(c, lang.T("error_happend"), -1000)
		return
	}

	if stateInt != 0 && stateInt != 1 && stateInt != 2   {
		merchant.Fail(c, lang.T("food_state_incorrect"), -1000)
		return
	}
	if stateInt == 1 {
		err = _restaurantFoodsSvc.ValidCombo(tools.ToInt(foodInfo.ID))
		if err != nil {
			merchant.Fail(c, err.Error(), -1000)
			return
		}
	}

	restaurant := service.GetRestaurantByAdmin1(admin)
	restaurantId := restaurant.ID
	isOk, msg,foodState := service.ChangeFoodState(foodInfo.ID, restaurantId,stateInt)
	if isOk == true {
		merchant.Success(c, foodState, lang.T("msg"), 200)
		tools.ImportantLog(
			"merchant_food_state_update",
			admin.ID,
			restaurantId,
			restaurantId,
			foodState,
		)

	} else {
		merchant.Fail(c, lang.T(msg), -1000)
	}
}


// PostUpdateRestaurantState
//
//	@Time 2022-12-15 18:54:59
//	<AUTHOR>
//	@Description: 更新商家状态接口
//	@receiver merchant *MerchantController
//	@param context
func (merchant *MerchantController) PostUpdateRestaurantState(c *gin.Context) {
	var (
		l, _         = c.Get("lang_util")
		lang         = l.(lang.LangUtil)
		value, _     = c.Get("admin")
		admin        = value.(models.Admin)
		restaurantId = c.PostForm("restaurant_id")
		service      = merchantService.NewMerchantService(c)
	)
	if restaurantId ==""{
		restaurantId = c.Query("restaurant_id")
	}
	restaurant := service.GetRestaurantByAdmin1(admin)
	if restaurant.ID == 0 {
		//餐厅不存在
		merchant.Fail(c, lang.T("restaurant_not_found"), -1000)
		return
	} else {
		if restaurant.ID!=tools.ToInt(restaurantId) {
			merchant.Fail(c, "admin_belong_merchant_not_found", -1000)
			return
		}
		//存在餐厅
		isOk, result := service.UpdateRestaurantState(restaurantId)

		if isOk == true {
			if tools.ToInt64(result["state"]) == -1 {
				merchant.Fail(c, "بىر تەرەپ قىلىنمىغان زاكاز بار، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ", -1000)
			}else if tools.ToInt64(result["state"]) == -2 {
				// ئاشخانا سالاھىيەت ئۇچۇرى تولۇق ئەمەسكەن ، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ
				merchant.Fail(c, "shop_not_verified", -1000)
			}else{
				merchant.Success(c, result, lang.T("msg"), 200)
				tools.ImportantLog(
					"merchant_update_state",
					admin.ID,
					restaurantId,
					restaurantId,
					result,
					)
			}
		} else {
			merchant.Fail(c, lang.T("failed"), -1000)
		}
	}

}

// GetRestaurantCategory
//
//	@Time 2022-12-15 22:21:19
//	<AUTHOR>
//	@Description: 返回商家分类
//	@receiver merchant *MerchantController
//	@param c
func (merchant *MerchantController) GetRestaurantCategory(c *gin.Context) {
	var (
		l, _    = c.Get("lang_util")
		lang    = l.(lang.LangUtil)
		service = merchantService.NewMerchantService(c)
	)
	category, err := service.RestaurantCategory()
	if err != nil {
		merchant.Fail(c, lang.T("failed"), -1000)
	} else {
		merchant.Success(c, category, lang.T("msg"), 200)
	}

}

// GetRestaurantInformation
//
//	@Time 2022-12-15 23:38:40
//	<AUTHOR>
//	@Description: 获取商家信息接口
//	@receiver merchant *MerchantController
//	@param c
func (merchant *MerchantController) GetRestaurantInformation(c *gin.Context) {
	var (
		l, _ = c.Get("lang_util")
		lang = l.(lang.LangUtil)
		//value, _     = c.Get("admin")
		//admin        = value.(models.Admin)
		restaurantId = c.Query("restaurant_id")
		service      = merchantService.NewMerchantService(c)
		transformer  = merchantTransformer.NewMerchantTransformer(c)
	)
	//restaurant, _, _ := service.GetRestaurantByAdmin(c, admin)
	trim := strings.Trim(restaurantId, " ")
	restId, err := strconv.Atoi(trim)
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	//if restaurant["id"].(int) != restId {
	//	//餐厅不存在
	//	merchant.Fail(c, lang.T("feedback.not_restaurant"), -1000)
	//} else {
	//存在餐厅
	result := service.RestaurantInformation(restId)
	if result == nil { //店铺数据不存在
		merchant.Fail(c, "restaurant_not_found", -1000)
		return
	}
	restaurantInformation := transformer.FormatRestaurantInformation(result)
	merchant.Success(c, restaurantInformation, lang.T("msg"), 200)
	//}

}

// GetFinanceInfo
//
//	@Time 2022-12-24 18:41:27
//	<AUTHOR>
//	@Description: 获取商家银行卡信息
//	@receiver merchant *MerchantController
//	@param c
func (merchant *MerchantController) GetFinanceInfo(c *gin.Context) {
	type FinanceInfo struct {
		RestaurantId string `form:"restaurant_id" binding:"required"`
	}

	var (
		list        = FinanceInfo{}
		err         = c.ShouldBind(&list)
		service     = merchantService.NewMerchantService(c)
		transformer = merchantTransformer.NewMerchantTransformer(c)
	)

	if err != nil {
		panic(err)
		return
	} else {
		bankCards := service.BankCardList(list.RestaurantId)
		result := service.FinanceInfo(list.RestaurantId)
		formattedResult := transformer.FormatFinanceInfo(result, bankCards)
		merchant.Success(c, formattedResult, "msg", 200)
		return
	}
}

// GetCashedOutList
//
//	@Description: 商家已提现列表查询
//	@author: Alimjan
//	@Time: 2023-01-16 12:17:12
//	@receiver merchant *MerchantController
//	@param context *gin.Context
func (merchant *MerchantController) GetCashedOutList(c *gin.Context) {
	var (
		l, _                = c.Get("lang_util")
		lang                = l.(lang.LangUtil)
		restaurantId, _     = strconv.Atoi(c.Query("restaurant_id"))
		startTime           = c.Query("start_time")
		endTime             = c.Query("end_time")
		merchantService     = merchantService.NewMerchantService(c)
		merchantTransformer = merchantTransfomer.NewMerchantTransformer(c)
	)
	lk := merchantService.IsLakalaCashOut(restaurantId)
	if lk {
		rs, total, todayTotal, yesterdayTotal := merchantService.GetCashoutListLakala(restaurantId, startTime, endTime)
		CashedList := merchantTransformer.FormatCashedListLakala(rs)
		c.JSON(200, gin.H{
			"msg":             lang.T("msg"),
			"status":          200,
			"data":            CashedList,
			"total":           total,
			"total_today":     todayTotal,
			"total_yesterday": yesterdayTotal,
			"time":            carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})

	} else { //不是拉卡拉
		rs := merchantService.GetCashoutList(restaurantId)

		CashedList := merchantTransformer.FormatCashedList(rs)
		c.JSON(200, gin.H{
			"msg":    lang.T("msg"),
			"status": 200,
			"data":   CashedList,
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})

	}

}

// LangList
//
//	@Description: 获取语言包
//	@author: Alimjan
//	@Time: 2023-02-09 11:59:16
//	@receiver merchant *MerchantController
//	@param context *gin.Context
func (merchant *MerchantController) LangList(c *gin.Context) {
	var (
		merchantService     = merchantService.NewMerchantService(c)
		merchantTransformer = merchantTransfomer.NewMerchantTransformer(c)
	)
	data := merchantService.LangList(c)
	result := merchantTransformer.FormatLangList(c, data)
	merchant.Success(c, result, "msg", 200)
}

/***
 * @Author: [rozimamat]
 * @description: 接收自取订单
 * @Date: 2023-05-10 19:31:03
 * @param {*gin.Context} c
 */
func (merchant *MerchantController) ReceiveSelfTakeOrder(c *gin.Context) {

	type Info struct {
		RestaurantId   string `form:"restaurant_id" binding:"required"`
		SelfTakeNumber string `form:"self_take_number" binding:"required"`
	}
	var (
		merchantService = merchantService.NewMerchantService(c)
	)
	var (
		list = Info{}
		err  = c.ShouldBind(&list)
	)
	if err != nil {
		panic(err)
		return
	}
	suc, msg := merchantService.SelfTakeOrderReceive(list.RestaurantId, list.SelfTakeNumber)
	if suc {
		merchant.Success(c, nil, "msg", 200)
	} else {
		merchant.Fail(c, msg, -1000)
	}
}

/***
 * @Author: [rozimamat]
 * @description:
 * @Date: 2023-05-18 09:51:19
 * @param {*gin.Context} c
 */
func (merchant *MerchantController) CheckSelfTakeOrder(c *gin.Context) {

	type Info struct {
		RestaurantId   string `form:"restaurant_id" binding:"required"`
		SelfTakeNumber string `form:"self_take_number" binding:"required"`
	}
	var (
		merchantService     = merchantService.NewMerchantService(c)
		merchantTransformer = merchantTransformer.NewMerchantTransformer(c)
	)
	var (
		list = Info{}
		err  = c.ShouldBind(&list)
	)
	if err != nil {
		panic(err)
		return
	}

	order, suc, msg := merchantService.SelfTakeOrderCheck(list.RestaurantId, list.SelfTakeNumber)
	if suc {
		res := merchantTransformer.FormatSelfTakeOrder(c, order)
		merchant.Success(c, res, "msg", 200)
	} else {
		merchant.Fail(c, msg, -1000)
	}
}

/* @description: 启动页和首页广告
 * @Date: 2023-05-06 13:50:57
 * @param {*gin.Context} c
 */
func (merchant *MerchantController) Adver(c *gin.Context) {
	var (
		merchantService     = merchantService.NewMerchantService(c)
		merchantTransformer = merchantTransfomer.NewMerchantTransformer(c)
	)
	resId := c.DefaultQuery("restaurant_id", "")
	tp := c.DefaultQuery("type", "1")
	var admin models.Admin
	if tools.ToInt(tp) == 3 {
		middlewares.AuthMiddleware()(c)
		var (
			value, _   = c.Get("admin")
		)
		if value == nil {
			return 
		}
		if value != nil {
			admin = value.(models.Admin)
		}
	}
	version :=0 //商家端 版本 
	if c.GetHeader("Version-Name") != ""{
		version =tools.ToInt(tools.ReplaceString(c.GetHeader("Version-Name"),".",""))
	}

	data := merchantService.Adver(resId, tp,admin,version)
	result := merchantTransformer.FormatAdver(c, data)
	merchant.Success(c, result, "msg", 200)
}

/***
 * @Author: [rozimamat]
 * @description: 设置店铺在小程序上的显示模板
 * @Date: 2023-05-19 18:38:20
 * @param {*gin.Context} c
 */
func (merchant *MerchantController) UpdateRestaurantViewType(c *gin.Context) {

	type Info struct {
		RestaurantId   string `form:"restaurant_id" binding:"required"`
		RestaurantType string `form:"restaurant_type" binding:"required"`
	}
	var (
		merchantService = merchantService.NewMerchantService(c)
	)
	var (
		list = Info{}
		err  = c.ShouldBind(&list)
	)
	if err != nil {
		panic(err)
		return
	}
	suc, msg := merchantService.UpdateRestaurantViewType(list.RestaurantId, list.RestaurantType)
	if suc {
		merchant.Success(c, nil, "msg", 200)
		return
	}

	merchant.Fail(c, msg, -1000)
}

/***
 * @Author: [rozimamat]
 * @description: 获取店铺在小程序上的显示模板
 * @Date: 2023-05-19 18:38:20
 * @param {*gin.Context} c
 */
func (merchant *MerchantController) GetRestaurantViewType(c *gin.Context) {
	var (
		merchantService = merchantService.NewMerchantService(c)
	)
	resId := c.DefaultQuery("restaurant_id", "")
	data := merchantService.GetRestaurantViewType(resId)
	merchant.Success(c, data, "msg", 200)
}

// 检查订单是否能打印
func (merchant *MerchantController) CheckPrintOrder(c *gin.Context) {
	type OrderInfo struct {
		OrderId int64 `form:"order_id" binding:"required"`
	}

	var (
		l, _            = c.Get("lang_util")
		lang            = l.(lang.LangUtil)
		merchantService = merchantService.NewMerchantService(c)
	)

	info := &OrderInfo{}
	err := c.ShouldBind(info)
	if err != nil {
		panic(err)
	}
	list, _ := merchantService.CheckPrintOrder(info.OrderId)
	if list.ID == 0 {
		merchant.Fail(c, lang.T("order_not_found"), -1000)
		return
	}
	merchant.Success(c, gin.H{
		"tag": lang.TArr("order_state")[list.State],
	}, "msg", 200)

}
//
// PostOrderReadyToSend
//  @Description: 商家准备好美食时使用
//  @receiver merchant
//  @param context
//
func (merchant *MerchantController) PostOrderReadyToSend(context *gin.Context) {
	type OrderReadyRequest struct {
		RestaurantId   string `form:"restaurant_id" binding:"required"`
		OrderID string `form:"order_id" binding:"required"`
	}
	var (
		merchantService = merchantService.NewMerchantService(context)
	)
	var (
		list = OrderReadyRequest{}
		err  = context.ShouldBind(&list)
	)
	if err != nil {
		panic(err)
		return
	}
	order, msg := merchantService.OrderReadyToSend(list.RestaurantId, list.OrderID,models.ORDER_STATE_ACCEPTED,models.ORDER_STATE_READY)
	if order != nil {
		if order.ShipperID>0 {
			engine.SendSocket(1, order.ShipperID, 5)
		}
		merchant.Success(context, nil, "msg", 200)
	} else {
		merchant.Fail(context, msg, -1000)
	}
}
//
// GetTerminalShowAdver
//  @Description: 老版本App 升级
//  @receiver merchant
//  @param context
//
func (merchant *MerchantController) GetTerminalShowAdver(context *gin.Context) {
	var (
		merchantService = merchantService.NewMerchantService(context)
		merchantTransformer = merchantTransformer.NewMerchantTransformer(context)
		osType  = tools.ToInt64(context.GetHeader("osType"))
		restaurnatId = context.DefaultQuery("restaurant_id", "")
	)
	terminal := merchantService.GetTerminalShowAdver(osType)
	var admin models.Admin
	version :=0 //商家端 版本 
	if context.GetHeader("Version-Name") != ""{
		version =tools.ToInt(tools.ReplaceString(context.GetHeader("Version-Name"),".",""))
	}
	popAdver := merchantService.Adver(restaurnatId, "2",admin,version)
	data := merchantTransformer.FormatTerminalShowAdver(terminal, popAdver)
	merchant.Success(context, data, "msg", 200)
}
//
// PostSendCheckCode
//  @Description: 发送找回密码验证码接口
//  @receiver merchant
//  @param context
//
func (merchant *MerchantController) PostSendCheckCode(c *gin.Context) {
	var (
		merchantService = merchantService.NewMerchantService(c)
		request = user.UserMobileSendCodeRequest{}
		err     = c.ShouldBind(&request)
	)
	if err != nil {
		panic(err)
		return
	}
	suc, msg := merchantService.SendCheckCode(request.Mobile)
	if suc {
		merchant.Success(c, nil, msg, 200)
	} else {
		merchant.Fail(c, msg, -1000)
	}
}
//
// PostChangePasswordCodeVerify
//  @Description: 找回密码验证码验证
//  @receiver merchant
//  @param c
//
func (merchant *MerchantController) PostChangePasswordCodeVerify(c *gin.Context) {
	var (
		merchantService = merchantService.NewMerchantService(c)
		request = user.UserMobileCodeVerifyRequest{}
		err     = c.ShouldBind(&request)
	)
	if err != nil {
		panic(err)
		return
	}
	suc, msg := merchantService.PostChangePasswordCodeVerify(request.Mobile,request.Code)
	if suc {
		merchant.Success(c, nil, msg, 200)
	} else {
		merchant.Fail(c, msg, -1000)
	}
}
//
// PostChangeSMSPassword
//  @Description: 修改密码
//  @receiver merchant
//  @param context
//
func (merchant *MerchantController) PostChangeSMSPassword(c *gin.Context) {
	var (
		merchantService = merchantService.NewMerchantService(c)

	)
	_,ok := c.GetQuery("old_password")
	if ok {
		//登录用户修改密码
		request := user.UserChangePasswordRequest{}
		err     := c.ShouldBind(&request)
		if err != nil {
			panic(err)
			return
		}
		middleWare := middlewares.AuthMiddleware()
		middleWare(c)
		//判断是否登录
		anyAdmin, isLogin := c.Get("admin")
		if isLogin == false{
			return
		}
		admin       := anyAdmin.(models.Admin)
		if admin.ID == 0 {
			merchant.Fail(c, "", -1000)
			return
		}
		suc, msg := merchantService.PostChangePassword(c,request.OldPassword,request.Password,request.PasswordConfirm,admin.ID)
		if suc {
			merchant.Success(c, nil, msg, 200)
		} else {
			merchant.Fail(c, msg, -1000)
		}
	}else{
		//找回密码修改密码
		request := user.UserChangeSMSPasswordRequest{}
		err     := c.ShouldBind(&request)
		if err != nil {
			panic(err)
			return
		}
		suc, msg := merchantService.PostChangeSMSPassword(c,request.Mobile,request.Password)
		if suc {
			merchant.Success(c, nil, msg, 200)
		} else {
			merchant.Fail(c, msg, -1000)
		}
	}


}
//
// PostReceiveOrder
//  @Description: 商家接单接口
//  @receiver merchant
//  @param c
//
func (merchant *MerchantController) PostReceiveOrder(c *gin.Context) {
	type OrderRequest struct {
		OrderID string `form:"order_id" binding:"required"`
	}
	var (
		merchantService = merchantService.NewMerchantService(c)
		request = OrderRequest{}
		err     = c.ShouldBind(&request)
	)
	if err != nil {
		panic(err)
		return
	}
	suc, msg := merchantService.PostReceiveOrder(request.OrderID)
	if suc {
		merchant.Success(c, nil, msg, 200)
	} else {
		merchant.Fail(c, msg, -1000)
	}
}

func (merchant *MerchantController) UpdateMerchantInfo(c *gin.Context) {
	var (
		merchantService = merchantService.NewMerchantService(c)
		request = marketingRequest.MerchantInfoUpdateRequest{}
		err     = c.ShouldBind(&request)
		lang   = c.MustGet("lang_util").(lang.LangUtil)
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
		adminRes = admin.GetAdminRestaurant()
	)
	if err != nil {
		panic(err)
		return
	}
	//  验证营业时间与美食时间是否冲突
	if code,msg := request.ValidateOpenTime(); code != marketingRequest.SUCCESS {
		if code == marketingRequest.TIME_CONFILICT {
			var errMap map[string]string
			json.Unmarshal([]byte(msg), &errMap)
			merchant.Fail(c, fmt.Sprintf(lang.T("food_open_time_error"),errMap["food_name_"+lang.Lang]), -1000)
			return
		}else{
			merchant.Fail(c, msg, -1000)
			return
		}
	}
	if adminRes.ID==0 {
		merchant.Fail(c, "admin_belong_merchant_not_found", -1000)
		return
	}
	request.RestaurantID = adminRes.ID
	suc, msg := merchantService.UpdateMerchantInfo(request)
	if suc {

		merchant.Success(c, nil, msg, 200)
		tools.ImportantLog(
			"merchant_info_update",
			admin.ID,
			adminRes.ID,
			adminRes.ID,
			request,
		)

	} else {
		merchant.Fail(c, msg, -1000)
	}
}

func (s *MerchantController) PostUploadFoodsImage(c *gin.Context) {
	typeName := c.PostForm("type")
	file, err := c.FormFile("image")
	if err != nil {
		s.Fail(c, "无法获取上传文件", http.StatusBadRequest)
		return
	}

	ext := filepath.Ext(file.Filename)

	if ext != ".jpg" && ext != ".jpeg" && ext != ".png" {
		s.Fail(c, "只允许jpg|jpeg|png类型图片", http.StatusBadRequest)
		return
	}

	// 临时的
	fileName := s.generateRandomFilename() + ext
	rootDir := configs.MyApp.UploadRootDir
	imageDir := "uploads/" + typeName
	if _, err := os.Stat(rootDir + imageDir); os.IsNotExist(err) {
		// 目录不存在，创建目录
		err := os.MkdirAll(rootDir+imageDir, 0755)
		if err != nil {
			// 创建目录失败
			fmt.Println("目录创建失败:", err)
			return
		}
	}
	imagePath := imageDir + "/" + fileName
	fullPath := rootDir + imagePath
	if err := c.SaveUploadedFile(file, fullPath); err != nil {
		s.Fail(c, "上传保存失败", http.StatusBadRequest)
		return
	}

	fileUrl := configs.MyApp.CdnUrl + imagePath

	s.Success(c, map[string]interface{}{ 
		"image_url":        fileUrl,
	}, "上传成功", http.StatusOK)
}

// 生成随机图片名称
func (s *MerchantController) generateRandomFilename() string {
	// 生成当前时间的字符串形式
	timestamp := time.Now().Format("20060102150405")
	// 生成五位随机数字（00000 - 99999）
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Intn(100000)
	randomString := strconv.Itoa(randomNumber)
	paddedRandomString := fmt.Sprintf("%05s", randomString)
	// 将随机数字追加到时间戳后面
	return timestamp + paddedRandomString
}
//
// GetComment
//  @Description: 评论列表
//  @receiver merchant
//  @param context
//
func (merchant *MerchantController) GetComment(c *gin.Context) {
	var (
		merchantService = merchantService.NewMerchantService(c)
		merchantTrans = merchantTransformer.NewMerchantTransformer(c)
		request = user.CommentListRequest{}
		err     = c.ShouldBind(&request)
		//admin
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
	)
	if err != nil {
		panic(err)
		return
	}
	adminRes := admin.GetAdminRestaurant()
	if adminRes.ID==0 {
		merchant.Fail(c, "admin_belong_merchant_not_found", -1000)
		return
	}
	request.RestaurantId = int64(adminRes.ID)
	suc, msg,comments,lastPage,limit := merchantService.GetComment(request.Type,request.Page,request.RestaurantId)
	star := merchantService.GetCommentStar(request.RestaurantId)
	commentType := merchantService.GetRestaurantCommentsCountByType(c,request.RestaurantId)
	rtn := merchantTrans.GetComment(comments,star,commentType, int(request.Page),lastPage,limit)
	if suc {
		merchant.Success(c, rtn, "msg", 200)
	} else {
		merchant.Fail(c, msg, -1000)
	}
}
//
// PostReply
//  @Description: 商家评论回复客户
//  @receiver merchant
//  @param c
//
func (merchant *MerchantController) PostReply(c *gin.Context) {
	var (
		merchantService = merchantService.NewMerchantService(c)
		request = user.CommentReplyRequest{}
		err     = c.ShouldBind(&request)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
	)
	if err != nil {
		panic(err)
		return
	}
	suc, msg,rtnMap := merchantService.PostReply(request.CommentId,request.Text,admin.ID)
	if suc {
		merchant.Success(c, rtnMap, msg, 200)
	} else {
		merchant.Fail(c, msg, -1000)
	}
}
//
// PostCreateFood
//  @Description: 商家创建美食
//  @receiver merchant
//  @param c
//
func (merchant *MerchantController) PostCreateFood(c *gin.Context) {
	var (
		merchantService = merchantService.NewMerchantService(c)
		request = food.FoodCreateRequest{}
		err     = c.ShouldBind(&request)
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
		adminRes = admin.GetAdminRestaurant()
	)
	if err != nil {
		panic(err)
		return
	}
	//  验证营业时间与美食时间是否冲突
	if code,msg := request.ValidateOpenTime(); code != marketingRequest.SUCCESS {
		if code == marketingRequest.TIME_CONFILICT {
			merchant.Fail(c, "time_confilict_with_res", -1000)
			return
		}else{
			merchant.Fail(c, msg, -1000)
			return
		}
	}

	suc, msg, createdFood := merchantService.PostCreateFood(request)
	if suc {
		merchant.Success(c, []int{}, msg, 200)
		tools.ImportantLog(
			"merchant_food_update",
			admin.ID,
			createdFood.ID,
			adminRes.ID,
			request,
		)
	} else {
		merchant.Fail(c, msg, -1000)
	}
}

func (merchant *MerchantController) PostUpdateFood(c *gin.Context) {
	var (
		merchantService = merchantService.NewMerchantService(c)
		request = food.FoodUpdateRequest{}
		err     = c.ShouldBind(&request)
		anyAdmin, _ = c.Get("admin")
		admin       = anyAdmin.(models.Admin)
		adminRes = admin.GetAdminRestaurant()
	)
	if err != nil {
		panic(err)
		return
	}
	//  验证营业时间与美食时间是否冲突
	if code,msg := request.ValidateOpenTime(); code != marketingRequest.SUCCESS {
		if code == marketingRequest.TIME_CONFILICT {
			merchant.Fail(c, "time_confilict_with_res", -1000)
			return
		}else{
			merchant.Fail(c, msg, -1000)
			return
		}
	}
	suc, msg := merchantService.PostUpdateFood(request)
	if suc {
		merchant.Success(c, []int{}, msg, 200)
		tools.ImportantLog(
			"merchant_food_update",
			admin.ID,
			request.ID,
			adminRes.ID,
			request,
		)

	} else {
		merchant.Fail(c, msg, -1000)
	}
}
//
// PostUploadBusinessLicense
//  @Description: 商家证书更新
//  @receiver s
//  @param c
//
func (s *MerchantController) PostUploadBusinessLicense(c *gin.Context) {
	licenseTypeId := c.PostForm("license_type_id")
	file, err := c.FormFile("image")
	if err != nil {
		s.Fail(c, "无法获取上传文件", http.StatusBadRequest)
		return
	}

	ext := filepath.Ext(file.Filename)

	if ext != ".jpg" && ext != ".jpeg" && ext != ".png" {
		s.Fail(c, "只允许jpg|jpeg|png类型图片", http.StatusBadRequest)
		return
	}

	// 临时的
	fileName := s.generateRandomFilename() + ext
	rootDir := configs.MyApp.UploadRootDir
	imageDir := "uploads/" + "restaurant_license"
	if _, err := os.Stat(rootDir + imageDir); os.IsNotExist(err) {
		// 目录不存在，创建目录
		err := os.MkdirAll(rootDir+imageDir, 0755)
		if err != nil {
			// 创建目录失败
			fmt.Println("目录创建失败:", err)
			return
		}
	}
	imagePath := imageDir + "/" + fileName
	fullPath := rootDir + imagePath
	if err := c.SaveUploadedFile(file, fullPath); err != nil {
		s.Fail(c, "上传保存失败", http.StatusBadRequest)
		return
	}

	var (
		merchantService = merchantService.NewMerchantService(c)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
	)
	resIds := admin.GetRestaurantIds(c,false)
	if len(resIds) == 0 {
		s.Fail(c, "无权限", -1000)
		return
	}
	suc,msg,rtn := merchantService.PostUploadBusinessLicense(imagePath,resIds[0],licenseTypeId,admin.ID )
	if !suc {
		s.Fail(c, msg, -1000)
		return
	}else{
		s.Success(c, rtn, "上传成功", http.StatusOK)
		tools.ImportantLog(
			"merchant_upload_business_license",
			admin.ID,
			resIds[0],
			resIds[0],
			rtn,
		)
	}

}
//
// PostUploadRestaurantAvatar
//  @Description: 商家上传餐厅头像
//  @receiver s
//  @param c
//
func (s *MerchantController) PostUploadRestaurantAvatar(c *gin.Context) {
	file, err := c.FormFile("image")
	if err != nil {
		s.Fail(c, "无法获取上传文件", http.StatusBadRequest)
		return
	}

	ext := filepath.Ext(file.Filename)

	if ext != ".jpg" && ext != ".jpeg" && ext != ".png" {
		s.Fail(c, "只允许jpg|jpeg|png类型图片", http.StatusBadRequest)
		return
	}
	typeName := "restaurant_avatar"
	// 临时的
	fileName := s.generateRandomFilename() + ext
	rootDir := configs.MyApp.UploadRootDir
	imageDir := "uploads/" + typeName
	if _, err := os.Stat(rootDir + imageDir); os.IsNotExist(err) {
		// 目录不存在，创建目录
		err := os.MkdirAll(rootDir+imageDir, 0755)
		if err != nil {
			// 创建目录失败
			fmt.Println("目录创建失败:", err)
			return
		}
	}
	imagePath := imageDir + "/" + fileName
	fullPath := rootDir + imagePath
	if err := c.SaveUploadedFile(file, fullPath); err != nil {
		s.Fail(c, "上传保存失败", http.StatusBadRequest)
		return
	}

	var (
		merchantService = merchantService.NewMerchantService(c)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
	)
	resIds := admin.GetRestaurantIds(c,false)
	if len(resIds) == 0 {
		s.Fail(c, "无权限", http.StatusBadRequest)
		return
	}
	suc,msg,rtn := merchantService.PostUploadRestaurantAvatar(imagePath,resIds[0],admin.ID )
	if !suc {
		s.Fail(c, msg, -1000)
		return
	}else{
		s.Success(c, rtn, "上传成功", http.StatusOK)
		tools.ImportantLog(
			"merchant_upload_avatar",
			admin.ID,
			resIds[0],
			resIds[0],
			rtn,
		)

	}

}

//
//
//  @Description: 商家上传餐厅内部照片
//  @receiver s
//  @param c
//
func (s *MerchantController) PostUploadRestaurantImage(c *gin.Context) {
	file, err := c.FormFile("image")
	if err != nil {
		s.Fail(c, "无法获取上传文件", http.StatusBadRequest)
		return
	}

	ext := filepath.Ext(file.Filename)

	if ext != ".jpg" && ext != ".jpeg" && ext != ".png" {
		s.Fail(c, "只允许jpg|jpeg|png类型图片", http.StatusBadRequest)
		return
	}
	typeName := "restaurant_license"
	// 临时的
	fileName := s.generateRandomFilename() + ext
	rootDir := configs.MyApp.UploadRootDir
	imageDir := "uploads/" + typeName
	if _, err := os.Stat(rootDir + imageDir); os.IsNotExist(err) {
		// 目录不存在，创建目录
		err := os.MkdirAll(rootDir+imageDir, 0755)
		if err != nil {
			// 创建目录失败
			fmt.Println("目录创建失败:", err)
			return
		}
	}
	imagePath := imageDir + "/" + fileName
	fullPath := rootDir + imagePath
	if err := c.SaveUploadedFile(file, fullPath); err != nil {
		s.Fail(c, "上传保存失败", http.StatusBadRequest)
		return
	}

	var (
		merchantService = merchantService.NewMerchantService(c)
		value, _ = c.Get("admin")
		admin    = value.(models.Admin)
	)
	resIds := admin.GetRestaurantIds(c,false)
	if len(resIds) == 0 {
		s.Fail(c, "无权限", http.StatusBadRequest)
		return
	}
	suc,msg,rtn := merchantService.PostUploadRestaurantImage(imagePath,resIds[0],admin.ID )
	if !suc {
		s.Fail(c, msg, -1000)
		return
	}else{
		s.Success(c, rtn, "上传成功", http.StatusOK)
		tools.ImportantLog(
			"merchant_upload_avatar",
			admin.ID,
			resIds[0],
			resIds[0],
			rtn,
		)
	}

}


// 描述：发送短信验证码
// 作者：Qurbanjan
// 文件：MerchantController.go
// 修改时间：2024/08/13 20:34
func (merchant *MerchantController) PostSmsCode(c *gin.Context) {
	type Params struct {
		Mobile string `form:"mobile" binding:"required"`
	}
	var (
		params      Params
		err         = c.ShouldBind(&params)
		l, _            = c.Get("lang_util")
		lang            = l.(lang.LangUtil)
	)
	
	if err := c.ShouldBind(&params); err != nil {
		merchant.Fail(c, lang.T("client_param_error"), -1000)
		return
	}
	// 检查手机号码合法性
	if !tools.VerifyMobileFormat(params.Mobile) {
		merchant.Fail(c, "mobile_incorrect", -1000)
		return
	}

	service := merchantService.NewMerchantService(c)
	// 查询用户信息、是否可以发送短信
	_, err = service.CheckSMSLogin(params.Mobile)
	if err != nil {
		merchant.Fail(c, err.Error(), -1000)
		return
	}
	// 发送短信验证码
	err = service.SendSMSCode(c, params.Mobile)
	if err != nil {
		merchant.Fail(c, err.Error(), -1000)
		return
	}
	merchant.Ok(c)
}

// 描述：短信验证码登录
// 作者：Qurbanjan
// 文件：MerchantController.go
// 修改时间：2024/08/13 20:34
func (merchant *MerchantController) PostSMSLogin(c *gin.Context) {
	type Params struct {
		Mobile        string `form:"mobile" binding:"required"`
		SMSCode       string `form:"code" binding:"required"`
		ClientId      string `form:"client_id" binding:"required"`
		SearialNumber string `form:"searial_number" binding:"required"`
	}
	var (
		redisHelper = tools.GetRedisHelper()
		params      Params
		err         = c.ShouldBind(&params)
	)

	if err != nil {
		panic(err)
	}
	// 验证手机号码的合法性
	if !tools.VerifyMobileFormat(params.Mobile) {
		merchant.Fail(c, "mobile_incorrect", -1000)
		return
	}
	// 获取缓存的key并判断验证码是否正确
	cacheKey := fmt.Sprintf("merchant_mobile_%s", params.Mobile)
	get := redisHelper.Get(c, cacheKey)
	if get.Val() != params.SMSCode {
		merchant.Fail(c, "captcha_incorrect", -1000)
		return
	}

	service := merchantService.NewMerchantService(c)
	// 检查用户信息
	admin, err := service.CheckSMSLogin(params.Mobile)
	if err != nil {
		merchant.Fail(c, err.Error(), -1000)
		return
	}
	// 获取登录信息
	loginEntity, err := service.GetLoginData(c,admin,params.SearialNumber)
	if err != nil {
		merchant.Fail(c, err.Error(), -1000)
		return
	}
	// 删除token 和 session
	err = service.CleanOldSessionAndToken(admin)
	if err != nil {
		tools.Logger.Error("无法删除session和token", err)
	}

	merchant.Success(c, loginEntity, "msg", 200)
}

// 描述：验证找回密码时使用的短信验证码
// 作者：Qurbanjan
// 文件：MerchantController.go
// 修改时间：2024/08/19 13:40
func (merchant *MerchantController) CheckSmsCode(c *gin.Context){
	type Params struct {
		Mobile 	string 	`form:"mobile" binding:"required"`
		SMSCode string 	`form:"code" binding:"required"`
	}

	var params Params
	if err := c.ShouldBind(&params); err != nil {
		merchant.Fail(c, err.Error(), -1000)
		return
	}
	// 验证手机号码的合法性
	if !tools.VerifyMobileFormat(params.Mobile) {
		merchant.Fail(c, "mobile_incorrect", -1000)
		return
	}

	service := merchantService.NewMerchantService(c)
	randStr, err := service.CheckSMSCode(c, params.Mobile, params.SMSCode)
	if err != nil {
		merchant.Fail(c, err.Error(), -1000)
		return
	}

	merchant.Success(c, gin.H{"random_str":randStr}, "msg", 200)
}

// 描述：找回密码
// 作者：Qurbanjan
// 文件：MerchantController.go
// 修改时间：2024/08/19 13:41
func (merchant *MerchantController) ResetPassword(ctx *gin.Context){
	type Params struct {
		Mobile      string `form:"mobile" binding:"required"`
		Password    string `form:"password" binding:"required"`
		RePassword 	string `form:"re_password" binding:"required"`
		RandomStr 	string `form:"random_str" binding:"required"`
	}

	var (
		params      Params
		err         = ctx.ShouldBind(&params)
		l, _        = ctx.Get("lang_util")
		lang        = l.(lang.LangUtil)
	)

	if err = ctx.ShouldBind(&params); err != nil {
		merchant.Fail(ctx, err.Error(), -1000)
		return
	}

	if params.Password != params.RePassword {
		merchant.Fail(ctx, lang.T("two_passwords_are_inconsistent"), -1000)
		return
	}

	service := merchantService.NewMerchantService(ctx)
	_, err = service.ResetPassword(ctx,params.Mobile, params.Password,params.RandomStr)
	if err != nil {
		merchant.Fail(ctx, err.Error(), -1000)
		return
	}

	merchant.Ok(ctx)
}



//弹窗确认
func (merchant *MerchantController) ConfirmAdver(c *gin.Context) {
	middlewares.AuthMiddleware()(c)
	
	var (
		value, _   = c.Get("admin")
	)
	
	if value == nil {
		return 
	}
	type Params struct {
		TypeId int `form:"type" json:"type" binding:"required"` // 
		MsgId int `form:"msg_id" json:"msg_id" binding:"required"`
	}
	admin      := value.(models.Admin)
	
	var(	
		params      Params
		err         = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	service := merchantService.NewMerchantService(c)
	resInfo,_:=service .GetResByAdmin(admin)
	
	content := service .AlertConfirm(admin,resInfo,params.TypeId,params.MsgId)
		
	merchant.Success(c, content, "msg", 200)
}
// GetConfig 获取AppConfig的值，发送keys ，逗号区分
//  @receiver merchant
//  @param c
//
func (merchant *MerchantController) GetConfig(c *gin.Context) {
	
	merchantService := merchantService.NewMerchantService(c)
	keys,ok := c.GetQuery("keys")
	brand :=""
	if c.GetHeader("Device-Brande") != ""{
		brand =tools.ToString(c.GetHeader("Device-Brande"))
	}
	deviceAppVersion := int64(0)
	if c.GetHeader("Version-Name") != ""{
		version2 :=tools.ToInt64(tools.ReplaceString(c.GetHeader("Version-Name"),".",""))
		if version2 > 0 {
			deviceAppVersion = version2
		}
	}
	if ok {
		merchantInfo := merchantService.GetConfig(keys,brand,deviceAppVersion)
		merchant.Success(c, merchantInfo , "msg", 200)
	}else{
		merchant.Success(c, map[int]int{}, "msg", 200)
	}
	
	
}

// PartRefundList
//
//  @Author: YaKupJan
//  @Date: 2024-11-29 17:47:59
//  @Description: 部分退款返回信息
//  @receiver merchant
//  @param c
func (merchant *MerchantController) PartRefundList(c *gin.Context) {
	type Params struct {
		OrderID  int `form:"order_id" binding:"required"`  // 排序字段
	}
	var (
		params Params
		service = merchantService.NewMerchantService(c)
		transformer = merchantTransformer.NewMerchantTransformer(c)
		cmsService1 = cmsService.NewPartRefundService(c)

	)
	if err := c.ShouldBindQuery(&params); err != nil {
		panic(err)
		return
	}
	pagination := tools.GetPagination(c)
	pagination.Limit = 100
	// 部分退款返回信息
	orderToday := service.GetPartRefundList(params.OrderID)
	// 原因列表
	reasonList, _ := cmsService1.RefundReasonList(pagination,0)
	// 部分退款返回信息 格式化
	data := transformer.PartRefundListFormat(orderToday,reasonList)
	merchant.Success(c,data,"msg",200)
}

// BatchSoldFoods
//
//  @Author: YaKupJan
//  @Date: 2024-12-02 17:29:47
//  @Description: 批量售完美食
//  @receiver merchant
//  @param c
func (merchant *MerchantController) BatchSoldFoods(c *gin.Context) {
	type Params struct {
		FoodIds  string `form:"food_ids" binding:"required"`
	}
	var (
		params Params
		service = merchantService.NewMerchantService(c)
	)
	if err := c.ShouldBind(&params); err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	restaurantIdByMerchantAdmin, err := permissions.GetRestaurantIdByMerchantAdmin(admin)
	if err!=nil {
		panic(errors.NewCustomError(err.Error()))
	}
	// 批量售完美食
	service.BatchSoldFoods(params.FoodIds,restaurantIdByMerchantAdmin)
	merchant.Ok(c)
}

// BatchSoldFoodsV2
//
//  @Author: Salam
//  @Date: 2025-06-02 10:24:47
//  @Description: 批量售完美食V2
//  @receiver ctr
//  @param ctx
func (ctr *MerchantController) BatchSoldFoodsV2(ctx *gin.Context) {
	var (
		body merchantFoodRequest.BatchSoldFoodsBody
	)
	if err := ctx.ShouldBind(&body); err != nil {
		panic(err)
	}

	admin := permissions.GetAdmin(ctx)
	restaurantId, err := permissions.GetRestaurantIdByMerchantAdmin(admin)
	if err != nil {
		panic(errors.NewCustomError(err.Error()))
	}

	// 批量售完美食
	service := merchantService.NewMerchantService(ctx)
	service.BatchSoldFoodsV2(body.FoodsJson, restaurantId)
	ctr.Ok(ctx)
}
