package merchant

import (
	"mulazim-api/configs"
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/services"
	merchantService "mulazim-api/services/merchant"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	_ "github.com/go-playground/validator/v10"
)

type PushController struct {
	controllers.BaseController
}

func (push *PushController) Register(c *gin.Context) {
	type RegisterRequest struct {
		Rid      string                    `form:"rid" binding:"required"`
		Platform models.PushDevicePlatform `form:"platform" binding:"required,oneof=android ios"`
		Lang     models.PushDeviceLang     `form:"lang" binding:"required,oneof=zh ug"`
	}
	var request RegisterRequest
	if err := c.ShouldBind(&request); err != nil {
		tools.Logger.Error("参数错误:", err)
		panic(err)
		return
	}
	anyAdmin, _ := c.Get("admin")
	admin := anyAdmin.(models.Admin)
	var merchantService     = merchantService.NewMerchantService(c)
	resInfo, err := merchantService.GetResByAdmin(admin)
	if err != nil {
		tools.Logger.Errorf("没有找到管理员:%d对应的商家,错误信息 %s", admin.ID, err.Error())
		push.Fail(c, "failed", 200)
		return
	}
	deviceAppVersion :=configs.MyApp.MerchantPushMinVersion //商家端 最小版本 
	if c.GetHeader("Version-Name") != ""{
		version2 :=tools.ToInt64(tools.ReplaceString(c.GetHeader("Version-Name"),".",""))
		if version2 > 0 {
			deviceAppVersion = version2
		}
	}
	Brand :=""
	if c.GetHeader("Device-Brande") != ""{
		Brand =tools.ToString(c.GetHeader("Device-Brande"))
	}

	_, err = services.PushDeviceServiceInst.CreateRestaurantDevice(resInfo, request.Rid, request.Platform, request.Lang,admin.ID,deviceAppVersion,Brand)
	if err != nil {
		tools.Logger.Errorf("管理员:%d对应的商家注册设备失败,错误信息 %s", admin.ID, err.Error())
		push.Fail(c, "failed", 200)
		return
	}
	push.Success(c, nil, "msg", 200)
}

// 注销
func (push *PushController) UnRegister(c *gin.Context) {
	type UnRegisterRequest struct {
		Rid string `form:"rid" json:"rid" binding:"required"`
	}
	var request UnRegisterRequest
	if err := c.ShouldBind(&request); err != nil {
		tools.Logger.Error("参数错误:", err)
		panic(err)
		return
	}
	err := services.PushDeviceServiceInst.DeleteAdminDevice(request.Rid, models.PushDeviceClientMerchant)
	if err != nil {
		tools.Logger.Error("注销设备失败:", err)
		push.Fail(c, "failed", 200)
		return
	}
	push.Success(c, nil, "msg", 200)
}
