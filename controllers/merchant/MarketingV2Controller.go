package merchant

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers"
	"mulazim-api/models"
	"mulazim-api/services"
	"mulazim-api/services/merchant"
	"mulazim-api/tools"
	merchantTrans "mulazim-api/transformers/merchant"
	"net/http"
)

type MarketingV2Controller struct {
	controllers.BaseController
}

/***
 * @Author: [<PERSON><PERSON>]
 * @description: 创建满减活动
 * @Date: 2025-04-11 12:10:36
 * @param {*gin.Context} ctx
 */
func (ctr *MarketingV2Controller) Create(ctx *gin.Context) {
	var params models.MarketingParam
	if err := ctx.ShouldBind(&params); err != nil {
		panic(err)
	}

	_foodSvc := services.NewRestaurantFoodsService(ctx)
	for idx, food := range params.FoodsJson {
		// 检查规格美食数据
		if food.FoodType == models.RestaurantFoodsTypeSpec {
			// 如果 SpecID 和 OptionIds 已提供，那就没问题，跳过。
			if food.FoodId > 0 && food.SpecId > 0 {
				continue
			}
			// 如果没有提供 option_ids，那就返回错误
			if len(food.OptionIds) == 0 {
				ctr.Fail(ctx, "spec_options_should_be_provided", -1000)
				return
			}
			// 说明没有提供 specId，那就找到或者创建 specId，然后附上数据
			specId, err := _foodSvc.SaveFoodSpec(params.RestaurantID, food.FoodId, food.OptionIds)
			if err != nil {
				ctr.Fail(ctx, "marketing_create_fail", -1000)
				return
			}
			params.FoodsJson[idx].FoodId = specId
		}
	}

	_marketingSvc := merchant.NewMarketingService(ctx)
	success, msg := _marketingSvc.CreateMarketing(params, 2)
	if !success {
		ctr.Fail(ctx, msg, -1000)
		return
	}
	ctr.Success(ctx, nil, "msg", http.StatusOK)
}

/***
 * @Author: [Salam]
 * @description: 获取满减活动详情
 * @Date: 2025-04-11 17:32:51
 * @param {*gin.Context} ctx
 */
func (ctr *MarketingV2Controller) GetOne(ctx *gin.Context) {
	type Params struct {
		ID int `uri:"id" binding:"required,min=1"`
	}
	var params Params
	if err := ctx.ShouldBindUri(&params); err != nil {
		panic(err)
	}

	_marketingSvc := merchant.NewMarketingService(ctx)
	mkt, err := _marketingSvc.GetOne(params.ID)
	if err != nil {
		ctr.Fail(ctx, err.Error(), -1000)
		return
	}

	_marketingTrans := merchantTrans.NewMarketingTransformer(ctx)
	formattedMkt := _marketingTrans.FormatMarketing(mkt)

	ctr.Success(ctx, formattedMkt, "msg", http.StatusOK)
}

/***
 * @Author: [Salam]
 * @description: 编辑满减活动
 * @Date: 2025-04-11 18:35:51
 * @param {*gin.Context} ctx
 */
func (ctr *MarketingV2Controller) Update(ctx *gin.Context) {
	type Params struct {
		ID int `uri:"id" binding:"required,min=1"`
	}
	var params Params
	if err := ctx.ShouldBindUri(&params); err != nil {
		panic(err)
	}

	// 绑定客户端发送活动信息
	var allParams models.MarketingParam
	if err := ctx.ShouldBind(&allParams); err != nil {
		panic(err)
	}
	// 更新活动信息
	_marketingSvc := merchant.NewMarketingService(ctx)
	err := _marketingSvc.UpdateMarketing(allParams, params.ID, 2)

	if err != nil {
		tools.Logger.Error(err.Error())
		ctr.Fail(ctx, "fail", -1000)
		return
	}
	ctr.Ok(ctx)
}

