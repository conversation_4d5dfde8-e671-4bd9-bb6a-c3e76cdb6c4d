package merchant

import (

	// "io"

	"mulazim-api/controllers"
	"mulazim-api/resources/LakalaEntity"
	"strconv"

	"mulazim-api/services/merchant/lakala"

	"github.com/gin-gonic/gin"
	// "github.com/gin-gonic/gin/binding"
)

type LakalaController struct {
	controllers.BaseController
}

/***
 * @Author: [rozimamat]
 * @description: 银联商务入驻的商户迁移到拉卡拉
 * @Date: 2023-07-10 18:04:20
 */
func (lak LakalaController) Migrate(c *gin.Context) {

	lakaService := lakala.NewLakalaService(c)
	resId := c.Query("id")
	mobile := c.Query("mobile")
	merchantInfo, err := lakaService.MigrateUMS(c, resId, mobile)
	if err != nil {
		lak.Fail(c, err.Error(), -1000)
		return
	}
	lak.Success(c, merchantInfo, "", 200)
}

/***
 * @Author: [rozimamat]
 * @description:入驻通知
 * @Date: 2023-07-13 17:57:03
 * @param {*gin.Context} c
 */
func (lak LakalaController) MemberNotify(c *gin.Context) {

	var (
		params LakalaEntity.LakalaNotify

		// err         = c.ShouldBind(&params)
		err         = c.ShouldBind(&params)
		lakaService = lakala.NewLakalaService(c)
	)
	if err != nil {
		panic(err)
		return
	}

	err = lakaService.MemberNotify(c, params)
	if err != nil {
		lak.Fail(c, err.Error(), -1000)
		return
	}

	c.String(200, "SUCCESS")
}

func (lak LakalaController) CardNotify(c *gin.Context) {

	var (
		params LakalaEntity.LakalaNotify

		// err         = c.ShouldBind(&params)
		err         = c.ShouldBind(&params)
		lakaService = lakala.NewLakalaService(c)
	)
	if err != nil {
		panic(err)
		return
	}

	err = lakaService.MemberNotify(c, params)
	if err != nil {
		lak.Fail(c, err.Error(), -1000)
		return
	}

	c.String(200, "SUCCESS")
}

/*** 
 * @Author: [rozimamat]
 * @description: 2.5 查询会员信息   http://*************:6524/docs/qzt/qzt-1d4ranrhtk1r4
 * @Date: 2023-07-13 17:57:34
 * @param {*gin.Context} c
 */
func (lak LakalaController) MemberQuery(c *gin.Context) {

	lakaService := lakala.NewLakalaService(c)
	resId := c.Query("res_id")
	info, err := lakaService.MemberQuery(c, resId)
	if err != nil {
		lak.Fail(c, err.Error(), -1000)
		return
	}
	lak.Success(c, info, "", 200)
}

/*** 
 * @Author: [rozimamat]
 * @description: 查询会员商户  http://*************:6524/docs/qzt/qzt-1d4raol7h83o8
 * @Date: 2023-07-18 16:38:39
 * @param {*gin.Context} c
 */
func (lak LakalaController) MerchantQuery(c *gin.Context) {

	lakaService := lakala.NewLakalaService(c)
	member_no := c.Query("member_no")
	info, err := lakaService.MerchantQuery(c, member_no)
	if err != nil {
		lak.Fail(c, err.Error(), -1000)
		return
	}
	lak.Success(c, info, "", 200)
}

/*** 
 * @Author: [rozimamat]
 * @description: 签名dsa，测试使用
 * @Date: 2023-07-18 16:56:17
 * @param {*gin.Context} c
 */
func (lak LakalaController) Sign(c *gin.Context) {

	lakaService := lakala.NewLakalaService(c)
	content := c.PostForm("content")
	api := c.PostForm("api")
	sign_type := c.PostForm("sign_type")
	info, err := lakaService.Sign(c,sign_type,api,content)
	if err != nil {
		lak.Fail(c, err.Error(), -1000)
		return
	}
	lak.Success(c, info, "", 200)
}

/*** 
 * @Author: [rozimamat]
 * @description: 银行卡列表
 * @Date: 2023-08-02 10:10:15
 * @param {*gin.Context} c
 */
func (lak LakalaController) BankCards(c *gin.Context) {

	lakaService := lakala.NewLakalaService(c)
	resId := c.Query("res_id")

	er,merno :=lakaService.GetMerNo(resId)
	
	if !er {
		lak.Fail(c, "data_not_found", -1000)
		return
	}
	merchantInfo, err := lakaService.GetBankCards(c, merno)
	if err != nil {
		lak.Fail(c, err.Error(), -1000)
		return
	}
	lak.Success(c, merchantInfo, "", 200)
}

func (lak LakalaController) BankCardBind(c *gin.Context) {

	lakaService := lakala.NewLakalaService(c)
	resId := c.Query("res_id")
	merchantInfo, err := lakaService.BankCardBind(c, resId)
	if err != nil {
		lak.Fail(c, err.Error(), -1000)
		return
	}
	lak.Success(c, merchantInfo, "", 200)
}

/*** 
 * @Author: [rozimamat]
 * @description: 提现明细
 * @Date: 2023-08-01 12:33:10
 * @param {*gin.Context} c
 */
func (lak LakalaController) GetCashOutDetail(c *gin.Context) {

	lakaService := lakala.NewLakalaService(c)
	withdraw_id,_ := strconv.Atoi(c.Query("id"))
	cash_platform := c.Query("cash_platform")
	merchantInfo, err := lakaService.WithdrawDetail(c, withdraw_id,cash_platform)
	if err != nil {
		lak.Fail(c, err.Error(), -1000)
		return
	}
	lak.Success(c, merchantInfo, "", 200)
}

/*** 
 * @Author: [rozimamat]
 * @description: 提现通知
 * @Date: 2023-08-02 12:23:40
 * @param {*gin.Context} c
 */
func (lak LakalaController) WithdrawNotify(c *gin.Context) {

	var (
		params LakalaEntity.LakalaNotify

		// err         = c.ShouldBind(&params)
		err         = c.ShouldBind(&params)
		lakaService = lakala.NewLakalaService(c)
	)
	if err != nil {
		panic(err)
		return
	}

	err = lakaService.WithdrawNotify(c, params)
	if err != nil {
		lak.Fail(c, err.Error(), -1000)
		return
	}

	c.String(200, "SUCCESS")
}


/*** 
 * @Author: [rozimamat]
 * @description: 归档 因为错误原因 不能归档的数据
 * @Date: 2023-08-11 15:35:53
 * @param {*gin.Context} c
 */
func (lak LakalaController) ArchiveErrorItems(c *gin.Context) {

	var (
		
		lakaService = lakala.NewLakalaService(c)
	)
	
	lakaService.ArchiveErrorItems()
	
	c.String(200, "SUCCESS")
}

