package merchant

import (
	"mulazim-api/controllers"
	"mulazim-api/lang"
	"mulazim-api/observers"
	"mulazim-api/permissions"
	"mulazim-api/requests/marketingRequest"
	marketingService "mulazim-api/services/marketing"
	marketingFormat "mulazim-api/transformers/marketing"

	"github.com/gin-gonic/gin"
)

type ShipperReduceController struct {
	controllers.BaseController
}

var (
	reduceService = marketingService.NewShipmentReduceService()
)

// GetInvitation
//
// @Description: 获取没有参加的团体活动列表
// @Author: Rixat
// @Time: 2024-01-18 12:01:57
// @receiver 
// @param c *gin.Context
func (marketing *ShipperReduceController) GetInvitation(c *gin.Context) {
	type Params struct {
		MarketingType int `form:"marketing_type" binding:"required"` // 2：减配送活动
	}
	var (
		params   Params
		err      = c.ShouldBind(&params)
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	restaurantID, err := permissions.GetRestaurantIdByMerchantAdmin(admin)
	if err != nil {
		marketing.Fail(c,err.Error(),-1000)
		return
	}
	res := reduceService.GetInvitationList(restaurantID, langUtil)
	marketing.Success(c, res, "msg", 200)
}

// GetInvitationDetail
//
// @Description: 获取
// @Author: Rixat
// @Time: 2024-01-18 11:50:39
// @receiver
// @param c *gin.Context
func (marketing *ShipperReduceController) GetInvitationDetail(c *gin.Context) {
	type Params struct {
		Id int `form:"id" binding:"required"`
	}
	var (
		params       Params
		err          = c.ShouldBind(&params)
		reduceFormat = marketingFormat.NewShipmentReduceTransformer(c)
	)
	if err != nil {
		panic(err)
		return;
	}
	reduceItem := reduceService.GetTemplateDetail(params.Id)
	// 格式化数据
	res := reduceFormat.FormatMerchantShipmentReduceTemplate(reduceItem)
	marketing.Success(c, res, "msg", 200)
}

// / PostInvitationAttendance
//
// @Description: 参加团体活动
// @Author: Rixat
// @Time: 2024-01-18 11:45:22
// @receiver
// @param c *gin.Context
func (marketing *ShipperReduceController) PostInvitationAttendance(c *gin.Context) {
	type Params struct {
		Id int `form:"id" binding:"required"`
	}
	var (
		params Params
		err    = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
	}
	admin := permissions.GetAdmin(c)
	restaurantID, err := permissions.GetRestaurantIdByMerchantAdmin(admin)
	err = reduceService.AttendanceGroupMarket(params.Id, restaurantID)
	if err != nil {
		marketing.Fail(c, err.Error(), -1000)
		return
	}
	// 格式化数据
	marketing.Ok(c)
}

// List
//
// @Description: 获取营销活动列表
// @Author: Rixat
// @Time: 2023-03-02 10:36:12
// @receiver
// @param c *gin.Context
func (reduce *ShipperReduceController) GetList(c *gin.Context) {
	type Params struct {
		State int `form:"state" `                  // 状态  0：所有，1:启动，2：暂停，3：失效
		Page  int `form:"page" binding:"required"` // 当前页数
		Limit int `form:"limit"`                   // 每页显示数量
	}
	var (
		params       Params
		err          = c.ShouldBind(&params)
		reduceFormat = marketingFormat.NewShipmentReduceTransformer(c)
	)
	if err != nil {
		panic(err)
	}
	admin := permissions.GetAdmin(c)
	restaurantID, err := permissions.GetRestaurantIdByMerchantAdmin(admin)
	if err != nil {
		reduce.Fail(c, err.Error(), -1000)
		return
	}
	// 获取数据
	reduceList, totalCount := reduceService.GetShipmentReduceList(restaurantID, params.Page, params.Limit, params.State)
	res := reduceFormat.FormatMerchantShipmentReduceList(reduceList)
	// 获取列表页没参加团体活动数量
	groupMarketCount := reduceService.GetGroupMarketCount(restaurantID)
	resData := map[string]interface{}{
		"group_market_count": groupMarketCount,
		"items":              res,
		"total":              totalCount,
	}
	reduce.Success(c, resData, "msg", 200)
}

// GetDetail
//
// @Description: 获取减配送活动详情
// @Author: Rixat
// @Time: 2024-01-18 10:33:39
// @receiver
// @param c *gin.Context
func (marketing *ShipperReduceController) GetDetail(c *gin.Context) {
	type Params struct {
		Id int `form:"id" binding:"required"`
	}
	var (
		params       Params
		err          = c.ShouldBind(&params)
		reduceFormat = marketingFormat.NewShipmentReduceTransformer(c)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取详情并格式化
	marketDetail := reduceService.GetDetail(params.Id)
	marketFormat := reduceFormat.FormatMerchantShipmentReduce(marketDetail)
	marketing.Success(c, marketFormat, "msg", 200)
}

// PostCreate
//
// @Description: 创建减配送活动
// @Author: Rixat
// @Time: 2024-01-18 10:33:39
// @receiver
// @param c *gin.Context
func (reduce *ShipperReduceController) PostCreate(c *gin.Context) {
	var (
		params   marketingRequest.ShipmentReduceRequest
		err      = c.ShouldBindJSON(&params)
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	if err != nil {
		panic(err)
		return
	}
	// 验证减配送活动
	if ok, err := params.MerchantValidateData(langUtil); !ok {
		reduce.Fail(c, err.Error(), -1000)
		return
	}
	admin := permissions.GetAdmin(c)
	restaurantID, err := permissions.GetRestaurantIdByMerchantAdmin(admin)
	if err != nil {
		reduce.Fail(c, err.Error(), -1000)
		return
	}
	// 保存减配送活动
	params.RestaurantID = restaurantID
	error := reduceService.CreateShipmentReduce(admin, params)
	if error != nil {
		reduce.Fail(c, error.Error(), -1000)
		return
	}
	reduce.Ok(c)
}

// PostUpdate
//
// @Description: 更新减配送活动
// @Author: Rixat
// @Time: 2023-03-03 16:29:10
// @receiver
// @param c *gin.Context
func (reduce *ShipperReduceController) PostUpdate(c *gin.Context) {
	var (
		params   marketingRequest.ShipmentReduceRequest
		err      = c.ShouldBind(&params)
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	if err != nil {
		panic(err)
		return
	}
	if ok, err := params.MerchantValidateData(langUtil); !ok {
		reduce.Fail(c, err.Error(), -1000)
		return
	}
	if params.ID == 0 {
		reduce.Fail(c, "id_not_fund", -1000)
		return
	}
	shipper := permissions.GetAdmin(c)
	//开始观察
	observe := observers.MarketingChangeObserve{}
	observe.Observe(params.ID)
	// 更新内容
	error := reduceService.UpdateShipmentReduce(params)
	if error != nil {
		reduce.Fail(c, error.Error(), -1000)
		return
	}
	// 提交观察结果
	observe.SaveChanges(shipper)
	reduce.Ok(c)
}

// UpdateState
//
// @Description: 减配送活动修改状态
// @Author: Rixat
// @Time: 2023-03-03 16:49:23
// @receiver
// @param c *gin.Context
func (reduce *ShipperReduceController) PostUpdateState(c *gin.Context) {
	type Params struct {
		ID    int `form:"id" binding:"required"`
		State int `form:"state" binding:"required"`
	}
	var (
		params Params
		err    = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	admin := permissions.GetAdmin(c)
	//开始观察
	observe := observers.MarketingChangeObserve{}
	observe.Observe(params.ID)
	// 更新状态
	err = reduceService.UpdateShipmentReduceState(admin, params.ID, params.State)
	if err != nil {
		reduce.Fail(c, err.Error(), -1000)
		return
	}
	observe.SaveChanges(admin)
	reduce.Ok(c)
}

// PostDelete
//
// @Description: 删除减配送活动
// @Author: Rixat
// @Time: 2023-03-03 16:49:40
// @receiver
// @param c *gin.Context
func (reduce *ShipperReduceController) PostDelete(c *gin.Context) {
	type Params struct {
		ID int `form:"id" binding:"required"`
	}
	var (
		params Params
		err    = c.ShouldBind(&params)
	)
	if err != nil {
		panic(err)
		return
	}
	// 删除减配送活动
	err = reduceService.DeleteShipmentReduce(params.ID)
	if err != nil {
		reduce.Fail(c, err.Error(), -1000)
		return
	}
	reduce.Ok(c)
}

// GetStatistics
//
// @Description: 减配送活动统计
// @Author: Rixat
// @Time: 2024-01-18 10:41:52
// @receiver
// @param c *gin.Context
func (reduce *ShipperReduceController) GetStatistics(c *gin.Context) {
	type Params struct {
		MarketingID int    `form:"marketing_id" binding:"required"`
		StartDate   string `form:"start_date" binding:"required"`
		EndDate     string `form:"end_date" binding:"required"`
	}
	var (
		params   Params
		err      = c.ShouldBind(&params)
		l, _     = c.Get("lang_util")
		langUtil = l.(lang.LangUtil)
	)
	if err != nil {
		panic(err)
		return
	}
	// 获取减配送活动统计
	res := reduceService.GetStatisticData(params.MarketingID, params.StartDate, params.EndDate, langUtil)
	reduce.Success(c, res, "msg", 200)

}

// GetChangeLog
//
// @Description: 获取减配送活动修改
// @Author: Rixat
// @Time: 2024-01-18 10:53:21
// @receiver
// @param c *gin.Context
func (s *ShipperReduceController) GetChangeLog(c *gin.Context) {
	type Params struct {
		MarketingID int `form:"marketing_id" binding:"required"`
	}
	var (
		params       Params
		err          = c.ShouldBind(&params)
		reduceFormat = marketingFormat.NewShipmentReduceTransformer(c)
		l, _         = c.Get("lang_util")
		langUtil     = l.(lang.LangUtil)
	)
	if err != nil {
		panic(err)
		return
	}
	reduceItem := reduceService.GetDetail(params.MarketingID)
	formatItem := reduceFormat.FormatMerchantShipmentReduce(reduceItem)
	changeLogs := reduceService.GetMarketChangeLog(params.MarketingID, formatItem, langUtil)
	s.Success(c, changeLogs, "msg", 200)
}
