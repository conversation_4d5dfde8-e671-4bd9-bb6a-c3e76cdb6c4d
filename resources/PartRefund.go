package resources



type PartRefund struct {
	OrderId                 int    `json:"order_id" form:"order_id" binding:"required"`
	From                string `json:"from" form:"from"`
	UserId               int    `json:"user_id"	form:"user_id"`
	UserType             int `json:"user_type"	form:"user_type"`
	StoreId              int `json:"store_id"	form:"store_id"`
	TerminalId              int `json:"terminal_id"	form:"terminal_id"`
	ReasonId           int `json:"reason_id" 	form:"reason_id"`
	ReasonText         string `json:"reason_text" 	form:"reason_text"`
	RefundType           int `json:"refund_type" 	form:"refund_type"`
	RefundAmount           int `json:"refund_amount" 	form:"refund_amount"`
	FoodDetails            []PartRefundFoodDetail `json:"food_details" form:"food_details"`
	LunchBoxDetails        []PartRefundLunchBoxDetails `json:"lunch_box_details" form:"lunch_box_details" `
}

type PartRefundFoodDetail struct {
	DetailId         int `json:"detail_id"	form:"detail_id"`
	FoodId         int `json:"food_id"	form:"food_id"`
	Count         int `json:"count"	form:"count"`
	RefundPrice   int64 `json:"refund_price"	form:"refund_price"`
	// lunch_box_group_index
	LunchBoxGroupIndex int `json:"lunch_box_group_index"	form:"lunch_box_group_index"`

}

type PartRefundLunchBoxDetails struct {
	DetailId         int `json:"detail_id"	form:"detail_id"`
	LunchBoxId         int `json:"lunch_box_id"	form:"lunch_box_id"`
	Count         int `json:"count"	form:"count"`
	RefundPrice   int64 `json:"refund_price"	form:"refund_price"`
	FoodId         int `json:"food_id"	form:"food_id"`
	LunchBoxReturned int `json:"lunch_box_returned"	form:"lunch_box_returned"`
	LunchBoxAccommodate int `json:"lunch_box_accommodate"	form:"lunch_box_accommodate"`
	LunchBoxOriginalCount int `json:"lunch_box_original_count"	form:"lunch_box_original_count"`
	LunchBoxGroupIndex int `json:"lunch_box_group_index"	form:"lunch_box_group_index"`
}

