package shipper

type ShipperLoginEntity struct {
	Tokens ShipperLoginEntityTokens `json:"tokens"`
	Admin  ShipperLoginEntityAdmin  `json:"admin"`
}
type ShipperLoginEntityTokens struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
}
type ShipperLoginEntityAdmin struct {
	ID     int    `json:"id"`
	Name   string `json:"name"`
	Mobile string `json:"mobile"`
	Type   string `json:"type"`
	Avatar string `json:"avatar"`
	AttendanceState int `json:"attendance_state"`
}
