package shipper

type MyOrderEntity struct {
	Count NewOrderCount  `json:"count"`
	Items []MyOrderItems `json:"items"`
}
type MyOrderItems struct {
	ID                     int64                    `json:"id"`
	OrderID                int64                    `json:"order_id"`
	OrderAddress           string                   `json:"order_address"`
	Timezone               int64                    `json:"timezone"`
	BookingRemainingMinute int                      `json:"booking_remaining_minute"`
	BookingTime            string                   `json:"booking_time"`
	BookingDateTime        string                   `json:"booking_date_time"`
	OrderState             int64                    `json:"order_state"`
	CityID                 int64                    `json:"city_id"`
	CityName               string                   `json:"city_name"`
	AreaID                 int64                    `json:"area_id"`
	AreaName               string                   `json:"area_name"`
	StreetID               int64                    `json:"street_id"`
	StreetName             string                   `json:"street_name"`
	BuildingID             int64                    `json:"building_id"`
	BuildingName           string                   `json:"building_name"`
	ConsumeType            int64                    `json:"consume_type"`
	Price                  float64                  `json:"price"`
	Shipment               float64                  `json:"shipment"`
	LunchBoxFee            float64                  `json:"lunch_box_fee"`
	RealShipment           float64                  `json:"real_shipment"`
	DeductionFee           float64                  `json:"deduction_fee"`
	SerialNumber           int64                    `json:"serial_number"`
	OrderType              int64                    `json:"order_type"`
	StoreName              string                   `json:"store_name"`
	StoreAddress           string                   `json:"store_address"`
	Distance               float64                  `json:"distance"`
	Mobile                 string                   `json:"mobile"`
	Description            string                   `json:"description"`
	RestaurantLat          float64                  `json:"restaurant_lat"`
	RestaurantLng          float64                  `json:"restaurant_lng"`
	CustomerLat            float64                  `json:"customer_lat"`
	CustomerLng            float64                  `json:"customer_lng"`
	RestaurantTel          string                   `json:"restaurant_tel"`
	Foods                  []map[string]interface{} `json:"foods"`
	OrderIdLong            string                   `json:"order_id_long"`
	PayTypeState           int                      `json:"pay_type_state"`
	EstimatedShippingPrice string                   `json:"estimated_shipping_price"` //估计的配送费
	ShipperArrivedShopAt   string                   `json:"shipper_arrived_shop_at"`  // 配送员已到达店时间
	ShipperTakeFoodAt      string                   `json:"shipper_take_food_at"`
	FoodsReadyTime         string                   `json:"foods_ready_time"`
	PayType                string                   `json:"pay_type"`
	MsgCount               int                      `json:"msg_count"` //订单消息数量
	CustomerMobiles        []MyOrderItemsMobiles `json:"customer_mobiles"`
	RestaurantMobiles      []MyOrderItemsMobiles `json:"restaurant_mobiles"`
	SetChannel      	   int `json:"set_channel"`
	SetChannelName         string `json:"set_channel_name"`

	ActualPaid            float64 					`json:"actual_paid"`
}

type MyOrderItemsMobiles struct {
	Mobile string `json:"mobile"`
	Name   string `json:"name"`
	ShowTime int `json:"show_time"`
}