package shipper

type Pi struct {
	Key    string  `json:"key"`
	Title  string  `json:"title"`
	Amount int `json:"amount"`
	Value  int `json:"value"`
	Color  string  `json:"color"`
}
type Details struct {
	Day       string  `json:"day"`
	Total     int `json:"total"`
	Shipment  int `json:"shipment"`
	Reward    int `json:"reward"`
	Deduction int `json:"deduction"`
	Other     int `json:"other"`
	Invite     int `json:"other"`
	
}
type IncomeStatisticsPieEntity struct {
	TotalIncomeAmount int   `json:"total_income_amount"`
	Pi                []Pi      `json:"pi"`
	Details           []Details `json:"details"`
	DetailTotalMax    int   `json:"detail_total_max"`
	Headers           []Details `json:"header"`
}

type OrderPi struct {
	Key   string `json:"key"`
	Title string `json:"title"`
	Count int    `json:"count"`
	Value int    `json:"value"`
	Color string `json:"color"`
}

type OrderStatisticsDetails struct {
	Id          int    `json:"id"`
	Day         string `json:"day"`
	Total       int    `json:"total"`
	FinishCount int    `json:"finish_count"`
	CancelCount int    `json:"cancel_count"`
	FailCount   int    `json:"fail_count"`
	LateCount   int    `json:"late_count"`
}
type OrderStatisticsPieEntity struct {
	TotalOrderCount int                      `json:"total_order_count"`
	Pi              []OrderPi                `json:"pi"`
	Details         []OrderStatisticsDetails `json:"details"`
	DetailTotalMax  int                      `json:"detail_total_max"`
	Headers         []OrderStatisticsDetails `json:"headers"`
}

type UserCenterUser struct {
	Id       int    `json:"id"`
	Avatar   string `json:"avatar"`
	Name     string `json:"name"`
	RealName string `json:"real_name"`
	Mobile   string `json:"mobile"`
	Openid   *string `json:"openid"`
	Rank     int    `json:"rank"`
	LevelUpRate float64	`json:"level_up_rate"`
}

type UserCenterIncome struct {
	Day    string  `json:"day"`
	Amount int `json:"amount"`
	Count int `json:"count"`
}
type UserCenterStatisticsPieEntity struct {
	User                  UserCenterUser     `json:"user"`
	TodayOrderCount       int64              `json:"today_order_count"`
	TodayCancelOrderCount int                `json:"today_cancel_order_count"`
	TodayIncomeAmount     int            `json:"today_income_amount"`
	TodayIncomeLess       int            `json:"today_income_less"`
	TodayIncomeRate       int            `json:"today_income_rate"`
	TodayCashAmount       int            `json:"today_cash_amount"`
	IncomeStatistic       []UserCenterIncome `json:"income_statistic"`
	LateOrderCount int64                `json:"late_order_count"`

	TodayCustomerCount int64            `json:"today_customer_count"`
	TodayCustomerOrderIncome int64            `json:"today_customer_order_income"`
	TodayTipAmount int64            `json:"today_tip_amount"`
	IntroduceAdImage string  `json:"introduce_ad_image"`
	ShowAdImage int  `json:"show_ad_image"`
}


type RankStatisticsEntity struct {
	Header RankStatisticsHeaderEntity	`json:"header"`
	Items []RankStatisticsEntityItem	`json:"items"`
	ScoreZAxis []int	`json:"score_z_axis"`
	ScoreRule string `json:"score_rule"`

}
type RankStatisticsHeaderEntity struct {
	Rank int `json:"rank"`
	RankGrowthRate float64	`json:"rank_growth_rate"`
	Score float64 `json:"score"`
	ScoreGrowthRate float64	`json:"score_growth_rate"`
	StartTime string `json:"start_time"`
	EndTime string `json:"end_time"`
}

type RankStatisticsEntityItem struct { 
	Name string `json:"name"`
	Rank int `json:"rank"`
	Score float64 `json:"score"`
	Details []RankStatisticsEntityItemDetails `json:"details"`
	Date string `json:"date"`
	Week int `json:"week"`
	StartTime string `json:"start_time"`
	EndTime string `json:"end_time"`
}

type RankStatisticsEntityItemDetails struct { 
	Name string `json:"name"`
	Count int `json:"count"`
	Score float64 `json:"score"`
	GrayBackGround bool `json:"gray_back_ground"`
	Plus bool `json:"plus"`
}