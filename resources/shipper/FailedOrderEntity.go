package shipper

type FailedOrderEntity struct {
	Count  NewOrderCount       `json:"count"`
	Failed []FailedOrderFailed `json:"failed"`
	Back   []FailedOrderFailed `json:"back"`
}

type FailedOrderFailed struct {
	ID                     int64   `json:"id"`
	OrderID                int64   `json:"order_id"`
	OrderAddress           string  `json:"order_address"`
	Timezone               int64   `json:"timezone"`
	BookingRemainingMinute int64   `json:"booking_remaining_minute"`
	BookingTime            string  `json:"booking_time"`
	BookingDateTime        string  `json:"booking_date_time"`
	OrderState             int64   `json:"order_state"`
	SerialNumber           int64   `json:"serial_number"`
	FialReason             string  `json:"fial_reason"`
	Distance               float64 `json:"distance"`
	CityID                 int64   `json:"city_id"`
	CityName               string  `json:"city_name"`
	AreaID                 int64   `json:"area_id"`
	AreaName               string  `json:"area_name"`
	StreetID               int64   `json:"street_id"`
	StreetName             string  `json:"street_name"`
	BuildingID             int64   `json:"building_id"`
	BuildingName           string  `json:"building_name"`
	StoreName              string  `json:"store_name"`
	StoreAddress           string  `json:"store_address"`
	SetChannel             int     `json:"set_channel"`
	SetChannelName         string     `json:"set_channel_name"`
}
