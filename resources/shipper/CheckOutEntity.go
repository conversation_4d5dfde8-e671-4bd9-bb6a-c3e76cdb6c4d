package shipper

type CheckOut struct {
	ConsumeType int    `json:"consume_type"`
	BookingTime string `json:"booking_time"`
	ID          int    `json:"id"`
	OrderID     int    `json:"order_id"`
	State       int    `json:"state"`
	OrderPrice  int    `json:"order_price"`
	IsClearing  int    `json:"is_clearing"`
	CreatedAt   string `json:"created_at"`
}

type CheckOutTransFormAfter struct {
	ShouldPayAmount float64                       `json:"should_pay_amount"`
	Items           []CheckOutTransFormAfterItems `json:"items"`
}
type CheckOutTransFormAfterItems struct {
	ID         int     `json:"id"`
	OrderID    int     `json:"order_id"`
	OrderPrice float64 `json:"order_price"`
	State      int     `json:"state"`
	Time       string  `json:"time"`
}
