/**
@author: captain
@since: 2022/9/21
@desc:
**/

package shipper

type ShipperNotPayedOrderEntitiy struct {
	List   NotPayedOrderList   `json:"list"`
	Header NotPayedOrderHeader `json:"header"`
}

type NotPayedOrder struct {
	ID             int    `json:"id"`
	OrderID        string `json:"order_id"`
	State          int    `json:"state"`
	PayPlatForm    int    `json:"pay_platform"`
	Price          string `json:"price"`
	BookingTime    string `json:"booking_time"`
	Timezone       int    `json:"timezone"`
	CashClearState int    `json:"cash_clear_state"`
	CashClearTime  string `json:"cash_clear_time"`
	PayTime        string `json:"pay_time"`
	CreatedAt      string `json:"created_at"`
	ShowSendBtn int `json:"show_send_btn"`
	OverTimeLabel string `json:"over_time_label"`
}
type NotPayedOrderList struct {
	PerPage     int             `json:"per_page"`
	CurrentPage int             `json:"current_page"`
	NextPageURL interface{}     `json:"next_page_url"`
	PrevPageURL interface{}     `json:"prev_page_url"`
	From        int             `json:"from"`
	To          int             `json:"to"`
	Data        []NotPayedOrder `json:"data"`
}
type NotPayedOrderHeader struct {
	TotalCashOrderPrice          float64 `json:"total_cash_order_price"`
	TotalOrderCount              int     `json:"total_order_count"`
	TotalCashUncheckedOrderPrice float64 `json:"total_cash_unchecked_order_price"`
	TotalCashUncheckedOrderCount int     `json:"total_cash_unchecked_order_count"`
	TotalCashCheckedOrderPrice   float64 `json:"total_cash_checked_order_price"`
}
