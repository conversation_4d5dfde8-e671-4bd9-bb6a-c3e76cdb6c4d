package shipper

import (
	"mulazim-api/models"
	"time"
)

// OrderDetails
// @Description: OrderDetails Transformer之后的结构体
// @Time 2022-09-08 01:31:06
// <AUTHOR>
type OrderDetails struct {
	ID                     int         `json:"id"`
	OrderID                string      `json:"order_id"`
	StoreAddress           string      `json:"store_address"`
	StoreName              string      `json:"store_name"`
	StorePhone             string      `json:"store_phone"`
	Name                   string      `json:"name"`
	Mobile                 string      `json:"mobile"`
	BuildingAddress        string      `json:"building_address"`
	OrderAddress           string      `json:"order_address"`
	ConsumeType            int         `json:"consume_type"`
	PayType                string      `json:"pay_type"`
	PayTypeState           int         `json:"pay_type_state"`
	State                  int         `json:"state"`
	Price                  float32     `json:"price"`
	Shipment               uint        `json:"shipment"`
	LunchBoxFee            float32     `json:"lunch_box_fee"`
	DeliveryEndTime        string      `json:"delivery_end_time"`
	Description            string      `json:"description"`
	Timezone               int         `json:"timezone"`
	BookingRemainingMinute int64       `json:"booking_remaining_minute"`
	BookingDateTime        string      `json:"booking_date_time"`
	BookingTime            string      `json:"booking_time"`
	CityID                 int64       `json:"city_id"`
	CityName               string      `json:"city_name"`
	AreaID                 int64       `json:"area_id"`
	AreaName               string      `json:"area_name"`
	StreetID               int64       `json:"street_id"`
	StreetName             string      `json:"street_name"`
	BuildingID             int64       `json:"building_id"`
	BuildingName           string      `json:"building_name"`
	BuildingLat            float64     `json:"building_lat"`
	BuildingLng            float64     `json:"building_lng"`
	StoreLat               float64     `json:"store_lat"`
	StoreLng               float64     `json:"store_lng"`
	OrderType              int         `json:"order_type"`
	CashClearState         int         `json:"cash_clear_state"`
	CashClearTime          interface{} `json:"cash_clear_time"`
	Foods                  []Foods     `json:"foods"`
	MarketingList                  []MarketingList     `json:"marketing_list"`
	IsMarkiting                  int     `json:"is_marketing"`
}
type MarketingList struct {
	MarketingName          string  `json:"marketing_name"`
	MarketingReductionFee float32 `json:"marketing_reduction_fee"`
}
type Foods struct {
	FoodName      string  `json:"food_name"`
	FoodPrice     float32 `json:"food_price"`
	Number        uint    `json:"number"`
	LunchBoxFee   int     `json:"lunch_box_fee"`
	LunchBoxCount int     `json:"lunch_box_count"`
}

// OrderDetailsInfo
// @Description: 订单详细Transformer之前的结构体 ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓
// @Time 2022-09-07 12:23:03
// <AUTHOR>
type OrderDetailsInfo struct {
	ID               int           `json:"id"`
	StoreID          int           `json:"store_id"`
	Timezone         int           `json:"timezone"`
	OrderID          string        `json:"order_id"`
	State            int           `json:"state"`
	BuildingID       int           `json:"building_id"`
	BookingTime      string        `json:"booking_time"`
	Name             string        `json:"name"`
	Mobile           string        `json:"mobile"`
	OrderAddress     string        `json:"order_address"`
	PayType          PayType       `json:"pay_type"`
	PayTypeState     int           `json:"pay_type_state"`
	ConsumeType      int           `json:"consume_type"`
	Price            float32       `json:"price"`
	Shipment         uint          `json:"shipment"`
	LunchBoxFee      float32       `json:"lunch_box_fee"`
	DeliveryEndTime  string        `json:"delivery_end_time"`
	Description      string        `json:"description"`
	CreatedAt        string        `json:"created_at"`
	OrderType        int           `json:"order_type"`
	CashClearState   int           `json:"cash_clear_state"`
	CashClearTime    time.Time     `json:"cash_clear_time"`
	PayTime          string        `json:"pay_time"`
	CashClearChannel interface{}   `json:"cash_clear_channel"`
	FoodSum          string        `json:"food_sum"`
	BookingDateTime  string        `json:"booking_date_time"`
	Restaurant       Restaurant    `json:"restaurant"`
	OrderDetail      []OrderDetail `json:"order_detail"`
	UserAddress      UserAddress   `json:"user_address"`
	MarketingList    []models.MarketingOrderLog   `json:"marketing_list"`

}
type PayType struct {
	ID     int    `json:"id"`
	NameUg string `json:"name_ug"`
	NameZh string `json:"name_zh"`
}
type Restaurant struct {
	ID        int     `json:"id"`
	NameUg    string  `json:"name_ug"`
	NameZh    string  `json:"name_zh"`
	AddressUg string  `json:"address_ug"`
	AddressZh string  `json:"address_zh"`
	Tel       string  `json:"tel"`
	Lat       float64 `json:"lat"`
	Lng       float64 `json:"lng"`
}
type RestaurantFood struct {
	ID     int    `json:"id"`
	NameUg string `json:"name_ug"`
	NameZh string `json:"name_zh"`
	Price  uint   `json:"price"`
}
type OrderDetail struct {
	ID              int            `json:"id"`
	OrderID         int            `json:"order_id"`
	StoreFoodsID    int            `json:"store_foods_id"`
	OriginalPrice   float32        `json:"original_price"`
	Price           float32        `json:"price"`
	MpPercent       float32        `json:"mp_percent"`
	DiscountPercent float32        `json:"discount_percent"`
	DealerPercent   float32        `json:"dealer_percent"`
	MpProfit        float32        `json:"mp_profit"`
	DealerProfit    float32        `json:"dealer_profit"`
	Number          uint           `json:"number"`
	LunchBoxID      interface{}    `json:"lunch_box_id"`
	LunchBoxFee     int            `json:"lunch_box_fee"`
	LunchBoxCount   int            `json:"lunch_box_count"`
	SeckillID       interface{}    `json:"seckill_id"`
	CreatedAt       string         `json:"created_at"`
	UpdatedAt       string         `json:"updated_at"`
	DeletedAt       interface{}    `json:"deleted_at"`
	RestaurantFood  RestaurantFood `json:"restaurant_food"`
}
type UserAddress struct {
	CityID               int64   `json:"city_id"`
	CityName             string  `json:"city_name"`
	CityNameUg           string  `json:"city_name_ug"`
	CityNameZh           string  `json:"city_name_zh"`
	AreaID               int64   `json:"area_id"`
	AreaName             string  `json:"area_name"`
	AreaNameUg           string  `json:"area_name_ug"`
	AreaNameZh           string  `json:"area_name_zh"`
	StreetID             int64   `json:"street_id"`
	StreetName           string  `json:"street_name"`
	StreetNameUg         string  `json:"street_name_ug"`
	StreetNameZh         string  `json:"street_name_zh"`
	StreetPrefixUg       string  `json:"street_prefix_ug"`
	StreetPrefixZh       string  `json:"street_prefix_zh"`
	BuildingID           int64   `json:"building_id"`
	BuildingName         string  `json:"building_name"`
	BuildingNameUg       string  `json:"building_name_ug"`
	BuildingNameZh       string  `json:"building_name_zh"`
	BuildingLat          float64 `json:"building_lat"`
	BuildingLng          float64 `json:"building_lng"`
	BuildingPrefixUg     string  `json:"building_prefix_ug"`
	BuildingPrefixZh     string  `json:"building_prefix_zh"`
	CityState            int     `json:"city_state"`
	PerKmShipment        int     `json:"per_km_shipment"`
	MinShipment          int     `json:"min_shipment"`
	ServicePhone         string  `json:"service_phone"`
	AreaWeight           int     `json:"area_weight"`
	AreaRestingStartTime string  `json:"area_resting_start_time"`
	AreaRestingEndTime   string  `json:"area_resting_end_time"`
	AreaRestingContentUg string  `json:"area_resting_content_ug"`
	AreaRestingContentZh string  `json:"area_resting_content_zh"`
	AreaState            int     `json:"area_state"`
	StreetWeight         int     `json:"street_weight"`
	StreetState          int     `json:"street_state"`
	BuildingType         int     `json:"building_type"`
	BuildingWeight       int     `json:"building_weight"`
	BuildingState        int     `json:"building_state"`
}
