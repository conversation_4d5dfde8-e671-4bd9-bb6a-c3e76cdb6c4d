package shipper

type ShipperIncomeDetailTag struct {
	IsAmount bool   `json:"is_amount"`
	Title    string `json:"title"`
	Type     int    `json:"type"`
	Value    int 	`json:"value"`
    Color    string `json:"color"`
}

type IncomeResponse struct {
    ID                      int         `json:"id"`
    TypeName                string      `json:"type_name"`
    UserName                string      `json:"user_name"`
    OrderID                 int         `json:"order_id"`
    OrderNo                 string      `json:"order_no"`
    OrderPrice              int     	`json:"order_price"`
    OrderType               int         `json:"order_type"`
    ShipmentIncome          int     	`json:"shipment_income"`
    Amount                  int     	`json:"amount"`
    ComplaintType           int         `json:"complaint_type"`
    IsComplaint             bool        `json:"is_complaint"`
    IsLate                  bool        `json:"is_late"`
    OrderDeliveryEndTime    string      `json:"order_delivery_end_time"`
    OrderCreatedAt          string      `json:"order_created_at"`
    ResName                 string      `json:"res_name"`
}

type ShipperInsurance struct {
    NameUg string `json:"name_ug"`
    NameZh string `json:"name_zh"`
    Amount int    `json:"amount"` 
}