package shipper

type NewOrderData struct {
	Count      NewOrderCount   `json:"count"`
	Items      []NewOrderItems `json:"items"`
	OrderCount int             `json:"order_count"`
}
type NewOrderCount struct {
	NewList      int64 `json:"new_list"`
	Back         int64 `json:"back"`
	MyOrder      int64 `json:"myOrder"`
	Failed       int64 `json:"failed"`
	Succeed      int64 `json:"succeed"`
	CommentCount int64 `json:"comment_count"`
}
type NewOrderItems struct {
	ID                     int     `json:"id"`
	StoreID                int     `json:"store_id"`
	Distance               float64 `json:"distance"`
	OrderAddress           string  `json:"order_address"`
	OrderType              int     `json:"order_type"`
	StoreName              string  `json:"store_name"`
	StoreAddress           string  `json:"store_address"`
	OrderState             int     `json:"order_state"`
	SerialNumber           int     `json:"serial_number"`
	Timezone               int     `json:"timezone"`
	BookingTime            string  `json:"booking_time"`
	CityID                 int     `json:"city_id"`
	CityName               string  `json:"city_name"`
	AreaID                 int     `json:"area_id"`
	AreaName               string  `json:"area_name"`
	StreetID               int     `json:"street_id"`
	StreetName             string  `json:"street_name"`
	BuildingID             int     `json:"building_id"`
	BuildingName           string  `json:"building_name"`
	BookingDateTime        string  `json:"booking_date_time"`
	BookingRemainingMinute int     `json:"booking_remaining_minute"`
	ShippingPrice          string  `json:"estimated_shipping_price"` //估计的配送费
	PayType                string  `json:"pay_type"`
	FoodsReadyTime         string  `json:"foods_ready_time"`
	FoodsReadyRemainMinute int     `json:"foods_ready_remain_time"`
}

// ListByStreet
//
//	@Description: 按街道获取订单列表
type ListByStreet struct {
	StreetID   int    `json:"id"`
	StreetName string `json:"name"`
	OrderCount int    `json:"order_count"`
}

// Statistics
// @Description: 配送员统计实体类
// @Time 2022-09-11 00:52:36
// <AUTHOR>
type Statistics struct {
	State              int     `json:"state"`
	ConsumeType        int     `json:"consume_type"`
	OrderCount         int     `json:"order_count"`
	TotalPrice         float32 `json:"total_price"`
	TotalLunchboxPrice float32 `json:"total_lunchbox_price"`
}

// StatisticsTrf
// @Description: 配送员统计 Transformer 后返回的结构体
// @Time 2022-09-12 01:02:06
// <AUTHOR>
type StatisticsTrf struct {
	Back       StatisticsTrfBack `json:"back"`
	Failed     StatisticsTrfBack `json:"failed"`
	Succeed    StatisticsTrfBack `json:"succeed"`
	TotalCount int               `json:"total_count"`
	TotalPrice float32           `json:"total_price"`
}

// StatisticsTrfBack
// @Description: 配送员统计返回的不同的状态
// @Time 2022-09-12 01:03:42
// <AUTHOR>
type StatisticsTrfBack struct {
	CashOrderCount   int     `json:"cash_order_count"`
	CashOrderPrice   float32 `json:"cash_order_price"`
	OnlineOrderCount int     `json:"online_order_count"`
	OnlinePayPrice   float32 `json:"online_pay_price"`
}

// StatisticsDetailTransformerBefore
// @Description: 配送员统计详细 Transformer之前
// @Time 2022-09-13 20:59:35
// <AUTHOR>
type StatisticsDetailTransformerBefore struct {
	ID                  int     `json:"id"`
	OrderID             int     `json:"order_id"`
	ReasonID            int     `json:"reason_id"`
	OrderTodayID        int     `json:"order_today_id"`
	State               int     `json:"state"`
	BookingTime         string  `json:"booking_time"`
	OrderAddress        string  `json:"order_address"`
	Timezone            int     `json:"timezone"`
	CreatedAt           string  `json:"created_at"`
	BuildingID          int     `json:"building_id"`
	CityID              int     `json:"city_id"`
	CityNameUg          string  `json:"city_name_ug"`
	CityNameZh          string  `json:"city_name_zh"`
	AreaID              int     `json:"area_id"`
	AreaNameUg          string  `json:"area_name_ug"`
	AreaNameZh          string  `json:"area_name_zh"`
	StreetID            int     `json:"street_id"`
	StreetNameUg        string  `json:"street_name_ug"`
	StreetNameZh        string  `json:"street_name_zh"`
	BuildingNameUg      string  `json:"building_name_ug"`
	BuildingNameZh      string  `json:"building_name_zh"`
	ReasonUg            string  `json:"reason_ug"`
	ReasonZh            string  `json:"reason_zh"`
	RestaurantNameUg    string  `json:"restaurant_name_ug"`
	RestaurantNameZh    string  `json:"restaurant_name_zh"`
	RestaurantAddressUg string  `json:"restaurant_address_ug"`
	RestaurantAddressZh string  `json:"restaurant_address_zh"`
	Distance            float64 `json:"distance"`
}

// StatisticsDetailTransformerAfter
// @Description: 配送员统计详细 Transformer之后
// @Time 2022-09-14 01:59:56
// <AUTHOR>
type StatisticsDetailTransformerAfter struct {
	PerPage     int                                     `json:"per_page"`
	CurrentPage int                                     `json:"current_page"`
	Total       int                                     `json:"total"`
	Items       []StatisticsDetailTransformerAfterItems `json:"items"`
}

// StatisticsDetailTransformerAfterItems
// @Description: 配送员统计详细 Transformer之后的Items
// @Time 2022-09-14 10:32:58
// <AUTHOR>
type StatisticsDetailTransformerAfterItems struct {
	ID           int         `json:"id"`
	OrderID      int         `json:"order_id"`
	StoreName    interface{} `json:"store_name"`
	StoreAddress interface{} `json:"store_address"`
	OrderAddress interface{} `json:"order_address"`
	Distance     float64     `json:"distance"`
	Timezone     int         `json:"timezone"`
	BookingTime  interface{} `json:"booking_time"`
	CreatedAt    string      `json:"created_at"`
	OrderState   int         `json:"order_state"`
	CityID       int         `json:"city_id"`
	CityName     string      `json:"city_name"`
	AreaID       int         `json:"area_id"`
	AreaName     string      `json:"area_name"`
	StreetID     int         `json:"street_id"`
	StreetName   string      `json:"street_name"`
	BuildingID   int         `json:"building_id"`
	BuildingName string      `json:"building_name"`
	FialReason   interface{} `json:"fial_reason"`
}

// AdminOrderListTransBefore
// @Description: AdminOrderList结构体 Transformer之前
// @Time 2022-09-15 10:24:26
// <AUTHOR>
type AdminOrderListTransBefore struct {
	ID                int     `json:"id"`
	OrderID           int     `json:"order_id"`
	Distance          float64 `json:"distance"`
	AdminID           int     `json:"admin_id"`
	OrderTodayID      int     `json:"order_today_id"`
	StoreID           int     `json:"store_id"`
	BuildingID        int     `json:"building_id"`
	OrderAddress      string  `json:"order_address"`
	Timezone          int     `json:"timezone"`
	BookingTime       string  `json:"booking_time"`
	OrderType         int     `json:"order_type"`
	RestaurantID      int     `json:"restaurant_id"`
	RestaurantName    string  `json:"restaurant_name"`
	RestaurantAddress string  `json:"restaurant_address"`
	CityID            int     `json:"city_id"`
	CityName          string  `json:"city_name"`
	AreaID            int     `json:"area_id"`
	AreaName          string  `json:"area_name"`
	StreetID          int     `json:"street_id"`
	StreetName        string  `json:"street_name"`
	BuildingName      string  `json:"building_name"`
}

// AdminOrderListTransAfter
// @Description: AdminOrderList结构体
// @Time 2022-09-15 17:41:45
// <AUTHOR>
type AdminOrderListTransAfter struct {
	AdminName  string                          `json:"admin_name"`
	OrderCount int                             `json:"order_count"`
	Items      []AdminOrderListTransAfterItems `json:"items"`
}
type AdminOrderListTransAfterItems struct {
	OrderID      int         `json:"order_id"`
	Distance     float64     `json:"distance"`
	OrderAddress string      `json:"order_address"`
	Timezone     int         `json:"timezone"`
	BookingTime  string      `json:"booking_time"`
	StoreID      int         `json:"store_id"`
	StoreName    interface{} `json:"store_name"`
	StoreAddress interface{} `json:"store_address"`
	CityID       int         `json:"city_id"`
	CityName     string      `json:"city_name"`
	AreaID       int         `json:"area_id"`
	AreaName     string      `json:"area_name"`
	StreetID     int         `json:"street_id"`
	StreetName   string      `json:"street_name"`
	BuildingID   int         `json:"building_id"`
	BuildingName string      `json:"building_name"`
	OrderType    int         `json:"order_type"`
}
