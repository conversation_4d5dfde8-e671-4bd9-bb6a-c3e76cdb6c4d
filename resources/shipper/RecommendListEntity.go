package shipper

type RecommendListEntity struct {
	Recommends []Recommends `json:"recommends"`
	Count      int64        `json:"count"`
}
type Recommends struct {
	Name      string `json:"name"`
	Mobile    string `json:"mobile"`
	CityName  string `json:"city_name"`
	CreatedAt string `json:"created_at"`
}

type TipsEntity struct {
	Tips []Tips `json:"tips"`
}

type Tips struct {
	UserName   string  `json:"user_name"`
	UserAvatar string  `json:"user_avatar"`
	Amount     float32 `json:"amount"`
	Id         int     `json:"id"`
	CreatedAt  string  `json:"created_at"`
}
