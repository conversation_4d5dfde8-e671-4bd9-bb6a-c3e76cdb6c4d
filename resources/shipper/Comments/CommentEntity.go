/*
*
@author: captain
@since: 2022/9/2
@desc:
*
*/
package Comments

type CommentListEntity struct {
	Shipper Shipper  `json:"shipper"`
	Counts  []Counts `json:"counts"`
	Page    Page     `json:"page"`
	Items   []Items  `json:"items"`
}

type Shipper struct {
	ID                        int    `json:"id"`
	ShipperName               string `json:"shipper_name"`
	ShipperMobile             string `json:"shipper_mobile"`
	ShipperAvatar             string `json:"shipper_avatar"`
	AreaName                  string `json:"area_name"`
	ShipperDistance           int    `json:"shipper_distance"`
	ShipperOnTimeDeliveryRate int    `json:"shipper_on_time_delivery_rate"`
	ShipperCustomerRate       int    `json:"shipper_customer_rate"`
	ShipperDeliveryAvgTime    int    `json:"shipper_delivery_avg_time"`
}
type Counts struct {
	NameUg string `json:"name_ug"`
	NameZh string `json:"name_zh"`
	Type   int    `json:"type"`
	Count  int    `json:"count"`
}
type Page struct {
	PerPage     int `json:"per_page"`
	CurrentPage int `json:"current_page"`
	LastPage    int `json:"last_page"`
}
type Items struct {
	ID          int       `json:"id"`
	Star        uint      `json:"star"`
	UserName    string    `json:"user_name"`
	Text        string    `json:"text"`
	UserAvatar  string    `json:"user_avatar"`
	IsAnonymous int       `json:"is_anonymous"`
	IsSatisfied int       `json:"is_satisfied"`
	CreatedAt   string    `json:"created_at"`
	Replies     []Replies `json:"replies"`
	Amount int       `json:"amount"`
}

type Replies struct {
	ID        int    `json:"id"`
	CommentID int    `json:"comment_id"`
	Type      int    `json:"type"`
	Text      string `json:"text"`
	AdminID   int    `json:"admin_id"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}
