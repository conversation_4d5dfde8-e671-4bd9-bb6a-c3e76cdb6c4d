package shipper
type MaterialCustomerListEntity struct {
	CategoryName string         `json:"category_name"`
	CategoryID   int64            `json:"category_id"`
	SortColumn string `json:"sort_column"`
	SortType   string `json:"sort_type"`
	Header       Header         `json:"header"`
	CustomerList []CustomerList `json:"customer_list"`
}
type Header struct {
	TotalCustomerCount int64 `json:"total_customer_count"`
	TotalOrderCount    int64 `json:"total_order_count"`
}

type CustomerList struct {
	ID         int64    `json:"id"`
	Name       string `json:"name"`
	Img        string `json:"img"`
	InviteTime string `json:"invite_time"`
	UserProfit int64    `json:"user_profit"`
	OrderCount int64    `json:"order_count"`
	OrderPrice int64    `json:"order_price"`
	MaterialName string `json:"material_name"`
}

type StatisticsHeader struct {
	TotalCustomerCount int64 `json:"total_customer_count"`
	TotalOrderCount    int64 `json:"total_order_count"`
	TotalOrderPrice     int64 `json:"total_order_price"`
	TotalOrderTipsFee    int64 `json:"total_order_tips"`
}

type StatisticsMaterialList struct {
	Name       string `json:"name"`
	UserCount int64    `json:"user_count"`
	OrderCount int64    `json:"order_count"`
	OrderPrice int64    `json:"order_price"`
}

type MaterialStatisticListDiagram struct {
	Day       string  `json:"day"`
	Total     int `json:"total"`
}

type MaterialStatisticListEntity struct {
	CategoryName string         `json:"category_name"`
	Diagram []MaterialStatisticListDiagram          `json:"diagram"` 
	Header       StatisticsHeader         `json:"header"`
	StatisticsMaterialList []StatisticsMaterialList `json:"list"`
}