package resources

type TerminalEntity struct {
	ID          int    `json:"id"`
	Name        string `json:"name"`
	OsType      int    `json:"os_type"`
	Type      int    `json:"type"`
	Icon        string `json:"icon"`
	Version     string `json:"version"`
	VersionCode int    `json:"version_code"`
	PackageName string `json:"package_name"`
	ForceUpdate int    `json:"force_update"`
	URL         string `json:"url"`
	Des         string `json:"des"`
}
