package resources



type CheckInfo struct {
	Id                 int    `json:"id"`
	VerifyContent      string `json:"verify_content"`
	State              int    `json:"state"`
	BankAcctType       string `json:"bank_acct_type"`
	BankAcctNum        string `json:"bank_acct_num"`
	BankAcctName       string `json:"bank_acct_name"`
	BankId             int    `json:"bank_id"`
	VerifyAmount       string `json:"verify_amount"`
	VerifyUrl          string `json:"verify_url"`
	RegMerType         string `json:"reg_mer_type"`
	UmsRegId           string `json:"ums_reg_id"`
	MerIdcardNum       string `json:"mer_idcard_num"`        // 经营者身份证号(商户)
	ShopLicenseNum     string `json:"shop_license_num"`      // 社会信用统一代码/营业执照号
	ShopName           string `json:"shop_name"`             // 营业执照店铺名称
	PermitLicNum       string `json:"permit_lic_num"`        // 许可证编号
	ShopBusinessName   string `json:"shop_business_name"`    // 店铺门牌名称
	PayProduct         string `json:"pay_product"`           // 开通的支付业务
	ShopBusinessCityID string `json:"shop_business_city_id"` // 店铺地区/城市
	ShopCategoryCode   string `json:"shop_category_code"`    // 商家业务类别代码（比如：餐厅、超市等）
	BankCityId         int    `json:"bank_city_id"`          // 开户银行城市
	SubmitType         int    `json:"submit_type"`           //
	LegalName          string `json:"legal_name"`
	MerMobile          string `json:"mer_mobile"`
	MerSex			  string  		 `json:"mer_sex"`
	LakalaVerifyState  int    `json:"lakala_verify_state"`  //
	LakalaVerifyMobile string `json:"lakala_verify_mobile"` //
	CityId         int    `json:"city_id"`           //
	AreaId         int    `json:"area_id"`           //
}
