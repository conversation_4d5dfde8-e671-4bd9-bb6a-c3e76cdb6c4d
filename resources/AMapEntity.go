package resources


type AMapEntity struct {
	Status string `json:"status"`
	Info string `json:"info"`
	Infocode string `json:"infocode"`
	Count string `json:"count"`
	Route AMapRoute `json:"route"`
}

type AMapSteps struct {
	Instruction string `json:"instruction"`
	Orientation string `json:"orientation"`
	Road string `json:"road"`
	Distance string `json:"distance"`
	Duration string `json:"duration"`
	Polyline string `json:"polyline"`
	Action string `json:"action"`
	AssistantAction []interface{} `json:"assistant_action"`
	WalkType string `json:"walk_type"`
}

type AMapPaths struct {
	Distance string `json:"distance"`
	Duration string `json:"duration"`
	Steps []AMapSteps `json:"steps"`
}

type AMapRoute struct {
	Origin string `json:"origin"`
	Destination string `json:"destination"`
	Paths []AMapPaths `json:"paths"`
}



type AMapEntityMass struct {
	Status string `json:"status"`
	Info string `json:"info"`
	Infocode string `json:"infocode"`
	Count string `json:"count"`
	Results []AMapEntityMassResults `json:"results"`
}

type AMapEntityMassResults struct {
	OriginId string `json:"origin_id"`
	DestId string `json:"dest_id"`
	Distance string `json:"distance"`
	Duration string `json:"duration"`
}


type BuildingShippingAreaSteps struct {
	Start int `json:"start"`
	End   int `json:"end"`
	MinShippingPrice int `json:"min_shipping_price"`
	FixedShippingPrice int `json:"fixed_shipping_price"`
	ShippingTime int `json:"shipping_time"`
}

type AMapEntityRiding struct {
	Status string `json:"status"`
	Info string `json:"info"`
	Infocode string `json:"infocode"`
	Count string `json:"count"`
	Route AMapEntityRoute `json:"route"`
}

type AMapEntityRidingSteps struct {
	Instruction string `json:"instruction"`
	Orientation string `json:"orientation"`
	Road []interface{} `json:"road"`
	Distance string `json:"distance"`
	Duration string `json:"duration"`
	Polyline string `json:"polyline"`
	Action string `json:"action"`
	AssistantAction []interface{} `json:"assistant_action"`
	WalkType string `json:"walk_type"`
}

type AMapEntityRidingPaths struct {
	Distance string `json:"distance"`
	Duration string `json:"duration"`
	Steps []AMapEntityRidingSteps `json:"steps"`
}

type AMapEntityRoute struct {
	Origin string `json:"origin"`
	Destination string `json:"destination"`
	Paths []AMapEntityRidingPaths `json:"paths"`
}