package cms

import (
	"mulazim-api/resources"
	"mulazim-api/resources/merchant"
)

type PartRefundReasonFormat struct {
	ID        int    `json:"id"`
	Name      string `json:"name"`
	NameUg    string `json:"name_ug"`
	NameZh    string `json:"name_zh"`
	Weight    int    `json:"weight"`
	State     int    `json:"state"`
	Type      int    `json:"type"`
	SubType   int    `json:"sub_type"`
	CreatedAt string `json:"created_at"`
}

type PartRefundReasonListResource struct {
	Items []PartRefundReasonFormat `json:"items"`
	Total int64                    `json:"total"`
}

type PartRefundFormat struct {
	ID                    int64  `json:"id"`
	RestaurantName        string `json:"restaurant_name"`
	CreatorName           string `json:"creator_name"`
	OrderNumber           string `json:"order_number"`
	PartRefundState       int64  `json:"part_refund_state"`
	Reason                string `json:"reason"`
	RefundTime            string `json:"refund_time"`
	CreatedAt             string `json:"created_at"`
	OrderAddress          string `json:"order_address"`
	AreaName              string `json:"area_name"`
	CityName              string `json:"city_name"`
	PartRefundCreatorType int64  `json:"part_refund_creator_type"` // 1:商家 2:后台
	PartRefundType        int64  `json:"part_refund_type"`         // 1:全额 2:部分退款
	UserName              string `json:"user_name"`
	UserMobile            string `json:"user_mobile"`
}

type PartRefundListResource struct {
	Items []PartRefundFormat `json:"items"`
	Total int64              `json:"total"`
}

type PartRefundOrderDetailFormat struct {
	ID             int64  `json:"id"`
	OrderNumber    string `json:"order_number"`
	UserName             string                `json:"user_name"`
	UserMobile           string                `json:"user_mobile"`
	UserRank       *int   `json:"user_rank"`
	Terminal             string                `json:"terminal"`
	RestaurantName string `json:"restaurant_name"`
	RestaurantPhone      string                `json:"restaurant_phone"`
	ShipperMobile        string                `json:"shipper_mobile"`
	CreatorName          string                `json:"creator_name"`
	RefundReason         string                `json:"refund_reason"`
	RefundReasonText     string                `json:"refund_reason_text"`

	PartRefundDetail PartRefundDetail   `json:"part_refund_detail"`
	Shipper          *PartRefundShipper `json:"shipper"`

	OriginalOrderDetails []OriginalOrderDetail `json:"original_order_details"`
	RefundDetails        []OriginalOrderDetail `json:"refund_details"`
	OriginalPayDetails   []OriginalPayDetail   `json:"original_pay_details"`
	MarketDetails        []MarketDetail        `json:"market_details"`
}

type PartRefundDetail struct {
	CreatedAt string `json:"created_at"` // 创建时间 （目前用作退款创建时间）

	DeliveryType      int    `json:"delivery_type"`       // 配送类型  1: 系统配送 2:自取
	OrderType         int    `json:"order_type"`          //  0:表示预订单；1：表示实时订单
	Description       string `json:"description"`         // 订单备注
	Timezone          int    `json:"timezone"`            // 预定时间时区：6乌鲁木齐时间，8北京时间
	BookingTime       string `json:"booking_time"`        // 预订时间
	PayTime           string `json:"pay_time"`            // 付费时间
	PrintTime         string `json:"print_time"`          // 打印时间
	PrintedTime       string `json:"printed_time"`        // 订单打印完成时间
	DeliveryTakedTime string `json:"delivery_taked_time"` // 抢订单时间
	DeliveryStartTime string `json:"delivery_start_time"` // 开始配送时间
	DeliveryEndTime   string `json:"delivery_end_time"`   // 订单结束时间

	PartRefundCreatorId   int64  `json:"part_refund_creator_id"`   // 管理员id
	PartRefundCreatorType int64  `json:"part_refund_creator_type"` // 1:用户 2:后台 3:商家 4:自动退单
	PartRefundReasonId    int64  `json:"part_refund_reason_id"`    // 退款原因 id
	PartRefundReasonText  string `json:"part_refund_reason_text"`  // 退款原因
	PartRefundType        int64  `json:"part_refund_type"`         // 1:全额 2:部分退款
	PartRefundAmount      int64  `json:"part_refund_amount"`       // 部分退款金额
	PartRefundState       int64  `json:"part_refund_state"`        // 退款状态 1.新建 2.成功 3：失败
	PartRefundFailReason  string `json:"part_refund_fail_reason"`  // 退款失败原因
	PartRefundedTime      string `json:"part_refunded_time"`
}

type PartRefundShipper struct {
	ID          int    `json:"id"`          // 自增编号
	Avatar      string `json:"avatar"`      // 用户头像
	Mobile      string `json:"mobile"`      // 联系电话
	Name        string `json:"name"`        // 管理员账号
	RealName    string `json:"real_name"`   // 管理员实名
	Description string `json:"description"` // 描述
}

type OriginalOrderDetail struct {
	ID int64 `json:"id"`
	FoodName      string `json:"food_name"`
	FoodCount     int    `json:"food_count"`
	OriginalPrice int    `json:"original_price"`
	Price         int    `json:"price"`
	MarkupPrice   int    `json:"markup_price"`
	TotalPrice    int    `json:"total_price"`

	FoodType       uint8                            `json:"food_type"`
	FoodSpecOption []resources.FoodSpecOption       `json:"food_spec_option,omitempty"`
	ComboFoodItems []merchant.CommentFoodComboItems `json:"combo_food_items,omitempty"`
}

type OriginalPayDetail struct {
	PayTypeName string `json:"pay_type_name"`
	FootCount   int    `json:"foot_count"`
	FoodPrice   int    `json:"food_price"`
	Shipment    int    `json:"shipment"`
	OrderPrice  int    `json:"order_price"`
	ActualPaid  int    `json:"actual_paid"`
	NeedPay     int    `json:"need_pay"`
	ResAmount   int    `json:"res_amount"`
}

type MarketDetail struct {
	MarketType   int    `json:"market_type"`
	MarketName   string `json:"market_name"`
	ReduceAmount int    `json:"reduce_amount"`
}