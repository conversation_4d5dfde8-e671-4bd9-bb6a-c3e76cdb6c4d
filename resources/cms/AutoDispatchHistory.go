package cms

type AutoDispatchHistoryList struct {
    List []AutoDispatchHistory `json:"list"`
    Total int `json:"total"`
}

type AutoDispatchHistory struct {

    ID                int             `json:"id"`

    // CityID            int             `json:"city_id"`
    // AreaID            int             `json:"area_id"`
    CityName          string          `json:"city_name"`
    AreaName          string          `json:"area_name"`
    OrderID           int             `json:"order_id"`
    OrderNo           string          `json:"order_no"`
    // OrderRank         int             `json:"order_rank"`

    // OrderScore        float64         `json:"order_score"`
    ShipperID         int             `json:"shipper_id"`
    ShipperName       string          `json:"shipper_name"`
    ShipperMobile       string          `json:"shipper_mobile"`
    
    ShipperRank       int             `json:"shipper_rank"`
    ShipperScore      float64         `json:"shipper_score"`
    
    ShipperLng        float64         `json:"shipper_lng"`
    ShipperLat        float64         `json:"shipper_lat"`
    ShipperBoxCapacity int            `json:"shipper_box_capacity"`
    ShipperOrderInfo  string         `json:"shipper_order_info"`

    // UserID            int             `json:"user_id"`
    // UserRank          int             `json:"user_rank"`
    // UserScore         float64         `json:"user_score"`

    DispatchType      int             `json:"dispatch_type"`
    Remark            string          `json:"remark"`
    RequestID         string          `json:"request_id"`
    CreatedAt         string       `json:"created_at"`
    // UpdatedAt         string       `json:"updated_at"`

}