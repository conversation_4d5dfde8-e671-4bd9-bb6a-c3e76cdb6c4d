package cms

import (
	"errors"
	"strings"
	"time"
)

type FlexibleTime struct {
	time.Time
}

// 支持的日期格式列表
var supportedFormats = []string{
	"2006-01-02 15:04:05", // 完整日期时间格式
	"2006-01-02 15:04",    // 年-月-日 小时:分钟
	"2006-01-02 15",       // 年-月-日 小时
	"2006-01-02",          // 年-月-日
	"2006-01",             // 年-月
	"2006",                // 年
}

// UnmarshalJSON 实现，用于解析多种时间格式
func (ft *FlexibleTime) UnmarshalJSON(b []byte) error {
	s := strings.Trim(string(b), `"`) // 去掉引号

	for _, format := range supportedFormats {
		parsedTime, err := time.Parse(format, s)
		if err == nil {
			ft.Time = parsedTime
			return nil
		}
	}
	return errors.New("unsupported date format")
}

type CreateMiniGameActivityParams struct {
	NameUg          string       `json:"name_ug" binding:"required"`        // 名称维吾尔语
	NameZh          string       `json:"name_zh" binding:"required"`        // 名称汉语
	RuleUg          string       `json:"rule_ug" binding:"required"`        // 活动规则维文
	RuleZh          string       `json:"rule_zh" binding:"required"`        // 活动规则中文
	GameRuleUg      string       `json:"game_rule_ug" binding:"required"`   // 游戏规则维文
	GameRuleZh      string       `json:"game_rule_zh" binding:"required"`   // 游戏规则中文
	UseAmountRuleUg string       `json:"use_amount_rule_ug"`                // 优惠金额使用规则维文
	UseAmountRuleZh string       `json:"use_amount_rule_zh"`                // 优惠金额使用规则中文
	StartTime       FlexibleTime `json:"start_time" binding:"required"`     // 开始时间
	EndTime         FlexibleTime `json:"end_time" binding:"required"`       // 结束时间
	StartUseTime    FlexibleTime `json:"start_use_time" binding:"required"` // 积分开始使用时间
	EndUseTime      FlexibleTime `json:"end_use_time" binding:"required"`   // 积分结束使用时间
	State           int          `json:"state" binding:"oneof=1 0"`         // 状态
	Type            int          `json:"type" binding:"required"`           // 游戏类型
}

type UpdateMiniGameActivityParams struct {
	ID              int          `json:"id" binding:"required"`                 // 活动ID
	NameUg          string       `json:"name_ug" binding:"required"`            // 名称维吾尔语
	NameZh          string       `json:"name_zh" binding:"required"`            // 名称汉语
	RuleUg          string       `json:"rule_ug" binding:"required"`            // 活动规则维文
	RuleZh          string       `json:"rule_zh" binding:"required"`            // 活动规则中文
	GameRuleUg      string       `json:"game_rule_ug" binding:"required"`       // 游戏规则维文
	GameRuleZh      string       `json:"game_rule_zh" binding:"required"`       // 游戏规则中文
	UseAmountRuleUg string       `json:"use_amount_rule_ug" binding:"required"` // 优惠金额使用规则维文
	UseAmountRuleZh string       `json:"use_amount_rule_zh" binding:"required"` // 优惠金额使用规则中文
	StartTime       FlexibleTime `json:"start_time" binding:"required"`         // 开始时间
	EndTime         FlexibleTime `json:"end_time" binding:"required"`           // 结束时间
	StartUseTime    FlexibleTime `json:"start_use_time" binding:"required"`     // 积分开始使用时间
	EndUseTime      FlexibleTime `json:"end_use_time" binding:"required"`       // 积分结束使用时间
	State           int          `json:"state" binding:"oneof=1 0"`                                 // 状态
	Type            int          `json:"type"`                                  // 游戏类型
}

type MiniGameActivityListContentFromUserRequest struct {
	ActivityID  int    `form:"activity_id" binding:"required"` // 活动ID
	SortColumns string `form:"sort_columns"`                   // 排序字段
	CityID      int    `form:"city_id"`                        // 地区ID
	AreaID      int    `form:"area_id"`                        // 区域ID
	Search      string `form:"search"`
	Gender      *int   `form:"gender"`
	State       *int   `form:"state"`
}

type MiniGameActivityListContentFromUserResponse struct {
	ID            int    `json:"id"` // 活动ID
	UserName      string `json:"user_name"`
	UserID        int    `json:"user_id"`
	Gender        int    `json:"gender"`
	GenderName    string `json:"gender_name"`
	AreaName      string `json:"area_name"`
	AreaID        int    `json:"area_id"`
	CityName      string `json:"city_name"`
	CityID        int    `json:"city_id"`
	Mobile        string `json:"mobile"`
	Image         string `json:"image"`
	Content       string `json:"content"`
	CreatedAt     string `json:"created_at"`
	ShareCount    int    `json:"share_count"`
	FavoriteCount int    `json:"favorite_count"`
	ViewCount     int    `json:"view_count"`
	State         int    `json:"state"`
	StateName     string `json:"state_name"`
	AdminName     string `json:"admin_name"`
	AdminID       int    `json:"admin_id"`
	ReviewTime    string `json:"review_time"`
	RefuseContent string `json:"refuse_content"`
}

type MiniGameActivityOperateContentFromUserRequest struct {
	ID          int    `form:"id" binding:"required"`
	OperateType string `form:"operate_type" binding:"required,oneof=review refuse delete"`
	Content     string `form:"content"`
}
