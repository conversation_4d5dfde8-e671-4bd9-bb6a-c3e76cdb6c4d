package shipper

// 账户信息
type ShipperAccountInfo struct {
	ID                      int    `json:"id" form:"id" binding:""`                                                         // 代理区域ID
	AdminCityID             int    `json:"city_id" form:"city_id" binding:""`                                               // 代理区域ID
	AdminAreaID             int    `json:"area_id" form:"area_id" binding:""`                                               // 代理区域ID
	Avatar                  string `json:"avatar" form:"avatar" binding:"required"`                                         // 头像
	Name                    string `json:"name" form:"name" binding:"required"`                                             // 手机号
	Mobile                  string `json:"mobile" form:"mobile" binding:"required"`                                         // 手机号
	Age                  int `json:"age" form:"age" binding:"required"`                                         // 年龄
	Sex                  int `json:"sex" form:"sex" binding:"required"`                                         // 性别
	Password                string `json:"password" form:"password" binding:""`                                             // 密码
	BackOrderTimeLimit      int    `json:"back_order_time_limit" form:"back_order_time_limit" binding:""`                   // 密码
	ShipperIncomeTemplateID int    `json:"shipper_income_template_id" form:"shipper_income_template_id" binding:""` // 模板ID
	IsInsuranceBlack     string  `json:"is_insurance_black" form:"is_insurance_black" binding:""`
}

// 银行卡信息
type ShipperIDCardInfo struct {
	ShipperID       int    `form:"shipper_id" json:"shipper_id" binding:"required"`                   // 配送端ID
	SelfSignID      int    `form:"self_sign_id" json:"self_sign_id"`                                  // 入驻ID
	IDCardFront     string `form:"idcard_front_image" json:"idcard_front_image" binding:"required"`   // 身份证正面
	IDCardBack      string `form:"idcard_back_image" json:"idcard_back_image" binding:"required"`     // 身份证反面
	IDCardHandel    string `form:"idcard_handel_image" json:"idcard_handel_image" binding:"required"` // 身份证手持式
	Gender          int    `form:"gender" json:"gender" binding:"required"`                           // 1:男 2:女
	RealName        string `form:"real_name" json:"real_name" binding:"required"`                     // 实名
	IDCardNumber    string `form:"idcard_number" json:"idcard_number" binding:"required"`             // 手机号
	IDCardStartDate string `form:"idcard_start_date" json:"idcard_start_date" binding:"required"`     // 生效日期
	IDCardEndDate   string `form:"idcard_end_date" json:"idcard_end_date" binding:"required"`         // 失效日期
	IDCardAddress   string `form:"idcard_address" json:"idcard_address" binding:"required"`           // 地址
}

// 银行卡信息
type ShipperBankInfo struct {
	SelfSignID         int    `json:"self_sign_id" form:"self_sign_id"`                                      // 入驻ID
	BankCardFrontImage string `json:"bank_card_front_image" form:"bank_card_front_image" binding:"required"` // 身份证正面
	BankCardBackImage  string `json:"bank_card_back_image" form:"bank_card_back_image" binding:"required"`   // 身份证反面
	BankAcctNum        string `json:"bank_acct_num" form:"bank_acct_num" binding:"required"`                 // 1:男 2:女
	BankId             int    `json:"bank_id" form:"bank_id" binding:"required"`                             // 实名
	BankProvinceId     int    `json:"bank_province_id" form:"bank_province_id" binding:"required"`           // 手机号
	BankCityId         int    `json:"bank_city_id" form:"bank_city_id" binding:"required"`                   // 手机号
	BankAreaId         int    `json:"bank_area_id" form:"bank_area_id" binding:"required"`                   // 手机号
	BankBranchName     string `json:"bank_branch_name" form:"bank_branch_name" binding:"required"`           // 手机号
	BankBranchCode     string `json:"bank_branch_code" form:"bank_branch_code" binding:"required"`           // 手机号
	BankBindMobile     string `json:"bank_bind_mobile" form:"bank_bind_mobile" binding:"required"`           // 生效日期
	VerifyCode         string `json:"verify_code" form:"verify_code" binding:"required"`                     // 生效日期
}

// 健康证信息
type ShipperHealthInfo struct {
	SelfSignID           int    `json:"self_sign_id" form:"self_sign_id"`                                        // 入驻ID
	CertificateImage     string `json:"certificate_image" form:"certificate_image" binding:"required"`           // 身份证正面
	CertificateRealName  string `json:"certificate_real_name" form:"certificate_real_name" binding:"required"`   // 身份证反面
	CertificateNumber    string `json:"certificate_number" form:"certificate_number" binding:"required"`         // 身份证反面
	CertificateStartDate string `json:"certificate_start_date" form:"certificate_start_date" binding:"required"` // 身份证反面
	CertificateEndDate   string `json:"certificate_end_date" form:"certificate_end_date" binding:"required"`     // 身份证反面
}
