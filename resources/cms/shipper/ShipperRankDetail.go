package shipper

const (
	ShipperRankTypeBaseScore          = 1  // 基础分
	ShipperRankTypeGoodComment        = 2  // 好评
	ShipperRankTypeDiliveredOnTime    = 3  // 按时送达
	ShipperRankTypeOrderCount         = 4  // 订单总数
	ShipperRankTypeLateTime0To5       = 5  // 订单迟到5-10分钟
	ShipperRankTypeLateTime5To10      = 6  // 订单迟到10-15分钟
	ShipperRankTypeLateTime10ToMax    = 7  // 订单迟到15-20分钟
	ShipperRankTypeBadComment         = 8  // 差评
	ShipperRankTypeComplain           = 9  // 投诉
	ShipperRankTypeEarlyDelivery20Min = 10 // 订单提前20分钟送达
)

// 配送员等级详情请求
type ShipperRankDetailRequest struct {
	ShipperId int64  `form:"shipper_id" json:"shipper_id" binding:"required"` // 配送员ID
	TypeId    int64  `form:"type_id" json:"type_id"`                          // 晒选类型ID
	Date      string `form:"date" json:"date"`                                // 日期  2025-05-07
	Page      int    `form:"page" json:"page"`                                // 页码
	Limit     int    `form:"limit" json:"limit"`                              // 每页条数
}

// 配送员等级详情响应
type ShipperRankDetailResponse struct {
	ShipperId              int64                        `json:"shipper_id"`              // 配送员ID
	Mobile                 string                       `json:"mobile"`                  // 电话
	RealName               string                       `json:"real_name"`               // 真实姓名
	Avatar                 string                       `json:"avatar"`                  // 头像
	ShipperRank            string                       `json:"shipper_rank"`            // 配送员等级
	ShipperRankStartTime   string                       `json:"shipper_rank_start_time"` // 配送员等级
	ShipperRankEndTime     string                       `json:"shipper_rank_end_time"`   // 配送员等级
	ShipperScore           string                       `json:"shipper_score"`           // 配送员评价
	ShipperDynamicRank     string                       `json:"shipper_dynamic_rank"`    // 配送员等级
	ShipperDynamicScore    string                       `json:"shipper_dynamic_score"`   // 配送员评价
	ShipperScoreGrowthRate string                       `json:"shipper_score_growth_rate"`
	ShipperRankGrowthRate  string                       `json:"shipper_rank_growth_rate"`
	DataTypePlusOrMinus    []DataTypePlusOrMinus        `json:"data_type_plus_or_minus"` // 配送的订单详细
	OrderList              ShipperRankOrderListResponse `json:"order_list"`              // 配送的订单详细

}

type DataTypePlusOrMinus struct {
	Name         string                 `json:"name"`
	DataTypeList []DataTypeListResponse `json:"data_type_list"`
}

// 配送员配送的订单详细响应
type DataTypeListResponse struct {
	Id      int64   `json:"id"`
	Name    string  `json:"name"`
	Count   float64 `json:"count"`
	Percent string `json:"percent"`
	Score   string `json:"score"`
}

// 配送员配送的订单详细响应
type ShipperRankOrderListResponse struct {
	Total int64                                `json:"total"` // 总数
	List  []ShipperRankDetailOrderListResponse `json:"list"`  // 配送的订单详细
}

type ShipperRankDetailOrderListResponse struct {
	RestaurantName     string  `json:"restaurant_name"`      // 餐厅名称
	OrderNo            string  `json:"order_no"`             // 订单编号
	OrderId            int     `json:"order_Id"`             // 订单Id
	UserName           string  `json:"user_name"`            // 用户名称
	UserId             int     `json:"user_id"`              // 用户Id
	OrderTime          string  `json:"order_time"`           // 下单时间
	DeliveryTime       string  `json:"delivery_time"`        // 配送时间
	DeliveryState      int     `json:"delivery_state"`       // 配送状态
	CommentState       int     `json:"feed_back_state"`      // 评论状态
	ComplaintState     int     `json:"complaint_state"`      // 投诉状态
	DeliveryStateName  string  `json:"delivery_state_name"`  // 配送状态
	CommentStateName   string  `json:"feed_back_state_name"` // 评论状态
	ComplaintStateName string  `json:"complaint_state_name"` // 投诉状态
	ScoreObtain        float64 `json:"score_obtain"`         // 配送员获得的分数
	ScoreDeduct        float64 `json:"score_deduct"`         // 配送员等级扣分
}

// 配送员等级平均值
type ShipperRankWeekHistory struct {
	BaseScore                 float64 `json:"base_score"`
	OrderTotalCount           float64 `json:"order_total_count"`
	PositiveReviewsCount      float64 `json:"positive_reviews_count"`
	PositiveReviewsScore      float64 `json:"positive_reviews_score"`
	DeliveredOnTime           float64 `json:"delivered_on_time"`
	DeliveredOnTimeOrderCount float64 `json:"delivered_on_time_order_count"`
	MildLatenessCount         float64 `json:"mild_lateness_count"`
	MildLatenessDeduct        float64 `json:"mild_lateness_deduct"`
	ModerateLatenessCount     float64 `json:"moderate_lateness_count"`
	ModerateLatenessDeduct    float64 `json:"moderate_lateness_deduct"`
	SevereLatenessCount       float64 `json:"severe_lateness_count"`
	SevereLatenessDeduct      float64 `json:"severe_lateness_deduct"`
	NegativeReviewsCount      float64 `json:"negative_reviews_count"`
	NegativeReviewsDeduct     float64 `json:"negative_reviews_deduct"`
	ComplaintsCount           float64 `json:"complaints_count"`
	ComplaintsDeduct          float64 `json:"complaints_deduct"`
	EarlyDeliveryCount        float64 `json:"early_delivery_count"`
	EarlyDeliveryDeduct       float64 `json:"early_delivery_deduct"`
	FinalScore                float64 `json:"final_score"`
	Rank                      float64 `json:"rank"`
}
