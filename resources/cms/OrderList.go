package cms

type OrderList struct {
	RecordsFiltered int                 `json:"recordsFiltered"`
	RecordsTotal    int                 `json:"recordsTotal"`
	Draw            interface{}         `json:"draw"`
	Total           int                 `json:"total"`
	PerPage         int                 `json:"per_page"`
	CurrentPage     int                 `json:"current_page"`
	LastPage        int                 `json:"last_page"`
	From            int                 `json:"from"`
	To              int                 `json:"to"`
	OrderTotal      int                 `json:"order_total"`
	CashTotal       string              `json:"cash_total"`
	OnlinePayTotal  string              `json:"online_pay_total"`
	AgentPayTotal   string              `json:"agent_pay_total"`
	TotalMoney      string              `json:"total_money"`
	Data            []OrderListTypeItem `json:"data"`
	Status          int                 `json:"status"`
	Msg             string              `json:"msg"`
}

type OrderListTypeItem struct {
	ID                 int                    `json:"id"`
	OrderID            string                 `json:"order_id"`
	OrderType          int                    `json:"order_type"`
	TerminalNameUg     string                 `json:"terminal_name_ug"`
	TerminalNameZh     string                 `json:"terminal_name_zh"`
	UserName           string                 `json:"user_name"`
	Name               string                 `json:"name"`
	OrderAddress       string                 `json:"order_address"`
	Mobile             string                 `json:"mobile"`
	OriginMobile       string                 `json:"origin_mobile"`
	TimeOut            int                    `json:"time_out"`
	AutoPrint          int                    `json:"auto_print"`
	LastQueryTimeColor int                    `json:"last_query_time_color"`
	LastQueryTime      string                 `json:"last_query_time"`
	SerialNumber       int                    `json:"serial_number"`
	PrintedTime        string                 `json:"printed_time"`
	BookingTime        string                 `json:"booking_time"`
	DeliveryEndTime    string                 `json:"delivery_end_time"`
	OrderState         string                 `json:"order_state"`
	Shipper            string                 `json:"shipper"`
	ShipperMobile      string                 `json:"shipper_mobile"`
	CategoryID         int                    `json:"category_id"`
	Taked              int                    `json:"taked"`
	BuildingID         int                    `json:"building_id"`
	BuildingLat        float64                `json:"building_lat"`
	BuildingLng        float64                `json:"building_lng"`
	RestaurantID       int                    `json:"restaurant_id"`
	RestaurantTag      string                 `json:"restaurant_tag"`
	RestaurantName     string                 `json:"restaurant_name"`
	RestaurantLat      float64                `json:"restaurant_lat"`
	RestaurantLng      float64                `json:"restaurant_lng"`
	RestaurantTel      string                 `json:"restaurant_tel"`
	ConsumeTypeID      int                    `json:"consume_type_id"`
	RefundChanel       int                    `json:"refund_chanel"`
	State              int                    `json:"state"`
	TotalFee           int                    `json:"total_fee"`
	ConsumeType        string                 `json:"consume_type"`
	PayTypeID          int                    `json:"pay_type_id"`
	PayType            string                 `json:"pay_type"`
	Price              int                    `json:"price"`
	OriginPrice        int                    `json:"origin_price"`
	Shipment           int                    `json:"shipment"`
	LunchBoxFee        int                    `json:"lunch_box_fee"`
	Description        string                 `json:"description"`
	FoodSum            string                 `json:"food_sum"`
	CreatedAt          string                 `json:"created_at"`
	LunchBoxes         []interface{}          `json:"lunch_boxes"`
	OrderDetail        []OrderListOrderDetail `json:"order_detail"`
	FailReason         string                 `json:"fail_reason"`
	DeliveryType       int                    `json:"delivery_type"`
	JSONInfo           string                 `json:"json_info"`
	PayPlatform        int                    `json:"pay_platform"`
	ShipperID          int                    `json:"shipper_id"`
}
type OrderListOrderDetail struct {
	FoodName        string `json:"food_name"`
	Price           int    `json:"price"`
	OriginPrice     int    `json:"origin_price"`
	DiscountPercent string `json:"discount_percent"`
	Number          int    `json:"number"`
	SumPrice        int    `json:"sum_price"`
}
