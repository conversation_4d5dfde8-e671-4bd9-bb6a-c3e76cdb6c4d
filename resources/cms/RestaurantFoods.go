package cms

import "mulazim-api/resources"

// 套餐美食返回数据结构体
type RestaurantFoodsComboResponse struct {
	ID                  int     `json:"id"`                    // 自增编号
	RestaurantID        int     `json:"restaurant_id"`         // 餐厅编号
	RestaurantName        string     `json:"restaurant_name"`         // 餐厅编号
	RestaurantNameUg        string     `json:"restaurant_name_ug"`         // 餐厅编号
	RestaurantNameZh        string     `json:"restaurant_name_zh"`         // 餐厅编号
	Name                string  `json:"name"`                  // 美食编号
	NameUg              string  `json:"name_ug"`                  // 美食编号
	NameZh              string  `json:"name_zh"`                  // 美食编号
	Image               string  `json:"image"`                 // 美食图片
	OriginalImage       string  `json:"original_image"`         // 美食图片
	Description         string  `json:"description"`           // 美食说明
	DescriptionUg         string  `json:"description_ug"`           // 美食说明
	DescriptionZh         string  `json:"description_zh"`           // 美食说明
	BeginTime           string  `json:"begin_time"`            // 配送开始时间
	EndTime             string  `json:"end_time"`              // 配送结束时间
	ReadyTime           int     `json:"ready_time"`            // 准备时间，下订单到饭熟所耗的时间(单位为：分钟）
	IsDistribution      int     `json:"is_distribution"`       // 表示是否配送（0表示可配送，1表示不配送，2可配送，可自取）
	StarAvg             string  `json:"star_avg"`              // 平均星数
	CommentCount        int     `json:"comment_count"`         // 评论次数
	MonthOrderCount     int     `json:"month_order_count"`     // 月订单量
	Price               uint    `json:"price"`                 // 出口价（单位：分）
	LunchBoxID          int     `json:"lunch_box_id"`          // 饭盒编号
	LunchBoxAccommodate int     `json:"lunch_box_accommodate"` // 一个饭盒能装的美食数量
	LunchBoxFee         int     `json:"lunch_box_fee"`         // 饭盒单价（单位：分）
	FoodQuantity        float64 `json:"food_quantity"`         // 美食量
	FoodQuantityType    uint8   `json:"food_quantity_type"`    // 美食量分类；例如：1表示份、2表示个、3表示克、4表示公斤
	State               int     `json:"state"`                 // 状态（0关闭，1开通，2已售完）
	MinCount            int     `json:"min_count"`             // 最低销售限制，比如馒头，最低两个
	IsRecommend         int     `json:"is_recommend"`          // 是否推荐：0:否，1:是
	Weight              int     `json:"weight"`                // 重量
	DistributionPercent int     `json:"distribution_percent"`  // 分销比例
	YourselfTakePercent int     `json:"yourself_take_percent"` // 自取比例
	FoodsGroupID        int     `json:"foods_group_id"`        // 美食组编号
	RestaurantPrinterID int     `json:"restaurant_printer_id"` // 餐厅打印机编号
	FoodsCategoryID     []int     `json:"foods_category_id"`     // 美食分类编号
	RestaurantBeginTime string     `json:"restaurant_begin_time"`     // 餐厅营业开始时间
	RestaurantEndTime   string     `json:"restaurant_end_time"`     // 餐厅营业结束时间

	// 美食是套餐时，不能是规格美食。
	// 美食是普通美食时，可以是规格美食。
	// 套餐中的美食不能是套餐美食。
	FoodType uint8 `json:"food_type"`                                     // 美食类型：0:普通美食，1:规格美食, 2:套餐美食
	ComboFoodItems []resources.ComboItems `json:"combo_food_items"` // 套餐中的美食项
	FoodSpecTypes []resources.FoodSpecTypeRes  `json:"food_spec_types"` // 美食规格
	
	OldFoodInfo   *OldFoodsInfo  `json:"old_food_info"` // 美食规格
	Printer   []Printer  `json:"printer"` // 美食规格
	RestaurantGroup   []FoodGroup  `json:"restaurant_group"` // 美食规格
}

type Printer struct{
	ID        int     `json:"id"`
	Name        string     `json:"name"`
}
type FoodGroup struct{
	ID        int     `json:"id"`
	Name        string     `json:"name"`
}

type OldFoodsInfo struct{
	ID        int     `json:"id"`
	FoodType        int     `json:"food_type"`
	NameUg        string     `json:"name_ug"`
	NameZh        string     `json:"name_zh"`
	Image        string     `json:"image"`
	DescriptionUg        string     `json:"description_ug"`
	DescriptionZh        string     `json:"description_zh"`
	Price        int     `json:"price"`
}

type FoodSpecType struct {
	ID int `json:"id"` // 主键ID
	NameUg string `json:"name_ug"` // 名称
	NameZh string `json:"name_zh"` // 名称
	PriceType int `json:"price_type"` // 价格类型（0：浮动类型；1：覆盖类型）
	SpecOptions []FoodSpecOption `json:"spec_options"` // 规格选项
}
type FoodSpecOption struct {
	ID int `json:"id"` // 主键ID
	NameUg string `json:"name_ug"` // 名称
	NameZh string `json:"name_zh"` // 名称
	Price int `json:"price"` // 价格
	IsSelected int `json:"is_selected"` // 是否已选
}


// 套餐美食 子美食头信息 返回数据结构体
type RestaurantFoodsComboItems struct {
	ID             int                     `json:"id"`              // 主键ID
	ComboID        int                     `json:"combo_id"`        // 套餐编号
	FoodType       uint8                   `json:"food_type"`       // 美食类型 (0: 普通美食, 1: 规格美食)
	FoodID         int                     `json:"food_id"`         // 美食编号
	Count          int                     `json:"count"`           // 数量
	RestaurantFood RestaurantFoodsResponse `json:"restaurant_food"` // 餐厅美食

}

// 套餐美食 子美食（包括规格数据） 返回数据结构体
type RestaurantFoodsResponse struct {
	ID                  int                 `json:"id"`                      // 自增编号
	Name                string              `json:"name"`                    // 美食编号
	Image               string              `json:"image"`                   // 美食图片
	Description         string              `json:"description"`             // 美食说明
	BeginTime           string              `json:"begin_time"`              // 配送开始时间
	EndTime             string              `json:"end_time"`                // 配送结束时间
	ReadyTime           int                 `json:"ready_time"`              // 准备时间，下订单到饭熟所耗的时间(单位为：分钟）
	IsDistribution      int                 `json:"is_distribution"`         // 表示是否配送（0表示可配送，1表示不配送，2可配送，可自取）
	StarAvg             string              `json:"star_avg"`                // 平均星数
	CommentCount        int                 `json:"comment_count"`           // 评论次数
	MonthOrderCount     int                 `json:"month_order_count"`       // 月订单量
	Price               uint                `json:"price"`                   // 出口价（单位：分）
	LunchBoxID          int                 `json:"lunch_box_id"`            // 饭盒编号
	LunchBoxAccommodate int                 `json:"lunch_box_accommodate"`   // 一个饭盒能装的美食数量
	LunchBoxFee         int                 `json:"lunch_box_fee"`           // 饭盒单价（单位：分）
	FoodQuantity        float64             `json:"food_quantity"`           // 美食量
	FoodQuantityType    uint8               `json:"food_quantity_type"`      // 美食量分类；例如：1表示份、2表示个、3表示克、4表示公斤
	State               int                 `json:"state"`                   // 状态（0关闭，1开通，2已售完）
	MinCount            int                 `json:"min_count"`               // 最低销售限制，比如馒头，最低两个
	IsRecommend         int                 `json:"is_recommend"`            // 是否推荐：0:否，1:是
	FoodType            uint8               `json:"food_type"`               // 美食类型：0:普通美食，1:规格美食
	SelectedSpec        *RestaurantFoodSpec `json:"selected_spec,omitempty"` // 已选的规格数据
	// SpecOptions         []RestaurantFoodSpecOption `json:"spec_options"`     // 所有规格数据，不能在套餐子美食中出现
}

// 套餐美食 子美食已选规格 返回数据结构体
type RestaurantFoodSpec struct {
	ID          int                        `gorm:"column:id;primaryKey;autoIncrement" json:"id"`                                                                                                // 主键ID
	SpecOptions []RestaurantFoodSpecOption `gorm:"many2one:t_food_spec_detail;foreignKey:spec_id;joinForeignKey:spec_id;References:ID;joinReferences:foods_spec_option_id" json:"spec_options"` // 分类下面的美食列表
}

// 套餐美食 子美食规格项 返回数据结构体
type RestaurantFoodSpecOption struct {
	ID             int                    `json:"id"`               // 主键ID
	Name           string                 `json:"name_ug"`          // 名称
	IsSelected     int                    `json:"is_selected"`      // 已选状态（0：未选；1：已选）
	Price          int                    `json:"price"`            // 价格（可为空）
	SpecOptionType RestaurantFoodSpecType `json:"spec_option_type"` // 规格类型
}

// 套餐美食 规格子美食分组 返回数据结构体
type RestaurantFoodSpecType struct {
	ID        int    `json:"id"`         // 主键ID
	Name      string `json:"name"`       // 名称
	PriceType int    `json:"price_type"` // 价格类型（0：浮动类型；1：覆盖类型）
}
