package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
)

type ShipmentReduceMarketingResource struct {
	Id               int    `json:"id"`
	Name             string `json:"name"`
	RestaurantName   string `json:"restaurant_name"`
	BeginDate        string `json:"begin_date"`
	EndDate          string `json:"end_date"`
	State            int    `json:"state"`
	StateLabel       string `json:"state_label"`
	TotalOrderPrice  int    `json:"total_order_price"`
	TotalReducePrice int    `json:"total_reduce_price"`
	OrderCount       int    `json:"order_count"`
	CreatorName      string `json:"creator_name"`
	AreaName         string `json:"area_name"`
	CityName         string `json:"city_name"`
	CreatedAt        string `json:"created_at"`
}

type MarketingListResourceCollection struct {
	Items        []ShipmentReduceMarketingResource `json:"items"`
	Total        int64                             `json:"total"`
	CityId       int                               `json:"city_id"`
	AreaId       int                               `json:"area_id"`
	RestaurantId int                               `json:"restaurant_id"`
	State        int                               `json:"state"`
	BeginDate    string                            `json:"begin_date"`
	EndDate      string                            `json:"end_date"`
	Page         int                               `json:"page"`
	Limit        int                               `json:"limit"`
	Keyword      string                            `json:"keyword"`
}

// NewMarketingListResourceCollection 营销活动列表资源集合构造函数
// @param marketingList 营销活动列表 <br/>
// @param total in64 总数
// @param cityId int 城市ID
// @param areaId int 区域ID
// @param restaurantId int  餐厅ID
// @param state int 状态
// @param beginDate string 开始时间
// @param endDate string 结束时间
// @param page int 页码
// @param limit int 每页数量
// @param keyword string 关键字
func NewMarketingListResourceCollection(
	marketingList []models.MarketingStaticWithOrderLog,
	total int64,
	cityId, areaId, restaurantId, state int,
	beginDate, endDate string,
	page, limit int,
	keyword string,
	langUtil lang.LangUtil,
) *MarketingListResourceCollection {
	items := []ShipmentReduceMarketingResource{}
	for _, marketing := range marketingList {
		item := ShipmentReduceMarketingResource{
			Id:               marketing.ID,
			BeginDate:        marketing.BeginDate.Format("2006-01-02"),
			EndDate:          marketing.EndDate.Format("2006-01-02"),
			State:            marketing.State,
			StateLabel:       langUtil.T("unkown"),
			TotalOrderPrice:  marketing.TotalOrderPrice,
			TotalReducePrice: marketing.TotalReducePrice,
			OrderCount:       marketing.OrderCount,
			CreatorName:      marketing.Creator.RealName,
			CreatedAt:        marketing.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		if stateLabel, ok := langUtil.TArr("marketing_state_name")[marketing.State]; ok {
			item.StateLabel = stateLabel
		}
		if marketing.Area.ID > 0 {
			if langUtil.Lang == "zh" {
				item.AreaName = marketing.Area.NameZh
			} else {
				item.AreaName = marketing.Area.NameUg
			}
		}
		if marketing.City.ID > 0 {
			if langUtil.Lang == "zh" {
				item.CityName = marketing.City.NameZh
			} else {
				item.CityName = marketing.City.NameUg
			}
		}
		if marketing.Restaurant != nil {
			if langUtil.Lang == "zh" {
				item.RestaurantName = marketing.Restaurant.NameZh
			} else {
				item.RestaurantName = marketing.Restaurant.NameUg
			}
		}
		if langUtil.Lang == "zh" {
			item.Name = marketing.NameZh
		} else {
			item.Name = marketing.NameUg
		}
		items = append(items, item)
	}
	return &MarketingListResourceCollection{
		Items:        items,
		Total:        total,
		CityId:       cityId,
		AreaId:       areaId,
		RestaurantId: restaurantId,
		State:        state,
		BeginDate:    beginDate,
		EndDate:      endDate,
		Page:         page,
		Limit:        limit,
		Keyword:      keyword,
	}
}

func NewGroupMarketingListResourceCollection(
	marketingList []models.MarketingTemplateStaticWithOrderLog,
	total int64,
	cityId, areaId, state int,
	beginDate, endDate string,
	page, limit int,
	keyword string,
	langUtil lang.LangUtil,
) *MarketingGroupListResourceCollection {
	items := []ShipmentReduceMarketingGroupResource{}
	for _, marketing := range marketingList {
		item := ShipmentReduceMarketingGroupResource{
			Id:               marketing.ID,
			BeginDate:        marketing.BeginDate.Format("2006-01-02"),
			EndDate:          marketing.EndDate.Format("2006-01-02"),
			State:            marketing.State,
			StateLabel:       langUtil.T("unkown"),
			TotalOrderPrice:  marketing.TotalOrderPrice,
			TotalReducePrice: marketing.TotalReducePrice,
			OrderCount:       marketing.OrderCount,
			InviteCount:      marketing.InviteCount,
			AcceptedCount:    marketing.AcceptedCount,
			SendCount:        marketing.SendCount,
			// CreatorName:      marketing.Creator.RealName,
			CreatedAt: marketing.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		if marketing.Creator != nil {
			item.CreatorName = marketing.Creator.RealName
		}
		if marketing.City.ID > 0 {
			if langUtil.Lang == "zh" {
				item.CityName = marketing.City.NameZh
			} else {
				item.CityName = marketing.City.NameUg
			}
		}
		if marketing.Area.ID > 0 {
			if langUtil.Lang == "zh" {
				item.AreaName = marketing.Area.NameZh
			} else {
				item.AreaName = marketing.Area.NameUg
			}
		}
		if stateLabel, ok := langUtil.TArr("marketing_state_name")[marketing.State]; ok {
			item.StateLabel = stateLabel
		}
		if langUtil.Lang == "zh" {
			item.Name = marketing.NameZh

		} else {
			item.Name = marketing.NameUg

		}
		items = append(items, item)
	}
	return &MarketingGroupListResourceCollection{
		Items:     items,
		Total:     total,
		CityId:    cityId,
		AreaId:    areaId,
		State:     state,
		BeginDate: beginDate,
		EndDate:   endDate,
		Page:      page,
		Limit:     limit,
		Keyword:   keyword,
	}
}

type ShipmentReduceMarketingGroupResource struct {
	Id               int    `json:"id"`
	Name             string `json:"name"`
	BeginDate        string `json:"begin_date"`
	EndDate          string `json:"end_date"`
	State            int    `json:"state"`
	StateLabel       string `json:"state_label"`
	TotalOrderPrice  int    `json:"total_order_price"`
	TotalReducePrice int    `json:"total_reduce_price"`

	CityName string `json:"city_name"`

	AreaName string `json:"area_name"`

	OrderCount int `json:"order_count"`

	InviteCount   int    `json:"invite_count"`   // 邀请店铺的数量
	AcceptedCount int    `json:"accepted_count"` // 接收邀请的店铺的数量
	SendCount     int    `json:"send_count"`
	CreatorName   string `json:"creator_name"`
	CreatedAt     string `json:"created_at"`
}

type MarketingGroupListResourceCollection struct {
	Items        []ShipmentReduceMarketingGroupResource `json:"items"`
	Total        int64                                  `json:"total"`
	CityId       int                                    `json:"city_id"`
	AreaId       int                                    `json:"area_id"`
	RestaurantId int                                    `json:"restaurant_id"`
	State        int                                    `json:"state"`
	BeginDate    string                                 `json:"begin_date"`
	EndDate      string                                 `json:"end_date"`
	Page         int                                    `json:"page"`
	Limit        int                                    `json:"limit"`
	Keyword      string                                 `json:"keyword"`
}
