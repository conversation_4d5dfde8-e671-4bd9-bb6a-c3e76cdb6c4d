package shipmentReduceResource

import (
	"mulazim-api/models"
)

type ShipmentReduceStatisticsOrderLogStatisticsResource struct {
	TotalShipmentReduceOrderPrice int `json:"total_shipment_reduce_order_price"`
	TotalShipmentReduceOrderCount int `json:"total_shipment_reduce_order_count"`
	TotalOrderCount               int `json:"total_order_count"`
	TotalShipmentReducePrice      int `json:"total_shipment_reduce_price"`
	TotalShipmentReduceDealerCost int `json:"total_shipment_reduce_dealer_cost"`
	TotalShipmentReduceResCost    int `json:"total_shipment_reduce_res_cost"`
}

func NewShipmentReduceStatisticsOrderLogStatisticsResource(statistics models.MarketingOrderLogGroupAggs, totalOrderCount int64) ShipmentReduceStatisticsOrderLogStatisticsResource {
	res := ShipmentReduceStatisticsOrderLogStatisticsResource{
		TotalShipmentReduceOrderPrice: statistics.TotalOrderPrice,
		TotalShipmentReduceOrderCount: statistics.OrderCount,
		TotalOrderCount:               int(totalOrderCount),
		TotalShipmentReducePrice:      statistics.TotalReducePrice,
		TotalShipmentReduceDealerCost: statistics.TotalDealerCost,
		TotalShipmentReduceResCost:    statistics.TotalResCost,
	}
	return res
}
