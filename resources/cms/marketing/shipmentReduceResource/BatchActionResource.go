package shipmentReduceResource

type BatchActionResource struct {
	SuccessCount int      `json:"success_count"`
	FailCount    int      `json:"fail_count"`
	FailReasons  []string `json:"fail_reasons"`
}

func NewBatchActionResource(successCount, failCount int, failReasons []string) BatchActionResource {
	return BatchActionResource{
		SuccessCount: successCount,
		FailCount:    failCount,
		FailReasons:  failReasons,
	}
}
