package shipmentReduceResource

import (
	"mulazim-api/lang"
	"mulazim-api/models"
)

type TemplateStatisticsResource struct {
	ID                       int    `json:"id"`
	Name                     string `json:"name"`
	InvitedCount             int    `json:"invited_count"`
	AcceptedCount            int    `json:"accepted_count"`
	State                    int    `json:"state"`
	StateLabel               string `json:"state_label"`
	TotalOrderPrice          int    `json:"total_order_price"`
	TotalReducePrice         int    `json:"total_reduce_price"`
	TotalOrderCount          int    `json:"total_order_count"`
	TotalComplatedOrderCount int    `json:"total_complated_order_count"`
	TotalRejectedOrderCount  int    `json:"total_rejected_order_count"`
	CreatedAt                string `json:"created_at"`
	BeginDate                string `json:"begin_date"`
	EndDate                  string `json:"end_date"`
	CreatorID                int    `json:"creator_id"`
	CreatorName              string `json:"creator_name"`
}

func NewTemplateStatisticsResource(template models.MarketingGroupTemplateAggsForStatistics, langUtils lang.LangUtil) *TemplateStatisticsResource {
	res := &TemplateStatisticsResource{
		ID: template.ID,
		//Name: 			   template.Name,
		InvitedCount:             template.InviteCount,
		AcceptedCount:            template.AcceptedCount,
		State:                    template.State,
		StateLabel:               langUtils.T("unkown"),
		TotalOrderPrice:          template.TotalOrderPrice,
		TotalReducePrice:         template.TotalReducePrice,
		TotalOrderCount:          template.CompletedOrderCount + template.RejectedOrderCount,
		TotalComplatedOrderCount: template.CompletedOrderCount,
		TotalRejectedOrderCount:  template.RejectedOrderCount,
		CreatedAt:                template.CreatedAt.Format("2006-01-02 15:04:05"),
		BeginDate:                template.BeginDate.Format("2006-01-02"),
		EndDate:                  template.EndDate.Format("2006-01-02"),
		CreatorID:                template.CreatorID,
		//CreatorName: 	   template.CreatorName,
	}
	if stateLabel, ok := langUtils.TArr("template_state_name")[template.State]; ok {
		res.StateLabel = stateLabel
	}
	if template.Creator != nil {
		res.CreatorName = template.Creator.RealName
	}
	if langUtils.Lang == "zh" {
		res.Name = template.NameZh
	} else {
		res.Name = template.NameUg
	}
	return res
}

type TemplateStatisticsResourceCollection struct {
	Items        []*TemplateStatisticsResource `json:"items"`
	Total        int64                         `json:"total"`
	CityId       int                           `json:"city_id"`
	AreaId       int                           `json:"area_id"`
	RestaurantId int                           `json:"restaurant_id"`
	BeginDate    string                        `json:"begin_date"`
	EndDate      string                        `json:"end_date"`
	Page         int                           `json:"page"`
	Limit        int                           `json:"limit"`
}

func NewTemplateStatisticsResourceCollection(templates []models.MarketingGroupTemplateAggsForStatistics, total int64, cityId, areaId, restaurantId int, beginDate, endDate string, page, limit int, langUtils lang.LangUtil) *TemplateStatisticsResourceCollection {
	var items = make([]*TemplateStatisticsResource, len(templates))
	for i, template := range templates {
		items[i] = NewTemplateStatisticsResource(template, langUtils)
	}
	return &TemplateStatisticsResourceCollection{
		Items:        items,
		Total:        total,
		CityId:       cityId,
		AreaId:       areaId,
		RestaurantId: restaurantId,
		BeginDate:    beginDate,
		EndDate:      endDate,
		Page:         page,
		Limit:        limit,
	}
}
