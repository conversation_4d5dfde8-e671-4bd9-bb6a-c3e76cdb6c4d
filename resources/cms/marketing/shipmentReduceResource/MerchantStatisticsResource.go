package shipmentReduceResource

import (
	"mulazim-api/lang"
	"mulazim-api/models"
)

type MerchantStatisticsResource struct {
	ID                 int    `json:"id"`
	RestaurantName     string `json:"restaurant_name"`
	Name               string `json:"name"`
	State              int    `json:"state"`
	StateLabel         string `json:"state_label"`
	TotalOrderPrice    int    `json:"total_order_price"`
	TotalReducePrice   int    `json:"total_shipment_reduce_price"`
	TotalOrderCount    int    `json:"total_order_count"`
	ComplateOrderCount int    `json:"complate_order_count"`
	RejectedOrderCount int    `json:"rejected_order_count"`
	BeginDate          string `json:"begin_date"`
	EndDate            string `json:"end_date"`
	CreatorName        string `json:"creator_name"`
	CreatedAt          string `json:"created_at"`
}

func NewMerchantStatisticsResource(marketing models.MarketingStatisticsWithOrderLog, langUtils lang.LangUtil) MerchantStatisticsResource {
	res := MerchantStatisticsResource{
		ID:                 marketing.ID,
		State:              marketing.State,
		StateLabel:         langUtils.T("unkown"),
		TotalOrderPrice:    marketing.TotalOrderPrice,
		TotalReducePrice:   marketing.TotalReducePrice,
		TotalOrderCount:    marketing.ComplatedOrderCount + marketing.RejectedOrderCount,
		ComplateOrderCount: marketing.ComplatedOrderCount,
		RejectedOrderCount: marketing.RejectedOrderCount,
		BeginDate:          marketing.BeginDate.Format("2006-01-02"),
		EndDate:            marketing.EndDate.Format("2006-01-02"),
		CreatedAt:          marketing.CreatedAt.Format("2006-01-02 15:04:05"),
	}
	// 状态文字形式
	if stateLabel, ok := langUtils.TArr("marketing_state_name")[marketing.State]; ok {
		res.StateLabel = stateLabel
	}
	// 配置餐厅名称
	if marketing.Restaurant != nil {
		if langUtils.Lang == "zh" {
			res.RestaurantName = marketing.Restaurant.NameZh
		} else {
			res.RestaurantName = marketing.Restaurant.NameUg
		}
	}
	// 创建者
	if marketing.Creator != nil {
		res.CreatorName = marketing.Creator.RealName
	}
	// 标题
	if langUtils.Lang == "zh" {
		res.Name = marketing.NameZh
	} else {
		res.Name = marketing.NameUg
	}
	return res
}

type MerchantStatisticsResourceCollection struct {
	Items        []MerchantStatisticsResource `json:"items"`
	Total        int64                        `json:"total"`
	CityID       int                          `json:"city_id"`
	AreaID       int                          `json:"area_id"`
	RestaurantID int                          `json:"restaurant_id"`
	BeginDate    string                       `json:"begin_date"`
	EndDate      string                       `json:"end_date"`
	Page         int                          `json:"page"`
	Limit        int                          `json:"limit"`
}

func NewMerchantStatisticsResourceCollection(marketingList []models.MarketingStatisticsWithOrderLog, total int64, cityID, areaID, restaurantID int, beginDate, endDate string, page, limit int, langUtils lang.LangUtil) MerchantStatisticsResourceCollection {
	items := make([]MerchantStatisticsResource, len(marketingList))
	for i, marketing := range marketingList {
		items[i] = NewMerchantStatisticsResource(marketing, langUtils)
	}
	return MerchantStatisticsResourceCollection{
		Items:        items,
		Total:        total,
		CityID:       cityID,
		AreaID:       areaID,
		RestaurantID: restaurantID,
		BeginDate:    beginDate,
		EndDate:      endDate,
		Page:         page,
		Limit:        limit,
	}
}
