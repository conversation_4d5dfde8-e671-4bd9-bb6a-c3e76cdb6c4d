package materialResource

import (
	"mulazim-api/lang"
	"mulazim-api/models"
)

type CategoryResource struct {
	ID        int    `json:"id"`
	NameUg    string `json:"name_ug"`
	NameZh    string `json:"name_zh"`
	State     int    `json:"state"`
	CreatedAt string `json:"created_at"`
}

type CategoryListItemResource struct {
	ID        int    `json:"id"`
	Name      string `json:"name"`
	State     int    `json:"state"`
	CreatedAt string `json:"created_at"`
}
type CategoryCollection struct {
	Items []CategoryListItemResource `json:"items"`
	Total int64                      `json:"total"`
}

func NewCategoryResource(category models.AdvertMaterialCategory) CategoryResource {
	return CategoryResource{
		ID:        category.ID,
		NameZh:    category.NameZh,
		NameUg:    category.NameUg,
		State:     category.State,
		CreatedAt: category.CreatedAt.Format("2006-01-02 15:04:05"),
	}
}

func NewCategoryListItemResource(category models.AdvertMaterialCategory, lang lang.LangUtil) CategoryListItemResource {
	item := CategoryListItemResource{
		ID:        category.ID,
		Name:      category.NameZh,
		State:     category.State,
		CreatedAt: category.CreatedAt.Format("2006-01-02 15:04:05"),
	}
	if lang.Lang == "ug" {
		item.Name = category.NameUg
	}
	return item
}
func NewCategoryCollectionResource(categories []models.AdvertMaterialCategory, total int64, lang lang.LangUtil) CategoryCollection {
	var collection CategoryCollection
	collection.Items = make([]CategoryListItemResource, 0)
	for _, category := range categories {
		collection.Items = append(collection.Items, NewCategoryListItemResource(category, lang))
	}
	collection.Total = total
	return collection
}
