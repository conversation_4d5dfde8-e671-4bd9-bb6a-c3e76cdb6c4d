package materialResource

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
)

type MaterialFullResource struct {
	ID         int    `json:"id"`
	CategoryId int    `json:"category_id"`
	NameUg     string `json:"name_ug"`
	NameZh     string `json:"name_zh"`
	Cover      string `json:"cover"`
	CoverUrl   string `json:"cover_url"`
	File       string `json:"file"`
	FileUrl    string `json:"file_url"`
	State      int    `json:"state"`
	CreatedAt  string `json:"created_at"`
	UpdateAt   string `json:"update_at"`
	DeletedAt  string `json:"deleted_at"`
}

type MaterialListItemResource struct {
	ID           int    `json:"id"`
	CategoryId   int    `json:"category_id"`
	CategoryName string `json:"category_name"`
	Name         string `json:"name"`
	Cover        string `json:"cover"`
	File         string `json:"file"`
	TakedCount   int    `json:"taked_count"`
	State        int    `json:"state"`
	CreatedAt    string `json:"created_at"`
	UpdateAt     string `json:"update_at"`
	//DeletedAt    string `json:"deleted_at"`
}

type MaterialCollectionResource struct {
	Items []MaterialListItemResource `json:"items"`
	Total int64                      `json:"total"`
}

func NewMaterialResource(material models.AdvertMaterial) MaterialFullResource {
	fileUrl := ""
	if len(material.File) > 0 {
		fileUrl = tools.CdnUrl(material.File)
	}
	return MaterialFullResource{
		ID:         material.ID,
		CategoryId: material.AdvertMaterialCategoryId,
		NameUg:     material.NameUg,
		NameZh:     material.NameZh,
		CoverUrl:   tools.CdnUrl(material.Cover),
		Cover:      material.Cover,
		File:       material.File,
		FileUrl:    fileUrl,
		State:      material.State,
		CreatedAt:  material.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdateAt:   material.UpdatedAt.Format("2006-01-02 15:04:05"),
		DeletedAt:  material.DeletedAt.Time.Format("2006-01-02 15:04:05"),
	}
}

func NewMaterialListItemResource(material models.AdvertMaterialWithTakeCount, lang lang.LangUtil) MaterialListItemResource {
	var name string
	var categoryName string
	if "zh" == lang.Lang {
		name = material.NameZh
		categoryName = material.AdvertMaterialCategory.NameZh
	} else {
		name = material.NameUg
		categoryName = material.AdvertMaterialCategory.NameUg
	}
	file := ""
	if len(material.File) > 0 {
		file = tools.CdnUrl(material.File)
	}
	return MaterialListItemResource{
		ID:           material.ID,
		CategoryId:   material.AdvertMaterialCategoryId,
		CategoryName: categoryName,
		Name:         name,
		Cover:        tools.CdnUrl(material.Cover),
		File:         file,
		State:        material.State,
		TakedCount:   material.TakedCount,
		CreatedAt:    material.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdateAt:     material.UpdatedAt.Format("2006-01-02 15:04:05"),
		//DeletedAt:  material.DeletedAt.Time.Format("2006-01-02 15:04:05"),
	}
}

func NewMaterialCollectionResource(materials []models.AdvertMaterialWithTakeCount, total int64, lang lang.LangUtil) MaterialCollectionResource {
	var items []MaterialListItemResource
	for _, material := range materials {
		items = append(items, NewMaterialListItemResource(material, lang))
	}
	return MaterialCollectionResource{
		Items: items,
		Total: total,
	}
}
