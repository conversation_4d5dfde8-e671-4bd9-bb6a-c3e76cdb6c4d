package materialResource

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
)

type TakeListResource struct {
	ID            int    `json:"id"`
	CategoryID    int    `json:"category_id"`
	CategoryName  string `json:"category_name"`
	MaterialID    int    `json:"material_id"`
	MaterialName  string `json:"material_name"`
	MaterialCover string `json:"material_cover"`
	ShipperID     int    `json:"shipper_id"`
	ShipperName   string `json:"shipper_name"`
	TakedCount    int    `json:"taked_count"`
	TakedAt       string `json:"taked_at"`
	StartCode     string `json:"start_code"`
	EndCode       string `json:"end_code"`
}

type TakeListCollectionResource struct {
	Items []*TakeListResource `json:"items"`
	Total int64               `json:"total"`
	Page  int                 `json:"page"`
	Limit int                 `json:"limit"`
}

type TakeDetailResource struct {
	TakeListResource
	TotalUserCount int `json:"total_user_count"`
	TotalReward    int `json:"total_reward"`
}

func NewTakeListResource(take models.AdvertMaterialTake, langUtil lang.LangUtil) *TakeListResource {
	categoryName := take.Category.NameUg
	if langUtil.Lang == "zh" {
		categoryName = take.Category.NameZh
	}
	materialName := take.AdvertMaterial.NameUg
	if langUtil.Lang == "zh" {
		materialName = take.AdvertMaterial.NameZh
	}
	return &TakeListResource{
		ID:            take.ID,
		CategoryID:    take.AdvertMaterialCategoryId,
		CategoryName:  categoryName,
		MaterialID:    take.AdvertMaterialId,
		MaterialName:  materialName,
		MaterialCover: tools.CdnUrl(take.AdvertMaterial.Cover),
		ShipperID:     take.ShipperId,
		ShipperName:   take.Shipper.RealName,
		TakedCount:    take.TakedCount,
		TakedAt:       take.TakedAt.Format("2006-01-02 15:04:05"),
		StartCode:     take.StartCode,
		EndCode:       take.EndCode,
	}
}

func NewTakeListCollectionResource(takes []models.AdvertMaterialTake, total int64, page int, limit int, langUtil lang.LangUtil) *TakeListCollectionResource {
	var data []*TakeListResource = make([]*TakeListResource, 0)
	for _, take := range takes {
		data = append(data, NewTakeListResource(take, langUtil))
	}
	return &TakeListCollectionResource{
		Items: data,
		Total: total,
		Page:  page,
		Limit: limit,
	}
}

// NewTakeDetailResource 创建详情资源
func NewTakeDetailResource(take models.AdvertMaterialTake, totalReward, totalUserCount int, langUtil lang.LangUtil) *TakeDetailResource {
	return &TakeDetailResource{
		TakeListResource: *NewTakeListResource(take, langUtil),
		TotalReward:      totalReward,
		TotalUserCount:   totalUserCount,
	}
}
