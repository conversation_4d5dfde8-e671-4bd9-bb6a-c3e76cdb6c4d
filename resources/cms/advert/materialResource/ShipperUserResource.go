package materialResource

import "mulazim-api/models"

type ShipperUserResource struct {
	ID        int    `json:"id"`
	Avatar    string `json:"avatar"`
	RealName  string `json:"real_name"`
	Reward    int    `json:"reward"`
	Qrcode    string `json:"qrcode"`
	CreatedAt string `json:"created_at"`
}

type ShipperUserCollectionResource struct {
	Items []ShipperUserResource `json:"items"`
	Total int64                 `json:"total"`
	Page  int                   `json:"page"`
	Limit int                   `json:"limit"`
}

// NewShipperUserResource 创建
func NewShipperUserResource(shipperUser models.AdvertMaterialShipperUser) ShipperUserResource {
	return ShipperUserResource{
		ID:        shipperUser.ID,
		Avatar:    shipperUser.User.Avatar,
		RealName:  shipperUser.User.Name,
		Reward:    shipperUser.Reward,
		Qrcode:    shipperUser.AdvertMaterialCode.Code,
		CreatedAt: shipperUser.CreatedAt.Format("2006-01-02 15:04:05"),
	}
}

// NewShipperUserCollectionResource 创建
func NewShipperUserCollectionResource(items []models.AdvertMaterialShipperUser, total int64, page int, limit int) ShipperUserCollectionResource {
	var resources []ShipperUserResource = make([]ShipperUserResource, 0)
	for _, item := range items {
		resources = append(resources, NewShipperUserResource(item))
	}
	return ShipperUserCollectionResource{
		Items: resources,
		Total: total,
		Page:  page,
		Limit: limit,
	}
}
