package materialResource

import (
	"mulazim-api/lang"
	"mulazim-api/models"
)

type PrintBatchListItemResource struct {
	ID         int    `json:"id"`
	BatchNo    int    `json:"batch_no"`
	Count      int    `json:"count"`
	State      int    `json:"state"`
	StateLabel string `json:"state_label"`
	StartCode  string `json:"start_code"`
	EndCode    string `json:"end_code"`
	CreatedAt  string `json:"created_at"`
}

type PrintBatchListResource struct {
	Items []PrintBatchListItemResource `json:"items"`
	Total int64                        `json:"total"`
}

func NewPrintBatchListItemResource(printBatch models.AdvertMaterialPrintBatch, langUtil lang.LangUtil) PrintBatchListItemResource {
	stateLabel := langUtil.T("unknown")
	if v, ok := langUtil.TArr("advert_material_print_batch_states")[printBatch.State]; ok {
		stateLabel = v
	}
	return PrintBatchListItemResource{
		ID:         printBatch.ID,
		BatchNo:    printBatch.BatchNo,
		Count:      printBatch.Count,
		State:      printBatch.State,
		StateLabel: stateLabel,
		StartCode:  printBatch.StartCode,
		EndCode:    printBatch.EndCode,
		CreatedAt:  printBatch.CreatedAt.Format("2006-01-02 15:04:05"),
	}
}

func NewPrintBatchListResource(printBatches []models.AdvertMaterialPrintBatch, total int64, langUtil lang.LangUtil) PrintBatchListResource {
	items := []PrintBatchListItemResource{}
	for _, printBatch := range printBatches {
		items = append(items, NewPrintBatchListItemResource(printBatch, langUtil))
	}
	return PrintBatchListResource{
		Items: items,
		Total: total,
	}
}
