package materialResource

import (
	"mulazim-api/lang"
	"mulazim-api/models"
)

type ShipperUserMonthlyStatisticListItemResource struct {
	UserName        string `json:"user_name"`
	UserAvatar      string `json:"user_avatar"`
	UserFrom        string `json:"user_from"`
	CategoryID      int64  `json:"category_id"`
	CategoryName    string `json:"category_name"`
	MaterialName    string `json:"material_name"`
	MaterialCode    string `json:"material_code"`
	OrderCount      int    `json:"order_count"`
	TotalOrderPrice int    `json:"total_order_price"`
	InviteUserFee   int    `json:"invite_user_fee"`
	TotalRewardFee  int    `json:"total_reward_fee"`
	IsNewUser  int    `json:"is_new_user"`
	InvitedAt       string `json:"invited_at"`
}

type ShipperUserMonthlyStatisticListResource struct {
	Items      []*ShipperUserMonthlyStatisticListItemResource `json:"items"`
	Total      int64                                          `json:"total"`
	Page       int                                            `json:"page"`
	Limit      int                                            `json:"limit"`
	ShipperID  int                                            `json:"shipper_id"`
	CategoryID int                                            `json:"category_id"`
}

func NewShipperUserMonthlyStatisticListItemResource(statistic models.AdvertMaterialShipperUserMonthStatistic, langUtil lang.LangUtil) *ShipperUserMonthlyStatisticListItemResource {
	userFrom := langUtil.T("advert_material")
	categoryName := statistic.AdvertMaterialCategory.NameZh
	materialName := statistic.AdvertMaterialShipperUser.AdvertMaterial.NameZh
	materialCode := ""
	if langUtil.Lang != "zh" {
		categoryName = statistic.AdvertMaterialCategory.NameUg
		materialName = statistic.AdvertMaterialShipperUser.AdvertMaterial.NameUg
	}
	if statistic.AdvertMaterialCategoryId == 1 {
		userFrom = langUtil.T("advert_code")
	}

	if statistic.AdvertMaterialShipperUser.AdvertMaterialCodeId > 0 {
		materialCode = statistic.AdvertMaterialShipperUser.AdvertMaterialCode.Code

	}
	return &ShipperUserMonthlyStatisticListItemResource{
		UserName:        statistic.User.Name,
		UserAvatar:      statistic.User.Avatar,
		UserFrom:        userFrom,
		CategoryName:    categoryName,
		CategoryID:      statistic.AdvertMaterialCategoryId,
		MaterialName:    materialName,
		MaterialCode:    materialCode,
		OrderCount:      int(statistic.OrderCount),
		TotalOrderPrice: int(statistic.TotalOrderPrice),
		InviteUserFee:   int(statistic.InviteUserFee),
		TotalRewardFee:  int(statistic.TotalOrderTipsFee),
		IsNewUser:  int(statistic.AdvertMaterialShipperUser.IsNewUser),
		InvitedAt:       statistic.AdvertMaterialShipperUser.CreatedAt.Format("2006-01-02 15:04:05"),
	}
}

func NewShipperUserMonthlyStatisticListResource(statistics []models.AdvertMaterialShipperUserMonthStatistic, total int64, page, limit, shipperID, categoryID int, langUtil lang.LangUtil) *ShipperUserMonthlyStatisticListResource {
	items := make([]*ShipperUserMonthlyStatisticListItemResource, 0)
	for _, statistic := range statistics {
		items = append(items, NewShipperUserMonthlyStatisticListItemResource(statistic, langUtil))
	}
	return &ShipperUserMonthlyStatisticListResource{
		Items:      items,
		Total:      total,
		Page:       page,
		Limit:      limit,
		ShipperID:  shipperID,
		CategoryID: categoryID,
	}
}
