package cms

import (
	"encoding/json"
	"mulazim-api/models"
)

type ShipmentReduceTemplateDetailForUpdateResource struct {
	ID               int                      `json:"id"`
	NameZh           string                   `json:"name_zh"`
	NameUg           string                   `json:"name_ug"`
	BeginDate        string                   `json:"begin_date"`
	EndDate          string                   `json:"end_date"`
	FullWeekState    int                      `json:"full_week_state"`
	Day              int                      `json:"day"`
	FullTimeState    int                      `json:"full_time_state"`
	AutoContinue     int                      `json:"auto_continue"`
	MinDeliveryPrice int                      `json:"min_delivery_price"`
	Steps            []ShipmentReduceStep     `json:"steps"`
	Timelines        []ShipmentReduceTimeline `json:"timelines"`
	CreatedAt        string                   `json:"created_at"`
	UpdatedAt        string                   `json:"updated_at"`
}

func NewShipmentReduceTemplateDetailForUpdateResource(group models.MarketingGroupTemplate) ShipmentReduceTemplateDetailForUpdateResource {
	var steps = make([]ShipmentReduceStep, 0)
	var timelines = make([]ShipmentReduceTimeline, 0)

	json.Unmarshal([]byte(group.Steps), &steps)
	response := ShipmentReduceTemplateDetailForUpdateResource{
		ID:               group.ID,
		NameZh:           group.NameZh,
		NameUg:           group.NameUg,
		BeginDate:        group.BeginDate.Format("2006-01-02"),
		EndDate:          group.EndDate.Format("2006-01-02"),
		FullWeekState:    group.FullWeekState,
		Day:              group.Day,
		FullTimeState:    group.FullTimeState,
		AutoContinue:     group.AutoContinue,
		MinDeliveryPrice: group.MinDeliveryPrice,
		Steps:            steps,
		Timelines:        timelines,
		CreatedAt:        group.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:        group.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	if group.Time1Start != "" && group.Time1End != "" {
		response.Timelines = append(response.Timelines, ShipmentReduceTimeline{
			Start: group.Time1Start[:5],
			End:   group.Time1End[:5],
		})
	}
	if group.Time2Start != "" && group.Time2End != "" {
		response.Timelines = append(response.Timelines, ShipmentReduceTimeline{
			Start: group.Time2Start[:5],
			End:   group.Time2End[:5],
		})
	}
	if group.Time3Start != "" && group.Time3End != "" {
		response.Timelines = append(response.Timelines, ShipmentReduceTimeline{
			Start: group.Time3Start[:5],
			End:   group.Time3End[:5],
		})
	}
	return response
}
