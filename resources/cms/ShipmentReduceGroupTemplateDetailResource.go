package cms

import (
	"encoding/json"
	"mulazim-api/lang"
	"mulazim-api/models"
)

type ShipmentReduceGroupTemplateDetailResource struct {
	ID               int                      `json:"id"`
	Name             string                   `json:"name"`
	NameUg             string                   `json:"name_ug"`
	NameZh             string                   `json:"name_zh"`
	BeginDate        string                   `json:"begin_date"`
	EndDate          string                   `json:"end_date"`
	FullWeekState    int                      `json:"full_week_state"`
	Day              int                      `json:"day"`
	FullTimeState    int                      `json:"full_time_state"`
	AutoContinue     int                      `json:"auto_continue"`
	MinDeliveryPrice int                      `json:"min_delivery_price"`
	Steps            []ShipmentReduceStep     `json:"steps"`
	Timelines        []ShipmentReduceTimeline `json:"timelines"`
	CreatorID        int                      `json:"creator_id"`
	CreatorName      string                   `json:"creator_name,omitempty"`
	State            int                      `json:"state"`
	CreatedAt        string                   `json:"created_at"`
	UpdatedAt        string                   `json:"updated_at"`
	TotalOrderPrice  int                      `json:"total_order_price"`  // 订单总金额（单位 分）
	OrderCount       int                      `json:"order_count"`        // 订单总数
	TotalReducePrice int                      `json:"total_reduce_price"` // 优惠金额（单位 分）
	TotalDealerCost  int                      `json:"total_dealer_cost"`  // 代理承担部分（单位 分）
	TotalResCost     int                      `json:"total_res_cost"`     // 商家承担部分（单位 分）
	InviteCount      int                      `json:"invite_count"`       // 邀请店铺的数量
	AcceptedCount    int                      `json:"accepted_count"`     // 接收邀请的店铺的数量

}

func NewShipmentReduceGroupTemplateDetailResource(template models.MarketingGroupTemplateAggs, langUtils lang.LangUtil) ShipmentReduceGroupTemplateDetailResource {
	response := ShipmentReduceGroupTemplateDetailResource{
		ID:               template.ID,
		BeginDate:        template.BeginDate.Format("2006-01-02"),
		EndDate:          template.EndDate.Format("2006-01-02"),
		FullWeekState:    template.FullWeekState,
		Day:              template.Day,
		FullTimeState:    template.FullTimeState,
		AutoContinue:     template.AutoContinue,
		MinDeliveryPrice: template.MinDeliveryPrice,
		CreatorID:        template.CreatorID,
		CreatorName:      template.Creator.RealName,
		State:            template.State,
		CreatedAt:        template.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:        template.UpdatedAt.Format("2006-01-02 15:04:05"),
		TotalOrderPrice:  template.TotalOrderPrice,
		OrderCount:       template.OrderCount,
		TotalReducePrice: template.TotalReducePrice,
		TotalDealerCost:  template.TotalDealerCost,
		TotalResCost:     template.TotalResCost,
		InviteCount:      template.InviteCount,
		AcceptedCount:    template.AcceptedCount,
	}
	if langUtils.Lang == "zh" {
		response.Name = template.NameZh
	} else {
		response.Name = template.NameUg
	}
	response.NameUg = template.NameUg
	response.NameZh = template.NameZh
	
	response.Steps = make([]ShipmentReduceStep, 0)
	json.Unmarshal([]byte(template.Steps), &response.Steps)
	response.Timelines = make([]ShipmentReduceTimeline, 0)
	if template.Time1Start != "" && template.Time1End != "" {
		response.Timelines = append(response.Timelines, ShipmentReduceTimeline{
			Start: template.Time1Start[:5],
			End:   template.Time1End[:5],
		})
	}
	if template.Time2Start != "" && template.Time2End != "" {
		response.Timelines = append(response.Timelines, ShipmentReduceTimeline{
			Start: template.Time2Start[:5],
			End:   template.Time2End[:5],
		})
	}
	if template.Time3Start != "" && template.Time3End != "" {
		response.Timelines = append(response.Timelines, ShipmentReduceTimeline{
			Start: template.Time3Start[:5],
			End:   template.Time3End[:5],
		})
	}
	return response
}
