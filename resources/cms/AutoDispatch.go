﻿package cms

type AutoDispatchParam struct {
	ID                                                  int     `form:"id" json:"id" binding:"required"`                                                                                                                  // 区域ID
	AutoDispatchState                                   int     `form:"auto_dispatch_state" json:"auto_dispatch_state" binding:"required,oneof=1 2"`                                                                      // 是否开启智能派单：1:开启，2:关闭
	AutoDispatchWait                                    int     `form:"auto_dispatch_wait" json:"auto_dispatch_wait" binding:"required,min=3"`                                                                            // 压单分钟数
	AutoDispatchParallelDistance                        int     `form:"auto_dispatch_parallel_distance" json:"auto_dispatch_parallel_distance" binding:"required"`                                                        // 同方向单子最小平行距离，默认 500米
	AutoDispatchPeakState                               int     `form:"auto_dispatch_peak_state" json:"auto_dispatch_peak_state" binding:"required,oneof=1 2"`                                                            // 1:高峰期，2:均匀分配
	AutoDispatchPeakTime                                string  `form:"auto_dispatch_peak_time" json:"auto_dispatch_peak_time" binding:""`                                                                                // 高峰期时间端
	AutoDispatchDeliveryTime                            int     `form:"auto_dispatch_delivery_time" json:"auto_dispatch_delivery_time" binding:"" default:"45"`                                                           // 最大配送时间(分钟)
	AutoDispatchTakeFoodTime                            int     `form:"auto_dispatch_take_food_time" json:"auto_dispatch_take_food_time" binding:"" default:"0"`                                                          // 取餐时间(分钟)
	AutoDispatchShipperSendFoodCustomerTime             int     `form:"auto_dispatch_shipper_send_food_customer_time" json:"auto_dispatch_shipper_send_food_customer_time" binding:"" default:"0"`                        // 配送员到达客户区域到送到门口时间(分钟)
	AutoDispatchShipperToRestaurantDistance             float64 `form:"auto_dispatch_shipper_to_restaurant_distance" json:"auto_dispatch_shipper_to_restaurant_distance" binding:"required"`                              // 配送员到餐厅最大距离（km）
	AutoDispatchSpecialOrderPeakState                   int     `form:"auto_dispatch_special_order_peak_state" json:"auto_dispatch_special_order_peak_state" binding:"required"`                                          // 配送员到餐厅最大距离（km）
	AutoDispatchNotTakeOrderThresholdTime               int     `form:"auto_dispatch_not_take_order_threshold_time" json:"auto_dispatch_not_take_order_threshold_time" binding:""`                                        // 未接订单时间阈值
	AutoDispatchSameBuildingRestaurantSeckillOrder      *int    `form:"auto_dispatch_same_building_restaurant_seckill_order" json:"auto_dispatch_same_building_restaurant_seckill_order" binding:"oneof=0 1"`             // 智能派单秒杀订单同一个餐厅和建筑分配一个配送员配置
	AutoDispatchSameBuildingRestaurantSpecialPriceOrder *int    `form:"auto_dispatch_same_building_restaurant_special_price_order" json:"auto_dispatch_same_building_restaurant_special_price_order" binding:"oneof=0 1"` // 智能派单特价活动订单同一个餐厅和建筑分配一个配送员配置
	AutoDispatchSameBuildingRestaurantNormalOrder       *int    `form:"auto_dispatch_same_building_restaurant_normal_order" json:"auto_dispatch_same_building_restaurant_normal_order" binding:"oneof=0 1"`               // 智能派单普通订单同一个餐厅和建筑分配一个配送员配置
	AutoDispatchReturnOrderState                        *int    `form:"auto_dispatch_return_order_state" json:"auto_dispatch_return_order_state" binding:"oneof=0 1"`
	AutoDispatchReturnOrderDistance                     int     `form:"auto_dispatch_return_order_distance" json:"auto_dispatch_return_order_distance" binding:""`
	AutoDispatchEnableRank                              int     `form:"auto_dispatch_enable_rank" json:"auto_dispatch_enable_rank" binding:"oneof=0 1"`
}
