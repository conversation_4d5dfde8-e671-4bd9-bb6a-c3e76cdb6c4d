package lottery

type LotteryActivityListData struct {
	Items []LotteryActivityListItem `json:"items"`
	Total int64 `json:"total"`
}

type LotteryActivityListItem struct {
	Id int `json:"id"`
	Name string `json:"name"`
	ActivityTime string `json:"start_time"`
	Position string `json:"show_position"`
	CreatedAt string `json:"created_at"`
	State int `json:"state"`
	AdminName string `json:"admin_name"`
}



type LotterySetListData struct {
	Items []LotterySetListItem `json:"items"`
	Total int64 `json:"total"`
}

type LotterySetListItem struct {
	Id           int    `json:"id"`
	Level        int    `json:"level"`
	PrizeCount   int    `json:"prize_count"`
	ActivityName string `json:"activity_name"`
	PrizeName    string `json:"prize_name"`
	Range        string `json:"range"`
	CreatedAt    string `json:"created_at"`
	State        int    `json:"state"`
	AdminName    string `json:"admin_name"`
	CurrentIndex int   `json:"current_index"`
}



type LotteryWinnersItem struct {
	Id           int    `json:"id"`
	ActivityId   int    `json:"activity_id"`
	CityName     string    `json:"city_name"`
	AreaName     string    `json:"area_name"`
	UserName     string    `json:"user_name"`
	UserPhone    string    `json:"user_phone"`
	PrizeName    string    `json:"prize_name"`
	PrizeID      *int           `json:"prize_id"`
	State        int    `json:"state"`
	StateName    string    `json:"state_name"`
	Level        string    `json:"level"`
	DrawIndex     int    `json:"draw_index"`
	AllChanceCount int `json:"all_chance_count"`
	UsedChanceCount int `json:"used_chance_count"`
	TypeName string `json:"type_name"`
	OrderNo      string `json:"order_no"`
	OrderPrice   int    `json:"order_price"`
	OrderTime   string `json:"order_time"`
	CreatedAt   string `json:"created_at"`
	PrizeOpenTime   string `json:"prize_open_time"`
	Tp 			int 	`json:"type"`

}



type LotteryWinnersItemOne struct {
	Id           int    `json:"id"`
	ActivityId   int    `json:"activity_id"`
	CityName     string    `json:"city_name"`
	AreaName     string    `json:"area_name"`
	UserName     string    `json:"user_name"`
	UserPhone    string    `json:"user_phone"`
	PrizeName    string    `json:"prize_name"`
	State        int    `json:"state"`
	StateName    string    `json:"state_name"`
	Level        string    `json:"level"`
	PrizeOpenTime string    `json:"prize_open_time"`
	PrizeImg     string    `json:"prize_img"`
	PrizePrice   int    `json:"prize_price"`
	BuildingId   int `json:"building_id"`
 	UserAddress  string `json:"user_address"`
	UserAddressName  string `json:"user_address_name"`
	UserAddressTel  string `json:"user_address_tel"`
	CouponPrice   int `json:"coupon_price"`
}

type LotteryCommentWithUserInfo struct {
	ID                uint   `json:"id"`                  // 主键ID
	CityID            *int   `json:"city_id"`             // 城市ID
	AreaID            *int   `json:"area_id"`             // 地区ID
	LotteryActivityID *int `json:"lottery_activity_id"`   // 抽奖活动ID
	UserID            int  `json:"user_id"`               // 用户ID
	Type              *int   `json:"type"`                // 类型：1. 喜欢 2. 不喜欢
	Content           string `json:"content"`             // 评论内容
	CreatedAt         string `json:"created_at"`

	UserName   string `json:"user_name"`
	UserMobile string `json:"user_mobile"`
}

type LotteryCommentResponse struct {
	Items []LotteryCommentWithUserInfo `json:"items"`
	Total int64            `json:"total"`
}

// 导入 Excel 后，获取奖品配置数据的结构
type LotteryActivityExcelPrizeResposne struct {
	PrizeID        uint                 `json:"prize_id"`
	PrizeData      LotteryPrizeResponse `json:"prize_data"`
	OrderIndexList []int                `json:"order_index_list"`
}