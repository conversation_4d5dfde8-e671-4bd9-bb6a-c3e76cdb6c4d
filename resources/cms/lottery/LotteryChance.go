package lottery

// LotteryChancesResponse
type LotteryChanceResponse struct {
	// 关键 ID
	ID                         int `json:"id"`
	CityId                     int `json:"city_id"`
	AreaId                     int `json:"area_id"`
	BuildingId int `json:"building_id"`

	// 辅助数据 - 可以增删
	LotteryActivityID int `json:"lottery_activity_id"`
	//LotteryActivityName    string `json:"lottery_activity_name"`
	Type                   int    `json:"type"`
	TypeID                 int    `json:"type_id"`
	UserID                 int    `json:"user_id"`
	State                  int    `json:"state"`
	LotteryActivityLevelID int    `json:"lottery_activity_level_id"`
	PrizeOpenTime          string `json:"prize_open_time"`
	PrizeOpenState         *int   `json:"prize_open_state"`
	PrizeID                *int   `json:"prize_id"`

	// 页面需要的数据 - 不能修改
	DrawIndex         *int   `json:"draw_index"`
	LotteryPrizeName  string `json:"lottery_prize_name"`
	LotteryPrizePrice int    `json:"lottery_prize_price"`
	UserOrderPrice    int    `json:"user_order_price"`
	OrderPrice        uint   `json:"order_price"`
	OrderID           string `json:"lottery_order_id"`
	CityName          string `json:"city_name"`
	BuildingAddress   string `json:"building_address"`
	AreaName          string `json:"area_name"`
	UserName          string `json:"user_name"`
	UserMobile        string `json:"user_mobile"`
	OrderTime         string `json:"lottery_order_time"`
}
