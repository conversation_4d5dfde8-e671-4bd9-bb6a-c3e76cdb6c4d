package lottery

// LotteryPrizeResponse represents the lottery prize table in the database.
type LotteryPrizeResponse struct {
	ID        uint   `json:"id"`
	TitleImg  string `json:"title_img"`  // 封面图片 维吾尔语
	SwiperImg string `json:"swiper_img"` // 轮播图片 维吾尔语
	Name      string `json:"name"`       // 名称维吾尔语
	Model     string `json:"model"`      // 型号
	Price     int    `json:"price"`      // 价格
	State     int    `json:"state"`      // 状态 0:未开启  1:开启
	AdminID   int    `json:"admin_id"`   // 创建人
	CreatedAt string `json:"created_at"` // 创建时间
}
