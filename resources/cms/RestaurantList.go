package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
)

type RestaurantList struct {
	ID               int    `json:"id"`
	RestaurantNameUg string `json:"restaurant_name_ug"`
	RestaurantNameZh string `json:"restaurant_name_zh"`
}

type RestaurantListForMarketingSearch struct {
	ID         int    `json:"id"`
	Name       string `json:"name"`
	Logo       string `json:"logo"`
	Tel        string `json:"tel"`
	State      int    `json:"state"`
	StateLabel string `json:"state_label"`
	Type       int    `json:"type"`
	TypeLabel  string `json:"type_label"`
	BeginTime  string `json:"begin_time"`
	EndTime  string `json:"end_time"`
}

type RestaurantListForMarketingSearchPaginate struct {
	Items []RestaurantListForMarketingSearch `json:"items"`
	Total int64                              `json:"total"`
	Page  int                                `json:"page"`
	Limit int                                `json:"limit"`
}

func NewRestaurantListForMarketingSearch(restaurant models.Restaurant, langUtil lang.LangUtil) RestaurantListForMarketingSearch {
	response := RestaurantListForMarketingSearch{
		ID:         restaurant.ID,
		Logo:       tools.AddCdn(restaurant.Logo),
		Tel:        restaurant.AdminTel,
		State:      restaurant.State,
		StateLabel: langUtil.T("unkown"),
		Type:       restaurant.Type,
		TypeLabel:  langUtil.T("unkown"),
		BeginTime:  restaurant.BeginTime,
		EndTime:    restaurant.EndTime,
	}

	if langUtil.Lang == "zh" {
		response.Name = restaurant.NameZh
	} else {
		response.Name = restaurant.NameUg

	}
	if stateLabel, ok := langUtil.TArr("restaurant_states")[restaurant.State]; ok {
		response.StateLabel = stateLabel
	}
	if typeLabel, ok := langUtil.TArr("restaurant_types")[restaurant.Type]; ok {
		response.TypeLabel = typeLabel
	}
	return response
}
func NewRestaurantListForMarketingSearchCollection(restaurants []models.Restaurant, langUtil lang.LangUtil) []RestaurantListForMarketingSearch {
	var response []RestaurantListForMarketingSearch
	for _, restaurant := range restaurants {
		response = append(response, NewRestaurantListForMarketingSearch(restaurant, langUtil))
	}
	return response
}

func NewRestaurantListForMarketingSearchResponse(items []RestaurantListForMarketingSearch, total int64, page int, limit int) RestaurantListForMarketingSearchPaginate {
	return RestaurantListForMarketingSearchPaginate{
		Items: items,
		Total: total,
		Page:  page,
		Limit: limit,
	}
}
