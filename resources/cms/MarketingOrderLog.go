package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/requests/marketingRequest"
)

type MarketingOrderLog struct {
	ID                    int    `json:"id"`
	OrderID               int    `json:"order_id"`
	OrderNo               string `json:"order_no"`
	ReceiverName          string `json:"receiver_name"`
	ReceiverPhone         string `json:"receiver_phone"`
	OrderAddress          string `json:"order_address"`
	OrderPrice            int    `json:"order_price"`
	OriginalShipmentPrice int    `json:"original_shipment_price"`
	ReductionFee          int    `json:"reduction_fee"`
	State                 int    `json:"state"`
	StateLabel            string `json:"state_label"`
	ShipperName           string `json:"shipper_name"`
	RestaurantName        string `json:"restaurant_name"`
	CreatedAt             string `json:"created_at"`
}

type MarkeingOrderLogPaginateResource struct {
	Items     []MarketingOrderLog `json:"items"`
	Total     int                 `json:"total"`
	Page      int                 `json:"page"`
	Limit     int                 `json:"limit"`
	Keyword   string              `json:"keyword"`
	BeginDate string              `json:"begin_date"`
	EndDate   string              `json:"end_date"`
}

func NewMarketingOrderLogResource(marketingOrderLog models.MarketingOrderLog, lang lang.LangUtil) MarketingOrderLog {

	log := MarketingOrderLog{
		ID:                    marketingOrderLog.ID,
		OrderID:               marketingOrderLog.OrderID,
		OrderNo:               marketingOrderLog.OrderNo,
		OrderPrice:            marketingOrderLog.OrderPrice,
		OriginalShipmentPrice: marketingOrderLog.OriginalShipment,
		ReductionFee:          marketingOrderLog.ReductionFee,
	}
	if marketingOrderLog.Restaurant != nil {
		if lang.Lang == "zh" {
			log.RestaurantName = marketingOrderLog.Restaurant.NameZh
		} else {
			log.RestaurantName = marketingOrderLog.Restaurant.NameUg
		}
	}
	if marketingOrderLog.Order != nil {
		log.ReceiverName = marketingOrderLog.Order.Name
		log.ReceiverPhone = marketingOrderLog.Order.Mobile
		log.OrderAddress = marketingOrderLog.Order.OrderAddress
		log.State = marketingOrderLog.Order.State
		log.StateLabel = lang.TArr("order_state")[marketingOrderLog.Order.State]
		log.CreatedAt = marketingOrderLog.Order.CreatedAt.Format("2006-01-02 15:04:05")
		if marketingOrderLog.Order.Shipper.ID != 0 {
			log.ShipperName = marketingOrderLog.Order.Shipper.RealName
		}
	}

	if marketingOrderLog.OrderToday != nil {
		log.ReceiverName = marketingOrderLog.OrderToday.Name
		log.ReceiverPhone = marketingOrderLog.OrderToday.Mobile
		log.OrderAddress = marketingOrderLog.OrderToday.OrderAddress
		log.State = marketingOrderLog.OrderToday.State
		log.StateLabel = lang.TArr("order_state")[marketingOrderLog.OrderToday.State]
		log.CreatedAt = marketingOrderLog.OrderToday.CreatedAt.Format("2006-01-02 15:04:05")
		if marketingOrderLog.OrderToday.Shipper.ID != 0 {
			log.ShipperName = marketingOrderLog.OrderToday.Shipper.RealName
		}
	}
	return log
}

func NewMarketingOrderLogListResourceCollection(marketingOrderLogs []models.MarketingOrderLog, pageInfo marketingRequest.MarketingOrderPageInfo, lang lang.LangUtil) MarkeingOrderLogPaginateResource {
	total := int(pageInfo.Total)
	response := MarkeingOrderLogPaginateResource{
		Items:     []MarketingOrderLog{},
		Total:     total,
		Page:      pageInfo.Page,
		Limit:     pageInfo.Limit,
		Keyword:   pageInfo.Keyword,
		BeginDate: pageInfo.BeginDate,
		EndDate:   pageInfo.EndDate,
	}
	for _, marketingOrderLog := range marketingOrderLogs {
		response.Items = append(response.Items, NewMarketingOrderLogResource(marketingOrderLog, lang))
	}
	return response
}
