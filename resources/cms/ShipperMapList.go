package cms

type ShipperMapList struct {
	ID       int     `json:"id"`
	RealName string  `json:"real_name"`
	RealNameFormatted string  `json:"real_name_formatted"`
	Lat      float64 `json:"lat"`
	Lng      float64 `json:"lng"`
	Speed      float64 `json:"speed"`
	Accuracy float64 `json:"accuracy"`
	PosState int     `json:"pos_state"`  // 1:正常 0：已下线
	LastTime string  `json:"last_time"`
	Location []string  `json:"location"`
	Avatar  string  `json:"avatar"`
	ShipperMapListSendingOrders []ShipperMapListSendingOrders  `json:"sending_orders"`
	OrderDistance float64 `json:"order_distance"`
	AttendanceState int `json:"attendance_state"`
	AttendanceStateName string `json:"attendance_state_name"`
	AutoDispatchRank int `json:"auto_dispatch_rank"`
}

type ShipperMapListSendingOrders struct {
	ID       int     `json:"id"`
	State       int     `json:"state"`
	OrderId  string  `json:"order_id"`
	ResName string  `json:"res_name"`
	CustomerPos []string  `json:"customer_pos"`
	CustomerAvatar string  `json:"customer_avatar"`
	ResPos 		[]string  `json:"res_pos"`
	ResLogo 		string  `json:"res_logo"`
	BuildingName string  `json:"building_name"`
	OrderAddress string  `json:"order_address"`
	BookingTime string  `json:"booking_time"`
	IsDelay int  `json:"is_delay"`
	LeftMinutes int  `json:"left_minutes"`
	Price string  `json:"price"`
	Distance string `json:"distance"`
	SerialNumber int64 `json:"serial_number"`
	ShipmentState string `json:"shipment_state"`
	SetChannel int `json:"set_channel"`
}