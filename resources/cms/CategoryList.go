package cms

type CategoryList struct {
	ID                   int    `json:"id"`
	ParentID             int    `json:"parent_id"`
	ParentCategoryNameUg string `json:"parent_category_name_ug"`
	ParentCategoryNameZh string `json:"parent_category_name_zh"`
	Child                []CategoryListChild `json:"child"`
}

type CategoryListChild struct {
	ID                  int    `json:"id"`
	ParentID            int    `json:"parent_id"`
	ChildCategoryNameUg string `json:"child_category_name_ug"`
	ChildCategoryNameZh string `json:"child_category_name_zh"`
}