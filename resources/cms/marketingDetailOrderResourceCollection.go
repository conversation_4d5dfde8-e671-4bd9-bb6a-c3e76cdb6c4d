package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	// "mulazim-api/tools"
)

type MarketingDetailOrderResourceCollectionItem struct {
	Id                    int    `json:"id"`
	OrderId               int    `json:"order_id"`
	RestaurantId          int    `json:"restaurant_id"`
	RestaurantName        string `json:"restaurant_name"`
	OrderNo               string `json:"order_no"`
	CustomerName          string `json:"receiver_name"`
	CustomerMobile        string `json:"receiver_phone"`
	OrderPrice            int    `json:"order_price"`
	OrderState            string `json:"state_label"`
	Shipment              int    `json:"shipment"`
	OriginalShipment      int    `json:"original_shipment"`
	ReduceShipment        int    `json:"reduce_shipment"`
	ReductionFee          int    `json:"reduction_fee"`
	OriginalShipmentPrice int    `json:"original_shipment_price"`
	OrderAddress          string `json:"order_address"`
	CreatedAt             string `json:"created_at"`
	ShipperName           string `json:"shipper_name"`
}

type MarketingDetailOrderResourceCollection struct {
	Items     []MarketingDetailOrderResourceCollectionItem `json:"items"`
	Total     int64                                        `json:"total"`
	BeginDate string                                       `json:"begin_date"`
	EndDate   string                                       `json:"end_date"`
	Page      int                                          `json:"page"`
	Limit     int                                          `json:"limit"`
	Keyword   string                                       `json:"keyword"`
}

// NewMarketingListResourceCollection 营销活动列表资源集合构造函数
// @param marketingList 营销活动列表 <br/>
// @param total in64 总数
// @param cityId int 城市ID
// @param areaId int 区域ID
// @param restaurantId int  餐厅ID
// @param state int 状态
// @param beginDate string 开始时间
// @param endDate string 结束时间
// @param page int 页码
// @param limit int 每页数量
// @param keyword string 关键字
func NewMarketingDetailOrderResourceCollection(
	OrderLogs []models.MarketingOrderLog,
	total int64,
	beginDate, endDate string,
	page, limit int,
	keyword string,
	langUtil lang.LangUtil,
	isDownLoad bool,
) *MarketingDetailOrderResourceCollection {
	items := []MarketingDetailOrderResourceCollectionItem{}
	for _, orderLog := range OrderLogs {
		item := MarketingDetailOrderResourceCollectionItem{
			Id:      orderLog.ID,
			OrderId: orderLog.OrderID,
			OrderNo: orderLog.OrderNo,
			// CustomerName      string `json:"customer_name"`
			// CustomerMobile    string `json:"customer_mobile"`
			OrderPrice: orderLog.OrderPrice,
			// OrderState:       langUtil.GetOrderState(marketing.OrderState),
			OriginalShipment:      orderLog.OriginalShipment,
			ReduceShipment:        orderLog.ReductionFee,
			ReductionFee:          orderLog.ReductionFee,
			OriginalShipmentPrice: orderLog.OriginalShipment,
			// OrderAddress	string `json:"order_address"`
			// ShipperName 	string `json:"shipper_name"`
			CreatedAt: orderLog.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		if orderLog.Order != nil {
			//item.Id = marketing.Order.ID
			item.CustomerName = orderLog.Order.Name
			item.CustomerMobile = orderLog.Order.Mobile
			item.OrderAddress = orderLog.Order.OrderAddress
			item.OrderState = langUtil.TArr("order_state")[orderLog.Order.State]
			item.Shipment = int(orderLog.Order.Shipment)
			if orderLog.Order.Shipper.ID > 0 {
				item.ShipperName = orderLog.Order.Shipper.RealName
			}
		} else if orderLog.OrderToday != nil {
			//item.Id = marketing.OrderToday.ID
			item.CustomerName = orderLog.OrderToday.Name
			item.CustomerMobile = orderLog.OrderToday.Mobile
			item.OrderAddress = orderLog.OrderToday.OrderAddress
			item.OrderState = langUtil.TArr("order_state")[orderLog.OrderToday.State]
			item.Shipment = int(orderLog.OrderToday.Shipment)
			if orderLog.OrderToday.Shipper.ID > 0 {
				item.ShipperName = orderLog.OrderToday.Shipper.RealName
			}
		}
		if langUtil.Lang == "zh" {
			if orderLog.Restaurant != nil {
				item.RestaurantId = orderLog.Restaurant.ID
				item.RestaurantName = orderLog.Restaurant.NameZh
			}
		} else {
			if orderLog.Restaurant != nil {
				item.RestaurantId = orderLog.Restaurant.ID
				if isDownLoad {
					item.RestaurantName = orderLog.Restaurant.NameZh
				}else{
					item.RestaurantName = orderLog.Restaurant.NameUg
				}
				
			}

		}
		items = append(items, item)
	}
	return &MarketingDetailOrderResourceCollection{
		Items:     items,
		Total:     total,
		BeginDate: beginDate,
		EndDate:   endDate,
		Page:      page,
		Limit:     limit,
		Keyword:   keyword,
	}
}
