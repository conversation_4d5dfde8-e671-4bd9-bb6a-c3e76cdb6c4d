package cms

import (
	"encoding/json"
	"mulazim-api/models"
	"mulazim-api/requests/marketingRequest"
)

type ShipmentReduceStep struct {
	DistanceStart int `json:"distance_start"`
	DistanceEnd   int `json:"distance_end"`
	PriceReduce   int `json:"price_reduce"`
	StoreReduce   int `json:"store_reduce"`
	DealerReduce  int `json:"dealer_reduce"`
}

type ShipmentReduceTimeline struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

type MarketingDetailResource struct {
	ID               int                      `json:"id"`
	NameZh           string                   `json:"name_zh"`
	NameUg           string                   `json:"name_ug"`
	BeginDate        string                   `json:"begin_date"`
	EndDate          string                   `json:"end_date"`
	FullWeekState    int                      `json:"full_week_state"`
	Day              int                      `json:"day"`
	FullTimeState    int                      `json:"full_time_state"`
	AutoContinue     int                      `json:"auto_continue"`
	MinDeliveryPrice int                      `json:"min_delivery_price"`
	Steps            []ShipmentReduceStep     `json:"steps"`
	Timelines        []ShipmentReduceTimeline `json:"timelines"`
	CreatorID        int                      `json:"creator_id"`
	CreatorName      string                   `json:"creator_name,omitempty"`
	RestaurantNameUg string                   `json:"restaurant_name_ug,omitempty"`
	RestaurantNameZh string                   `json:"restaurant_name_zh,omitempty"`
	State            int                      `json:"state"`
	CreatedAt        string                   `json:"created_at"`
	UpdatedAt        string                   `json:"updated_at"`
}

// NewMarketingDetailResource 活动详情
func NewMarketingDetailResource(marketing models.Marketing) MarketingDetailResource {
	var steps = make([]ShipmentReduceStep, 0)
	var timelines = make([]ShipmentReduceTimeline, 0)
	json.Unmarshal([]byte(marketing.Steps), &steps)
	if marketing.Time1Start != "" && marketing.Time1End != "" {
		timelines = append(timelines, ShipmentReduceTimeline{
			Start: marketing.Time1Start[:5],
			End:   marketing.Time1End[:5],
		})
	}
	if marketing.Time2Start != "" && marketing.Time2End != "" {
		timelines = append(timelines, ShipmentReduceTimeline{
			Start: marketing.Time2Start[:5],
			End:   marketing.Time2End[:5],
		})
	}
	if marketing.Time3Start != "" && marketing.Time3End != "" {
		timelines = append(timelines, ShipmentReduceTimeline{
			Start: marketing.Time3Start[:5],
			End:   marketing.Time3End[:5],
		})
	}
	resource := MarketingDetailResource{
		ID:               marketing.ID,
		NameZh:           marketing.NameZh,
		NameUg:           marketing.NameUg,
		BeginDate:        marketing.BeginDate.Format("2006-01-02"),
		EndDate:          marketing.EndDate.Format("2006-01-02"),
		FullWeekState:    marketing.FullWeekState,
		Day:              marketing.Day,
		FullTimeState:    marketing.FullTimeState,
		AutoContinue:     marketing.AutoContinue,
		MinDeliveryPrice: marketing.MinDeliveryPrice,
		CreatorID:        marketing.CreatorID,
		Steps:            steps,
		Timelines:        timelines,
		State:            marketing.State,
		CreatedAt:        marketing.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:        marketing.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	if marketing.Creator != nil && marketing.Creator.ID != 0 {
		resource.CreatorName = marketing.Creator.RealName
	}
	if marketing.Restaurant != nil && marketing.Restaurant.ID != 0 {
		resource.RestaurantNameUg = marketing.Restaurant.NameUg
		resource.RestaurantNameZh = marketing.Restaurant.NameZh
	}
	return resource
}

type MarketingDetailWithAggsResource struct {
	MarketingDetailResource MarketingDetailResource      `json:"marketing"`
	Aggs                    models.MarketingOrderLogAggs `json:"aggs"`
}

func NewMarketingDetailWithAggsResource(marketing models.Marketing, orderAggs models.MarketingOrderLogAggs) MarketingDetailWithAggsResource {
	resource := MarketingDetailWithAggsResource{
		MarketingDetailResource: NewMarketingDetailResource(marketing),
		Aggs: models.MarketingOrderLogAggs{
			MarketingID:      marketing.ID,
			TotalOrderPrice:  orderAggs.TotalOrderPrice,
			OrderCount:       orderAggs.OrderCount,
			TotalReducePrice: orderAggs.TotalReducePrice,
			TotalDealerCost:  orderAggs.TotalDealerCost,
			TotalResCost:     orderAggs.TotalResCost,
		},
	}
	return resource
}

type OrderLogResource struct {
	ID   int `json:"id"`
	Type int `json:"type"`
}

func NewOrderLogResource(orderLogs []models.MarketingOrderLog, request marketingRequest.MarketingOrderPageInfo) OrderLogResource {
	resource := OrderLogResource{}
	return resource
}
