package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
)

type ThemeActivityResource struct {
	ID                    int    `json:"id"`
	CreaterID             int    `json:"creater_id"`
	CreatorName           string `json:"creator_name"`
	Name                  string `json:"name"` // 根据语言返回对应名称
	NameUg                string `json:"name_ug"`
	NameZh                string `json:"name_zh"`
	Desc                  string `json:"desc"` // 根据语言返回对应说明
	DescUg                string `json:"desc_ug"`
	DescZh                string `json:"desc_zh"`
	BeginTime             string `json:"begin_time"`
	EndTime               string `json:"end_time"`
	DiscountPercent       int    `json:"discount_percent"`
	CoverUg               string `json:"cover_ug"`
	CoverZh               string `json:"cover_zh"`
	Color                 string `json:"color"`
	State                 int    `json:"state"`
	OrderType             string `json:"order_type"`
	ShowPreferential      int    `json:"show_preferential"`
	ShowSeckill           int    `json:"show_seckill"`
	ShowFoodType          int    `json:"show_food_type"`
	HasKeyword            int    `json:"has_keyword"`
	KeywordUg             string `json:"keyword_ug"`
	KeywordZh             string `json:"keyword_zh"`
	ExcludeKeywordUG     string `json:"exclude_keyword_ug"`
	ExcludeKeywordZH     string `json:"exclude_keyword_zh"`
	ShareContentUg        string `json:"share_content_ug"`
	ShareContentZh        string `json:"share_content_zh"`
	ShareCoverUg          string `json:"share_cover_ug"`
	ShareCoverZh          string `json:"share_cover_zh"`
	ShareCoverToFriendUg  string `json:"share_cover_to_friend_ug"`
	ShareCoverToFriendZh  string `json:"share_cover_to_friend_zh"`
	CreatedAt             string `json:"created_at"`
	UpdatedAt             string `json:"updated_at"`
}

func NewThemeActivityResource(activity models.ThemeActivity, langUtil *lang.LangUtil) ThemeActivityResource {
	resource := ThemeActivityResource{
		ID:                    activity.ID,
		CreaterID:             activity.CreaterID,
		NameUg:                activity.NameUg,
		NameZh:                activity.NameZh,
		DescUg:                activity.DescUg,
		DescZh:                activity.DescZh,
		DiscountPercent:       activity.DiscountPercent,
		CoverUg:               activity.CoverUg,
		CoverZh:               activity.CoverZh,
		Color:                 activity.Color,
		State:                 activity.State,
		OrderType:             activity.OrderType,
		ShowPreferential:      activity.ShowPreferential,
		ShowSeckill:           activity.ShowSeckill,
		ShowFoodType:          activity.ShowFoodType,
		HasKeyword:            activity.HasKeyword,
		KeywordUg:             activity.KeywordUg,
		KeywordZh:             activity.KeywordZh,
		ExcludeKeywordUG:      activity.ExcludeKeywordUG,
		ExcludeKeywordZH:      activity.ExcludeKeywordZH,
		ShareContentUg:        activity.ShareContentUg,
		ShareContentZh:        activity.ShareContentZh,
		ShareCoverUg:          activity.ShareCoverUg,
		ShareCoverZh:          activity.ShareCoverZh,
		ShareCoverToFriendUg:  activity.ShareCoverToFriendUg,
		ShareCoverToFriendZh:  activity.ShareCoverToFriendZh,
	}

	if activity.BeginTime != nil {
		resource.BeginTime = activity.BeginTime.Format("2006-01-02 15:04:05")
	}
	if activity.EndTime != nil {
		resource.EndTime = activity.EndTime.Format("2006-01-02 15:04:05")
	}
	if activity.CreatedAt != nil {
		resource.CreatedAt = activity.CreatedAt.Format("2006-01-02 15:04:05")
	}
	if activity.UpdatedAt != nil {
		resource.UpdatedAt = activity.UpdatedAt.Format("2006-01-02 15:04:05")
	}

	// 根据语言设置名称和描述
	if langUtil.Lang == "zh" {
		resource.Name = activity.NameZh
		resource.Desc = activity.DescZh
	} else {
		resource.Name = activity.NameUg
		resource.Desc = activity.DescUg
	}

	if activity.Creator != nil {
		resource.CreatorName = activity.Creator.RealName
	}

	return resource
}
