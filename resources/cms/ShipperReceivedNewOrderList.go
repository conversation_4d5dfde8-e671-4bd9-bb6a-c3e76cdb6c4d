package cms

type ShipperReceivedNewOrderList struct {
	ID       int     `json:"id"`
	State       int     `json:"state"`
	OrderId  string  `json:"order_id"`
	ResName string  `json:"res_name"`
	CustomerPos []string  `json:"customer_pos"`
	ResPos 		[]string  `json:"res_pos"`
	BuildingName string  `json:"building_name"`
	OrderAddress string  `json:"order_address"`
	BookingTime string  `json:"booking_time"`
	IsDelay int  `json:"is_delay"`
	LeftMinutes int  `json:"left_minutes"`
	Price string  `json:"price"`
	Distance string `json:"distance"`
	SerialNumber int64 `json:"serial_number"`
	OrderType int64 `json:"order_type"`
}
