package shipment

// 账户信息
type ShipperAttendance struct {
	CityID    int    `form:"city_id" binding:""`            // 1.请假，2:事故
	AreaID    int    `form:"area_id" binding:""`            // 1.请假，2:事故
	StateType int    `form:"state_type" binding:"required"` // 1.请假，2:事故
	ShipperID int    `form:"shipper_id" binding:"required"` // 地区ID
	LeaveType int    `form:"leave_type" binding:""`         // 1:事假 2:病假 3:婚嫁 4:产假/陪产假
	StartTime string `form:"start_time" binding:""`         // 地区ID
	EndTime   string `form:"end_time" binding:""`           // 地区ID
	Remark    string `form:"remark" binding:""`             // 地区ID
	Images    string `form:"images" binding:""`             // 地区ID
}
