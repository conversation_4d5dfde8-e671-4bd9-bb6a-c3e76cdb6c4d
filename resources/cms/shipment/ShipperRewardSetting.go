package shipment

// 配送员奖励设置
type ShipperRewardSetting struct {
	ID              int    `form:"id" binding:""`                        // 地区DI
	CityID          int    `form:"city_id" binding:""`                   // 地区DI
	AreaID          int    `form:"area_id" binding:""`                   // 城市ID
	MonthRestDay    int    `form:"month_rest_day" binding:""`    // 每月考勤天数
	DailyOrderLimit int    `form:"daily_order_limit" binding:"required"` // 每天最少订单数量
	RewardAmount    int    `form:"reward_amount" binding:"required"`     // 奖励金额
	WorkTime        string `form:"work_time" binding:""`                 // 奖励金额
	State           int    `form:"state" binding:"required"`             // 状态
}
