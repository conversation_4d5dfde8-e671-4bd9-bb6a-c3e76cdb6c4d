package shipment

// 特殊天气
type SpecialWeather struct {
	CityID      int    `form:"city_id" binding:""`              // 地区ID
	AreaID      int    `form:"area_id" binding:""`              // 城市ID
	NameUg      string `form:"name_ug" binding:"required"`      // 维文名称
	NameZh      string `form:"name_zh" binding:"required"`      // 中文名称
	StartTime   string `form:"start_time" binding:"required"`   // 开始时间
	EndTime     string `form:"end_time" binding:"required"`     // 结束时间
	ShipmentFee int    `form:"shipment_fee" binding:"required"` // 配送费
}
