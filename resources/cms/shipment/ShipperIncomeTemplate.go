package shipment

// 配送费基本信息
type ShipmentBaseInfo struct {
	ID                 int    `form:"id" binding:""`                    // ID
	CityID             int    `form:"city_id" binding:""`               // 地区ID
	AreaID             int    `form:"area_id" binding:""`               // 区域ID
	NameUg             string `form:"name_ug" binding:"required"`       // 模板维文名称
	NameZh             string `form:"name_zh" binding:"required"`       // 模板中文名称
	BaseSalary         int    `form:"base_salary" binding:""`           // 基本工资
	RuleType           int    `form:"rule_type" binding:"required"`     // 规则类型:1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
	RuleContent        string `form:"rule_content" binding:"required"`  // 规则内容
	RuleOrderCountType int    `form:"rule_order_count_type" binding:""` // 按订单数量计算配送费类型：1:全部订单，2:超过部分
	// 第二页
	SpecialShipmentState   int    `form:"special_shipment_state" binding:"required"`   // 特殊配送费状态：0:启用，1:禁用
	SpecialShipmentContent string `form:"special_shipment_content" binding:"required"` // 特殊配送费规则
	// 第三页
	CommentDeductionFee  int    `form:"comment_deduction_fee" binding:""`          // 差评扣款金额
	CommentIncrementFee  int    `form:"comment_increment_fee" binding:""`          // 好评扣款金额
	ComplainDeductionFee int    `form:"complain_deduction_fee" binding:""`         // 投诉扣款金额
	LateDeductionType    int    `form:"late_deduction_type" binding:"required"`    // 迟到扣款类型:1:按订单数量,2:迟到时间
	LateDeductionContent string `form:"late_deduction_content" binding:"required"` // 迟到扣款规则

	InviteUserFee int    `form:"invite_user_fee" binding:""`         // 推广新用户奖励额度 
	InviteOldUserFee int    `form:"invite_old_user_fee" binding:""`         // 推广老客户奖励额度(没下单超过3个月)
	OrderPercent int    `form:"order_percent" binding:""`         // 推广用户订单分成百分比，单位：万分之一	
	OrderOldUserPercent int    `form:"order_old_user_percent" binding:""`         // 推广用户订单分成百分比，单位：万分之一	

}

// 特殊配送费信息
type ShipmentSpecialInfo struct {
	SpecialShipmentState   int    `form:"special_shipment_state" binding:"required"`   // 特殊配送费状态
	SpecialShipmentContent string `form:"special_shipment_content" binding:"required"` // 特殊配送费规则
}

// 扣款配送费信息
type ShipmentDeductionInfo struct {
	CommentDeductionFee  int    `form:"comment_deduction_fee" binding:"required"`  // 差评扣款金额
	ComplainDeductionFee int    `form:"complain_deduction_fee" binding:"required"` // 投诉扣款金额
	LateDeductionType    int    `form:"late_deduction_type" binding:"required"`    // 迟到扣款类型:1:按订单数量,2:迟到时间
	LateDeductionContent string `form:"late_deduction_content" binding:"required"` // 迟到扣款规则
}

// FixShipmentFeeRule 固定配送费规则
type FixShipmentFeeRule struct {
	FixShipmentFee int `json:"fix_shipment_fee"` // 固定配送费
}

// DistanceBaseShipmentFeeRule 按距离计算配送费规则
type DistanceBaseShipmentFeeRule struct {
	Distance    int `json:"distance"`     // 距离
	ShipmentFee int `json:"shipment_fee"` // 配送费
}

// TaxiBaseShipmentFeeRule 按出租车配送费计算规则
type TaxiBaseShipmentFeeRule struct {
	FixedStartFee     int `json:"fixed_start_fee"`     // 基础配送费	单位分
	Distance          int `json:"distance"`            // 固定费用所包含的配送距离 单位米
	PricePerKilometer int `json:"price_per_kilometer"` // 超出最低配送距离部分按每公里配送价计算 单位分
}

// OrderCountBaseShipmentFeeRule 按订单数量计算配送费规则
type OrderCountBaseShipmentFeeRule struct {
	StartOrderCount int `json:"start_order_count"` // 订单数到这个值每个订单分配给配送员的配送费
	ShipmentFee     int `json:"shipment_fee"`      // 配送费	单位分
}

// 特殊配送员规则
type SpecialShipment struct {
	StartTime    string         `json:"start_time"`    // 时间头
	EndTime      string         `json:"end_time"`      // 时间尾
	TimeList     []int          `json:"time_list"`     // 时间尾
	DistanceStep []DistanceStep `json:"distance_step"` // 距离阶梯
}
type DistanceStep struct {
	Distance    int `json:"distance"`     // 距离
	ShipmentFee int `json:"shipment_fee"` // 配送费
}

// 按迟到订单数量固定扣款配送费
type LateDeductionFix struct {
	DeductionFee int `json:"deduction_fee"` // 扣款费用
}

// 按迟到时间扣款配送费
type LateDeductionTime struct {
	MinMinute    int `json:"min_minute"`    // 开始分钟数
	MaxMinute    int `json:"max_minute"`    // 结束分钟数
	DeductionFee int `json:"deduction_fee"` // 扣款配送费
}
