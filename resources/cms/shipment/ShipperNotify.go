package shipment

// 账户信息
type ShipperNotify struct {
	CityID    int    `json:"city_id" form:"city_id" binding:""`            //
	AreaID    int    `json:"area_id" form:"area_id" binding:""`            //
	TitleUg   string `json:"title_ug" form:"title_ug" binding:"required"`   //
	TitleZh   string `json:"title_zh" form:"title_zh" binding:"required"`   //
	ContentUg string `json:"content_ug" form:"content_ug" binding:"required"` //
	ContentZh string `json:"content_zh" form:"content_zh" binding:"required"` //
	SendTime  string `json:"send_time" form:"send_time" binding:""`          //
	State     int    `json:"state" form:"state" binding:""`              // 1:新建,2:已发,3:重发
	AreaIDs   []int  `json:"area_ids" form:"area_ids" binding:""`
}
