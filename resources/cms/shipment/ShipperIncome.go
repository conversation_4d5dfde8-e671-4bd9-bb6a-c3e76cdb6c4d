package shipment

// 账户信息
type ShipperIncome struct {
	CityID        int    `form:"city_id" binding:""`                // 城市ID
	AreaID        int    `form:"area_id" binding:""`                // 地区DI
	Type          int    `form:"type" binding:"required"`           // 类型 1：订单收入，2:奖励收入，3:打赏收入，4.特殊时间，5.特殊天气，6.好评，7.差评，8.投诉，9:迟到
	ComplaintType int    `form:"complaint_type" binding:"required"` // 投诉类型(1:客户，2:餐厅)
	Amount        int    `form:"amount" binding:"required"`         // 扣款
	ShipperID     int    `form:"shipper_id" binding:"required"`     //
	OrderID       int    `form:"order_id" binding:""`               //
	Remark        string `form:"remark" binding:""`                 //
}
