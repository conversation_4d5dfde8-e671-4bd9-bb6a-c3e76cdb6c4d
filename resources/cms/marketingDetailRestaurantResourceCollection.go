package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
)

type MarketingDetailRestaurantResourceCollectionItem struct {
	Id                  int    `json:"id"`
	Name                string `json:"name"`
	CityName            string `json:"city_name"`
	AreaName            string `json:"area_name"`
	RestaurantId        int    `json:"restaurant_id"`
	RestaurantName      string `json:"restaurant_name"`
	RestaurantNameUg    string `json:"restaurant_name_ug"`
	RestaurantNameZh    string `json:"restaurant_name_zh"`
	RestaurantLogo      string `json:"restaurant_logo"`
	RestaurantMobile    string `json:"restaurant_mobile"`
	BeginDate           string `json:"begin_date"`
	EndDate             string `json:"end_date"`
	State               int    `json:"state"`
	StateLabel          string `json:"state_label"`
	TotalOrderPrice     int    `json:"total_order_price"`
	TotalReducePrice    int    `json:"total_reduce_price"`
	OrderCount          int    `json:"order_count"`
	TotalCostRestaurant int    `json:"total_cost_restaurant"` // 优惠金额（单位 分） 店铺承担金额
	TotalCostDealer     int    `json:"total_cost_dealer"`     // 优惠金额（单位 分） 代理承担金额
	CreatorName         string `json:"creator_name"`
	CreatedAt           string `json:"created_at"`
	AcceptTime          string `json:"accept_time"`
	AcceptMobile        string `json:"accept_mobile"`
	SendCount           int    `json:"send_count"`
	AttendanceState     int    `json:"attendance_state"`
}

type MarketingDetailRestaurantResourceCollection struct {
	Items     []MarketingDetailRestaurantResourceCollectionItem `json:"items"`
	Total     int64                                             `json:"total"`
	BeginDate string                                            `json:"begin_date"`
	EndDate   string                                            `json:"end_date"`
	Page      int                                               `json:"page"`
	Limit     int                                               `json:"limit"`
	Keyword   string                                            `json:"keyword"`
}

// NewMarketingListResourceCollection 营销活动列表资源集合构造函数
// @param marketingList 营销活动列表 <br/>
// @param total in64 总数
// @param cityId int 城市ID
// @param areaId int 区域ID
// @param restaurantId int  餐厅ID
// @param state int 状态
// @param beginDate string 开始时间
// @param endDate string 结束时间
// @param page int 页码
// @param limit int 每页数量
// @param keyword string 关键字
func NewMarketingDetailRestaurantResourceCollection(
	marketingList []models.MarketingGroupTemplateAttendance,
	total int64,
	beginDate, endDate string,
	page, limit int,
	keyword string,
	langUtil lang.LangUtil,
	isDownLoad bool,
) *MarketingDetailRestaurantResourceCollection {
	items := []MarketingDetailRestaurantResourceCollectionItem{}
	for _, template := range marketingList {
		marketing := template.Marketing
		detailItems := marketing.MarketingOrderLogForDetail
		item := MarketingDetailRestaurantResourceCollectionItem{
			Id: template.ID,
			// BeginDate:           marketing.BeginDate.Format("2006-01-02"),
			// EndDate:             marketing.EndDate.Format("2006-01-02"),

			SendCount:       template.SendCount,
			StateLabel:      "",
			AttendanceState: template.State,
		}
		if marketing.ID > 0 {
			item.BeginDate = marketing.BeginDate.Format("2006-01-02")
			item.EndDate = marketing.EndDate.Format("2006-01-02")
			item.State = marketing.State
			item.CreatedAt = marketing.CreatedAt.Format("2006-01-02 15:04:05")
			if stateLabel, ok := langUtil.TArr("marketing_state_name")[marketing.State]; ok {
				item.StateLabel = stateLabel
				if isDownLoad {
					if stateLabel2, ok := langUtil.TArrZh("marketing_state_name")[marketing.State]; ok {
						item.StateLabel = stateLabel2
					}
				}
			}
			
			if marketing.City.ID > 0 {
				item.CityName = marketing.Area.NameZh
			}
			if marketing.Area.ID > 0 {
				item.AreaName = marketing.Area.NameZh
			}
		}
		if marketing.Creator != nil {
			item.CreatorName = marketing.Creator.RealName
		}

		if detailItems != nil {
			if template.Restaurant != nil {
				for _, detailItem := range *detailItems {
					if detailItem.RestaurantId == template.Restaurant.ID {
						item.TotalOrderPrice = detailItem.TotalOrderPrice
						item.TotalReducePrice = detailItem.TotalReducePrice
						item.OrderCount = detailItem.OrderCount
						item.TotalCostRestaurant = detailItem.TotalResCost
						item.TotalCostDealer = detailItem.TotalDealerCost
					}
				}
			}
		}
		if langUtil.Lang == "zh" {
			item.Name = marketing.NameZh
			if template.Restaurant != nil {
				item.RestaurantId = template.Restaurant.ID
				item.RestaurantName = template.Restaurant.NameZh
				item.RestaurantNameZh = template.Restaurant.NameZh
				item.RestaurantNameUg = template.Restaurant.NameUg
				item.RestaurantLogo = tools.CdnUrl(template.Restaurant.Logo)
				item.RestaurantMobile = tools.MaskMobile(template.Restaurant.AdminTel)
				if marketing.ID > 0 {

					item.AcceptMobile = tools.MaskMobile(marketing.Restaurant.AdminTel)
					item.AcceptTime = marketing.CreatedAt.Format("2006-01-02 15:04:05")
				}

			}
		} else {
			item.Name = marketing.NameUg
			if isDownLoad {
				item.Name = marketing.NameZh
			}
			if template.Restaurant != nil {
				item.RestaurantId = template.Restaurant.ID
				item.RestaurantName = template.Restaurant.NameUg
				item.RestaurantNameUg = template.Restaurant.NameUg
				item.RestaurantNameZh = template.Restaurant.NameZh
				item.RestaurantLogo = tools.CdnUrl(template.Restaurant.Logo)
				item.RestaurantMobile = tools.MaskMobile(template.Restaurant.AdminTel)
				if marketing.ID > 0 {
					item.RestaurantMobile = tools.MaskMobile(template.Restaurant.AdminTel)
					item.AcceptMobile = tools.MaskMobile(template.Restaurant.AdminTel)
					item.AcceptTime = marketing.CreatedAt.Format("2006-01-02 15:04:05")
				}
			}

		}
		items = append(items, item)
	}
	return &MarketingDetailRestaurantResourceCollection{
		Items:     items,
		Total:     total,
		BeginDate: beginDate,
		EndDate:   endDate,
		Page:      page,
		Limit:     limit,
		Keyword:   keyword,
	}
}
