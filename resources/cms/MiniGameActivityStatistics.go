package cms

type MiniGameActivityStatistics struct {
	Index          int     `json:"index"`
	ID             int     `json:"id"`
	AreaName       string  `json:"area_name"`        // 区域名称
	UserName       string  `json:"user_name"`        // 用户名称
	Mobile         string  `json:"mobile"`           // 手机号
	GameCount      float64 `json:"game_count"`       // 游戏次数
	AllAmount      float64 `json:"all_amount"`       // 总共金额
	SpendAmount    float64 `json:"spend_amount"`     // 消费金额
	TakeOrderCount float64 `json:"take_order_count"` // 下单数量

}

type MiniGameActivityStatisticByNumber struct {
	AllAttendCount int64                        `json:"all_attend_count"` // 总游戏参加次数
	SendAmount     int64                        `json:"send_amount"`      // 总发放的金额
	UsedAmount     int64                        `json:"used_amount"`      // 总使用的金额
	UseOrderCount  int64                        `json:"use_order_count"`  // 使用订单的数量
	AllPlayCount   int64                        `json:"all_play_count"`   // 总玩的次数
	Total          int64                        `json:"total"`            // 分页数据总数
	Items          []MiniGameActivityStatistics `json:"items"`            //
}

type ActivityStatisticsByAreaFormat struct {
	AreaName       string `json:"area_name"` // 区域名称
	ActivityName   string `json:"activity_name"`
	AllAttendCount int64  `json:"all_attend_count"` // 总游戏参加次数
	SendAmount     int64  `json:"send_amount"`      // 总发放的金额
	UsedAmount     int64  `json:"used_amount"`      // 总使用的金额
}

type UseDiscountStatistics struct {
	AreaName       string  `json:"area_name"`       // 区域名称
	RestaurantName string  `json:"restaurant_name"` // 餐厅名称
	OrderNo        string  `json:"order_no"`        // 订单编号
	OriginalPrice  float64 `json:"original_price"`  // 原价
	OrderPercent   float64 `json:"order_percent"`   // 订单优惠比例
	Discount       float64 `json:"discount"`        // 优惠价格
	ActualPaid     int64   `json:"actual_paid"`     // 实际支付
	CreatedAt      string  `json:"created_at"`      //下单时间
}
type UseDiscountStatisticsByNumber struct {
	SendAmount          int64                   `json:"send_amount"`           // 总发放的金额
	UsedAmount          int64                   `json:"used_amount"`           // 总使用的金额
	UseOrderCount       int64                   `json:"use_order_count"`       // 使用订单的数量
	DiscountOrderAmount int64                   `json:"discount_order_amount"` // 使用优惠的订单总价
	Total               int64                   `json:"total"`                 // 分页数据总数
	Items               []UseDiscountStatistics `json:"items"`                 //
}

type UseDiscountStatisticsByAreaFormat struct {
	AreaName       string `json:"area_name"` // 区域名称
	ActivityName   string `json:"activity_name"`
	AllAttendCount int64  `json:"all_attend_count"` // 总游戏参加次数
	SendAmount     int64  `json:"send_amount"`      // 总发放的金额
	UsedAmount     int64  `json:"used_amount"`      // 总使用的金额
	TakeOrderCount int64  `json:"take_order_count"` // 下的单数量
	NewUserCount   int64  `json:"new_user_count"`
}
