package resources
import "encoding/xml"

type WechatPayResponse struct {
	XMLName         xml.Name `xml:"xml"`
	AppId           string   `xml:"appid"`
	BankType        string   `xml:"bank_type"`
	CashFee         string   `xml:"cash_fee"`
	FeeType         string   `xml:"fee_type"`
	IsSubscribe     string   `xml:"is_subscribe"`
	MchId           string   `xml:"mch_id"`
	NonceStr        string   `xml:"nonce_str"`
	OpenId          string   `xml:"openid"`
	OutTradeNo      string   `xml:"out_trade_no"`
	ResultCode      string   `xml:"result_code"`
	ReturnCode      string   `xml:"return_code"`
	Sign            string   `xml:"sign"`
	TimeEnd         string   `xml:"time_end"`
	TotalFee        int      `xml:"total_fee"` // 注意这里假设 total_fee 是整数类型
	TradeType       string   `xml:"trade_type"`
	TransactionId   string   `xml:"transaction_id"`
}