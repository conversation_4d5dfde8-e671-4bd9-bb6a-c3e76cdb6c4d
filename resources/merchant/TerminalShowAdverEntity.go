package merchant


type TerminalShowAdverEntityVersion struct {
	Id int `json:"id"`
	Name string `json:"name"`
	Type int `json:"type"`
	Icon string `json:"icon"`
	Version string `json:"version"`
	VersionCode int `json:"version_code"`
	PackageName string `json:"package_name"`
	Url string `json:"url"`
	Des string `json:"des"`
	ForceUpdate int `json:"force_update"`
}

type TerminalShowAdverEntityAdver struct {
	Id int `json:"id"`
	LinkType int `json:"link_type"`
	LinkUrl string `json:"link_url"`
	AppId interface{} `json:"app_id"`
	TitleUg string `json:"title_ug"`
	TitleZh string `json:"title_zh"`
	ContentUg string `json:"content_ug"`
	ContentZh string `json:"content_zh"`
	ImageUg string `json:"image_ug"`
	ImageZh string `json:"image_zh"`
	ShowTime int `json:"show_time"`
}

type TerminalShowAdverEntity struct {
	Version TerminalShowAdverEntityVersion `json:"version"`
	Adver *TerminalShowAdverEntityAdver `json:"adver"`
}