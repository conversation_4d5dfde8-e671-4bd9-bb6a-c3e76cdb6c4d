package merchant

// NewOrderEntity 返回给商家端的json 数据
// @Description:
type NewOrderEntity struct {
	NewOrderList    []NewOrderList `json:"order_list"`
	NewOrderCount   NewOrderCount  `json:"order_count"`
	ServerTime      int            `json:"server_time"`
	VoiceOrderCount int            `json:"voice_order_count"`
}
type NewOrderDetail struct {
	FoodID        int     `json:"food_id"`
	OriginalPrice float32 `json:"original_price"`
	Price         float32 `json:"price"`
	Count         int     `json:"count"`
	Name          string  `json:"name"`
	Image         string  `json:"image"`

	FoodType uint8 `json:"food_type"`                                           // 美食类型：0:普通美食，1:规格美食，2:套餐美食
	ComboFoodItems []OrderFoodComboItems    `json:"combo_food_items,omitempty"` // 套餐中的美食项
	SelectedSpec   *OrderRestaurantFoodSpec `json:"selected_spec,omitempty"`    // 已选的规格数据

	OrderNum uint `json:"-"` // 中间属性，为了排序，不需要转换成 JSON 时展示
}
type NewOrderList struct {
	ID                     int              `json:"id"`
	SerialNumber           int              `json:"serial_number"`
	Name                   string           `json:"name"`
	Price                  float32          `json:"price"`
	OrderNo                string           `json:"order_no"`
	Mobile                 string           `json:"mobile"`
	RealMobile             string           `json:"real_mobile"`
	ShipperMobile          string           `json:"shipper_mobile,omitempty"`
	ShipperName            string           `json:"shipper_name,omitempty"`
	OrderTime              int              `json:"order_time"`
	Timezone               int              `json:"timezone"`
	BookingTime            string           `json:"booking_time"`
	BookingDateTime        string           `json:"booking_date_time"`
	CanBeReceivedIn        int64            `json:"can_be_received_in"`
	PrintTime              string           `json:"print_time"`
	CityName               string           `json:"city_name"`
	AreaName               string           `json:"area_name"`
	StreetName             string           `json:"street_name"`
	BuildingName           string           `json:"building_name"`
	OrderAddress           string           `json:"order_address"`
	OrderNote              string           `json:"order_note"`
	SendNotify             int              `json:"send_notify"`
	ConsumeType            int              `json:"consume_type"`
	Shipment               float64          `json:"shipment"`
	OriginalShipment       float64          `json:"original_shipment"`
	OrderState             int              `json:"order_state"`
	OrderStateDes          string           `json:"order_state_des"`
	OrderType              int              `json:"order_type"`
	ShowCustomerInfo       bool             `json:"show_customer_info"`
	Now                    int              `json:"now,omitempty"`
	IsMarketing            int              `json:"is_marketing"`
	IsCoupon               int              `json:"is_coupon"`
	OrderTimeoutTime       string           `json:"order_timeout_time,omitempty"`
	OrderTimeoutTimestamp  int              `json:"order_timeout_timestamp,omitempty"`
	OrderTimeLeft          int              `json:"order_time_left,omitempty"`
	NewOrderDetail         []NewOrderDetail `json:"order_detail"`
	MarketingList          []MarketingList  `json:"marketing_list"`
	OrderPartRefundList    OrderPartRefundInfo  `json:"order_part_refund_info"`
	Coupon                 Coupon           `json:"coupon"`
	DeliveryType           int              `json:"delivery_type"`
	FoodsReadyTime         string           `json:"foods_ready_time"`
	FoodsReadyRemainMinute int              `json:"foods_ready_remain_time"`
	MsgCount               int              `json:"msg_count"` //订单消息数量
	ResIncome              float64          `json:"res_income"`
	OriginalPrice          float64          `json:"original_price"`
	DisplayShipment        int              `json:"display_shipment"`
	DisplayMerchantProfit        int              `json:"display_merchant_profit"`
}

// MarketingList
//
//	@Description: 存储满减活动明细
//	@author: Alimjan
//	@Time: 2022-10-15 18:09:45
type MarketingList struct {
	MarketingName         string  `json:"marketing_name,omitempty"`
	MarketingReductionFee float32 `json:"marketing_reduction_fee"`
	MarketingImage        string  `json:"marketing_image"`
}
type Coupon struct {
	Name  string  `json:"name,omitempty"`
	Price float32 `json:"price"`
}
type NewOrderCount struct {
	NewOrderCount      int `json:"newOrderCount"`
	PreOrderCount      int `json:"preOrderCount"`
	ReceivedOrderCount int `json:"receivedOrderCount"`
	FinishedOrderCount int `json:"finishedOrderCount"`
	CanceledOrderCount int `json:"canceledOrderCount"`
}

type OrderPartRefund struct {
	FoodID        int     `json:"food_id"`
	OriginalPrice float32 `json:"original_price"`
	Price         float32 `json:"price"`
	Count         int     `json:"count"`
	Name          string  `json:"name"`
	Image         string  `json:"image"`
	RefundPrice   float64 `json:"refund_price"`

	FoodType       uint8                    `json:"food_type"`                  // 美食类型：0:普通美食，1:规格美食，2:套餐美食
	SpecID         int                      `json:"spec_id,omitempty"`          // 美食已选规格编号
	ComboFoodItems []OrderFoodComboItems    `json:"combo_food_items,omitempty"` // 套餐中的美食项
	SelectedSpec   *OrderRestaurantFoodSpec `json:"selected_spec,omitempty"`    // 已选的规格数据
}

type OrderPartRefundInfo struct {
	OrderPartRefundList       []OrderPartRefund `json:"order_part_refund_list"`
	OrderPartRefundReason     string            `json:"order_part_refund_reason"`
	OrderPartRefundPrice      float64           `json:"order_part_refund_price"`
	RefundChanelName          string            `json:"refund_chanel_name"`
	PartRefundCreatorName     string            `json:"part_refund_creator_name"`
	RefundChanel              int               `json:"refund_chanel"`
}

// 定义一个结构体实例来接收查询结果
type RealAndPreOrderCount struct {
	NewOrderCount int64 `json:"new_order_count"`
	PreOrderCount int64 `json:"pre_order_count"`
}

// 套餐美食 子美食头信息 返回数据结构体
type OrderFoodComboItems struct {
	ID             int                          `json:"id"`              // 主键ID
	ComboID        int                          `json:"combo_id"`        // 套餐编号
	FoodType       uint8                        `json:"food_type"`       // 美食类型 (0: 普通美食, 1: 规格美食)
	FoodID         int                          `json:"food_id"`         // 美食编号
	Count          int                          `json:"count"`           // 数量
	RestaurantFood OrderRestaurantFoodsResponse `json:"restaurant_food"` // 餐厅美食
}

// 套餐美食 子美食（包括规格数据） 返回数据结构体
type OrderRestaurantFoodsResponse struct {
	ID               int                      `json:"id"`                      // 自增编号
	Name             string                   `json:"name"`                    // 美食编号
	Image            string                   `json:"image"`                   // 美食图片
	Description      string                   `json:"description"`             // 美食说明
	StarAvg          string                   `json:"star_avg"`                // 平均星数
	Price            uint                     `json:"price"`                   // 出口价（单位：分）
	FoodQuantity     float64                  `json:"food_quantity"`           // 美食量
	FoodQuantityType uint8                    `json:"food_quantity_type"`      // 美食量分类；例如：1表示份、2表示个、3表示克、4表示公斤
	SelectedSpec     *OrderRestaurantFoodSpec `json:"selected_spec,omitempty"` // 已选的规格数据
}

// 套餐美食 子美食已选规格 返回数据结构体
type OrderRestaurantFoodSpec struct {
	ID          int                             `gorm:"column:id;primaryKey;autoIncrement" json:"id"`                                                                                                // 主键ID
	SpecOptions []OrderRestaurantFoodSpecOption `gorm:"many2one:t_food_spec_detail;foreignKey:spec_id;joinForeignKey:spec_id;References:ID;joinReferences:foods_spec_option_id" json:"spec_options"` // 分类下面的美食列表
}

// 套餐美食 子美食规格项 返回数据结构体
type OrderRestaurantFoodSpecOption struct {
	ID    int    `json:"id"`    // 主键ID
	Name  string `json:"name"`  // 名称
	NameUg string `json:"name_ug"` // 名称
	NameZh string `json:"name_zh"` // 名称
	Price int    `json:"price"` // 价格（可为空）
}
