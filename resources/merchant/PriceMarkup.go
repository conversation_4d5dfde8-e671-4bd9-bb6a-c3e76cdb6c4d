package merchant

type HomeShowPriceMarkupEntity struct {
	Id            int    `json:"id"`
	FoodsName     string `json:"foods_name"`
	OriginalPrice int    `json:"original_price"`
	InPrice       int    `json:"in_price"`
	TotalCount    int    `json:"total_count"`
	StartDate     string `json:"start_date"`
	EndDate       string `json:"end_date"`
	TotalPrice    int    `json:"total_price"`
	PayTime       string `json:"pay_time"`

	FoodType       uint8                      `json:"food_type"`                  // 美食类型：0:普通美食，1:规格美食，2:套餐美食
	SelectedSpec   *CommentRestaurantFoodSpec `json:"selected_spec,omitempty"`    // 已选的规格数据
	ComboFoodItems []CommentFoodComboItems    `json:"combo_food_items,omitempty"` // 套餐中的美食项
}

type PriceMarkupDetailEntity struct {
	Id               int    `json:"id"`
	FoodsName        string `json:"foods_name"`
	State            int    `json:"state"`
	StateName        string `json:"state_name"`
	RunningState     int    `json:"running_state"`
	RunningStateName string `json:"running_state_name"`
	OriginalPrice    int    `json:"original_price"`
	TotalCount       int    `json:"total_count"`
	Price            int    `json:"price"`
	TotalPrice       int    `json:"total_price"`
	PayTime          string `json:"pay_time"`
	ReviewAt         string `json:"review_at"`
	StartDate        string `json:"start_date"`
	EndDate          string `json:"end_date"`
	SailedCount      int    `json:"sailed_count"`
	SailedPrice      int    `json:"sailed_price"`

	FoodType       uint8                      `json:"food_type"`                  // 美食类型：0:普通美食，1:规格美食，2:套餐美食
	SelectedSpec   *CommentRestaurantFoodSpec `json:"selected_spec,omitempty"`    // 已选的规格数据
	ComboFoodItems []CommentFoodComboItems    `json:"combo_food_items,omitempty"` // 套餐中的美食项
}


type PriceMarkupStatisticSailedMarketDetail struct {
	Id               int    `json:"id"`
	FoodsName        string `json:"foods_name"`
	State            int    `json:"state"`
	StateName        string `json:"state_name"`
	RunningState     int    `json:"running_state"`
	RunningStateName string `json:"running_state_name"`
	TotalCount       int    `json:"total_count"`
	Price            int    `json:"price"`
	ReviewAt         string `json:"review_at"`
	SailedCount      int    `json:"sailed_count"`
	SailedPrice      int    `json:"sailed_price"`
}
