package merchant

type SeckillListEntity struct {
	ID                int     `json:"id"`
	FoodID            int64   `json:"food_id"`
	FoodName          string  `json:"food_name"`
	FoodImg           string  `json:"food_img"`
	OriginalPrice     int     `json:"original_price"`
	Price             int64   `json:"price"`
	TotalCount        int64   `json:"total_count"`
	UserMaxOrderCount int64   `json:"user_max_order_count"`
	SaledCount        int64   `json:"saled_count"`
	BeginTime         string  `json:"begin_time"`
	EndTime           string  `json:"end_time"`
	Order             int     `json:"order"`
	State             int     `json:"state"`
	StateName         string  `json:"state_name"`
	ReviewState       int     `json:"review_state"`
	ReviewStateName   string  `json:"review_state_name"`
	CreatedAt         string  `json:"created_at"`
	DeletedAt         string  `json:"deleted_at,omitempty"`
	Creator           string  `json:"creator"`

	FoodType       uint8                      `json:"food_type"`                  // 美食类型：0:普通美食，1:规格美食，2:套餐美食
	SelectedSpec   *CommentRestaurantFoodSpec `json:"selected_spec,omitempty"`    // 已选的规格数据
	ComboFoodItems []CommentFoodComboItems    `json:"combo_food_items,omitempty"` // 套餐中的美食项
}

type SeckillListResponse struct {
	Page  int                  `json:"page"`
	Limit int                  `json:"limit"`
	Total int64               `json:"total"`
	Items []SeckillListEntity `json:"items"`
}

type SeckillDetailResponse struct {
	ID                int64  `json:"id"`
	FoodID            int64  `json:"food_id"`
	FoodName          string `json:"food_name"`
	FoodPrice         uint   `json:"food_price"`
	FoodImg           string `json:"food_img"`
	TotalCount        int64  `json:"total_count"`
	Price             int64  `json:"price"`
	BeginTime         string `json:"begin_time"`
	EndTime           string `json:"end_time"`
	CreatedAt         string `json:"created_at"`
	UpdatedAt         string `json:"updated_at"`
	State             int    `json:"state"`
	ReviewState       int    `json:"review_state"`
	UserMaxOrderCount int64  `json:"user_max_order_count"`
	StateName         string `json:"state_name"`
	ReviewStateName   string `json:"review_state_name"`

	FoodType       uint8                      `json:"food_type"`                  // 美食类型：0:普通美食，1:规格美食，2:套餐美食
	SelectedSpec   *CommentRestaurantFoodSpec `json:"selected_spec,omitempty"`    // 已选的规格数据
	ComboFoodItems []CommentFoodComboItems    `json:"combo_food_items,omitempty"` // 套餐中的美食项
} 