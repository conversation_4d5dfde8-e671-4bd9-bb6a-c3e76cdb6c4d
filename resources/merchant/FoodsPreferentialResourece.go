package merchant

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
)

// FoodsPreferentialListItemResource 优惠商品
type FoodsPreferentialListItemResource struct {
	ID               int    `json:"id"`                  // 自增编号
	FoodID           int    `json:"food_id"`             // 美食编号
	TypeName         string `json:"type_name"`           // 优惠类型名称
	FoodName         string `json:"food_name"`           // 美食名称
	FoodImage        string `json:"food_image"`          // 美食图片
	StartDate        string `json:"start_date"`          // 开始时间
	EndDate          string `json:"end_date"`            // 结束时间
	StartTime        string `json:"start_time"`          // 开始时间
	EndTime          string `json:"end_time"`            // 结束时间
	Percent          uint   `json:"percent"`             // 百分比
	DiscountPrice    uint   `json:"discount_price"`      // 优惠价格（单位：分）
	OrderCountPerDay int    `json:"order_count_per_day"` // 一天内的订购次数
	State            int    `json:"state"`               // 状态（0关闭，1开通）
	MaxOrderCount    int    `json:"max_order_count"`     // 最大订购次数
	StateLabel       string `json:"state_label"`         // 状态标签
	CreatedAt        string `json:"created_at"`          // 创建时间
	OriginalPrice    uint   `json:"original_price"`     // 原始价格
	Foods map[string]interface{}   `json:"foods"`

	FoodType       uint8                      `json:"food_type"`                  // 美食类型：0:普通美食，1:规格美食，2:套餐美食
	SelectedSpec   *CommentRestaurantFoodSpec `json:"selected_spec,omitempty"`    // 已选的规格数据
	ComboFoodItems []CommentFoodComboItems    `json:"combo_food_items,omitempty"` // 套餐中的美食项
}

type FoodsPreferentialListResource struct {
	Items []FoodsPreferentialListItemResource `json:"items"`
	Total int64                               `json:"total"`
	Page  int                                 `json:"page"`
	Limit int                                 `json:"limit"`
}

func NewFoodsPreferentialListItemResource(preferential models.FoodsPreferential, langUtil lang.LangUtil) FoodsPreferentialListItemResource {
	stateLabel := langUtil.T("unknown")
	if label, ok := langUtil.TArr("foods_preferential_states")[preferential.State]; ok {
		stateLabel = label
	}
	// 美食信息
	foodMap := make(map[string]interface{})
	foodMap["id"] = preferential.RestaurantFood.ID
	foodMap["restaurant_id"] = preferential.RestaurantFood.RestaurantID
	foodMap["foods_group_id"] = preferential.RestaurantFood.FoodsGroupId
	foodMap["name"] = tools.GetNameByLang(preferential.RestaurantFood, langUtil.Lang)
	foodMap["price"] = tools.ToFloat64(preferential.RestaurantFood.Price)/100
	foodMap["image"] = tools.CdnUrl(preferential.RestaurantFood.Image)
	foodMap["ready_time"] = preferential.RestaurantFood.ReadyTime
	foodMap["begin_time"] = tools.TimeToMinuteString(preferential.RestaurantFood.BeginTime)
	foodMap["end_time"] = tools.TimeToMinuteString(preferential.RestaurantFood.EndTime)
	foodMap["is_distribution"] = preferential.RestaurantFood.IsDistribution
	foodMap["state"] = preferential.RestaurantFood.State
	foodMap["is_recommend"] = preferential.RestaurantFood.IsRecommend
	foodMap["weight"] = preferential.RestaurantFood.Weight

	var selectedSpec *CommentRestaurantFoodSpec = nil
	comboItems := make([]CommentFoodComboItems, 0)
	if preferential.FoodType == models.RestaurantFoodsTypeSpec && preferential.SelectedSpec != nil {
		specOptions := make([]OrderRestaurantFoodSpecOption, 0)
		for _, option := range preferential.SelectedSpec.FoodSpecOptions {
			specOption := OrderRestaurantFoodSpecOption{
				ID:    option.ID,
				Name:  tools.If(langUtil.Lang == "zh", option.NameZh, option.NameUg),
				Price: option.Price,
			}
			specOptions = append(specOptions, specOption)
		}

		selectedSpec = &CommentRestaurantFoodSpec{
			ID:          preferential.SelectedSpec.ID,
			SpecOptions: specOptions,
		}
	} else if preferential.FoodType == models.RestaurantFoodsTypeCombo && preferential.RestaurantFood.ComboFoodItems != nil &&
		len(preferential.RestaurantFood.ComboFoodItems) > 0 {
		// 处理套餐类型美食
		for _, item := range preferential.RestaurantFood.ComboFoodItems {
			// 处理子美食的规格选项
			var itemSelectedSpec *CommentRestaurantFoodSpec = nil
			if item.FoodType == models.FoodsComboItemFoodTypeSpec && item.SelectedSpec != nil {
				specOptions := make([]OrderRestaurantFoodSpecOption, 0)
				for _, option := range item.SelectedSpec.FoodSpecOptions {
					specOption := OrderRestaurantFoodSpecOption{
						ID:    option.ID,
						Name:  tools.If(langUtil.Lang == "zh", option.NameZh, option.NameUg),
						Price: option.Price,
					}
					specOptions = append(specOptions, specOption)
				}

				itemSelectedSpec = &CommentRestaurantFoodSpec{
					ID:          item.SelectedSpec.ID,
					SpecOptions: specOptions,
				}
			}

			// 处理子美食信息
			restaurantFood := CommentRestaurantFoodsResponse{
				ID:               item.RestaurantFood.ID,
				Name:             tools.GetNameByLang(item.RestaurantFood, langUtil.Lang),
				NameUg: item.RestaurantFood.NameUg,
				NameZh: item.RestaurantFood.NameZh,
				Image:            tools.CdnUrl(item.RestaurantFood.Image),
				Description:      tools.If(langUtil.Lang == "zh", item.RestaurantFood.DescriptionZh, item.RestaurantFood.DescriptionUg),
				StarAvg:          item.RestaurantFood.StarAvg,
				Price:            item.RestaurantFood.Price,
				FoodQuantity:     item.RestaurantFood.FoodQuantity,
				FoodQuantityType: item.RestaurantFood.FoodQuantityType,
				SelectedSpec:     itemSelectedSpec,
			}

			comboItem := CommentFoodComboItems{
				ID:             item.ID,
				FoodType:       item.FoodType,
				FoodID:         item.FoodID,
				Count:          item.Count,
				RestaurantFood: restaurantFood,
			}
			comboItems = append(comboItems, comboItem)
		}
	}

	rtn:= FoodsPreferentialListItemResource{
		ID:               preferential.ID,
		TypeName:         tools.GetNameByLang(preferential.PreferentialType, langUtil.Lang),
		FoodName:         tools.GetNameByLang(preferential.RestaurantFood, langUtil.Lang),
		FoodImage:        tools.AddCdn(preferential.RestaurantFood.Image),
		FoodID:           preferential.RestaurantFoodsID,
		StartDate:        preferential.StartDateTime.Format("2006-01-02"),
		EndDate:          preferential.EndDateTime.Format("2006-01-02"),
		StartTime:        preferential.StartTime[0:5],
		EndTime:          preferential.EndTime[0:5],
		Percent:          preferential.Percent,
		DiscountPrice:    preferential.DiscountPrice,
		OrderCountPerDay: preferential.OrderCountPerDay,
		State:            preferential.State,
		StateLabel:       stateLabel,
		CreatedAt:        preferential.CreatedAt.Format("2006-01-02 15:04:05"),
		OriginalPrice:    preferential.RestaurantFood.Price,
		Foods:            foodMap,
		FoodType:       preferential.FoodType,
		SelectedSpec:   selectedSpec,
		ComboFoodItems: comboItems,
	}
	if preferential.MaxOrderCount!=nil {
		rtn.MaxOrderCount = *preferential.MaxOrderCount
	}
	return rtn
}

func NewFoodsPreferentialListResource(preferentials []models.FoodsPreferential, total int64, page int, limit int, langUtil lang.LangUtil) FoodsPreferentialListResource {
	items := make([]FoodsPreferentialListItemResource, 0)
	for _, preferential := range preferentials {
		items = append(items, NewFoodsPreferentialListItemResource(preferential, langUtil))
	}
	return FoodsPreferentialListResource{
		Items: items,
		Total: total,
		Page:  page,
		Limit: limit,
	}
}
