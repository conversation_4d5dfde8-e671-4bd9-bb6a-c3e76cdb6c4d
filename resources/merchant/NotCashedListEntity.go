/**
@author: captain
@since: 2022/11/7
@desc: //商户没有体现的账单信息
**/
package merchant

type NotCashedListEntity struct {
	TotalAmount int64           `json:"total_amount"`
	List        []NotCashedList `json:"list"`
}

type NotCashedList struct {
	ID         int64  `json:"id"`
	TransDate  string `json:"trans_date"`
	ResIncome  int64  `json:"res_income"`
	LeftDay    int64  `json:"left_day"`
	CanCashout int64  `json:"can_cashout"`
}
