package merchant

type MerchantLoginEntity struct {
	Admin  MerchantLoginAdmin  `json:"admin"`
	Tokens MerchantLoginTokens `json:"tokens"`
	Permissions []string `json:"permissions"`
}
type MerchantLoginAdmin struct {
	AreaID                int64    `json:"area_id"`
	BusinessType          int64    `json:"business_type"`
	DealerMobile          string `json:"dealer_mobile"`
	DealerName            string `json:"dealer_name"`
	ID                    int    `json:"id"`
	Mobile                string `json:"mobile"`
	Name                  string `json:"name"`
	ReceiveOrderTimeLimit int64    `json:"receive_order_time_limit"`
	RestaurantID          int64    `json:"restaurant_id"`
	RestaurantLogo        string `json:"restaurant_logo"`
	RestaurantName        string `json:"restaurant_name"`
	RestaurantState       int64    `json:"restaurant_state"`
	Type                  string `json:"type"`
	MerchantType          int `json:"merchant_type"`
	MerchantTypeName      string `json:"merchant_type_name"`
	MerchantTypeNameUg    string `json:"merchant_type_name_ug"`
	MerchantTypeNameZh    string `json:"merchant_type_name_zh"`
}
type MerchantLoginTokens struct {
	AccessToken  string `json:"access_token"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
}