package merchant

import (
	cmsResources "mulazim-api/resources"
)

// RefundListData 表示最终返回的数据结构
type RefundListData struct {
	Order        RefundOrderData `json:"order"`
	ReturnReason []RefundReason  `json:"return_reason"`
}

// RefundOrderData 包含订单的详细信息
type RefundOrderData struct {
	ActualPaid float64              `json:"actual_paid"`
	Shipment   float64              `json:"shipment"`
	OrderID    int64                `json:"order_id"`
	LunchBox   []RefundLunchBoxData `json:"lunch_box"`
	Foods      []RefundFoodData     `json:"foods"`
}

// RefundFoodData 表示单个食品的详细信息
type RefundFoodData struct {
	Name          string `json:"name"`
	Image         string `json:"image"`
	OrderDetailID int    `json:"order_detail_id"`
	StoreFoodID   int    `json:"store_food_id"`
	Price         uint   `json:"price"`
	RefundPrice   int    `json:"refund_price"`
	Number        uint   `json:"number"`
	LunchBoxGroupIndex int `json:"lunch_box_group_index"`

	FoodType int `json:"food_type"`
	SpecID   int `json:"spec_id"`
	SelectedSpec  []cmsResources.FoodSpecOption `json:"spec_options"`   // 加载规格美食
	ComboItems    []cmsResources.ComboItems `json:"combo_items"`
}

// RefundLunchBoxData 表示单个餐盒的详细信息
type RefundLunchBoxData struct {
	StoreFoodID         int `json:"store_food_id"`
	OrderDetailID int    `json:"order_detail_id"`
	LunchBoxID          int `json:"lunch_box_id"`
	SpecID          int `json:"spec_id"`
	LunchBoxCount       int `json:"lunch_box_count"`
	LunchBoxFee         int `json:"lunch_box_fee"`
	LunchBoxRefundPricePer int `json:"lunch_box_refund_price_per"`
	LunchBoxGroupIndex int `json:"lunch_box_group_index"`
	LunchBoxRefundPrice int `json:"lunch_box_refund_price"`
	LunchBoxAccommodate int `json:"lunch_box_accommodate"`
	MultiDiscountId int `json:"multi_discount_id"`
}

// RefundReason 表示退款原因的详细信息
type RefundReason struct {
	ID    int    `json:"id"`
	Title string `json:"title"`
	Type  int    `json:"type"`
}
