package merchant

// BusinessStatistics
// @Description:商家客户端统计
// @Time 2022-09-30 13:19:38
// <AUTHOR>
type BusinessStatistics struct {
	Total     []TotalInfo `json:"total"`
	ByPayType []ByPayType `json:"by_pay_type"`
	FoodSales []FoodSales `json:"food_sales"`
}

type TotalInfo struct {

	TotalPrice string `json:"total_price"`
	TotalCount int         `json:"total_count"`
	TotalCountMp int         `json:"total_count_mp"`
	TotalCountSelf int         `json:"total_count_self"`
	TotalPriceMp string `json:"total_price_mp"`
	TotalPriceSelf string `json:"total_price_self"`
}
type TotalInfoString struct {

	TotalPrice string `json:"total_price"`
	TotalCount int         `json:"total_count"`
	TotalCountMp int         `json:"total_count_mp"`
	TotalCountSelf int         `json:"total_count_self"`
	TotalPriceMp string `json:"total_price_mp"`
	TotalPriceSelf string `json:"total_price_self"`
}

type ByPayType struct {
	PayTypeID   int    `json:"pay_type_id"`
	PayTypeName string `json:"pay_type_name"`
	Icon        string `json:"icon"`
	OrderCount  int    `json:"order_count"`
	Price       string `json:"price"`
}

type FoodSales struct {
	FoodID     int    `json:"food_id"`
	Name       string `json:"name"`
	FoodsCount string `json:"foods_count"`
	Price      string `json:"price"`
}

// RestaurantFoodsByCategory
// @Description: 根据餐厅编号和美食分类获取美食列表
// @Time 2022-09-30 20:22:23
// <AUTHOR>
type RestaurantFoodsByCategory struct {
	ID    int    `json:"id"`
	Name  string `json:"name"`
	State int    `json:"state"`
}

// RestaurantFoods
// @Description:
// @Time 2022-09-30 21:08:18
// <AUTHOR>
type RestaurantFoods struct {
	ID              int    `json:"id"`
	FoodType uint8 `json:"food_type"`
	AllFoodsID      int    `json:"all_foods_id"`
	Name            string `json:"name"`
	Image           string `json:"image"`
	Description     int    `json:"description"`
	BeginTime       string `json:"begin_time"`
	EndTime         string `json:"end_time"`
	ReadyTime       int    `json:"ready_time"`
	IsDistribution  int    `json:"is_distribution"`
	StarAvg         string `json:"star_avg"`
	CommentCount    int    `json:"comment_count"`
	MonthOrderCount int    `json:"month_order_count"`
	Price           float64   `json:"price"`
	DiscountPrice   float64   `json:"discount_price"`
	Weight          int    `json:"weight"`
	State           int    `json:"state"`
}

type TotalInfoStat struct {
	TotalPrice string `json:"total_price"`
	TotalCount int         `json:"total_count"`
}
