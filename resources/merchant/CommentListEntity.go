package merchant

type CommentListEntity struct {
	PerPage int `json:"per_page"`
	CurrentPage int `json:"current_page"`
	LastPage int `json:"last_page"`
	Type []CommentListEntityType `json:"type"`
	Star CommentListEntityStar `json:"star"`
	Items []CommentListEntityItems `json:"items"`
}

type CommentListEntityType struct {
	Name string `json:"name"`
	Type int `json:"type"`
	Count int `json:"count"`
}

type CommentListEntityStar struct {
	StarAvg int `json:"star_avg"`
	FoodStarAvg int `json:"food_star_avg"`
	BoxStarAvg int `json:"box_star_avg"`
	ShipperAvg int `json:"shipper_avg"`
}

type CommentListEntityReplies struct {
	Id int `json:"id"`
	Type int `json:"type"`
	Text string `json:"text"`
	CreatedAt string `json:"created_at"`
}

type CommentListEntityItems struct {
	Id int `json:"id"`
	Type int `json:"type"`
	CreatedAt string `json:"created_at"`
	Star int `json:"star"`
	Text string `json:"text"`
	FoodStar int `json:"food_star"`
	BoxStar int `json:"box_star"`
	FoodName string `json:"food_name"`
	FoodId int `json:"food_id"`
	UserAvatar string `json:"user_avatar"`
	UserName string `json:"user_name"`
	Images []string `json:"images"`
	Replies []CommentListEntityReplies `json:"replies"`

	OrderDetail *CommentOrderDetail `json:"order_detail"`
}

type CommentOrderDetail struct {
	FoodType uint8 `json:"food_type"`                                          // 美食类型：0:普通美食，1:规格美食，2:套餐美食
	ComboFoodItems []CommentFoodComboItems    `json:"combo_food_items"`        // 套餐中的美食项
	SelectedSpec   *CommentRestaurantFoodSpec `json:"selected_spec,omitempty"` // 已选的规格数据
}

type CommentFoodComboItems struct {
	ID             int                            `json:"id"`              // 主键ID
	FoodType       uint8                          `json:"food_type"`       // 美食类型 (0: 普通美食, 1: 规格美食)
	FoodID         int                            `json:"food_id"`         // 美食编号
	Count          int                            `json:"count"`           // 数量
	RestaurantFood CommentRestaurantFoodsResponse `json:"restaurant_food"` // 餐厅美食
}

type CommentRestaurantFoodSpec struct {
	ID          int                             `gorm:"column:id;primaryKey;autoIncrement" json:"id"`                                                                                                // 主键ID
	SpecOptions []OrderRestaurantFoodSpecOption `gorm:"many2one:t_food_spec_detail;foreignKey:spec_id;joinForeignKey:spec_id;References:ID;joinReferences:foods_spec_option_id" json:"spec_options"` // 分类下面的美食列表
}

type CommentRestaurantFoodsResponse struct {
	ID               int                        `json:"id"`                      // 自增编号
	Name string `json:"name"`                                                    // 美食编号
	NameUg           string                     `json:"name_ug"`                 // 美食编号
	NameZh           string                     `json:"name_zh"`                 // 美食编号
	Image            string                     `json:"image"`                   // 美食图片
	Description      string                     `json:"description"`             // 美食说明
	StarAvg          string                     `json:"star_avg"`                // 平均星数
	Price            uint                       `json:"price"`                   // 出口价（单位：分）
	FoodQuantity     float64                    `json:"food_quantity"`           // 美食量
	FoodQuantityType uint8                      `json:"food_quantity_type"`      // 美食量分类；例如：1表示份、2表示个、3表示克、4表示公斤
	SelectedSpec     *CommentRestaurantFoodSpec `json:"selected_spec,omitempty"` // 已选的规格数据
}