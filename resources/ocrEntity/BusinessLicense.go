
package ocrEntity

type BusinessLicense struct {
	Code int64 `json:"code"`
	Data struct {
		Address       string `json:"address"`
		Business      string `json:"business"`
		Capital       string `json:"capital"`
		Company       string `json:"company"`
		EstablishDate string `json:"establish_date"`
		<PERSON><PERSON><PERSON>   string `json:"legal_person"`
		OrderNo       string `json:"order_no"`
		RegNum        string `json:"reg_num"`
		Type          string `json:"type"`
		ValidDate     string `json:"valid_date"`
	} `json:"data"`
	
	Msg     string `json:"msg"`
	Success bool   `json:"success"`
}


