package ocrEntity

type IDBack struct {
	Code int64 `json:"code"`
	Data struct {
		ImageURL interface{} `json:"image_url"`
		Info     struct {
			Authority string `json:"authority"`
			Timelimit string `json:"timelimit"`
			Image    string `json:"image"`
			StartTime string `json:"start_time"`
			EndTime string `json:"end_time"`

		} `json:"info"`
		OrderNo  string `json:"order_no"`
		Result   int64  `json:"result"`
		Side     string `json:"side"`
		Validity struct {
			Authority bool `json:"authority"`
			Timelimit bool `json:"timelimit"`
		} `json:"validity"`
	} `json:"data"`
	Msg     string `json:"msg"`
	Success bool   `json:"success"`
}