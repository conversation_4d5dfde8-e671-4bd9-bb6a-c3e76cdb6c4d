package ocrEntity

type FrontInfo struct {
				
		Address string `json:"address"`
		Day     string `json:"day"`
		Month   string `json:"month"`
		Name    string `json:"name"`
		Nation  string `json:"nation"`
		Number  string `json:"number"`
		Sex     string `json:"sex"`
		Year    string `json:"year"`
		Image    string `json:"image"`
	
}
type IDFront struct {
	Code int64 `json:"code"`
	Data struct {
		ImageURL string `json:"image_url"`
		FrontInfo `json:"info"`
		OrderNo  string `json:"order_no"`
		Result   int64  `json:"result"`
		Side     string `json:"side"`
		Validity struct {
			Address  bool `json:"address"`
			Birthday bool `json:"birthday"`
			Name     bool `json:"name"`
			Number   bool `json:"number"`
			Sex      bool `json:"sex"`
		} `json:"validity"`
	} `json:"data"`
	Msg     string `json:"msg"`
	Success bool   `json:"success"`
}