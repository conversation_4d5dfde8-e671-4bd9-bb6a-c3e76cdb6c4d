package ocrEntity

type CompanyInfo struct {
	Code int64 `json:"code"`
	Data struct {
		Data struct {
			Allows []struct {
				DocName string `json:"docName"`
				DocNo   string `json:"docNo"`
				EndDate string `json:"endDate"`
			} `json:"allows"`
			Base struct {
				Authority        string      `json:"authority"`
				BusinessDateFrom string      `json:"businessDateFrom"`
				BusinessDateTo   interface{} `json:"businessDateTo"`
				BusinessScope    string      `json:"businessScope"`
				Capital          string      `json:"capital"`
				CompanyAddress   string      `json:"companyAddress"`
				CompanyCode      string      `json:"companyCode"`
				CompanyName      string      `json:"companyName"`
				CompanyStatus    string      `json:"companyStatus"`
				CompanyType      string      `json:"companyType"`
				CreditNo         string      `json:"creditNo"`
				EstablishDate    string      `json:"establishDate"`
				IsOnStock        string      `json:"isOnStock"`
				IssueDate        string      `json:"issueDate"`
				KeyNo            string      `json:"keyNo"`
				Legal<PERSON>erson      string      `json:"legalPerson"`
				OrgCode          string      `json:"orgCode"`
				Province         string      `json:"province"`
				RevokeDate       interface{} `json:"revokeDate"`
				StockNumber      interface{} `json:"stockNumber"`
				StockType        interface{} `json:"stockType"`
				UpdatedDate      interface{} `json:"updatedDate"`
			} `json:"base"`
			Branches []interface{} `json:"branches"`
			Changes  []struct {
				ChangeAfter  string `json:"changeAfter"`
				ChangeBefore string `json:"changeBefore"`
				ChangeDate   string `json:"changeDate"`
				ChangeField  string `json:"changeField"`
			} `json:"changes"`
			ContactInfo struct {
				Email       string `json:"email"`
				PhoneNumber string `json:"phoneNumber"`
			} `json:"contactInfo"`
			Employees []struct {
				EmployeeName string `json:"employeeName"`
				Position     string `json:"position"`
			} `json:"employees"`
			Exceptions []interface{} `json:"exceptions"`
			Industry   struct {
				IndustryL1Name string `json:"industryL1Name"`
				IndustryL2Name string `json:"industryL2Name"`
			} `json:"industry"`
			Liquidation  interface{}   `json:"liquidation"`
			MPledges     []interface{} `json:"mPledges"`
			OriginalName []interface{} `json:"originalName"`
			Partners     []struct {
				CapiDate         interface{} `json:"capiDate"`
				InvestName       interface{} `json:"investName"`
				InvestType       string      `json:"investType"`
				ShoudDate        string      `json:"shoudDate"`
				StockCapital     string      `json:"stockCapital"`
				StockName        string      `json:"stockName"`
				StockPercent     string      `json:"stockPercent"`
				StockRealcapital string      `json:"stockRealcapital"`
				StockType        string      `json:"stockType"`
			} `json:"partners"`
			Pledges        []interface{} `json:"pledges"`
			Punishes       []interface{} `json:"punishes"`
			ShiXinItems    []interface{} `json:"shiXinItems"`
			SpotChecks     []interface{} `json:"spotChecks"`
			TaxCreditltems []interface{} `json:"taxCreditltems"`
			ZhiXingItems   []interface{} `json:"zhiXingItems"`
		} `json:"data"`
		OrderNo string `json:"orderNo"`
	} `json:"data"`
	Msg     string `json:"msg"`
	Success bool   `json:"success"`
}