package ocrEntity

type BankCardEntity struct {
	Code int64 `json:"code"`
	Data struct {
		BankIdentificationNumber string      `json:"bank_identification_number"`
		BankName                 string      `json:"bank_name"`
		CardName                 string      `json:"card_name"`
		CardNumber               string      `json:"card_number"`
		CardType                 string      `json:"card_type"`
		ExifOrientation          interface{} `json:"exif_orientation"`
		ImageID                  string      `json:"image_id"`
		OrderNo                  string      `json:"order_no"`
		Image                    string      `json:"image"`
		BankId                   int         `json:"bank_id"`
		IsBankCard				 bool   	 `json:"is_bank_card"`
		AreaCode				int 		 `json:"area_code"`
		CityCode				int 		 `json:"city_code"`
		ProvinceCode				int 		 `json:"province_code"`
		
	} `json:"data"`
	Msg     string `json:"msg"`
	Success bool   `json:"success"`
}

type BankCardRegionEntity struct {
	Code int64 `json:"code"`
	Data struct {
		Bank     string `json:"bank"`
		Province string `json:"province"`
		City     string `json:"city"`
	} `json:"data"`
	Msg     string `json:"msg"`
	Success bool   `json:"success"`
}