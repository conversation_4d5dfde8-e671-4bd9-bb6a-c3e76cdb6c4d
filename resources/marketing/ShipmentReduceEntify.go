package marketing

type FormatMerchantShipmentReduceList struct {
	ID              int    `json:"id"`
	Name            string `json:"name"`
	State           int    `json:"state"`
	StateName       string `json:"state_name"`
	CreatorType     int    `json:"creator_type"`
	CreatorTypeName string `json:"creator_type_name"` // 谁创建的 1.代理 2:商家
	TimeOutDay      string `json:"time_out_day"`
	BeginDate       string `json:"begin_date"`
	EndDate         string `json:"end_date"`
	GroupType       int    `json:"group_type"`
	GroupTypeName   string `json:"group_type_name"`
}

type FormatMerchantShipmentReduceDetail struct {
	ID                  int                      `json:"id"`
	Name                string                   `json:"name"`
	NameUg              string                   `json:"name_ug"`
	NameZh              string                   `json:"name_zh"`
	CreatorType         int                      `json:"creator_type"`      // 谁创建的 1.代理 2:商家
	CreatorTypeName     string                   `json:"creator_type_name"` // 谁创建的 1.代理 2:商家
	GroupType           int                      `json:"group_type"`        // 谁创建的 1.代理 2:商家
	BeginDate           string                   `json:"begin_date"`
	EndDate             string                   `json:"end_date"`
	FullWeekState       int                      `json:"full_week_state"`
	FullTimeState       int                      `json:"full_time_state"`
	Day                 int                      `json:"day"`
	TimeLines           []TimeLines              `json:"time_lines"`            // 自动延续 0:  否，1：是
	AutoContinue        int                      `json:"auto_continue"`         // 自动延续 0:  否，1：是
	Steps               []map[string]interface{} `json:"steps"`                 // 价格阶梯
	MinDeliveryPrice    int                      `json:"min_delivery_price"`    // 运费减免活动： 起送价
	AttendanceState     int                      `json:"attendance_state"`      //  1：新建，2：已发送邀请，3：已参加，4：拒绝参加
	AttendanceStateName string                   `json:"attendance_state_name"` // 1：新建，2：已发送邀请，3：已参加，4：拒绝参加
	TimeOutDay          string                   `json:"time_out_day"`
	WebUrl              string                   `json:"web_url"`
	State               int                      `json:"state"`
	StateName           string                   `json:"state_name"`
	CreatedAt           string                   `json:"created_at"`
	AttendanceAt        string                   `json:"attendance_at"`
}
type TimeLines struct {
	TimeStart string `json:"time_start"`
	TimeEnd   string `json:"time_end"`
}
