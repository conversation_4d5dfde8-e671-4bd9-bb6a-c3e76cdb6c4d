package resources

import (
	"database/sql/driver"
	"encoding/binary"
	"fmt"
	"math"
	"strings"

	"github.com/paulmach/orb"
)

type DeliveryRestaurantList struct {
	CityID         int    `json:"city_id"`
	AreaID         int    `json:"area_id"`
	AreaName         string    `json:"area_name"`
	RestaurantID   int    `json:"restaurant_id"`
	RestaurantName string `json:"restaurant_name"`
	RestaurantLogo string `json:"restaurant_logo"`
	Type           uint8  `json:"type"`
	AreaRunningState uint8  `json:"area_running_state"`
	OrderAddTime     string `json:"order_add_time"`
	Radius           int `json:"radius"`
	Option          uint8  `json:"option"`
	FeeState         uint8  `json:"fee_state"`
	UpdatedAdminID   int    `json:"updated_admin_id"`
	Polygon        string `json:"polygon"`
	Lat            float64 `json:"lat"`
	Lng            float64 `json:"lng"`
	FeeCount       int `json:"fee_count"`
}


// Polygon 包装 orb.Polygon 以支持数据库操作
type Polygon struct {
	orb.Polygon
}

// Scan 实现 sql.Scanner 接口
func (p *Polygon) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case string:
		return p.scanWKT(v)
	case []uint8:
		// Try WKT first
		if len(v) > 0 && v[0] == 'P' {
			return p.scanWKT(string(v))
		}
		// If not WKT, assume it's MySQL's binary format
		return p.scanBinary(v)
	default:
		return fmt.Errorf("expected string or []uint8, got %T", value)
	}
}


// scanWKT 解析WKT格式的polygon数据
func (p *Polygon) scanWKT(wkt string) error {
	if len(wkt) < 10 || wkt[:8] != "POLYGON(" || wkt[len(wkt)-2:] != "))" {
		return fmt.Errorf("invalid WKT polygon format: %s", wkt)
	}

	// 提取坐标点
	coords := wkt[9 : len(wkt)-2]
	points := make(orb.Ring, 0)

	// 解析坐标点
	for _, pointStr := range strings.Split(coords, ",") {
		var x, y float64
		_, err := fmt.Sscanf(strings.TrimSpace(pointStr), "%f %f", &x, &y)
		if err != nil {
			return fmt.Errorf("failed to parse WKT polygon coordinates: %v", err)
		}
		points = append(points, orb.Point{x, y})
	}

	// 创建多边形
	p.Polygon = orb.Polygon{points}
	return nil
}

// scanBinary 解析MySQL二进制格式的polygon数据
func (p *Polygon) scanBinary(data []uint8) error {
	if len(data) < 13 {  // 至少需要头部信息
		return fmt.Errorf("binary polygon data too short")
	}

	// Skip header (4 bytes for SRID + 1 byte for byteOrder + 4 bytes for type + 4 bytes for numRings)
	pos := 13
	
	// Read number of points (4 bytes)
	if len(data) < pos+4 {
		return fmt.Errorf("binary polygon data truncated")
	}
	numPoints := int(data[pos]) | int(data[pos+1])<<8 | int(data[pos+2])<<16 | int(data[pos+3])<<24
	pos += 4

	points := make(orb.Ring, 0, numPoints)
	
	// Read points
	for i := 0; i < numPoints && pos+16 <= len(data); i++ {
		// MySQL stores coordinates as IEEE 754 double-precision floating-point
		x := math.Float64frombits(binary.LittleEndian.Uint64(data[pos : pos+8]))
		y := math.Float64frombits(binary.LittleEndian.Uint64(data[pos+8 : pos+16]))
		points = append(points, orb.Point{x, y})
		pos += 16
	}

	if len(points) < 3 {
		return fmt.Errorf("polygon must have at least 3 points")
	}

	p.Polygon = orb.Polygon{points}
	return nil
}

// float64frombytes converts 8 bytes to float64 in little-endian
func float64frombytes(bytes []byte) float64 {
	bits := uint64(bytes[0]) | uint64(bytes[1])<<8 | uint64(bytes[2])<<16 | uint64(bytes[3])<<24 |
		uint64(bytes[4])<<32 | uint64(bytes[5])<<40 | uint64(bytes[6])<<48 | uint64(bytes[7])<<56
	return float64(bits)
}

// Value 实现 driver.Valuer 接口
func (p Polygon) Value() (driver.Value, error) {
	if len(p.Polygon) == 0 {
		return nil, nil
	}

	// 获取第一个环（外环）
	ring := p.Polygon[0]
	if len(ring) < 3 {
		return nil, fmt.Errorf("polygon must have at least 3 points")
	}

	// 确保多边形是闭合的
	if ring[0] != ring[len(ring)-1] {
		ring = append(ring, ring[0])
	}

	// 构建 MySQL POLYGON 格式的字符串
	var points []string
	for _, point := range ring {
		// 注意：MySQL 的 POLYGON 格式要求经度在前，纬度在后
		points = append(points, fmt.Sprintf("%f %f", point[0], point[1]))
	}

	// 构建完整的 POLYGON 字符串
	polygonStr := fmt.Sprintf("POLYGON((%s))", strings.Join(points, ","))
	return polygonStr, nil
}

type DeliveryAreaList struct {
	CityID         int    `json:"city_id"`
	AreaID         int    `json:"area_id"`
	AreaName         string    `json:"area_name"`
	CityName         string    `json:"city_name"`
	Name         string    `json:"name"`
	Notice         string    `json:"notice"`
	Type           uint8  `json:"type"`
	Polygon        string `json:"polygon"`
	AreaRunningState uint8  `json:"area_running_state"`
	OrderAddTime     string `json:"order_add_time"`
	Radius           string `json:"radius"`
	Option          uint8  `json:"option"`
	FeeState         uint8  `json:"fee_state"`
	UpdatedAdminID   int    `json:"updated_admin_id"`
}
