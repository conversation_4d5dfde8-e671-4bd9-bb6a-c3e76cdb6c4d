package resources

// 套餐美食 子美食规格项 返回数据结构体
type FoodSpecOption struct {
	ID    int    `json:"id"`      // 主键ID
	Name  string `json:"name"` // 名称
	NameUg  string `json:"name_ug"` // 名称
	NameZh  string `json:"name_zh"` // 名称
	Price int    `json:"price"`   // 价格（可为空）
	SpecTypeID int    `json:"spec_type_id"`   // 价格（可为空）
}


// 套餐美食 子美食规格项 请求数据结构体
type FoodSpecTypeRes struct {
	ID    int    `json:"id"`      // 主键ID
	PriceType int    `json:"price_type"`   // 价格（可为空）
	FoodID int    `json:"food_id"`   // 价格（可为空）
	Name string `json:"name"` // 名称
	NameUg  string `json:"name_ug"` // 名称
	NameZh  string `json:"name_zh"` // 名称
	State uint8 `json:"state"` // 是否可选状态
	Options []FoodSpecTypeOptions `json:"options"`   // 价格（可为空）
}

type FoodSpecTypeOptions struct {
	ID    int    `json:"id"`      // 主键ID
	Name string `json:"name"`     // 名称
	NameUg  string `json:"name_ug"` // 名称
	NameZh  string `json:"name_zh"` // 名称
	Price int    `json:"price"`   // 价格（可为空）
	State uint8 `json:"state"` // 是否可选状态
	IsSelected int `json:"is_selected"`
	SpecTypeID int    `json:"spec_type_id"`   // 价格（可为空）
}