package resources
type ShipperRestSpotEntity struct {
    
	CityId int `json:"city_id" binding:"required"`
    AreaId int `json:"area_id" binding:"required"`
    AdminId int `json:"admin_id"  `
    Items []ShipperRestSpotItemEntity `json:"items" `
}

type ShipperRestSpotUpdateEntity struct {
    
	CityId int `json:"city_id" binding:"required"`
    AreaId int `json:"area_id" binding:"required"`
    AdminId int `json:"admin_id"  `
    Items []ShipperRestSpotItemUpdateEntity `json:"items" `
}
type ShipperRestSpotItemEntity struct {
    
    Lat string `json:"lat" binding:"required"`
    Lng string `json:"lng" binding:"required"`
    NameUg string `json:"name_ug" `
    NameZh string `json:"name_zh" `
    DescUg string `json:"desc_ug" `
    DescZh string `json:"desc_zh" `
    State int `json:"state" `
}


type ShipperRestSpotItemUpdateEntity struct {
    ID int `json:"id"   binding:"required"`
    Lat string `json:"lat" binding:"required"`
    Lng string `json:"lng" binding:"required"`
    NameUg string `json:"name_ug" `
    NameZh string `json:"name_zh" `
    DescUg string `json:"desc_ug" `
    DescZh string `json:"desc_zh" `
    State int `json:"state" `
}

type ShipperRestSpotItemResponseEntity struct {
    ID int `json:"id"   binding:"required"`
    CityId int `json:"city_id" binding:"required"`
    AreaId int `json:"area_id" binding:"required"`
    CityName string `json:"city_name"`
    AreaName string `json:"area_name"`
    Lat string `json:"lat" binding:"required"`
    Lng string `json:"lng" binding:"required"`
    NameUg string `json:"name_ug" `
    NameZh string `json:"name_zh" `
    Name string `json:"name" `
    DescUg string `json:"desc_ug" `
    DescZh string `json:"desc_zh" `
    Desc string `json:"desc"`
    State int `json:"state" `
    CreatedAt string `json:"created_at"`
    UpdatedAt string `json:"updated_at"`
    StateName string `json:"state_name"`
    Distance float64 `json:"distance"`
    Duration float64 `json:"duration"`
    AdminName string `json:"admin_name"`
}


type ShipperRestSpotItemResponseEntityCms struct {
    Items []ShipperRestSpotItemResponseEntity `json:"items"`
    Total int `json:"total"`
    Page int `json:"page"`
    Limit int `json:"limit"`
}
