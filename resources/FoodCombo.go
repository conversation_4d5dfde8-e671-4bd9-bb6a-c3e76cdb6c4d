package resources

type ComboItems struct {
	ID                  int              `json:"id"`             // 主键ID
	SpecID              int              `json:"spec_id"`        // 已选规格编号
	FoodID              int              `json:"food_id"`        // 已选规格编号
	Count               int              `json:"count"`          // 数量
	Price               int              `json:"price"`          // 加载普通美食
	FoodName            string           `json:"food_name"`      // 加载普通美食
	FoodNameUg          string           `json:"food_name_ug"`   // 加载普通美食
	FoodNameZh          string           `json:"food_name_zh"`   // 加载普通美食
	FoodImage           string           `json:"food_image"`     // 加载普通美食
	SelectedSpec        []FoodSpecOption `json:"spec_options"`   // 加载规格美食
	BeginTime           string           `json:"begin_time"`     // 加载普通美食
	EndTime             string           `json:"end_time"`       // 加载普通美食
	FoodType            int              `json:"food_type"`      // 加载普通美食
	Image               string           `json:"image"`          // 加载普通美食
	OriginalImage       string           `json:"original_image"` // 加载普通美食
	ReadyTime           int              `json:"ready_time"`     // 加载普通美食
	LunchBoxID          int              `json:"lunch_box_id"`
	LunchBoxFee         int              `json:"lunch_box_fee"`
	LunchBoxAccommodate int              `json:"lunch_box_accommodate"`
}
