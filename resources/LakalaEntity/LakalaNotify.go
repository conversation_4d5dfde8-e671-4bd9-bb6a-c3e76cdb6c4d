package LakalaEntity

type LakalaNotify struct {
	EventType string `form:"event_type" json:"event_type" `
	Event     string `form:"event" json:"event" `
	AppId     string `form:"app_id" json:"app_id" `
	TimeStamp string `form:"timestamp" json:"timestamp"`
	Version   string `form:"version" json:"version"`
}

type Event struct {
	MemberNo    string `form:"member_no" json:"member_no" `
	OutMemberNo string `form:"out_member_no" json:"out_member_no" `
}
type MemberResponse struct {
	// Response MemberAdd  `form:"response" json:"response" `
	Response string `form:"response" json:"response" `
	Sign     string `form:"sign" json:"sign" `
}
type MemberAdd struct {
	Status    string          `form:"status" json:"status" `
	ErrorCode string          `form:"error_code" json:"error_code" `
	Message   string          `form:"error_code" json:"message" `
	Result    MemberAddResult //string `form:"result" json:"result" `
}

type MemberAddResult struct {
	Url string `form:"url" json:"url" `
}

type MemberCardQueryResult struct {
	Response string `form:"response" json:"response" `
	// Response MemberQueryResultResponse `form:"response" json:"response" `
	Sign string `form:"sign" json:"sign" `
}

type MemberQueryResult struct {
	Response string `form:"response" json:"response" `
	Sign     string `form:"sign" json:"sign" `
}

type MemberQueryResultResult struct {
	AccountOpeningLicenseImgNo string `form:"account_opening_license_img_no" json:"account_opening_license_img_no"`
	AgreementImgNo             string `form:"agreement_img_no" json:"agreement_img_no"`
	BusinessLicense            string `form:"business_license" json:"business_license"`
	BusinessLicenseImgNo       string `form:"business_license_img_no" json:"business_license_img_no"`
	FacadeNo                   string `form:"facade_no" json:"facade_no"`
	IdentityBackImgNo          string `form:"identity_back_img_no" json:"identity_back_img_no"`
	IdentityFrontImgNo         string `form:"identity_front_img_no" json:"identity_front_img_no"`
	IdentityName               string `form:"identity_name" json:"identity_name"`
	IdentityNo                 string `form:"identity_no" json:"identity_no"`
	IdentityType               string `form:"identity_type" json:"identity_type"`
	LinkmanBackImgNo           string `form:"linkman_back_img_no" json:"linkman_back_img_no"`
	LinkmanFrontImgNo          string `form:"linkman_front_img_no" json:"linkman_front_img_no"`
	LinkmanIdentityNo          string `form:"linkman_identity_no" json:"linkman_identity_no"`
	MemberCategory             int    `form:"member_category" json:"member_category"`
	MemberNo                   string `form:"member_no" json:"member_no"`
	MemberStatus               int    `form:"member_status" json:"member_status"`
	MemberType                 int    `form:"member_type" json:"member_type"`
	Mobile                     string `form:"mobile" json:"mobile"`
	MobileBind                 bool   `form:"mobile_bind" json:"mobile_bind"`
	Name                       string `form:"name" json:"name"`
	RealStatus                 int    `form:"real_status" json:"real_status"`
}
type MemberQueryResultResponse struct {
	Status string                  `form:"status" json:"status" `
	Result MemberQueryResultResult `form:"result" json:"result" `
}
type MemberCardQueryResultResponse struct {
	Status string                      `form:"status" json:"status" `
	Result MemberCardQueryResultResult `form:"result" json:"result" `
}
type MemberCardQueryResultResult struct {
	AccountOpeningLicenseImgNo string `form:"account_opening_license_img_no" json:"account_opening_license_img_no"`
	AgreementImgNo             string `form:"agreement_img_no" json:"agreement_img_no"`
	BusinessLicense            string `form:"business_license" json:"business_license"`
	BusinessLicenseImgNo       string `form:"business_license_img_no" json:"business_license_img_no"`
	FacadeNo                   string `form:"facade_no" json:"facade_no"`
	IdentityBackImgNo          string `form:"identity_back_img_no" json:"identity_back_img_no"`
	IdentityFrontImgNo         string `form:"identity_front_img_no" json:"identity_front_img_no"`
	IdentityName               string `form:"identity_name" json:"identity_name"`
	IdentityNo                 string `form:"identity_no" json:"identity_no"`
	IdentityType               string `form:"identity_type" json:"identity_type"`
	LinkmanBackImgNo           string `form:"linkman_back_img_no" json:"linkman_back_img_no"`
	LinkmanFrontImgNo          string `form:"linkman_front_img_no" json:"linkman_front_img_no"`
	LinkmanIdentityNo          string `form:"linkman_identity_no" json:"linkman_identity_no"`
	MemberCategory             int    `form:"member_category" json:"member_category"`
	MemberNo                   string `form:"member_no" json:"member_no"`
	MemberStatus               int    `form:"member_status" json:"member_status"`
	MemberType                 int    `form:"member_type" json:"member_type"`
	Mobile                     string `form:"mobile" json:"mobile"`
	MobileBind                 bool   `form:"mobile_bind" json:"mobile_bind"`
	Name                       string `form:"name" json:"name"`
	RealStatus                 int    `form:"real_status" json:"real_status"`
}

type BalanceSplitResult struct {
	Response  string `form:"response" json:"response" `
	Sign      string `form:"sign" json:"sign" `
	ErrorCode string `form:"error_code" json:"error_code"`
	Message   string `form:"message" json:"message"`
}

type BalanceSplitResultResponse struct {
	Status    string                   `form:"status" json:"status" `
	Result    BalanceSplitResultResult `form:"result" json:"result" `
	ErrorCode string                   `form:"error_code" json:"error_code"`
	Message   string                   `form:"message" json:"message"`
}

type BalanceSplitResultResult struct {
	IsConfirm       bool                          `form:"is_confirm" json:"is_confirm"`
	OrderNo         string                        `form:"order_no" json:"order_no"`
	SplitSeqNo      string                        `form:"split_seq_no" json:"split_seq_no"`
	SplitRuleResult []BalanceSplitResultSplitRule `form:"split_rule_result" json:"split_rule_result"`
}

type BalanceSplitResultSplitRule struct {
	MemberId    string `form:"member_id" json:"member_id"`
	TotalAmount int    `form:"total_amount" json:"total_amount"`
}

type BalanceSplitCancelResult struct {
	Response string `form:"response" json:"response" `
	Sign     string `form:"sign" json:"sign" `
}

type LakalaResult struct {
	Response  string `form:"response" json:"response" `
	Sign      string `form:"sign" json:"sign" `
	ErrorCode string `form:"error_code" json:"error_code"`
	Message   string `form:"message" json:"message"`
}

type LakalaBankResult struct {
	Status string `form:"status" json:"status" `
	Result LakalaBankResultResult
}

type LakalaBankResultResult struct {
	BankCardList string `form:"bank_card_list" json:"bank_card_list" `
}

type WithDrawResultResponse struct {
	Status    string               `form:"status" json:"status" `
	Result    WithDrawResultResult `form:"result" json:"result" `
	ErrorCode string               `form:"error_code" json:"error_code"`
	Message   string               `form:"message" json:"message"`
}

type WithDrawResultResult struct {
	OrderNo      string `form:"order_no" json:"order_no"`
	OrderStatus  int    `form:"order_status" json:"order_status"`
	ErrorMessage string `form:"error_message" json:"error_message"`
}

type LakalaDefaultResult struct {
	Response  string `form:"response" json:"response" `
	Sign      string `form:"sign" json:"sign" `
	ErrorCode string `form:"error_code" json:"error_code"`
	Message   string `form:"message" json:"message"`
}

type BalanceCancelResponse struct {
	Status    string                         `form:"status" json:"status" `
	Result    BalanceSplitCancelResultResult `form:"result" json:"result" `
	ErrorCode string                         `form:"error_code" json:"error_code"`
	Message   string                         `form:"message" json:"message"`
}

type BalanceSplitCancelResultResult struct {
	OrderNo    string `form:"order_no" json:"order_no"`
	SplitSeqNo string `form:"split_seq_no" json:"split_seq_no"`
}

type BalanceResultResponse struct {
	Status    string              `form:"status" json:"status" `
	Result    BalanceResultResult `form:"result" json:"result" `
	ErrorCode string              `form:"error_code" json:"error_code"`
	Message   string              `form:"message" json:"message"`
}
type BalanceResultResult struct {
	TotalAmount     int `form:"total_amount" json:"total_amount" `
	AvailableAmount int `form:"available_amount" json:"available_amount" `
	FreezeAmount    int `form:"freeze_amount" json:"freeze_amount" `
}

type PayQueryResult struct {
	Response string `form:"response" json:"response" `
	Sign     string `form:"sign" json:"sign" `
}

type QueryResultResponse struct {
	Status    string              `form:"status" json:"status" `
	Result    QueryResultResult `form:"result" json:"result" `
	ErrorCode string              `form:"error_code" json:"error_code"`
	Message   string              `form:"message" json:"message"`
}

type QueryResultResult struct {
	 OrderNo string `form:"order_no" json:"order_no"`
	 OrderStatus int `form:"order_status" json:"order_status"`
	 OutOrderNo  string `form:"out_order_no" json:"out_order_no"`
	 OrderAmount int `form:"order_amount" json:"order_amount"`
	 PayAmount int `form:"pay_amount" json:"pay_amount"`
	 SplitAmount int `form:"split_amount" json:"split_amount"`
	 RefundAmount int `form:"refund_amount" json:"refund_amount"`
	 PayList    []QueryResultPayList `form:"pay_list" json:"pay_list"`
	 RefundList []QueryResultRefundList `form:"refund_list" json:"refund_list"`
	 SplitList []QueryResultSplitList	`form:"split_list" json:"split_list"`

}

type QueryResultPayList struct {
	OutRequestNo string `form:"out_request_no" json:"out_request_no"`
	PaySeqNo string `form:"pay_seq_no" json:"pay_seq_no"`
	PayStatus int `form:"pay_status" json:"pay_status"`
	PayMethod QueryResultPayListPayMethod `form:"pay_method" json:"pay_method"`
	Amount int `form:"amount" json:"amount"`
	QztChannelPayRequestNo string `form:"qzt_channel_pay_request_no" json:"qzt_channel_pay_request_no"`
	ChannelTradeNo string `form:"channel_trade_no" json:"channel_trade_no"`
	ChannelSeqNo string `form:"channel_seq_no" json:"channel_seq_no"`
	PayChannelTradeNo string `form:"pay_channel_trade_no" json:"pay_channel_trade_no"`
	PayTime string `form:"pay_time" json:"pay_time"`
	ThirdPartyPayment string `form:"third_party_payment" json:"third_party_payment"` 	
}

type QueryResultPayListPayMethod struct {

}
type QueryResultRefundList struct {
	RefundStatus int `form:"refund_status" json:"refund_status"`
	OutRequestNo string `form:"out_request_no" json:"out_request_no"`
	Amount int `form:"amount" json:"amount"`
	RefundSeqNo string `form:"refund_seq_no" json:"refund_seq_no"`
	QztChannelPayRequestNo string `form:"qzt_channel_pay_request_no" json:"qzt_channel_pay_request_no"`
	ChannelTradeNo string `form:"channel_trade_no" json:"channel_trade_no"`
	ChannelSeqNo string `form:"channel_seq_no" json:"channel_seq_no"`
	PayChannelTradeNo string `form:"pay_channel_trade_no" json:"pay_channel_trade_no"`
	RefundTime string `form:"refund_time" json:"refund_time"`
}
type QueryResultSplitList struct {
	SplitStatus int `form:"split_status" json:"split_status"`
	OutRequestNo string `form:"out_request_no" json:"out_request_no"`
	Amount int `form:"amount" json:"amount"`
	SplitSeqNo string `form:"split_seq_no" json:"split_seq_no"`
	MemberIdNo string `form:"member_id_no" json:"member_id_no"`
	SplitTime string `form:"split_time" json:"split_time"`	 	 
}	




type PayInfo struct {
	PaySign      string `json:"pay_sign"`
	NonceStr     string `json:"nonce_str"`
	AppId        string `json:"appId"`
	Timestamp    string `json:"timestamp"`
	Package      string `json:"package"`
	SignType     string `json:"sign_type"`
	PrepayID     string `json:"prepay_id"`
	RedirectURL  string `json:"redirect_url"`
}

// OrderResult defines the structure for the "result" part of the JSON.
type PayResult struct {
	OrderNo      string    `json:"order_no"`
	PayStatus    int       `json:"pay_status"`
	OrderStatus  int       `json:"order_status"`
	PaySeqNo     string    `json:"pay_seq_no"`
	BuyerMemberNo string   `json:"buyer_member_no"`
	PayInfo      PayInfo   `json:"pay_info"`
}

// ResponseData defines the complete structure for the provided JSON data.
type PayResponseData struct {
	Status string     `json:"status"`
	Result PayResult `json:"result"`
}

type PayResponse struct {
	Response string `form:"response" json:"response" `
	Sign     string `form:"sign" json:"sign" `
}