package response

import (
	"mulazim-api/models"
	"mulazim-api/resources/merchant"
)

// MarketingDetail 满减活动详情响应结构
type MarketingDetail struct {
	ID            int    `json:"id"`
	RestaurantID  int    `json:"restaurant_id"`
	MarketingType int    `json:"marketing_type"`
	Type          int    `json:"type"`
	NameUg        string `json:"name_ug"`
	NameZh        string `json:"name_zh"`
	BeginDate     string `json:"begin_date"` // "Y-m-d"
	EndDate       string `json:"end_date"`   // "Y-m-d"
	CreatedAt     string `json:"created_at"` // "Y-m-d H:i:s"
	State         int    `json:"state"`
	StateName     string `json:"state_name"`

	MarketTime []MarketTimeItem `json:"market_time"` // 时间段列表
	Day1       int              `json:"day1"`
	Day2       int              `json:"day2"`
	Day3       int              `json:"day3"`
	Day4       int              `json:"day4"`
	Day5       int              `json:"day5"`
	Day6       int              `json:"day6"`
	Day7       int              `json:"day7"`

	Steps []MarketingStepItem `json:"steps"`
	//StepItems       []MarketingStepItem `json:"step_items"`
	PriceMax        *int   `json:"price_max"`
	PriceReduceMax  *int   `json:"price_reduce_max"`
	CreatorType     int    `json:"creator_type"`
	CreatorTypeName string `json:"creator_type_name"`

	FoodsListV2 []MarketingFoodInfo      `json:"foods_list_v2"`
	FoodsJson   models.FoodItemJsonSlice `json:"foods_json"` // 美食列表，包括 food_id 美食ID，food_type 美食类型，spec_id 美食规格ID
}

type MarketTimeItem struct {
	TimeStart string `json:"time_start"`
	TimeEnd   string `json:"time_end"`
}

type MarketingFoodInfo struct {
	ID           int                                 `json:"id"`
	Name         string                              `json:"name"`
	Image        string                              `json:"image"`
	ComboItems []merchant.CommentFoodComboItems `json:"combo_items,omitempty"`
	SelectedSpec *merchant.CommentRestaurantFoodSpec `json:"selected_spec,omitempty"`
}

type MarketingStepItem struct {
	PriceStart  float64 `json:"price_start"`
	PriceReduce float64 `json:"price_reduce"`
	StepTitle   string  `json:"step_title"`
}
