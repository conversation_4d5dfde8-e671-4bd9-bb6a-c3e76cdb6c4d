package response

// OrderRank 订单等级详情响应结构
type OrderRank struct {
	OrderPrice    int64       `json:"order_price"`	//订单价格	
	OrderCount     int64      `json:"order_count"`	//订单数量
	CustomerCount int64      `json:"customer_count"`	//客户数量
	ShipperCount  int64	`json:"shipper_count"`	//配送员数量
	AvgDeliveryTime float64 `json:"avg_delivery_time"`	// 平均配送时间

	OrderPrice2    int64       `json:"order_price2"`	//订单价格2
	OrderCount2     int64      `json:"order_count2"`	//订单数量2
	CustomerCount2 int64      `json:"customer_count2"`	//客户数量2
	ShipperCount2  int64	`json:"shipper_count2"`	//配送员数量2
	AvgDeliveryTime2 float64 `json:"avg_delivery_time2"`	// 平均配送时间2

	OrderPriceGrowthRate float64 `json:"order_price_growth_rate"`	// 订单价格涨幅
	OrderCountGrowthRate float64 `json:"order_count_growth_rate"`	// 订单数量涨幅
	CustomerCountGrowthRate float64 `json:"customer_count_growth_rate"` //客户数量涨幅
	ShipperCountGrowthRate float64 `json:"shipper_count_growth_rate"`//配送员数量涨幅
	AvgDeliveryTimeGrowthRate float64 `json:"avg_delivery_time_growth_rate"` // 平均配送时间涨幅 


	OrderRankData OrderRankData `json:"order_rank_data"`	// 订单等级详情
	CustomerRankData CustomerRankData `json:"customer_rank_data"` // 客户等级详情
	ShipperRankData	ShipperRankData `json:"shipper_rank_data"` //配送员等级详情
	OrderCustomerData OrderCustomerData `json:"order_customer_data"` // 订单/客户分布
	OrderShipperData OrderShipperData `json:"order_shipper_data"` // 订单/配送员分布
	OrderLateData OrderLateData `json:"order_late_data"` //订单迟到数据


}

type RankGroup struct {
	RankCount int64 `gorm:"column:rank_count"`
	Rank int `gorm:"column:ranks"`
}
type OrderRankData struct { 
	OrderCount int64 `json:"order_count"`
	// OrderPrice int64 `json:"order_price"`

	RankOneCount int64 `json:"rank_one_count"`
	RankTwoCount int64 `json:"rank_two_count"`
	RankThreeCount int64 `json:"rank_three_count"`
	RankFourCount int64 `json:"rank_four_count"`
	RankFiveCount int64 `json:"rank_five_count"`

	OrderCount2 int64 `json:"order_count2"`
	RankOneCount2 int64 `json:"rank_one_count2"`
	RankTwoCount2 int64 `json:"rank_two_count2"`
	RankThreeCount2 int64 `json:"rank_three_count2"`
	RankFourCount2 int64 `json:"rank_four_count2"`
	RankFiveCount2 int64 `json:"rank_five_count2"`

	

	RankOneGrowthRate float64 `json:"rank_one_growth_rate"` 
	RankTwoGrowthRate float64 `json:"rank_two_growth_rate"`
	RankThreeGrowthRate float64 `json:"rank_three_growth_rate"`
	RankFourGrowthRate float64 `json:"rank_four_growth_rate"`
	RankFiveGrowthRate float64 `json:"rank_five_growth_rate"`
}

type CustomerRankData struct { 

	CustomerCount int64 `json:"customer_count"`
	RankOneCount int64 `json:"rank_one_count"`
	RankTwoCount int64 `json:"rank_two_count"`
	RankThreeCount int64 `json:"rank_three_count"`
	RankFourCount int64 `json:"rank_four_count"`
	RankFiveCount int64 `json:"rank_five_count"`

	CustomerCount2 int64 `json:"customer_count2"`
	RankOneCount2 int64 `json:"rank_one_count2"`
	RankTwoCount2 int64 `json:"rank_two_count2"`
	RankThreeCount2 int64 `json:"rank_three_count2"`
	RankFourCount2 int64 `json:"rank_four_count2"`
	RankFiveCount2 int64 `json:"rank_five_count2"`

	

	RankOneGrowthRate float64 `json:"rank_one_growth_rate"` 
	RankTwoGrowthRate float64 `json:"rank_two_growth_rate"`
	RankThreeGrowthRate float64 `json:"rank_three_growth_rate"`
	RankFourGrowthRate float64 `json:"rank_four_growth_rate"`
	RankFiveGrowthRate float64 `json:"rank_five_growth_rate"`

	DayItems []CustomerRankDataDayItems `json:"day_items"`

}

type CustomerRankDataDayItems struct { 
	Day string `json:"day" gorm:"column:day"`
	RankOneCount int `json:"rank_one_count"	gorm:"column:rank_one_count"`
	RankTwoCount int `json:"rank_two_count" gorm:"column:rank_two_count"`
	RankThreeCount int `json:"rank_three_count" gorm:"column:rank_three_count"`
	RankFourCount int `json:"rank_four_count" 	gorm:"column:rank_four_count"`
	RankFiveCount int `json:"rank_five_count" gorm:"column:rank_five_count"`
}



type ShipperRankData struct { 

	ShipperCount int64 `json:"shipper_count"`
	RankOneCount int64 `json:"rank_one_count"`
	RankTwoCount int64 `json:"rank_two_count"`
	RankThreeCount int64 `json:"rank_three_count"`
	RankFourCount int64 `json:"rank_four_count"`
	RankFiveCount int64 `json:"rank_five_count"`

	ShipperCount2 int64 `json:"shipper_count2"`
	RankOneCount2 int64 `json:"rank_one_count2"`
	RankTwoCount2 int64 `json:"rank_two_count2"`
	RankThreeCount2 int64 `json:"rank_three_count2"`
	RankFourCount2 int64 `json:"rank_four_count2"`
	RankFiveCount2 int64 `json:"rank_five_count2"`

	

	RankOneGrowthRate float64 `json:"rank_one_growth_rate"` 
	RankTwoGrowthRate float64 `json:"rank_two_growth_rate"`
	RankThreeGrowthRate float64 `json:"rank_three_growth_rate"`
	RankFourGrowthRate float64 `json:"rank_four_growth_rate"`
	RankFiveGrowthRate float64 `json:"rank_five_growth_rate"`

	DayItems []CustomerRankDataDayItems `json:"day_items"`

}

type OrderDataGorm struct {
	OrderCount int64 `gorm:"column:order_count"`
	OrderPrice int64 `gorm:"column:order_price"`
	CustomerCount int64 `gorm:"column:customer_count"`
	ShipperCount int64 `gorm:"column:shipper_count"`
	DeliveryTime int64 `gorm:"column:delivery_time"`


}


type OrderRankGorm struct {
	OrderId int64 `gorm:"column:order_id"`
	OrderRank int `gorm:"column:order_rank"`
	UserRank int64 `gorm:"column:user_rank"`
	OrderPrice int64 `gorm:"column:order_price"`
	
}

type OrderCustomerData struct {
	CustomerCount int64 `json:"customer_count"`
	OrderPrice int64 `json:"order_price"`
	OrderCount int64 `json:"order_count"`
	Items []OrderCustomerDataItems	`json:"items"`
	OrderRankOneCount int64 `json:"order_rank_one_count"`
	OrderRankOnePrice int64 `json:"order_rank_one_price"`
	OrderRankTwoCount int64 `json:"order_rank_two_count"`
	OrderRankTwoPrice int64 `json:"order_rank_two_price"`
	OrderRankThreeCount int64 `json:"order_rank_three_count"`
	OrderRankThreePrice int64 `json:"order_rank_three_price"`
	OrderRankFourCount int64 `json:"order_rank_four_count"`
	OrderRankFourPrice int64 `json:"order_rank_four_price"`
	OrderRankFiveCount int64 `json:"order_rank_five_count"`
	OrderRankFivePrice int64 `json:"order_rank_five_price"` 
	
}
type OrderCustomerDataGorm struct {
	UserRank int `gorm:"column:user_rank"`
	OrderRank int `gorm:"column:order_rank"`
	OrderCount int64 `gorm:"column:order_count"`
	OrderPrice int64 `gorm:"column:order_price"`
}

type OrderShipperDataGorm struct {
	ShipperRank int `gorm:"column:shipper_rank"`
	OrderRank int `gorm:"column:order_rank"`
	OrderCount int64 `gorm:"column:order_count"`
	OrderPrice int64 `gorm:"column:order_price"`
}



type OrderCustomerDataItems struct {

	CustomerRank int `json:"customer_rank"`
	CustomerCount int `json:"customer_count"`

	OrderRankOneCount int64 `json:"order_rank_one_count"`
	OrderRankOnePrice int64 `json:"order_rank_one_price"`

	OrderRankTwoCount int64 `json:"order_rank_two_count"`
	OrderRankTwoPrice int64 `json:"order_rank_two_price"`

	OrderRankThreeCount int64 `json:"order_rank_three_count"`
	OrderRankThreePrice int64 `json:"order_rank_three_price"`


	OrderRankFourCount int64 `json:"order_rank_four_count"`
	OrderRankFourPrice int64 `json:"order_rank_four_price"`

	OrderRankFiveCount int64 `json:"order_rank_five_count"`
	OrderRankFivePrice int64 `json:"order_rank_five_price"`
}

type OrderCustomerCountData struct {

	CustomerCount int `json:"customer_count"`

	CustomerRankOneCount int64 `json:"customer_rank_one_count"`
	OrderRankOnePrice int64 `json:"order_rank_one_price"`

	CustomerRankTwoCount int64 `json:"customer_rank_two_count"`
	OrderRankTwoPrice int64 `json:"order_rank_two_price"`

	CustomerRankThreeCount int64 `json:"customer_rank_three_count"`
	OrderRankThreePrice int64 `json:"order_rank_three_price"`


	CustomerRankFourCount int64 `json:"customer_rank_four_count"`
	OrderRankFourPrice int64 `json:"order_rank_four_price"`

	CustomerRankFiveCount int64 `json:"customer_rank_five_count"`
	OrderRankFivePrice int64 `json:"order_rank_five_price"`
}




type OrderShipperCountData struct {

	ShipperCount int `json:"shipper_count"`

	ShipperRankOneCount int64 `json:"shipper_rank_one_count"`
	OrderRankOnePrice int64 `json:"order_rank_one_price"`

	ShipperRankTwoCount int64 `json:"shipper_rank_two_count"`
	OrderRankTwoPrice int64 `json:"order_rank_two_price"`

	ShipperRankThreeCount int64 `json:"shipper_rank_three_count"`
	OrderRankThreePrice int64 `json:"order_rank_three_price"`


	ShipperRankFourCount int64 `json:"shipper_rank_four_count"`
	OrderRankFourPrice int64 `json:"order_rank_four_price"`

	ShipperRankFiveCount int64 `json:"shipper_rank_five_count"`
	OrderRankFivePrice int64 `json:"order_rank_five_price"`
}




type OrderLateCountData struct {

	CustomerCount int `json:"order_count"`

	LateRankZeroCount int64 `json:"late_rank_zero_count"`
	OrderRankZeroPrice int64 `json:"late_rank_zero_price"`

	LateRankOneCount int64 `json:"late_rank_one_count"`
	OrderRankOnePrice int64 `json:"late_rank_one_price"`

	LateRankTwoCount int64 `json:"late_rank_one_count"`
	OrderRankTwoPrice int64 `json:"late_rank_two_price"`

	LateRankThreeCount int64 `json:"late_rank_one_count"`
	OrderRankThreePrice int64 `json:"late_rank_three_price"`


	LateRankFourCount int64 `json:"late_rank_one_count"`
	OrderRankFourPrice int64 `json:"late_rank_four_price"`

}



type OrderShipperData struct { 
	ShipperCount int64 `json:"shipper_count"`
	OrderPrice int64 `json:"order_price"`
	OrderCount int64 `json:"order_count"`
	Items []OrderShipperDataItems	`json:"items"`
	OrderRankOneCount int64 `json:"order_rank_one_count"`
	OrderRankOnePrice int64 `json:"order_rank_one_price"`
	OrderRankTwoCount int64 `json:"order_rank_two_count"`
	OrderRankTwoPrice int64 `json:"order_rank_two_price"`
	OrderRankThreeCount int64 `json:"order_rank_three_count"`
	OrderRankThreePrice int64 `json:"order_rank_three_price"`
	OrderRankFourCount int64 `json:"order_rank_four_count"`
	OrderRankFourPrice int64 `json:"order_rank_four_price"`
	OrderRankFiveCount int64 `json:"order_rank_five_count"`
	OrderRankFivePrice int64 `json:"order_rank_five_price"` 

}


type OrderShipperDataItems struct {

	ShipperRank int `json:"shipper_rank"`
	ShipperCount int `json:"shipper_count"`
	OrderRankOneCount int64 `json:"order_rank_one_count"`
	OrderRankOnePrice int64 `json:"order_rank_one_price"`

	OrderRankTwoCount int64 `json:"order_rank_two_count"`
	OrderRankTwoPrice int64 `json:"order_rank_two_price"`

	OrderRankThreeCount int64 `json:"order_rank_three_count"`
	OrderRankThreePrice int64 `json:"order_rank_three_price"`

	OrderRankFourCount int64 `json:"order_rank_four_count"`
	OrderRankFourPrice int64 `json:"order_rank_four_price"`

	OrderRankFiveCount int64 `json:"order_rank_five_count"`
	OrderRankFivePrice int64 `json:"order_rank_five_price"`

}


type OrderLateData struct { 

	LateMinute int64 `json:"late_minute"`
	OrderPrice int64 `json:"order_price"`
	OrderCount int64 `json:"order_count"`
	Items []OrderLateDataItems	`json:"items"`
	OrderRankOneCount int64 `json:"order_rank_one_count"`
	OrderRankOnePrice int64 `json:"order_rank_one_price"`
	OrderRankTwoCount int64 `json:"order_rank_two_count"`
	OrderRankTwoPrice int64 `json:"order_rank_two_price"`
	OrderRankThreeCount int64 `json:"order_rank_three_count"`
	OrderRankThreePrice int64 `json:"order_rank_three_price"`
	OrderRankFourCount int64 `json:"order_rank_four_count"`
	OrderRankFourPrice int64 `json:"order_rank_four_price"`
	OrderRankFiveCount int64 `json:"order_rank_five_count"`
	OrderRankFivePrice int64 `json:"order_rank_five_price"` 

}

type OrderLateDataGorm struct {
	LateRank int `gorm:"column:late_rank"`
	OrderRank int `gorm:"column:order_rank"`
	OrderCount int64 `gorm:"column:order_count"`
	OrderPrice int64 `gorm:"column:order_price"`
}

type OrderLateDataItems struct {

	

	LateState int `json:"late_state"`
	LateCount int `json:"late_count"`

	OrderRankOneCount int64 `json:"order_rank_one_count"`
	OrderRankOnePrice int64 `json:"order_rank_one_price"`

	OrderRankTwoCount int64 `json:"order_rank_two_count"`
	OrderRankTwoPrice int64 `json:"order_rank_two_price"`

	OrderRankThreeCount int64 `json:"order_rank_three_count"`
	OrderRankThreePrice int64 `json:"order_rank_three_price"`

	OrderRankFourCount int64 `json:"order_rank_four_count"`
	OrderRankFourPrice int64 `json:"order_rank_four_price"`

	OrderRankFiveCount int64 `json:"order_rank_five_count"`
	OrderRankFivePrice int64 `json:"order_rank_five_price"`
	
	
}