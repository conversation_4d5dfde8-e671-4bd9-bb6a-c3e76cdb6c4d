package response

import merchantResource "mulazim-api/resources/merchant"

type FoodInformation struct {
	BeginTime      string  `json:"begin_time"`
	CategoryIDs    []int   `json:"category_ids"` // 可能是 []int 或 nil，可根据实际情况调整
	DescriptionUg  string  `json:"description_ug"`
	DescriptionZh  string  `json:"description_zh"`
	EndTime        string  `json:"end_time"`
	FoodsGroupID   int     `json:"foods_group_id"`
	ID             int     `json:"id"`
	Image          string  `json:"image"`
	IsDistribution int     `json:"is_distribution"`
	IsRecommend    int     `json:"is_recommend"`
	NameUg         string  `json:"name_ug"`
	NameZh         string  `json:"name_zh"`
	Price          float64 `json:"price"`
	ReadyTime      int     `json:"ready_time"`
	RestaurantID   int     `json:"restaurant_id"`
	State          int     `json:"state"`
	Weight         int     `json:"weight"`

	FoodType       uint8                                    `json:"food_type"`                  // 美食类型：0:普通美食，1:规格美食，2:套餐美食
	ComboFoodItems []merchantResource.CommentFoodComboItems `json:"combo_food_items,omitempty"` // 套餐中的美食项
}
