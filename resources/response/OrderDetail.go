package response

import "mulazim-api/resources/merchant"

// OrderDetail 订单详情响应结构
type OrderDetail struct {
	OrderInfo      OrderInfo       `json:"order_info"`
	ShipmentInfo   ShipmentInfo    `json:"shipment_info"`
	FoodsInfo      []FoodInfo      `json:"foods_info"`
	CustomerInfo   CustomerInfo    `json:"customer_info"`
	RestaurantInfo RestaurantInfo  `json:"restaurant_info"`
	IncomeInfo     []IncomeInfo    `json:"income_info"`
	MarketInfo     []MarketInfo    `json:"market_info"`
}

// OrderInfo 订单基本信息
type OrderInfo struct {
	ID                  int     `json:"id"`
	Name                string  `json:"name"`
	Mobile              string  `json:"mobile"`
	OrderNo             string  `json:"order_no"`
	CreatedAt           string  `json:"created_at"`
	Remark              string  `json:"remark"`
	Terminal            string  `json:"terminal"`
	PayType             string  `json:"pay_type"`
	TotalPrice          uint     `json:"total_price"`
	OriginalPrice       uint    `json:"original_price"`
	OriginalShipment    uint    `json:"original_shipment"`
	TotalDiscountAmount uint    `json:"total_discount_amount"`
	ActualPaid          uint    `json:"actual_paid"`
	DiscountAmount      int     `json:"discount_amount"`
	ShipmentFee         uint    `json:"shipment_fee"`
	PayAmount           uint     `json:"pay_amount"`
	PayTypeID           int     `json:"pay_type_id"`
	PayPlatform         int  `json:"pay_platform"`
	FoodAllPrice        uint    `json:"food_all_price"`
	FoodAllReducePrice  uint    `json:"food_all_reduce_price"`
	FoodOriginalPrice   uint    `json:"food_original_price"`
	TotalFee            uint     `json:"total_fee"`
	ConsumeType         string  `json:"consume_type"`
	AllReducePrice      uint    `json:"all_reduce_price"`
}

// ShipmentInfo 配送信息
type ShipmentInfo struct {
	State            string `json:"state"`
	StateColor       string `json:"state_color"`
	ShipmentState    string `json:"shipment_state"`
	BookingTime      string `json:"booking_time"`
	DeliveryEndTime  string `json:"delivery_end_time"`
	ShipperName      string `json:"shipper_name"`
	ShipperMobile    string `json:"shipper_mobile"`
	OriginalShipment uint   `json:"original_shipment"`
}

// FoodInfo 美食信息
type FoodInfo struct {
	Image           string  `json:"image"`
	Name            string  `json:"name"`
	OriginalPrice   int `json:"original_price"`
	Price           int `json:"price"`
	Discount        int `json:"discount"`
	Count           int `json:"count"`
	LunchBoxFee     int    `json:"lunch_box_fee"`
	TotalFoodsPrice int `json:"total_foods_price"`
	TotalPrice      int `json:"total_price"`

	FoodType       uint8                               `json:"food_type"`                  // 美食类型：0:普通美食，1:规格美食，2:套餐美食
	SelectedSpec   *merchant.CommentRestaurantFoodSpec `json:"selected_spec,omitempty"`    // 已选的规格数据
	ComboFoodItems []merchant.CommentFoodComboItems    `json:"combo_food_items,omitempty"` // 套餐中的美食项
}

// CustomerInfo 客户信息
type CustomerInfo struct {
	Name    string `json:"name"`
	Mobile  string `json:"mobile"`
	Address string `json:"address"`
}

// RestaurantInfo 餐厅信息
type RestaurantInfo struct {
	Name              string `json:"name"`
	Mobile            string `json:"mobile"`
	Address           string `json:"address"`
	OrderReceiveTime  string `json:"order_receive_time"`
	OrderReadyTime    string `json:"order_ready_time"`
}

// IncomeInfo 收入信息
type IncomeInfo struct {
	Name   string `json:"name"`
	Amount int    `json:"amount"`
}

// MarketInfo 营销信息
type MarketInfo struct {
	Name         string `json:"name"`
	Amount       int    `json:"amount"`
	FoodPriceAdd int    `json:"food_price_add"`
	ReducePrice  int    `json:"reduce_price"`
} 