package response

import (
	delivery "mulazim-api/requests/cms/delivery"
)

// DeliveryConfigsResponse 配送配置详情响应
type DeliveryRestaurantListResponse struct {
	RestaurantID                      int                        `json:"restaurant_id"`
	RestaurantName                    string                      `json:"restaurant_name"`
	RestaurantLogo            		string         				`json:"restaurant_logo"`
	Radius            		int         				`json:"radius"`
	Option            		uint8         				`json:"option"`
	FeeState            	uint8         				`json:"fee_state"`
	Polygon            		[][]float64         				`json:"polygon"`
	Lat            		float64         				`json:"lat"`
	Lng            		float64         				`json:"lng"`
}

type DeliveryRestaurantDetailResponse struct {
	Radius            		uint8         				`json:"radius"`
	Option            		uint8         				`json:"option"`
	FeeState            	uint8         				`json:"fee_state"`
	Polygon            		[][]float64         				`json:"polygon"`
}

type DeliveryAreaDefaultPolygonResponse struct {
	Polygon            		[][]float64         				`json:"polygon"`
	RunningDeliveryAreaConfig   RunningDeliveryAreaConfig `json:"running_delivery_area_config"`
}

type RunningDeliveryAreaConfig struct {
	ID                      int                        `json:"id"`
	Name                    string                      `json:"name"`
	Polygon            		[][]float64         		`json:"polygon"`
	DeliveryFees           []DeliveryFeeConfigDetailResponse `json:"delivery_fees"`
}

type DeliveryAreaListResponse struct {
	AreaID                      int                        `json:"area_id"`
	CityID                      int                        `json:"city_id"`
	AreaName                    string                      `json:"area_name"`
	CityName                    string                      `json:"city_name"`
	Name                    string                      `json:"name"`
	Notice                    string                      `json:"notice"`
	Count            		int         				`json:"count"`
	OrderAddTime            int         				`json:"order_add_time"`
	ShipmentFeeMin          int         				`json:"shipment_fee_min"`
	ShipmentType            int         				`json:"shipment_type"`
	Radius                  uint                    `json:"radius"`
}

type DeliveryAreaConfigListResponse struct {
	ID                      int                        `json:"id"`
	Name                    string                      `json:"name"`
	Notice                  string                      `json:"notice"`
	NoticeUg				string						`json:"notice_ug"`
	NoticeZh				string						`json:"notice_zh"`
	OrderAddTime            int         				`json:"order_add_time"`
	ShipmentFeeMin          int         				`json:"shipment_fee_min"`
	ShipmentType            int         				`json:"shipment_type"`
	State                   uint8                      `json:"state"`
	Radius                  uint                    `json:"radius"`
}

type DeliveryAreaConfigDetailResponse struct {
	AreaInfo           DeliveryConfigDetailAreaInfo `json:"area_info"`
	DeliveryFees           []DeliveryFeeConfigDetailResponse `json:"delivery_fees"`
}

type DeliveryConfigDetailAreaInfo struct {
	ID                      int                        `json:"id"`
	NameUg                  string                      `json:"name_ug"`
	NameZh                  string                      `json:"name_zh"`
	NoticeUg                string                      `json:"notice_ug"`
	NoticeZh                string                      `json:"notice_zh"`
	OrderAddTime            int         				`json:"order_add_time"`
	ShipmentFeeMin          int         				`json:"shipment_fee_min"`
	ShipmentType            int         				`json:"shipment_type"`
	Polygon                 [][]float64                `json:"polygon"`
	AreaPolygon             [][]float64                `json:"area_polygon"`
	Option                  uint8                    `json:"option"`
	Radius                  uint                    `json:"radius"`
	AreaRunningState        uint8                    `json:"area_running_state"`
	
}

type DeliveryFeeConfigDetailResponse struct {
	ID                      int                        `json:"id"`
	Type                    uint8                      `json:"type"`
	Default                 uint8                      `json:"default"`
	Stages                  delivery.DeliveryFeeStages `json:"stages"`
	StageFeeMin             uint                       `json:"stage_fee_min"`
	BeginTime               string                 `json:"begin_time"`
	EndTime                 string                 `json:"end_time"`
	StartFee                uint                       `json:"start_fee"`
}