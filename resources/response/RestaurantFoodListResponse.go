package response

type FoodListResponseData struct {
	Data   FoodListResponse   `json:"data"`
	Msg    string `json:"msg"`
	Status int    `json:"status"`
	Time   string `json:"time"`
}

type FoodListResponse struct {
	CanMulazimTake int                 `json:"can_mulazim_take"`
	CanSelfTake    int                 `json:"can_self_take"`
	Distribution   FoodListDistribution        `json:"distribution"`
	FoodType       []FoodListFoodType          `json:"food_type"`
	Foods          []FoodListFood              `json:"foods"`
	ID             int                 `json:"id"`
	IsResting      int                 `json:"is_resting"`
	Lang           string              `json:"lang,omitempty"`
	Limit          int                 `json:"limit"`
	LotteryActive  int                 `json:"lottery_active"`
	LotteryMinOrderPrice float64         `json:"lottery_min_order_price"`
	LowestPercent  float64             `json:"lowest_percent"`
	Market         FoodListMarket              `json:"market"`
	Name           string              `json:"name"`
	NotSameLocation int                 `json:"not_same_location"`
	NotSameLocationMsg FoodListNotSameLocationMsg `json:"not_same_location_msg"`
	Notice         FoodListNotice              `json:"notice"`
	Page           int                 `json:"page"`
	RestaurantType int                 `json:"restaurant_type"`
	Total          int                 `json:"total"`
	TotalPage      int                 `json:"total_page"`
}

type FoodListDistribution struct {
	Distance float64 `json:"distance"`
	Param    float64 `json:"param"`
	Shipment float64 `json:"shipment"`
}

type FoodListFoodType struct {
	ID        int      `json:"id"`
	Name      string   `json:"name"`
	Shake     int      `json:"shake"`
	Type      int      `json:"type"`
	License   []FoodListLicense `json:"license,omitempty"`
	LicenseType int     `json:"license_type,omitempty"`
	NameUG    string   `json:"name_ug,omitempty"`
	NameZH    string   `json:"name_zh,omitempty"`
}

type FoodListLicense struct {
	BigImage  string `json:"big_image"`
	ID        int    `json:"id"`
	Image     string `json:"image"`
	Name      string `json:"name"`
	SmallImage string `json:"small_image"`
}

type FoodListFood struct {
	BeginTime         string      `json:"begin_time"`
	BigImage          string      `json:"big_image"`
	EndTime           string      `json:"end_time"`
	FoodQuantity      string      `json:"food_quantity"`
	FoodQuantityType  int         `json:"food_quantity_type"`
	FoodQuantityTypeValUG string `json:"food_quantity_type_val_ug"`
	FoodQuantityTypeValZH string `json:"food_quantity_type_val_zh"`
	HasFoodsPries     float64         `json:"has_foods_pries"`
	ID                int         `json:"id"`
	Image             string      `json:"image"`
	LunchBoxAccommodate int       `json:"lunch_box_accommodate"`
	LunchBoxFee       float64         `json:"lunch_box_fee"`
	LunchBoxID        int         `json:"lunch_box_id"`
	LunchBoxName      string      `json:"lunch_box_name"`
	MaxOrderCount     int         `json:"max_order_count,omitempty"`
	MinCount          int         `json:"min_count"`
	MonthOrderCount   int         `json:"month_order_count"`
	Name              string      `json:"name"`
	NameUG            string      `json:"name_ug"`
	NameZH            string      `json:"name_zh"`
	OrderCount        int         `json:"order_count"`
	OriginPrice       float64     `json:"origin_price"`
	OriginProfit      float64     `json:"origin_profit"`
	Percent           float64     `json:"percent"`
	Price             float64     `json:"price"`
	Profit            float64     `json:"profit"`
	Relations         []FoodListFood `json:"relations"`
	SeckillActive     int         `json:"seckill_active"`
	SeckillId     int         `json:"seckill_id,omitempty"`

	SeckillMaxOrderCount int `json:"seckill_max_order_count,omitempty"` 
	SeckillTotalCount  int `json:"seckill_total_count,omitempty"`
	SeckillSaledCount  int `json:"seckill_saled_count,omitempty"`
	SeckillOrderTime  int	 `json:"seckill_order_time,omitempty"`
	SeckillPrice  float64 		 `json:"seckill_price,omitempty"`
	SeckillPriceMarkupId  int 	 `json:"seckill_price_markup_id,omitempty"`
	SeckillTime  int 	 `json:"seckill_time,omitempty"`

	
	State             int         `json:"state"`
	TypeID            int         `json:"type_id"`
	Weight            int         `json:"weight"`
	WeightInGroup     int         `json:"weight_in_group"`
	PrefrentialID     int         `json:"prefrential_id,omitempty"`
	PrefrentialPrice  float64     `json:"prefrential_price,omitempty"`
	PrefrentialPriceMarkupID int `json:"prefrential_price_markup_id"`
}

type FoodListMarket struct {
	HasReduction bool `json:"has_reduction"`
	HasShipmentReduction bool `json:"has_shipment_reduction"`
	Steps          FoodListSteps `json:"steps,omitempty"`
	Tags           FoodListTags  `json:"tags,omitempty"`
	ReductionFoodsId []string  `json:"reduction_foods_id,omitempty"`
}

type FoodListSteps struct {
	ShipmentStep FoodListShipmentStep `json:"shipment_step,omitempty"`
	FoodListReductionStep []FoodListReductionStep `json:"reduction_step,omitempty"`
}

type FoodListShipmentStep struct {
	DistanceEnd   float64     `json:"distance_end"`
	DistanceStart float64     `json:"distance_start"`
	MinDeliveryPrice float64 `json:"min_delivery_price"`
	ShipmentReduce  float64     `json:"shipment_reduce"`
}

type FoodListReductionStep struct {
	Price   float64     `json:"price"`
	Reduce   float64     `json:"reduce"`
	
}

type FoodListTags struct {
	ReductionTags       []FoodListReductionTag       `json:"reduction_tags,omitempty"`
	ShipmentReductionTags []FoodListShipmentReductionTag `json:"shipment_reduction_tags,omitempty"`
}

type FoodListReductionTag struct {
	Background string `json:"background"`
	BorderColor string `json:"border_color"`
	Color       string `json:"color"`
	Image       string `json:"image"`
	Title       string `json:"title"`
	Type        int    `json:"type"`
}

type FoodListShipmentReductionTag struct {
	Background string `json:"background"`
	BorderColor string `json:"border_color"`
	Color       string `json:"color"`
	DistanceEnd   float64     `json:"distance_end"`
	DistanceStart float64     `json:"distance_start"`
	Image       string `json:"image"`
	MinDeliveryPrice float64 `json:"min_delivery_price"`
	Price       float64 `json:"price"`
	Title       string `json:"title"`
	Type        int    `json:"type"`
}

type FoodListNotSameLocationMsg struct {
	Btn  string `json:"btn"`
	Msg  string `json:"msg"`
	Title string `json:"title"`
}

type FoodListNotice struct {
	Content string `json:"content"`
	Title   string `json:"title"`
}