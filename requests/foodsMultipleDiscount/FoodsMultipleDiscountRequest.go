package foodsMultipleDiscount

// FoodsMultipleDiscountCreateRequest 多份打折活动请求
// @Description 多份打折活动创建参数
type FoodsMultipleDiscountCreateRequest struct {
	// @Description 食品ID列表
	// @Example 1
	FoodID int `json:"food_id" binding:"required"`
	// @Description 两份折扣百分比
	// @Example 80
	Discount2Percent float64 `json:"discount2_percent" binding:"required,min=0,max=100"`
	// @Description 三份折扣百分比
	// @Example 70
	Discount3Percent float64 `json:"discount3_percent" binding:"min=0,max=100"`
	// @Description 四份折扣百分比
	// @Example 60
	Discount4Percent float64 `json:"discount4_percent" binding:"min=0,max=100"`
	// @Description 五份折扣百分比
	// @Example 50
	Discount5Percent float64 `json:"discount5_percent" binding:"min=0,max=100"`
	// @Description 活动开始时间
	// @Example 2023-01-01 00:00:00
	StartTime string `json:"start_time" binding:"required"`
	// @Description 活动结束时间
	// @Example 2023-12-31 23:59:59
	EndTime string `json:"end_time" binding:"required"`
}

// FoodsMultipleDiscountListRequest 多份打折活动列表请求
// FoodsMultipleDiscountListRequest 多份打折活动列表请求
// @Description 多份打折活动列表查询参数
type FoodsMultipleDiscountListRequest struct {
	// @Description 页码
	// @Example 1
	Page int `json:"page" form:"page"`
	// @Description 每页数量
	// @Example 20
	Limit int `json:"limit" form:"limit"`
	// @Description 开始时间
	// @Example 2023-01-01 00:00:00
	StartTime string `json:"start_time" form:"start_time"`
	// @Description 结束时间
	// @Example 2023-12-31 23:59:59
	EndTime string `json:"end_time" form:"end_time"`
	// @Description 区域ID
	// @Example 1
	AreaId int `json:"area_id" form:"area_id"`
	// @Description 城市ID
	// @Example 1
	CityId int `json:"city_id" form:"city_id"`
	// @Description 状态(0:未开始,1:进行中,2:已结束,3:已取消)
	// @Example 1
	State *int `json:"state" form:"state"`
	// @Description 搜索关键词
	// @Example 打折活动
	Search string `json:"search" form:"search"`
}

// FoodsMultipleDiscountChangeStateRequest 多份打折活动状态请求
// @Description 多份打折活动状态变更参数
type FoodsMultipleDiscountChangeStateRequest struct {
	// @Description 活动ID
	// @Example 1
	ID int `json:"id" form:"id" binding:"required,gt=0"`
}

// FoodsMultipleDiscountDetailRequest 多份打折活动详情请求
// @Description 多份打折活动详情查询参数
type FoodsMultipleDiscountDetailRequest struct {
	// @Description 活动ID
	// @Example 1
	ID int `json:"id" form:"id" binding:"required,gt=0"`
}

// FoodsMultipleDiscountDetailRequest 多份打折活动详情请求
// @Description 多份打折活动详情查询参数
type FoodsMultipleDiscountListHeaderRequest struct {
	// @Description 城市ID
	// @Example 1
	CityID int `json:"city_id" form:"city_id" binding:""`
	// @Description 区域ID
	// @Example 1
	AreaID int `json:"area_id" form:"area_id" binding:""`
	
}

// FoodsMultipleDiscountOrderListRequest 多份打折活动订单列表请求
// @Description 多份打折活动订单列表查询参数
type FoodsMultipleDiscountOrderListRequest struct {
		// @Description 开始时间
	// @Example 2023-01-01 00:00:00
	ID int `json:"id" form:"id" binding:"required,gt=0"`
	// @Description 开始时间
	// @Example 2023-01-01 00:00:00
	BeginTime string `json:"begin_time" form:"begin_time"`
	// @Description 结束时间
	// @Example 2023-12-31 23:59:59
	EndTime string `json:"end_time" form:"end_time"`
	Search string `json:"search" form:"search"`
	// @Description 页码
	// @Example 1
	Page int `json:"page" form:"page"`
	// @Description 每页数量
	// @Example 20
	Limit int `json:"limit" form:"limit"`
}

// FoodsMultipleDiscountDeleteRequest 多份打折活动删除请求
// @Description 多份打折活动删除参数
type FoodsMultipleDiscountDeleteRequest struct {
	// @Description 活动ID
	// @Example 1
	ID int `json:"id" form:"id" binding:"required,gt=0"`
}

// FoodsMultipleDiscountUpdateRequest 多份打折活动更新请求
// @Description 多份打折活动更新参数
type FoodsMultipleDiscountUpdateRequest struct {
	// @Description 活动ID
	// @Example 1
	ID int `json:"id" binding:"required,gt=0"`
	// @Description 两份折扣百分比
	// @Example 90
	Discount2Percent float64 `json:"discount2_percent" binding:"required,min=0,max=100"`
	// @Description 三份折扣百分比
	// @Example 80
	Discount3Percent float64 `json:"discount3_percent" binding:"min=0,max=100"`
	// @Description 四份折扣百分比
	// @Example 70
	Discount4Percent float64 `json:"discount4_percent" binding:"min=0,max=100"`
	// @Description 五份折扣百分比
	// @Example 60
	Discount5Percent float64 `json:"discount5_percent" binding:"min=0,max=100"`
	// @Description 开始时间
	// @Example 2023-01-01 00:00:00
	StartTime string `json:"start_time" binding:"required"`
	// @Description 结束时间
	// @Example 2023-12-31 23:59:59
	EndTime string `json:"end_time" binding:"required"`
}
