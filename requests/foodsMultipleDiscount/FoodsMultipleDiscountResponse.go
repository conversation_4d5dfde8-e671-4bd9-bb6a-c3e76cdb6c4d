package foodsMultipleDiscount

// FoodsMultipleDiscountResponse 多份打折活动响应
// @Description 多份打折活动列表响应
type FoodsMultipleDiscountCmsResponse struct {
	// @Description 活动ID
	// @Example 1
	ID int64 `json:"id"`
	// @Description 名称
	// @Example 你好
	Name string `json:"name"`
	// @Description 城市ID
	// @Example 1
	CityId int64 `json:"city_id"`
	// @Description 城市名称
	// @Example 北京
	CityName string `json:"city_name"`
	// @Description 区域ID
	// @Example 1
	AreaId int64 `json:"area_id"`
	// @Description 区域名称
	// @Example 朝阳区
	AreaName string `json:"area_name"`
	// @Description 餐厅ID
	// @Example 1
	RestaurantId int64 `json:"restaurant_id"`
	// @Description 餐厅名称
	// @Example 好吃餐厅
	RestaurantName string `json:"restaurant_name"`
	// @Description 食品ID
	// @Example 1
	FoodId int64 `json:"food_id"`
	// @Description 食品名称
	// @Example 炒饭
	FoodName string `json:"food_name"`

	// @Description 原价
	// @Example 25.00
	OriginalPrice string `json:"original_price"`

	// @Description 两份折扣百分比
	// @Example 90
	Discount2Percent string `json:"discount2_percent"`
	// @Description 三份折扣百分比
	// @Example 80
	Discount3Percent string `json:"discount3_percent"`
	// @Description 四份折扣百分比
	// @Example 70
	Discount4Percent string `json:"discount4_percent"`
	// @Description 五份折扣百分比
	// @Example 60
	Discount5Percent string `json:"discount5_percent"`

	// @Description 开始时间
	// @Example 2023-01-01 00:00:00
	StartTime string `json:"start_time"`
	// @Description 结束时间
	// @Example 2023-12-31 23:59:59
	EndTime string `json:"end_time"`

	// @Description 创建者ID
	// @Example 1
	CreatorId int64 `json:"creator_id"`
	// @Description 创建者名称
	// @Example 管理员
	CreatorName string `json:"creator_name"`

	// @Description 状态(0:未开始,1:进行中,2:已结束,3:已取消)
	// @Example 1
	State int `json:"state"`
	// @Description 状态名称
	// @Example 进行中
	StateName string `json:"state_name"`

	// @Description 创建时间
	// @Example 2023-01-01 00:00:00
	CreatedAt string `json:"created_at"`
	// @Description 更新时间
	// @Example 2023-01-01 00:00:00
	UpdatedAt string `json:"updated_at"`
}

// FoodsMultipleDiscountDetailFoodCmsResponse 多份打折活动详情食品响应
// @Description 多份打折活动详情食品信息
type FoodsMultipleDiscountDetailFoodCmsResponse struct {
	// @Description 食品ID
	// @Example 1
	ID int `json:"id"`
	// @Description 食品名称
	// @Example 炒饭
	FoodName string `json:"food_name"`
	// @Description 原价
	// @Example 25.00
	OriginalPrice float64 `json:"original_price"`
	// @Description 餐厅名称
	// @Example 好味餐厅
	RestaurantName string `json:"restaurant_name"`
	// @Description 美食图片
	// @Example
	FoodImage string `json:"food_image"`
}

// FoodsMultipleDiscountDetailCmsResponse 多份打折活动详情响应
// @Description 多份打折活动详情信息
type FoodsMultipleDiscountDetailCmsResponse struct {
	// @Description 活动ID
	// @Example 1
	ID int64 `json:"id"`
	// @Description 食品信息
	Food FoodsMultipleDiscountDetailFoodCmsResponse `json:"food"`
	// @Description 两份折扣百分比
	// @Example 90
	Discount2Percent float64 `json:"discount2_percent"`
	// @Description 三份折扣百分比
	// @Example 80
	Discount3Percent float64 `json:"discount3_percent"`
	// @Description 四份折扣百分比
	// @Example 70
	Discount4Percent float64 `json:"discount4_percent"`
	// @Description 五份折扣百分比
	// @Example 60
	Discount5Percent float64 `json:"discount5_percent"`
	// @Description 开始时间
	// @Example 2023-01-01 00:00:00
	StartTime string `json:"start_time"`
	// @Description 结束时间
	// @Example 2023-12-31 23:59:59
	EndTime string `json:"end_time"`
}

// FoodsMultipleDiscountDetailFoodResponse 五阶梯多份折扣活动详情食品响应
// @Description 多份打折活动详情食品信息
type FoodsMultipleDiscountDetailMerchantViweResponse struct {
	Header map[string]interface{} `json:"header"`
	Info FoodsMultipleDiscountDetailViewResponse `json:"info"`
}

// FoodsMultipleDiscountDetailFoodResponse 五阶梯多份折扣活动详情食品响应
// @Description 多份打折活动详情食品信息
type FoodsMultipleDiscountDetailMerchantResponse struct {
	// @Description 商品ID
	// @Example 1
	ID        int64     `json:"id"`

	

	// @Description 商品名称
	// @Example 炒饭
	Name      string  `json:"name"`
	// @Description 原价（元）
	// @Example 25.00
	Price     string `json:"price"`

	// @Description 折扣率（0-1）
	// @Example 1.0
	Discount1Percent  float64 `json:"discount1_percent"`
	// @Description 已售份数
	// @Example 10
	Discount1SaledCount string     `json:"discount1_saled_count"`
	// @Description 销售额（元，字符串防精度）
	// @Example 250.00
	Discount1SaledAmount string  `json:"discount1_saled_amount"`
	// @Description 价格
	// @Example 1.0
	Discount1Price  string `json:"discount1_price"`

	// @Description 两份折扣率
	// @Example 0.9
	Discount2Percent  float64 `json:"discount2_percent"`
	// @Description 两份折扣已售份数
	// @Example 20
	Discount2SaledCount string     `json:"discount2_saled_count"`
	// @Description 两份折扣销售额（元）
	// @Example 450.00
	Discount2SaledAmount string  `json:"discount2_saled_amount"`
	// @Description 两份折扣价格
	// @Example 22.50
	Discount2Price  string `json:"discount2_price"`

	// @Description 三份折扣率
	// @Example 0.8
	Discount3Percent  float64 `json:"discount3_percent"`
	// @Description 三份折扣已售份数
	// @Example 15
	Discount3SaledCount string     `json:"discount3_saled_count"`
	// @Description 三份折扣销售额（元）
	// @Example 300.00
	Discount3SaledAmount string  `json:"discount3_saled_amount"`
	// @Description 三份折扣价格
	// @Example 20.00
	Discount3Price  string `json:"discount3_price"`

	// @Description 四份折扣率
	// @Example 0.7
	Discount4Percent  float64 `json:"discount4_percent"`
	// @Description 四份折扣已售份数
	// @Example 8
	Discount4SaledCount string     `json:"discount4_saled_count"`
	// @Description 四份折扣销售额（元）
	// @Example 140.00
	Discount4SaledAmount string  `json:"discount4_saled_amount"`
	// @Description 四份折扣价格
	// @Example 17.50
	Discount4Price  string `json:"discount4_price"`

	// @Description 五份折扣率
	// @Example 0.6
	Discount5Percent  float64 `json:"discount5_percent"`
	// @Description 五份折扣已售份数
	// @Example 5
	Discount5SaledCount string     `json:"discount5_saled_count"`
	// @Description 五份折扣销售额（元）
	// @Example 75.00
	Discount5SaledAmount string  `json:"discount5_saled_amount"`
	// @Description 五份折扣价格
	// @Example 15.00
	Discount5Price  string `json:"discount5_price"`

	// @Description 总售出份数（各阶梯总和）
	// @Example 58
	TotalSaledCount  string     `json:"total_saled_count"`
	// @Description 总销售额（元，字符串防精度）
	// @Example 1215.00
	TotalSaledAmount string  `json:"total_saled_amount"`

	// @Description 状态(0:未开始,1:进行中,2:已结束,3:已取消)
	// @Example 1
	State       int    `json:"state"`
	// @Description 状态名称
	// @Example 进行中
	StateName   string `json:"state_name"`

	// @Description 创建人昵称
	// @Example 管理员
	CreatorName string `json:"creator_name"`
	// @Description 创建时间
	// @Example 2023-01-01 00:00:00
	CreatedAt   string `json:"created_at"`
	// @Description 活动开始时间（ISO8601）
	// @Example 2023-01-01 00:00:00
	StartTime   string `json:"start_time"`
	// @Description 活动结束时间（ISO8601）
	// @Example 2023-12-31 23:59:59
	EndTime     string `json:"end_time"`

	// @Description 美食图片
	// @Example https://www.baidu.com/1.jpg
	FoodImage string `json:"food_image"`

	// @Description 美食ID
	// @Example https://www.baidu.com/1.jpg
	FoodID int `json:"food_id"`

	// @Description 能否删除
	// @Example true
	CanDelete bool `json:"can_delete"`
}




// FoodsMultipleDiscountDetailFoodResponse 五阶梯多份折扣活动详情食品响应
// @Description 多份打折活动详情食品信息
type FoodsMultipleDiscountDetailViewResponse struct {
	// @Description 商品ID
	// @Example 1
	ID        int64     `json:"id"`
	CityName string `json:"city_name"`
	AreaName string `json:"area_name"`
	RestaurantName string `json:"restaurant_name"`
	FoodName      string  `json:"food_name"`
	// @Description 原价（元）
	// @Example 25.00
	Price     string `json:"price"`

	// @Description 折扣率（0-1）
	// @Example 1.0
	Discount1Percent  string `json:"discount1_percent"`
	// @Description 已售份数
	// @Example 10
	Discount1SaledCount string     `json:"discount1_saled_count"`
	// @Description 销售额（元，字符串防精度）
	// @Example 250.00
	Discount1SaledAmount string  `json:"discount1_saled_amount"`
	// @Description 价格
	// @Example 1.0
	Discount1Price  string `json:"discount1_price"`

	// @Description 两份折扣率
	// @Example 0.9
	Discount2Percent  string `json:"discount2_percent"`
	// @Description 两份折扣已售份数
	// @Example 20
	Discount2SaledCount string     `json:"discount2_saled_count"`
	// @Description 两份折扣销售额（元）
	// @Example 450.00
	Discount2SaledAmount string  `json:"discount2_saled_amount"`
	// @Description 两份折扣价格
	// @Example 22.50
	Discount2Price  string `json:"discount2_price"`

	// @Description 三份折扣率
	// @Example 0.8
	Discount3Percent  string `json:"discount3_percent"`
	// @Description 三份折扣已售份数
	// @Example 15
	Discount3SaledCount string     `json:"discount3_saled_count"`
	// @Description 三份折扣销售额（元）
	// @Example 300.00
	Discount3SaledAmount string  `json:"discount3_saled_amount"`
	// @Description 三份折扣价格
	// @Example 20.00
	Discount3Price  string `json:"discount3_price"`

	// @Description 四份折扣率
	// @Example 0.7
	Discount4Percent  string `json:"discount4_percent"`
	// @Description 四份折扣已售份数
	// @Example 8
	Discount4SaledCount string     `json:"discount4_saled_count"`
	// @Description 四份折扣销售额（元）
	// @Example 140.00
	Discount4SaledAmount string  `json:"discount4_saled_amount"`
	// @Description 四份折扣价格
	// @Example 17.50
	Discount4Price  string `json:"discount4_price"`

	// @Description 五份折扣率
	// @Example 0.6
	Discount5Percent  string `json:"discount5_percent"`
	// @Description 五份折扣已售份数
	// @Example 5
	Discount5SaledCount string     `json:"discount5_saled_count"`
	// @Description 五份折扣销售额（元）
	// @Example 75.00
	Discount5SaledAmount string  `json:"discount5_saled_amount"`
	// @Description 五份折扣价格
	// @Example 15.00
	Discount5Price  string `json:"discount5_price"`

	// @Description 总售出份数（各阶梯总和）
	// @Example 58
	TotalSaledCount  string     `json:"total_saled_count"`
	// @Description 总销售额（元，字符串防精度）
	// @Example 1215.00
	TotalSaledAmount string  `json:"total_saled_amount"`

	// @Description 状态(0:未开始,1:进行中,2:已结束,3:已取消)
	// @Example 1
	State       int    `json:"state"`
	// @Description 状态名称
	// @Example 进行中
	StateName   string `json:"state_name"`

	// @Description 创建人昵称
	// @Example 管理员
	CreatorName string `json:"creator_name"`
	// @Description 创建时间
	// @Example 2023-01-01 00:00:00
	CreatedAt   string `json:"created_at"`
	// @Description 活动开始时间（ISO8601）
	// @Example 2023-01-01 00:00:00
	StartTime   string `json:"start_time"`
	// @Description 活动结束时间（ISO8601）
	// @Example 2023-12-31 23:59:59
	EndTime     string `json:"end_time"`

	// @Description 美食图片
	// @Example https://www.baidu.com/1.jpg
	FoodImage string `json:"food_image"`

	// @Description 美食ID
	// @Example https://www.baidu.com/1.jpg
	FoodID int `json:"food_id"`
}


// FoodsMultipleDiscountResponse 多份打折活动响应
// @Description 多份打折活动列表响应
type FoodsMultipleDiscountListMerchantResponse struct {
	// @Description 活动ID
	// @Example 1
	ID        int64     `json:"id"`
	// @Description 活动名称
	// @Example 双份优惠活动
	Name        string `json:"name"`
	// @Description 开始时间
	// @Example 2023-01-01 00:00:00
	StartTime   string `json:"start_time"`
	// @Description 结束时间
	// @Example 2023-12-31 23:59:59
	EndTime     string `json:"end_time"`
	// @Description 状态(0:未开始,1:进行中,2:已结束,3:已取消)
	// @Example 1
	State       int    `json:"state"`
	// @Description 状态名称
	// @Example 进行中
	StateName   string `json:"state_name"`
	// @Description 创建者名称
	// @Example 管理员
	CreatorName string `json:"creator_name"`
	// @Description 美食图片
	// @Example https://www.baidu.com/1.jpg
	FoodImage string `json:"food_image"`
}



// FoodsMultipleDiscountOrderListResponse 多份打折活动订单列表响应
// @Description 多份打折活动订单列表响应
type FoodsMultipleDiscountOrderListResponse struct {
	Items []FoodsMultipleDiscountOrderList `json:"items"`
	Total int64 `json:"total"`
}
type FoodsMultipleDiscountOrderList struct {
	// @Description 订单ID
	// @Example 1
	ID int64 `json:"id"`
	// @Description 订单号
	// @Example 1234567890
	OrderNo string `json:"order_no"`
	// @Description 用户名称
	// @Example 张三
	UserName string `json:"user_name"`
	// @Description 用户手机号
	// @Example 13800138000
	UserMobile string `json:"user_mobile"`
	// @Description 订单金额
	// @Example 100.00
	Count int64 `json:"count"`
	// @Description 订单状态
	// @Example 1
	TotalPrice int64 `json:"total_price"`
	// @Description 订单状态名称
	// @Example 已支付
	TotalDiscountPrice int64 `json:"total_discount_price"`
	// @Description 订单状态
	// @Example 1
	State int `json:"state"`
	// @Description 订单状态名称
	// @Example 已支付
	StateName string `json:"state_name"`
	// @Description 创建时间
	// @Example 2023-01-01 00:00:00
	CreatedAt string `json:"created_at"`
}