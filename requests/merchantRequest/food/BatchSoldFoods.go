package food

import (
	"fmt"
)

type BatchSoldFoodsBody struct {
	FoodsJson FoodItemJsonSlice `json:"foods_json" binding:"required,min=1"`
}
type FoodItem struct {
	FoodID    int       `json:"food_id"`
	FoodType  int       `json:"food_type"`
	OptionIds []int `json:"option_ids,omitempty"` // 可选字段
}

type FoodItemJsonSlice []FoodItem

// 你可以在上面添加 Validate 方法、String 方法等
func (f FoodItemJsonSlice) Validate() error {
	if len(f) == 0 {
		return fmt.Errorf("foods_json 不能为空")
	}
	return nil
}