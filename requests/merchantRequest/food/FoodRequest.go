package food

import (
	"mulazim-api/models"
	"mulazim-api/tools"
	"regexp"
)

type FoodCreateRequest struct {
	RestaurantID int64 `form:"restaurant_id" binding:"required"`
	AllFoodsId int64 `form:"all_foods_id" binding:"required"`
	NameUg string `form:"name_ug" binding:"required"`
	NameZh string `form:"name_zh" binding:"required"`
	Price float64 `form:"price" binding:"required"`
	ReadyTime int64 `form:"ready_time" binding:"required"`
	BeginTime string `form:"begin_time" binding:"required"`
	EndTime string `form:"end_time" binding:"required"`
	DescriptionUg string `form:"description_ug" binding:"required"`
	DescriptionZh string `form:"description_zh" binding:"required"`
	Image string `form:"image" binding:"required"`
	Weight int `form:"weight" binding:""`
	IsRecommend int `form:"is_recommend" binding:""`
	CategoryIds string `form:"category_ids" binding:""`
}


type FoodUpdateRequest struct {
	RestaurantID int64 `form:"restaurant_id" binding:"required"`
	ID int64 `form:"id" binding:"required"`
	AllFoodsId int64 `form:"all_foods_id" binding:"required"`
	NameUg string `form:"name_ug" binding:"required"`
	NameZh string `form:"name_zh" binding:"required"`
	Price float64 `form:"price" binding:"required"`
	ReadyTime int64 `form:"ready_time" binding:"required"`
	BeginTime string `form:"begin_time" binding:"required"`
	EndTime string `form:"end_time" binding:"required"`
	DescriptionUg string `form:"description_ug" binding:"required"`
	DescriptionZh string `form:"description_zh" binding:"required"`
	Image string `form:"image" binding:"required"`
	Weight int `form:"weight" binding:""`
	IsRecommend int `form:"is_recommend" binding:""`
	CategoryIds string `form:"category_ids" binding:""`
}



const SUCCESS = 1
const PARAM_ERROR = 2
const TIME_CONFILICT = 3

func (r FoodUpdateRequest) ValidateOpenTime() (int64,string) {

	db := tools.Db
	var restaurant models.Restaurant
	db.Model(&restaurant).Where("id = ?", r.RestaurantID).Find(&restaurant)

	if r.isValidTimeFormat(r.BeginTime) && r.isValidTimeFormat(r.EndTime) {
		foodStartTime := r.BeginTime + ":00"
		foodEndTime := r.EndTime + ":00"

		startTime := restaurant.BeginTime
		endTime := restaurant.EndTime

		if tools.ResTimeFoodTimeValid(startTime, endTime, foodStartTime, foodEndTime) == false{
			return TIME_CONFILICT,""
		}
	}else{
		return PARAM_ERROR,"time_param_format_error"
	}
	return SUCCESS,""
}
func (r FoodUpdateRequest)isValidTimeFormat(timeStr string) bool {
	// 正则表达式匹配时间格式 HH:MM，范围 00:00 到 23:59
	re := regexp.MustCompile(`^([01]?[0-9]|2[0-3]):[0-5][0-9]$`)
	return re.MatchString(timeStr)
}




func (r FoodCreateRequest) ValidateOpenTime() (int64,string) {

	db := tools.Db
	var restaurant models.Restaurant
	db.Model(&restaurant).Where("id = ?", r.RestaurantID).Find(&restaurant)

	if r.isValidTimeFormat(r.BeginTime) && r.isValidTimeFormat(r.EndTime) {
		foodStartTime := r.BeginTime + ":00"
		foodEndTime := r.EndTime + ":00"

		startTime := restaurant.BeginTime
		endTime := restaurant.EndTime

		if tools.ResTimeFoodTimeValid(startTime, endTime, foodStartTime, foodEndTime) == false{
			return TIME_CONFILICT,""
		}
	}else{
		return PARAM_ERROR,"time_param_format_error"
	}
	return SUCCESS,""
}
func (r FoodCreateRequest)isValidTimeFormat(timeStr string) bool {
	// 正则表达式匹配时间格式 HH:MM，范围 00:00 到 23:59
	re := regexp.MustCompile(`^([01]?[0-9]|2[0-3]):[0-5][0-9]$`)
	return re.MatchString(timeStr)
}


type FoodCreateRequestNew struct {
	RestaurantID int64 `form:"restaurant_id" binding:""`
	NameUg string `form:"name_ug" binding:"required"`
	NameZh string `form:"name_zh" binding:"required"`
	Price float64 `form:"price" binding:"required"`
	ReadyTime int64 `form:"ready_time" binding:"required"`
	BeginTime string `form:"begin_time" binding:"required"`
	EndTime string `form:"end_time" binding:"required"`
	DescriptionUg string `form:"description_ug" binding:"required"`
	DescriptionZh string `form:"description_zh" binding:"required"`
	Image string `form:"image" binding:"required"`
	MinCount int `form:"min_count" binding:"required"`
	Weight int `form:"weight" binding:"required"`
	IsRecommend int `form:"is_recommend" binding:""`
	CategoryIds string `form:"category_ids" binding:"required"`
	FoodsGroupId int `form:"foods_group_id" binding:"required"`
}



func (r FoodCreateRequestNew) ValidateOpenTime() (int64,string) {

	db := tools.Db
	var restaurant models.Restaurant
	db.Model(&restaurant).Where("id = ?", r.RestaurantID).Find(&restaurant)

	if r.isValidTimeFormat(r.BeginTime) && r.isValidTimeFormat(r.EndTime) {
		foodStartTime := r.BeginTime + ":00"
		foodEndTime := r.EndTime + ":00"

		startTime := restaurant.BeginTime
		endTime := restaurant.EndTime

		if tools.ResTimeFoodTimeValid(startTime, endTime, foodStartTime, foodEndTime) == false{
			return TIME_CONFILICT,""
		}
	}else{
		return PARAM_ERROR,"time_param_format_error"
	}
	return SUCCESS,""
}
func (r FoodCreateRequestNew)isValidTimeFormat(timeStr string) bool {
	// 正则表达式匹配时间格式 HH:MM，范围 00:00 到 23:59
	re := regexp.MustCompile(`^([01]?[0-9]|2[0-3]):[0-5][0-9]$`)
	return re.MatchString(timeStr)
}

type FoodUpdateRequestNew struct {
	ID int `form:"id" binding:"required"`
	RestaurantID int64 `form:"restaurant_id" binding:""`
	NameUg string `form:"name_ug" binding:"required"`
	NameZh string `form:"name_zh" binding:"required"`
	Price float64 `form:"price" binding:"required"`
	ReadyTime int64 `form:"ready_time" binding:"required"`
	BeginTime string `form:"begin_time" binding:"required"`
	EndTime string `form:"end_time" binding:"required"`
	DescriptionUg string `form:"description_ug" binding:"required"`
	DescriptionZh string `form:"description_zh" binding:"required"`
	Image string `form:"image" binding:"required"`
	MinCount int `form:"min_count" binding:"required"`
	Weight int `form:"weight" binding:""`
	IsRecommend int `form:"is_recommend" binding:""`
	CategoryIds string `form:"category_ids" binding:"required"`
	FoodsGroupId int `form:"foods_group_id" binding:"required"`
}

func (r FoodUpdateRequestNew) ValidateOpenTime() (int64,string) {

	db := tools.Db
	var restaurant models.Restaurant
	db.Model(&restaurant).Where("id = ?", r.RestaurantID).Find(&restaurant)

	if r.isValidTimeFormat(r.BeginTime) && r.isValidTimeFormat(r.EndTime) {
		foodStartTime := r.BeginTime + ":00"
		foodEndTime := r.EndTime + ":00"

		startTime := restaurant.BeginTime
		endTime := restaurant.EndTime

		if tools.ResTimeFoodTimeValid(startTime, endTime, foodStartTime, foodEndTime) == false{
			return TIME_CONFILICT,""
		}
	}else{
		return PARAM_ERROR,"time_param_format_error"
	}
	return SUCCESS,""
}
func (r FoodUpdateRequestNew)isValidTimeFormat(timeStr string) bool {
	// 正则表达式匹配时间格式 HH:MM，范围 00:00 到 23:59
	re := regexp.MustCompile(`^([01]?[0-9]|2[0-3]):[0-5][0-9]$`)
	return re.MatchString(timeStr)
}
