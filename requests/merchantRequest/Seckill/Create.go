package Seckill

type SeckillCreateParams struct {
	BeginTime         string `form:"begin_time" json:"begin_time" binding:"required"`
	FoodId            int64  `form:"food_id" json:"food_id" binding:"required"`
	FoodType          uint8  `form:"food_type" json:"food_type" binding:""`
	OptionIds []int `form:"option_ids" json:"option_ids" binding:""`
	TotalCount        int64  `form:"total_count" json:"total_count" `
	Price             int64  `form:"price" json:"price" binding:"required"`
	UserMaxOrderCount int64  `form:"user_max_order_count" json:"user_max_order_count" binding:"required"`
}
