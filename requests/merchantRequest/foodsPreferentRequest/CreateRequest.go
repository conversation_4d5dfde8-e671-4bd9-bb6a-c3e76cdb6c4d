package foodsPreferentRequest

import (
	"errors"
	"time"
)

const (
	PreferentialStyleSingle = 1
	PreferentialStyleAll    = 2
)

type CreateRequest struct {
	Type                   int    `json:"type" binding:"required,oneof=1"`
	PreferentialStyle      int    `json:"preferential_style" binding:"required,oneof=1 2"` // 1 : 单商品优惠， 2： 商家全部商品折扣
	StartTime              string `json:"start_time" binding:"required,datetime=15:04:00"`
	EndTime                string `json:"end_time" binding:"required,datetime=15:04:00"`
	StartDateTime          string `json:"start_date_time" binding:"required,datetime=2006-01-02"`
	EndDateTime            string `json:"end_date_time" binding:"required,datetime=2006-01-02"`
	MaxOrderCount          int    `json:"max_order_count" binding:"required,min=1,max=999"`
	OrderCountPerDay       int    `json:"order_count_per_day" binding:""`
	FoodID                 int    `json:"food_id" binding:""`
	PreferentialPercentage uint   `json:"preferential_percentage" binding:""` // 折扣百分比, 当商家全部商品这周有效
	DiscountPrice          uint   `json:"discount_price" binding:""`          // 折扣价格, 单位： 分

	FoodType uint8 `json:"food_type"`          // 美食类型 0: 普通美食 1: 规格美食 2: 套餐美食
	//SpecID   int   `json:"spec_id" binding:""` // 规格ID
	OptionIds []int `form:"option_ids" json:"option_ids" binding:""` // 美食规格子项 ID
}

// Validate 验证
func (r *CreateRequest) Validate() error {
	var (
		startTime, _     = time.Parse("15:04:00", r.StartTime)
		endTime, _       = time.Parse("15:04:00", r.EndTime)
		startDateTime, _ = time.Parse("2006-01-02", r.StartDateTime)
		endDateTime, _   = time.Parse("2006-01-02", r.EndDateTime)
	)
	if startTime.After(endTime) {
		return errors.New("start_time_must_before_end_time")
	}
	if startDateTime.After(endDateTime) {
		return errors.New("start_date_time_must_before_end_date_time")
	}
	if r.PreferentialStyle == 1 {
		if r.FoodID <= 0 {
			return errors.New("food_id_required")
		}
		if r.DiscountPrice <= 0 {
			return errors.New("discount_price_required")
		}
	}
	if r.PreferentialStyle == 2 {
		if r.PreferentialPercentage <= 0 || r.PreferentialPercentage >= 100 {
			return errors.New("preferential_percentage_required")
		}
	}
	return nil
}
