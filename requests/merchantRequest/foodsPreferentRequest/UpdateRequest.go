package foodsPreferentRequest

import (
	"errors"
	"time"
)

type UpdateRequest struct {
	Id               uint   `json:"id" binding:"required,min=1"`
	DiscountPrice    uint   `json:"discount_price" binding:"required,min=1"`
	MaxOrderCount    int    `json:"max_order_count" binding:"required,min=1"`
	OrderCountPerDay *int   `json:"order_count_per_day" binding:"required,oneof=0 1"`
	StartTime        string `json:"start_time" binding:"required,datetime=15:04:05"`
	EndTime          string `json:"end_time" binding:"required,datetime=15:04:05"`
	StartDateTime    string `json:"start_date_time" binding:"required,datetime=2006-01-02"`
	EndDateTime      string `json:"end_date_time" binding:"required,datetime=2006-01-02"`
	State            *int   `json:"state" binding:"required,oneof=0 1"`
}

// Validate 验证
func (r *UpdateRequest) Validate() error {
	var (
		startDateTime, _ = time.Parse("2006-01-02", r.StartDateTime)
		endDateTime, _   = time.Parse("2006-01-02 15:04:05", r.EndDateTime+" 23:59:59")
	)
	if startDateTime.After(endDateTime) {
		return errors.New("start_date_time_must_before_end_date_time")
	}
	if time.Now().After(endDateTime) {
		return errors.New("end_date_time_must_after_today")
	}
	return nil
}
