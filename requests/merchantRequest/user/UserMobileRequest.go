package user

type UserMobileSendCodeRequest struct {
	Mobile string `form:"mobile" binding:"required,len=11,phone,exists=t_admin mobile ((type =5 or type =6) and state = 1 and deleted_at is null)"`
}

type UserMobileCodeVerifyRequest struct {
	Mobile string `form:"mobile" binding:"required,len=11,phone,exists=t_admin mobile ((type =5 or type =6) and state = 1 and deleted_at is null)"`
	Code string `form:"check_code" binding:"required,len=6"`
}

type UserChangeSMSPasswordRequest struct {
	Mobile string `form:"mobile" binding:"required,len=11,phone,exists=t_admin mobile ((type =5 or type =6) and state = 1 and deleted_at is null)"`
	Password string `form:"password" binding:"required,min=8,password"`
}

type UserChangePasswordRequest struct {
	OldPassword string `form:"old_password" binding:"required,min=8,password"`
	Password string `form:"password" binding:"required,min=8,password"`
	PasswordConfirm string `form:"password_confirmation" binding:"required,min=8,password"`
}