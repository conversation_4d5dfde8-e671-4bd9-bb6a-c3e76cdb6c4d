package marketingRequest

import (
	"errors"
	"fmt"
	"math"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/tools"
	"time"

	"github.com/golang-module/carbon/v2"
)

type ShipmentReduceRequest struct {
	ID               int                  `json:"id"`
	RestaurantID     int                  `json:"restaurant_id" binding:""`
	NameUg           string               `json:"name_ug" binding:"required"`
	NameZh           string               `json:"name_zh" binding:"required"`
	BeginDate        string               `json:"begin_date" binding:"required"`
	EndDate          string               `json:"end_date" binding:"required"`
	FullWeekState    string               `json:"full_week_state"`
	FullTimeState    string               `json:"full_time_state"`
	AutoContinue     string               `json:"auto_continue" binding:""`
	Day              int                  `json:"day"`
	MinDeliveryPrice int                  `json:"min_delivery_price"`
	Steps            []ShipmentReduceStep `json:"steps" json:"steps" binding:"required"`
	Timelines        []Timeline           `json:"time_lines" json:"time_lines"` // 时间段 	// 有效时间段3结束时间
}

// ValidateData 检查数据合法性
func (r *ShipmentReduceRequest) MerchantValidateData(language lang.LangUtil) (bool, error) {
	// language := lang.LangUtil{}
	if configs.MyApp.MinDeliveryPrice > r.MinDeliveryPrice {
		msg := fmt.Sprintf(language.T("min_delivery_price_can_not_be_less_than_system_config"), float64(configs.MyApp.MinDeliveryPrice)/100)
		// msg := fmt.Sprintf(language.T("min_delivery_price_can_not_be_less_than_system_config"), float64(configs.MyApp.MinDeliveryPrice)/100)
		return false, errors.New(msg)
	}
	// 检查工作日合法性
	if ok, err := r.CheckDayInTimeRange(); !ok {
		return ok, err
	}
	// 检查时间段合法性
	if ok, err := r.ValidateTimeline(); !ok {
		return ok, err
	}
	// 检查阶梯合法性
	if ok, err := r.ValidateSteps(); !ok {
		return ok, err
	}
	return true, nil
}

func (r *ShipmentReduceRequest) ValidateTimeline() (bool, error) {
	// 全天开启
	if tools.ToInt(r.FullTimeState) == 0 {
		var timeLines []Timeline
		timeLines = append(timeLines, Timeline{
			Start: "00:00",
			End:   "23:59",
		})
		r.Timelines = timeLines
		return true, nil
	}
	timelineLen := len(r.Timelines)
	if timelineLen > 3 {
		return false, errors.New("timeline_can_not_be_more_than_three_item")
	}
	if timelineLen == 0 {
		return false, errors.New("timeline_must_be_at_least_one")
	}
	for i := 0; i < timelineLen; i++ {
		if ok, err := r.Timelines[i].Validate(); !ok {
			return ok, err
		}
		if i > 0 {
			currentStartTime, _ := time.Parse("15:04", r.Timelines[i].Start)
			currentEndTime, _ := time.Parse("15:04", r.Timelines[i].End)
			previousEndTime, _ := time.Parse("15:04", r.Timelines[i-1].End)
			if !currentStartTime.Before(currentEndTime) {
				return false, errors.New("begin_time_must_be_after_time")
			}
			if currentStartTime.Before(previousEndTime) {
				return false, errors.New("timeline_can_not_overlap")
			}
		}
	}
	return true, nil
}

func (r *ShipmentReduceRequest) ValidateSteps() (bool, error) {
	stepsLen := len(r.Steps)
	if stepsLen == 0 {
		return false, errors.New("shipment_reduce_step_must_be_at_least_one")
	}
	r.Steps[0].DistanceStart = 0
	r.Steps[stepsLen-1].DistanceEnd = 999000 // 最后一个step结束距离设置为999千米（默认值）
	for i := 0; i < stepsLen; i++ {
		if ok, err := r.Steps[i].Validate(); !ok {
			return ok, err
		}
		if i > 0 {
			if r.Steps[i].DistanceStart != r.Steps[i-1].DistanceEnd {
				return false, errors.New("shipment_reduce_step_start_must_be_equal_to_previous_step_end")
			}
		}
	}
	return true, nil
}

// CheckDayInTimeRange 检查星期几是否在时间范围内函数
func (r *ShipmentReduceRequest) CheckDayInTimeRange() (bool, error) {
	beginDate := carbon.Parse(r.BeginDate)
	endDate := carbon.Parse(r.EndDate)
	diffDay := int(beginDate.DiffInDays(endDate))
	today := carbon.Parse(time.Now().Format("2006-01-02"))
	if today.Gt(beginDate) {
		return false, errors.New("begin_time_must_be_after_today")

	}
	if beginDate.Gt(endDate) {
		return false, errors.New("begin_time_must_be_after_today")

	}
	if tools.ToInt(r.FullWeekState) == 0 {
		r.Day = 127
		return true, nil
	}
	if r.Day < 0 || r.Day > 127 {
		return false, errors.New("wrong_weekday_selected")
	}
	// 如果选择了星期几全部，直接返回true
	if r.Day == 127 {
		r.FullWeekState = "1"
		return true, nil
	}
	// 没有选择星期几，直接返回false
	if r.Day == 1 {
		return false, errors.New("begin_time_must_be_after_today")
	}

	// 如果两个时间段相差大于等于大于7天，说明是全天有效
	if diffDay >= 6 {
		return true, nil
	}

	// 0 到 diffDay 循环
	// 有效的工作日
	var notValidWeekDay int = r.Day
	for i := 0; i <= diffDay; i++ {
		// 获取当前循环的日期
		currentDate := beginDate.AddDays(i)
		// 获取当前循环的日期是星期几
		currentDay := currentDate.DayOfWeek()
		weekDayValue := int(math.Pow(2, float64(7-currentDay)))
		// 如果当日所属于的工作日在用户选择的工作内删除该工作日，逐步删除只剩下该时间段没有的工作日
		if notValidWeekDay&weekDayValue != 0 {
			notValidWeekDay -= weekDayValue
		}
	}
	// 如果没有无效的工作日返回成功
	if notValidWeekDay == 0 {
		return true, nil
	}
	// 逐步检查那些工作日不在选择的时间段返回相应的错误信息
	notValidWeekDayStr := fmt.Sprintf("%07b", notValidWeekDay)
	// 从星期一开始检查是否存在给定的时间段不包含的工作日
	for i := 0; i < 7; i++ {
		if notValidWeekDayStr[i] == '1' {
			return false, errors.New(fmt.Sprintf("day_%d_not_in_range", i+1))
		}
	}
	return true, nil
}
