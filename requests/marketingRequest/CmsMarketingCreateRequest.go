package marketingRequest

import (
	"errors"
	"fmt"
	"github.com/golang-module/carbon/v2"
	"math"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"time"
)

// ShipmentReduceStep 满减活动减免规则阶梯结构
type ShipmentReduceStep struct {
	DistanceStart int `form:"distance_start" json:"distance_start" binding:"required"` // 开始距离
	DistanceEnd   int `form:"distance_end" json:"distance_end" binding:"required"`     // 结束距离
	PriceReduce   int `form:"price_reduce" json:"price_reduce" binding:"required"`     // 减免金额
	StoreReduce   int `form:"store_reduce" json:"store_reduce" binding:"required"`     // 商家承担金额
	DealerReduce  int `form:"dealer_reduce" json:"dealer_reduce" binding:"required"`   // 代理承担金额
}

// Validate ShimentReduceStep 检查减配送费活动阶梯合法性
func (s *ShipmentReduceStep) Validate() (bool, error) {
	if s.DistanceStart < 0 {
		return false, errors.New("shipment_reduce_step_start_must_be_positive")
	}

	if s.DistanceEnd < s.DistanceStart {
		return false, errors.New("shipment_reduce_step_end_must_be_bigger_than_start")
	}
	if s.PriceReduce < 0 {
		return false, errors.New("shipment_reduce_step_reduce_must_be_positive")
	}
	if s.StoreReduce < 0 {
		return false, errors.New("shipment_reduce_store_price_must_be_positive")
	}
	if s.DealerReduce < 0 {
		return false, errors.New("shipment_reduce_dealer_price_must_be_positive")
	}
	if s.StoreReduce+s.DealerReduce != s.PriceReduce {
		return false, errors.New("shipment_reduce_step_reduce_price_not_equal_to_sum_of_store_price_and_dealer_price")

	}
	return true, nil
}

// Timeline 活动时间段结构
type Timeline struct {
	Start string `form:"start" json:"start" binding:"required,datetime=15:04"` // 有效时间段1开始时间
	End   string `form:"end" json:"end" binding:"required,datetime=15:04"`     // 有效时间段1结束时间
}

// Validate Timeline 检查时间段合法性
func (t *Timeline) Validate() (bool, error) {
	if t.Start == "" {
		return false, errors.New("timeline_start_can_not_be_empty")
	}
	if t.End == "" {
		return false, errors.New("timeline_end_can_not_be_empty")
	}
	if t.Start != "" && t.End != "" {
		startTime, err := time.Parse("15:04", t.Start)
		if err != nil {
			return false, errors.New("timeline_start_format_error")
		}
		endTime, err := time.Parse("15:04", t.End)
		if err != nil {
			return false, errors.New("timeline_end_format_error")
		}
		if startTime.After(endTime) {
			return false, errors.New("timeline_start_must_be_before_end")
		}
		return true, nil
	}
	return false, errors.New("timeline_start_and_end_can_not_be_empty")
}

// CmsMarketingCreateRequest 活动创建请求结构
type CmsMarketingCreateRequest struct {
	AreaId           *int                 `form:"area_id" json:"area_id"`
	IsAllRestaurant  *int                 `form:"is_all_restaurant" json:"is_all_restaurant" binding:"required"`
	RestaurantIds    []int                `form:"restaurant_ids" json:"restaurant_ids"`             // 店铺id
	NameZh           string               `form:"name_zh" json:"name_zh" binding:"required,lt=255"` // 活动名称
	NameUg           string               `form:"name_ug" json:"name_ug" binding:"required,lt=255"` // 活动名称
	BeginDate        string               `form:"begin_date" json:"begin_date" binding:"required,datetime=2006-01-02" time_format:"2006-01-02"`
	EndDate          string               `form:"end_date" json:"end_date" binding:"required,datetime=2006-01-02" time_format:"2006-01-02"`
	FullWeekState    int                  ``                                                                       // 是否全周有效
	Day              int                  `form:"day" json:"day" binding:"required"`                               // 选择的星期几七位二进制形式转十进制保存 第一位星期日开始
	FullTimeState    *int                 `form:"full_time_state" json:"full_time_state" binding:"required"`       // 是否全天有效
	Timelines        []Timeline           `form:"timelines" json:"timelines"`                                      // 时间段 	// 有效时间段3结束时间
	AutoContinue     *int                 `form:"auto_continue" json:"auto_continue" binding:"required"`           // 是否自动续期
	MinDeliveryPrice int                  `form:"min_delivery_price" json:"min_delivery_price" binding:"required"` // 最低起送价
	Steps            []ShipmentReduceStep `form:"steps" json:"steps" binding:"required"`                           // 减配送费阶梯阶梯
}

// CheckDayInTimeRange 检查星期几是否在时间范围内函数
func (r *CmsMarketingCreateRequest) CheckDayInTimeRange() (bool, error) {
	if r.Day < 0 || r.Day > 127 {
		return false, errors.New("wrong_weekday_selected")
	}
	// 如果选择了星期几全部，直接返回true
	if r.Day == 127 {
		r.FullWeekState = 0
		return true, nil
	} else {
		r.FullWeekState = 1
	}
	// 没有选择星期几，直接返回false
	if r.Day == 0 {
		return false, errors.New("begin_time_must_be_after_today")
	}
	beginDate := carbon.Parse(r.BeginDate)
	endDate := carbon.Parse(r.EndDate)
	diffDay := int(beginDate.DiffInDays(endDate))
	today := carbon.Parse(time.Now().Format("2006-01-02"))

	if today.Gte(beginDate) {
		return false, errors.New("begin_time_must_be_after_today")

	}

	// 如果两个时间段相差大于等于大于7天，说明是全天有效
	if diffDay >= 6 {
		return true, nil
	}

	// 0 到 diffDay 循环
	// 有效的工作日
	var notValidWeekDay int = r.Day
	for i := 0; i <= diffDay; i++ {
		// 获取当前循环的日期
		currentDate := beginDate.AddDays(i)
		// 获取当前循环的日期是星期几
		currentDay := currentDate.DayOfWeek()
		weekDayValue := int(math.Pow(2, float64(7-currentDay)))
		// 如果当日所属于的工作日在用户选择的工作内删除该工作日，逐步删除只剩下该时间段没有的工作日
		if notValidWeekDay&weekDayValue != 0 {
			notValidWeekDay -= weekDayValue
		}
	}
	// 如果没有无效的工作日返回成功
	if notValidWeekDay == 0 {
		return true, nil
	}
	// 逐步检查那些工作日不在选择的时间段返回相应的错误信息
	notValidWeekDayStr := fmt.Sprintf("%07b", notValidWeekDay)
	// 从星期一开始检查是否存在给定的时间段不包含的工作日
	for i := 0; i < 7; i++ {
		if notValidWeekDayStr[i] == '1' {
			return false, errors.New(fmt.Sprintf("day_%d_not_in_range", i+1))
		}
	}
	return true, nil
}

func (r *CmsMarketingCreateRequest) ValidateSteps() (bool, error) {
	stepsLen := len(r.Steps)
	if stepsLen == 0 {
		return false, errors.New("shipment_reduce_step_must_be_at_least_one")
	}
	r.Steps[0].DistanceStart = 0
	r.Steps[stepsLen-1].DistanceEnd = 999000 // 最后一个step结束距离设置为999千米（默认值）
	for i := 0; i < stepsLen; i++ {
		if ok, err := r.Steps[i].Validate(); !ok {
			return ok, err
		}
		if i > 0 {
			if r.Steps[i].DistanceStart != r.Steps[i-1].DistanceEnd {
				return false, errors.New("shipment_reduce_step_start_must_be_equal_to_previous_step_end")
			}
		}
	}
	return true, nil
}

// ValidateTimeline 检查时间段合法性
func (r *CmsMarketingCreateRequest) ValidateTimeline() (bool, error) {
	// 全天开启
	if *r.FullTimeState == 0 {
		r.Timelines = []Timeline{
			{
				Start: "00:00",
				End:   "23:59",
			},
		}
		return true, nil
	}
	timelineLen := len(r.Timelines)
	if timelineLen > 3 {
		return false, errors.New("timeline_can_not_be_more_than_three_item")
	}
	if timelineLen == 0 {
		return false, errors.New("timeline_must_be_at_least_one")
	}
	for i := 0; i < timelineLen; i++ {
		if ok, err := r.Timelines[i].Validate(); !ok {
			return ok, err
		}
		if i > 0 {
			currentStartTime, _ := time.Parse("15:04", r.Timelines[i].Start)
			previousEndTime, _ := time.Parse("15:04", r.Timelines[i-1].End)
			if currentStartTime.Before(previousEndTime) {
				return false, errors.New("timeline_can_not_overlap")
			}
		}
	}
	return true, nil
}

// ValidateData 检查数据合法性
func (r *CmsMarketingCreateRequest) ValidateData(language lang.LangUtil) (bool, error) {

	if *r.IsAllRestaurant != 0 {
		*r.IsAllRestaurant = 1
		r.RestaurantIds = []int{}
		//return true, nil
	} else if len(r.RestaurantIds) == 0 {
		return false, errors.New("restaurant_ids_can_not_be_empty")
	}
	if configs.MyApp.MinDeliveryPrice > r.MinDeliveryPrice {
		msg := fmt.Sprintf(language.T("min_delivery_price_can_not_be_less_than_system_config"), float64(configs.MyApp.MinDeliveryPrice)/100)
		return false, errors.New(msg)
	}
	// 检查工作日合法性
	if ok, err := r.CheckDayInTimeRange(); !ok {
		return ok, err
	}
	// 检查时间段合法性
	if ok, err := r.ValidateTimeline(); !ok {
		return ok, err
	}
	// 检查阶梯合法性
	if ok, err := r.ValidateSteps(); !ok {
		return ok, err
	}
	return true, nil
}
