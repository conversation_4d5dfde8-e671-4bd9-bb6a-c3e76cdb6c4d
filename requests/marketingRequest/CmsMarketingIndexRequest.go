package marketingRequest

import (
	"errors"

	"github.com/golang-module/carbon/v2"
)

type CmsMarketingIndexRequest struct {
	CityId       int    `json:"city_id" form:"city_id"`                                              //城市ID
	AreaId       int    `json:"area_id" form:"area_id"`                                              // 区域ID
	RestaurantId int    `json:"restaurant_id" form:"restaurant_id"`                                  // 餐厅ID
	State        int    `json:"state" form:"state" binding:"omitempty,required,min=-1,max=4"`        // 状态
	BeginDate    string `json:"begin_date" form:"begin_date" binding:"required,datetime=2006-01-02"` // 开始时间
	EndDate      string `json:"end_date" form:"end_date" binding:"required,datetime=2006-01-02"`     // 结束时间
	Page         int    `json:"page" form:"page" binding:"required,min=1"`                           // 页码
	Limit        int    `json:"limit" form:"limit" binding:"required,min=1,max=100"`                 // 每页数量
	Keyword      string `json:"keyword" form:"keyword"`                                              // 关键字
	Total        int    `json:"total"`                                                               // 总数
	SortBy       string `json:"sort_by" form:"sort_by" binding:"omitempty,oneof=created_at"`         // 排序字段
	SortType     string `json:"sort_type" form:"sort_type" binding:"omitempty,oneof=asc desc"`       // 排序类型
}

// Validate 验证表单
func (r *CmsMarketingIndexRequest) Validate() error {
	var (
		beginDate carbon.Carbon
		endDate   carbon.Carbon
	)
	if len(r.BeginDate) > 0 {
		beginDate = carbon.ParseByLayout(r.BeginDate, "2006-01-02")
		if beginDate.IsFuture() {
			return errors.New("begin_date_can_not_feature")
		}
	}
	if len(r.EndDate) > 0 {
		endDate = carbon.ParseByLayout(r.EndDate, "2006-01-02")
		if len(r.BeginDate) > 0 && beginDate.Gt(endDate) {
			return errors.New("end_date_can_not_before_begin_date")
		}
		// if len(r.BeginDate) > 0 && beginDate.DiffInDays(endDate) > 30 {
		// 	return errors.New("date_range_can_not_over_31_days")
		// }
	}

	return nil
}

type CmsMarketingDeleteRequest struct {
	Ids []int `form:"ids" json:"ids"` // 店铺id
}

func (r *CmsMarketingDeleteRequest) Validate() error {
	if len(r.Ids) == 0 {

		return errors.New("not_empty")

	}
	return nil
}

type CmsMarketingGroupTemplateSatateRequest struct {
	Ids   []int `form:"ids" json:"ids" binding:"required"`               // 店铺id
	State int   `form:"state" json:"state" binding:"required,oneof=1 2"` // 状态
}

type CmsMarketingGroupSendJoinActivityMessageRequest struct {
	Ids []int `form:"ids" json:"ids" binding:"required"` //  店铺签名页面id
}

type CmsMarketingGroupSendJoinActivityMessageRequestOne struct {
	Id int `form:"id" json:"id" binding:"required"` //  店铺签名页面id
}
