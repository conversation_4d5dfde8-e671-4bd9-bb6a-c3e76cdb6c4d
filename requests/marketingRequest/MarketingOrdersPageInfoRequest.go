package marketingRequest

import (
	"errors"
	"github.com/golang-module/carbon/v2"
)

type MarketingOrderPageInfo struct {
	Page      int    `json:"page" form:"page" query:"page" binding:"required,min=1"`
	Limit     int    `json:"limit" form:"limit" query:"limit" binding:"required,min=10,max=100"`
	Total     int64  `json:"total"`
	Keyword   string `json:"keyword" form:"keyword" query:"keyword"`
	BeginDate string `json:"begin_date" form:"begin_date" query:"begin_date" binding:"required,datetime=2006-01-02"`
	EndDate   string `json:"end_date" form:"end_date" query:"end_date" binding:"required,datetime=2006-01-02"`
	SortBy    string `json:"sort_by" form:"sort_by" binding:"omitempty,oneof=created_at"`   // 排序字段
	SortType  string `json:"sort_type" form:"sort_type" binding:"omitempty,oneof=asc desc"` // 排序类型
}

func (s *MarketingOrderPageInfo) Validate() error {
	if s.<PERSON> < 1 {
		s.Page = 1
	}
	if s.Limit < 1 {
		s.Limit = 10
	}
	if s.Limit > 50 {
		s.Limit = 10
	}
	if s.BeginDate != "" && s.EndDate != "" {
		beginDate := carbon.ParseByLayout(s.BeginDate, "2006-01-02")
		endDate := carbon.ParseByLayout(s.EndDate, "2006-01-02")
		if beginDate.IsFuture() {
			return errors.New("begin_date_can_not_feature")
		}
		if endDate.IsFuture() {
			return errors.New("end_date_can_not_feature")
		}
		if endDate.Lt(beginDate) {
			return errors.New("end_date_can_not_before_begin_date")
		}
		if beginDate.DiffInDays(endDate) > 30 {
			return errors.New("date_range_can_not_over_31_days")

		}
	}
	return nil
}

type MarketingOrdersDownloadRequest struct {
	Keyword   string `json:"keyword" form:"keyword" query:"keyword"`
	BeginDate string `json:"begin_date" form:"begin_date" query:"begin_date" binding="datetime=2006-01-02"`
	EndDate   string `json:"end_date" form:"end_date" query:"end_date" binding="datetime=2006-01-02"`
}

func (s *MarketingOrdersDownloadRequest) Validate() error {
	if s.BeginDate != "" && s.EndDate != "" {
		beginDate := carbon.ParseByLayout(s.BeginDate, "2006-01-02")
		endDate := carbon.ParseByLayout(s.EndDate, "2006-01-02")
		if beginDate.IsFuture() {
			return errors.New("begin_date_can_not_feature")
		}
		if endDate.IsFuture() {
			return errors.New("end_date_can_not_feature")
		}
		if endDate.Lt(beginDate) {
			return errors.New("end_date_can_not_before_begin_date")
		}
		if beginDate.DiffInDays(endDate) > 30 {
			return errors.New("date_range_can_not_over_31_days")

		}
	}
	return nil
}
