package marketingRequest

import (
	"encoding/json"
	"mulazim-api/models"
	"mulazim-api/tools"
	"regexp"
)

type MerchantInfoUpdateRequest struct {
	RestaurantID   int    `form:"restaurant_id" binding:"required" json:"restaurant_id"`
	Type           int    `form:"type" binding:"required,min=1" json:"type"`

	OpenTime       *string `form:"open_time" json:"open_time"`
	CloseTime      *string `form:"close_time" json:"close_time"`
	//长度不许超过12位
	Tel            *string `form:"tel" binding:"required,min=7,max=12" json:"tel"`
	Tel2           string `form:"tel2" binding:"omitempty,max=12,min=0|min=7" json:"tel2"`
	Tel3           string `form:"tel3" binding:"omitempty,max=12,min=0|min=7" json:"tel3"`
	Tel4           string `form:"tel4" binding:"omitempty,max=12,min=0|min=7" json:"tel4"`
	Tel5           string `form:"tel5" binding:"omitempty,max=12,min=0|min=7" json:"tel5"`
	NameZH         string `form:"name_zh" binding:"required,min=1" json:"name_zh"`
	NameUG         string `form:"name_ug" binding:"required,min=1" json:"name_ug"`
	AddressZH      string `form:"address_zh" binding:"required,min=1" json:"address_zh"`
	AddressUG      string `form:"address_ug" binding:"required,min=1" json:"address_ug"`
	DescriptionZH  string `form:"description_zh" binding:"required,min=1" json:"description_zh"`
	DescriptionUG  string `form:"description_ug" binding:"required,min=1" json:"description_ug"`
	CanSelfTake    *int    `form:"can_self_take" json:"can_self_take"`
}
const SUCCESS = 1
const PARAM_ERROR = 2
const TIME_CONFILICT = 3
func (r MerchantInfoUpdateRequest) ValidateOpenTime() (int64,string) {
	db := tools.Db
	var foods []models.RestaurantFoods
	db.Model(&foods).Where("restaurant_id = ? and state > 0 and deleted_at is null", r.RestaurantID).Find(&foods)
	if r.OpenTime != nil && r.CloseTime == nil {
		return PARAM_ERROR,"请填写餐厅关闭时间"
	}
	if r.OpenTime == nil && r.CloseTime != nil {
		return PARAM_ERROR,"请填写餐厅开放时间"
	}
	if r.OpenTime != nil && r.CloseTime != nil {
		if r.isValidTimeFormat(*r.OpenTime) && r.isValidTimeFormat(*r.CloseTime) {
			startTime := *r.OpenTime + ":00"
			endTime := *r.CloseTime + ":00"
			for _, food := range foods {
				foodStartTime := food.BeginTime
				foodEndTime := food.EndTime
				if tools.ResTimeFoodTimeValid(startTime, endTime, foodStartTime, foodEndTime) == false{
					errMap := map[string]string{
						"food_name_ug": food.NameUg,
						"food_name_zh": food.NameZh,
					}
					errByte,_ :=json.Marshal(errMap)
					return TIME_CONFILICT,string(errByte)
				}

			}
		}else{
			return PARAM_ERROR,"时间格式错误"
		}
	}
	return SUCCESS,""
}
func (r MerchantInfoUpdateRequest)isValidTimeFormat(timeStr string) bool {
	// 正则表达式匹配时间格式 HH:MM，范围 00:00 到 23:59
	re := regexp.MustCompile(`^([01]?[0-9]|2[0-3]):[0-5][0-9]$`)
	return re.MatchString(timeStr)
}
