package marketingRequest

import (
	"errors"

	"github.com/golang-module/carbon/v2"
)

type CmsMarketingGroupDetailRestaurantRequest struct {
	Id        int    `json:"id" form:"id" binding:"required,min=1"`
	BeginDate string `json:"begin_date" form:"begin_date" binding:"omitempty,datetime=2006-01-02"` // 开始时间
	EndDate   string `json:"end_date" form:"end_date" binding:"omitempty,datetime=2006-01-02"`     // 结束时间
	Page      int    `json:"page" form:"page" binding:"required,min=1"`                            // 页码
	Limit     int    `json:"limit" form:"limit" binding:"min=1,max=100"`                           // 每页数量
	Keyword   string `json:"keyword" form:"keyword"`                                               // 关键字
	Total     int    `json:"total"`                                                                // 总数
	SortBy    string `json:"sort_by" form:"sort_by" binding:"omitempty,oneof=created_at"`          // 排序字段
	SortType  string `json:"sort_type" form:"sort_type" binding:"omitempty,oneof=asc desc"`        // 排序类型
	State     int    `json:"state" form:"state"`
}

// Validate 验证表单
func (r *CmsMarketingGroupDetailRestaurantRequest) Validate() error {
	var (
		beginDate carbon.Carbon
		endDate   carbon.Carbon
	)
	if len(r.BeginDate) > 0 {
		beginDate = carbon.ParseByLayout(r.BeginDate, "2006-01-02")
		if beginDate.IsFuture() {
			return errors.New("begin_date_can_not_feature")
		}
	}
	if len(r.EndDate) > 0 {
		endDate = carbon.ParseByLayout("2006-01-02", r.EndDate)
		if len(r.BeginDate) > 0 && beginDate.Gt(endDate) {
			return errors.New("end_date_can_not_before_begin_date")
		}
		if len(r.BeginDate) > 0 && beginDate.DiffInDays(endDate) > 31 {
			return errors.New("date_range_can_not_over_30_days")
		}
	}

	return nil
}

type CmsMarketingGroupDetailOrderRequest struct {
	Id          int    `json:"id" form:"id" binding:"required,min=1"`
	BeginDate   string `json:"begin_date" form:"begin_date" binding:"omitempty,datetime=2006-01-02"` // 开始时间
	EndDate     string `json:"end_date" form:"end_date" binding:"omitempty,datetime=2006-01-02"`     // 结束时间
	Page        int    `json:"page" form:"page" binding:"required,min=1"`                            // 页码
	Limit       int    `json:"limit" form:"limit" binding:"min=1,max=100"`                           // 每页数量
	Keyword     string `json:"keyword" form:"keyword"`                                               // 关键字
	Total       int    `json:"total"`                                                                // 总数
	ResturantId int    `json:"restaurant_id" form:"restaurant_id" `
	OrderState  int    `json:"order_state" form:"order_state" `
	SortBy      string `json:"sort_by" form:"sort_by" binding:"omitempty,oneof=created_at"`   // 排序字段
	SortType    string `json:"sort_type" form:"sort_type" binding:"omitempty,oneof=asc desc"` // 排序类型
}

// Validate 验证表单
func (r *CmsMarketingGroupDetailOrderRequest) Validate() error {
	var (
		beginDate carbon.Carbon
		endDate   carbon.Carbon
	)
	if len(r.BeginDate) > 0 {
		beginDate = carbon.ParseByLayout(r.BeginDate, "2006-01-02")
		if beginDate.IsFuture() {
			return errors.New("begin_date_can_not_feature")
		}
	}
	if len(r.EndDate) > 0 {
		endDate = carbon.ParseByLayout("2006-01-02", r.EndDate)
		if len(r.BeginDate) > 0 && beginDate.Gt(endDate) {
			return errors.New("end_date_can_not_before_begin_date")
		}
		if len(r.BeginDate) > 0 && beginDate.DiffInDays(endDate) > 31 {
			return errors.New("date_range_can_not_over_30_days")
		}
	}

	return nil
}

type CmsMarketingGroupDetailRestaurantAddRequest struct {
	Id            int   `json:"id" form:"id" binding:"required"`
	RestaurantIds []int `json:"res_ids" form:"res_ids" binding:"required"`
}
