package marketingRequest

import (
	"errors"
	"github.com/golang-module/carbon/v2"
)

type ShipmentReduceStatisticsRestaurantRequest struct {
	ShipmentReduceStatisticsRestaurantStatisticsRequest
	Page     int    `form:"page" binding:"required,min=1"`
	Limit    int    `form:"limit" binding:"required,min=1,max=100"`
	SortBy   string `json:"sort_by" form:"sort_by" binding:"omitempty,oneof=created_at"`   // 排序字段
	SortType string `json:"sort_type" form:"sort_type" binding:"omitempty,oneof=asc desc"` // 排序类型
	Keyword  string `json:"keyword" form:"keyword" binding:"omitempty"`
}

type ShipmentReduceStatisticsRestaurantStatisticsRequest struct {
	CityID       int    `form:"city_id" binding:""`
	AreaID       int    `form:"area_id" binding:""`
	RestaurantID int    `form:"restaurant_id" binding:""`
	BeginDate    string `form:"begin_date" binding:"required,datetime=2006-01-02"`
	EndDate      string `form:"end_date" binding:"required,datetime=2006-01-02"`
	Keyword      string `form:"keyword" binding:"omitempty"`
}

func (r *ShipmentReduceStatisticsRestaurantStatisticsRequest) Validate() error {
	startDate := carbon.Parse(r.BeginDate)
	endDate := carbon.Parse(r.EndDate)
	if startDate.DiffInDays(endDate) > 30 {
		return errors.New("date_range_can_not_over_30_days")
	}

	return nil
}
