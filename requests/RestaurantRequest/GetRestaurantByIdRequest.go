package RestaurantRequest

type GetRestaurantByIdRequest struct {
	Keyword string `form:"keyword" json:"keyword" query:"keyword" binding:""`
	State   int    `form:"state" json:"state" query:"state" binding:"number"`
	Page    int    `form:"page" json:"page" query:"page" binding:"required,number,min=1"`
	Limit   int    `form:"limit" json:"limit" query:"limit" binding:"required,number,min=1,max=100"`
	OpenStates string `form:"open_states" json:"open_states" query:"open_states"`
}
