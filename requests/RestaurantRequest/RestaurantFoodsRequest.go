package RestaurantRequest

type RestaurantListRequest struct {
	Page          int     `form:"page" binding:"required"`  // 当前页数
	Limit         int     `form:"limit" binding:"required"` // 每页显示数量
	RestaurantID  int     `form:"restaurant_id" `           // 餐厅ID
	GroupID       int     `form:"group_id" `                // 组ID
	CityID        int     `form:"city_id" `                 // 餐厅ID
	AreaID        int     `form:"area_id" `                 // 餐厅ID
	State         *int    `form:"state" `                   // 状态
	DealerPercent float64 `form:"dealer_percent" `          // 状态
	LunchBoxID    int     `form:"lunch_box_id" `            // 饭盒ID
	LunchBoxState *int    `form:"lunch_box_state" `         // 饭盒状态
	Kw            string  `form:"kw" `                      // 关键字
	FoodType      *int    `form:"food_type" `               // 类型 0:普通 1:规格  2:套餐
	SortColumns   string  `form:"sort_columns" `            // 排序字段
}

// RestaurantFoodsCreateBody 创建餐厅美食请求
type RestaurantFoodsCreateBody struct {
	FoodsGroupID        int     `form:"foods_group_id" json:"foods_group_id" validate:""`                                                                      // 美食分组ID
	AllFoodsId          int64   `form:"all_foods_id" json:"all_foods_id" binding:""`                                                                           // 美食分组ID
	FoodsCategoryID     []int   `form:"foods_category_id" json:"foods_category_id"`                                                                            // 美食分类ID
	RestaurantPrinterID int     `form:"restaurant_printer_id" json:"restaurant_printer_id" validate:"required,exists=t_restaurant_printer,id,deleted_at,NULL"` // 餐厅打印机ID
	RestaurantID        int     `form:"restaurant_id" json:"restaurant_id" validate:"required,exists=t_restaurant,id,deleted_at,NULL"`                         // 餐厅ID
	NameUg              string  `form:"name_ug" json:"name_ug" validate:"required,min=3" binding:"required,min=3"`                                                                      // 维文名称
	NameZh              string  `form:"name_zh" json:"name_zh" validate:"required,min=1" binding:"required,min=1"`                                                                      // 中文名称
	FoodQuantity        float64 `form:"food_quantity" json:"food_quantity" validate:""`                                                                        // 美食量
	FoodQuantityType    uint8   `form:"food_quantity_type" json:"food_quantity_type" validate:""`                                                              // 美食量分类
	DescriptionUg       string  `form:"description_ug" json:"description_ug" validate:"min=3" binding:"min=3"`                                                                 // 维文描述
	DescriptionZh       string  `form:"description_zh" json:"description_zh" validate:"required_with=DescriptionUg,min=2"`                                     // 中文描述
	State               int     `form:"state" json:"state" validate:"required,oneof=0 1"`                                                                      // 状态（0：关闭、1：开启）
	IsDistribution      int     `form:"is_distribution" json:"is_distribution" validate:"required,oneof=0 1 2"`                                                // 是否配送（0：否、1：是、2：自取）
	Image               string  `form:"image" json:"image" validate:"required,min=1"`                                                                          // 图片URL
	BeginTime           string  `form:"begin_time" json:"begin_time" validate:"required,time"`                                                                 // 开始时间
	EndTime             string  `form:"end_time" json:"end_time" validate:"required,time"`                                                                     // 结束时间
	ReadyTime           int64   `form:"ready_time" json:"ready_time" validate:"required"`                                                                      // 准备时间（秒）
	OriginalPrice       int     `form:"original_price" json:"original_price" `                                                                                 // 价格
	Price               int     `form:"price" json:"price" validate:"required,min=0,max=10" binding:"required,min=0,max=500000"`                                                                          // 价格
	Weight              int     `form:"weight" json:"weight" validate:""`                                                                                      // 权重
	LunchBoxID          int     `form:"lunch_box_id" json:"lunch_box_id"`                                                                                      // 饭盒ID
	LunchBoxAccommodate int     `form:"lunch_box_accommodate" json:"lunch_box_accommodate"`                                                                    // 饭盒容纳量
	MinCount            int     `form:"min_count" json:"min_count" validate:"required"`                                                                        // 最小购买数量
	IsRecommend         int     `form:"is_recommend" json:"is_recommend" `                                                                                     // 是否推荐（0：否、1：是）
	DistributionPercent float64 `form:"distribution_percent" json:"distribution_percent" validate:""`                                                          // 配送百分比
	YourSelfTakePercent float64 `form:"yourself_take_percent" json:"yourself_take_percent" validate:""`                                                        // 自取百分比
	FoodType            uint8   `form:"food_type" json:"food_type,omitempty"`                                                                                  // 美食类型：0:普通美食，1:规格美食，2:套餐美食
	// 美食是套餐时，不能是规格美食。必须有子美食。
	// 美食是普通美食时，可以是规格美食。
	// 套餐中的美食不能是套餐美食。
	ComboFoodItems []RestaurantFoodsComboItems `form:"combo_food_items" json:"combo_food_items"`
}

// RestaurantFoodsEditBody 编辑餐厅美食请求
type RestaurantFoodsEditBody struct {
	RestaurantFoodsCreateBody
	// ID int `form:"id" json:"id" validate:"required"`
}

type FoodsSpecBody struct {
	FoodIds                  []int                      `form:"food_ids" json:"food_ids" validate:"required"`
	RestaurantFoodsSpecTypes []RestaurantFoodsSpecTypes `form:"restaurant_foods_spec_types" json:"restaurant_foods_spec_types"`
}

// 规格组
type RestaurantFoodsSpecTypes struct {
	ID                        int                         `form:"id" json:"id"`
	PriceType                 int                         `form:"price_type" json:"price_type"`
	NameUg                    string                      `form:"name_ug" json:"name_ug" validate:"required"`
	NameZh                    string                      `form:"name_zh" json:"name_zh" validate:"required"`
	State                     uint8                       `form:"state" json:"state" validate:"required,oneof=0 1"`
	RestaurantFoodsSpecOption []RestaurantFoodsSpecOption `form:"restaurant_foods_spec_option" json:"restaurant_foods_spec_option"`
}

// 规格项
type RestaurantFoodsSpecOption struct {
	ID         int    `form:"id" json:"id"`
	SpecTypeID int    `form:"spec_type_id" json:"spec_type_id"`
	NameUg     string `form:"name_ug" json:"name_ug" validate:"required"`
	NameZh     string `form:"name_zh" json:"name_zh" validate:"required"`
	IsSelected int    `form:"is_selected" json:"is_selected" `
	State      uint8  `form:"state" json:"state" validate:"required,oneof=0 1"`
	Sort       int    `form:"sort" json:"sort"`
	Price      int    `form:"price" json:"price" validate:"required"`
}

type RestaurantFoodsComboItems struct {
	FoodID    int   `form:"food_id" json:"food_id" validate:"required"` // 美食编号
	FoodType  uint8 `form:"food_type" json:"food_type"`                 // 美食类型 (0: 普通美食, 1: 规格美食)
	Count     int   `form:"count" json:"count" validate:"required"`     // 套餐中这个美食的数量
	OptionIds []int `form:"option_ids" json:"option_ids"`               // 已选规格编号
}
