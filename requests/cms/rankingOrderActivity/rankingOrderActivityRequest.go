package rankingorderactivity

type RankingOrderActivityCreateRequest struct {
	// 活动名称
	NameZh                  string                             `json:"name_zh" binding:"required"`
	NameUg                  string                             `json:"name_ug" binding:"required"`
	// 公布开始时间
	AnnounceBeginTime       string                             `json:"announce_begin_time" binding:"required"`
	// 开始时间
	StartTime               string                             `json:"start_time" binding:"required"`
	// 结束时间
	EndTime                 string                             `json:"end_time" binding:"required"`
	// 结果公布结束时间
	ResultShowEndTime       int                                `json:"result_show_end_time" binding:""`
	// 活动状态
	State                   int                                `json:"state" binding:"required"`
	// 奖品列表
	PrizeList               []RankingOrderActivityPrizeRequest `json:"prize_list" binding:"required"`
	// 分享封面图
	ShareCoverImages        string                             `json:"share_cover_images" binding:"required"`
	// 分享获奖者图片
	ShareWinnerImageUg      string                             `json:"share_winner_image_ug" binding:""`
	// 分享获奖者图片
	ShareWinnerImageZh      string                             `json:"share_winner_image_zh" binding:""`
	// 公布入口图片
	AnnounceEntranceImageUg string                             `json:"announce_entrance_image_ug" binding:"required"`
	// 公布入口图片
	AnnounceEntranceImageZh string                             `json:"announce_entrance_image_zh" binding:"required"`
	// 公布页面图片
	AnnouncePageImageUg     string                             `json:"announce_page_image_ug" binding:"required"`
	// 公布页面图片
	AnnouncePageImageZh     string                             `json:"announce_page_image_zh" binding:"required"`
	// 规则
	RuleUg                  string                             `json:"rule_ug" binding:"required"`
	// 规则
	RuleZh                  string                             `json:"rule_zh" binding:"required"`
}

type RankingOrderActivityPrizeRequest struct {
	// 奖品id
	Id     int    `json:"id" binding:"required"`
	// 奖品indexs
	Indexs string `json:"indexs" binding:"required"`
}

type RankingOrderActivityUpdateRequest struct {
	// 活动id
	ID                      int                                `json:"id" binding:"required"`
	// 活动名称
	NameZh                  string                             `json:"name_zh" binding:"required"`
	// 活动名称
	NameUg                  string                             `json:"name_ug" binding:"required"`
	// 公布开始时间
	AnnounceBeginTime       string                             `json:"announce_begin_time" binding:"required"`
	// 开始时间
	StartTime               string                             `json:"start_time" binding:"required"`
	// 结束时间
	EndTime                 string                             `json:"end_time" binding:"required"`
	// 结果公布结束时间
	ResultShowEndTime       int                                `json:"result_show_end_time" binding:""`
	// 活动状态
	State                   int                                `json:"state" binding:"required"`
	// 奖品列表
	PrizeList               []RankingOrderActivityPrizeRequest `json:"prize_list" binding:"required"`
	// 分享封面图
	ShareCoverImages        string                             `json:"share_cover_images" binding:"required"`
	// 分享获奖者图片
	ShareWinnerImageUg      string                             `json:"share_winner_image_ug" binding:""`
	// 分享获奖者图片
	ShareWinnerImageZh      string                             `json:"share_winner_image_zh" binding:""`
	// 公布入口图片
	AnnounceEntranceImageUg string                             `json:"announce_entrance_image_ug" binding:"required"`
	// 公布入口图片
	AnnounceEntranceImageZh string                             `json:"announce_entrance_image_zh" binding:"required"`
	// 公布页面图片
	AnnouncePageImageUg     string                             `json:"announce_page_image_ug" binding:"required"`
	// 公布页面图片
	AnnouncePageImageZh     string                             `json:"announce_page_image_zh" binding:"required"`
	// 规则
	RuleUg                  string                             `json:"rule_ug" binding:"required"`
	// 规则
	RuleZh                  string                             `json:"rule_zh" binding:"required"`
}

type RankingOrderActivityListRequest struct {
	// 活动状态
	State      *int    `json:"state" form:"state" binding:""`
	// 开始时间
	BeginTime  *string `json:"begin_time" form:"begin_time" binding:""`
	// 结束时间
	EndTime    *string `json:"end_time" form:"end_time" binding:""`
	// 搜索
	Search     *string `json:"search" form:"search" binding:""`
	// 页码
	Page       int     `json:"page" form:"page" binding:""`
	// 每页数量
	Limit      int     `json:"limit" form:"limit" binding:""`
	// 是否平台活动
	IsPlatform *int    `json:"is_platform" form:"is_platform" binding:"required,oneof=0 1"`
	// 城市id
	CityId *int `json:"city_id" form:"city_id" binding:""`
	// 区域id
	AreaId *int `json:"area_id" form:"area_id" binding:""`
}

type RankingOrderActivityWinnerListRequest struct {
	// 活动id
	ID    int `json:"id" form:"id" binding:"required,gt=0"`
	// 页码
	Page  int `json:"page" form:"page" binding:""`
	// 每页数量
	Limit int `json:"limit" form:"limit" binding:""`
}
