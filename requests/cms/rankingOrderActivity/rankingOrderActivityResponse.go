package rankingorderactivity

type RankingOrderActivityListResponse struct {
	// 活动ID
	ID               int  `json:"id"`
	// 活动名称
	Name             string `json:"name"`
	// 参与人数
	AttendCount      int64  `json:"attend_count"`
	// 订单数量
	OrderCount       int64  `json:"order_count"`
	// 订单金额
	TotalOrderAmount float64  `json:"total_order_amount"`
	// 活动开始时间
	StartTime        string `json:"start_time"`
	// 活动结束时间
	EndTime          string `json:"end_time"`
	// 活动创建时间
	CreatedAt        string `json:"created_at"`
	// 活动创建人
	CreatedBy        string `json:"created_by"`
	// 活动状态
	State            int    `json:"state"`
	// 活动状态名称
	StateName        string `json:"state_name"`
	// 区域名称
	AreaName        string `json:"area_name"`
}


type RankingOrderActivityDetailResponse struct {
	// 活动ID
	ID   int    `json:"id"`
	// 活动名称
	Name string `json:"name"`
	// 活动区域名称
	AreaName string `json:"area_name"`
	// 活动开始时间
	StartTime string `json:"start_time"`
	// 活动结束时间
	EndTime string `json:"end_time"`
	// 活动创建时间
	CreatedAt string `json:"created_at"`
	// 活动创建人
	CreatedBy string `json:"created_by"`
	//参与人数
	AttendCount int64 `json:"attend_count"`
	//订单数量
	OrderCount int64 `json:"order_count"`
	//订单金额
	TotalOrderAmount float64 `json:"total_order_amount"`
	// 奖品数量
	PrizeCount int64 `json:"prize_count"`
	// 已发出奖品数量
	SendPrizeCount int64 `json:"send_prize_count"`
	// 活动状态
	State int `json:"state"`
	// 活动状态名称
	StateName string `json:"state_name"`
	// 分享数量
	ShareCount int64 `json:"share_count"`
	// 喜欢人数
	LikeCount int64 `json:"like_count"`
	// 不喜欢人数
	DislikeCount int64 `json:"dislike_count"`
	// 页面浏览数量
	PageViewCount int64 `json:"page_view_count"`
	// 
	PageShareCount int64 `json:"page_share_count"`

}

// 中奖列表
type RankingOrderActivityDetailPrizeListResponse struct {
	// 中奖ID
	ID int `json:"id"`
	// 订单序号
	OrderIndex int `json:"order_index"`
	// 奖品名称
	PrizeName string `json:"prize_name"`
	// 奖品价格
	PrizePrice string `json:"prize_price"`
	// 订单号
	OrderNum string `json:"order_num"`
	// 城市名称
	CityName string `json:"city_name"`
	// 区域名称
	AreaName string `json:"area_name"`
	// 用户名称
	UserName string `json:"user_name"`
	// 用户手机号
	UserMobile string `json:"user_mobile"`
	// 下单时间
	OrderTime string `json:"order_time"`
	// 地址
	Address string `json:"address"`
}

// 修改的详细
type RankingOrderActivityUpdateDetailResponse struct {
	// 活动ID
	ID int `json:"id"`
	// 活动名称
	NameUg string `json:"name_ug"`
	// 活动名称
	NameZh string `json:"name_zh"`
	// 活动开始时间
	StartTime string `json:"start_time"`
	// 活动结束时间
	EndTime string `json:"end_time"`
	// 活动预热开始时间
	AnnounceBeginTime string `json:"announce_begin_time"`
	// 活动结果展示结束时间
	ResultShowEndTime int `json:"result_show_end_time"`
	// 活动状态
	State int `json:"state"`
	// 奖品列表
	PrizeList []RankingOrderActivityUpdatePrizeListResponse `json:"prize_list"`
	// 分享封面图
	ShareCoverImages []ImagesResponse `json:"share_cover_images" binding:"required"`
	// 公布入口图片
	AnnounceEntranceImageUg ImagesResponse `json:"announce_entrance_image_ug" binding:"required"`
	// 公布入口图片
	AnnounceEntranceImageZh ImagesResponse `json:"announce_entrance_image_zh" binding:"required"`
	// 公布页面图片
	AnnouncePageImageUg ImagesResponse `json:"announce_page_image_ug" binding:"required"`
	// 公布页面图片
	AnnouncePageImageZh ImagesResponse `json:"announce_page_image_zh" binding:"required"`
	// 规则
	RuleUg string `json:"rule_ug" binding:"required"`
	// 规则
	RuleZh string `json:"rule_zh" binding:"required"`
}

// 修改的奖品列表
type RankingOrderActivityUpdatePrizeListResponse struct {
	// 奖品ID
	PrizeID int `json:"prize_id"`
	// 奖品名称
	PrizeName string `json:"prize_name"`
	// 奖品价格
	PrizePrice int `json:"prize_price"`
	// 中奖序号
	Indexs string `json:"indexs"`
	// 图片
	PrizeImage string `json:"prize_image"`
}

type ImagesResponse struct {
	// 图片
	Url string `json:"url"`
	// 图片名称
	FullUrl string `json:"full_url"`
}