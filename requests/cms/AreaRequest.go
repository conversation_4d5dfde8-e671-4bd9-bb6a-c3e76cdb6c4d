package seckill

// CmsSeckillList
//
// @Description: 后台秒杀列表请求参数
// @Author: Rixat
// @Time: 2024-08-26 16:56:12
// @receiver
// @param c *gin.Context
type AreaList struct {
	Page         int    `form:"page" binding:"required"`  // 当前页数
	Limit        int    `form:"limit" binding:"required"` // 每页显示数量
	CityID       int    `form:"city_id"`                  // 地区ID
	AreaID       int    `form:"area_id"`                  // 地区ID
	State        *int `form:"state" binding:""`  // 执行状态：0:关闭:1：开启
}

type AreaDetail struct {
	CityID       int    `form:"city_id"`                  // 地区ID
	AreaID       int    `form:"area_id"`                  // 地区ID
}

type AreaShopLicenseInfo struct {
	ShopLicenseImage        string `form:"shop_license_image" binding:""`  // 营业执照
	ShopPermitLicenseImage        string `form:"shop_permit_license_image" binding:""`  // 许可证
	CityID       int    `form:"city_id"`                  // 地区ID
	AreaID       int    `form:"area_id"`                  // 地区ID
}

type AreaBusinessTime struct {
	AreaID            uint   `form:"area_id" binding:"required"`
	BusinessStartTime string `form:"business_start_time"`
	BusinessEndTime   string `form:"business_end_time"`
	BusinessTimeType  int    `form:"business_time_type"` //0:未设置， 1：已设置， 2: 24小时营业
}
