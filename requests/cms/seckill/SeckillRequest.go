﻿package seckill

// CmsSeckillList
//
// @Description: 后台秒杀列表请求参数
// @Author: Rixat
// @Time: 2024-08-26 16:56:12
// @receiver
// @param c *gin.Context
type CmsSeckillList struct {
	Page         int    `form:"page" binding:"required"`  // 当前页数
	Limit        int    `form:"limit" binding:"required"` // 每页显示数量
	CityID       int    `form:"city_id"`                  // 地区ID
	AreaID       int    `form:"area_id"`                  // 区域ID
	RestaurantID int    `form:"restaurant_id" `           // 餐厅ID
	FoodID       int    `form:"food_id" `                 // 美食ID
	BeginTime    string `form:"begin_time"`               // 开始时间
	EndTime      string `form:"end_time"`                 // 结束时间
	RunState     string `form:"run_state"`                // 执行状态：0:未开始，1:进行中，2-已结束
	State        string `form:"state"`                    // 执行状态：0:关闭:1：开启
	MarkupType   int    `form:"markup_type"`              // 执行状态：0:全部:1：餐厅美食，2：加价美食
}

type CmsSeckillLogList struct {
	Page         int    `form:"page" binding:"required"`  // 当前页数
	Limit        int    `form:"limit" binding:"required"` // 每页显示数量
	CityID       int    `form:"city_id"`                  // 地区ID
	AreaID       int    `form:"area_id"`                  // 区域ID
	RestaurantID int    `form:"restaurant_id" `           // 餐厅ID
	FoodID       int    `form:"food_id" `                 // 美食ID
	BeginTime    string `form:"begin_time"`               // 开始时间
	EndTime      string `form:"end_time"`                 // 结束时间
}

// CmsSeckillItem
//
// @Description: 后台秒杀创建请求参数
// @Author: Rixat
// @Time: 2024-08-26 16:56:12
// @receiver
// @param c *gin.Context
type CmsSeckillItem struct {
	RestaurantID      int    `json:"restaurant_id" binding:"required"` // 餐厅ID
	FoodID            int    `json:"food_id" binding:"required"`       // 美食ID
	Price             int    `json:"price" binding:"required"`         // 秒杀价格
	OriginalPrice     int    `json:"original_price" binding:"required"` // 原价
	BeginTime         string `json:"begin_time" binding:"required"`    // 开始时间
	EndTime           string `json:"end_time" binding:"required"`      // 结束时间
	OrderTime         string `json:"order_time" binding:""`            // 配送时间
	TotalCount        int    `json:"total_count" binding:"required"`   // 总库存
	UserMaxOrderCount int    `json:"user_max_order_count" binding:""`  // 用户最多能购买数量
	Order             int    `json:"order" binding:""`                 // 循序
	State             int    `json:"state" binding:"oneof=0 1"`        // 状态
	PriceMarkupID     int    `json:"price_markup_id" binding:""`       // 活动类型:1:秒杀，2：特价，3：加价
	OptionIds         []int    `json:"option_ids" binding:""`               // 规格ID
	FoodType 		  uint8 `json:"food_type" binding:""`                       // 美食类型：
}

// CmsSeckillItemRequest
//
// @Description: 后台秒杀创建请求参数
// @Author: Rixat
// @Time: 2024-08-26 16:56:12
// @receiver
// @param c *gin.Context
type CmsSeckillItemRequest struct {
	Type  int              `json:"type" binding:"required"` // 1：同一个美食，2：多个美食
	Items []CmsSeckillItem `json:"items" binding:"required,dive"`
}

// CmsSeckillItem
//
// @Description: 后台秒杀创建请求参数
// @Author: Rixat
// @Time: 2024-08-26 16:56:12
// @receiver
// @param c *gin.Context
type CmsSeckillEditRequest struct {
	ID                int    `json:"id" binding:"required"`            // 餐厅ID
	RestaurantID      int    `json:"restaurant_id" binding:"required"` // 餐厅ID
	FoodID            int    `json:"food_id" binding:"required"`       // 美食ID
	Price             int    `json:"price" binding:"required"`         // 秒杀价格
	BeginTime         string `json:"begin_time" binding:"required"`    // 开始时间
	EndTime           string `json:"end_time" binding:"required"`      // 结束时间
	OrderTime         string `json:"order_time" binding:""`            // 配送时间
	TotalCount        int    `json:"total_count" binding:"required"`   // 总库存
	UserMaxOrderCount int    `json:"user_max_order_count" binding:""`  // 用户最多能购买数量
	Order             int    `json:"order" binding:""`                 // 循序
	State             int    `json:"state" binding:"oneof=0 1"`        // 循序
	PriceMarkupID     int    `json:"price_markup_id" binding:""`       // 活动类型:1:秒杀，2：特价，3：加价
	OptionIds         []int  `json:"option_ids" binding:""`            // 规格ID
	FoodType          int    `json:"food_type" binding:""`             // 美食类型：0：普通  1：规格，2：套餐
	SpecID            int    `json:"spec_id" binding:""`             // 规格ID
}
