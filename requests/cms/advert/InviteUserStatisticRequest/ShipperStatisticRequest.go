package InviteUserStatisticRequest

import (
	"errors"
	"mulazim-api/models"
	"time"
)

type ShipperStatisticRequest struct {
	CityID    int    `json:"city_id" form:"city_id" binding:""`
	AreaID    int    `json:"area_id" form:"area_id" binding:""`
	BeginDate string `json:"begin_date" form:"begin_date" binding:"required,datetime=2006-01-02"`
	EndDate   string `json:"end_date" form:"end_date" binding:"required,datetime=2006-01-02"`
	ShipperID int    `json:"shipper_id" form:"shipper_id" binding:""`
}

// Validate 验证
func (r *ShipperStatisticRequest) Validate(admin models.Admin) error {
	switch {
	case admin.IsDealerSub() || admin.IsDealer():
		r.CityID = admin.AdminCityID
		r.AreaID = admin.AdminAreaID
	case admin.IsAdmin() || admin.IsOwner():
		if r.CityID == 0 {
			return errors.New("city_id_required")
		}
		if r.AreaID == 0 {
			return errors.New("area_id_required")
		}
	default:
		return errors.New("no_permission")
	}
	beginDate, err := time.Parse("2006-01-02", r.BeginDate)
	if err != nil {
		return errors.New("begin_date_format_error")
	}
	endDate, err := time.Parse("2006-01-02", r.EndDate)
	if err != nil {
		return errors.New("end_date_format_error")
	}
	now, _ := time.Parse("2006-01-02", time.Now().Format("2006-01-02"))
	if endDate.After(now) {
		return errors.New("end_date_can_not_feature")
	}
	if beginDate.After(endDate) {
		return errors.New("begin_time_must_be_after_time")
	}
	if endDate.Sub(beginDate).Hours() > 720 {
		return errors.New("date_range_can_not_over_31_days")
	}
	return nil
}

type AreaStatisticRequest struct {
	BeginDate string `json:"begin_date" form:"begin_date" binding:"required,datetime=2006-01-02"`
	EndDate   string `json:"end_date" form:"end_date" binding:"required,datetime=2006-01-02"`
}

// Validate 验证
func (r *AreaStatisticRequest) Validate() error {
	beginDate, err := time.Parse("2006-01-02", r.BeginDate)
	if err != nil {
		return errors.New("begin_date_format_error")
	}
	endDate, err := time.Parse("2006-01-02", r.EndDate)
	if err != nil {
		return errors.New("end_date_format_error")
	}
	now, _ := time.Parse("2006-01-02", time.Now().Format("2006-01-02"))
	if endDate.After(now) {
		return errors.New("end_date_can_not_feature")
	}
	if beginDate.After(endDate) {
		return errors.New("begin_time_must_be_after_time")
	}
	if endDate.Sub(beginDate).Hours() > 720 {
		return errors.New("date_range_can_not_over_31_days")
	}
	return nil
}
