package takeRequest

import (
	"errors"
	"mulazim-api/models"
	"time"
)

type ListRequest struct {
	CityID     int    `form:"city_id"`
	AreaID     int    `form:"area_id"`
	CategoryID int    `form:"category_id"`
	BeginDate  string `form:"begin_date" binding:"required,datetime=2006-01-02"`
	EndDate    string `form:"end_date" binding:"required,datetime=2006-01-02"`
	ShipperID  int    `form:"shipper_id"`
}

// Validate 验证
func (r *ListRequest) Validate(admin models.Admin) error {
	switch {
	case admin.IsDealerSub() || admin.IsDealer():
		r.CityID = admin.AdminCityID
		r.CityID = admin.AdminAreaID
	case admin.IsAdmin() || admin.IsOwner():
	default:
		return errors.New("no_permission")
	}

	beginDate, err := time.Parse("2006-01-02", r.BeginDate)
	if err != nil {
		return errors.New("begin_date_format_error")
	}
	endDate, err := time.Parse("2006-01-02", r.EndDate)
	if err != nil {
		return errors.New("end_date_format_error")
	}
	now, _ := time.Parse("2006-01-02", time.Now().Format("2006-01-02"))
	if endDate.After(now) {
		return errors.New("end_date_can_not_feature")
	}
	if beginDate.After(endDate) {
		return errors.New("begin_time_must_be_after_time")
	}
	if endDate.Sub(beginDate).Hours() > 720 {
		return errors.New("date_range_can_not_over_31_days")
	}

	return nil
}
