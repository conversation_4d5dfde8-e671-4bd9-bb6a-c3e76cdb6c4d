package materialRequest

type CreateRequest struct {
	CategoryId int    `json:"category_id" binding:"required,min=1"`
	NameUg     string `json:"name_ug" binding:"required,min=3,max=254"`
	NameZh     string `json:"name_zh" binding:"required,min=3,max=254"`
	Cover      string `json:"cover" binding:"required,min=3,max=254"`
	File       string `json:"file" binding:""`
	State      int    `json:"state" binding:"required,min=1,max=2"`
}
