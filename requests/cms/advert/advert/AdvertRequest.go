package advert

import "encoding/json"

// AdvertListRequest 获取广告列表参数
type AdvertListRequest struct {
	AreaId           int    `form:"area_id" json:"area_id"`
	CityId           int    `form:"city_id" json:"city_id"`
	AdvertPositionId int    `form:"advert_position_id" json:"advert_position_id"`
	LinkType         int    `form:"link_type" json:"link_type"`
	LangId           int    `form:"lang_id" json:"lang_id"`
	Search           string `form:"search" json:"search"`
	Page             int    `form:"page" json:"page"`
	Limit            int    `form:"limit" json:"limit"`
	SortColumns      string `form:"sort_column" json:"sort_column"`
	State            *int   `form:"state" json:"state"`
	IsRunning        int64  `form:"is_running" json:"is_running"`
	StartDate        string `form:"start_date" json:"start_date"`
	EndDate          string `form:"end_date" json:"end_date"`
}

// AdvertListResponse 获取广告列表返回参数
type AdvertListResponse struct {
	Id                  int64  `json:"id"`
	Image               string `json:"image"`
	Weight              int64  `json:"weight"`
	LinkType            int64  `json:"link_type"`
	LinkTypeName        string `json:"link_type_name"`
	LinkUrl             string `json:"link_url"`
	LinkName            string `json:"link_name"`
	AdvertPositionId    int64  `json:"advert_position_id"`
	AdvertPositionName  string `json:"advert_position_name"`
	AreaName            string `json:"area_name"`
	AreaId              int64  `json:"area_id"`
	StartTime           string `json:"start_time"`
	EndTime             string `json:"end_time"`
	StartDate           string `json:"start_date"`
	EndDate             string `json:"end_date"`
	MiniProgramId       string `json:"mini_program_id"`
	MiniProgramLinkPage string `json:"mini_program_link_page"`
	Content             string `json:"content"`
	LangId              int64  `json:"lang_id"`
	State               int64  `json:"state"`
	StateName           string `json:"state_name"`
	UpdatedAt           string `json:"updated_at"`
	CreatedAt           string `json:"created_at"`
}

// AdvertCreateRequest 创建广告参数
type AdvertCreateRequest struct {
	AdvertPositionId    int64   `form:"advert_position_id" json:"advert_position_id" binding:"required"`
	LinkType            *int64  `form:"link_type"  json:"link_type" binding:"required"`
	LinkId              *int64  `form:"link_id" json:"link_id" binding:""`
	LinkUrl             string  `form:"link_url" json:"link_url" binding:""`
	StartTime           string  `form:"start_time" json:"start_time" binding:"required"`
	EndTime             string  `form:"end_time" json:"end_time" binding:"required"`
	StartDate           string  `form:"start_date" json:"start_date" binding:"required"`
	EndDate             string  `form:"end_date" json:"end_date" binding:"required"`
	Content             string  ` form:"content" json:"content" binding:"required"`
	ImageUg             string  ` form:"image_ug" json:"image_ug" binding:"required"`
	ImageZh             string  ` form:"image_zh" json:"image_zh" binding:"required"`
	UseAutoImage        bool    `form:"use_auto_image" json:"use_auto_image"`
	State               *int64  ` form:"state" json:"state" binding:"required,oneof=0 1 2"`
	AreaIds             []int64 `form:"area_ids" json:"area_ids"`
	Weight              int64   `form:"weight" json:"weight" binding:"required"`
	StoreId             int64   `form:"store_id" json:"store_id"`
	MiniProgramId       string  `form:"mini_program_id" json:"mini_program_id"`
	MiniProgramLinkPage string  `form:"mini_program_link_page,mini_link_url" json:"mini_program_link_page"`
}

// AdvertChangeStateRequest 修改广告状态参数
type AdvertChangeStateRequest struct {
	Id    json.RawMessage  `form:"id" json:"id" binding:"required"`
	State *int64 `form:"state" json:"state" binding:"required"`
}

// AdvertEditRequest 编辑广告参数
type AdvertEditRequest struct {
	Id                  int64  `form:"id" json:"id" binding:"required"`
	AdvertPositionId    int64  `form:"advert_position_id" json:"advert_position_id" binding:"required"`
	State               *int64 `form:"state" json:"state" `
	Content             string `form:"content" json:"content" binding:"required"`
	Weight              int64  `form:"weight" json:"weight" binding:"required"`
	LinkType            *int64 `form:"link_type" json:"link_type" binding:"required"`
	LinkUrl             string `form:"link_url" json:"link_url"`
	LinkId              int64  `form:"link_id" json:"link_id"`
	StoreId             int64  `form:"store_id" json:"store_id"`
	StartDate           string `form:"start_date" json:"start_date" binding:"required"`
	EndDate             string `form:"end_date" json:"end_date" binding:"required"`
	StartTime           string `form:"start_time" json:"start_time" binding:"required"`
	EndTime             string `form:"end_time" json:"end_time" binding:"required"`
	LangId              int64  `form:"lang_id" json:"lang_id" binding:"required"`
	Image               string `form:"image" json:"image" binding:"required"`
	MiniProgramId       string `form:"mini_program_id" json:"mini_program_id"`
	MiniProgramLinkPage string `form:"mini_program_link_page,mini_link_url" json:"mini_program_link_page"`
	UseAutoImage        bool   `form:"use_auto_image" json:"use_auto_image"`
}

// AdvertDeleteRequest 删除广告参数
type AdvertDeleteRequest struct {
	Ids []int64 `form:"ids" json:"ids" binding:"required"`
}

// AdvertPositionResponse 广告位置返回参数
type AdvertPositionResponse struct {
	Id          int64  `json:"id"`
	Description string `json:"description"`
	Width       int64  `json:"width"`
	Height      int64  `json:"height"`
}

// CanvasAdvertTemplateResponse
type CanvasAdvertTemplateResponse struct {
	Id      int64  `json:"id"`
	CoverZh string `json:"cover_zh"`
	CoverUg string `json:"cover_ug"`
	Width   int64  `json:"width"`
	Height  int64  `json:"height"`
	Crop    bool   `json:"crop"`
}

// AdvertDetailRequest 获取广告详情参数
type AdvertDetailRequest struct {
	Id int64 `form:"id" json:"id" binding:"required"`
}

// AdvertDetailResponse 获取广告详情返回参数
type AdvertDetailResponse struct {
	Id                  int64  `json:"id"`
	AdvertPositionId    int64  `json:"advert_position_id"`
	LinkId              int64  `json:"link_id"`
	LinkType            int64  `json:"link_type"`
	LinkUrl             string `json:"link_url"`
	StartTime           string `json:"start_time"`
	EndTime             string `json:"end_time"`
	StartDate           string `json:"start_date"`
	EndDate             string `json:"end_date"`
	Content             string `json:"content"`
	Weight              int64  `json:"weight"`
	AreaName            string `json:"area_name"`
	LangId              int64  `json:"lang_id"`
	State               int64  `json:"state"`
	Image               string `json:"image"`
	ImageUrl            string `json:"image_url"`
	RestaurantName      string `json:"restaurant_name"`
	FoodName            string `json:"food_name"`
	MiniProgramId       string `json:"mini_program_id"`
	MiniProgramLinkPage string `json:"mini_program_link_page"`
}
