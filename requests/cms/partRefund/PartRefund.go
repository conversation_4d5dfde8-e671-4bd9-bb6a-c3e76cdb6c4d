package partRefund

type PartRefundOrderListRequest struct {
	CityID              int    `form:"city_id" binding:""`
	AreaID              int    `form:"area_id" binding:""`
	RestaurantID        int    `form:"restaurant_id" binding:""`
	State               int    `form:"state" binding:""`
	PartRefundCreatorID int    `form:"part_refund_creator_id" binding:""`
	PartRefundType      int    `form:"part_refund_type" binding:""`
	BeginDate           string `form:"begin_date" binding:""`
	EndDate             string `form:"end_date" binding:""`
	Kw                  string `form:"kw" binding:""`
}

type PartRefundOrderDetailRequest struct {
	ID int `form:"id" binding:"required"`
}
