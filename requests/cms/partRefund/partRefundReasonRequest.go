package partRefund

type PartRefundReasonCreateRequest struct {
	NameUg  string `json:"name_ug" binding:"required"`
	NameZh  string `json:"name_zh" binding:"required"`
	State   int    `json:"state" binding:"required,oneof=1 2"`
	Weight  int    `json:"weight" binding:"required"`
	SubType int    `json:"sub_type" binding:""`
}

type PartRefundReasonUpdateRequest struct {
	ID      int    `json:"id" binding:"required"`
	NameUg  string `json:"name_ug" binding:""`
	NameZh  string `json:"name_zh" binding:""`
	State   int    `json:"state" binding:"oneof=1 2"`
	Weight  int    `json:"weight" binding:""`
	SubType int    `json:"sub_type" binding:""`
}
