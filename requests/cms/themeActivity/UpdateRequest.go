package themeActivity

type UpdateRequest struct {
	ID                    int    `json:"-"`
	NameUg                string `json:"name_ug" binding:"required"`
	NameZh                string `json:"name_zh" binding:"required"`
	DescUg                string `json:"desc_ug" binding:"required"`
	DescZh                string `json:"desc_zh" binding:"required"`
	BeginTime             string `json:"begin_time" binding:"required"`
	EndTime               string `json:"end_time" binding:"required"`
	DiscountPercent       int    `json:"discount_percent" binding:"min=0,max=100"`
	CoverUg               string `json:"cover_ug" binding:"required"`
	CoverZh               string `json:"cover_zh" binding:"required"`
	Color                 string `json:"color" binding:"required"`
	State                 int    `json:"state" binding:"oneof=0 1"`
	OrderType             string `json:"order_type" binding:"required,oneof=by_price_desc by_price_asc by_saled_desc by_saled_asc by_add_time"`
	ShowPreferential      int    `json:"show_preferential" binding:"oneof=0 1"`
	ShowSeckill           int    `json:"show_seckill" binding:"oneof=0 1"`
	ShowFoodType          int    `json:"show_food_type" binding:"oneof=1 2"`
	HasKeyword            int    `json:"has_keyword" binding:"oneof=0 1"`
	KeywordUg             string `json:"keyword_ug"`
	KeywordZh             string `json:"keyword_zh"`
	ExcludeKeywordUG      string `json:"exclude_keyword_ug"`
	ExcludeKeywordZH      string `json:"exclude_keyword_zh"`
	ShareContentUg        string `json:"share_content_ug"`
	ShareContentZh        string `json:"share_content_zh"`
	ShareCoverUg          string `json:"share_cover_ug"`
	ShareCoverZh          string `json:"share_cover_zh"`
	ShareCoverToFriendUg  string `json:"share_cover_to_friend_ug"`
	ShareCoverToFriendZh  string `json:"share_cover_to_friend_zh"`
}
