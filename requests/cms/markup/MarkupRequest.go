﻿package markup

// CmsPriceMarkupList
//
// @Description: 列表请求参数
// @Author: Rixat
// @Time: 2024-10-22 13:18:04
// @receiver
// @param c *gin.Context
type CmsPriceMarkupList struct {
	Page         int `form:"page" binding:"required"`  // 当前页数
	Limit        int `form:"limit" binding:"required"` // 每页显示数量
	CityID       int `form:"city_id"`                  // 地区ID
	AreaID       int `form:"area_id"`                  // 区域ID
	RestaurantID int `form:"restaurant_id" `           // 餐厅ID
	FoodID       int `form:"food_id" `                 // 美食ID
	RunState       int `form:"run_state" `                 // 美食ID
	State       int `form:"state" `                 // 美食ID
}

// CmsPriceMarkupCreate
//
// @Description: 创建请求参数
// @Author: Rixat
// @Time: 2024-10-22 13:18:30
// @receiver 
// @param c *gin.Context
type CmsPriceMarkupCreate struct {
	FoodID     int    `form:"food_id" json:"food_id" binding:"required"`     // 美食ID
	InPrice    int    `form:"in_price" json:"in_price" binding:"required"`    // 秒杀价格
	StartDate  string `form:"start_date" json:"start_date" binding:"required"`  // 开始时间
	EndDate    string `form:"end_date" json:"end_date" binding:"required"`    // 结束时间
	TotalCount int    `form:"total_count" json:"total_count" binding:"required"` // 总库存
	OptionIds  []int  `form:"option_ids" json:"option_ids" binding:""`
	FoodType   int    `form:"food_type" json:"food_type" binding:""`          // 美食类型
}

// CmsPriceMarkupEdit
//
// @Description: 编辑请求参数
// @Author: Rixat
// @Time: 2024-10-22 13:18:47
// @receiver 
// @param c *gin.Context
type CmsPriceMarkupEdit struct {
	ID         int    `form:"id" json:"id" binding:"required"`          // 餐厅ID
	FoodID     int    `form:"food_id" json:"food_id" binding:"required"`     // 美食ID
	InPrice    int    `form:"in_price" json:"in_price" binding:"required"`    // 秒杀价格
	StartDate  string `form:"start_date" json:"start_date" binding:"required"`  // 开始时间
	EndDate    string `form:"end_date" json:"end_date" binding:"required"`    // 结束时间
	TotalCount int    `form:"total_count" json:"total_count" binding:"required"` // 总库存
	SpecID     int    `form:"spec_id" json:"spec_id" binding:""`            // 规格ID
	OptionIds  []int  `form:"option_ids" json:"option_ids" binding:""`
	FoodType   int    `form:"food_type" json:"food_type" binding:""`          // 美食类型
}
