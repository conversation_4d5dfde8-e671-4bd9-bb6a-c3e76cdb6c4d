package delivery

import (
	"fmt"
	_ "github.com/go-playground/validator/v10"
)

type CreateRestaurantFeeStagesRequest struct {
	// 基础字段
	CityId        int   `json:"city_id"`                                 // 城市编号
	AreaId        int   `json:"area_id"`                                 // 区域编号
	RestaurantIds []int `json:"restaurant_ids" binding:"required,min=1"` // 餐厅编号列表
	ParentID      int   `json:"parent_id" binding:"required"`            // 父ID
	// 配送费列表
	//Option     uint8                   `json:"option" binding:"required,oneof=1 2"`              // 配送费选项 - 1 跟随区域 / 2 使用餐厅配置
	FeeConfigs []CreateDeliveryFeeData `json:"fee_configs" binding:"required_if=Option 2,min=1"` // 配送费列表 - 区域时必填，餐厅时选填
}

// Validate 验证嵌套结构体
func (r *CreateRestaurantFeeStagesRequest) Validate() error {
	for i, fee := range r.FeeConfigs {
		if err := fee.Validate(); err != nil {
			return fmt.Errorf("fee_configs[%d]: %w", i, err)
		}
	}
	return nil
}

type CreateRestaurantAreaPolygonRequest struct {
	// 基础字段
	CityId        int   `json:"city_id"`                                 // 城市编号
	AreaId        int   `json:"area_id"`                                 // 区域编号
	RestaurantIds []int `json:"restaurant_ids" binding:"required,min=1"` // 餐厅编号列表
	ParentID      int   `json:"parent_id" binding:"required"`            // 父ID
	// 配送范围
	AreaPoints   []DeliveryAreaPoint `json:"area_points"`                           // binding:"required_if=Option 2,min=3"` // 地图范围数据 - 至少得有 3 个点
	Option       uint8               `json:"option" binding:"required,oneof=1 2 3"` // 地图选项 - 1 跟随区域 / 2 使用普通 / 3 使用圆形 - 区域创建时必须为 2 或者 3
	Radius       uint                `json:"radius" binding:"required_if=Option 3"` // 圆形半径（米） - 当 option == 3 时必填
	OrderAddTime int                 `json:"order_add_time" binding:""`             // 增加配送时间（分）
}

// UpdateRestaurantDeliveryConfigs - 更新餐厅的 DeliveryArea 或者 DeliveryFee 是否使用区域配置时使用
type UpdateRestaurantDeliveryConfigs struct {
	CityId        int   `json:"city_id"`                                    // 城市编号 - 为了权限
	AreaId        int   `json:"area_id"`                                    // 区域编号 - 为了权限
	ParentID      int   `json:"parent_id" binding:"required"`               // 父ID
	OptionType    uint8 `json:"option_type" binding:"required,oneof=1 2 3"` // 1 更新配送范围/配送费 / 2 更新配送范围 / 3 更新配送费
	RestaurantIds []int `json:"restaurant_ids" binding:"required,min=1"`    // 餐厅编号列表
}
