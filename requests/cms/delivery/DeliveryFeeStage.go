package delivery

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// DeliveryFeeStage 配送费阶梯配置项
type DeliveryFeeStage struct {
	FixedFee              uint `json:"fixed_fee"`               // 0 按距离浮动费用 / 1 固定费用 - 分
	StartDistance         int  `json:"start_distance"`          // 起始距离：米，默认 0
	EndDistance           int  `json:"end_distance,omitempty"`  // 截止距离：米（空 或者 0 表示无上限）
	StartFee              uint `json:"start_fee"`               // 起始费用：分
	LowOrderFee           uint `json:"low_order_fee"`           // 低价订单费用：分
	AdditionalFeeDistance uint `json:"additional_fee_distance"` // 阶梯距离：米
	AdditionalFee         uint `json:"additional_fee"`          // 阶梯费用：分
}

// DeliveryFeeStages 是一个切片类型，用于存储多个阶梯配置
type DeliveryFeeStages []DeliveryFeeStage

// Scan 实现 sql.Scanner 接口，用于从数据库读取 JSON 数据
func (d *DeliveryFeeStages) Scan(src interface{}) error {
	if src == nil {
		*d = nil
		return nil
	}

	var source []byte
	switch v := src.(type) {
	case string:
		source = []byte(v)
	case []byte:
		source = v
	default:
		return fmt.Errorf("unexpected type for DeliveryFeeStages: %T", src)
	}

	if err := json.Unmarshal(source, d); err != nil {
		return fmt.Errorf("failed to unmarshal DeliveryFeeStages: %v", err)
	}
	return nil
}

// Value 实现 driver.Valuer 接口，用于写入数据库
func (d DeliveryFeeStages) Value() (driver.Value, error) {
	if len(d) == 0 {
		return nil, nil
	}

	bytes, err := json.Marshal(d)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal DeliveryFeeStages: %v", err)
	}
	return bytes, nil
}
