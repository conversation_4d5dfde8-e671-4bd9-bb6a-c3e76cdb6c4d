package delivery

import (
	"fmt"
	"time"

	_ "github.com/go-playground/validator/v10"
)

/*
  Validator 文档 - 包括 required_if 用法。
  - https://pkg.go.dev/github.com/go-playground/validator/v10
*/

type GetListDeliveryConfigsRequest struct {
	Page     int    `form:"page" binding:""`  // 当前页数
	Limit    int    `form:"limit" binding:""` // 每页显示数量
	CityID   int    `form:"city_id" `         // 城市ID
	AreaID   int    `form:"area_id" `         // 区域ID
	ParentID int    `form:"parent_id" `       // 父区域ID
	Option   int    `form:"option" `          // 地图选项
	FeeState int    `form:"fee_state" `       // 配送费状态
	Kw       string `form:"kw" `              // 关键字
}

type UpdateDeliveryConfigStatus struct {
	ID               int   `uri:"id" binding:"required"`
	AreaRunningState uint8 `json:"area_running_state" binding:"required,oneof=1"` // "只能开启一个配置，系统将会自动关闭该区域的其他配置"
	CityId           int   `json:"city_id"`
	AreaId           int   `json:"area_id"`
	Type             uint8 `json:"type" binding:"required,oneof=1 2"`

	ParentId     int `json:"parent_id"`
	RestaurantId int `json:"restaurant_id"`
	FeeState     int `json:"fee_state"`
}

type UpdateDeliveryConfigNotice struct {
	ID       int    `uri:"id" binding:"required"`
	CityId   int    `json:"city_id"`
	AreaId   int    `json:"area_id"`
	NoticeUg string `json:"notice_ug" binding:"required"` // 公告内容 - 维吾尔文
	NoticeZh string `json:"notice_zh" binding:"required"` // 公告内容 - 中文
}

type GetDeliveryConfigsRequest struct {
	Page   int `form:"page" binding:""`  // 当前页数
	Limit  int `form:"limit" binding:""` // 每页显示数量
	CityID int `form:"city_id" `         // 城市ID
	AreaID int `form:"area_id" `         // 区域ID
}

// CreateDeliveryFeeData 配送费阶梯 JSON 部分结构体
type CreateDeliveryFeeData struct {
	ID        int               `form:"id" binding:""`                                                // 编号，在更新时使用
	Default   uint8             `json:"default" binding:"required,oneof=1 2"`                         // 是否为默认配送费配置 - 1 默认配置 / 2 时间段配置
	BeginTime string            `json:"begin_time" binding:"required_if=Default 2,datetime=15:04:05"` // 配送费生效开始时间 - default == 2 时必填
	EndTime   string            `json:"end_time" binding:"required_if=Default 2,datetime=15:04:05"`   // 配送费生效截止时间 - default == 2 时必填
	StartFee  uint              `json:"start_fee" binding:""`                                         // 起送价（分）
	Stages    DeliveryFeeStages `json:"stages" binding:"required,min=2"`                              // 配送费价格阶梯数据
}

// Validate 验证嵌套结构体的条件验证
func (f *CreateDeliveryFeeData) Validate() error {
	if f.Default > 2 || f.Default < 1 {
		return fmt.Errorf("default 的值必须为 1 或者 2")
	}
	if len(f.Stages) < 2 {
		return fmt.Errorf("stages 大小必须大于等于 2")
	}

	// 验证时间
	if f.Default == 2 {
		if f.BeginTime == "" || f.EndTime == "" {
			return fmt.Errorf("default=2 时 begin_time 和 end_time 为必填")
		}
		// 验证时间格式
		if _, err := time.Parse("15:04:05", f.BeginTime); err != nil {
			return fmt.Errorf("begin_time 格式错误，必须为 HH:MM:SS")
		}
		if _, err := time.Parse("15:04:05", f.EndTime); err != nil {
			return fmt.Errorf("end_time 格式错误，必须为 HH:MM:SS")
		}
	}

	// 每一行的 EndDistance 必须大于 StartDistance，后一行的 StartDistance 必须大于前一行的 EndDistance
	for i := 0; i < len(f.Stages)-1; i++ {
		if f.Stages[i].EndDistance <= f.Stages[i].StartDistance {
			return fmt.Errorf("stages[%d].end_distance 必须大于 stages[%d].start_distance", i, i)
		}
		// 如果 i 不是最后一行，则后一行的 StartDistance 必须等于前一行的 EndDistance
		if i+1 < len(f.Stages) {
			if f.Stages[i+1].StartDistance != f.Stages[i].EndDistance {
				return fmt.Errorf("stages[%d].start_distance 必须等于 stages[%d].end_distance", i+1, i)
			}
		}
	}

	return nil
}

// CreateDeliveryConfigsRequest 创建区域配送配置结构体
type CreateDeliveryConfigsRequest struct {
	// 基础字段
	CityId       int   `json:"city_id"`                                    // 城市编号
	AreaId       int   `json:"area_id"`                                    // 区域编号
	Type         uint8 `json:"type"`                                       // 类型：1 区域 / 2 餐厅 - 只用作入库
	RestaurantId int   `json:"restaurant_id" binding:"required_if=Type 2"` // 餐厅编号
	ParentID     int   `json:"parent_id" binding:"required_if=Type 2"`     // 父ID
	// 配送范围
	AreaPoints   []DeliveryAreaPoint `json:"area_points"`                           //  binding:"required_if=Option 2,min=3" // 地图范围数据 - 至少得有 3 个点
	Option       uint8               `json:"option" binding:"required,oneof=1 2 3"` // 地图选项 - 1 跟随区域 / 2 使用普通 / 3 使用圆形 - 区域创建时必须为 2 或者 3
	Radius       uint                `json:"radius" binding:"required_if=Option 3"` // 圆形半径（米） - 当 option == 3 时必填
	OrderAddTime int                 `json:"order_add_time" binding:""`             // 增加配送时间（分）
	NameUg       string              `json:"name_ug" binding:"required_if=Type 1"`  // 配置名称 - 维吾尔文 - type 是区域时必填
	NameZh       string              `json:"name_zh" binding:"required_if=Type 1"`  // 配置名称 - 中文 - type 是区域时必填
	NoticeUg     string              `json:"notice_ug"`                             // 公告 - 维吾尔文 - 非必填
	NoticeZh     string              `json:"notice_zh"`                             // 公告 - 中文 - 非必填
	// 配送费列表
	FeeConfigs []CreateDeliveryFeeData `json:"fee_configs" binding:"required_if=Option 2,min=1"` // 配送费列表 - 区域时必填，餐厅时选填
	// 是否应用至所有餐厅
	Status uint `json:"status" binding:"oneof=0 1"` // 0 只保存 / 1 保存并应用于所有餐厅
}

// Validate 验证嵌套结构体
func (r *CreateDeliveryConfigsRequest) Validate() error {
	for i, fee := range r.FeeConfigs {
		if err := fee.Validate(); err != nil {
			return fmt.Errorf("fee_configs[%d]: %w", i, err)
		}
	}
	return nil
}

// UpdateDeliveryConfigsRequest 更新配送配置结构体
type UpdateDeliveryConfigsRequest struct {
	ID             int `json:"id" binding:"required,gte=1"`
	DeliveryAreaId int `json:"delivery_area_id" binding:""` // 如果有，则与配送范围数据绑定
	CreateDeliveryConfigsRequest
}

type DeliveryAreaPoint struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}
