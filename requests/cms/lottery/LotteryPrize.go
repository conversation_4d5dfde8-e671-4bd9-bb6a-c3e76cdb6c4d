﻿package lottery

type LotteryPrizeCreateRequest struct {
	TitleImgUg string `form:"title_img_ug" binding:"required"`
	// TitleImgZh        string `form:"title_img_zh" binding:"required"`
	SwiperImgUg string `form:"swiper_img_ug" binding:"required"`
	SwiperImgZh string `form:"swiper_img_zh" binding:"required"`
	NameUg      string `form:"name_ug" binding:"required"`
	NameZh      string `form:"name_zh" binding:"required"`
	Model       string `form:"model" binding:"required"`
	Price       int    `form:"price" binding:"required"`
	State       string `form:"state" binding:"required,oneof=0 1"`
	// Type   uint `form:"type" binding:"oneof=1 2"` // 1. 抽奖活动  2. 订单排行榜活动
	AreaID int  `form:"area_id"`
	CityID int  `form:"city_id"`
}

type LotteryPrizeEditRequest struct {
	ID         int    `form:"id" binding:"required"`
	TitleImgUg string `form:"title_img_ug" binding:"required"`
	// TitleImgZh        string `form:"title_img_zh" binding:"required"`
	SwiperImgUg string `form:"swiper_img_ug" binding:"required"`
	SwiperImgZh string `form:"swiper_img_zh" binding:"required"`
	NameUg      string `form:"name_ug" binding:"required"`
	NameZh      string `form:"name_zh" binding:"required"`
	Model       string `form:"model" binding:"required"`
	Price       int    `form:"price" binding:"required"`
	State       string `form:"state" binding:"required,oneof=0 1"`
	// Type   uint `form:"type" binding:"oneof=1 2"` // 1. 抽奖活动  2. 订单排行榜活动
	AreaID int  `form:"area_id"`
	CityID int  `form:"area_id"`
}
