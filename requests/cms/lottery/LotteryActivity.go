﻿package lottery

import "time"

type LotteryActivityCreateRequest struct {
	NameUg                   string                     `json:"name_ug" binding:"required"`
	NameZh                   string                     `json:"name_zh" binding:"required"`
	StartTime                string                     `json:"start_time" binding:"required"`
	EndTime                  string                     `json:"end_time" binding:"required"`
	ShowPosition             int                        `json:"show_position" binding:"required"`
	State                    string                     `json:"state" binding:"required,oneof=0 1"`
	Price                    int                        `json:"price" binding:"required"`
	MinPrizeOrderPrice       int                        `json:"min_prize_order_price" binding:""`
	MaxBuyCount              int                        `json:"max_buy_count" binding:""`
	ShareCount               int                        `json:"share_count" binding:""`
	ShareMinOrderPrice       int                        `json:"share_min_order_price" binding:""`
	ShareImage               string                     `json:"share_image" binding:"required"`
	CouponGroupID            int                        `json:"coupon_group_id" binding:"required"`
	RuleUG                   string                     `json:"rule_ug" binding:"required"`
	RuleZH                   string                     `json:"rule_zh" binding:"required"`
	TimeType                 int                        `json:"time_type" binding:"required"`
	ActiveDate               int                        `json:"active_date" binding:""`
	CouponEndTime            string                     `json:"coupon_end_time" binding:""`
	LotteryActivityLevelItem []LotteryActivityLevelItem `json:"lottery_level" binding:"required"`
}

type LotteryActivityLevelItem struct {
	Level   int `json:"level" binding:"required"`
	PrizeID int `json:"prize_id" binding:"required"`
	Count   int `json:"count" binding:"required"`
}

type LotteryActivityEditRequest struct {
	ID                       int                        `json:"id" binding:"required"`
	NameUg                   string                     `json:"name_ug" binding:"required"`
	NameZh                   string                     `json:"name_zh" binding:"required"`
	StartTime                string                     `json:"start_time" binding:"required"`
	EndTime                  string                     `json:"end_time" binding:"required"`
	ShowPosition             int                        `json:"show_position" binding:"required"`
	State                    string                     `json:"state" binding:"required,oneof=0 1"`
	Price                    int                        `json:"price" binding:"required"`
	MinPrizeOrderPrice       int                        `json:"min_prize_order_price" binding:""`
	MaxBuyCount              int                        `json:"max_buy_count" binding:""`
	ShareCount               int                        `json:"share_count" binding:""`
	ShareMinOrderPrice       int                        `json:"share_min_order_price" binding:""`
	ShareImage               string                     `json:"share_image" binding:"required"`
	CouponGroupID            int                        `json:"coupon_group_id" binding:"required"`
	RuleUG                   string                     `json:"rule_ug" binding:"required"`
	RuleZH                   string                     `json:"rule_zh" binding:"required"`
	TimeType                 int                        `json:"time_type" binding:"required"`
	ActiveDate               int                        `json:"active_date" binding:""`
	CouponEndTime            string                     `json:"coupon_end_time" binding:""`
	LotteryActivityLevelItem []LotteryActivityLevelItem `json:"lottery_level" binding:"required"`
}

type LotteryCommentRequestParams struct {
	LotteryActivityID int        `form:"lottery_activity_id" binding:"required"`
	CityID            int        `form:"city_id"`
	AreaID            int        `form:"area_id"`
	Type              int        `form:"type" binding:"omitempty,oneof=1 2"`
	Query             string     `form:"query"`
	BeginTime         *time.Time `form:"begin_time" time_format:"2006-01-02 15:04:05"`
	EndTime           *time.Time `form:"end_time" time_format:"2006-01-02 15:04:05"`
}

type GetUserPrizeExcelParams struct {
	LotteryActivityID int `form:"lottery_activity_id" binding:"required"`
}
