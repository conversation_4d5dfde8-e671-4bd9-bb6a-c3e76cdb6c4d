package lottery

type CouponDetail struct {
	MinPrice int `json:"min_price" binding:"required"`
	Price    int `json:"price" binding:"required"`
}

type CreateLotteryActivityGroupCouponRequest struct {
	NameUg       string         `json:"name_ug" binding:"required"`
	NameZh       string         `json:"name_zh" binding:"required"`
	State        int8           `json:"state" binding:"required"`
	CouponDetail []CouponDetail `json:"coupon_detail" binding:"required"`
	AdminID      *int           `json:"admin_id" binding:"omitempty"`
}

type UpdateLotteryActivityGroupCouponRequest struct {
	ID           int            `json:"id" binding:"required"`
	NameUg       *string        `json:"name_ug" binding:"omitempty"`
	NameZh       *string        `json:"name_zh" binding:"omitempty"`
	State        *int8          `json:"state" binding:"omitempty"`
	CouponDetail []CouponDetail `json:"coupon_detail" binding:"omitempty"`
	AdminID      *int           `json:"admin_id" binding:"omitempty"`
}
