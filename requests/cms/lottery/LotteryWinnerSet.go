package lottery

import (
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
	"time"
)

type LotteryWinnerSetPrize struct {
	Name                        string `json:"name" binding:"required"`                            // 奖品名称
	LotteryActivityID           int    `json:"lottery_activity_id" binding:"required"`             // 抽奖活动ID
	LotteryActivityLevelID      int    `json:"lottery_activity_level_id" binding:"required"`       // 抽奖活动级别ID
	LotteryActivityLevelPrizeID int    `json:"lottery_activity_level_prize_id" binding:"required"` // 抽奖活动级别奖品ID
	MinIndex                    int    `json:"min_index" binding:"required,min:1"`                 // 最小索引
	MaxIndex                    int    `json:"max_index" binding:"required,min:1"`                 // 最大索引
	PrizeCount                  int    `json:"prize_count" binding:"required,min:0"`               // 奖品数量
	Winners                     []int
}

type LotteryWinnerSetRequest struct {
	ActivityID int                     `json:"activity_id" binding:"required"` // 活动ID
	Prizes     []LotteryWinnerSetPrize `json:"prizes" binding:"required"`      // 奖品列表
}

func (r *LotteryWinnerSetRequest) Validate(langUtil lang.LangUtil) (bool, string) {

	if r.ActivityID == 0 {
		return false, langUtil.T("lottery_activity_id_required")
	}
	if len(r.Prizes) == 0 {
		return false, langUtil.T("lottery_activity_id_required")
	}
	db := tools.Db
	lotteryActivityDeail := models.LotteryActivity{}
	db.Model(&lotteryActivityDeail).Where("id=?", r.ActivityID).
		Preload("LotteryActivityLevel.LotteryActivityLevelPrize.LotteryPrize").
		Preload("LotteryActivityLevel.LotteryActivityLevelPrize.LotteryActivityLevelWinnersSet", "state = ?", 1).
		First(&lotteryActivityDeail)

	if lotteryActivityDeail.ID == 0 {
		return false, langUtil.T("lottery_activity_not_found")
	}
	if lotteryActivityDeail.State == models.LOTTERY_ACTIVITY_STATE_PAUSE ||
		lotteryActivityDeail.State == models.LOTTERY_ACTIVITY_STATE_EXPIRED {
		return false, langUtil.T("lottery_activity_state_error")
	}
	if lotteryActivityDeail.EndTime.Before(time.Now()) {
		return false, langUtil.T("lottery_activity_is_expired")
	}

	winners := make(map[int]int)
	for key, prize := range r.Prizes {
		contained := false
		for _, levelPrize := range lotteryActivityDeail.LotteryActivityLevel[0].LotteryActivityLevelPrize {

			if prize.LotteryActivityID == levelPrize.LotteryID &&
				prize.LotteryActivityLevelID == levelPrize.LotteryActivityLevelID &&
				prize.LotteryActivityLevelPrizeID == levelPrize.ID {
				// 如果所有的设置为0 时 跳过验证、 关闭所有的中奖位次设置
				if prize.MaxIndex == 0 && prize.MinIndex == 0 && prize.PrizeCount == 0 {
					contained = true
					break
				}
				stock := levelPrize.Count - int(levelPrize.TakenCount)
				if prize.PrizeCount > stock {
					return false, fmt.Sprintf(langUtil.T("lottery_activity_prize_stock_not_enough"),
						tools.GetNameByLang(levelPrize.LotteryPrize, langUtil.Lang), stock)
				}
				diff := prize.MaxIndex - prize.MinIndex
				if diff < 20 {
					return false, fmt.Sprintf(langUtil.T("lottery_activity_prize_range_error"),
						tools.GetNameByLang(levelPrize.LotteryPrize, langUtil.Lang), 20)
				}
				if diff < prize.PrizeCount*3 {
					return false, fmt.Sprintf(langUtil.T("lottery_activity_prize_range_must_bigger_than"),
						tools.GetNameByLang(levelPrize.LotteryPrize, langUtil.Lang))
				}

				var MaxDrawIndex *int
				db.Model(&models.LotteryChance{}).
					Where("lottery_activity_level_id = ?", 98).
					Select("MAX(draw_index)").
					Scan(&MaxDrawIndex)
				if MaxDrawIndex == nil {
					maxDrawIndex := 0
					MaxDrawIndex = &maxDrawIndex
				}

				if *MaxDrawIndex >= prize.MinIndex {
					return false, fmt.Sprintf(langUtil.T("lottery_activity_prize_start_index_must_bigger_than"),
						tools.GetNameByLang(levelPrize.LotteryPrize, langUtil.Lang), *MaxDrawIndex)
				}
				contained = true
				break
			}
		}
		if contained == false {
			return false, langUtil.T("lottery_activity_prize_id_not_match")
		}
		if ok, msg := r.generateWinners(&r.Prizes[key], &winners, langUtil); !ok {
			return false, msg
		}
	}
	return true, ""
}

func (r LotteryWinnerSetRequest) generateWinners(prize *LotteryWinnerSetPrize, winners *map[int]int, langUtil lang.LangUtil) (bool, string) {
	if prize.PrizeCount == 0 {
		return true, ""
	}
	diff := prize.MaxIndex - prize.MinIndex
	if prize.PrizeCount == 1 {
		num := diff / 4
		start := prize.MinIndex + num
		end := prize.MinIndex + num*2
		var randNum int
		i := 0
		for {
			randNum = tools.RandInt(start, end)
			if _, ok := (*winners)[randNum]; !ok {
				(*winners)[randNum] = prize.LotteryActivityLevelPrizeID
				prize.Winners = append(prize.Winners, randNum)
				break
			}
			i++
			if i > 3 {
				return false, fmt.Sprintf(langUtil.T("lottery_activity_generate_winners_fail"), prize.MinIndex, prize.MaxIndex)
			}
		}

	} else {
		num := diff / prize.PrizeCount
		for i := 0; i < prize.PrizeCount; i++ {
			start := prize.MinIndex + num*i
			end := prize.MinIndex + num*(i+1)
			var randNum int
			i := 0
			for {
				randNum = tools.RandInt(start, end)
				if _, ok := (*winners)[randNum]; !ok {
					(*winners)[randNum] = prize.LotteryActivityLevelPrizeID
					prize.Winners = append(prize.Winners, randNum)
					break
				}
				i++
				if i > 3 {
					return false, fmt.Sprintf(langUtil.T("lottery_activity_generate_winners_fail"), prize.MinIndex, prize.MaxIndex)
				}
			}
		}
	}
	return true, ""
}
