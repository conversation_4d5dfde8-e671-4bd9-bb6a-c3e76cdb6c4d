version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: mulazim-api-dev
    ports:
      - "8080:8080"  # 根据你的应用端口调整
    volumes:
      # 挂载整个项目目录到容器中，实现代码热重载
      - .:/app
      # 挂载Go模块缓存，提高构建速度
      - go-mod-cache:/go/pkg/mod
      # 挂载Go构建缓存
      - go-build-cache:/root/.cache/go-build
    environment:
      - GO111MODULE=on
      - GOPROXY=https://goproxy.cn
      - MYSQL_DSN=root:password@tcp(mysql:3306)/mulazim?charset=utf8mb4&parseTime=True&loc=Local
      - REDIS_IP=redis
      - REDIS_PASS=
    depends_on:
      - mysql
      - redis
    working_dir: /app
    # 使用air进行热重载
    command: air
    networks:
      - mulazim-network

  mysql:
    image: mysql:8.0
    container_name: mulazim-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: mulazim
      MYSQL_USER: mulazim
      MYSQL_PASSWORD: mulazim123
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - mulazim-network

  redis:
    image: redis:7-alpine
    container_name: mulazim-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - mulazim-network

volumes:
  mysql-data:
  redis-data:
  go-mod-cache:
  go-build-cache:

networks:
  mulazim-network:
    driver: bridge
