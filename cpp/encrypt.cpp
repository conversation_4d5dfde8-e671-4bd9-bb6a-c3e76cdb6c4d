#include <iostream>
#include <string>
#include <stdio.h>
#include <stdlib.h>
#include <openssl/evp.h>
#include <openssl/pem.h>
#include <fstream>
#include <string.h>
#include <openssl/dsa.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>
#include <openssl/evp.h>
#include <openssl/x509.h>
extern "C"
{
    char* base64Encode(const unsigned char* input, int length) {
        BIO *bio, *b64;
        FILE* stream;
        int encodedSize = 4 * ((length + 2) / 3); // 计算Base64编码后的长度

        char* buffer = (char *)malloc(encodedSize + 1); // 分配足够的空间来存储编码后的数据
        if (buffer == NULL) {
            printf("内存分配失败\n");
            return NULL;
        }

        stream = fmemopen(buffer, encodedSize + 1, "w");
        if (stream == NULL) {
            printf("打开流失败\n");
            free(buffer);
            return NULL;
        }

        b64 = BIO_new(BIO_f_base64());
        bio = BIO_new_fp(stream, BIO_NOCLOSE);
        bio = BIO_push(b64, bio);

        BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);
        BIO_write(bio, input, length);
        BIO_flush(bio);
        BIO_free_all(bio);
        fclose(stream);

        return buffer;
    }
    //  使用DSA私钥对数据进行签名
    char* signDataWithDSA(const char* privateKeyFilePath,  unsigned char* data, size_t dataLength) {
        // 读取私钥文件

        printf("file-path --- %s\n",privateKeyFilePath);
        printf("data --- %s\n",data);
        printf("dataLength --- %d\n",dataLength);

        FILE* privateKeyFile = fopen(privateKeyFilePath, "r");
        if (privateKeyFile == NULL) {
            printf("无法打开私钥文件\n");
            return NULL;
        }

        DSA* dsa = PEM_read_DSAPrivateKey(privateKeyFile, NULL, NULL, NULL);
        fclose(privateKeyFile);
        if (dsa == NULL) {
            printf("无法读取私钥\n");
            return NULL;
        }
        // 计算数据的SHA-1哈希值
        unsigned char hash[SHA_DIGEST_LENGTH];
        SHA1(data, dataLength, hash);
        // 进行DSA签名
        unsigned char signature[DSA_size(dsa)];
        unsigned int signatureLength;
        if (DSA_sign(0, hash, SHA_DIGEST_LENGTH, signature, &signatureLength, dsa) != 1) {
            printf("签名失败\n");
            return NULL;
        }

        const char* base64Signature = base64Encode(signature, signatureLength);
        DSA_free(dsa);
        //获取base64编码后的签名的长度并打印
        printf("base64Signature length: %d\n", sizeof(base64Signature));
        printf("base64Signature: %s\n", base64Signature);
        return (char *)base64Signature;
    }

}