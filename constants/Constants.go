package constants

// 管理员类型（OWNER:1,ADMIN:2,DEALER:3,DEALER_SUB:4,RESTAURANT_ADMIN:5,RESTAURANT_ADMIN_SUB:6,CENTURION:7,SHIPPER_ADMIN:8,SHIPPER:9）
const (
	ADMIN_TYPE_OWNER                = 1 // 超级管理员
	ADMIN_TYPE_ADMIN                = 2 // 管理员
	ADMIN_TYPE_DEALER               = 3 // 代理
	ADMIN_TYPE_DEALER_SUB           = 4 // 代理副账户
	ADMIN_TYPE_RESTAURANT_ADMIN     = 5 // 餐厅管理员
	ADMIN_TYPE_RESTAURANT_ADMIN_SUB = 6 // 餐厅管理员副账户
	ADMIN_TYPE_CENTURION            = 7 //
	ADMIN_TYPE_SHIPPER_ADMIN        = 8 // 配送员管理员
	ADMIN_TYPE_SHIPPER              = 9 // 配送员
)

// 支付相关常量
const (
	PaymentCash        = 1 // 现金
	PaymentCoin        = 2 // 餐币
	PaymentAlipay      = 3 // 支付宝
	PaymentUnionpay    = 4 // 银联
	PaymentWechat      = 5 // 微信
	PaymentAgentWechat = 6 // 代理商微信代付
	PaymentYunshanfu   = 7 // 云闪付
)

// 订单状态
const (
	ORDER_NEW             = 1  //新订单
	ORDER_CONFIRM         = 2  //确认订单
	ORDER_WAITING_RECEIVE = 3  //等待接收订单
	ORDER_RECEIVE         = 4  //已接受订单
	ORDER_READY_FOR_SEND  = 5  //订单已准备
	ORDER_SENDING         = 6  //订单配送中
	ORDER_SENDING_FINISH  = 7  //订单配送完成
	ORDER_CANCEL          = 8  //取消订单
	ORDER_RECEIVE_REFUSE  = 9  //餐厅拒绝接单
	ORDER_SENDING_FAIL    = 10 //订单配送失败
)

// 退款类型
const (
	REFUND_CHANEL_SYSTEM_AUTO = 1
	REFUND_CHANEL_DEALER      = 2
	REFUND_CHANEL_MERCHANT    = 3
	REFUND_CHANEL_CUSTOM      = 4
)

// 特殊天气类型 1：订单收入，2:奖励收入，3:打赏收入，4.特殊时间，5.特殊天气，6.好评，7.差评，8.投诉
const (
	TypeOrder          = 1 // 订单收入
	TypeAward          = 2 // 奖励收入
	TypeTips           = 3 // 打赏收入
	TypeSpecialTime    = 4 // 特殊时间
	TypeSpecialWeather = 5 // 特殊天气
	TypeGoodComment    = 6 // 好评
	TypeBadComment     = 7 // 差评
	TypeComplaint      = 8 // 投诉
	TypeLate           = 9 // 迟到
	TypeCancel	       = 10 //取消订单
	TypeFail	       = 11 //失败订单
	TypeBaseSalary	   = 12 //基本工资
	TypeFullAttendance = 13 //全勤奖
	TypeIntroduceCustomer = 14 //推荐用户奖励收入
	TypeIntroduceCustomerOrder = 15 //推荐用户下单奖励收入
	TypeInsurance = 16 //保险扣费
	
)

const (
	ShipperIncomeGeneralOrder          = 1 // 普通订单
	ShipperIncomeSpecialPriceOrder     = 2 // 特价活动订单
)

const (
	MerchantOrderPushReady             = 1 //订单开始推送 
	MerchantOrderPushSend              = 2 //订单推送 发送HTTP 请求
	MerchantOrderPushSendComplete      = 3 //订单推送 完成 发出声音


	ShipperOrderPushReady             = 1 //订单开始推送 
	ShipperOrderPushSend              = 2 //订单推送 发送HTTP 请求
	ShipperOrderPushSendComplete      = 3 //订单推送 完成 发出声音
)


const (

	PushUserTypeMerchant              = "t_restaurant" //商家端 key
	PushUserTypeShipper               = "t_admin" //配送端 key

    //商家端 默认通道  ,新订单有, 退单 
	ChannelTypeMerchantDefault        = 1  //商家默认渠道
	ChannelTypeMerchantOrder          = 2 //商家新订单渠道
	ChannelTypeMerchantOrderReturn    = 3 //商家退单渠道

	ChannelTypeShipperDefault         = 4  //配送员 默认
	ChannelTypeShipperNewOrder        = 5 //配送员 新订单
	ChannelTypeShipperAssignOrder     = 6 //配送员 管理员分配订单

	ChannelTypeShipperFoodReady     = 7 //配送员 订单已准备

	ChannelTypeShipperOrderCancelCms = 8 //配送员 取消订单 后台
	ChannelTypeShipperOrderCancelStore = 9 //配送员 取消订单 店铺
	ChannelTypeShipperOrderCancelCustomer = 10 //配送员 取消订单 客户

	ChannelTypeShipperChat     = 11 //配送员 聊天室

	


	ChannelTypeUserDefault            = 21  //用户默认渠道 21聊天室 2x 开始用其他类型的推送


	ChannelTypeShipperAutoDispatchAssignOrder     = 13 //配送员 智能派单分配订单



	SoundDefault                      = "" //商家端 默认声音
 	SoundMerchantNewOrder             = "order_tone" //商家端 新订单声音
	SoundMerchantAdminCancelOrder     = "admin_canceled_order" //商家端 被管理员取消订单
	
	SoundShipperAssignOrder           = "admin_assigned_order_to_shipper" //配送端 分配新订单
	SoundShipperFoodReady             = "food_is_ready_by_restaurant"
	SoundShipperNotification          = "home_notify" //配送端 普通通知
	SoundShipperCancelOrder           = "admin_canceled_order" //配送端 取消订单

	SoundShipperCancelOrderCustomer   = "customer_canceled_order" //客户取消订单
	SoundShipperCancelOrderStore      = "store_canceled_order" //餐厅取消订单
 
	SoundShipperAutoDispatchAssignOrder           = "system_assigned_order_to_shipper" //配送端 系统分配新订单
	

	SoundShipperNewOrder             = "order_tone" //配送端 新订单声音

	//配送端 socket 声音分类
	SoundShipperSocketTypeNewOrder                = 1 //有新的订单要抢 
	SoundShipperSocketTypeAdminAssignOrder        = 2 //管理员分配订单 
	SoundShipperSocketTypeAdminCancelOrder        = 3 //管理员取消订单 
	SoundShipperSocketTypeRestaurantCancelOrder   = 4 //店铺取消订单 
	SoundShipperSocketTypeCustomerCancelOrder     = 5 //客户取消订单 
	SoundShipperSocketTypeChat   				  = 6 //聊天室 
	SoundShipperSocketTypeFoodReady  			  = 7 //餐厅准备美食 
	SoundShipperSocketTypeHurry  			      = 8 //客户催单
	SoundShipperSocketTypeMeeting  			      = 9 // 有会议 要参加
	SoundShipperSocketTypeAlert  			      = 10 // 特殊情况
	SoundShipperSocketTypeChangeShipper  		  = 11 // 管理员切换配送员
	SoundShipperSocketTypeOrderPaid  		      = 12 // 订单支付完成
	SoundShipperSocketTypeSystemAssignOrder        = 13 // 智能派单推送



	TitleShipperNewOrderUg      	  = "يېڭى زاكاز چۈشتى" //商家端 新订单标题
	TitleShipperNewOrderZh      	  = "有新订单" //商家端 新订单标题
	ContentShipperNewOrderUg   	  = "يېڭى زاكاز چۈشتى" // 商家端 新订单内容
	ContentShipperNewOrderZh    	  = "有新订单" // 商家端 新订单内容





	ContentShipperAssignOrderUg       = "باشقۇرغۇچى سىزگە زاكاز تەقسىملىدى"
	ContentShipperAssignOrderZh       = "管理员给您分配订单，请及时处理"

	ContentShipperFoodReadyUg         = "ئاشخانا تاماقنى تەييار قىلدى"
	ContentShipperFoodReadyZh         = "商家已备餐"
	ContentShipperOrderCancelUg  	  = "زاكاز بىكار قىلىندى"
	ContentShipperOrderCancelZh  	  = "您的订单已被取消"

	TitleMerchantNewOrderUg      	  = "يېڭى زاكاز چۈشتى" //商家端 新订单标题
	TitleMerchantNewOrderZh      	  = "有新订单" //商家端 新订单标题
	ContentMerchantNewOrderUg   	  = "يېڭى زاكاز چۈشتى" // 商家端 新订单内容
	ContentMerchantNewOrderZh    	  = "有新订单" // 商家端 新订单内容

	TitleMerchantGroupShipmentUg 	  = "پائالىيەتكە تەكلىپ قىلىش"
	TitleMerchantGroupShipmentZh 	  = "邀请参加活动"

	TitleMerchantAdminCancelOrderUg   = "باشقۇرغۇچى زاكازنى قايتۇرۇۋەتتى"
	TitleMerchantAdminCancelOrderZh   = "管理员取消订单"
	ContentMerchantAdminCancelOrderUg = "باشقۇرغۇچى زاكازنى قايتۇرۇۋەتتى"
	ContentMerchantAdminCancelOrderZh = "管理员取消订单"


	ContentShipperAutoDispatchAssignOrderUg       = "سىستېما سىزگە زاكاز تەقسىملىدى"
	ContentShipperAutoDispatchAssignOrderZh       = "系统给您分配订单，请及时处理"

)


type StoreUserOrder struct {
	StoreId    		int  
	UserId     		int 
	OrderId    		int
	Type       		int 
	SerialNumber 	int
	Send            bool
	OrderSendKey    string
	AppVersion        int 
	AppPlatform     string
	Lang     string
	AppBrand        string
}


const (
	// 分配通道：1:配送员抢单，2:后台分配，3:智能派单，4:客户指定
	TakeOrderChannelShipper      = 1 // 1.配送员抢单
	TakeOrderChannelAdmin     = 2    // 2.后台分配
	TakeOrderChannelAutoDispatch = 3  // 3:智能派单
	TakeOrderChannelCustomer = 4   // 4:客户指定
)

const(
	FromMerchantClient = 1
	FromCmsClient = 2
)

