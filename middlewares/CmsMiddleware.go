package middlewares

import (
	"encoding/base64"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
	"net/http"

	"github.com/elliotchance/phpserialize"
	"github.com/gin-gonic/gin"
)

type SessionData struct {
	Uid   int64  `json:"login_dashboard_59ba36addc2b2f9401580f014c7f58ea4e30989d"`
	Token string `json:"_token"`
}

func CmsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		redis := tools.GetRedisCmsHelper()
		// 从 cookie 获取 登录信息
		cookie, err := c.<PERSON>("mulazim_two_point_zero")
		
		if err != nil {
			tools.Logger.Error("cms 认证 cookie 获取失败,uri:",c.Request.RequestURI)
			authorizeFail(c)
			return
		}
		// 解码cookie
		cipher := configs.MyApp.CmsCipher
		key, err := base64.StdEncoding.DecodeString(configs.MyApp.CmsKey)
		if err != nil {
			tools.Logger.Error("cms key 错误,uri:",c.Request.RequestURI)
			authorizeFail(c)
			return
		}
		// 解码cookie
		encrypter := tools.NewEncrypter(key, cipher)
		out, err := encrypter.Decrypt(cookie)
		if err != nil {
			tools.Logger.Error("cookie 解密失败 获取失败,uri:",c.Request.RequestURI)
			authorizeFail(c)
			return
		}

		var sessionId string
		// 解析 获取session id
		if err = phpserialize.Unmarshal(out, &sessionId); err != nil {
			tools.Logger.Error("cookie key unsereialize fail,uri:",c.Request.RequestURI)
			authorizeFail(c)
			return
		}
		// 从redis 获取用户session信息
		var session_key = "laravel:" + sessionId
		redisData := redis.Get(c, session_key)
		//var sessionData SessionData
		var sessionStr string
		if err = phpserialize.Unmarshal([]byte(redisData.Val()), &sessionStr); err != nil {
			tools.Logger.Error("解析session data 失败,uri:",c.Request.RequestURI)
			authorizeFail(c)
			return
		}
		// session信息反序列化
		var sessionMap map[interface{}]interface{}
		if sessionMap, err = phpserialize.UnmarshalAssociativeArray([]byte(sessionStr)); err != nil {
			tools.Logger.Error("解析session data 失败,uri:",c.Request.RequestURI)
			authorizeFail(c)
			return
		}

		// 获取 adminId
		adminId, keyExist := sessionMap["login_dashboard_59ba36addc2b2f9401580f014c7f58ea4e30989d"]
		if keyExist {
			db := tools.GetDB()
			fields :="id,"
			fields +="admin_city_id,"
			fields +="admin_area_id,"
			fields +="type,"
			fields +="level,"
			fields +="mobile,"
			fields +="name,"
			fields +="real_name,"
			fields +="grab_order_count,"
			fields +="recommend_qrcode,"
			fields +="state,"
			fields +="created_at,"
			fields +="take_cash_order,"
			fields +="back_order_time_limit,"
			fields +="last_comment_readed,"
			fields +="parent_id,"
			fields +="group_id,"
			fields +="team_id,"
			fields +="is_captain"
			
			var admin models.Admin
			db.Table("t_admin").
				Select(fields).
				//Joins("INNER JOIN `oauth_sessions` ON `oauth_sessions`.`owner_id` = `t_admin`.`id`").Joins("INNER JOIN `oauth_access_tokens` ON `oauth_sessions`.`id` = `oauth_access_tokens`.`session_id`").
				Where("`id`=? AND `state`=1 AND `deleted_at` IS NULL", adminId).
				Limit(1).
				Scan(&admin)
			//fmt.Println("admin : middlewre: ", admin)
			c.Set("admin", admin)
			c.Next()
		} else {
			tools.Logger.Error("用户ID 不存在")
			authorizeFail(c)
			return
		}
	}
}

func authorizeFail(c *gin.Context) {
	langUtil, _ := c.Get("lang_util")
	lang := langUtil.(lang.LangUtil)
	c.JSON(http.StatusOK, gin.H{
		"msg":    lang.T("not_authorized"),
		"status": http.StatusUnauthorized,
	})
	c.Abort()
}
