package middlewares

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/lang"
)

func LangMiddleware() gin.HandlerFunc {
	return func(context *gin.Context) {
		locale := context.Param("locale")
		if locale != "zh" && locale != "ug" {
			locale = "ug" //没传语言默认填充 ug
			// context.JSON(404, gin.H{
			// 	"status": 404,
			// 	"msg":    "lang not found",
			// 	"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
			// })
		}
		context.Set("lang", locale)
		context.Set("lang_util", lang.LangUtil{
			Lang: locale,
		})
		context.Next()
	}
}
