package middlewares

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"mulazim-api/constants"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
)

func PermissionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		langUtil, _ := c.Get("lang_util")
		lang := langUtil.(lang.LangUtil)
		check := CheckPermission(c)
		if !check{
			c.JSON(http.StatusOK, gin.H{
				"msg":    lang.T("forbidden"),
				"status": http.StatusForbidden,
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// CheckPermission
//
// @Description: 验证权限
// @Author: Rixat
// @Time: 2024-05-25 11:02:55
// @receiver 
// @param c *gin.Context
func CheckPermission(c *gin.Context) bool{
	urlPath := c.Request.URL.Path
	urlPath = strings.Replace(urlPath,"/ug","",1)
	urlPath = strings.Replace(urlPath,"/zh","",1)
	method := c.Request.Method
	// tools.Logger.Error("接口信息:",method,":",urlPath)
	enforcer := tools.GetCasbinEnforcer()

	var value, _ = c.Get("admin")
	if value == nil{
		return true
	}
	var admin    = value.(models.Admin)
	if admin.MerchantType == 1 || admin.Type != constants.ADMIN_TYPE_RESTAURANT_ADMIN_SUB {
		return true;
	}
	
    a, err := enforcer.Enforce(tools.ToString(admin.ID), urlPath, method)
	if (a && err==nil) {
		return true
	}
	tools.Logger.Error("权限不通过:",method,":",urlPath)
	return false
}