package middlewares

import (
	"bytes"
	"github.com/gin-gonic/gin"
	"io"
	"mulazim-api/configs/monitor"
	"mulazim-api/tools"
	"strings"
)

type responseWriter struct {
gin.ResponseWriter
b *bytes.Buffer
}
func(w responseWriter)Write(b []byte)(int, error) {
	//向一个bytes.buffer中写一份数据来为获取body使用
	w.b.Write(b)
	//完成gin.Context.Writer.Write()原有功能
	return w.ResponseWriter.Write(b)
}
func MonitorMiddleware() gin.HandlerFunc {
	return func(context *gin.Context) {
		path := context.FullPath()
		shouldPrint := false
		for _,url := range monitor.MonitoredUrls.Urls {
			if strings.Contains(path,url) {
				shouldPrint = true
			}
		}
		if shouldPrint {
			writer := responseWriter{
				context.Writer,
				bytes.NewBuffer([]byte{}),
			}
			context.Writer = writer
			context.Next()
			params := make(map[string]interface{})
			if context.Request.Method == "GET" {
				q := context.Request.URL.Query()
				for k, v := range q {
					params[k] = v
				}
			} else {
				pf := context.Request.PostForm
				for key, _ := range pf {
					params[key] = context.PostForm(key)
				}
				bd := context.Request.Body
				if bd != nil {
					body, _ := io.ReadAll(context.Request.Body)
					params["params"] = string(body)
				}
			}
			tools.Logger.Infow("request",
				"method", context.Request.Method,
				"uri", context.Request.RequestURI,
				"headers", context.Request.Header,
				"params", params,
				"ip", context.ClientIP(),
				"ua", context.Request.UserAgent(),
				"referer", context.Request.Referer(),
				"host", context.Request.Host,
				"proto", context.Request.Proto,
				"response_status", context.Writer.Status(),
				"response", writer.b.String(),
			)
		}else{
			context.Next()
		}


	}
}