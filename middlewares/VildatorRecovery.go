package middlewares

import (
	"fmt"
	"io"
	"mulazim-api/errors"
	"mulazim-api/lang"
	"mulazim-api/tools"
	"mulazim-api/validators"
	ugTranslations "mulazim-api/validators/ug"
	chTranslations "mulazim-api/validators/zh"
	"net/http"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/en"
	"github.com/go-playground/locales/ug"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
)

var transUg ut.Translator
var transZh ut.Translator

// init
//
//	@Description: 加载所有的语言文件
func init() {
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		zhT := zh.New() //chinese
		enT := en.New() //english
		ugT := ug.New() //english
		uni := ut.New(ugT, enT, zhT, ugT)

		transUg, _ = uni.GetTranslator("ug")
		transZh, _ = uni.GetTranslator("zh")
		chTranslations.RegisterDefaultTranslations(v, transZh)
		ugTranslations.RegisterDefaultTranslations(v, transUg)
		//register translate
		// 注册翻译器
		_ = v.RegisterValidation("date", dateValidater)
		_ = v.RegisterValidation("phone", validators.PhoneValidater)
		_ = v.RegisterValidation("password", validators.PasswordValidater)
		_ = v.RegisterValidation("exists", validators.ExistsValidater)

		// 添加额外翻译
		_ = v.RegisterTranslation("date", transUg, func(ut ut.Translator) error {
			return ut.Add("date", "{0} شەكلى خاتا", true)
		}, func(ut ut.Translator, fe validator.FieldError) string {
			t, _ := ut.T("date", fe.Field())
			return t
		})
		_ = v.RegisterTranslation("date", transZh, func(ut ut.Translator) error {
			return ut.Add("date", "{0}格式错误", true)
		}, func(ut ut.Translator, fe validator.FieldError) string {
			t, _ := ut.T("date", fe.Field())
			return t
		})

		// 添加额外翻译
		_ = v.RegisterTranslation("phone", transUg,validators.PhoneRegisterTranslationsUg , validators.PhoneTranslationFuncUg)
		_ = v.RegisterTranslation("phone", transZh,validators.PhoneRegisterTranslationsZh , validators.PhoneTranslationFuncZh)

		_ = v.RegisterTranslation("password", transUg,validators.PasswordRegisterTranslationsUg , validators.PasswordTranslationFuncUg)
		_ = v.RegisterTranslation("password", transZh,validators.PasswordRegisterTranslationsZh , validators.PasswordTranslationFuncZh)

		_ = v.RegisterTranslation("exists", transUg,validators.ExistisRegisterTranslationsUg , validators.ExistisTranslationFuncUg)
		_ = v.RegisterTranslation("exists", transZh,validators.ExistisRegisterTranslationsZh , validators.ExistisTranslationFuncZh)

		return
	}
}

/*
日期验证 2022年09月02日
*/
var dateValidater validator.Func = func(fl validator.FieldLevel) bool {
	timStr := fl.Field().String()
	if len(timStr) == 0 {
		return true
	}
	if _, err := time.Parse("2006-01-02", timStr); err != nil {
		return false
	}
	return true
}

// VildatorRecovery
//
//	@Description: 语言验证处理异常
//	@author: Alimjan
//	@Time: 2022-09-03 13:53:51
//	@return func(c *gin.Context)
func VildatorRecovery() func(c *gin.Context) {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil { //用于捕获panic

				l, _ := c.Get("lang_util")
				langUtil := l.(lang.LangUtil)
				if e, ok := err.(*strconv.NumError); ok {
					c.JSON(http.StatusOK, map[string]interface{}{"status": -1000, "msg": fmt.Sprintf(langUtil.T("validator_should_be_int"), e.Num)})
					c.Abort()
					return
				}
				if e, ok := err.(validator.ValidationErrors); ok {

					rtnError := ""
					lang, _ := c.Get("lang")
					langStr := lang.(string)
					var errMap validator.ValidationErrorsTranslations
					if langStr == "ug" {
						errMap = e.Translate(transUg)
					} else {
						errMap = e.Translate(transZh)
					}
					for k, v := range errMap {
						if strings.Contains(v, "Field validation") { //validation error

							index := strings.LastIndex(k, ".")
							langKey := k[index+1:]
							transVal := langUtil.T(langKey)
							//输出 未经过翻译的字段
							if langKey == transVal {
								urlPath := c.Request.URL.Path
								tools.Logger.Error("未经过翻译: ",urlPath+"==>"+langStr+":"+langKey)
							}
							v = transVal
							rtnError += v + " " + langUtil.T("not_empty") + ","
							break
						}
						index := strings.LastIndex(k, ".")
						langKey := k[index+1:]
						transVal := langUtil.T(langKey)
						//输出 未经过翻译的字段
						if langKey == transVal {
							urlPath := c.Request.URL.Path
							tools.Logger.Error("未经过翻译: ",urlPath+"==>"+langStr+":"+langKey)
						}
						v = strings.ReplaceAll(v, langKey, transVal)
						rtnError += v + ","
						break //只输出第一个错误
					}
					if len(rtnError) > 0 {
						rtnError = rtnError[:len(rtnError)-1]
					}
					c.JSON(http.StatusOK, map[string]interface{}{"status": -1000, "msg": rtnError})
					c.Abort()
				} else if e, ok := err.(errors.CustomError); ok {
					c.JSON(http.StatusOK, map[string]interface{}{"status": -1000, "msg": langUtil.T(e.Error())})
					c.Abort()
				} else {
					params := make(map[string]interface{})
					if c.Request.Method == "GET" {
						q := c.Request.URL.Query()
						for k, v := range q {
							params[k] = v
						}
					} else {
						pf := c.Request.PostForm

						for key, _ := range pf {
							params[key] = c.PostForm(key)
						}
						bd := c.Request.Body
						if bd != nil {
							body, _ := io.ReadAll(c.Request.Body)
							params["params"] = string(body)
						}

					}
					
					if !strings.Contains(c.Request.RequestURI,"completed-order-list") &&
					 !strings.Contains(c.Request.RequestURI,"completed")  &&
					 !strings.Contains(c.Request.RequestURI,"shipper-list")  &&
					 !strings.Contains(c.Request.RequestURI,"/v1/about/detail")&& !strings.Contains(c.Request.RequestURI,"/v2/chat/list"){
						tools.Logger.Errorw("FATAL exception",
							"err", err,
							"uri", c.Request.RequestURI,
							"method", c.Request.Method,
							"headers", c.Request.Header,
							"params", params,
							"desc", string(debug.Stack()),
						)
					}

					c.JSON(http.StatusOK, map[string]interface{}{"status": 403, "msg": langUtil.T("error_happend")})
					c.Abort()
				}
			}
		}()
		c.Next() // 调用下一个处理
	}

}
