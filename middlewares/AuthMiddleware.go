package middlewares

import (
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		langUtil, _ := c.Get("lang_util")
		lang := langUtil.(lang.LangUtil)
		if token := c.GetHeader("authorization"); token == "" || len(token) < 7 {
			fmt.Printf("%s token 为空\n", carbon.Now().ToDateTimeString())
			c.J<PERSON>(http.StatusOK, gin.H{
				"msg":    lang.T("not_authorized"),
				"status": http.StatusUnauthorized,
			})
			c.Abort()
			return
		} else {
			//serialNumber := c.GetHeader("searialnumber")
			db := tools.GetDB()
			var admin models.Admin
			
			selectFields :="t_admin.id,"+
						   "t_admin.type,"+
						   "t_admin.parent_id,"+
						   "t_admin.merchant_type,"+
						   "t_admin.admin_city_id,"+
						   "t_admin.admin_area_id,"+
						   "t_admin.level,"+
						   "t_admin.mobile,"+
						   "t_admin.name,"+
						   "t_admin.real_name,"+
						   "t_admin.avatar,"+
						   "t_admin.grab_order_count,"+
						   "t_admin.auto_dispatch_rank_order_count,"+
						   "t_admin.recommend_qrcode,"+
						   "t_admin.state,t_admin.created_at,"+
						   "t_admin.attendance_state,"+
						   "t_admin.shipper_income_template_id,"+
						   "take_cash_order,"+
						   "back_order_time_limit,"+
						   "last_comment_readed"

			query := db.Table("t_admin").
				Select(selectFields)
			tokenString := token[7:]
			if strings.Contains(tokenString, "."){
				//jwt
				adminID,jwtSerialNumber,err := tools.NewJWTTools().ParseTokenAndGetIDAndJWTSerialNumber(tokenString)
				if err!=nil || adminID == 0{
					c.JSON(http.StatusOK, gin.H{
						"msg":    lang.T("not_authorized"),
						"status": http.StatusUnauthorized,
					})
					c.Abort()
					return
				}else{
					query.Where("t_admin.id = ? and jwt_serial_number = ?", adminID,jwtSerialNumber)
				}
			}else{
				query.Joins("INNER JOIN `oauth_sessions` ON `oauth_sessions`.`owner_id` = `t_admin`.`id`").Joins("INNER JOIN `oauth_access_tokens` ON `oauth_sessions`.`id` = `oauth_access_tokens`.`session_id`").
					Where("`oauth_access_tokens`.`id`=?", tokenString)

			}
			rs := query.Where("`t_admin`.`deleted_at` IS NULL ").
				Limit(1).
				Scan(&admin)

			if rs.Error != nil {
				msg := fmt.Sprintf("token = %s,查询admin失败,错误信息：%s", token, rs.Error.Error())
				tools.Logger.Error(msg)
				c.JSON(http.StatusOK, gin.H{
					"msg":    lang.T("fail"),
					"status": http.StatusBadRequest,
				})
				c.Abort()
				return
			}
			if admin.ID == 0 {
				fmt.Printf("%s  admin 查询失败 token=%s\n", carbon.Now().ToDateTimeString(), token)
				c.JSON(http.StatusOK, gin.H{
					"msg":    lang.T("not_authorized"),
					"status": http.StatusUnauthorized,
				})
				c.Abort()
				return
			}
			c.Set("admin", admin)
			// 让程序继续正常运行
			c.Next()
		}
	}
}
