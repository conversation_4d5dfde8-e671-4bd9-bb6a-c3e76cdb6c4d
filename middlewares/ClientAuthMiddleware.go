package middlewares

import (
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

func ClientAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		langUtil, _ := c.Get("lang_util")
		lang := langUtil.(lang.LangUtil)
		serialNumber := c.GetHeader("searialnumber")
		if token := c.GetHeader("Authorization"); token == "" || serialNumber == "" || len(token) < 7 {
			fmt.Printf("%s token 为空\n", carbon.Now().ToDateTimeString())
			c.JSON(http.StatusOK, gin.H{
				"msg":    lang.T("not_authorized"),
				"status": http.StatusUnauthorized,
			})
			c.Abort()
		} else {
			db := tools.GetDB()
			var user models.User
			db.Table("t_user").
				Select("t_user.id,t_user.searial_number,t_user.state").
				Joins("INNER JOIN `oauth_sessions` ON `oauth_sessions`.`owner_id` = `t_user`.`id`").
				Joins("INNER JOIN `oauth_access_tokens` ON `oauth_sessions`.`id` = `oauth_access_tokens`.`session_id`").
				Where("oauth_access_tokens.id= ?", token[7:]).
				Where("t_user.searial_number=? ", serialNumber).
				Where("oauth_access_tokens.expire_time > ?", carbon.Now(configs.AsiaShanghai)).
				Where("t_user.deleted_at IS NULL").
				Find(&user)
			c.Set("user", user)
			if user.ID == 0 || user.State != 1 || user.SearialNumber != serialNumber {
				fmt.Printf("%s  user 查询失败 token=%s,serialNumber=%s\n", carbon.Now().ToDateTimeString(), token, serialNumber)
				c.JSON(http.StatusOK, gin.H{
					"msg":    lang.T("not_authorized"),
					"status": http.StatusUnauthorized,
				})
				c.Abort()
			}
			// 让程序继续正常运行
			c.Next()
		}
	}
}
