package middlewares

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/models/cms"
	"mulazim-api/tools"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

var permissions = map[string]string{
	"/:locale/cms/v2/today-order/new-order-count":                       "cms.todayOrder.list.newOrder.count",
	"/:locale/cms/v2/today-order/list":                                  "cms.todayOrder.list",
	"/:locale/cms/v2/today-order/state-list":                            "cms.todayOrder.list.state",
	"/:locale/cms/v2/today-order/order-reset-reason-list-by-select-box": "cms.todayOrder.list.reason.select",
	"/:locale/cms/v2/today-order/edit-shipper":                          "cms.todayOrder.update.shipper",
	"/:locale/cms/v2/address/city/list-for-select-box":                  "cms.city.list.select",
	"/:locale/cms/v2/address/area/list-for-select-box":                  "cms.area.list.select",
	"/:locale/cms/v2/today-order/grant-complete":                        "cms.street.list.select",
	"/:locale/cms/v2/today-order/set-shipper":                           "cms.todayOrder.set.shipper",

	"/:locale/cms/v2/menu/list":    "cms.menu.list",
	"/:locale/cms/v2/upload-image": "cms.upload.image",

	"/:locale/cms/v2/shipper/list":                          "cms.shipper.list",
	"/:locale/cms/v2/shipper/info":                          "cms.shipper.info",
	"/:locale/cms/v2/shipper/account-info":                  "cms.shipper.account.info",
	"/:locale/cms/v2/shipper/update-account":                "cms.shipper.account.update",
	"/:locale/cms/v2/shipper/batch-update-shipper-template": "cms.shipper.account.update",
	"/:locale/cms/v2/shipper/idcard-info":                   "cms.shipper.idcard.info",
	"/:locale/cms/v2/shipper/bank-info":                     "cms.shipper.bank.info",
	"/:locale/cms/v2/shipper/health-info":                   "cms.shipper.health.info",
	"/:locale/cms/v2/shipper/delete":                        "cms.shipper.delete",
	"/:locale/cms/v2/shipper/change-state":                  "cms.shipper.change-state",
	"/:locale/cms/v2/shipper/bank-areas":                    "cms.shipper.bank.areas",
	"/:locale/cms/v2/shipper/bank-list":                     "cms.shipper.bank.list",
	"/:locale/cms/v2/shipper/branch-bank":                   "cms.shipper.branch.bank",
	"/:locale/cms/v2/shipper/send-code":                     "cms.shipper.send.code",
	
	"/:locale/cms/v2/shipper/update-info":                     "cms.shipper.update-info",
	"/:locale/cms/v2/shipper/need-alert":                     "cms.shipper.need-alert",
	


	"/:locale/cms/v2/shipment/template/list":                "cms.shipment.template.list",
	"/:locale/cms/v2/shipment/template/detail":              "cms.shipment.template.detail",
	"/:locale/cms/v2/shipment/template/create":              "cms.shipment.template.create",
	"/:locale/cms/v2/shipment/template/delete":              "cms.shipment.template.delete",
	"/:locale/cms/v2/shipment/special-weather/list":         "cms.shipment.special.weather.list",
	"/:locale/cms/v2/shipment/special-weather/detail":       "cms.shipment.special.weather.detail",
	"/:locale/cms/v2/shipment/special-weather/create":       "cms.shipment.special.weather.create",
	"/:locale/cms/v2/shipment/special-weather/update":       "cms.shipment.special.weather.update",
	"/:locale/cms/v2/shipment/special-weather/change-state": "cms.shipment.special.weather.change.state",
	"/:locale/cms/v2/shipment/special-weather/delete":       "cms.shipment.special.weather.delete",
	"/:locale/cms/v2/shipment/complaint/list":               "cms.shipment.complaint.list",
	"/:locale/cms/v2/shipment/complaint/detail":             "cms.shipment.complaint.detail",
	"/:locale/cms/v2/shipment/complaint/create":             "cms.shipment.complaint.create",
	"/:locale/cms/v2/shipment/income/types":                 "cms.shipment.income.types",
	"/:locale/cms/v2/common/order-list":                     "cms.shipment.common.order.list",
	"/:locale/cms/v2/common/shipper-list":                   "cms.shipment.common.shipper.list",
	
	"/:locale/cms/v2/common/agent-form-list":                   "cms.shipment.common.agent-form.list",

	"/:locale/cms/v2/shipment/salary/list":                  "cms.shipment.salary.list",
	"/:locale/cms/v2/shipment/salary/detail":                "cms.shipment.salary.detail",
	"/:locale/cms/v2/shipment/salary/list-export":           "cms.shipment.salary.list.export",
	"/:locale/cms/v2/shipment/salary/detail-export":         "cms.shipment.salary.detail.export",

	"/:locale/cms/v2/shipment/notify/list":                 "cms.shipment.notify.list",
	"/:locale/cms/v2/shipment/notify/create":               "cms.shipment.notify.create",
	"/:locale/cms/v2/shipment/notify/change-state":         "cms.shipment.notify.change.state",
	"/:locale/cms/v2/shipment/notify/delete":               "cms.shipment.notify.delete",
	"/:locale/cms/v2/shipment/attendance/list-clock":       "cms.shipment.attendance.clock.list",
	"/:locale/cms/v2/shipment/attendance/clock-detail":     "cms.shipment.attendance.clock.detail",
	"/:locale/cms/v2/shipment/attendance/list":             "cms.shipment.attendance.list",
	"/:locale/cms/v2/shipment/attendance/detail":           "cms.shipment.attendance.detail",
	"/:locale/cms/v2/shipment/attendance/create":           "cms.shipment.attendance.create",
	"/:locale/cms/v2/shipment/attendance/review":           "cms.shipment.attendance.review",
	"/:locale/cms/v2/shipment/attendance/save-clock":       "cms.shipment.attendance.clock.save",
	"/:locale/cms/v2/shipment/attendance/leave-types":      "cms.shipment.attendance.leave.types",
	"/:locale/cms/v2/shipment/situation/list":              "cms.shipment.situation.list",
	"/:locale/cms/v2/shipment/situation/detail":            "cms.shipment.situation.detail",
	"/:locale/cms/v2/shipment/reward-setting/list":         "cms.shipment.reward-setting.list",
	"/:locale/cms/v2/shipment/reward-setting/detail":       "cms.shipment.reward-setting.detail",
	"/:locale/cms/v2/shipment/reward-setting/save":         "cms.shipment.reward-setting.save",
	"/:locale/cms/v2/shipment/reward-setting/change-state": "cms.shipment.reward-setting.change.state",

	"/:locale/cms/v2/today-order/detail":                 "cms.today.order.detail",
	"/:locale/cms/v2/address/street/list-for-select-box": "cms.address.street.list-for-select-box",

	"/:locale/cms/v2/chat/send-msg":               "cms.chat.send-msg",
	"/:locale/cms/v2/chat/list":                   "cms.chat.list",
	"/:locale/cms/v2/chat/detail":                 "cms.chat.detail",
	"/:locale/cms/v2/shipment/opinion/list":       "cms.opinion.list",
	"/:locale/cms/v2/opinion/detail":              "cms.opinion.detail",
	"/:locale/cms/v2/map/new-received-order-list": "cms.map.new-received-order-list",
	"/:locale/cms/v2/map/shipper-list":            "cms.map.shipper-list",

	"/:locale/cms/v2/restaurant/search-by-area-id":                    "cms.v2.restaurant.search-by-area-id",
	"/:locale/cms/v2/marketing/shipment-reduce/list":                  "cms.v2.marketing.shipment-reduce.list",
	"/:locale/cms/v2/marketing/shipment-reduce/create":                "cms.v2.marketing.shipment-reduce.create",
	"/:locale/cms/v2/marketing/shipment-reduce/download":              "cms.v2.marketing.shipment-reduce.download",
	"/:locale/cms/v2/marketing/shipment-reduce/delete":                "cms.v2.marketing.shipment-reduce.delete",
	"/:locale/cms/v2/marketing/shipment-reduce/:id/detail-for-update": "cms.v2.marketing.shipment-reduce.detail-for-update",
	"/:locale/cms/v2/marketing/shipment-reduce/:id/update":            "cms.v2.marketing.shipment-reduce.update",
	"/:locale/cms/v2/marketing/shipment-reduce/change-state":          "cms.v2.marketing.shipment-reduce.change-state",
	"/:locale/cms/v2/marketing/shipment-reduce/:id/detail":            "cms.v2.marketing.shipment-reduce.detail",
	"/:locale/cms/v2/marketing/shipment-reduce/:id/orders":            "cms.v2.marketing.shipment-reduce.orders",
	"/:locale/cms/v2/marketing/shipment-reduce/:id/orders-download":   "cms.v2.marketing.shipment-reduce.orders-download",

	"/:locale/cms/v2/marketing-group/create":                "cms.v2.marketing-group.create",
	"/:locale/cms/v2/marketing-group/:id/detail-for-update": "cms.v2.marketing-group.detail-for-update",
	"/:locale/cms/v2/marketing-group/:id/detail":            "cms.v2.marketing-group.detail",
	"/:locale/cms/v2/marketing-group/:id/on":                "cms.v2.marketing-group.on",
	"/:locale/cms/v2/marketing-group/:id/pause":             "cms.v2.marketing-group.pause",
	//"/:locale/cms/v2/marketing-group/change-state":                          "cms.v2.marketing-group.change-state",
	"/:locale/cms/v2/marketing-group/send-join-push":             "cms.v2.marketing-group.send-join-push",
	"/:locale/cms/v2/marketing-group/send-join-push-one":         "cms.v2.marketing-group.send-join-push-one",
	"/:locale/cms/v2/marketing-group/:id/update":                 "cms.v2.marketing-group.update",
	"/:locale/cms/v2/marketing-group/list":                       "cms.v2.marketing-group.list",
	"/:locale/cms/v2/marketing-group/download":                   "cms.v2.marketing-group.download",
	"/:locale/cms/v2/marketing-group/delete":                     "cms.v2.marketing-group.delete",
	"/:locale/cms/v2/marketing-group/detail-restaurant":          "cms.v2.marketing-group.detail-restaurant",
	"/:locale/cms/v2/marketing-group/detail-restaurant-add":      "cms.v2.marketing-group.detail-restaurant-add",
	"/:locale/cms/v2/marketing-group/detail-restaurant-download": "cms.v2.marketing-group.detail-restaurant-download",
	"/:locale/cms/v2/marketing-group/detail-order":               "cms.v2.marketing-group.detail-order",
	"/:locale/cms/v2/marketing-group/detail-order-download":      "cms.v2.marketing-group.detail-order-download",

	"/:locale/cms/v2/marketing/shipment-reduce/statistic/restaurants":       "cms.v2.marketing.shipment-reduce.statistic.restaurants",
	"/:locale/cms/v2/marketing/shipment-reduce/statistic/restaurant/statis": "cms.v2.marketing.shipment-reduce.statistic.restaurant.statis",
	"/:locale/cms/v2/marketing/shipment-reduce/statistic/groups":            "cms.v2.marketing.shipment-reduce.statistic.groups",
	"/:locale/cms/v2/marketing/shipment-reduce/statistic/group/statis":      "cms.v2.marketing.shipment-reduce.statistic.group.statis",
	"/:locale/cms/v2/advert-material/category/create":                       "cms.v2.advert-material.category.create", // 创建宣传材料分类
	"/:locale/cms/v2/advert-material/category/:id/update":                   "cms.v2.advert-material.category.update", // 更新宣传材料分类
	"/:locale/cms/v2/advert-material/category/:id/update-state":             "cms.v2.advert-material.category.update", // 更新宣传材料分类状态
	"/:locale/cms/v2/advert-material/category/:id/delete":                   "cms.v2.advert-material.category.delete", // 删除宣传材料分类
	"/:locale/cms/v2/advert-material/category/:id/detail":                   "cms.v2.advert-material.category.detail", // 查看宣传材料分类
	"/:locale/cms/v2/advert-material/category/list":                         "cms.v2.advert-material.category.list",   // 宣传材料分类列表

	"/:locale/cms/v2/advert-material/create":           "cms.v2.advert-material.create", // 宣传材料创建
	"/:locale/cms/v2/advert-material/list":             "cms.v2.advert-material.list",   // 宣传材料分类列表
	"/:locale/cms/v2/advert-material/:id/update":       "cms.v2.advert-material.update", // 宣传材料更新
	"/:locale/cms/v2/advert-material/:id/update-state": "cms.v2.advert-material.update", // 宣传材料更新状态
	"/:locale/cms/v2/advert-material/:id/detail":       "cms.v2.advert-material.detail", // 宣传材料详情
	"/:locale/cms/v2/advert-material/:id/delete":       "cms.v2.advert-material.delete", // 宣传材料删除

	"/:locale/cms/v2/advert-material/:materialId/print-batch/create": "cms.v2.advert-material.print-batch.create", // 宣传材料打印批次创建
	"/:locale/cms/v2/advert-material/:materialId/print-batch/list":   "cms.v2.advert-material.print-batch.list",   // 宣传材料打印批次列表
	"/:locale/cms/v2/advert-material/print-batch/:batchId/download":  "cms.v2.advert-material.print-batch.list",   // 宣传材料打印批次列表下载

	"/:locale/cms/v2/advert-material/take-history/statistic":           "cms.v2.advert-material.take-history.list", // 宣传材料领取统计
	"/:locale/cms/v2/advert-material/take-history/list":                "cms.v2.advert-material.take-history.list", // 宣传材料领取列表
	"/:locale/cms/v2/advert-material/take-history/:takeId/detail":      "cms.v2.advert-material.take-history.list", // 宣传材料领取详情
	"/:locale/cms/v2/advert-material/take-history/:takeId/detail/list": "cms.v2.advert-material.take-history.list", // 宣传材料领取详情

	"/:locale/cms/v2/advert-material/invite-user-statistic/by-shipper":          "cms.v2.advert-material.invite-user-statistic.by-shipper", // 邀请用户统计
	"/:locale/cms/v2/advert-material/invite-user-statistic/by-shipper-download": "cms.v2.advert-material.invite-user-statistic.by-shipper", // 邀请用户统计下载

	"/:locale/cms/v2/advert-material/invite-user-statistic/by-area":          "cms.v2.advert-material.invite-user-statistic.by-area", // 邀请用户区域
	"/:locale/cms/v2/advert-material/invite-user-statistic/by-area-download": "cms.v2.advert-material.invite-user-statistic.by-area", // 邀请用户区域下载

	"/:locale/cms/v2/advert-material/shipper-monthly-statistic/:shipperId/head": "cms.v2.advert-material.shipper-monthly-statistic", // 配送员邀请用户统计下载
	"/:locale/cms/v2/advert-material/shipper-monthly-statistic/:shipperId/list": "cms.v2.advert-material.shipper-monthly-statistic", // 配送员邀请用户统计下载

	"/:locale/cms/v2/food-statics/food-statics":              "cms.v2.food-statics.food-statics-list",
	"/:locale/cms/v2/food-statics/restaurant-statics":        "cms.v2.food-statics.food-statics-list",
	"/:locale/cms/v2/food-statics/restaurant-statics-export": "cms.v2.food-statics.food-statics-list",

	"/:locale/cms/v2/customer-statics/customer-statics":      "cms.v2.customer-statics.customer-statics-list.list",
	"/:locale/cms/v2/customer-statics/customer-store-statics":      "cms.v2.customer-statics.customer-statics-list.list",
	"/:locale/cms/v2/customer-statics/customer-food-statics":      "cms.v2.customer-statics.customer-statics-list.list",

	"/:locale/cms/v2/order-statics/today":      "todayReport.list",


	"/:locale/cms/v2/shipper-user-change/list":      "cms.v2.customer-statics.shipper-user-change.list",
	"/:locale/cms/v2/shipper-user-change/create":      "cms.v2.customer-statics.shipper-user-change.create",
	"/:locale/cms/v2/shipper-user-change/delete":      "cms.v2.customer-statics.shipper-user-change.delete",

	"/:locale/cms/v2/customer-statics/new-customer-statics":      "cms.v2.customer-statics.new-customer-statics.list",
	

	"/:locale/cms/v2/shipping-area/create":                    "cms.v2.restaurant.create-shipping-area",
	"/:locale/cms/v2/shipping-area/check":                    "cms.v2.restaurant.create-shipping-area",

	"/:locale/cms/v2/auto-dispatch/setting/list":                    "cms.v2.auto-dispatch.setting.list",
	"/:locale/cms/v2/auto-dispatch/setting/detail":                    "cms.v2.auto-dispatch.setting.detail",
	"/:locale/cms/v2/auto-dispatch/setting/edit":                    "cms.v2.auto-dispatch.setting.edit",
	"/:locale/cms/v2/auto-dispatch/setting/change-auto-dispatch-state":                    "cms.v2.auto-dispatch.setting.change-auto-dispatch-state",
	"/:locale/cms/v2/auto-dispatch/setting/change-auto-dispatch-peak-state":                    "cms.v2.auto-dispatch.setting.change-auto-dispatch-peak-state",
	"/:locale/cms/v2/auto-dispatch/setting/change-auto-dispatch-special-order-peak-state":                    "cms.v2.auto-dispatch.setting.change-auto-dispatch-special-order-peak-state",

	//配送员保险
	//实名认证 列表
	"/:locale/cms/v2/shipper-insurance/real-name":"cms.v2.shipper-insurance.real-name.list",
	//实名认证 详情
	"/:locale/cms/v2/shipper-insurance/real-name-detail":"cms.v2.shipper-insurance.real-name.list",
	"/:locale/cms/v2/shipper-insurance/real-name-verify-text":"cms.v2.shipper-insurance.real-name.list",
	"/:locale/cms/v2/shipper-insurance/real-name-refuse-review":"cms.v2.shipper-insurance.real-name.confirm",
	"/:locale/cms/v2/shipper-insurance/real-name-verify-sub":"cms.v2.shipper-insurance.real-name.confirm",
	
	//保险列表
	"/:locale/cms/v2/shipper-insurance/list":"cms.v2.shipper-insurance.list",
	"/:locale/cms/v2/shipper-insurance/excel-export":"cms.v2.shipper-insurance.real-name.export",
	"/:locale/cms/v2/shipper-insurance/confirm":"cms.v2.shipper-insurance.confirm",
	"/:locale/cms/v2/shipper-insurance/log":"cms.v2.shipper-insurance.list",
	"/:locale/cms/v2/shipper-insurance/log-export":"cms.v2.shipper-insurance.real-name.export",
	"/:locale/cms/v2/shipper-insurance/insurance-create":"cms.v2.shipper-insurance.confirm",

	"/:locale/cms/v2/shipper-insurance/enable-force":"cms.v2.shipper-insurance.enable-force",
	"/:locale/cms/v2/shipper-insurance/upload-confirm":"cms.v2.shipper-insurance.upload-confirm",
	"/:locale/cms/v2/shipper-insurance/upload-confirm-file":"cms.v2.shipper-insurance.upload-confirm",
	


	"/:locale/cms/v2/lottery/lottery-activity/list":"cms.v2.lottery.lottery-activity.list",
	"/:locale/cms/v2/lottery/lottery-activity/create":"cms.v2.lottery.lottery-activity.create",
	"/:locale/cms/v2/lottery/lottery-activity/edit":"cms.v2.lottery.lottery-activity.edit",
	"/:locale/cms/v2/lottery/lottery-activity/detail":"cms.v2.lottery.lottery-activity.detail",
	"/:locale/cms/v2/lottery/lottery-activity/change-state":"cms.v2.lottery.lottery-activity.change-state",
	"/:locale/cms/v2/lottery/lottery-activity/delete":"cms.v2.lottery.lottery-activity.delete",


	"/:locale/cms/v2/lottery/prize/list":"cms.v2.lottery.prize.list",
	"/:locale/cms/v2/lottery/prize/create":"cms.v2.lottery.prize.create",
	"/:locale/cms/v2/lottery/prize/edit":"cms.v2.lottery.prize.edit",
	"/:locale/cms/v2/lottery/prize/detail":"cms.v2.lottery.prize.detail",
	"/:locale/cms/v2/lottery/prize/change-state":"cms.v2.lottery.prize.change-state",
	"/:locale/cms/v2/lottery/prize/delete":"cms.v2.lottery.prize.delete",

	"/:locale/cms/v2/lottery/activity-group-coupon/list":"cms.v2.lottery.activity-group-coupon.list",
	"/:locale/cms/v2/lottery/activity-group-coupon/create":"cms.v2.lottery.activity-group-coupon.create",
	"/:locale/cms/v2/lottery/activity-group-coupon/edit":"cms.v2.lottery.activity-group-coupon.edit",
	"/:locale/cms/v2/lottery/activity-group-coupon/detail":"cms.v2.lottery.activity-group-coupon.detail",
	"/:locale/cms/v2/lottery/activity-group-coupon/change-state":"cms.v2.lottery.activity-group-coupon.change-state",
	"/:locale/cms/v2/lottery/activity-group-coupon/delete":"cms.v2.lottery.activity-group-coupon.delete",

	"/:locale/cms/v2/foods-preferential/discount-list":"foods-preferential.list",// 优惠活动列表
	"/:locale/cms/v2/foods-preferential/create":"foods-preferential.create",// 创建优惠
	"/:locale/cms/v2/foods-preferential/change-state":"foods-preferential.changeState",// 修改优惠状态
	"/:locale/cms/v2/foods-preferential/edit":"foods-preferential.edit",// 编辑优惠
	"/:locale/cms/v2/foods-preferential/delete":"foods-preferential.delete",// 删除优惠
	"/:locale/cms/v2/foods-preferential/update-weight":"foods-preferential.updateWeight",// 更新优惠排序


	"/:locale/cms/v2/seckill/list":"cms.v2.seckill.list",
	"/:locale/cms/v2/seckill/change-state":"cms.v2.seckill.change-state",
	"/:locale/cms/v2/seckill/set-order":"cms.v2.seckill.set-order",
	"/:locale/cms/v2/seckill/create":"cms.v2.seckill.create",
	"/:locale/cms/v2/seckill/edit":"cms.v2.seckill.edit",
	"/:locale/cms/v2/seckill/detail":"cms.v2.seckill.detail",
	"/:locale/cms/v2/seckill/delete":"cms.v2.seckill.delete",
	"/:locale/cms/v2/seckill/log":"cms.v2.seckill.log",

	"/:locale/cms/v2/foods/list":"cms.v2.foods.list",

	"/:locale/cms/v2/area/list":"cms.v2.area.list",
	"/:locale/cms/v2/area/detail":"cms.v2.area.detail",
	"/:locale/cms/v2/area/shop-license-info":"cms.v2.area.shop-license-info",
	"/:locale/cms/v2/area/change-business-time":"cms.v2.area.change-business-time",



	"/:locale/cms/v2/seckill/set-markup-price":"cms.v2.seckill.set-markup-price",
	"/:locale/cms/v2/seckill/markup-step-info":"cms.v2.seckill.markup-step-info",

	"/:locale/cms/v2/price-markup/create":"cms.v2.price-markup.create",
	"/:locale/cms/v2/price-markup/edit":"cms.v2.price-markup.edit",
	"/:locale/cms/v2/price-markup/list":"cms.v2.price-markup.list",
	"/:locale/cms/v2/price-markup/detail":"cms.v2.price-markup.detail",
	"/:locale/cms/v2/price-markup/logs":"cms.v2.price-markup.logs",
	"/:locale/cms/v2/price-markup/logs-detail":"cms.v2.price-markup.logs-detail",
	"/:locale/cms/v2/price-markup/logs-detail-list":"cms.v2.price-markup.logs-logs-detail-list",
	"/:locale/cms/v2/price-markup/change-state":"cms.v2.price-markup.change-state",
	

	"/:locale/cms/v2/notification/foods-group/review-list":"cms.v2.notification.foods-group.review-list", // 通知->美食分组审核列表
	"/:locale/cms/v2/notification/foods-group/review":"cms.v2.notification.foods-group.review", // 通知->美食分组审核
	"/:locale/cms/v2/notification/foods-group/update-group-name":"cms.v2.notification.foods-group.update-group-name", // 通知->美食分组审核修改名称

	"/:locale/cms/v2/foods/group/restaurant-list":"cms.v2.foods.group.restaurant-list", // 餐厅列表
	"/:locale/cms/v2/foods/group/create":"cms.v2.foods.group.create", // 新增美食分组
	"/:locale/cms/v2/foods/group/edit":"cms.v2.foods.group.edit", // 修改美食分组
	"/:locale/cms/v2/foods/group/delete":"cms.v2.foods.group.delete", // 删除美食分组
	"/:locale/cms/v2/foods/group/list":"cms.v2.foods.group.list", // 美食分组列表
	"/:locale/cms/v2/foods/group/foods-list":"cms.v2.foods.group.foods-list", // 获取美食列表
	"/:locale/cms/v2/foods/group/edit-foods-group-weights":"cms.v2.foods.group.edit-foods-group-weights", // 修改美食分组顺序
	"/:locale/cms/v2/foods/group/edit-foods-groups":"cms.v2.foods.group.edit-foods-groups", // 批量修改美食的分组
	"/:locale/cms/v2/foods/group/edit-foods-state":"cms.v2.foods.group.edit-foods-state", // 批量修改美食的状态（批量）
	"/:locale/cms/v2/foods/group/edit-foods-in-group-weights":"cms.v2.foods.group.edit-foods-in-group-weights", // 修改美食在分组中的顺序
	"/:locale/cms/v2/foods/group/recommend":"cms.v2.foods.group.recommend", // 美食分组的时候推荐

	"/:locale/cms/v2/foods/category/list":"cms.v2.foods.category.list", // 美食分类列表
	"/:locale/cms/v2/foods/category/create":"cms.v2.foods.category.create", // 新增美食分类
	"/:locale/cms/v2/foods/category/detail":"cms.v2.foods.category.detail", // 美食分类详情
	"/:locale/cms/v2/foods/category/upload-image":"cms.v2.foods.category.upload-image", // 上传美食分类图片
	"/:locale/cms/v2/foods/category/edit":"cms.v2.foods.category.edit", // 编辑美食分类
	"/:locale/cms/v2/foods/category/foods-list":"cms.v2.foods.category.foods-list", // 获取分组内美食的列表

	"/:locale/cms/v2/mini-game/activity-statistics":"cms.v2.mini-game.activity-statistics", // 活动统计
	"/:locale/cms/v2/mini-game/activity-statistics-by-area":"cms.v2.mini-game.activity-statistics-by-area", // 活动统计 按区域
	"/:locale/cms/v2/mini-game/activity-list":"cms.v2.mini-game.activity-list", // 获取游戏活动列表
	"/:locale/cms/v2/mini-game-discount/use-discount-statistics":"cms.v2.mini-game-discount.use-discount-statistics", // 游戏优惠使用统计
	"/:locale/cms/v2/mini-game-discount/use-discount-statistics-by-area":"cms.v2.mini-game-discount.use-discount-statistics-by-area", // 游戏优惠使用统计
	"/:locale/cms/v2/mini-game-discount/use-discount-statistics-export":"cms.v2.mini-game-discount.use-discount-statistics-export", // 游戏优惠使用统计导出
	"/:locale/cms/v2/mini-game/list-content-from-user":"cms.v2.mini-game.list-content-from-user", // 获取用户提交的活动内容列表
	"/:locale/cms/v2/mini-game/operate-content-from-user":"cms.v2.mini-game.operate-content-from-user", // 操作用户的活动内容


	

	"/:locale/cms/v2/part-refund/refund-reason-list":"cms.v2.part-refund-reason.refund-reason-list",
	"/:locale/cms/v2/part-refund/create-refund-reason":"cms.v2.part-refund-reason.create-refund-reason",
	"/:locale/cms/v2/part-refund/update-refund-reason":"cms.v2.part-refund-reason.update-refund-reason",
	"/:locale/cms/v2/part-refund/delete-refund-reason":"cms.v2.part-refund-reason.delete-refund-reason",
	"/:locale/cms/v2/part-refund/refund-list":"cms.v2.part-refund-reason.refund-list",
	"/:locale/cms/v2/part-refund/order-info":"cms.v2.part-refund-reason.order-info",
	"/:locale/cms/v2/part-refund/refund-order-list":"cms.v2.part-refund-order.refund-order-list",
	"/:locale/cms/v2/part-refund/refund-order-detail":"cms.v2.part-refund-order.refund-order-detail",



	"/:locale/cms/v2/ranking-order-activity/create":"cms.v2.ranking-order-activity.create",
	"/:locale/cms/v2/ranking-order-activity/update":"cms.v2.ranking-order-activity.update",
	"/:locale/cms/v2/ranking-order-activity/list":"cms.v2.ranking-order-activity.list",
	"/:locale/cms/v2/ranking-order-activity/detail":"cms.v2.ranking-order-activity.detail",
	"/:locale/cms/v2/ranking-order-activity/winner-list":"cms.v2.ranking-order-activity.winner-list",
	"/:locale/cms/v2/ranking-order-activity/change-state":"cms.v2.ranking-order-activity.change-state",
	"/:locale/cms/v2/ranking-order-activity/delete":"cms.v2.ranking-order-activity.delete",
	"/:locale/cms/v2/ranking-order-activity/update-detail":"cms.v2.ranking-order-activity.update-detail",
	"/:locale/cms/v2/lottery/lottery-activity/comment":"cms.v2.ranking-order-activity.comment",
	"/:locale/cms/v2/lottery/lottery-activity/user-prize-excel":"cms.v2.ranking-order-activity.user-prize-excel",
	"/:locale/cms/v2/lottery/lottery-activity/excel-to-prize":"cms.v2.ranking-order-activity.excel-to-prize",
	
	"/:locale/cms/v2/marketing/update-state":	"market.activities.updateState",


	"/:locale/cms/v2/foods-multiple-discount/create":"cms.v2.foods-multiple-discount.create",
	"/:locale/cms/v2/foods-multiple-discount/list":"cms.v2.foods-multiple-discount.list",
	"/:locale/cms/v2/foods-multiple-discount/change-state":"cms.v2.foods-multiple-discount.change-state",
	"/:locale/cms/v2/foods-multiple-discount/detail":"cms.v2.foods-multiple-discount.detail",
	"/:locale/cms/v2/foods-multiple-discount/delete":"cms.v2.foods-multiple-discount.delete",
	"/:locale/cms/v2/foods-multiple-discount/update":"cms.v2.foods-multiple-discount.update",
	"/:locale/cms/v2/foods-multiple-discount/order-list":"cms.v2.foods-multiple-discount.order-list",
	"/:locale/cms/v2/foods-multiple-discount/list-header":"cms.v2.foods-multiple-discount.list-header",
	"/:locale/cms/v2/foods-multiple-discount/detail-view":"cms.v2.foods-multiple-discount.detail-view",

	"/:locale/cms/v2/auto-dispatch/history/list":                    "cms.v2.auto-dispatch.history.list",



	"/:locale/cms/v2/foods/import":"cms.v2.restaurant.food_import",
	"/:locale/cms/v2/foods/import-confirm":"cms.v2.restaurant.food_import",
	"/:locale/cms/v2/foods/import-result-query":"cms.v2.restaurant.food_import",

	// 套餐与规格
	"/:locale/cms/v2/restaurant/foods/quantity-list":        "cms.v2.restaurant.foods.quantity-list",
	"/:locale/cms/v2/restaurant/foods/combo-food-time":        "cms.v2.restaurant.foods.combo-food-time",
	"/:locale/cms/v2/restaurant/foods/list-for-select-table":        "cms.v2.restaurant.foods.list-for-select-table",
	"/:locale/cms/v2/restaurant/foods/food-types":        "cms.v2.restaurant.foods.food-types",
	"/:locale/cms/v2/restaurant/foods/spec":        "cms.v2.restaurant.foods.spec",
	"/:locale/cms/v2/foods/spec":        "cms.v2.restaurant.foods.spec",
	"/:locale/cms/v2/foods/:food_id/spec":        "cms.v2.restaurant.foods.spec",
	// 美食
	"/:locale/cms/v2/restaurant/foods/:id":                    "cms.v2.restaurant.foods",
	"/:locale/cms/v2/restaurant/foods":                        "cms.v2.restaurant.foods",
	


	
	"/:locale/cms/v2/advert/list":           "advert.list",
	"/:locale/cms/v2/advert/create":           "advert.create",
	"/:locale/cms/v2/advert/change-state":           "advert.changeState",
	"/:locale/cms/v2/advert/update":           "advert.edit",
	"/:locale/cms/v2/advert/delete":           "advert.delete",
	"/:locale/cms/v2/advert/ad-position":           "advert.list",
	"/:locale/cms/v2/advert/city-area-tree":           "advert.list",
	"/:locale/cms/v2/advert/canvas-template":           "advert.list",
	"/:locale/cms/v2/advert/detail":           "advert.list",


	"/:locale/cms/v2/shipper-rest-spot/list":           "cms.shipper-rest-spot.list",
	"/:locale/cms/v2/shipper-rest-spot/create":           "cms.shipper-rest-spot.create",
	"/:locale/cms/v2/shipper-rest-spot/update":           "cms.shipper-rest-spot.update",
	"/:locale/cms/v2/shipper-rest-spot/change-state":           "cms.shipper-rest-spot.changeState",
	"/:locale/cms/v2/shipper-rest-spot/delete":           "cms.shipper-rest-spot.delete",
	"/:locale/cms/v2/shipper-rest-spot/detail":           "cms.shipper-rest-spot.list",
	"/:locale/cms/v2/order-rank/list":           "cms.v2.order-rank.list",
	"/:locale/cms/v2/shipper/rank-detail":                          "cms.shipper.list",
	"/:locale/cms/v2/delivery-configs/area/area-polygon/:id": "cms.v2.delivery-configs.area.area-polygon",
	"/:locale/cms/v2/delivery-configs/area": "cms.v2.delivery-configs.area",
	"/:locale/cms/v2/delivery-configs/area/configs": "cms.v2.delivery-configs.area.configs",
	"/:locale/cms/v2/delivery-configs/area/configs/:id": "cms.v2.delivery-configs.area.configs",
	"/:locale/cms/v2/delivery-configs/area/notice": "cms.v2.delivery-configs.area.notice",
	"/:locale/cms/v2/delivery-configs/area/status": "cms.v2.delivery-configs.area.status",

	"/:locale/cms/v2/delivery-configs/restaurant": "cms.v2.delivery-configs.restaurant",
	"/:locale/cms/v2/delivery-configs/restaurant/detail": "cms.v2.delivery-configs.restaurant.detail",
	"/:locale/cms/v2/delivery-configs/restaurant/delivery-area": "cms.v2.delivery-configs.restaurant.delivery-area",
	"/:locale/cms/v2/delivery-configs/restaurant/delivery-fee": "cms.v2.delivery-configs.restaurant.delivery-fee",
	"/:locale/cms/v2/delivery-configs/restaurant/option": "cms.v2.delivery-configs.restaurant.option",
	"/:locale/cms/v2/draft/:key": "cms.v2.draft",
	"/:locale/cms/v2/draft": "cms.v2.draft",


}

// 代理和子代理白名单
var dealerBaseRouteNames = map[string]string{
	"cms.v2.seckill.markup-step-info":             "",
	"cms.v2.restaurant.foods.food-types":             "",
	"cms.v2.restaurant.foods.combo-food-time":        "",
	"cms.v2.restaurant.foods.quantity-list":          "",
	"home.index":                                     "",
	"restaurant.bank.list.select":                    "",
	"prevOrder.list.state":                           "",
	"admin.shipper.by.restaurant_id":                 "",
	"advert.advertPosition":                          "",
	"admin.change.password.index":                    "",
	"admin.change.statisticPassword.index":           "",
	"admin.change.password":                          "",
	"admin.change.statisticPassword":                 "",
	"admin.require.statisticPermission":              "",
	"admin.clear.statisticPermission":                "",
	"admin.recharge.index":                           "",
	"admin.recharge.log":                             "",
	"admin.native.recharge":                          "",
	"admin.recharge.check.pay":                       "",
	"restaurant.shipping.building.distance":          "",
	"file.upload.image":                              "",
	"file.upload.apk":                                "",
	"todayOrder.list.newOrder.count":                 "",
	"buildingType.list":                              "",
	"restaurant.foods.oneFood.byId":                  "",
	"city.list.tree":                                 "",
	"building.list.pointsByBound":                    "",
	"restaurant.shipping.building.restaurantPoints":  "",
	"building.list.pointsByBoundAndRestaurant":       "",
	"admin.user.getAdminRestaurantBuilding":          "",
	"admin.user.getPointsAdminRestaurant":            "",
	"building-restaurant.list":                       "",
	"building-restaurant.change.state":               "",
	"building-restaurant.batch.edit":                 "",
	"building-restaurant.batch.delete":               "",
	"building-restaurant.restaurantPoints":           "",
	"building-restaurant.add":                        "",
	"building-restaurant.pointsByBoundAndRestaurant": "",
	"building-restaurant.shipping.create":            "",
	"quickOrder.building.search":                     "",
	"area.changeAdminSMSPhone":                       "",
	"notification.list.index":                        "",
	"notification.list":                              "",
	"notification.changeState":                       "",
	"comment.list.index":                             "",
	"comment.list":                                   "",
	"comment.changeState":                            "",
	"comment.commentReply":                           "",
	"comment.commentReplyDelete":                     "",
	"comment.shipper.list":                           "",
	"statistics.trendency.list.index":                "",
	"statistics.trendency.data":                      "",
	"map.shipper.order":                              "",
	"restaurant.change.adminPhone":                   "",
	"todayOrder.wechat.check":                        "",
	"todayOrder.user.addr":                           "",
	"market.activities.index":                        "",
	"market.activities.create":                       "",
	"market.activities.detail":                       "",
	"market.activities.list":                         "",
	"market.activities.edit":                         "",
	"market.activities.update":                       "",
	"market.activities.updateState":                  "",
	"market.activities.delete":                       "",
	"market.statistics.index":                        "",
	"market.statistics.create":                       "",
	"market.statistics.detail":                       "",
	"market.statistics.list":                         "",
	"admin.changeAttendanceState":                    "",
	"admin.changeAttendanceSpot":                     "",
	"merchantProcess.merchantState":                  "",
	"merchantProcess.list.index":                     "",
	"merchantProcess.list":                           "",
	"rank.statistics.index":                          "",
	"rank.statistics.list":                           "",
	"quickOrder.address.update":                      "",
	"quickOrder.address.info":                        "",
	"restaurant.template.selection":                  "",
	"restaurant.edit.shipper":                        "",
	"restaurant.foods.quantity_list":                 "",
	"restaurant.edit.list":                           "",
	"market.ranking.list":                            "",
	"market.ranking.create":                          "",
	"market.ranking.index":                           "",
	"market.ranking.edit":                            "",
	"market.ranking.detail":                          "",
	"market.ranking.updateState":                     "",
	"market.ranking.delete":                          "",
	"market.ranking.user":                            "",
	"market.ranking.updatePrize":                     "",
	"market.ranking.updateBlackList":                 "",
	"market.ranking.rewardFinish":                    "",
	"admin.lakala.recharge":                          "",
	"todayOrder.lakala.check":                        "",
	"market.special.index":                           "",
	"market.special.list":                            "",
	"market.special.create":                          "",
	"market.special.detail":                          "",
	"market.special.edit":                            "",
	"market.special.details":                         "",
	"market.special.update":                          "",
	"market.special.update.state":                    "",
	"market.special.delete":                          "",
	"map.map.index":                                  "",
	"cms.shipment.income.types":                      "",

	"cms.v2.restaurant.search-by-area-id":   "",
	"cms.v2.food-statics.food-statics-list": "",
	"cms.v2.customer-statics.customer-statics-list.list":"",

	"cms.shipper.update-info":"",
	"cms.shipper.need-alert":"",
	"cms.v2.customer-statics.order-statics.list":"",
	"cms.v2.foods.list":"",
	"cms.v2.foods.group.recommend":"",
	"cms.v2.mini-game.activity-list":"",
	"cms.v2.mini-game-discount.use-discount-statistics-export":"",
	"cms.v2.part-refund-reason.order-info":"",
	"foods-preferential.updateWeight":"",


	"cms.v2.foods-multiple-discount.order-list":"",
	"cms.v2.foods-multiple-discount.list-header":"",
	"cms.v2.restaurant.foods.list-for-select-table":"",

	"cms.v2.delivery-configs.area.area-polygon":"",
	"cms.v2.draft":"",


}

// 代理扩展白名单只能允许代理访问
var dealerExtendRouteNames = map[string]string{
	"cashClear.index":                "",
	"cashClear.list":                 "",
	"cashClear.update":               "",
	"cashClear.updateAll":            "",
	"todayReport.list.index":         "",
	"todayReport.list":               "",
	"statistics.shippper.list":       "",
	"statistics.shippper.list.index": "",
	"special.shipment.index":         "",
	"special.shipment.list":          "",
	"special.shipment.delete":        "",
	"special.shipment.changeState":   "",
	"special.shipment.create":        "",
	"special.shipment.edit":          "",
	"market.consume.list":            "",
	"market.recharge.log":            "",
	"market.native.recharge":         "",
	"market.recharge.check.pay":      "",
	"market.packet.list":             "",
	"market.packet.index":            "",
	"market.packet.delete":           "",
	"market.packet.changeState":      "",
	"market.packet.create":           "",
	"market.packet.edit":             "",
	"market.packetlog.list":          "",
	"market.packetlog.index":         "",
	"market.packetlog.packets":       "",
	"finance.cashout.list":           "",
	"finance.cashout.add":            "",
	"notification.ChangeAllState":    "",
	"restaurant.changeRankState":     "",
	"area.changeRankState":           "",
	"home-score.list":                "",
	"area.changeAreaPolygon":         "",
	"cms.shipment.income.types":      "",

	"cms.v2.restaurant.search-by-area-id":   "",
	"cms.v2.food-statics.food-statics-list": "",
	"cms.v2.customer-statics.customer-statics-list.list":"",
	"cms.shipper.update-info":"",
	"cms.shipper.need-alert":"",
	"cms.v2.customer-statics.order-statics.list":"",
	"cms.v2.mini-game.activity-list":"",
	"cms.v2.mini-game-discount.use-discount-statistics-export":"",
	"cms.v2.part-refund-reason.order-info":"",
	"market.activities.updateState":                  "",

	
	
}

type CmsAuthenticate struct {
}

// Authenticate 验证用户权限
func (c *CmsAuthenticate) Authenticate() gin.HandlerFunc {
	return func(context *gin.Context) {
		var (
			l, _     = context.Get("lang_util")
			langUtil = l.(lang.LangUtil)
		)

		fullPath := tools.ToString(context.FullPath())
		method := tools.ToString(context.Request.Method)
		routeName, route_exists := permissions[fullPath]
		anyAdmin, exists := context.Get("admin")
		if !exists {
			context.JSON(401, gin.H{
				"status": 401,
				"msg":    langUtil.T("not_authorized"),
				"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
			})
			context.Abort()
			return
		}
		admin := anyAdmin.(models.Admin)
		if c.CheckUserPermission(&admin, routeName,method) {
			context.Next()
			return
		}
		if !route_exists {
			context.JSON(403, gin.H{
				"status": 403,
				"msg":    langUtil.T("forbidden"),
			})
			context.Abort()
			return
		}
		context.JSON(403, gin.H{
			"status": 403,
			"msg":    langUtil.T("forbidden"),
			"time":   carbon.Now("Asia/Shanghai").ToDateTimeString(),
		})
		context.Abort()
	}
}

// checkUserPermission 检查用户权限
func (c *CmsAuthenticate) CheckUserPermission(admin *models.Admin, currentRouteName string,method string) bool {
	// return true;
	// 超级管理员直接通过验证
	if admin.Type == models.AdminTypeOwner {
		return true
	}
	// 管理员
	if admin.Type == models.AdminTypeAdmin && c.checkDealerRouteName(currentRouteName) {
		return true
	}
	// 代理白名单
	if admin.Type == models.AdminTypeDealer && c.checkDealerRouteName(currentRouteName) {
		return true
	}
	// 子代理白名单
	if admin.Type == models.AdminTypeDealerSub && c.checkSubDealerRouteName(currentRouteName) {
		return true
	}
	// 从数据库获取权限判断
	db := tools.GetDB()
	var roles []cms.Roles
	db.Model(&admin).Association("Roles").Find(&roles)
	db.Preload("Permissions","method = ? or method is null",method).
		Joins("INNER JOIN `role_user` ON `role_user`.`role_id` = `roles`.`id`").
		Where("`role_user`.`admin_id` = ?", admin.ID).
		Find(&roles)
	return c.checkRolesPermission(roles, currentRouteName)
}

// checkSubDealerRouteName 检查子代理路由白名单
func (c *CmsAuthenticate) checkSubDealerRouteName(name string) bool {
	_, exists := dealerBaseRouteNames[name]
	return exists
}

// checkDealerRouteName 检查代理路由白名单
func (c *CmsAuthenticate) checkDealerRouteName(name string) bool {
	_, exists := dealerExtendRouteNames[name]
	return exists || c.checkSubDealerRouteName(name)
}

// checkRouteName 检查路由名
func (c *CmsAuthenticate) checkRouteName(currentRouteName string, routeName string) bool {
	if currentRouteName == routeName {
		return true
	}
	// 如路由不含星号不需要正则判断
	if strings.Contains(routeName, "*") {
		return false
	}
	routeName = regexp.QuoteMeta(routeName)
	routeName = strings.Replace(routeName, "\\*", ".*", -1)
	if match, _ := regexp.Match(routeName, []byte(currentRouteName)); match {
		return true
	}
	return false
}

// checkRolesPermission 检查角色权限
func (c *CmsAuthenticate) checkRolesPermission(roles []cms.Roles, currentRouteName string) bool {
	for _, role := range roles {
		for _, permission := range role.Permissions {
			if c.checkRouteName(currentRouteName, permission.Name) {
				return true
			}
		}
	}
	return false
}
