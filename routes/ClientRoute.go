package routes

import (
	"mulazim-api/controllers/client"
	"mulazim-api/middlewares"

	"github.com/gin-gonic/gin"
)

// 移动端API
func RegisterClientRoute(r *gin.RouterGroup) {
	clintRouteGroup := r.Group("/client/v2")
	chatGroup := clintRouteGroup.Group("/chat").Use(middlewares.ClientAuthMiddleware())
	{
		chatGroup.POST("/send", new(client.ClientChatController).PostSendMessage)
		chatGroup.GET("/detail", new(client.ClientChatController).GetChatDetail)
		chatGroup.GET("/list", new(client.ClientChatController).GetChatList)
	}
}
