package routes

import (
	"mulazim-api/constants"
	"mulazim-api/controllers"
	"mulazim-api/controllers/cms"
	"mulazim-api/controllers/cms/Marketing"
	"mulazim-api/controllers/cms/advert"
	"mulazim-api/controllers/cms/advert/material"
	"mulazim-api/controllers/cms/delivery"
	"mulazim-api/middlewares"

	"github.com/gin-gonic/gin"
)

// RegisterCmsRoute
//
//	@Description: 注册后台
//	@author: Alimjan
//	@Time: 2022-09-23 16:39:14
//	@param r *gin.RouterGroup
func RegisterCmsRoute(r *gin.RouterGroup) {
	var (
		shipperController                       = cms.ShipperController{}
		shipperIncomeTemplateController         = cms.ShipperIncomeTemplateController{}
		shipperSpecialWeatherController         = cms.ShipmentSpecialWeatherController{}
		shipperIncomeController                 = cms.ShipperIncomeController{}
		shipperSalaryController                 = cms.ShipperSalaryController{}
		shipperNotifyController                 = cms.ShipperNotifyController{}
		shipperAttendanceController             = cms.ShipperAttendanceController{}
		orderSituationController                = cms.OrderSituationController{}
		shipperRewardSettingController          = cms.ShipperRewardSettingController{}
		cmsController                           = cms.CmsController{}
		cmsChatController                       = cms.CmsChatController{}
		shipperOpinionController                = cms.ShipperOpinionController{}
		cmsAuthenticate                         = middlewares.CmsAuthenticate{}
		shipmentReduceController                = Marketing.ShipmentReduceController{}
		shipmentReduceGroupController           = Marketing.ShipmentReduceGroupController{}
		shipmentReduceStatisticsController      = Marketing.ShipmentReduceStatisticsController{}
		shipmentReduceGroupStatisticsController = Marketing.ShipmentReduceGroupStatisticsController{}
		restaurantController                    = cms.RestaurantController{}
		mapController                           = cms.MapController{}
		foodStaticsController                   = cms.FoodStaticsController{}
		customerStaticsController               = cms.CustomerStaticsController{}
		orderStaticsController               = cms.OrderStaticsController{}
		shipperUserChangeController          = cms.ShipperUserChangeController{}
		autoDispatchController               = cms.AutoDispatchController{}
		shipperInsuranceController           = cms.ShipperInsuranceController{}
		foodsPreferentialController          = cms.FoodsPreferentialController{}
		areaController                       = cms.AreaController{}
		seckillController                    = cms.SeckillController{}
		restaurantFoodsController            = controllers.RestaurantFoodsController{}
		restaurantFoodsSpecController        = controllers.RestaurantFoodsSpecController{}
		lotteryPrizeController               = cms.LotteryPrizeController{}
		lotteryActivityController            = cms.LotteryActivityController{}
		lotteryActivityGroupCouponController = cms.LotteryActivityGroupCouponController{}
		lotteryWinnerSetController           = cms.LotteryWinnerSetController{}
		foodsCategoryController              = cms.FoodsCategoryController{}
		foodsGroupController                 = cms.FoodsGroupController{}
		lotteryWinnersController             = cms.LotteryWinnersController{}
		priceMarkupController                = cms.PriceMarkupController{}
		miniGameActivityController           = cms.MiniGameActivityController{}

		partRefundController = cms.PartRefundController{}

		themeActivityController = cms.ThemeActivityController{}

		rankingOrderActivityController = cms.RankingOrderActivityController{}

		foodsMultipleDiscountController = cms.FoodsMultipleDiscountController{}
		marketingController             = Marketing.MarketingController{}


		cmsLoginController                = cms.CmsLoginController{}
		advertController  = advert.AdvertController{}
		cmsOrderRankController = cms.CmsOrderRankController{}

		shipperRestSpotController             = cms.ShipperRestSpotController{}


		areaConfigsController = new(delivery.AreaConfigsController)
		restaurantConfigsController = new(delivery.RestaurantConfigsController)
		draftController = new(controllers.DraftController)
	)
	cmsGroup := r.Group("cms/v2")
	{
		// 登录接口，不需要中间件验证
		cmsGroup.POST("login", cmsLoginController.Login)
		cmsGroup.Use(middlewares.CmsMiddleware()).GET("menu/list", cmsController.GetMenuList)
		// 配送员列表
		cmsGroup.POST("upload-image", cmsController.UploadImage)                             // 上传图片
		cmsGroup.POST("upload-advert-material-file", cmsController.UploadAdvertMaterialFile) // 上传压缩包
		commonGroup := cmsGroup.Group("common").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
		{
			commonGroup.GET("order-list", shipperIncomeController.GetOrderList)     // 订单列表
			commonGroup.GET("shipper-list", shipperIncomeController.GetShipperList) // 配送员列表
			commonGroup.GET("agent-form-list", cmsController.GetAgentForm)          // 代理意愿 列表
		}

		cmsGroup.GET("need-alert", shipperController.GetNeedAlert) //  是否需要弹窗

		// 配送员管理
		shipperGroup := cmsGroup.Group("shipper").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
		{
			shipperGroup.GET("list", shipperController.GetShipperList) // 配送员列表
			shipperGroup.GET("info", shipperController.GetShipperInfo) // 配送员信息
			// 创建配送员
			shipperGroup.POST("account-info", shipperController.PostAccountInfo)                             // 提交配送员账号信息
			shipperGroup.POST("update-account", shipperController.PostUpdateAccountInfo)                     // 更新配送员账号信息
			shipperGroup.POST("batch-update-shipper-template", shipperController.BatchUpdateShipperTemplate) // 批量更新配送员模板
			shipperGroup.POST("idcard-info", shipperController.PostIDCardInfo)                               // 提交配送员身份证信息
			shipperGroup.POST("bank-info", shipperController.PostBankInfo)                                   // 提交银行卡信息
			shipperGroup.POST("health-info", shipperController.PostHealthInfo)                               // 提交健康证信息
			shipperGroup.POST("delete", shipperController.PostDelete)                                        // 删除配送员
			shipperGroup.POST("change-state", shipperController.PostChangeState)                             // 修改状态(配送状态，现金订单状态)
			// 银行有关接口
			shipperGroup.GET("bank-areas", shipperController.GetBankAreas)   // 获取银行地区
			shipperGroup.GET("bank-list", shipperController.GetBankList)     // 获取银行列表
			shipperGroup.GET("branch-bank", shipperController.GetBranchBank) // 获取支行列表
			shipperGroup.POST("send-code", shipperController.PostSendCode)   // 发送验证码


			
			shipperGroup.GET("update-info", shipperController.GetShipperUpdateInfo)   // 获取待 更新性别和年龄
			shipperGroup.POST("update-info", shipperController.PostUpdateInfo)   // 获取更新性别和年龄
			shipperGroup.GET("rank-detail", shipperController.ShipperRankDetail) // 配送员等级排行详情

		}
		// 配送费收入有关接口
		shipmentGroup := cmsGroup.Group("shipment")
		{
			// 模板
			templateGroup := shipmentGroup.Group("template").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
			{
				templateGroup.GET("list", shipperIncomeTemplateController.GetList)       // 列表
				templateGroup.GET("detail", shipperIncomeTemplateController.GetDetail)   // 详情
				templateGroup.POST("create", shipperIncomeTemplateController.PostCreate) // 创建
				templateGroup.POST("delete", shipperIncomeTemplateController.PostDelete) // 删除
			}
			// 特殊天气
			specialWeatherGroup := shipmentGroup.Group("special-weather").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
			{
				specialWeatherGroup.GET("list", shipperSpecialWeatherController.GetList)                  // 列表
				specialWeatherGroup.GET("detail", shipperSpecialWeatherController.GetDetail)              // 详情
				specialWeatherGroup.POST("create", shipperSpecialWeatherController.PostCreate)            // 创建
				specialWeatherGroup.POST("update", shipperSpecialWeatherController.PostUpdate)            // 更新
				specialWeatherGroup.POST("change-state", shipperSpecialWeatherController.PostChangeState) // 修改状态
				specialWeatherGroup.POST("delete", shipperSpecialWeatherController.PostDelete)            // 删除
			}

			// 配送员收入记录(type类型 1：订单收入，2:奖励收入，3:打赏收入，4.特殊时间，5.特殊天气，6.好评，7.差评，8.投诉，9:迟到 10:取消订单(只做记录不扣费) 11:失败订单(只做记录不扣费))
			incomeGroup := shipmentGroup.Group("income").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
			{
				incomeGroup.GET("types", shipperIncomeController.GetIncomeTypes) // 创建
			}

			// 投诉
			complaintGroup := shipmentGroup.Group("complaint").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
			{
				complaintGroup.GET("list", shipperIncomeController.GetList)       // 列表
				complaintGroup.GET("detail", shipperIncomeController.GetDetail)   // 详情
				complaintGroup.POST("create", shipperIncomeController.PostCreate) // 创建
			}
			// 配送员工资
			salaryGroup := shipmentGroup.Group("salary").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
			{
				salaryGroup.GET("list", shipperSalaryController.GetList)                  // 列表
				salaryGroup.GET("detail", shipperSalaryController.GetDetail)              // 详情
				salaryGroup.GET("list-export", shipperSalaryController.GetListExport)     // 列表导出excel
				salaryGroup.GET("detail-export", shipperSalaryController.GetDetailExport) // 详情excel导出
			}
			// 配送员通知
			notifyGroup := shipmentGroup.Group("notify").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
			{
				notifyGroup.GET("list", shipperNotifyController.GetList)                  // 列表
				notifyGroup.POST("create", shipperNotifyController.PostCreate)            // 创建
				notifyGroup.POST("change-state", shipperNotifyController.PostChangeState) // 修改状态
				notifyGroup.POST("delete", shipperNotifyController.PostDelete)            // 删除
			}
			// 考勤管理(请假，事故，打卡)
			attendanceGroup := shipmentGroup.Group("attendance").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
			{
				attendanceGroup.GET("list-clock", shipperAttendanceController.GetListClock)       // 打卡列表
				attendanceGroup.GET("clock-detail", shipperAttendanceController.GetClockDetail)   // 打卡详情
				attendanceGroup.POST("save-clock", shipperAttendanceController.PostSaveClock)     // 后台给配送员打卡
				attendanceGroup.GET("list", shipperAttendanceController.GetList)                  // 请假/事故列表
				attendanceGroup.GET("detail", shipperAttendanceController.GetDetail)              // 请假/事故详情
				attendanceGroup.POST("create", shipperAttendanceController.PostCreate)            // 请假/事故创建
				attendanceGroup.POST("review", shipperAttendanceController.PostChangeReviewState) // 请假/事故审核
				attendanceGroup.GET("leave-types", shipperAttendanceController.GetLeaveTypes)     // 请假类型
			}
			// 上报情况
			situationGroup := shipmentGroup.Group("situation").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
			{
				situationGroup.GET("list", orderSituationController.GetList)     // 列表
				situationGroup.GET("detail", orderSituationController.GetDetail) // 详情
			}
			// 配送员奖励设置
			settingGroup := shipmentGroup.Group("reward-setting").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
			{
				settingGroup.GET("list", shipperRewardSettingController.GetList)                  // 列表
				settingGroup.GET("detail", shipperRewardSettingController.GetDetail)              // 详情
				settingGroup.POST("save", shipperRewardSettingController.PostSave)                // 列表
				settingGroup.POST("change-state", shipperRewardSettingController.PostChangeState) // 修改状态
			}

			// 故障和意见
			opinionGroup := shipmentGroup.Group("opinion").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
			{
				opinionGroup.GET("list", shipperOpinionController.GetList)     // 列表
				opinionGroup.GET("detail", shipperOpinionController.GetDetail) // 详情
			}
		}
		// cmsGroup.Use(middlewares.CmsMiddleware()).GET("cms/menu/list", cmsController.GetMenuList)
		// cmsGroup.Use(middlewares.CmsMiddleware()).GET("cms/restaurant/restaurant-by-param-for-select", cmsController.GetRestaurantList)
		// cmsGroup.Use(middlewares.CmsMiddleware()).GET("cms/admin/shipper-list-by-res-id", cmsController.GetShipperList)
		// cmsGroup.Use(middlewares.CmsMiddleware()).GET("cms/admin/shipper-list-by-res-id/:res_id", cmsController.GetShipperList)
		// cmsGroup.Use(middlewares.CmsMiddleware()).GET("cms/systemconfig/category/list-for-select-box", cmsController.GetCategoryList)

		// 配送范围/配送费阶梯管理
		configsGroup := cmsGroup.Group("delivery-configs", middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			// 区域管理
			areaGroup := configsGroup.Group("area")
			{
				areaGroup.GET("area-polygon/:id", areaConfigsController.GetDefaultPolygon)  // 区域默认服务区域范围
				areaGroup.GET("", areaConfigsController.GetAreaList)                     // 配送范围区域列表
				areaGroup.GET("/configs", areaConfigsController.GetConfigList)           // 区域配送配置列表
				areaGroup.GET("/configs/:id", areaConfigsController.GetAreaConfigDetail) // 区域配送配置详情
				areaGroup.POST("", areaConfigsController.Create)                         // 创建
				areaGroup.PUT("", areaConfigsController.Update)                          // 更新
				areaGroup.PATCH("/notice", areaConfigsController.UpdateNotice)           // 公告更新
				areaGroup.PATCH("/status", areaConfigsController.UpdateStatus)           // 状态更新
			}
			// 餐厅管理
			restaurantGroup := configsGroup.Group("restaurant")
			{
				restaurantGroup.GET("", restaurantConfigsController.GetRestaurantList)              // 配送范围餐厅列表
				restaurantGroup.GET("/detail", restaurantConfigsController.GetRestaurantDetail)        // 餐厅配送范围详情
				restaurantGroup.PUT("/delivery-area", restaurantConfigsController.SaveDeliveryArea) // 保存配送范围 - 批量
				restaurantGroup.PUT("/delivery-fee", restaurantConfigsController.SaveDeliveryFee)   // 保存配送费阶梯 - 批量
				restaurantGroup.PATCH("/option", restaurantConfigsController.UpdateOption)          // 配送范围/配送费选项更新 - 批量
			}
		}

		// 草稿通用接口
		draftGroup := cmsGroup.Group("draft", middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			// 保存草稿
			draftGroup.PUT("", func(ctx *gin.Context) {
				draftController.Save(ctx, constants.FromCmsClient)
			})
			// 获取草稿
			draftGroup.GET("/:key", func(ctx *gin.Context) {
				draftController.Get(ctx, constants.FromCmsClient)
			})
			// 删除草稿
			draftGroup.DELETE("/:key", func(ctx *gin.Context) {
				draftController.Delete(ctx, constants.FromCmsClient)
			})
		}

		// 今日订单
		todayOrderGroup := cmsGroup.Group("today-order").Use(middlewares.CmsMiddleware())
		{
			todayOrderGroup.GET("new-order-count", cmsController.GetNewOrderCount)
			todayOrderGroup.GET("list", cmsController.GetOrderList)
			todayOrderGroup.POST("grant-complete", cmsController.PostGrantComplete)
			todayOrderGroup.POST("set-shipper", cmsController.PostSetShipper)
			todayOrderGroup.GET("state-list", cmsController.GetOrderStateList)
			todayOrderGroup.GET("order-reset-reason-list-by-select-box", cmsController.GetOrderCancelReasonList)
			todayOrderGroup.GET("edit-shipper", cmsController.PostEditShipper)
			todayOrderGroup.GET("detail", cmsController.GetOrderDetail)
		}
		// 地址
		addressGroup := cmsGroup.Group("address").Use(middlewares.CmsMiddleware())
		{
			addressGroup.GET("city/list-for-select-box", cmsController.GetCityList)
			addressGroup.GET("area/list-for-select-box", cmsController.GetAreaList)
			addressGroup.GET("street/list-for-select-box", cmsController.GetStreetList)
		}
		// 聊天室
		chatGroup := cmsGroup.Group("chat").Use(cmsAuthenticate.Authenticate())
		{
			chatGroup.POST("send-msg", cmsChatController.PostSendMessage)
			chatGroup.GET("/list", cmsChatController.GetChatList)
			chatGroup.GET("/detail", cmsChatController.GetChatDetail)
		}
		marketingGroup := cmsGroup.Group("marketing").Use(cmsAuthenticate.Authenticate())
		{
			marketingGroup.GET("shipment-reduce/list", shipmentReduceController.Index)        // 减配送费活动列表
			marketingGroup.POST("shipment-reduce/create", shipmentReduceController.Create)    // 减配送费活动创建
			marketingGroup.GET("shipment-reduce/download", shipmentReduceController.Download) // 减配送费活动列表 删除
			marketingGroup.POST("shipment-reduce/delete", shipmentReduceController.Delete)    // 减配送费活动列表 下载

			marketingGroup.GET("shipment-reduce/:id/detail-for-update", shipmentReduceController.DetailForEdit) // 减配送费编辑
			marketingGroup.POST("shipment-reduce/:id/update", shipmentReduceController.Update)                  // 减配送费编辑
			marketingGroup.POST("shipment-reduce/change-state", shipmentReduceController.ChangeState)           // 更新状态
			marketingGroup.GET("shipment-reduce/:id/detail", shipmentReduceController.Detail)
			marketingGroup.GET("shipment-reduce/:id/orders", shipmentReduceController.Orders)
			marketingGroup.GET("shipment-reduce/:id/orders-download", shipmentReduceController.OrdersDownload)

			// statistics
			marketingGroup.GET("shipment-reduce/statistic/restaurants", shipmentReduceStatisticsController.Restaurant)
			// RestaurantStatistics
			marketingGroup.GET("shipment-reduce/statistic/restaurant/statis", shipmentReduceStatisticsController.RestaurantStatis)
			// Group
			marketingGroup.GET("shipment-reduce/statistic/groups", shipmentReduceGroupStatisticsController.Groups)
			// GroupStatistics
			marketingGroup.GET("shipment-reduce/statistic/group/statis", shipmentReduceGroupStatisticsController.GroupStatis)
		}
		//减配送费 团体活动
		marketingGroupGroup := cmsGroup.Group("marketing-group").Use(cmsAuthenticate.Authenticate())
		{
			// 创建
			marketingGroupGroup.POST("create", shipmentReduceGroupController.Create)
			// 详情（编辑专用）
			marketingGroupGroup.GET(":id/detail-for-update", shipmentReduceGroupController.DetailForUpdate)
			// 编辑
			marketingGroupGroup.GET(":id/detail", shipmentReduceGroupController.Detail)
			// 开启
			marketingGroupGroup.GET(":id/on", shipmentReduceGroupController.On)
			//关机
			marketingGroupGroup.GET(":id/pause", shipmentReduceGroupController.Pause)
			// 发送推送 批量
			marketingGroupGroup.POST("send-join-push", shipmentReduceGroupController.SendJoinPush)
			//  发送推送 一个
			marketingGroupGroup.POST("send-join-push-one", shipmentReduceGroupController.SendJoinPushOne)
			// 更新
			marketingGroupGroup.POST(":id/update", shipmentReduceGroupController.Update)
			//列表
			marketingGroupGroup.GET("list", shipmentReduceGroupController.Index)
			//下载
			marketingGroupGroup.GET("download", shipmentReduceGroupController.Download)
			//删除
			marketingGroupGroup.POST("delete", shipmentReduceGroupController.Delete)

			//详情 店铺列表
			marketingGroupGroup.GET("detail-restaurant", shipmentReduceGroupController.DetailRestaurant)

			marketingGroupGroup.POST("detail-restaurant-add", shipmentReduceGroupController.DetailRestaurantAdd)

			//详情 店铺列表 下载
			marketingGroupGroup.GET("detail-restaurant-download", shipmentReduceGroupController.DownloadDetailRestaurant)
			// 详情 订单列表
			marketingGroupGroup.GET("detail-order", shipmentReduceGroupController.DetailOrder)
			// 详情 订单列表 下载
			marketingGroupGroup.GET("detail-order-download", shipmentReduceGroupController.DownloadDetailOrder)

		}
		restaurantFoodsGroup := cmsGroup.Group("foods").Use(middlewares.CmsMiddleware())
		{
			// 美食列表
			restaurantFoodsGroup.GET("list", func(ctx *gin.Context) {
				restaurantFoodsController.GetList(ctx, constants.FromCmsClient)
			})
			restaurantFoodsGroup.POST("import", restaurantFoodsController.MassImport)
			restaurantFoodsGroup.POST("import-confirm", restaurantFoodsController.MassImportConfirm)
			restaurantFoodsGroup.GET("import-result-query", restaurantFoodsController.MassImportResultQuery)
		}
		restaurantGroup := cmsGroup.Group("restaurant").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
		{
			restaurantGroup.GET("search-by-area-id", restaurantController.GetByAreaId)
			// 创建餐厅美食
			restaurantGroup.POST("/foods", func(c *gin.Context) {
				new(controllers.RestaurantFoodsController).PostCreate(c, constants.FromCmsClient)
			})
			// 编辑美食
			restaurantGroup.PUT("/foods/:id", func(c *gin.Context) {
				new(controllers.RestaurantFoodsController).PostEdit(c, constants.FromCmsClient)
			})
			restaurantGroup.GET("foods/combo-food-time", restaurantFoodsController.GetComboFoodTime)
			restaurantGroup.GET("foods/quantity-list", restaurantFoodsController.GetQuantityList)
			restaurantGroup.GET("foods/food-types", restaurantFoodsController.GetFoodTypes)
			restaurantGroup.GET("foods/list-for-select-table", restaurantFoodsController.GetListForSelectTable)
			// 获取单个餐厅美食
			restaurantGroup.GET("/foods/:id", restaurantFoodsController.GetDetail)
			// 获取餐厅美食列表
			restaurantGroup.GET("/foods", func(ctx *gin.Context) {
				restaurantFoodsController.GetList(ctx, constants.FromCmsClient)
			})
			// 删除单个餐厅美食
			restaurantGroup.DELETE("/foods/:id", restaurantFoodsController.DeleteOne)

			// 美食规格
			foodsSpecGroup := cmsGroup.Group("foods").Use(cmsAuthenticate.Authenticate())
			{

				// 创建规格
				foodsSpecGroup.POST("/spec", restaurantFoodsSpecController.CreateSpec)
				// 获取规格列表
				foodsSpecGroup.GET("/:food_id/spec", restaurantFoodsSpecController.GetSpecList)
				// 更新规格
				foodsSpecGroup.PUT("/:food_id/spec", restaurantFoodsSpecController.UpdateSpec)
			}
			mapGroup := cmsGroup.Group("map").Use(cmsAuthenticate.Authenticate())
			{
				mapGroup.GET("/shipper-list", mapController.GetShipperMapList)
				mapGroup.GET("/new-received-order-list", mapController.GetNewReceivedOrderList)
			}

			shippingGroup := cmsGroup.Group("shipping-area").Use(cmsAuthenticate.Authenticate())
			{
				shippingGroup.POST("/create", restaurantController.CreateShippingArea)
				shippingGroup.GET("/check", restaurantController.CreateShippingAreaCheck)
			}

		}

		advertMaterialGroup := cmsGroup.Group("advert-material").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
		{
			var (
				categoryController                = material.CategoryController{}
				materialController                = advert.MaterialController{}
				printBatchController              = material.PrintBatchController{}
				takeHistoryController             = advert.TakeHistoryController{}
				inviteUserStatisticController     = advert.InviteUserStatisticController{}
				shipperMonthlyStatisticController = advert.ShipperMonthlyStatisticController{}
			)
			// 宣传材料分类管理
			advertMaterialGroup.POST("category/create", categoryController.Create)
			advertMaterialGroup.GET("category/list", categoryController.List)
			advertMaterialGroup.PUT("category/:id/update", categoryController.Update)
			advertMaterialGroup.PUT("category/:id/update-state", categoryController.UpdateState)
			advertMaterialGroup.GET("category/:id/detail", categoryController.Detail)
			advertMaterialGroup.DELETE("category/:id/delete", categoryController.Delete)

			// 宣传材料管理
			advertMaterialGroup.POST("create", materialController.Create)
			advertMaterialGroup.GET("list", materialController.List)
			advertMaterialGroup.PUT(":materialId/update", materialController.Update)
			advertMaterialGroup.GET(":materialId/update-state", materialController.UpdateState)
			advertMaterialGroup.GET(":materialId/detail", materialController.Detail)
			advertMaterialGroup.DELETE(":materialId/delete", materialController.Delete)

			// 宣传材料打印批次
			advertMaterialGroup.POST(":materialId/print-batch/create", printBatchController.Create)
			advertMaterialGroup.GET(":materialId/print-batch/list", printBatchController.List)
			advertMaterialGroup.GET("/print-batch/:batchId/download", printBatchController.Download)

			// 宣传材料领取记录
			advertMaterialGroup.GET("take-history/statistic", takeHistoryController.Statistic)
			advertMaterialGroup.GET("take-history/list", takeHistoryController.List)
			advertMaterialGroup.GET("take-history/:takeId/detail", takeHistoryController.Detail)
			advertMaterialGroup.GET("take-history/:takeId/detail/list", takeHistoryController.TakeDetailList)

			// 宣传材料邀请用户统计
			advertMaterialGroup.GET("invite-user-statistic/by-shipper", inviteUserStatisticController.ByShipper)                  // 邀请用户统计(代理商根据区域内的配送员)
			advertMaterialGroup.GET("invite-user-statistic/by-shipper-download", inviteUserStatisticController.ByShipperDownload) // 邀请用户统计(代理商根据区域内的配送员)
			advertMaterialGroup.GET("invite-user-statistic/by-area", inviteUserStatisticController.ByArea)
			advertMaterialGroup.GET("invite-user-statistic/by-area-download", inviteUserStatisticController.ByAreaDownload)
			advertMaterialGroup.GET("shipper-monthly-statistic/:shipperId/head", shipperMonthlyStatisticController.Head)
			advertMaterialGroup.GET("shipper-monthly-statistic/:shipperId/list", shipperMonthlyStatisticController.List)
		}

		foodStaticsGroup := cmsGroup.Group("food-statics").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			foodStaticsGroup.GET("food-statics", foodStaticsController.GetFoodStatics)
			foodStaticsGroup.GET("restaurant-statics", foodStaticsController.GetRestaurantStatics)
			foodStaticsGroup.GET("restaurant-statics-export", foodStaticsController.GetRestaurantStaticsExport) // 详情excel导出
		}

		customerStaticsGroup := cmsGroup.Group("customer-statics").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			customerStaticsGroup.GET("customer-statics", customerStaticsController.GetCustomerStatics)
			customerStaticsGroup.GET("customer-store-statics", customerStaticsController.GetCustomerStaticsByStore)
			customerStaticsGroup.GET("customer-food-statics", customerStaticsController.GetCustomerStaticsByFood)
			customerStaticsGroup.GET("new-customer-statics", customerStaticsController.GetNewCustomerStatics)
		}

		orderStaticsGroup := cmsGroup.Group("order-statics").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			orderStaticsGroup.GET("today", orderStaticsController.GetTodayOrderStatics)
		}
		//配送员个人二维码替换关系
		shipperUserChangeGroup := cmsGroup.Group("shipper-user-change").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			shipperUserChangeGroup.GET("list", shipperUserChangeController.GetList)
			shipperUserChangeGroup.POST("create", shipperUserChangeController.Create)
			shipperUserChangeGroup.POST("delete", shipperUserChangeController.Delete)
		}
		//配送员个人二维码替换关系
		autoDispatchGroup := cmsGroup.Group("auto-dispatch")
		{
			autoDispatchSettingGroup := autoDispatchGroup.Group("setting").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
			{
				// 智能派单配送列表
				autoDispatchSettingGroup.GET("list", autoDispatchController.GetList)
				// 智能派单配置详情
				autoDispatchSettingGroup.GET("detail", autoDispatchController.GetDetail)
				// 智能派单配置编辑
				autoDispatchSettingGroup.POST("edit", autoDispatchController.PostEdit)
				// 智能派单修改状态
				autoDispatchSettingGroup.POST("change-auto-dispatch-state", autoDispatchController.PostChangeAutoDispatchState)
				// 智能派单高峰期状态修改
				autoDispatchSettingGroup.POST("change-auto-dispatch-peak-state", autoDispatchController.PostChangeAutoDispatchPeakState)
				// 智能派单特价活动订单均匀分配状态修改
				autoDispatchSettingGroup.POST("change-auto-dispatch-special-order-peak-state", autoDispatchController.PostChangeAutoDispatchSpecialOrderPeakState)
			}

			//智能派单派单记录
			autoDispatchHIstoryGroup := autoDispatchGroup.Group("history").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
			{
				// 智能派单记录列表
				autoDispatchHIstoryGroup.GET("list", autoDispatchController.GetHistoryList)
			}
		}
		shipperInsuranceGroup := cmsGroup.Group("shipper-insurance").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			//实名认证信息
			shipperInsuranceGroup.GET("real-name", shipperInsuranceController.RealName)

			shipperInsuranceGroup.GET("real-name-detail", shipperInsuranceController.RealNameDetail)

			shipperInsuranceGroup.GET("real-name-verify-text", shipperInsuranceController.GetVerifyText)   // 推荐关键字
			shipperInsuranceGroup.POST("real-name-refuse-review", shipperInsuranceController.RefuseReview) // 审核拒绝
			shipperInsuranceGroup.POST("real-name-verify-sub", shipperInsuranceController.ApprovedReview)  // 审核通过

			shipperInsuranceGroup.GET("list", shipperInsuranceController.List)
			shipperInsuranceGroup.GET("excel-export", shipperInsuranceController.GetExcelExport)
			shipperInsuranceGroup.POST("confirm", shipperInsuranceController.PostConfirm)
			shipperInsuranceGroup.GET("log", shipperInsuranceController.Log)
			shipperInsuranceGroup.GET("log-export", shipperInsuranceController.LogExport)
			shipperInsuranceGroup.GET("insurance-create", shipperInsuranceController.CreateShipperInsurance)
			shipperInsuranceGroup.POST("enable-force", shipperInsuranceController.PostInsuranceEnableManually)
			shipperInsuranceGroup.POST("upload-confirm-file", shipperInsuranceController.PostUploadConfirmFile)
			shipperInsuranceGroup.POST("upload-confirm", shipperInsuranceController.PostUploadConfirmConfirm)

		}

		// 抽奖活动
		lotteryGroup := cmsGroup.Group("lottery")
		{
			// 奖品管理
			prizeGroup := lotteryGroup.Group("prize").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
			{
				// 奖品列表
				prizeGroup.GET("list", lotteryPrizeController.GetList)
				// 奖品详情
				prizeGroup.GET("detail", lotteryPrizeController.GetDetail)
				// 奖品创建
				prizeGroup.POST("create", lotteryPrizeController.PostCreate)
				// 奖品编辑
				prizeGroup.POST("edit", lotteryPrizeController.PostEdit)
				// 奖品删除
				prizeGroup.POST("delete", lotteryPrizeController.PostDelete)
				// 奖品状态更改
				prizeGroup.POST("change-state", lotteryPrizeController.PostChangeState)
			}
			// 优惠券管理
			activityGroupCouponGroup := lotteryGroup.Group("activity-group-coupon").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
			{
				// 创建优惠券
				activityGroupCouponGroup.POST("create", lotteryActivityGroupCouponController.Create)
				// 修改优惠券
				activityGroupCouponGroup.POST("update", lotteryActivityGroupCouponController.Update)
				// 删除优惠券
				activityGroupCouponGroup.POST("delete", lotteryActivityGroupCouponController.Delete)
				// 优惠券列表
				activityGroupCouponGroup.GET("list", lotteryActivityGroupCouponController.List)
				// 优惠卷详情
				activityGroupCouponGroup.GET("detail", lotteryActivityGroupCouponController.Detail)

			}
			//  抽奖活动管理
			lotteryActivity := lotteryGroup.Group("lottery-activity").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
			{
				// 奖品列表
				lotteryActivity.GET("list", lotteryActivityController.GetList)
				// 奖品列表
				lotteryActivity.POST("create", lotteryActivityController.PostCreate)
				// 奖品列表
				lotteryActivity.POST("edit", lotteryActivityController.PostEdit)
				// 奖品列表
				lotteryActivity.GET("detail", lotteryActivityController.GetDetail)
				// 奖品列表
				lotteryActivity.POST("change-state", lotteryActivityController.PostChangeState)
				// 活动删除
				lotteryActivity.POST("delete", lotteryActivityController.PostDelete)
				// 获取活动评价
				lotteryActivity.GET("comment", lotteryActivityController.GetComments)
				// 导入 Excel 获取活动中奖配置数据
				lotteryActivity.POST("excel-to-prize", lotteryActivityController.PostExcelToPrize)
				// 导出活动中奖用户信息 Excel
				lotteryActivity.GET("user-prize-excel", lotteryActivityController.UserPrizeExcel)
			}

			//  抽奖设置
			lotteryWinnerSetActivity := lotteryGroup.Group("winner-set").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
			{
				// 获取当前活动信息
				lotteryWinnerSetActivity.GET("current-info", lotteryWinnerSetController.GetCurrentInfo)
				lotteryWinnerSetActivity.GET("list", lotteryWinnerSetController.GetList)
				lotteryWinnerSetActivity.POST("set", lotteryWinnerSetController.PostSet)
			}
			lotteryWinners := lotteryGroup.Group("winners").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
			{
				lotteryWinners.GET("list", lotteryWinnersController.GetList)
				lotteryWinners.GET("detail", lotteryWinnersController.GetDetail)
				lotteryWinners.GET("list-export", lotteryWinnersController.GetListExport)
			}

			lotteryOrder := lotteryGroup.Group("orders").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
			{
				lotteryOrder.GET("list", lotteryWinnersController.GetOrders)
				lotteryOrder.GET("detail", lotteryWinnersController.GetOrderDetail)
				lotteryOrder.POST("refund", lotteryWinnersController.PostRefund)
				lotteryOrder.GET("chance-list", lotteryWinnersController.GetChanceList)
				lotteryOrder.GET("chance-list-by-area", lotteryWinnersController.GetChanceListByArea)

			}
		}

		// 秒杀
		seckillGroup := cmsGroup.Group("seckill").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			// 列表
			seckillGroup.GET("list", seckillController.GetList)
			// 状态修改
			seckillGroup.POST("change-state", seckillController.PostChangeState)
			// 设置排序
			seckillGroup.POST("set-order", seckillController.PostSetOrder)
			// 创建活动
			seckillGroup.POST("create", seckillController.PostCreate)
			// 编辑活动
			seckillGroup.POST("edit", seckillController.PostEdit)
			// 详情
			seckillGroup.GET("detail", seckillController.GetDetail)
			// 删除
			seckillGroup.POST("delete", seckillController.PostDelete)
			// 秒杀日志
			seckillGroup.GET("log", seckillController.GetSeckillLog)
			// 加价美食设置价格
			seckillGroup.POST("set-markup-price", seckillController.PostSetMarkupPrice)
			seckillGroup.GET("markup-step-info", seckillController.GetMarkupStepInfo)

		}

		// 优惠管理
		foodsPreferentialGroup := cmsGroup.Group("foods-preferential").Use(middlewares.CmsMiddleware())
		{
			// 优惠活动类型列表
			foodsPreferentialGroup.GET("preferential-type", foodsPreferentialController.PreferentialTypeList)
			// 判断是否有优惠
			foodsPreferentialGroup.GET("has-preferential", foodsPreferentialController.HasPreferential)
			// 优惠活动列表
			foodsPreferentialGroup.GET("discount-list", foodsPreferentialController.DiscountList)
			// 创建优惠
			foodsPreferentialGroup.POST("create", foodsPreferentialController.CreatePreferential)
			// 修改优惠状态
			foodsPreferentialGroup.POST("change-state", foodsPreferentialController.ChangePreferentialState)
			// 更新优惠排序
			foodsPreferentialGroup.POST("update-weight", foodsPreferentialController.UpdateWeight)
			// 编辑优惠
			foodsPreferentialGroup.POST("edit", foodsPreferentialController.EditPreferential)
			// 删除优惠
			foodsPreferentialGroup.POST("delete", foodsPreferentialController.DeletePreferential)
		}

		// 美食管理
		foodsRouterGroup := cmsGroup.Group("foods")
		{
			// 美食分组相关接口
			foodsGroup := foodsRouterGroup.Group("group").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
			{
				// 餐厅列表
				foodsGroup.GET("restaurant-list", foodsGroupController.RestaurantList)
				// 新增美食分组
				foodsGroup.POST("create", foodsGroupController.Create)
				// 修改美食分组
				foodsGroup.POST("edit", foodsGroupController.Edit)
				// 删除美食分组
				foodsGroup.POST("delete", foodsGroupController.Delete)
				// 美食分组列表
				foodsGroup.GET("list", foodsGroupController.List)
				// 根据条件获取美食列表
				foodsGroup.GET("foods-list", foodsGroupController.FoodsList)
				// 修改美食分组顺序
				foodsGroup.POST("edit-foods-group-weights", foodsGroupController.EditFoodsGroupWeights)
				// 修改美食在分组中的顺序
				foodsGroup.POST("edit-foods-in-group-weights", foodsGroupController.EditFoodsInGroupWeights)
				// 批量修改美食的分组
				foodsGroup.POST("edit-foods-groups", foodsGroupController.EditFoodsGroups)
				// 批量修改美食的状态
				foodsGroup.POST("edit-foods-state", foodsGroupController.EditFoodsState)
				// 推荐分组
				foodsGroup.GET("recommend", foodsGroupController.GetRecommendGroup)
			}
			// 美食分类相关接口
			foodsCategory := foodsRouterGroup.Group("category").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
			{
				// 美食分类列表
				foodsCategory.GET("list", foodsCategoryController.List)
				// 新增美食分类
				foodsCategory.POST("create", foodsCategoryController.Create)
				// 美食分类详情
				foodsCategory.GET("detail", foodsCategoryController.Detail)
				// 上传美食分类图片
				foodsCategory.POST("upload-image", foodsCategoryController.UploadImage)
				// 编辑美食分类
				foodsCategory.POST("edit", foodsCategoryController.Edit)
				// 获取分组内美食的列表
				foodsCategory.GET("foods-list", foodsCategoryController.FoodsList)

			}
		}

		// 通知路由
		notificationGroup := cmsGroup.Group("notification")
		{
			// 美食分组通知
			foodsGroupNotificationRouter := notificationGroup.Group("foods-group").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
			{
				// 审核美食分组审核列表
				foodsGroupNotificationRouter.GET("review-list", foodsGroupController.ReviewList)
				// 审核美食分组
				foodsGroupNotificationRouter.POST("review", foodsGroupController.Review)
				// 修改美食分组名称
				foodsGroupNotificationRouter.POST("update-group-name", foodsGroupController.UpdateGroupName)
			}
		}
		// 代理区域管理
		areaGroup := cmsGroup.Group("area").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			// 区域列表
			areaGroup.GET("list", areaController.GetList)
			areaGroup.GET("detail", areaController.GetDetail)
			areaGroup.POST("shop-license-info", areaController.PostShopLicenseInfo)
			areaGroup.POST("change-business-time", areaController.PostChangeBusinessTime)
		}

		// 加价美食库管理
		priceMarkupGroup := cmsGroup.Group("price-markup").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			// 创建加价
			priceMarkupGroup.POST("create", priceMarkupController.PostCreate)
			// 编辑加价
			priceMarkupGroup.POST("edit", priceMarkupController.PostEdit)
			// 加价列表
			priceMarkupGroup.GET("list", priceMarkupController.GetList)
			// 加价详情
			priceMarkupGroup.GET("detail", priceMarkupController.GetDetail)
			// 修改状态
			priceMarkupGroup.POST("change-state", priceMarkupController.PostChangeState)
			// 明细列表（秒杀，特价，优惠）
			priceMarkupGroup.GET("logs", priceMarkupController.GetLogs)
			// 明细列表详情（秒杀，特价，优惠）
			priceMarkupGroup.GET("logs-detail", priceMarkupController.GetLogsDetail)
			priceMarkupGroup.GET("logs-detail-list", priceMarkupController.GetLogsDetailList)
		}
		// 游戏活动
		miniGameGroup := cmsGroup.Group("mini-game").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			// 获取游戏活动列表
			miniGameGroup.GET("activity-list", miniGameActivityController.ActivityList)
			// 切换游戏活动状态
			miniGameGroup.POST("toggle-activity-state", miniGameActivityController.ToggleActivityState)
			// 创建新的游戏活动
			miniGameGroup.POST("create-activity", miniGameActivityController.CreateActivity)
			// 修改游戏活动
			miniGameGroup.POST("update-activity", miniGameActivityController.UpdateActivity)
			// 删除游戏活动
			miniGameGroup.POST("delete-activity", miniGameActivityController.DeleteActivity)
			// 活动详细
			miniGameGroup.GET("activity-detail", miniGameActivityController.ActivityDetail)
			// 活动统计
			miniGameGroup.GET("activity-statistics", miniGameActivityController.ActivityStatistics)
			// 活动统计 按区域
			miniGameGroup.GET("activity-statistics-by-area", miniGameActivityController.ActivityStatisticsByArea)
			// 创建游戏
			miniGameGroup.POST("create-mini-game", miniGameActivityController.CreateMiniGame)
			// 修改游戏
			miniGameGroup.POST("update-mini-game", miniGameActivityController.UpdateMiniGame)
			// 删除游戏
			miniGameGroup.POST("delete-mini-game", miniGameActivityController.DeleteMiniGame)
			// 游戏列表
			miniGameGroup.GET("list-mini-game", miniGameActivityController.ListMiniGame)
			// 获取用户提交的活动内容列表
			miniGameGroup.GET("list-content-from-user", miniGameActivityController.ListContentFromUser)
			// 操作用户的活动内容
			miniGameGroup.POST("operate-content-from-user", miniGameActivityController.OperateContentFromUser)

		}
		miniGameDiscountGroup := cmsGroup.Group("mini-game-discount").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			// 游戏优惠使用统计
			miniGameDiscountGroup.GET("use-discount-statistics", miniGameActivityController.UseDiscountStatistics)
			// 游戏优惠使用统计Excel导出
			miniGameDiscountGroup.GET("use-discount-statistics-export", miniGameActivityController.UseDiscountStatisticsExport)
			// 游戏优惠使用统计 按区域
			miniGameDiscountGroup.GET("use-discount-statistics-by-area", miniGameActivityController.UseDiscountStatisticsByArea)
		}

		partRefundGroup := cmsGroup.Group("part-refund").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			// 退款原因列表
			partRefundGroup.GET("refund-reason-list", partRefundController.RefundReasonList)
			// 添加退款原因
			partRefundGroup.POST("create-refund-reason", partRefundController.CreateRefundReason)
			// 修改退款原因
			partRefundGroup.POST("update-refund-reason", partRefundController.UpdateRefundReason)
			// 删除退款原因
			partRefundGroup.POST("delete-refund-reason", partRefundController.DeleteRefundReason)

			// 退款页面数据
			partRefundGroup.GET("order-info", partRefundController.PartRefundInfo)
			// 退款列表
			partRefundGroup.GET("refund-list", partRefundController.RefundList)

			// 退款订单列表
			partRefundGroup.GET("refund-order-list", partRefundController.GetPartRefundOrderList)
			// 订单退款详情
			partRefundGroup.GET("refund-order-detail", partRefundController.GetPartRefundOrderDetail)
		}

		// 主题活动
		themeActivityGroup := cmsGroup.Group("theme-activities").Use(middlewares.CmsMiddleware(), cmsAuthenticate.Authenticate())
		{
			// 获取活动列表
			themeActivityGroup.GET("", themeActivityController.List)
			// 创建活动
			themeActivityGroup.POST("", themeActivityController.Create)
			// 更新活动
			themeActivityGroup.PUT("/:id", themeActivityController.Update)
			// 删除活动
			themeActivityGroup.DELETE("/:id", themeActivityController.Delete)
			// 修改活动状态
			themeActivityGroup.PATCH("/:id/state", themeActivityController.ChangeState)
			// 获取活动详情
			themeActivityGroup.GET("/:id", themeActivityController.Detail)

		}
		// 排行榜活动
		rankingOrderActivityGroup := cmsGroup.Group("ranking-order-activity").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
		{
			// 排行订单活动创建
			rankingOrderActivityGroup.POST("/create", rankingOrderActivityController.CreateRankingOrderActivity)
			// 排行订单活动修改
			rankingOrderActivityGroup.POST("/update", rankingOrderActivityController.UpdateRankingOrderActivity)
			// 排行订单活动列表
			rankingOrderActivityGroup.GET("/list", rankingOrderActivityController.ListRankingOrderActivity)
			// 排行订单活动详情
			rankingOrderActivityGroup.GET("/detail", rankingOrderActivityController.DetailRankingOrderActivity)
			// 活动中奖列表
			rankingOrderActivityGroup.GET("/winner-list", rankingOrderActivityController.WinnerList)
			// 活动修改状态
			rankingOrderActivityGroup.POST("/change-state", rankingOrderActivityController.ChangeState)
			// 删除活动
			rankingOrderActivityGroup.POST("/delete", rankingOrderActivityController.DeleteRankingOrderActivity)
			// 修改的详细
			rankingOrderActivityGroup.GET("/update-detail", rankingOrderActivityController.UpdateDetail)
		}

		// 多买打折活动
		foodsMultipleDiscountGroup := cmsGroup.Group("foods-multiple-discount").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
		{
			// 创建多买打折活动
			foodsMultipleDiscountGroup.POST("create", foodsMultipleDiscountController.Create)
			// 获取多买打折活动列表
			foodsMultipleDiscountGroup.GET("list", foodsMultipleDiscountController.List)
			// 修改多买打折活动状态
			foodsMultipleDiscountGroup.POST("change-state", foodsMultipleDiscountController.ChangeState)
			// 获取多买打折活动详情(编辑用)
			foodsMultipleDiscountGroup.GET("detail", foodsMultipleDiscountController.Detail)
			// 获取多买打折活动详情(查看详情)
			foodsMultipleDiscountGroup.GET("detail-view", foodsMultipleDiscountController.DetailView)
			foodsMultipleDiscountGroup.GET("list-header", foodsMultipleDiscountController.ListHeader)
			foodsMultipleDiscountGroup.GET("order-list", foodsMultipleDiscountController.OrderList)
			// 删除多买打折活动
			foodsMultipleDiscountGroup.POST("delete", foodsMultipleDiscountController.Delete)
			// 修改多买打折活动
			foodsMultipleDiscountGroup.POST("update", foodsMultipleDiscountController.Update)

		}

		//满减
		marketing := cmsGroup.Group("marketing").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
		{
			//满减状态修改
			marketing.POST("update-state", marketingController.UpdateState)

		}

		// 广告
		advertRoute := cmsGroup.Group("advert").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
		{
			// 广告列表
			advertRoute.GET("list", advertController.List)
			// 新增广告
			advertRoute.POST("create", advertController.Create)
			// 修改广告
			advertRoute.POST("change-state", advertController.ChangeState)
			// 修改广告
			advertRoute.POST("update", advertController.Update)
			// 删除广告
			advertRoute.POST("delete", advertController.Delete)
			// 广告位置
			advertRoute.GET("ad-position", advertController.AdvertPosition)
			// 获取城区树结构
			advertRoute.GET("city-area-tree", advertController.CityAreaTree)
			// 获取可自动生成的模板
			advertRoute.GET("canvas-template", advertController.GetCanvasTemplate)
			// 获取广告详情
			advertRoute.GET("detail", advertController.Detail)
		}

		//配送员休息区域
		shipperResSpotGroup := cmsGroup.Group("shipper-rest-spot").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
		{
			shipperResSpotGroup.GET("list", shipperRestSpotController.GetList)
			shipperResSpotGroup.POST("create", shipperRestSpotController.PostCreate)
			shipperResSpotGroup.POST("update", shipperRestSpotController.PostUpdate)
			shipperResSpotGroup.POST("change-state", shipperRestSpotController.PostChangeState)
			shipperResSpotGroup.POST("delete", shipperRestSpotController.PostDelete)
			shipperResSpotGroup.GET("detail", shipperRestSpotController.GetDetail)
		}

		orderRankRoute := cmsGroup.Group("order-rank").Use(middlewares.CmsMiddleware()).Use(cmsAuthenticate.Authenticate())
		{
			orderRankRoute.GET("list", cmsOrderRankController.GetOrderRankStat)
		}
	}

}
