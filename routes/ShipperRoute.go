package routes

import (
	"mulazim-api/controllers"
	"mulazim-api/controllers/merchant"
	"mulazim-api/controllers/shipper"
	"mulazim-api/controllers/shipper/order"
	shipperV2 "mulazim-api/controllers/shipperv2"
	"mulazim-api/middlewares"

	"github.com/gin-gonic/gin"
)

// RegisterShipperRoute
//
//	@Description:  注册所有配送员路由
//	@author: Alimjan
//	@Time: 2022-09-23 16:30:07
//	@param r *gin.RouterGroup
func RegisterShipperRoute(r *gin.RouterGroup) {

	var (
		
		cashController      = shipper.CashController{}
		terminalController  = controllers.TerminalController{}
		shipmentController  = shipper.ShipmentController{}
		reportController    = order.ReportController{}
		shipperV2Controller = shipperV2.ShipperController{}
		pushController      = shipperV2.PushController{}
		materialController  = shipperV2.MaterialController{}
		shipperRestSpotController  = shipperV2.ShipperRestSpotController{}
	)

	r.GET("/check-server", shipperV2Controller.CheckServer)
	// 发送图片（通用接口）
	r.POST("/upload/image", shipperV2Controller.PostUploadImage)
	
	//新配送端的路由 v2 开头
	shipperV2Group := r.Group("/shipper/v2")
	{
		//配送端微信支付异步通知接口（native支付和app支付通用）
		shipperV2Group.GET("/v3-pay-notify", cashController.Notify)
		shipperV2Group.POST("/v3-pay-notify", cashController.Notify)

		//  客户端升级
		shipperV2Group.GET("/terminal/show", terminalController.GetShow)

		//  配送员登录
		shipperV2Group.POST("/oauth/login", shipperV2Controller.Login)
		// 发送短信验证码
		shipperV2Group.POST("/send-sms", shipperV2Controller.PostSmsCode)
		// 手机验证码登录
		shipperV2Group.POST("/oauth/cms-login", shipperV2Controller.PostSMSLogin)
		// 验证手机验证码
		shipperV2Group.POST("/oauth/verify-sms", shipperV2Controller.VerifySMSCode)
		// 找回忘记密码
		shipperV2Group.POST("/oauth/recover-pass", shipperV2Controller.RecoverPassword)
		//  配送员排行榜
		shipperV2Group.GET("/rank/list", shipperV2Controller.RankList)

		// 智能派单订单分配配送员接口
		shipperV2Group.POST("auto-dispatch-set-shipper", new(shipperV2.ShipperController).AutoDispatchSetShipper)

		shipperAdmin := shipperV2Group.Group("/admin")
		shipperAdmin.Use(middlewares.AuthMiddleware())
		{
			// 获取AppConfig的值，发送keys ，逗号区分
			shipperAdmin.GET("get-config", shipperV2Controller.GetConfig) //获取AppConfig的值
			//获取配送员新订单列表
			//  已发布
			shipperAdmin.GET("/order-list", shipperV2Controller.ShipperNewOrderList)
			//  我的订单
			shipperAdmin.GET("/my-order-list", shipperV2Controller.ShipperMyOrderList)
			//  完成订单列表
			shipperAdmin.GET("/finished-order", shipperV2Controller.ShipperFinishedOrder)
			//  失败订单
			shipperAdmin.GET("/fail-order", shipperV2Controller.ShipperFailOrder)

			//配送员发展新客户统计
			shipperAdmin.GET("/recommend-list", shipperV2Controller.RecommendList)
			//获取小程序推荐二维码(配送员发展新客户)
			shipperAdmin.GET("/qr-recommend", shipperV2Controller.GetQrRecommend)
			// 配送员进入小区扫二维码
			shipperAdmin.POST("/scan-community-code", shipperV2Controller.PostScanCommunityCode)

			//配送员绑定微信号
			// 已发布
			shipperAdmin.POST("/rebind-wechat", shipperV2Controller.PostRebindWechat)

			//配送员修改密码
			shipperAdmin.POST("/change-password", shipperV2Controller.PostChangePassword)

			//获取派送员最大能抢的订单数量
			//  已发布
			shipperAdmin.GET("/grab-order-count", shipperV2Controller.GetGrabOrderCount)

			//配送员抢单
			shipperAdmin.POST("/take-order", shipperV2Controller.PostTakeOrder)

			//配送员抢单 新版
			shipperAdmin.POST("/grab-order", shipperV2Controller.PostGrabOrder)

			//按街道获取订单列表
			shipperAdmin.GET("/order-list-by-street", shipperV2Controller.GetOrderListByStreet)

			//订单详细
			shipperAdmin.GET("/order-detail", shipperV2Controller.OrderDetail)

			shipperAdmin.GET("/customer-address", shipperV2Controller.CustomerAddress)

			//配送员统计
			shipperAdmin.GET("/statistics", shipperV2Controller.GetStatistics)
			//配送员更改订单状态
			shipperAdmin.POST("/change-order-state", shipperV2Controller.PostChangeOrderState)

			//配送员退单
			shipperAdmin.POST("/back-order", shipperV2Controller.PostBackOrder)
			//订单详细统计
			shipperAdmin.GET("/statistic-detail", shipperV2Controller.GetStatisticDetail)

			//显示配送员订单列表
			shipperAdmin.GET("/admin-order-list", shipperV2Controller.GetAdminOrderList)

			//更新商家阅读评论时间
			shipperAdmin.POST("/close-account", shipperV2Controller.CloseAccount)

			shipperAdmin.GET("/update-profile", shipperV2Controller.GetUpdateProfile)
			shipperAdmin.POST("/update-profile", shipperV2Controller.PostUpdateProfile)

			shipperAdmin.GET("/check-out", shipperV2Controller.GetCheckOut)

			//获取考勤信息
			shipperAdmin.GET("/attendance-info", shipperV2Controller.GetAttendanceInfo)
			//配送员打卡
			shipperAdmin.POST("/attendance", shipperV2Controller.PostAttendance)
			//配送员打卡记录
			shipperAdmin.GET("/attendance-list", shipperV2Controller.GetAttendanceList)
			// 配送员取消的订单历史
			shipperAdmin.GET("/canceled-order", shipperV2Controller.GetCanceledOrder)
			// 获取超时的订单列表
			shipperAdmin.GET("/overtime-orders", shipperV2Controller.GetOverTimeOrders)

			shipperAdmin.POST("order/:order_id/reports", reportController.Store)
			shipperAdmin.GET("order/reports/types", reportController.GetReportTypes)
			shipperAdmin.POST("/order/arrived_shop", shipperV2Controller.ShipperArrivedShop)
			// 首页通知消息
			shipperAdmin.GET("notify", shipperV2Controller.GetNotify)
			// 发送图片（通用接口）
			shipperAdmin.POST("upload/image", shipperV2Controller.PostUploadImage)
			shipperAdmin.GET("/list", shipperV2Controller.GetAdminList)
			// 获取配送员管理员手下的配送员列表
			shipperAdmin.GET("/all-shipper-list", shipperV2Controller.GetAllShipperList)
			// 推送设备注册
			shipperAdmin.POST("push/register", pushController.Register)
			// 检查应用黑名单
			shipperAdmin.POST("/black-list", shipperV2Controller.CheckAppBlackList)
			// 文字转换维吾尔语音频
			shipperAdmin.POST("/tts", shipperV2Controller.TextToSpeech)
			// 获取所有黑名单中的app
			shipperAdmin.GET("/black-list-apps", shipperV2Controller.GetBlackListApps)

			
			// 申请修改配送时间
			shipperAdmin.GET("/modify-booking-time", shipperV2Controller.GetApplyModifyBookingTime)
			shipperAdmin.POST("/modify-booking-time", shipperV2Controller.PostApplyModifyBookingTime)


		}
		// 推送设备注销
		shipperV2Group.POST("admin/push/unregister", pushController.UnRegister)
		// 帮助
		shipperHelp := shipperV2Group.Group("/help")
		shipperHelp.Use(middlewares.AuthMiddleware())
		{
			// 帮助列表
			shipperHelp.GET("/list", shipperV2Controller.GetHelpList)
			// 帮助详情
			shipperHelp.GET("/detail", shipperV2Controller.GetHelpDetail)
		}
		// 隐私协议列表
		shipperV2Group.GET("/privacy", shipperV2Controller.GetPrivacyList)

		shipperComment := shipperV2Group.Group("/comment")
		shipperComment.Use(middlewares.AuthMiddleware())
		{

			//配送员评论列表
			shipperComment.GET("/list", middlewares.AuthMiddleware(), shipperV2Controller.GetShipperCommentList)
			//配送员赞赏列表
			shipperComment.GET("/tip", middlewares.AuthMiddleware(), shipperV2Controller.GetTip)
			//更新评论读取时间
			// 已发布
			shipperComment.POST("/update-read-time", middlewares.AuthMiddleware(), shipperV2Controller.PostUpdateReadTime)
		}

		shipperCash := shipperV2Group.Group("/cash")
		shipperCash.Use(middlewares.LangMiddleware(), middlewares.AuthMiddleware())
		{
			//配送员收客户订单金额和配送员结账现金订单接口
			shipperCash.POST("/v3-pay", cashController.PostPay)
			//配送端查询现金订单微信付款结果接口
			shipperCash.GET("/v3-pay-query", cashController.GetPayQuery)
			//获取配送员未缴纳订单列表接口(准备迁移)
			shipperCash.GET("/statistics", cashController.GetNotPayedOrderList)
			shipperCash.GET("/pay-query", cashController.GetPayQueryV2)

			shipperCash.POST("/union-pay", cashController.PostUnionPay) //统一支付

		}

		// 配送员收入,考勤有关接口
		shipment := shipperV2Group.Group("/shipment")
		shipment.Use(middlewares.LangMiddleware(), middlewares.AuthMiddleware())
		{
			// 用户中心-用户信息-收入概括
			shipment.GET("/user-center", shipmentController.GetShipperInfo)
			// 收入统计
			shipment.GET("/income-statistic", shipmentController.GetIncomeStatistic)


			// 配送员收入详情
			shipment.GET("/income-detail", shipmentController.GetIncomeStatisticDetail)
			// 配送员收入扣款标签
			shipment.GET("/income-tags", shipmentController.GetIncomeTags)

			// 订单统计
			shipment.GET("/order-statistic", shipmentController.GetOrderStatistic)
			// 请假/事故  状态:  4:请假,5:事故
			shipment.POST("/attendance/save", shipmentController.PostAttendanceSave)
			shipment.GET("/attendance/list", shipmentController.GetAttendanceList)
			shipment.GET("/attendance/detail", shipmentController.GetAttendanceDetail)
			shipment.GET("/attendance/leave-types", shipmentController.GetLeaveTypes)
			//意见和故障
			shipment.POST("/opinion/save", shipmentController.OpinionSave)

			//等级统计
			shipment.GET("/rank-statistic", shipmentController.GetRankStatistic)

		}

		// 配送员 拓展 客户有关的 接口  宣传材料
		

		material := shipperV2Group.Group("/material")
		material.Use(middlewares.LangMiddleware(), middlewares.AuthMiddleware())
		{
			//领取
			material.POST("/take", materialController.Take)
			//获取 材料信息
			material.GET("/scan", materialController.Scan)
			//领取列表
			material.GET("/list", materialController.MaterialList)

			//领取详情
			material.GET("/detail", materialController.MaterialTakeDetail)

			//	我的客户页面-按月统计
			material.GET("/customer-statistic", materialController.GetCustomerStatistic)

			// 获取宣传材料分类
			material.GET("/material-category", materialController.GetMaterialCategory)

			//推广中心
			material.GET("/center", materialController.MaterialCenter)

			//	推广统计页面
			material.GET("/material-statistic", materialController.GetMaterialStatistic)


			//获取 材料信息
			material.GET("/scan-log", materialController.ScanLog)
		}

		insurance := shipperV2Group.Group("/insurance")
		insurance.Use(middlewares.AuthMiddleware())
		{

			//  图片上传
			insurance.POST("upload-file", new(shipper.InsuranceController).PostUploadFile)
			//审核结果
			insurance.GET("check-state", new(shipper.InsuranceController).GetCheckState)

			//审核通过后的资料
			insurance.GET("check-info", new(shipper.InsuranceController).GetInfo)

			//第二页 手机短信验证码
			insurance.POST("account-sms", new(shipper.InsuranceController).PostSmsCode)

			//身份证信息提交
			insurance.POST("idcard-info", new(shipper.InsuranceController).PostIdCardInfo)
			
			// 第3页 账户信息
			insurance.POST("account-info", new(shipper.InsuranceController).PostAccountInfo)

			insurance.GET("business-bankList", new(merchant.CollectionInfoController).GetBankList)
			//
			insurance.GET("branch-bank-list", new(merchant.CollectionInfoController).GetBranchBankList) //原来的银联商务支行接口

			insurance.GET("branch-bank-list2", new(merchant.CollectionInfoController).GetBranchBankList2) //新的支行接口

			//保险购买 
			insurance.POST("insurance-buy", new(shipper.InsuranceController).PostInsuranceBuy)

			//保险状态查询 
			insurance.GET("insurance-status", new(shipper.InsuranceController).GetInsuranceStatus)

			//保险记录
			insurance.GET("insurance-log", new(shipper.InsuranceController).GetInsuranceLog)

			//保险取消
			insurance.POST("insurance-cancel", new(shipper.InsuranceController).PostInsuranceCancel)
			
		}

		shipperRestSpotGroup := shipperV2Group.Group("/rest-spot").Use(middlewares.LangMiddleware(), middlewares.AuthMiddleware())
		{
			shipperRestSpotGroup.GET("list", shipperRestSpotController.List)

		}


	}

}
