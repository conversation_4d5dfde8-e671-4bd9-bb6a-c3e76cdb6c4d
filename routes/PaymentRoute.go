package routes

import (
	"mulazim-api/controllers/payment"
	"mulazim-api/middlewares"

	"github.com/gin-gonic/gin"
)

// RegisterShipperRoute
//
//	@Description:  注册所有配送员路由
//	@author: Alimjan
//	@Time: 2022-09-23 16:30:07
//	@param r *gin.RouterGroup
func RegisterPaymentRoute(r *gin.RouterGroup) {
	paymentGroup := r.Group("/payment/v1")
	{

		// paymentGroup.POST("/cancel-order", middlewares.LangMiddleware(), new(payment.PaymentController).CancelOrder)
		paymentGroup.POST("/cancel-order", middlewares.LangMiddleware(), new(payment.PaymentController).PostPartRefund)

		paymentGroup.GET("/download-order-file", new(payment.PaymentController).DownloadOrderFile)
		paymentGroup.GET("/check-order-hourly", new(payment.PaymentController).CheckOrderPayStateHourly)
		paymentGroup.GET("/check-res-cash", new(payment.PaymentController).CheckResCash)
		paymentGroup.GET("/check-mulazim-balance", new(payment.PaymentController).CheckMulazimBalance)
		paymentGroup.GET("/check-take-order-error", new(payment.PaymentController).CheckTakeOrderError)
		// 拉卡拉退款出错时，手动退款
		paymentGroup.GET("/order-back-to-user", new(payment.PaymentController).OrderBackToUser)
		paymentGroup.Use(middlewares.LangMiddleware()).POST("/coupon-refund", new(payment.PaymentController).CouponRefund)
	}
	paymentGroup2 := r.Group("/payment/v2")
	{
		//微信支付获取参数
		paymentGroup2.Any("/pay-param", new(payment.PaymentController).GetPayParam)

		//微信支付获取参数 代理支付
		paymentGroup2.GET("/agent-pay-param", new(payment.PaymentController).GetAgentPayParam)


		paymentGroup2.Any("/wechat-notify", new(payment.PaymentController).MiniNotify)

		//抽奖活动 支付参数获取
		paymentGroup2.Any("/lottery-pay-param", new(payment.PaymentController).GetLotteryPayParam)


		//微信支付获取参数 代理支付 加价美食 显示二维码的接口
		paymentGroup2.GET("/agent-priceup-paycode", new(payment.PaymentController).GetAgentPriceUpPayCode)

		//微信支付获取参数 代理支付 加价美食 支付
		paymentGroup2.GET("/agent-priceup-pay-param", new(payment.PaymentController).GetAgentPriceUpPayParam)

		//支付查询
		paymentGroup2.GET("/agent-priceup-check", new(payment.PaymentController).GetAgentPriceUpCheck)


		paymentGroup2.GET("/agent-priceup-refund", new(payment.PaymentController).GetAgentPriceUpRefund)

		//部分退款
		paymentGroup2.POST("/part-refund", new(payment.PaymentController).PostPartRefund)


		paymentGroup2.Any("/order-status", new(payment.PaymentController).GetOrderStatus)

	}
}
