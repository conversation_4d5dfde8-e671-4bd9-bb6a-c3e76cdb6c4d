package routes

import (
	"mulazim-api/constants"
	"mulazim-api/controllers"
	"mulazim-api/controllers/merchant"
	"mulazim-api/middlewares"

	"github.com/gin-gonic/gin"
	"mulazim-api/controllers/payment"
)

// RegisterMerchantRoute
//
//	@Description: 注册商家路由
//	@author: Alim<PERSON>
//	@Time: 2022-09-23 16:39:14
//	@param r *gin.RouterGroup
func RegisterMerchantRoute(r *gin.RouterGroup) {
	var (
		pushController              = merchant.PushController{}
		shipmentReduceController    = merchant.ShipperReduceController{}
		foodsPreferentialController = merchant.FoodsPreferentialController{}
		permissionController = merchant.PermissionController{}

		priceMarkupController = merchant.PriceMarkupController{}
		
		foodsGroupController = merchant.FoodsGroupController{}
		foodsCategoryController = merchant.FoodsCategoryController{}
		foodsMultipleDiscountController = merchant.FoodsMultipleDiscountController{}

		restaurantFoodsController = new(controllers.RestaurantFoodsController)
		restaurantFoodsSpecController = new(controllers.RestaurantFoodsSpecController)

		marketingV2Controller = new(merchant.MarketingV2Controller)
		orderV2Controller = new(merchant.OrderV2Controller)
		merchantController = new(merchant.MerchantController)
	)
	// 处理银联商务商家入网结果通知
	r.POST("merchant/self-sign/notify", new(merchant.CollectionInfoController).PostNotify)

	merchantGroup := r.Group("/merchant/v1")
	{
		//  配送员登录
		merchantGroup.POST("/oauth/login", new(merchant.MerchantController).Login)

		//启动页和首页广告
		merchantGroup.GET("/oauth/splash-full-adver", new(merchant.MerchantController).Adver)

		merchantGroup.POST("/oauth/adver-confirm", new(merchant.MerchantController).ConfirmAdver)
		

		merchantGroup.GET("/oauth/terminal-show-adver", new(merchant.MerchantController).GetTerminalShowAdver)

		//  商家修改密码发送验证码
		merchantGroup.POST("/oauth/send-check-code", new(merchant.MerchantController).PostSendCheckCode)
		// 商家修改密码验证验证码
		merchantGroup.POST("/oauth/check-merchant-mobile", new(merchant.MerchantController).PostChangePasswordCodeVerify)

		// 发送短信验证码
		merchantGroup.POST("/oauth/send-verify-code", new(merchant.MerchantController).PostSmsCode)
		// 验证短信验证码
		merchantGroup.POST("/oauth/check-verify-code", new(merchant.MerchantController).CheckSmsCode)
		// 手机验证码登录
		merchantGroup.POST("/oauth/sms-login", new(merchant.MerchantController).PostSMSLogin)
		// 重置密码
		merchantGroup.POST("/oauth/reset-password", new(merchant.MerchantController).ResetPassword)


		//  配送员登录
		merchantGroup.GET("/lang/list", new(merchant.MerchantController).LangList)

		// flutter 版本app 升级
		merchantGroup.GET("/terminal/newAppShow", new(controllers.TerminalController).GetNewAppShow)

		// 商家满减活动
		marketing := merchantGroup.Group("marketing").Use(middlewares.AuthMiddleware()).Use(middlewares.PermissionMiddleware())
		{
			marketing.GET("marketing", new(merchant.MarketingController).Marketing)
			marketing.GET("list", new(merchant.MarketingController).List)
			marketing.POST("create", new(merchant.MarketingController).Create)
			marketing.GET("edit", new(merchant.MarketingController).Edit)
			marketing.POST("update", new(merchant.MarketingController).Update)
			marketing.POST("update-state", new(merchant.MarketingController).UpdateState)
			marketing.POST("delete", new(merchant.MarketingController).Delete)
			marketing.GET("detail", new(merchant.MarketingController).Detail)
			marketing.GET("statistics", new(merchant.MarketingController).Statistics)
			marketing.POST("join", new(merchant.MarketingController).Join)

		}

		shipmentReduce := merchantGroup.Group("shipment-reduce").Use(middlewares.AuthMiddleware()).Use(middlewares.PermissionMiddleware())
		{
			shipmentReduce.GET("list", shipmentReduceController.GetList)                                    // 获取减配送活动列表
			shipmentReduce.POST("create", shipmentReduceController.PostCreate)                              // 创建减配送活动
			shipmentReduce.GET("detail", shipmentReduceController.GetDetail)                                // 获取减配送活动详情
			shipmentReduce.POST("update", shipmentReduceController.PostUpdate)                              // 更新减配送活动
			shipmentReduce.POST("update-state", shipmentReduceController.PostUpdateState)                   // 修改减配送活动状态
			shipmentReduce.POST("delete", shipmentReduceController.PostDelete)                              // 删除减配送活动
			shipmentReduce.GET("statistics", shipmentReduceController.GetStatistics)                        // 获取减配送活动统计
			shipmentReduce.GET("change-log", shipmentReduceController.GetChangeLog)                         // 获取减配送活动修改记录
			shipmentReduce.GET("invitation", shipmentReduceController.GetInvitation)                        // 活动未参加团体活动列表
			shipmentReduce.GET("invitation/detail", shipmentReduceController.GetInvitationDetail)           // 获取团体活动详情
			shipmentReduce.POST("invitation/attendance", shipmentReduceController.PostInvitationAttendance) // 参加团体活动

		}
		merchantAdmin := merchantGroup.Group("/admin")
		merchantAdmin.POST("change-password", new(merchant.MerchantController).PostChangeSMSPassword)
		/**
		* 商家信息后台提交银联商务后面流程
		 */
		// 后台上传资料(银联商务)
		merchantAdmin.POST("upload-doc", new(merchant.CollectionInfoController).GetUploadDoc)
		merchantAdmin.GET("ums-apply-qry-from-cms", new(merchant.CollectionInfoController).GetUmsApplyQueryFromCms)
		merchantAdmin.GET("alter-account", new(merchant.CollectionInfoController).GetAlterAccountInfo)

		//给dcat后台提供银行列表
		merchantAdmin.GET("business-bankList", new(merchant.CollectionInfoController).GetBankList)
		//给dcat后台提供银行分支列表列表
		merchantAdmin.GET("branch-bank-list2", new(merchant.CollectionInfoController).GetBranchBankList2) //新的支行接口
		// 获取AppConfig的值，发送keys ，逗号区分
		merchantAdmin.GET("get-config", new(merchant.MerchantController).GetConfig) //新的支行接口

		merchantAdmin.Use(middlewares.AuthMiddleware()).Use(middlewares.PermissionMiddleware())
		{
			//获取商家端新订单列表
			//  已发布
			merchantAdmin.GET("new-order-list", new(merchant.MerchantController).MerchantNewOrderList)
			//商家端餐厅详细信息
			//  已发布
			//生成采集餐厅员工微信信息的二维码（小程序码）
			merchantAdmin.GET("smart-app-code", new(merchant.MerchantController).GetSmartAppCode)

			merchantAdmin.POST("receive-order", new(merchant.MerchantController).PostReceiveOrder)

			merchantAdmin.POST("update-restaurant-information", new(merchant.MerchantController).UpdateMerchantInfo)

			merchantAdmin.POST("upload-foods-image", new(merchant.MerchantController).PostUploadFoodsImage)

			merchantAdmin.POST("upload-restaurant-avatar", new(merchant.MerchantController).PostUploadRestaurantAvatar)

			merchantAdmin.POST("upload-restaurant-image", new(merchant.MerchantController).PostUploadRestaurantImage)
			//获取商家未提现账单列表
			merchantAdmin.GET("not-cashed-list", new(merchant.MerchantController).GetNotCashedList)
			//  商家未体现列表
			merchantAdmin.GET("cash-out-list", new(merchant.MerchantController).GetCashedOutList)

			//商家提现接口
			merchantAdmin.POST("cash-out", new(merchant.MerchantController).PostCashOut)

			merchantAdmin.GET("restaurant-home", new(merchant.MerchantController).GetRestaurantInfo)

			//获取美食信息
			merchantAdmin.GET("foods-information", new(merchant.RestaurantFoodsController).GetFoodsInformation)

			//获取商家已接单订单列表接口(时间格式!!!!) √
			merchantAdmin.GET("received-order-list", new(merchant.MerchantController).GetReceivedOrderList)
			//获取餐厅当日已完成的订单列表（包括正在配送和已送完的订单） √
			merchantAdmin.GET("completed-order-list", new(merchant.MerchantController).GetCompletedOrderList)
			//获取餐厅当日已取消的订单列表（包括客户取消的和餐厅拒绝的订单） √
			merchantAdmin.GET("canceled-order-list", new(merchant.MerchantController).GetCanceledOrderList)

			merchantAdmin.GET("check-self-take-order", new(merchant.MerchantController).CheckSelfTakeOrder)

			merchantAdmin.POST("receive-self-take-order", new(merchant.MerchantController).ReceiveSelfTakeOrder)

			// 查询要打印的订单的有效性
			merchantAdmin.GET("check-print-order", new(merchant.MerchantController).CheckPrintOrder)

			//餐厅营业统计接口 √
			merchantAdmin.GET("business-statistics", new(merchant.MerchantController).GetBusinessStatistics)
			//返回商家美食分类接口 √
			merchantAdmin.GET("foods-category", new(merchant.RestaurantFoodsController).GetFoodsCategory)
			//获取餐厅美食列表  √
			merchantAdmin.GET("restaurant-foods", new(merchant.RestaurantFoodsController).GetRestaurantFoods)
			//获取美食分类列表 × 缓存问题
			merchantAdmin.GET("foods-category-list", new(merchant.MerchantController).GetFoodsCategoryList)

			//获取餐厅员工绑定信息 √
			merchantAdmin.GET("merchant-stuff", new(merchant.MerchantController).GetMerchantStuff)
			//删除商家评论
			merchantAdmin.POST("delete-reply", new(merchant.MerchantController).PostDeleteReply)
			merchantAdmin.POST("reply", new(merchant.MerchantController).PostReply)
			//更新商家阅读评论时间
			merchantAdmin.POST("update-comment-time", new(merchant.MerchantController).PostUpdateCommentTime)
			//创建美食
			merchantAdmin.POST("create-foods", new(merchant.MerchantController).PostCreateFood)
			merchantAdmin.POST("update-food", new(merchant.MerchantController).PostUpdateFood)
			merchantAdmin.POST("upload-business-license", new(merchant.MerchantController).PostUploadBusinessLicense)

			// 餐厅
			restaurantGroup := merchantAdmin.Group("restaurant")
			{
				// 美食资源
				restaurantFoodsGroup := restaurantGroup.Group("foods")
				{
					restaurantFoodsGroup.GET("combo-food-time", restaurantFoodsController.GetComboFoodTime)
					// 美食列表
					restaurantFoodsGroup.GET("", func(ctx *gin.Context) {
						restaurantFoodsController.GetList(ctx, constants.FromMerchantClient)
					})
					// 创建美食
					restaurantFoodsGroup.POST("", func(c *gin.Context) {
						restaurantFoodsController.PostCreate(c, constants.FromMerchantClient)
					})
					// 编辑美食
					restaurantFoodsGroup.PATCH("/:id", func(c *gin.Context) {
						restaurantFoodsController.PostEdit(c, constants.FromMerchantClient)
					})

					// 批量设置规格
					restaurantFoodsGroup.POST("/spec", restaurantFoodsSpecController.CreateSpec)
					// 获取规格列表
					restaurantFoodsGroup.GET("/:food_id/spec", restaurantFoodsSpecController.GetSpecList)
					// 更新美食规格
					restaurantFoodsGroup.PUT("/:food_id/spec", restaurantFoodsSpecController.UpdateSpec)
					// 删除美食规格
					restaurantFoodsGroup.DELETE("/:food_id/spec", restaurantFoodsSpecController.DeleteSpec)
				}
			}

			merchantAdmin.GET("comment", new(merchant.MerchantController).GetComment)

			//	更新美食状态
			merchantAdmin.POST("update-food-state", new(merchant.MerchantController).PostUpdateFoodState)
			//	更新美食状态(关闭)
			merchantAdmin.POST("change-food-state", new(merchant.MerchantController).ChangeFoodState)
			// 更新商家状态
			merchantAdmin.POST("update-restaurant-state", new(merchant.MerchantController).PostUpdateRestaurantState)

			//设置店铺在小程序上的显示模板
			merchantAdmin.POST("update-restaurant-view-type", new(merchant.MerchantController).UpdateRestaurantViewType)

			//设置店铺在小程序上的显示模板
			merchantAdmin.GET("restaurant-view-type", new(merchant.MerchantController).GetRestaurantViewType)

			// 返回商家分类
			merchantAdmin.GET("restaurant-category", new(merchant.MerchantController).GetRestaurantCategory)
			// 获取商家信息
			merchantAdmin.GET("restaurant-information", new(merchant.MerchantController).GetRestaurantInformation)
			//获取商家银行卡信息
			merchantAdmin.GET("finance-info", new(merchant.MerchantController).GetFinanceInfo)

			merchantAdmin.POST("order-ready-to-send", new(merchant.MerchantController).PostOrderReadyToSend)
			// 部分退款返回信息
			merchantAdmin.GET("part-refund-list", new(merchant.MerchantController).PartRefundList)
			// 批量售完美食
			merchantAdmin.POST("batch-sold-foods", new(merchant.MerchantController).BatchSoldFoods)


			selfSign := merchantAdmin.Group("self-sign")
			{
				//  图片上传
				selfSign.POST("upload-file", new(merchant.CollectionInfoController).PostUploadFile)
				// 第1页  营业执照信息
				selfSign.POST("shop-license-info", new(merchant.CollectionInfoController).PostShopLicenseInfo)
				// 第2页 营业环境 Business environment
				selfSign.POST("idcard-info", new(merchant.CollectionInfoController).PostIdCardInfo)
				//// 第3页 账户信息
				selfSign.POST("account-info", new(merchant.CollectionInfoController).PostAccountInfo)
				//// 第4页 门店信息
				selfSign.POST("shop-info", new(merchant.CollectionInfoController).PostShopInfo)
				//// 第5页 账单号码  Bill number
				//selfSign.POST("bill-num", new(chinaums.SelfSignController).PostBillNum)
				//// 第6页 特殊管理员 Special administrator
				//selfSign.POST("special-admin", new(chinaums.SelfSignController).PostSpecialAdmin)

				// 获取支行列表
				selfSign.GET("branch-bank-list", new(merchant.CollectionInfoController).GetBranchBankList) //原来的银联商务支行接口

				selfSign.GET("branch-bank-list2", new(merchant.CollectionInfoController).GetBranchBankList2) //新的支行接口
				// 第5页 管理员  administrator
				selfSign.POST("administrator", new(merchant.CollectionInfoController).PostAdministrator)
				// 第6页 食品经营许可证
				selfSign.POST("business-license", new(merchant.CollectionInfoController).PostBusinessLicense)
				// 第7页 员工信息
				selfSign.POST("staff-info", new(merchant.CollectionInfoController).PostStaffInfo)
				//获取第一页信息
				selfSign.GET("shop-license-info", new(merchant.CollectionInfoController).GetShopLicenseInfo)
				//获取第二页信息
				selfSign.GET("idcard-info", new(merchant.CollectionInfoController).GetIdCardInfo)
				//获取第三页信息
				selfSign.GET("account-info", new(merchant.CollectionInfoController).GetAccountInfo)
				//获取第四页信息
				selfSign.GET("shop-info", new(merchant.CollectionInfoController).GetShopInfo)
				//获取第五页信息
				selfSign.GET("administrator", new(merchant.CollectionInfoController).GetAdministrator)
				//获取第六页信息
				selfSign.GET("business-license", new(merchant.CollectionInfoController).GetBusinessLicense)
				//获取第七页信息
				selfSign.GET("staff-info", new(merchant.CollectionInfoController).GetStaffInfo)

				// 对公账户认证
				selfSign.POST("ums-account-verify", new(merchant.CollectionInfoController).PostUmsVerifyAccount)
				// 查询入网状态
				selfSign.GET("ums-apply-qry", new(merchant.CollectionInfoController).GetUmsApplyQuery)

				//第二页 手机短信验证码
				selfSign.POST("idcard-sms", new(merchant.CollectionInfoController).PostSmsCode)

				//第四页 业务类型
				selfSign.GET("business-payproduct", new(merchant.CollectionInfoController).GetPayProduct)

				//第三页 银行列表
				selfSign.GET("business-bankList", new(merchant.CollectionInfoController).GetBankList)

				//员工健康卡 修改
				selfSign.POST("staff-update", new(merchant.CollectionInfoController).PostUpdateStaffInfo)

				//员工健康卡 删除
				selfSign.POST("staff-delete", new(merchant.CollectionInfoController).PostDelStaffInfo)

				//审核结果
				selfSign.GET("shop-check-state", new(merchant.CollectionInfoController).GetCheckState)

				//审核通过后的资料
				selfSign.GET("shop-check-info", new(merchant.CollectionInfoController).GetRestaurantInfo)

				//清空信息
				selfSign.POST("clear", new(merchant.CollectionInfoController).Clear)

				//提交资料
				selfSign.POST("submit", new(merchant.CollectionInfoController).Submit)

				//重新提交资料
				selfSign.POST("submit-again", new(merchant.CollectionInfoController).SubmitAgain)
			}

		}

		lakala := merchantGroup.Group("lakala")
		{
			//银联商务入驻的商户迁移到拉卡拉
			lakala.GET("migrate", new(merchant.LakalaController).Migrate)
			//入驻通知
			lakala.Any("memberNotify", new(merchant.LakalaController).MemberNotify)

			lakala.Any("cardNotify", new(merchant.LakalaController).CardNotify)

			lakala.Any("withdrawNotify", new(merchant.LakalaController).WithdrawNotify)

			member := lakala.Group("member")
			{
				//查询会员信息
				member.GET("query", new(merchant.LakalaController).MemberQuery)

			}
			merchantGroup := lakala.Group("merchant")
			{
				//查询商户信息
				merchantGroup.GET("query", new(merchant.LakalaController).MerchantQuery)

			}
			//签名
			lakala.POST("sign", new(merchant.LakalaController).Sign)

			lakala.GET("bank-cards", new(merchant.LakalaController).BankCards)

			lakala.GET("bank-card-bind", new(merchant.LakalaController).BankCardBind)

			//提现明细
			lakala.GET("cash-out-detail", new(merchant.LakalaController).GetCashOutDetail)

			lakala.GET("archive-error-items", new(merchant.LakalaController).ArchiveErrorItems)

		}
		push := merchantGroup.Group("push")
		{
			// 推送
			push.POST("register", middlewares.AuthMiddleware(), pushController.Register)
			push.POST("unregister", pushController.UnRegister)
		}

		//秒杀
		seckill := merchantGroup.Group("seckill").Use(middlewares.AuthMiddleware()).Use(middlewares.PermissionMiddleware())
		{
			//列表
			seckill.GET("list", new(merchant.SeckillController).List)
			//创建
			seckill.POST("create", new(merchant.SeckillController).Create)
			//编辑查看
			seckill.GET("edit", new(merchant.SeckillController).Edit)
			// 详情
			seckill.GET("/:id", new(merchant.SeckillController).Detail)
			//修改状态
			seckill.POST("update-state", new(merchant.SeckillController).UpdateState)
			//删除
			seckill.POST("delete", new(merchant.SeckillController).Delete)
			//日志
			seckill.GET("log", new(merchant.SeckillController).Log)

		}

		// 优惠
		foodsPreferential := merchantGroup.Group("foods-preferential").Use(middlewares.AuthMiddleware()).Use(middlewares.PermissionMiddleware())
		{
			foodsPreferential.GET("list", foodsPreferentialController.List)
			foodsPreferential.POST("create", foodsPreferentialController.Create)
			foodsPreferential.POST("update", foodsPreferentialController.Update)
			foodsPreferential.POST("update-state", foodsPreferentialController.UpdateState)
			foodsPreferential.GET("detail", foodsPreferentialController.Detail)
			foodsPreferential.DELETE("delete", foodsPreferentialController.Delete)
		}

		// 商家权限
		permission := merchantGroup.Group("permission").Use(middlewares.AuthMiddleware()).Use(middlewares.PermissionMiddleware())
		{
			permission.GET("role-list", permissionController.RoleList)  		// 获取角色列表
			permission.GET("role-types", permissionController.RoleTypes)  		// 获取角色类型和所有权限列表
			permission.GET("staff-list", permissionController.StaffList)  		// 员工列表
			permission.POST("change-state", permissionController.ChangeState)   // 修改状态
			permission.GET("staff-info", permissionController.StaffInfo)  		// 员工信息
			permission.POST("create", permissionController.Create)  		    // 创建员工
			permission.POST("update", permissionController.Update)  		    // 更新员工
			permission.POST("delete", permissionController.Delete) 				// 删除员工
		}

		// 美食接口（根据新分类模块实现）
		foodsRouterGroup := merchantGroup.Group("foods")
		{
			// 美食详情
			foodsRouterGroup.GET("information", middlewares.AuthMiddleware(), foodsGroupController.GetDetail)
			// 美食详情 - 新 - 价格分 + 套餐
			foodsRouterGroup.GET("detail", middlewares.AuthMiddleware(), foodsGroupController.GetDetailNew)
			// 根据分组获取美食列表
			foodsRouterGroup.GET("list", middlewares.AuthMiddleware(), foodsGroupController.FoodsListByGroupId)

			// 美食分组相关接口（餐厅美食分类）
			foodsGroup := foodsRouterGroup.Group("group").Use(middlewares.AuthMiddleware())
			{
				// 创建分组
				foodsGroup.POST("create", foodsGroupController.Create)
				// 编辑分组
				foodsGroup.POST("edit", foodsGroupController.Edit)
				// 分组删除
				foodsGroup.POST("delete", foodsGroupController.Delete)
				// 分组列表
				foodsGroup.GET("list", foodsGroupController.List)
				// 分组详情
				foodsGroup.GET("detail", foodsGroupController.Detail)
				// 美食分组排序
				foodsGroup.POST("set-group-order", foodsGroupController.PostSetGroupOrder)
				// 推荐分组
				foodsGroup.GET("recommend", foodsGroupController.GetRecommendGroup)

			}
			// 美食分类相关接口（美食对应平台分类）
			foodsCategory := foodsRouterGroup.Group("category")
			{
				// 美食分类
				foodsCategory.GET("list",foodsCategoryController.List)
			}
		}

		//抽奖记录
		lottery := merchantGroup.Group("lottery").Use(middlewares.AuthMiddleware()).Use(middlewares.PermissionMiddleware())
		{

			lottery.GET("list", new(merchant.LotteryController).LotteryCouponLog)
		}

		// 加价活动
		priceMarkupRouterGroup := merchantGroup.Group("price-markup").Use(middlewares.AuthMiddleware()).Use(middlewares.PermissionMiddleware())
		{
			// 首页显示加价信息列表
			priceMarkupRouterGroup.GET("home-show-list",priceMarkupController.HomeShowList)
			// 商家加价活动处理 1 同意 2 拒绝
			priceMarkupRouterGroup.POST("handle",priceMarkupController.Handle)
			// 发送验证码
			priceMarkupRouterGroup.POST("send-cms-code",priceMarkupController.SendSMSCode)
			// 加价活动详细列表
			priceMarkupRouterGroup.GET("detail-list",priceMarkupController.DetailList)
			// 统计页面显示美食销售标签
			priceMarkupRouterGroup.GET("statistic-sailed-market",priceMarkupController.StatisticSailedMarket)
			// 统计页面美食加价销售明细
			priceMarkupRouterGroup.GET("statistic-sailed-market-detail",priceMarkupController.StatisticSailedMarketDetail)
		}

		//部分退款 
		partRefundRouterGroup := merchantGroup.Group("part-refund")
		{
			partRefundRouterGroup.POST("/create", new(payment.PaymentController).PostPartRefund)
		}

		// 多买打折活动
		foodsMultipleDiscountGroup := merchantGroup.Group("foods-multiple-discount").Use(middlewares.AuthMiddleware()).Use(middlewares.PermissionMiddleware())
		{
			// 创建多买打折活动
			foodsMultipleDiscountGroup.POST("create", foodsMultipleDiscountController.Create)
			// 获取多买打折活动列表
			foodsMultipleDiscountGroup.GET("list", foodsMultipleDiscountController.List)
			// 修改多买打折活动状态
			foodsMultipleDiscountGroup.POST("change-state", foodsMultipleDiscountController.ChangeState)
			// 获取多买打折活动详情
			foodsMultipleDiscountGroup.GET("detail", foodsMultipleDiscountController.Detail)
			// 删除多买打折活动
			foodsMultipleDiscountGroup.POST("delete", foodsMultipleDiscountController.Delete)
			// 修改多买打折活动
			foodsMultipleDiscountGroup.POST("update", foodsMultipleDiscountController.Update)

		}
	}

	merchantV2Group := r.Group("/merchant/v2")
	{
		// 商家满减活动
		marketing := merchantV2Group.Group("marketing").Use(middlewares.AuthMiddleware()).Use(middlewares.PermissionMiddleware())
		{
			// 创建
			marketing.POST("", marketingV2Controller.Create)
			// 获取详情
			marketing.GET("/:id", marketingV2Controller.GetOne)
			// 更新
			marketing.PUT("/:id", marketingV2Controller.Update)
		}

		merchantV2AdminGroup := merchantV2Group.Group("admin")
		{
			// 批量售完美食V2
			merchantV2AdminGroup.Use(middlewares.AuthMiddleware()).Use(middlewares.PermissionMiddleware()).
				POST("batch-sold-foods", merchantController.BatchSoldFoodsV2)

			// 订单列表
			orders := merchantV2AdminGroup.Group("orders")
			{
				// 获取商家端新订单列表
				orders.GET("new", orderV2Controller.GetNewList)
				// 获取商家已接单订单列表接口
				orders.GET("received", orderV2Controller.GetReceivedList)
				// 获取餐厅当日已完成的订单列表（包括正在配送和已送完的订单）
				orders.GET("completed", orderV2Controller.GetCompletedList)
				// 获取餐厅当日已取消的订单列表（包括客户取消的和餐厅拒绝的订单）
				orders.GET("canceled", orderV2Controller.GetCanceledList)
			}
		}
	}
	// {merchantV2Group}

}