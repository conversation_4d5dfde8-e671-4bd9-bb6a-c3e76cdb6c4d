package routes

import (
	"mulazim-api/controllers"
	"mulazim-api/middlewares"

	"github.com/gin-gonic/gin"
)

// RegisterShipperRoute
//
//	@Description:  注册所有配送员路由
//	@author: Alimjan
//	@Time: 2022-09-23 16:30:07
//	@param r *gin.RouterGroup
func RegisterChatRoute(r *gin.RouterGroup) {
	chatGroup := r.Group("/v2/chat")
	chatGroup.POST("/create-forbidden-word", new(controllers.ChatController).CreateForbiddenWord) // 创建禁用词
	chatGroup.POST("/encrypt-state-zero-forbidden-words", new(controllers.ChatController).EncryptAndUpdateStateZeroWords) // 将状态为0的禁用词进行加密并更新
	chatGroup.Use(middlewares.AuthMiddleware()).Use(middlewares.PermissionMiddleware())
	{
		//清理redis 数量 接口
		chatGroup.POST("/clear", middlewares.LangMiddleware(), new(controllers.ChatController).PostClear)
		chatGroup.POST("/send", middlewares.LangMiddleware(), new(controllers.ChatController).PostSend)
		chatGroup.GET("/detail", middlewares.LangMiddleware(), new(controllers.ChatController).GetChatDetail)
		chatGroup.GET("/list", middlewares.AuthMiddleware(), new(controllers.ChatController).GetChatList)
		chatGroup.POST("/upload", new(controllers.ChatController).Upload)
	}
}
