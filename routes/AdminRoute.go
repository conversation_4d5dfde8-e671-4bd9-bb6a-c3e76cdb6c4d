package routes

import (
	"mulazim-api/controllers/admin/lakala"
	"mulazim-api/middlewares"

	"github.com/gin-gonic/gin"
)

func RegisterAdminRoute(r *gin.RouterGroup) {
	adminRouteGroup := r.Group("admin/v1")
	{
		adminHGroup := adminRouteGroup.Group("admin")
		{
			lakalaGroup := adminHGroup.Group("lakala")
			{
				withdrawGroup := lakalaGroup.Group("withdraws").Use(middlewares.PermissionMiddleware())
				{
					withdrawGroup.POST("withdraw", new(lakala.WithdrawController).Store)

					//转到余额
					withdrawGroup.POST("toBalance", new(lakala.WithdrawController).ToBalance)

				}
			}
		}
	}
}
