/***
 * @Date: 2023-07-13 11:01:52
 * @LastEditors: rzbb
 * @LastEditTime: 2023-10-24 16:13:14
 * @FilePath: \mulazim-api-go\routes\route.go
 */
package routes

import (
	"mulazim-api/controllers"
	"mulazim-api/controllers/cmd"
	"mulazim-api/controllers/cms"
	"mulazim-api/inits"

	"github.com/gin-gonic/gin"
)

// InitLangRoute
//
//	@Description: 因平台特殊性 所有的路由前面 加Ug/Zh 前缀
//	@author: Alimjan
//	@Time: 2022-09-23 16:47:16
//	@param r *gin.Engine
func InitLangRoute(r *gin.Engine) {

	//系统监测
	r.GET("/", new(controllers.SystemController).Check)
	r.GET("check-server", new(controllers.SystemController).Check)
	//同步银行数据
	r.GET("sync-bankbranch", new(controllers.SystemController).SyncBankBranch)

	r.GET("find-user-area", new(cmd.FindUserAreaController).FindUserArea)
	r.GET("insert-2-user", new(cmd.InsertOrderUserTwoTUser).InsertToTUser)
	r.GET("delete-order-job-key", new(cmd.DeleteOrderCreateRedisKey).DeleteKey)
	//删除voice notify 中的历史数据
	r.GET("delete-old-voice-notify", new(cmd.DeleteVoiceNotifyOldData).DeleteOldData)
	r.GET("delete-old-shipper-push", new(cmd.DeleteShipperPushOldData).DeleteOldData)
	r.GET("wechat-order-download", new(cmd.DownloadCmsStaticsCSV).DownloadWechatPayStatics)
	r.GET("lakala-order-download", new(cmd.DownloadCmsStaticsCSV).DownloadLakalaPayStatics)
	//  定时任务归档数据
	r.GET("table-backup", new(cmd.TableBackupController).BackupTask)
	//手续费文件下载
	r.GET("lakala-fee-download", new(cmd.DownloadCmsStaticsCSV).DownloadLakalaFee)

	//自取发布前的数据统计数据更新
	r.GET("self-take-data-update", new(cmd.DailySaleDataCorrection).CorrectData)

	r.GET("find-related-foods", new(cmd.FindRelatedFoods).FindFoods)
	//更新拉卡拉提现后的不记录问题
	r.GET("withdraw-data-update", new(cmd.LakalaWithdrawDataCorrection).CorrectData)

	r.GET("lakala-double-payment-check", new(cmd.LakalaRefundDoublePayment).CheckDoublePayment)

	//手动处理退款
	r.GET("lakala-manual-refund", new(cmd.LakalaRefundDoublePayment).RefundManual)

	r.GET("lakala-check", new(cmd.LakalaRefundDoublePayment).CheckPayment)

	//定时任务 删除3天前的未支付的订单数据
	r.GET("delete_unpaid_orders", new(cmd.DeleteUnPaidOrders).DeleteUnPaidOrders)

	r.GET("marketing/orderlog/sync", new(cmd.MarketingOrderLogController).Sync)

	// 更新订单表中的配送员拓展用户分给的金额
	r.GET("invite/update-order-shipper-reward", new(cmd.InviteUserController).UpdateOrderShipperReward)

	//订单推送
	r.GET("order-push", new(controllers.SystemController).OrderPushToMerchant)
	//订单推送 配送员
	r.GET("order-shipper-push", new(controllers.SystemController).OrderShipperPush)

	r.GET("clear-pay-lakala-payinfo", new(cmd.ClearPayInfo).ClearPayInfo)
	r.Static("/archived_files", inits.ConfigFilePath+"./archived_files")
	
	r.GET("lakala-withdraw-fix", new(cmd.LakalaRefundDoublePayment).FixLakalaWithDraw)

	//后台手动打印时 给商家 发送新订单推送 
	r.GET("order-push-to-merchant", new(controllers.SystemController).OrderPushToMerchant)

	langRouteGroup := r.Group(":locale")
	{
		
		//配送员 保险 默认数据创建 定时任务
		langRouteGroup.GET("insurance-create", new(cms.ShipperInsuranceController).CreateShipperInsuranceForce)
		//获取电信优惠券 
		langRouteGroup.GET("get-telecom-coupon", new(controllers.SystemController).GetTelecomCoupon)
		
		langRouteGroup.Static("/log", inits.ConfigFilePath+"./log")

		//配送员收入 归档
		langRouteGroup.GET("/shipper-income-archive", new(cmd.ShipperIncomeCalculate).IncomeArchive)

		// 配送员收入 对账
		langRouteGroup.GET("/shipper-income-check", new(cmd.ShipperIncomeCalculate).IncomeCheck)

		langRouteGroup.GET("/shipper-income-all", new(cmd.ShipperIncomeCalculate).IncomeArchiveAll)

		//按日期计算配送员工资 
		langRouteGroup.GET("/shipper-income-by-day", new(cmd.ShipperIncomeCalculate).IncomeArchiveByDay)
		//计算全勤奖
		langRouteGroup.GET("/shipper-full-attendance", new(cmd.ShipperIncomeCalculate).IncomeFullAttendance)

		//修复跨月时的数据错误
		langRouteGroup.GET("/shipper-income-last-day", new(cmd.ShipperIncomeCalculate).FixLastDayError)
		langRouteGroup.GET("/shipper-income-fix-amount", new(cmd.ShipperIncomeCalculate).FixSpecialOrderShipperPriceError)

		// 跟新配送员上个月设置的新模板
		langRouteGroup.GET("/update-new-shipper-template", new(cmd.ShipperTemplateUpdate).UpdateShipperTemplates)
		// 修复 根据订单数量 计算工资的 超过 每一个 等级次的 计算缺陷
		langRouteGroup.GET("/shipper-income-fix-by-order-count", new(cmd.ShipperIncomeCalculate).FixSalaryOrderCountByOverLimit)
		
		//配送员拓展用户 计算今日数据 每分钟统计一次
		langRouteGroup.GET("/shipper-introduce-tip-by-minute", new(cmd.ShipperIntroduceCustomer).DailyStatisticMinute)
		langRouteGroup.GET("/shipper-introduce-tip-update", new(cmd.ShipperIntroduceCustomer).ShipperIncomeUpdateByShipperID)
		//配送员拓展用户 计算今日数据 每天统计一次
		langRouteGroup.GET("/shipper-introduce-tip-by-day", new(cmd.ShipperIntroduceCustomer).DailyStatisticDay)

		langRouteGroup.GET("/shipper-introduce-tip-by-month", new(cmd.ShipperIntroduceCustomer).MonthStatistic)
        //  用户端App升级
		langRouteGroup.GET("/v1/terminal/show", new(controllers.TerminalController).GetAppShow)
		langRouteGroup.GET("/v1/about/detail", new(controllers.TerminalController).GetAboutDetail)

		// 所有配送员状态更新下班状态(每天早晨6:30执行)
		langRouteGroup.GET("/update-shipper-attendance",  new(cmd.ArchiveData).UpdateShipperAttendanceState)

		//同步订单数据到elastic search
		langRouteGroup.GET("/sync-order-to-elastic", new(cmd.ElasticOrderSync).SyncOrderToday)
		//  注册所有配送员路由
		RegisterShipperRoute(langRouteGroup)
		//  注册商家所有路由
		RegisterMerchantRoute(langRouteGroup)

		RegisterCmsRoute(langRouteGroup)
		//注册退单路由
		RegisterPaymentRoute(langRouteGroup)
		//后台注册
		RegisterAdminRoute(langRouteGroup)
		// 队列路由
		RegisterJobsRoute(langRouteGroup)
		// 聊天
		RegisterChatRoute(langRouteGroup)
		// 用户端API
		RegisterClientRoute(langRouteGroup)
		//小程序
		RegisterSmartRoute(langRouteGroup)

		// 平台通用接口
		RegisterCommonRoute(langRouteGroup)
	}

}
