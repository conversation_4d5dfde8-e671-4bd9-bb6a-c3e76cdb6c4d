package routes

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/controllers/smart"
)

// RegisterSmartRoute
// 小程序接口
func RegisterSmartRoute(r *gin.RouterGroup) {
	var lotteryOrderController = new(smart.LotteryOrderController)
	smartGroup := r.Group("smart/v1")
	{
		lotteryOrderGroup := smartGroup.Group("lottery")
		{
			orderGroup := lotteryOrderGroup.Group("order")
			{
				orderGroup.POST("refund", lotteryOrderController.Refund)
			}
		}
	}
}
