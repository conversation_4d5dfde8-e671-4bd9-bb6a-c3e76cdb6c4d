package validators

import (
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	"mulazim-api/tools"
	"strings"
)
// ExistisValidater is a custom validator function
func ExistsValidater(fl validator.FieldLevel) bool {
	params := strings.SplitN(fl.Param(), " ",3)
	if len(params) < 1 {
		return false
	}

	tableName := params[0]
	id := fl.Field().String()
	db := tools.Db
	idStr := "id"
	if len(params) > 2 {
		idStr = params[1]
	}
	query := db.Table(tableName).Where(idStr+" = ?", id)

	if len(params) > 2 {
			// Use Raw SQL to apply additional conditions
		query = query.Where(params[2])
	}
	var count int64
	result := query.Count(&count)
	return result.Error == nil && count > 0
}

// Custom translation registration function for Uyghur
var ExistisRegisterTranslationsUg validator.RegisterTranslationsFunc = func(ut ut.Translator) error {
	return ut.Add("existis", "{0} تىپىلمىدى", true)
}

// Custom translation function for Uyghur
var ExistisTranslationFuncUg validator.TranslationFunc = func(ut ut.Translator, fe validator.FieldError) string {
	t, _ := ut.T("existis", fe.Field())
	return t
}

// Custom translation registration function for Chinese
var ExistisRegisterTranslationsZh validator.RegisterTranslationsFunc = func(ut ut.Translator) error {
	return ut.Add("existis", "{0}未找到", true)
}

// Custom translation function for Chinese
var ExistisTranslationFuncZh validator.TranslationFunc = func(ut ut.Translator, fe validator.FieldError) string {
	t, _ := ut.T("existis", fe.Field())
	return t
}
