package validators

import (
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	"regexp"

)

func PhoneValidater(fl validator.FieldLevel) bool {
	phone := fl.Field().String()
	matched, _ := regexp.MatchString(`^1[3456789]\d{9}$`, phone)
	return matched
}
//定义 这个类型的变量并赋值 RegisterTranslationsFunc
var PhoneRegisterTranslationsUg validator.RegisterTranslationsFunc =  func(ut ut.Translator) error {
	return ut.Add("phone", "{0} شەكلى خاتا", true)
}

var PhoneTranslationFuncUg validator.TranslationFunc = func(ut ut.Translator, fe validator.FieldError) string {
	t, _ := ut.T("phone", fe.Field())
	return t
}
var PhoneRegisterTranslationsZh validator.RegisterTranslationsFunc =  func(ut ut.Translator) error {
	return ut.Add("phone", "{0}格式错误", true)
}

var PhoneTranslationFuncZh validator.TranslationFunc = func(ut ut.Translator, fe validator.FieldError) string {
	t, _ := ut.T("phone", fe.Field())
	return t
}
