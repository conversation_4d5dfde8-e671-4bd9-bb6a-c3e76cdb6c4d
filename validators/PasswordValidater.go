package validators

import (
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	"unicode"
)
// PasswordValidater is a custom validator function
func PasswordValidater(fl validator.FieldLevel) bool {
	password := fl.Field().String()
	// Password must be at least 8 characters long and contain both letters and numbers
	// Password must be at least 8 characters long
	if len(password) < 8 {
		return false
	}

	hasLetter := false
	hasDigit := false

	for _, char := range password {
		switch {
		case unicode.IsLetter(char):
			hasLetter = true
		case unicode.IsDigit(char):
			hasDigit = true
		}
		if hasLetter && hasDigit {
			return true
		}
	}

	return false
}

// Custom translation registration function for Uyghur
var PasswordRegisterTranslationsUg validator.RegisterTranslationsFunc = func(ut ut.Translator) error {
	return ut.Add("password", "{0} نىڭ فورماتى خاتا، كەم دىگەندە 8 ھەرپ بىلەن سانىڭ قوشۇلغان بۇلىشى كېرەك", true)
}

// Custom translation function for Uyghur
var PasswordTranslationFuncUg validator.TranslationFunc = func(ut ut.Translator, fe validator.FieldError) string {
	t, _ := ut.T("password", fe.Field())
	return t
}

// Custom translation registration function for Chinese
var PasswordRegisterTranslationsZh validator.RegisterTranslationsFunc = func(ut ut.Translator) error {
	return ut.Add("password", "{0}格式错误，至少8位字符，必须包含字母和数字", true)
}

// Custom translation function for Chinese
var PasswordTranslationFuncZh validator.TranslationFunc = func(ut ut.Translator, fe validator.FieldError) string {
	t, _ := ut.T("password", fe.Field())
	return t
}
