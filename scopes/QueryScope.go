package scopes

import (
	"gorm.io/gorm"
)

// WhereCityAndAreaAndState
//
//	@Time 2023-01-03 13:14:21
//	<AUTHOR>
//	@Description: 分页插件 使用方法: db.Scopes(Paginate(c)).Find(&users)
//	@param c
//	@return func(db *gorm.DB) *gorm.DB
//

func CityArea(cityID int, areaID int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if cityID > 0 {
			db.Where("city_id = ?", cityID)
		}

		if areaID > 0 {
			db.Where("area_id = ?", areaID)
		}
		return db
	}
}

func CityAreaState(cityID int, areaID int, state int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if cityID > 0 {
			db.Where("city_id = ?", cityID)
		}

		if areaID > 0 {
			db.Where("area_id = ?", areaID)
		}
		if state > 0 {
			db.Where("state = ?", state)
		}
		return db
	}
}

func CityAreaStateDate(cityID int, areaID int, state int, startDate string, endDate string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if cityID > 0 {
			db.Where("city_id = ?", cityID)
		}

		if areaID > 0 {
			db.Where("area_id = ?", areaID)
		}
		if state > 0 {
			db.Where("state = ?", state)
		}
		if len(startDate) > 0 && len(endDate) > 0 {
			db.Where("created_at between ? and ?", startDate+" 00:00:00", endDate+" 23:59:59")
		}
		return db
	}
}

func CityAreaDate(cityID int, areaID int, startDate string, endDate string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if cityID > 0 {
			db.Where("city_id = ?", cityID)
		}

		if areaID > 0 {
			db.Where("area_id = ?", areaID)
		}
		if len(startDate) > 0 && len(endDate) > 0 {
			db.Where("created_at between ? and ?", startDate+" 00:00:00", endDate+" 23:59:59")
		}
		return db
	}
}
