package inits

import (
	"flag"
	"os"

	rmq_client "github.com/apache/rocketmq-clients/golang"
)

var ConfigFilePath string
var RuntimeType string
var Action string

// init
//
//	@Description: 项目初始化时，第一个调用
//	@author: Ali<PERSON>jan
//	@Time: 2023-05-24 11:19:17
func init() {
	flag.StringVar(&ConfigFilePath, "configPath", ".", "config文件在哪个目录")
	flag.StringVar(&RuntimeType, "runtimeType", "web", "web 是http服务,order_first_push 是第一次插入到base table并推送给用户\n order_second_push:根据插入到的数据再次推送")
	flag.StringVar(&Action, "action", "", "启动canal 服务时，可以制定action,\nupdate_import_all_t_order 表示更新所有的t_order表,\nupdate_import_all_t_order_today 表示更新所有的t_order_today表,\nstart_observe_order 表示开始观察者")
	flag.Parse()	
	// ConfigFilePath = "d:/GoProjects/mulazim-api-go-copy"
	// RuntimeType = "web"
	os.Setenv("mq.consoleAppender.enabled", "true")
	os.Setenv("rocketmq.client.logLevel", "warn")
	rmq_client.ResetLogger()
}
