package transformers

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources"
	merchantResources "mulazim-api/resources/merchant"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type FoodSpecTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func NewFoodSpecTransformer(c *gin.Context) *FoodSpecTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	chatTransformer := FoodSpecTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &chatTransformer
}


// 描述：格式化CMS后台了详情的订单状态和用户相关的信息
// 作者：Rixat
// 文件：FoodSpecTransformer.go
// 修改时间：2025/04/15 17:01
func (t *FoodSpecTransformer) FormatFoodSpecList(specList []models.FoodSpecType) []resources.FoodSpecTypeRes {
	specRes := []resources.FoodSpecTypeRes{}
	for _, spec := range specList {
		// 规格项
		specOptions := []resources.FoodSpecTypeOptions{}
		for _, option := range spec.FoodSpecOptions {
			specOptions = append(specOptions, resources.FoodSpecTypeOptions{
				ID: option.ID,
				Name: tools.If(t.language == "zh", option.NameZh, option.NameUg),
				NameUg: option.NameUg,
				NameZh: option.NameZh,
				Price: option.Price,
				State: option.State,
				IsSelected: option.IsSelected,
				SpecTypeID: spec.ID,
			})
		}
		specRes = append(specRes, resources.FoodSpecTypeRes{
			ID: spec.ID,
			Name: tools.If(t.language == "zh", spec.NameZh, spec.NameUg),
			NameUg: spec.NameUg,
			NameZh: spec.NameZh,
			State: spec.State,
			PriceType: spec.PriceType,
			FoodID: spec.FoodID,
			Options: specOptions,
		})
	}
	return specRes
}

// 格式化套餐子美食
func (trns *FoodSpecTransformer) GetComboItems(comboFoodItems []models.FoodsComboItem) []merchantResources.OrderFoodComboItems {
	if comboFoodItems == nil || len(comboFoodItems) <= 0 {
		return nil
	}

	_comboItems := make([]merchantResources.OrderFoodComboItems, 0)
	for _, item := range comboFoodItems {
		// 处理子美食的规格选项
		var selectedSpec *merchantResources.OrderRestaurantFoodSpec = nil
		if item.FoodType == models.FoodsComboItemFoodTypeSpec && item.SelectedSpec != nil {
			var specOptions []merchantResources.OrderRestaurantFoodSpecOption
			// 从数据库中获取规格信息
			for _, option := range item.SelectedSpec.FoodSpecOptions {
				specOption := merchantResources.OrderRestaurantFoodSpecOption{
					ID:    option.ID,
					Name:  tools.If(trns.language == "zh", option.NameZh, option.NameUg),
					Price: option.Price,
					// 订单暂时不需要规格项分组
					//SpecOptionType: merchantResources.OrderRestaurantFoodSpecType{
					//	ID:        option.FoodSpecType.ID,
					//	Name:      tools.If(trns.language == "zh", option.FoodSpecType.NameZh, option.FoodSpecType.NameUg),
					//	PriceType: option.FoodSpecType.PriceType,
					//},
				}
				specOptions = append(specOptions, specOption)
			}

			selectedSpec = &merchantResources.OrderRestaurantFoodSpec{
				ID:          item.SelectedSpec.ID,
				SpecOptions: specOptions,
			}
		}

		// 处理子美食信息
		restaurantFood := merchantResources.OrderRestaurantFoodsResponse{
			ID:               item.RestaurantFood.ID,
			Name:             tools.GetNameByLang(item.RestaurantFood, trns.language),
			Image:            tools.CdnUrl(item.RestaurantFood.Image),
			Description:      tools.If(trns.language == "zh", item.RestaurantFood.DescriptionZh, item.RestaurantFood.DescriptionUg),
			StarAvg:          item.RestaurantFood.StarAvg,
			Price:            item.RestaurantFood.Price,
			FoodQuantity:     item.RestaurantFood.FoodQuantity,
			FoodQuantityType: item.RestaurantFood.FoodQuantityType,
			SelectedSpec:     selectedSpec,
		}

		comboItem := merchantResources.OrderFoodComboItems{
			ID:             item.ID,
			ComboID:        item.ComboID,
			FoodType:       item.FoodType,
			FoodID:         item.FoodID,
			Count:          item.Count,
			RestaurantFood: restaurantFood,
		}
		_comboItems = append(_comboItems, comboItem)
	}
	return _comboItems
}

// 格式化已选规格
func (trns *FoodSpecTransformer) GetSelectedSpec(selectedSpec *models.FoodSpec) *merchantResources.OrderRestaurantFoodSpec {
	if selectedSpec == nil || selectedSpec.ID == 0 {
		return nil
	}

	// 如果是规格美食
	var specOptions []merchantResources.OrderRestaurantFoodSpecOption
	// 从数据库中获取规格信息
	for _, option := range selectedSpec.FoodSpecOptions {
		specOption := merchantResources.OrderRestaurantFoodSpecOption{
			ID:    option.ID,
			Name:  tools.If(trns.language == "zh", option.NameZh, option.NameUg),
			Price: option.Price,
			// 订单暂时不需要规格项分组
			//SpecOptionType: merchantResources.RestaurantFoodSpecType{
			//	ID:        option.FoodSpecType.ID,
			//	Name:      tools.If(trns.language == "zh", option.FoodSpecType.NameZh, option.FoodSpecType.NameUg),
			//	PriceType: option.FoodSpecType.PriceType,
			//},
		}
		specOptions = append(specOptions, specOption)
	}

	_selectedSpec := &merchantResources.OrderRestaurantFoodSpec{
		ID:          selectedSpec.ID,
		SpecOptions: specOptions,
	}
	return _selectedSpec
}