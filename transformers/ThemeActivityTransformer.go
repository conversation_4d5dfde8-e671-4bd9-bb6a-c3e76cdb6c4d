package transformers

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
)

type ThemeActivityTransformer struct {
	ID                    int    `json:"id"`
	Name                  string `json:"name"` 
	NameUg                string `json:"name_ug"`
	NameZh                string `json:"name_zh"`
	Desc                  string `json:"desc"`
	DescUg                string `json:"desc_ug"`
	DescZh                string `json:"desc_zh"`
	CreatorName           string `json:"creator_name"`
	BeginTime             string `json:"begin_time"`
	EndTime               string `json:"end_time"`
	State                 int    `json:"state"`
	DiscountPercent       int    `json:"discount_percent"`
	CoverUg               string `json:"cover_ug"`
	CoverZh               string `json:"cover_zh"`
	OriginalCoverUg       string `json:"original_cover_ug"`
	OriginalCoverZh       string `json:"original_cover_zh"`
	Color                 string `json:"color"`
	OrderType             string `json:"order_type"`
	ShowPreferential      int    `json:"show_preferential"`
	ShowSeckill           int    `json:"show_seckill"`
	ShowFoodType          int    `json:"show_food_type"`
	HasKeyword            int    `json:"has_keyword"`
	KeywordUg             string `json:"keyword_ug"`
	KeywordZh             string `json:"keyword_zh"`
	ExcludeKeywordUG      string `json:"exclude_keyword_ug"`
	ExcludeKeywordZH      string `json:"exclude_keyword_zh"`
	ShareContentUg        string `json:"share_content_ug"`
	ShareContentZh        string `json:"share_content_zh"`
	ShareCoverUg          string `json:"share_cover_ug"`
	ShareCoverZh          string `json:"share_cover_zh"`
	ShareCoverToFriendUg  string `json:"share_cover_to_friend_ug"`
	ShareCoverToFriendZh  string `json:"share_cover_to_friend_zh"`
	OriginalShareCoverUg  string `json:"original_share_cover_ug"`
	OriginalShareCoverZh  string `json:"original_share_cover_zh"`
	OriginalShareCoverToFriendUg  string `json:"original_share_cover_to_friend_ug"`
	OriginalShareCoverToFriendZh  string `json:"original_share_cover_to_friend_zh"`
	CreatedAt             string `json:"created_at"`
	UpdatedAt             string `json:"updated_at"`
}

func NewThemeActivityTransformer(activity models.ThemeActivity, langUtil *lang.LangUtil) ThemeActivityTransformer {
	transformer := ThemeActivityTransformer{
		ID:                    activity.ID,
		NameUg:                activity.NameUg,
		NameZh:                activity.NameZh,
		DescUg:                activity.DescUg,
		DescZh:                activity.DescZh,
		DiscountPercent:       activity.DiscountPercent,
		CoverUg:               tools.AddCdn(activity.CoverUg),
		CoverZh:               tools.AddCdn(activity.CoverZh),
		OriginalCoverUg:       activity.CoverUg,
		OriginalCoverZh:       activity.CoverZh,
		Color:                 activity.Color,
		State:                 activity.State,
		OrderType:             activity.OrderType,
		ShowPreferential:      activity.ShowPreferential,
		ShowSeckill:           activity.ShowSeckill,
		ShowFoodType:          activity.ShowFoodType,
		HasKeyword:            activity.HasKeyword,
		KeywordUg:             activity.KeywordUg,
		KeywordZh:             activity.KeywordZh,
		ExcludeKeywordUG:      activity.ExcludeKeywordUG,
		ExcludeKeywordZH:      activity.ExcludeKeywordZH,
		ShareContentUg:        activity.ShareContentUg,
		ShareContentZh:        activity.ShareContentZh,
		ShareCoverUg:          tools.AddCdn(activity.ShareCoverUg),
		ShareCoverZh:          tools.AddCdn(activity.ShareCoverZh),
		ShareCoverToFriendUg:  tools.AddCdn(activity.ShareCoverToFriendUg),
		ShareCoverToFriendZh:  tools.AddCdn(activity.ShareCoverToFriendZh),
		OriginalShareCoverUg:  activity.ShareCoverUg,
		OriginalShareCoverZh:  activity.ShareCoverZh,
		OriginalShareCoverToFriendUg:  activity.ShareCoverToFriendUg,
		OriginalShareCoverToFriendZh:  activity.ShareCoverToFriendZh,
	}

	if activity.BeginTime != nil {
		transformer.BeginTime = activity.BeginTime.Format("2006-01-02 15:04:05")
	}
	if activity.EndTime != nil {
		transformer.EndTime = activity.EndTime.Format("2006-01-02 15:04:05")
	}
	if activity.CreatedAt != nil {
		transformer.CreatedAt = activity.CreatedAt.Format("2006-01-02 15:04:05")
	}
	if activity.UpdatedAt != nil {
		transformer.UpdatedAt = activity.UpdatedAt.Format("2006-01-02 15:04:05")
	}

	// 根据语言设置名称和描述
	if langUtil.Lang == "zh" {
		transformer.Name = activity.NameZh
		transformer.Desc = activity.DescZh
	} else {
		transformer.Name = activity.NameUg
		transformer.Desc = activity.DescUg
	}

	if activity.Creator != nil {
		transformer.CreatorName = activity.Creator.RealName
	}

	return transformer
}
