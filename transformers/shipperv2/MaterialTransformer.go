package shipperv2

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources/shipper"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type MaterialTransformer struct {
	langUtil *lang.LangUtil
	language string
}
//
// GetCustomerStatistic
//  @Description: 获取客户统计 格式化返回数据
//  @receiver t
//  @param data
//  @param totalCustomerCount
//  @param totalOrderCount
//  @param categoryName
//  @param categoryId
//  @return shipper.MaterialCustomerListEntity
//
func (t MaterialTransformer) GetCustomerStatistic(data []models.AdvertMaterialShipperUserMonthStatistic, totalCustomerCount int64, totalOrderCount int64, categoryName string, categoryId int,sortColumn string,sortType string) shipper.MaterialCustomerListEntity {
	rtn := shipper.MaterialCustomerListEntity{}
	rtn.Header.TotalCustomerCount = totalCustomerCount
	rtn.Header.TotalOrderCount = totalOrderCount
	rtn.CategoryName = categoryName
	rtn.CategoryID = int64(categoryId)
	rtn.SortColumn = sortColumn
	rtn.SortType = sortType

	rtn.CustomerList = []shipper.CustomerList{}
	for _, v := range data {
		userName := tools.FilterEmoji(v.User.Name)
		if userName == "" {
			userName = "微信用户"
		}
		userAvatar := v.User.Avatar
		if userAvatar == "" {
			userAvatar = tools.GetDefaultUserImage()
		}
		rtn.CustomerList = append(rtn.CustomerList, shipper.CustomerList{
			ID:         v.UserId,
			Name:   userName,
			Img: 	  tools.AddCdn(userAvatar),
			OrderCount: v.OrderCount,
			OrderPrice: v.TotalOrderPrice,
			UserProfit: v.InviteUserFee+v.TotalOrderTipsFee,
			InviteTime: v.AdvertMaterialShipperUser.CreatedAt.Format("2006-01-02 15:04:05"),
			MaterialName: tools.If(t.language == "ug",v.AdvertMaterialCategory.NameUg,v.AdvertMaterialCategory.NameZh),
		})
	}
	return rtn
}
//
// NewMaterialTransformer
//  @Description: 宣传材料
//  @param c
//  @return *ShipperTransformer
//
func NewMaterialTransformer(c *gin.Context) *MaterialTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	shipperTransformer := MaterialTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &shipperTransformer
}
// 宣传材料 总统计
func (t MaterialTransformer) GetMaterialStatistic(data []models.AdvertMaterialShipperDailyStatisticPage,dataDiagram []models.AdvertMaterialShipperDailyStatisticPage, totalCustomerCount int64, totalOrderCount int64, totalOrderPrice int64,totalOrderTipsFee int64) shipper.MaterialStatisticListEntity {
	rtn := shipper.MaterialStatisticListEntity{}
	rtn.Header.TotalCustomerCount = totalCustomerCount
	rtn.Header.TotalOrderCount = totalOrderCount
	rtn.Header.TotalOrderPrice = totalOrderPrice
	rtn.Header.TotalOrderTipsFee = totalOrderTipsFee
	rtn.Diagram =[]shipper.MaterialStatisticListDiagram{}
	
	rtn.StatisticsMaterialList = []shipper.StatisticsMaterialList{}
	for _, v := range data {
		rtn.StatisticsMaterialList = append(rtn.StatisticsMaterialList, shipper.StatisticsMaterialList{
			Name:   v.AdvertMaterialCategory.NameZh,
			OrderCount: v.OrderCount,
			UserCount:v.InviteUserCount,
			OrderPrice: int64(v.OrderPrice),
		})
	}
	for _, v := range dataDiagram {
		rtn.Diagram = append(rtn.Diagram, shipper.MaterialStatisticListDiagram{
			Day:       v.Date,
			Total:    v.TotalOrderTipsFee,
		})
	}
	return rtn
}