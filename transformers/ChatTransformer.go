package transformers

import (
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models/chat"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ChatTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func NewChatTransformer(c *gin.Context) *ChatTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	chatTransformer := ChatTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &chatTransformer
}

// 描述：格式化CMS后台了详情的订单状态和用户相关的信息
// 作者：Qurbanjan
// 文件：ChatTransformer.go
// 修改时间：2023/11/17 17:01
func (t ChatTransformer) FormatChatDetailOrderInfo(info chat.OrderInfo) map[string]interface{} {
	orderInfo := make(map[string]interface{}, 0)
	orderInfo["order_id"] = info.OrderId
	orderInfo["restaurant_name"] = info.RestaurantName
	orderInfo["restaurant_address"] = info.RestaurantAddress
	orderInfo["restaurant_mobile"] = info.RestaurantMobile
	orderInfo["user_address"] = info.AreaName +" "+ info.StreetName +" "+ info.BuildingName
	orderInfo["user_name"] = info.UserName
	orderInfo["user_mobile"] = info.UserMobile
	orderInfo["taked"] = info.Taked
	orderInfo["created_at"] = info.CreatedAt
	orderInfo["booking_time"] = info.BookingTime
	orderInfo["delivery_start_time"]=info.DeliveryStartTime
	orderInfo["delivery_taked_time"]=info.DeliveryTakedTime
	orderInfo["delivery_end_time"]=info.DeliveryEndTime
	orderInfo["shipper_arrived_at_shop"]=info.ShipperArrivedAtShop
	orderInfo["shipper_take_food_at"]=info.ShipperTakeFoodAt
	orderInfo["description"]=info.Description

	foods := make([]map[string]interface{}, 0)
	for _, value := range info.Foods {
		food := make(map[string]interface{})
		food["foods_name"] = value.FoodName
		food["price"] = float64(value.Price) / 100
		food["number"] = value.Number
		food["lunch_box_fee"] = float64(value.LunchBoxFee) / 100
		food["lunch_box_count"] = value.LunchBoxCount
		food["store_foods_id"] = value.StoreFoodsId
		foods = append(foods, food)
	}
	orderInfo["foods"] = foods
	orderInfo["shipper_name"] = info.Shipper.ShipperName
	orderInfo["shipper_mobile"] = info.Shipper.ShipperMobile

	// 格式化OrderStateLog
	orderInfo["order_log"] = FormatOrderLog(info.OrderLog)

	return orderInfo
}

// orderStateLog 转换成我们想要的格式
func FormatOrderLog(orderLog []chat.OrderLog) map[string]string {
	orderLogMap := make(map[string]string)
	for _, log := range orderLog {
		createdAt :=carbon.Parse(log.CreatedAt,configs.AsiaShanghai).Format("Y-m-d H:i:s")
		switch int(log.OrderStateID) {
		case 1:
			orderLogMap["booking_time"] = createdAt
		case 2:
			orderLogMap["delivery_time"] = createdAt
		case 3:
			orderLogMap["order_taked_time"] = createdAt
		case 4:
			orderLogMap["order_confirm_time"] = createdAt
		case 5:
			orderLogMap["order_ready_time"] = createdAt
		}
	}

	// 检查并填充空字符串
	if _, ok := orderLogMap["booking_time"]; !ok {
		orderLogMap["booking_time"] = ""
	}
	if _, ok := orderLogMap["delivery_time"]; !ok {
		orderLogMap["delivery_time"] = ""
	}
	if _, ok := orderLogMap["order_taked_time"]; !ok {
		orderLogMap["order_taked_time"] = ""
	}
	if _, ok := orderLogMap["other_confirm_time"]; !ok {
		orderLogMap["other_confirm_time"] = ""
	}
	if _, ok := orderLogMap["other_ready_time"]; !ok {
		orderLogMap["other_ready_time"] = ""
	}
	return orderLogMap
}
