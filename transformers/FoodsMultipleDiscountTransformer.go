package transformers

import (
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/requests/foodsMultipleDiscount"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
)

// FoodsMultipleDiscountTransformer 多份打折活动服务
type FoodsMultipleDiscountTransformer struct {
	langUtil *lang.LangUtil
	language string
}

// NewFoodsMultipleDiscountTransformer 创建多份打折活动服务
func NewFoodsMultipleDiscountTransformer(c *gin.Context) *FoodsMultipleDiscountTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := FoodsMultipleDiscountTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &service
}

// ListFormat 转换多份打折活动列表 (cms)
func (t *FoodsMultipleDiscountTransformer) FormatCmsFoodsMultipleDiscountList(list []models.FoodsMultipleDiscount) []foodsMultipleDiscount.FoodsMultipleDiscountCmsResponse {
	responses := make([]foodsMultipleDiscount.FoodsMultipleDiscountCmsResponse, 0)
	for _, item := range list {
		response := foodsMultipleDiscount.FoodsMultipleDiscountCmsResponse{
			ID:               item.ID,
			CityId:           item.CityId,
			AreaId:           item.AreaId,
			RestaurantId:     item.RestaurantId,
			FoodId:           item.FoodId,
			CityName:         tools.GetNameByLang(item.City, t.language),
			AreaName:         tools.GetNameByLang(item.Area, t.language),
			RestaurantName:   tools.GetNameByLang(item.Restaurant, t.language),
			FoodName:         tools.GetNameByLang(item.Food, t.language),
			OriginalPrice:    tools.ToPrice(tools.ToFloat64(item.Food.Price) / 100),
			Discount2Percent: tools.ToString(item.Discount2Percent)+"%",
			Discount3Percent: tools.ToString(item.Discount3Percent)+"%",
			Discount4Percent: tools.ToString(item.Discount4Percent)+"%",
			Discount5Percent: tools.ToString(item.Discount5Percent)+"%",
			StartTime:        item.StartTime.Format(time.DateTime),
			EndTime:          item.EndTime.Format(time.DateTime),
			CreatorId:        item.CreatorId,
			CreatorName:      item.Creator.Name,
			State:            item.State,
			StateName:        t.langUtil.TArr("foods_multiple_discount_state_names")[item.State],
			CreatedAt:        item.CreatedAt.Format(time.DateTime),
			UpdatedAt:        item.UpdatedAt.Format(time.DateTime),
		};
		if item.IsEnd() {
			response.State = models.FoodsMultipleDiscountStateExpired
		}else {
			response.State = item.State
		}

		response.StateName = t.langUtil.TArr("foods_multiple_discount_state_names")[response.State]
		responses = append(responses, response)
	}
	return responses
}

// DetailFormat 转换多份打折活动详情 (cms)
func (t *FoodsMultipleDiscountTransformer) FormatCmsFoodsMultipleDiscountDetail(detail models.FoodsMultipleDiscount) foodsMultipleDiscount.FoodsMultipleDiscountDetailCmsResponse {

	return foodsMultipleDiscount.FoodsMultipleDiscountDetailCmsResponse{
		ID: detail.ID,
		Food: foodsMultipleDiscount.FoodsMultipleDiscountDetailFoodCmsResponse{
			ID:             detail.Food.ID,
			FoodName:       tools.GetNameByLang(detail.Food, t.language),
			OriginalPrice:  tools.ToFloat64(detail.Food.Price),
			RestaurantName: tools.GetNameByLang(detail.Restaurant, t.language),
			FoodImage:      tools.AddCdn(detail.Food.Image),
		},
		Discount2Percent: float64(detail.Discount2Percent),
		Discount3Percent: float64(detail.Discount3Percent),
		Discount4Percent: float64(detail.Discount4Percent),
		Discount5Percent: float64(detail.Discount5Percent),
		StartTime:        detail.StartTime.Format(time.DateTime),
		EndTime:          detail.EndTime.Format(time.DateTime),
	}
}



// FormatMerchantFoodsMultipleDiscountList 转换多份打折活动列表 (商家)
func (t FoodsMultipleDiscountTransformer) FormatMerchantFoodsMultipleDiscountList(list []models.FoodsMultipleDiscount) []foodsMultipleDiscount.FoodsMultipleDiscountListMerchantResponse {
	var items []foodsMultipleDiscount.FoodsMultipleDiscountListMerchantResponse
	for _, discount := range list {
		var item foodsMultipleDiscount.FoodsMultipleDiscountListMerchantResponse
		item.ID = discount.ID
		item.Name = tools.GetNameByLang(discount, t.language)
		item.StartTime = discount.StartTime.Format(time.DateTime)
		item.EndTime = discount.EndTime.Format(time.DateTime)
		item.FoodImage = tools.AddCdn(discount.Food.Image)
		if discount.IsEnd() {
			item.State = models.FoodsMultipleDiscountStateExpired
		}else {
			item.State = discount.State
		}

		item.StateName = t.langUtil.TArr("foods_multiple_discount_state_names")[item.State]
		item.CreatorName = discount.Creator.Name
		items = append(items, item)
	}
	if items == nil {
		items = []foodsMultipleDiscount.FoodsMultipleDiscountListMerchantResponse{}
	}
	return items
}

// FormatMerchantFoodsMultipleDiscountDetail 转换多份打折活动详情 (商家)
func (t FoodsMultipleDiscountTransformer) FormatMerchantFoodsMultipleDiscountDetail(detail models.FoodsMultipleDiscountDetailQuery, hasOrder bool) foodsMultipleDiscount.FoodsMultipleDiscountDetailMerchantResponse {
	// 将详情列表预处理成map
	detailMap := make(map[int]int64)
	for _, d := range detail.Details {
		detailMap[int(d.DiscountIndex)] = d.DiscountPrice
	}
	
	// 使用map获取价格
	getPrice := func(index int) int64 {
		if price, ok := detailMap[index]; ok {
			return price
		}
		return 0
	}

	item := foodsMultipleDiscount.FoodsMultipleDiscountDetailMerchantResponse{
		ID:                   detail.ID,
		Name:                 tools.GetNameByLang(detail, t.language),
		Price:                tools.ToPrice(tools.ToFloat64(detail.Food.Price) / 100),
		Discount1Percent:     0,
		Discount1SaledAmount: tools.ToPrice(tools.ToFloat64(detail.Discount1SaledAmount) / 100),
		Discount1SaledCount:  tools.ToString(detail.Discount1SaledCount),
		Discount1Price:       tools.ToPrice(tools.ToFloat64(getPrice(1)) / 100),

		Discount2Percent:     detail.Discount2Percent,
		Discount2SaledAmount: tools.ToPrice(tools.ToFloat64(detail.Discount2SaledAmount) / 100),
		Discount2SaledCount:  tools.ToString(detail.Discount2SaledCount),
		Discount2Price:       tools.ToPrice(tools.ToFloat64(getPrice(2)) / 100),


		Discount3Percent:     detail.Discount3Percent,
		Discount3SaledAmount: tools.ToPrice(tools.ToFloat64(detail.Discount3SaledAmount) / 100),
		Discount3SaledCount:  tools.ToString(detail.Discount3SaledCount),
		Discount3Price:       tools.ToPrice(tools.ToFloat64(getPrice(3)) / 100),


		Discount4Percent:     detail.Discount4Percent,
		Discount4SaledAmount: tools.ToPrice(tools.ToFloat64(detail.Discount4SaledAmount) / 100),
		Discount4SaledCount:  tools.ToString(detail.Discount4SaledCount),
		Discount4Price:       tools.ToPrice(tools.ToFloat64(getPrice(4)) / 100),


		Discount5Percent:     detail.Discount5Percent,
		Discount5SaledAmount: tools.ToPrice(tools.ToFloat64(detail.Discount5SaledAmount) / 100),
		Discount5SaledCount:  tools.ToString(detail.Discount5SaledCount),
		Discount5Price:       tools.ToPrice(tools.ToFloat64(getPrice(5)) / 100),


		TotalSaledCount:  tools.ToString(detail.TotalSaledCount),
		TotalSaledAmount: tools.ToPrice(tools.ToFloat64(detail.TotalSaledAmount) / 100),

		CreatorName: detail.Creator.Name,
		CreatedAt:   detail.CreatedAt.Format(time.DateTime),
		StartTime:   detail.StartTime.Format(time.DateTime),
		EndTime:     detail.EndTime.Format(time.DateTime),

		State:            detail.State,
		StateName:        t.langUtil.TArr("foods_multiple_discount_state_names")[detail.State],

		FoodImage: tools.AddCdn(detail.Food.Image),
		FoodID: detail.Food.ID,
		CanDelete: !hasOrder,
	}
	return item

}


// FormatMerchantFoodsMultipleDiscountDetail 转换多份打折活动详情 (商家)
func (t FoodsMultipleDiscountTransformer) FormatMerchantFoodsMultipleDiscountDetailView(detail models.FoodsMultipleDiscountDetailQuery) foodsMultipleDiscount.FoodsMultipleDiscountDetailViewResponse {
	// 将详情列表预处理成map
	detailMap := make(map[int]int64)
	for _, d := range detail.Details {
		detailMap[int(d.DiscountIndex)] = d.DiscountPrice
	}
	
	// 使用map获取价格
	getPrice := func(index int) int64 {
		if price, ok := detailMap[index]; ok {
			return price
		}
		return 0
	}

	item := foodsMultipleDiscount.FoodsMultipleDiscountDetailViewResponse{
		ID:                   detail.ID,
		CityName:             tools.GetNameByLang(detail.City, t.language),
		AreaName:             tools.GetNameByLang(detail.Area, t.language),
		RestaurantName:       tools.GetNameByLang(detail.Restaurant, t.language),
		FoodName:                 tools.GetNameByLang(detail, t.language),
		Price:                tools.ToPrice(tools.ToFloat64(detail.Food.Price) / 100),
		Discount1Percent:     "0%",
		Discount1SaledAmount: tools.ToPrice(tools.ToFloat64(detail.Discount1SaledAmount) / 100),
		Discount1SaledCount:  tools.ToString(detail.Discount1SaledCount),
		Discount1Price:       tools.ToPrice(tools.ToFloat64(getPrice(1)) / 100),

		Discount2Percent:     fmt.Sprintf("%d%%", int(detail.Discount2Percent)),
		Discount2SaledAmount: tools.ToPrice(tools.ToFloat64(detail.Discount2SaledAmount) / 100),
		Discount2SaledCount:  tools.ToString(detail.Discount2SaledCount),
		Discount2Price:       tools.ToPrice(tools.ToFloat64(getPrice(2)) / 100),


		Discount3Percent:     fmt.Sprintf("%d%%", int(detail.Discount3Percent)),
		Discount3SaledAmount: tools.ToPrice(tools.ToFloat64(detail.Discount3SaledAmount) / 100),
		Discount3SaledCount:  tools.ToString(detail.Discount3SaledCount),
		Discount3Price:       tools.ToPrice(tools.ToFloat64(getPrice(3)) / 100),


		Discount4Percent:     fmt.Sprintf("%d%%", int(detail.Discount4Percent)),
		Discount4SaledAmount: tools.ToPrice(tools.ToFloat64(detail.Discount4SaledAmount) / 100),
		Discount4SaledCount:  tools.ToString(detail.Discount4SaledCount),
		Discount4Price:       tools.ToPrice(tools.ToFloat64(getPrice(4)) / 100),


		Discount5Percent:     fmt.Sprintf("%d%%", int(detail.Discount5Percent)),
		Discount5SaledAmount: tools.ToPrice(tools.ToFloat64(detail.Discount5SaledAmount) / 100),
		Discount5SaledCount:  tools.ToString(detail.Discount5SaledCount),
		Discount5Price:       tools.ToPrice(tools.ToFloat64(getPrice(5)) / 100),


		TotalSaledCount:  tools.ToString(detail.TotalSaledCount),
		TotalSaledAmount: tools.ToPrice(tools.ToFloat64(detail.TotalSaledAmount) / 100),

		CreatorName: detail.Creator.Name,
		CreatedAt:   detail.CreatedAt.Format(time.DateTime),
		StartTime:   detail.StartTime.Format(time.DateTime),
		EndTime:     detail.EndTime.Format(time.DateTime),

		State:            detail.State,
		StateName:        t.langUtil.TArr("foods_multiple_discount_state_names")[detail.State],

		FoodImage: tools.AddCdn(detail.Food.Image),
		FoodID: detail.Food.ID,
	}
	return item

}


func (t FoodsMultipleDiscountTransformer) FormatMerchantFoodsMultipleDiscountOrderList(list []models.FoodsMultipleDiscountOrderLog) []foodsMultipleDiscount.FoodsMultipleDiscountOrderList {
	var items []foodsMultipleDiscount.FoodsMultipleDiscountOrderList
	for _, item := range list {
		if item.OrderToday != nil {
			items = append(items, foodsMultipleDiscount.FoodsMultipleDiscountOrderList{
				ID: item.OrderId,
				OrderNo: item.OrderToday.OrderID,
				UserName: item.OrderToday.User.Name,
				UserMobile: item.OrderToday.User.Mobile,
				Count: item.Count,
				TotalPrice: tools.ToInt64(item.OrderToday.OrderPrice),
				TotalDiscountPrice: item.TotalOriginalPrice - item.TotalPrice,
				State: item.OrderToday.State,
				StateName: t.langUtil.TArr("order_state")[item.OrderToday.State],
				CreatedAt: tools.TimeFormatYmdHis(&item.OrderToday.CreatedAt),
			})
		}
		if item.Order != nil {
			items = append(items, foodsMultipleDiscount.FoodsMultipleDiscountOrderList{
				ID: item.OrderId,
				OrderNo: item.Order.OrderID,
				UserName: item.Order.User.Name,
				UserMobile: item.Order.User.Mobile,
				Count: item.Count,
				TotalPrice: tools.ToInt64(item.Order.OrderPrice),
				TotalDiscountPrice: item.TotalOriginalPrice - item.TotalPrice,
				State: item.Order.State,
				StateName: t.langUtil.TArr("order_state")[item.Order.State],
				CreatedAt: tools.TimeFormatYmdHis(&item.Order.CreatedAt),
			})
		}
	}
	return items
}
