package shipper

import (
	"math"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	CommentsModel "mulazim-api/models/Comments"
	"mulazim-api/models/shipment"
	"mulazim-api/resources/shipper"
	shipperResources "mulazim-api/resources/shipper"
	"mulazim-api/resources/shipper/Comments"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ShipperTransformer struct {
	langUtil *lang.LangUtil
	language string
}

// isPushedShipper
//
//	@Description: 判断这个配送员是否被推送区域的
//	@author: Alimjan
//	@Time: 2023-08-31 13:08:53
//	@receiver t ShipperTransformer
//	@param list []models.OrderToday
//	@return bool
func (t ShipperTransformer) isPushedShipper(list []models.OrderToday) bool {
	if len(list) == 0 {
		return false
	}
	if configs.MyApp.ShipperPushAll == 1 {
		return true
	}
	areaId := list[0].AreaID
	return tools.InArray(areaId, configs.MyApp.ShipperPushArea)
}

// FormatOrderList
//
//	@Description: 格式化新订单列表
//	@author: Alimjan
//	@Time: 2022-09-12 18:42:13
//	@receiver t ShipperTransformer
//	@param list []models.OrderToday
//	@param count map[string]int64
//	@param streetId string
//	@param admin models.Admin
//	@return shipper.NewOrderData
func (t ShipperTransformer) FormatOrderList(list []models.OrderToday, count map[string]int64, streetId string, admin models.Admin) shipper.NewOrderData {
	newOrderData := shipper.NewOrderData{}
	if streetId == "" {
		newOrderData.Count.NewList = count["new_list"]
		newOrderData.Count.Back = count["back"]
		newOrderData.Count.MyOrder = count["myOrder"]
		newOrderData.Count.Failed = count["failed"]
		newOrderData.Count.Succeed = count["succeed"]
		newOrderData.Count.CommentCount = count["comment_count"]
	} else {
		newOrderData.OrderCount = len(list)
	}
	newOrderData.Items = []shipper.NewOrderItems{}
	//判断是否是推送区域
	isPushedAreaShipper := t.isPushedShipper(list)
	// 该配送员收到的推送订单id集合
	pushedOrderIds := t.pushedOrderIds(admin)
	//tools.Logger.Info("配送员id:"+tools.ToString(admin.ID)+",被推送的订单IDS",pushedOrderIds)
	for i := 0; i < len(list); i++ {
		old := list[i]

		//满足 条件 (是推送区域的配送员 和 不在 该配送员收到的推送订单id集合里面 和 普通配送员)  的配送员看不到该订单 因为 还没开始推送,推送给该配送员后才显示
		if isPushedAreaShipper && (!tools.InArray(old.ID, pushedOrderIds)) && admin.Type == 9 {
			//tools.Logger.Info("不显示这个订单，还没开始推送:配送员ID:",admin.ID," ,订单ID:",old.ID)
			continue
		}
		item := shipper.NewOrderItems{
			ID:           old.ID,
			StoreID:      old.StoreID,
			Distance:     old.Distance / 1000,
			OrderAddress: old.OrderAddress,
			OrderType:    old.OrderType,
			StoreName:    old.Restaurant.Name,
			StoreAddress: old.Restaurant.Address,
			OrderState:   old.State,
			SerialNumber: int(old.SerialNumber),
			Timezone:     old.Timezone,
			CityID:       old.CityID,
			CityName:     old.AddressView.CityName,
			AreaID:       old.AreaID,
			AreaName:     old.AddressView.AreaName,
			StreetID:     int(old.AddressView.StreetId),
			StreetName:   old.AddressView.StreetName,
			BuildingID:   int(old.AddressView.BuildingId),
			BuildingName: old.AddressView.BuildingName,
		}
		item.BookingTime = carbon.Parse(old.BookingTime).Format("H:i")
		item.BookingDateTime = carbon.Parse(old.BookingTime).Format("Y-m-d H:i:s")

		item.BookingRemainingMinute = int(carbon.Now().DiffInMinutes(carbon.Parse(item.BookingDateTime)))
		newOrderData.Items = append(newOrderData.Items, item)
	}
	return newOrderData
}

// FormatMyOrderList
//
//	@Description: 格式化我的订单和完成订单列表
//	@author: Alimjan
//	@Time: 2022-09-12 18:41:57
//	@receiver t ShipperTransformer
//	@param list []map[string]interface{}
//	@param count map[string]int64
//	@return interface{}
func (t ShipperTransformer) FormatMyOrderList(list []map[string]interface{}, count map[string]int64) interface{} {
	var myOrders shipper.MyOrderEntity
	myOrders.Count.NewList = count["new_list"]
	myOrders.Count.Back = count["back"]
	myOrders.Count.MyOrder = count["myOrder"]
	myOrders.Count.Failed = count["failed"]
	myOrders.Count.Succeed = count["succeed"]
	myOrders.Count.CommentCount = count["comment_count"]
	myOrders.Items = []shipper.MyOrderItems{}
	for _, dbItem := range list {
		item := shipper.MyOrderItems{
			ID:                     tools.ToInt64(dbItem["id"]),
			OrderID:                tools.ToInt64(dbItem["order_today_id"]),
			OrderAddress:           "",
			Timezone:               tools.ToInt64(dbItem["timezone"]),
			BookingRemainingMinute: 0,
			BookingTime:            "",
			BookingDateTime:        "",
			OrderState:             tools.ToInt64(dbItem["state"]),
			CityID:                 tools.ToInt64(dbItem["city_id"]),
			CityName:               dbItem["city_name"].(string),
			AreaID:                 tools.ToInt64(dbItem["area_id"]),
			AreaName:               dbItem["area_name"].(string),
			StreetID:               tools.ToInt64(dbItem["street_id"]),
			StreetName:             dbItem["street_name"].(string),
			BuildingID:             tools.ToInt64(dbItem["building_id"]),
			BuildingName:           dbItem["building_name"].(string),
			ConsumeType:            tools.ToInt64(dbItem["consume_type"]),
			Price:                  float64(tools.ToInt64(dbItem["price"])) / 100,
			Shipment:               float64(tools.ToInt64(dbItem["shipment"])) / 100,
			LunchBoxFee:            float64(tools.ToInt64(dbItem["lunch_box_fee"])) / 100,
			RealShipment:           float64(tools.ToInt64(dbItem["real_shipment"])) / 100,
			DeductionFee:           float64(tools.ToInt64(dbItem["deduction_fee"])) / 100,
			SerialNumber:           tools.ToInt64(dbItem["serial_number"]),
			OrderType:              tools.ToInt64(dbItem["order_type"]),
			StoreName:              dbItem["restaurant_name"].(string),
			StoreAddress:           dbItem["restaurant_address"].(string),
			Distance:               float64(tools.ToInt64(dbItem["distance"])) / 1000.0,
			Mobile:                 dbItem["mobile"].(string),
			Description:            dbItem["description"].(string),
			RestaurantLat:          float64(tools.ToFloat64(dbItem["res_lat"])),
			RestaurantLng:          float64(tools.ToFloat64(dbItem["res_lng"])),
			RestaurantTel:          dbItem["res_tel"].(string),
			CustomerLat:            float64(tools.ToFloat64(dbItem["building_lat"])),
			CustomerLng:            float64(tools.ToFloat64(dbItem["building_lng"])),
			Foods:                  dbItem["order_details"].([]map[string]interface{}),
			OrderIdLong:            dbItem["order_id_long"].(string),
			PayTypeState:           int(tools.ToInt64(dbItem["pay_type"])),
		}
		var bookingTime string
		bookingTime = dbItem["booking_time"].(time.Time).Format("2006-01-02 15:04:05")

		item.BookingTime = carbon.Parse(bookingTime).Format("H:i")
		item.BookingDateTime = carbon.Parse(bookingTime).Format("Y-m-d H:i:s")

		item.BookingRemainingMinute = int(carbon.Now().DiffInMinutes(carbon.Parse(item.BookingDateTime)))

		for i, foodsItem := range item.Foods {
			item.Foods[i]["food_name"] = foodsItem["food_name"]
			item.Foods[i]["lunch_box_fee"] = float64(tools.ToInt64(foodsItem["lunch_box_fee"])) / 100
			item.Foods[i]["price"] = float64(tools.ToInt64(foodsItem["price"])) / 100.0
		}

		myOrders.Items = append(myOrders.Items, item)
	}
	return myOrders
}

// FormatFailedList
//
//	@Description: 格式化失败订单列表API
//	@author: Alimjan
//	@Time: 2022-09-12 18:42:27
//	@receiver t ShipperTransformer
//	@param list []map[string]interface{}
//	@param count map[string]int64
//	@param categoryId int
//	@return interface{}
func (t ShipperTransformer) FormatFailedList(list []map[string]interface{}, count map[string]int64, categoryId int) interface{} {
	var failedEntity shipper.FailedOrderEntity
	failedEntity.Count.NewList = count["new_list"]
	failedEntity.Count.Back = count["back"]
	failedEntity.Count.MyOrder = count["myOrder"]
	failedEntity.Count.Failed = count["failed"]
	failedEntity.Count.Succeed = count["succeed"]
	failedEntity.Count.CommentCount = count["comment_count"]
	failedEntity.Failed = []shipper.FailedOrderFailed{}
	failedEntity.Back = []shipper.FailedOrderFailed{}
	for _, dbItem := range list {
		item := shipper.FailedOrderFailed{
			ID:                     tools.ToInt64(dbItem["id"]),
			OrderID:                tools.ToInt64(dbItem["order_today_id"]),
			OrderAddress:           "",
			Timezone:               tools.ToInt64(dbItem["timezone"]),
			BookingRemainingMinute: 0,
			BookingTime:            "",
			BookingDateTime:        "",
			OrderState:             tools.ToInt64(dbItem["state"]),
			CityID:                 tools.ToInt64(dbItem["city_id"]),
			CityName:               dbItem["city_name"].(string),
			AreaID:                 tools.ToInt64(dbItem["area_id"]),
			AreaName:               dbItem["area_name"].(string),
			StreetID:               tools.ToInt64(dbItem["street_id"]),
			StreetName:             dbItem["street_name"].(string),
			BuildingID:             tools.ToInt64(dbItem["building_id"]),
			BuildingName:           dbItem["building_name"].(string),
			SerialNumber:           tools.ToInt64(dbItem["serial_number"]),
			StoreName:              dbItem["restaurant_name"].(string),
			StoreAddress:           dbItem["restaurant_address"].(string),
			Distance:               float64(tools.ToInt64(dbItem["distance"])) / 1000.0,
			FialReason:             tools.ToString(dbItem["reason"]),
		}
		var bookingTime string
		bookingTime = dbItem["booking_time"].(time.Time).Format("2006-01-02 15:04:05")

		item.BookingTime = carbon.Parse(bookingTime).Format("H:i")
		item.BookingDateTime = carbon.Parse(bookingTime).Format("Y-m-d H:i:s")

		item.BookingRemainingMinute = int64(int(carbon.Now().DiffInMinutes(carbon.Parse(item.BookingDateTime))))

		if tools.ToInt64(dbItem["state"]) == 0 {
			failedEntity.Back = append(failedEntity.Back, item)
		} else {
			failedEntity.Failed = append(failedEntity.Failed, item)
		}

	}
	return failedEntity
}

// NewShipperTransformer
//
//	@Description: 初始化配送员Transformer
//	@return *ShipperTransformer
func NewShipperTransformer(c *gin.Context) *ShipperTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	shipperTransformer := ShipperTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &shipperTransformer
}

// 格式化配送员推荐用户统计

func (t ShipperTransformer) FormatRecommendList(list map[string]interface{}) shipper.RecommendListEntity {
	var users []models.User
	recommendList := shipper.RecommendListEntity{}
	recommendList.Count = list["count"].(int64)
	recommendList.Recommends = []shipper.Recommends{}
	users = list["recommends"].([]models.User)
	for i := 0; i < len(users); i++ {
		user := users[i]
		recommend := shipper.Recommends{
			Name:      user.Name,
			Mobile:    user.Mobile,
			CityName:  user.CityName,
			CreatedAt: user.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		recommendList.Recommends = append(recommendList.Recommends, recommend)
	}
	return recommendList
}

// GetTip
func (t ShipperTransformer) GetTip(tipsList []models.ShipperTips) shipper.TipsEntity {
	tipsEntity := shipper.TipsEntity{}
	for i := 0; i < len(tipsList); i++ {
		tip := tipsList[i]
		shipperTips := shipper.Tips{
			UserName:   tip.UserName,
			UserAvatar: tip.UserAvatar,
			Amount:     float32(tip.Amount) / 100,
			Id:         tip.ID,
			CreatedAt:  tip.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		tipsEntity.Tips = append(tipsEntity.Tips, shipperTips)
	}
	return tipsEntity
}
func (t ShipperTransformer) FormatCommentList(shipperInfo models.Admin, list map[string]interface{}, counts []map[string]interface{}) Comments.CommentListEntity {

	commentList := Comments.CommentListEntity{}
	//序列化配送员信息
	shipper := Comments.Shipper{}
	shipper.ID = shipperInfo.ID
	shipper.ShipperName = shipperInfo.RealName
	//shipper.ShipperAvatar = t.emptyStringToNull(shipperInfo.Avatar)
	shipper.ShipperMobile = shipperInfo.Mobile
	shipper.ShipperDistance = shipperInfo.ShipperDistance
	shipper.AreaName = shipperInfo.AreaName
	shipper.ShipperCustomerRate = shipperInfo.ShipperCustomerRate
	shipper.ShipperOnTimeDeliveryRate = shipperInfo.ShipperOnTimeDeliveryRate
	shipper.ShipperDeliveryAvgTime = shipperInfo.ShipperDeliveryAvgTime
	commentList.Shipper = shipper
	//序列化评论按类型统计数据
	var countItem Comments.Counts
	for i := 0; i < len(counts); i++ {
		countItem.NameUg = counts[i]["name_ug"].(string)
		countItem.NameZh = counts[i]["name_zh"].(string)
		countItem.Type = int(counts[i]["type"].(float64))
		countItem.Count = int(counts[i]["count"].(float64))
		commentList.Counts = append(commentList.Counts, countItem)
	}
	//序列化分页信息
	var page map[string]int
	page = list["page"].(map[string]int)
	commentList.Page = Comments.Page{
		PerPage:     page["per_page"],
		CurrentPage: page["current_page"],
		LastPage:    page["last_page"],
	}
	//
	var comments []CommentsModel.Comment
	comments = list["comments"].([]CommentsModel.Comment)
	for i := 0; i < len(comments); i++ {
		comment := comments[i]
		item := Comments.Items{
			ID:          comment.ID,
			Star:        comment.Star,
			UserName:    comment.UserName,
			Text:        comment.Text,
			UserAvatar:  comment.UserAvatar,
			IsAnonymous: comment.IsAnonymous,
			IsSatisfied: comment.IsSatisfied,
			CreatedAt:   comment.CreatedAt.Format("2006-01-02 15:04:05"),
			Replies:     t.formatCommentReply(comment),
		}
		if comment.IsAnonymous == 1 {
			if len(comment.UserName) > 0 {
				item.UserName = string([]rune(comment.UserName)[0:1]) + "*****"
			} else {
				item.UserName = "******"
			}
			item.UserAvatar = configs.MyApp.CdnUrl + "images/default/user_anonymous.png"
		}
		commentList.Items = append(commentList.Items, item)
	}
	//如果没有评论，则把null值换成空数组[]
	if len(commentList.Items) == 0 {
		commentList.Items = make([]Comments.Items, 0)
	}
	return commentList
}

func (t ShipperTransformer) formatCommentReply(comment CommentsModel.Comment) []Comments.Replies {
	var commentReplies []Comments.Replies
	for i := 0; i < len(comment.CommentReply); i++ {
		reply := comment.CommentReply[i]
		commentReplies = append(commentReplies, Comments.Replies{
			ID:        reply.ID,
			CommentID: reply.CommentID,
			Type:      reply.Type,
			Text:      reply.Text,
			AdminID:   reply.AdminID,
			CreatedAt: reply.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt: reply.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	//如果评论没有回复的信息，则把null值换成空数组[]
	if len(comment.CommentReply) == 0 {
		commentReplies = make([]Comments.Replies, 0)
	}
	return commentReplies
}

// FormatOrderDetail
//
//	@Time 2022-10-21 12:10:58
//	<AUTHOR>
//	@Description: 订单详细1
//	@receiver t ShipperTransformer
//	@param data
//	@return interface{}
func (t ShipperTransformer) FormatOrderDetail(list models.OrderToday) interface{} {
	data := make(map[string]interface{})
	data["id"] = list.ID
	data["order_id"] = list.OrderID
	data["store_address"] = tools.If(t.language == "ug", list.Restaurant.AddressUg, list.Restaurant.AddressZh)
	data["store_name"] = tools.If(t.language == "ug", list.Restaurant.NameUg, list.Restaurant.NameZh)
	data["store_phone"] = list.Restaurant.Tel
	data["name"] = list.Name
	data["mobile"] = list.Mobile
	data["building_address"] = tools.If(t.language == "ug", list.AddressView.BuildingNameUg, list.AddressView.BuildingNameZh)
	data["order_address"] = list.OrderAddress
	data["consume_type"] = tools.If(list.ConsumeType == 2, 1, list.ConsumeType) //(暂时解决配送客户端应付款的问题)
	data["pay_type"] = tools.If(t.language == "ug", list.PayTypes.NameUg, list.PayTypes.NameZh)
	if list.PayType != 0 {
		if list.CashClearState == 1 && list.CashClearChannel == 3 {
			data["pay_type"] = t.langUtil.T("pay_type_app_pay_desctription")
		}
		if list.CashClearState == 1 && list.CashClearChannel == 4 {
			data["pay_type"] = t.langUtil.T("pay_type_qr_code_desctription")
		}
		arr := []int{1, 2}
		var inarr bool
		for _, v := range arr {
			if v == list.CashClearChannel {
				inarr = true
			}
		}
		if list.CashClearState == 1 && inarr {
			data["pay_type"] = t.langUtil.T("pay_type_sms_desctription")
		}
	}
	data["pay_type_state"] = list.PayType
	data["state"] = list.State
	data["price"] = float64(list.Price) / 100
	data["shipment"] = float64(list.Shipment) / 100
	data["lunch_box_fee"] = float64(list.LunchBoxFee) / 100
	deliveryEndTime := list.DeliveryEndTime
	if deliveryEndTime.IsZero() {
		data["delivery_end_time"] = nil
	} else {
		data["delivery_end_time"] = deliveryEndTime.Format("2006-01-02 15:04")
	}
	data["description"] = list.Description
	data["timezone"] = list.Timezone

	var bookingTime string
	bookingTime = carbon.Parse(list.BookingTime).Format("Y-m-d H:i:s")
	//if list.Timezone == 6 {
	data["booking_time"] = carbon.Parse(bookingTime).Format("H:i")
	data["booking_date_time"] = carbon.Parse(bookingTime).Format("Y-m-d H:i:s")
	//} else {
	//	data["booking_time"] = carbon.Parse(bookingTime).AddHours(-2).Format("H:i")
	//	data["booking_date_time"] = carbon.Parse(bookingTime).AddHours(-2).Format("Y-m-d H:i:s")
	//}
	data["booking_remaining_minute"] = int(carbon.Now().DiffInMinutes(carbon.Parse(data["booking_date_time"].(string))))

	data["city_id"] = list.AddressView.CityId
	data["area_id"] = list.AddressView.AreaId
	data["street_id"] = list.AddressView.StreetId
	data["street_name"] = tools.If(t.language == "ug", list.AddressView.StreetNameUg, list.AddressView.StreetNameZh)
	data["city_name"] = tools.If(t.language == "ug", list.AddressView.CityNameUg, list.AddressView.CityNameZh)
	data["area_name"] = tools.If(t.language == "ug", list.AddressView.AreaNameUg, list.AddressView.AreaNameZh)
	data["building_id"] = list.AddressView.BuildingId
	data["building_name"] = tools.If(t.language == "ug", list.AddressView.BuildingNameUg, list.AddressView.BuildingNameZh)
	data["building_lat"] = list.AddressView.BuildingLat
	data["building_lng"] = list.AddressView.BuildingLng
	data["store_lat"] = list.Restaurant.Lat
	data["store_lng"] = list.Restaurant.Lng
	data["store_logo"] = configs.MyApp.CdnUrl + list.Restaurant.Logo
	data["order_type"] = list.OrderType
	data["cash_clear_state"] = list.CashClearState
	data["marketing_list"] = list.MarketingList
	data["is_marketing"] = 0
	if list.MarketingList != nil && len(list.MarketingList) > 0 {
		data["is_marketing"] = 1
	}
	if list.PayType == 5 {
		data["cash_clear_state"] = 1
		data["cash_clear_time"] = list.PayTime.Format("2006-01-02 15:04:05")
	} else if list.CashClearTime.IsZero() {
		data["cash_clear_time"] = nil
	} else {
		data["cash_clear_time"] = list.CashClearTime.Format("2006-01-02 15:04:05")
	}

	marketList := make([]map[string]interface{}, 0)
	for _, value := range list.MarketingList {
		market := make(map[string]interface{})
		market["marketing_reduction_fee"] = float64(value.StepReduce) / 100
		market["marketing_name"] = value.Name
		marketList = append(marketList, market)
	}

	if list.Coupon.ID > 0 {
		data["is_marketing"] = 1
		market := make(map[string]interface{})
		market["marketing_reduction_fee"] = float32(tools.ToFloat32(list.Coupon.Price) / 100)
		market["marketing_name"] = t.langUtil.T("coupon")
		marketList = append(marketList, market)
	}

	data["marketing_list"] = marketList

	foods := make([]map[string]interface{}, 0) //data["id"] = list.ID
	for _, value := range list.OrderDetail {
		if value.Number == 0 {
			continue
		}
		food := make(map[string]interface{})
		food["food_name"] = tools.If(t.language == "ug", value.RestaurantFoods.NameUg, value.RestaurantFoods.NameZh)
		food["food_price"] = float64(value.Price) / 100
		food["number"] = value.Number
		food["lunch_box_fee"] = float64(value.LunchBoxFee) / 100
		food["lunch_box_count"] = value.LunchBoxCount
		foods = append(foods, food)
	}

	for i := 0; i < len(list.LunchBox); i++ {
		lunchBox := list.LunchBox[i]
		if lunchBox.Price > 0 {
			food := make(map[string]interface{})
			food["food_name"] = lunchBox.Name
			food["food_price"] = (float32(lunchBox.OriginalPrice)) / 100.0
			food["number"] = int(lunchBox.LunchBoxCount)
			food["lunch_box_fee"] = (float32(lunchBox.Price)) / 100.0
			food["lunch_box_count"] = int(lunchBox.LunchBoxCount)
			foods = append(foods, food)
		}
	}

	data["foods"] = foods

	comments := make([]map[string]interface{}, 0)
	for _, item := range list.Comments {
		comment := make(map[string]interface{})
		comment["star"] = tools.ToFloat32((item.Star + item.FoodStar + item.BoxStar) / 3)
		comment["level"] = 2
		comment["text"] = item.Text
		comment["create_time"] = item.CreatedAt.Format("2006-01-02 15:04:05")
		comments = append(comments, comment)
	}
	data["comments"] = comments
	return data

}

// FormatStatistics
//
//	@Time 2022-09-11 00:55:38
//	<AUTHOR>
//	@Description: 配送员统计
//	@receiver t ShipperTransformer
//	@param data
//	@return interface{}
func (t ShipperTransformer) FormatStatistics(list []shipperResources.Statistics) interface{} {
	data := shipperResources.StatisticsTrf{}
	for i, value := range list {
		orderPrice := value.TotalPrice
		if value.State == 3 {
			orderPrice = value.TotalLunchboxPrice
		}
		data.TotalCount += value.OrderCount
		data.TotalPrice += orderPrice

		if list[i].State == 0 || list[i].State == 1 {
			if value.ConsumeType == 0 || value.ConsumeType == 3 {
				data.Back.CashOrderCount += value.OrderCount
				data.Back.CashOrderPrice += orderPrice
			}
			if value.ConsumeType == 1 {
				data.Back.OnlineOrderCount += value.OrderCount
				data.Back.OnlinePayPrice += orderPrice
			}
		}
		if list[i].State == 2 {
			if value.ConsumeType == 0 || value.ConsumeType == 3 {
				data.Failed.CashOrderCount += value.OrderCount
				data.Failed.CashOrderPrice += orderPrice
			}
			if value.ConsumeType == 1 {
				data.Failed.OnlineOrderCount += value.OrderCount
				data.Failed.OnlinePayPrice += orderPrice
			}
		}
		if list[i].State == 3 {
			if value.ConsumeType == 0 || value.ConsumeType == 3 {
				data.Succeed.CashOrderCount += value.OrderCount
				data.Succeed.CashOrderPrice += orderPrice
			}
			if value.ConsumeType == 1 {
				data.Succeed.OnlineOrderCount += value.OrderCount
				data.Succeed.OnlinePayPrice += orderPrice
			}
		}

	}

	return data
}

// GetShipperProfile
//
//	@Description: 返回配送员信息
//	@author: Alimjan
//	@Time: 2022-09-16 11:22:10
//	@receiver t ShipperTransformer
//	@param info models.Admin
//	@return interface{}
func (t ShipperTransformer) GetShipperProfile(info models.Admin) shipperResources.ShipperProfileEntity {
	profile := shipper.ShipperProfileEntity{
		ID:       info.ID,
		Avatar:   configs.MyApp.CdnUrl + info.Avatar,
		Mobile:   info.Mobile,
		Name:     info.Name,
		RealName: info.RealName,
		Openid:   info.Openid,
	}
	return profile
}

func (t ShipperTransformer) Transform(userId int64, list []map[string]interface{}, champion map[string]interface{}) shipperResources.RankEntity {
	rank := shipperResources.RankEntity{}
	if tools.ToInt64(champion["shipper_id"]) == 0 {
		rank.Champion = make(map[string]interface{}, 0)
	} else {
		champion["trans_date"] = tools.ToTime(champion["trans_date"]).Format("2006-01-02")
		champion["created_at"] = tools.ToTime(champion["created_at"]).Format("2006-01-02")
		rank.Champion = champion
	}

	for i, v := range list {
		if userId != 0 && tools.ToInt64(v["shipper_id"]) == userId {
			rank.User = v
			rank.User["no"] = i + 1
		}
	}
	if rank.User == nil {
		rank.User = make(map[string]interface{}, 0)
	}
	result30 := make([]map[string]interface{}, 0)
	for i := 0; (i < 30) && i < len(list); i++ {
		list[i]["shipper_distance"] = list[i]["shipper_distance"].(float64)
		result30 = append(result30, list[i])
	}
	rank.Data = result30
	return rank
}

// FormatStatisticsDetails
//
//	@Time 2022-09-14 02:06:34
//	<AUTHOR>
//	@Description:订单统计详细
//	@receiver t ShipperTransformer
func (t ShipperTransformer) FormatStatisticsDetails(detail []shipperResources.StatisticsDetailTransformerBefore, currentPage int, perPage int) shipperResources.StatisticsDetailTransformerAfter {
	afterItems := shipperResources.StatisticsDetailTransformerAfter{}
	afterItem := shipperResources.StatisticsDetailTransformerAfterItems{}
	afterItems.Total = len(detail)
	afterItems.CurrentPage = currentPage
	afterItems.PerPage = perPage
	for _, item := range detail {
		afterItem.ID = item.ID
		afterItem.OrderID = item.OrderID
		afterItem.StreetID = item.ReasonID
		afterItem.OrderAddress = item.OrderAddress
		afterItem.Distance = item.Distance / 1000
		afterItem.Timezone = item.Timezone
		afterItem.BookingTime = carbon.Parse(item.BookingTime).Format("H:i")
		afterItem.CreatedAt = carbon.Parse(item.CreatedAt).Format("H:i")
		afterItem.OrderState = item.State
		afterItem.CityID = item.CityID
		afterItem.AreaID = item.AreaID
		afterItem.AreaID = item.AreaID
		afterItem.StreetID = item.StreetID
		afterItem.BuildingID = item.BuildingID

		if t.language == "ug" {
			afterItem.StoreName = item.RestaurantNameUg
			afterItem.StoreAddress = item.RestaurantAddressUg
			afterItem.CityName = item.CityNameUg
			afterItem.AreaName = item.AreaNameUg
			afterItem.BuildingName = item.BuildingNameUg
			if item.ReasonUg == "" {
				afterItem.FialReason = nil
			} else {
				afterItem.FialReason = item.ReasonUg
			}
			afterItem.StreetName = item.StreetNameUg

		} else {
			afterItem.StoreName = item.RestaurantNameZh
			afterItem.StoreAddress = item.RestaurantAddressZh
			afterItem.CityName = item.CityNameZh
			afterItem.AreaName = item.AreaNameZh
			afterItem.BuildingName = item.BuildingNameZh
			if item.ReasonZh == "" {
				afterItem.FialReason = nil
			} else {
				afterItem.FialReason = item.ReasonZh
			}
			afterItem.StreetName = item.StreetNameZh

		}
		afterItems.Items = append(afterItems.Items, afterItem)
	}
	if len(afterItems.Items) == 0 {
		afterItems.Items = make([]shipper.StatisticsDetailTransformerAfterItems, 0)
	}
	return afterItems
}

// FormatAdminOrderList
//
//	@Time 2022-09-15 17:43:32
//	<AUTHOR>
//	@Description: 显示配送员订单列表
//	@receiver t ShipperTransformer
//	@param list
func (t ShipperTransformer) FormatAdminOrderList(list []shipperResources.AdminOrderListTransBefore, admin models.Admin) shipperResources.AdminOrderListTransAfter {
	var data shipperResources.AdminOrderListTransAfter
	var item shipperResources.AdminOrderListTransAfterItems
	data.AdminName = admin.Name
	data.OrderCount = len(list)
	for _, value := range list {
		item.StreetID = value.StreetID
		item.StoreName = value.RestaurantName
		item.StoreID = value.RestaurantID
		item.BuildingID = value.BuildingID
		item.BookingTime = carbon.Parse(value.BookingTime).Format("H:i")
		item.CityName = value.CityName
		item.AreaName = value.AreaName
		item.AreaID = value.AreaID
		item.CityID = value.CityID
		item.OrderID = value.OrderID
		item.OrderAddress = value.OrderAddress
		item.OrderType = value.OrderType
		item.Distance = value.Distance / 1000
		item.StreetName = value.StreetName
		item.BuildingName = value.BuildingName
		item.StoreAddress = value.RestaurantAddress
		item.Timezone = value.Timezone
		data.Items = append(data.Items, item)
	}

	return data

}

// FormatCheckOut
//
//	@Time 2022-09-21 12:20:54
//	<AUTHOR>
//	@Description:
//	@receiver shipper ShipperTransformer
//	@param list
func (shipper ShipperTransformer) FormatCheckOut(list []shipperResources.CheckOut, date string) shipperResources.CheckOutTransFormAfter {
	data := shipperResources.CheckOutTransFormAfter{}
	item := shipperResources.CheckOutTransFormAfterItems{}
	var shouldPayAmount float64
	for i := 0; i < len(list); i++ {
		item.ID = list[i].ID
		item.OrderID = list[i].OrderID
		item.OrderPrice = tools.ToFloat64(list[i].OrderPrice) / 100
		item.State = list[i].State
		item.Time = carbon.Parse(list[i].BookingTime).Format("H:i")
		if list[i].ConsumeType == 0 || list[i].ConsumeType == 3 {
			shouldPayAmount += tools.ToFloat64(list[i].OrderPrice) / 100
		}
		data.Items = append(data.Items, item)
	}
	data.ShouldPayAmount = math.Round(shouldPayAmount*100) / 100
	if len(list) == 0 {
		data.Items = []shipperResources.CheckOutTransFormAfterItems{}
	}
	return data

}

// FormatAttendanceList
//
//	@Time 2022-12-31 19:34:06
//	<AUTHOR>
//	@Description: 格式化考勤列表
//	@receiver t ShipperTransformer
//	@param data
//	@return interface{}
func (t ShipperTransformer) FormatAttendanceList(c *gin.Context, data []map[string]interface{}, currentEndDate string, nextEndDate string) interface{} {
	result := make([]map[string]interface{}, 0)
	// 本人打卡
	var myAttendance interface{} = t.langUtil.T("personally-attendance")
	for _, dataItem := range data {
		item := make(map[string]interface{})
		item["date_time"] = dataItem["created_at"].(time.Time).Format("2006-01-02 15:04:05")
		item["admin_name"] = tools.If(dataItem["admin_name"] == nil, myAttendance, dataItem["admin_name"])
		//item["admin_id"] =tools.If( dataItem["admin_id"]== nil|| dataItem["admin_id"].(int64)== 0 ,nil,dataItem["admin_id"])
		item["state"] = dataItem["state"]
		item["state_name"] = dataItem["attendance_name"]
		item["position"] = dataItem["position"]
		result = append(result, item)
	}
	groupList := make([]map[string]interface{}, 0)
	dateTemplate := "2006-01-02"
	for _, item := range result {
		groupByDayList := make([]map[string]interface{}, 0)              // 定义一个map 用来存放分组后的数据 以日期为key
		date := carbon.Parse(item["date_time"].(string)).Format("Y-m-d") // 获取日期 用来分组
		if dateTemplate != date {
			dateTemplate = date //  // 如果不存在 则把日期存入临时变量中  用来判断是否存在
			// 把数据存入map中 以日期为key
			item["date_time"] = carbon.Parse(item["date_time"].(string)).Format("H:i:s")
			groupByDayList = append(groupByDayList, item)
			// 把数据存入数组中
			groupList = append(groupList, map[string]interface{}{
				"date": date,
				"item": groupByDayList,
			})
		} else {
			item["date_time"] = carbon.Parse(item["date_time"].(string)).Format("H:i:s")
			// 如果存在 则把数据存入map中 以日期为key
			for _, groupItem := range groupList {
				if groupItem["date"] == date {
					groupByDayList = groupItem["item"].([]map[string]interface{})
					groupByDayList = append(groupByDayList, item)
					groupItem["item"] = groupByDayList
				}
			}
		}
		dateTemplate = date
		groupByDay := make(map[string]interface{})
		if groupByDay["date"] == nil {
			groupByDay["date"] = date

		}
	}

	return gin.H{
		"items": groupList,
		"params": gin.H{
			"cur_end_date":  currentEndDate,
			"next_end_date": nextEndDate,
		},
	}
}

// FormatAttendanceInfo
//
//	@Time 2023-01-03 13:41:59
//	<AUTHOR>
//	@Description: 格式化考勤详情
//	@receiver t ShipperTransformer
//	@param currentState
//	@param nextState
//	@return gin.H
func (t ShipperTransformer) FormatAttendanceInfo(currentState models.AttendanceState, nextState []map[string]interface{}, todayHistoryStates []map[string]interface{}) gin.H {
	tempHistoryStates := make([]map[string]interface{}, 0)
	for _, historyState := range todayHistoryStates {
		tempHistoryState := make(map[string]interface{})
		tempHistoryState["state"] = historyState["state"]
		tempHistoryState["name"] = historyState["name"]
		tempHistoryState["date_time"] = historyState["created_at"].(time.Time).Format("15:04:05")
		tempHistoryStates = append(tempHistoryStates, tempHistoryState)
	}
	var data = gin.H{
		"current_state": gin.H{
			"state":      currentState.Id,
			"name":       currentState.Name,
			"next_state": currentState.NextState,
			"now":        time.Now().Format("2006-01-02"),
		},
		"next_state":           nextState,
		"today_history_states": tempHistoryStates,
	}
	return data
}

// pushedOrderIds
//
//	@Description: 获取骑手推送订单id
//	@author: Alimjan
//	@Time: 2023-08-31 12:36:32
//	@receiver t ShipperTransformer
//	@param admin models.Admin
//	@return []int
func (t ShipperTransformer) pushedOrderIds(admin models.Admin) []int {
	db := tools.Db
	var rtn []int
	cr := carbon.Now().SubDays(1).ToDateTimeString()
	db.Model(&models.ShipperOrderPushDetail{}).Select("order_id").
		Where("created_at > ? ", cr).
		Where("shipper_id = ?", admin.ID).Pluck("t_shipper_order_push_detail.order_id", &rtn)
	return rtn
}

// 描述：格式化配送员取消的订单历史
// 作者：Qurbanjan
// 文件：ShipperTransformer.go
// 修改时间：2023/09/25 17:54
func (t ShipperTransformer) FormatCanceledOrder(list []map[string]interface{}) []map[string]interface{} {
	result := make([]map[string]interface{}, 0)
	for _, listItem := range list {
		item := make(map[string]interface{})
		item["store_name"] = listItem["store_name"]
		item["order_id"] = listItem["order_id"]
		item["store_address"] = listItem["store_address"]
		item["building_name"] = listItem["building_name"]
		item["order_address"] = listItem["order_address"]
		item["area_name"] = listItem["area_name"]
		item["street_name"] = listItem["street_name"]
		item["distance"] = tools.ToFloat64(listItem["distance"])
		item["income"] = 8.00
		item["canceled_time"] = listItem["updated_at"].(time.Time).Format("2006-01-02 15:04:05")
		result = append(result, item)
	}
	return result
}

// 描述：格式化超时订单
// 作者：Qurbanjan
// 文件：ShipperTransformer.go
// 修改时间：2023/10/08 17:01
func (t ShipperTransformer) FormatOverTimeOrder(list []map[string]interface{}) []map[string]interface{} {
	result := make([]map[string]interface{}, 0)
	for _, listItem := range list {
		item := make(map[string]interface{})
		item["order_id"] = listItem["order_id"]
		item["shipment"] = float64(tools.ToInt64(listItem["shipment"])) / 100
		item["deduction_amount"] = -1.5 // 临时数据、有了正式数据后替换这里
		item["booking_time"] = listItem["booking_time"].(time.Time).Format("2006-01-02 15:04:05")
		item["delivery_end_time"] = listItem["delivery_end_time"].(time.Time).Format("2006-01-02 15:04:05")
		// 计算超时时间
		bookingTime := listItem["booking_time"].(time.Time)
		deliveryEndTime := listItem["delivery_end_time"].(time.Time)
		lateTime := int(deliveryEndTime.Sub(bookingTime).Minutes())
		item["late_time"] = lateTime
		result = append(result, item)
	}
	return result
}

// FormatAttendanceCommonList
//
// @Description: 格式化配送员考勤列表通用(打卡，请假，事故)
// @Author: Rixat
// @Time: 2023-11-18 09:04:35
// @receiver
// @param c *gin.Context
func (t ShipperTransformer) FormatAttendanceCommonList(admin models.Admin, attendanceList []models.ShipperAttendanceLog, attendanceType int) []map[string]interface{} {
	attendanceMap := make([]map[string]interface{}, 0)
	if attendanceType == 1 {
		for _, value := range attendanceList {
			attendanceMap = append(attendanceMap, map[string]interface{}{
				"created_at": carbon.Time2Carbon(value.CreatedAt).Format("H:i"),
				"state":      value.State,
				"state_name": t.langUtil.TArr("attendance_states")[value.State],
				"position":   value.Position,
			})
		}
	}
	if attendanceType == 2 || attendanceType == 3 {
		for _, value := range attendanceList {
			attendanceMap = append(attendanceMap, map[string]interface{}{
				"id":                value.Id,
				"avatar":            configs.MyApp.CdnUrl + admin.Avatar,
				"name":              admin.RealName,
				"leave_type":        value.LeaveType,
				"leave_type_name":   t.langUtil.TArr("leave_types")[value.LeaveType],
				"start_time":        carbon.Time2Carbon(value.StartTime).ToDateTimeString(),
				"end_time":          tools.TimeFormatYmdHis(value.EndTime),
				"created_at":        carbon.Time2Carbon(value.CreatedAt).ToDateTimeString(),
				"state":             value.State,
				"review_remark":     value.ReviewRemark,
				"review_state":      value.ReviewState,
				"review_state_name": t.langUtil.TArr("review_state")[value.ReviewState],
				"remark":            value.Remark,
			})
		}
	}
	return attendanceMap
}

// FormatAttendanceCommonList
//
// @Description: 格式化配送员考勤列表通用(打卡，请假，事故)
// @Author: Rixat
// @Time: 2023-11-18 09:04:35
// @receiver
// @param c *gin.Context
func (t ShipperTransformer) FormatAttendanceCommonDetail(admin models.Admin, attendance models.ShipperAttendanceLog, attendanceType int) map[string]interface{} {
	attendanceMap := make(map[string]interface{})
	if attendanceType == 1 {
		attendanceMap = map[string]interface{}{
			"created_at": carbon.Time2Carbon(attendance.CreatedAt).Format("H:i"),
			"state":      attendance.State,
			"state_name": t.langUtil.TArr("attendance_states")[attendance.State],
			"position":   attendance.Position,
		}
	}
	if attendanceType == 2 || attendanceType == 3 {
		attendanceMap = map[string]interface{}{
			"id":                attendance.Id,
			"avatar":            admin.Avatar,
			"name":              admin.RealName,
			"leave_type":        attendance.LeaveType,
			"leave_type_name":   t.langUtil.TArr("leave_types")[attendance.LeaveType],
			"start_time":        carbon.Time2Carbon(attendance.StartTime).ToDateTimeString(),
			"end_time":          tools.TimeFormatYmdHis(attendance.EndTime),
			"created_at":        carbon.Time2Carbon(attendance.CreatedAt).ToDateTimeString(),
			"state":             attendance.State,
			"review_remark":     attendance.ReviewRemark,
			"review_state":      attendance.ReviewState,
			"review_state_name": t.langUtil.TArr("review_state")[attendance.ReviewState],
			"remark":            attendance.Remark,
		}
	}
	return attendanceMap
}

func (t ShipperTransformer) FormatShipperIncomeTagDetailHeader(shipperIncomeSummary *shipment.ShipperIncomeSummary) (tagHead []shipperResources.ShipperIncomeDetailTag, details []shipperResources.ShipperIncomeDetailTag) {
	rtn := []shipperResources.ShipperIncomeDetailTag{}
	head := []shipperResources.ShipperIncomeDetailTag{}

	var red = "0xfffe4b4b"
	var green = "0xff07bf60"
	
	head = append(head, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("total_salary"),
		Type:  0,
		Value: tools.ToInt(shipperIncomeSummary.TotalSalary),
		IsAmount: true,
		Color: green,
	})
	
	head = append(head, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("punishment"),
		Type: 0,
		Value: tools.ToInt(shipperIncomeSummary.Punishment),
		IsAmount: true,
		Color: red,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("total_order_count"),
		Type:  1,
		Value: tools.ToInt(shipperIncomeSummary.TotalOrderCount),
		IsAmount: false,
		Color: "",
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("amount_order"),
		Type:  1,
		Value: tools.ToInt(shipperIncomeSummary.AmountOrder),
		IsAmount: true,
		Color: green,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("amount_reward"),
		Type:  2,
		Value: tools.ToInt(shipperIncomeSummary.AmountReward),
		IsAmount: true,
		Color: green,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("complain_count"),
		Type:  8,
		Value: tools.ToInt(shipperIncomeSummary.ComplainCount),
		IsAmount: false,
		Color: red,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("count_comment_bad"),
		Type:  7,
		Value: tools.ToInt(shipperIncomeSummary.CountCommentBad),
		IsAmount: false,
		Color: red,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("amount_complain"),
		Type:  8,
		Value: tools.ToInt(shipperIncomeSummary.AmountComplain),
		IsAmount: true,
		Color: red,
	})
	
	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("late_order_count"),
		Type:  9,
		Value: tools.ToInt(shipperIncomeSummary.LateCount),
		IsAmount: false,
		Color: red,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("amount_late"),
		Type:  9,
		Value: tools.ToInt(shipperIncomeSummary.AmountLate),
		IsAmount: true,
		Color: red,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("amount_tips"),
		Type:  3,
		Value: tools.ToInt(shipperIncomeSummary.AmountTips),
		IsAmount: true,
		Color: green,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("amount_spectal_time"),
		Type: 4,
		Value: tools.ToInt(shipperIncomeSummary.AmountSpecialTime),
		IsAmount: true,
		Color: green,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("amount_special_weather"),
		Type: 5,
		Value: tools.ToInt(shipperIncomeSummary.AmountSpecialWeather),
		IsAmount: true,
		Color: green,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("amount_comment_good"),
		Type: 6,
		Value: tools.ToInt(shipperIncomeSummary.AmountCommentGood),
		IsAmount: true,
		Color: green,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("amount_comment_bad"),
		Type: 7,
		Value: tools.ToInt(shipperIncomeSummary.AmountCommentBad),
		IsAmount: true,
		Color: red,
	})


	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("amount_invite_user"),
		Type: 0,
		Value: tools.ToInt(shipperIncomeSummary.AmountInviteUser),
		IsAmount: true,
		Color: green,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("amount_order_tips"),
		Type: 3,
		Value: tools.ToInt(shipperIncomeSummary.AmountOrderTips),
		IsAmount: true,
		Color: green,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("amount_special_price_order"),
		Type: 1,
		Value: tools.ToInt(shipperIncomeSummary.AmountSpecialPriceOrder),
		IsAmount: true,
		Color: green,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("count_special_price_order"),
		Type: 1,
		Value: tools.ToInt(shipperIncomeSummary.CountSpecialPriceOrder),
		IsAmount: false,
		Color: green,
	})

	rtn = append(rtn, shipperResources.ShipperIncomeDetailTag{
		Title: t.langUtil.T("amount_insurance"),
		Type:  16,
		Value: tools.ToInt(shipperIncomeSummary.AmountInsurance),
		IsAmount: true,
		Color: red,
	})
	
	return head, rtn
}

// 描述：格式化配送员收入详情列表
// 作者：Qurbanjan
// 文件：ShipperTransformer.go
// 修改时间：2024/07/12 11:45
func (t ShipperTransformer) FormatShipperIncomeList(items []shipment.ShipperIncome) []shipper.IncomeResponse{
	var listItems []shipper.IncomeResponse 
	for _, income := range items {
		typeName := t.langUtil.TArr("income_types")[income.Type]
		resName :=tools.If(t.language == "ug", income.Order.Restaurant.NameUg, income.Order.Restaurant.NameZh)
		orderCreatedAt :=tools.TimeFormatYmdHis(&income.Order.CreatedAt)
		deliveryEndTime :=tools.TimeFormatYmdHis(&income.Order.DeliveryEndTime)
		if income.Type == 16 { //保险扣费 
			resName = typeName
			orderCreatedAt  = carbon.Parse(income.Date,configs.AsiaShanghai).Format("Y-m-d")
			deliveryEndTime = carbon.Parse(income.Date,configs.AsiaShanghai).Format("Y-m-d")
		}
		
		item := shipper.IncomeResponse {
			ID:                    income.ID,
            TypeName:              typeName,
            UserName:              income.Order.Name,
            OrderID:               income.OrderID,
            OrderNo:               income.OrderNo,
            OrderPrice:            income.OrderPrice,
            OrderType:             income.OrderType,
            ShipmentIncome:        income.Amount,
            Amount:                income.Amount,
            ComplaintType:         income.ComplaintType,
            IsComplaint:           income.Type == 8,
            IsLate:                income.Type == 9,
            OrderDeliveryEndTime:  deliveryEndTime,
            OrderCreatedAt:        orderCreatedAt,
            ResName:               resName,
		}
		listItems = append(listItems, item)
	}
	return listItems
}
