/*
*
@author: captain
@since: 2022/10/27
@desc:
*
*/
package shipper

import (
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/resources/shipper"
	"mulazim-api/tools"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type CashTransformer struct {
	langUtil *lang.LangUtil
	language string
}

// NewCashTransformer
//
//	@Description: 初始化cashTransformer对象
//	@author: Captain
//	@Time: 2022-10-27 11:05:59
//	@param c gin.Context
//	@return *CashTransformer
func NewCashTransformer(c *gin.Context) *CashTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	cashTransformer := CashTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &cashTransformer
}

// FormatNotPayedOrderList
//
//	@Description: 格式化配送员未结账订单列表数据
//	@author: Captain
//	@Time: 2022-10-28 17:36:50
//	@receiver t CashTransformer
//	@param orderList []map[string]interface{} 未结账订单列表
//	@param headers []map[string]interface{} 页面头部统计数据
//	@param page int 分页的页数
//	@param limit int	每页显示的订单数量
//	@return *shipper.ShipperNotPayedOrderEntitiy
func (t CashTransformer) FormatNotPayedOrderList(orderList []map[string]interface{}, headers []map[string]interface{}, page int, limit int,cashClearStateState int) *shipper.ShipperNotPayedOrderEntitiy {
	data := new(shipper.ShipperNotPayedOrderEntitiy)
	var headerInfo shipper.NotPayedOrderHeader
	headerInfo.TotalCashUncheckedOrderPrice = 0 //未缴纳现金订单金额
	headerInfo.TotalCashUncheckedOrderCount = 0 //未缴纳现金订单数
	headerInfo.TotalCashCheckedOrderPrice = 0   //未缴纳现金订单数
	//格式化页面头部统计数据
	for i := 0; i < len(headers); i++ {
		cashClearState := int(headers[i]["cash_clear_state"].(int64))
		orderPrice, _ := strconv.ParseFloat(headers[i]["order_price"].(string), 32)
		orderCount := int(headers[i]["count"].(int64))

		headerInfo.TotalCashOrderPrice += orderPrice / 100 //获取所有现金订单总额
		headerInfo.TotalOrderCount += orderCount           //获取所有的订单数
		if cashClearState == 0 {
			totalCashUncheckedOrderPrice, _ := strconv.ParseFloat(headers[i]["order_price"].(string), 32)
			headerInfo.TotalCashUncheckedOrderPrice = totalCashUncheckedOrderPrice / 100
			headerInfo.TotalCashUncheckedOrderCount = int(headers[i]["count"].(int64))
		} else if cashClearState == 1 {
			totalCashCheckedOrderPrice, _ := strconv.ParseFloat(headers[i]["order_price"].(string), 32)
			headerInfo.TotalCashCheckedOrderPrice = totalCashCheckedOrderPrice / 100
		}
	}
	//格式化未结账的订单信息
	var list shipper.NotPayedOrderList
	for k := 0; k < len(orderList); k++ {
		var item shipper.NotPayedOrder
		item.ID = int(orderList[k]["id"].(int32))
		item.OrderID = orderList[k]["order_id"].(string)
		item.State = int(orderList[k]["state"].(int8))
		item.PayPlatForm = int(orderList[k]["pay_platform"].(int8))
		price, _ := strconv.ParseFloat(orderList[k]["price"].(string), 32)
		item.Price = fmt.Sprintf("%.2f", price)
		item.OverTimeLabel = ""
		item.Timezone = int(orderList[k]["timezone"].(int64))
		if orderList[k]["cash_clear_time"] != nil {
			item.CashClearTime = carbon.Parse(orderList[k]["cash_clear_time"].(time.Time).Format(time.RFC3339)).Format("Y-m-d H:i:s")
		}else{
			item.ShowSendBtn = 1
			if cashClearStateState == 1 {
				item.ShowSendBtn = 0
				item.OverTimeLabel = t.langUtil.T("order_over_time")
			}else if tools.ToString(orderList[k]["order_origin"]) == "t_order" { //属于已归档的订单
				item.ShowSendBtn = 0
				item.OverTimeLabel = t.langUtil.T("order_over_time")
			}else if tools.ToString(orderList[k]["order_origin"]) == "t_order_today" { //属于 今天的订单 需要查询 是否超过 24小时 以防 多天 未归档数据 突然归档 
				// item.ShowSendBtn = 0
				hours := carbon.Parse(tools.ToString(orderList[k]["created_at"]), configs.AsiaShanghai).DiffInHours(carbon.Now(configs.AsiaShanghai))
				if hours > 24 {
					item.ShowSendBtn = 0
					item.OverTimeLabel = t.langUtil.T("order_over_time")
				}
			}

		}
		item.CashClearState = int(orderList[k]["cash_clear_state"].(int64))
		item.PayTime = carbon.Parse(orderList[k]["pay_time"].(time.Time).Format(time.RFC3339)).Format("Y-m-d H:i:s")
		item.CreatedAt = carbon.Parse(orderList[k]["created_at"].(time.Time).Format(time.RFC3339)).Format("Y-m-d")
		item.BookingTime = carbon.Parse(orderList[k]["booking_time"].(time.Time).Format(time.RFC3339)).Format("Y-m-d H:i:s")
		list.Data = append(list.Data, item)
	}
	list.PerPage = limit
	list.CurrentPage = page
	list.From = (page-1)*limit + 1
	list.To = (page-1)*limit + len(list.Data)
	// 如果没有数据返回空数组
	if len(list.Data) == 0 {
		list.Data = make([]shipper.NotPayedOrder, 0)
	}
	data.List = list
	data.Header = headerInfo
	return data

}
