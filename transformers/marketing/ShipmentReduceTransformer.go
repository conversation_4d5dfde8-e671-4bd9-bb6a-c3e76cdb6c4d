package marketing

import (
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	resources "mulazim-api/resources/marketing"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ShipmentReduceTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func NewShipmentReduceTransformer(c *gin.Context) *ShipmentReduceTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	transformer := ShipmentReduceTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &transformer
}

func (t *ShipmentReduceTransformer) FormatMerchantShipmentReduceList(list []models.Marketing) []resources.FormatMerchantShipmentReduceList {
	result := make([]resources.FormatMerchantShipmentReduceList, 0)
	if len(list) > 0 {
		for _, item := range list {
			beginDate := carbon.Parse(item.BeginDate.Format("2006-01-02"))
			endDate := carbon.Parse(item.EndDate.Format("2006-01-02"))
			remainDate := beginDate.DiffInDays(endDate)
			remainDateName := fmt.Sprintf(t.langUtil.T("marketing_remain_day"), remainDate)
			if remainDate == 0 {
				remainDateName = t.langUtil.T("today_stop")
			}
			itemFormat := resources.FormatMerchantShipmentReduceList{
				ID:              item.ID,
				Name:            tools.GetNameByLang(item, t.language),
				CreatorType:     item.CreatorType,
				CreatorTypeName: tools.If(item.CreatorType == 1, t.langUtil.T("admin_create"), t.langUtil.T("restaurant_create")),
				State:           item.State,
				StateName:       t.langUtil.TArr("marketing_state_name")[item.State],
				TimeOutDay:      remainDateName,
				BeginDate:       item.BeginDate.Format("2006-01-02"),
				EndDate:         item.EndDate.Format("2006-01-02"),
				GroupType:       item.GroupType,                                                                                     // 1:团体活动，2：商家活动
				GroupTypeName:   tools.If(item.GroupType == 1, t.langUtil.T("group_marketing"), t.langUtil.T("merchant_marketing")), // 1:团体活动，2：商家活动
			}
			result = append(result, itemFormat)
		}
	}
	return result
}

// FormatMerchantShipmentReduce
//
// @Description: 格式化减配送活动详情
// @Author: Rixat
// @Time: 2024-01-25 10:23:30
// @receiver
// @param c *gin.Context
func (t *ShipmentReduceTransformer) FormatMerchantShipmentReduce(item models.Marketing) resources.FormatMerchantShipmentReduceDetail {
	var itemFormat resources.FormatMerchantShipmentReduceDetail
	if item.ID == 0 {
		return itemFormat
	}
	beginDate := carbon.Parse(item.BeginDate.Format("2006-01-02"))
	endDate := carbon.Parse(item.EndDate.Format("2006-01-02"))
	remainDate := beginDate.DiffInDays(endDate)
	remainDateName := fmt.Sprintf(t.langUtil.T("marketing_remain_day"), remainDate)
	if remainDate == 0 {
		remainDateName = t.langUtil.T("today_stop")
	}
	itemFormat = resources.FormatMerchantShipmentReduceDetail{
		ID:               item.ID,
		Name:             tools.GetNameByLang(item, t.language),
		NameUg:           item.NameUg,
		NameZh:           item.NameZh,
		CreatorType:      item.CreatorType,
		CreatorTypeName:  tools.If(item.CreatorType == models.MarketingCreatorTypeDealer, t.langUtil.T("admin_create"), t.langUtil.T("restaurant_create")),
		GroupType:        item.GroupType,
		BeginDate:        item.BeginDate.Format("2006-01-02"),
		EndDate:          item.EndDate.Format("2006-01-02"),
		FullWeekState:    item.FullWeekState,
		FullTimeState:    item.FullTimeState,
		Day:              item.Day,
		AutoContinue:     item.AutoContinue,
		Steps:            tools.StringToMapArr(item.Steps),
		MinDeliveryPrice: item.MinDeliveryPrice,
		TimeOutDay:       remainDateName,
		WebUrl:           "https://cer.mulazim.com/event-detail?lang=" + t.language,
		State:            item.State,
		StateName:        t.langUtil.TArr("marketing_state_name")[item.State],
		CreatedAt:        tools.TimeFormatYmdHis(&item.CreatedAt),
		AttendanceAt:     tools.TimeFormatYmdHis(&item.CreatedAt),
	}
	var marketingAttendance models.MarketingGroupTemplateAttendance
	tools.Db.Model(marketingAttendance).Where("marketing_id=?", itemFormat.ID).Order("created_at desc").First(&marketingAttendance)
	if marketingAttendance.ID > 0 {
		itemFormat.AttendanceState = marketingAttendance.State
		itemFormat.AttendanceStateName = t.langUtil.TArr("marketing_attendance_state_name")[marketingAttendance.State]
	}
	// 获取参加团体活动信息
	itemFormat.TimeLines = t.GetTimeSteps(item)
	return itemFormat
}

// / func_name
//
// @Description: 格式化团体活动详情
// @Author: Rixat
// @Time: 2024-01-25 10:24:59
// @receiver
// @param c *gin.Context
func (t *ShipmentReduceTransformer) FormatMerchantShipmentReduceTemplate(item models.MarketingGroupTemplate) resources.FormatMerchantShipmentReduceDetail {
	var itemFormat resources.FormatMerchantShipmentReduceDetail
	if item.ID == 0 {
		return itemFormat
	}
	beginDate := carbon.Parse(item.BeginDate.Format("2006-01-02"))
	endDate := carbon.Parse(item.EndDate.Format("2006-01-02"))
	createdAt := item.CreatedAt
	itemFormat = resources.FormatMerchantShipmentReduceDetail{
		ID:               item.ID,
		Name:             tools.GetNameByLang(item, t.language),
		NameUg:           item.NameUg,
		NameZh:           item.NameZh,
		CreatorType:      item.CreatorType,
		CreatorTypeName:  tools.If(item.CreatorType == models.MarketingCreatorTypeDealer, t.langUtil.T("admin_create"), t.langUtil.T("restaurant_create")),
		GroupType:        models.MarketingGroupTypeGroupActivity,
		BeginDate:        item.BeginDate.Format("2006-01-02"),
		EndDate:          item.EndDate.Format("2006-01-02"),
		FullWeekState:    item.FullWeekState,
		FullTimeState:    item.FullTimeState,
		Day:              item.Day,
		AutoContinue:     item.AutoContinue,
		Steps:            tools.StringToMapArr(item.Steps),
		MinDeliveryPrice: item.MinDeliveryPrice,
		TimeOutDay:       fmt.Sprintf(t.langUtil.T("marketing_remain_day"), beginDate.DiffInDays(endDate)),
		WebUrl:           "https://cer.mulazim.com/event-detail?lang=" + t.language,
		CreatedAt:        tools.TimeFormatYmdHis(createdAt),
		AttendanceAt:     tools.TimeFormatYmdHis(createdAt),
	}
	var marketingAttendance models.MarketingGroupTemplateAttendance
	tools.Db.Model(marketingAttendance).Where("marketing_id=?", itemFormat.ID).Order("created_at desc").First(&marketingAttendance)
	if marketingAttendance.ID > 0 {
		itemFormat.AttendanceState = marketingAttendance.State
		itemFormat.AttendanceStateName = t.langUtil.TArr("marketing_attendance_state_name")[marketingAttendance.State]
	}
	// 获取参加团体活动信息
	itemFormat.TimeLines = t.GetTimeStepsTemplate(item)
	return itemFormat
}

func (t *ShipmentReduceTransformer) GetTimeStepsTemplate(item models.MarketingGroupTemplate) []resources.TimeLines {
	var timeSteps []resources.TimeLines
	if len(item.Time1Start) > 0 && len(item.Time1End) > 0 {
		timeSteps = append(timeSteps, resources.TimeLines{
			TimeStart: item.Time1Start,
			TimeEnd:   item.Time1End,
		})
	}
	if len(item.Time2Start) > 0 && len(item.Time2End) > 0 {
		timeSteps = append(timeSteps, resources.TimeLines{
			TimeStart: item.Time2Start,
			TimeEnd:   item.Time2End,
		})
	}
	if len(item.Time3Start) > 0 && len(item.Time3End) > 0 {
		timeSteps = append(timeSteps, resources.TimeLines{
			TimeStart: item.Time3Start,
			TimeEnd:   item.Time3End,
		})
	}
	return timeSteps
}
func (t *ShipmentReduceTransformer) GetTimeSteps(item models.Marketing) []resources.TimeLines {
	var timeSteps []resources.TimeLines
	if len(item.Time1Start) > 0 && len(item.Time1End) > 0 {
		timeSteps = append(timeSteps, resources.TimeLines{
			TimeStart: item.Time1Start,
			TimeEnd:   item.Time1End,
		})
	}
	if len(item.Time2Start) > 0 && len(item.Time2End) > 0 {
		timeSteps = append(timeSteps, resources.TimeLines{
			TimeStart: item.Time2Start,
			TimeEnd:   item.Time2End,
		})
	}
	if len(item.Time3Start) > 0 && len(item.Time3End) > 0 {
		timeSteps = append(timeSteps, resources.TimeLines{
			TimeStart: item.Time3Start,
			TimeEnd:   item.Time3End,
		})
	}
	return timeSteps
}

func (t *ShipmentReduceTransformer) FormatMerchantShipmentReduceStatistics(statistic map[string]interface{}) map[string]interface{} {
	return nil
}
