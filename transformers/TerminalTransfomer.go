package transformers

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources"
	"mulazim-api/tools"
	"strings"
)

type TerminalTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func (t TerminalTransformer) FormatShow(terminal models.Terminal) resources.TerminalEntity {
	cdnUrl := configs.MyApp.CdnUrl
	entity := resources.TerminalEntity{
		ID:          terminal.ID,
		Name:        terminal.Name,
		OsType:      terminal.OsType,
		Type:       terminal.OsType,
		ForceUpdate: terminal.ForceUpdate,
		Version:     terminal.Version,
		VersionCode: terminal.VersionCode,
		URL:         tools.If(strings.HasPrefix(terminal.Url, "http"), terminal.Url, cdnUrl+terminal.Url),
		PackageName: terminal.PackageName,
		Icon:        cdnUrl + strings.TrimLeft(terminal.Icon,"/"),
	}
	if t.language == "ug" {
		entity.Des = terminal.DescriptionUg
	} else {
		entity.Des = terminal.DescriptionZh
	}
	return entity
}

//
// NewTerminalTransformer
//  @Description: 初始化
//  @author: Alimjan
//  @Time: 2022-09-06 13:09:52
//  @param c *gin.Context
//  @return *TerminalTransformer
//
func NewTerminalTransformer(c *gin.Context) *TerminalTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	terminalTransformer := TerminalTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &terminalTransformer
}
