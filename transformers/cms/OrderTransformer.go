package cms

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	shipmentModels "mulazim-api/models/shipment"
	"mulazim-api/resources/response"
	"mulazim-api/tools"
	"mulazim-api/transformers/merchant"

	"github.com/golang-module/carbon/v2"
)

type OrderTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func NewOrderTransformer(ctx *gin.Context) *OrderTransformer {
	l, _ := ctx.Get("lang_util")
	language, _ := ctx.Get("lang")
	langUtil := l.(lang.LangUtil)
	orderTransformer := OrderTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &orderTransformer
}

func (trns *OrderTransformer) FormatOrderInfo(order models.OrderToday) response.OrderDetail {
	foods := make([]response.FoodInfo, 0)
	foodAllPrice := order.Price
	var foodOriginalPrice uint
	var foodAllReducePrice uint

	for _, v := range order.OrderDetail {
		foods = append(foods, response.FoodInfo{
			Image:           configs.MyApp.CdnUrl + v.RestaurantFoods.Image,
			Name:            v.RestaurantFoods.NameUg, // Assuming NameUg is preferred, adjust if needed
			OriginalPrice:   int(v.OriginalPrice),
			Price:           int(v.Price),
			Discount:        int(v.OriginalPrice - v.Price),
			Count:           int(v.Number),
			LunchBoxFee:     0, // This was 0 in the map, confirm if correct
			TotalFoodsPrice: int(v.Price * v.Number),
			TotalPrice:      int(v.Price * v.Number),

			FoodType:       v.FoodType,
			ComboFoodItems: merchant.GetFormattedComboItems(v.RestaurantFoods.ComboFoodItems, trns.language),
			SelectedSpec:   merchant.GetFormattedSelectedSpec(v.SelectedSpec, trns.language),
		})
		foodAllReducePrice += (v.OriginalPrice * v.Number) - (v.Price * v.Number)
		foodOriginalPrice += v.OriginalPrice * v.Number
	}

	for _, v := range order.LunchBox {
		foods = append(foods, response.FoodInfo{
			Image:           configs.MyApp.CdnUrl, // Confirm if this is correct, was empty in map
			Name:            tools.GetNameByLang(v, trns.language),
			OriginalPrice:   v.OriginalPrice,
			Price:           v.Price,
			Discount:        0,
			Count:           v.LunchBoxCount,
			LunchBoxFee:     0, // This was 0 in the map
			TotalFoodsPrice: v.Price * v.LunchBoxCount,
			TotalPrice:      v.Price * v.LunchBoxCount,
		})
		order.OriginalPrice += uint(v.Price * v.LunchBoxCount)
		foodAllPrice += uint(v.Price * v.LunchBoxCount)
		foodOriginalPrice += uint(v.Price * v.LunchBoxCount)
	}
	order.OriginalPrice += order.OriginalShipment

	marketInfoStructs := trns.GetOrderMarketInfo(order.ID)
	var marketSum uint
	for _, mi := range marketInfoStructs {
		if mi.FoodPriceAdd != 0 { // Check if it can be non-zero
			foodAllPrice += uint(mi.FoodPriceAdd)
			marketSum += uint(mi.ReducePrice)
		}
	}

	if len(marketInfoStructs) > 0 {
		marketInfoStructs = append(marketInfoStructs, response.MarketInfo{
			Name:         trns.langUtil.T("food_price_reduce"),
			Amount:       int(foodAllReducePrice), // Cast to int
			FoodPriceAdd: 0,
			ReducePrice:  int(foodAllReducePrice), // Cast to int
		})
	}
	marketSum += foodAllReducePrice

	totalFee := order.OrderPrice
	consumeTypeName := ""
	switch order.ConsumeType {
	case 0:
		consumeTypeName = trns.langUtil.TArr("consume_type")[0]
	case 3:
		consumeTypeName = trns.langUtil.TArr("consume_type")[3]
	default:
		totalFee = 0
	}

	return response.OrderDetail{
		OrderInfo: response.OrderInfo{
			ID:                  order.ID,
			Name:                order.User.Name,
			Mobile:              order.User.Mobile,
			OrderNo:             order.OrderID,
			CreatedAt:           tools.TimeFormatYmdHis(&order.CreatedAt),
			Remark:              order.Description,
			Terminal:            tools.GetNameByLang(order.Terminal, trns.language),
			PayType:             tools.GetNameByLang(order.PayTypes, trns.language),
			TotalPrice:          order.OrderPrice, // This was order.OrderPrice
			OriginalPrice:       order.OriginalPrice,
			OriginalShipment:    order.OriginalShipment,
			TotalDiscountAmount: order.TotalDiscountAmount,
			ActualPaid:          order.ActualPaid,
			DiscountAmount:      tools.ToInt(order.OriginalPrice) - int(order.Price),
			ShipmentFee:         order.Shipment,
			PayAmount:           order.OrderPrice, // This was order.OrderPrice
			PayTypeID:           order.PayType,
			PayPlatform:         order.PayPlatform,
			FoodAllPrice:        foodAllPrice,
			FoodAllReducePrice:  foodAllReducePrice,
			FoodOriginalPrice:   foodOriginalPrice,
			TotalFee:            totalFee,
			ConsumeType:         consumeTypeName,
			AllReducePrice:      marketSum,
		},
		ShipmentInfo: response.ShipmentInfo{
			State:            tools.GetNameByLang(order.OrderState, trns.language),
			StateColor:       order.OrderState.Color,
			ShipmentState:    trns.langUtil.TArr("shipper_order_state")[order.OrderExtend.ShipperOrderState],
			BookingTime:      carbon.Parse(order.BookingTime).Format("Y-m-d H:i:s"),
			DeliveryEndTime:  tools.TimeFormatYmdHis(&order.DeliveryEndTime),
			ShipperName:      order.Shipper.RealName,
			ShipperMobile:    order.Shipper.Mobile,
			OriginalShipment: order.OriginalShipment,
		},
		FoodsInfo: foods,
		CustomerInfo: response.CustomerInfo{
			Name:    order.Name,
			Mobile:  order.Mobile,
			Address: order.OrderAddress,
		},
		RestaurantInfo: response.RestaurantInfo{
			Name:             tools.GetNameByLang(order.Restaurant, trns.language),
			Mobile:           order.Restaurant.AdminTel,
			Address:          tools.GetNameByLangAndColumn(order.Restaurant, trns.language, "Address"),
			OrderReceiveTime: tools.TimeFormatYmdHis(&order.CreatedAt),
			OrderReadyTime:   order.GetFoodsReadyTime(),
		},
		IncomeInfo: trns.GetOrderIncomeInfo(order.ID),
		MarketInfo: marketInfoStructs,
	}
}

// TODO: 可能需要迁移到 Service / Transformers 里
func (s *OrderTransformer) GetOrderIncomeInfo(orderID int) []response.IncomeInfo {
	info := make([]response.IncomeInfo, 0)
	var incomes []shipmentModels.ShipperIncome
	tools.Db.Model(&incomes).Where("order_id = ?", orderID).Scan(&incomes) // Changed from Model(incomes)
	for _, v := range incomes {
		info = append(info, response.IncomeInfo{
			Name:   s.langUtil.TArr("income_types")[v.Type],
			Amount: v.Amount,
		})
	}
	return info
}

func (trns *OrderTransformer) GetOrderMarketInfo(orderID int) []response.MarketInfo {
	db := tools.GetDB()
	info := make([]response.MarketInfo, 0)

	// 满减活动
	var market []models.MarketingOrderLog
	db.Model(&market).Where("order_id = ?", orderID).Scan(&market) // Changed from Model(market)
	for _, v := range market {
		marketName := ""
		foodPriceAdd := 0
		switch v.Type {
		case 1:
			marketName = trns.langUtil.T("marketing_reduce")
			foodPriceAdd = v.StepReduce
		case 2:
			marketName = trns.langUtil.T("marketing_shipment_reduce")
		}
		info = append(info, response.MarketInfo{
			Name:         marketName,
			Amount:       v.StepReduce,
			FoodPriceAdd: foodPriceAdd,
			ReducePrice:  v.StepReduce,
		})
	}

	// 优惠卷
	var coupon models.CouponLog
	db.Model(&coupon).Preload("Coupon").Where("order_id = ?", orderID).Where("state = ?", 1).First(&coupon) // Changed from Model(coupon)
	if coupon.ID > 0 {
		info = append(info, response.MarketInfo{
			Name:         trns.langUtil.T("marketing_coupon"),
			Amount:       coupon.Coupon.Price,
			FoodPriceAdd: 0,
			ReducePrice:  coupon.Coupon.Price,
		})
	}
	return info
}
