package cms

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
)

type FoodsCategoryTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func (t FoodsCategoryTransformer) RestaurantListFormat(restaurantList []models.Restaurant) []map[string]interface{} {
	language := t.language
	var list []map[string]interface{}
	for _, restaurant := range restaurantList {
		item := map[string]interface{}{
			"id":            restaurant.ID,
			"name":          tools.GetNameByLang(restaurant, language),
			"logo":          configs.MyApp.CdnUrl + restaurant.Logo,
			"weight":        restaurant.Weight,
			"state":         restaurant.State,
			"tel":           restaurant.Tel,
			"can_self_take": restaurant.CanSelfTake,
		}
		list = append(list, item)
	}
	return list
}

// ListFormat
//
//	@Author: YaKupJan
//	@Date: 2024-09-19 17:10:59
//	@Description: 格式化美食分类列表
//	@receiver t
//	@param foodsCategoryList
//	@return []map[string]interface{}
func (t FoodsCategoryTransformer) ListFormat(foodsCategoryList []models.FoodsCategory) []map[string]interface{} {
	items := make([]map[string]interface{}, 0)
	for _, foodsCategory := range foodsCategoryList {
		item := map[string]interface{}{
			"id":             foodsCategory.ID,
			"name":           tools.GetNameByLang(foodsCategory,t.language),
			"name_zh":        foodsCategory.NameZh,
			"name_ug":        foodsCategory.NameUg,
			"image":          configs.MyApp.CdnUrl + foodsCategory.Image,
			"original_image": foodsCategory.Image,
			"weight":         foodsCategory.Weight,
			"state":          foodsCategory.State,
			"created_at":     foodsCategory.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		items = append(items, item)
	}
	return items
}

// DetailFormat
//
//	@Author: YaKupJan
//	@Date: 2024-09-19 17:11:45
//	@Description:格式化美食分类详情
//	@receiver t
//	@param foodsCategory
//	@return map[string]interface{}
func (t FoodsCategoryTransformer) DetailFormat(foodsCategory models.FoodsCategory) map[string]interface{} {
	language := t.language
	return map[string]interface{}{
		"id":             foodsCategory.ID,
		"name":           tools.GetNameByLang(foodsCategory, language),
		"name_zh":        foodsCategory.NameZh,
		"name_ug":        foodsCategory.NameUg,
		"image":          configs.MyApp.CdnUrl + foodsCategory.Image,
		"original_image": foodsCategory.Image,
		"weight":         foodsCategory.Weight,
		"state":          foodsCategory.State,
	}
}

// FoodsListFormat
//
//  @Author: YaKupJan
//  @Date: 2024-09-20 11:04:49
//  @Description: 获取分组内美食的列表格式化
//  @receiver t
//  @param foodsList
//  @return []map[string]interface{}
func (t FoodsCategoryTransformer) FoodsListFormat(foodsList []models.RestaurantFoods) []map[string]interface{} {
	var formattedFoodsList []map[string]interface{}
	for _, food := range foodsList {
		formattedFood := map[string]interface{}{
			"id":              food.ID,
			"name":            tools.GetNameByLang(food, t.language),
			"image":           configs.MyApp.CdnUrl + food.Image,
			"state":           food.State,
			"state_name":      t.langUtil.TArr("restaurant_foods_state")[int(food.State)],
			"price":           food.Price,
			"created_at":      food.CreatedAt.Format("2006-01-02 15:04:05"),
			"city_name":       tools.GetNameByLang(food.Restaurant.City, t.language),
			"area_name":       tools.GetNameByLang(food.Restaurant.Area, t.language),
			"restaurant_name": tools.GetNameByLang(food.Restaurant, t.language),
		}
		formattedFoodsList = append(formattedFoodsList, formattedFood)
	}
	return formattedFoodsList
}

func NewFoodsCategoryTransformer(c *gin.Context) *FoodsCategoryTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	transformer := FoodsCategoryTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &transformer
}
