﻿package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	resLottery "mulazim-api/resources/cms/lottery"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
)

type LotteryTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func (t *LotteryTransformer) LotteryTransformer(list interface{}) interface{} {
	return list
}


//
// NewFoodStaticsTransformer
//  @Description:
//  @param c
//  @return *FoodStaticsTransformer
//
func NewLotteryTransformer(c *gin.Context) *LotteryTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	lotteryTransformer := LotteryTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &lotteryTransformer
}


func (t *LotteryTransformer) FormatLotteryPrizeList(lotteryPrizeList []models.LotteryPrize) interface{} {
    list := make([]map[string]interface{}, 0)
	lang := t.language
    for _, value := range lotteryPrizeList {
        list = append(list, map[string]interface{}{
			"id":                  value.ID,
			"city_id":   value.CityID,
			"area_id":   value.AreaID,
			"city_name": tools.If(lang == "zh", value.City.NameZh, value.City.NameUg),
			"area_name": tools.If(lang == "zh", value.Area.NameZh, value.Area.NameUg),
			"title_img":           tools.CdnUrl(tools.GetNameByLangAndColumn(value, t.language, "TitleImg")),
			"swiper_img":          tools.CdnUrl(tools.GetNameByLangAndColumn(value, t.language, "SwiperImg")),
			"name_ug":             tools.GetNameByLang(value, t.language),
			"model":               value.Model,
			"price":               value.Price,
			"state":               value.State,
			"created_at":          tools.TimeFormatYmdHis(&value.CreatedAt),
		})
    }
	return list
}


func (t *LotteryTransformer) FormatLotteryPrizeDetail(lotteryPrize models.LotteryPrize) map[string]interface{} {
	lang := t.language
    result := map[string]interface{}{
		"id":                  lotteryPrize.ID,
		"city_id":   lotteryPrize.CityID,
		"area_id":   lotteryPrize.AreaID,
		"city_name": tools.If(lang == "zh", lotteryPrize.City.NameZh, lotteryPrize.City.NameUg),
		"area_name": tools.If(lang == "zh", lotteryPrize.Area.NameZh, lotteryPrize.Area.NameUg),
		"title_img_ug_url":        tools.CdnUrl(lotteryPrize.TitleImgUg),
		"title_img_zh_url":        tools.CdnUrl(lotteryPrize.TitleImgZh),
		"swiper_img_ug_url":       tools.CdnUrl(lotteryPrize.SwiperImgUg),
		"swiper_img_zh_url":       tools.CdnUrl(lotteryPrize.SwiperImgZh),
		"title_img_ug":        lotteryPrize.TitleImgUg,
		"title_img_zh":        lotteryPrize.TitleImgZh,
		"swiper_img_ug":       lotteryPrize.SwiperImgUg,
		"swiper_img_zh":       lotteryPrize.SwiperImgZh,
		"name_ug":             lotteryPrize.NameUg,
		"name_zh":             lotteryPrize.NameZh,
		"model":               lotteryPrize.Model,
		"price":               lotteryPrize.Price,
		"state":               lotteryPrize.State,
		"created_at":          tools.TimeFormatYmdHis(&lotteryPrize.CreatedAt),
	}
	return result
}

func (trs *LotteryTransformer) FormatCommentsWithUserInfo(
	comments []models.LotteryComment) []resLottery.LotteryCommentWithUserInfo {

	result := make([]resLottery.LotteryCommentWithUserInfo, 0)
	for _, comment := range comments {
		result = append(result, resLottery.LotteryCommentWithUserInfo{
			ID:                comment.ID,
			CityID:            comment.CityID,
			AreaID:            comment.AreaID,
			LotteryActivityID: comment.LotteryActivityID,
			Type:              comment.Type,
			Content:           comment.Content,
			CreatedAt:         comment.CreatedAt.Format(time.DateTime),
			UserName:   comment.User.Name,
			UserMobile: comment.User.Mobile,
		})
	}
	return result
}

// 格式化 Prize Model list 成为 LotteryPrizeResponse
func (trns *LotteryTransformer) FormatPrizeList(lotteryPrizeList []models.LotteryPrize) []resLottery.LotteryPrizeResponse {
	var list []resLottery.LotteryPrizeResponse
	for _, value := range lotteryPrizeList {
		list = append(list, resLottery.LotteryPrizeResponse{
			ID:        value.ID,
			TitleImg:  tools.CdnUrl(tools.GetNameByLangAndColumn(value, trns.language, "TitleImg")),
			SwiperImg: tools.CdnUrl(tools.GetNameByLangAndColumn(value, trns.language, "SwiperImg")),
			Name:      tools.GetNameByLang(value, trns.language),
			Model:     value.Model,
			Price:     value.Price,
			State:     value.State,
			CreatedAt: tools.TimeFormatYmdHis(&value.CreatedAt),
		})
	}
	return list
}
