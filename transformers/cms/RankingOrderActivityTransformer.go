package cms

import (
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	rankingorderactivity "mulazim-api/requests/cms/rankingOrderActivity"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type RankingOrderActivityTransformer struct {
    langUtil *lang.LangUtil
    language string
}

// 格式化排行订单活动列表
func (t RankingOrderActivityTransformer) FormatListRankingOrderActivity(list []models.LotteryActivityOrderRanking) []rankingorderactivity.RankingOrderActivityListResponse {
    var rankingOrderActivityListResponse []rankingorderactivity.RankingOrderActivityListResponse
    for _, item := range list {
        rankingOrderActivityListResponse = append(rankingOrderActivityListResponse, rankingorderactivity.RankingOrderActivityListResponse{
            ID: item.ID,
            Name: tools.If(t.language =="zh", item.NameZh, item.NameUg),
            AreaName: tools.If(t.language =="zh", item.Area.NameZh, item.Area.NameUg),
            AttendCount: item.AttendCount,
            OrderCount: item.OrderCount,
            TotalOrderAmount: item.TotalOrderAmount,
            State: item.State,
            StateName: t.langUtil.TArr("ranking_order_activity_state_names")[item.State],
            CreatedAt: item.CreatedAt.Format(time.DateTime),
            StartTime: item.StartTime.Format(time.DateTime),
            EndTime: item.EndTime.Format(time.DateTime),
            CreatedBy: item.Admin.RealName,
        })
    }

    if rankingOrderActivityListResponse == nil {
        rankingOrderActivityListResponse = []rankingorderactivity.RankingOrderActivityListResponse{}
    }

    return rankingOrderActivityListResponse
}

func NewRankingOrderActivityTransformer(c *gin.Context) *RankingOrderActivityTransformer {
    l, _ := c.Get("lang_util")
    language, _ := c.Get("lang")
    langUtil := l.(lang.LangUtil)
    transformer := RankingOrderActivityTransformer{
        langUtil: &langUtil,
        language: language.(string),
    }
    return &transformer
}

// 格式化排行订单活动详情
func (t RankingOrderActivityTransformer) FormatDetailRankingOrderActivity(activity models.LotteryActivityOrderRanking) rankingorderactivity.RankingOrderActivityDetailResponse {
    // 判断指针类型是否为空
    activityStartTime := ""
    if activity.StartTime != nil {
        activityStartTime = activity.StartTime.Format(time.DateTime)
    }
    activityEndTime := ""
    if activity.EndTime != nil {
        activityEndTime = activity.EndTime.Format(time.DateTime)
    }
	activityCreatedAt := ""
	if !activity.CreatedAt.IsZero() {
		activityCreatedAt = activity.CreatedAt.Format(time.DateTime)
	}
   

    rankingOrderActivityDetailResponse := rankingorderactivity.RankingOrderActivityDetailResponse{
		ID: activity.ID,
		Name: tools.If(t.language =="zh", activity.NameZh, activity.NameUg),
		AreaName: tools.If(t.language =="zh", activity.Area.NameZh, activity.Area.NameUg),
		StartTime: activityStartTime,
		EndTime: activityEndTime,
		CreatedAt: activityCreatedAt,
		CreatedBy: activity.Admin.RealName,
		AttendCount: activity.AttendCount,
		OrderCount: activity.OrderCount,
		TotalOrderAmount: activity.TotalOrderAmount,
		PrizeCount: activity.PrizeCount,
		SendPrizeCount: activity.SendPrizeCount,
		State: activity.State,
		StateName: t.langUtil.TArr("ranking_order_activity_state_names")[activity.State],
        ShareCount: int64(activity.ShareCount),
        PageViewCount: int64(activity.PageViewCount),
        PageShareCount: int64(activity.PageShareCount),
	}
	return rankingOrderActivityDetailResponse
}


// 格式化活动中奖列表
func (t RankingOrderActivityTransformer) FormatWinnerList(list []models.LotteryChance) []rankingorderactivity.RankingOrderActivityDetailPrizeListResponse {
    var rankingOrderActivityDetailPrizeListResponse []rankingorderactivity.RankingOrderActivityDetailPrizeListResponse
    for _, item := range list {
        drawIndex := 0
        if item.DrawIndex != nil {
            drawIndex = *item.DrawIndex
        }
        orderNum := ""
        if item.OrderToday.OrderID != "" {
            orderNum = item.OrderToday.OrderID
        } else {
            orderNum = item.Order.OrderID
        }
        rankingOrderActivityDetailPrizeListResponse = append(rankingOrderActivityDetailPrizeListResponse, rankingorderactivity.RankingOrderActivityDetailPrizeListResponse{
            ID: item.ID,
            OrderIndex: drawIndex,
            PrizeName: tools.If(t.language =="zh", item.LotteryPrize.NameZh, item.LotteryPrize.NameUg),
            // 保留两位小数 
            PrizePrice: fmt.Sprintf("%.2f", float64(item.LotteryPrize.Price) / 100),
            OrderNum: orderNum,
            CityName: tools.If(t.language =="zh", item.City.NameZh, item.City.NameUg),
            AreaName: tools.If(t.language =="zh", item.Area.NameZh, item.Area.NameUg),
            UserName: item.User.Name,
            UserMobile: item.User.Mobile,
            OrderTime: item.CreatedAt.Format(time.DateTime),
            Address: item.UserBuilding.Address,
        })
    }
	if rankingOrderActivityDetailPrizeListResponse == nil {
		rankingOrderActivityDetailPrizeListResponse = []rankingorderactivity.RankingOrderActivityDetailPrizeListResponse{}
	}
    return rankingOrderActivityDetailPrizeListResponse
}

// 格式化奖品列表
func (t RankingOrderActivityTransformer) FormatUpdatePrizeList(prizes []models.LotteryActivityLevelPrize) []rankingorderactivity.RankingOrderActivityUpdatePrizeListResponse {
    var prizeList []rankingorderactivity.RankingOrderActivityUpdatePrizeListResponse
    prizeMap := make(map[int]string)
    
    // 先收集每个奖品ID对应的中奖序号
    for _, item := range prizes {
        if _, ok := prizeMap[item.PrizeID]; !ok {
            prizeMap[item.PrizeID] = ""
        }
        prizeMap[item.PrizeID] += fmt.Sprintf("%d,", item.LuckyUserIndex)
    }
    
    // 生成奖品列表
    processedPrizeIDs := make(map[int]bool)
    for _, item := range prizes {
        // 跳过已处理的奖品ID
        if processedPrizeIDs[item.PrizeID] {
            continue
        }
        
        // 去除最后一个逗号
        luckyIndexStr := strings.TrimRight(prizeMap[item.PrizeID], ",")
        
        prizeList = append(prizeList, rankingorderactivity.RankingOrderActivityUpdatePrizeListResponse{
            PrizeID:       item.PrizeID,
            PrizeName:     tools.If(t.language == "zh", item.LotteryPrize.NameZh, item.LotteryPrize.NameUg),
            PrizePrice:    item.LotteryPrize.Price,
            Indexs:    luckyIndexStr,
            PrizeImage:   tools.If(t.language == "zh", tools.CdnUrl(item.LotteryPrize.TitleImgZh), tools.CdnUrl(item.LotteryPrize.TitleImgUg)),
        })
        
        processedPrizeIDs[item.PrizeID] = true
    }
    
    return prizeList
}

// 格式化修改的详细
func (t RankingOrderActivityTransformer) FormatUpdateDetail(activity models.LotteryActivityOrderRanking) rankingorderactivity.RankingOrderActivityUpdateDetailResponse {
    var activityResponse rankingorderactivity.RankingOrderActivityUpdateDetailResponse
    activityResponse.ID = activity.ID
    activityResponse.NameUg = activity.NameUg
    activityResponse.NameZh = activity.NameZh
    
    // 添加时间的空值检查
    if activity.StartTime != nil {
        activityResponse.StartTime = activity.StartTime.Format(time.DateTime)
    }
    
    if activity.EndTime != nil {
        activityResponse.EndTime = activity.EndTime.Format(time.DateTime)
    }
    
    if activity.AnnounceBeginTime != nil {
        activityResponse.AnnounceBeginTime = activity.AnnounceBeginTime.Format(time.DateTime)
    }
    
    // 计算结果展示结束时间与活动结束时间的天数差
    if activity.ResultShowEndTime != nil && activity.EndTime != nil {
        daysDiff := int(activity.ResultShowEndTime.Sub(*activity.EndTime).Hours() / 24)
        activityResponse.ResultShowEndTime = daysDiff
    }
    
    activityResponse.State = activity.State
    
    // 处理奖品列表
    if len(activity.LotteryActivityLevel) > 0 {
        activityResponse.PrizeList = t.FormatUpdatePrizeList(activity.LotteryActivityLevel[0].LotteryActivityLevelPrize)
    } else {
        activityResponse.PrizeList = []rankingorderactivity.RankingOrderActivityUpdatePrizeListResponse{}
    }

    // 处理分享封面图片
    if activity.ShareCoverImages != "" {
        shareCoverImages := strings.Split(activity.ShareCoverImages, ",")
        activityResponse.ShareCoverImages = []rankingorderactivity.ImagesResponse{}
        for _, item := range shareCoverImages {
            if item != "" {
                activityResponse.ShareCoverImages = append(activityResponse.ShareCoverImages, rankingorderactivity.ImagesResponse{
                    Url: item,
                    FullUrl: tools.CdnUrl(item),
                })
            }
        }
    } else {
        activityResponse.ShareCoverImages = []rankingorderactivity.ImagesResponse{}
    }

    // 处理图片相关字段
    activityResponse.AnnounceEntranceImageUg = rankingorderactivity.ImagesResponse{
        Url: activity.AnnounceEntranceImageUg,
        FullUrl: tools.CdnUrl(activity.AnnounceEntranceImageUg),
    }

    activityResponse.AnnounceEntranceImageZh = rankingorderactivity.ImagesResponse{
        Url: activity.AnnounceEntranceImageZh,
        FullUrl: tools.CdnUrl(activity.AnnounceEntranceImageZh),
    }

    activityResponse.AnnouncePageImageUg = rankingorderactivity.ImagesResponse{
        Url: activity.AnnouncePageImageUg,
        FullUrl: tools.CdnUrl(activity.AnnouncePageImageUg),
    }

    activityResponse.AnnouncePageImageZh = rankingorderactivity.ImagesResponse{
        Url: activity.AnnouncePageImageZh,
        FullUrl: tools.CdnUrl(activity.AnnouncePageImageZh),
    }   

    activityResponse.RuleUg = activity.RuleUg
    activityResponse.RuleZh = activity.RuleZh

    return activityResponse
}
