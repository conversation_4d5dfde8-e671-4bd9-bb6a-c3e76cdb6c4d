package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type PriceMarkupTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func NewPriceMarkupTransformer(c *gin.Context) *PriceMarkupTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	seckillTransformer := PriceMarkupTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &seckillTransformer
}


// FormatMenu
//
//	@Description: 格式化菜单数据
//	@author: Alimjan
//	@Time: 2023-06-05 13:26:07
//	@receiver t CmsTransformer
//	@param admin models.Admin
//	@param menus []cms.Menus
//	@param badge int64
//	@return interface{}
func (t PriceMarkupTransformer) FormatPriceMarkupList(markupList []models.PriceMarkupFood) []map[string]interface{}{
	items := make([]map[string]interface{}, 0)
	for _, v := range markupList {
		item := make(map[string]interface{})
		item["id"] = v.ID
		item["city_name"] = tools.GetNameByLang(v.City,t.language)
		item["area_name"] = tools.GetNameByLang(v.Area,t.language)
		item["creator_name"] = v.Admin.RealName
		item["restaurant_id"] = v.RestaurantID
		item["restaurant_name"] = tools.GetNameByLang(v.Restaurant,t.language)
		item["food_image"] = tools.CdnUrl(v.RestaurantFoods.Image)
		item["food_name"] = tools.GetNameByLang(v.RestaurantFoods,t.language)
		item["food_id"] = v.RestaurantFoodsID
		item["total_count"] = v.TotalCount
		item["remain_count"] = v.TotalCount - v.PriceMarkupFoodLogSum.SaledCount
		item["food_price"] = v.RestaurantFoods.Price
		if v.FoodType == models.RestaurantFoodsTypeSpec {
			item["food_price"] = v.SelectedSpec.Price
		}
		item["in_price"] = v.InPrice
		item["food_begin_time"] = v.RestaurantFoods.BeginTime
		item["food_end_time"] = v.RestaurantFoods.EndTime
		item["food_ready_time"] = v.RestaurantFoods.ReadyTime
		item["food_state"] = v.RestaurantFoods.State
		item["start_date"] = tools.TimeFormatYmd(v.StartDate)
		item["end_date"] = tools.TimeFormatYmd(v.EndDate)
		item["running_state"] = v.RunningState
		item["can_refund"] = false
		item["state"] = v.State
		item["food_type"] = v.FoodType
		item["spec_id"] = v.SpecID
		item["pay_time"] = tools.TimeFormatYmdHis(v.PayTime)
		item["stop_time"] = tools.TimeFormatYmdHis(v.StopTime)
		item["review_at"] = tools.TimeFormatYmdHis(v.ReviewAt)
		item["created_at"] = tools.TimeFormatYmdHis(&v.CreatedAt)
		if(v.RunningState == 2 && v.StopTime != nil){
			stopDiffSecond := carbon.Time2Carbon(*v.StopTime).DiffInSeconds(carbon.Now())
			if stopDiffSecond > 60*60*24 {
				item["can_refund"] = true
			}
		}
		if v.FoodType == models.RestaurantFoodsTypeSpec && v.SelectedSpec.ID > 0 {
			item["spec_options"] = v.SelectedSpec.GetOptions(*v.SelectedSpec,t.language)
		}
		if v.RestaurantFoods.ComboFoodItems != nil {
			item["combo_food_items"] = v.RestaurantFoods.GetComboItems(v.RestaurantFoods,t.language)
		}
		items = append(items, item)
	}

	return items
}

// FormatPriceMarkupDetail
//
// @Description: 格式化加价活动详情信息
// @Author: Rixat
// @Time: 2024-10-25 12:08:12
// @receiver 
// @param c *gin.Context
func (t PriceMarkupTransformer) FormatPriceMarkupDetail(markup models.PriceMarkupFood) map[string]interface{}{
	item := make(map[string]interface{})
	if markup.ID == 0 {
		return item
	}
	item["id"] = markup.ID
	item["creator_name"] = markup.Admin.RealName
	item["food_type"] = markup.FoodType
	item["restaurant_id"] = markup.RestaurantID
	item["restaurant_name"] = tools.GetNameByLang(markup.Restaurant,t.language)
	item["food_name"] = tools.GetNameByLang(markup.RestaurantFoods,t.language)
	item["food_image"] = tools.CdnUrl(markup.RestaurantFoods.Image)
	item["food_id"] = markup.RestaurantFoodsID
	item["total_count"] = markup.TotalCount
	item["can_use_count"] = markup.CanUseCount()
	item["food_price"] = markup.RestaurantFoods.Price
	item["in_price"] = markup.InPrice
	item["start_date"] = tools.TimeFormatYmd(markup.StartDate)
	item["end_date"] = tools.TimeFormatYmd(markup.EndDate)
	item["state"] = markup.State
	item["created_at"] = tools.TimeFormatYmdHis(&markup.CreatedAt)
	if markup.SpecID > 0 && markup.SelectedSpec.ID > 0 {
		item["spec_options"] = markup.SelectedSpec.GetOptions(*markup.SelectedSpec,t.language)
	}
	if markup.RestaurantFoods.ComboFoodItems != nil {
		item["combo_food_items"] = markup.RestaurantFoods.GetComboItems(markup.RestaurantFoods,t.language)
	}
	return item
}

// FormatPriceMarkupSeckillLog
//
// @Description: 格式化秒杀和特价入驻信息
// @Author: Rixat
// @Time: 2024-10-25 13:28:16
// @receiver 
// @param c *gin.Context
func (t PriceMarkupTransformer) FormatPriceMarkupSeckillLog(markupList []models.Seckill) []map[string]interface{}{
	items := make([]map[string]interface{}, 0)
	for _, v := range markupList {
		item := make(map[string]interface{})
		item["id"] = v.ID
		item["type"] = v.Type
		item["activity_name"] =tools.GetNameByLang(v.SeckillMarket,t.language)
		item["food_name"] =tools.GetNameByLang(v.RestaurantFoods,t.language)
		var saledCount, price int
		if v.PriceMarkupFoodLogSum.SaledCount != 0 {
			saledCount = v.PriceMarkupFoodLogSum.SaledCount
			price = v.PriceMarkupFoodLogSum.Price
		}
		item["count"] = saledCount
		item["price"] = price
		item["begin_time"] = carbon.Parse(v.BeginTime).Format("Y-m-d H:i:s")
		item["end_time"] = carbon.Parse(v.EndTime).Format("Y-m-d H:i:s")
		if v.SpecID > 0 && v.SelectedSpec.ID>0 {
			item["spec_options"] = v.SelectedSpec.FoodSpecOptions
		}
		if v.RestaurantFoods.ComboFoodItems != nil {
			item["combo_food_items"] = v.RestaurantFoods.GetComboItems(v.RestaurantFoods,t.language)
		}
		items = append(items, item)
	}
	return items
}

func (t PriceMarkupTransformer) FormatPriceMarkupPrefLog(markupList []models.FoodsPreferential) []map[string]interface{}{
	items := make([]map[string]interface{}, 0)
	for _, v := range markupList {
		item := make(map[string]interface{})
		item["id"] = v.ID
		item["type"] = v.Type
		item["food_name"] =tools.GetNameByLang(v.RestaurantFood,t.language)
		item["count"] = v.PriceMarkupFoodLogSum.SaledCount
		item["price"] = v.PriceMarkupFoodLogSum.Price
		item["begin_time"] = tools.TimeFormatYmdHis(&v.StartDateTime)
		item["end_time"] = tools.TimeFormatYmdHis(&v.EndDateTime)
		if v.SpecID > 0 && v.SelectedSpec.ID > 0 {
			item["spec_options"] = v.SelectedSpec.FoodSpecOptions
		}
		if v.RestaurantFood.ComboFoodItems != nil {
			item["combo_food_items"] = v.RestaurantFood.GetComboItems(v.RestaurantFood,t.language)
		}
		items = append(items, item)
	}
	return items
}


// FormatPriceMarkupSeckillLog
//
// @Description: 格式化秒杀和特价入驻信息
// @Author: Rixat
// @Time: 2024-10-25 13:28:16
// @receiver 
// @param c *gin.Context
func (t PriceMarkupTransformer) FormatPriceMarkupSeckillDetail(seckill models.Seckill) map[string]interface{}{
	res := make(map[string]interface{})
	res["restaurant_name"] = tools.GetNameByLang(seckill.Restaurant,t.language)
	res["food_name"] = tools.GetNameByLang(seckill.RestaurantFoods,t.language)
	res["food_price"] = seckill.RestaurantFoods.Price
	res["price_markup_id"] = seckill.PriceMarkupID
	res["state"] = seckill.State
	res["saled_count"] = seckill.SeckillLogGroupBySelect.SaledCount
	res["saled_price"] = seckill.SeckillLogGroupBySelect.SeckillPrice
	res["in_price"] = seckill.PriceMarkupFood.InPrice
	res["user_max_order_count"] = seckill.UserMaxOrderCount
	res["begin_time"] = carbon.Parse(seckill.BeginTime).Format("Y-m-d H:i:s")
	res["end_time"] = carbon.Parse(seckill.EndTime).Format("Y-m-d H:i:s")
	res["created_at"] = tools.TimeFormatYmdHis(&seckill.CreatedAt)
	if seckill.SpecID > 0 && seckill.SelectedSpec.ID > 0 {
		res["spec_options"] = seckill.SelectedSpec.GetOptions(*seckill.SelectedSpec,t.language)
	}
	if seckill.RestaurantFoods.ComboFoodItems != nil {
		res["combo_food_items"] = seckill.RestaurantFoods.GetComboItems(seckill.RestaurantFoods,t.language)
	}
	markupSeckillPriceSteps := seckill.PriceMarkupSeckillPriceLog
	items := make([]map[string]interface{}, 0)
	if seckill.PriceMarkupID > 0 && seckill.Type == 1 {
		for _, v := range markupSeckillPriceSteps {
			item := make(map[string]interface{})
			item["price"] =v.Price
			item["count"] =v.Count
			item["saled_count"] = v.PriceMarketSeckillPriceLogSum.SaledCount
			item["in_price"] =seckill.PriceMarkupFood.InPrice
			item["profit"] = (v.Price - seckill.PriceMarkupFood.InPrice) * v.PriceMarketSeckillPriceLogSum.SaledCount
			item["start_time"] = tools.TimeFormatYmdHis(&v.CreatedAt)
			item["end_time"] = tools.TimeFormatYmdHis(v.UpdatedAt)
			if v.PriceMarketSeckillPriceLogSum.SeckillPriceLogID > 0 {
				item["start_time"] = tools.TimeFormatYmdHis(&v.PriceMarketSeckillPriceLogSum.BeginSellTime)
				item["end_time"] = tools.TimeFormatYmdHis(&v.PriceMarketSeckillPriceLogSum.EndSellTime)
			}
			items = append(items, item)
		}
	}else{
		item := make(map[string]interface{})
		item["price"] =seckill.Price
		item["count"] =seckill.TotalCount
		item["saled_count"] =seckill.SaledCount
		item["in_price"] =seckill.Price
		item["profit"] = (tools.ToInt(seckill.Price) - seckill.PriceMarkupFood.InPrice) * tools.ToInt(seckill.SaledCount)
		item["start_time"] = tools.TimeFormatYmdHis(&seckill.SeckillLogGroupBySelect.StartSellTime)
		item["end_time"] = tools.TimeFormatYmdHis(&seckill.SeckillLogGroupBySelect.EndSellTime)
		item["created_at"] = tools.TimeFormatYmdHis(&seckill.CreatedAt)
		items = append(items, item)
	}
	res["steps"] = items
	return res
}

func (t PriceMarkupTransformer) FormatPriceMarkupPrefDetail(pref models.FoodsPreferential) map[string]interface{}{
	res := make(map[string]interface{})
	res["restaurant_name"] = tools.GetNameByLang(pref.Restaurant,t.language)
	res["food_name"] = tools.GetNameByLang(pref.RestaurantFood,t.language)
	res["food_price"] = pref.RestaurantFood.Price
	res["price_markup_id"] = pref.PriceMarkupId
	res["state"] = pref.State
	res["saled_count"] = pref.PriceMarkupSaledCount
	res["saled_price"] = pref.FoodsPreferentialLogGroupBySelect.PrefPrice
	res["in_price"] = pref.PriceMarkupFood.InPrice
	res["begin_time"] = tools.TimeFormatYmdHis(&pref.StartDateTime)
	res["end_time"] = tools.TimeFormatYmdHis(&pref.EndDateTime)
	res["created_at"] = tools.TimeFormatYmdHis(&pref.CreatedAt)
	res["user_max_order_count"] = pref.MaxOrderCount
	if pref.SpecID > 0 && pref.SelectedSpec.ID > 0 {
		res["spec_options"] = pref.SelectedSpec.GetOptions(*pref.SelectedSpec,t.language)
	}
	if pref.RestaurantFood.ComboFoodItems != nil {
		res["combo_food_items"] = pref.RestaurantFood.GetComboItems(pref.RestaurantFood,t.language)
	}
	items := make([]map[string]interface{}, 0)
	item := make(map[string]interface{})
	item["price"] =pref.DiscountPrice
	item["count"] =pref.PriceMarkupFood.TotalCount
	item["saled_count"] =pref.PriceMarkupSaledCount
	item["in_price"] =pref.PriceMarkupFood.InPrice
	item["profit"] = (tools.ToInt(pref.DiscountPrice)-pref.PriceMarkupFood.InPrice ) * pref.PriceMarkupSaledCount
	item["created_at"] = tools.TimeFormatYmdHis(&pref.CreatedAt)
	item["start_time"] = tools.TimeFormatYmdHis(&pref.StartDateTime)
	item["end_time"] = tools.TimeFormatYmdHis(&pref.EndDateTime)
	if pref.FoodsPreferentialLogGroupBySelect.PrefID > 0 {
		item["start_time"] = tools.TimeFormatYmdHis(&pref.FoodsPreferentialLogGroupBySelect.BeginSellTime)
		item["end_time"] = tools.TimeFormatYmdHis(&pref.FoodsPreferentialLogGroupBySelect.EndSellTime)
	}
	items = append(items, item)
	res["steps"] = items
	return res
}

func (t PriceMarkupTransformer) FormatPriceMarkupSeckillLogList(markupList []models.SeckillLog) []map[string]interface{}{
	items := make([]map[string]interface{}, 0)
	for _, v := range markupList {
		item := make(map[string]interface{})
		item["order_id"] = v.OrderId
		item["saled_price"] =v.SeckillPrice
		item["saled_count"] =v.SaledCount
		item["created_at"] = tools.If(v.OrderToday.ID>0,tools.TimeFormatYmdHis(&v.OrderToday.CreatedAt),tools.TimeFormatYmdHis(&v.Order.CreatedAt))
		item["delivery_end_time"] = tools.If(v.OrderToday.ID>0,tools.TimeFormatYmdHis(&v.OrderToday.DeliveryEndTime),tools.TimeFormatYmdHis(v.Order.DeliveryEndTime))
		item["mobile"] = tools.If(v.OrderToday.ID>0,v.OrderToday.Mobile,v.Order.Mobile)
		item["state"] = tools.If(v.OrderToday.ID>0,v.OrderToday.State,v.Order.State)
		item["state_name"] = t.langUtil.TArr("order_state")[tools.If(v.OrderToday.ID>0,v.OrderToday.State,v.Order.State)]
		item["address"] = tools.If(v.OrderToday.ID>0,v.OrderToday.OrderAddress,v.Order.OrderAddress)
		items = append(items, item)
	}
	return items
}	

func (t PriceMarkupTransformer) FormatPriceMarkupPrefLogList(markupList []models.FoodsPreferentialLog) []map[string]interface{}{
	items := make([]map[string]interface{}, 0)
	for _, v := range markupList {
		item := make(map[string]interface{})
		item["order_id"] = v.OrderId
		item["saled_price"] =v.PrefPrice
		item["saled_count"] =v.SaledCount
		item["created_at"] = tools.If(v.OrderToday.ID>0,tools.TimeFormatYmdHis(&v.OrderToday.CreatedAt),tools.TimeFormatYmdHis(&v.Order.CreatedAt))
		item["delivery_end_time"] = tools.If(v.OrderToday.ID>0,tools.TimeFormatYmdHis(&v.OrderToday.DeliveryEndTime),tools.TimeFormatYmdHis(v.Order.DeliveryEndTime))
		item["mobile"] = tools.If(v.OrderToday.ID>0,v.OrderToday.Mobile,v.Order.Mobile)
		item["state"] = tools.If(v.OrderToday.ID>0,v.OrderToday.State,v.Order.State)
		item["state_name"] = t.langUtil.TArr("order_state")[tools.If(v.OrderToday.ID>0,v.OrderToday.State,v.Order.State)]
		item["address"] = tools.If(v.OrderToday.ID>0,v.OrderToday.OrderAddress,v.Order.OrderAddress)
		items = append(items, item)
	}
	return items
}

