package cms

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/lang"
	"mulazim-api/models"
	lotRes "mulazim-api/resources/cms/lottery"
	"mulazim-api/tools"
	"time"
)

type LotteryChanceTransformer struct {
	langUtil *lang.LangUtil
	language string
}

// NewLotteryChanceTransformer
//
//	@param ctx
//	@return *LotteryChanceTransformer
func NewLotteryChanceTransformer(ctx *gin.Context) *LotteryChanceTransformer {
	l, _ := ctx.Get("lang_util")
	language, _ := ctx.Get("lang")

	langUtil := l.(lang.LangUtil)

	transformer := LotteryChanceTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}

	return &transformer
}

// FormatList 格式化返回数据
//
//	@receiver t
//	@param count
//	@param list
//	@return lottery.LotteryChancesResponse
func (trns *LotteryChanceTransformer) FormatList(list []models.LotteryChance) []lotRes.LotteryChanceResponse {
	var result []lotRes.LotteryChanceResponse
	lang := trns.language

	for _, item := range list {
		orderId, orderPrice, orderTime := "", uint(0), ""
		if item.Order.ID != 0 {
			orderId = item.Order.OrderID
			orderPrice = item.Order.Price
			orderTime = item.Order.CreatedAt.Format(time.DateTime)
		} else if item.OrderToday.ID != 0 {
			orderId = item.OrderToday.OrderID
			orderPrice = item.OrderToday.Price
			orderTime = item.OrderToday.CreatedAt.Format(time.DateTime)
		}

		nItem := lotRes.LotteryChanceResponse{
			ID:                         item.ID,
			CityId:                     item.CityId,
			AreaId:                     item.AreaId,
			BuildingId:                 item.BuildingId,

			LotteryActivityID: item.LotteryActivityID,
			//LotteryActivityName:    tools.If(lang == "zh", item.LotteryActivity.NameZh, item.LotteryActivity.NameUg),
			Type:                   item.Type,
			TypeID:                 item.TypeID,
			UserID:                 item.UserID,
			State:                  item.State,
			LotteryActivityLevelID: item.LotteryActivityLevelID,
			PrizeOpenTime:          item.PrizeOpenTime.Format(time.DateTime),
			PrizeOpenState:         item.PrizeOpenState,
			PrizeID:                item.PrizeID,

			DrawIndex:         item.DrawIndex,
			LotteryPrizeName:  tools.If(lang == "zh", item.LotteryPrize.NameZh, item.LotteryPrize.NameUg),
			LotteryPrizePrice: item.LotteryPrize.Price / 100,
			UserOrderPrice:    item.UserOrderPrice / 100,
			OrderPrice:        orderPrice / 100,
			OrderID:           orderId,
			CityName:          tools.If(lang == "zh", item.City.NameZh, item.City.NameUg),
			BuildingAddress: item.UserBuilding.Address,
			AreaName:          tools.If(lang == "zh", item.Area.NameZh, item.Area.NameUg),
			UserName:          item.User.Name,
			UserMobile:        item.User.Mobile,
			OrderTime:         orderTime,
		}
		result = append(result, nItem)
	}

	return result
}
