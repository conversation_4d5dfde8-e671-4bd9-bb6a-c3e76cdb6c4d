package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/requests/cms/advert/advert"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
)

type AdvertTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func (t *AdvertTransformer) FormatAdvertList(list []models.Advert) []advert.AdvertListResponse {

	var items []advert.AdvertListResponse
	for _, adver := range list {
		var linkName string
		if adver.LinkType == 1 {
			linkName = tools.GetNameByLang(adver.Restaurant, t.language)
		}else if adver.LinkType == 2 {
			linkName = tools.GetNameByLang(adver.RestaurantFood, t.language)
		}else{
			linkName = adver.LinkUrl
		}
		item := advert.AdvertListResponse{
			Id:                  adver.ID,
			AdvertPositionId:     adver.AdverPositionId,
			AdvertPositionName:   tools.If(t.language == "ug", adver.AdvertPosition.Description, adver.AdvertPosition.DescriptionZh),
			AreaId:              adver.AreaId,
			AreaName:            tools.GetNameByLang(adver.Area, t.language),
			Content:             adver.Content,
			Image:               tools.AddCdn(adver.ImageUrl),
			LinkType:            adver.LinkType,
			LinkTypeName:        t.langUtil.TArr("adv_link_type")[int(adver.LinkType)],
			LinkName:            linkName,
			LinkUrl:             linkName,
			MiniProgramId:       adver.MiniProgramId,
			MiniProgramLinkPage: adver.MiniProgramLinkPage,
			StartTime:           adver.TimeBegin,
			EndTime:             adver.TimeEnd,
			StartDate:           adver.StartTime.Format(time.DateOnly),
			EndDate:             adver.EndTime.Format(time.DateOnly),
			State:               adver.State,
			LangId:              adver.LangId,
			StateName:           t.langUtil.TArr("foods_preferential_states")[int(adver.State)],
			UpdatedAt:           adver.UpdatedAt.Format(time.DateTime),
			CreatedAt:           adver.CreatedAt.Format(time.DateTime),
			Weight:                adver.Weight,
		}
		items = append(items, item)
	}
	return items
}

func (t *AdvertTransformer) FormatCanvasTemplate(templates []models.PosterTemplate) []advert.CanvasAdvertTemplateResponse {
	var items []advert.CanvasAdvertTemplateResponse
	for _, template := range templates {
		var item = advert.CanvasAdvertTemplateResponse{}
		item.Id = template.ID
		item.CoverUg = tools.AddCdn(template.CoverUg)
		item.CoverZh = tools.AddCdn(template.CoverZh)
		item.Width = template.Width
		item.Height = template.Height
		item.Crop = tools.If(template.Crop == 1, true, false)
		items = append(items, item)
	}
	return items
}

func (t *AdvertTransformer) FormatAdvertDetail(advertModel models.Advert) advert.AdvertDetailResponse {
	var item  advert.AdvertDetailResponse
	item.Id = advertModel.ID
	item.AdvertPositionId = advertModel.AdverPositionId
	item.AreaName = tools.GetNameByLang(advertModel.Area, t.language)
	item.State = advertModel.State
	item.Content = advertModel.Content
	item.Weight = advertModel.Weight
	item.LinkType = advertModel.LinkType
	item.LinkUrl = advertModel.LinkUrl
	item.LinkId = advertModel.LinkId
	item.StartTime = advertModel.TimeBegin
	item.EndTime = advertModel.TimeEnd
	item.StartDate = advertModel.StartTime.Format(time.DateOnly)
	item.EndDate = advertModel.EndTime.Format(time.DateOnly)
	item.LangId = advertModel.LangId
	item.ImageUrl = tools.AddCdn(advertModel.ImageUrl)
	item.Image = advertModel.ImageUrl
	item.MiniProgramId = advertModel.MiniProgramId
	item.MiniProgramLinkPage = advertModel.MiniProgramLinkPage
	if advertModel.LinkType == 1 {
		item.RestaurantName = tools.GetNameByLang(advertModel.Restaurant, t.language)
	}
	if advertModel.LinkType == 2 {
		item.FoodName = tools.GetNameByLang(advertModel.RestaurantFood, t.language)
	}
	return item
}

func NewAdvertTransformer(c *gin.Context) *AdvertTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	advertTransformer := AdvertTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &advertTransformer
}
