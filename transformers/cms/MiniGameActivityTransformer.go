package cms

import (
	"github.com/gin-gonic/gin"
	"math"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources/cms"
	"mulazim-api/tools"
	"time"
)

type MiniGameActivityTransformer struct {
	langUtil *lang.LangUtil
	language string
}

// ActivityListFormat
//
//  @Author: YaKupJan
//  @Date: 2024-11-13 15:34:38
//  @Description: 格式化游戏活动列表
//  @receiver m
//  @param miniGameActivities
//  @return []map[string]interface{}
func (m MiniGameActivityTransformer) ActivityListFormat(miniGameActivities []models.MiniGameActivity) []map[string]interface{} {
	var items []map[string]interface{}
	for _, miniGameActivity := range miniGameActivities {
		item := map[string]interface{}{
			"id":             miniGameActivity.ID,
			"name":           tools.GetNameByLang(miniGameActivity, m.language),
			"name_zh":        miniGameActivity.NameZh,
			"name_ug":        miniGameActivity.NameUg,
			"start_time":     miniGameActivity.StartTime.Format("2006-01-02 15:04:05"),
			"end_time":       miniGameActivity.EndTime.Format("2006-01-02 15:04:05"),
			"start_use_time": miniGameActivity.StartUseTime.Format("2006-01-02 15:04:05"),
			"end_use_time":   miniGameActivity.EndUseTime.Format("2006-01-02 15:04:05"),
			"state":          miniGameActivity.State,
			"type":           miniGameActivity.Type,
			"admin_id":       miniGameActivity.AdminId,
			"created_at":     miniGameActivity.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":     miniGameActivity.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		items = append(items, item)
	}
	return items
}

func (m MiniGameActivityTransformer) ActivityDetailFormat(miniGameActivity *models.MiniGameActivity) map[string]interface{} {

	return map[string]interface{}{
		"id":                  miniGameActivity.ID,
		"name_ug":             miniGameActivity.NameUg,
		"name_zh":             miniGameActivity.NameZh,
		"rule_ug":             miniGameActivity.RuleUg,
		"rule_zh":             miniGameActivity.RuleZh,
		"game_rule_ug":        miniGameActivity.GameRuleUg,
		"game_rule_zh":        miniGameActivity.GameRuleZh,
		"use_amount_rule_ug":  miniGameActivity.UseAmountRuleUg,
		"use_amount_rule_zh":  miniGameActivity.UseAmountRuleZh,
		"start_time":          miniGameActivity.StartTime.Format("2006-01-02 15:04:05"),
		"end_time":            miniGameActivity.EndTime.Format("2006-01-02 15:04:05"),
		"start_use_time":      miniGameActivity.StartUseTime.Format("2006-01-02 15:04:05"),
		"end_use_time":        miniGameActivity.EndUseTime.Format("2006-01-02 15:04:05"),
		"state":               miniGameActivity.State,
		"type":                miniGameActivity.Type,
		"admin_id":            miniGameActivity.AdminId,
		"created_at":          miniGameActivity.CreatedAt.Format("2006-01-02 15:04:05"),
		"updated_at":          miniGameActivity.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}

func (m MiniGameActivityTransformer) ActivityStatisticFormat(statistics []models.MiniGameActivityUserForCmsStatics, pagination tools.Pagination, allAttendCount int64, sendAmount int64, usedAmount int64, allPlayCount int64, allTakeOrderCount int64) cms.MiniGameActivityStatisticByNumber {
	var items []cms.MiniGameActivityStatistics
	for i, statistic := range statistics {
		index := (pagination.Page-1)*pagination.Limit + i + 1
		item := cms.MiniGameActivityStatistics{
			Index:          index,
			ID:             statistic.ID,
			AreaName:       tools.GetNameByLang(statistic.Area, m.language),
			UserName:       statistic.User.Name,
			Mobile:         statistic.User.Mobile,
			GameCount:      statistic.GameCount,
			AllAmount:      float64(statistic.AllAmount),
			SpendAmount:    statistic.SpendAmount,
			TakeOrderCount: statistic.TakeOrderCount,
		}
		items = append(items, item)
	}
	data := cms.MiniGameActivityStatisticByNumber{
		AllAttendCount: allAttendCount,
		SendAmount:     sendAmount,
		UsedAmount:     usedAmount,
		AllPlayCount:   allPlayCount,
		UseOrderCount:  allTakeOrderCount,
		Total:          allAttendCount,
		Items:          tools.If(items!=nil,items,[]cms.MiniGameActivityStatistics{}),
	}
	return data
}

// ListMiniGameFormat
//
//  @Author: YaKupJan
//  @Date: 2024-11-15 18:40:32
//  @Description: 格式化 游戏列表
//  @receiver m
//  @param data
//  @return []map[string]interface{}
func (m MiniGameActivityTransformer) ListMiniGameFormat(data []models.MiniGame) []map[string]interface{} {
	var items []map[string]interface{}
	for _, da := range data {
		item := map[string]interface{}{
			"id":   da.ID,
			"name": tools.GetNameByLang(da, m.language),
			"remark": tools.GetNameByLangAndColumn(da, m.language,"Remark"),
			"image": tools.AddCdn(da.Image),
			"created_at": da.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at": da.UpdatedAt.Format("2006-01-02 15:04:05"),
			"category_name": tools.GetNameByLang(da.MiniGameCategory, m.language),
		}
		items = append(items, item)
	}
	return items
}

// UseDiscountStatisticsFormat
//
//  @Author: YaKupJan
//  @Date: 2024-11-20 11:22:49
//  @Description: 游戏优惠使用统计 格式化
//  @receiver m
//  @param statistics
//  @return []map[string]interface{}
func (m MiniGameActivityTransformer) UseDiscountStatisticsFormat(statistics []models.MiniGameUserLog, numbers cms.UseDiscountStatisticsByNumber) cms.UseDiscountStatisticsByNumber {
	var items []cms.UseDiscountStatistics
	for _, statistic := range statistics {
		orderNo := ""
		amount := math.Abs(float64(statistic.Amount))
		if statistic.TOrderToday.ID != 0 {
			orderNo = statistic.TOrderToday.OrderId
		} else {
			orderNo = statistic.TOrder.OrderId
		}
		item := cms.UseDiscountStatistics{
			AreaName:       tools.GetNameByLang(statistic.Area, m.language),
			RestaurantName: tools.GetNameByLang(tools.If(statistic.TOrder.ID>0,statistic.TOrder.Restaurant,statistic.TOrderToday.Restaurant), m.language),
			OrderNo:        orderNo,
			OriginalPrice:  float64(tools.If(statistic.TOrder.ID>0,statistic.TOrder.ActualPaid,statistic.TOrderToday.ActualPaid)) + amount,
			OrderPercent:   statistic.OrderPercent,
			Discount:       amount,
			ActualPaid:     tools.If(statistic.TOrder.ID>0,statistic.TOrder.ActualPaid,statistic.TOrderToday.ActualPaid),
			CreatedAt:      statistic.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		items = append(items, item)
	}
	numbers.Items = items
	return numbers
}

func (m MiniGameActivityTransformer) ActivityStatisticsByAreaFormat(data []models.MiniGameActivityUserForCmsStatics) []cms.ActivityStatisticsByAreaFormat {
	var items []cms.ActivityStatisticsByAreaFormat
	for _, d := range data {
		item := cms.ActivityStatisticsByAreaFormat{
			AreaName:       tools.GetNameByLang(d.Area, m.language),
			ActivityName:   tools.GetNameByLang(d.MiniGameActivity, m.language),
			AllAttendCount: int64(d.GameCount),
			SendAmount:     int64(d.AllAmount),
			UsedAmount:     int64(d.SpendAmount),
		}
		items = append(items, item)
	}
	return items
}

func (m MiniGameActivityTransformer) UseDiscountStatisticsByAreaFormat(data []models.MiniGameActivityUserForCmsStatics) []cms.UseDiscountStatisticsByAreaFormat {
	var items []cms.UseDiscountStatisticsByAreaFormat
	for _, d := range data {
		item := cms.UseDiscountStatisticsByAreaFormat{
			AreaName:       tools.GetNameByLang(d.Area, m.language),
			ActivityName:   tools.GetNameByLang(d.MiniGameActivity, m.language),
			AllAttendCount: int64(d.GameCount),
			SendAmount:     int64(d.AllAmount),
			UsedAmount:     int64(d.SpendAmount),
			TakeOrderCount: int64(d.TakeOrderCount),
			NewUserCount: d.NewUserCount,
		}
		items = append(items, item)
	}
	return items
}

// 格式化 获取用户提交的情人节活动内容列表
func (m MiniGameActivityTransformer) ListContentFromUserFormat(listContentFromUser []models.MiniGameActivityUserForm) []cms.MiniGameActivityListContentFromUserResponse {
	var items []cms.MiniGameActivityListContentFromUserResponse

	// 遍历用户提交的内容列表
	for _, data := range listContentFromUser {
		// 初始化默认值
		image := ""            // 图片 URL，默认为空
		content := ""          // 文本内容，默认为空
		adminID := 0           // 审核管理员 ID，默认为 0
		refuseContent := ""    // 审核拒绝原因，默认为空
		reviewTime := time.Time{} // 审核时间，默认为零值

		// 处理可选字段，避免 nil 指针错误
		if data.Image != nil {
			image = *data.Image
		}
		if data.Content != nil {
			content = *data.Content // 提取用户提交的内容
		}
		if data.AdminID != nil {
			adminID = *data.AdminID // 提取审核管理员 ID
		}
		if data.ReviewTime != nil {
			reviewTime = *data.ReviewTime // 提取审核时间
		}
		if data.RefuseContent != nil {
			refuseContent = *data.RefuseContent // 提取审核拒绝理由
		}

		// 检查 reviewTime 是否为零值，若是则设置为空字符串
		reviewTimeStr := ""
		if !reviewTime.IsZero() {
			reviewTimeStr = reviewTime.Format("2006-01-02 15:04:05")
		}
		// 构造响应数据结构
		item := cms.MiniGameActivityListContentFromUserResponse{
			ID:            data.ID, // 用户提交内容的唯一 ID
			UserName:      tools.If(data.User.ID != 0, data.User.Name, ""), // 根据 User ID 是否为 0，决定是否赋值用户名
			UserID:        data.UserID, // 用户 ID
			Gender:        data.Gender, // 用户性别
			GenderName:    m.langUtil.TArr("gender")[data.Gender], // 通过语言工具获取性别名称
			AreaName:      tools.GetNameByLang(data.Area, m.language), // 获取区域名称（支持多语言）
			AreaID:        data.AreaID, // 区域 ID
			CityName:      tools.GetNameByLang(data.City, m.language), // 获取城市名称（支持多语言）
			CityID:        data.CityID, // 城市 ID
			Mobile:        data.Mobile, // 用户手机号
			Image:         tools.CdnUrl(image), // 处理后的图片 URL
			Content:       content, // 用户提交的内容
			CreatedAt:     data.CreatedAt.Format("2006-01-02 15:04:05"), // 格式化创建时间
			ShareCount:    data.ShareCount, // 分享次数
			FavoriteCount: data.FavoriteCount, // 收藏次数
			ViewCount:     data.ViewCount, // 浏览次数
			State:         data.State, // 当前审核状态
			StateName:     m.langUtil.TArr("review_state")[data.State + 1], // 审核状态名称
			AdminName:     data.Admin.Name, // 审核管理员名称
			AdminID:       adminID, // 审核管理员 ID
			ReviewTime:    reviewTimeStr, // 审核时间格式化
			RefuseContent: refuseContent, // 审核拒绝理由
		}

		// 将转换后的数据添加到返回列表
		items = append(items, item)
	}

	// 确保返回的列表不会为 nil，而是返回空数组
	if len(items) == 0 {
		items = []cms.MiniGameActivityListContentFromUserResponse{}
	}

	return items
}


func NewMiniGameActivityTransformer(c *gin.Context) *MiniGameActivityTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	transformer := MiniGameActivityTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &transformer
}
