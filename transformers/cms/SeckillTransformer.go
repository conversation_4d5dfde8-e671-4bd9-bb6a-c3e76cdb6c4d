package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type SeckillTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func NewSeckillTransformer(c *gin.Context) *SeckillTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	seckillTransformer := SeckillTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &seckillTransformer
}


// FormatMenu
//
//	@Description: 格式化菜单数据
//	@author: Alimjan
//	@Time: 2023-06-05 13:26:07
//	@receiver t CmsTransformer
//	@param admin models.Admin
//	@param menus []cms.Menus
//	@param badge int64
//	@return interface{}
func (t SeckillTransformer) FormatSeckillList(seckills []models.Seckill) []map[string]interface{}{
	items := make([]map[string]interface{}, 0)
	for _, seckill := range seckills {
		item := make(map[string]interface{})
		item["id"] = seckill.ID
		item["price_markup_id"] = seckill.PriceMarkupID
		item["creator_name"] = seckill.Admin.RealName
		item["restaurant_name"] = tools.GetNameByLang(seckill.Restaurant,t.language)
		item["food_name"] = tools.GetNameByLang(seckill.RestaurantFoods,t.language)
		item["seckill_price"] = seckill.Price
		item["original_price"] = seckill.RestaurantFoods.Price
		if seckill.FoodType == models.RestaurantFoodsTypeSpec {
			item["original_price"] = seckill.SelectedSpec.Price
		}
		item["user_max_order_count"] = seckill.UserMaxOrderCount
		item["saled_count"] = seckill.SaledCount
		item["total_count"] = seckill.TotalCount
		item["begin_time"] = carbon.Parse(seckill.BeginTime).Format("Y-m-d H:i:s")
		item["end_time"] = carbon.Parse(seckill.EndTime).Format("Y-m-d H:i:s")
		item["restaurant_state"] = seckill.Restaurant.State
		item["food_state"] = seckill.RestaurantFoods.State
		item["food_state_name"] =  t.langUtil.T("normal")
		if seckill.RestaurantFoods.State == 0 {
			item["food_state_name"] = t.langUtil.T("foods_close_state")
		}
		if seckill.Restaurant.State == 0 {
			item["food_state_name"] =  t.langUtil.T("restaurant_close_state")
			item["food_state"] = 0
		}
		// item["order_time"] = tools.TimeFormatYmdHis(&seckill.OrderTime) 
		item["order"] = seckill.Order
		item["state"] = seckill.State
		item["run_state"] = t.GetSeckillRunState(seckill)
		item["run_state_name"] = t.langUtil.TArr("seckill_run_state_name")[tools.ToInt(item["run_state"])]
		item["review_state"] = seckill.ReviewState
		item["food_type"] = seckill.FoodType
		item["spec_id"] = seckill.SpecID
		item["created_at"] = tools.TimeFormatYmdHis(&seckill.CreatedAt)
		priceMarkupStep := make([]map[string]interface{},0)
		maxPrice := 0
		minPrice := 0
		if len(seckill.PriceMarkupSeckillPriceLog)>0{
			if seckill.PriceMarkupID > 0 {
				maxPrice = seckill.PriceMarkupSeckillPriceLog[0].Price
				minPrice = seckill.PriceMarkupSeckillPriceLog[0].Price
			}
			for _, v := range seckill.PriceMarkupSeckillPriceLog {
				priceMarkupStepItem := make(map[string]interface{})
				priceMarkupStepItem["id"] = v.ID
				priceMarkupStepItem["saled_count"] = v.PriceMarkupFoodLogSum.SaledCount
				priceMarkupStepItem["count"] = v.Count
				priceMarkupStepItem["price"] = v.Price
				priceMarkupStepItem["in_price"] = seckill.PriceMarkupFood.InPrice
				priceMarkupStepItem["profit"] =  (v.Price - seckill.PriceMarkupFood.InPrice) * v.PriceMarkupFoodLogSum.SaledCount
				priceMarkupStep = append(priceMarkupStep, priceMarkupStepItem)
				if v.Price > maxPrice {
					maxPrice = v.Price
				}
				if v.Price < minPrice {
					minPrice = v.Price
				}
			}
		}

		// 规格
		if seckill.SpecID > 0 && seckill.SelectedSpec.ID > 0 {
			item["spec_options"] = seckill.SelectedSpec.GetOptions(*seckill.SelectedSpec,t.language)
		}
		// 套餐
		if seckill.RestaurantFoods.ComboFoodItems != nil {
			item["combo_food_items"] = seckill.RestaurantFoods.GetComboItems(seckill.RestaurantFoods,t.language)
		}
		item["price_markup_min_price"] = minPrice
		item["price_markup_max_price"] = maxPrice
		item["price_markup_step"] = priceMarkupStep
		items = append(items, item)
	}
	return items
}

func (t SeckillTransformer) FormatSeckillLogList(seckills []models.SeckillLog) []map[string]interface{}{
	items := make([]map[string]interface{}, 0)
	for _, v := range seckills {
		item := make(map[string]interface{})
		item["id"] = v.ID
		item["user_name"] = v.User.Name
		item["order_id"] = v.OrderId
		item["restaurant_name"] = tools.GetNameByLang(v.Seckill.Restaurant,t.language)
		item["city_name"] = tools.GetNameByLang(v.Seckill.City,t.language)
		item["area_name"] = tools.GetNameByLang(v.Seckill.Area,t.language)
		item["restaurant_name"] = tools.GetNameByLang(v.Seckill.Restaurant,t.language)
		item["food_name"] = tools.GetNameByLang(v.Food,t.language)
		item["saled_count"] = v.SaledCount
		item["seckill_price"] = v.SeckillPrice
		item["food_type"] = v.FoodType
		item["created_at"] = tools.TimeFormatYmdHis(&v.CreatedAt)

		// 规格
		if v.SpecID > 0 && v.SelectedSpec.ID > 0 {
			item["spec_options"] = v.SelectedSpec.GetOptions(v.SelectedSpec,t.language)
		}
		// 套餐
		if v.FoodType == models.RestaurantFoodsTypeCombo {
			item["combo_food_items"] = v.RestaurantFoods.GetComboItems(v.RestaurantFoods,t.language)
		}
		items = append(items, item)

	}
	return items
}

func (t SeckillTransformer) FormatSeckillDetail(seckill models.Seckill) map[string]interface{}{
	item := make(map[string]interface{})
	if seckill.ID == 0 {
		return item
	}
	item["id"] = seckill.ID
	item["creator_name"] = seckill.Admin.RealName
	item["price_markup_id"] = seckill.PriceMarkupID
	item["restaurant_id"] = seckill.Restaurant.ID
	item["restaurant_name"] = tools.GetNameByLang(seckill.Restaurant,t.language)
	item["restaurant_state"] = seckill.Restaurant.State
	item["food_id"] = seckill.RestaurantFoods.ID
	item["food_name"] = tools.GetNameByLang(seckill.RestaurantFoods,t.language)
	item["food_image"] = tools.CdnUrl(seckill.RestaurantFoods.Image)
	item["seckill_price"] = seckill.Price
	item["food_in_price"] = seckill.PriceMarkupFood.InPrice
	item["original_price"] = seckill.RestaurantFoods.Price
	item["user_max_order_count"] = seckill.UserMaxOrderCount
	item["saled_count"] = seckill.SaledCount
	item["price_markup_total_count"] =  seckill.PriceMarkupFood.TotalCount
	item["can_use_count"] =  seckill.PriceMarkupFood.CanUseCount()
	item["total_count"] = seckill.TotalCount
	item["begin_time"] = carbon.Parse(seckill.BeginTime).Format("Y-m-d H:i:s")
	item["end_time"] = carbon.Parse(seckill.EndTime).Format("Y-m-d H:i:s")
	item["food_state"] = seckill.RestaurantFoods.State
	item["order"] = seckill.Order
	item["state"] = seckill.State
	item["run_state"] = t.GetSeckillRunState(seckill)
	item["run_state_name"] = t.langUtil.TArr("seckill_run_state_name")[tools.ToInt(item["run_state"])]
	item["review_state"] = seckill.ReviewState
	item["created_at"] = tools.TimeFormatYmdHis(&seckill.CreatedAt)

	// 规格
	if seckill.SpecID > 0 && seckill.SelectedSpec.ID > 0 {
		item["spec_options"] = seckill.SelectedSpec.GetOptions(*seckill.SelectedSpec, t.language)
	}
	// 套餐
	if seckill.RestaurantFoods.ComboFoodItems != nil {
		item["combo_food_items"] = seckill.RestaurantFoods.GetComboItems(seckill.RestaurantFoods,t.language)
	}
	return item
}

// GetSeckillRunState
//
// @Description: 获取秒杀执行状态
// @Author: Rixat
// @Time: 2024-08-27 13:03:52
// @receiver 
// @param c *gin.Context
func (t SeckillTransformer) GetSeckillRunState(seckill models.Seckill) int {
	nowTime := carbon.Now()
	if nowTime.Between(carbon.Parse(seckill.BeginTime), carbon.Parse(seckill.EndTime)) {
		return 1
	} else if nowTime.Lt(carbon.Parse(seckill.BeginTime)) {
		return 0
	} else {
		return 2
	}
}