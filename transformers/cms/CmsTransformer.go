package cms

import (
	"fmt"
	"math"
	"mulazim-api/lang"
	"mulazim-api/models"
	cmsModels "mulazim-api/models/cms"
	cmsResource "mulazim-api/resources/cms"
	"mulazim-api/tools"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type CmsTransformer struct {
	langUtil *lang.LangUtil
	language string
}

// FormatMenu
//
//	@Description: 格式化菜单数据
//	@author: Alimjan
//	@Time: 2023-06-05 13:26:07
//	@receiver t CmsTransformer
//	@param admin models.Admin
//	@param menus []cms.Menus
//	@param badge int64
//	@return interface{}
func (t CmsTransformer) FormatMenu(admin models.Admin, menus []cmsModels.Menus, badge int64,lang string) (interface{},[]string,interface{}) {
	var exceptMenu []string
	if admin.Type == 1 {
		exceptMenu = append(exceptMenu, "permission.*")
		exceptMenu = append(exceptMenu, "menu.*")
		exceptMenu = append(exceptMenu, "role.*")
	}
	var data = make([]map[string]interface{}, 0)

	for _, menu := range menus {

		var item = make(map[string]interface{}, 0)
		item["text"] = menu.NameUg
		item["text_ug"] = menu.NameUg
		item["text_zh"] = menu.NameZh
		item["id"] = menu.ID
		item["active_class"] = menu.ActiveClass
		item["icon_class"] = menu.IconClass
		var subItems = make([]map[string]interface{}, 0)
		if admin.Type != 1 && item["active_class"] == "active_notification" {
			item["badge"] = badge
		}

		for _, child := range menu.Childs {
			if tools.InArray(child.Perm.Name, exceptMenu) {
				continue
			}
			if admin.Type != 1 {
				can := false
				for _, role := range admin.Roles {
					for _, permission := range role.Permissions {
						match, _ := regexp.MatchString(child.Perm.Name, permission.Name)
						if match {
							can = true
							break
						}
					}
				}
				if can == false {
					continue
				}
			}
			var subItem = make(map[string]interface{}, 0)
			subItem["text"] = child.Perm.DisplayNameUg
			subItem["text_ug"] = child.Perm.DisplayNameUg
			subItem["text_zh"] = child.Perm.DisplayNameZh
			subItem["active_class"] = child.ActiveClass
			subItem["id"] = child.ID
			subItem["url"] = strings.Replace(child.Perm.Url, ":lang", lang, -1)
			subItem["url_ug"] = strings.Replace(child.Perm.Url, ":lang", "ug", -1)
			subItem["url_zh"] = strings.Replace(child.Perm.Url, ":lang", "zh", -1)
			subItems = append(subItems, subItem)
		}
		item["sub_menu"] = subItems

		if len(subItems) > 0 {
			data = append(data, item)
		}
	}
	roles :=make([]map[string]interface{},0)
	permissions :=make([]string,0)
	for _, role := range admin.Roles {
		for _, permission := range role.Permissions {
			permissions = append(permissions, permission.Name)
		}
		roleMap :=make(map[string]interface{})
		roleMap["id"]=role.ID
		roleMap["name"]=role.Name
		roleMap["display_name_ug"]=role.DisplayNameUg
		roleMap["display_name_zh"]=role.DisplayNameZh
		roles=append(roles, roleMap)
	}
	user :=make(map[string]interface{})
	user["id"]=admin.ID
	user["type"]=admin.Type
	user["admin_city_id"] = admin.AdminCityID
	user["admin_area_id"] = admin.AdminAreaID
	// user["avatar"]=admin.Avatar
	user["mobile"]=admin.Mobile
	user["real_name"]=admin.RealName
	user["name"]=admin.Name
	user["roles"]=roles
	return data,permissions,user
}

func (t CmsTransformer) FormatCities(cities []cmsModels.City) []cmsResource.CityListEntity {
	var data = make([]cmsResource.CityListEntity, 0)
	for _, city := range cities {
		item := cmsResource.CityListEntity{
			ID:         city.ID,
			CityNameUg: city.NameUg,
			CityNameZh: city.NameZh,
		}
		data = append(data, item)
	}
	return data
}

// FormatAreas
//
//	@Description: 格式化区域列表
//	@author: Alimjan
//	@Time: 2023-06-05 18:37:33
//	@receiver t CmsTransformer
//	@param areas []models.Area
//	@return interface{}
func (t CmsTransformer) FormatAreas(areas []models.Area) interface{} {
	var data = make([]cmsResource.AreaListEntity, 0)
	for _, area := range areas {
		item := cmsResource.AreaListEntity{
			ID:         area.ID,
			AreaNameUg: area.NameUg,
			AreaNameZh: area.NameZh,
		}
		data = append(data, item)
	}
	return data
}

// FormatStateList
//
//	@Description: 格式话状态
//	@author: Alimjan
//	@Time: 2023-06-06 15:37:39
//	@receiver t CmsTransformer
//	@param list []cms.OrderState
//	@return interface{}
func (t CmsTransformer) FormatStateList(list []cmsModels.OrderState) []cmsResource.StateList {
	var data = make([]cmsResource.StateList, 0)
	for _, state := range list {
		item := cmsResource.StateList{
			ID:        state.ID,
			StateName: state.NameUg,
		}
		data = append(data, item)
	}
	return data
}

// FormatCategoryList
//
//	@Description: 格式化业务分类
//	@author: Alimjan
//	@Time: 2023-06-06 16:10:36
//	@receiver t CmsTransformer
//	@param list []cmsModels.Category
//	@return []cmsResource.CategoryList
func (t CmsTransformer) FormatCategoryList(list []cmsModels.Category) []cmsResource.CategoryList {
	var data = make([]cmsResource.CategoryList, 0)
	for _, category := range list {
		item := cmsResource.CategoryList{
			ID:                   category.ID,
			ParentID:             category.ParentID,
			ParentCategoryNameUg: category.NameUg,
			ParentCategoryNameZh: category.NameZh,
		}
		var subItems = make([]cmsResource.CategoryListChild, 0)
		for _, child := range category.Categorys {
			subItem := cmsResource.CategoryListChild{
				ID:                  child.ID,
				ParentID:            child.ParentID,
				ChildCategoryNameUg: child.NameUg,
				ChildCategoryNameZh: child.NameZh,
			}
			subItems = append(subItems, subItem)
		}

		item.Child = subItems
		data = append(data, item)
	}
	return data
}

// FormatOrderCancelReasonList
//
//	@Description: 格式化订单取消原因列表
//	@author: Alimjan
//	@Time: 2023-06-06 16:24:35
//	@receiver t CmsTransformer
//	@param list []cmsModels.OrderFailReason
//	@return []cmsResource.ReasonList
func (t CmsTransformer) FormatOrderCancelReasonList(list []cmsModels.OrderFailReason) []cmsResource.ReasonList {
	var data = make([]cmsResource.ReasonList, 0)
	for _, reason := range list {
		item := cmsResource.ReasonList{
			ID:       reason.ID,
			ReasonUg: reason.ReasonUg,
			ReasonZh: reason.ReasonZh,
		}
		data = append(data, item)
	}
	return data
}

// FormatStreetList
//
//	@Description: 格式化街道列表
//	@author: Alimjan
//	@Time: 2023-06-06 17:20:37
//	@receiver t CmsTransformer
//	@param list []cmsModels.Street
//	@return []cmsResource.StreetList
func (t CmsTransformer) FormatStreetList(list []cmsModels.Street) []cmsResource.StreetList {
	var data = make([]cmsResource.StreetList, 0)
	for _, street := range list {
		item := cmsResource.StreetList{
			ID:           street.ID,
			StreetNameUg: street.NameUg,
			StreetNameZh: street.NameZh,
		}
		data = append(data, item)
	}
	return data
}

// FormatRestaurantList
//
//	@Description: 格式化餐厅列表数据
//	@author: Alimjan
//	@Time: 2023-06-06 17:55:10
//	@receiver t CmsTransformer
//	@param list []models.Restaurant
//	@return []cmsResource.RestaurantList
func (t CmsTransformer) FormatRestaurantList(list []models.Restaurant) []cmsResource.RestaurantList {
	var data = make([]cmsResource.RestaurantList, 0)
	for _, restaurant := range list {
		item := cmsResource.RestaurantList{
			ID:               restaurant.ID,
			RestaurantNameUg: restaurant.NameUg,
			RestaurantNameZh: restaurant.NameZh,
		}
		data = append(data, item)
	}
	return data
}

// FormatShipperList
//
//	@Description: 格式化骑手列表数据
//	@author: Alimjan
//	@Time: 2023-06-07 11:40:09
//	@receiver t CmsTransformer
//	@return unc (t CmsTransformer) FormatShipperList(list []models.Admin, adminOrderCount []struct
func (t CmsTransformer) FormatShipperList(list []models.Admin, adminOrderCount []struct {
	AdminId    int
	OrderCount int
}) []cmsResource.ShipperList {
	var data = make([]cmsResource.ShipperList, 0)
	for _, shipper := range list {
		item := cmsResource.ShipperList{
			ID:   shipper.ID,
			Name: shipper.RealName,
		}
		for _, c := range adminOrderCount {
			if c.AdminId == shipper.ID {
				item.OrderCount = c.OrderCount
				break
			}
		}
		data = append(data, item)
	}
	return data
}

// FormatOrderList
//
//	@Description: 格式化订单列表数据
//	@author: Alimjan
//	@Time: 2023-06-08 13:57:51
//	@receiver t CmsTransformer
//	@param list []models.TodayOrderView
//	@return cmsResource.OrderList
func (t CmsTransformer) FormatOrderList(header []map[string]interface{}, list []models.TodayOrderView) cmsResource.OrderList {
	var data = cmsResource.OrderList{}
	orderListsResouce := make([]cmsResource.OrderListTypeItem, 0)
	for _, order := range list {
		//OrderListTypeItem 初始化
		orderListTypeItem := cmsResource.OrderListTypeItem{
			ID:             int(order.Id),
			OrderID:        order.OrderId,
			OrderType:      int(order.OrderType),
			TerminalNameUg: order.TerminalNameUg,
			TerminalNameZh: order.TerminalNameZh,
			UserName:       order.User.Name,
			OrderAddress:   order.BuildingNameUg + " | " + order.OrderAddress,
			Name:           order.Name,
			Mobile:         order.Mobile,
			OriginMobile:   order.User.Mobile,
			SerialNumber:   int(order.SerialNumber),
		}

		//转换以上PHP代码
		now := time.Now()
		orderListTypeItem.LastQueryTimeColor = 1
		//判断时间LastQueryTime是否null

		if order.LoginTime != nil && order.LoginTime.LastQueryTime != nil {
			lastQueryTime := order.LoginTime.LastQueryTime
			day := now.Sub(*lastQueryTime).Hours() / 24
			hour := now.Sub(*lastQueryTime).Hours()
			minute := now.Sub(*lastQueryTime).Minutes()
			second := now.Sub(*lastQueryTime).Seconds()
			if day > 0 {
				orderListTypeItem.LastQueryTime = fmt.Sprintf("%0.0fd", day)
				orderListTypeItem.LastQueryTimeColor = 2
			} else if hour > 0 {
				orderListTypeItem.LastQueryTime = fmt.Sprintf("%0.0fh", hour)
				orderListTypeItem.LastQueryTimeColor = 2
			} else if minute > 0 {
				if minute > 4 {
					orderListTypeItem.LastQueryTimeColor = 2
				}
				orderListTypeItem.LastQueryTime = fmt.Sprintf("%0.0fm", minute)
			} else if second > 0 {
				orderListTypeItem.LastQueryTime = fmt.Sprintf("%0.0fs", second)
			}
		} else {
			orderListTypeItem.LastQueryTime = "ئىتىك"
			orderListTypeItem.LastQueryTimeColor = 2
		}

		bookingTime := order.BookingTime
		deliveryEndTime := order.DeliveryEndTime
		printedTime := order.PrintedTime

		timeDifference := -9999
		takedMinute := 0
		//判断deliveryEndTime 是否zero

		if deliveryEndTime.IsZero() == false {
			bookingTimeParsed := bookingTime
			deliveryEndTimeParsed := deliveryEndTime
			//计算时间差 bookingTimeParsed 和 deliveryEndTimeParsed 之间有多少分钟
			timeDifference = int(math.Floor(float64(bookingTimeParsed.Sub(deliveryEndTimeParsed).Minutes())))
		}

		if printedTime.IsZero() == false && deliveryEndTime.IsZero() == false {
			printedTimeParsed := printedTime
			deliveryEndTimeParsed := deliveryEndTime
			takedMinute = int(math.Floor(float64(printedTimeParsed.Sub(deliveryEndTimeParsed).Minutes())))
		}

		printedTimeFormatted := ""
		if printedTime.IsZero() == false {
			printedTimeFormatted = printedTime.Format("01-02 15:04")
			orderListTypeItem.PrintedTime = printedTimeFormatted
		}

		deliveryEndTimeFormatted := ""
		if deliveryEndTime.IsZero() == false {
			deliveryEndTimeFormatted = deliveryEndTime.Format("01-02 15:04")
			orderListTypeItem.DeliveryEndTime = deliveryEndTimeFormatted
		}

		ss := ""
		if timeDifference == -9999 {
			ss = ""
		} else {
			if timeDifference < 0 {
				ss = "<br/><span style='color: #ff0000;'>" + strconv.Itoa(takedMinute) + "<span style='color: #676a6c'>&nbsp|&nbsp</span>" + strconv.Itoa(int(math.Abs(float64(timeDifference)))) + "&nbspمىنۇت كىچىكتى</span>"
			} else {
				ss = "<br/><span style='color: #31af64;'>" + strconv.Itoa(takedMinute) + "<span style='color: #676a6c'>&nbsp|&nbsp</span>" + strconv.Itoa(int(math.Abs(float64(timeDifference)))) + "&nbspمىنۇت بۇرۇن</span>"
			}
		}
		if ss != "" {
			orderListTypeItem.DeliveryEndTime = orderListTypeItem.DeliveryEndTime + ss
		}

		if carbon.Now("Asia/Shanghai").Carbon2Time().Unix() >= order.BookingTime.Unix() {
			orderListTypeItem.TimeOut = 1
		}
		if printedTimeFormatted == deliveryEndTimeFormatted {
			orderListTypeItem.AutoPrint = 1
		}
		orderListTypeItem.OrderState = order.OrderState.NameUg

		if order.CategoryId == 1 {
			orderListTypeItem.Shipper = "ئۆزى ئېلىۋالىدۇ"
		} else {
			orderListTypeItem.Shipper = order.ShipperName
			orderListTypeItem.ShipperMobile = order.Shipper.Mobile
		}

		orderListTypeItem.CategoryID = tools.ToInt(order.CategoryId)
		orderListTypeItem.Taked = tools.ToInt(order.Taked)
		orderListTypeItem.BuildingID = tools.ToInt(order.BuildingId)
		orderListTypeItem.BuildingLat, _ = strconv.ParseFloat(order.BuildingLat, 64)
		orderListTypeItem.BuildingLng, _ = strconv.ParseFloat(order.BuildingLng, 64)
		orderListTypeItem.RestaurantID = tools.ToInt(order.StoreId)
		orderListTypeItem.RestaurantTag = order.RestaurantTag
		orderListTypeItem.RestaurantName = order.RestaurantNameUg
		orderListTypeItem.RestaurantLat, _ = strconv.ParseFloat(order.RestaurantLat, 64)
		orderListTypeItem.RestaurantLng, _ = strconv.ParseFloat(order.RestaurantLng, 64)
		orderListTypeItem.RestaurantTel = order.Restaurant.AdminTel
		orderListTypeItem.ConsumeTypeID = tools.ToInt(order.ConsumeType)
		orderListTypeItem.RefundChanel = tools.ToInt(order.RefundChanel)
		orderListTypeItem.State = tools.ToInt(order.State)
		orderListTypeItem.TotalFee = (tools.ToInt(order.Price) + tools.ToInt(order.Shipment) + tools.ToInt(order.LunchBoxFee)) / 100

		if order.ConsumeType == 0 {
			orderListTypeItem.ConsumeType = t.GetPayType("cash")
		} else if order.ConsumeType == 3 {
			orderListTypeItem.ConsumeType = t.GetPayType("agent_wechat_pay")
		} else {
			orderListTypeItem.ConsumeType = t.GetPayType("online_pay")
			orderListTypeItem.TotalFee = 0
		}
		orderListTypeItem.PayTypeID = tools.ToInt(order.PayType)
		if order.PayType == 1 {
			orderListTypeItem.PayType = t.GetPayType("cash")
		} else if order.PayType == 2 {
			orderListTypeItem.PayType = t.GetPayType("coin")
		} else if order.PayType == 3 {
			orderListTypeItem.PayType = "ئالىي پاي"
		} else if order.PayType == 4 {
			orderListTypeItem.PayType = "بانكا بىرلەشمىسى"
		} else if order.PayType == 5 {
			orderListTypeItem.PayType = "ئۈندىدار"
		} else if order.PayType == 6 {
			orderListTypeItem.PayType = "ۋاكالەتچى توردا تۆلىگەن"
		} else {
			orderListTypeItem.PayType = ""
			orderListTypeItem.TotalFee = 0
		}
		orderListTypeItem.Price = tools.ToInt(order.Price) / 100
		orderListTypeItem.OriginPrice = tools.ToInt(order.OriginalPrice) / 100
		orderListTypeItem.Shipment = tools.ToInt(order.Shipment) / 100
		orderListTypeItem.LunchBoxFee = tools.ToInt(order.LunchBoxFee) / 100
		orderListTypeItem.Description = order.Description
		orderListTypeItem.FoodSum = tools.ToString(len(order.OrderDetail))
		orderListTypeItem.CreatedAt = order.CreatedAt.Format("01-02 15:04")
		orderListTypeItem.DeliveryType = order.DeliveryType
		orderListTypeItem.PayPlatform = order.PayPlatform
		orderListTypeItem.ShipperID = int(order.ShipperId)
		orderListTypeItem.BookingTime = order.BookingTime.Format("01-02 15:04")
		var lunchBoxes []interface{}
		for _, lunchBox := range order.LunchBox {
			lunchBoxes = append(lunchBoxes, lunchBox)
		}
		orderListTypeItem.LunchBoxes = lunchBoxes
		orderListTypeItem.OrderDetail = []cmsResource.OrderListOrderDetail{}
		for _, orderDetail := range order.OrderDetail {
			if orderDetail.Number == 0 {
				continue
			}
			orderDetailTypeItem := cmsResource.OrderListOrderDetail{}
			orderDetailTypeItem.Price = int(orderDetail.Price) / 100
			orderDetailTypeItem.OriginPrice = int(orderDetail.OriginalPrice) / 100
			orderDetailTypeItem.FoodName = orderDetail.RestaurantFoods.NameUg
			if orderDetail.Price < orderDetail.OriginalPrice {
				orderDetailTypeItem.DiscountPercent = "ئېتىبار قىلىنغان"
			} else {
				orderDetailTypeItem.DiscountPercent = "ئېتىبار يوق"
			}
			orderDetailTypeItem.Number = tools.ToInt(orderDetail.Number)
			orderDetailTypeItem.SumPrice = tools.ToInt(orderDetail.Number*orderDetail.Price) / 100
			orderListTypeItem.OrderDetail = append(orderListTypeItem.OrderDetail, orderDetailTypeItem)
		}

		for _, log := range order.OrderStateLog {
			if log.OrderStateID == models.REASON_UNSUBSCRIBE_ORDER || log.OrderStateID == models.REASON_ORDER_FAIL {
				if log.FailReason != 0 {
					orderListTypeItem.FailReason = log.OrderFailReason.ReasonUg
				}
			}
		}
		if order.RefundChanel != 0 {
			orderListTypeItem.FailReason = t.GetRefundChanel(order.RefundChanel)
		}

		orderListsResouce = append(orderListsResouce, orderListTypeItem)
	}
	orderTotal := 0
	cashTotal := 0
	agentPayTotal := 0
	onlinePayTotal := 0

	for _, value := range header {
		fmt.Println("VALUE : ", value)
		//写出以上PHP 代码
		//从value中取出consume_type,判断是否存在
		if _, ok := value["consume_type"]; ok {
			value["total_fee"] = tools.ToInt(value["lunch_box_fee"]) + tools.ToInt(value["price"])
			switch tools.ToInt(value["consume_type"]) {
			case 0:
				orderTotal += tools.ToInt(value["total"])
				cashTotal += tools.ToInt(value["total_fee"])
				break
			case 3:
				agentPayTotal += tools.ToInt(value["total_fee"])
				orderTotal += tools.ToInt(value["total"])
				break
			default:
				onlinePayTotal += tools.ToInt(value["total_fee"])
				orderTotal += tools.ToInt(value["total"])
				break
			}
		} else {
			onlinePayTotal += tools.ToInt(value["total_fee"])
			orderTotal += tools.ToInt(value["total"])
		}
	}
	data.Data = orderListsResouce
	data.OrderTotal = orderTotal
	data.CashTotal = tools.FormatNumber(float64(cashTotal)/100.0, ",", ".", 2)
	data.AgentPayTotal = tools.FormatNumber(float64(agentPayTotal)/100.0, ",", ".", 2)
	data.OnlinePayTotal = tools.FormatNumber(float64(onlinePayTotal)/100.0, ",", ".", 2)
	data.TotalMoney = tools.FormatNumber(float64(cashTotal+agentPayTotal+onlinePayTotal)/100.0, ",", ".", 2)
	return data
}

func (t CmsTransformer) GetRefundChanel(key int8) string {
	refundChanelMap := map[int]string{
		1: "سىستېما ئاپتوماتىك قايتۇرغان",
		2: "باشقۇرغۇچى قايتۇرۋەتتى",
		3: "ئاشخانا قايتۇرۋەتتى",
		4: "خېرىدار قايتۇرۋەتتى",
	}
	return refundChanelMap[int(key)]
}
func (t CmsTransformer) GetPayType(key string) string {
	payTypeMap := map[string]string{
		"cash":             "نەق پۇل",
		"coin":             "تاماق تەڭگىسى",
		"online_pay":       "توردا تۆلەش",
		"agent_wechat_pay": "ۋاكالەتچى ئۈندىداردا تۆلىگەن",
	}
	return payTypeMap[key]
}

var menuUrl = map[string]string{
	"todayOrder.*":           "/ug/v2/today-order",
	"prevOrder.*":            "/ug/prev-order",
	"quickOrder.*":           "/ug/quick-order",
	"map.*":                  "/ug/map",
	"shipperCanceled.*":      "/ug/shipper-canceled",
	"notification.list.*":    "/ug/notification",
	"restaurant.*":           "/ug/restaurant/list",
	"merchantProcess.*":      "/ug/merchant-process",
	"comment.list.*":         "/ug/comment",
	"storeApply.*":           "/ug/store-apply",
	"restaurant.foods.*":     "/ug/restaurant/foods/list",
	"resFood.foods.*":        "/ug/restaurant-food/foods/list",
	"resFood.category.*":     "/ug/restaurant-food/category/list",
	"resFood.allFoods.*":     "/ug/restaurant-food/all-foods/list",
	"lunchBox.*":             "/ug/restaurant-food/box",
	"foods-preferential.*":   "/ug/foods-preferential/list",
	"city.*":                 "/ug/address/city/list",
	"area.*":                 "/ug/address/area/list",
	"street.*":               "/ug/address/street/list",
	"building.*":             "/ug/address/building/list",
	"buildingType.*":         "/ug/address/btype/list",
	"special.shipment.*":     "/ug/address/shipment",
	"user.*":                 "/ug/user/list",
	"user.statistic.*":       "/ug/user/statistic-regist-user",
	"admin.*":                "/ug/admin",
	"role.*":                 "/ug/admin/role",
	"permission.*":           "/ug/admin/permission",
	"distribution.*":         "/ug/admin/distribution",
	"cashClear.*":            "/ug/admin/cash-clear",
	"market.consume.*":       "/ug/market/recharge-log/list",
	"market.packet.*":        "/ug/market/offer-packet",
	"finance.cashout.*":      "/ug/finance/agent-cash-out/list",
	"market.packetlog.*":     "/ug/market/offer-packet-log",
	"market.activities.*":    "/ug/market/marketing-activities",
	"market.statistics.*":    "/ug/market/marketing-statistics",
	"advert.*":               "/ug/advert/advert/list",
	"notice.*":               "/ug/advert/notice/list",
	"merchantAdvert.*":       "/ug/advert/merAdvert/list",
	"about.*":                "/ug/setting/about/list",
	"helpType.*":             "/ug/common/helpType/list",
	"help.*":                 "/ug/common/help/list",
	"feedBack.*":             "/ug/common/feedback/list",
	"bookingTime.*":          "/ug/systemconfig/bookingTime/list",
	"message.*":              "/ug/systemconfig/message/list",
	"lang.*":                 "/ug/systemconfig/lang/list",
	"category.*":             "/ug/systemconfig/category/list",
	"pType.*":                "/ug/systemconfig/paytype/list",
	"menu.*":                 "/ug/common/menu",
	"wechat.*":               "/ug/systemconfig/wechat",
	"option.*":               "/ug/systemconfig/option/list",
	"terminal.*":             "/ug/systemconfig/terminal/list",
	"addressAdd.*":           "/ug/systemconfig/address-add/list",
	"printer.*":              "/ug/systemconfig/printer-manage",
	"statistic.total.*":      "/ug/statistics",
	"statistics.trendency.*": "/ug/statistics/trendency",
	"rank.statistics.*":      "/ug/statistics/rank",
	"report.restaurant.*":    "/ug/report/restaurant",
	"todayReport.*":          "/ug/statistics/today-report",
	"statistics.restaurant.statisticsByGoods.*": "/ug/statistics/restaurant/goods-index",
	"statistics.restaurant.dailySellDetail.*":   "/ug/statistics/restaurant",
	"statistic.mulazim.balance.*":               "/ug/statistics/balance-index",
	"statistics.msg.*":                          "/ug/statistics/msg",
	"statistics.shippper.*":                     "/ug/statistics/shippper",
	"statistics.cashOut.*":                      "/ug/statistics/cash-out",
	"customerDevelop.*":                         "/ug/statistics/customer-develop",
	"businessPlan.*":                            "/ug/statistics/business-plan",
	"home-score.list":                           "/ug/home-score/list",
}

// NewMerchantTransformer
//
//	@Description: 初始化商家端Transformer
//	@return *MerchantTransformer
func NewCmsTransformer(c *gin.Context) *CmsTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	merchantTransformer := CmsTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &merchantTransformer
}
