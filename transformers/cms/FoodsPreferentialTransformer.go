package cms

import (
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type FoodsPreferentialTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func NewFoodsPreferentialTransformer(c *gin.Context) *FoodsPreferentialTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	transformer := FoodsPreferentialTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &transformer
}

// FoodsPreferentialListTransform
//
//	@Author: YaKupJan
//	@Date: 2024-09-10 12:47:25
//	@Description: 转换 美食优惠信息表
//	@receiver fpt
//	@param result
//	@return interface{}
func (fpt FoodsPreferentialTransformer) FoodsPreferentialListTransform(result []models.FoodsPreferential) interface{} {
	var items []interface{}
	for _, item := range result {
		foodMap := map[string]interface{}{
			"id":                       item.ID,
			"restaurant_id":            item.RestaurantID,
			"restaurant_foods_id":      item.RestaurantFoodsID,
			"gift_restaurant_foods_id": item.GiftRestaurantFoodsID,
			"type":                     item.Type,
			"start_date_time":          item.StartDateTime.Format("2006-01-02"),
			"end_date_time":            item.EndDateTime.Format("2006-01-02"),
			"start_time":               carbon.ParseByFormat(item.StartTime, "H:i:s").Format("H:i"),
			"end_time":                 carbon.ParseByFormat(item.EndTime, "H:i:s").Format("H:i"),
			"level":                    item.Level,
			"percent":                  item.Percent,
			"discount_price":           item.DiscountPrice,
			"original_price":           item.RestaurantFood.Price,
			"origin_price":             item.RestaurantFood.Price,
			"count":                    item.Count,
			"max_order_count":          item.MaxOrderCount,
			"order_count_per_day":      item.OrderCountPerDay,
			"gift_count":               item.GiftCount,
			"send_platform":            item.SendPlatform,
			"state":                    item.State,
			"created_at":               item.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":               item.UpdatedAt.Format("2006-01-02 15:04:05"),
			"price_markup_type":        item.PriceMarkupType,
			"price_markup_id":          item.PriceMarkupId,
			"price_markup_count":       item.PriceMarkupCount,
			"price_markup_saled_count": item.PriceMarkupFood.PriceMarkupFoodLogSum.PriceMarkupFoodSaledTotalCount,
			"in_price":                 item.PriceMarkupFood.InPrice,
			"food_type":                item.FoodType,
			"spec_id":                  item.SpecID,
			"weight":                   item.Weight,
		}
		if item.FoodType == 1 && item.SelectedSpec != nil {
			foodMap["original_price"] = item.SelectedSpec.Price
			foodMap["origin_price"] = item.SelectedSpec.Price
		}
		// 规格信息
		if item.SpecID > 0 && item.SelectedSpec != nil && item.SelectedSpec.ID > 0 {
			foodMap["spec_options"] = item.SelectedSpec.GetOptions(*item.SelectedSpec, fpt.language)
		}
		// 套餐美食
		if item.FoodType == models.RestaurantFoodsTypeCombo {
			foodMap["combo_food_items"] = item.RestaurantFood.GetComboItems(item.RestaurantFood, fpt.language)
		}
		// 美食
		if &item.RestaurantFood != nil {
			if fpt.language == "zh" {
				foodMap["foods_name"] = item.RestaurantFood.NameZh
			} else {
				foodMap["foods_name"] = item.RestaurantFood.NameUg
			}
			foodMap["food_images"] = configs.MyApp.CdnUrl + item.RestaurantFood.Image
		} else {
			foodMap["foods_name"] = ""
			foodMap["food_images"] = ""
		}
		// 优惠类型
		if &item.PreferentialType != nil {
			if fpt.language == "zh" {
				foodMap["type_name"] = item.PreferentialType.NameZh
			} else {
				foodMap["type_name"] = item.PreferentialType.NameUg
			}
		} else {
			foodMap["type_name"] = ""
		}

		// 处理 GiftFood 相关字段
		if &item.GiftFood != nil {
			if fpt.language == "zh" {
				foodMap["gift_foods_name"] = item.GiftFood.NameZh
			} else {
				foodMap["gift_foods_name"] = item.GiftFood.NameUg
			}
		} else {
			foodMap["gift_foods_name"] = ""
		}

		// 餐厅美食
		if &item.Restaurant != nil {
			if fpt.language == "zh" {
				foodMap["restaurant_name"] = item.Restaurant.NameZh
			} else {
				foodMap["restaurant_name"] = item.Restaurant.NameUg
			}
		} else {
			foodMap["restaurant_name"] = ""
		}

		// 创建者
		if &item.Creator != nil {
			foodMap["creator_id"] = item.Creator.ID
			foodMap["creator_real_name"] = item.Creator.RealName
		} else {
			foodMap["creator_id"] = nil
			foodMap["creator_real_name"] = ""
		}
		items = append(items, foodMap)
	}
	return items
}
