package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources"
	cmsResponse "mulazim-api/resources/cms"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type RestaurantFoodsTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func NewRestaurantFoodsTransformer(c *gin.Context) *RestaurantFoodsTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	restaurantFoodsTransformer := RestaurantFoodsTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &restaurantFoodsTransformer
}


// FormatMenu
//
//	@Description: 格式化菜单数据
//	@author: Alimjan
//	@Time: 2023-06-05 13:26:07
//	@receiver t CmsTransformer
//	@param admin models.Admin
//	@param menus []cms.Menus
//	@param badge int64
//	@return interface{}
func (t RestaurantFoodsTransformer) FormatRestaurantFoodsList(foods []models.RestaurantFoods) []map[string]interface{}{
	items := make([]map[string]interface{}, 0)
	for _, value := range foods {
		item := make(map[string]interface{})
		item["id"] = value.ID
		item["city_name"] = tools.GetNameByLang(value.Restaurant.City,t.language)
		item["area_name"] = tools.GetNameByLang(value.Restaurant.Area,t.language)
		item["name"] = tools.GetNameByLang(value,t.language)
		item["name_ug"] = value.NameUg
		item["name_zh"] = value.NameZh
		item["group_id"] = value.FoodsGroupId
		item["group_name"] = tools.GetNameByLang(value.FoodsGroup,t.language)
		item["restaurant_id"] = value.Restaurant.ID
		item["restaurant_name"] = tools.GetNameByLang(value.Restaurant,t.language)
		item["image_url"] = tools.CdnUrl(value.Image)
		item["preview_image"] = tools.CdnUrl(value.Image)
		item["original_image"] = value.Image
		item["price"] = value.Price
		item["original_price"] = value.OriginalPrice
		item["state"] = value.State
		item["begin_time"] = value.BeginTime
		item["end_time"] = value.EndTime
		item["ready_time"] = value.ReadyTime
		item["is_distribution"] = value.IsDistribution
		item["distribution_percent"] = value.DealerPercent+value.MpPercent
		item["dealer_percent"] =  value.DealerPercent+value.MpPercent
		item["yourself_take_percent"] = value.MpYourselfTakePercent+value.DealerYourselfTakePercent
		item["lunch_box_id"] = value.LunchBoxID
		item["lunch_box_fee"] = value.LunchBoxFee
		item["lunch_box_name"] = tools.If(t.language == "zh", value.LunchBox.NameZh, value.LunchBox.NameUg)
		item["weight"] = value.Weight
		item["min_count"] = value.MinCount
		item["food_type"] = value.FoodType
		item["created_at"] = tools.TimeFormatYmdHis(&value.CreatedAt)
		if value.FoodType == models.RestaurantFoodsTypeCombo {
			item["combo_items"] = value.GetComboItems(value, t.language)
			if value.LunchBoxID > 0 {
				item["lunch_box_name"] = tools.If(t.language == "zh", "套餐饭盒", "يۈرۈشلۈك قاچا")
			}
		}
		items = append(items, item)
	}
	return items
}

func (trns RestaurantFoodsTransformer) FormatRestaurantComboFoods(food *models.RestaurantFoods,
) cmsResponse.RestaurantFoodsComboResponse {

	
	// if food.FoodType == models.RestaurantFoodsTypeCombo && len(food.ComboFoodItems) > 0 {
	// 	for _, item := range food.ComboFoodItems {
	// 		// 处理子美食的规格选项
	// 		var selectedSpec *cmsResponse.RestaurantFoodSpec = nil
	// 		if item.FoodType == models.FoodsComboItemFoodTypeSpec && item.SelectedSpec != nil {
	// 			var specOptions []cmsResponse.RestaurantFoodSpecOption
	// 			// 从数据库中获取规格信息
	// 			for _, option := range item.SelectedSpec.FoodSpecOptions {
	// 				specOption := cmsResponse.RestaurantFoodSpecOption{
	// 					ID:         option.ID,
	// 					Name:       tools.If(trns.language == "zh", option.NameZh, option.NameUg),
	// 					IsSelected: option.IsSelected,
	// 					Price:      option.Price,
	// 					SpecOptionType: cmsResponse.RestaurantFoodSpecType{
	// 						ID:        option.FoodSpecType.ID,
	// 						Name:      tools.If(trns.language == "zh", option.FoodSpecType.NameZh, option.FoodSpecType.NameUg),
	// 						PriceType: option.FoodSpecType.PriceType,
	// 					},
	// 				}
	// 				specOptions = append(specOptions, specOption)
	// 			}

	// 			selectedSpec = &cmsResponse.RestaurantFoodSpec{
	// 				ID:          item.SelectedSpec.ID,
	// 				SpecOptions: specOptions,
	// 			}
	// 		}

	// 		// 处理子美食信息
	// 		restaurantFood := cmsResponse.RestaurantFoodsResponse{
	// 			ID:                  item.RestaurantFood.ID,
	// 			Name:                tools.GetNameByLang(item.RestaurantFood, trns.language),
	// 			Image:               tools.CdnUrl(item.RestaurantFood.Image),
	// 			Description:         tools.If(trns.language == "zh", item.RestaurantFood.DescriptionZh, item.RestaurantFood.DescriptionUg),
	// 			BeginTime:           item.RestaurantFood.BeginTime,
	// 			EndTime:             item.RestaurantFood.EndTime,
	// 			ReadyTime:           item.RestaurantFood.ReadyTime,
	// 			IsDistribution:      item.RestaurantFood.IsDistribution,
	// 			StarAvg:             item.RestaurantFood.StarAvg,
	// 			CommentCount:        item.RestaurantFood.CommentCount,
	// 			MonthOrderCount:     item.RestaurantFood.MonthOrderCount,
	// 			Price:               item.RestaurantFood.Price,
	// 			LunchBoxID:          item.RestaurantFood.LunchBoxID,
	// 			LunchBoxAccommodate: item.RestaurantFood.LunchBoxAccommodate,
	// 			LunchBoxFee:         item.RestaurantFood.LunchBoxFee,
	// 			FoodQuantity:        item.RestaurantFood.FoodQuantity,
	// 			FoodQuantityType:    item.RestaurantFood.FoodQuantityType,
	// 			State:               item.RestaurantFood.State,
	// 			MinCount:            item.RestaurantFood.MinCount,
	// 			IsRecommend:         item.RestaurantFood.IsRecommend,
	// 			SelectedSpec:        selectedSpec,
	// 		}

	// 		comboItem := cmsResponse.RestaurantFoodsComboItems{
	// 			ID:             item.ID,
	// 			ComboID:        item.ComboID,
	// 			FoodType:       item.FoodType,
	// 			FoodID:         item.FoodID,
	// 			Count:          item.Count,
	// 			RestaurantFood: restaurantFood,
	// 		}
	// 		comboItems = append(comboItems, comboItem)
	// 	}
	// }

	comboItems := make([]resources.ComboItems, 0)
	if food.FoodType == models.RestaurantFoodsTypeCombo && len(food.ComboFoodItems) > 0 {
		comboItems = food.GetComboItems(*food, trns.language)
	}

	// 格式化美食规格
	FoodSpecTypes := make([]cmsResponse.FoodSpecType, 0)
	if len(food.FoodSpecTypes) > 0 {
		for _, spec := range food.FoodSpecTypes {
			specOptions := make([]cmsResponse.FoodSpecOption, 0)
			for _, option := range spec.FoodSpecOptions {
				specOptions = append(specOptions, cmsResponse.FoodSpecOption{
					ID: option.ID,
					NameUg: option.NameUg,
					NameZh: option.NameZh,
					Price: option.Price,
					IsSelected: option.IsSelected,
				})
			}	
			FoodSpecTypes = append(FoodSpecTypes, cmsResponse.FoodSpecType{
				ID: spec.ID,
				NameUg: spec.NameUg,
				NameZh: spec.NameZh,
				PriceType: spec.PriceType,
				SpecOptions: specOptions,
			})
		}
	}


	formattedFood := cmsResponse.RestaurantFoodsComboResponse{
		ID:                  food.ID,
		RestaurantID:        food.RestaurantID,
		Name:                tools.GetNameByLang(*food, trns.language),
		Image:               tools.CdnUrl(food.Image),
		Description:         tools.If(trns.language == "zh", food.DescriptionZh, food.DescriptionUg),
		BeginTime:           food.BeginTime,
		EndTime:             food.EndTime,
		ReadyTime:           food.ReadyTime,
		IsDistribution:      food.IsDistribution,
		StarAvg:             food.StarAvg,
		CommentCount:        food.CommentCount,
		MonthOrderCount:     food.MonthOrderCount,
		Price:               food.Price,
		LunchBoxID:          food.LunchBoxID,
		LunchBoxAccommodate: food.LunchBoxAccommodate,
		LunchBoxFee:         food.LunchBoxFee,
		FoodQuantity:        food.FoodQuantity,
		FoodQuantityType:    food.FoodQuantityType,
		State:               food.State,
		MinCount:            food.MinCount,
		IsRecommend:         food.IsRecommend,
		FoodType:            food.FoodType,
		ComboFoodItems:      comboItems,
	}
	return formattedFood
}


func (trns RestaurantFoodsTransformer) FormatRestaurantFoodDetail(food *models.RestaurantFoods) cmsResponse.RestaurantFoodsComboResponse {
		 comboItems := make([]resources.ComboItems,0)
	if food.FoodType == models.RestaurantFoodsTypeCombo {
		comboItems = food.GetComboItems(*food, trns.language)
	}
		// 格式化美食规格
		FoodSpecTypes := make([]cmsResponse.FoodSpecType, 0)
		if len(food.FoodSpecTypes) > 0 {
			for _, spec := range food.FoodSpecTypes {
				specOptions := make([]cmsResponse.FoodSpecOption, 0)
				for _, option := range spec.FoodSpecOptions {
					specOptions = append(specOptions, cmsResponse.FoodSpecOption{
						ID: option.ID,
						NameUg: option.NameUg,
						NameZh: option.NameZh,
						Price: option.Price,
						IsSelected: option.IsSelected,
					})
				}	
				FoodSpecTypes = append(FoodSpecTypes, cmsResponse.FoodSpecType{
					ID: spec.ID,
					NameUg: spec.NameUg,
					NameZh: spec.NameZh,
					PriceType: spec.PriceType,
					SpecOptions: specOptions,
				})
			}
		}
	

	formattedFood := cmsResponse.RestaurantFoodsComboResponse{
		ID:                  food.ID,
		RestaurantID:        food.RestaurantID,
		Name:                tools.GetNameByLang(*food, trns.language),
		NameUg:              food.NameUg,
		NameZh:              food.NameZh,
			Description:       tools.If(trns.language == "zh", food.DescriptionZh, food.DescriptionUg),
		DescriptionUg:       food.DescriptionUg,
		DescriptionZh:       food.DescriptionZh,
		RestaurantName:      tools.GetNameByLang(food.Restaurant, trns.language),
		RestaurantNameUg:    food.Restaurant.NameUg,
		RestaurantNameZh:    food.Restaurant.NameZh,
		Image:               tools.CdnUrl(food.Image),
		OriginalImage:       food.Image,
		BeginTime:           food.BeginTime,
		EndTime:             food.EndTime,
		ReadyTime:           food.ReadyTime,
		IsDistribution:      food.IsDistribution,
		StarAvg:             food.StarAvg,
		CommentCount:        food.CommentCount,
		MonthOrderCount:     food.MonthOrderCount,
		Price:               food.Price,
		LunchBoxID:          food.LunchBoxID,
		LunchBoxAccommodate: food.LunchBoxAccommodate,
		LunchBoxFee:         food.LunchBoxFee,
		FoodQuantity:        food.FoodQuantity,
		FoodQuantityType:    food.FoodQuantityType,
		State:               food.State,
		MinCount:            food.MinCount,
		IsRecommend:         food.IsRecommend,
		FoodType:            food.FoodType,
		ComboFoodItems:      comboItems,
		Weight:              food.Weight,
			DistributionPercent: int(food.MpPercent+food.DealerPercent),
			YourselfTakePercent: int(food.MpYourselfTakePercent+food.DealerYourselfTakePercent),
		FoodsGroupID:        food.FoodsGroupId,
		RestaurantPrinterID: food.RestaurantPrinterID,
		RestaurantBeginTime: food.Restaurant.BeginTime,
		RestaurantEndTime:   food.Restaurant.EndTime,
	}
	formattedFood.FoodsCategoryID = make([]int, 0)
	for _, category := range food.Categories {
		formattedFood.FoodsCategoryID = append(formattedFood.FoodsCategoryID, int(category.ID))
	}

		// 打印机
	restaurantPrinter := make([]cmsResponse.Printer, 0)
	if food.Restaurant.ID > 0 && len(food.Restaurant.RestaurantPrinter) > 0 {
		for _, printer := range food.Restaurant.RestaurantPrinter {
			restaurantPrinter = append(restaurantPrinter, cmsResponse.Printer{
				ID:   int(printer.ID),
				Name: tools.GetNameByLangAndColumn(printer, trns.language, "LocationName"),
			})
		}
	}
	formattedFood.Printer = restaurantPrinter

	restaurantGroup := make([]cmsResponse.FoodGroup, 0)
	if food.Restaurant.ID > 0 && len(food.Restaurant.FoodsGroup) > 0 {
		for _, group := range food.Restaurant.FoodsGroup {
			restaurantGroup = append(restaurantGroup, cmsResponse.FoodGroup{
				ID:   group.ID,
				Name: tools.GetNameByLang(group, trns.language),
			})
		}
	}
	formattedFood.RestaurantGroup = restaurantGroup
	return formattedFood
}
	

func (trns RestaurantFoodsTransformer) FormatRestaurantFoodsQuantityList(quantityList []models.FoodsQuantity) []map[string]interface{} {
	items := make([]map[string]interface{}, 0)
	for _, value := range quantityList {
		item := make(map[string]interface{})
		item["id"] = value.ID
		item["name"] = tools.If(trns.language == "zh", value.NameZh, value.NameUg)
		item["name_ug"] = value.NameUg
		item["name_zh"] = value.NameZh
		items = append(items, item)
	}
	return items
}	

func (trns RestaurantFoodsTransformer) FormatRestaurantFoodsTypeList(foodTypesList []models.BFoodsType) []map[string]interface{} {
	items := make([]map[string]interface{}, 0)
	for _, value := range foodTypesList {
		item := make(map[string]interface{})
		item["id"] = value.ID
		item["name"] = tools.If(trns.language == "zh", value.NameZh, value.NameUg)
		item["name_ug"] = value.NameUg
		item["name_zh"] = value.NameZh
		item["shake"] = value.Shake
		item["state"] = value.State
		item["created_at"] = tools.TimeFormatYmdHis(&value.CreatedAt)
		items = append(items, item)
	}
	return items
}

func (trns RestaurantFoodsTransformer) FormatRestaurantFoodsListForSelectTable(foodList []models.BFoods) []map[string]interface{} {
	items := make([]map[string]interface{}, 0)
	for _, value := range foodList {
		item := make(map[string]interface{})
		item["id"] = value.ID
		item["all_foods_id"] = value.AllFoodsID
		item["image"] = tools.CdnUrl(value.Image)
		item["original_image"] = value.Image
		item["name"] = tools.If(trns.language == "zh", value.NameZh, value.NameUg)
		item["name_ug"] = value.NameUg
		item["name_zh"] = value.NameZh
		item["all_foods_name"] = tools.If(trns.language == "ug",value.AllFoods.NameUg,value.AllFoods.NameZh)
		item["foods_type_name"] = tools.If(trns.language == "ug",value.AllFoods.BFoodsType.NameUg,value.AllFoods.BFoodsType.NameZh)
		items = append(items, item)
	}
	return items
}
