package cms

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/lang"
)

type FoodStaticsTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func (t FoodStaticsTransformer) TransformList(list interface{}) interface{} {
	return list
}


//
// NewFoodStaticsTransformer
//  @Description:
//  @param c
//  @return *FoodStaticsTransformer
//
func NewFoodStaticsTransformer(c *gin.Context) *FoodStaticsTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	merchantTransformer := FoodStaticsTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &merchantTransformer
}