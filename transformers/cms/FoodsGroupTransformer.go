package cms

import (
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type FoodsGroupTransformer struct {
	langUtil *lang.LangUtil
	language string
}

// RestaurantListFormat
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:49:05
//	@Description: 餐厅列表
//	@receiver t
//	@param restaurantList
//	@return []map[string]interface{}
func (t FoodsGroupTransformer) RestaurantListFormat(restaurantList []models.Restaurant) []map[string]interface{} {
	language := t.language
	var list []map[string]interface{}
	for _, restaurant := range restaurantList {
		item := map[string]interface{}{
			"id":            restaurant.ID,
			"name":          tools.GetNameByLang(restaurant, language),
			"logo":          configs.MyApp.CdnUrl + restaurant.Logo,
			"weight":        restaurant.Weight,
			"state":         restaurant.State,
			"tel":           restaurant.Tel,
			"can_self_take": restaurant.CanSelfTake,
			"city_name":     tools.GetNameByLang(restaurant.City, language),
			"area_name":     tools.GetNameByLang(restaurant.Area, language),
			"created_at":    restaurant.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		list = append(list, item)
	}
	return list
}


func (t FoodsGroupTransformer) FoodsListByGroupIdFormat(item interface{}) interface{}{
	return nil
}

// ListFormat
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:47:41
//	@Description: 美食分组列表格式化
//	@receiver t
//	@param foodsGroupList
//	@return []map[string]interface{}
func (t FoodsGroupTransformer) ListFormat(foodsGroupList []models.FoodsGroup) []map[string]interface{} {
	language := t.language
	items := make([]map[string]interface{},0)
	for _, group := range foodsGroupList {
		item := map[string]interface{}{
			"id":                group.ID,
			"name":              tools.GetNameByLang(group, language),
			"name_zh":           group.NameZh,
			"name_ug":           group.NameUg,
			"weight":            group.Weight,
			"state":             group.State,
			"state_name":        t.langUtil.TArr("foods_group_state")[group.State],
			"review_state":      group.ReviewState,
			"review_state_name": t.langUtil.TArr("foods_group_review_state")[group.ReviewState],
			"refuse_reason":     group.RefuseReason,
		}
		items = append(items, item)
	}
	return items
}

func (t FoodsGroupTransformer) DetailFormat(foodsGroup models.FoodsGroup) map[string]interface{} {
	language := t.language
	return map[string]interface{}{
		"id":            foodsGroup.ID,
		"name":          tools.GetNameByLang(foodsGroup, language),
		"weight":        foodsGroup.Weight,
		"state":         foodsGroup.State,
	}
}

// FoodsListFormat
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:47:12
//	@Description: 美食列表格式化
//	@receiver t
//	@param foodsList
//	@param groupList
//	@return []map[string]interface{}
//	@return []map[string]interface{}
func (t FoodsGroupTransformer) FoodsListFormat(foodsList []models.RestaurantFoods) []map[string]interface{} {
	language := t.language
	var foodsItems []map[string]interface{}
	for _, foods := range foodsList {
		item := map[string]interface{}{
			"id":             foods.ID,
			"name":           tools.GetNameByLang(foods, language),
			"name_zh":        foods.NameZh,
			"name_ug":        foods.NameUg,
			"weight":         foods.Weight,
			"weight_in_group":         foods.WeightInGroup,
			"state":          foods.State,
			"state_name":     t.langUtil.TArr("restaurant_foods_state")[foods.State],
			"image":          configs.MyApp.CdnUrl + foods.Image,
			"original_image": foods.Image,
			"price":          foods.Price,
			"foods_group_id": foods.FoodsGroupId,
			"food_type": foods.FoodType,
			"begin_time": foods.BeginTime,
			"end_time": foods.EndTime,
			"ready_time": foods.ReadyTime,
			"lunch_box_id": foods.LunchBoxID,
			"lunch_box_fee": foods.LunchBoxFee,
			"lunch_box_accommodate": foods.LunchBoxAccommodate,
		}
		foodsItems = append(foodsItems, item)
	}
	return foodsItems
}

func (t FoodsGroupTransformer) ReviewListFormat(reviewList []models.FoodsGroup) []map[string]interface{} {
	language := t.language
	var list []map[string]interface{}
	for _, group := range reviewList {
		item := map[string]interface{}{
			"id":                group.ID,
			"name":              tools.GetNameByLang(group, language),
			"name_zh":           group.NameZh,
			"name_ug":           group.NameUg,
			"restaurant_name":   tools.GetNameByLang(group.Restaurant, language),
			"city_name":         tools.GetNameByLang(group.Restaurant.City, language),
			"area_name":         tools.GetNameByLang(group.Restaurant.Area, language),
			"state":             group.State,
			"state_name":        t.langUtil.TArr("foods_group_state")[group.State],
			"review_state":      group.ReviewState,
			"review_state_name": t.langUtil.TArr("foods_group_review_state")[group.ReviewState],
			"refuse_reason":     group.RefuseReason,
			"created_at":        tools.TimeFormatYmdHis(group.CreatedAt),
			"review_at":         tools.TimeFormatYmdHis(group.ReviewAt),
			"review_admin":      group.ReviewAdmin.Name,
		}
		list = append(list, item)
	}
	return list
}

// ListRecommendFormat
//
//  @Author: YaKupJan
//  @Date: 2024-10-22 20:56:48
//  @Description: 推荐分组
//  @receiver t
//  @param foodsGroupList
//  @return []map[string]interface{}
func (t FoodsGroupTransformer) ListRecommendFormat(foodsGroupList []models.Dictionary) []map[string]interface{} {
	items := make([]map[string]interface{},0)
	for _, foodsGroup := range foodsGroupList {
		item := map[string]interface{}{
			"name_ug":   foodsGroup.NameUg,
			"name_zh":   foodsGroup.NameZh,
		}
		items = append(items, item)
	}
	return items
}

func NewFoodsGroupTransformer(c *gin.Context) *FoodsGroupTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	transformer := FoodsGroupTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &transformer
}
