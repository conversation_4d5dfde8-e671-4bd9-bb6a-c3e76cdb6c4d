package cms

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources/cms/lottery"
	"mulazim-api/tools"
)

type LotteryWinnerSetTransfomer struct {
	langUtil *lang.LangUtil
	language string
}
// FormatList 格式化返回数据
//  @receiver t
//  @param count
//  @param list
//  @return lottery.LotteryActivityListData
//
func (t LotteryWinnerSetTransfomer) FormatList(count int64,
	list []models.LotteryActivityLevelWinnersSet) lottery.LotterySetListData {
	rtn := lottery.LotterySetListData{}
	rtn.Total = count
	for _, v := range list {
		rtn.Items = append(rtn.Items, lottery.LotterySetListItem{
			Id:           v.ID,
			ActivityName:         tools.If(t.language=="ug",v.LotteryActivity.NameUg,v.LotteryActivity.NameZh),
			CreatedAt:    v.CreatedAt.Format("2006-01-02 15:04:05"),
			State:        v.State,
			Range: fmt.Sprintf("%d-%d",v.MinIndex ,v.MaxIndex ),
			CurrentIndex: v.CurrentCount,
			AdminName: v.Admin.RealName,
			PrizeCount: v.PrizeCount,
			PrizeName: v.PrizeName,
			Level: v.Level,
		})
	}

	return rtn
}

// NewLotteryActivityTransfomer
//  @param c
//  @return *LotteryActivityTransfomer
//
func NewLotteryWinnerSetTransfomer(c *gin.Context) *LotteryWinnerSetTransfomer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")

	langUtil := l.(lang.LangUtil)

	transformer := LotteryWinnerSetTransfomer{
		langUtil: &langUtil,
		language: language.(string),
	}

	return &transformer
}
