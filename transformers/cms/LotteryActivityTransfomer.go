package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources/cms/lottery"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type LotteryActivityTransformer struct {
	langUtil *lang.LangUtil
	language string
}
// FormatList 格式化返回数据
//  @receiver t
//  @param count
//  @param list
//  @return lottery.LotteryActivityListData
//
func (t LotteryActivityTransformer) FormatList(count int64,
	list []models.LotteryActivity) lottery.LotteryActivityListData {
	rtn := lottery.LotteryActivityListData{}
	rtn.Total = count
	for _, v := range list {
		rtn.Items = append(rtn.Items, lottery.LotteryActivityListItem{
			Id:           v.ID,
			Name:         tools.If(t.language=="ug",v.NameUg,v.Name<PERSON>h),
			ActivityTime: v.StartTime.Format("2006-01-02 15:04") +" ~ "+ v.EndTime.Format("2006-01-02 15:04"),
			Position:  t.langUtil.TArr("lottery_activity_pos")[*v.ShowPosition],
			CreatedAt:    v.CreatedAt.Format("2006-01-02 15:04:05"),
			State:        v.State,
			AdminName: v.Admin.RealName,
		})
	}

	return rtn
}


func (t LotteryActivityTransformer) FormatDetail(detail models.LotteryActivity) interface{} {
	price := 0
	rule_ug := ""
	rule_zh := ""
	lotteryLevelPrize := []models.LotteryActivityLevelPrize{}
	if len(detail.LotteryActivityLevel)>0 {
		price = detail.LotteryActivityLevel[0].Price
		rule_ug = detail.LotteryActivityLevel[0].RuleUG
		rule_zh = detail.LotteryActivityLevel[0].RuleZH
		lotteryLevelPrize = detail.LotteryActivityLevel[0].LotteryActivityLevelPrize
	}
	rtn := map[string]interface{}{
		"name_ug":detail.NameUg,
		"name_zh":detail.NameZh,
		"start_time":tools.TimeFormatYmdHis(detail.StartTime),
		"end_time":tools.TimeFormatYmdHis(detail.EndTime),
		"show_position":detail.ShowPosition,
		"state":detail.State,
		"price":price,
		"min_prize_order_price":detail.MinPrizeOrderPrice,
		"max_buy_count":detail.MaxBuyCount,
		"share_count":detail.ShareCount,
		"share_min_order_price":detail.ShareMinOrderPrice,
		"share_image_url":tools.CdnUrl(detail.ShareImage),
		"share_image":detail.ShareImage,
		"coupon_group_id":detail.LotteryActivityGroupID,
		"rule_ug":rule_ug,
		"rule_zh":rule_zh,
		"time_type":detail.CouponEndTimeType,
		"active_date":detail.CouponInvalidDate,
		"coupon_end_time":tools.TimeFormatYmdHis(detail.CouponEndTime),
		"can_edit":true,
	}

	startTime := carbon.Time2Carbon(*detail.StartTime)
	if startTime.Lte(carbon.Now()){
		rtn["can_edit"] = false
	}

	// lotteryLevel := detail.LotteryActivityLevel
	levelRes := make([]map[string]interface{},0)
	for _, v := range lotteryLevelPrize {
		levelRes = append(levelRes, map[string]interface{}{
			"level":v.Level,
			"count":v.Count,
			"prize_id":v.LotteryPrize.ID,
			"title_img":tools.CdnUrl(tools.GetNameByLangAndColumn(v.LotteryPrize, t.language, "TitleImg")),
			"swiper_img":tools.CdnUrl(tools.GetNameByLangAndColumn(v.LotteryPrize, t.language, "SwiperImg")),
			"name":tools.GetNameByLang(v.LotteryPrize, t.language),
			"model":v.LotteryPrize.Model,
			"price":v.LotteryPrize.Price,
		})
	}
	rtn["lottery_level"] = levelRes
	return rtn
}
// NewLotteryActivityTransformer
//  @param c
//  @return *LotteryActivityTransformer
//
func NewLotteryActivityTransformer(c *gin.Context) *LotteryActivityTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")

	langUtil := l.(lang.LangUtil)

	transformer := LotteryActivityTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}

	return &transformer
}
