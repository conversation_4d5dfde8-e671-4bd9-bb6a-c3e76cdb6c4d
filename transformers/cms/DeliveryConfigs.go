package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/models/shipment"
	"mulazim-api/resources"
	"mulazim-api/resources/response"
	"mulazim-api/tools"

	"math"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/paulmach/orb"
)

type DeliveryConfigsTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func NewDeliveryConfigsTransformer(ctx *gin.Context) *DeliveryConfigsTransformer {
	l, _ := ctx.Get("lang_util")
	language, _ := ctx.Get("lang")
	langUtil := l.(lang.<PERSON>til)
	return &DeliveryConfigsTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
}

// FormatRestaurantList
//
//	@Description: 餐厅配送设置信息格式化
//	@Author: Rixat
//	@Time: 2025-07-09 11:01:10
//	@receiver trns *DeliveryConfigsTransformer
//	@param items []resources.DeliveryRestaurantList
//	@return []response.DeliveryRestaurantListResponse
func (trns *DeliveryConfigsTransformer) FormatRestaurantList(items []resources.DeliveryRestaurantList,areaConfig *shipment.DeliveryAreas) []response.DeliveryRestaurantListResponse {
	res := make([]response.DeliveryRestaurantListResponse, 0)
	for _, v := range items {
		item := response.DeliveryRestaurantListResponse{
			RestaurantID:    v.RestaurantID,
			RestaurantName:  v.RestaurantName,
			RestaurantLogo:  tools.CdnUrl(v.RestaurantLogo),
			Radius:         v.Radius,
			FeeState:       v.FeeState,
			Polygon:        parsePolygonWKT(v.Polygon),
			Lat:            v.Lat,
			Lng: v.Lng,
			Option: v.Option,
		}
		// 餐厅优先
		if(v.Option>1){
			item.Radius = int(v.Radius)
			item.Option = v.Option
		}else if(areaConfig.Option == 3){
			item.Option = areaConfig.Option
		    item.Radius = int(areaConfig.Radius)
		}else {
			item.Option = 1
		}
		
		if v.FeeState == 0 {
			item.FeeState = 2
		}
		// if v.FeeCount > 0 {
		// 	item.FeeState = 1
		// }
		res = append(res, item)
	}
	return res
}

// FormatAreaList
//
//	@Description: 区域信息格式化
//	@Author: Rixat
//	@Time: 2025-07-09 11:01:10
//	@receiver trns *DeliveryConfigsTransformer
//	@param items []models.Area
//	@return []response.DeliveryAreaListResponse
func (trns *DeliveryConfigsTransformer) FormatAreaList(items []models.Area) []response.DeliveryAreaListResponse {
	res := make([]response.DeliveryAreaListResponse, 0)
	for _,v := range items{
		var notice string
		if trns.language == "zh" {
			notice = v.DeliveryRunningConfig.NoticeZh
		} else {
			notice = v.DeliveryRunningConfig.NoticeUg
		}
		addOrderTime := 0
		if v.DeliveryRunningConfig.OrderAddTime > 0 {
			addOrderTime = int(v.DeliveryRunningConfig.OrderAddTime)
		}
		item := response.DeliveryAreaListResponse{
			AreaID                      :v.ID,
			CityID                      :v.CityID,
			CityName: tools.GetNameByLang(v.City,trns.language),
			AreaName                      :tools.GetNameByLang(v,trns.language),
			Name: tools.GetNameByLang(v.DeliveryRunningConfig,trns.language),
			Notice: notice,
			OrderAddTime:              addOrderTime,
			Radius:v.DeliveryRunningConfig.Radius,
			Count: len(v.DeliveryAllConfigs),
			ShipmentFeeMin: trns.GetShipmentFeeMin(v.DeliveryRunningConfig.DeliveryFees),
			ShipmentType:0,
		}
		res = append(res,item )	
	}
	return res
}

func (trns *DeliveryConfigsTransformer) GetShipmentFeeMin(items []shipment.DeliveryFees) int {
	min := 0
	for _,v := range items{
		if v.Type == 1 {
			if min ==  0 || int(v.StageFeeMin) < min {
				min = int(v.StageFeeMin)
			}
		}
	}
	return min
}

// FormatAreaConfigList
//
//	@Description: 区域配送设置列表
//	@Author: Rixat
//	@Time: 2025-06-27 10:00:00
//	@receiver trns *DeliveryConfigsTransformer
//	@param items []shipment.DeliveryAreas
//	@return []response.DeliveryAreaConfigListResponse
func (trns *DeliveryConfigsTransformer) FormatAreaConfigList(items []shipment.DeliveryAreas) []response.DeliveryAreaConfigListResponse {
	res := make([]response.DeliveryAreaConfigListResponse, 0)
	for _,v := range items{
		var notice string
		if trns.language == "zh" {
			notice = v.NoticeZh
		} else {
			notice = v.NoticeUg
		}
		item := response.DeliveryAreaConfigListResponse{
			ID: v.ID,
			Name: tools.GetNameByLang(v,trns.language),
			Notice: notice,
			NoticeUg: v.NoticeUg,
			NoticeZh: v.NoticeZh,
			OrderAddTime:             v.OrderAddTime,
			ShipmentFeeMin: trns.GetShipmentFeeMin(v.DeliveryFees),
			ShipmentType:0,
			State:v.AreaRunningState,
			Radius:v.Radius,
		}
		res = append(res,item )
	}
	return res
}
// func (trns *DeliveryConfigsTransformer) GetShipmentFeeMin(items []shipment.DeliveryFees) int {
// 	min := 0
// 	for _,v := range items{
// 		if v.Type == 1 {
// 			if min == 0 || int(v.StartFee) < min {
// 				min = int(v.StartFee)
// 			}
// 		}
// 	}
// 	return min
// }

// FormatPolygon
//
//	@Description: 格式化多边形
//	@Author: Rixat
//	@Time: 2025-07-09 11:01:10
//	@param polygon orb.Polygon
//	@return [][]float64
func FormatPolygon(polygon orb.Polygon) [][]float64 {
	res := make([][]float64, 0)
	if len(polygon) > 0 {
		// 只取第一个环（外环）
		for _, point := range polygon[0] {
		lng := math.Round(point[0]*10000) / 10000
		lat := math.Round(point[1]*10000) / 10000
		res = append(res, []float64{lng, lat})
		}
	}
	return res
}

func (trns *DeliveryConfigsTransformer) FormatDeliveryConfigsList(list []shipment.DeliveryAreas) []response.DeliveryAreaConfigDetailResponse {
	res := make([]response.DeliveryAreaConfigDetailResponse, 0)
	for _, mdl := range list {
		item := trns.FormatDeliveryConfigsDetail(mdl)
		res = append(res, item)
	}
	return res
}

// FormatDeliveryConfigsDetail
//
//	@Description: 格式化区域配送配置详情
//	@Author: Rixat
//	@Time: 2025-07-09 11:01:10
//	@receiver trns *DeliveryConfigsTransformer
//	@param mdl shipment.DeliveryAreas
//	@return response.DeliveryAreaConfigDetailResponse
func (trns *DeliveryConfigsTransformer) FormatDeliveryConfigsDetail(mdl shipment.DeliveryAreas) response.DeliveryAreaConfigDetailResponse {
	var (
		areaInfo     = trns.FormatDeliveryArea(mdl)
		deliveryFees = trns.FormatDeliveryFeeStages(mdl.DeliveryFees)
	)
	return response.DeliveryAreaConfigDetailResponse{
		AreaInfo: areaInfo,
		DeliveryFees: deliveryFees,
	}
}

// FormatDeliveryArea
//
//	@Description: 格式化区域配送配置详情
//	@Author: Rixat
//	@Time: 2025-07-09 11:01:10
//	@receiver trns *DeliveryConfigsTransformer
//	@param area shipment.DeliveryAreas
//	@return response.DeliveryConfigDetailAreaInfo
func (trns *DeliveryConfigsTransformer) FormatDeliveryArea(area shipment.DeliveryAreas) response.DeliveryConfigDetailAreaInfo {
	polygon := [][]float64{}
	if area.Polygon != nil && len(area.Polygon.Polygon) > 0 {
		polygon = FormatPolygon(area.Polygon.Polygon)
	}

	areaPolygon := [][]float64{}
	if area.AreaInfo.Polygon != nil && len(area.AreaInfo.Polygon.Polygon) > 0 {
		areaPolygon = FormatPolygon(area.AreaInfo.Polygon.Polygon)
	}
	res := response.DeliveryConfigDetailAreaInfo{
		ID:             area.ID,
		NameUg:         area.NameUg,
		NameZh:         area.NameZh,
		NoticeUg:       area.NoticeUg,
		NoticeZh:       area.NoticeZh,
		OrderAddTime:   area.OrderAddTime,
		ShipmentFeeMin: 0,
		ShipmentType:   0,
		Polygon:        polygon,
		AreaPolygon:    areaPolygon,
		Option:         area.Option,
		Radius:         area.Radius,
		AreaRunningState:area.AreaRunningState,
	}
	return res
}

func (trns *DeliveryConfigsTransformer) FormatRunningDeliveryConfig(area shipment.DeliveryAreas) response.RunningDeliveryAreaConfig {
	polygon := [][]float64{}
	if area.Polygon != nil && len(area.Polygon.Polygon) > 0 {
		polygon = FormatPolygon(area.Polygon.Polygon)
	}
	res := response.RunningDeliveryAreaConfig{
		ID:             area.ID,
		Name:         tools.GetNameByLang(area,trns.language),
		Polygon:        polygon,
		DeliveryFees: trns.FormatDeliveryFeeStages(area.DeliveryFees),
	}
	return res
}
func (trns *DeliveryConfigsTransformer) FormatDeliveryAreaList(areaList []shipment.DeliveryAreas) []response.DeliveryConfigDetailAreaInfo {
	res := make([]response.DeliveryConfigDetailAreaInfo, 0)
	for _, area := range areaList {
		item := trns.FormatDeliveryArea(area)
		res = append(res, item)
	}
	return res
}

func (trns *DeliveryConfigsTransformer) FormatDeliveryFeeStagesList(feeLists [][]shipment.DeliveryFees) [][]response.DeliveryFeeConfigDetailResponse {
	res := make([][]response.DeliveryFeeConfigDetailResponse, 0)
	for _, feeList := range feeLists {
		item := trns.FormatDeliveryFeeStages(feeList)
		res = append(res, item)
	}
	return res
}

// FormatDeliveryFeeStages
//
//	@Description: 格式化配送费阶梯
//	@Author: Rixat
//	@Time: 2025-07-09 11:01:10
//	@receiver trns *DeliveryConfigsTransformer
//	@param feeList []shipment.DeliveryFees
//	@return []response.DeliveryFeeConfigDetailResponse
func (trns *DeliveryConfigsTransformer) FormatDeliveryFeeStages(feeList []shipment.DeliveryFees) []response.DeliveryFeeConfigDetailResponse {
	deliveryFees := make([]response.DeliveryFeeConfigDetailResponse, 0)
	for _, v := range feeList {
		deliveryFees = append(deliveryFees, response.DeliveryFeeConfigDetailResponse{
			ID:        v.ID,
			Type:        v.Type,
			Default:     v.Default,
			Stages:      v.Stages,
			StageFeeMin: v.StageFeeMin,
			BeginTime:   v.BeginTime,
			EndTime:     v.EndTime,
			StartFee:    v.StartFee,
		})
	}
	return deliveryFees
}

// FormatRestaurantDetail
//
//	@Description: 格式化餐厅配送范围详情
//	@Author: Rixat
//	@Time: 2025-07-09 11:01:10
//	@receiver trns *DeliveryConfigsTransformer
//	@param mdl shipment.DeliveryAreas
//	@return response.DeliveryRestaurantDetailResponse
func (trns *DeliveryConfigsTransformer) FormatRestaurantDetail(mdl shipment.DeliveryAreas) response.DeliveryRestaurantDetailResponse {
	if mdl.ID == 0 {
		return response.DeliveryRestaurantDetailResponse{}
	}
	res := response.DeliveryRestaurantDetailResponse{
		Radius: uint8(mdl.Radius),
		Option: uint8(mdl.Option),
		FeeState: uint8(mdl.FeeState),
	}
	if mdl.Polygon != nil && len(mdl.Polygon.Polygon) > 0 {
		res.Polygon = FormatPolygon(mdl.Polygon.Polygon)
	}
	return res
}

func (trns *DeliveryConfigsTransformer) FormatDeliveryFee(feeLists []shipment.DeliveryFees) []response.DeliveryFeeConfigDetailResponse {
	res := make([]response.DeliveryFeeConfigDetailResponse, 0)
	for _,v := range feeLists{
		res = append(res, response.DeliveryFeeConfigDetailResponse{
			ID:             v.ID,
			Type:           v.Type,
			Default:        v.Default,
			Stages:         v.Stages,
			StageFeeMin:    v.StageFeeMin,
			BeginTime:      v.BeginTime,
			EndTime:        v.EndTime,
			StartFee:       v.StartFee,
		})
	}	
	return res
}

// FormatAreaDefaultPolygon
//
//	@Description: 格式化区域默认服务区域范围
//	@Author: Rixat
//	@Time: 2025-07-09 11:01:10
//	@receiver trns *DeliveryConfigsTransformer
//	@param area shipment.AreaInfo
//	@return response.DeliveryAreaDefaultPolygonResponse
func (trns *DeliveryConfigsTransformer) FormatAreaDefaultPolygon(area shipment.AreaInfo) response.DeliveryAreaDefaultPolygonResponse {
	polygon := [][]float64{}
	if area.Polygon != nil && len(area.Polygon.Polygon) > 0 {
		polygon = FormatPolygon(area.Polygon.Polygon)
	}
	res := response.DeliveryAreaDefaultPolygonResponse{
		Polygon: polygon,
	}
	return res
}

// parsePolygonWKT 将 WKT 格式的 polygon 字符串转换为 [][]float64
func parsePolygonWKT(wkt string) [][]float64 {
	if wkt == "" {
		return [][]float64{}
	}

	// 移除 "POLYGON((" 和 "))" 
	wkt = strings.TrimPrefix(wkt, "POLYGON((")
	wkt = strings.TrimSuffix(wkt, "))")

	// 分割坐标点
	points := strings.Split(wkt, ",")
	result := make([][]float64, 0, len(points))

	for _, point := range points {
		coords := strings.Fields(point)
		if len(coords) != 2 {
			continue
		}

		x, err1 := strconv.ParseFloat(coords[0], 64)
		y, err2 := strconv.ParseFloat(coords[1], 64)
		if err1 != nil || err2 != nil {
			continue
		}

		result = append(result, []float64{x, y})
	}

	return result
}