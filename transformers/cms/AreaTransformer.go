package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type AreaTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func NewAreaTransformer(c *gin.Context) *AreaTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	seckillTransformer := AreaTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &seckillTransformer
}


// FormatMenu
//
//	@Description: 格式化菜单数据
//	@author: Alimjan
//	@Time: 2023-06-05 13:26:07
//	@receiver t CmsTransformer
//	@param admin models.Admin
//	@param menus []cms.Menus
//	@param badge int64
//	@return interface{}
func (t AreaTransformer) FormatAreaList(areaList []models.Area) []map[string]interface{}{
	items := make([]map[string]interface{}, 0)
	for _, area := range areaList {
		item := make(map[string]interface{})
		item["id"] = area.ID
		item["name"] = tools.GetNameByLang(area, t.language)
		item["min_shipment"] = area.MinShipment
		item["per_km_shipment"] = area.PerKmShipment
		item["mp_percent"] = area.MpPercent
		item["service_phone"] = area.ServicePhone
		item["admin_sms_phone"] = area.AdminSmsPhone
		item["state"] = area.State
		item["rank_state"] = area.RankState
		item["shipper_rank_state"] = area.ShipperRankState
		item["weight"] = area.Weight
		item["show_restaurant_contact"] = area.ShowRestaurantContact
		item["show_custom_contact"] = area.ShowCustomContact
		item["max_order_per_hour"] = area.MaxOrderPerHour
		item["insurance_enable"] = area.InsuranceEnable
		item["receive_order_time"] = area.ReceiveOrderTime
		item["has_license_images"] = tools.If(len(area.SelfSignImages)>0,1,0)
		item["created_at"] = tools.TimeFormatYmdHis(&area.CreatedAt)
		item["updated_at"] = tools.TimeFormatYmdHis(&area.UpdatedAt)
		items = append(items, item)
	}
	return items
}

func (t AreaTransformer) FormatAreaDetail(area models.Area,selfSignInfo models.SelfSignMerchantInfo) map[string]interface{}{
	res := make(map[string]interface{})
	res["info"] = map[string]interface{}{
		"id": area.ID,
		"name": tools.GetNameByLang(area, t.language),
		"min_shipment": area.MinShipment,
		"per_km_shipment": area.PerKmShipment,
		"mp_percent": area.MpPercent,
		"service_phone": area.ServicePhone,
		"admin_sms_phone": area.AdminSmsPhone,
		"state": area.State,
		"rank_state": area.RankState,
		"shipper_rank_state": area.ShipperRankState,
		"weight": area.Weight,
		"show_restaurant_contact": area.ShowRestaurantContact,
		"show_custom_contact": area.ShowCustomContact,
		"max_order_per_hour": area.MaxOrderPerHour,
		"insurance_enable": area.InsuranceEnable,
		"receive_order_time": area.ReceiveOrderTime,
		"created_at": tools.TimeFormatYmdHis(&area.CreatedAt),
		"updated_at": tools.TimeFormatYmdHis(&area.UpdatedAt),
	}
	res["license_info"] = map[string]interface{}{
		"shop_name": selfSignInfo.ShopName,
		"shop_license_image": tools.CdnUrl(t.GetImageByDocType(selfSignInfo.SelfSignImages,"0002")),
		"original_shop_license_image": t.GetImageByDocType(selfSignInfo.SelfSignImages,"0002"),
		"shop_permit_license_image": tools.CdnUrl(t.GetImageByDocType(selfSignInfo.SelfSignImages,"1001")),
		"original_shop_permit_license_image": t.GetImageByDocType(selfSignInfo.SelfSignImages,"1001"),
	}
	return res
}


func (t AreaTransformer) GetImageByDocType(images []models.SelfSignImages,docType string) string{
	imageUrl := ""
	for _, image := range images {
		if image.DocType == docType {
			imageUrl = image.MlzFilePath
		}
	}
	return imageUrl
}

// FormatCityAreaTree 格式化城市区域树
func (t AreaTransformer) FormatCityAreaTree(cityList []models.City) []map[string]interface{} {
	result := make([]map[string]interface{}, 0)
	
	for _, city := range cityList {
		if len(city.Areas)==0{
			continue
		}
		cityItem := map[string]interface{}{
			"name": tools.GetNameByLang(city, t.language),
			"id":   "city_"+tools.ToString(city.ID),
		}
		
		// 格式化区域数据
		areas := make([]map[string]interface{}, 0)
		for _, area := range city.Areas {
			areaItem := map[string]interface{}{
				"name": tools.GetNameByLang(area, t.language),
				"id":   "area_"+tools.ToString(area.ID),
			}
			areas = append(areas, areaItem)
		}
		
		cityItem["areas"] = areas
		result = append(result, cityItem)
	}
	
	return result
}