package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	shipperResource "mulazim-api/resources/cms/shipper"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
)

type ShipperTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func (t ShipperTransformer) ShipperRankDetailFormat(shipper models.AdminBase, thisWeekHistory, lastWeekHistory shipperResource.ShipperRankWeekHistory, orderList []models.AutoDispatchHistory, total int64, startTime, endTime string, rankGrowthRate, scoreGrowthRate float64) shipperResource.ShipperRankDetailResponse {
	response := shipperResource.ShipperRankDetailResponse{}

	// 基本信息
	response.ShipperId = tools.ToInt64(shipper.ID)
	response.Mobile = shipper.Mobile
	response.RealName = shipper.RealName
	response.Avatar = tools.CdnUrl(shipper.Avatar)
	// 固定的分数
	response.ShipperScore = tools.FormatScore(thisWeekHistory.FinalScore)
	response.ShipperRank = tools.FormatScore(thisWeekHistory.Rank)
	response.ShipperRankStartTime = startTime
	response.ShipperRankEndTime = endTime
	response.ShipperRankGrowthRate = tools.FormatScore(rankGrowthRate)
	response.ShipperScoreGrowthRate = tools.FormatScore(scoreGrowthRate)
	// 动态的分数
	response.ShipperDynamicScore = tools.FormatScore(lastWeekHistory.FinalScore)
	response.ShipperDynamicRank = tools.FormatScore(lastWeekHistory.Rank)


	// 基础分及增长率
	baseScoreResponse := shipperResource.DataTypeListResponse{
		Id:      shipperResource.ShipperRankTypeBaseScore,
		Name:    t.langUtil.TArr("shipper_rank_detail_types")[shipperResource.ShipperRankTypeBaseScore],
		Count:   thisWeekHistory.BaseScore,
		Percent: tools.FormatScore(t.calculateGrowthRate(thisWeekHistory.BaseScore, lastWeekHistory.BaseScore)),
		Score:   "0",
	}

	// 好评相关
	goodCommentResponse := shipperResource.DataTypeListResponse{
		Id:      shipperResource.ShipperRankTypeGoodComment,
		Name:    t.langUtil.TArr("shipper_rank_detail_types")[shipperResource.ShipperRankTypeGoodComment],
		Count:   thisWeekHistory.PositiveReviewsCount,
		Percent: tools.FormatScore(t.calculateGrowthRate(thisWeekHistory.PositiveReviewsCount, lastWeekHistory.PositiveReviewsCount)),
		Score:   tools.FormatScore(thisWeekHistory.PositiveReviewsScore),
	}


	// 准时送达相关
	diliveredOnTimeResponse := shipperResource.DataTypeListResponse{
		Id:      shipperResource.ShipperRankTypeDiliveredOnTime,
		Name:    t.langUtil.TArr("shipper_rank_detail_types")[shipperResource.ShipperRankTypeDiliveredOnTime],
		Count:   thisWeekHistory.DeliveredOnTimeOrderCount,
		Percent: tools.FormatScore(t.calculateGrowthRate(thisWeekHistory.DeliveredOnTimeOrderCount, lastWeekHistory.DeliveredOnTimeOrderCount)),
		Score:   tools.FormatScore(thisWeekHistory.DeliveredOnTime),
	}



	// 订单总数及增长率
	orderCountResponse := shipperResource.DataTypeListResponse{
		Id:      shipperResource.ShipperRankTypeOrderCount,
		Name:    t.langUtil.TArr("shipper_rank_detail_types")[shipperResource.ShipperRankTypeOrderCount],
		Count:   thisWeekHistory.OrderTotalCount,
		Percent: tools.FormatScore(t.calculateGrowthRate(thisWeekHistory.OrderTotalCount, lastWeekHistory.OrderTotalCount)),
		Score:   "0",
	}



	// 轻度迟到相关
	lateTime0To5Response := shipperResource.DataTypeListResponse{
		Id:      shipperResource.ShipperRankTypeLateTime0To5,
		Name:    t.langUtil.TArr("shipper_rank_detail_types")[shipperResource.ShipperRankTypeLateTime0To5],
		Count:   thisWeekHistory.MildLatenessCount,
		Percent: tools.FormatScore(t.calculateGrowthRate(thisWeekHistory.MildLatenessCount, lastWeekHistory.MildLatenessCount)),
		Score:   tools.FormatScore(thisWeekHistory.MildLatenessDeduct),
	}



	// 中度迟到相关
	lateTime5To10Response := shipperResource.DataTypeListResponse{
		Id:      shipperResource.ShipperRankTypeLateTime5To10,
		Name:    t.langUtil.TArr("shipper_rank_detail_types")[shipperResource.ShipperRankTypeLateTime5To10],
		Count:   thisWeekHistory.ModerateLatenessCount,
		Percent: tools.FormatScore(t.calculateGrowthRate(thisWeekHistory.ModerateLatenessCount, lastWeekHistory.ModerateLatenessCount)),
		Score:   tools.FormatScore(thisWeekHistory.ModerateLatenessDeduct),
	}

	// 严重迟到相关
	lateTime10ToMaxResponse := shipperResource.DataTypeListResponse{
		Id:      shipperResource.ShipperRankTypeLateTime10ToMax,
		Name:    t.langUtil.TArr("shipper_rank_detail_types")[shipperResource.ShipperRankTypeLateTime10ToMax],
		Count:   thisWeekHistory.SevereLatenessCount,
		Percent: tools.FormatScore(t.calculateGrowthRate(thisWeekHistory.SevereLatenessCount, lastWeekHistory.SevereLatenessCount)),
		Score:   tools.FormatScore(thisWeekHistory.SevereLatenessDeduct),
	}

	// 差评相关 ShipperRankTypeBadComment
	badCommentResponse := shipperResource.DataTypeListResponse{
		Id:      shipperResource.ShipperRankTypeBadComment,
		Name:    t.langUtil.TArr("shipper_rank_detail_types")[shipperResource.ShipperRankTypeBadComment],
		Count:   thisWeekHistory.NegativeReviewsCount,
		Percent: tools.FormatScore(t.calculateGrowthRate(thisWeekHistory.NegativeReviewsCount, lastWeekHistory.NegativeReviewsCount)),
		Score:   tools.FormatScore(thisWeekHistory.NegativeReviewsDeduct),
	}

	// 投诉相关
	complainResponse := shipperResource.DataTypeListResponse{
		Id:      shipperResource.ShipperRankTypeComplain,
		Name:    t.langUtil.TArr("shipper_rank_detail_types")[shipperResource.ShipperRankTypeComplain],
		Count:   thisWeekHistory.ComplaintsCount,
		Percent: tools.FormatScore(t.calculateGrowthRate(thisWeekHistory.ComplaintsCount, lastWeekHistory.ComplaintsCount)),
		Score:   tools.FormatScore(thisWeekHistory.ComplaintsDeduct),
	}

	// 早送相关
	earlyDelivery20MinResponse := shipperResource.DataTypeListResponse{
		Id:      shipperResource.ShipperRankTypeEarlyDelivery20Min,
		Name:    t.langUtil.TArr("shipper_rank_detail_types")[shipperResource.ShipperRankTypeEarlyDelivery20Min],
		Count:   thisWeekHistory.EarlyDeliveryCount,
		Percent: tools.FormatScore(t.calculateGrowthRate(thisWeekHistory.EarlyDeliveryCount, lastWeekHistory.EarlyDeliveryCount)),
		Score:   tools.FormatScore(thisWeekHistory.EarlyDeliveryDeduct),
	}

	var dataTypePlusList []shipperResource.DataTypeListResponse
	dataTypePlusList = append(
		dataTypePlusList,

		baseScoreResponse,

		orderCountResponse,
		goodCommentResponse,
		diliveredOnTimeResponse,
		)
	var dataTypeMinusList []shipperResource.DataTypeListResponse
	dataTypeMinusList = append(
		dataTypeMinusList,

		lateTime0To5Response,
		lateTime5To10Response,
		lateTime10ToMaxResponse,
		badCommentResponse,
		complainResponse,
		earlyDelivery20MinResponse,
	)
	var plus = shipperResource.DataTypePlusOrMinus{
		Name: "plus",
		DataTypeList: dataTypePlusList,
	}
	var minus = shipperResource.DataTypePlusOrMinus{
		Name: "minus",
		DataTypeList: dataTypeMinusList,
	}
	response.DataTypePlusOrMinus = []shipperResource.DataTypePlusOrMinus{plus, minus}

	// 订单列表
	var shipperRankDetailOrderList = []shipperResource.ShipperRankDetailOrderListResponse{}

	for _, autoDispatchOrder := range orderList {
		// 安全获取餐厅名称
		var restaurantName string
		if autoDispatchOrder.Order.ID > 0 && autoDispatchOrder.Order.Restaurant.ID > 0 {
			restaurantName = tools.GetNameByLang(autoDispatchOrder.Order.Restaurant, t.language)
		} else if autoDispatchOrder.OrderToday.ID > 0 && autoDispatchOrder.OrderToday.Restaurant.ID > 0 {
			restaurantName = tools.GetNameByLang(autoDispatchOrder.OrderToday.Restaurant, t.language)
		} else {
			restaurantName = ""
		}

		// 安全获取用户名称
		var userName string
		var userId int
		if autoDispatchOrder.Order.ID > 0 && autoDispatchOrder.Order.User.ID > 0 {
			userName = autoDispatchOrder.Order.User.Name
			userId = autoDispatchOrder.Order.User.ID
		} else if autoDispatchOrder.OrderToday.ID > 0 && autoDispatchOrder.OrderToday.User.ID > 0 {
			userName = autoDispatchOrder.OrderToday.User.Name
			userId = autoDispatchOrder.OrderToday.User.ID
		} else {
			userName = ""
		}

		// 安全获取配送时间
		var deliveryTime string
		if autoDispatchOrder.Order.ID > 0 {
			if autoDispatchOrder.Order.DeliveryEndTime != nil {
				deliveryTime = autoDispatchOrder.Order.DeliveryEndTime.Format(time.DateTime)
			}
		} else if autoDispatchOrder.OrderToday.ID > 0 {
			if !autoDispatchOrder.OrderToday.DeliveryEndTime.IsZero() {
				deliveryTime = autoDispatchOrder.OrderToday.DeliveryEndTime.Format(time.DateTime)
			}
		}
		// 计算获得的分数
		calculateScoreObtain := func(autoDispatchOrder models.AutoDispatchHistory) float64 {

			return autoDispatchOrder.PositiveReviewsScore;
		}
		// 计算扣除的分数
		calculateScoreDeduct := func(autoDispatchOrder models.AutoDispatchHistory) float64 {
			return autoDispatchOrder.MildLatenessDeduct + autoDispatchOrder.ModerateLatenessDeduct + autoDispatchOrder.SevereLatenessDeduct + autoDispatchOrder.NegativeReviewsDeduct + autoDispatchOrder.ComplaintsDeduct + autoDispatchOrder.EarlyDeliveryDeduct;
		}
		// 转换为响应格式
		orderItem := shipperResource.ShipperRankDetailOrderListResponse{
			OrderNo:            autoDispatchOrder.OrderNo,
			OrderId:            autoDispatchOrder.OrderID,
			OrderTime:          autoDispatchOrder.CreatedAt.Format(time.DateTime),
			ScoreObtain:        calculateScoreObtain(autoDispatchOrder),
			ScoreDeduct:        calculateScoreDeduct(autoDispatchOrder), // 默认值，如果需要计算扣分可以在这里添加逻辑
			RestaurantName:     restaurantName,
			UserName:           userName,
			UserId:             userId,
			DeliveryTime:       deliveryTime,
			DeliveryState:      autoDispatchOrder.OrderDeliverState,
			DeliveryStateName:  t.langUtil.TArr("auto_dispatch_order_deliver_state")[autoDispatchOrder.OrderDeliverState],
			CommentState:       tools.If(len(autoDispatchOrder.Comments) > 0, 1, 0),
			CommentStateName:   tools.If(len(autoDispatchOrder.Comments) > 0, t.langUtil.T("auto_dispatch_order_comment_state_has"), t.langUtil.T("auto_dispatch_order_comment_state_has_not")),
			ComplaintState:     tools.If(len(autoDispatchOrder.ShipperIncomes) > 0, 1, 0),
			ComplaintStateName: tools.If(len(autoDispatchOrder.ShipperIncomes) > 0, t.langUtil.T("auto_dispatch_order_complain_state_has"), t.langUtil.T("auto_dispatch_order_complain_state_has_not")),
		}

		shipperRankDetailOrderList = append(shipperRankDetailOrderList, orderItem)
	}
	response.OrderList = shipperResource.ShipperRankOrderListResponse{
		Total: total,
		List:  shipperRankDetailOrderList,
	}

	return response
}

// calculateGrowthRate 计算增长率
func (t ShipperTransformer) calculateGrowthRate(thisWeek, lastWeek float64) float64 {
	if lastWeek == 0 {
		if thisWeek > 0 {
			return 100.0 // 如果上周为0，本周有值，则视为100%增长
		}
		return 0.0 // 如果都为0，则没有增长
	}
	return ((thisWeek - lastWeek) / lastWeek) * 100
}

func NewShipperTransformer(c *gin.Context) *ShipperTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	shipperTransformer := ShipperTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &shipperTransformer
}
