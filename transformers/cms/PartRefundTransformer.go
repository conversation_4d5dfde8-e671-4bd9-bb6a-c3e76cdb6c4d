package cms

import (
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	cmsResources "mulazim-api/resources/cms"
	"mulazim-api/tools"
	"mulazim-api/transformers/merchant"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type PartRefundTransformer struct {
	langUtil *lang.LangUtil
	language string
}

// RefundReasonListFormat
//
//	@Author: YaKupJan
//	@Date: 2024-11-26 19:14:10
//	@Description: 格式化退款原因列表
//	@receiver p
//	@param refundReasonList
//	@param total
//	@return cmsResources.PartRefundReasonListResource
func (p PartRefundTransformer) RefundReasonListFormat(refundReasonList []models.Dictionary, total int64) cmsResources.PartRefundReasonListResource {
	var items []cmsResources.PartRefundReasonFormat
	for _, reason := range refundReasonList {
		item := cmsResources.PartRefundReasonFormat{
			ID:        reason.ID,
			Name:      tools.GetNameByLang(reason, p.language),
			NameUg:    reason.NameUg,
			NameZh:    reason.NameZh,
			Weight:    reason.Weight,
			State:     reason.State,
			Type:      reason.Type,
			SubType:   reason.SubType,
			CreatedAt: tools.TimeFormatYmdHis(reason.CreatedAt),
		}
		items = append(items, item)
	}
	data := cmsResources.PartRefundReasonListResource{
		Items: tools.If(len(items) == 0, []cmsResources.PartRefundReasonFormat{}, items),
		Total: total,
	}
	return data
}

func (p PartRefundTransformer) RefundListFormat(orderPartRefunds []models.OrderPartRefund, total int64) cmsResources.PartRefundListResource {
	var items []cmsResources.PartRefundFormat
	for _, refund := range orderPartRefunds {
		item := cmsResources.PartRefundFormat{
			ID:              refund.ID,
			RestaurantName:  tools.GetNameByLang(refund.Restaurant, p.language),
			CreatorName:     refund.Creator.Name,
			OrderNumber:     refund.OrderNumber,
			PartRefundState: refund.PartRefundState,
			Reason:          tools.If(refund.PartRefundReasonId == 0, refund.PartRefundReasonText, tools.GetNameByLang(refund.RefundReason, p.language)),
			RefundTime:      carbon.Parse(refund.PartRefundedTime, configs.AsiaShanghai).Format("Y-m-d H:i:s"),
		}
		items = append(items, item)
	}
	data := cmsResources.PartRefundListResource{
		Items: tools.If(len(items) == 0, []cmsResources.PartRefundFormat{}, items),
		Total: total,
	}
	return data
}

// PartRefundOrderListFormat
//
//  @Author: Rixat
//  @Date: 2025-03-09 17:47:59
//  @Description: 格式化部分退款订单列表
//  @param partRefundList []models.OrderPartRefund 退款订单列表
//  @param total int64 总记录数
//  @return cmsResources.PartRefundListResource 格式化后的退款订单列表
func (p PartRefundTransformer) PartRefundOrderListFormat(partRefundList []models.OrderPartRefund, total int64) cmsResources.PartRefundListResource {
	var items []cmsResources.PartRefundFormat
	for _, refund := range partRefundList {
		item := cmsResources.PartRefundFormat{
			ID:              refund.ID,
			RestaurantName:  tools.GetNameByLang(refund.Restaurant, p.language),	
			OrderAddress : refund.OrderAddress,
			CreatorName:     refund.Creator.Name,
			OrderNumber:     refund.OrderNumber,
			PartRefundState: refund.PartRefundState,
			Reason:          tools.If(refund.PartRefundReasonId == 0, refund.PartRefundReasonText, tools.GetNameByLang(refund.RefundReason, p.language)),
			CreatedAt:       tools.TimeFormatYmdHis(&refund.CreatedAt),
			RefundTime:      carbon.Parse(refund.PartRefundedTime, configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			AreaName:        tools.GetNameByLang(refund.Area, p.language),
			CityName:        tools.GetNameByLang(refund.City, p.language),
			PartRefundCreatorType:      refund.PartRefundCreatorType, // 1:商家 2:后台
			PartRefundType:      refund.PartRefundType,   // 1:全额 2:部分退款
			UserName:      refund.Name,   // 1:全额 2:部分退款
			UserMobile:      refund.Mobile,   // 1:全额 2:部分退款
		}
		items = append(items, item)
	}
	data := cmsResources.PartRefundListResource{
		Items: tools.If(len(items) == 0, []cmsResources.PartRefundFormat{}, items),
		Total: total,
	}
	return data
}

// PartRefundOrderDetailFormat
//
//  @Author: Rixat
//  @Date: 2025-03-09 17:47:59
//  @Description: 格式化部分退款订单详情
//  @param partRefund models.OrderPartRefund 退款订单详情
//  @return cmsResources.PartRefundOrderDetailFormat 格式化后的退款订单详情
func (p PartRefundTransformer) PartRefundOrderDetailFormat(partRefund models.OrderPartRefund) cmsResources.PartRefundOrderDetailFormat {
	// 退款主体
	data := cmsResources.PartRefundOrderDetailFormat{
		ID:             partRefund.ID,
		OrderNumber:    partRefund.OrderNumber,
		UserName:        partRefund.Name,
		UserMobile:      partRefund.Mobile,
		UserRank:       partRefund.UserRank.Rank,
		Terminal:        tools.GetNameByLang(partRefund.Terminal, p.language),
		RestaurantName: tools.GetNameByLang(partRefund.Restaurant, p.language),
		RestaurantPhone: partRefund.Restaurant.Tel,
		ShipperMobile:   tools.If(partRefund.Shipper.ID > 0, partRefund.Shipper.Mobile, ""),
		CreatorName:     tools.If(partRefund.Creator.ID > 0, partRefund.Creator.Name, ""),
		RefundReason:    tools.If(partRefund.RefundReason.ID > 0, tools.GetNameByLang(partRefund.RefundReason, p.language), ""),
		RefundReasonText: partRefund.PartRefundReasonText,

		Shipper: tools.If(partRefund.Shipper.ID > 0, &cmsResources.PartRefundShipper{
			ID:          partRefund.Shipper.ID,
			Avatar:      partRefund.Shipper.Avatar,
			Mobile:      partRefund.Shipper.Mobile,
			Name:        partRefund.Shipper.Name,
			RealName:    partRefund.Shipper.RealName,
			Description: partRefund.Shipper.Description,
		}, nil),

		PartRefundDetail: cmsResources.PartRefundDetail{
			CreatedAt: tools.TimeFormatYmdHis(&partRefund.CreatedAt),

			DeliveryType:      partRefund.DeliveryType,
			Description:       partRefund.Description,
			Timezone:          partRefund.Timezone,
			BookingTime:       tools.TimeFormatYmdHis(&partRefund.BookingTime),
			PayTime:           tools.TimeFormatYmdHis(partRefund.PayTime),
			PrintTime:         tools.TimeFormatYmdHis(partRefund.PrintTime),
			PrintedTime:       tools.TimeFormatYmdHis(partRefund.PrintedTime),
			DeliveryTakedTime: tools.TimeFormatYmdHis(partRefund.DeliveryTakedTime),
			DeliveryStartTime: tools.TimeFormatYmdHis(partRefund.DeliveryStartTime),
			DeliveryEndTime:   tools.TimeFormatYmdHis(partRefund.DeliveryEndTime),

			PartRefundCreatorId:   partRefund.PartRefundCreatorId,
			PartRefundCreatorType: partRefund.PartRefundCreatorType,
			PartRefundReasonId:    partRefund.PartRefundReasonId,
			PartRefundReasonText:  partRefund.PartRefundReasonText,
			PartRefundType:        partRefund.PartRefundType,
			PartRefundAmount:      partRefund.PartRefundAmount,
			PartRefundState:       partRefund.PartRefundState,
			PartRefundFailReason:  partRefund.PartRefundFailReason,
			PartRefundedTime:      carbon.Parse(partRefund.PartRefundedTime).Format("Y-m-d H:i:s"),
		},
	}
	if partRefund.PartRefundType == 1 {
		if partRefund.OrderFailReason.ID >0 {
			data.RefundReason = tools.If(p.language == "zh", partRefund.OrderFailReason.ReasonZh, partRefund.OrderFailReason.ReasonUg)
			data.RefundReasonText = tools.If(p.language == "zh", partRefund.OrderFailReason.ReasonZh, partRefund.OrderFailReason.ReasonUg)
		}
	}

	// 原始订单详情
	var lunchBoxDetail []cmsResources.OriginalOrderDetail
	var originalOrderDetails []cmsResources.OriginalOrderDetail
	for _, refundDetail := range partRefund.OrderPartRefundDetail {
		if refundDetail.Type == 1 {
			// 构造美食数据
			detail := cmsResources.OriginalOrderDetail{
				ID: refundDetail.OrderId,
				FoodName:      tools.GetNameByLang(refundDetail.RestaurantFoods, p.language),
				FoodCount:     tools.ToInt(refundDetail.Number),
				OriginalPrice: tools.ToInt(refundDetail.OriginalPrice),
				Price:         tools.ToInt(refundDetail.Price),
				MarkupPrice:   tools.ToInt(refundDetail.PriceMarkupPrice),
				TotalPrice:    tools.ToInt(refundDetail.Price) * tools.ToInt(refundDetail.Number),
				FoodType: refundDetail.FoodType,
			}
			// 如果是规格美食
			if refundDetail.FoodType == models.RestaurantFoodsTypeSpec {
				detail.FoodSpecOption = refundDetail.SelectedSpec.GetOptions(refundDetail.SelectedSpec, p.language)
			} else if refundDetail.FoodType == models.RestaurantFoodsTypeCombo {
				// 如果是套餐美食
				detail.ComboFoodItems = merchant.GetFormattedComboItems(refundDetail.RestaurantFoods.ComboFoodItems, p.language)
			}
			originalOrderDetails = append(originalOrderDetails, detail)

			// 收集餐盒数据
			if refundDetail.LunchBoxId > 0 && refundDetail.LunchBoxCount > 0 {
				lunchBox := refundDetail.LunchBox
				if refundDetail.FoodType == models.RestaurantFoodsTypeCombo {
					lunchBox.NameUg = "يۈرۈشلۈك قاچا"
					lunchBox.NameZh = "套餐饭盒"
					lunchBox.UnitPrice = tools.ToInt(refundDetail.LunchBoxFee)
				}
				detail := cmsResources.OriginalOrderDetail{
					FoodName:      tools.GetNameByLang(lunchBox, p.language),
					FoodCount:     tools.ToInt(refundDetail.LunchBoxCount),
					OriginalPrice: lunchBox.UnitPrice,
					Price:         tools.ToInt(refundDetail.LunchBoxFee),
					TotalPrice:    tools.ToInt(refundDetail.LunchBoxFee) * tools.ToInt(refundDetail.LunchBoxCount),
				}
				lunchBoxDetail = append(lunchBoxDetail, detail)
			}
		}
	}
	originalOrderDetails = append(originalOrderDetails, lunchBoxDetail...)
	data.OriginalOrderDetails = originalOrderDetails

	// 退款详情
	refundDetails := make([]cmsResources.OriginalOrderDetail,0)
	refundLunchBoxDetails := make([]cmsResources.OriginalOrderDetail,0)
	if partRefund.PartRefundType == 1 {
		// 全额退款
		refundDetails = originalOrderDetails
	} else {
		for _, refundDetail := range partRefund.OrderPartRefundDetail {
			if refundDetail.Type == 2 {
				// 构造美食数据
				detail := cmsResources.OriginalOrderDetail{
					ID:       refundDetail.ID,
					FoodName:      tools.GetNameByLang(refundDetail.RestaurantFoods, p.language),
					FoodCount:     tools.ToInt(refundDetail.Number),
					OriginalPrice: tools.ToInt(refundDetail.OriginalPrice),
					Price:         tools.ToInt(refundDetail.Price),
					MarkupPrice:   tools.ToInt(refundDetail.PriceMarkupPrice),
					TotalPrice:    tools.ToInt(refundDetail.Price) * tools.ToInt(refundDetail.Number),
					FoodType: refundDetail.FoodType,
				}
				// 如果是规格美食
				if refundDetail.FoodType == models.RestaurantFoodsTypeSpec {
					detail.FoodSpecOption = refundDetail.SelectedSpec.GetOptions(refundDetail.SelectedSpec, p.language)
				} else if refundDetail.FoodType == models.RestaurantFoodsTypeCombo {
					// 如果是套餐美食
					detail.ComboFoodItems = merchant.GetFormattedComboItems(refundDetail.RestaurantFoods.ComboFoodItems, p.language)
				}
				refundDetails = append(refundDetails, detail)

				// 收集餐盒数据
				if refundDetail.LunchBoxId > 0 {
					lunchBox := refundDetail.LunchBox
					if refundDetail.FoodType == models.RestaurantFoodsTypeCombo {
						lunchBox.NameUg = "يۈرۈشلۈك قاچا"
						lunchBox.NameZh = "套餐饭盒"
						lunchBox.UnitPrice = tools.ToInt(refundDetail.LunchBoxFee)
					}
					detail := cmsResources.OriginalOrderDetail{
						FoodName:      tools.GetNameByLang(lunchBox, p.language),
						FoodCount:     tools.ToInt(refundDetail.LunchBoxCount),
						OriginalPrice: lunchBox.UnitPrice,
						Price:         tools.ToInt(refundDetail.LunchBoxFee),
						TotalPrice:    tools.ToInt(refundDetail.LunchBoxFee) * tools.ToInt(refundDetail.LunchBoxCount),
					}
					refundLunchBoxDetails = append(refundLunchBoxDetails, detail)
				}
			}
		}
	}
	refundDetails = append(refundDetails, refundLunchBoxDetails...)
	data.RefundDetails = refundDetails

	foodsCount := 0
	for _, refundDetail := range partRefund.OrderPartRefundDetail {
		if refundDetail.Type == 1 {
			foodsCount += tools.ToInt(refundDetail.Number)
		}
	}

	// 原始支付详情
	data.OriginalPayDetails = []cmsResources.OriginalPayDetail{
		{
			PayTypeName: tools.GetNameByLang(partRefund.PayTypes, p.language),
			FootCount:   tools.ToInt(foodsCount),
			FoodPrice:   tools.ToInt(partRefund.Price),
			Shipment:    tools.ToInt(partRefund.Shipment),
			OrderPrice:  tools.ToInt(partRefund.OrderPrice),
			ActualPaid:  tools.ToInt(partRefund.ActualPaid),
			NeedPay:     tools.ToInt(partRefund.OrderPrice),
			ResAmount:   tools.ToInt(partRefund.ResProfit),
		},
	}

	var marketDetails []cmsResources.MarketDetail
	for _, market := range partRefund.MarketingOrderLog {
		reduceAmount := tools.ToInt(market.StepReduce)
		detail := cmsResources.MarketDetail{
			MarketType:   market.Type,
			MarketName:   tools.GetNameByLang(market, p.language),
			ReduceAmount: reduceAmount,
		}
		marketDetails = append(marketDetails, detail)
	}
	data.MarketDetails = marketDetails

	return data
}


func NewPartRefundTransformer(c *gin.Context) *PartRefundTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	transformer := PartRefundTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &transformer
}
