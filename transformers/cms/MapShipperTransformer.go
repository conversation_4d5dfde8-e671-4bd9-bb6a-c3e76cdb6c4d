package cms

import (
	"fmt"
	"math"
	"mulazim-api/lang"
	"mulazim-api/models"
	cmsResource "mulazim-api/resources/cms"
	"mulazim-api/tools"
	"sort"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type MapShipperTransformer struct {
	langUtil *lang.LangUtil
	language string
}
//
// TransformList
//  @Description: 转换列表
//  @receiver t
//  @param list
//  @return []cmsResource.ShipperMapList
//
func (t MapShipperTransformer) TransformList(list []models.Admin, orderToday *models.OrderToday) []cmsResource.ShipperMapList {
	result := make([]cmsResource.ShipperMapList,0)
	uyBasesAndEx := tools.UyBasesAndEx{}
	for _, v := range list {
		item := cmsResource.ShipperMapList{
			ID:         v.ID,
			RealName:   v.RealName,
			RealNameFormatted: uyBasesAndEx.GetReorderedText(uyBasesAndEx.ToEx(v.RealName)),
			Lat:        v.Lat,
			Lng:        v.Lng,
			Speed:      v.Speed,
			Accuracy:   v.Accuracy,
			Avatar:     tools.AddCdn(v.Avatar),
			AttendanceState : v.AttendanceState,
			AttendanceStateName : tools.If(v.AttendanceState==1,"",t.langUtil.T("attendance_off")),
			AutoDispatchRank : v.AutoDispatchRank,
			
		}
		if orderToday.ID != 0 && v.Lat != 0 && v.Lng != 0{
			item.OrderDistance = tools.CalculateLatitudeLongitudeDistance(v.Lng,v.Lat,orderToday.Restaurant.Lng,orderToday.Restaurant.Lat)
		}
		sendingOrders := make([]cmsResource.ShipperMapListSendingOrders,0)
		for _,order := range v.TodayOrders{
			price := tools.ToInt64(order.Price)+tools.ToInt64(order.Shipment)+tools.ToInt64(order.LunchBoxFee)
			sOrder := cmsResource.ShipperMapListSendingOrders{
				ID:         order.ID,
				State:      order.State,
				OrderId:    order.OrderID,
				CustomerPos: []string{fmt.Sprintf("%f",order.Building.Lng),fmt.Sprintf("%f",order.Building.Lat)},
				ResPos:     []string{fmt.Sprintf("%f",order.Restaurant.Lng),fmt.Sprintf("%f",order.Restaurant.Lat)},
				ResName: order.Restaurant.NameUg,
				BuildingName: order.Building.NameUg,
				Price:      tools.ToPrice(float64(price)/float64(100)),
				OrderAddress: order.OrderAddress,
				SerialNumber: tools.ToInt64(order.SerialNumber),
				Distance: fmt.Sprintf("%.2f",order.Distance),
				ResLogo: tools.AddCdn(order.Restaurant.Logo),
				CustomerAvatar: tools.AddCdn(order.User.Avatar),
				SetChannel: order.TakeOrder.SetChannel,
			}
			if order.OrderExtend!=nil && order.OrderExtend.OrderID != 0{
				sOrder.ShipmentState = t.langUtil.TArr("shipper_order_state")[order.OrderExtend.ShipperOrderState]
			}
			//判断订单是否迟到
			if order.BookingTime != ""{
				bookingTime := carbon.Parse(order.BookingTime).Carbon2Time()
				if bookingTime.Before(time.Now()){
					sOrder.IsDelay = 1
				}else{
					sOrder.IsDelay = 0
				}
			}
			//计算迟到分钟
			if order.BookingTime != ""{
				bookingTime := carbon.Parse(order.BookingTime).Carbon2Time()
				sOrder.LeftMinutes = -int(time.Now().Sub(bookingTime).Minutes())
			}
			sendingOrders = append(sendingOrders,sOrder)
		}
		item.ShipperMapListSendingOrders = sendingOrders
		item.Location = []string{fmt.Sprintf("%f",v.Lng),fmt.Sprintf("%f",v.Lat)}
		item.PosState = 1
		if math.Abs(item.Lat)<0.01 || math.Abs(item.Lng)<0.01{
			item.PosState = 0
			item.LastTime = t.langUtil.T("offline")
		}else{
			item.LastTime,item.PosState = t.formatDuration(time.Now().Sub(v.LoginTime))
		}
		result = append(result, item)
	}
	return result
}
//
// TransformNewReceivedOrderList
//  @Description: 转换新接单列表,
//  @receiver t
//  @param list
//  @return []cmsResource.ShipperReceivedNewOrderList
//
func (t MapShipperTransformer) TransformNewReceivedOrderList(list []models.OrderToday) []cmsResource.ShipperReceivedNewOrderList {
	result := make([]cmsResource.ShipperReceivedNewOrderList,0)
	for _, v := range list {
		price := tools.ToInt64(v.Price)+tools.ToInt64(v.Shipment)+tools.ToInt64(v.LunchBoxFee)
		item := cmsResource.ShipperReceivedNewOrderList{
			ID:         v.ID,
			State:      v.State,
			OrderId:    v.OrderID,
			ResName:    tools.If(t.language=="ug",v.Restaurant.NameUg,v.Restaurant.NameZh),
			BuildingName: tools.If(t.language=="ug",v.Building.NameUg,v.Building.NameZh),
			CustomerPos: []string{fmt.Sprintf("%f",v.Building.Lng),fmt.Sprintf("%f",v.Building.Lat)},
			ResPos:     []string{fmt.Sprintf("%f",v.Restaurant.Lng),fmt.Sprintf("%f",v.Restaurant.Lat)},
			BookingTime: carbon.Parse(v.BookingTime).Format("Y-m-d H:i"),
			Price:      tools.ToPrice(float64(price)/float64(100)),
			OrderAddress: v.OrderAddress,
			SerialNumber: tools.ToInt64(v.SerialNumber),
			Distance: fmt.Sprintf("%.2f",v.Distance),
			OrderType: int64(v.OrderType),
		}
		//判断订单是否迟到
		if v.BookingTime != ""{
			bookingTime := carbon.Parse(v.BookingTime).Carbon2Time()
			if bookingTime.Before(time.Now()){
				item.IsDelay = 1
			}else{
				item.IsDelay = 0
			}
		}
		//计算迟到分钟
		if v.BookingTime != ""{
			bookingTime := carbon.Parse(v.BookingTime).Carbon2Time()
			item.LeftMinutes = -int(time.Now().Sub(bookingTime).Minutes())
		}
		//if carbon.Parse(v.BookingTime).{
		//
		//}
		result = append(result, item)
	}
	return result
}
func (t MapShipperTransformer) formatDuration(duration time.Duration) (string,int) {
	minute := time.Minute
	hour := time.Hour
	day := 24 * time.Hour
	month := 30 * day

	switch {
	case duration < minute:
		return fmt.Sprintf("%d%s", int(duration.Seconds()),t.langUtil.T("seconds_ago")),1
	case duration < hour:
		return fmt.Sprintf("%d%s", int(duration.Minutes()),t.langUtil.T("minute_ago")),0
	case duration < day:
		return fmt.Sprintf("%d%s", int(duration.Hours()),t.langUtil.T("hour_ago")),0
	case duration < month:
		return fmt.Sprintf("%d%s", int(duration.Hours()/24),t.langUtil.T("day_ago")),0
	default:
		return fmt.Sprintf(t.langUtil.T("offline")),0
	}
}

type ByDistance []cmsResource.ShipperMapList

func (s ByDistance) Len() int {
	return len(s)
}

func (s ByDistance) Less(i, j int) bool {
	//无法定位的排到最后
	if (s[i].Lat == 0 || s[i].Lng == 0 ) && (s[j].Lat != 0 && s[j].Lng != 0 ) {
		return false
	}else if (s[i].Lat != 0 && s[i].Lng != 0 ) && (s[j].Lat == 0 || s[j].Lng == 0 ) {
		return true
	}else{
		return s[i].OrderDistance < s[j].OrderDistance
	}
}
func (s ByDistance) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

type ByState []cmsResource.ShipperMapList

func (s ByState) Len() int {
	return len(s)
}

func (s ByState) Less(i, j int) bool {
	//无法定位的排到最后
	if s[i].PosState != 0 && s[j].PosState ==0 {
		return true
	}
	return false
	//if (s[i].Lat == 0 || s[i].Lng == 0 ) && (s[j].Lat != 0 && s[j].Lng != 0 ) {
	//	return false
	//}else if (s[i].Lat != 0 && s[i].Lng != 0 ) && (s[j].Lat == 0 || s[j].Lng == 0 ) {
	//	return true
	//}else{
	//	return s[i].OrderDistance < s[j].OrderDistance
	//}
}

func (s ByState) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}
//
// SortResult
//  @Description: 排序结果
//  @receiver t
//  @param result
//  @return []cmsResource.ShipperMapList
//
func (t MapShipperTransformer) SortResult(result []cmsResource.ShipperMapList, today *models.OrderToday) []cmsResource.ShipperMapList {
	if today.ID>0 {
		sort.Sort(ByDistance(result))
	}else{
		sort.Sort(ByState(result))
	}
	return result
}




//
// NewMapShipperTransformer
//  @Description: 初始化
//  @param c
//  @return *MapShipperTransformer
//
func NewMapShipperTransformer(c *gin.Context) *MapShipperTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	merchantTransformer := MapShipperTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &merchantTransformer
}