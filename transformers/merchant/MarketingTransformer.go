package merchant

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources/merchant"
	"mulazim-api/resources/response"
	"mulazim-api/tools"
	"strconv"
)

type MarketingTransformer struct {
	langUtil *lang.LangUtil
	language string
}

// NewMerchantTransformer
//
//	@Description: 初始化商家端Transformer
//	@return *MerchantTransformer
func NewMarketingTransformer(c *gin.Context) *MarketingTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	marketingTransformer := MarketingTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &marketingTransformer
}

// FormatNewOrder
//
//	@Description: 新订单返回数据格式化，适配旧版本Android客户端
//	@receiver t
//	@param orderToday
//	@param receiveOrderTimeLimit
//	@return resources.NewOrderEntity
func (t *MarketingTransformer) FormatMarketingList(merketModelArr []models.Marketing) []models.Marketing{
	merketList := []models.Marketing{}
	for i := 0; i < len(merketModelArr); i++ {
		order := merketModelArr[i]
		rtnOrder := models.Marketing{
			ID:           order.ID,
			
		}
		merketList = append(merketList, rtnOrder)
	}
	return merketList
}

/***
 * @Author: [Salam]
 * @description: 格式化满减活动
 * @Date: 2025-05-28 15:55:53
 * @param {models.Marketing} mkt
 */
func (trns *MarketingTransformer) FormatMarketing(mkt *models.Marketing) response.MarketingDetail {
	fmtMkt := response.MarketingDetail{
		ID:             mkt.ID,
		RestaurantID:   mkt.RestaurantID,
		MarketingType:  mkt.MarketingType,
		Type:           mkt.Type,
		State:          mkt.State,
		NameUg:         mkt.NameUg,
		NameZh:         mkt.NameZh,
		BeginDate:      carbon.Time2Carbon(mkt.BeginDate).Format("Y-m-d"),
		EndDate:        carbon.Time2Carbon(mkt.EndDate).Format("Y-m-d"),
		CreatedAt:      carbon.Time2Carbon(mkt.CreatedAt).Format("Y-m-d"),
		PriceMax:       mkt.PriceMax,
		PriceReduceMax: mkt.PriceReduceMax,
		CreatorType:    mkt.CreatorType,
		FoodsJson:      mkt.FoodsJson,
	}

	if mkt.CreatorType > 0 {
		if mkt.CreatorType == 1 {
			fmtMkt.CreatorTypeName = trns.langUtil.T("created_by_admin")
		} else if mkt.CreatorType == 2 {
			fmtMkt.CreatorTypeName = trns.langUtil.T("created_by_owner")
		}
	}

	if mkt.State > 0 {
		stt := tools.ToString(mkt.State)
		fmtMkt.StateName = trns.langUtil.T("marketing_state_" + stt)
	}

	marketTime := make([]response.MarketTimeItem, 0)
	if len(mkt.Time1Start) > 0 {
		marketTime = append(marketTime, response.MarketTimeItem{
			TimeStart: mkt.Time1Start,
			TimeEnd:   mkt.Time1End,
		})
	}
	if len(mkt.Time2Start) > 0 {
		marketTime = append(marketTime, response.MarketTimeItem{
			TimeStart: mkt.Time2Start,
			TimeEnd:   mkt.Time2End,
		})
	}
	if len(mkt.Time3Start) > 0 {
		marketTime = append(marketTime, response.MarketTimeItem{
			TimeStart: mkt.Time3Start,
			TimeEnd:   mkt.Time3End,
		})
	}
	fmtMkt.MarketTime = marketTime

	if mkt.Day > 0 {
		days := tools.DecimalToBinary(mkt.Day)
		if len(days) < 7 {
			zeros := ""
			for i := 0; i < (7 - len(days)); i++ {
				zeros += "0"
			}
			days = zeros + days
		}
		fmtMkt.Day1, _ = strconv.Atoi(days[0:1])
		fmtMkt.Day2, _ = strconv.Atoi(days[1:2])
		fmtMkt.Day3, _ = strconv.Atoi(days[2:3])
		fmtMkt.Day4, _ = strconv.Atoi(days[3:4])
		fmtMkt.Day5, _ = strconv.Atoi(days[4:5])
		fmtMkt.Day6, _ = strconv.Atoi(days[5:6])
		fmtMkt.Day7, _ = strconv.Atoi(days[6:7])
	}

	fmtMkt.Steps = make([]response.MarketingStepItem, 0)
	if len(mkt.Steps) > 0 {
		steps := tools.StringToMapArr(mkt.Steps)

		for i := 0; i < len(steps); i++ {

			ps := float64(tools.ToInt(steps[i]["price_start"]))
			pr := float64(tools.ToInt(steps[i]["price_reduce"]))

			price_start, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", ps/100), 64)
			price_end, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", pr/100), 64)

			ti := ""
			if trns.language == "ug" {
				ti = " پەشتاق [" + strconv.Itoa(i+1) + "] " + tools.ToString(price_start) + " يۈەن گە توشسا " + tools.ToString(price_end) + " يۈەن ئېتىبار "
			} else {
				ti = " 阶梯 [" + strconv.Itoa(i+1) + "] 满" + tools.ToString(price_start) + " 减 " + tools.ToString(price_end) + ""
			}

			fmtMkt.Steps = append(fmtMkt.Steps, response.MarketingStepItem{
				PriceStart:  price_start,
				PriceReduce: price_end,
				StepTitle:   ti,
			})
		}
	}

	// api V2 逻辑
	foodsList := make([]response.MarketingFoodInfo, 0)
	foodIdMap := make(map[int]models.FoodItemJson, 0)
	foodIds := make([]int, 0)

	// 如果foods_json不为空，才能解析
	if mkt.FoodsJson != nil {
		for _, food := range mkt.FoodsJson {
			foodIds = append(foodIds, food.FoodId)
			foodIdMap[food.FoodId] = food
		}
	}

	if len(foodIds) > 0 {
		var foods []models.RestaurantFoods
		// TODO: 迁移到 Svc 里
		db := tools.GetDB()
		db.Model(foods).
			Preload("ComboFoodItems.RestaurantFood"). // 套餐子美食
			Preload("ComboFoodItems.SelectedSpec.FoodSpecOptions"). // 套餐子美食，以及子美食已选规格
			Preload("SelectedSpec.FoodSpecOptions"). // 已选规格
			Where("id in (?)", foodIds).
			Scan(&foods)
		for _, food := range foods {
			comboItems := make([]merchant.CommentFoodComboItems, 0)
			var selectedSpec *merchant.CommentRestaurantFoodSpec = nil

			if food.FoodType == models.RestaurantFoodsTypeCombo {
				if food.ComboFoodItems != nil && len(food.ComboFoodItems) > 0 {
					comboItems = GetFormattedComboItems(food.ComboFoodItems, trns.language)
				}
			} else if food.FoodType == models.RestaurantFoodsTypeSpec && foodIdMap[food.ID].SpecId > 0 {
				var foodSpec models.FoodSpec
				db.Model(&foodSpec).Where("id = ?", foodIdMap[food.ID].SpecId).Scan(&foodSpec)
				if foodSpec.ID > 0 {
					selectedSpec = GetFormattedSelectedSpec(&foodSpec, trns.language)
				}
			}

			foodsList = append(foodsList, response.MarketingFoodInfo{
				ID:           food.ID,
				Name:         tools.GetNameByLang(food, trns.language),
				Image:        tools.CdnUrl(food.Image),
				ComboItems:   comboItems,
				SelectedSpec: selectedSpec,
			})
		}
	}

	fmtMkt.FoodsListV2 = foodsList
	return fmtMkt
}
