package merchant

import (
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources/merchant"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type FoodsGroupTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func NewFoodsGroupTransformer(c *gin.Context) *FoodsGroupTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	transformer := FoodsGroupTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &transformer
}


// FoodsListByGroupIdFormat
//
// @Description: 格式化美食列表返回数据
// @Author: Rixat
// @Time: 2024-09-26 12:56:43
// @receiver 
// @param c *gin.Context
func (t FoodsGroupTransformer) FoodsListByGroupIdFormat(foods  []models.RestaurantFoodsHasPreferential) interface{}{
	res := make([]merchant.RestaurantFoods,0)
	for _,food := range foods {
		var foodInfo merchant.RestaurantFoods
		foodInfo.ID = food.ID
		foodInfo.FoodType = food.FoodType
		foodInfo.Name = tools.GetNameByLang(food,t.language)
		foodInfo.Image =  getFullUrl(food.Image, configs.MyApp.CdnUrl)
		foodInfo.BeginTime =  food.BeginTime
		foodInfo.EndTime =  food.EndTime
		foodInfo.ReadyTime =  food.ReadyTime
		foodInfo.IsDistribution =  food.IsDistribution
		foodInfo.StarAvg =  food.StarAvg
		foodInfo.CommentCount =  food.CommentCount
		foodInfo.MonthOrderCount =  food.MonthOrderCount
		foodInfo.Price =  tools.ToFloat64(food.Price)/100
		foodInfo.DiscountPrice =  tools.ToFloat64(food.FoodsPreferential.DiscountPrice)/100
		foodInfo.Weight = food.Weight
		foodInfo.State = food.State
		res = append(res, foodInfo)
	}
	return res
}

// ListFormat
//
// @Description: 分组列表格式化
// @Author: Rixat
// @Time: 2024-09-26 12:58:52
// @receiver 
// @param c *gin.Context
func (t FoodsGroupTransformer) ListFormat(foodsGroupList []models.FoodsGroup) []map[string]interface{} {
	language := t.language
	items := make([]map[string]interface{},0)
	for _, foodsGroup := range foodsGroupList {
		item := map[string]interface{}{
			"id":     foodsGroup.ID,
			"name":   tools.GetNameByLang(foodsGroup, language),
			"weight": foodsGroup.Weight,
			"state":  foodsGroup.State,
			"state_name":  t.langUtil.TArr("foods_group_state")[foodsGroup.State],
			"review_state":  foodsGroup.ReviewState,
			"review_state_name":  t.langUtil.TArr("foods_group_review_state")[foodsGroup.ReviewState],
			"refuse_reason":  foodsGroup.RefuseReason,
			"foods_count": len(foodsGroup.RestaurantFoods),
		}
		items = append(items, item)
	}
	return items
}


func (t FoodsGroupTransformer) ListRecommendFormat(foodsGroupList []models.Dictionary) []map[string]interface{} {
	// language := t.language
	items := make([]map[string]interface{},0)
	for _, foodsGroup := range foodsGroupList {
		item := map[string]interface{}{
			"name_ug":   foodsGroup.NameUg,
			"name_zh":   foodsGroup.NameZh,
		}
		items = append(items, item)
	}
	return items
}

// DetailFormat
//
// @Description: 分组详情格式化
// @Author: Rixat
// @Time: 2024-09-26 12:59:10
// @receiver 
// @param c *gin.Context
func (t FoodsGroupTransformer) DetailFormat(foodsGroup models.FoodsGroup) map[string]interface{} {
	language := t.language
	return map[string]interface{}{
		"id":            foodsGroup.ID,
		"name":      tools.GetNameByLang(foodsGroup, language),
		"name_ug":      foodsGroup.NameUg,
		"name_zh":          foodsGroup.NameZh,
		"weight":        foodsGroup.Weight,
		"state":         foodsGroup.State,
	}
}


