package merchant

import (
	"mulazim-api/models"
	"mulazim-api/resources/merchant"
	"mulazim-api/tools"
)

func GetFormattedComboItems(comboFoodItems []models.FoodsComboItem, lang string) []merchant.CommentFoodComboItems {
	comboItems := make([]merchant.CommentFoodComboItems, 0)
	for _, comboItem := range comboFoodItems {
		// 处理子美食的规格选项
		var itemSelectedSpec *merchant.CommentRestaurantFoodSpec = nil
		if comboItem.FoodType == models.FoodsComboItemFoodTypeSpec && comboItem.SelectedSpec != nil {
			specOptions := make([]merchant.OrderRestaurantFoodSpecOption, 0)
			for _, option := range comboItem.SelectedSpec.FoodSpecOptions {
				specOption := merchant.OrderRestaurantFoodSpecOption{
					ID:    option.ID,
					Name:  tools.If(lang == "zh", option.NameZh, option.NameUg),
					NameUg: option.NameUg,
					NameZh: option.NameZh,
					Price: option.Price,
				}
				specOptions = append(specOptions, specOption)
			}

			itemSelectedSpec = &merchant.CommentRestaurantFoodSpec{
				ID:          comboItem.SelectedSpec.ID,
				SpecOptions: specOptions,
			}
		}

		// 处理子美食信息
		restaurantFood := merchant.CommentRestaurantFoodsResponse{
			ID:               comboItem.RestaurantFood.ID,
			Name:             tools.GetNameByLang(comboItem.RestaurantFood, lang),
			NameUg: comboItem.RestaurantFood.NameUg,
			NameZh: comboItem.RestaurantFood.NameZh,
			Image:            tools.CdnUrl(comboItem.RestaurantFood.Image),
			Description:      tools.If(lang == "zh", comboItem.RestaurantFood.DescriptionZh, comboItem.RestaurantFood.DescriptionUg),
			StarAvg:          comboItem.RestaurantFood.StarAvg,
			Price:            comboItem.RestaurantFood.Price,
			FoodQuantity:     comboItem.RestaurantFood.FoodQuantity,
			FoodQuantityType: comboItem.RestaurantFood.FoodQuantityType,
			SelectedSpec:     itemSelectedSpec,
		}

		// 补丁 - 从 Spec 获取价格给到美食的价格
		if comboItem.FoodType == models.FoodsComboItemFoodTypeSpec {
			restaurantFood.Price = uint(comboItem.SelectedSpec.Price)
		}

		_comboItem := merchant.CommentFoodComboItems{
			ID:             comboItem.ID,
			FoodType:       comboItem.FoodType,
			FoodID:         comboItem.FoodID,
			Count:          comboItem.Count,
			RestaurantFood: restaurantFood,
		}
		comboItems = append(comboItems, _comboItem)
	}
	return comboItems
}

func GetFormattedSelectedSpec(selectedSpec *models.FoodSpec, lang string) *merchant.CommentRestaurantFoodSpec {
	var formattedSelectedSpec *merchant.CommentRestaurantFoodSpec = nil
	if selectedSpec == nil {
		return formattedSelectedSpec
	}

	specOptions := make([]merchant.OrderRestaurantFoodSpecOption, 0)
	for _, option := range selectedSpec.FoodSpecOptions {
		specOption := merchant.OrderRestaurantFoodSpecOption{
			ID:    option.ID,
			Name:  tools.If(lang == "zh", option.NameZh, option.NameUg),
			NameUg: option.NameUg,
			NameZh: option.NameZh,
			Price: option.Price,
		}
		specOptions = append(specOptions, specOption)
	}
	formattedSelectedSpec = &merchant.CommentRestaurantFoodSpec{
		ID:          selectedSpec.ID,
		SpecOptions: specOptions,
	}
	return formattedSelectedSpec
}

func GetFormattedFoodSpecCombo(seckill models.Seckill, lang string,
	) ([]merchant.CommentFoodComboItems, *merchant.CommentRestaurantFoodSpec) {
	// 返回结果格式化
	comboItems := make([]merchant.CommentFoodComboItems, 0)
	var selectedSpec *merchant.CommentRestaurantFoodSpec = nil

	if seckill.FoodType == models.RestaurantFoodsTypeCombo && seckill.RestaurantFoods.ComboFoodItems != nil &&
		len(seckill.RestaurantFoods.ComboFoodItems) > 0 {
		comboItems = GetFormattedComboItems(seckill.RestaurantFoods.ComboFoodItems, lang)
	} else if seckill.FoodType == models.RestaurantFoodsTypeSpec && seckill.SelectedSpec != nil {
		selectedSpec = GetFormattedSelectedSpec(seckill.SelectedSpec, lang)
	}

	return comboItems, selectedSpec
}
