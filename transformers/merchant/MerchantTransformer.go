package merchant

import (
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	other "mulazim-api/models/other"
	"mulazim-api/resources/merchant"
	merchantResources "mulazim-api/resources/merchant"
	"mulazim-api/tools"
	"mulazim-api/transformers"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type MerchantTransformer struct {
	langUtil *lang.LangUtil
	language string
}

// NewMerchantTransformer
//
//	@Description: 初始化商家端Transformer
//	@return *MerchantTransformer
func NewMerchantTransformer(c *gin.Context) *MerchantTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	merchantTransformer := MerchantTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &merchantTransformer
}

// FormatNewOrder
//
//	@Description: 新订单返回数据格式化，适配旧版本Android客户端
//	@receiver t
//	@param orderToday
//	@param receiveOrderTimeLimit
//	@return resources.NewOrderEntity
func (t *MerchantTransformer) FormatNewOrder(ctx *gin.Context, orderToday []models.OrderToday, receiveOrderTimeLimit int, showCustomerInfo bool) merchant.NewOrderEntity {
	var newOrderEntity = merchant.NewOrderEntity{
		ServerTime: int(time.Now().Unix()),
	}
	orderCancelTimeLeft := 2
	newOrderEntity.NewOrderList = []merchant.NewOrderList{}
	groupPrefix := "group_"
	adminType := "_restaurant_count"
	for i := 0; i < len(orderToday); i++ {
		order := orderToday[i]
		key := groupPrefix + tools.ToString(order.ID) + adminType
		price := float32((tools.ToFloat32(order.Price) + tools.ToFloat32(order.LunchBoxFee)) / 100)
		if order.OrderPriceRes > 0 {
			price =tools.ToFloat32(order.OrderPriceRes) / 100
		}
		rtnOrder := merchant.NewOrderList{
			ID:           order.ID,
			SerialNumber: int(order.SerialNumber),
			Name:         order.Name,
			Price:        price,
			OrderNo:      order.OrderID,

			OrderTime:        order.OrderTime,
			Timezone:         order.Timezone,
			ConsumeType:      order.ConsumeType,
			Shipment:         tools.ToFloat64(order.Shipment) / 100,
			OriginalShipment: tools.ToFloat64(order.OriginalShipment) / 100,
			ResIncome:        tools.ToFloat64(order.ResIncome) / 100,
			OriginalPrice:    tools.ToFloat64(order.OriginalPrice) / 100,
			//BookingTime:     carbon.Parse(carbon.Now("Asia/Shanghai").ToDateString()+" "+order.BookingTime).Format("H:i"),
			//BookingDateTime: carbon.Parse(carbon.Now("Asia/Shanghai").ToDateString()+" "+order.BookingTime).ToDateTimeString(),//1234
			CityName:      order.AddressView.CityName,
			AreaName:      order.AddressView.AreaName,
			StreetName:    order.AddressView.StreetName,
			BuildingName:  order.AddressView.BuildingName,
			OrderAddress:  order.OrderAddress,
			OrderNote:     order.Description,
			SendNotify:    order.SendNotify,
			OrderState:    order.State,
			OrderStateDes: t.getOrderStateDesc(order.State, order.RefundChanel),
			OrderType:     order.OrderType,
			//Now: order.Now ,
			//OrderTimeoutTime: order.OrderTimeoutTime ,
			//OrderTimeoutTimestamp: order.OrderTimeoutTimestamp ,
			//OrderTimeLeft: order.OrderTimeLeft ,
			NewOrderDetail: t.formatOrderDetail(ctx, order),
			FoodsReadyTime:         order.GetFoodsReadyTime(),
			FoodsReadyRemainMinute: order.FoodsReadyRemainMinute(),
			MsgCount:               tools.GetGroupChatMsgCount(key),
			DisplayShipment:        order.Restaurant.DisplayShipment,
			DisplayMerchantProfit:        order.Restaurant.DisplayMerchantProfit,
		}
		if order.Restaurant.DisplayShipment == 2 {
			rtnOrder.Shipment =  tools.ToFloat64(0) 
		}
		rtnOrder.BookingTime = carbon.Parse(order.BookingTime).Format("H:i")
		rtnOrder.BookingDateTime = carbon.Parse(order.BookingTime).Format("Y-m-d H:i:s")
		rtnOrder.Timezone = 8

		//计算可以接受订单时间
		printTime := carbon.Parse(order.PrintTime)
		diffInSeconds := carbon.Now().DiffInSeconds(printTime) // 计算时间差（秒）
		rtnOrder.CanBeReceivedIn = diffInSeconds

		rtnOrder.PrintTime = printTime.Format("Y-m-d H:i:s")

		rtnOrder.ShowCustomerInfo = showCustomerInfo

		if order.Shipper.ID != 0 {
			rtnOrder.ShipperMobile = order.Shipper.Mobile
			rtnOrder.ShipperName = order.Shipper.RealName
		}

		if showCustomerInfo {
			rtnOrder.Mobile = order.Mobile
			rtnOrder.RealMobile = order.Mobile
			rtnOrder.OrderAddress = order.OrderAddress
		} else {
			rtnOrder.Mobile = "***********"
			rtnOrder.RealMobile = "***********"
			rtnOrder.OrderAddress = "***********"
			rtnOrder.BuildingName = "***********"
			rtnOrder.StreetName = "***********"
			rtnOrder.Name = "***********"
		}

		if order.OrderType == 1{
			orderPayTime := carbon.Time2Carbon(order.PayTime)

			rtnOrder.Now = int(carbon.Now("Asia/Shanghai").Timestamp())
			rtnOrder.OrderTimeoutTime = carbon.Time2Carbon(order.PayTime).AddMinutes(receiveOrderTimeLimit + orderCancelTimeLeft).ToTimeString()
			rtnOrder.OrderTimeoutTimestamp = int(orderPayTime.AddMinutes(receiveOrderTimeLimit + orderCancelTimeLeft).Timestamp())

			rtnOrder.OrderTimeLeft = int(carbon.Now("Asia/Shanghai").DiffInSeconds(orderPayTime.AddMinutes(receiveOrderTimeLimit + orderCancelTimeLeft)))
		}

		//商家端新订单,已接订单  显示 满减/减配送费 等活动标志和金额
		rtnOrder.IsMarketing = tools.If(len(order.MarketingList) > 0, 1, 0)
		rtnOrder.MarketingList = []merchant.MarketingList{}
		rtnOrder.DeliveryType = order.DeliveryType
		rtnOrder.IsCoupon = tools.If(order.Coupon.ID > 0, 1, 0)

		if order.DeliveryType == 2 && len(order.Mobile) > 0 {
			rtnOrder.ShowCustomerInfo = true
			rtnOrder.Mobile = order.Mobile
			rtnOrder.RealMobile = order.Mobile
			rtnOrder.OrderAddress = order.OrderAddress
		}

		for _, info := range order.MarketingList {
			image := ""
			switch info.Type {
			case 1: //满减
				image = strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/order_detail_price_discount_" + t.language + ".png"
			case 2: //减配送费
				image = strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/order_detail_shipment_discount_icon_" + t.language + ".png"
			}
			marketInfo := merchant.MarketingList{
				MarketingImage:        image,
				MarketingName:         info.Name,
				MarketingReductionFee: float32(tools.ToFloat32(info.StepReduce) / 100),
			}
			rtnOrder.MarketingList = append(rtnOrder.MarketingList, marketInfo)
		}

		if order.Coupon.ID > 0 {
			rtnOrder.IsMarketing = 1
			marketInfo := merchant.MarketingList{
				MarketingName:         t.langUtil.T("coupon"),
				MarketingReductionFee: float32(tools.ToFloat32(order.Coupon.Price) / 100),
				MarketingImage: strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/merchant_home_coupon_"+t.language+".png",
			}
			rtnOrder.MarketingList = append(rtnOrder.MarketingList, marketInfo)
		}
		// 处理部分退款信息
		rtnOrder.OrderPartRefundList = t.processOrderPartRefund(ctx, order, "NewOrder")


		// if order.MiniGameUserLog.ID > 0 {	
		// 	rtnOrder.IsMarketing = 1
		// 	marketInfo := merchant.MarketingList{
		// 		MarketingName:         t.langUtil.T("snow_game"),
		// 		MarketingReductionFee: float32(tools.ToFloat32((-1)*order.MiniGameUserLog.Amount) / 100),
		// 		MarketingImage: strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/wechat_mini/img/snow/snow_merchant.png",
		// 	}
		// 	rtnOrder.MarketingList = append(rtnOrder.MarketingList, marketInfo)

		// }

		newOrderEntity.NewOrderList = append(newOrderEntity.NewOrderList, rtnOrder)
	}
	return newOrderEntity
}

// formatOrderDetail
// @Description: 格式化订单美食数据
// @receiver t
// @param order
// @return []resources.NewOrderDetail
func (trns *MerchantTransformer) formatOrderDetail(ctx *gin.Context, order models.OrderToday) []merchant.NewOrderDetail {
	// Note:
	//  - 原因：商家端在 2.6.9 或以上的版本，对该接口的价格数据做了除以100的操作。
	//  - 决定：以下判断是为了适配以上的情况，使商家端正常展示价格数据。
	isVersionGTE269 := tools.IsVersionGTE(ctx, "2.6.9")

	var (
		newOrderDetail []merchant.NewOrderDetail
		foodSpecTrns   = transformers.NewFoodSpecTransformer(ctx)
	)
	//newOrderEntity.NewOrderList = new(resources.NewOrderList)
	detailMap := make(map[string]merchant.NewOrderDetail)
	// map[food_id + spec_id]

	for i := 0; i < len(order.OrderDetail); i++ {
		detail := order.OrderDetail[i]
		if detail.Number == 0 {
			continue
		}
		food := detail.RestaurantFoods

		price :=(float32(detail.Price)) / 100.0
		if detail.PriceMarkupId > 0 {//加价 成本价
			price = (float32(detail.PriceMarkupPrice)) / 100.0
		}
		if isVersionGTE269 {
			price *= 100
		}

		// 构造 map ID
		mapId := fmt.Sprintf("%d", detail.StoreFoodsID)
		if detail.SelectedSpec != nil && detail.SelectedSpec.ID > 0 {
			mapId = fmt.Sprintf("%d_%d", detail.StoreFoodsID, detail.SelectedSpec.ID)
		}

		// 合并相同美食
		if existingDetail, exists := detailMap[mapId]; exists {
			existingDetail.Count += int(detail.Number)
			existingDetail.Price += price * float32(detail.Number)
			existingDetail.OriginalPrice += tools.If(isVersionGTE269, float32(detail.OriginalPrice*detail.Number), float32(detail.OriginalPrice*detail.Number)/100.0)
			detailMap[mapId] = existingDetail
		} else {
			detailMap[mapId] = merchant.NewOrderDetail{
				FoodID:        detail.StoreFoodsID,
				OriginalPrice: tools.If(isVersionGTE269, float32(detail.OriginalPrice), float32(detail.OriginalPrice)/100.0) * float32(detail.Number),
				Price:         price * float32(detail.Number),
				Count:         int(detail.Number),
				Image:         tools.AddCdn(detail.RestaurantFoods.Image),
				Name:          tools.If(trns.language == "zh", food.NameZh, food.NameUg),

				FoodType:       detail.FoodType,
				ComboFoodItems: foodSpecTrns.GetComboItems(food.ComboFoodItems),
				SelectedSpec:   foodSpecTrns.GetSelectedSpec(detail.SelectedSpec),
			}
		}
	}
	for _, detail := range detailMap {
		newOrderDetail = append(newOrderDetail, detail)
	}

	for i := 0; i < len(order.LunchBox); i++ {
		lunchBox := order.LunchBox[i]

		if lunchBox.Price > 0 {
			newOrderDetail = append(newOrderDetail, merchant.NewOrderDetail{
				FoodID:        0,
				OriginalPrice: tools.If(isVersionGTE269, float32(lunchBox.OriginalPrice), float32(lunchBox.OriginalPrice)/100.0) * float32(lunchBox.LunchBoxCount),
				Price:         tools.If(isVersionGTE269, float32(lunchBox.Price), float32(lunchBox.Price)/100.0) * float32(lunchBox.LunchBoxCount),
				Count: lunchBox.LunchBoxCount,
				Image:         tools.AddCdn("upload/restaurantfoods/202007/15/d49b559e40dc048ebb9817c93f4999b6.jpg"),
				Name:          lunchBox.Name,
			})
		}

	}
	return newOrderDetail
}

// getOrderStateDesc
// @Description: 根据订单状态获取状态名称
// @receiver t
// @param state
// @param refundChannel
// @return string
func (t *MerchantTransformer) getOrderStateDesc(state int, refundChannel int) string {
	if state == 8 || state == 9 {
		return t.getRefundChannel(refundChannel)
	} else {
		return t.getOrderState(state)
	}
}

// getOrderState
// @Description: 根据订单状态获取状态名称
// @receiver t
// @param state
// @return string
func (t *MerchantTransformer) getOrderState(state int) string {
	return t.langUtil.TArr("order_state")[state]
}

// getRefundChannel
// @Description: 根据退款类型编号，获取退款类型名称
// @receiver t
// @param refundChannel
// @return string
func (t *MerchantTransformer) getRefundChannel(refundChannel int) string {
	return t.langUtil.TArr("order_refund_channel")[refundChannel]
}

// FormatRestaurantPersonal
//
//	@Description: 给客户端返回餐厅信息
//	@receiver t
//	@param result
//	@param count
//	@return map[string]interface{}
func (t *MerchantTransformer) FormatRestaurantPersonal(result models.Restaurant, count int64) map[string]interface{} {
	data := map[string]interface{}{
		"restaurant_id":     result.ID,
		"restaurant_name":   result.Name,
		"restaurant_logo":   tools.If(strings.HasPrefix(result.Logo, "http"), result.Logo, configs.MyApp.CdnUrl+result.Logo),
		"new_comment_count": count,
		"state":             result.State,
		"cash_platform":     result.CashPlatform,
	}
	return data
}

// Login
//
//	@Description: 登录数据格式化
//	@author: Alimjan
//	@Time: 2022-09-24 17:42:02
//	@receiver t *MerchantTransformer
//	@param token map[string]interface{}
//	@param admin models.Admin
//	@param resInfo map[string]interface{}
//	@param dealerInfo map[string]interface{}
//	@param areaInfo map[string]interface{}
//	@return merchant.MerchantLoginEntity
func (t *MerchantTransformer) Login(token string, admin models.Admin, resInfo map[string]interface{}, dealerInfo map[string]interface{}, areaInfo map[string]interface{}) merchant.MerchantLoginEntity {
	ruleNamesUg := t.langUtil.TArrUg("merchant_type_name")
	ruleNamesZh := t.langUtil.TArrZh("merchant_type_name")
	ruleTypeNameUg := ""
	ruleTypeNameZh := ""
	for key, name := range ruleNamesUg {
		if key == admin.MerchantType{
			ruleTypeNameUg = name
			break
		}
	}
	for key, name := range ruleNamesZh {
		if key == admin.MerchantType{
			ruleTypeNameZh = name
			break
		}
	}
	loginEntity := merchantResources.MerchantLoginEntity{
		Tokens: merchantResources.MerchantLoginTokens{
			AccessToken:  token,
			TokenType:    "Bearer",
			ExpiresIn:    31536000,
			RefreshToken: "",
		},
		Admin: merchantResources.MerchantLoginAdmin{
			ID:           admin.ID,
			Name:         admin.Name,
			Mobile:       admin.Mobile,
			Type:         tools.If(admin.Type == 5, "RESTAURANT_ADMIN", "RESTAURANT_ADMIN_SUB"),
			RestaurantID: tools.ToInt64(resInfo["id"]),
			//RestaurantName: resInfo["res_name"].(string),
			RestaurantLogo:        strings.TrimRight(configs.MyApp.CdnUrl, "/") + resInfo["logo"].(string),
			BusinessType:          tools.ToInt64(resInfo["type"]),
			RestaurantState:       tools.ToInt64(resInfo["state"]),
			RestaurantName:        resInfo["name"].(string),
			DealerName:            dealerInfo["real_name"].(string),
			DealerMobile:          dealerInfo["mobile"].(string),
			AreaID:                tools.ToInt64(areaInfo["id"]),
			ReceiveOrderTimeLimit: tools.ToInt64(areaInfo["receive_order_time"]),
			MerchantType: admin.MerchantType,
			MerchantTypeNameUg: ruleTypeNameUg,
			MerchantTypeNameZh: ruleTypeNameZh,

		},
		Permissions: t.GetPermissions(admin),
	}
	return loginEntity
}
func (t *MerchantTransformer) GetPermissions(admin models.Admin) []string{
	var permissions []string
	if admin.MerchantType == 1{
		tools.GetDB().Model(&models.MerchantPermissionCategory{}).Select("name").Where("pid<>0").Scan(&permissions)
		permissions = append(permissions,"manager")
	}else{
		tools.GetDB().Model(&models.MerchantPermission{}).Select("v1 as name").Where("v0=? and ptype = ?",tools.ToString(admin.ID),"g").Scan(&permissions)
		for _,v := range permissions{
			if v == "food-update"{
				permissions = append(permissions,"foods_add")
			}
		}
	}
	return permissions
}
// FormatNotCashedList
//
//	@Description: 序列化商户未体现账单列表
//	@author: Captain
//	@Time: 2022-11-07 12:24:17
//	@receiver t *MerchantTransformer
//	@param totalAmount int64 未体现总额
//	@param list []map[string]interface{} 未体现列表
//	@return merchant.NotCashedListEntity
func (t *MerchantTransformer) FormatNotCashedList(totalAmount int64, list []map[string]interface{}) merchant.NotCashedListEntity {
	var item merchantResources.NotCashedList
	var notCashedListEntity merchant.NotCashedListEntity
	diffDate := 3 //只能提现3天前的未体现金额
	notCashedListEntity.TotalAmount = totalAmount
	for i := 0; i < len(list); i++ {
		item.ID = tools.ToInt64(list[i]["id"])
		item.TransDate = carbon.Time2Carbon(list[i]["trans_date"].(time.Time)).Format("Y-m-d", "Asia/Shanghai")
		item.ResIncome = tools.ToInt64(list[i]["res_income"])
		leftDay := tools.Today("Asia/Shanghai").DiffInDays(carbon.Parse(item.TransDate, "Asia/Shanghai").AddDays(diffDate))
		item.LeftDay = leftDay
		if leftDay <= 0 {
			item.CanCashout = 1
		} else {
			item.CanCashout = 0
		}
		notCashedListEntity.List = append(notCashedListEntity.List, item)
	}
	return notCashedListEntity
}


// FormatRestaurantOrderList
//
//	@Time 2022-09-25 19:41:45
//	<AUTHOR>
//	@Description: 序列化商家订单列表
//	@receiver t *MerchantTransformer
//	@param list
func (t *MerchantTransformer) FormatRestaurantOrderList(ctx *gin.Context, list []models.OrderToday,
	receive_order_time_limit int64, showCustomerInfo bool, orderType string,
) merchant.NewOrderEntity {
	var order_cancel_time_left int64 = 2

	data := merchant.NewOrderEntity{}
	orderlists := []merchant.NewOrderList{}
	orderlist := merchant.NewOrderList{}
	groupPrefix := "group_"
	adminType := "_restaurant_count"
	for _, v := range list {
		key := groupPrefix + tools.ToString(v.ID) + adminType
		orderlist.OrderTime = v.OrderTime
		orderlist.ID = v.ID
		orderlist.SerialNumber = int(v.SerialNumber)
		orderlist.Name = v.Name

		price := float32((tools.ToFloat32(v.Price) + tools.ToFloat32(v.LunchBoxFee)) / 100)
		if v.OrderPriceRes > 0 {
			price = tools.ToFloat32(v.OrderPriceRes) / 100
		}
		orderlist.Price = price
		
		orderlist.OrderNo = v.OrderID

		orderlist.OrderTime = v.OrderTime
		orderlist.Timezone = v.Timezone
		orderlist.ConsumeType = v.ConsumeType
		orderlist.DisplayShipment = v.Restaurant.DisplayShipment
		orderlist.DisplayMerchantProfit = v.Restaurant.DisplayMerchantProfit
		if v.Restaurant.DisplayShipment == 1 {
			orderlist.Shipment = tools.ToFloat64(v.Shipment) / 100
		}else{
			orderlist.Shipment = tools.ToFloat64(0)
		}
		orderlist.OriginalShipment = tools.ToFloat64(v.OriginalShipment) / 100
		orderlist.ResIncome = tools.ToFloat64(v.ResIncome) / 100
		orderlist.OriginalPrice = tools.ToFloat64(v.OriginalPrice) / 100

		if v.Shipper.ID != 0 {
			orderlist.ShipperMobile = v.Shipper.Mobile
			orderlist.ShipperName = v.Shipper.RealName
		}
		orderlist.BookingTime = carbon.Parse(v.BookingTime).Format("H:i")
		orderlist.BookingDateTime = carbon.Parse(v.BookingTime).Format("Y-m-d H:i:00")
		//fmt.Println()
		if t.language == "ug" {
			orderlist.CityName = v.AddressView.CityNameUg
			orderlist.AreaName = v.AddressView.AreaNameUg
			orderlist.StreetName = v.AddressView.StreetNameUg
			orderlist.BuildingName = v.AddressView.BuildingNameUg
		} else {
			orderlist.CityName = v.AddressView.CityNameZh
			orderlist.AreaName = v.AddressView.AreaNameZh
			orderlist.StreetName = v.AddressView.StreetNameZh
			orderlist.BuildingName = v.AddressView.BuildingNameZh
		}

		orderlist.OrderNote = v.Description
		orderlist.SendNotify = v.SendNotify
		if v.RefundChanel == 0 {
			orderlist.OrderStateDes = t.getOrderState(v.State)
		} else {
			orderlist.OrderStateDes = t.getOrderStateDesc(v.State, v.RefundChanel)
		}
		orderlist.OrderState = v.State
		orderlist.OrderType = v.OrderType
		if v.OrderType == 1 {

			orderPayTime := carbon.Time2Carbon(v.PayTime)

			orderlist.Now = int(carbon.Now("Asia/Shanghai").Timestamp())
			orderlist.OrderTimeoutTime = carbon.Time2Carbon(v.PayTime).AddMinutes(int(receive_order_time_limit + order_cancel_time_left)).ToTimeString()
			orderlist.OrderTimeoutTimestamp = int(orderPayTime.AddMinutes(int(receive_order_time_limit + order_cancel_time_left)).Timestamp())
			orderlist.OrderTimeLeft = int(carbon.Now("Asia/Shanghai").DiffInSeconds(orderPayTime.AddMinutes(int(receive_order_time_limit + order_cancel_time_left))))
		}
		//商家端新订单,已接订单  显示 满减/减配送费 等活动标志和金额
		orderlist.IsMarketing = tools.If(len(v.MarketingList) > 0, 1, 0)
		orderlist.MarketingList = []merchant.MarketingList{}
		orderlist.IsCoupon = tools.If(v.Coupon.ID > 0, 1, 0)
		for _, info := range v.MarketingList {
			image := ""
			switch info.Type {
			case 1: //满减
				image = strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/order_detail_price_discount_" + t.language + ".png"
			case 2: //减配送费
				image = strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/order_detail_shipment_discount_icon_" + t.language + ".png"
			}
			marketInfo := merchant.MarketingList{
				MarketingName:         info.Name,
				MarketingReductionFee: float32(tools.ToFloat32(info.StepReduce) / 100),
				MarketingImage:        image,
			}
			orderlist.MarketingList = append(orderlist.MarketingList, marketInfo)
		}
		if v.Coupon.ID > 0 {
			orderlist.IsMarketing = 1
			marketInfo := merchant.MarketingList{
				MarketingName:         t.langUtil.T("coupon"),
				MarketingReductionFee: float32(tools.ToFloat32(v.Coupon.Price) / 100),
				MarketingImage: strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/merchant_home_coupon_"+t.language+".png",
			}
			orderlist.MarketingList = append(orderlist.MarketingList, marketInfo)
		}

		// if v.MiniGameUserLog.ID > 0 {	
		// 	orderlist.IsMarketing = 1
		// 	marketInfo := merchant.MarketingList{
		// 		MarketingName:         t.langUtil.T("snow_game"),
		// 		MarketingReductionFee: float32(tools.ToFloat32((-1)*v.MiniGameUserLog.Amount) / 100),
		// 		MarketingImage: strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/wechat_mini/img/snow/snow_merchant.png",
		// 	}
		// 	orderlist.MarketingList = append(orderlist.MarketingList, marketInfo)

		// }

		orderlist.ShowCustomerInfo = showCustomerInfo
		if showCustomerInfo {
			orderlist.Mobile = v.Mobile
			orderlist.RealMobile = v.Mobile
			orderlist.OrderAddress = v.OrderAddress
		} else {
			orderlist.Mobile = "***********"
			orderlist.RealMobile = "***********"
			orderlist.OrderAddress = "***********"
			orderlist.BuildingName = "***********"
			orderlist.StreetName = "***********"
			orderlist.Name = "***********"
		}
		if v.DeliveryType == 2 && len(v.Mobile) > 0 {
			orderlist.ShowCustomerInfo = true
			orderlist.Mobile = v.Mobile
			orderlist.Mobile = v.Mobile
			orderlist.RealMobile = v.Mobile
			orderlist.OrderAddress = v.OrderAddress
		}
		orderlist.MsgCount = tools.GetGroupChatMsgCount(key)
		orderlist.DeliveryType = v.DeliveryType
		orderlist.NewOrderDetail = t.formatOrderDetail(ctx, v)
		orderlist.FoodsReadyTime = v.GetFoodsReadyTime()
		orderlist.FoodsReadyRemainMinute = v.FoodsReadyRemainMinute()

		// 处理部分退款信息
		orderlist.OrderPartRefundList = t.processOrderPartRefund(ctx, v, orderType)
		orderlists = append(orderlists, orderlist)

	}
	data.NewOrderList = orderlists
	data.ServerTime = int(time.Now().Unix())
	return data
}

// FormatRestaurantStatistics
//
//	@Time 2022-09-30 17:29:18
//	<AUTHOR>
//	@Description: 格式化餐厅业营业统计报表
//	@receiver t *MerchantTransformer
//	@param statistics
func (t *MerchantTransformer) FormatRestaurantStatistics(statistics merchant.BusinessStatistics) map[string]interface{} {

	for i := range statistics.ByPayType {
		statistics.ByPayType[i].Icon = strings.TrimRight(configs.MyApp.CdnUrl, "/") + statistics.ByPayType[i].Icon
	}

	data := make(map[string]interface{})
	if statistics.ByPayType == nil {
		data["by_pay_type"] = []int{}
	} else {
		data["by_pay_type"] = statistics.ByPayType
	}
	if statistics.FoodSales == nil {
		data["food_sales"] = []int{}
	} else {
		data["food_sales"] = statistics.FoodSales
	}
	//if statistics.Total[0].TotalPrice==""{
	//	statistics.Total[0].TotalPrice=nil
	//}
	data["total"] = statistics.Total[0]
	return data
}


// FormatRestaurantInformation
//
//	@Time 2022-12-16 13:08:15
//	<AUTHOR>
//	@Description: 格式化商家信息
//	@receiver t *MerchantTransformer
//	@param result
//	@return interface{}
func (t *MerchantTransformer) FormatRestaurantInformation(list map[string]interface{}) interface{} {
	data := make(map[string]interface{})
	data["id"] = list["id"]

	data["description_ug"] = list["description_ug"]
	data["description_zh"] = list["description_zh"]
	data["address_ug"] = list["address_ug"]
	data["address_zh"] = list["address_zh"]
	data["name_zh"] = list["name_zh"]
	data["name_ug"] = list["name_ug"]

	restaurant_logo := tools.ToString(list["restaurant_logo"]) //防止出现 interface {} is nil, not string 异常
	data["restaurant_logo"] = configs.MyApp.CdnUrl + restaurant_logo
	data["tag"] = list["tag"]

	data["tel"] = list["tel"]
	data["tel2"] = list["tel2"]
	data["tel3"] = list["tel3"]
	data["tel4"] = list["tel4"]
	data["tel5"] = list["tel5"]

	data["street_id"] = list["street_id"]
	data["can_self_take"] = list["can_self_take"]
	data["restaurant_type"] = list["restaurant_type"]

	data["open_time"] = carbon.ParseByFormat(tools.ToString(list["open_time"]), "H:i:s").Format("H:i")
	data["close_time"] = carbon.ParseByFormat(tools.ToString(list["close_time"]), "H:i:s").Format("H:i")
	data["begin_time"] = carbon.ParseByFormat(tools.ToString(list["begin_time"]), "H:i:s").Format("H:i")
	data["end_time"] = carbon.ParseByFormat(tools.ToString(list["end_time"]), "H:i:s").Format("H:i")
	//美食最早出餐时间和最晚结束出餐时间
	data["food_begin_time"] = list["food_begin_time"]
	data["food_end_time"] = list["food_end_time"]

	// 图片审核内容
	image_verify := make([]map[string]interface{}, 0)
	if list["image_verify"] != nil {
		image_verify = list["image_verify"].([]map[string]interface{})
	}

	data["restaurant_logo_state"] = 2
	data["restaurant_license_state"] = 2
	data["restaurant_permit_state"] = 2
	data["restaurant_images_state"] = 2
	verify_license_image := ""
	verify_permet_image := ""
	restaurantImagesList := make([]map[string]interface{}, 0)
	for _, image_state := range image_verify {
		if tools.ToInt(image_state["type"]) == tools.ToInt(1) { // 餐厅logo
			data["restaurant_logo_state"] = image_state["state"]
			if tools.ToInt(image_state["state"]) == tools.ToInt(1) {
				data["restaurant_logo"] = configs.MyApp.CdnUrl + image_state["new_value"].(string)
			}
		} else if tools.ToInt(image_state["type"]) == tools.ToInt(2) { // 营业执照
			data["restaurant_license_state"] = image_state["state"]
			if tools.ToInt(image_state["state"]) == tools.ToInt(1) {
				verify_license_image = configs.MyApp.CdnUrl + image_state["new_value"].(string)
			}
		} else if tools.ToInt(image_state["type"]) == tools.ToInt(3) {
			data["restaurant_permit_state"] = image_state["state"]
			if tools.ToInt(image_state["state"]) == tools.ToInt(1) {
				verify_permet_image = configs.MyApp.CdnUrl + image_state["new_value"].(string)
			}
		} else if tools.ToInt(image_state["type"]) == tools.ToInt(4) { // 餐厅照片
			data["restaurant_images_state"] = image_state["state"]
			if tools.ToInt(image_state["state"]) == tools.ToInt(1) {
				new_images := image_state["new_value"].(string)
				newImageArr := strings.Split(new_images, ",")
				for _, image := range newImageArr {
					restaurantImagesListItem := make(map[string]interface{})
					restaurantImagesListItem["id"] = 0
					restaurantImagesListItem["restaurant_id"] = list["id"]
					restaurantImagesListItem["image_url"] = configs.MyApp.CdnUrl + image
					restaurantImagesListItem["state"] = 1
					restaurantImagesList = append(restaurantImagesList, restaurantImagesListItem)
				}
			}
		}
	}
	restaurantImages := make([]map[string]interface{}, 0)
	if list["restaurant_images"] != nil {
		restaurantImages = list["restaurant_images"].([]map[string]interface{})
	}
	if len(restaurantImagesList) == 0 {
		for i := 0; i < len(restaurantImages); i++ {
			restaurantImagesListItem := make(map[string]interface{})
			restaurantImagesListItem["id"] = restaurantImages[i]["id"]
			restaurantImagesListItem["restaurant_id"] = restaurantImages[i]["restaurant_id"]
			restaurantImagesListItem["image_url"] = configs.MyApp.CdnUrl + restaurantImages[i]["image_url"].(string)
			restaurantImagesListItem["state"] = restaurantImages[i]["state"]
			restaurantImagesList = append(restaurantImagesList, restaurantImagesListItem)
		}
	}

	restaurantLicense := make([]map[string]interface{}, 0)
	if list["restaurant_license"] != nil {
		restaurantLicense = list["restaurant_license"].([]map[string]interface{})
	}

	restaurantLicenseList := make([]map[string]interface{}, 0)
	for i := 0; i < len(restaurantLicense); i++ {
		restaurantLicenseListItem := make(map[string]interface{})
		restaurantLicenseListItem["id"] = restaurantLicense[i]["id"]
		restaurantLicenseListItem["restaurant_id"] = restaurantLicense[i]["restaurant_id"]
		restaurantLicenseListItem["license_type_id"] = restaurantLicense[i]["license_type_id"]
		if restaurantLicense[i]["image_url"] != nil {
			restaurantLicenseListItem["image_url"] = configs.MyApp.CdnUrl + restaurantLicense[i]["image_url"].(string)
		}
		if tools.ToInt(restaurantLicense[i]["license_type_id"]) == tools.ToInt(1) && verify_permet_image != "" {
			restaurantLicenseListItem["image_url"] = verify_permet_image
		} else if tools.ToInt(restaurantLicense[i]["license_type_id"]) == tools.ToInt(2) && verify_license_image != "" {
			restaurantLicenseListItem["image_url"] = verify_license_image
		}
		restaurantLicenseListItem["state"] = restaurantLicense[i]["state"]
		restaurantLicenseList = append(restaurantLicenseList, restaurantLicenseListItem)
	}

	restaurantCategory := make([]map[string]interface{}, 0)
	if list["restaurant_category"] != nil {
		restaurantCategory = list["restaurant_category"].([]map[string]interface{})
	}
	restaurantCategoryList := make([]map[string]interface{}, len(restaurantCategory))
	for i := 0; i < len(restaurantCategory); i++ {
		restaurantCategoryList[i] = make(map[string]interface{})
		restaurantCategoryList[i]["name"] = tools.If(t.language == "ug", restaurantCategory[i]["name_ug"], restaurantCategory[i]["name_zh"])
		restaurantCategoryList[i]["icon"] = configs.MyApp.CdnUrl + tools.ToString(restaurantCategory[i]["icon"])
		restaurantCategoryList[i]["restaurant_id"] = restaurantCategory[i]["t_restaurant_id"]
		restaurantCategoryList[i]["category_id"] = restaurantCategory[i]["id"]
	}

	category := make([]map[string]interface{}, 0)
	if list["category"] != nil {
		category = list["category"].([]map[string]interface{})
	}
	categoryList := make([]map[string]interface{}, len(category))

	for i := 0; i < len(category); i++ {
		categoryList[i] = make(map[string]interface{})
		categoryList[i]["id"] = category[i]["id"]
		categoryList[i]["parent_id"] = category[i]["parent_id"]
		categoryList[i]["name"] = category[i]["name"]
		categoryList[i]["icon"] = configs.MyApp.CdnUrl + category[i]["icon"].(string)
		categoryList[i]["rgb"] = category[i]["rgb"]
		categoryList[i]["state"] = category[i]["state"]
	}

	data["restaurant_images"] = restaurantImagesList
	data["restaurant_license"] = restaurantLicenseList
	data["restaurant_category"] = restaurantCategoryList
	data["category"] = categoryList

	return data

}

// FormatFinanceInfo
//
//	@Time 2022-12-24 19:22:35
//	<AUTHOR>
//	@Description: 格式化财务信息
//	@receiver t *MerchantTransformer
//	@param result
//	@param cards
//	@return interface{}
func (t *MerchantTransformer) FormatFinanceInfo(list []map[string]interface{}, cards []map[string]interface{}) interface{} {
	data := make(map[string]interface{})
	if len(list) > 0 {
		data["id"] = list[0]["id"]
		data["restaurant_id"] = list[0]["restaurant_id"]
		data["account_name"] = list[0]["account_name"]
		data["card_number"] = list[0]["card_number"]
		data["account_address"] = list[0]["account_address"]
		data["state"] = list[0]["state"]
		if len(cards) > 0 {
			data["bank_id"] = cards[0]["id"]
			data["bank_name"] = cards[0]["name"]
			data["bank_logo"] = cards[0]["logo"]

		}
		return data
	}
	return data
}

// FormatCashedList
//
//	@Description: 格式化已经提现列表
//	@author: Alimjan
//	@Time: 2023-01-16 13:30:35
//	@receiver t *MerchantTransformer
//	@param rs []models.MerchantCashoutLog
//	@return interface{}
func (t *MerchantTransformer) FormatCashedList(rtnLog []models.MerchantCashoutLog) interface{} {
	rtnMap := make([]map[string]interface{}, 0)
	for _, item := range rtnLog {
		curItem := make(map[string]interface{}, 0)
		curItem["id"] = item.ID
		curItem["mobile"] = item.Mobile

		curItem["cashout_amount"] = tools.ToFloat64(item.CashoutAmount)
		curItem["cashout_time"] = item.CashoutTime.Format("2006-01-02 15:04:05")

		details := make([]map[string]interface{}, 0)

		for _, detail := range item.CashoutDetails {
			curItemDetail := make(map[string]interface{}, 0)
			curItemDetail["id"] = detail.ID
			curItemDetail["res_income"] = detail.ResIncome
			curItemDetail["trans_date"] = detail.TransDate.Format("2006-01-02")
			details = append(details, curItemDetail)
		}

		curItem["cashout_details"] = details
		rtnMap = append(rtnMap, curItem)
	}
	return rtnMap
}

func (t *MerchantTransformer) FormatCashedListLakala(rtnLog []models.MerchantCashoutLogLakala) interface{} {
	rtnMap := make([]map[string]interface{}, 0)
	for _, item := range rtnLog {
		curItem := make(map[string]interface{}, 0)
		curItem["id"] = item.ID
		curItem["mobile"] = item.Mobile

		curItem["cashout_amount"] = tools.ToFloat64(item.CashoutAmount)
		curItem["cashout_time"] = item.CashoutTime.Format("2006-01-02 15:04:05")

		curItem["cash_platform"] = item.CashPlatform

		curItem["state"] = item.State
		curItem["bank_name"] = item.BankName
		curItem["card_id"] = item.CardId
		curItem["card_no"] = item.CardNo
		curItem["bank_logo"] = item.BankLogo

		rtnMap = append(rtnMap, curItem)
	}
	return rtnMap
}

// FormatLangList
//
//	@Description: 格式化语言包
//	@author: Alimjan
//	@Time: 2023-02-09 12:13:35
//	@receiver t *MerchantTransformer
//	@param c *gin.Context
//	@param data []map[string]interface{}
//	@return interface{}
func (t *MerchantTransformer) FormatLangList(c *gin.Context, data []map[string]interface{}) interface{} {

	result := make(map[string]interface{}, 0)
	for _, datum := range data {
		result[datum["key"].(string)] = datum["value_"+t.language]
	}
	return result
}

// getFullUrl
//
//	@Time 2022-10-01 17:27:12
//	<AUTHOR>
//	@Description: 正则匹配Url
//	@param url
//	@param cdn
//	@return string
func getFullUrl(url string, cdn string) string {
	regexp1, _ := regexp.Compile("(http|ftp|https):\\/\\/.*")
	if regexp1.MatchString(url) {
		//匹配成功
		return url
	} else if url != "" && cdn != "" {
		//匹配不成功 加上cdn返回
		regexp2, _ := regexp.Compile("(http|ftp|https):\\/\\/.*")
		if regexp2.MatchString(url) {
			cdn = cdn
		}
		return strings.Trim(cdn, "/") + "/" + strings.Trim(url, "/")
	} else {
		return url
	}
}

/***
 * @Author: [rozimamat]
 * @description: 启动页和首页广告
 * @Date: 2023-05-06 13:56:52
 * @param {*gin.Context} c
 * @param {map[string]interface{}} data
 */
func (t *MerchantTransformer) FormatAdver(c *gin.Context, data map[string]interface{}) interface{} {

	if data != nil && data["image"] != nil {
		data["image"] = getFullUrl(tools.ToString(data["image"]), configs.MyApp.CdnUrl)
	}
	return data
}

/***
 * @Author: [rozimamat]
 * @description: 自取订单格式化
 * @Date: 2023-05-18 10:02:17
 * @param {models.OrderToday} order
 */
func (t *MerchantTransformer) FormatSelfTakeOrder(ctx *gin.Context, order models.OrderToday) interface{} {
	// Note:
	//  - 原因：商家端在 2.6.9 或以上的版本，对该接口的价格数据做了除以100的操作。
	//  - 决定：以下判断是为了适配以上的情况，使商家端正常展示价格数据。
	isVersionGTE269 := tools.IsVersionGTE(ctx, "2.6.9")

	price := float32((tools.ToFloat32(order.Price) + tools.ToFloat32(order.LunchBoxFee)) / 100)
	if isVersionGTE269 {
		price *= 100
	}
	rtnOrder := merchant.NewOrderList{
		ID:            order.ID,
		SerialNumber:  int(order.SerialNumber),
		Name:          order.Name,

		Price:         price,

		OrderNo:       order.OrderID,
		OriginalPrice: tools.If(isVersionGTE269, tools.ToFloat64(order.OriginalPrice), tools.ToFloat64(order.OriginalPrice)/100),
		OrderTime:     order.OrderTime,
		Timezone:      order.Timezone,
		ConsumeType:   order.ConsumeType,

		CityName:      order.AddressView.CityName,
		AreaName:      order.AddressView.AreaName,
		StreetName:    order.AddressView.StreetName,
		BuildingName:  order.AddressView.BuildingName,
		OrderAddress:  order.OrderAddress,
		OrderNote:     order.Description,
		SendNotify:    order.SendNotify,
		OrderState:    order.State,
		OrderStateDes: t.getOrderStateDesc(order.State, order.RefundChanel),
		OrderType:     order.OrderType,

		NewOrderDetail: t.formatOrderDetail(ctx, order),
		FoodsReadyTime:         order.GetFoodsReadyTime(),
		FoodsReadyRemainMinute: order.FoodsReadyRemainMinute(),
	}
	rtnOrder.BookingTime = carbon.Parse(order.BookingTime).Format("H:i")
	rtnOrder.BookingDateTime = carbon.Parse(order.BookingTime).Format("Y-m-d H:i:s")
	rtnOrder.Timezone = 8

	rtnOrder.ShowCustomerInfo = false

	if order.Shipper.ID != 0 {
		rtnOrder.ShipperMobile = order.Shipper.Mobile
	}

	//商家端新订单,已接订单  显示 满减/减配送费 等活动标志和金额
	rtnOrder.IsMarketing = tools.If(len(order.MarketingList) > 0, 1, 0)
	rtnOrder.MarketingList = []merchant.MarketingList{}
	rtnOrder.DeliveryType = order.DeliveryType
	rtnOrder.IsCoupon = tools.If(order.Coupon.ID > 0, 1, 0)
	for _, info := range order.MarketingList {
		image := ""
		switch info.Type {
		case 1: //满减
			image = strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/order_detail_price_discount_" + t.language + ".png"
		case 2: //减配送费
			image = strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/order_detail_shipment_discount_icon_" + t.language + ".png"
		}
		marketInfo := merchant.MarketingList{
			MarketingImage:        image,
			MarketingName:         info.Name,
			MarketingReductionFee: tools.If(isVersionGTE269, tools.ToFloat32(info.StepReduce), float32(tools.ToFloat32(info.StepReduce)/100)),
		}
		rtnOrder.MarketingList = append(rtnOrder.MarketingList, marketInfo)
	}
	if order.Coupon.ID > 0 {
		rtnOrder.IsMarketing = 1
		marketInfo := merchant.MarketingList{
			MarketingName:         t.langUtil.T("coupon"),
			MarketingReductionFee: tools.If(isVersionGTE269, tools.ToFloat32(order.Coupon.Price), float32(tools.ToFloat32(order.Coupon.Price)/100)),
			MarketingImage: strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/merchant_home_coupon_"+t.language+".png",
		}
		rtnOrder.MarketingList = append(rtnOrder.MarketingList, marketInfo)
	}

	// if order.MiniGameUserLog.ID > 0 {	
	// 	rtnOrder.IsMarketing = 1
	// 	marketInfo := merchant.MarketingList{
	// 		MarketingName:         t.langUtil.T("snow_game"),
	// 		MarketingReductionFee: float32(tools.ToFloat32((-1)*order.MiniGameUserLog.Amount) / 100),
	// 		MarketingImage: strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/wechat_mini/img/snow/snow_merchant.png",
	// 	}
	// 	rtnOrder.MarketingList = append(rtnOrder.MarketingList, marketInfo)

	// }


	return rtnOrder
}
//
// FormatTerminalShowAdver
//  @Description: 老版本升级App
//  @receiver t
//  @param terminal
//  @param adver
//  @return merchantResources.TerminalShowAdverEntity
//
func (t *MerchantTransformer) FormatTerminalShowAdver(terminal models.Terminal, adver map[string]interface{}) merchantResources.TerminalShowAdverEntity {

	rtn :=  merchantResources.TerminalShowAdverEntity{
	}
	//           "version_code": 322,
	//            "package_name": "app-merchant",
	//            "url": "https://acdn.mulazim.com/upload/apk/20240314/63a43039f7914d8845adb1b47e127daa/merchant-android.apk",
	//            "des": null,
	//            "force_update": 0
	rtn.Version = merchantResources.TerminalShowAdverEntityVersion{
		Id: terminal.ID,
		Name: terminal.Name,
		Type: terminal.OsType,
		Icon: tools.AddCdn(terminal.Icon),
		Version: terminal.Version,
		VersionCode: terminal.VersionCode,
		Des: terminal.DescriptionZh,
		ForceUpdate: terminal.ForceUpdate,
		Url: tools.AddCdn(terminal.Url),
		PackageName: terminal.PackageName,
	}
	if tools.ToInt(adver["id"])!=0 {
		rtn.Adver = &merchantResources.TerminalShowAdverEntityAdver{
			Id: tools.ToInt(adver["id"]),
			LinkType: tools.ToInt(adver["link_type"]),
			LinkUrl: tools.AddCdn(tools.ToString(adver["link_url"])),
			//AppId: tools.ToInt64(adver["app_id"]),
			TitleUg: tools.ToString(adver["title_ug"]),
			TitleZh: tools.ToString(adver["title_zh"]),
			ContentUg: tools.ToString(adver["content_ug"]),
			ContentZh: tools.ToString(adver["content_zh"]),
			ImageUg: tools.AddCdn(tools.ToString(adver["image_ug"])),
			ImageZh: tools.AddCdn(tools.ToString(adver["image_zh"])),
			ShowTime: tools.ToInt(adver["show_time"]),
		}
	}

	return rtn
}
//
// GetComment
//  @Description: 获取评论
//  @receiver t
//  @param comments
//  @param star
//  @param commentType
//  @param page
//  @param lastPage
//  @param limit
//  @return merchantResources.CommentListEntity
//
func (t *MerchantTransformer) GetComment(comments []other.CommentModel, star models.Restaurant, commentType []map[string]interface{}, page int, lastPage int, limit int) merchantResources.CommentListEntity {
	rtn := merchantResources.CommentListEntity{
		PerPage: limit,
		CurrentPage: page,
		LastPage: lastPage,
	}

	rtn.Star.StarAvg = int(star.StarAvg)
	rtn.Star.FoodStarAvg = int(star.FoodStarAvg)
	rtn.Star.BoxStarAvg = int(star.BoxStarAvg)
	rtn.Star.ShipperAvg = int(star.ShipperAvg)
	//commentType
	rtn.Type = make([]merchantResources.CommentListEntityType, 0)
	for _, v := range commentType {
		rtn.Type = append(rtn.Type, merchantResources.CommentListEntityType{
			Name: tools.ToString(v["name_"+t.language]),
			Count: tools.ToInt(v["count"]),
			Type: tools.ToInt(v["type"]),
		})
	}
	//comment
	rtn.Items = make([]merchantResources.CommentListEntityItems, 0)
	for _, v := range comments {
		item := merchantResources.CommentListEntityItems{
			Id:           v.ID,
			Type: tools.ToInt(v.Type),
			CreatedAt:   v.CreatedAt.Format("2006-01-02 15:04:05"),
			Star: int(v.Star),
			Text: v.Text,

			FoodStar: int(v.FoodStar),
			BoxStar: int(v.BoxStar),
			FoodId: v.FoodID,
			FoodName: tools.If(t.language=="ug",v.Food.NameUg,v.Food.NameZh),
			Images:   make([]string, 0),
			Replies: make([]merchantResources.CommentListEntityReplies, 0),
			UserName: v.User.Name,
			UserAvatar: tools.AddCdn(v.User.Avatar),
			OrderDetail: nil,
		}

		// 处理评论中的订单详情，包括 spec 和 combo 类型
		if v.OrderDetail != nil {
			detail := v.OrderDetail
			food := detail.RestaurantFoods
			comboItems := make([]merchantResources.CommentFoodComboItems, 0)
			var selectedSpec *merchantResources.CommentRestaurantFoodSpec = nil

			// 如果是套餐美食
			if detail.FoodType == models.RestaurantFoodsTypeCombo && len(food.ComboFoodItems) > 0 {
				for _, item := range food.ComboFoodItems {
					// 处理子美食的规格选项
					var itemSelectedSpec *merchantResources.CommentRestaurantFoodSpec = nil
					if item.FoodType == models.FoodsComboItemFoodTypeSpec && item.SelectedSpec != nil {
						var specOptions []merchantResources.OrderRestaurantFoodSpecOption
						// 从数据库中获取规格信息
						for _, option := range item.SelectedSpec.FoodSpecOptions {
							specOption := merchantResources.OrderRestaurantFoodSpecOption{
								ID:    option.ID,
								Name:  tools.If(t.language == "zh", option.NameZh, option.NameUg),
								Price: option.Price,
							}
							specOptions = append(specOptions, specOption)
						}

						itemSelectedSpec = &merchantResources.CommentRestaurantFoodSpec{
							ID:          item.SelectedSpec.ID,
							SpecOptions: specOptions,
						}
					}

					// 处理子美食信息
					restaurantFood := merchantResources.CommentRestaurantFoodsResponse{
						ID:               item.RestaurantFood.ID,
						Name:             tools.GetNameByLang(item.RestaurantFood, t.language),
						Image:            tools.CdnUrl(item.RestaurantFood.Image),
						Description:      tools.If(t.language == "zh", item.RestaurantFood.DescriptionZh, item.RestaurantFood.DescriptionUg),
						StarAvg:          item.RestaurantFood.StarAvg,
						Price:            item.RestaurantFood.Price,
						FoodQuantity:     item.RestaurantFood.FoodQuantity,
						FoodQuantityType: item.RestaurantFood.FoodQuantityType,
						SelectedSpec:     itemSelectedSpec,
					}

					comboItem := merchantResources.CommentFoodComboItems{
						ID:             item.ID,
						FoodType:       item.FoodType,
						FoodID:         item.FoodID,
						Count:          item.Count,
						RestaurantFood: restaurantFood,
					}
					comboItems = append(comboItems, comboItem)
				}
			} else if detail.FoodType == models.RestaurantFoodsTypeSpec && detail.SelectedSpec != nil {
				// 如果是规格美食
				var specOptions []merchantResources.OrderRestaurantFoodSpecOption
				// 从数据库中获取规格信息
				for _, option := range detail.SelectedSpec.FoodSpecOptions {
					specOption := merchantResources.OrderRestaurantFoodSpecOption{
						ID:    option.ID,
						Name:  tools.If(t.language == "zh", option.NameZh, option.NameUg),
						Price: option.Price,
					}
					specOptions = append(specOptions, specOption)
				}

				selectedSpec = &merchantResources.CommentRestaurantFoodSpec{
					ID:          detail.SelectedSpec.ID,
					SpecOptions: specOptions,
				}
			}

			item.OrderDetail = &merchant.CommentOrderDetail{
				FoodType:       detail.FoodType,
				ComboFoodItems: comboItems,
				SelectedSpec:   selectedSpec,
			}
		}

		for _, image := range v.Images {
			item.Images = append(item.Images, tools.AddCdn(image.Image))
		}
		for _, reply := range v.CommentReply {
			item.Replies = append(item.Replies, merchantResources.CommentListEntityReplies{
				Id: reply.ID,
				Type: reply.Type,
				CreatedAt: reply.CreatedAt.Format("2006-01-02 15:04:05"),
				Text: reply.Text,
			})
		}
		rtn.Items = append(rtn.Items, item)
	}
	return rtn
}

// PartRefundListFormat
//
//  @Author: YaKupJan
//  @Date: 2024-11-29 18:29:44
//  @Description: 部分退款返回信息 格式化
//  @receiver t
//  @param orderToday
//  @param reasonList
//  @return interface{}
func (t *MerchantTransformer) PartRefundListFormat(orderToday models.OrderToday, reasonList []models.Dictionary) interface{} {
	var foods []merchant.RefundFoodData
	for _, orderDetail := range orderToday.OrderDetail {
		item := merchant.RefundFoodData{
			Name:          tools.GetNameByLang(orderDetail.RestaurantFoods, t.language),
			Image:         tools.CdnUrl(orderDetail.RestaurantFoods.Image),
			OrderDetailID: orderDetail.ID,
			StoreFoodID:   orderDetail.StoreFoodsID,
			Price:         orderDetail.Price,
			RefundPrice:   orderDetail.RefundPrice,
			Number:        orderDetail.Number,
			LunchBoxGroupIndex: orderDetail.LunchBoxGroupIndex,
			FoodType:      tools.ToInt(orderDetail.FoodType),
			SpecID: orderDetail.SpecID,
		}
		if orderDetail.FoodType == models.RestaurantFoodsTypeSpec {
			item.SelectedSpec = orderDetail.SelectedSpec.GetOptions(*orderDetail.SelectedSpec, t.language)
		}
		if orderDetail.FoodType == models.RestaurantFoodsTypeCombo {
			item.ComboItems = orderDetail.RestaurantFoods.GetComboItems(orderDetail.RestaurantFoods, t.language)
		}
		foods = append(foods, item)
	}

	var lunchBoxes []merchant.RefundLunchBoxData
	for _, lunchBoxDetail := range orderToday.OrderDetail {
		if lunchBoxDetail.LunchBoxFee == 0 || lunchBoxDetail.LunchBoxCount == 0{
			continue
		}
		lunchBoxes = append(lunchBoxes, merchant.RefundLunchBoxData{
			StoreFoodID:         lunchBoxDetail.StoreFoodsID,
			OrderDetailID: lunchBoxDetail.ID,
			LunchBoxID:          lunchBoxDetail.LunchBoxID,
			LunchBoxCount:       lunchBoxDetail.LunchBoxCount,
			LunchBoxFee:         lunchBoxDetail.LunchBoxFee,
			LunchBoxRefundPrice: lunchBoxDetail.LunchBoxRefundPrice,
			SpecID: lunchBoxDetail.SpecID,
			LunchBoxGroupIndex: lunchBoxDetail.LunchBoxGroupIndex,
			LunchBoxAccommodate: tools.If(lunchBoxDetail.RestaurantFoods.LunchBoxAccommodate == 0,1,lunchBoxDetail.RestaurantFoods.LunchBoxAccommodate),
		})
	}
	var returnReasons []merchant.RefundReason

	for _, reason := range reasonList {
		returnReasons = append(returnReasons, merchant.RefundReason{
			Title: tools.GetNameByLang(reason, t.language),
			Type:  reason.SubType,
			ID:    reason.ID,
		})
	}
	data := make(map[string]interface{})
	data["order"] = map[string]interface{}{
		"actual_paid": orderToday.ActualPaid,
		"shipment":    orderToday.Shipment,
		"order_id":    orderToday.ID,
		"lunch_box":   lunchBoxes,
		"foods":       foods,
	}
	data["return_reason"] = returnReasons
	return data
}

// 处理部分退款信息 包括 新订单/已接收订单/已完成订单/退款订单
func (t *MerchantTransformer) processOrderPartRefund(ctx *gin.Context, order models.OrderToday, orderType string,
) merchantResources.OrderPartRefundInfo {
	var (
		foodSpecTrns = transformers.NewFoodSpecTransformer(ctx)
	)

	// 部分退款信息
	var orderPartRefundInfo merchant.OrderPartRefundInfo
	// 部分退款列表
	var orderPartRefundList []merchant.OrderPartRefund
	// 部分退款 金额 包括餐费和打包费
	var orderPartRefundPrice float64
	// 用于保存最后一个退款渠道和原因
	var lastPartRefund models.OrderPartRefund
	// 定义是否有部分退款
	hasPartRefund  := false
	// 遍历获取是否有部分退款
	for _, partRefund := range order.OrderPartRefund {
		// 如果有部分退款
		if partRefund.PartRefundType == models.PartRefundTypePart{
			hasPartRefund = true
		}
	}

	// 如果有部分退款信息 且 不是全部退款订单 那就显示部分退款信息
	if hasPartRefund && orderType != "Canceled" {
		for _, partRefund := range order.OrderPartRefund {
			if partRefund.PartRefundType != models.PartRefundTypePart {
				continue
			}

			for _, partRefundDetail := range partRefund.OrderPartRefundDetail {
				if partRefundDetail.Type != models.PartRefundTypePart {
					continue
				}
				// 保存订单
				orderPartRefund := merchant.OrderPartRefund{
					Name:          tools.GetNameByLang(partRefundDetail.RestaurantFoods, t.language),
					Count:         tools.ToInt(partRefundDetail.Number),
					Price:         float32(tools.ToFloat32(partRefundDetail.Price) / 100),
					Image:         tools.CdnUrl(partRefundDetail.RestaurantFoods.Image),
					OriginalPrice: float32(tools.ToFloat32(partRefundDetail.OriginalPrice) / 100),
					FoodID:        tools.ToInt(partRefundDetail.StoreFoodsId),
					RefundPrice:   float64(partRefundDetail.RefundPrice / 100),

					FoodType:       partRefundDetail.FoodType,
					SpecID:         partRefundDetail.SpecID,
					ComboFoodItems: foodSpecTrns.GetComboItems(partRefundDetail.RestaurantFoods.ComboFoodItems),
					SelectedSpec:   foodSpecTrns.GetSelectedSpec(&partRefundDetail.SelectedSpec),
				}
				orderPartRefundList = append(orderPartRefundList,orderPartRefund)
				orderPartRefundPrice +=  float64(partRefundDetail.RefundPrice * partRefundDetail.Number)
				// 如果有饭盒
				if partRefundDetail.LunchBox.ID != 0{
					var lunchBox merchant.OrderPartRefund
					lunchBox.Name = tools.GetNameByLang(partRefundDetail.LunchBox, t.language)
					lunchBox.Count = tools.ToInt(partRefundDetail.LunchBoxCount)
					lunchBox.Price = float32(tools.ToFloat32(partRefundDetail.LunchBoxFee) / 100)
					lunchBox.Image = tools.AddCdn("upload/restaurantfoods/202007/15/d49b559e40dc048ebb9817c93f4999b6.jpg")
					lunchBox.OriginalPrice = float32(tools.ToFloat32(partRefundDetail.LunchBoxFee) / 100)
					lunchBox.FoodID = tools.ToInt(partRefundDetail.StoreFoodsId)
					lunchBox.RefundPrice = float64(partRefundDetail.LunchBoxRefundPrice / 100)
					orderPartRefundList = append(orderPartRefundList, lunchBox)
					orderPartRefundPrice += float64(partRefundDetail.LunchBoxFee * partRefundDetail.LunchBoxCount)
				}
			}
			// 保存每次循环中的退款渠道和原因，但最终取最后一个
			lastPartRefund = partRefund

		}
		// 部分退款原因
		orderPartRefundInfo.OrderPartRefundReason = tools.If(lastPartRefund.PartRefundReasonText != "",lastPartRefund.PartRefundReasonText,tools.If(t.language =="zh",lastPartRefund.RefundReason.NameZh,lastPartRefund.RefundReason.NameUg))

		// 退款渠道
		orderPartRefundInfo.RefundChanelName = t.langUtil.TArr("part_refund_chanel_name")[lastPartRefund.RefundChanel]
		orderPartRefundInfo.RefundChanel = lastPartRefund.RefundChanel

		// 部分退款方
		partRefundCreatorTypeInt := tools.ToInt(lastPartRefund.PartRefundCreatorType)
		orderPartRefundInfo.PartRefundCreatorName = t.langUtil.TArr("part_refund_creator_name")[partRefundCreatorTypeInt]

		// 第一次部分退款 的数据 和 第二次 全退款的数据都写在这里面
		orderPartRefundInfo.OrderPartRefundList = orderPartRefundList
		orderPartRefundInfo.OrderPartRefundPrice  =  orderPartRefundPrice  / 100
	}

	return orderPartRefundInfo
}
