package merchant

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources/merchant"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type PriceMarkupTransformer struct {
	langUtil *lang.LangUtil
	language string
}

func NewPriceMarkupTransformer(c *gin.Context) *PriceMarkupTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	transformer := PriceMarkupTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &transformer
}


// HomeShowListFormat
//
//  @Author: YaKupJan
//  @Date: 2024-10-22 19:15:37
//  @Description: 首页显示加价信息列表
//  @receiver t
//  @param markupFoods
//  @return []map[string]interface{}
func (t PriceMarkupTransformer) HomeShowListFormat(markupFoods []models.PriceMarkupFood) []merchant.HomeShowPriceMarkupEntity {
	// 定义返回数据
	var items []merchant.HomeShowPriceMarkupEntity
	// 遍历传递的参数 进行格式化数据
	for _, markupFood := range markupFoods {
		comboItems := make([]merchant.CommentFoodComboItems, 0)
		var selectedSpec *merchant.CommentRestaurantFoodSpec = nil

		if markupFood.FoodType == models.RestaurantFoodsTypeSpec && markupFood.SelectedSpec != nil {
			specOptions := make([]merchant.OrderRestaurantFoodSpecOption, 0)
			for _, option := range markupFood.SelectedSpec.FoodSpecOptions {
				specOption := merchant.OrderRestaurantFoodSpecOption{
					ID:    option.ID,
					Name:  tools.If(t.language == "zh", option.NameZh, option.NameUg),
					Price: option.Price,
				}
				specOptions = append(specOptions, specOption)
			}

			selectedSpec = &merchant.CommentRestaurantFoodSpec{
				ID:          markupFood.SelectedSpec.ID,
				SpecOptions: specOptions,
			}
		} else if markupFood.FoodType == models.RestaurantFoodsTypeCombo && markupFood.RestaurantFoods.ComboFoodItems != nil &&
			len(markupFood.RestaurantFoods.ComboFoodItems) > 0 {
			for _, comboItem := range markupFood.RestaurantFoods.ComboFoodItems {
				// 处理子美食的规格选项
				var itemSelectedSpec *merchant.CommentRestaurantFoodSpec = nil
				if comboItem.FoodType == models.FoodsComboItemFoodTypeSpec && comboItem.SelectedSpec != nil {
					specOptions := make([]merchant.OrderRestaurantFoodSpecOption, 0)
					for _, option := range comboItem.SelectedSpec.FoodSpecOptions {
						specOption := merchant.OrderRestaurantFoodSpecOption{
							ID:    option.ID,
							Name:  tools.If(t.language == "zh", option.NameZh, option.NameUg),
							Price: option.Price,
						}
						specOptions = append(specOptions, specOption)
					}

					itemSelectedSpec = &merchant.CommentRestaurantFoodSpec{
						ID:          comboItem.SelectedSpec.ID,
						SpecOptions: specOptions,
					}
				}

				// 处理子美食信息
				restaurantFood := merchant.CommentRestaurantFoodsResponse{
					ID:               comboItem.RestaurantFood.ID,
					Name:             tools.GetNameByLang(comboItem.RestaurantFood, t.language),
					Image:            tools.CdnUrl(comboItem.RestaurantFood.Image),
					Description:      tools.If(t.language == "zh", comboItem.RestaurantFood.DescriptionZh, comboItem.RestaurantFood.DescriptionUg),
					StarAvg:          comboItem.RestaurantFood.StarAvg,
					Price:            comboItem.RestaurantFood.Price,
					FoodQuantity:     comboItem.RestaurantFood.FoodQuantity,
					FoodQuantityType: comboItem.RestaurantFood.FoodQuantityType,
					SelectedSpec:     itemSelectedSpec,
				}

				comboItem := merchant.CommentFoodComboItems{
					ID:             comboItem.ID,
					FoodType:       comboItem.FoodType,
					FoodID:         comboItem.FoodID,
					Count:          comboItem.Count,
					RestaurantFood: restaurantFood,
				}
				comboItems = append(comboItems, comboItem)
			}
		}

		var item = merchant.HomeShowPriceMarkupEntity{
			Id: markupFood.ID,
			FoodsName: tools.GetNameByLang(markupFood.RestaurantFoods,t.language),
			OriginalPrice: tools.ToInt(markupFood.RestaurantFoods.Price),
			InPrice: markupFood.InPrice,
			TotalCount: markupFood.TotalCount,
			StartDate: tools.TimeFormatYmd(markupFood.StartDate),
			EndDate: tools.TimeFormatYmd(markupFood.EndDate),
			TotalPrice: markupFood.TotalCount * markupFood.InPrice,
			PayTime: tools.TimeFormatYmdHis(markupFood.PayTime),
			FoodType:       markupFood.FoodType,
			SelectedSpec:   selectedSpec,
			ComboFoodItems: comboItems,
		}
		items = append(items,item)
	}
	if items == nil {
		items = []merchant.HomeShowPriceMarkupEntity{}
	}
	return items
}

// DetailListFormat
//
//  @Author: YaKupJan
//  @Date: 2024-10-24 18:15:52
//  @Description: 格式化 加价信息列表
//  @receiver t
//  @param data
//  @return []map[string]interface{}
func (t PriceMarkupTransformer) DetailListFormat(data []models.PriceMarkupFood) []merchant.PriceMarkupDetailEntity {
	var entities []merchant.PriceMarkupDetailEntity
	for _, item := range data {
		sailedCount := 0
		if len(item.PriceMarkupFoodLog)>0{
			sailedCount = int(item.PriceMarkupFoodLog[0].PriceMarkupFoodSaledTotalCount)
		}

		comboItems := make([]merchant.CommentFoodComboItems, 0)
		var selectedSpec *merchant.CommentRestaurantFoodSpec = nil

		if item.FoodType == models.RestaurantFoodsTypeSpec && item.SelectedSpec != nil {
			specOptions := make([]merchant.OrderRestaurantFoodSpecOption, 0)
			for _, option := range item.SelectedSpec.FoodSpecOptions {
				specOption := merchant.OrderRestaurantFoodSpecOption{
					ID:    option.ID,
					Name:  tools.If(t.language == "zh", option.NameZh, option.NameUg),
					Price: option.Price,
				}
				specOptions = append(specOptions, specOption)
			}

			selectedSpec = &merchant.CommentRestaurantFoodSpec{
				ID:          item.SelectedSpec.ID,
				SpecOptions: specOptions,
			}
		} else if item.FoodType == models.RestaurantFoodsTypeCombo && item.RestaurantFoods.ComboFoodItems != nil &&
			len(item.RestaurantFoods.ComboFoodItems) > 0 {
			for _, comboItem := range item.RestaurantFoods.ComboFoodItems {
				// 处理子美食的规格选项
				var itemSelectedSpec *merchant.CommentRestaurantFoodSpec = nil
				if comboItem.FoodType == models.FoodsComboItemFoodTypeSpec && comboItem.SelectedSpec != nil {
					specOptions := make([]merchant.OrderRestaurantFoodSpecOption, 0)
					for _, option := range comboItem.SelectedSpec.FoodSpecOptions {
						specOption := merchant.OrderRestaurantFoodSpecOption{
							ID:    option.ID,
							Name:  tools.If(t.language == "zh", option.NameZh, option.NameUg),
							Price: option.Price,
						}
						specOptions = append(specOptions, specOption)
					}

					itemSelectedSpec = &merchant.CommentRestaurantFoodSpec{
						ID:          comboItem.SelectedSpec.ID,
						SpecOptions: specOptions,
					}
				}

				// 处理子美食信息
				restaurantFood := merchant.CommentRestaurantFoodsResponse{
					ID:               comboItem.RestaurantFood.ID,
					Name:             tools.GetNameByLang(comboItem.RestaurantFood, t.language),
					Image:            tools.CdnUrl(comboItem.RestaurantFood.Image),
					Description:      tools.If(t.language == "zh", comboItem.RestaurantFood.DescriptionZh, comboItem.RestaurantFood.DescriptionUg),
					StarAvg:          comboItem.RestaurantFood.StarAvg,
					Price:            comboItem.RestaurantFood.Price,
					FoodQuantity:     comboItem.RestaurantFood.FoodQuantity,
					FoodQuantityType: comboItem.RestaurantFood.FoodQuantityType,
					SelectedSpec:     itemSelectedSpec,
				}

				comboItem := merchant.CommentFoodComboItems{
					ID:             comboItem.ID,
					FoodType:       comboItem.FoodType,
					FoodID:         comboItem.FoodID,
					Count:          comboItem.Count,
					RestaurantFood: restaurantFood,
				}
				comboItems = append(comboItems, comboItem)
			}
		}

		entity := merchant.PriceMarkupDetailEntity{
			Id: item.ID,
			FoodsName: tools.GetNameByLang(item.RestaurantFoods,t.language),
			TotalCount: item.TotalCount,
			Price: item.InPrice,
			TotalPrice: item.TotalCount * item.InPrice,
			PayTime: tools.TimeFormatYmdHis(item.PayTime),
			ReviewAt: tools.TimeFormatYmdHis(item.ReviewAt),
			StartDate: tools.TimeFormatYmd(item.StartDate),
			EndDate: tools.TimeFormatYmd(item.EndDate),
			State: item.State,
			StateName: t.langUtil.TArr("price_markup_food_state")[item.State],
			RunningState: item.RunningState,
			RunningStateName: t.langUtil.TArr("price_markup_food_running_state")[item.RunningState],
			SailedCount: sailedCount,
			SailedPrice: (sailedCount) * item.InPrice,
			OriginalPrice: int(item.RestaurantFoods.Price),
			FoodType:       item.FoodType,
			SelectedSpec:   selectedSpec,
			ComboFoodItems: comboItems,
		}
		entities = append(entities,entity)
	}
	if entities == nil{
		entities = []merchant.PriceMarkupDetailEntity{}
	}
	return entities
}

func (t PriceMarkupTransformer) StatisticSailedMarketDetailFormat(priceMarkupFoods []models.PriceMarkupFood) []merchant.PriceMarkupStatisticSailedMarketDetail {
	var entities []merchant.PriceMarkupStatisticSailedMarketDetail
	for _, item := range priceMarkupFoods {
		sailedCount := 0
		if len(item.PriceMarkupFoodLog)>0{
			sailedCount = int(item.PriceMarkupFoodLog[0].PriceMarkupFoodSaledTotalCount)
		}
		entity := merchant.PriceMarkupStatisticSailedMarketDetail{
			Id: item.ID,
			FoodsName: tools.GetNameByLang(item.RestaurantFoods,t.language),
			TotalCount: item.TotalCount,
			Price: item.InPrice,
			SailedCount: sailedCount,
			SailedPrice: sailedCount *  item.InPrice,
			State: item.State,
			StateName: t.langUtil.TArr("price_markup_food_state")[item.State],
			RunningState: item.RunningState,
			RunningStateName: t.langUtil.TArr("price_markup_food_running_state")[item.RunningState],
			ReviewAt: tools.TimeFormatYmdHis(item.ReviewAt),
		}
		entities = append(entities,entity)
	}
	if entities == nil{
		entities = []merchant.PriceMarkupStatisticSailedMarketDetail{}
	}
	return entities
}