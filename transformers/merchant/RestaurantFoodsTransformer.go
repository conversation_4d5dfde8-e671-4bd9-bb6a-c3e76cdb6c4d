package merchant

import (
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources/merchant"
	"mulazim-api/resources/response"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type RestaurantFoodsTransformer struct {
	langUtil *lang.LangUtil
	language string
}


func NewRestaurantFoodsTransformer(c *gin.Context) *RestaurantFoodsTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	restaurantFoodsTransformer := RestaurantFoodsTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &restaurantFoodsTransformer
}


func (t *RestaurantFoodsTransformer) FormatRestaurantFoods(foods []models.RestaurantFoodsHasPreferential) []merchant.RestaurantFoods {
	res := make([]merchant.RestaurantFoods,0)
	for _,food := range foods {
		var foodInfo merchant.RestaurantFoods
		foodInfo.ID = food.ID
		foodInfo.AllFoodsID = tools.ToInt(food.FoodsGroupId)
		foodInfo.Name = tools.GetNameByLang(food,t.language)
		foodInfo.Image =  getFullUrl(food.Image, configs.MyApp.CdnUrl)
		foodInfo.Description =  food.Description
		foodInfo.BeginTime =  food.BeginTime
		foodInfo.EndTime =  food.EndTime
		foodInfo.ReadyTime =  food.ReadyTime
		foodInfo.IsDistribution =  food.IsDistribution
		foodInfo.StarAvg =  food.StarAvg
		foodInfo.CommentCount =  food.CommentCount
		foodInfo.MonthOrderCount =  food.MonthOrderCount
		foodInfo.Price =  tools.ToFloat64(food.Price)/100
		foodInfo.DiscountPrice =  tools.ToFloat64(food.FoodsPreferential.DiscountPrice)/100
		foodInfo.Weight = food.Weight
		foodInfo.State = food.State
		res = append(res, foodInfo)
	}
	// 关闭状态排到最后
	return res
}

func (t *RestaurantFoodsTransformer) FormatFoodsInformation(food models.RestaurantFoods) map[string]interface{} {
	foodMap := make(map[string]interface{})
	foodMap["id"] = food.ID
	foodMap["restaurant_id"] = food.RestaurantID
	foodMap["all_foods_id"] = food.FoodsGroupId
	foodMap["category"] = tools.GetNameByLang(food.FoodsGroup,t.language)
	foodMap["name_ug"] = food.NameUg
	foodMap["name_zh"] = food.NameZh
	foodMap["description_ug"] = food.DescriptionUg
	foodMap["description_zh"] = food.DescriptionZh
	foodMap["price"] = tools.ToFloat64(food.Price)/100
	foodMap["image"] = getFullUrl(food.Image,configs.MyApp.CdnUrl)
	foodMap["ready_time"] = food.ReadyTime
	foodMap["begin_time"] = tools.TimeToMinuteString(food.BeginTime)
	foodMap["end_time"] = tools.TimeToMinuteString(food.EndTime)
	foodMap["is_distribution"] = food.IsDistribution
	foodMap["state"] = food.State
	foodMap["store_open_time"] = tools.TimeToMinuteString(food.Restaurant.OpenTime)
	foodMap["store_close_time"] = tools.TimeToMinuteString(food.Restaurant.CloseTime)
	foodMap["store_begin_time"] = tools.TimeToMinuteString(food.Restaurant.BeginTime)
	foodMap["store_end_time"] = tools.TimeToMinuteString(food.Restaurant.EndTime)
	foodMap["foods_group_id"] = food.FoodsGroupId
	foodMap["category_ids"] = t.GetCategoryIds(food.Categories)
	foodMap["weight"] = food.Weight
	return foodMap
}

func (t *RestaurantFoodsTransformer) FormatFoodsInformationNew(food models.RestaurantFoods) response.FoodInformation {
	comboFoodItems := make([]merchant.CommentFoodComboItems, 0)

	if food.FoodType == models.RestaurantFoodsTypeCombo && food.ComboFoodItems != nil &&
		len(food.ComboFoodItems) > 0 {
		comboFoodItems = GetFormattedComboItems(food.ComboFoodItems, t.language)
	}

	foodInfo := response.FoodInformation{
		ID:             food.ID,
		RestaurantID:   food.RestaurantID,
		FoodsGroupID:   food.FoodsGroupId,
		CategoryIDs:    t.GetCategoryIds(food.Categories),
		NameUg:         food.NameUg,
		NameZh:         food.NameZh,
		DescriptionUg:  food.DescriptionUg,
		DescriptionZh:  food.DescriptionZh,
		Price:          tools.ToFloat64(food.Price),
		Image:          getFullUrl(food.Image, configs.MyApp.CdnUrl),
		ReadyTime:      food.ReadyTime,
		BeginTime:      tools.TimeToMinuteString(food.BeginTime),
		EndTime:        tools.TimeToMinuteString(food.EndTime),
		IsDistribution: food.IsDistribution,
		State:          food.State,
		IsRecommend:    food.IsRecommend,
		Weight:         food.Weight,

		FoodType:       food.FoodType,
		ComboFoodItems: comboFoodItems,
	}
	return foodInfo
}

func (t *RestaurantFoodsTransformer) GetCategoryIds(categories []models.FoodsCategory) []int{
	var categoryIds []int
	for _,category := range categories {
		categoryIds = append(categoryIds,category.ID)
	}
	return categoryIds
}
	