package merchant

import (
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources/merchant"
	merchantResources "mulazim-api/resources/merchant"
	"mulazim-api/tools"
	"mulazim-api/transformers"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type OrderV2Transformer struct {
	langUtil *lang.LangUtil
	language string
}

// NewOrderV2Transformer
//
//	@Description: 初始化商家端订单Transformer
//	@return *NewOrderV2Transformer
func NewOrderV2Transformer(c *gin.Context) *OrderV2Transformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	orderV2Transformer := OrderV2Transformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &orderV2Transformer
}

// FormatNewOrder
//
//	@Description: 新订单返回数据格式化，适配旧版本Android客户端
//	@receiver trns
//	@param orderToday
//	@param receiveOrderTimeLimit
//	@return resources.NewOrderEntity
func (trns *OrderV2Transformer) FormatNewOrder(ctx *gin.Context, orderToday []models.OrderToday,
	receiveOrderTimeLimit int, showCustomerInfo bool,
) merchant.NewOrderEntity {

	var newOrderEntity = merchant.NewOrderEntity{
		ServerTime: int(time.Now().Unix()),
	}
	orderCancelTimeLeft := 2
	newOrderEntity.NewOrderList = []merchant.NewOrderList{}
	groupPrefix := "group_"
	adminType := "_restaurant_count"
	for i := 0; i < len(orderToday); i++ {
		order := orderToday[i]
		key := groupPrefix + tools.ToString(order.ID) + adminType
		price := float32((tools.ToFloat32(order.Price) + tools.ToFloat32(order.LunchBoxFee)))
		if order.OrderPriceRes > 0 {
			price = tools.ToFloat32(order.OrderPriceRes)
		}
		rtnOrder := merchant.NewOrderList{
			ID:           order.ID,
			SerialNumber: int(order.SerialNumber),
			Name:         order.Name,
			Price:        price,
			OrderNo:      order.OrderID,

			OrderTime:        order.OrderTime,
			Timezone:         order.Timezone,
			ConsumeType:      order.ConsumeType,
			Shipment:         tools.ToFloat64(order.Shipment),
			OriginalShipment: tools.ToFloat64(order.OriginalShipment),
			ResIncome:        tools.ToFloat64(order.ResIncome),
			OriginalPrice:    tools.ToFloat64(order.OriginalPrice),
			//BookingTime:     carbon.Parse(carbon.Now("Asia/Shanghai").ToDateString()+" "+order.BookingTime).Format("H:i"),
			//BookingDateTime: carbon.Parse(carbon.Now("Asia/Shanghai").ToDateString()+" "+order.BookingTime).ToDateTimeString(),//1234
			CityName:      order.AddressView.CityName,
			AreaName:      order.AddressView.AreaName,
			StreetName:    order.AddressView.StreetName,
			BuildingName:  order.AddressView.BuildingName,
			OrderAddress:  order.OrderAddress,
			OrderNote:     order.Description,
			SendNotify:    order.SendNotify,
			OrderState:    order.State,
			OrderStateDes: trns.getOrderStateDesc(order.State, order.RefundChanel),
			OrderType:     order.OrderType,
			//Now: order.Now ,
			//OrderTimeoutTime: order.OrderTimeoutTime ,
			//OrderTimeoutTimestamp: order.OrderTimeoutTimestamp ,
			//OrderTimeLeft: order.OrderTimeLeft ,
			NewOrderDetail:         trns.formatOrderDetail(order),
			FoodsReadyTime:         order.GetFoodsReadyTime(),
			FoodsReadyRemainMinute: order.FoodsReadyRemainMinute(),
			MsgCount:               tools.GetGroupChatMsgCount(key),
			DisplayShipment:        order.Restaurant.DisplayShipment,
			DisplayMerchantProfit:  order.Restaurant.DisplayMerchantProfit,
		}
		if order.Restaurant.DisplayShipment == 2 {
			rtnOrder.Shipment = tools.ToFloat64(0)
		}
		rtnOrder.BookingTime = carbon.Parse(order.BookingTime).Format("H:i")
		rtnOrder.BookingDateTime = carbon.Parse(order.BookingTime).Format("Y-m-d H:i:s")
		rtnOrder.Timezone = 8

		//计算可以接受订单时间
		printTime := carbon.Parse(order.PrintTime)
		diffInSeconds := carbon.Now().DiffInSeconds(printTime) // 计算时间差（秒）
		rtnOrder.CanBeReceivedIn = diffInSeconds

		rtnOrder.PrintTime = printTime.Format("Y-m-d H:i:s")

		rtnOrder.ShowCustomerInfo = showCustomerInfo

		if order.Shipper.ID != 0 {
			rtnOrder.ShipperMobile = order.Shipper.Mobile
			rtnOrder.ShipperName = order.Shipper.RealName
		}

		if showCustomerInfo {
			rtnOrder.Mobile = order.Mobile
			rtnOrder.RealMobile = order.Mobile
			rtnOrder.OrderAddress = order.OrderAddress
		} else {
			rtnOrder.Mobile = "***********"
			rtnOrder.RealMobile = "***********"
			rtnOrder.OrderAddress = "***********"
			rtnOrder.BuildingName = "***********"
			rtnOrder.StreetName = "***********"
			rtnOrder.Name = "***********"
		}

		if order.OrderType == 1 {
			orderPayTime := carbon.Time2Carbon(order.PayTime)

			rtnOrder.Now = int(carbon.Now("Asia/Shanghai").Timestamp())
			rtnOrder.OrderTimeoutTime = carbon.Time2Carbon(order.PayTime).AddMinutes(receiveOrderTimeLimit + orderCancelTimeLeft).ToTimeString()
			rtnOrder.OrderTimeoutTimestamp = int(orderPayTime.AddMinutes(receiveOrderTimeLimit + orderCancelTimeLeft).Timestamp())

			rtnOrder.OrderTimeLeft = int(carbon.Now("Asia/Shanghai").DiffInSeconds(orderPayTime.AddMinutes(receiveOrderTimeLimit + orderCancelTimeLeft)))
		}

		//商家端新订单,已接订单  显示 满减/减配送费 等活动标志和金额
		rtnOrder.IsMarketing = tools.If(len(order.MarketingList) > 0, 1, 0)
		rtnOrder.MarketingList = []merchant.MarketingList{}
		rtnOrder.DeliveryType = order.DeliveryType
		rtnOrder.IsCoupon = tools.If(order.Coupon.ID > 0, 1, 0)

		if order.DeliveryType == 2 && len(order.Mobile) > 0 {
			rtnOrder.ShowCustomerInfo = true
			rtnOrder.Mobile = order.Mobile
			rtnOrder.RealMobile = order.Mobile
			rtnOrder.OrderAddress = order.OrderAddress
		}

		for _, info := range order.MarketingList {
			image := ""
			switch info.Type {
			case 1: //满减
				image = strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/order_detail_price_discount_" + trns.language + ".png"
			case 2: //减配送费
				image = strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/order_detail_shipment_discount_icon_" + trns.language + ".png"
			}
			marketInfo := merchant.MarketingList{
				MarketingImage:        image,
				MarketingName:         info.Name,
				MarketingReductionFee: tools.ToFloat32(info.StepReduce),
			}
			rtnOrder.MarketingList = append(rtnOrder.MarketingList, marketInfo)
		}

		if order.Coupon.ID > 0 {
			rtnOrder.IsMarketing = 1
			marketInfo := merchant.MarketingList{
				MarketingName:         trns.langUtil.T("coupon"),
				MarketingReductionFee: tools.ToFloat32(order.Coupon.Price),
				MarketingImage:        strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/merchant_home_coupon_" + trns.language + ".png",
			}
			rtnOrder.MarketingList = append(rtnOrder.MarketingList, marketInfo)
		}
		// 处理部分退款信息
		rtnOrder.OrderPartRefundList = trns.processOrderPartRefund(ctx, order, "NewOrder")

		newOrderEntity.NewOrderList = append(newOrderEntity.NewOrderList, rtnOrder)
	}
	return newOrderEntity
}

// formatOrderDetail
// @Description: 格式化订单美食数据
// @receiver trns
// @param order
// @return []resources.NewOrderDetail
func (trns *OrderV2Transformer) formatOrderDetail(order models.OrderToday) []merchant.NewOrderDetail {
	var newOrderDetail []merchant.NewOrderDetail
	//newOrderEntity.NewOrderList = new(resources.NewOrderList)
	detailMap := make(map[string]merchant.NewOrderDetail)
	// map[food_id + spec_id]

	for i := 0; i < len(order.OrderDetail); i++ {
		detail := order.OrderDetail[i]
		if detail.Number == 0 {
			continue
		}
		price := float32(detail.Price)
		if detail.PriceMarkupId > 0 { //加价 成本价
			price = float32(detail.PriceMarkupPrice)
		}

		food := detail.RestaurantFoods
		comboItems := make([]merchantResources.OrderFoodComboItems, 0)
		var selectedSpec *merchantResources.OrderRestaurantFoodSpec = nil

		// 如果是套餐美食
		if detail.FoodType == models.RestaurantFoodsTypeCombo && len(food.ComboFoodItems) > 0 {
			for _, item := range food.ComboFoodItems {
				// 处理子美食的规格选项
				var selectedSpec *merchantResources.OrderRestaurantFoodSpec = nil
				if item.FoodType == models.FoodsComboItemFoodTypeSpec && item.SelectedSpec != nil {
					var specOptions []merchantResources.OrderRestaurantFoodSpecOption
					// 从数据库中获取规格信息
					for _, option := range item.SelectedSpec.FoodSpecOptions {
						specOption := merchantResources.OrderRestaurantFoodSpecOption{
							ID:    option.ID,
							Name:  tools.If(trns.language == "zh", option.NameZh, option.NameUg),
							Price: option.Price,
							// 订单暂时不需要规格项分组
							//SpecOptionType: merchantResources.OrderRestaurantFoodSpecType{
							//	ID:        option.FoodSpecType.ID,
							//	Name:      tools.If(trns.language == "zh", option.FoodSpecType.NameZh, option.FoodSpecType.NameUg),
							//	PriceType: option.FoodSpecType.PriceType,
							//},
						}
						specOptions = append(specOptions, specOption)
					}

					selectedSpec = &merchantResources.OrderRestaurantFoodSpec{
						ID:          item.SelectedSpec.ID,
						SpecOptions: specOptions,
					}
				}

				// 处理子美食信息
				restaurantFood := merchantResources.OrderRestaurantFoodsResponse{
					ID:               item.RestaurantFood.ID,
					Name:             tools.GetNameByLang(item.RestaurantFood, trns.language),
					Image:            tools.CdnUrl(item.RestaurantFood.Image),
					Description:      tools.If(trns.language == "zh", item.RestaurantFood.DescriptionZh, item.RestaurantFood.DescriptionUg),
					StarAvg:          item.RestaurantFood.StarAvg,
					Price:            item.RestaurantFood.Price,
					FoodQuantity:     item.RestaurantFood.FoodQuantity,
					FoodQuantityType: item.RestaurantFood.FoodQuantityType,
					SelectedSpec:     selectedSpec,
				}

				comboItem := merchantResources.OrderFoodComboItems{
					ID:             item.ID,
					ComboID:        item.ComboID,
					FoodType:       item.FoodType,
					FoodID:         item.FoodID,
					Count:          item.Count,
					RestaurantFood: restaurantFood,
				}
				comboItems = append(comboItems, comboItem)
			}
		} else if detail.FoodType == models.RestaurantFoodsTypeSpec && detail.SelectedSpec != nil {
			// 如果是规格美食
			var specOptions []merchantResources.OrderRestaurantFoodSpecOption
			// 从数据库中获取规格信息
			for _, option := range detail.SelectedSpec.FoodSpecOptions {
				specOption := merchantResources.OrderRestaurantFoodSpecOption{
					ID:    option.ID,
					Name:  tools.If(trns.language == "zh", option.NameZh, option.NameUg),
					Price: option.Price,
					// 订单暂时不需要规格项分组
					//SpecOptionType: merchantResources.RestaurantFoodSpecType{
					//	ID:        option.FoodSpecType.ID,
					//	Name:      tools.If(trns.language == "zh", option.FoodSpecType.NameZh, option.FoodSpecType.NameUg),
					//	PriceType: option.FoodSpecType.PriceType,
					//},
				}
				specOptions = append(specOptions, specOption)
			}

			selectedSpec = &merchantResources.OrderRestaurantFoodSpec{
				ID:          detail.SelectedSpec.ID,
				SpecOptions: specOptions,
			}
		}

		// 构造 map ID
		mapId := fmt.Sprintf("%d", detail.StoreFoodsID)
		if detail.SelectedSpec != nil && detail.SelectedSpec.ID > 0 {
			mapId = fmt.Sprintf("%d_%d", detail.StoreFoodsID, detail.SelectedSpec.ID)
		}

		// 合并相同美食
		if existingDetail, exists := detailMap[mapId]; exists {
			existingDetail.Count += int(detail.Number)
			existingDetail.Price += price * float32(detail.Number)
			existingDetail.OriginalPrice += float32(detail.OriginalPrice * detail.Number)
			detailMap[mapId] = existingDetail
		} else {
			detailMap[mapId] = merchant.NewOrderDetail{
				FoodID:        detail.StoreFoodsID,
				OriginalPrice: float32(detail.OriginalPrice) * float32(detail.Number),
				Price:         price * float32(detail.Number),
				Count:         int(detail.Number),
				Image:         tools.AddCdn(detail.RestaurantFoods.Image),
				Name:          tools.If(trns.language == "zh", food.NameZh, food.NameUg),

				FoodType:       detail.FoodType,
				ComboFoodItems: comboItems,
				SelectedSpec:   selectedSpec,

				// 存储排序序号，防止在 detailMap 中丢失原有排序
				OrderNum: uint(i + 1),
			}
		}
	}
	for _, detail := range detailMap {
		newOrderDetail = append(newOrderDetail, detail)
	}

	// 对订单详情进行排序
	sort.Slice(newOrderDetail, func(i, j int) bool {
		// 如果 OrderNum 不为 0，则按照 OrderNum 排序
		if newOrderDetail[i].OrderNum != 0 && newOrderDetail[j].OrderNum != 0 {
			return newOrderDetail[i].OrderNum < newOrderDetail[j].OrderNum
		}

		// 然后按照 FoodID 排序
		if newOrderDetail[i].FoodID != newOrderDetail[j].FoodID {
			return newOrderDetail[i].FoodID < newOrderDetail[j].FoodID
		}

		// 如果 FoodID 相同，则按照价格排序
		return newOrderDetail[i].Price > newOrderDetail[j].Price
	})

	for i := 0; i < len(order.LunchBox); i++ {
		lunchBox := order.LunchBox[i]

		if lunchBox.Price > 0 {
			newOrderDetail = append(newOrderDetail, merchant.NewOrderDetail{
				FoodID:        0,
				OriginalPrice: (float32(lunchBox.OriginalPrice)) * float32(lunchBox.LunchBoxCount),
				Price:         (float32(lunchBox.Price)) * float32(lunchBox.LunchBoxCount),
				Count:         lunchBox.LunchBoxCount,
				Image:         tools.AddCdn("upload/restaurantfoods/202007/15/d49b559e40dc048ebb9817c93f4999b6.jpg"),
				Name:          lunchBox.Name,
			})
		}

	}
	return newOrderDetail
}

// getOrderStateDesc
// @Description: 根据订单状态获取状态名称
// @receiver trns
// @param state
// @param refundChannel
// @return string
func (trns *OrderV2Transformer) getOrderStateDesc(state int, refundChannel int) string {
	if state == 8 || state == 9 {
		return trns.getRefundChannel(refundChannel)
	} else {
		return trns.getOrderState(state)
	}
}

// getOrderState
// @Description: 根据订单状态获取状态名称
// @receiver trns
// @param state
// @return string
func (trns *OrderV2Transformer) getOrderState(state int) string {
	return trns.langUtil.TArr("order_state")[state]
}

// getRefundChannel
// @Description: 根据退款类型编号，获取退款类型名称
// @receiver trns
// @param refundChannel
// @return string
func (trns *OrderV2Transformer) getRefundChannel(refundChannel int) string {
	return trns.langUtil.TArr("order_refund_channel")[refundChannel]
}

// FormatRestaurantPersonal
//
//	@Description: 给客户端返回餐厅信息
//	@receiver trns
//	@param result
//	@param count
//	@return map[string]interface{}
func (trns *OrderV2Transformer) FormatRestaurantPersonal(result models.Restaurant, count int64) map[string]interface{} {
	data := map[string]interface{}{
		"restaurant_id":     result.ID,
		"restaurant_name":   result.Name,
		"restaurant_logo":   tools.If(strings.HasPrefix(result.Logo, "http"), result.Logo, configs.MyApp.CdnUrl+result.Logo),
		"new_comment_count": count,
		"state":             result.State,
		"cash_platform":     result.CashPlatform,
	}
	return data
}

// FormatRestaurantOrderList
//
//	@Time 2025-05-29 11:37:45
//	<AUTHOR>
//	@Description: 序列化商家订单列表
//	@receiver t *OrderV2Transformer
//	@param list
func (trns *OrderV2Transformer) FormatRestaurantOrderList(
	ctx *gin.Context, list []models.OrderToday, receive_order_time_limit int64, showCustomerInfo bool, orderType string,
) merchant.NewOrderEntity {
	var order_cancel_time_left int64 = 2

	data := merchant.NewOrderEntity{}
	orderlists := []merchant.NewOrderList{}
	orderlist := merchant.NewOrderList{}
	groupPrefix := "group_"
	adminType := "_restaurant_count"
	for _, v := range list {
		key := groupPrefix + tools.ToString(v.ID) + adminType
		orderlist.OrderTime = v.OrderTime
		orderlist.ID = v.ID
		orderlist.SerialNumber = int(v.SerialNumber)
		orderlist.Name = v.Name

		price := (tools.ToFloat32(v.Price) + tools.ToFloat32(v.LunchBoxFee))
		if v.OrderPriceRes > 0 {
			price = tools.ToFloat32(v.OrderPriceRes)
		}
		orderlist.Price = price

		orderlist.OrderNo = v.OrderID

		orderlist.OrderTime = v.OrderTime
		orderlist.Timezone = v.Timezone
		orderlist.ConsumeType = v.ConsumeType
		orderlist.DisplayShipment = v.Restaurant.DisplayShipment
		orderlist.DisplayMerchantProfit = v.Restaurant.DisplayMerchantProfit
		if v.Restaurant.DisplayShipment == 1 {
			orderlist.Shipment = tools.ToFloat64(v.Shipment)
		} else {
			orderlist.Shipment = tools.ToFloat64(0)
		}
		orderlist.OriginalShipment = tools.ToFloat64(v.OriginalShipment)
		orderlist.ResIncome = tools.ToFloat64(v.ResIncome)
		orderlist.OriginalPrice = tools.ToFloat64(v.OriginalPrice)

		if v.Shipper.ID != 0 {
			orderlist.ShipperMobile = v.Shipper.Mobile
			orderlist.ShipperName = v.Shipper.RealName
		}
		orderlist.BookingTime = carbon.Parse(v.BookingTime).Format("H:i")
		orderlist.BookingDateTime = carbon.Parse(v.BookingTime).Format("Y-m-d H:i:00")

		if trns.language == "ug" {
			orderlist.CityName = v.AddressView.CityNameUg
			orderlist.AreaName = v.AddressView.AreaNameUg
			orderlist.StreetName = v.AddressView.StreetNameUg
			orderlist.BuildingName = v.AddressView.BuildingNameUg
		} else {
			orderlist.CityName = v.AddressView.CityNameZh
			orderlist.AreaName = v.AddressView.AreaNameZh
			orderlist.StreetName = v.AddressView.StreetNameZh
			orderlist.BuildingName = v.AddressView.BuildingNameZh
		}

		orderlist.OrderNote = v.Description
		orderlist.SendNotify = v.SendNotify
		if v.RefundChanel == 0 {
			orderlist.OrderStateDes = trns.getOrderState(v.State)
		} else {
			orderlist.OrderStateDes = trns.getOrderStateDesc(v.State, v.RefundChanel)
		}
		orderlist.OrderState = v.State
		orderlist.OrderType = v.OrderType
		if v.OrderType == 1 {

			orderPayTime := carbon.Time2Carbon(v.PayTime)

			orderlist.Now = int(carbon.Now("Asia/Shanghai").Timestamp())
			orderlist.OrderTimeoutTime = carbon.Time2Carbon(v.PayTime).AddMinutes(int(receive_order_time_limit + order_cancel_time_left)).ToTimeString()
			orderlist.OrderTimeoutTimestamp = int(orderPayTime.AddMinutes(int(receive_order_time_limit + order_cancel_time_left)).Timestamp())
			orderlist.OrderTimeLeft = int(carbon.Now("Asia/Shanghai").DiffInSeconds(orderPayTime.AddMinutes(int(receive_order_time_limit + order_cancel_time_left))))
		}
		//商家端新订单,已接订单  显示 满减/减配送费 等活动标志和金额
		orderlist.IsMarketing = tools.If(len(v.MarketingList) > 0, 1, 0)
		orderlist.MarketingList = []merchant.MarketingList{}
		orderlist.IsCoupon = tools.If(v.Coupon.ID > 0, 1, 0)
		for _, info := range v.MarketingList {
			image := ""
			switch info.Type {
			case 1: //满减
				image = strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/order_detail_price_discount_" + trns.language + ".png"
			case 2: //减配送费
				image = strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/order_detail_shipment_discount_icon_" + trns.language + ".png"
			}
			marketInfo := merchant.MarketingList{
				MarketingName:         info.Name,
				MarketingReductionFee: tools.ToFloat32(info.StepReduce),
				MarketingImage:        image,
			}
			orderlist.MarketingList = append(orderlist.MarketingList, marketInfo)
		}
		if v.Coupon.ID > 0 {
			orderlist.IsMarketing = 1
			marketInfo := merchant.MarketingList{
				MarketingName:         trns.langUtil.T("coupon"),
				MarketingReductionFee: tools.ToFloat32(v.Coupon.Price),
				MarketingImage:        strings.TrimRight(configs.MyApp.CdnUrl, "/") + "/images/shipment-icons/merchant_home_coupon_" + trns.language + ".png",
			}
			orderlist.MarketingList = append(orderlist.MarketingList, marketInfo)
		}

		orderlist.ShowCustomerInfo = showCustomerInfo
		if showCustomerInfo {
			orderlist.Mobile = v.Mobile
			orderlist.RealMobile = v.Mobile
			orderlist.OrderAddress = v.OrderAddress
		} else {
			orderlist.Mobile = "***********"
			orderlist.RealMobile = "***********"
			orderlist.OrderAddress = "***********"
			orderlist.BuildingName = "***********"
			orderlist.StreetName = "***********"
			orderlist.Name = "***********"
		}
		if v.DeliveryType == 2 && len(v.Mobile) > 0 {
			orderlist.ShowCustomerInfo = true
			orderlist.Mobile = v.Mobile
			orderlist.Mobile = v.Mobile
			orderlist.RealMobile = v.Mobile
			orderlist.OrderAddress = v.OrderAddress
		}
		orderlist.MsgCount = tools.GetGroupChatMsgCount(key)
		orderlist.DeliveryType = v.DeliveryType
		orderlist.NewOrderDetail = trns.formatOrderDetail(v)
		orderlist.FoodsReadyTime = v.GetFoodsReadyTime()
		orderlist.FoodsReadyRemainMinute = v.FoodsReadyRemainMinute()

		// 处理部分退款信息
		orderlist.OrderPartRefundList = trns.processOrderPartRefund(ctx, v, orderType)
		orderlists = append(orderlists, orderlist)

	}
	data.NewOrderList = orderlists
	data.ServerTime = int(time.Now().Unix())
	return data
}

// 处理部分退款信息 包括 新订单/已接收订单/已完成订单/退款订单
func (trns *OrderV2Transformer) processOrderPartRefund(ctx *gin.Context, order models.OrderToday, orderType string,
) merchantResources.OrderPartRefundInfo {
	var (
		foodSpecTrns = transformers.NewFoodSpecTransformer(ctx)
	)
	// 部分退款信息
	var orderPartRefundInfo merchant.OrderPartRefundInfo
	// 部分退款列表
	var orderPartRefundList []merchant.OrderPartRefund
	// 部分退款 金额 包括餐费和打包费
	var orderPartRefundPrice float64
	// 用于保存最后一个退款渠道和原因
	var lastPartRefund models.OrderPartRefund
	// 定义是否有部分退款
	hasPartRefund := false
	// 遍历获取是否有部分退款
	for _, partRefund := range order.OrderPartRefund {
		// 如果有部分退款
		if partRefund.PartRefundType == models.PartRefundTypePart {
			hasPartRefund = true
		}
	}

	// 如果有部分退款信息 且 不是全部退款订单 那就显示部分退款信息
	if hasPartRefund && orderType != "Canceled" {
		for _, partRefund := range order.OrderPartRefund {
			if partRefund.PartRefundType != models.PartRefundTypePart {
				continue
			}

			for _, partRefundDetail := range partRefund.OrderPartRefundDetail {
				if partRefundDetail.Type != models.PartRefundTypePart {
					continue
				}
				// 保存订单
				orderPartRefund := merchant.OrderPartRefund{
					Name:          tools.GetNameByLang(partRefundDetail.RestaurantFoods, trns.language),
					Count:         tools.ToInt(partRefundDetail.Number),
					Price:         tools.ToFloat32(partRefundDetail.Price * partRefundDetail.Number),
					Image:         tools.CdnUrl(partRefundDetail.RestaurantFoods.Image),
					OriginalPrice: tools.ToFloat32(partRefundDetail.OriginalPrice * partRefundDetail.Number),
					FoodID:        tools.ToInt(partRefundDetail.StoreFoodsId),
					RefundPrice:   float64(partRefundDetail.RefundPrice),

					FoodType:       partRefundDetail.FoodType,
					SpecID:         partRefundDetail.SpecID,
					ComboFoodItems: foodSpecTrns.GetComboItems(partRefundDetail.RestaurantFoods.ComboFoodItems),
					SelectedSpec:   foodSpecTrns.GetSelectedSpec(&partRefundDetail.SelectedSpec),
				}
				orderPartRefundList = append(orderPartRefundList, orderPartRefund)

				orderPartRefundPrice += float64(partRefundDetail.RefundPrice * partRefundDetail.Number)
				// 如果有饭盒
				if partRefundDetail.LunchBox.ID != 0 {
					var lunchBox merchant.OrderPartRefund
					lunchBox.Name = tools.GetNameByLang(partRefundDetail.LunchBox, trns.language)
					lunchBox.Count = tools.ToInt(partRefundDetail.LunchBoxCount)
					lunchBox.Price = tools.ToFloat32(partRefundDetail.LunchBoxFee * partRefundDetail.LunchBoxCount)
					lunchBox.Image = tools.AddCdn("upload/restaurantfoods/202007/15/d49b559e40dc048ebb9817c93f4999b6.jpg")
					lunchBox.OriginalPrice = tools.ToFloat32(partRefundDetail.LunchBoxFee * partRefundDetail.LunchBoxCount)
					lunchBox.FoodID = tools.ToInt(partRefundDetail.StoreFoodsId)
					lunchBox.RefundPrice = float64(partRefundDetail.LunchBoxRefundPrice)
					orderPartRefundList = append(orderPartRefundList, lunchBox)
					orderPartRefundPrice += float64(partRefundDetail.LunchBoxFee * partRefundDetail.LunchBoxCount)
				}
			}
			// 保存每次循环中的退款渠道和原因，但最终取最后一个
			lastPartRefund = partRefund

		}
		// 部分退款原因
		orderPartRefundInfo.OrderPartRefundReason = tools.If(
			lastPartRefund.PartRefundReasonText != "",
			lastPartRefund.PartRefundReasonText,
			tools.If(trns.language == "zh", lastPartRefund.RefundReason.NameZh, lastPartRefund.RefundReason.NameUg),
		)

		// 退款渠道
		orderPartRefundInfo.RefundChanelName = trns.langUtil.TArr("part_refund_chanel_name")[lastPartRefund.RefundChanel]
		orderPartRefundInfo.RefundChanel = lastPartRefund.RefundChanel

		// 部分退款方
		partRefundCreatorTypeInt := tools.ToInt(lastPartRefund.PartRefundCreatorType)
		orderPartRefundInfo.PartRefundCreatorName = trns.langUtil.TArr("part_refund_creator_name")[partRefundCreatorTypeInt]

		// 第一次部分退款 的数据 和 第二次 全退款的数据都写在这里面
		orderPartRefundInfo.OrderPartRefundList = orderPartRefundList
		orderPartRefundInfo.OrderPartRefundPrice = orderPartRefundPrice
	}

	return orderPartRefundInfo
}

