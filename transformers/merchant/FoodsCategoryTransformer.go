package merchant

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type FoodsCategoryTransformer struct {
	langUtil *lang.LangUtil
	language string
}
func NewFoodsCategoryTransformer(c *gin.Context) *FoodsCategoryTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	transformer := FoodsCategoryTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &transformer
}

func (t FoodsCategoryTransformer) ListFormat(foodsCategoryList []models.FoodsCategory) []map[string]interface{} {
	language := t.language
	items := make([]map[string]interface{},0)
	for _, foodsGroup := range foodsCategoryList {
		item := map[string]interface{}{
			"id":            foodsGroup.ID,
			"name":          tools.GetNameByLang(foodsGroup, language),
		}
		items = append(items, item)
	}
	return items
}
