package merchant

import (
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type CollectionInfoTransformer struct {
	langUtil *lang.LangUtil
	language string
}

// NewCollectionInfoTransformer
//
//	@Time 2023-01-12 11:58:57
//	<AUTHOR>
//	@Description: 商家信息收集Transformer初始化
//	@param c
//	@return *CollectionInfoTransformer
func NewCollectionInfoTransformer(c *gin.Context) *CollectionInfoTransformer {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	transformer := CollectionInfoTransformer{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &transformer
}

// FormatShopLicenseInfo

// @Time 2023-01-12 12:01:27
// <AUTHOR>
// @Description: 格式化信息
// @receiver t CollectionInfoTransformer
// @param info
func (t CollectionInfoTransformer) FormatShopLicenseInfo(info models.SelfSignMerchantInfo, bnf []models.SelfSignBnf, licenseImg models.SelfSignImages) map[string]interface{} {

	image := make(map[string]interface{})
	if licenseImg.ID == 0 {
		image = nil
	} else {
		image["id"] = licenseImg.ID
		image["restaurant_id"] = licenseImg.RestaurantID
		image["image_url"] = configs.MyApp.CdnUrl + licenseImg.MlzFilePath
		image["doc_type"] = licenseImg.DocType
	}

	data := make(map[string]interface{})
	if info.RegMerType == "00" { // 企业
		licStart := ""
		licEnd := ""
		shareStart := ""
		shareEnd := ""
		if info.ShopLicenseStart != nil {
			licStart = info.ShopLicenseStart.Format("2006-01-02")
		}
		if info.ShopLicenseEnd != nil {
			licEnd = info.ShopLicenseEnd.Format("2006-01-02")
		}
		if info.ShareholderIdcardStart != nil {
			shareStart = info.ShareholderIdcardStart.Format("2006-01-02")
		}
		if info.ShareholderIdcardEnd != nil {
			shareEnd = info.ShareholderIdcardEnd.Format("2006-01-02")
		}
		data["reg_mer_type"] = info.RegMerType
		data["shop_license_num"] = info.ShopLicenseNum
		data["shop_name"] = info.ShopName
		data["legal_name"] = info.LegalName
		data["reg_address"] = info.RegAddress
		data["reg_capital"] = info.RegCapital
		data["business_scope"] = info.BusinessScope
		zero := 0
		if info.ShopLicenseLimitedType == nil {
			zero = 0
		} else {
			zero = *info.ShopLicenseLimitedType
		}
		data["shop_license_limited_type"] = zero
		data["shop_license_start"] = licStart
		data["shop_license_end"] = tools.If(licEnd == "9999-12-31", "长期", licEnd)
		data["shareholder_name"] = info.ShareholderName
		data["shareholder_idcard"] = info.ShareholderIdcard
		data["shareholder_address"] = info.ShareholderAddress
		data["shareholder_idcard_limited_type"] = info.ShareholderIdcardLimitedType
		data["shareholder_idcard_start"] = shareStart
		data["shareholder_idcard_end"] = tools.If(shareEnd == "9999-12-31", "长期", shareEnd)
		res := make([]map[string]interface{}, 0)
		for _, v := range bnf {
			res = append(res, map[string]interface{}{
				"bnf_name":                v.BnfName,
				"bnf_idcard_num":          v.BnfIdcardNum,
				"bnf_address":             v.BnfAddress,
				"bnf_idcard_limited_type": v.BnfIdcardLimitedType,
				"bnf_idcard_start":        v.BnfIdcardStart.Format("2006-01-02"),
				"bnf_idcard_end":          tools.If(v.BnfIdcardEnd.Format("2006-01-02") == "9999-12-31", "长期", v.BnfIdcardEnd.Format("2006-01-02")),
			})
		}
		data["bnf"] = res
		data["shop_license_img"] = image

	} else if info.RegMerType == "01" { //商铺
		licStart := ""
		licEnd := ""
		if info.ShopLicenseStart != nil {
			licStart = info.ShopLicenseStart.Format("2006-01-02")
		}
		if info.ShopLicenseEnd != nil {
			licEnd = info.ShopLicenseEnd.Format("2006-01-02")
		}
		data["reg_mer_type"] = info.RegMerType
		data["shop_license_num"] = info.ShopLicenseNum
		data["shop_name"] = info.ShopName
		data["legal_name"] = info.LegalName
		data["reg_address"] = info.RegAddress
		data["reg_capital"] = info.RegCapital
		data["business_scope"] = info.BusinessScope
		zero := 0
		if info.ShopLicenseLimitedType == nil {
			zero = 0
		} else {
			zero = *info.ShopLicenseLimitedType
		}
		data["shop_license_limited_type"] = zero
		data["shop_license_start"] = licStart
		data["shop_license_end"] = tools.If(licEnd == "9999-12-31", "长期", licEnd)
		data["shop_license_img"] = image
	} else if info.RegMerType == "02" {
		data["reg_mer_type"] = info.RegMerType
		data["shop_name"] = info.ShopName
		data["legal_name"] = info.LegalName
		data["shop_license_img"] = image
	}
	if image == nil {
		imageMap := make(map[string]interface{})
		imageMap["id"] = 0
		imageMap["restaurant_id"] = info.RestaurantId
		imageMap["image_url"] = configs.MyApp.CdnUrl + "merchant/holders/prefect_normal.png"
		if info.RegMerType == "02" {
			imageMap["doc_type"] = "0016"
		} else {
			imageMap["doc_type"] = "0002"
		}

		data["shop_license_img"] = imageMap
	}

	return data
}

func (t CollectionInfoTransformer) FormatIdCardInfo(idCardInfo models.SelfSignMerchantInfo, idcardsImg []models.SelfSignImages) map[string]interface{} {
	images := make([]map[string]interface{}, 3)
	// 照片按身份证正面，身份证反面，手持照循序填充
	if len(idcardsImg) > 0 {
		for _, value := range idcardsImg {
			if value.DocType == "0001" {
				images[0] = map[string]interface{}{
					"id":         value.ID,
					"restaurant": value.RestaurantID,
					"image_url":  configs.MyApp.CdnUrl + value.MlzFilePath,
					"doc_type":   value.DocType,
				}
			}
			if value.DocType == "0011" {
				images[1] = map[string]interface{}{
					"id":         value.ID,
					"restaurant": value.RestaurantID,
					"image_url":  configs.MyApp.CdnUrl + value.MlzFilePath,
					"doc_type":   value.DocType,
				}
			}
			if value.DocType == "0007" {
				images[2] = map[string]interface{}{
					"id":         value.ID,
					"restaurant": value.RestaurantID,
					"image_url":  configs.MyApp.CdnUrl + value.MlzFilePath,
					"doc_type":   value.DocType,
				}
			}

		}
	}
	// 如果没有对应照片，则填充默认图片作为封面
	if images[0] == nil {
		images[0] = map[string]interface{}{
			"id":         1,
			"restaurant": idCardInfo.RestaurantId,
			"image_url":  configs.MyApp.CdnUrl + "merchant/holders/identify_person_ug.png",
			"doc_type":   "0001",
		}
	}
	if images[1] == nil {
		images[1] = map[string]interface{}{
			"id":         2,
			"restaurant": idCardInfo.RestaurantId,
			"image_url":  configs.MyApp.CdnUrl + "merchant/holders/identify_flag_ug.png",
			"doc_type":   "0011",
		}
	}
	if images[2] == nil {
		images[2] = map[string]interface{}{
			"id":         3,
			"restaurant": idCardInfo.RestaurantId,
			"image_url":  configs.MyApp.CdnUrl + "merchant/holders/holding_id_card.png",
			"doc_type":   "0007",
		}
	}

	data := make(map[string]interface{})
	data["mer_idcard_name"] = idCardInfo.MerIdcardName
	data["mer_is_bnf"] = idCardInfo.MerIsBnf
	data["mer_idcard_num"] = idCardInfo.MerIdcardNum
	data["mer_mobile"] = idCardInfo.MerMobile
	data["mer_sex"] = idCardInfo.MerSex

	idLimitType := 0
	cEnd := idCardInfo.MerIdcardEnd

	idcardEnd := ""
	if cEnd != nil {
		idcardEnd = cEnd.Format("2006-01-02")
		if idcardEnd == "9999-12-31" {
			idLimitType = 1
			idcardEnd = "长期"
		}
	}

	midStartStr := ""
	midStart := idCardInfo.MerIdcardStart

	if midStart != nil {
		midStartStr = midStart.Format("2006-01-02")
	}

	data["mer_idcard_limited_type"] = idLimitType
	data["mer_idcard_start"] = midStartStr
	data["mer_idcard_end"] = idcardEnd
	data["legal_home_address"] = idCardInfo.LegalmanHomeAddr
	data["mer_idcard_img"] = images
	return data
}

// FormatAccountInfo
//
//	@Time 2023-01-12 16:49:22
//	<AUTHOR>
//	@Description: 格式化信息
//	@receiver t CollectionInfoTransformer
//	@param accountInfo
//	@param bankCardsImg
//	@return interface{}
func (t CollectionInfoTransformer) FormatAccountInfo(accountInfo models.SelfSignMerchantInfo, bankCardsImg []models.SelfSignImages, bankNames map[string]interface{}) interface{} {
	images := make([]map[string]interface{}, 0)
	if len(bankCardsImg) == 0 {
		images = nil
	} else {
		for _, value := range bankCardsImg {
			images = append(images, map[string]interface{}{
				"id":         value.ID,
				"restaurant": value.RestaurantID,
				"image_url":  configs.MyApp.CdnUrl + value.MlzFilePath,
				"doc_type":   value.DocType,
			})
		}
	}

	data := make(map[string]interface{})
	data["bank_acct_type"] = accountInfo.BankAcctType
	data["bank_acct_num"] = accountInfo.BankAcctNum
	data["bank_name"] = bankNames["name"]
	data["bank_name_zh"] = bankNames["name_zh"]
	data["bank_id"] = accountInfo.BankId
	provinceId := accountInfo.BankProvinceId
	if accountInfo.BankProvinceId == 0 {
		provinceId = 65
	}
	data["bank_province_id"] = provinceId
	data["bank_city_id"] = accountInfo.BankCityId
	data["bank_area_id"] = accountInfo.BankAreaId
	data["bank_branch_name"] = accountInfo.BankBranchName
	data["bank_branch_code"] = accountInfo.BankBranchCode
	data["bank_bind_mobile"] = accountInfo.BankBindMobile
	data["bank_card_img"] = images
	data["reg_mer_type"] = accountInfo.RegMerType

	if images == nil {
		imageMaps := make([]map[string]interface{}, 0)

		bt := int(0)
		if accountInfo.BankAcctType == nil {
			imageMap := make(map[string]interface{})
			imageMap["id"] = 1
			imageMap["restaurant_id"] = accountInfo.RestaurantId
			imageMap["image_url"] = configs.MyApp.CdnUrl + "merchant/holders/identify_person_ug.png"
			imageMap["doc_type"] = "0025"
			imageMaps = append(imageMaps, imageMap)

			imageMap = make(map[string]interface{})
			imageMap["id"] = 2
			imageMap["restaurant_id"] = accountInfo.RestaurantId
			imageMap["image_url"] = configs.MyApp.CdnUrl + "merchant/holders/identify_person_ug.png"
			imageMap["doc_type"] = "0026"
			imageMaps = append(imageMaps, imageMap)

			imageMap = make(map[string]interface{})
			imageMap["id"] = 3
			imageMap["restaurant_id"] = accountInfo.RestaurantId
			imageMap["image_url"] = configs.MyApp.CdnUrl + "merchant/holders/identify_person_ug.png"
			imageMap["doc_type"] = "0006"
			imageMaps = append(imageMaps, imageMap)

		} else if accountInfo.BankAcctType == &bt { // 银行卡

			imageMap := make(map[string]interface{})
			imageMap["id"] = 1
			imageMap["restaurant_id"] = accountInfo.RestaurantId
			imageMap["image_url"] = configs.MyApp.CdnUrl + "merchant/holders/identify_person_ug.png"
			imageMap["doc_type"] = "0025"
			imageMaps = append(imageMaps, imageMap)

			imageMap = make(map[string]interface{})
			imageMap["id"] = 2
			imageMap["restaurant_id"] = accountInfo.RestaurantId
			imageMap["image_url"] = configs.MyApp.CdnUrl + "merchant/holders/identify_person_ug.png"
			imageMap["doc_type"] = "0026"
			imageMaps = append(imageMaps, imageMap)

			imageMap = make(map[string]interface{})
			imageMap["id"] = 3
			imageMap["restaurant_id"] = accountInfo.RestaurantId
			imageMap["image_url"] = configs.MyApp.CdnUrl + "merchant/holders/identify_person_ug.png"
			imageMap["doc_type"] = "0006"
			imageMaps = append(imageMaps, imageMap)

		} else { // 开户许可证

			imageMap := make(map[string]interface{})
			imageMap["id"] = 1
			imageMap["restaurant_id"] = accountInfo.RestaurantId
			imageMap["image_url"] = configs.MyApp.CdnUrl + "merchant/holders/identify_person_ug.png"
			imageMap["doc_type"] = "0006"
			imageMaps = append(imageMaps, imageMap)

		}

		data["bank_card_img"] = imageMaps

	}

	return data
}

func (t CollectionInfoTransformer) FormatShopInfo(shopInfo models.SelfSignMerchantInfo, shopImages []models.SelfSignImages, categoryList []map[string]interface{}) interface{} {
	images := make([]map[string]interface{}, 0)
	if len(shopImages) == 0 {
		images = nil
	} else {
		for _, value := range shopImages {
			images = append(images, map[string]interface{}{
				"id":         value.ID,
				"restaurant": value.RestaurantID,
				"image_url":  configs.MyApp.CdnUrl + value.MlzFilePath,
				"doc_type":   value.DocType,
			})
		}
	}

	data := make(map[string]interface{})

	if shopInfo.State == 0 && len(shopInfo.ShopBusinessName) == 0 { //首次进入第四页 自动填 店铺名称和地址
		shopInfo.ShopBusinessName = shopInfo.ShopName
		shopInfo.ShopBusinessAddress = shopInfo.RegAddress
	}
	data["shop_business_name"] = shopInfo.ShopBusinessName
	data["shop_business_province_id"] = shopInfo.ShopBusinessProvinceID
	data["shop_business_city"] = shopInfo.ShopBusinessCityID
	data["shop_business_area"] = shopInfo.ShopBusinessCountryID
	data["shop_business_address"] = shopInfo.ShopBusinessAddress
	data["category_code"] = shopInfo.ShopCategoryCode
	//data["pay_product"] = strings.Split(shopInfo.PayProduct, ",")
	data["shop_images"] = images
	data["category_list"] = categoryList

	if images == nil {
		imageMaps := make([]map[string]interface{}, 0)

		//"0015", "0005"

		imageMap := make(map[string]interface{})
		imageMap["id"] = 1
		imageMap["restaurant_id"] = shopInfo.RestaurantId
		imageMap["image_url"] = configs.MyApp.CdnUrl + "merchant/holders/prefect_normal.png"
		imageMap["doc_type"] = "0015"
		imageMaps = append(imageMaps, imageMap)

		imageMap = make(map[string]interface{})
		imageMap["id"] = 2
		imageMap["restaurant_id"] = shopInfo.RestaurantId
		imageMap["image_url"] = configs.MyApp.CdnUrl + "merchant/holders/prefect_normal.png"
		imageMap["doc_type"] = "0005"
		imageMaps = append(imageMaps, imageMap)

		data["shop_images"] = imageMaps

	}

	return data
}

// FormatAdministratorInfo
//
//	@Time 2023-01-13 10:20:00
//	<AUTHOR>
//	@Description: 格式化信息
//	@receiver t CollectionInfoTransformer
//	@param shopInfo
//	@param SelfSignImages
//	@return interface{}
func (t CollectionInfoTransformer) FormatAdministratorInfo(shopInfo models.SelfSignMerchantInfo, SelfSignImages []models.SelfSignImages) interface{} {
	if shopInfo.ShopManagerType == 1 {
		data := make(map[string]interface{})
		data["shop_manager_type"] = shopInfo.ShopManagerType
		data["shop_manager_name"] = shopInfo.MerIdcardName
		data["shop_manager_idcard"] = shopInfo.MerIdcardNum
		data["shop_manager_mobile"] = shopInfo.MerMobile
		data["shop_manager_email"] = shopInfo.ShopManagerEmail
		idcardImgs := make([]map[string]interface{}, 0)
		otherImgs := make([]map[string]interface{}, 0)
		if len(SelfSignImages) == 0 {
			idcardImgs = nil
			otherImgs = nil
		} else {
			for _, value := range SelfSignImages {
				if value.DocType == "0099" {
					otherImgs = append(otherImgs, map[string]interface{}{
						"id":         value.ID,
						"restaurant": value.RestaurantID,
						"image_url":  configs.MyApp.CdnUrl + value.MlzFilePath,
						"doc_type":   value.DocType,
					})
				}

			}
		}
		data["other_imgs"] = otherImgs
		data["idcard_imgs"] = idcardImgs
		return data
	} else {
		data := make(map[string]interface{})
		data["shop_manager_type"] = shopInfo.ShopManagerType
		data["shop_manager_name"] = shopInfo.ShopManagerName
		data["shop_manager_idcard"] = shopInfo.ShopManagerIdcard
		data["shop_manager_mobile"] = shopInfo.ShopManagerMobile
		data["shop_manager_email"] = shopInfo.ShopManagerEmail
		idcardImgs := make([]map[string]interface{}, 0)
		otherImgs := make([]map[string]interface{}, 0)
		if len(SelfSignImages) == 0 {
			otherImgs = nil
			idcardImgs = nil
		} else {
			for _, value := range SelfSignImages {
				if value.DocType == "0099" {
					otherImgs = append(otherImgs, map[string]interface{}{
						"id":         value.ID,
						"restaurant": value.RestaurantID,
						"image_url":  configs.MyApp.CdnUrl + value.MlzFilePath,
						"doc_type":   value.DocType,
					})
				} else {
					idcardImgs = append(idcardImgs, map[string]interface{}{
						"id":         value.ID,
						"restaurant": value.RestaurantID,
						"image_url":  configs.MyApp.CdnUrl + value.MlzFilePath,
						"doc_type":   value.DocType,
					})
				}
			}
		}
		data["other_imgs"] = otherImgs
		data["idcard_imgs"] = idcardImgs
		return data
	}

}

// FormatBusinessLicenseInfo
//
//	@Time 2023-01-13 10:29:09
//	<AUTHOR>
//	@Description: 格式化信息
//	@receiver t CollectionInfoTransformer
//	@param shopInfo
//	@param images
//	@return interface{}
func (t CollectionInfoTransformer) FormatBusinessLicenseInfo(shopInfo models.SelfSignMerchantInfo, images models.SelfSignImages) interface{} {
	data := make(map[string]interface{})
	permitStartDate := ""
	permitEndDate := ""
	if shopInfo.PermitStartDate != nil {
		permitStartDate = shopInfo.PermitStartDate.Format("2006-01-02")
	}
	if shopInfo.PermitEndDate != nil {
		permitEndDate = shopInfo.PermitEndDate.Format("2006-01-02")
	}

	data["permit_start_date"] = permitStartDate
	data["permit_end_date"] = permitEndDate
	data["permit_shop_name"] = shopInfo.PermitShopName
	data["permit_lic_num"] = shopInfo.PermitLicNum
	data["permit_credit_code"] = shopInfo.PermitCreditCode
	data["permit_legal_name"] = shopInfo.PermitLegalName
	data["permit_shop_addr"] = shopInfo.PermitShopAddr
	data["permit_business_premises"] = shopInfo.PermitBusinessPremises
	data["permit_state"] = shopInfo.PermitState
	data["permit_business_type"] = shopInfo.PermitBusinessType
	data["permit_supervise_org"] = shopInfo.PermitSuperviseOrg
	data["permit_supervise_managers"] = shopInfo.PermitSuperviseManagers
	data["permit_issuing_authority"] = shopInfo.PermitIssuingAuthority
	if images.ID == 0 {

		imageMap := make(map[string]interface{})
		imageMap["id"] = 0
		imageMap["restaurant_id"] = shopInfo.RestaurantId
		imageMap["image_url"] = configs.MyApp.CdnUrl + "merchant/holders/prefect_normal.png"
		imageMap["doc_type"] = "1001"

		data["business_license_img"] = imageMap
	} else {
		data["business_license_img"] = map[string]interface{}{
			"id":         images.ID,
			"restaurant": images.RestaurantID,
			"image_url":  configs.MyApp.CdnUrl + images.MlzFilePath,
			"doc_type":   images.DocType,
		}
	}
	return data
}

func (t CollectionInfoTransformer) FormatStuffInfo(stuffs []models.SelfSignStuff) interface{} {
	data := make([]map[string]interface{}, 0)
	if len(stuffs) == 0 {
		data = nil
	} else {
		for _, value := range stuffs {
			hsStart := ""
			hsEnd := ""
			if value.HealthCertificateStart != nil {
				hsStart = value.HealthCertificateStart.Format("2006-01-02")
			}
			if value.HealthCertificateEnd != nil {
				hsEnd = value.HealthCertificateEnd.Format("2006-01-02")
			}
			data = append(data, map[string]interface{}{
				"id":                       value.Id,
				"staff_name":               value.StaffName,
				"staff_idcard":             value.StaffIdCard,
				"img_src":                  configs.MyApp.CdnUrl + value.HealthCertificateImage,
				"health_certificate_start": hsStart,
				"health_certificate_end":   hsEnd,
			})
		}
	}
	return data
}
