#!/bin/bash

# 开发环境管理脚本

case "$1" in
    "start")
        echo "启动开发环境..."
        docker-compose up -d
        echo "开发环境已启动！"
        echo "应用地址: http://localhost:8080"
        echo "MySQL: localhost:3306 (用户名: root, 密码: password)"
        echo "Redis: localhost:6379"
        echo ""
        echo "查看日志: ./dev.sh logs"
        echo "停止环境: ./dev.sh stop"
        ;;
    "stop")
        echo "停止开发环境..."
        docker-compose down
        echo "开发环境已停止！"
        ;;
    "restart")
        echo "重启开发环境..."
        docker-compose restart
        echo "开发环境已重启！"
        ;;
    "logs")
        if [ -z "$2" ]; then
            docker-compose logs -f
        else
            docker-compose logs -f "$2"
        fi
        ;;
    "build")
        echo "重新构建镜像..."
        docker-compose build --no-cache
        echo "镜像构建完成！"
        ;;
    "clean")
        echo "清理Docker资源..."
        docker-compose down -v
        docker system prune -f
        echo "清理完成！"
        ;;
    "shell")
        echo "进入应用容器..."
        docker-compose exec app sh
        ;;
    "mysql")
        echo "连接到MySQL..."
        docker-compose exec mysql mysql -u root -ppassword mulazim
        ;;
    "redis")
        echo "连接到Redis..."
        docker-compose exec redis redis-cli
        ;;
    *)
        echo "用法: $0 {start|stop|restart|logs|build|clean|shell|mysql|redis}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动开发环境"
        echo "  stop    - 停止开发环境"
        echo "  restart - 重启开发环境"
        echo "  logs    - 查看日志 (可指定服务名，如: ./dev.sh logs app)"
        echo "  build   - 重新构建镜像"
        echo "  clean   - 清理所有Docker资源"
        echo "  shell   - 进入应用容器shell"
        echo "  mysql   - 连接到MySQL数据库"
        echo "  redis   - 连接到Redis"
        exit 1
        ;;
esac
