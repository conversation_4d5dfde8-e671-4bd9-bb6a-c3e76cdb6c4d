package main

import (
	"github.com/fvbock/endless"
	"log"
	"mulazim-api/configs"
	"mulazim-api/controllers/cmd"
	"mulazim-api/inits"
	_ "mulazim-api/inits"
	"mulazim-api/jobs"
	"mulazim-api/mygin"
	"mulazim-api/observers"
	"mulazim-api/tools"
	_ "mulazim-api/tools"
)

func main() {
	if inits.RuntimeType == "job" {
		tools.Logger.Infof("wechat job running")
		job := jobs.NewSendWechatMiniMessageJob()
		job.StartConsumer()
		return
	}
	if inits.RuntimeType == "send_chat_msg_job" {
		tools.Logger.Infof("send_chat_msg_job 发送聊天室内容 job running")
		job := jobs.NewSendOrderChatDetailJob()
		job.StartConsumer()
		return
	}

	if inits.RuntimeType == "lakala-refund-job" {
		tools.Logger.Infof("lakala refund job running")
		job := jobs.NewLakalaRefundJob()
		job.StartConsumer()
		return
	}
	// 生成宣传材料代码队列
	if inits.RuntimeType == "advert-material-print-batch-job" {
		tools.Logger.Infof("lakala refund job running")
		job := jobs.NewAdvertMaterialPrintBatchJob()
		job.StartConsumer()
		return
	}
	if inits.RuntimeType == "push-job" {
		tools.Logger.Infof("push job running")
		job := jobs.NewPushJob()
		job.StartConsumer()
		return
	}

	if inits.RuntimeType == "order_first_push" {
		tools.Logger.Infof("order_first_push running")
		cmd.BeginReceivedOrderToPushTable()
		//更新 t_take_order 预计配送费
		// tools.Logger.Infof("order_shipping_price_update running")
		// cmd.UpdateTakeOrderShippingPrice()
		return
	}
	if inits.RuntimeType == "order_second_push" {
		tools.Logger.Infof("order_second_push running")
		cmd.PushOrderSecondTime()
		return
	}
	if inits.RuntimeType == "order_observe" {
		tools.Logger.Infof("order_observe running")
		observers.StartOrderObserver(inits.Action)
		tools.Logger.Infof("order_observe ended")
		return
	}
	//订单推送到 商家端 发出声音
	if inits.RuntimeType == "order_push_to_merchant1" {
		tools.Logger.Infof("order_push_to_merchant1 running")
		cmd.OrderPushToMerchant1()
		tools.Logger.Infof("order_push_to_merchant1 ended")
		return
	}
	//监测 商家端 是否在线 
	if inits.RuntimeType == "order_push_to_merchant1_job1" {
		tools.Logger.Infof("order_push_to_merchant1_job1 running")
		job := jobs.NewOrderPushToMerchant1Job()
		job.StartConsumer()
		return
	}
	//监测 商家端 是否已经完成 发出声音
	if inits.RuntimeType == "order_push_to_merchant1_job2" {
		tools.Logger.Infof("order_push_to_merchant1_job2 running")
		job := jobs.NewOrderPushToMerchant2Job()
		job.StartConsumer()
		return
	}

	//订单推送到 配送员端 发出声音 socket
	if inits.RuntimeType == "order_push_to_shipper1_job1" {
		tools.Logger.Infof("order_push_to_shipper1_job1 running")
		job := jobs.NewOrderPushToShipper1Job()
		job.StartConsumer()
		return
	}
	//订单推送到 配送端 发出声音 队列
	if inits.RuntimeType == "order_push_to_shipper1" {
		tools.Logger.Infof("order_push_to_shipper1 running")
		cmd.OrderPushToShipper1()
		tools.Logger.Infof("order_push_to_shipper1 ended")
		return
	}

	//拉卡拉主动查询任务
	if inits.RuntimeType == "lakala-query-job" {
		tools.Logger.Infof("lakala query job running")
		job := jobs.NewLakalaQueryJob()
		job.StartConsumer()
		return
	}
	if inits.RuntimeType == "food-import-job" {
		tools.Logger.Infof("food-import-job running")
		job := jobs.NewFoodImportJob()
		job.StartConsumer()
		return
	}

	r := mygin.InitGinWeb()
	port := configs.MyApp.Port
	tools.Logger.Infof("server run on %s", port)
	if err := endless.ListenAndServe(port, r); err != nil {
		log.Fatalf("listen: %s\n", err)
	}
	// r.Run("0.0.0.0:8085") // 监听并在 0.0.0.0:8080 上启动服务
}
