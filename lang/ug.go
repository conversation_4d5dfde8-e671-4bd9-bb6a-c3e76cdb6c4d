package lang

var Ug map[string]interface{}

func init() {
	Ug = map[string]interface{}{
		"msg":                  "مەشغۇلات مۇۋەپپەقىيەتلىك بولدى",
		"not_authorized":       "سىز تېخى كىرمەپسىز",
		"forbidden":            "ھوقۇقىڭىز يەتمەيدۇ",
		"rebind_error":         "مۇشۇ تېلېفون نومۇرى ئارقىلىق مۇلازىم ئەپچاقتىن زاكاز چۈشۈرۈپ بولغاندىن كېيىن باغلىسىڭىز بولىدۇ",
		"password_error":       "مەخپى نومۇر خاتا",
		"order_is_null":        "زاكاز خاتىرىسى يوق",
		"retry_after_3_second": "3 سېكونتتىن كېيىن قايتا سىناڭ",
		"order_state": map[int]string{
			1:  "يېڭى زاكاز",
			2:  "زاكاز مۇقىملاندى",
			3:  "قوبۇللاشنى ساقلاۋاتىدۇ",
			4:  "زاكاز قوبۇللاندى",
			6:  "يەتكۈزۈلۈۋاتىدۇ...",
			7:  "تاماملاندى",
			8:  "بىكار قىلىندى",
			9:  "ئاشخانا قايتۇرۇۋەتتى",
			10: "يەتكۈزۈش مەغلۇپ بولدى",
			5:  "زاكاز تەييارلىنىپ بولدى",
		},
		"order_refund_channel": map[int]string{
			1: "ئاشخانا قوبۇل قىلمىغانلىق سەۋەبىدىن سىستېما ئاپتوماتىك قايتۇرۇۋەتكەن",
			2: "باشقۇرغۇچى قايتۇرۇۋەتتى",
			3: "ئاشخانا قايتۇرۇۋەتتى",
			4: "خېرىدار قايتۇرۇۋەتتى",
		},
		"Username":      "ئەزا ئىسمى",
		"Limit":         "بەت چوڭلۇقى",
		"Page":          "بەت سانى",
		"Day":           "كۈن",
		"error_happend": "خاتالىق كۆرۈلدى",
		//"shipper": map[string]string{
		//	"already_taked": "بۇ زاكازنى تالىشىپ بولغان",
		//},
		"already_taked":                     "بۇ زاكازنى تالىشىپ بولغان",
		"self_taken_order":                  "بۇ خېرىدار ئېلىپ كېتىدىغان زاكاز، تالىشالمايسىز",
		"not_found":                         "مۇناسىۋەتلىك ئۇچۇر تېپىلمىدى",
		"failed":                            "مەشغۇلات مەغلۇپ بولدى",
		"order_not_found":                   "مۇناسىۋەتلىك زاكاز ئۇچۇرى يوق",
		"did_not_arrive_destination":        "سىزنىڭ خېرىدار تاماق بۇيرۇتقان ئادرېس بىلەن بولغان ئارىلىقىڭىز %0.1f مېتىر، زاكازنى تاماملاشقا بولمايدۇ",
		"change_state_fail":                 "زاكاز ھالىتى خاتا، ئاشخانا زاكازنى قوبۇل قىلمىغان",
		"overstep_time":                     "بۇ زاكاز قايتۇرۇش ۋاقتىدىن ئۆتۈپ كەتكەن",
		"already_backed":                    "بۇ زاكاز ئاللىبۇرۇن قايتۇرۇلغان",
		"back_order_failed":                 "زاكاز قايتۇرۇش مەغلۇپ بولدى",
		"admin_is_not_active":               "بۇ ئەزا تېخى ئاكتىپلانمىغان",
		"admin_is_not_shipper":              "بۇ ئەزا يەتكۈزگۈچى ئەمەسكەن",
		"admin_password_error":              "ئەزا ئىسمى ياكى مەخپىي نومۇرى خاتا بولۇپ قالدى",
		"order_state_error":                 "زاكاز ھالىتى خاتا",
		"order_extend_info_not_found":       "زاكاز قوشۇمچە ئۇچۇرى تېپىلمىدى",
		"order_already_update_arrived_shop": "سىز دۇكانغا بېرىش يوقلىمىسىدىن ئۆتۈپ بولغان",
		"order_cannot_repaid":               "زاكازغا تەكرار پۇل تۆلىگىلى بولمايدۇ",
		"client_param_error":                "پارامېتىر خاتا",
		"order_sending_finish":              "بۇ زاكاز ئاللىبۇرۇن يەتكۈزۈلۈپ بولغان",
		"order_cancel":                      "بۇ زاكاز ئاللىبۇرۇن بىكار قىلىنغان",
		"order_seized":                      "يەتكۈزگۈچى تالىشىپ بولغان زاكازنى قايتۇرالمايسىز",
		"order_receive_refuse":              "بۇ زاكاز ئاللىبۇرۇن رەت قىلىنغان",
		"order_sending_fail":                "بۇ زاكاز ئاللىبۇرۇن مەغلۇپ بولغان",
		"merchant_role_id_error":            "role id چوقۇم 1, 2, 3 بولۇشى كېرەك",
		"merchant_password_error":           "مەخپىي نومۇر خاتا",
		"merchant_id_not_found":             "ئاشخانا باشقۇرغۇچىسى نومۇرى تېپىلمىدى",
		"merchant_not_found":                "ئاشخانا ئۇچۇرى تېپىلمىدى",
		"generate_merchant_qrCode_failed":   "ئىككىلىك كود شەكىللەندۈرۈش مەغلۇپ بولدى",
		"shipper_failed_to_locate_location": "ئورۇن بەلگىلەش مەغلۇپ بولدى، ئەپنىڭ ئورۇن بەلگىلەش ھوقۇقىنى ئېچىپ، قايتا سىناڭ",
		"shopper_working_off":               "سىز ئارام ئېلىش ھالىتىدە ئىكەنسىز، زاكاز تالىشالمايسىز، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ",
		"cashout_amount_error":              "چىقىرىدىغان پۇل سوممىسىدا خاتالىق بار",
		"cashout_bill_id_not_found":         "پۇل چىقىرىدىغان ھېسابات نومۇرى تېپىلمىدى",
		"pay_type_app_pay_desctription":     "يەتكۈزگۈچى ئەپتە تۆلىگەن",
		"pay_type_qr_code_desctription":     "خېرىدار ئىككىلىك كود ئارقىلىق تۆلىگەن",
		"pay_type_sms_desctription":         "خېرىدار قىسقا ئۇچۇردا تۆلىگەن",
		"fail":                              " مەغلۇپ بولدى",
		"success":                           "مەشغۇلات مۇۋەپپەقىيەتلىك بولدى",
		"attendance-limit":                  "بىر مىنۇت ئىچىدە پەقەت بىر قېتىملا يوقلىمىدىن ئۆتكىلى بولىدۇ",
		"personally-attendance":             "ئۆزۈم",

		"sms_in_two_minutes": "ئىككى مىنۇتتىن كېيىن قايتا سىناڭ",
		"mobile_incorrect":   "تېلېفون نومۇرى توغرا ئەمەس",
		"captcha_incorrect":  "دەلىللەش كودىى توغرا ئەمەس ",
		"captcha_time_out":   "دەلىللەش كودىنىڭ ۋاقتى ئۆتۈپ كەتتى",
		// 信息收集
		"RegMerType":                   "تىزىمغا ئالدۇرۇش تىپى",
		"RegCapital":                   "تىزىمغا ئالدۇرغان مەبلەغ ",
		"ShopLicenseNum":               "تىجارەت كىنىشكىسى نومۇرى ",
		"RegAddress":                   "تىزىمغا ئالدۇرۇش ئادرېسى ",
		"BusinessScope":                "تىجارەت دائىرىسى",
		"ShopName":                     "دۇكان نامى",
		"LegalName":                    "قانۇنىي ۋەكىل ئىسمى",
		"ShopLicenseLimitedType":       "تىجارەت كىنىشكىسىنىڭ ئۈنۈملۈك مۇددىتى تىپى",
		"ShopLicenseStart":             "تىجارەت كىنىشكىسىنىڭ ئۈنۈملۈك ۋاقتى باشلىنىش ۋاقتى",
		"ShopLicenseEnd":               "تىجارەت كىنىشكىسىنىڭ ئۈنۈملۈك ۋاقتى ئاخىرلىشىش ۋاقتى",
		"ShareholderName":              "پايچىكنىڭ ئىسىم - فامىلىسى",
		"ShareholderIdcard":            "پايچىك كىملىك نومۇرى",
		"ShareholderAddress":           "پايچىك ئادرېسى",
		"ShareholderIdcardLimitedType": "پايچىك كىملىكىنىڭ كۈچكە ئىگە مۇددىتى تىپى",
		"ShareholderIdcardStart":       "پايچىك كىملىكىنىڭ كۈچكە ئىگە ۋاقتى",
		"ShareholderIdcardEnd":         "پايچىك كىملىكىنىڭ ئاخىرلىشىش ۋاقتى",
		"Captcha":                      "دەلىللەش كودى",
		"MerIdcardName":                "كىملىكتىكى ئىسمى",
		"MerIdcardNum":                 "كىملىك نومۇرى",
		"MerIdcardStart":               "كىملىك ئۈنۈملۈك ۋاقتى باشلىنىش ۋاقتى",
		"MerIdcardEnd":                 "كىملىك ئۈنۈملۈك ۋاقتى ئاخىرلىشىش ۋاقتى",
		"MerIdcardTimeType":            "كىملىك ئۈنۈملۈك مۇددىتى تىپى",
		"MerMobile":                    "تېلېفون نومۇرى",
		"MerIsBnf":                     "كارخانا ئىگىسىمۇ",
		"MerSex":                       "جىنسى",
		"BankAcctNum":                  "بانكا كارتا نومۇرى",
		"BankName":                     "بانكا نامى",
		"BankProvinceId":               "بانكا تەۋە ئۆلكە ئاپتونۇم رايون",
		"BankCityId":                   "بانكا تەۋە شەھەر",
		"BankAreaId":                   "بانكا تەۋە رايون",
		"BankBranchName":               "بانكا تەۋە نامى",
		"BankBranchCode":               "بانكا نومۇرى",
		"BankBindMobile":               "بانكا كارتىسىنىڭ تېلېفون نومۇرى",
		"ShopBusinessName":             "دۇكاننىڭ ۋىۋىسكىدىكى نامى",
		"ShopBusinessCity":             "دۇكان تەۋە شەھەر",
		"ShopBusinessArea":             "دۇكان تەۋە رايون",
		"ShopBusinessAddress":          "دۇكان ئادرېسى",
		"ShopCategoryCode":             "دۇكان تۈرى",
		"ShopManagerType":              "دۇكان باشقۇرغۇچى تىپى",
		"ShopManagerName":              "دۇكان باشقۇرغۇچىسى ئىسىمى",
		"ShopManagerIdcard":            "دۇكان باشقۇرغۇچىسى كىملىك نومۇرى",
		"ShopManagerMobile":            "دۇكان باشقۇرغۇچىسى تېلېفون نومۇرى",
		"ShopManagerEmail":             "دۇكان باشقۇرغۇچىسى ئېلخەت ئادرېسى",

		"not_idcard_front":         "كىملىكنىڭ ئادەم سۈرىتى بار يۈزىنى يوللاڭ",
		"not_idcard_back":          "كىملىكنىڭ دۆلەت گېربى بار يۈزىنى يوللاڭ",
		"del_mer_stuf_fail":        "خىزمەتچى ئۇچۇرىنى ئۆچۈرۈش مەغلۇپ بولدى",
		"data_not_found":           "ئۇچۇر تېپىلمىدى",
		"File":                     "ھۆججەت",
		"Id":                       "ID",
		"StaffName":                "خىزمەتچى ئىسمى",
		"StaffIdcard":              "خىزمەتچى كىملىك نومۇرى",
		"HealthCertificateImage":   "ساغلاملىق كىنىشكىسى",
		"HealthCertificateStart":   "ساغلاملىق كىنىشكىسى كۈچكە ئىگە ۋاقتى",
		"HealthCertificateEnd":     "ساغلاملىق كىنىشكىسى كۈچتىن قالىدىغان ۋاقتى",
		"INVALID_BUSINESS_LICENSE": "ئىناۋەتسىز تىجارەت كىنىشكىسى",
		"captcha_requiered":        "دەلىللەش كودىنى تولدۇرۇڭ",

		"save_img_fail": "رەسىم ساقلاش مەغلۇپ بولدى",
		"file_too_big":  "رەسىم بەك چوڭ",

		"PermitStartDate":         "ئىجازەتنامىنىڭ كۈچكە ئىگە ۋاقتى",
		"PermitEndDate":           "ئىجازەتنامىنىڭ كۈچتىن قالىدىغان ۋاقتى",
		"PermitShopName":          "ئىجازەتنامىدىكى دۇكان نامى",
		"PermitLicNum":            "ئىجازەتنامە نومۇرى",
		"PermitCreditCode":        "ئىجازەتنامە ئىناۋەت كودى",
		"PermitLegalName":         "ئىجازەتنامە قانۇنىي ئىگىسىنىڭ ئىسىم - فامىلىسى",
		"PermitShopAddr":          "ئىجازەتنامىدىكى دۇكان ئادرېسى",
		"PermitBusinessPremises":  "ئىجازەتنامىلىك تىجارەت سورۇنى",
		"PermitState":             "ئىجازەتنامە ھالىتى",
		"PermitBusinessType":      "ئىجازەتنامىلىك تىجارەت تۈرى",
		"PermitSuperviseOrg":      "نازارەت قىلىپ باشقۇرغۇچى ئورگان",
		"PermitSuperviseManagers": "نازارەت قىلىپ باشقۇرغۇچى",
		"PermitIssuingAuthority":  "ئىجازەتنامە تارقاتقان ئورگان",
		"ImgSrc":                  "رەسىم",

		"staff_prame_error":          "خىزمەتچىلەر ئۇچۇرىنى تولۇق تولدۇرۇڭ",
		"mini_shop_must_fill_gender": "كىچىك تىجارەتچى جىنسى ئايرىمىسىنى چوقۇم تولدۇرۇڭ",

		"marketing_name":                              "پائالىيەت ئىسمى",
		"marketing_type":                              "پائالىيەت تۈرى",
		"marketing_begin_date":                        "باشلىنىش ۋاقتى",
		"marketing_end_date":                          "ئاخىرلىشىش ۋاقتى",
		"marketing_full_week_state":                   "پۈتۈن ھەپتە ھالىتى",
		"marketing_full_time_state":                   "پۈتۈن ۋاقىت ھالىتى",
		"marketing_auto_continue":                     "ئاپتوماتىك داۋاملىشىش",
		"marketing_date_format_error":                 "ۋاقىت توغرا ئەمەس",
		"exists_same_timeline_event":                  "بۇ ۋاقىت بۆلىكىدە باشقا بىر پائالىيەت بار، پائالىيەت ۋاقتىنى تەكشۈرۈڭ",
		"full_week_empty_days":                        "ھەممىنى تاللىمىغاندا ھېچبولمىسا بىرەر كۈننى تاللاڭ",
		"full_time_empty_time":                        "پۈتۈن كۈن تاللانمىغاندا ھېچبولمىسا مەلۇم ۋاقىت ئارىلىقى تاللىنىشى كېرەك",
		"second_time_must_be_after_first":             "كېيىنكى ۋاقىت ئالدىنقى ۋاقىتتىن كېيىن بولۇشى كېرەك",
		"marketing_foods_exists":                      "ئوخشاش ۋاقىت بۆلىكىدە ئوخشاش مەھسۇلات قوشۇلۇپ بولغان",
		"price_must_exist":                            "باھا چوقۇم يېزىلىشى كېرەك",
		"price_must_not_equal_to_reduce":              "ئېتىبار باھاسى باشلىنىش باھاسىدىن تۆۋەن بولۇشى كېرەك",
		"reduce_price_must_not_empty":                 "ئېتىبار باھاسى بوش قالسا بولمايدۇ",
		"price_must_not_empty":                        "باھا بوش قالسا بولمايدۇ",
		"price_reduce_must_not_empty":                 "كېمەيتىلىدىغان باھا بوش قالسا بولمايدۇ",
		"marketing_foods_not_empty":                   "مەھسۇلات بوش قالسا بولمايدۇ",
		"price_must_be_plus":                          "باھاسى چوقۇم نۆلدىن يۇقىرى بولۇشى كېرەك",
		"next_price_must_bigger_than_previous":        "كېيىنكى دەرىجىدىكى باھا دەسلەپكى دەرىجىدىكى باھادىن يۇقىرى بولۇشى كېرەك",
		"next_price_reduce_must_bigger_than_previous": "كېيىنكى دەرىجىدىكى باھا كېمەيتىش سوممىسى ئالدىنقى دەرىجىدىكى باھا كېمەيتىش سوممىسىدىن يۇقىرى بولۇشى كېرەك",
		"marketing_not_found":                         "پائالىيەت مەۋجۇت ئەمەس",
		"marketing_not_editable":                      "پائالىيەتنى تەھرىرلىگىلى بولمايدۇ",
		"marketing_create_by_merchant_not_editable":   "بۇ پائالىيەتنى ئاشخانا قۇرغان ئۆزگەرتشىكە بولمايدۇID:%d،",
		"marketing_wrong_state_can_not_updated":       "ID:%d بولغان پائالىيەتنىڭ ھالىتى %s بولغان، ئۆزگەرتىشكە بولمايدۇ",
		"merketing_conflict_not_editable":             "بۇ پائالىيەتنى قوزغىتالمايسىز، ئاۋۋال مۇشۇ ۋاقىتتا مېڭىۋاتقان پائالىيەت <<%s>>نى توختىتىپ ئاندىن قوزغىتىڭ",
		"marketing_enable_change":                     "ھالەت ئوزگەرتىش مەغلۇپ بولدى",
		"marketing_not_deletable":                     "زاكاز چۈشۈپ بولغان پائالىيەتنى ئۆچۈرگىلى بولمايدۇ",
		"marketing_delet_failed":                      "پائالىيەتنى ئۆچۈرۈش مەغلۇپ بولدى",
		"marketing_store":                             "دۇكان پائالىيتى",
		"marketing_product":                           "مال پائالىيتى",
		"marketing_state_0":                           "يېڭى قۇرۇلغان",
		"marketing_state_1":                           "داۋاملىشىۋاتىدۇ",
		"marketing_state_2":                           "توختىتىلغان",
		"marketing_state_3":                           "ئىناۋەتسىز",
		"marketing_state_4":                           "ئۆچۈرۈلگەن",
		"marketing_expired":                           "كۈندىن كېيىن ئىناۋەتسىز بولىدۇ",
		"marketing_customer_type":                     "خېرىدار تۈرى بوش قالسا بولمايدۇ",
		"marketing_create_fail":                       "پائالىيەت قۇرۇش مەغلۇپ بولدى",
		"marketing_can_not_update_state_to_new":       "پائالىيەت ھالىتىنى يېڭى قۇرۇلغان ھالەتكە يېڭىلاشقا بولمايدۇ",
		"conflict_marketing_not_found":                "توقۇنۇشقان پائالىيەت مەۋجۇت ئەمەس",
		"conflict_marketing_found":                    "توقۇنۇشقان پائالىيەت مەۋجۇت",
		"marketing_conflict_with_other_marketing":     "ID نومۇرى %d  باشلىنىش ۋاقتى %s ئاخىرلىشى ۋاقتى %s بولغان پائالىيەت بىلەن توقۇنۇشۇپ قالغان",
		"marketing_state_is_can_not_update":           "پائالىيەت ھالىتى خاتا يېڭىلىغىلى بولمايدۇ",
		"marketing_only_edit_merchant_activity":       "پەقەت ئادەتتىكى كىرا كېمەيتىش پائالىيىتىنى تەھرىرلىيەلەيسىز",
		"price_type_must_exist":                       "كىرا كېمەيتىش تۈرى بوش قالسا بولمايدۇ",
		"marketing_add_by_admin_not_editable":         "بۇ پائالىيەتنى ۋاكالەتچى قۇرغان ئۆزگەرتشىكە بولمايدۇ",
		"price_type_must_in_area":                     "كىرا كېمەيتىش تۈرى (1،2) ئىچىدە بولۇشى كېرەك",
		"price_type_before":                           "ئالدىنقى دەرىجىدىكى كىرا كېمەيتىش تۈرى پۈتۈنلەي ھەقسىز تەڭشىلىپ بولغان",
		"created_by_admin":                            "ۋاكالەتچى قۇرغان",
		"marketing_reduce_fee":                        "كېمەيتىش پائالىيتى كېمەيتكەن باھا",
		"marketing_shipment_reduce_fee":               "توشۇش پائالىيتى كېمەيتكەن باھا",
		"all_customer":                                "بارلىق خېرىدار",
		"price_type1":                                 "يېرىم كىرا",
		"price_type2":                                 "ھەقسىز كىرا",
		"created_by_owner":                            "دۇكاندار قۇرغان",
		"creator_type1":                               "ۋاكالەتچى ",
		"creator_type2":                               "دۇكاندار",
		"restaurant_not_found":                        "دۇكان ئۇچۇرى تېپىلمىدى",
		"marketing_group_template_not_found":          "ۋاكالەتچى تەشكىللىگەن پائالىيەت مەۋجۇت ئەمەس",
		"marketing_group_added":                       "پائالىيەتكە قوشۇلۇپ بولدى",
		"marketing_add_group_template_failed":         "پائالىيەت قوشۇش مەغلۇپ بولدى",
		"marketing_group_template_has_joined_marchant_can_not_be_updated": "كوللېكتىپ پائالىيەتكە قاتناشقان ئاشخانا بار ئىكەن، تەھرىرلىگىلى بولمايدۇ",
		"marketing_group_template_update_failed":                          "كوللېكتىپ پائالىيەت تەھرىرلەش مەغلۇپ بولدى",
		"marketing_group_template_statistics_failed":                      "كوللېكتىپ پائالىيەت ئۇچۇرى ئىستاتىستىكىسى مەغلۇپ بولدى",
		"marketing_merchant_shipment_reduce_statistics_failed":            "دۇكاندار كىرا كېمەيتىش پائالىيەت زاكاز ئىستاتىستىكىسى مەغلۇپ بولدى",
		"marketing_group_template_change_state_failed":                    "كوللېكتىپ پائالىيەت ھالىتىنى ئۆزگەرتىش مەغلۇپ بولدى",
		"marketing_group_template_change_state_too_many_ids_received":     "كوللېكتىپ پائالىيەت ھالىتىنى ئۆزگەرتىش مەغلۇپ بولدى، يوللانغان كوللېكتىپ پائالىيەت بەك كۆپ",
		"marketing_group_template_too_many_ids_received":                  "يوللانغان كوللېكتىپ پائالىيەت بەك كۆپ",
		"NameUg":                             "پائالىيەت ئىسمى (ئۇيغۇرچە)",
		"NameZh":                             "پائالىيەت ئىسمى (خەنزۇچە)",
		"ResId":                              "دۇكان",
		"MarketingType":                      "پائالىيەت تۈرى",
		"Type":                               "پائالىيەت تۈرى",
		"BeginDate":                          "باشلىنىش ۋاقتى",
		"EndDate":                            "ئاخىرلىشىش ۋاقتى",
		"at_least_one_food_required":         "ئاز بولغاندا بىر خىل تاماق تاللاڭ",
		"2second_time_must_be_after_first":   "ئىككىنىچى ۋاقىت بۆلىكىدىكى ئاخىرلىشىش ۋاقتى باشلىنىش ۋاقتىدىن كېيىن بولۇشى كېرەك",
		"3second_time_must_be_after_first":   "ئۈچىنچى ۋاقىت بۆلىكىدىكى ئاخىرلىشىش ۋاقتى باشلىنىش ۋاقتىدىن كېيىن بولۇشى كېرەك",
		"target":                             "تۈرى",
		"StartDate":                          "باشلىنىش ۋاقتى",
		"EndtDate":                           "ئاخىرلىشىش ۋاقتى",
		"marketing_home_page_notify_content": "يېڭىدىن باھا كېمەيتىش ئىقتىدارى قوشۇلدى",
		"marketing_type1_description":        "تېخىمۇ كۆپ خېرىدار ئېقىمىغا ئېرىشەلەيسىز",
		"marketing_type1_rule":               "",
		"marketing_type2_description":        "تېخىمۇ كۆپ خېرىدار ئېقىمىغا ئېرىشەلەيسىز",
		"marketing_type2_rule":               "",
		"min_delivery_price_can_not_be_less_than_system_config":                              "ئەڭ تۆۋەن تاماق سوممىسى %0.2f يۈەندىن تۆۋەن بولسا بولمايدۇ",
		"area_id_can_not_be_empty":                                                           "رايون تاللاڭ",
		"area_not_found":                                                                     "رايون ئۇچۇرى تېپىلمىدى",
		"area_business_start_time_must_be_earlier_than_end_time":                             "تىجارەت باشلىنىش ۋاقتى چوقۇم ئاخىرىلىشىش ۋاقتىدىن بۇرۇن بولۇشى كېرەك",
		"area_business_time_must_be_longger_than_eight_hours":                                "تىجارەت ۋاقتى 8 سائەتتىن ئاز بولماسلىقى كېرەك",
		"active_restaurant_not_found":                                                        "ئوچۇق دۇكان ئۇچۇرى تېپىلمىدى",
		"restaurant_ids_can_not_be_empty":                                                    "سىز ئاشخانا تاللىماپسىز",
		"timeline_must_be_at_least_one":                                                      "ۋاقىت بۆلىكىنى تولدۇرۇڭ",
		"timeline_start_can_not_be_empty":                                                    "ۋاقىت بۆلىكى باشلىنىش ۋاقتى بوش قالسا بولمايدۇ",
		"timeline_end_can_not_be_empty":                                                      "ۋاقىت بۆلىكى ئاخىرلىشىش ۋاقتى بوش قالسا بولمايدۇ",
		"timeline_start_format_error":                                                        "ۋاقىت بۆلىكى باشلىنىش ۋاقتى توغرا ئەمەس",
		"timeline_end_format_error":                                                          "ۋاقىت بۆلىكى ئاخىرلىشىش ۋاقتى توغرا ئەمەس",
		"timeline_start_must_be_before_end":                                                  "ۋاقىت بۆلىكى باشلىنىش ۋاقتى ئاخىرلىشىش ۋاقتىدىن بۇرۇن",
		"timeline_start_and_end_can_not_be_empty":                                            "ۋاقىت بۆلىكى باشلىنىش ۋاقتى ۋە ئاخىرلىشىش ۋاقتى بوش قالدى",
		"timeline_can_not_overlap":                                                           "ۋاقىت بۆلىكى كېسىشىپ قالسا بولمايدۇ",
		"timeline_can_not_be_more_than_three_item":                                           "ۋاقىت بۆلىكى 3 دىن كۆپ بولسا بولمايدۇ",
		"shipment_reduce_step_must_be_at_least_one":                                          "كىرا كېمەيتىش پەشتىقى بوش قالدى",
		"shipment_reduce_step_start_must_be_positive":                                        "كىرا كېمەيتىش پەشتىقى باشلىنىش ئارىلىقى 0 دىن كىچىك",
		"shipment_reduce_step_end_must_be_bigger_than_start":                                 "كىرا كېمەيتىش پەشتىقى باشلىنىش ئارىلىقى ئاخىرلىشىش ئارىلىقىدىن كىچىك",
		"shipment_reduce_step_end_must_be_smaller_than_next_start":                           "كىرا كېمەيتىش پەشتىقى ئ‍اخىرلىشىش ئارىلىقى كېلەركى پەشتاقنىڭ باشلىنىش ئارىلىقىدىن چوڭ",
		"shipment_reduce_step_reduce_must_be_positive":                                       "كىرا كېمەيتىش پەشتىقى ئېتىبار كىرا ھەققى 0 دىن كىچىك",
		"shipment_reduce_store_price_must_be_positive":                                       "كىرا كېمەيتىش پەشتىقى ئاشخانا كۆتۈرىدىغان قىسمى 0 دىن كىچىك",
		"shipment_reduce_dealer_price_must_be_positive":                                      "كىرا كېمەيتىش پەشتىقى ۋاكالەتچى كۆتۈرىدىغان قىسمى 0 دىن كىچىك",
		"marketing_group_template_send_join_activity_message_failed":                         "كوللېكتىپ پائالىيەتكە تەكلىپ ئۇچۇر يوللاش مەغلۇپ بولدى",
		"marketing_group_template_only_active_new_template":                                  "پەقەت يېڭى قۇرۇلغان كوللېكتىپ پائالىيەتنى ئاچقىلى بولىدۇ",
		"marketing_group_template_already_active":                                            "كوللېكتىپ پائالىيەت ئېچىلىپ بولغان",
		"marketing_group_template_only_pause_new_or_active_state":                            "پەقەت يېڭى قۇرۇلغان ياكى ئېچىلغان كوللېكتىپ پائالىيەتنى توختىتىشقا بولىدۇ",
		"marketing_group_template_already_pause":                                             "كوللېكتىپ پائالىيەت توختىتىلغان",
		"marketing_group_template_pause_failed":                                              "كوللېكتىپ پائالىيەت توختىتىش مەغلۇپ بولدى",
		"shipment_reduce_step_reduce_price_not_equal_to_sum_of_store_price_and_dealer_price": "كىرا كېمەيتىش پەشتىقى ئېتىبار كىرا ھەققى چوقۇم ئاشخانا كۆتۈرىدىغان قىممەت بىلەن ۋاكالەتچى كۆتۈرىدىغان قىممەتنىڭ يىغىندىسىغا تەڭ بولۇشى كېرەك",
		"begin_time_must_be_after_today":                                                     "باشلىنىش ۋاقتى ھازىرقى ۋاقىتتىن كېيىن بولۇشى كېرەك",
		"begin_time_must_be_after_time":                                                      "ئاخىرلىشى ۋاقتى باشلىنىش ۋاقىتىدىن كېيىن بولۇشى كېرەك",
		"begin_date_can_not_feature":                                                         "باشلىنىش ۋاقتى بۈگۈندىن چوڭ بولسا بولمايدۇ",
		"begin_date_format_error":                                                            "باشلىنىش ۋاقتى توغرا ئەمەس",
		"end_date_format_error":                                                              "ئاخىرلىشىش ۋاقتى توغرا ئەمەس",
		"end_date_can_not_feature":                                                           "ئاخىرلىشىش ۋاقتى بۈگۈندىن چوڭ بولسا بولمايدۇ",
		"date_range_can_not_over_30_days":                                                    "باشلىنىش ۋاقتى ۋە ئاخىرلىشىش ۋاقتى ئارىلىقى 30 كۈندىن ئېشىپ كەتمەسلىكى كېرەك",
		"date_range_can_not_over_31_days":                                                    "باشلىنىش ۋاقتى ۋە ئاخىرلىشىش ۋاقتى ئارىلىقى 31 كۈندىن ئېشىپ كەتمەسلىكى كېرەك",
		"end_date_can_not_before_begin_date":                                                 "ئاخىرلىشىش ۋاقتى باشلىنىش ۋاقتىدىن بۇرۇن بولسا بولمايدۇ",
		"wrong_weekday_selected":                                                             "تاللىغان كۈن بۆلەكلىرىدە مەسىلە بار",
		"day_7_not_in_range":                                                                 "بۇ ۋاقىت ئارىلىقىدا يەكشەنبە يوق",
		"day_1_not_in_range":                                                                 "بۇ ۋاقىت ئارىلىقىدا دۈشەنبە يوق",
		"day_2_not_in_range":                                                                 "بۇ ۋاقىت ئارىلقىدا سەيشەنبە يوق",
		"day_3_not_in_range":                                                                 "بۇ ۋاقىت ئارىلقىىدا چارشەنبە يوق",
		"day_4_not_in_range":                                                                 "بۇ ۋاقىت ئارىلقىدا پەيشەنبە يوق",
		"day_5_not_in_range":                                                                 "بۇ ۋاقىت ئارىلقىقىدا جۈمە يوق",
		"day_6_not_in_range":                                                                 "بۇ ۋاقىت ئارىلقىدا شەنبە يوق",
		"merInfoaudit1":                                                                      "ماتېرىيال تاپشۇرۇلدى، باشقۇرغۇچىنىڭ تەكشۈرۈپ بېكىتىشىنى كۈتۈڭ",
		"merInfoaudit3":                                                                      "ئارقا سۇپا تەكشۈرۈشىدىن ئۆتۈپ بولغان، ئۆزگەرتسىڭىز بولمايدۇ",
		"merInfoaudit4":                                                                      "باشقۇرغۇچىنىڭ تەكشۈرۈشىدىن ئۆتتى، ماتېرىيال يوللاشنى كۈتۈۋاتىدۇ",
		"merInfoaudit5":                                                                      "ماتېرىيال تاپشۇرۇلدى، ئۆزگەرتىسڭىز بولمايدۇ",
		"merInfoaudit7":                                                                      "شىركەت ھېساباتى تەستىقلىنىۋاتىدۇ، ئۆزگەرتسىڭىز بولمايدۇ",
		"merInfoaudit8":                                                                      "توردا كېلىشىم ئىمزالاشنى ساقلاۋاتىدۇ، ئۆزگەرتسىڭىز بولمايدۇ",
		"merInfoaudit9":                                                                      "تورغا كىرىش تەستىقلىنىۋاتىدۇ، ئۆزگەرتسىڭىز بولمايدۇ",
		"merInfoaudit10":                                                                     "تورغا كىرىش ئوڭۇشلۇق بولدى",
		"merInfoaudit11":                                                                     "تورغا كىرىش مەغلۇپ بولدى",
		"update_info_state":                                                                  "يوللاش مەغلۇپ بولدى",
		"state_0":                                                                            "يوللانمىغان",
		"state_1":                                                                            "تەستىققا يوللاندى",
		"state_2":                                                                            "تەستىقتىن ئۆتمىدى",
		"state_3":                                                                            "مۇلازىم تەستىقىدىن ئۆتتى",
		"state_4":                                                                            "ماتېرىيال يوللىنىۋاتىدۇ",
		"state_5":                                                                            "ماتېرىيال يوللىنىپ بولدى",
		"state_6":                                                                            "ماتېرىيال تەستىقتىن ئۆتمىدى",
		"state_7":                                                                            "شىركەت ھېسابات نومۇرى تەستىقلىنىۋاتىدۇ",
		"state_8":                                                                            "توردا كېلىشىم ئىمزالاشنى ساقلاۋاتىدۇ",
		"state_9":                                                                            "توردىكى كېلىشىم تەستىقلىنىۋاتىدۇ",
		"state_10":                                                                           "تورغا كىرىش ئوڭۇشلۇق بولدى",
		"state_11":                                                                           "تورغا كىرىش مەغلۇپ بولدى",
		"check_state_failed":                                                                 "ھالەت تەكشۈرۈش مەغلۇپ بولدى",
		"对公账户验证失败,已错误1次,最大允许错误次数5次":                  "شىركەت ھېسابات نومۇرى تەستىقتىن ئۆتمىدى، بىرىنچى قېتىم خاتالىق يۈز بەردى، ئەڭ كۆپ بولغاندا بەش قېتىم سىنىيالايسىز",
		"对公账户验证失败,已错误2次,最大允许错误次数5次":                  "شىركەت ھېسابات نومۇرى تەستىقتىن ئۆتمىدى، ئىككىنچى قېتىم خاتالىق يۈز بەردى، ئەڭ كۆپ بولغاندا بەش قېتىم سىنىيالايسىز",
		"对公账户验证失败,已错误3次,最大允许错误次数5次":                  "شىركەت ھېسابات نومۇرى تەستىقتىن ئۆتمىدى، ئۈچىنچى قېتىم خاتالىق يۈز بەردى، ئەڭ كۆپ بولغاندا بەش قېتىم سىنىيالايسىز",
		"对公账户验证失败,已错误4次,最大允许错误次数5次":                  "شىركەت ھېسابات نومۇرى تەستىقتىن ئۆتمىدى، تۆتىنچى قېتىم خاتالىق يۈز بەردى، ئەڭ كۆپ بولغاندا بەش قېتىم سىنىيالايسىز",
		"对公账户验证失败,已错误5次,最大允许错误次数5次":                  "شىركەت ھېسابات نومۇرى تەستىقتىن ئۆتمىدى، بەشىنچى قېتىم خاتالىق يۈز بەردى، ئەڭ كۆپ بولغاندا بەش قېتىم سىنىيالايسىز",
		"you_have_some_meter_to_attendence_position": "يوقلىما نۇقتىسىسغا %0.1fm ئارىلىق بار",
		"you_must_open_gps":         "ئورۇن بەلگىلەش ھوقۇقىنى ئىچىڭ",
		"grab_order_count_exceeded": "تالىشالايدىغان زاكاز سانى %d دىن ئېشىپ كەتتى",
		"shop_licence_incomplete":   "تىجارەت كىنىشكە ئۇچۇرى تولۇق ئەمەس",
		"idcard_incomplete":         "كىملىىك ئۇچۇرى تولۇق ئەمەس",
		"bank_incomplete":           "ھېسابات ئۇچۇرى تولۇق ئەمەس",
		"shop_incomplete":           "دۇكان ئۇچۇرى تولۇق ئەمەس",
		"info_incomplete":           "ئۇچۇرلارنى تولدۇرۇپ ئاندىن يوللاڭ",
		"submitted_wait_for_check":  "يوللىنىپ بولدى، تەستىق كۈتۈۋاتىدۇ",
		"bank_card_error":           "كارتا نومۇرى توغرا ئەمەس",
		"api_error":                 "بانكا كارتا تەكشۈرۈش ئېغىزىدىن مەسىلە كۆرۈلدى، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ",
		"idcard_recommit":           "تىجارەتچى ئۇچۇرىنى قايتا يوللاڭ",
		"bank_verify_result": map[int]string{
			0:   "تەستىقتىن ئۆتتى",
			-1:  "بانكا كارتا تەستىقتىن ئۆتمىدى، بانكا كارتا نومۇرى، كارتا باغلىغان تېلېفون نومۇرى، كىملىك نومۇرى، كىملىكتىكى ئىسىم بىردەك بولۇشى كېرەك",
			-6:  "بانكا كارتا ئىگىسى ئۇچۇرىدا مەسىلە بار",
			-7:  "未开通无卡支付",
			-8:  "此卡被没收",
			-9:  "كارتا نومۇرى ئىناۋەتسىز",
			-10: "بانكا كارتا نومۇرىغا ماس كېلىدىغان بانكا يوق",
			-11: "该卡未初始化或睡眠卡",
			-12: "作弊卡、吞卡",
			-13: "كارتا يۈتۈپ كەتتى دەپ مەلۇم قىلنغان كارتا",
			-14: "بانكا كارتىسىنىڭ ۋاقتى ئۆتۈپ كېتىپتۇ",
			-15: "بانكا كارتىسى چەكلىمىگە ئۇچرىغان",
			-16: "密码错误次数超限",
			-17: "发卡行不支持此交易",
			-2:  "بانكا كارتىسىدىكى ئسىىم توغرا ئەمەس",
			-3:  "بانكا كارتىسىدىكى كىملىك نومۇرى توغرا ئەمەس",
			-4:  "بانكا كارتا نومۇرى توغرا ئەمەس",
			-5:  "تېلېفون نومۇرى توغرا ئەمەس",
			-18: "بانكا كارتسى تەكشۈرۈش مۇلازىمىتى ئالدىراش، سەل تۇرۇپ قايتا سىناڭ",
		},
		"order_not_exits":                     "بۇ زاكاز مەۋجۇت ئەمەس",
		"server_error":                        "مۇلازىمىتېردىن خاتالىق كۆرۈلدى",
		"legal_name_and_idcard_name_not_same": "قانۇنى ۋەكىل ئىسمى بىلەن كىملىكتىكى ئىسىم بىردەك ئەمەس",
		"Kw":                                  "ئىزدەيدىغان سۆز",
		"this_admin_has_more_than_one_store":  "بۇ ئاكونت باشقۇرۇشىدا",
		"multi_admin_error":                   "بار ئىكەن، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ",
		"RestaurantId":                        "دۇكان ID",
		"RestaurantID": "دۇكان ID",
		"OptionIds":    "ئۆلچەم ID",
		"FoodType":     "تاماق تىپى",
		"SelfTakeNumber":                      "تاماق ئېلىۋېلىش نومۇرى",
		"order_been_taken":                    "تاماقنى ئاللىقاچان ئېلىپ كەتتى",
		"errors": map[string]string{
			"database": "سانداندىن خاتالىق چىقتى",
		},
		"open_time_or_close_time_not_in_food_serving_time": "تەڭشەلگەن دۇكان ئېچىش ياكى ئېتىش ۋاقتى بىلەن تاماققا تەڭشەلگەن تاماق چىقىش ۋاقىت ئارىلىقدا خاتالىق بار'",
		"send_before":               "يوللاپ بولغان",
		"id_card_no_empty":          "كىملىك نومۇرى بوش قالسا بولمايدۇ",
		"business_license_no_empty": "تىجارەت كىنىشكە نومۇرى بوشق قالسا بولمايدۇ",
		"lakala_copy_content": map[string]string{
			"shop_license_num":       "营业执照号码",
			"shop_business_name":     "店铺名称",
			"legal_name":             "法人名称",
			"bank_acct_num":          "银行账户",
			"bank_acct_name":         "银行账户名称",
			"bank_name":              "银行名称",
			"bank_acct_type_name":    "账户类型",
			"bank_branch_name":       "银行支行名称",
			"bank_branch_code":       "银行支行行号",
			"shop_business_province": "所属省份",
			"shop_business_city":     "所属城市",
			"shop_business_country":  "所属区域",
			"shop_business_address":  "经营地址",
		},
		"not_fund_merchant":   "دۇكاندار تېپىلمىدى",
		"not_found_customer":  "خېرىدار تېپىلمىدى",
		"cashout_in_progress": "پۇل چىقىرىلىۋاتىدۇ، ساقلاپ تۇرۇڭ",
		"per_cashe_out_limit": "بىر قېتىمدا ئەڭ كۆپ بولغاندا 500 يۈەن چىقىرالايسىز",
		"not_in_working_time": "پۇل چىقىرىش ۋاقتى توشتى، پۇل چىقىرىش ۋاقتى سەھەر سائەت بەشتە باشلىنىدۇ",

		"illiegal_bill":                  "پۇل چىقىرىش ھېسابات كۈنىدە خاتالىق بار",
		"cashout_amount_incorrect":       "چىقىرىدىغان پۇل سوممىسىدا خاتالىق بار",
		"retry_after_10_second":          "10 سېكۇنتتىن كېيىن قايتا سىناڭ",
		"system_error":                   "مۇلازىمىتېردا خاتالىق كۆرۈلدى",
		"cash_out_config_not_ok":         "پۇل چىقىرش تەڭشىكىدە مەسىلە بار",
		"cashout_record_not_found":       "چىقىرىشقا تېگىشلىك ئۇچۇردا مەسىلە بار",
		"system_maintaining_in_progress": "سىستېما ئاسرىلىۋاتىدۇ",
		"lakala_withdraw_states": map[int]string{
			1: "يېڭى يوللانغان",
			2: "تەقسىملەشنى كۈتۈۋاتىدۇ",
			3: "تەقسىملەش ئوڭۇشلۇق بولدى",
			4: "تەقسىملەش مەغلۇپ بولدى",
			5: "پۇل چىقىرىش ئوڭۇشلۇق بولدى",
			6: "پۇل چىقىرىش مەغلۇپ بولدى",
			7: "تەقسىملەشنى قايتۇرۇش ئوڭۇشلۇق بولدى",
			8: "تەقسىملەشنى قايتۇرۇش مەغلۇپ بولدى",
		},
		"lakala_withdraw_state_processing":  "پۇل چىقىرىلىۋاتىدۇ",
		"lakala_withdraw_state_success":     "پۇل چىقىرىلدى",
		"lakala_withdraw_state_fail":        "پۇل چىقىرىش مەغلۇپ بولدى",
		"withdraw_request_sent":             "پۇل چىقىرىش ئىلتىماسى يوللاندى، نەتىجىسىنى كۈتۈڭ",
		"cardid_is_not_null":                "پۇل چىقىرىدىغان بانكا كارتىسىنى تاللاڭ",
		"shipper_app_is_old":                "يەتكۈزگۈچى ئەپىنىڭ نەشرى كونا ئىكەن، ئەپنى يېڭىلاڭ",
		"res_manager_app_is_old":            "دۇكاندار ئەپنىڭ نەشرى كونا ئىكەن، ئەپنى يېڭىلاڭ",
		"cash_per_day":                      "كۈندىلىك پۇل چىقرىش سوممىڭىز %s يۈەن، بۈگۈن چىقرىپ بولغىنىڭىز %s يۈەن، ھازىرقى چىقارماقچى بولغان سوممىڭىز  %s يۈەن، بۇ قېىتمقى سومما پۇل چىقرىش چېكىدىن %s يۈەن ئېشىپ كەتتى",
		"lakala_archive_not_found":          "بۇ دۇكان سالاھىيەتلەشتۈرۈشتىن ئۆتمىگەن",
		"wechat_withdraw_stop_msg":          "دۇكان سالاھىيەتلەشتۈرۈشنى تاماملاپ، ئاندىن قايتا سىناڭ",
		"BnfIdcardStart":                    "پاي قوشقۇچى كىملىكىنىڭ باشلىنىش ۋاقتى",
		"BnfName":                           "پاي قوشقۇچى ئىسمى",
		"BnfIdcardNum":                      "پاي قوشقۇچى كىملىك نومۇرى",
		"BnfAddress":                        "پاي قوشقۇچى ئادرېسى",
		"BnfIdcardEnd":                      "پاي قوشقۇچى كىملىكىنىڭ كۈچتىن قالىدىغان ۋاقتى",
		"Bnf.BnfIdcardStart":                "پاي قوشقۇچى كىملىكىنىڭ كۈچكە ئىگە ۋاقتىنىڭ باشلىنىش ۋاقتى",
		"Bnf.BnfName":                       "پاي قوشقۇچى ئىسمى",
		"Bnf.BnfIdcardNum":                  "پاي قوشقۇچى كىملىك نومۇرى",
		"Bnf.BnfAddress":                    "پاي قوشقۇچى ئادرېسى",
		"Bnf.BnfIdcardEnd":                  "پاي قوشقۇچى كىملىكىنىڭ كۈچتىن قالىدىغان ۋاقتى",
		"BankId":                            "بانكا",
		"Bnf":                               "پاي قوشقۇچى ئۇچۇرى",
		"not_empty":                         "بوش قالسا بولمايدۇ",
		"retry_after_second":                "%d سېكۇنتتىن كېيىن قايتا سىناڭ",
		"right_now":                         "ھازىر",
		"before_minute":                     "%d مىنۇت بۇرۇن",
		"before_hour":                       "%d سائەت بۇرۇن",
		"before_day":                        "%d كۈن بۇرۇن",
		"coupon_refunded":                   "پۇل قايتۇرۇلۇپ بولغان",
		"coupon_not_found":                  "ئېتىبار بېلەت مەۋجۇت ئەمەس",
		"coupon_refund_no_amount":           "قايتۇرغۇدەك سومما يوقكەن",
		"coupon":                            "ئېتىبار بېلەت",
		"ShopLicenseStart_error":            "تىجارەت كىنىشكىسى كۈچكە ئىگە ۋاقتىنىڭ باشلىنىش ۋاقتى توغرا ئەمەس، رەسىمنى قايتا يوللاپ سىناڭ",
		"ShopLicenseEnd_error":              "تىجارەت كىنىشكىسى كۈچكە ئىگە ۋاقتىنىڭ ئاخىرلىشىش ۋاقتى توغرا ئەمەس، رەسىمنى قايتا يوللاپ سىناڭ",
		"coupon_refund_request_in_progress": "پۇل قايتۇرۇش ئىلتىماسىڭىز ئىجرا بولۇۋاتىدۇ ساقلاپ تۇرۇڭ",
		"retry_after_minute":                "%d مىنۇتتىن كېيىن قايتا سىناڭ",
		// 配送员管理
		"not_found_shipper":      "يەتكۈزگۈچىنى تاپالمىدى",
		"no_permission":          "ھوقۇقىڭىز يەتمىدى",
		"shipper_has_restaurant": "يەتكۈزگۈچىگە تەقسىملەنگەن ئاشخانا بار ئىكەن، ئاۋال تەقسىملەنگەن ئاشخانىنى چىقىرۋېتىپ قايتا سىناڭ",
		"shipper_has_take_order": "يەتكۈزگۈچى يەتكۈزگەن زاكاز بار ئىكەن، بۇ يەتكۈزگۈچىنى ئۈچۈرەلمەيسىز",
		"failed_delete_shipper":  "يەتكۈزگۈچىنى ئۆچۈرۈش مەغلۇپ بولدى",
		// 配送费模板
		"DeductionTypeNames":           map[int]string{1: "مۇقىم تۇتۇش", 2: "زاكاز سانىغا ئاساسەن تۇتۇش"},
		"RuleTypeNames":                map[int]string{1: "مۇقىم ھەق بېرىش", 2: "ئارىلىققا ئاساسەن ھېسابلاش", 3: "بەلگىلىك ئارىلىققا ئاساسەن ھېسابلاش", 4: "زاكاز سانىغا ئاساسەن ھېسابلاش"}, // 规则类型:1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"ShipperIncomeType":            map[int]string{1: "زاكاز كىرىمى", 2: "مۇكاپات", 3: "تاپان ھەققى", 4: "ئالاھىدە ۋاقىت كىرىمى", 5: "ئالاھىدە ھاۋارايى", 6: "ياخشى باھا", 7: "ناچار باھا", 8: "ئەرز - شىكايەت", 9: "كېچىكككەن"},
		"ShipperIncomeComplainType":    map[int]string{1: "خېرىدار ئەرز قىلغان", 2: "ئاشخانا ئەرز قىلغان"},
		"ShipperNotAroundShop":         "تېخى ئاشخانا ئەتراپىغا كەلمەپسىز",
		"ShipperStatisticsIncomeTypes": map[int]string{1: "يەتكۈزۈش كىرىمى", 2: "مۇكاپات", 3: "جازا", 4: "باشقا كىرىم", 5: "خېرىدار كۆپەيتىش"},
		"ShipperStatisticsOrderTypes":  map[int]string{1: "يەتكۈزگەن زاكاز", 2: "قايتۇرغان زاكاز", 3: "مەغلۇپ بولغان", 4: "كىچىككەن زاكاز"},
		"shipper_order_state": map[int]string{
			0:   "يېڭى زاكاز",
			100: "يەتكۈزگۈچى زاكازنى قوبۇل قىلدى",
			200: "يەتكۈزگۈچى ئاشخانىغا كەلدى",
			300: "يەتكۈزگۈچى تاماقنى ئالدى",
			400: "يەتكۈزگۈچى تاماقنى يەتكۈزۈپ بولدى",
			500: "يەتكۈزگۈچى ئاشخانىغا كەلدى، لېكىن ئاشخانا تېخى تاماقنى چىقارمىدى",
		},
		// 1:事假 2:病假 3:婚嫁 4:产假/陪产假
		"leave_types": map[int]string{
			1: "ئىش رۇخسىتى",
			2: "ئاغرىق رۇخسىتى",
			3: "توي رۇخسىتى",
			4: "تۇغۇت رۇخسىتى",
		},
		// 1:待审核,2:审核通过,3:审核不通过,4:撤销
		"review_state": map[int]string{
			1: "تەستىقلاۋاتىدۇ",
			2: "تەستىقتىن ئۆتتى",
			3: "رەت قىلىندى",
			4: "بىكار قىلىندى",
		},
		// 1 上班 2 下班 3 休息
		"attendance_states": map[int]string{
			1: "ئىشقا چىقىش",
			2: "ئىشتىن چۈشۈش",
			3: "ئارام ئېلىش",
		},
		// 类型 1：订单收入，2:奖励收入，3:打赏收入，4.特殊时间，5.特殊天气，6.好评，7.差评，8.投诉，9:迟到
		"income_types": map[int]string{
			1: "زاكاز كىرىم",
			2: "مۇكاپات",
			3: "تارتۇقلاش",
			4: "ئالاھىدە يەتكۈزۈش ۋاقتى",
			5: "ئالاھىدە ھاۋارايى",
			6: "ياخشى باھا",
			7: "ناچار باھا",
			8: "ئەرز - شىكايەت",
			9: "كېچىككەن",

			// 14:"خېرىدار تەكلىپ قىلىش كىرىمى",
			// 15:"يېڭى خېرىدار چۈشۈرگەن زاكازدىن ئايرىلغان كىرىم",
			16: "سۇغۇرتىغا تۇتۇلغان پۇل",
		},
		"ShipperNotTakeFood":                                       "تېخى تاماقنى ئالماپسىز",
		"FileNotFound":                                             "ھۆججەت تېپىلمىدى",
		"FileImageFormatError":                                     "شەكلىدىكى رەسىم يوللاڭ (jpg|jpeg|png)",
		"only_support_xlsx": "پەقەت xlsx ھۆججىتىنى قوللايدۇ",
		"name_already_exist":                                       "بۇ ئىسىمنى ئىشلىتىپ بولغان",
		"mobile_already_exist":                                     "بۇ تېلېفون نومۇرى ئىشلىتىلىپ بولغان",
		"already_used_unable_delete":                               "بۇ ئۇچۇر ئىشلىتىلىپ بولغان، ئۆچۈرەلمەيسىز",
		"special_rule_content_enable_decode":                       "ئالاھىدە ۋاقىت قائىدىسى مەزمۇنى ئىناۋەتسىز",
		"base_rule_content_enable_decode":                          "يەتكۈزۈش ھەققى قائىدىسى مەزمۇنى ئىناۋەتسىز",
		"late_rule_content_enable_decode":                          " كېچىككەن زاكاز جەرىمانە قائىدىسى مەزمۇنى ئىناۋەتسىز",
		"invalid_deduction_fee":                                    "كېچىككەن زاكازغا تۇتۇلغان سومما خاتا",
		"invalid_price":                                            "پۇل سوممىسىدا خاتالىق بار",
		"min_time_not_greater_max_time":                            "باشلىنىش ۋاقتى ئاخىرلىشىش ۋاقتىدىن چوڭ بولۇپ قالغان",
		"time_must_asc_and_not_conflict":                           "ۋاقىت بۆلىكى تەرتىپ بىلەن كېلىشى كېرەك ھەمدە ۋاقىت بۆلىكى كېسىشىپ قالماسلىكى كېرەك",
		"min_time_must_greater_zero":                               "باشلىنىش ۋاقتى چوقۇم نۆلدىن يۇقىرى بولۇشى كېرەك",
		"max_time_must_greater_zero":                               "ئاخىرلىشىش ۋاقتى چوقۇم نۆلدىن يۇقىرى بولىشى كېرەك",
		"template_unable_edit":                                     "يەتكۈزۈش قېلىپى يەتكۈزگۈچىگە باغلىنىپ بولغان ئۆزگەرتەلمەيسىز",
		"password_length_error":                                    "مەخپىي نومۇر 6 خانىدىن يۇقىرى بولۇشى كېرەك",
		"this_time_opening_other_special_weather":                  "بىرلا ۋاقىتتا بىرلا ئالاھىدە ھاۋارايى تەڭشىكى ئاچالايسىز، ئاۋال ‹%s› نى توختىتىپ ئاندىن ئېچىڭ",
		"this_time_opening_other_notify":                           "بىرلا ۋاقىتتا بىرلا ئۇقتۇرۇش ئاچالايسىز",
		"area_id_is_required":                                      "رايون تاللاڭ",
		"marketing_reduce":                                         "باھا كېمەيتىش پائالىيىتى",
		"marketing_coupon":                                         "ئېتىبار چەك پائالىيىتى",
		"not_id":                                                   "IDنى چوقۇم يوللىشى كېرەك ",
		"unable_review":                                            "تەستىقلىنىپ بولغان، قايتا تەستىقلىيالمايسىز",
		"this_area_already_exists_reward_setting":                  "بۇ رايوندا يوقلىما مۇكاپاتى تەڭشىلىپ بولغان، قايتا قۇرالمايسىز",
		"distance_must_greater_zero":                               "ئارىلىق نۆلدىن چوڭ بولۇشى كېرەك",
		"shipment_fee_must_greater_zero":                           "يەتكۈزۈش ھەققى نۆلدىن يۇقىرى بولۇشى كېرەك",
		"order_count_not_latter_zero":                              "زاكاز سانى 0 دىن كىچىك بولسا بولمايدۇ",
		"distance_must_asc":                                        "ئارىلىق تەرتىپى چوقۇم كىچىكتىن چوڭغا بولۇشى كىرەك",
		"order_count_must_asc":                                     "زاكاز سانى تەرتىپى چوقۇم كىچىكتىن چوڭغا بولۇشى كېرەك",
		"max_distance_shipment_must_greater_distance_min_shipment": "ئارىلىقى ئۇزۇن بۆلەكنىڭ يەتكۈزۈش ھەققى ئارىلىقى قىسقا بۆلەكتىن چوڭ بولۇشى كېرەك",
		"max_order_count_shipment_must_greater_min_order_count_min_shipment": "كۆپ زاكاز بۆلەكنىڭ يەتكۈزۈش ھەققى ئاز زاكاز بۆلەكتىن چوڭ بولۇشى كېرەك",
		"count_order_failed":                              "زاكاز سانى ھېسابلاش مەغلۇپ بولدى",
		"shipper_state_unable":                            "يەتكۈزگۈچىنىڭ ھالىتى ئېتىك",
		"already_send_notify_unable_delete":               "يوللىنىپ بولغان ئۇچۇرنى ئۆچۈرەلمەيسىز",
		"this_special_weather_already_used_unable_delete": "ئالاھىدە ھاۋارايى ئىشلىتىلگەن، ئۆچۈرەلمەيسىز",
		"can_not_send_message_now":                        "بۇ زاكازغا ئەمدى خەت يازالمايسىز",
		"contains_forbidden_word":                         "چەكلەنگەن سۆزلەرنى يوللىغىلى بولمايدۇ",
		"back_end":                                        "ئارقا سۇپا",
		"content_type_img":                                "[رەسىم]",
		"content_type_card":                               "[كارتا]",
		// 20 商户关门、21： 商户没有准备好订单
		"reports": "ئەھۋال مەلۇم قىلىش ئۇقتۇرۇشى",
		"report_types": map[int]string{
			20: "دۇكان ئېتىك",
			21: "دۇكان تاماقنى تەييار قىلمىدى",
		},
		"FullTimeState":       "ۋاقىت بۆلىكى",
		"MinDeliveryPrice":    "ئەڭ تۆۋەن تاماق سوممىسى",
		"AllRestaurant":       "ئاشخانا",
		"Steps":               "پەشتاق",
		"did_not_arrive_shop": "دۇكان بىلەن بولغان ئارىلىقىڭىز %0.1f مېتىر",
		"Lang":                "تىل",
		"Platform":            "سۇپا",
		// 状态  0：新建，1:启动，2：暂停，3：失效 ，4:删除
		"marketing_state_name":            map[int]string{0: "يېڭى قۇرۇلدى", 1: "داۋاملىشىۋاتىدۇ", 2: "توختىتىلدى", 3: "ئاخىرلاشتى", 4: "ئۆچۈرۈلگەن"},
		"template_state_name":             map[int]string{0: "يېڭى قۇرۇلغان", 1: "باشلانغان", 2: "توختىتىلغان", 3: "ئاخىرلاشقان", 4: "ئۆچۈرۈلگەن"},
		"marketing_attendance_state_name": map[int]string{1: "يېڭى قۇرۇلغان", 2: "قاتنىشىشنى كۈتۈۋاتقان", 3: "قاتناشقان", 4: "رەت قىلغان"},
		"marketing_remain_day":            "%d كۈندىن كېيىن ئاخىرلىشىدۇ",
		"today_stop":                      "بۈگۈن ئاخىرلىشىدۇ",
		"group_marketing":                 "كوللېكتىپ پائالىيەت",
		"merchant_marketing":              "دۇكان پائالىيىتى",
		"submit_account_info":             "ئاۋۋال شەخسىي ئۇچۇرنى تولۇق يوللاڭ",
		"State":                           "ھالەت",
		"OrderNo":                         "زاكاز نومۇرى",
		"Receiver":                        "خېرىدار ئىسمى",
		"Mobile":                          "تېلېفون",
		"OrderPrice":                      "زاكاز سوممىسى",
		"OriginPrice": "ئەسلى باھاسى",
		"OriginalShipmentPrice":           "ئەسلى كىرا",
		"DiscountShipmentPrice":           "ئېتىبار قىلغان كىرا",
		"Address":                         "ئادرېس",
		"OrderTime":                       "زاكاز چۈشۈرگەن ۋاقىت",
		"Shipper":                         "يەتكۈزگۈچى",
		"OrderState":                      "زاكاز ھالىتى",
		"restaurant_types": map[int]string{
			1: "ئاشخانا",
			2: "تاللا بازىرى",
			3: "قولاي دۇكان",
		},
		"restaurant_states": map[int]string{
			0: "ئېتىك",
			1: "تىجارەت قىلىۋاتىدۇ",
			2: "ئارام ئېلىۋاتىدۇ",
		},
		"unkown":                             "نامەلۇم",
		"shipper_income_template_cant_empty": "قېلىپ چوقۇم تولدۇرۇلۇشى كېرەك",
		"market_order_exists_can_not_delete": "زاكاز چۈشۈرۈلگەن، ئۆچۈرۈشكە بولمايدۇ",
		"marketing_has_order_can_not_delete": "ID:‮%d ،بۇ پائالىيەتتە زاكاز بار، ئۆچۈرۈشكە بولمايدۇ",
		"must_be_greater_than_zero":          "%s چوقۇم نۆلدىن چوڭ بولۇشى كېرەك",
		"shipment_reduce_step_name":          "%s Km دىن %s Km غىچە يەتكۈزۈش ھەققى %s يۈەن ۋاكالەتچى كۆتۈرىدىغىنى %s  /يۈەن ئاشخانا كۆتۈرىدىغىنى %s يۈەن",
		"shipment_reduce_step_name_up":       "%s Km دىن يۇقىرىسىغا يەتكۈزۈش ھەققى %s يۈەن ۋاكالەتچى كۆتۈرىدىغىنى %s  /يۈەن ئاشخانا كۆتۈردىغىنى %s يۈەن",
		"shipper_app_location_disabled":      "يەتكۈزگۈچى ئەپنىڭ ئورۇن بەلگىلەش ئىقتىدارى ئېتىلىپ قالغان ياكى ئاشخانىنىڭ كوردىناتى توغرا بولماسلىقى مۇمكىن تەكشۈرۈپ بېقىڭ",
		"AreaID":                             "رايون ",
		"CityID":                             "شەھەر ",
		"seconds_ago":                        " سېكونت بۇرۇن",
		"minute_ago":                         " مىنۇت بۇرۇن",
		"hour_ago":                           " سائەت بۇرۇن",
		"day_ago":                            " كۈن بۇرۇن",
		"offline":                            " ئەپ ئېتىك",
		"name_ug":                            "نامى (ئۇيغۇرچە)",
		"name_zh":                            "نامى (خەنزۇچە)",
		"start_time":                         "باشلىنىش ۋاقتى",
		"end_time":                           "ئاخىرلىشىش ۋاقتى",
		"full_week_state":                    "تولۇق ھەپتە",
		"day":                                "كۈن",
		"full_day_state":                     "تولۇق كۈن",
		"time":                               "ۋاقىت",
		"auto_continue":                      "ئاپتوماتىك داۋاملىشىش",
		"status":                             "ھالەت",
		"step":                               "پەشتاق",
		"shipment_reduce_name":               "%s ~ %s km ئىچىدە %s يۈەن ئېتىبار",
		"shipment_reduce_name_up":            "%s km دىن يۇقىرىسىغا %s يۈەن ئېتىبار",
		"marketing_group_added_current_res":  "%s پائالىيەتكە قوشۇلۇپ بولغان",
		"days": map[int]string{
			0: "دۈشەنبە",
			1: "سەيشەنبە",
			2: "چارشەنبە",
			3: "پەيشەنبە",
			4: "جۈمە",
			5: "شەنبە",
			6: "يەكشەنبە",
		},
		"group_template_push_lock":  "بەش مىنۇتتىن كېيىن قايتا يوللاڭ",
		"marketing_shipment_reduce": "كېرا كېمەيتىش پائالىيىتى",
		"id_not_fund":               "IDچوقۇم يوللىنىشى كېرەك",
		"yes":                       "ھەئە",
		"no":                        "ياق",
		"this_group_marketing_stopped_enable_start": "بۇ كوللېكتىپ پائالىيەت توختىتىلدى، قايتا قوزغىتالمايسىز",
		"group_market_not_deletable":                "كوللېكتىپ پائالىيەتنى ئۆچۈرەلمەيسىز",
		"admin_create":                              "باشقۇرغۇچى قۇرغان",
		"restaurant_create":                         "دۇكاندار قۇرغان",
		"sum":                                       "جەمئىي",
		"food_price_reduce":                         "تاماق ئېتىبار سوممىسى",
		"consume_type": map[int]string{
			0: "نەقپۇل",
			3: "ۋاكالەتچى ئۈندىداردا تۆلىگەن",
		},
		"pay_types": map[int]string{
			1: "نەقپۇل",
			2: "تاماق تەڭگىسى",
			3: "ئالىپاي",
			4: "بانكا بىرلەشمىسى",
			5: "ئۈندىدار",
			6: "ۋاكالەتچى توردا تۆلىگەن",
			7: "يۈنشەنفۇ",
		},
		"order_over_time":              "ۋاقتى ئۆتتى",
		"material_start_code_is_empty": "تەشۋىقات ماتېرىيالى نومۇرى بوش قالسا بولمايدۇ",
		"material_is_taken":            "ماتېرىيال ئېلىنىپ بولغان",
		"advert_code_taken_by_others":  "باشىقىلار ئېلىپ بولغان [%s]",
		"Month":                        "ئاي",
		"SortColumn":                   "تەرتىپلەش ئىستونى",
		"SortType":                     "تەرتىپلەش شەكلى",
		"state_error":                  "ھالەت خاتا",
		"advert_material_category_has_material_can_not_delete": "ماتېرىيال تۈرىدە ماتېرىيال بار، ئۆچۈرۈشكە بولمايدۇ",
		"advert_material_has_printed_can_not_delete":           "ماتېرىيالنى بېسىپ بولغان، ئۆچۈرۈشكە بولمايدۇ",
		"advert_material_not_found":                            "تەشۋىقات ماتېرىيالى تېپىلمىدى",
		"advert_material_print_batch_states": map[int]string{
			1: "ھاسىللاش ئالدىدا",
			2: "ھاسىللىنىۋاتىدۇ",
			3: "ھاسىللىنىپ بولدى",
		},
		"advert_material_print_batch_not_found": "تەشۋىقات ماتېرىيال باسمىلاش تۈركۈمى تېپىلمىدى",
		"advert_material_unit":                  "دانە",
		"advert_material_category_is_different": "باشتا سىكاننېرلانغان ماتېرىيال بىلەن ئاخىرىدا سىكاننېرلانغان ماتېرىيال تۈرى ئوخشاش ئەمەس",
		"shipper_income_template_not_set":       "يەتكۈزگۈچىگە مائاش قېلىپى تەڭشەلمىگەن، ماتېرىيال ئېلىشقا بولمايدۇ",
		"time_range_error_3_month":              "ۋاقىت دائىرىسى 3 ئايدىن ئېشىپ كەتمەسلىكى كېرەك",
		"city_id_required":                      "شەھەر تاللاڭ",
		"area_id_required":                      "رايون تاللاڭ",
		"month_error":                           "ئاي خاتا",
		"shipper_not_exist":                     "يەتكۈزگۈچى تېپىلمىدى",
		"advert_material":                       "تەشۋىقات ماتېرىيالى",
		"advert_code":                           "تەشۋىقات كودى",

		"invalid_invite_user_fee":       "خېرىدار ئەكىرىش مۇكاپاتى ئۆلچەملىك ئەمەس",
		"invalid_invite_old_user_fee":       "سۇپىدىكى كونا خېرىدارنى ئويغىتىش مۇكاپاتى ئۆلچەملىك ئەمەس",
		"invalid_order_percent":         "خېرىدار زاكاس چۈشۈرگەندىكى مۇكاپات پىرسەنتى ئۆلچەملىك ئەمەس",
		"get_prize_user_list_failed": "ئىناۋەتلىك مۇكاپاتقا ئېرىشكەن ئەزا ئۇچۇرىنى ئېلىش مەغلۇپ بولدى",
		"empty_prize_user_list": "ئىناۋەتلىك مۇكاپاتقا ئېرىشكەن ئەزا ئۇچۇرى يوق",
		"advert_code_end_invalid":       "تەشۋىقات ماتېرىيالى ئاخىرلىش نومۇرى توغرا ئەمەس",
		"advert_code_start_end_invalid": "سىكانېرلاش تەرتىپى توغرا ئەمەس،تەرتىپ ئالمىشىپ قالدىمۇ يوق تەكشۈرۈڭ",

		"special_price_order_not_belong_shipper": "بۇ ئالاھىدە باھالىق پائالىيەتلىك زاكاز سىزگە تەۋە ئەمەسكەن",
		"admin_types": map[int]string{
			1: "ئالاھىدە باشقۇرغۇچى",
			2: "رايون باشقۇغۇچىسى",
			3: "ۋاكالەتچى",
			4: "ۋاكالەتچى قوشۇمچە",
			5: "ئاشخانا باشقۇرغۇچى",
			6: "ئاشخانا باشقۇرغۇچى قوشۇمچە",
			7: "يۈز بېشى",
			8: "يەتكۈزگۈچى باشقۇرغۇچى",
			9: "يەتكۈزگۈچى",
		},
		"customer":                       "خېرىدار",
		"advert_material_over_the_limit": "تەشۋىقات ماتېرىيال سانى %d دىن ئېشىپ كەتمىسۇن",
		"foods_preferential_states": map[int]string{
			0: "تاقاق",
			1: "ئ‍وچۇق",
		},
		"seckill_states": map[int]string{
			0: "ئېتىك",
			1: "ئوچۇق",
		},
		"seckill_review_states": map[int]string{
			1: "تەستىق كۈتۈۋاتىدۇ",
			2: "تەستىقلاندى",
			3: "رەت قىلىندى",
		},
		"user_max_order_count_is_zero":                      "ئەڭ كۆپ سېتىۋېلىش سانى قۇرۇق بولسا بولمايدۇ",
		"begin_time_is_not_valid":                           "باشلىنىش ۋاقتى ھازىردىن بۇرۇن بولسا بولمايدۇ",
		"seckill_is_exist":                                  "باشلىنىش ۋاقتى ئوخشاش بولغان تالىشىپ سېتىۋېلىش پائالىيتى مەۋجۇت",
		"price_is_less_than_food_price":                     "پائالىيەت باھاسى ئەسلىدىكى باھادىن تۆۋەن بولۇشى كېرەك",
		"food_not_exist":                                    "تاللىغان تاماق بۇ ئاشخانىغا تەۋە ئەمەسكەن",
		"seckill_update_state_failed":                       "تالىشىپ سېتىۋېلىش ھالىتىنى ئۆزگەرتىش مەغلۇب بولدى",
		"seckill_not_exist":                                 "تالىشىپ سېتىۋېلىش ئۇچۇرى تېپىلمىدى",
		"seckill_delete_failed":                             "تالىشىپ سېتىۋېلىشنى ئۆچۈرۈش مەغلۇب بولدى",
		"seckill_is_sold":                                   "پائالىيەتنى ئۆچۈرۈشكە بولمايدۇ،سەۋەبى سېتىلغان تاماق بار",
		"food_state_error":                                  "تاماق ھالىتى خاتا",
		"food_state_error_0":                                "تاماق ھالىتى خاتا، تەھرىرلىگەندىن كېيىن تەستىقلىنىشنى ساقلاڭ",
		"food_state_under_review":                           "تاماق ھالىتى تەستىقلىنىۋاتىدۇ",
		"food_state_incorrect":                              "تاماق ھالىتى 0،1 ياكى 2 بولۇشى كېرەك",
		"restaurant_state_error":                            "ئاشخانا ھالىتى خاتا",
		"seckill_end_time_is_over":                          "پائاليەت ئاخىرلىشىش ۋاقتى ئۆتۈپ كەتتى ، ئاچقىلى بولمايدۇ",
		"food_id_required":                                  "تاماق تاللاڭ",
		"discount_price_required":                           "ئېتبار باھاسىنى تولدۇرۇشى كىرەك",
		"preferential_percentage_required":                  "ئېتبار پىرسەنتىنى تولدۇرۇشى كىرەك، ئېتبار پىرسەنتى چوقۇم 0 دىن چوڭ، 100 دىن كىچىك بولىشى كېرەك",
		"start_time_must_before_end_time":                   "باشلىنىش ۋاقتى ئاخىرلىشىش ۋاقتىدىن كىچىك بولۇشى كېرەك",
		"start_date_time_must_before_end_date_time":         "باشلىنىش چىسلا ئاخىرلىشىش چىسلادىن كىچىك بولۇشى كېرەك",
		"food_referential_time_overlap":                     "تاماق ئېتبار ۋاقتى توقۇنۇشقان",
		"discount_price_must_less_than_price":               "ئېتبار باھاسى ھازىرقى باھادىن كىچىك بولۇشى كېرەك",
		"discount_timeline_not_in_restaurant_open_timeline": "ئېتبار ۋاقتى ئاشخانا ئېچىلغان ۋاقىتتا بولۇشى كېرەك",
		"restaurant_foods_not_found":                        "ئاشخانىدا مۇناسىۋەتلىك تاماق تېپىلمىدى",
		"StartDateTime":                                     "باشلىنىش چىسلا",
		"EndDateTime":                                       "ئاخىرلىشىش چىسلا",
		"OrderCountPerDay":                                  "كۈنلىك سېتىۋېلىش سانى",
		"FoodID":                                            "تاماق",
		"PreferentialStyle":                                 "ئېتبار ",
		"MaxOrderCount":                                     "ئەڭ كۆپ سېتىۋېلىش سانى",
		"end_date_time_must_after_today":                    "ئاخىرلىشىش چىسلا بۈگۈن ياكى بۈگۈندىن كىيىن بولۇشى كېرەك",
		"can_not_update_expired_foods_preferential":         "ئاخىرلاشقان پائالىيەتكە مەشغۇلات قىلىشقا بولمايدۇ",

		"function_disabled":                         "بۇ ئىقتىدار تاقىۋېتىلدى ",
		"need_to_update_shipper_update_sex_and_age": "سۇپا ئىقتىدارىنى  مۇكەممەلەشتۈرۈش ئۈچۈن،  سۇپىمىزدىكى بارلىق يەتكۈزگۈچىلەرنىڭ ئۇچۇرىنى تولۇقلاش ئىھتىياجى تۇغۇلدى، سىزنىڭ خىزمىتىمىزگە ماسلىشىپ، مۇناسىۋەتلىك ئۇچۇرلارنى ۋاقتىدا تولۇقلىشىڭىزنى ئەسكەرتىمىز.",
		"msg_title":                      "ئەسكەرتىش",
		"yes_btn":                        "ماقۇل",
		"no_btn":                         "ياق",
		"age_error":                      "ياش ئۆلچەملىك دائىرىدە ئەمەس",
		"Age":                            "يېشى",
		"Sex":                            "جىنسى",
		"invite_user_fee_invalid":        "يېڭى ئەكىرگەن خېرىدارغا بىردىغان مۇكاپاتنى 1 يۈەندىن كىچىك تەڭشىيەلمەيسىز",
		"order_percent_invalid":          "يېڭى ئەكىرگەن خېرىدار چۈشۈرگەن زاكازغا بىردىغان پايدا پىرسەنتىنى 2 پىرسەنتتىن كىچىك تەڭشىيەلمەيسىز",
		"invite_old_user_fee_invalid":    "ئويغىتىلغان كونا خېرىدارغا بىردىغان پۇلنى 0.2 يۈەندىن كىچىك تەڭشىيەلەيسىز",
		"order_old_user_percent_invalid": "ئويغىتىلغان خېرىدار چۈشۈرگەن زاكازغا بىردىغان پايدا پىرسەنتىنى 1 پىرسەنتىنى كىچىك تەڭشىيەلمەيسىز",
		"shipper_income_template_not_set_can_not_use":       "يەتكۈزگۈچىگە مائاش قېلىپى تەڭشەلمىگەن،بۇ ئىقتىدارنى ئىشلىتەلمەيسىز",
		"package_delivery_in_progress_can_not_return_order": "زاكاز يەتكۈزۈلىۋاتىدۇ،قايتۇرۇالمايسىز،باشقۇرغۇچى بىلەن ئالاقىلىشىڭ",
		"cash_order_not_pay":                                "پۇل تۆلىمىگەن نەق پۇللۇق زاكازنى تاماملىيالمايسىز",
		"total_count_is_less_than_user_max_order_count":     "جەمئىي سانى يەككە ئەزا سېتىۋېلىش سانىدىن ئاز بولسا بولمايدۇ",
		"seckill_food_not_in_activity_range":                "پائالىيەت ۋاقتى تاماق چىقىش ۋاقتىدا ئەمەس",
		"food_not_in_activity_range":                        "پائالىيەت تاماق چىقىش ۋاقتىدا ئەمەس،تاماق چىقىش ۋاقتى [%s] تىن [%s] گىچە",
		"old_shipper_not_exists":                            "بۇرۇنقى يەتكۈزگۈچى مەۋجۇت ئەمەس",
		"new_shipper_not_exists":                            "ھازىرقى يەتكۈزگۈچى مەۋجۇت ئەمەس",
		"old_shipper_not_disabled":                          "بۇرۇنقى يەتكۈزگۈچى ئوچۇق ھالەتتە ئىكەن،ئالماشتۇرالمايسىز",
		"new_shipper_is_disabled":                           "ھازىرقى يەتكۈزگۈچى ئېتىك ھالىتىدە ئىكەن،ئالماشتۇرالمايسىز",
		// 1:老板，2:店铺管理员，3:财务管理员，4:订单管理员，5：客服
		"merchant_type_name": map[int]string{
			1: "خوجايىن",
			2: "دۇكان باشقۇرغۇچى",
			3: "مالىيە باشقۇرغۇچى",
			4: "زاكاز باشقۇرغۇچى",
			5: "تېلېفونىست",
		},
		"shop_have_order_enable_delete": "دۇكاندا بۈگۈنگە تەۋە زاكاز بار ئۆچۈرەلمەيسىز",
		"invalid_password":              "مەخپىي نومۇر 6 خانىدىن تۆۋەن بولسا بولمايدۇ ھەم چۇقۇم ئاز دېگەندە ئىككى ھەرپنى ئۆز ئىچىگە ئېلىشى كېرەك",
		"shipping_area_steps_error":     "ئارىلىق پەشتىقى توغرا ئەمەس",
		"rush_hour_can_not_operate":     "يۇقىرى پەللە مەزگىلىدە بۇ مەشغۇلاتنى قىلىشقا بولمايدۇ, (چۈشتە سائەت 12 دىن 16 گىچە، چۈشتىن كېيىن سائەت 18 دىن 21 گىچە)",
		"amap_key_error":                "گاۋدې خەرىتە تەڭشىكىدە مەسىلە بار، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ",
		"restaurant_state_close":        "ئاشخانىڭىز ئېتىك ھالەتتە ئىكەن، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ",
		"ResID":                         "دوكان ID",
		"Rid":                           "دوكان ID",
		"FoodsCategoryID":               "تاماق تۈرى",
		"FoodId":                        "تاماق ID",
		"Position":                      "ئورۇن",
		"NewPassword":                   "يېڭى مەخپى نومۇرى",
		"OrderID":                       "زاكاز ID",

		"please_try_again_later":          "تەستىق كودى يوللىنىپ بولدى، سەل تۇرۇپ قايتا سىناڭ.",
		"send_code_fail":                  "تەستىق كودى يوللاش مەغلۇپ بولدى.",
		"send_code_success":               "تەستىق كودى يوللاش مۇۋەپپەقىيەتلىك بولدى.",
		"Password":                        " مەخپى نومۇرى ",
		"verify_success":                  "تەستىق كودى توغرا",
		"verify_code_error":               "تەستىق كودى خاتا",
		"verify_code_fail":                "تەستىق كودى تىپىلمىدى",
		"verify_code_not_found":           "تەستىق كودى تىپىلمىدى",
		"password_update_success":         "مەخپى نومۇر ئۆزگەرتىلىدى",
		"verify_code_not_verify":          "تەستىق كودى خاتا، ياكى تەستىق كودىنىڭ ۋاقتى ئۆتۈپ كەتكەن",
		"function_maintenance":            "بۇ ئىقتىدار ئاسرىلىۋاتىدۇ",
		"food_open_time_error":            "«%s»نىڭ تەييارلىنىش ۋاقتى ئاشخانا ئېچىلىش دائىرسىدە ئەمەسكەن، ئۆزگەرتىپ قايتا يوللاڭ",
		"admin_belong_merchant_not_found": "باشقۇرغىچىغا تەۋە ئاشخانا تىپىلمىدى",
		"validator_should_be_int":         "\"%s\"چوقۇم سان بولۇشى كىرەك",
		//"info_in_review":"信息在审核中，请勿重复提交",
		"info_in_review":                   "ئۇچۇر تەستىقلىنىۋاتىدۇ، قايتا يوللىماڭ",
		"DescriptionUg":                    "ئۇيغۇرچە چۈشەندۈرلىشى",
		"DescriptionZh":                    "دۆلەت تىلى چۈشەندۈرلىشى",
		"BeginTime":                        "باشلىنىش ۋاقتى",
		"EndTime":                          "ئاخىرلىشىش ۋاقتى",
		"AllFoodsId":                       "تاماق تۈرى",
		"Price":                            "باھاسى",
		"ReadyTime":                        "تەييارلىنش ۋاقتى",
		"Image":                            "رەسىم",
		"password_not_match":               "ئىككى يېڭى مەخپى نومۇر ئوخشاش ئەمەس",
		"old_password_error":               "ئالدىنقى مەخپى نومۇر خاتا",
		"OldPassword":                      "ئالدىنقى مەخپى نومۇر",
		"customer_order_mobile":            "تاماق تاپشۇرۇۋالغۇچى نومۇرى",
		"user_mobile":                      "ئەسلى زاكاز چۈشۈرگەن نومۇرى",
		"order_dealer_mobile":              "زاكاز بىرتەرەپ قىلغۇچى نومۇرى",
		"admin_mobile":                     "باشقۇرغۇچى نومۇرى",
		"boss_mobile":                      "خوجايىن نومۇرى",
		"order_is_overtime":                "پۇل تۆلەش ۋاقتى ئۆتۈپ كەتتى",
		"lottery_activity_is_expired":      "مۇكاپاتلىق چەك تارتىش پائالىيتى ۋاقتى ئۆتۈپ كەتتى",
		"total_salary":                     "ئالىدىغان مائاش",
		"total_order_count":                "ئومۇمىي يەتكۈزگەن زاكاز",
		"amount_order":                     "زاكاز كىرىمى",
		"amount_reward":                    "مۇكاپات كىرىمى",
		"complain_count":                   "جەمئىي ئەرز",
		"count_comment_bad":                "ناچار باھا",
		"amount_complain":                  "ئەرزگە تۇتۇلغىنى",
		"late_order_count":                 "جەمئىي كېچىككەن زاكاز",
		"amount_late":                      "كېچىككەن زاكازغا تۇتۇلغىنى",
		"amount_tips":                      "تارتۇقلاش كىرىمى",
		"amount_spectal_time":              "ئالاھىدە يەتكۈزۈش كىرىمى",
		"amount_special_weather":           "ئالاھىدە ھاۋارايى كىرىمى",
		"amount_comment_good":              "ياخشى باھا كىرىمى",
		"amount_comment_bad":               "ناچار باھاغا تۇتۇلغىنى",
		"comment_good_count":"ياخشى باھا سانى",
		"punishment":                       "جەمئىي تۇتۇلغان پۇل",
		"amount_invite_user":               "خېرىدار كۆپەيتىش كىرىمى",
		"amount_order_tips":                "خېرىدار چۈشۈرگەن زاكازدىن ئېرىشكەن كىرىم",
		"amount_special_price_order":       "ئالاھىدە باھالىق پائالىيەت يەتكۈزۈش كىرىمى",
		"count_special_price_order":        "ئالاھىدە باھالىق پائالىيەت يەتكۈزۈش سانى",
		"time_confilict_with_res":          "ئاشخانىنىڭ ئېچىلىش ۋاقتىغا ماس كەلمىدى",
		"time_param_format_error":          "ۋاقىت شەكلى خاتا",
		"pay_platform_is_not_lakala":       "بۇ زاكازغا لاكالادا پۇل تۆلىيەلمەيسىز",
		"your_phone_has_non_compliant_app": "تېلفۇنىڭىزدا [%s] قاتارلىق قائىدىگە خىلاپ ئەپ دىتالى باركەن، ئۆچۈرۋەتكەندىن كېيىن يەتكۈزگۈچى ئەپنى نورمال ئىشلىتەلەيسىز",
		"promotion_create_disabled":        " بۇ رايوندا پائالىيەت قۇرۇش ۋاقتىنچە توختىتىلدى",
		"promotion_update_disabled":        " بۇ رايوندا پائالىيەت تەھرىرلەش ۋاقتىنچە توختىتىلدى",
		"customer_order_address":           "خېرىدارنىڭ زاكاز يەتكۈزىدىغانغا تاللىغان ئادرېسى",
		"customer_app_pos":                 "خېرىدارنىڭ نۆۋەتتىكى ئادرېسى",
		"customer_old_address":             "خېرىدارنىڭ بۇرۇن چۈشۈرگەن زاكاز ئادرېسلىرى",
		"order_is_closed_can_not_pay":      "زاكاز تاقالغان،پۇل تۆلەشكە بولمايدۇ",
		"order_is_canceled_can_not_pay":    "زاكاز قايتۇرۇۋېتىلگەن،پۇل تۆلەشكە بولمايدۇ",
		"not_found_state_params":           "ھالەت ئۇچۇرىنى يوللىماپسىز",
		"shipper_attendance_off":           "سىز تېخى ئىشقا چىقماپسىز،ئاۋال يوقلىمىدىن ئىشقا چىقىپ ئاندىن تالىشىڭ",
		"insurance_already_buy":            "ئەتىدىكى سۇغۇرتا ئېلىنىپ بولغان",
		"shipper_not_insured":              "بۈگۈن سۇغۇرتا سېتىۋېىلنمىغان،زاكاز تۇتۇشقا بولمايدۇ",
		"insurance_not_buy":                "سۇغۇرتا سېتىۋېللىنمىغان",
		"insurance_send_can_not_cancel":    "سۇغۇرتا يوللىنىپ بولغان بىكار قىلىشقا بولمايدۇ",
		"lottery_activity_id_required":         "پائالىيەت ID قۇرۇق",
		"lottery_activity_prize_list_required": "مۇكاپات تىزىملىكى قۇرۇق",
		"lottery_activity_prize_stock_not_enough":             "%s مۇكاپات يېتىشمەيدۇ، ئامباردىكى قالدۇق: %d",
		"lottery_activity_not_found":                          "پائالىيەت تېپىلمىدى",
		"lottery_activity_state_error":                        "پائالىيەت ھالىتىدە خاتالىق بار",
		"lottery_activity_prize_range_error":                  "%s مۇكاپات دائىرىسى %d دىن كىچىك بولسا بولمايدۇ",
		"lottery_activity_prize_start_index_must_bigger_than": "%s مۇكاپات باشلىنىش دائىرىسى %d دىن چوڭ بولىشى كېرەك",
		"lottery_activity_prize_range_must_bigger_than":       "%s بۇكاپات چىقىش دائىرىسى مۇكاپات سانىنىڭ 3 ھەسسىدىن چوڭ بولىشى كېرەك",
		"lottery_activity_prize_id_not_match":                 "مۇكاپات ID خاتا",
		// 生成中奖用户失败，请扩大中奖区域:开始位置:%d, 结束位置: %d
		"lottery_activity_generate_winners_fail": "تەلەيلىك ئەزا نومۇرىنى ھاسىل قىلىش مەغلۇپ بولدى، مۇكاپات چىقىش دائىرىسىنى كېڭەيتىڭ باشنىقىش دائىرىسى: %d ئاخىرلىشىش دائىرىسى: %d",
		"lottery_activity_prize_get_valid_data_failed": "ئىناۋەتلىك مۇكاپات ئۇچۇرى تېپىلمىدى",
		"insurance_state": map[int]string{
			0: "يېڭى قۇرۇلغان",
			1: "سۇغۇرتا تاپشۇرىدۇ",
			2: "سۇغۇرتىغا يوللاش ئالدىدا",
			3: "سۇغۇرتا سېتىۋېلىندى",
			4: "سۇغۇرتىدىن ئۆتمىدى",
			5: "ئارام ئالىدۇ",
		},
		"real_name_states": map[int]string{
			0: "يوللانمىغان",
			1: "تەستىقلىنىۋاتىدۇ",
			2: "تەستىقتىن ئۆتمىدى",
			3: "تەستىقتىن ئۆتتى",
		},
		"collection_merchant_alter_state": map[int]string{
			0: "",
			1: "تىجارەت كىنىشكىسى ۋە قانۇنىي ۋەكىلى ئۆزگەردى",
			2: "بانكا ئۇچۇرى ئۆزگەردى",
			3: "باشقۇرغۇچى ئۇچۇرى ئۆزگەردى",
			4: "كىنىشكا ئۇچۇرى ئۆزگەردى",
			5: "دۇكان ئۇچۇرى ئۆزگەردى",
		},
		"page_errors": map[int]string{
			1: "دۇكاندار ئۇچۇرى",
			2: "كىملىك ئۇچۇرى",
			3: "ھېسابات ئۇچۇرى",
			4: "دۇكان ئۇچۇرى",
			5: "باشقۇرغۇچى ئۇچۇرى",
			6: "ئىجازەتنامە ئۇچۇرى",
			7: "خىزمەتچى ئۇچۇرى",
		},
		"verify_state_name": map[int]string{
			0: "تەستىققا يوللاش",
			1: "كېلىشىم ئىمزالاۋاتىدۇ",
			2: "تورغا كىرىش مۇۋەپپەقىيەتلىك بولدى",
			3: "تورغا كىرىش مەغلۇپ بولدى",
		},
		"id_card_info_not_exist": "كىملىك ئۇچۇرى تولۇق ئەمەس،تولدۇرۇپ بولۇپ ئاندىن بانكا ئۇچۇرىنى يوللاڭ",
		"amount_insurance":       "سۇغۇرتىغا تۇتۇلغان پۇل",
		"real_name_alert":        "سالاھىيەت دەلىللەش تاماملانمىغان، ئۇچۇرىڭىزنى توغىرلىغاندىن كېيىن ئاندىن سۇغۇرتا سېتىۋېلىڭ",
		"real_name_alert_info": []map[string]interface{}{
			map[string]interface{}{
				"number":     0,
				"title":      "سالاھىيەت دەلىللەڭ",
				"content":    "سىز تېخى سالاھىيەت دەلىللىمەپسىز",
				"ok_btn":     "سالاھىيەت دەلىللەش",
				"cancel_btn": "",
			},
			map[string]interface{}{
				"number":     1,
				"title":      "سالاھىيەت دەلىللەش",
				"content":    "سىز تېخى سالاھىيەت دەلىللىمەپسىز",
				"ok_btn":     "سالاھىيەت دەلىللەش",
				"cancel_btn": "",
			},
			map[string]interface{}{
				"number":     2,
				"title":      "سالاھىيەت دەلىللەش",
				"content":    "سىز تېخى سالاھىيەت دەلىللىمەپسىز",
				"ok_btn":     "سالاھىيەت دەلىللەش",
				"cancel_btn": "",
			},
			map[string]interface{}{
				"number":     3,
				"title":      "سالاھىيەت دەلىللەش",
				"content":    "سالاھىيەت دەلىلىلىنىپ بولدى",
				"ok_btn":     "سالاھىيەت دەلىللەش",
				"cancel_btn": "",
			},
		},
		"insurance_alert_info": []map[string]interface{}{
			map[string]interface{}{
				"number":     0,
				"title":      "سۇغۇرتا تاپشۇرۇڭ",
				"content":    "ئەتىدىكى خىزمىتىڭىزنى نورمال ئېلىپ بېرىش ئۈچۈن سۇغۇرتا تاپشۇرۇڭ،بېيجىڭ ۋاقتى 23:00 دىن كېيىن ئاپتوماتىك تاپشۇرۇلىدۇ",
				"ok_btn":     "تاپشۇرۇش",
				"cancel_btn": "ئەتە ئارام ئالىمەن",
			},
			map[string]interface{}{
				"number":     1,
				"title":      "سۇغۇرتا تاپشۇرۇڭ",
				"content":    "ئەتىدىكى خىزمىتىڭىزنى نورمال ئېلىپ بېرىش ئۈچۈن سۇغۇرتا تاپشۇرۇڭ،بېيجىڭ ۋاقتى 23:00 دىن كېيىن ئاپتوماتىك تاپشۇرۇلىدۇ",
				"ok_btn":     "تاپشۇرۇش",
				"cancel_btn": "ئەتە ئارام ئالىمەن",
			},
			map[string]interface{}{
				"number":     2,
				"title":      "سۇغۇرتا تاپشۇرۇڭ",
				"content":    "ئەتىدىكى خىزمىتىڭىزنى نورمال ئېلىپ بېرىش ئۈچۈن سۇغۇرتا تاپشۇرۇڭ،بېيجىڭ ۋاقتى 23:00 دىن كېيىن ئاپتوماتىك تاپشۇرۇلىدۇ",
				"ok_btn":     "تاپشۇرۇش",
				"cancel_btn": "ئەتە ئارام ئالىمەن",
			},
			map[string]interface{}{
				"number":     3,
				"title":      "سۇغۇرتا تاپشۇرۇڭ",
				"content":    "ئەتىدىكى خىزمىتىڭىزنى نورمال ئېلىپ بېرىش ئۈچۈن سۇغۇرتا تاپشۇرۇڭ،بېيجىڭ ۋاقتى 23:00 دىن كېيىن ئاپتوماتىك تاپشۇرۇلىدۇ",
				"ok_btn":     "تاپشۇرۇش",
				"cancel_btn": "ئەتە ئارام ئالىمەن",
			},
			map[string]interface{}{
				"number":     4,
				"title":      "سۇغۇرتا ئۆتمىدى",
				"content":    "سۇغۇرتىڭىز ئۆتمىدى، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ",
				"ok_btn":     "تەپسىلاتى",
				"cancel_btn": "ئەتە ئارام ئالىمەن",
			},
		},

		"try_again_after_period_time":                                       "%s سكىنونتتىن كېيىن قايتا سىناڭ",
		"two_passwords_are_inconsistent":                                    "ئىككى قېتىملىق مەخپى نۇمۇر ئوخشاش ئەمەس",
		"the_random_string_has_expired_please_resend_the_verification_code": "ئىختىيارىي ھەرپ تىزمىسىنىڭ ۋاقتى ئۆتۈپ كەتتى، دەلىللەش نومۇرىنى قايتىدىن يوللاڭ",
		"insurance_over_time_can_not_buy":                                   "سۇغۇرتا سېتىۋېلىش ۋاقتى ئۆتۈپ كەتتى",
		"insurance_not_approved":                                            "سۇغۇرتىڭىز ئۆتمىدى،باشقۇرغۇچى بىلەن ئالاقىلىشىڭ",
		"insurance_before_time_can_not_buy":                                 "سۇغۇرتا سېتىۋېلىش ۋاقتى كەلمىدى [%s] تىن كېيىن سېتىۋېلىشقا بولىدۇ",
		"insurance_not_exist":                                               "[%s] كۈنىدىكى سۇغۇرتا ئۇچۇرى تېپىلمىدى",
		"insurance_state_not_ok":                                            "[%s] كۈنىدىكى سۇغۇرتا ھالىتى توغرا ئەمەس",
		"seckill_run_state_name": map[int]string{
			0: "باشلانمىغان",
			1: "باشلانغان",
			2: "ئاخىرلاشقان",
		},
		"exist_conflict_seckill":               "[%s] بۇ تاماقنىڭ باشلىنىش ۋاقتى ئوخشاش، ھالىتى ئۇچۇق تالىشىپ سېتىۋېلىش پائالىيىتى بار",
		"exist_conflict_special":               "[%s] بۇ تاماقنىڭ باشلىنىش ۋاقتى ئوخشاش، ھالىتى ئۇچۇق ئالاھىدە باھالىق پائالىيىتى بار",
		"seckill_time_not_in_foods_time_range": "پائالىيەت ۋاقتى چۇقۇم تاماق نۇرمال چىقىش ۋاقتى ئىچىدە تەڭشىلىشى كېرەك",
		"seckill_time_must_after_now_time":     "پائالىيەت باشلىنىش ۋاقتى چۇقۇم ھازىرقى ۋاقىتتىن چوڭ بولۇشى كېرەك",
		"seckill_order_time_must_after_end_time":     "يەتكۈزۈش ۋاقتى چۇقۇم پائالىيەت ئاخىرلىشىش ۋاقىتتىن چوڭ بولۇشى كېرەك",
		"same_time_error":     "پائالىيەت ۋاقتى تەكرار بولۇپ قالغان",
		"idcard_repeat":                                                     "بۇ كىملىك نومۇرى باشقا يەتكۈزگۈچىنىڭ نامىدا تەستىقتىن ئۆتۈپ بولغان",
		"insurance_request_sent":                                            "ئەتىدىكى سۇغۇرتا ئېلىش ئىلتىماسى يوللىنىپ بولدى",
		"shipper_not_real_name":                                             "ھەقىقىي ئىسىم دەلىللەش تاماملانمىغان،زاكاز تۇتۇشقا بولمايدۇ",
		"permit_start_time_err":                                             "كىنىشكە ئىناۋەتلىك ۋاقتىنىڭ باشلىنىش ۋاقتى خاتا",
		"permit_end_time_err":                                               "كىنىشكە ئىناۋەتلىك ۋاقتىنىڭ ئاخىرلىشىش ۋاقتى خاتا",
		"info_update_err":                                                   "ئۇچۇر يېڭىلاش مەغلۇپ بولدى",
		"request_denied_by_admin":                                           "قايتۇرۇش ئىلتىماسى باشقۇرغۇچى تەرىپىدىن رەت قىلىندى",
		"try_after_some_time":                                               "دىن كېيىن قايتا سىناڭ [%s]",
		//'显示位置 1.首页弹窗 2.首页内容上方 3.首页弹窗 和 首页内容上方',
		"lottery_activity_pos": map[int]string{
			1: "سەكرەتمە ئېلان",
			2: "باش بەت",
			3: "سەكرەتمە ئېلان ۋە باش بەت",
		},
		"order_amount_cannot_less_than_discount_amount":" ئېتىبار سوممىسى  زاكازسوممىسىدەن چوڭ بولسا بولمايدۇ",
		"activity_already_started_enable_delete":"پائالىيەت باشىلىنىپ بولغان،ئۆچۈرەلمەيسىز",
		"prize_already_used_enable_delete":"پائالىيەتكە باغلىنىپ بولغان مۇكاپاتنى ئېتەلمەيسىز",
		"prize_already_used_enable_edit":"پائالىيەتكە باغلىنىپ بولغان مۇكاپاتنى ئۆزگەرتەلمەيسىز",
		"prize_already_used_enable_close":"پائالىيەتكە باغلىنىپ بولغان مۇكاپاتنى ئىتەلمەيسىز",
		"coupon_already_used_cannot_delete":"ئىتىبار بېلىتى ئىشلىتىلىپ بولدى يۇيىۋېتىشكە بولمايدۇ",
		"coupon_already_used_cannot_update":"ئېتىبار بېلىتى ئىشلىتىلىپ بولدى تەھرىرلەشكە بولمايدۇ",
		"end_active_cannot_open":"ئاخىرلاشقان پائالىيەتنى ئاچالمايسىز",
		"end_active_cannot_edit":"ئاخىرلاشقان پائالىيەتنى ئۆزگەرتەلمەيسىز",
		"activity_time_conflict":"ئوخشاش ۋاقىت دائىرىسىدە ئۇچۇق پائالىيەت بار،ھالىتىنى ئاچالمايسىز",

		"already_send_request_once":"بىر قېتىم ئىلتىماس يوللىنىپ بولغان،قايتا يوللىماڭ",
		"already_finish_order":"تاماملىنىپ بولغان زاكازنى ئىلتىماس قىلالمايسىز",
		"prolong_booking_time_failed":"يەتكۈزۈش ۋاقتىنى ئۇزارتىش مەغلۇب بولدى",
		"set_channel_name":map[int]string{
			0:"يەتكۈزگۈچى تالاشقان",
			1:"يەتكۈزگۈچى تالاشقان",
			2:"باشقۇرغۇچى تەقسىملىگەن",
			3:"سىستىما تەقسىملىگەن",
			4:"خېرىدار بەلگىلىگەن",
		},
		"excel_file_empty":"Excelھۆججىتى مەزمۇنى توغرا ئەمەس",
		"insurance_date_error":"Excelھۆججىتىىدىكى چېسلا توغرا ئەمەس",
		"foods_group_state": map[int]string{
			0: "ئېتىك",
			1: "ئوچۇق",
		},
		"foods_group_review_state": map[int]string{
			1: "تەستىق كۈتۈۋاتىدۇ",
			2: "تەستىقلاندى",
			3: "رەت قىلىندى",
		},
		"restaurant_foods_state": map[int]string{
			0: "ئېتىك",
			1: "ئوچۇق",
			2: "سېتىلىپ بولدى",
			3: "تەستىق كۈتۈۋاتىدۇ",
		},
		"cant_delete_bind_foods_group":"تاماق باغلىنىپ بولغان گۇرۇپنى ئۆچۈرەلمەيسىز",
		"chance_state_names":map[int]string{
			1:"چەك تارتىشنى كۈتۈۋاتىدۇ",
			2:"چەك تارتىلىپ بولدى",
			3:"مۇكاپات ئېلىنىپ بولدى",
			4:"مۇكاپات تېخى ئېلىنمىدى",
			5:"زاكازنىڭ ۋاقتى ئۆتۈپ كەتتى",
			6:"زاكاز قايتۇرۇلدى",
		},
		"lottery_prize_level":"%d-دەرىجىلىك مۇكاپات",
		"you_have_some_order_not_finish":"قۇلىڭىزدا تاماملىمىغان زاكاز بار،ئاۋال زاكازنى بىر تەرەپ قىلىپ ئاندىن بۇ مەشغۇلاتنى قىلىڭ",

		"lottery_used":"چەك تارتىلىپ بولغان",
		"coupon_used":"ئېتىبار بېلىتى ئىشلىتىلگەن",
		"refunded":                   "پۇل قايتۇرۇلۇپ بولغان",
		"chance_type_names":map[int]string{
			1:"چەك سېتىۋېلىپ ئېرىشكەن",
			2:"يۇقىرى سوممىلىق زاكاز چۈشۈرۈپ ئېرىشكەن",
			3:"ھەمبەھىرلىگەندىن كېيىن قارشى تەرەپ يۇقىرى سوممىلىق زاكاز چۈشۈرۈپ ئېرىشكەن",
			4:"ھەمبەھىرلىگەندىن كېيىن قارشى تەرەپ چەك سېتىۋېلىپ ئېرىشكەن",
		},
		"AutoDispatchWait":"سېستىما ئاپتۇماتىك بىر تەرەپ قىلىش ۋاقتى",
		"seckill_max_order_count_error":"ئەڭ كۆپ سېتىۋېلىش سانىنى توغرا كىرگۈزۈڭ",
		"seckill_price_error":"پائالىيەت باھاسىنى توغرا كىرگۈزۈڭ",
		"no_seckill_params":"پائالىيەت ئۇچۇرىنى تۇلۇق كىرگۈزۈڭ",
		"TotalCount":"جەمئى سانى",
		"restaurant_close_state":"دۇكان ھالىتى ئىتىك",
		"foods_close_state":"تاماق ھالىتى ئىتىك",
		"normal":"نورمال",

		"price_food_in_progress_cant_refund":"پائالىيەت ۋاقتى توشمىدى،پۇل قايتۇرۇشقا بولمايدۇ",
		"price_markup_food_payed":map[int]string{
			0:"پۇل تۆلىمىدى",
			1:"پۇل تۆلىدى",
		},
		"price_markup_food_refunded":map[int]string{
			0:"پۇل قايتۇرۇلمىدى",
			1:"پۇل قايتۇرۇلدى",
		},
		"price_markup_food_state": map[int]string{
			1: "پۇل تۆلەشنى كۈتۈپ تۇرۋاتىدۇ",
			2: "دۇكاندار جەزىملەشتۈرىۋاتىدۇ",
			3: "دۇكاندار جەزىملەشتۈردى",
			4: "دۇكانداررەتقىلدى",
			5: "پۇل قايتۇرۇلدى",
		},
		"price_markup_food_running_state": map[int]string{
			1: "نۇرمال",
			2: "توختىتىلدى",
		},
		"price_markup_conflict":             "مۇشۇ ۋاقىت دائىرىسىگە تەڭشەلگەن مېڭىۋاتقان بار <<%s>> ",
		"price_markup_stop_enable_edit":             "پائالىيەت ئاخىرلاشقان تەھرىرلىيەلمەيسىز",
		"payed_price_markup_stop_enable_edit": "پۇل تۆلەپ بۇلغان پائالىيەتنىڭ پەقەت ۋاقتىنىلا ئۆزگەرتەلەيسىز",
		"missing_parameter":"پارامېتىر تولۇق يوللانمىغان",

		"already_have_the_same_zh_name":"خەنزۇچە ئوخشاش  ئىسىم مەۋجۇت",
		"already_have_the_same_ug_name":"ئۇيغۇرچە ئوخشاش  ئىسىم مەۋجۇت",
		"try_again_refresh_page":"بەتنى يىڭىلاپ قايتا سىناڭ",

		"attendance_off":"ئىشقا چىقمىغان",

		"price_markup_total_count_is_not_enough":"سىتىۋالغان تاماقنىڭ ئامبار قالدۇقى يېتەرلىك ئەمەس، ئامبار قالدۇقى سانى %d",
		"SmsCode":"دەلىللەش كودى",
		"markup_sale":"سۇپا ساتقىنى",
		"merchant_sale":"دۇكاندار ساتقىنى",
		"in_price":"كىرىش باھاسى",
		"total_count":"جەمئى سانى",
		"in_price_must_less_food_price":"كىرىش باھاسى تاماق باھاسىدىن چوڭ بولسا بولمايدۇ",
		"price_markup_food_is_not_running":"سىتىۋالغان تاماقنىڭ مىڭىۋاتقان ھالىتىنى تەكشۈرۈڭ",
		"price_markup_food_discount_price_gte_in_price":"ئىتىبار باھا سىتىۋالغان تاماقنىڭ باھا سىدىن يۇقىرى",
		"not_stopped_yet":"پائالىيەت تېخى توختىتىلمىغان",
		"no_pay_log":"پۇل تۆلەش خاتىرىسى تېپىلمىدى",
		"price_markup_seckill_not_allow_edit":"ۋاكالەتچى سېتىۋالغان تاماق پائالىيىتىنى ئۆزگەرتەلمەيسىز",

		"snow_game":"قار تۇتۇش ئويۇنى ئېتىبارى",
		"ended_active_enable_edit":"ئاخىرلاشقان پائالىيەت ئۇچۇرىنى ئۆزگەرتەلمەيسىز",
		"time_enable_out_price_markup_time_range":"پائالىيەتنىڭ ۋاقتى سېتىۋالغان تاماق پائالىيىتىنىڭ ۋاقىت دائىرىسىدە بۇلىشى كىرەك",
		"max_order_count_cannot_exceed_markup_count":"ئەڭ كۆپ سېتىۋېلىش سانى باھاسىنى ئۆستۈرۈپ ساتقان ئامبار قالدۇقى سانىدىن ئېشىپ كەتمەسلىكى كېرەك",

		"update_fail":"تەھرىرلەش  مەغلۇپ بولدى ",
		"create_fail":"قۇرۇش مەغلۇپ بولدى ",
		"delete_fail":"يۇيىۋېتىش مەغلۇپ بولدى",
		"query_fail":"تەكشۈرۈش مەغلۇپ بولدى",
		"please_select_an_activity":"بىر پائالىيەت تاللاڭ ",

		"part_refund_food_count_error":"قايتۇرىدىغان سانى توغرا ئەمەس",
		"part_refund_food_price_error":"قايتۇرىدىغان پۇل توغرا ئەمەس",
		"not_fund_admin":   "باشقۇرغۇچى ئۇچۇرى تېپىلىدى",
		"part_refund_amount_error":"پۇل قايتۇرۇش سوممىسى توغرا ئەمەس",

		"max_duration_err":"ئۇزارتىش ۋاقتى %d مىنۇتتىن ئېشىپ كەتسە بولمايدۇ",
		"open_state_out_off_price_markup_stock":"بۇ پائالىيەتنى ئاچسىڭىز،سېتىۋالغان تاماقنىڭ جەمئىي ئامبار سانىدىن ئېشىپ كىتىدىكەن،تەڭشىگەن پائالىيەتلىرىڭىزنىڭ ئامبار ئىشلىتىش ئەھۋالىنى تەكشۈرۈڭ",
		"theme_activity_not_found":     "تېما پائالىيىتى مەۋجۇت ئەمەس",
		"theme_activity_time_conflict": "بۇ ۋاقىت ئارىلىقىدا باشقا تېما پائالىيىتى بار",
		"shop_not_verified": "ئاشخانا سالاھىيەت ئۇچۇرى تولۇق ئەمەسكەن ، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ",
		"no_shipper_info":"يەتكۈزگۈچى ئۇچۇرى تىپىلمىدى",
		"gender":map[int]string{
			0: "نامەلۇم",
			1: "ئەر",
			2: "ئايال",
		},
		"can_not_part_refund_twice":"بۇ زاكازنى يەنە بىر قېتىم قىسمەن پۇل قايتۇرغىلى بولمايدۇ",
		"ranking_order_activity_time_error":"پائالىيەت قىزىتىش ۋاقتى باشلىنىش ۋاقتىدىن كىچىك بولۇشى كېرەك",
		"ranking_order_activity_prize_list_error":"مۇكاپات  ئىرىشىش زاكاز تەرتىبىنى تەكشۈرۈڭ",
		"ranking_order_activity_prize_list_empty":"مۇكاپات قۇرۇق بۇلسا بولمايدۇ",
		"ranking_order_activity_time_cross":"بۇ ۋاقتى دائىرىسىدا باشقا پائالىيەت بار",
		"ranking_order_activity_state_names":map[int]string{
			0:"ئىتىك",
			1:"ئوچۇق",
			2:"توختىتىلدى",
			3:"ئاخىرلاشقان",
		},
		"activity_already_started_can_not_delete":"پائالىيەت باشلىنىپ بولغان،ئۆچۈرەلمەيسىز",
		"activity_state_open_can_not_delete":"پائالىيەت ئوچۇق ،ئۆچۈرەلمەيسىز",
		"activity_has_record_can_not_delete":"پائالىيەت ئىشلىتىلىپ بولدى،ئۆچۈرەلمەيسىز",
		"activity_already_started_can_not_update":"پائالىيەت باشلىنىپ بولغان،تەھرىرلەش مەغلۇپ بولدى",
		"ranking_order_activity_prize_index_duplicate":"تەرتىپ نۇمۇرىنى تەكرارلانسا بولمايدۇ",
		"ranking_order_activity_prize_id_duplicate":"مۇكاپات تەكرارلانسا بولمايدۇ",

		"food_has_multiple_discount": "[%s] كۆپنى ئالسا ئېتىبار پائالىيتى تەڭشىلىپ بولغان ",
		"food_has_seckill":"[%s]تالىشىپ سىتىۋىلىش پائالىيتى تەڭشىلىپ بولغان ",
		"food_has_special":"[%s]ئالاھىدە باھالىق پائالىيەت تەڭشىلىپ بولغان ",
		"food_has_pref":" [%s] ئېتىبار پائالىيتى تەڭشىلىپ بولغان ",
		"foods_multiple_discount_state_names":map[int]string{
			0:"يىڭى قۇرۇلدى",
			1:"داۋاملىشىۋاتىدۇ",
			2:"توختىتىلدى",
			3:"ئاخىرلاشقان",
		},
		"you_are_not_creator_can_not_update":"سىز قۇرغۇچى ئەمەس ، ئۆزگەرتىشكە بولمايدۇ ",
		"you_are_not_creator_can_not_delete":"سىز قۇرغۇچى ئەمەس ، يۇيىۋەتسىڭىز بولمايدۇ ",

		"part_refund_chanel_name":map[int]string{
			0:"نورمال",
			1:"سىستىما",
			2:"ئارقاسۇپا",
			3:"دۇكاندار",
			4:"خېرىدار",
		},
		//1:用户 2:后台 3:商家 4:自动退单
		"part_refund_creator_name":map[int]string{
			1:"خېرىدار",
			2:"ئارقاسۇپا",
			3:"دۇكاندار",
			4:"سىستىما",
		},
		"total_count_over_price_markup_total_count":"سىتىۋالغان تاماقنىڭ جەمئىي سانى ئامبار قالدۇقىدىن ئېشىپ كەتمەسلىكى كېرەك",
		"please_use_new_image":"يېڭى رەسىم ئىشلىتىڭ",
		"未戴头盔":"بىخەتەرلىك قالپىقىنى تاقاڭ",
		"表情未微笑":"كۈلۈمسىرەپ قايتا تارتىڭ",
		"拍摄角度不正，未显示正面":"رەسىمگە توغرا چىرايلىق تارتىڭ،بىخەتەرلىك قالپىقىنى كىيىپ قايتىدىن رەسىمگە تارتىڭ",
		"服装不整齐或不干净":"كىيىمىڭىزنى تۈزلەپ قايتا رەسىمگە تارتىڭ",

		"created_by_store":"دۇكاندار تەرىپىدىن قۇرۇلغان",
		"time_conflict":"ۋاقىت توقۇنۇشۇپ قالدى",
		"marketing_have_order":"پائالىيەتكە زاكاز چۈشۈرۈلگەن ئۆچۈرۈشكە بولمايدۇ",
		"and":"بىلەن",
		"ing":"نىڭ",
		"expired":"ۋاقتى ئۆتۈپ كەتتى",
		"time_conflict_in_multi_discount":"كۆپتىن ئالسا ئېتىبار پائالىيتى توقۇنۇشۇپ قالدى",
		"food_has_marketing":"[%s] باھا كېمەيتىش پائالىيتى تەڭشەلگەن",
		"activity_has_record_can_not_update":"پائالىيەت ئىشلىتىلىپ بولدى،ئۆزگەرتەلمەيسىز",
		"foods_multiple_discount_activity_has_record_can_not_update":"كۆپ سېتىۋالسا ئېتىبار پائالىيىتىدا چۈشكەن زاكاز بار، ئۆزگەرتىشكە بولمايدۇ",
		"foods_multiple_discount_activity_has_record_can_not_delete":"كۆپ سېتىۋالسا ئېتىبار پائالىيىتىدا چۈشكەن زاكاز بار، يۇيىۋېتىشكە بولمايدۇ",

		"token_is_error":"token خاتا",
		"multi_discount_count":"(%s) دە %d تال",
		"spec_food_cannot_create_multiple_discount":"ئۆلچەم تەڭشىگەن تاماقىنى كۆپ سېتىۋالسا ئېتىبار پائالىيىتىگە تەڭشىگىلى بولمايدۇ",
		"spec_options_should_be_provided": "ئۆلچەم تەڭشىگەن تاماقنىڭ ئۆلچەم ئۇچۇرىنى تەمىنلەڭ",

		"adv_link_type": map[int]string{
			0:  "wap تېلېفون بېتى",
			1:  "ئاشخانا تەپسىلات بېتى",
			2:  "تاماق تەپسىلات بېتى",
			3:  "ئۈچىنچى تەرەپ ئەپچاق",
			4:  "مۇلازىم ئەپچاق بېتى",
			5:  "تەرتىپ پائالىيىتى بېتى",
			6:  "ئالاھىدە باھالىق پائالىيەت",
			7:  "تالىشىپ سېتىۋېلىش پائالىيىتى",
			99: "رەسىم(بەتكە سەكرىمەيدۇ)",
		},
		"adv_link_type_dealer": map[int]string{
			0:  "wap تېلېفون بېتى",
			1:  "ئاشخانا تەپسىلات بېتى",
			2:  "تاماق تەپسىلات بېتى",
			// 3:  "ئۈچىنچى تەرەپ ئەپچاق",
			// 4:  "مۇلازىم ئەپچاق بېتى",
			5:  "تەرتىپ پائالىيىتى بېتى",
			6:  "ئالاھىدە باھالىق پائالىيەت",
			7:  "تالىشىپ سېتىۋېلىش پائالىيىتى",
			99: "رەسىم(بەتكە سەكرىمەيدۇ)",
		},

		"Tel":"تېلېفۇن نۇمۇرى",
		"Tel2":"تېلېفۇن نۇمۇرى",
		"Tel3":"تېلېفۇن نۇمۇرى",
		"Tel4":"تېلېفۇن نۇمۇرى",
		"Tel5":"تېلېفۇن نۇمۇرى",
		"excel_process_start":"ھۆججەت تەكشۈرۈلۈۋاتىدۇ",
		"excel_process_running":"ئۇچۇر بىر تەرەپ قىلىنىۋاتىدۇ",
		"excel_process_finished":"تاماملاندى",
		"excel_process_failed":"بىرتەرەپ قىلىش مەغلۇب بولدى",
		"excel_row_empty":	"%d-قۇر قۇرۇقكەن",
		"excel_row_column_empty":"%d-قۇردىكى [%s] قۇرۇقكەن",
		"excel_row_column_illegal":"%d-قۇردىكى [出餐时间] [%s] دائىرىدە ئەمەسكەن",
		
		"excel_row_column_dealer_profit_min_illegal":"%d-قۇردىكى [配送百分比] [%f] دىن كىچىك بولسا بولمايدۇ",
		"excel_row_column_dealer_profit_max_illegal":"%d-قۇردىكى [配送百分比] [%f] دىن چوڭ بولسا بولمايدۇ",

		"excel_row_column_dealer_self_profit_min_illegal":"%d-قۇردىكى [自取百分比] [%f] دىن كىچىك بولسا بولمايدۇ",
		"excel_row_column_dealer_self_profit_max_illegal":"%d-قۇردىكى [自取百分比] [%f] دىن چوڭ بولسا بولمايدۇ",
		"excel_row_column_lunch_box_illegal":"%d-قۇردىكى [饭盒] [%s] مەۋجۇت ئەمەس",
		"excel_row_column_food_quantity_illegal":"%d-قۇردىكى [美食数量类型中文] [%s] مەۋجۇت ئەمەس",
		"excel_row_column_img_illegal":"%d-قۇردىكى [美食图片]چوقۇم http ياكى https نى ئۆز ئىچىگە ئېلىشى كېرەك ",
		"excel_row_column_zip_illegal":"zip ھۆججىتى ئىچىدە xls ھۆججىتى يوقكەن ",
		"lng_or_lat_is_empty":"ئورۇن بەلگىلەش ئۇچۇرى كەم، ئەپنىڭ GPS ئوچۇق ئەمەسلىكىنى، ھەم ئەپكە ئورۇن بەلگىلەش ھوقۇق بىرىلگەنمۇ تەكشۈرەپ قايتا سىناڭ",
		"combo_food_time_illegal":"سىز تاللىغان تاماقلاردا ئورتاق چىقىش ۋاقتى يوقكەن",
		"this_has_spec_foods":"بۇ ئاشخانىدا ئۆلچەم تەڭشىگەن تاماق بار،بارلىق تاماققا ئېتىبار تەڭشىيەلمەيسىز",
		"price_type_group_price_error":                      "مىقدارنىڭ ئۆلىچەم باھاسى 0 دىن يوقىرى بۇلىشى كىرەك",
		"spec_option_must_selected":                         "چۇقۇم بىر ئۆلچەم تاللىنىش كىرەك",
		"spec_food_setting_seckill":                         "بۇ تاماققا تەڭشەلگەن ئاخىرلاشمىغان تالىشىپ سېتىۋېلىش پائالىيىتى بار ئىكەن،پائالىيەتنى توختۇتۇپ ئاندىن تەھرىرلەڭ",
		"spec_food_setting_special_price":                   "بۇ تاماققا تەڭشەلگەن ئالاھىدە باھالىق پائالىيەت بار ئىكەن،پائالىيەتنى توختۇتۇپ ئاندىن تەھرىرلەڭ",
		"spec_food_setting_preferential":                   "بۇ تاماققا تەڭشەلگەن ئېتىبار پائالىيەت بار ئىكەن،پائالىيەتنى توختۇتۇپ ئاندىن تەھرىرلەڭ",
		"spec_food_setting_price_markup":                   "بۇ تاماققا تەڭشەلگەن سېتىۋالغان تامام بار ئىكەن،پائالىيەتنى توختۇتۇپ ئاندىن تەھرىرلەڭ",
		"spec_food_setting_marketing_reduce":               "بۇ تاماققا تەڭشەلگەن باھا كېمەيتىش پائالىيەت بار ئىكەن،پائالىيەتنى توختۇتۇپ ئاندىن تەھرىرلەڭ",
		"update_not_allowed":"تەستىقلىنىۋاتىدۇ،ھالىتىنى ئۆزگەرتىشكە بولمايدۇ",
		"advert_state_error":"ئېلان ھالىتى خاتا",
		"combo_not_able_create_spec":"يۈرۈشلۈك تاماققا ئۆلچەم قۇرغىلى بولمايدۇ",
		"spec_info_has_changed":"تەڭشىگەن ئۆلچەم ئۇچۇرى ئ‍ۆزگەرتىۋېتىلگەن،بۇ پائالىيەتنى ئاچالمايسىز،قايتىدىن قۇرۇپ ئىشلىتىڭ",
		"foods_combo_items_empty":"يۈرۈشلۈك تاماقنىڭ تاللانغان تاماقلىرى يوقكەن",
		"foods_combo_items_can_not_be_combo":"يۈرۈشلۈك تاماققا يۈرۈشلۈك قوشقىلى بولمايدۇ",
		"combo_price_need_le_items_price":"يۈرۈشلۈكنىڭ باھاسى تاللانغان تاماقلارنىڭ باھاسىدىن تۈۋەن بۇلىشى كېرەك",
		"spec_option_cannot_be_empty":"ئۆلچەم گۇرپىسىغا ئۆلچەم تەڭشەلمىگەن",
		"food_id_cannot_be_empty":"تاماق تاللانمىغان،قايتا تاللاڭ",
		"food_not_found":"تاماق ئۇچۇرى تېپىلمىدى",
		"multi_discount_foods_enable_create_spec":"كۆپ سېتىۋالسا ئېتىبار تەڭشەگەن تاماققا ئۆلچەم تەڭشىيەلمەيسىز",
		"foods_time_not_in_restaurant_time_range":"تاماقنىڭ چىقىش ۋاقتى ئاشخانا تېجارەت ۋاقىت دائىرىسىدە ئەمەس",
		"foods_percent_range":"تاماق پىرسەنتى",
		"self_take_percent_range":"ئۈزى ئېلىۋېلىش پىرسەنتى",
		"lunch_box_not_found":"تاماق قاچىسى تېپىلمىدى",
		"update_foods_info_fail":"تاماق ئۇچۇرىنى يېڭىلاش مەغلۇب بولدى",
		"update_foods_category_fail":"تاماق تۈرى ئۇچۇرىنى يېڭىلاش مەغلۇب بولدى",
		"update_combo_fail":"يۈرۈشلۈك ئۇچۇرىنى يېڭىلاش مەغلۇب بولدى",
		"spec_type_count_max_6":"ئۆلچەم  گۇرپىسى 6 دىن  ئېشىپ كەتسە بولمايدۇ",
		"spec_food_no_options":"ئۆلچەم تەشڭىگەن تاماقنىڭ ئۆلچەم ئۇچۇرى يوقكەن",
		"food_spec_type_error":"تاماقنىڭ تىپى توغرا  ئەمەس",
		"combo_item_deleted":"يۈرۈشلۈكنىڭ ئىچىدىكى [%s] تاماق ئۈچۈرۋېتىلگەن،بۇ تاماقنى تاللىيالمايسىز",
		"states": map[int]string{
			0: "ئېتىك",
			1: "ئوچۇق",
		},
		"order_rank_list":[]map[string]interface{}{
			{"key1":"","key2":"base_score","name":"ئاساسىي نومۇرى","multiply":"1"},
			
			{"key1":"delivered_on_time_order_count","key2":"delivered_on_time","name":"ۋاقتىدا يەتكۈزۈش","multiply":"1"},
	
			{"key1":"positive_reviews_count","key2":"positive_reviews_score","name":"ياخشى ئىنكاس","multiply":"1"},
			
			{"key1":"mild_lateness_count","key2":"mild_lateness_deduct","name":"كېچىكىش(1-5مىنۇت)","multiply":"-1"},
			
			{"key1":"moderate_lateness_count","key2":"moderate_lateness_deduct","name":"كېچىكىش (6-10مىنۇت)","multiply":"-1"},
			
			{"key1":"severe_lateness_count","key2":"severe_lateness_deduct","name":"ئېغىر كېچىكىش","multiply":"-1"},
			
			{"key1":"negative_reviews_count","key2":"negative_reviews_deduct","name":"ناچار ئىنكاس","multiply":"-1"},
			
			{"key1":"complaints_count","key2":"complaints_deduct","name":"ئەرز","multiply":"-1"},
			
			{"key1":"early_delivery_count","key2":"early_delivery_deduct","name":"بالدۇر يەتكۈزۈش","multiply":"1"},
	
		},
		"week":"ھەپتە",
		"auto_dispatch_order_deliver_state": map[int]string{
			0:  "ۋاقتىدا يەتكۈزۈلگەن",
			1:  "5 مىنۇت كىچىككەن",
			2:  "10 مىنۇت كىچىككەن",
			3:  "ئېغىر كىچىككەن",
			4:  "بالدۇر يەتكۈزگەن",
		},
		"auto_dispatch_order_comment_state_has":"بار",
		"auto_dispatch_order_comment_state_has_not":"يوق",
		"auto_dispatch_order_complain_state_has":"بار",
		"auto_dispatch_order_complain_state_has_not":"يوق",
		"date_range_can_not_over_x_days":                                                    "باشلىنىش ۋاقتى ۋە ئاخىرلىشىش ۋاقتى ئارىلىقى %d كۈندىن ئېشىپ كەتمەسلىكى كېرەك",
		"late":"كېچىكىش",
		"shipper_rank_detail_types": map[int]string{
			1:  "ئاساسىي نومۇر",
			2:  "ياخشى باھا",
			3:  "ۋاقتىدا يەتكۈزۈلگەن زاكاز",
			4:  "ئومۇمىي زاكاز",
			5:  "كىچىكىش ۋاقتى 0-5 مىنۇت",
			6:  "كىچىكىش ۋاقتى 5-10 مىنۇت",
			7:  "كىچىكىش ۋاقتى 10 مىنۇتتىن يوقىرى",
			8:  "ناچار باھا",
			9:  "ئەرز - شىكايەت",
			10:  "ئالدىن زاكازنى 20 مىنۇت بالدۇر يەتكۈزۈش",
		},
		"not_area_config":"رايۇن تەڭشىكى يوقكەن،ئاۋال رايۇن تەڭشىكىنى تەڭشەڭ",
		"not_area_server_config":"رايۇن مۇلازىمەت دائىرىسى تەڭشەلمەپتۇ، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ",

	}
}
