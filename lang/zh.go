package lang

var Zh map[string]interface{}

func init() {
	Zh = map[string]interface{}{
		"msg":                  "操作成功",
		"not_authorized":       "你还未登录",
		"forbidden":            "你没有权限",
		"rebind_error":         "你可以通过这个手机号码在美滋来小程序上下单后再进行绑定",
		"password_error":       "密码错误",
		"order_is_null":        "订单为空",
		"retry_after_3_second": "请在3秒后重试",
		"order_state": map[int]string{
			1:  "新订单",
			2:  "确认订单",
			3:  "等待接收订单",
			4:  "订单已接受",
			6:  "配送中...",
			7:  "已完成",
			8:  "订单取消",
			9:  "餐厅拒绝接单",
			10: "订单配送失败",
			5:  "已备餐",
		},
		"order_refund_channel": map[int]string{
			1: "系统自动取消订单",
			2: "后台管理员取消订单",
			3: "商家取消订单",
			4: "客户取消订单",
		},
		"Username":      "用户名",
		"Limit":         "页大小",
		"Page":          "当前页数",
		"Day":           "天数",
		"error_happend": "出错了，请重试",
		//"shipper": map[string]string{
		//	"already_taked": "此订单已被抢单！",
		//},
		"already_taked":                     "此订单已被抢单！",
		"self_taken_order":                  "此订单客户自取，不能抢单！",
		"not_found":                         "找不到相关内容",
		"failed":                            "操作失败！",
		"order_not_found":                   "找不到相关订单信息",
		"did_not_arrive_destination":        "您跟客户下单地址还有%0.1f米距离，不能完成订单！",
		"change_state_fail":                 "订单状态错误，餐厅没有接单!",
		"overstep_time":                     "此订单已超出退订时间范围。",
		"already_backed":                    "此订单已退订！",
		"back_order_failed":                 "退单操作失败！",
		"admin_is_not_active":               "该用户未激活.",
		"admin_is_not_shipper":              "该用户不是配送员",
		"admin_password_error":              "用户名或密码错误！",
		"order_state_error":                 "订单状态有误！",
		"order_extend_info_not_found":       "订单附加信息未找到",
		"order_already_update_arrived_shop": "您已到店签到、无需重复签到",
		"order_cannot_repaid":               "订单不能重复付款！",
		"client_param_error":                "客户端传来的参数错误！",
		"order_sending_finish":              "此订单以配送完成!",
		"order_cancel":                      "此订单以取消!",
		"order_seized":                      "配送员已抢过的订单不能取消！",
		"order_receive_refuse":              "此订单以拒绝配送!",
		"order_sending_fail":                "此订单以配送失败!",
		"merchant_role_id_error":            "role id 一定要 1, 2, 3 。",
		"merchant_password_error":           "商家登录密码错误！",
		"merchant_id_not_found":             "商家编号不存在！",
		"merchant_not_found":                "未找到餐厅信息。",
		"generate_merchant_qrCode_failed":   "二维码生成失败！",
		"cashout_amount_error":              "提现金额有误！",
		"cashout_bill_id_not_found":         "未找到要提现的账单信息！",
		"shipper_failed_to_locate_location": "定位失败，请先开启应用定位权限，再试！",
		"shopper_working_off":               "您正在处于休息状态，不能抢单，请先联系管理员。",
		"pay_type_app_pay_desctription":     "配送员APP支付",
		"pay_type_qr_code_desctription":     "客户二维码支付",
		"pay_type_sms_desctription":         "客户短信支付",
		"fail":                              "失败！",
		"success":                           "成功！",
		"attendance-limit":                  "1分钟内只能打卡一次",
		"personally-attendance":             "本人",

		"sms_in_two_minutes": "两分钟后重试",
		"mobile_incorrect":   "手机号错误",
		"captcha_incorrect":  "验证码错误",
		"captcha_time_out":   "验证码过期",
		// 信息收集
		"RegMerType":                   "注册类型",
		"RegCapital":                   "注册资本",
		"ShopLicenseNum":               "营业执照号",
		"RegAddress":                   "注册地址",
		"BusinessScope":                "经营范围",
		"ShopName":                     "店铺名称",
		"LegalName":                    "法人姓名",
		"ShopLicenseLimitedType":       "营业执照有效期类型",
		"ShopLicenseStart":             "营业执照有效期开始时间",
		"ShopLicenseEnd":               "营业执照有效期结束时间",
		"ShareholderName":              "股东姓名",
		"ShareholderIdcard":            "股东身份证号",
		"ShareholderAddress":           "股东地址",
		"ShareholderIdcardLimitedType": "股东身份证有效期类型",
		"ShareholderIdcardStart":       "股东身份证有效期开始时间",
		"ShareholderIdcardEnd":         "股东身份证有效期结束时间",
		"Captcha":                      "验证码",
		"MerIdcardName":                "身份证姓名",
		"MerIdcardNum":                 "身份证号",
		"MerIdcardStart":               "身份证有效期开始时间",
		"MerIdcardEnd":                 "身份证有效期结束时间",
		"MerIdcardTimeType":            "身份证有效期类型",
		"MerMobile":                    "手机号",
		"MerIsBnf":                     "是否是受益人",
		"MerSex":                       "性别",
		"BankAcctNum":                  "银行卡号",
		"BankName":                     "银行名称",
		"BankProvinceId":               "银行所在省份ID",
		"BankCityId":                   "银行所在城市ID",
		"BankAreaId":                   "银行所在区域ID",
		"BankBranchName":               "银行支行名称",
		"BankBranchCode":               "银行支行联行号",
		"BankBindMobile":               "银行预留手机号",
		"ShopBusinessName":             "商户经营名称",
		"ShopBusinessCity":             "商户经营城市",
		"ShopBusinessArea":             "商户经营区域",
		"ShopBusinessAddress":          "商户经营地址",
		"ShopCategoryCode":             "商户经营类目",
		"ShopManagerType":              "商户经营者类型",
		"ShopManagerName":              "商户经营者姓名",
		"ShopManagerIdcard":            "商户经营者身份证号",
		"ShopManagerMobile":            "商户经营者手机号",
		"ShopManagerEmail":             "商户经营者邮箱",
		"not_idcard_front":             "请上传身份证 前面",
		"not_idcard_back":              "请上传身份证 背面",
		"del_mer_stuf_fail":            "删除员工健康证信息失败",
		"data_not_found":               "数据不存在",
		"File":                         "文件",
		"Id":                           "ID",
		"StaffName":                    "员工名称",
		"StaffIdcard":                  "员工身份证号",
		"HealthCertificateImage":       "员工健康证图片",
		"HealthCertificateStart":       "员工健康证开始时间",
		"HealthCertificateEnd":         "员工健康证结束时间",
		"INVALID_BUSINESS_LICENSE":     "不是有效的营业执照",
		"captcha_requiered":            "请输入验证码",

		"save_img_fail": "图片保存失败",
		"file_too_big":  "图片过大",

		"PermitStartDate":         "许可证起效期日期",
		"PermitEndDate":           "许可证失效期日期",
		"PermitShopName":          " 许可证店铺名称",
		"PermitLicNum":            "许可证编号",
		"PermitCreditCode":        "许可证统一社会信用代码",
		"PermitLegalName":         "许可证法人姓名",
		"PermitShopAddr":          "许可证店铺地址",
		"PermitBusinessPremises":  "许可证经营场所",
		"PermitState":             "许可证状态",
		"PermitBusinessType":      "许可证经营类目",
		"PermitSuperviseOrg":      "许可证监管机构",
		"PermitSuperviseManagers": "许可证监管人员",
		"PermitIssuingAuthority":  "许可证发证机关",
		"ImgSrc":                  "图片",

		"marketing_name":                              "活动名称",
		"marketing_type":                              "活动类型",
		"marketing_begin_date":                        "开始时间",
		"marketing_end_date":                          "结束时间",
		"marketing_full_week_state":                   "是否全星期",
		"marketing_full_time_state":                   "是否全天",
		"marketing_auto_continue":                     "是否自动延续",
		"marketing_date_format_error":                 "日期格式不对",
		"exists_same_timeline_event":                  "该时间段内存在另一个活动，检查活动时间段",
		"full_week_empty_days":                        "不是全星期进行的话，需要选择至少一天",
		"full_time_empty_time":                        "不是全天的话，需要选择至少一个时间段",
		"second_time_must_be_after_first":             "结束时间必须在开始时间后",
		"marketing_foods_exists":                      "同一个时间段中的活动中存在同样的商品",
		"price_must_exist":                            "价格必填",
		"price_must_not_equal_to_reduce":              "减免价格必须小于开始价格",
		"reduce_price_must_not_empty":                 "减免价格不能为空",
		"price_must_not_empty":                        "价格不能为空",
		"price_reduce_must_not_empty":                 "减免金额不能为空",
		"marketing_foods_not_empty":                   "商品不能为空",
		"price_must_be_plus":                          "价格必须大于0",
		"next_price_must_bigger_than_previous":        "下一个阶梯的价格必须大于上一个阶梯的价格",
		"next_price_reduce_must_bigger_than_previous": "下一个阶梯的减免金额必须大于上一个阶梯的减免金额",
		"marketing_not_found":                         "活动不存在",
		"marketing_not_editable":                      "不能编辑活动",
		"marketing_create_by_merchant_not_editable":   "ID:%d，该活动由商家创建无法编辑",
		"marketing_wrong_state_can_not_updated":       "ID:%d状态为[%s]，不可更新状态",
		"merketing_conflict_not_editable":             "该活动不能启动,先停止<<%s>>活动",
		"marketing_enable_change":                     "状态更新失败",
		"marketing_not_deletable":                     "已下单的活动,无法删除",
		"marketing_delet_failed":                      "删除活动失败",
		"marketing_store":                             "全店活动",
		"marketing_product":                           "商品活动",
		"marketing_state_0":                           "新建",
		"marketing_state_1":                           "进行中",
		"marketing_state_2":                           "暂停",
		"marketing_state_3":                           "失效",
		"marketing_state_4":                           "删除",
		"marketing_expired":                           "天后失效",
		"marketing_customer_type":                     "目标客户类型不能为空",
		"marketing_can_not_update_state_to_new":       "活动状态不能更新为新建",
		"marketing_create_fail":                       "活动创建失败",
		"conflict_marketing_not_found":                "没有冲突的活动",
		"conflict_marketing_found":                    "该活动跟其他活动有冲突",
		"marketing_conflict_with_other_marketing":     "该活动跟ID为：%d开始日期%s结束日期%s的活动冲突",
		"marketing_only_edit_merchant_activity":       "只能编辑普通减配送费活动",
		"marketing_state_is_can_not_update":           "该活动状态错误无法更新",
		"price_type_must_exist":                       "减免配送费类型不能为空",
		"marketing_add_by_admin_not_editable":         "该活动由代理创建无法编辑",
		"price_type_must_in_area":                     "减免配送费类型范围在(1,2)内",
		"price_type_before":                           "上一个阶梯的减免配送费类型已经设置过全部免配送费",
		"created_by_admin":                            "由代理创建",
		"marketing_reduce_fee":                        "满减活动减免金额",
		"marketing_shipment_reduce_fee":               "减免配送活动减免金额",
		"all_customer":                                "全部客户",
		"price_type1":                                 "配送费减半",
		"price_type2":                                 "免配送费",
		"created_by_owner":                            "由商家创建",

		"creator_type1":                       "代理",
		"creator_type2":                       "商家",
		"restaurant_not_found":                "店铺信息不存在",
		"marketing_group_template_not_found":  "代理组织的活动不存在",
		"marketing_group_added":               "已经参加过该活动",
		"marketing_add_group_template_failed": "创建活动失败",
		"marketing_group_template_has_joined_marchant_can_not_be_updated": "该团体活动有商家参加，不能修改",
		"marketing_group_template_update_failed":                          "修改团体活动失败",
		"marketing_group_template_statistics_failed":                      "统计团体活动失败",
		"marketing_merchant_shipment_reduce_statistics_failed":            "统计商家减配送费活动失败",
		"marketing_group_template_change_state_failed":                    "修改团体活动状态失败",
		"marketing_group_template_change_state_too_many_ids_received":     "修改团体活动状态失败，接收到的团体活动数量过多",
		"marketing_group_template_too_many_ids_received":                  "接收到的团体活动数量过多",
		"marketing_group_template_send_join_activity_message_failed":      "发送参加活动推送失败",
		"NameUg":                             "活动维吾尔语名称",
		"NameZh":                             "活动中文名称",
		"ResId":                              "店铺",
		"MarketingType":                      "活动类型",
		"Type":                               "活动商品类型",
		"BeginDate":                          "开始日期",
		"EndDate":                            "结束日期",
		"at_least_one_food_required":         "至少选择一种美食",
		"2second_time_must_be_after_first":   "第二个时间段的时间必须大于第一个时间段的时间",
		"3second_time_must_be_after_first":   "第三个时间段的时间必须大于第二个时间段的时间",
		"target":                             "类型",
		"StartDate":                          "开始时间",
		"EndtDate":                           "结束时间",
		"marketing_home_page_notify_content": "新增满减活动功能",
		"marketing_type1_description":        "获取更多的客户流量",
		"marketing_type1_rule":               "",
		"marketing_type2_description":        "获取更多的客户流量",
		"marketing_type2_rule":               "",
		"min_delivery_price_can_not_be_less_than_system_config":                              "最低订单价格不能小于%0.2f元",
		"area_id_can_not_be_empty":                                                           "请选择区域",
		"area_not_found":                                                                     "区域不存在",
		"area_business_start_time_must_be_earlier_than_end_time":                             "营业开始时间要早于结束时间",
		"area_business_time_must_be_longger_than_eight_hours":                                "营业时间要大于8小时",
		"active_restaurant_not_found":                                                        "一个开启的餐厅都没有找到",
		"restaurant_ids_can_not_be_empty":                                                    "您没有选择餐厅",
		"timeline_must_be_at_least_one":                                                      "时间区间必须至少有一个",
		"timeline_start_can_not_be_empty":                                                    "时间区间开始时间不能为空",
		"timeline_end_can_not_be_empty":                                                      "时间区间结束时间不能为空",
		"timeline_start_format_error":                                                        "时间区间开始时间格式错误",
		"timeline_end_format_error":                                                          "时间区间结束时间格式错误",
		"timeline_start_must_be_before_end":                                                  "时间区间开始时间必须在结束时间之前",
		"timeline_start_and_end_can_not_be_empty":                                            "时间区间开始时间和结束时间不能同时为空",
		"timeline_can_not_overlap":                                                           "时间区间不能重叠",
		"timeline_can_not_be_more_than_three_item":                                           "时间区间不能超过三项",
		"shipment_reduce_step_must_be_at_least_one":                                          "减配送费活动阶梯必须至少有一个",
		"shipment_reduce_step_start_must_be_positive":                                        "减配送费活动阶梯开始距离必须大于0",
		"shipment_reduce_step_end_must_be_bigger_than_start":                                 "减配送费活动阶梯结束距离必须大于开始距离",
		"shipment_reduce_step_end_must_be_smaller_than_next_start":                           "减配送费活动阶梯结束距离必须小于下一个阶梯的开始距离",
		"shipment_reduce_step_reduce_must_be_positive":                                       "减配送费活动阶梯减免金额必须大于0",
		"shipment_reduce_store_price_must_be_positive":                                       "减配送费活动阶梯商家承担金额必须大于0",
		"shipment_reduce_dealer_price_must_be_positive":                                      "减配送费活动阶梯代理承担金额必须大于0",
		"marketing_group_template_only_active_new_template":                                  "只能开启新建的团体活动",
		"marketing_group_template_already_active":                                            "团体活动已开启",
		"marketing_group_template_only_pause_new_or_active_state":                            "只能暂停新建或者开启的团体活动",
		"marketing_group_template_already_pause":                                             "团体活动已暂停",
		"marketing_group_template_pause_failed":                                              "暂停团体活动失败",
		"shipment_reduce_step_reduce_price_not_equal_to_sum_of_store_price_and_dealer_price": "减配送费活动阶梯减免金额必须等于商家承担金额和代理承担金额",
		"begin_time_must_be_after_today":                                                     "开始日期必须大于或等于今天",
		"begin_time_must_be_after_time":                                                      "結束日期必须大于或等于开始日期",
		"begin_date_can_not_feature":                                                         "开始日期不能大于当天",
		"begin_date_format_error":                                                            "开始日期格式错误",
		"end_date_format_error":                                                              "结束日期格式错误",
		"end_date_can_not_feature":                                                           "结束日期不能大于当天",
		"date_range_can_not_over_30_days":                                                    "开始日期到结束日期距离不能大于30天",
		"date_range_can_not_over_31_days":                                                    "开始日期到结束日期距离不能大于31天",
		"end_date_can_not_before_begin_date":                                                 "结束日期不能开始日期之前",
		"wrong_weekday_selected":                                                             "您选择的工作日有误",
		"day_1_not_in_range":                                                                 "该时间段内不存在星期一",
		"day_2_not_in_range":                                                                 "该时间段内不存在星期二",
		"day_3_not_in_range":                                                                 "该时间段内不存在星期三",
		"day_4_not_in_range":                                                                 "该时间段内不存在星期四",
		"day_5_not_in_range":                                                                 "该时间段内不存在星期五",
		"day_6_not_in_range":                                                                 "该时间段内不存在星期六",
		"day_7_not_in_range":                                                                 "该时间段内不存在星期天",
		"staff_prame_error":                                                                  "员工信息填写不全",
		"mini_shop_must_fill_gender":                                                         "小微商户性别必填",
		"merInfoaudit1":                                                                      "资料已提交,等待管理员审核",
		"merInfoaudit3":                                                                      "后台审核通过,不能修改",
		"merInfoaudit4":                                                                      "管理员审核已通过,等待提交资料",
		"merInfoaudit5":                                                                      "已提交资料,不能修改",
		"merInfoaudit7":                                                                      "对公账户待确认",
		"merInfoaudit8":                                                                      "待在线签约",
		"merInfoaudit9":                                                                      "待银商入网审核",
		"merInfoaudit10":                                                                     "银商入网成功",
		"merInfoaudit11":                                                                     "银商入网成功",
		"update_info_state":                                                                  "操作失败",
		"state_0":                                                                            "未提交",
		"state_1":                                                                            "待审核",
		"state_2":                                                                            "审核未通过",
		"state_3":                                                                            "后台审核通过",
		"state_4":                                                                            "待提交资料",
		"state_5":                                                                            "已提交资料",
		"state_6":                                                                            "资料未通过",
		"state_7":                                                                            "对公账户待确认",
		"state_8":                                                                            "待在线签约",
		"state_9":                                                                            "待银商入网审核",
		"state_10":                                                                           "入网成功",
		"state_11":                                                                           "入网失败",
		"check_state_failed":                                                                 "获取状态失败",
		"对公账户验证失败,已错误1次,最大允许错误次数5次":                  "对公账户验证失败,已错误1次,最大允许错误次数5次",
		"对公账户验证失败,已错误2次,最大允许错误次数5次":                  "对公账户验证失败,已错误2次,最大允许错误次数5次",
		"对公账户验证失败,已错误3次,最大允许错误次数5次":                  "对公账户验证失败,已错误3次,最大允许错误次数5次",
		"对公账户验证失败,已错误4次,最大允许错误次数5次":                  "对公账户验证失败,已错误4次,最大允许错误次数5次",
		"对公账户验证失败,已错误5次,最大允许错误次数5次":                  "对公账户验证失败,已错误5次,最大允许错误次数5次",
		"you_have_some_meter_to_attendence_position": "您与打卡点还有%0.1fm",
		"you_must_open_gps":         "定位信息获取失败，请先开启定位权限",
		"grab_order_count_exceeded": "已超过能同时配送的订单数量%d",
		"shop_licence_incomplete":   "营业只汇总啊信息不全",
		"idcard_incomplete":         "身份证信息不全",
		"bank_incomplete":           "银行卡信息不全",
		"shop_incomplete":           "店铺信息不全",
		"info_incomplete":           "填写信息后再提交",
		"submitted_wait_for_check":  "已经提交过了，耐心等待审核",
		"bank_card_error":           "银行卡号码有误",
		"api_error":                 "银行卡验证接口出现错误",
		"idcard_recommit":           "重新提交账户信息",
		"bank_verify_result": map[int]string{
			0:   "认证通过",
			-1:  "认证未通过,请检查 身份证号,姓名,银行卡和银行卡预留的手机号是否一致",
			-6:  "持卡人信息有误",
			-7:  "未开通无卡支付",
			-8:  "此卡被没收",
			-9:  "无效卡号",
			-10: "此卡无对应发卡行",
			-11: "该卡未初始化或睡眠卡",
			-12: "作弊卡、吞卡",
			-13: "此卡已挂失",
			-14: "该卡已过期",
			-15: "受限制的卡",
			-16: "密码错误次数超限",
			-17: "发卡行不支持此交易",
			-2:  "姓名校验不通过,请检查银行卡上的姓名",
			-3:  "身份证号码有误,请检查办理银行卡的身份证信息",
			-4:  "银行卡号码有误",
			-5:  "手机号码不合法",
			-18: "验证中心服务繁忙",
		},
		"order_not_exits":                     "订单不存在",
		"server_error":                        "服务器出现错误",
		"legal_name_and_idcard_name_not_same": "法人姓名和身份证上的姓名不一致",
		"Kw":                                  "关键字",
		"this_admin_has_more_than_one_store":  "该登录账户下存在",
		"multi_admin_error":                   "店铺，请联系管理员",
		"RestaurantId":                        "店铺id",
		"RestaurantID": "店铺id",
		"OptionIds":    "规格id",
		"FoodType":     "美食类型",
		"SelfTakeNumber":                      "自取编号",
		"order_been_taken":                    "饭已经被被带走",
		"errors": map[string]string{
			"database": "数据库出现错误",
		},
		"open_time_or_close_time_not_in_food_serving_time": "美食出菜时间端内的时间与开启店铺或关闭店铺时间不符",
		"send_before":      "已经发送过",
		"id_card_no_empty": "身份证号不能为空",
		"lakala_copy_content": map[string]string{
			"shop_license_num":       "营业执照号码",
			"shop_business_name":     "店铺名称",
			"legal_name":             "法人名称",
			"bank_acct_num":          "银行账户",
			"bank_acct_name":         "银行账户名称",
			"bank_name":              "银行名称",
			"bank_acct_type_name":    "账户类型",
			"bank_branch_name":       "银行支行名称",
			"bank_branch_code":       "银行支行行号",
			"shop_business_province": "所属省份",
			"shop_business_city":     "所属城市",
			"shop_business_country":  "所属区域",
			"shop_business_address":  "经营地址",
		},
		"not_fund_merchant":              "找不到商户",
		"not_found_customer":             "找不到客户",
		"cashout_in_progress":            "提现中，请等待",
		"per_cashe_out_limit":            "一次提现额度限制为500元",
		"not_in_working_time":            "提现时间过了，提现时间明天早上5点开始。",
		"illiegal_bill":                  "提现账单日期错误！",
		"cashout_amount_incorrect":       "提现金额不正确",
		"retry_after_10_second":          "10秒后重试",
		"system_error":                   "服务器内部错误",
		"cash_out_config_not_ok":         "提现配置出现错误",
		"cashout_record_not_found":       "提现记录有错误",
		"system_maintaining_in_progress": "系统维护中",
		"lakala_withdraw_states": map[int]string{
			1: "新提交",
			2: "待分账",
			3: "分账成功",
			4: "分账失败",
			5: "提现成功",
			6: "提现失败",
			7: "分账撤回成功",
			8: "分账撤回失败",
		},
		"lakala_withdraw_state_processing":  "提现中",
		"lakala_withdraw_state_success":     "提现成功",
		"lakala_withdraw_state_fail":        "提现失败",
		"withdraw_request_sent":             "提现请求已发送，请耐心等待结果",
		"cardid_is_not_null":                "选择要提现的银行卡",
		"shipper_app_is_old":                "配送端APP版本过期，请更新新的配送端APP",
		"res_manager_app_is_old":            "商家端APP版本过期，请更新新的商家端APP",
		"cash_per_day":                      "每日可提现总金额 %s 元,今日已提现金额 %s 元,此次提现金额 %s 元，金额超过 %s元",
		"lakala_archive_not_found":          "该店铺尚未通过商家认证",
		"wechat_withdraw_stop_msg":          "请尽快完成商家认证，然后重试",
		"Bnf.BnfIdcardStart":                "股东身份证有效期开始时间",
		"Bnf.BnfName":                       "股东姓名",
		"Bnf.BnfIdcardNum":                  "股东身份证号",
		"Bnf.BnfAddress":                    "股东地址",
		"Bnf.BnfIdcardEnd":                  "股东身份证有效期结束时间",
		"BnfIdcardStart":                    "股东身份证有效期开始时间",
		"BnfName":                           "股东姓名",
		"BnfIdcardNum":                      "股东身份证号",
		"BnfAddress":                        "股东地址",
		"BnfIdcardEnd":                      "股东身份证有效期结束时间",
		"BankId":                            "银行",
		"Bnf":                               "股东信息",
		"not_empty":                         "不能为空",
		"retry_after_second":                "%d秒后重试",
		"right_now":                         "刚刚",
		"before_minute":                     "%d 分钟前",
		"before_hour":                       "%d 小时前",
		"before_day":                        "%d 天前",
		"coupon_refunded":                   "已经退款",
		"coupon_not_found":                  "优惠券不存在",
		"coupon_refund_no_amount":           "没有可退款的金额",
		"coupon":                            "优惠券",
		"ShopLicenseStart_error":            "营业执照有效期开始时间错误，请重新上传",
		"ShopLicenseEnd_error":              "营业执照有效期结束时间错误，请重新上传",
		"coupon_refund_request_in_progress": "优惠券退款请求处理中,请耐心等待",
		"retry_after_minute":                "%d分钟后重试",

		// 配送员管理
		"not_found_shipper":      "找不到配送员",
		"no_permission":          "权限不够",
		"shipper_has_restaurant": "当前配送员已有分配的餐厅，先接触分配的餐厅再重实",
		"shipper_has_take_order": "当前配送员已有配送订单，不能删除",
		"failed_delete_shipper":  "删除配送员失败",
		// 配送费模板
		"DeductionTypeNames":           map[int]string{1: "固定扣款", 2: "按订单数量扣款"},
		"RuleTypeNames":                map[int]string{1: "固定配送费", 2: "按距离计算", 3: "按出租车计算", 4: "按订单数量"}, // 规则类型:1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"ShipperIncomeType":            map[int]string{1: "订单收入", 2: "奖励收入", 3: "打赏收入", 4: "特殊时间", 5: "特殊天气", 6: "好评", 7: "差评", 8: "投诉", 9: "迟到"},
		"ShipperIncomeComplainType":    map[int]string{1: "客户投诉", 2: "餐厅投诉"},
		"ShipperNotAroundShop":         "还没有到达餐厅",
		"ShipperStatisticsIncomeTypes": map[int]string{1: "配送费", 2: "奖励", 3: "惩罚", 4: "其他", 5: "客户拓展"},
		"ShipperStatisticsOrderTypes":  map[int]string{1: "完成订单", 2: "退单订单", 3: "失败订单", 4: "迟到订单"},
		"shipper_order_state": map[int]string{
			0:   "订单未抢单",
			100: "已抢单",
			200: "到餐厅",
			300: "已取餐",
			400: "完成订单",
			500: "餐厅未准备好美食",
		},
		// 1:事假 2:病假 3:婚嫁 4:产假/陪产假
		"leave_types": map[int]string{
			1: "事假",
			2: "病假",
			3: "婚假",
			4: "产假/陪产假",
		},
		// 1:待审核,2:审核通过,3:审核不通过,4:撤销
		"review_state": map[int]string{
			1: "待审核",
			2: "审核通过",
			3: "审核拒绝",
			4: "撤销",
		},
		// 1 上班 2 下班 3 休息
		"attendance_states": map[int]string{
			1: "上班",
			2: "下班",
			3: "休息",
		},
		// 类型 1：订单收入，2:奖励收入，3:打赏收入，4.特殊时间，5.特殊天气，6.好评，7.差评，8.投诉，9:迟到
		"income_types": map[int]string{
			1: "订单收入",
			2: "奖励收入",
			3: "打赏收入",
			4: "特殊配送时间",
			5: "特殊天气",
			6: "好评",
			7: "差评",
			8: "投诉",
			9: "迟到",

			// 14:"客户介绍提成",
			// 15:"客户下单提成",
			16: "保险扣费",
		},
		"ShipperNotTakeFood":                                       "还没有取餐",
		"FileNotFound":                                             "无法获取上传文件",
		"FileImageFormatError":                                     "只允许jpg|jpeg|png类型图片",
		"only_support_xlsx":                                        "只支持 xlsx 类型文件",
		"name_already_exist":                                       "该名称已经存在",
		"already_used_unable_delete":                               "该数据已使用，不能删除",
		"area_id_is_required":                                      "请选择区域",
		"mobile_already_exist":                                     "该手机号已被使用",
		"special_rule_content_enable_decode":                       "特殊配送员时间规则内容无效",
		"base_rule_content_enable_decode":                          "配送员费规则内容无效",
		"late_rule_content_enable_decode":                          "扣款规则内容无效",
		"invalid_deduction_fee":                                    "扣款金额无效",
		"invalid_price":                                            "金额错误",
		"time_must_asc_and_not_conflict":                           "时间段必须按顺序设置，并不能交叉",
		"min_time_not_greater_max_time":                            "开始时间不能大于结束时间",
		"min_time_must_greater_zero":                               "开始时间必须大于0",
		"max_time_must_greater_zero":                               "结束时间必须大于0",
		"template_unable_edit":                                     "配送费模板已绑定过配送员，无法修改",
		"password_length_error":                                    "密码必须大于等于6位",
		"this_time_opening_other_special_weather":                  "同一个时间端只能开启一个特殊天气，请先关闭《%s》后，再开启",
		"this_time_opening_other_notify":                           "同时发送一个消息，请先关闭开启的消息",
		"marketing_reduce":                                         "满减活动",
		"marketing_coupon":                                         "优惠券活动",
		"not_id":                                                   "ID必填",
		"unable_review":                                            "已审核的内容无法审核",
		"this_area_already_exists_reward_setting":                  "该代理区已存在奖励设置，不能重复创建",
		"distance_must_greater_zero":                               "距离必须大于0",
		"shipment_fee_must_greater_zero":                           "配送费必须大于0",
		"order_count_not_latter_zero":                              "订单数量不能小于0",
		"distance_must_asc":                                        "距离必须小到大顺序",
		"order_count_must_asc":                                     "订单数量必须小到大顺序",
		"max_distance_shipment_must_greater_distance_min_shipment": "距离大的配送费必须大于距离小的配送费",
		"max_order_count_shipment_must_greater_min_order_count_min_shipment": "订单多的配送费必须大于订单少的配送费",
		"count_order_failed":                              "统计订单失败",
		"shipper_state_unable":                            "当前配送员已关闭状态",
		"already_send_notify_unable_delete":               "已发送的通知不能删除",
		"this_special_weather_already_used_unable_delete": "当前特殊天气已使用，不能删除",
		"can_not_send_message_now":                        "该订单不能再写信",
		"contains_forbidden_word":                         "违禁词不能发送",
		"back_end":                                        "后台",
		"content_type_img":                                "[图片]",
		"content_type_card":                               "[卡片]",
		"reports":                                         "上报情况通知",
		// 20 商户关门、21： 商户没有准备好订单
		"report_types": map[int]string{
			20: "商户关门",
			21: "商户没有准备好订单",
		},
		"FullTimeState":       "时间区间",
		"MinDeliveryPrice":    "最低订单价格",
		"AllRestaurant":       "餐厅",
		"Steps":               "阶梯",
		"did_not_arrive_shop": "你与店铺的距离 %0.1f 米",
		"Lang":                "语言",
		"Platform":            "平台",
		// 状态  0：新建，1:启动，2：暂停，3：失效 ，4:删除
		"marketing_state_name":            map[int]string{0: "新建", 1: "启动", 2: "暂停", 3: "失效", 4: "删除"},
		"template_state_name":             map[int]string{0: "新建", 1: "启动", 2: "暂停", 3: "失效", 4: "删除"},
		"marketing_attendance_state_name": map[int]string{1: "新建", 2: "已发送邀请", 3: "已参加", 4: "拒绝参加"},
		"marketing_remain_day":            "活动结束还有%d天",
		"today_stop":                      "今天结束",
		"group_marketing":                 "团体活动",
		"merchant_marketing":              "商家活动",
		"submit_account_info":             "请提交账户信息后，再提交该信息",
		"State":                           "状态",
		"OrderNo":                         "订单号",
		"Receiver":                        "收货人",
		"Mobile":                          "手机号",
		"OrderPrice":                      "订单金额",
		"OriginPrice": "原价",
		"OriginalShipmentPrice":           "原配送价",
		"DiscountShipmentPrice":           "优惠配送金额",
		"Address":                         "地址",
		"OrderTime":                       "下单时间",
		"Shipper":                         "配送员",
		"OrderState":                      "订单状态",
		"restaurant_types": map[int]string{
			1: "餐厅",
			2: "超市",
			3: "便利店",
		},
		"restaurant_states": map[int]string{
			0: "关闭",
			1: "营业中",
			2: "休息中",
		},
		"unkown":                             "未知",
		"AreaID":                             "区域",
		"CityID":                             "城市",
		"seconds_ago":                        "秒前",
		"minute_ago":                         "分钟前",
		"hour_ago":                           "小时前",
		"day_ago":                            "天前",
		"offline":                            "未上线",
		"shipper_income_template_cant_empty": "模板不能为空",
		"market_order_exists_can_not_delete": "已经存在下单数据不能删除",
		"marketing_has_order_can_not_delete": "ID:%d, 该活动已经存在下单数据不能删除",
		"must_be_greater_than_zero":          "%s 必须大于0",
		"shipper_app_location_disabled":      "配送客户端的定位功能可能被关闭请检查或店铺位置数据可能不准确请检查",
		"shipment_reduce_step_name":          "%sKm~%sKm 减配送金额%s,商家承担%s/代理承担%s",
		"shipment_reduce_step_name_up":       "%sKm以上减配送金额%s,商家承担%s/代理承担%s",
		"name_ug":                            "维文名称",
		"name_zh":                            "中文名称",
		"start_time":                         "开始时间",
		"end_time":                           "结束时间",
		"full_week_state":                    "全周",
		"day":                                "日期",
		"full_day_state":                     "全日",
		"time":                               "时间",
		"auto_continue":                      "自动继续",
		"status":                             "状态",
		"step":                               "阶梯",
		"shipment_reduce_name":               "%s~%s公里以内减免 %s 元",
		"marketing_group_added_current_res":  "%s 已经加入过该活动",
		"shipment_reduce_name_up":            "%s公里以上减免 %s 元",
		"days": map[int]string{
			0: "星期一",
			1: "星期二",
			2: "星期三",
			3: "星期四",
			4: "星期五",
			5: "星期六",
			6: "星期日",
		},
		"group_template_push_lock":                  "5分钟后再次发送",
		"marketing_shipment_reduce":                 "减配送活动",
		"id_not_fund":                               "ID必须填",
		"this_group_marketing_stopped_enable_start": "该团体活动已停止，不能再次启动",
		"group_market_not_deletable":                "团体活动不能删除",
		"admin_create":                              "代理创建",
		"restaurant_create":                         "商家创建",
		"yes":                                       "是",
		"no":                                        "否",
		"sum":                                       "总共",
		"food_price_reduce":                         "美食优惠价格",
		"consume_type": map[int]string{
			0: "现金",
			3: "代理微信支付",
		},
		"pay_types": map[int]string{
			1: "现金",
			2: "餐币",
			3: "支付宝",
			4: "银联",
			5: "微信",
			6: "代理微信支付",
			7: "云闪付",
		},
		"order_over_time":              "过期",
		"material_start_code_is_empty": "宣传材料编号不能为空",
		"material_is_taken":            "材料已被领取",
		"advert_code_taken_by_others":  "[%s] 被别人领取",
		"Month":                        "月",
		"SortColumn":                   "排序字段",
		"SortType":                     "排序类型",
		"state_error":                  "状态错误",
		"advert_material_category_has_material_can_not_delete": "该分类下有宣传材料，不能删除",
		"advert_material_has_printed_can_not_delete":           "该宣传材料已经被打印，不能删除",
		"advert_material_not_found":                            "找不到宣传材料",
		"advert_material_print_batch_states": map[int]string{
			1: "待生成",
			2: "正在生成",
			3: "已生成",
		},
		"advert_material_print_batch_not_found":  "找不到宣传材料打印批次",
		"advert_material_unit":                   "个",
		"advert_material_category_is_different":  "先扫描的宣传文件和后扫描的文件类型不一致",
		"shipper_income_template_not_set":        "没有配置收入模板，不能领取材料",
		"time_range_error_3_month":               "时间范围不能超过3个月",
		"city_id_required":                       "城市不能为空",
		"area_id_required":                       "区域不能为空",
		"month_error":                            "月份错误",
		"shipper_not_exist":                      "配送员不存在",
		"advert_material":                        "宣传材料",
		"advert_code":                            "宣传码",
		"invalid_invite_user_fee":                "客户介绍奖励金额不正确",
		"invalid_invite_old_user_fee":            "老客户介绍奖励金额不正确",
		"invalid_order_percent":                  "客户下单奖励比例不正确",
		"get_prize_user_list_failed":             "获取中奖用户列表失败",
		"empty_prize_user_list":                  "无有效中奖用户数据",
		"advert_code_end_invalid":                "结束号码不存在",
		"advert_code_start_end_invalid":          "扫描顺序不正确,请检查扫描顺序是否正确",
		"special_price_order_not_belong_shipper": "特价活动订单不属于该配送员",
		"admin_types": map[int]string{
			1: "超级管理员",
			2: "区域管理员",
			3: "代理",
			4: "代理副账号",
			5: "餐厅管理员",
			6: "餐厅管理员副账号",
			7: "百户长",
			8: "配送员管理员",
			9: "配送员",
		},

		"customer":                       "客户",
		"advert_material_over_the_limit": "宣传材料数量不可超过%d个",
		"foods_preferential_states": map[int]string{
			0: "关闭",
			1: "开启",
		},
		"seckill_states": map[int]string{
			0: "关闭",
			1: "开启",
		},
		"seckill_review_states": map[int]string{
			1: "待审核",
			2: "审核通过",
			3: "被拒绝",
		},
		"user_max_order_count_is_zero":                      "最大可购买次数不能为空",
		"begin_time_is_not_valid":                           "开始时间不能小于现在",
		"seckill_is_exist":                                  "存在开始时间相同的秒杀",
		"price_is_less_than_food_price":                     "活动价必须小于原价",
		"food_not_exist":                                    "选择的美食不属于该店铺",
		"seckill_update_state_failed":                       "更改秒杀状态失败",
		"seckill_not_exist":                                 "秒杀数据不存在",
		"seckill_delete_failed":                             "秒杀活动删除失败",
		"food_state_error":                                  "美食状态错误",
		"food_state_error_0":                                "美食状态错误，请编辑后等待审核完成",
		"food_state_under_review":                           "美食处于审核状态",
		"food_state_incorrect":                              "美食状态的值只能是 0, 1, 或 2",
		"restaurant_state_error":                            "店铺状态错误",
		"seckill_end_time_is_over":                          "秒杀活动结束时间已过，无法开启",
		"food_id_required":                                  "美食不能为空",
		"discount_price_required":                           "折扣价不能为空",
		"preferential_percentage_required":                  "折扣百分比不能为空,且必须大于0，小于100",
		"start_time_must_before_end_time":                   "开始时间必须小于结束时间",
		"start_date_time_must_before_end_date_time":         "开始日期时间必须小于结束日期时间",
		"food_referential_time_overlap":                     "美食优惠时间重叠",
		"discount_price_must_less_than_price":               "折扣价必须小于原价",
		"discount_timeline_not_in_restaurant_open_timeline": "折扣时间不在店铺营业时间内",
		"restaurant_foods_not_found":                        "店铺美食不存在",
		"StartDateTime":                                     "开始日期",
		"EndDateTime":                                       "结束日期",
		"OrderCountPerDay":                                  "每日可购买次数",
		"FoodID":                                            "美食",
		"PreferentialStyle":                                 "优惠范围",
		"MaxOrderCount":                                     "最大购买数",
		"end_date_time_must_after_today":                    "结束日期必须大于等于今天",
		"can_not_update_expired_foods_preferential":         "无法修改到期的优惠活动",

		"function_disabled":                         "该功能已被禁用",
		"need_to_update_shipper_update_sex_and_age": "为更加完善平台功能，目前需要补充配送员相关信息，希望您配合平台工作及时完善",
		"msg_title": "提示",
		"yes_btn":   "确认",
		"no_btn":    "取消",
		"age_error": "年龄不在合理范围内",
		"Age":       "年龄",
		"Sex":       "性别",

		"invite_user_fee_invalid":                           "拓展新客户奖励不能低于1元",
		"order_percent_invalid":                             "新客户订单分润不能低于2%",
		"invite_old_user_fee_invalid":                       "激活老客户奖励不能低于0.2元",
		"order_old_user_percent_invalid":                    "激活老客户订单分润不能低于1%",
		"shipper_income_template_not_set_can_not_use":       "没有配置收入模板,无法使用该功能",
		"package_delivery_in_progress_can_not_return_order": "订单配送中，不能退单，请联系管理员",
		"cash_order_not_pay":                                "没有支付的现金订单不能完成",
		"total_count_is_less_than_user_max_order_count":     "最多销售数量不能小于单个用户可购买数量",
		"seckill_food_not_in_activity_range":                "活动时间不在出餐时间端内",
		"food_not_in_activity_range":                        "活动时间不在出餐时间端内,出餐时间从[%s]到[%s]",
		"old_shipper_not_exists":                            "原来的配送员不存在",
		"new_shipper_not_exists":                            "现在的配送员不存在",
		"old_shipper_not_disabled":                          "原配送员在开启状态，不能替换",
		"new_shipper_is_disabled":                           "现在的配送员状态已关闭，不能替换",
		// 1:老板，2:店铺管理员，3:财务管理员，4:订单管理员，5：客服
		"merchant_type_name": map[int]string{
			1: "老板",
			2: "店铺管理员",
			3: "财务管理员",
			4: "订单管理员",
			5: "客服",
		},
		"shop_have_order_enable_delete": "店铺有今天的订单，暂不能删除",
		"invalid_password":              "密码必须要6位以上，并最少包含2个字母",
		"shipping_area_steps_error":     "距离阶梯数据不正确",
		"rush_hour_can_not_operate":     "高峰期不能执行此操作,（中午12点至16点，下午18点至21点）",
		"amap_key_error":                "高德地图配置错误，请联系管理员",
		"restaurant_state_close":        "餐厅在关闭状态，请联系管理员",
		"ResID":                         "餐厅ID",
		"Rid":                           "餐厅ID",
		"FoodsCategoryID":               "美食分类",
		"FoodId":                        "美食ID",
		"Position":                      "定位",
		"NewPassword":                   "新密码",
		"OrderID":                       "订单",
		"please_try_again_later":        "验证码已发送，请稍后再试！",
		"send_code_fail":                "验证码发送失败！",
		"send_code_success":             "验证码发送成功！",
		"Password":                      "密码",
		"verify_success":                "验证成功",
		"verify_code_error":             "验证码不正确",
		"verify_code_fail":              "查询验证码失败",
		"verify_code_not_found":         "未找到有效验证码",
		"password_update_success":       "密码修改成功",
		"verify_code_not_verify":        "短信未验证，或者短信验证过期",
		"function_maintenance":          "此功能维护中",

		"food_open_time_error": "\"%s\"的出餐时间与餐厅开放时间不一致，请重新修改后提交",
		//为找所属商家信息
		"admin_belong_merchant_not_found": "未找到所属商家信息",
		"validator_should_be_int":         "\"%s\"必须是数字",
		"info_in_review":                  "信息在审核中，请勿重复提交",
		"DescriptionUg":                   "维文描述",
		"DescriptionZh":                   "中文描述",

		"BeginTime":                            "开始时间",
		"EndTime":                              "结束时间",
		"AllFoodsId":                           "美食分类",
		"Price":                                "价格",
		"ReadyTime":                            "准备时间",
		"Image":                                "图片",
		"password_not_match":                   "新的验证码不一致",
		"old_password_error":                   "旧密码错误",
		"OldPassword":                          "旧密码",
		"customer_order_mobile":                "订餐收货手机号",
		"user_mobile":                          "原下单手机号",
		"order_dealer_mobile":                  "订单处理人手机号",
		"admin_mobile":                         "管理员手机号",
		"boss_mobile":                          "老板手机号",
		"order_is_overtime":                    "支付时间已过",
		"lottery_activity_is_expired":          "抽奖活动已过期",
		"lottery_activity_id_required":         "活动ID不能为空",
		"lottery_activity_prize_list_required": "奖品列表不能为空",
		"lottery_activity_prize_stock_not_enough":             "%s奖品库存不足，库存数量：%d",
		"lottery_activity_not_found":                          "未找到活动",
		"lottery_activity_state_error":                        "活动状态有误",
		"lottery_activity_prize_range_error":                  "%s中奖范围不能小于%d",
		"lottery_activity_prize_start_index_must_bigger_than": "%s中奖范围开始区间不能小于已抽奖数量+%d",
		"lottery_activity_prize_range_must_bigger_than":       "%s中奖范围区间不能小于奖品数量的3倍",
		"lottery_activity_prize_id_not_match":                 "奖项ID和奖项不匹配",
		"lottery_activity_generate_winners_fail":              "生成中奖用户失败，请扩大中奖区域:开始位置:%d, 结束位置: %d",
		"lottery_activity_prize_get_valid_data_failed":        "获取有效奖品数据失败",
		//美食状态错误
		"total_salary":               "工资",
		"total_order_count":          "订单数量",
		"amount_order":               "配送收入",
		"amount_reward":              "考勤奖励",
		"complain_count":             "投诉数量",
		"count_comment_bad":          "差评",
		"amount_complain":            "投诉扣款",
		"late_order_count":           "迟到订单",
		"amount_late":                "迟到扣款",
		"amount_tips":                "打赏",
		"amount_spectal_time":        "特殊时间",
		"amount_special_weather":     "特殊天气",
		"amount_comment_good":        "好评奖励",
		"amount_comment_bad":         "差评扣款",
		"comment_good_count":"好评数量",
		"punishment":                 "扣款金额",
		"amount_invite_user":         "客户介绍",
		"amount_order_tips":          "下单分成",
		"amount_special_price_order": "活动收入",
		"count_special_price_order":  "活动数量",
		//与餐厅营业时间有冲突
		"time_confilict_with_res":          "与餐厅营业时间有冲突",
		"time_param_format_error":          "时间格式错误",
		"pay_platform_is_not_lakala":       "该订单不能走拉卡拉支付通道",
		"your_phone_has_non_compliant_app": "你的手机中存在 [%s] 等不合规软件，请卸载后使用配送客户端",
		"promotion_create_disabled":        "该区域暂时不能创建活动",
		"promotion_update_disabled":        "该区域暂时不能编辑活动",
		"customer_order_address":           "客户收货地址",
		"customer_app_pos":                 "客户下单位置",
		"customer_old_address":             "客户历史收货地址",
		"order_is_closed_can_not_pay":      "订单已关闭,不能支付",
		"order_is_canceled_can_not_pay":    "订单已取消,不能支付",
		"not_found_state_params":           "必传状态参数",
		"shipper_attendance_off":           "先上班打卡，在抢订单",
		"insurance_already_buy":            "明天的保险已购买",
		"shipper_not_insured":              "未购买今天的保险，不能抢单",
		"insurance_not_buy":                "保险还没有购买",
		"insurance_send_can_not_cancel":    "保险已发送，不能撤回",
		"insurance_state": map[int]string{
			0: "新建",
			1: "购买保险",
			2: "等待发送到保险",
			3: "保险通过",
			4: "保险不通过",
			5: "休息",
		},
		"real_name_states": map[int]string{
			0: "未提交",
			1: "待审核",
			2: "审核未通过",
			3: "审核通过",
		},
		"collection_merchant_alter_state": map[int]string{
			0: "",
			1: "营业执照和法人信息变更",
			2: "账户信息变更",
			3: "管理员信息变更",
			4: "许可证",
			5: "门店信息",
		},
		"page_errors": map[int]string{
			1: "商家信息",
			2: "身份证信息",
			3: "结算账户",
			4: "店铺信息",
			5: "管理员信息",
			6: "许可证信息",
			7: "员工信息",
		},
		"verify_state_name": map[int]string{
			0: "提交入网",
			1: "在线签约中",
			2: "入网成功",
			3: "入网失败",
		},
		"id_card_info_not_exist": "身份证信息不全,请提交账户信息后，再提交该信息",
		"amount_insurance":       "保险扣费",
		"real_name_alert":        "实名认证未完成，请完成实名认证",
		"real_name_alert_info": []map[string]interface{}{
			map[string]interface{}{
				"number":     0,
				"title":      "实名认证",
				"content":    "你没有进行实名认证，需要进行实名认证",
				"ok_btn":     "实名认证",
				"cancel_btn": "",
			},
			map[string]interface{}{
				"number":     1,
				"title":      "实名认证",
				"content":    "你没有进行实名认证，需要进行实名认证",
				"ok_btn":     "实名认证",
				"cancel_btn": "",
			},
			map[string]interface{}{
				"number":     2,
				"title":      "实名认证",
				"content":    "你没有进行实名认证，需要进行实名认证",
				"ok_btn":     "实名认证",
				"cancel_btn": "",
			},
			map[string]interface{}{
				"number":     3,
				"title":      "实名认证",
				"content":    "实名认证已完成",
				"ok_btn":     "实名认证",
				"cancel_btn": "",
			},
		},
		"insurance_alert_info": []map[string]interface{}{
			map[string]interface{}{
				"number":     0,
				"title":      "保险缴费",
				"content":    "为了顺利进行第二天的工作请购买保险，23:00点后会自动进行保险扣费",
				"ok_btn":     "缴费",
				"cancel_btn": "明天休息",
			},
			map[string]interface{}{
				"number":     1,
				"title":      "保险缴费",
				"content":    "为了顺利进行第二天的工作请购买保险，23:00点后会自动进行保险扣费",
				"ok_btn":     "缴费",
				"cancel_btn": "明天休息",
			},
			map[string]interface{}{
				"number":     2,
				"title":      "保险缴费",
				"content":    "为了顺利进行第二天的工作请购买保险，23:00点后会自动进行保险扣费",
				"ok_btn":     "缴费",
				"cancel_btn": "明天休息",
			},
			map[string]interface{}{
				"number":     3,
				"title":      "保险缴费",
				"content":    "为了顺利进行第二天的工作请购买保险，23:00点后会自动进行保险扣费",
				"ok_btn":     "缴费",
				"cancel_btn": "明天休息",
			},
			map[string]interface{}{
				"number":     4,
				"title":      "保险被拒绝",
				"content":    "保险被拒绝，请联系管理员",
				"ok_btn":     "详情",
				"cancel_btn": "明天休息",
			},
		},
		"try_again_after_period_time":                                       "请在%s秒后重试",
		"two_passwords_are_inconsistent":                                    "两次密码不一致",
		"the_random_string_has_expired_please_resend_the_verification_code": "随机字符串已过期、请重新发送验证码",
		"insurance_over_time_can_not_buy":                                   "保险购买时间已过",
		"insurance_not_approved":                                            "保险被拒绝，请联系管理员",
		"insurance_before_time_can_not_buy":                                 "保险购买时间还没到 [%s] 后可以开始购买",
		"insurance_not_exist":                                               "[%s]的保险信息不存在",
		"insurance_state_not_ok":                                            "[%s]的保险状态不对",
		"seckill_run_state_name": map[int]string{
			0: "未开始",
			1: "执行中",
			2: "已结束",
		},
		"exist_conflict_seckill":                 "改美食已存在开启状态的，同一个开始时间的秒杀活动，请先关闭",
		"exist_conflict_special":                 "改美食已存在开启状态的，同一个开始时间的特价活动，请先关闭",
		"seckill_time_not_in_foods_time_range":   "活动时间必须美食出餐时间内",
		"seckill_time_must_after_now_time":       "活动开始时间必须大于当前时间",
		"seckill_order_time_must_after_end_time": "配送时间必须大于活动结束时间",
		"same_time_error":                        "活动时间重复",
		"idcard_repeat":                          "该身份证号已经被其他配送员通过认证",
		"insurance_request_sent":                 "明天的购买保险请求已经发送",
		"shipper_not_real_name":                  "未完成实名认证，不能抢单",
		"permit_start_time_err":                  "相关证书有效期开始时间错误",
		"permit_end_time_err":                    "相关证书有效期结束时间错误",
		"info_update_err":                        "信息更新错误",
		"request_denied_by_admin":                "取消请求被管理员拒绝",
		"try_after_some_time":                    "[%s]后后再试",
		//'显示位置 1.首页弹窗 2.首页内容上方 3.首页弹窗 和 首页内容上方',
		"lottery_activity_pos": map[int]string{
			1: "首页弹窗",
			2: "首页内容上方",
			3: "首页弹窗 和 首页内容上方",
		},
		"order_amount_cannot_less_than_discount_amount": "优惠金额不能大于订单金额",
		"activity_already_started_enable_delete":        "活动已开始，不能删除",
		"prize_already_used_enable_delete":              "奖品已使用，不能删除",
		"prize_already_used_enable_edit":                "奖品已使用，不能更新",
		"prize_already_used_enable_close":               "奖品已使用，不能关闭",
		"coupon_already_used_cannot_delete":             "优惠券已使用,不能删除",
		"coupon_already_used_cannot_update":             "优惠券已使用,不能修改",
		"end_active_cannot_open":                        "已结束的活动不能开启",
		"end_active_cannot_edit":                        "已结束的活动不能编辑",
		"activity_time_conflict":                        "存在时间冲突的开启状态活动，不能开启状态",

		"set_channel_name": map[int]string{
			0: "配送员枪单",
			1: "配送员枪单",
			2: "管理员分配",
			3: "系统分配",
			4: "客户指定",
		},
		"already_send_request_once":   "已经发送过一次请求了，请不要再次发送",
		"already_finish_order":        "已经完成订单，不能申请",
		"prolong_booking_time_failed": "延长配送时间失败",
		"excel_file_empty":            "Excel文件内容不正确",
		"insurance_date_error":        "Excel文件中的日期不对",
		"foods_group_state": map[int]string{
			0: "关闭",
			1: "开启",
		},
		"foods_group_review_state": map[int]string{
			1: "待审核",
			2: "审核通过",
			3: "被拒绝",
		},
		"restaurant_foods_state": map[int]string{
			0: "关闭",
			1: "开启",
			2: "已售完",
			3: "审核中",
		},
		"cant_delete_bind_foods_group": "改分组已绑定过美食，不能删除",
		"chance_state_names": map[int]string{
			1: "待抽奖",
			2: "已抽奖",
			3: "已中奖",
			4: "未兑换奖品",
			5: "订单已过期",
			6: "订单已退款",
		},
		"lottery_prize_level":            "%d-等奖",
		"you_have_some_order_not_finish": "您有未完成的订单,请先处理订单再重试",
		"lottery_used":                   "已经抽奖过了",
		"coupon_used":                    "优惠券已经被使用",
		"refunded":                       "已经退款",
		"chance_type_names": map[int]string{
			1: "抽奖购买获得",
			2: "高额下单获得",
			3: "分享后对方高额下单获得",
			4: "分享后对方抽奖购买获得",
		},

		"AutoDispatchWait":                   "智能派单压单时间",
		"seckill_max_order_count_error":      "最大订单数有误",
		"seckill_price_error":                "秒杀价格有误",
		"no_seckill_params":                  "必传秒杀参数",
		"TotalCount":                         "总数",
		"restaurant_close_state":             "店铺关闭状态",
		"foods_close_state":                  "美食关闭状态",
		"normal":                             "状态",
		"price_food_in_progress_cant_refund": "活动进行中，不能退款",
		"price_markup_food_payed": map[int]string{
			0: "未支付",
			1: "已支付",
		},
		"price_markup_food_refunded": map[int]string{
			0: "未退款",
			1: "已退款",
		},
		"price_markup_food_state": map[int]string{
			1: "待支付",
			2: "待商家确认",
			3: "商家已确认",
			4: "商家拒绝",
			5: "已退款",
		},
		"price_markup_food_running_state": map[int]string{
			1: "正常",
			2: "暂停",
		},
		"price_markup_conflict":               "该时间段已有正常执行的美食加价活动 <<%s>>",
		"price_markup_stop_enable_edit":       "该活动已结束，不能编辑",
		"payed_price_markup_stop_enable_edit": "该活动已支付，只能修改活动时间",
		"missing_parameter":                   "缺少参数",

		"already_have_the_same_zh_name": "已存在相同的国语名称",
		"already_have_the_same_ug_name": "已存在相同的维语名称",
		"try_again_refresh_page":        "请刷新页面后重试",

		"attendance_off": "未上班",

		"price_markup_total_count_is_not_enough": "加价美食库存不足，库存数量：%d",
		"SmsCode":                                "验证码",
		"markup_sale":                            "平台销售",
		"merchant_sale":                          "商家销售",
		"in_price":                               "进价",
		"total_count":                            "总数",
		"in_price_must_less_food_price":          "进价不能大于美食价格",
		"price_markup_food_is_not_running":       "请检查加价美食状态是否启动",
		"price_markup_food_discount_price_gte_in_price": "加价美食优惠价格大于或等于进价",
		"not_stopped_yet":                     "活动还没有停止",
		"no_pay_log":                          "支付记录不存在",
		"price_markup_seckill_not_allow_edit": "代理销售美食的活动不能修改",

		"snow_game":                                  "抓雪球游戏优惠",
		"ended_active_enable_edit":                   "已结束的活动信息不能修改",
		"time_enable_out_price_markup_time_range":    "设置的活动时间不在加价活动时间范围内",
		"max_order_count_cannot_exceed_markup_count": "最大购买数量不能大于加价销售库存数量",

		"update_fail":               "修改失败",
		"create_fail":               "创建失败",
		"delete_fail":               "删除失败",
		"query_fail":                "查询失败",
		"please_select_an_activity": "请选择一个活动",

		"part_refund_food_count_error": "退款数量不正确",
		"part_refund_food_price_error": "退款金额不正确",
		"not_fund_admin":               "管理员信息不存在",
		"part_refund_amount_error":     "退款金额不正确",

		"max_duration_err":                      "加长的时间不能超过%d分钟",
		"open_state_out_off_price_markup_stock": "开启本活动，购买的加价美食超过仓库数量，查看您已创建的活动仓库使用情况。",
		"theme_activity_not_found":              "主题活动不存在",
		"theme_activity_time_conflict":          "该时间段已存在其他主题活动",
		"shop_not_verified":                     "商户未入驻，请入驻后重试",
		"no_shipper_info":                       "无法找到骑手信息",
		"gender": map[int]string{
			0: "未知",
			1: "男",
			2: "女",
		},
		"can_not_part_refund_twice":               "该订单不能进行在部分退款",
		"ranking_order_activity_time_error":       "预热时间要小于活动开始时间!",
		"ranking_order_activity_prize_list_error": "请检查中奖序列是否正常",
		"ranking_order_activity_prize_list_empty": "奖品列表不能为空",
		"ranking_order_activity_time_cross":       "这个时间段内已存在其他活动",
		"ranking_order_activity_state_names": map[int]string{
			0: "关闭",
			1: "开启",
			2: "暂停",
			3: "过期",
		},

		"activity_already_started_can_not_delete":      "活动已经开始，不能删除",
		"activity_state_open_can_not_delete":           "活动状态为开启，不能删除",
		"activity_has_record_can_not_delete":           "活动已产生记录，不能删除",
		"activity_already_started_can_not_update":      "活动已经开始，不能修改",
		"ranking_order_activity_prize_index_duplicate": "序号不能重复",
		"ranking_order_activity_prize_id_duplicate":    "奖品不能重复",
		"food_has_multiple_discount":                   "[%s]已存在正在进行的多份打折活动",
		"food_has_pref":                                "[%s]已存在正在进行的优惠活动",
		"food_has_seckill":                             "[%s]已存在正在进行的秒杀活动",
		"food_has_special":                             "[%s]已存在正在进行的特价活动",
		"foods_multiple_discount_state_names": map[int]string{
			0: "新建",
			1: "进行中",
			2: "暂停",
			3: "过期",
		},
		"you_are_not_creator_can_not_update": "你不是创建人，不能修改",
		"you_are_not_creator_can_not_delete": "你不是创建人，不能删除",

		"part_refund_chanel_name": map[int]string{
			0: "正常",
			1: "系统",
			2: "后台",
			3: "商家",
			4: "顾客",
		},
		// 1:用户 2:后台 3:商家 4:自动退单
		"part_refund_creator_name": map[int]string{
			1: "用户",
			2: "后台",
			3: "商家",
			4: "自动退单",
		},
		"total_count_over_price_markup_total_count": "设置的秒杀数量超过加价美食库存数量",
		"please_use_new_image":                      "请使用新的图片",
		"未戴头盔":                                      "未戴头盔",
		"表情未微笑":                                     "表情未微笑",
		"拍摄角度不正，未显示正面":                              "请头戴头盔，露出正面，再进行拍摄",
		"服装不整齐或不干净":                                 "服装不整齐或不干净",

		"created_by_store":                   "商家创建的",
		"time_conflict":                      "有时间冲突",
		"marketing_have_order":               "活动已存在下单数据，不能删除",
		"and":                                "和",
		"ing":                                " ",
		"expired":                            "已过期",
		"time_conflict_in_multi_discount":    "该时间段内已存在其他多份打折活动",
		"food_has_marketing":                 "[%s] 设置过满减活动",
		"activity_has_record_can_not_update": "活动已产生记录，不能修改",
		"foods_multiple_discount_activity_has_record_can_not_update": "多份打折活动已有用户下单,不能修改",
		"foods_multiple_discount_activity_has_record_can_not_delete": "多份打折活动已有用户下单,不能删除",

		"token_is_error": "token 错误",
		"multiple_foods_cannot_create_multiple_spec_groups": "多个美食不能创建多个规格组",
		"spec_group_cannot_be_empty":                        "规格组不能为空",
		"spec_group_must_letter_6":                          "最多能创建6个规格组",
		"spec_option_cannot_be_empty":                       "规格项不能为空",
		"price_type_group_price_error":                      "改价类型规格项价格不能为0",
		"spec_option_must_selected":                         "改价类型规格项价格不能为0",
		"food_id_cannot_be_empty":                           "美食ID不能为空",
		"food_not_found":                                    "找不到美食",
		"spec_food_setting_activity":                        "美食规格已设置过活动，请先关闭后，再编辑",
		"multi_discount_count":                              "(%s) %d 个",
		"Tel":                                               "电话",
		"Tel2":                                              "电话",
		"Tel3":                                              "电话",
		"Tel4":                                              "电话",
		"Tel5":                                              "电话",
		"excel_process_start":                               "开始处理",
		"excel_process_running":                             "处理中",
		"excel_process_finished":                            "处理完成",
		"excel_process_failed":                              "处理失败",
		"excel_row_empty":                                   "第%d行 为空",
		"excel_row_column_empty":                            "第%d行[%s]为空",
		"excel_row_column_illegal":                          "第%d行 [出餐时间] 不在 [%s] 之内",
		"excel_row_column_dealer_profit_min_illegal":        "第%d行 [配送百分比] 不能小于 [%f]",
		"excel_row_column_dealer_profit_max_illegal":        "第%d行 [配送百分比] 不能大于 [%f]",

		"excel_row_column_dealer_self_profit_min_illegal": "第%d行 [配送百分比] 不能小于 [%f]",
		"excel_row_column_dealer_self_profit_max_illegal": "第%d行 [配送百分比] 不能大于 [%f]",
		"excel_row_column_lunch_box_illegal":              "第%d行 [饭盒] [%s] 数据库中不存在",
		"excel_row_column_food_quantity_illegal":          "第%d行 [美食数量类型中文] [%s] 数据库中不存在",
		"excel_row_column_img_illegal":                    "第%d行 图片地址必须包含http或https",
		"excel_row_column_zip_illegal":                    "zip 压缩包中不存在xlsx文件",
		"lng_or_lat_is_empty":                             "手机定位信息不能为空，请检查手机GPS是否开启，或者是否给App定位权限",

		"adv_link_type": map[int]string{
			0:  "Wap手机页面",
			1:  "商家详情",
			2:  "商品详情",
			3:  "第三方小程序",
			4:  "Mulazim小程序",
			5:  "排行榜活动",
			6:  "特价活动",
			7:  "秒杀活动",
			99: "图片(不会跳转)",
		},
		"adv_link_type_dealer": map[int]string{
			0: "Wap手机页面",
			1: "商家详情",
			2: "商品详情",
			// 3:  "第三方小程序",
			// 4:  "Mulazim小程序",
			5:  "排行榜活动",
			6:  "特价活动",
			7:  "秒杀活动",
			99: "图片(不会跳转)",
		},
		"advert_state_error":                        "广告状态有误",
		"spec_food_cannot_create_multiple_discount": "规格美食不能创建多份打折活动",
		"spec_options_should_be_provided":           "规格美食，需提供规格项数据",
		"combo_food_time_illegal":                   "您选的美食没有共同时间段",
		"this_has_spec_foods":                       "该餐厅有设置规格的美食，不能批量设置优惠活动",
		"spec_food_setting_seckill":                 "该美食已设置过秒杀活动，请先关闭后，再编辑",
		"spec_food_setting_special_price":           "该美食已设置过特价活动，请先关闭后，再编辑",
		"spec_food_setting_preferential":            "该美食已设置过优惠活动，请先关闭后，再编辑",
		"spec_food_setting_price_markup":            "该美食已设置过加价活动，请先关闭后，再编辑",
		"spec_food_setting_marketing_reduce":        "该美食已设置过满减活动，请先关闭后，再编辑",
		"combo_not_able_create_spec":"套餐不能创建规格",
		"spec_info_has_changed":"设置的规格信息已经有更改，不能开启状态，请重新创建使用",
		"foods_combo_items_empty":"套餐美食没有选择美食",
		"foods_combo_items_can_not_be_combo":"套餐美食不能设置为套餐",
		"combo_price_need_le_items_price":"套餐价格不能高于总美食原价",
		"multi_discount_foods_enable_create_spec":"多分打折设置过的美食不能创建规格",
		"foods_time_not_in_restaurant_time_range":"美食的出餐时间段不在餐厅的出餐范围内",
		"foods_percent_range":"美食百分比范围",
		"self_take_percent_range":"自取百分比范围",
		"lunch_box_not_found":"未找到相关饭盒信息",
		"update_foods_info_fail":"更新美食信息失败",
		"update_foods_category_fail":"分类更新失败",
		"update_combo_fail":"套餐更新失败",
		"spec_type_count_max_6":"该美食规格组数量不能超过6",
		"spec_food_no_options":"规格美食没有规格项信息",
		"food_spec_type_error":"美食类型错误",
		"combo_item_deleted":"套餐中的[%s]美食已被删除,不能选择已删除的美食",
		"states": map[int]string{
			0: "关闭",
			1: "开启",
		},
		"order_rank_list":[]map[string]interface{}{
			{"key1":"","key2":"base_score","name":"基础分","multiply":"1"},
			
			{"key1":"delivered_on_time_order_count","key2":"delivered_on_time","name":"准时送达","multiply":"1"},
	
			{"key1":"positive_reviews_count","key2":"positive_reviews_score","name":"好评","multiply":"1"},
			
			{"key1":"mild_lateness_count","key2":"mild_lateness_deduct","name":	"迟到(5分钟之内）","multiply":"-1"},
			
			{"key1":"moderate_lateness_count","key2":"moderate_lateness_deduct","name":	"迟到(5-10分钟)","multiply":"-1"},
			
			{"key1":"severe_lateness_count","key2":"severe_lateness_deduct","name":"严重迟到","multiply":"-1"},
			
			{"key1":"negative_reviews_count","key2":"negative_reviews_deduct","name":"差评","multiply":"-1"},
			
			{"key1":"complaints_count","key2":"complaints_deduct","name":"投诉","multiply":"-1"},
			
			{"key1":"early_delivery_count","key2":"early_delivery_deduct","name":"早送(20分钟)","multiply":"-1"},
	
		},
		"week":"星期",
		"auto_dispatch_order_deliver_state": map[int]string{
			0:  "按时配送",
			1:  "迟到5分钟",
			2:  "迟到10分钟",
			3:  "重度迟到",
			4:  "早送",
		},
		"auto_dispatch_order_comment_state_has":"有",
		"auto_dispatch_order_comment_state_has_not":"没有",
		"auto_dispatch_order_complain_state_has":"有",
		"auto_dispatch_order_complain_state_has_not":"没有",
		"date_range_can_not_over_x_days":                                                    "开始日期到结束日期距离不能大于%d天",
		"late":"迟到",
		"shipper_rank_detail_types": map[int]string{
			1:  "基础分",
			2:  "好评",
			3:  "准时送达的订单",
			4:  "总订单",
			5:  "迟到5分钟以下",
			6:  "迟到5-10分钟",
			7:  "迟到10分钟以上",
			8:  "差评",
			9:  "投诉",
			10:  "预约订单提前20分钟送达",
		},
		"not_area_config":"区域配置不存在,请先配置区域",
		"not_area_server_config":"区域默认服务区域范围不存在,请联系管理员",
	}
}
