package lang

// LangUtil
//
//	@Description: 国际化助手
//	@author: <PERSON><PERSON><PERSON>
//	@Time: 2022-09-03 13:59:36
type LangUtil struct {
	Lang string
}

// T
//
//	@Description: 获取国际化文字
//	@author: <PERSON><PERSON><PERSON>
//	@Time: 2022-09-03 13:58:04
//	@receiver l *LangUtil
//	@param key string 键
//	@return string 值
func (l *LangUtil) T(key string) string {
	if l.Lang == "ug" {
		if _, ok := Ug[key]; ok {
			// 存在
			return Ug[key].(string)
		}
		return key
	}
	if _, ok := Zh[key]; ok {
		// 存在
		return Zh[key].(string)
	}
	return key
}

func (l *LangUtil) TUg(key string) string {
	if _, ok := Ug[key]; ok {
		// 存在
		return Ug[key].(string)
	}
	return key
}

func (l *LangUtil) TZh(key string) string {
	if _, ok := Zh[key]; ok {
		// 存在
		return Zh[key].(string)
	}
	return key
}

// TArr
//
//	@Description: 从国际化文件获取Map 类型
//	@author: <PERSON><PERSON><PERSON>
//	@Time: 2022-09-03 13:56:35
//	@receiver l *LangUtil
//	@param key string 键
//	@return map[int]string 返回Map
func (l *LangUtil) TArr(key string) map[int]string {
	if l.Lang == "ug" {
		return Ug[key].((map[int]string))
	}
	return Zh[key].((map[int]string))
}

func (l *LangUtil) TArrMap(key string) []map[string]interface{} {
	if l.Lang == "ug" {
		return Ug[key].(([]map[string]interface{}))
	}
	return Zh[key].(([]map[string]interface{}))
}

func (l *LangUtil) TArrZh(key string) map[int]string {
	return Zh[key].((map[int]string))
}

func (l *LangUtil) TArrUg(key string) map[int]string {
	return Ug[key].((map[int]string))
}

func (l *LangUtil) TArrString(key string) map[string]string {
	if l.Lang == "ug" {
		return Ug[key].((map[string]string))
	}
	return Zh[key].((map[string]string))
}
