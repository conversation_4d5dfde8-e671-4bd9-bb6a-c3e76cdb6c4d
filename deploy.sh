cat>$1<<EOF
{
  "environment": "production",
  "config": {
    "production": {
      "redis_ip": "$REDIS_IP",
      "redis_password": "$REDIS_PASSWORD",
      "redis_db": 0,
      "concurrency": 1,
      "cdn_url": "https://acdn.mulazim.com/",
      "upload_root_dir": "/mnt/mulazim-files/",
      "wechat_app_id" : "wxdc256e0bac869579",
      "wechat_app_secret" : "$WECHAT_APP_SECRET",
      "mysql_dsn": "$MYSQL_DNS",
      "mysql_dsn_read_db1": "$MYSQL_DNS_READ_DB1",
      "jwt_key": "$JWT_KEY",
      "shumai_appid":"rItBa0bst8MCthsp",
      "shumai_appsecret":"rItBa0bst8MCthsp1B45hj9WT6jKBDo4",
      "aliyun_app_code":"7bc2bf702a72425d906f23bbd83c9ef4",
       "aliyun_sms_access_id":"LTAI4GJf1F5GrTjtzQLk93c9",
      "aliyun_sms_access_secret":"******************************",
      "pay_money_time" : 10,
      "tencent_api_key":"AKIDAbu1ynQq59EJYZdaOzb4M7lBOOKJG3qs",
      "tencent_api_secret":"85rX5QWOxMbgXaT7XWL4d4LQIyKagZFg",
      "ding_ding_msg":"http://**********:8081/send-dingding-msg",
      "archived_file_path":"/www/wwwroot/mulazim-api-go/archived_files",
      "socket_url":"http://smart-go.golang.ec-api.internal.mulazim.com:8086/ug/v1/socket/order-state-change/",
      "cash_order_post_sms_url":"http://**********:1215/ug/v1/sms-send",
      "max_cashout_amount":50000,
      "day_cashout_limit":2000000,
      "ffmpeg_mode":2,
      "port":"0.0.0.0:8085",
      "shipper_push_order_distance": [600,1200,3000],
      "shipper_push_order_time_delta": [1,2,3],
      "shipper_push_area": [56,65,66,69,67,70,59,53,124],
      "shipper_push_area_all": 1,
      "shipper_app_version":"2.99",
      "merchant_app_version":"2.2.7",
      "cms_key" : "fteTgPwQbWtaB8HX3YHZFewKLukYTCzb6KNH9i1MnaQ=",
      "cms_cipher" : "AES-256-CBC",
      "cms_url":"https://cms.mulazim.com/",
      "lakala":{
          "app_id":"7076471896390946817",
          "server_url":"https://moneyconnect.lakala.com/service/soa",
          "root_url":"http://api.mulazim.com",
          "dsa_private_key_pem_path":"./configs/certs/lakala/rsa_privatekey.pem",
          "rsa_public_pub_path":"./configs/certs/lakala/cloud_rsa_public.pub",
           "system_mer_no":"7076471907119063040",
           "system_merchant_no":"7201883786084397056",
           "system_min_reserve_amount":100000,
           "warning_admin_number":"18129290467",
           "mulazim_withdraw_cart_id" : "7102952371870699520",
           "lakala_order_prefix":"LAKALA_",
           "shipper_pay_qrcode_url":"https://qr.mulazim.com/shipper-pay?id=",
           "agent_pay_app_id":"wxe2fefdde08e515a6",
           "pay_qrcode_url":"https://qr.mulazim.com"
      },
      "cash_out_start_time":7,
      "cash_out_end_time":23,
      "cash_out_notification_ug":"پۇل چىقىرىش ۋاقتى توشتى، پۇل چىقىرىش ۋاقتى سەھەر سائەت توققۇزدا باشلىنىدۇ.",
      "cash_out_notification_zh":"提现时间过了，提现时间明天早上9点开始。",
      "shipper_app_jump_wechat_mini_original_id":"gh_4c15504e80b3",
      "shipper_app_jump_wechat_mini_page":"pages/miniPay/index",
      "shipper_arrive_at_shop_distance":500,
      "chat_msg_cache_time":1209600,
      "push" : {
            "shipper" : {
                "app_key": "f8a4b77833b7b91464ae72dd",
                "app_secret": "ff42b61c06846088cfb199ae"
            },
            "merchant": {
                "app_key": "2321f3c6825941b6d4d377db",
                "app_secret": "f4663748bfa0002823eaeda0"
            },
            "user": {
                "app_key": "3326fe4b331a003902154e24",
                "app_secret": "207db40bdc88977e56f89c98"
            }
        },
      "check_for_attendance":0,
      "chat_socket_server":"http://**********:6001/send-message",
      "shipper_v1_stop_area":[1],
      "shipper_no_check_arrive_shops":[2121,9251],
      "min_delivery_price": 1400,
      "shipper_introduce_rule_url":"https://cer.mulazim.com/invite?lang=",
      "shipper_introduce_ad_image_ug_url":"https://acdn.mulazim.com/wechat_mini/img/shipperIntroduce/shipper_introduce_ad_image_ug_url.png",
      "shipper_introduce_ad_image_zh_url":"https://acdn.mulazim.com/wechat_mini/img/shipperIntroduce/shipper_introduce_ad_image_zh_url.png",
      "show_shipper_introduce_ad":1,
      "advert_material_qrcode_url":"https://q.mulazim.com/mini",
      "advert_material_take_count_max":2000,
      "merchant_voice_url":"http://**********:6001",
      "merchant_push_min_version":242,
      "shipper_push_min_version":319,
      "swoole_url":"http://**********:1215",
      "amap_key":"b88dc50e6a32ceab15215e470eec9569", 
	  "amap_url":"http://restapi.amap.com",
      "enable_rush_hour_protection":1,
      "shipper_voice_url":"http://**********:6001",
      "swoole_out_url":"https://smart.mulazim.com",
      "seckill_and_pref_stop_area": [],
      "amap_web_key":"c4a64ece6f6e026bcdc374a276d203b4",
      "amap_web_sign":"b5a9f6953715706f0667c5f0de703d26",
      "telecom_coupon_access_key":"mulazimifexXhDiPeZfNBZNwnAHkbqHa",
      "telecom_coupon_access_iv":"1234567890123456",
      "insurance":{
          "name_ug":"中国平安",
          "name_zh":"中国平安",
          "amount":300
      },
      "insurance_stop_time": "18:42:00",
      "aes_key":"$CHAT_AES_KEY",
      "food": {
        "percent": {
          "mp_dealer_min": 3.6,
          "mp_dealer_max": 50
        },
        "self_percent": {
          "mp_dealer_min": 7.2,
          "mp_dealer_max": 50
        }
      },
      "api_smart_url":"http://smart-go.golang.ec-api.internal.mulazim.com:8086",
      "shipper_app_jump_env":"release",
      "store_multi_discount_count":3,
      "food_percent_mp_dealer_min":7.2,
      "food_percent_mp_dealer_max":50,
      "food_self_percent_mp_dealer_min":7.2,
      "food_self_percent_mp_dealer_max":50,
      "click_house_dsn":"$CLICK_HOUSE_DNS"

    }
    
  }
}

EOF


