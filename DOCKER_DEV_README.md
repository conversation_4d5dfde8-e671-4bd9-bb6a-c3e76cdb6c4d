# Docker 开发环境使用指南

## 快速开始

### 1. 启动开发环境
```bash
./dev.sh start
```

这将启动以下服务：
- **应用服务**: 你的Go应用，支持热重载
- **MySQL**: 数据库服务
- **Redis**: 缓存服务

### 2. 访问应用
- 应用地址: http://localhost:8080
- MySQL: localhost:3306 (用户名: root, 密码: password)
- Redis: localhost:6379

## 开发特性

### 🔥 热重载
当你修改Go代码时，应用会自动重新编译和重启，无需手动操作。

### 📁 代码挂载
整个项目目录被挂载到Docker容器中，你在本地的任何修改都会立即反映到容器内。

### 🚀 快速构建
Go模块缓存和构建缓存被持久化，提高后续构建速度。

## 常用命令

```bash
# 启动开发环境
./dev.sh start

# 查看所有服务日志
./dev.sh logs

# 查看特定服务日志（如应用服务）
./dev.sh logs app

# 停止开发环境
./dev.sh stop

# 重启开发环境
./dev.sh restart

# 重新构建镜像
./dev.sh build

# 进入应用容器shell
./dev.sh shell

# 连接到MySQL数据库
./dev.sh mysql

# 连接到Redis
./dev.sh redis

# 清理所有Docker资源
./dev.sh clean
```

## 配置说明

### 环境变量
在 `docker-compose.yml` 中可以修改以下环境变量：
- `MYSQL_DSN`: MySQL连接字符串
- `REDIS_IP`: Redis服务器地址
- `REDIS_PASS`: Redis密码

### 端口映射
- 应用端口: 8080 (可在docker-compose.yml中修改)
- MySQL端口: 3306
- Redis端口: 6379

### 热重载配置
热重载配置在 `.air.toml` 文件中，你可以根据需要调整：
- 监听的文件扩展名
- 排除的目录和文件
- 构建命令和参数

## 故障排除

### 1. 端口冲突
如果遇到端口冲突，请修改 `docker-compose.yml` 中的端口映射。

### 2. 权限问题
确保 `dev.sh` 脚本有执行权限：
```bash
chmod +x dev.sh
```

### 3. 数据库连接问题
检查 `config.json` 中的数据库配置是否正确。

### 4. 查看详细日志
```bash
./dev.sh logs app
```

## 生产环境部署

开发完成后，可以使用原有的 `Dockerfile` 进行生产环境部署：
```bash
docker build -t mulazim-api .
```

## 注意事项

1. 首次启动可能需要较长时间下载依赖
2. 确保本地没有其他服务占用相同端口
3. 修改代码后，观察日志确认应用是否成功重启
4. 数据库数据会持久化保存在Docker volume中
