FROM golang:1.20-alpine3.17

WORKDIR /app

ENV GO111MODULE=on
ENV GOPROXY=https://goproxy.cn
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
RUN apk upgrade --update-cache --available && \
    apk add gcc  openssl1.1-compat openssl1.1-compat-dev openssl1.1-compat-libs-static build-base && \
    rm -rf /var/cache/apk/*

COPY go.mod /app/
RUN go mod tidy && go mod vendor

COPY docker-entrypoint.sh /usr/local/bin/

ENTRYPOINT ["docker-entrypoint.sh"]

# RUN go mod tidy && go mod vendor
CMD [ "go", "run", "main.go", "--configPath=/app/"]