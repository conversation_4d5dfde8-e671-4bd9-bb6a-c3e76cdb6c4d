FROM golang:1.20-alpine3.17

WORKDIR /app

ENV GO111MODULE=on
ENV GOPROXY=https://goproxy.cn

# 更新包管理器镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories

# 安装必要的包和air（用于热重载）
RUN apk upgrade --update-cache --available && \
    apk add gcc openssl1.1-compat openssl1.1-compat-dev openssl1.1-compat-libs-static build-base git && \
    rm -rf /var/cache/apk/*

# 安装air用于热重载
RUN go install github.com/cosmtrek/air@latest

# 复制go.mod和go.sum（如果存在）
COPY go.mod go.sum* ./

# 下载依赖
RUN go mod download

# 复制entrypoint脚本
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# 暴露端口（根据你的应用调整）
EXPOSE 8080

ENTRYPOINT ["docker-entrypoint.sh"]

# 默认使用air进行热重载
CMD ["air"]
