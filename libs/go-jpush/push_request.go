package jpush

import (
	"encoding/json"
)

type Platform string

const (
	PlatformAndroid  Platform = "android"
	PlatformIOS      Platform = "ios"
	PlatformWinPhone Platform = "winphone"
)

type PushAudience struct {
	Tag            []string `json:"tag,omitempty"`
	TagAnd         []string `json:"tag_and,omitempty"`
	TagNot         []string `json:"tag_not,omitempty"`
	Alias          []string `json:"alias,omitempty"`
	RegistrationId []string `json:"registration_id,omitempty"`
	Segment        []string `json:"segment,omitempty"`
	ABTest         []string `json:"abtest,omitempty"`
}

type PushNotification struct {
	Alert    string                `json:"alert,omitempty"`
	Android  *NotificationAndroid  `json:"android,omitempty"`
	IOS      *NotificationIOS      `json:"ios,omitempty"`
	WinPhone *NotificationWinPhone `json:"winphone,omitempty"`
}

type NotificationAndroid struct {
	Alert      string                 `json:"alert"`
	Title      string                 `json:"title,omitempty"`
	BuilderId  int                    `json:"builder_id,int"`
	Priority   int                    `json:"priority"`
	Category   string                 `json:"category"`
	Style      int                    `json:"style,int,omitempty"`
	AlertType  int                    `json:"alert_type,int"`
	BigText    string                 `json:"big_text,omitempty"`
	Inbox      map[string]interface{} `json:"inbox,omitempty"`
	Intent     map[string]interface{} `json:"intent,omitempty"`
	BigPicPath string                 `json:"big_pic_path,omitempty"`
	Sound      string                 `json:"sound"`
	Extras     map[string]interface{} `json:"extras,omitempty"`
	ChannelID  string                 `json:"channel_id,omitempty"`
}

type NotificationIOS struct {
	Alert            interface{}            `json:"alert"`
	Sound            string                 `json:"sound,omitempty"`
	Badge            int                    `json:"badge,int,omitempty"`
	ContentAvailable bool                   `json:"content-available,omitempty"`
	MutableContent   bool                   `json:"mutable-content,omitempty"`
	Category         string                 `json:"category,omitempty"`
	Extras           map[string]interface{} `json:"extras,omitempty"`
}

type NotificationWinPhone struct {
	Alert    string                 `json:"alert"`
	Title    string                 `json:"title,omitempty"`
	OpenPage string                 `json:"_open_page,omitempty"`
	Extras   map[string]interface{} `json:"extras,omitempty"`
}

type PushMessage struct {
	MsgContent  string                 `json:"msg_content"`
	Title       string                 `json:"title,omitempty"`
	ContentType string                 `json:"content_type,omitempty"`
	Extras      map[string]interface{} `json:"extras,omitempty"`
}

type SmsMessage struct {
	Content   string `json:"content"`
	DelayTime int    `json:"delay_time,int,omitempty"`
}

type PushOptions struct {
	SendNo          int    `json:"sendno,int,omitempty"`
	TimeToLive      int    `json:"time_to_live,int,omitempty"`
	OverrideMsgId   int64  `json:"override_msg_id,int64,omitempty"`
	ApnsProduction  bool   `json:"apns_production"`
	ApnsCollapseId  string `json:"apns_collapse_id,omitempty"`
	BigPushDuration int    `json:"big_push_duration,int,omitempty"`
	Classification  int    `json:"classification,int"`
	//third_party_channel
	ThirdPartyChannel map[string]interface{} `json:"third_party_channel,omitempty"`
}

// XiaomiOptions 小米推送
type XiaomiOptions struct {
	ChannelId       string `json:"channel_id"`
	Distribution    string `json:"distribution,omitempty"`
	DistributionFcm string `json:"distribution_fcm,omitempty"`
	SkipQuota       bool   `json:"skip_quota"`
}

// OppoOptions oppo推送
type OppoOptions struct {
	ChannelId       string `json:"channel_id"`
	Distribution    string `json:"distribution,omitempty"`
	DistributionFcm string `json:"distribution_fcm,omitempty"`
	SkipQuota       bool   `json:"skip_quota"`
}

// VivoOptions vivo推送
type VivoOptions struct {
	CallbackId      string `json:"callback_id"`
	Category        string `json:"category"`
	Distribution    string `json:"distribution,omitempty"`
	DistributionFcm string `json:"distribution_fcm,omitempty"`
	SkipQuota       bool   `json:"skip_quota"`
}

// HuaweiOptions 华为推送
type HuaweiOptions struct {
	Category        string `json:"category"`
	Importance        string `json:"importance"`
	Sound        string `json:"sound"`
	DefaultSound        bool `json:"default_sound"`
	Distribution    string `json:"distribution,omitempty"`
	DistributionFcm string `json:"distribution_fcm,omitempty"`
	ChannelId       string `json:"channel_id,omitempty"`
	SkipQuota       bool   `json:"skip_quota,omitempty"`
}
type PushRequest struct {
	Cid          string            `json:"cid,omitempty"`
	Platform     []Platform        `json:"platform"`
	Audience     *PushAudience     `json:"audience,omitempty"`
	Notification *PushNotification `json:"notification,omitempty"`
	Message      *PushMessage      `json:"message,omitempty"`
	SmsMessage   *SmsMessage       `json:"sms_message,omitempty"`
	Options      *PushOptions      `json:"options,omitempty"`
	DataMsgType   int            `json:"data_msgtype,omitempty"`
	PushType   int            `json:"push_type,omitempty"`
	SendSource   int            `json:"send_source,omitempty"`
}

type Response struct {
	data []byte
}

func (res *Response) Array() ([]interface{}, error) {
	list := make([]interface{}, 0)
	err := json.Unmarshal(res.data, &list)
	return list, err
}

func (res *Response) Map() (map[string]interface{}, error) {
	result := make(map[string]interface{})
	err := json.Unmarshal(res.data, &result)
	return result, err
}

func (res *Response) Bytes() []byte {
	return res.data
}
