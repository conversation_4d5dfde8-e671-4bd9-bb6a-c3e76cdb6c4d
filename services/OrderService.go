package services

import (
	"errors"
	"fmt"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/models"
	"mulazim-api/tools"
)

type OrderService struct {
}

// CountComplatedOrder 统计已完成订单
func (s *OrderService) CountComplatedOrder(cityId, areaId, restaurantId int, startDate string, endDate string) (int64, error) {
	var todayOrderCount int64 = 0
	var orderCount int64 = 0
	result := map[string]interface{}{}
	var err error
	// 查询当天不需要统计归档订单表
	if startDate != carbon.Now().Format("Y-m-d") {
		query := tools.GetDB().Model(models.RestaurantSalesDailyData{}).
			Select("sum(order_count) as order_count").
			//Where("state in ?", []int{models.ORDER_STATE_DELIVERY_COMPLETED, models.ORDER_STATE_DELIVERY_FAIL}).
			Where("date >= ?", startDate).
			Where("date <= ?", endDate)
		if restaurantId > 0 {
			query.Where("restaurant_id = ?", restaurantId)
		} else if areaId > 0 {
			query.Where("area_id = ?", areaId)
		} else if cityId > 0 {
			query.Where("city_id = ?", cityId)
		}
		err = query.First(&result).
			Error
		if err != nil {
			msg := fmt.Sprintf("统计历史订单已完成订单失败： 开始日期: %s, 结束日期: %s 错误：%v", startDate, endDate, err)
			tools.Logger.Error(msg)
		} else {
			orderCount += tools.ToInt64(result["order_count"])
		}
	}
	query := tools.GetDB().Model(models.OrderToday{}).
		Where("state = ?", models.ORDER_STATE_DELIVERY_COMPLETED).
		Where("created_at >= ?", startDate+" 00:00:00").
		Where("created_at <= ?", endDate+" 23:59:59")
	if restaurantId > 0 {
		query.Where("store_id = ?", restaurantId)
	} else if areaId > 0 {
		query.Where("area_id = ?", areaId)
	} else if cityId > 0 {
		query.Where("city_id = ?", cityId)
	}
	err = query.Count(&todayOrderCount).Error
	if err != nil {
		msg := fmt.Sprintf("统计当天订单已完成订单失败： 开始日期: %s, 结束日期: %s 错误：%v", startDate, endDate, err)
		tools.Logger.Error(msg)
		return todayOrderCount, errors.New("count_order_failed")
	}
	return todayOrderCount + orderCount, nil
}
