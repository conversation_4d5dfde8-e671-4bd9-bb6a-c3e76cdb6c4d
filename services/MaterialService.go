package services

import (
	"errors"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/models/shipment"
	"mulazim-api/requests/cms/advert/InviteUserStatisticRequest"
	"mulazim-api/requests/cms/advert/materialRequest"
	"mulazim-api/requests/cms/advert/materialRequest/takeRequest"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"time"

	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type MaterialService struct {
	BaseService
	langUtil *lang.LangUtil
	language string
}

// NewMaterialService
//
//	@Description: 宣传材料
//	@param langUtil
//	@param language
//	@return *MaterialService
func NewMaterialService(langUtil lang.LangUtil, language string) *MaterialService {

	materialService := MaterialService{
		langUtil: &langUtil,
		language: language,
	}
	return &materialService
}

// 宣传资料 领取
func (m MaterialService) MaterialTake(admin models.Admin, startCode string, endCode string) (map[string]interface{}, error) {

	if admin.ShipperIncomeTemplateID == 0 {
		return nil, fmt.Errorf("shipper_income_template_not_set")
	}
	//限流
	key := "material_take_" + tools.ToString(admin.ID)
	sec := 3 //请求间隔 秒
	ret := tools.RedisLock(key, sec)
	if !ret {
		return nil, fmt.Errorf(fmt.Sprintf(m.langUtil.T("retry_after_second"), sec))
	}

	if startCode == "" && endCode == "" {
		return nil, errors.New("material_start_code_is_empty")
	}
	result := make(map[string]interface{})
	db := tools.GetDB()

	var code models.AdvertMaterialCode
	db.Model(&models.AdvertMaterialCode{}).Preload("AdvertMaterial.AdvertMaterialCategory").Where("code = ?", startCode).Find(&code)
	if code.ID == 0 {
		//不存在
		return nil, errors.New("advert_material_not_found")
	}
	if code.State != 1 {
		//已被领取
		return nil, errors.New("material_is_taken")
	}

	result["start_code"] = startCode
	result["end_code"] = endCode
	result["image"] = tools.AddCdn(code.AdvertMaterial.Cover)
	if m.langUtil.Lang == "ug" {
		result["name"] = code.AdvertMaterial.NameUg
		result["category"] = code.AdvertMaterial.AdvertMaterialCategory.NameUg
	} else {
		result["name"] = code.AdvertMaterial.NameZh
		result["category"] = code.AdvertMaterial.AdvertMaterialCategory.NameZh
	}
	result["count"] = 1 //数量

	if startCode != "" && endCode != "" && startCode != endCode { // 区间扫描

		var endCodeItem models.AdvertMaterialCode
		db.Model(&models.AdvertMaterialCode{}).Preload("AdvertMaterial.AdvertMaterialCategory").Where("code = ?", endCode).Find(&endCodeItem)
		if endCodeItem.ID == 0 {
			return nil, errors.New("advert_code_end_invalid")
		}

		var codes []models.AdvertMaterialCode
		db.Model(&models.AdvertMaterialCode{}).Preload("AdvertMaterial.AdvertMaterialCategory").Where("id >= ? and id <= ?", code.ID, endCodeItem.ID).Find(&codes)

		if len(codes) == 0 {
			return nil, errors.New("advert_code_start_end_invalid")
		}
		if len(codes) >= configs.MyApp.AdvertMaterialTakeCountMax {
			return nil,fmt.Errorf(fmt.Sprintf(m.langUtil.T("advert_material_over_the_limit"),configs.MyApp.AdvertMaterialTakeCountMax))
		}
		firstCategoryId := 0
		if len(codes) >= 2 {
			firstCategoryId = codes[0].AdvertMaterial.AdvertMaterialCategoryId
		}

		takenByOthers := false
		takenCode := ""
		for _, v := range codes {
			secondCategoryId := v.AdvertMaterial.AdvertMaterialCategoryId
			if firstCategoryId != secondCategoryId {
				//存在两个分类的
				return nil, errors.New("advert_material_category_is_different")
			}
			if v.State != 1 { //其中一个被被人领取了
				takenByOthers = true
				takenCode = v.Code
				break
			}
		}
		if takenByOthers {
			//被别人领取的号码返回个客户端
			return nil, fmt.Errorf(fmt.Sprintf(m.langUtil.T("advert_code_taken_by_others"), takenCode))
		}
		ct := len(codes)

		var takeInfo models.AdvertMaterialTake
		db.Model(&models.AdvertMaterialTake{}).Where("shipper_id = ? and start_code = ? and end_code = ?", admin.ID, startCode, endCode).Find(&takeInfo)
		if takeInfo.ID > 0 {
			//已经领取过
			return nil, fmt.Errorf(fmt.Sprintf(m.langUtil.T("advert_code_taken_by_others"), startCode))
		}
		takeInfo.CityId = admin.AdminCityID
		takeInfo.AreaId = admin.AdminAreaID
		takeInfo.AdvertMaterialId = code.AdvertMaterialId
		takeInfo.AdvertMaterialCategoryId = code.AdvertMaterial.AdvertMaterialCategoryId
		takeInfo.AdvertMaterialPrintBatchId = code.AdvertMaterialPrintBatchId
		takeInfo.ShipperId = admin.ID
		takeInfo.TakedAt = carbon.Now(configs.AsiaShanghai).Carbon2Time()
		takeInfo.TakedCount = ct
		takeInfo.StartCode = startCode
		takeInfo.EndCode = endCode
		takeInfo.CreatedAt = carbon.Now(configs.AsiaShanghai).Carbon2Time()
		takeInfo.UpdatedAt = carbon.Now(configs.AsiaShanghai).Carbon2Time()
		er := db.Create(&takeInfo).Error
		if er != nil {
			tools.Logger.Error("领取宣传材料失败", er)
			return nil, errors.New("error_happend")
		}

		result["count"] = ct
		db.Model(&models.AdvertMaterialCode{}).Where("id >= ? and id <= ?", code.ID, endCodeItem.ID).Updates(&map[string]interface{}{
			"shipper_id":              admin.ID,
			"state":                   2,
			"advert_material_take_id": takeInfo.ID,
			"updated_at":              carbon.Now(configs.AsiaShanghai),
			"taked_at":                carbon.Now(configs.AsiaShanghai),
		})
	} else {
		var takeInfo models.AdvertMaterialTake
		db.Model(&models.AdvertMaterialTake{}).Where("shipper_id = ? and start_code = ? ", admin.ID, startCode).Find(&takeInfo)
		if takeInfo.ID > 0 {
			//已经领取过
			return nil, fmt.Errorf(fmt.Sprintf(m.langUtil.T("advert_code_taken_by_others"), startCode))
		}
		takeInfo.CityId = admin.AdminCityID
		takeInfo.AreaId = admin.AdminAreaID
		takeInfo.AdvertMaterialId = code.AdvertMaterialId
		takeInfo.AdvertMaterialCategoryId = code.AdvertMaterial.AdvertMaterialCategoryId
		takeInfo.AdvertMaterialPrintBatchId = code.AdvertMaterialPrintBatchId
		takeInfo.ShipperId = admin.ID
		takeInfo.TakedAt = carbon.Now(configs.AsiaShanghai).Carbon2Time()
		takeInfo.TakedCount = 1
		takeInfo.StartCode = startCode
		takeInfo.EndCode = startCode
		takeInfo.CreatedAt = carbon.Now(configs.AsiaShanghai).Carbon2Time()
		takeInfo.UpdatedAt = carbon.Now(configs.AsiaShanghai).Carbon2Time()
		er := db.Create(&takeInfo).Error
		if er != nil {
			tools.Logger.Error("领取宣传材料失败", er)
			return nil, errors.New("error_happend")
		}

		db.Model(&models.AdvertMaterialCode{}).Where("code = ? ", startCode).Updates(&map[string]interface{}{
			"shipper_id":              admin.ID,
			"state":                   2,
			"advert_material_take_id": takeInfo.ID,
			"updated_at":              carbon.Now(configs.AsiaShanghai),
			"taked_at":                carbon.Now(configs.AsiaShanghai),
		})
	}

	return result, nil
}

// 宣传资料 扫描后获取 信息
func (m MaterialService) MaterialInfo(startCode string, endCode string) (map[string]interface{}, error) {

	if startCode == "" && endCode == "" {
		return nil, errors.New("material_start_code_is_empty")
	}
	result := make(map[string]interface{})
	db := tools.GetDB()

	var code models.AdvertMaterialCode
	db.Model(&models.AdvertMaterialCode{}).Preload("AdvertMaterial.AdvertMaterialCategory").Where("code = ?", startCode).Find(&code)
	if code.ID == 0 {
		//不存在
		return nil, errors.New("advert_material_not_found")
	}
	if code.State != 1 {
		//已被领取
		return nil, fmt.Errorf(fmt.Sprintf(m.langUtil.T("advert_code_taken_by_others"), startCode))
	}
	result["start_code"] = startCode
	result["end_code"] = endCode
	result["image"] = tools.AddCdn(code.AdvertMaterial.Cover)
	if m.langUtil.Lang == "ug" {
		result["name"] = code.AdvertMaterial.NameUg
		result["category"] = code.AdvertMaterial.AdvertMaterialCategory.NameUg
	} else {
		result["name"] = code.AdvertMaterial.NameZh
		result["category"] = code.AdvertMaterial.AdvertMaterialCategory.NameZh
	}
	result["count"] = 1 //数量

	if startCode != "" && endCode != "" && startCode != endCode { // 区间扫描

		var endCodeItem models.AdvertMaterialCode
		db.Model(&models.AdvertMaterialCode{}).Preload("AdvertMaterial.AdvertMaterialCategory").Where("code = ?", endCode).Find(&endCodeItem)
		if endCodeItem.ID == 0 {
			return nil, errors.New("advert_code_end_invalid")
		}

		var codes []models.AdvertMaterialCode
		db.Model(&models.AdvertMaterialCode{}).Preload("AdvertMaterial.AdvertMaterialCategory").Where("id >= ? and id <= ?", code.ID, endCodeItem.ID).Find(&codes)

		if len(codes) == 0 {
			return nil, errors.New("advert_code_start_end_invalid")
		}
		firstCategoryId := 0
		if len(codes) >= 2 {
			firstCategoryId = codes[0].AdvertMaterial.AdvertMaterialCategoryId
		}
		if len(codes) >= configs.MyApp.AdvertMaterialTakeCountMax {
			return nil,fmt.Errorf(fmt.Sprintf(m.langUtil.T("advert_material_over_the_limit"),configs.MyApp.AdvertMaterialTakeCountMax))
		}
		takenByOthers := false
		takenCode := ""
		for _, v := range codes {
			secondCategoryId := v.AdvertMaterial.AdvertMaterialCategoryId
			if firstCategoryId != secondCategoryId {
				//存在两个分类的
				return nil, errors.New("advert_material_category_is_different")
			}
			if v.State != 1 { //其中一个被被人领取了
				takenByOthers = true
				takenCode = v.Code
				break
			}
		}
		if takenByOthers {
			//被别人领取的号码返回个客户端
			return nil, fmt.Errorf(fmt.Sprintf(m.langUtil.T("advert_code_taken_by_others"), takenCode))
		}
		ct := len(codes)
		result["count"] = ct
	}
	return result, nil
}

// GetCustomerStatistic
//
//	@Description: 获取客户统计
//	@receiver m
//	@param admin
//	@param month
//	@return interface{}
//	@return interface{}
func (m MaterialService) GetCustomerStatistic(admin models.Admin, month string, categoryId int, page int, limit int, sortColumn string, sortType string) ([]models.AdvertMaterialShipperUserMonthStatistic, error) {
	db := tools.Db
	var data []models.AdvertMaterialShipperUserMonthStatistic
	query := db.Model(&models.AdvertMaterialShipperUserMonthStatistic{}).Where("month = ?", month).
		Where("shipper_id = ?", admin.ID)
	if categoryId > 0 {
		query = query.Where("advert_material_category_id = ?", categoryId)
	}
	if sortColumn != "" {
		if sortColumn == "total_order_tips_fee" {
			query = query.Order("(total_order_tips_fee+invite_user_fee) " + sortType)
		} else {
			query = query.Order(sortColumn + " " + sortType)
		}
	}
	query.Scopes(scopes.Page(page, limit)).Preload("User").Preload("AdvertMaterialCategory").Preload("AdvertMaterialShipperUser").Find(&data)
	return data, nil
}

// GetCustomerStatisticHeader
//
//	@Description: 获取客户统计头部
//	@receiver m
//	@param admin
//	@param month
//	@param categoryId
//	@param page
//	@param limit
//	@return int64
//	@return int64
func (m MaterialService) GetCustomerStatisticHeader(admin models.Admin, month string, categoryId int, page int, limit int) (int64, int64) {
	//SELECT sum(order_count) AS total_order_count,count(1) AS total_customer_count FROM `t_advert_material_shipper_user_month_statistic` WHERE MONTH='2024-02-01' AND shipper_id=6202
	db := tools.Db

	var totalOrderCount int64
	var totalCustomerCount int64
	var mapData map[string]interface{}
	query := db.Model(&models.AdvertMaterialShipperUserMonthStatistic{}).
		Select("sum(order_count) AS total_order_count,count(1) AS total_customer_count").
		Where("month = ? ", month).
		Where("shipper_id = ?", admin.ID)
	if categoryId > 0 {
		query = query.Where("advert_material_category_id = ?", categoryId)
	}
	query.Scan(&mapData)
	totalOrderCount = tools.ToInt64(mapData["total_order_count"])
	totalCustomerCount = tools.ToInt64(mapData["total_customer_count"])
	return totalCustomerCount, totalOrderCount
}

// GetCategoryName
//
//	@Description: 根据ID获取name
//	@receiver m
//	@param c
//	@param categoryId
//	@return string
func (m MaterialService) GetCategoryName(categoryId int) string {
	db := tools.Db
	type Category struct {
		NameZh string `json:"name_zh" gorm:"column:name_zh"`
		NameUg string `json:"name_ug" gorm:"column:name_ug"`
	}
	var category Category

	db.Table("t_advert_material_category").Where("id = ?", categoryId).First(&category)
	return tools.If(m.language == "zh", category.NameZh, category.NameUg)
}

func (m MaterialService) GetMaterialCategory(page string) (interface{}, interface{}) {
	db := tools.Db
	type AdvertMaterialCategory struct {
		ID   int    `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
		Name string `json:"name"` // 宣传材料分类维吾尔语名称
	}
	var data []AdvertMaterialCategory
	category := db.Table("t_advert_material_category")
	if page == "material_center" {
		category = category.Where("id > 1")
	}
	category.Select("id,name_" + m.language + " as name").Find(&data)
	return data, nil
}

// 宣传资料 列表
func (m MaterialService) MaterialList(admin models.Admin, month string, categoryId int) (map[string]interface{}, error) {

	result := make(map[string]interface{})
	list := make([]map[string]interface{}, 0)
	head := make(map[string]interface{})
	db := tools.Db

	var customerCount int64 //总客户数量
	var materialCount int64 //总素材数量
	monthStart, monthEnd := tools.GetMonthStartAndEndWithParam(month, "Y-m")
	takeModel := db.Model(&models.AdvertMaterialTake{}).Where("shipper_id = ? and created_at between ? and ?", admin.ID, monthStart, monthEnd)
	if categoryId > 0 {
		takeModel = takeModel.Where("advert_material_category_id = ?", categoryId)
	}
	takeModel.Pluck("IF(ISNULL(sum(taked_count)),0,sum(taked_count))  as taked_count", &materialCount)
	var takeIds []int
	takeModel.Select("id").Pluck("id", &takeIds)
	db.Model(&models.AdvertMaterialShipperUser{}).Where("advert_material_take_id in ?", takeIds).Count(&customerCount)

	var records []models.AdvertMaterialTake
	takeModel.Select("t_advert_material_take.*").
		Preload("AdvertMaterial").
		Preload("AdvertMaterialShipperUserCount", func(d *gorm.DB) *gorm.DB {
			return d.Where("state = ?", 1).Group("advert_material_take_id").Select("id,count(id) as customer_count,advert_material_take_id")
		}).
		Find(&records)

	for _, v := range records {
		item := make(map[string]interface{})
		item["id"] = v.ID
		item["count"] = v.TakedCount
		item["taked_at"] = v.TakedAt.Format("2006-01-02")
		item["category_id"] = v.AdvertMaterialCategoryId
		item["category"] = m.GetCategoryName(v.AdvertMaterialCategoryId)
		item["image"] = tools.AddCdn(v.AdvertMaterial.Cover)
		if m.language == "ug" {
			item["name"] = v.AdvertMaterial.NameUg
		} else {
			item["name"] = v.AdvertMaterial.NameZh
		}

		item["unit"] = m.langUtil.T("advert_material_unit")
		item["customer_count"] = v.AdvertMaterialShipperUserCount.CustomerCount
		list = append(list, item)
	}
	head["customer_count"] = customerCount
	head["material_count"] = materialCount
	result["head"] = head
	result["list"] = list

	return result, nil
}

// 宣传资料 领取详情
func (m MaterialService) MaterialTakeDetail(TakeId int) (map[string]interface{}, error) {

	result := make(map[string]interface{})
	head := make(map[string]interface{})
	db := tools.Db

	var materialCount int64 //总素材数量
	takeModel := db.Model(&models.AdvertMaterialTake{}).Where(" id = ?", TakeId)
	takeModel.Pluck("taked_count", &materialCount)
	var record models.AdvertMaterialTake
	takeModel.Select("t_advert_material_take.*").
		Preload("AdvertMaterial").
		Preload("AdvertMaterialShipperUser.AdvertMaterialCode").
		Preload("AdvertMaterialShipperUser.User", func(d *gorm.DB) *gorm.DB {
			return d.Where("state = ?", 1)
		}).Find(&record)

	v := record
	item := make(map[string]interface{})
	item["count"] = v.TakedCount
	item["start_code"] = v.StartCode
	item["end_code"] = v.EndCode
	item["taked_at"] = v.TakedAt.Format("2006-01-02")
	item["category"] = m.GetCategoryName(v.AdvertMaterialCategoryId)
	item["image"] = tools.AddCdn(v.AdvertMaterial.Cover)
	if m.language == "ug" {
		item["name"] = v.AdvertMaterial.NameUg
	}else{
		item["name"] = v.AdvertMaterial.NameZh
	}
	

	customers := make([]map[string]interface{}, 0)
	for _, cu := range v.AdvertMaterialShipperUser {
		cc := make(map[string]interface{})
		userName := tools.FilterEmoji(cu.User.Name)
		if userName == "" {
			userName = "微信用户"
		}
		cc["name"] = userName
		cc["mobile"] = tools.MaskMobile(cu.User.Mobile)
		userAvatar := cu.User.Avatar
		if userAvatar == "" {
			userAvatar = tools.GetDefaultUserImage()
		}
		cc["image"] = userAvatar
		cc["created_at"] = cu.CreatedAt.Format("2006-01-02")
		cc["material_code"] = cu.AdvertMaterialCode.Code
		customers = append(customers, cc)
	}

	head["customer_count"] = len(v.AdvertMaterialShipperUser)
	head["material_count"] = materialCount
	result["head"] = head
	result["info"] = item
	result["list"] = customers
	return result, nil
}

// Create 创建
func (m MaterialService) Create(request materialRequest.CreateRequest) (models.AdvertMaterial, error) {
	material := models.AdvertMaterial{
		NameUg:                   request.NameUg,
		NameZh:                   request.NameZh,
		AdvertMaterialCategoryId: request.CategoryId,
		Cover:                    request.Cover,
		File:                     request.File,
		State:                    request.State,
	}
	rs := tools.GetDB().Create(&material)
	if rs.Error != nil {
		msg := fmt.Sprintf("创建宣传材料创建失败: %s", rs.Error.Error())
		tools.Logger.Warn(msg)
		return models.AdvertMaterial{}, errors.New("failed")
	}
	return material, nil
}

// 宣传中心
func (m MaterialService) MaterialCenter(admin models.Admin) (map[string]interface{}, error) {

	result := make(map[string]interface{})
	rule := make(map[string]interface{})
	head := make(map[string]interface{})
	db := tools.Db

	var customerCount int //今天的客户数量

	todayStart, todayEnd := tools.GetTodayStartAndEnd()
	shipperCustomerModel := db.Model(&models.AdvertMaterialShipperUser{}).
		Where("shipper_id = ? and created_at between ? and ? and state = ?", admin.ID, todayStart, todayEnd, 1)
	shipperCustomerModel.Pluck("count(id)", &customerCount)

	dailyStatModel := db.Model(&models.AdvertMaterialShipperDailyStatistic{}).
		Where("shipper_id = ?", admin.ID).
		Where("created_at between ? and ?", todayStart, todayEnd)
	// var todayIncome int64 //今天的收入
	// var orderCount int64  //总订单数量
	// var orderPrice int64  //总订单金额
	type DailyStat struct {
		OrderCount int64 `json:"order_count"`
		OrderPrice int64 `json:"order_price"`
		Income     int64 `json:"income"`
	}
	var dailyStatItem DailyStat
	dailyStatModel.
		Select("IF(ISNULL(sum(order_count)),0,sum(order_count)) as order_count,IF(ISNULL(sum(total_order_price)),0,sum(total_order_price)) as order_price,IF(ISNULL(sum(invite_user_fee+total_order_tips_fee)),0,sum(invite_user_fee+total_order_tips_fee)) as income").
		Scan(&dailyStatItem)

	head["customer_count"] = customerCount
	head["income"] = dailyStatItem.Income
	head["order_count"] = dailyStatItem.OrderCount
	head["order_price"] = dailyStatItem.OrderPrice

	result["head"] = head
	var template shipment.ShipperIncomeTemplate
	db.Model(&shipment.ShipperIncomeTemplate{}).Where("id = ?", admin.ShipperIncomeTemplateID).Select("id,invite_user_fee,invite_old_user_fee,order_percent,order_old_user_percent").Find(&template)
	rule["invite_user_fee"] = template.InviteUserFee
	rule["invite_old_user_fee"] = template.InviteOldUserFee
	rule["order_percent"] = tools.ToFloat64(template.OrderPercent) / tools.ToFloat64(100) //推广用户订单分成百分比，单位：万分之一
	rule["order_old_user_percent"] = tools.ToFloat64(template.OrderOldUserPercent) / tools.ToFloat64(100) //推广用户订单分成百分比，单位：万分之一
	if(m.language == "ug"){
		rule["invite_rule_data"] = []map[string]interface{}{
			{"icon":"https://acdn.mulazim.com/wechat_mini/img/invite/invite_user_fee.png","title":"يېڭى خېرىدار كۆپەيتىش مۇكاپاتى","description":"يېڭىدىن كۆپەيتكەن ھەر بىر خېرىدار ئۈچۈن يەتكۈزگۈچىگە بېرىلىدىغان مۇكاپات","value":"￥"+tools.ToString(tools.ToFloat64(template.InviteUserFee) / tools.ToFloat64(100) )},
			{"icon":"https://acdn.mulazim.com/wechat_mini/img/invite/invite_old_user_fee.png","title":"سۇپىدىكى كونا خېرىدارنى ئويغىتىش مۇكاپاتى","description":"يەتكۈزگۈچى سۇپىدىكى خېرىدارلارنى ئويغاتقاندا  بېرىلىدىغان مۇكاپات","value":"￥"+tools.ToString(tools.ToFloat64(template.InviteOldUserFee) / tools.ToFloat64(100) )},
			{"icon":"https://acdn.mulazim.com/wechat_mini/img/invite/order_percent.png","title":"يېڭى خېرىدار چۈشۈرگەن زاكاز ئۈچۈن  ئايرىلىدىغان پايدا","description":"يېڭى خېرىدار چۈشۈرگەن ھەر بىر زاكازدىن يەتكۈزگۈچىگە ئايرىلىدىغان پايدا مۇكاپات","value":tools.ToString(rule["order_percent"])+"%"},
			{"icon":"https://acdn.mulazim.com/wechat_mini/img/invite/order_old_user_percent.png","title":"ئويغىتىلغان كونا خېرىدار چۈشۈرگەن زاكازدىن ئايرىلىدىغان پايدا","description":"ئويغىتىلغان خېرىدار چۈشۈرگەن ھەر بىر زاكازدىن يەتكۈزگۈچىگە ئايرىلىدىغان پايدا","value":tools.ToString(rule["order_old_user_percent"])+"%"},
		}
	}else{
		rule["invite_rule_data"] = []map[string]interface{}{
			{"icon":"https://acdn.mulazim.com/wechat_mini/img/invite/invite_user_fee.png","title":"拓展新客户奖励","description":"每一个拓展的新客户奖励金额","value":"￥"+tools.ToString(tools.ToFloat64(template.InviteUserFee) / tools.ToFloat64(100) )},
			{"icon":"https://acdn.mulazim.com/wechat_mini/img/invite/invite_old_user_fee.png","title":"平台老客户激活奖励","description":"配送员激活平台老客户的奖励金额","value":"￥"+tools.ToString(tools.ToFloat64(template.InviteOldUserFee) / tools.ToFloat64(100) )},
			{"icon":"https://acdn.mulazim.com/wechat_mini/img/invite/order_percent.png","title":"新客户下单分润","description":"新客户下的每一个订单分润百分比","value":tools.ToString(rule["order_percent"])+"%"},
			{"icon":"https://acdn.mulazim.com/wechat_mini/img/invite/order_old_user_percent.png","title":"老客户下单分润","description":"被激活的老客户下的每一个订单分润百分比","value":tools.ToString(rule["order_old_user_percent"])+"%"},
		}
	}
	

	
	result["income_rule"] = rule
	result["rule_url"] = configs.MyApp.ShipperIntroduceRuleUrl+m.language

	needAlert :=0
	alertMsg :=""
	if template.ID == 0 {
		needAlert =1
		alertMsg = m.langUtil.T("shipper_income_template_not_set_can_not_use")
	}
	result["need_alert"] = needAlert
	result["alert_msg"] = alertMsg

	return result, nil
}

// Paginate 分页
func (m MaterialService) Paginate(keyword string, state int, categoryId int, page int, limit int) ([]models.AdvertMaterialWithTakeCount, int64, error) {
	var materials []models.AdvertMaterialWithTakeCount = make([]models.AdvertMaterialWithTakeCount, 0)
	var total int64 = 0
	query := tools.GetDB().Model(models.AdvertMaterial{}).Where("id > 1")
	if state > 0 {
		query = query.Where("state = ?", state)
	}
	if categoryId > 0 {
		query = query.Where("advert_material_category_id = ?", categoryId)
	}
	if keyword != "" {
		query = query.Where("name_zh like ? or name_ug like ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	query.Count(&total)
	rs := query.
		Select("t_advert_material.*,(select count(taked_count) from t_advert_material_take where t_advert_material_take.advert_material_id=t_advert_material.id) as taked_count").
		Preload("AdvertMaterialCategory").
		Offset((page - 1) * limit).
		Limit(limit).
		Order("id desc").
		Find(&materials)
	if rs.Error != nil {
		msg := fmt.Sprintf("查看宣传材料失败: errors: %s", rs.Error.Error())
		tools.Logger.Warn(msg)
		return materials, 0, errors.New("failed")
	}
	return materials, total, nil
}

// Find 查找
func (m MaterialService) Find(id int) (material models.AdvertMaterial, err error) {
	rs := tools.GetDB().First(&material, id)
	if rs.RowsAffected == -1 {
		msg := fmt.Sprintf("查看宣传材料失败: ID: %d, errors: %s", id, rs.Error.Error())
		tools.Logger.Warn(msg)
		return material, errors.New("failed")
	}
	return material, nil
}

// Update 更新
func (m MaterialService) Update(material models.AdvertMaterial, request materialRequest.CreateRequest) (models.AdvertMaterial, error) {
	rs := tools.GetDB().Model(&material).Updates(map[string]interface{}{
		"name_ug":                     request.NameUg,
		"name_zh":                     request.NameZh,
		"advert_material_category_id": request.CategoryId,
		"cover":                       request.Cover,
		"file":                        request.File,
		"state":                       request.State,
	})
	if rs.Error != nil {
		msg := fmt.Sprintf("更新宣传材料失败: ID: %d, request: %v, errors: %s", material.ID, request, rs.Error.Error())
		tools.Logger.Warn(msg)
		return material, errors.New("failed")
	}
	return material, nil
}

// UpdateState 更新状态
func (m MaterialService) UpdateState(material models.AdvertMaterial, state int) (models.AdvertMaterial, error) {
	material.State = state
	rs := tools.GetDB().Model(&models.AdvertMaterial{}).Where("id = ?", material.ID).Update("state", state)
	if rs.Error != nil {
		msg := fmt.Sprintf("更新宣传材料状态失败: ID: %d, State: %d, errors: %s", material.ID, state, rs.Error.Error())
		tools.Logger.Warn(msg)
		return material, errors.New("failed")
	}
	return material, nil
}

// Delete 删除
func (m MaterialService) Delete(material models.AdvertMaterial) error {
	var batchCount int64
	tools.GetDB().Model(&models.AdvertMaterialPrintBatch{}).Where("advert_material_id = ?", material.ID).Count(&batchCount)
	if batchCount > 0 {
		return errors.New("该宣传材料已经被打印，不能删除")

	}
	rs := tools.GetDB().Delete(&models.AdvertMaterial{}, material.ID)
	if rs.Error != nil {
		msg := fmt.Sprintf("删除宣传材料失败: ID: %d,  errors: %s", material.ID, rs.Error.Error())
		tools.Logger.Warn(msg)
		return errors.New("failed")
	}
	return nil

}

// 宣传材料总统计 头部
func (m MaterialService) GetMaterialStatisticHeader(admin models.Admin, month string) (int64, int64, int64, int64) {

	db := tools.Db

	var totalOrderCount int64
	var totalCustomerCount int64
	var mapData map[string]interface{}
	fields := "IF(ISNULL(sum(order_count)),0,sum(order_count))  AS total_order_count,"
	fields += "count(1) AS total_customer_count,"
	fields += "IF(ISNULL(sum(total_order_price)),0,sum(total_order_price))  as total_order_price,"
	fields += "IF(ISNULL(sum(invite_user_fee+total_order_tips_fee)),0,sum(invite_user_fee+total_order_tips_fee)) as total_order_tips_fee"
	query := db.Model(&models.AdvertMaterialShipperUserMonthStatistic{}).
		Select(fields).
		Where("month = ?", month).
		Where("shipper_id = ?", admin.ID)
	query.Scan(&mapData)
	totalOrderCount = tools.ToInt64(mapData["total_order_count"])
	totalCustomerCount = tools.ToInt64(mapData["total_customer_count"])
	totalOrderPrice := tools.ToInt64(mapData["total_order_price"])
	totalOrderTipsFee := tools.ToInt64(mapData["total_order_tips_fee"])
	return totalCustomerCount, totalOrderCount, totalOrderPrice, totalOrderTipsFee
}

// 宣传材料总统计
func (m MaterialService) GetMaterialStatistic(admin models.Admin, month string) ([]models.AdvertMaterialShipperDailyStatisticPage, []models.AdvertMaterialShipperDailyStatisticPage, error) {
	db := tools.Db
	var data []models.AdvertMaterialShipperDailyStatisticPage
	var dataDiagram []models.AdvertMaterialShipperDailyStatisticPage

	monthStart, monthEnd := tools.GetMonthStartAndEndWithParam(month, "Y-m-d")

	fields := "advert_material_category_id,"
	fields += "IF(ISNULL(sum(invite_user_count)),0,sum(invite_user_count)) as  invite_user_count,"
	fields += "IF(ISNULL(sum(order_count)),0,sum(order_count))  as  order_count,"
	fields += "IF(ISNULL(sum(total_order_price)),0,sum(total_order_price)) as total_order_price"
	query := db.Model(&models.AdvertMaterialShipperDailyStatistic{}).
		Preload("AdvertMaterialCategory").
		Where("shipper_id = ? and date between ? and ?", admin.ID, monthStart, monthEnd)
	query.Group("advert_material_category_id").
		Select(fields).
		Find(&data)

	for i := 0; i < len(data); i++ {
		if m.language == "ug" {
			data[i].AdvertMaterialCategory.NameZh = data[i].AdvertMaterialCategory.NameUg
		}
	}
	fields = "advert_material_category_id,date,"
	fields += "IF(ISNULL(sum(invite_user_fee+total_order_tips_fee)),0,sum(invite_user_fee+total_order_tips_fee)) as total_order_tips_fee"

	query.Group("date").
		Select(fields).
		Find(&dataDiagram)

	var dataDiagramList []models.AdvertMaterialShipperDailyStatisticPage

	days := tools.GetDaysOfMonth(monthStart)
	for _, date := range days {
		var dayData models.AdvertMaterialShipperDailyStatisticPage
		dayData.Date = date
		dayData.OrderCount = 0
		for _, dataDiagramItems := range dataDiagram {
			itemDate := carbon.Parse(dataDiagramItems.Date, configs.AsiaShanghai).Format("Y-m-d")
			if itemDate == date {
				dayData.OrderCount = dataDiagramItems.OrderCount
				dayData.TotalOrderTipsFee = dataDiagramItems.TotalOrderTipsFee
				break
			}
		}

		dataDiagramList = append(dataDiagramList, dayData)
	}

	return data, dataDiagramList, nil
}

// CmsTakeStatistic 宣传材料领取统计
func (m MaterialService) CmsTakeStatistic(request takeRequest.ListRequest) (
	shipperCount, totalTakedCount, newUserCount, totalOrderCount, totalOrderPrice, totalShipperIncome int,
	categories []map[string]interface{},
	err error) {
	db := tools.Db

	var mapData map[string]interface{}
	fields := "count(taked_count) as total_taked_count,"
	fields += "count(DISTINCT shipper_id) as shipper_count"
	query := db.Model(&models.AdvertMaterialTake{}).
		Select(fields).
		Where("taked_at between ? and ?", request.BeginDate+" 00:00:00", request.EndDate+" 23:59:59")
	if request.CategoryID > 0 {
		query.Where("advert_material_category_id = ?", request.CategoryID)
	}
	if request.CityID > 0 {
		query.Where("city_id = ?", request.CityID)
	}
	if request.AreaID > 0 {
		query.Where("area_id = ?", request.AreaID)
	}
	if request.ShipperID > 0 {
		query.Where("shipper_id = ?", request.ShipperID)
	}
	query.Scan(&mapData)
	totalTakedCount = tools.ToInt(mapData["total_taked_count"])
	shipperCount = tools.ToInt(mapData["shipper_count"])

	fields = "SUM(invite_user_count) as new_user_count,"
	fields += "SUM(order_count) as total_order_count,"
	fields += "SUM(total_order_price) as total_order_price,"
	fields += "SUM(invite_user_fee+total_order_tips_fee) as total_shipper_income"
	query = db.Model(&models.AdvertMaterialShipperDailyStatistic{}).
		Select(fields).
		Where("date between ? and ?", request.BeginDate, request.EndDate)
	if request.CategoryID > 0 {
		query.Where("advert_material_category_id = ?", request.CategoryID)
	}
	if request.CategoryID > 0 {
		query.Where("advert_material_category_id = ?", request.CategoryID)
	}
	if request.CityID > 0 {
		query.Where("city_id = ?", request.CityID)
	}
	if request.AreaID > 0 {
		query.Where("area_id = ?", request.AreaID)
	}
	if request.ShipperID > 0 {
		query.Where("shipper_id = ?", request.ShipperID)
	}
	query.Scan(&mapData)
	newUserCount = tools.ToInt(mapData["new_user_count"])
	totalOrderCount = tools.ToInt(mapData["total_order_count"])
	totalOrderPrice = tools.ToInt(mapData["total_order_price"])
	totalShipperIncome = tools.ToInt(mapData["total_shipper_income"])
	fields = "t_advert_material_take.advert_material_category_id as category_id,"
	fields += "count(t_advert_material_take.id) as record_count, "
	fields += "(select t_advert_material_category.name_" + m.language +
		" from t_advert_material_category where t_advert_material_take.advert_material_category_id=t_advert_material_category.id) as category_name"
	query = db.Model(&models.AdvertMaterialTake{}).
		Select(fields).
		Group("t_advert_material_take.advert_material_category_id").
		Where("taked_at between ? and ?", request.BeginDate+" 00:00:00", request.EndDate+" 23:59:59")
	if request.CityID > 0 {
		query.Where("city_id = ?", request.CityID)
	}
	if request.AreaID > 0 {
		query.Where("area_id = ?", request.AreaID)
	}
	if request.ShipperID > 0 {
		query.Where("shipper_id = ?", request.ShipperID)
	}
	query.Order("record_count desc").Scan(&categories)
	if len(categories) == 0 {
		categories = make([]map[string]interface{}, 0)
	}
	//for _, v := range cateogryData {
	//	recordCount := tools.ToInt(v["record_count"])
	//	if recordCount > 0 {
	//		shipperCount++
	//	}
	//}

	return
}

// CmsTakeList 宣传材料领取列表
func (s MaterialService) CmsTakeList(request takeRequest.ListRequest, page int, limit int) ([]models.AdvertMaterialTake, int64, error) {
	db := tools.Db
	var data []models.AdvertMaterialTake
	var total int64
	query := db.Model(&models.AdvertMaterialTake{}).
		Where("taked_at between ? and ?", request.BeginDate+" 00:00:00", request.EndDate+" 23:59:59")
	if request.CategoryID > 0 {
		query.Where("advert_material_category_id = ?", request.CategoryID)
	}
	if request.CityID > 0 {
		query.Where("city_id = ?", request.CityID)
	}
	if request.AreaID > 0 {
		query.Where("area_id = ?", request.AreaID)
	}
	if request.ShipperID > 0 {
		query.Where("shipper_id = ?", request.ShipperID)
	}
	query.Count(&total)
	query.Preload("AdvertMaterial").
		Preload("Category").
		Preload("Shipper").
		Order("id desc").
		Scopes(scopes.Page(page, limit)).Find(&data)
	return data, total, nil
}

func (s MaterialService) CmsTakeDetail(takeId int) (take models.AdvertMaterialTake, err error) {
	db := tools.Db

	query := db.Model(&models.AdvertMaterialTake{}).
		Where("id = ?", takeId)
	rs := query.Preload("AdvertMaterial").
		Preload("Category").
		Preload("Shipper").
		First(&take)
	if rs.Error != nil && rs.RowsAffected < 0 {
		return take, errors.New("failed")
	}
	return
}

func (s MaterialService) FindTakeById(id int) (take models.AdvertMaterialTake, err error) {
	db := tools.Db
	rs := db.First(&take, id)
	if rs.RowsAffected < 0 {
		return take, errors.New("failed")
	}
	return take, nil
}

// CmsTakeNewUserList 宣传材料领取新用户列表
func (s MaterialService) CmsTakeNewUserList(takeId int, page int, limit int) ([]models.AdvertMaterialShipperUser, int64, error) {
	db := tools.Db
	var data []models.AdvertMaterialShipperUser
	var total int64
	query := db.Model(&models.AdvertMaterialShipperUser{}).
		Where("advert_material_take_id = ?", takeId).
		Where("state = ?", 1)
	query.Count(&total)
	query.Preload("User").
		Preload("AdvertMaterialCode").
		Scopes(scopes.Page(page, limit)).Find(&data)
	return data, total, nil
}

// CmsInviteStatisticList 宣传材料邀请统计
func (s MaterialService) CmsInviteStatisticList(request InviteUserStatisticRequest.ShipperStatisticRequest) ([]models.AdvertMaterialShipperDailyStatisticForCmsInviteUserStatistic, error) {
	db := tools.Db
	var data []models.AdvertMaterialShipperDailyStatisticForCmsInviteUserStatistic = make([]models.AdvertMaterialShipperDailyStatisticForCmsInviteUserStatistic, 0)
	fields := "t_advert_material_shipper_daily_statistic.shipper_id,"
	fields += "t_admin.real_name as shipper_name,"
	fields += "SUM(t_advert_material_shipper_daily_statistic.invite_user_count) as invite_user_count,"
	fields += "SUM(t_advert_material_shipper_daily_statistic.qr_invite_count) as qr_invite_count,"
	fields += "SUM(t_advert_material_shipper_daily_statistic.invite_user_fee) as invite_user_fee,"
	fields += "SUM(t_advert_material_shipper_daily_statistic.material_invite_count) as material_invite_count,"
	fields += "SUM(t_advert_material_shipper_daily_statistic.order_count) as order_count,"
	fields += "SUM(t_advert_material_shipper_daily_statistic.total_order_price) as total_order_price,"
	fields += "SUM(t_advert_material_shipper_daily_statistic.invite_user_fee+total_order_tips_fee) as total_reward_fee"
	rs := db.Model(models.AdvertMaterialShipperDailyStatistic{}).
		Joins("left join t_admin on t_admin.id = t_advert_material_shipper_daily_statistic.shipper_id").
		Select(fields).
		Where("date between ? and ?", request.BeginDate, request.EndDate).
		Where("city_id = ? and area_id = ?", request.CityID, request.AreaID).
		Group("t_advert_material_shipper_daily_statistic.shipper_id").
		Find(&data)
	if rs.RowsAffected < 0 {
		return data, errors.New("failed")
	}
	return data, nil
}

// CmsInviteStatisticByArea 宣传材料邀请用户根据区域统计
func (s MaterialService) CmsInviteStatisticByArea(beginDate string, endDate string, langUtil lang.LangUtil) ([]models.AdvertMaterialShipperDailyStatisticByArea, error) {
	db := tools.Db
	var data []models.AdvertMaterialShipperDailyStatisticByArea = make([]models.AdvertMaterialShipperDailyStatisticByArea, 0)
	fields := "b_area.id AS area_id," +
		"b_area.name_" + langUtil.Lang + " AS area_name," +
		"SUM(s.invite_user_count) AS invite_user_count," +
		"SUM(s.qr_invite_count) AS qr_invite_count," +
		"SUM(s.invite_user_fee) AS invite_user_fee," +
		"SUM(s.material_invite_count) AS material_invite_count," +
		"SUM(s.order_count) AS order_count," +
		"SUM(s.total_order_price) AS total_order_price," +
		"SUM(s.invite_user_fee + s.total_order_tips_fee) AS total_reward_fee"
	rs := db.Model(models.Area{}).
		Joins("LEFT join t_advert_material_shipper_daily_statistic s on b_area.id = s.area_id and s.date between ? and ?", beginDate, endDate).
		Select(fields).
		Group("b_area.id").
		Where("b_area.state = 1").
		Order("invite_user_count desc").
		Find(&data)
	if rs.RowsAffected < 0 {
		return data, errors.New("failed")
	}
	return data, nil
}

// GetCmsShipperUserMonthlyStatistic 获取配送员用户月统计
func (s MaterialService) GetCmsShipperUserMonthlyStatistic(shipper models.Admin, month string, categoryId int, langUtil lang.LangUtil) (map[string]interface{}, error) {
	db := tools.Db
	var data map[string]interface{} = make(map[string]interface{})
	var categories []map[string]interface{} = make([]map[string]interface{}, 0)
	fields := "SUM(1) AS invite_user_count," +
		"SUM(t_advert_material_shipper_user_month_statistic.order_count) AS order_count," +
		"SUM(t_advert_material_shipper_user_month_statistic.total_order_price) AS total_order_price," +
		"SUM(t_advert_material_shipper_user_month_statistic.invite_user_fee + t_advert_material_shipper_user_month_statistic.total_order_tips_fee) AS total_reward_fee"
	query := db.Model(models.AdvertMaterialShipperUserMonthStatistic{}).
		Joins("left join t_admin on t_admin.id = t_advert_material_shipper_user_month_statistic.shipper_id").
		Select(fields).
		Where("shipper_id = ?", shipper.ID).
		Where("month = ?", month+"-01")
	if categoryId > 0 {
		query.Where("t_advert_material_shipper_user_month_statistic.advert_material_category_id = ? ", categoryId)
	}
	rs := query.Scan(&data)
	if rs.RowsAffected < 0 {
		return data, nil
	}

	fields = "m.advert_material_category_id as category_id,"
	fields += "c.name_" + langUtil.Lang + " as category_name,"
	fields += "count(m.id) as user_count"
	rs = db.Table("t_advert_material_shipper_user_month_statistic m").
		Select(fields).
		Joins("join t_advert_material_category c on c.id = m.advert_material_category_id").
		Where("shipper_id = ?", shipper.ID).
		Where("month = ?", month+"-01").
		Group("m.advert_material_category_id").
		Scan(&categories)
	data["categories"] = categories
	return data, nil
}

// GetCmsShipperUserMonthlyStatisticList 获取配送员用户月统计列表
func (s MaterialService) GetCmsShipperUserMonthlyStatisticList(shipper models.Admin, month string, categoryId, page int, limit int, sortColumn, sortType string) ([]models.AdvertMaterialShipperUserMonthStatistic, int64, error) {
	db := tools.Db
	var data []models.AdvertMaterialShipperUserMonthStatistic = make([]models.AdvertMaterialShipperUserMonthStatistic, 0)
	var total int64
	query := db.Model(models.AdvertMaterialShipperUserMonthStatistic{}).
		Preload("AdvertMaterialShipperUser.AdvertMaterial").
		Preload("AdvertMaterialShipperUser.AdvertMaterialCode").
		Preload("AdvertMaterialCategory").
		Preload("User").
		Where("month = ?", month+"-01").
		Where("shipper_id = ?", shipper.ID)
	if categoryId > 0 {
		query.Where("advert_material_category_id = ?", categoryId)
	}
	rs := query.Count(&total)
	if rs.RowsAffected < 0 {
		return data, total, errors.New("failed")
	}
	offset := (page - 1) * limit
	if total > int64(offset) {
		rs := query.Offset((page - 1) * limit).
			Order(fmt.Sprintf("%s %s", sortColumn, sortType)).
			Limit(limit).
			Find(&data)
		if rs.RowsAffected < 0 {
			return data, total, errors.New("failed")
		}
	}

	return data, total, nil
}

// 扫码记录
func (m MaterialService) GetMaterialScanLog(shipperId int,page int,limit int) (interface{}, interface{}) {
	db := tools.Db
	type ScanLogData struct {
		Code   string    `gorm:"column:code" json:"code"`
		CodeType int  `gorm:"column:code_type" json:"code_type"`
		IsSuccess int  `gorm:"column:is_success" json:"is_success"`
		MsgUg string `gorm:"column:msg_ug" json:"msg_ug"`
		MsgZh string `gorm:"column:msg_zh" json:"msg_zh"`
		CreatedAt time.Time `gorm:"column:created_at" json:"created_at"`
		OpenId string `gorm:"column:open_id" json:"open_id"`

	}
	var data []ScanLogData
	query := db.Table("t_advert_material_code_scan").Where("shipper_id = ?",shipperId)
	query.Scopes(scopes.Page(page, limit)).Order("id desc").Find(&data)
	result := make([]map[string]interface{},0)
	for _, v := range data {
		item :=make(map[string]interface{})
		item["code"]=v.Code
		if v.CodeType == 1{
			item["code_type"]=m.langUtil.T("advert_code")
		}else {
			item["code_type"]=m.langUtil.T("advert_matmerial")
		}
		if m.langUtil.Lang == "ug" {
			item["msg"]=v.MsgUg
		}else{
			item["msg"]=v.MsgZh
		}
		if v.IsSuccess == 1{
			item["is_success"]=m.langUtil.T("success")
		}else{
			item["is_success"]=m.langUtil.T("failed")
		}
		item["success"] = v.IsSuccess
		item["open_id"] = tools.MaskStrByLocation(v.OpenId,12,2) 
		item["created_at"] = v.CreatedAt.Format("2006-01-02 15:04:05")
		result = append(result,item)
	}
	
	return result, nil
}
