package services

import (
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
	"sort"

	resource "mulazim-api/resources"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ShipperRestSpotService struct {
	langUtil *lang.LangUtil
	language string
}

func NewShipperRestSpotService(c *gin.Context) *ShipperRestSpotService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	ShipperRestSpotService := ShipperRestSpotService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &ShipperRestSpotService
}

// 创建
func (s ShipperRestSpotService) Create(admin models.Admin, params resource.ShipperRestSpotEntity) error { 
	db := tools.GetDB()
	var items []models.ShipperRestSpot
	for _, item := range params.Items {
		items = append(items, models.ShipperRestSpot{
			CityID: params.CityId,
			AreaID: params.AreaId,
			AdminID: admin.ID,
			State: item.State,
			NameZh: item.NameZh,
			NameUg: item.NameUg,
			DescZh: item.DescZh,
			DescUg: item.DescUg,
			Lat: item.Lat,
			Lng: item.Lng,
			CreatedAt: carbon.Now(configs.AsiaShanghai).Carbon2Time(),
			UpdatedAt: carbon.Now(configs.AsiaShanghai).Carbon2Time(),
		})
	}
	er :=db.Create(&items).Error
	if er != nil {
		tools.Logger.Error("创建失败", er)
		return er
	}

	return nil
}
// 列表
func (s ShipperRestSpotService) List(admin models.Admin, page int, limit int,  cityId int, areaId int, state int,keyWord string,lg string,isShipper bool,otherParam ...string) []resource.ShipperRestSpotItemResponseEntity { 
	db := tools.GetDB()
	var items []models.ShipperRestSpot
	spotItemQuery :=db.Model(&models.ShipperRestSpot{}).Where("city_id = ? and area_id = ?", cityId,areaId).Preload("City").Preload("Area")
	if isShipper {
		spotItemQuery.Where("state = ?",1).Find(&items)
	}else{
		if len(keyWord) > 0 {
			keyWord="%"+keyWord+"%"
			spotItemQuery.Where("(name_ug like ? or name_zh like ? or desc_ug like ? or desc_zh like ?)",keyWord,keyWord,keyWord,keyWord).Find(&items)
		}else{
			spotItemQuery.Find(&items)
		}
	}
	
	var spots []resource.ShipperRestSpotItemResponseEntity
	lat :=""
	lng :=""
	if len(otherParam) > 0{
		lat = otherParam[0]
		lng = otherParam[1]
	}
	for _, item := range items {
		name :=item.NameZh 
		cityName :=item.City.NameZh
		areaName :=item.Area.NameZh 
		desc :=item.DescZh
		if lg == "ug" { 
			cityName =item.City.NameUg
			areaName =item.Area.NameUg
			name = item.NameUg
			desc = item.DescUg
		}
		distance :=float64(0)
		duration :=float64(0)
		distanceMode :=1 //1:gps 2:高德
		if lat != "" && lng != "" { 
			
			switch(distanceMode)  { 
			case 1:
				distance = tools.ToFloat64(tools.CalculateLatitudeLongitudeDistance(tools.ToFloat64(lng),tools.ToFloat64(lat),tools.ToFloat64(item.Lng),tools.ToFloat64(item.Lat)),2)
			case  2: 
				//高德获取的距离
				ridingTime :=0.5 //骑行时间 按步行时间的 1/2 来计算
				tools.Logger.Info("休息区距离信息获取,",lat,lng,tools.ToString(item.Lat),tools.ToString(item.Lng))
				origins, _ := tools.GetDistanceFromAMap(lat,lng,tools.ToString(item.Lat),tools.ToString(item.Lng))
				if len(origins.Route.Paths) > 0 {
					
					du :=tools.ToFloat64(origins.Route.Paths[0].Duration)
					tm :=float64(0)
					if du > 0 && du < 60 {
						tm  = 1
					}else{
						tm =du/60
					}
					if tm > 0 {
						tm = float64(tm) * ridingTime
						if tm < 1 {
							tm = 1
						}
					}
					
					distance = tools.ToFloat64(origins.Route.Paths[0].Distance)
					duration = tools.ToFloat64(tm,2)
				}
			
			}

			
		}
		spots = append(spots, resource.ShipperRestSpotItemResponseEntity{
			ID: item.ID,
			CityId: item.CityID,
			AreaId: item.AreaID,
			NameZh: item.NameZh,
			NameUg: item.NameUg,
			Name: name,
			CityName: cityName,
			AreaName: areaName,
			DescZh: item.DescZh,
			DescUg: item.DescUg,
			Desc: desc,
			Lat: item.Lat,
			Lng: item.Lng,
			State: item.State,
			CreatedAt: carbon.Time2Carbon(item.CreatedAt).SetTimezone(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			UpdatedAt: carbon.Time2Carbon(item.UpdatedAt).SetTimezone(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			StateName: s.langUtil.TArr("states")[item.State],
			Distance: distance,
			Duration: duration,
		})
	}

	if isShipper {
		//根据spot中的distance asc 排序 
		sort.Slice(spots, func(i, j int) bool {
			return spots[i].Distance < spots[j].Distance
		})
		
	}
	return spots
}

// 详情
func (s ShipperRestSpotService) Detail(ID int,lg string) (resource.ShipperRestSpotItemResponseEntity, error) { 
	var item models.ShipperRestSpot
	db := tools.GetDB()
	db.Model(&item).Where("id = ?", ID).Preload("City").Preload("Area").Find(&item)
	name :=item.NameZh 
	cityName :=item.City.NameZh
	areaName :=item.Area.NameZh 
	if lg == "ug" { 
		cityName =item.City.NameUg
		areaName =item.Area.NameUg
		name = item.NameUg
	}
	result := resource.ShipperRestSpotItemResponseEntity{
		ID: item.ID,
		CityId: item.CityID,
		AreaId: item.AreaID,
		Name: name,
		NameZh: item.NameZh,
		NameUg: item.NameUg,
		CityName: cityName,
		AreaName: areaName,
		DescZh: item.DescZh,
		DescUg: item.DescUg,
		Lat: item.Lat,
		Lng: item.Lng,
		State: item.State,
		CreatedAt: carbon.Time2Carbon(item.CreatedAt).SetTimezone(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
		UpdatedAt: carbon.Time2Carbon(item.UpdatedAt).SetTimezone(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
		StateName: s.langUtil.TArr("states")[item.State],
	}
	return result, nil
}

// 修改
func (s ShipperRestSpotService) ChangeState(ID int, state int) error { 
	db := tools.GetDB()
	var item models.ShipperRestSpot
	db.Model(&item).Where("id = ?", ID).Update("state", state)
	return nil
}

// 删除
func (s ShipperRestSpotService) Delete(ID int) error { 
	db := tools.GetDB()
	db.Model(&models.ShipperRestSpot{}).Where("id = ?", ID).Delete(&models.ShipperRestSpot{})
	return nil
}

// 修改
func (s ShipperRestSpotService) Update(admin models.Admin,params resource.ShipperRestSpotUpdateEntity) error { 

	db := tools.GetDB()
	for _, item := range params.Items { 
		db.Model(&models.ShipperRestSpot{}).Where("id = ?", item.ID).Updates(&map[string]interface{}{
			"name_zh": item.NameZh,
			"name_ug": item.NameUg,
			"desc_zh": item.DescZh,
			"desc_ug": item.DescUg,
			"lat": item.Lat,
			"lng": item.Lng,
			"state": item.State,
			"updated_at": carbon.Now(configs.AsiaShanghai).Carbon2Time(),
		})
	}

	return nil
}



// 列表
func (s ShipperRestSpotService) ListForCms(admin models.Admin, page int, limit int,  cityId int, areaId int, state int,keyWord string,lg string) resource.ShipperRestSpotItemResponseEntityCms { 
	db := tools.GetDB()
	var items []models.ShipperRestSpot
	spotItemQuery :=db.Model(&models.ShipperRestSpot{}).Preload("City").Preload("Area").Preload("Admin")
	if cityId > 0 { 
		spotItemQuery.Where("city_id = ?", cityId)
	}
	if areaId > 0 { 
		spotItemQuery.Where("area_id = ?", areaId)
	}
	if page <= 0 { 
		page = 1
	}
	if limit <= 0 { 
		limit = 10
	}
	offset := (page - 1) * limit
	count :=int64(0)
	if len(keyWord) > 0 {
		keyWord="%"+keyWord+"%"
		q :=spotItemQuery.Where("(name_ug like ? or name_zh like ? or desc_ug like ? or desc_zh like ?)",keyWord,keyWord,keyWord,keyWord).Order("id desc")
		q.Count(&count)
		q.Offset(offset).Limit(limit).Find(&items)
	}else{
		q:=spotItemQuery.Order("id desc")
		q.Count(&count)
		q.Offset(offset).Limit(limit).Find(&items)
	}
	
	
	var spots []resource.ShipperRestSpotItemResponseEntity
	lat :=""
	lng :=""
	
	for _, item := range items {
		name :=item.NameZh 
		cityName :=item.City.NameZh
		areaName :=item.Area.NameZh 
		desc :=item.DescZh
		adminName :=item.Admin.Name
		if lg == "ug" { 
			cityName =item.City.NameUg
			areaName =item.Area.NameUg
			name = item.NameUg
			desc = item.DescUg
		}
		distance :=float64(0)
		duration :=float64(0)
		distanceMode :=1 //1:gps 2:高德
		if lat != "" && lng != "" { 
			
			switch(distanceMode)  { 
			case 1:
				distance = tools.ToFloat64(tools.CalculateLatitudeLongitudeDistance(tools.ToFloat64(lng),tools.ToFloat64(lat),tools.ToFloat64(item.Lng),tools.ToFloat64(item.Lat)),2)
			case  2: 
				//高德获取的距离
				ridingTime :=0.5 //骑行时间 按步行时间的 1/2 来计算
				tools.Logger.Info("休息区距离信息获取,",lat,lng,tools.ToString(item.Lat),tools.ToString(item.Lng))
				origins, _ := tools.GetDistanceFromAMap(lat,lng,tools.ToString(item.Lat),tools.ToString(item.Lng))
				if len(origins.Route.Paths) > 0 {
					
					du :=tools.ToFloat64(origins.Route.Paths[0].Duration)
					tm :=float64(0)
					if du > 0 && du < 60 {
						tm  = 1
					}else{
						tm =du/60
					}
					if tm > 0 {
						tm = float64(tm) * ridingTime
						if tm < 1 {
							tm = 1
						}
					}
					
					distance = tools.ToFloat64(origins.Route.Paths[0].Distance)
					duration = tools.ToFloat64(tm,2)
				}
			
			}

			
		}
		spots = append(spots, resource.ShipperRestSpotItemResponseEntity{
			ID: item.ID,
			CityId: item.CityID,
			AreaId: item.AreaID,
			NameZh: item.NameZh,
			NameUg: item.NameUg,
			Name: name,
			CityName: cityName,
			AreaName: areaName,
			DescZh: item.DescZh,
			DescUg: item.DescUg,
			Desc: desc,
			Lat: item.Lat,
			Lng: item.Lng,
			State: item.State,
			CreatedAt: carbon.Time2Carbon(item.CreatedAt).SetTimezone(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			UpdatedAt: carbon.Time2Carbon(item.UpdatedAt).SetTimezone(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			StateName: s.langUtil.TArr("states")[item.State],
			Distance: distance,
			Duration: duration,
			AdminName: adminName,
		})
	}
	return resource.ShipperRestSpotItemResponseEntityCms{
		Items: spots,
		Limit: limit,
		Page: page,
		Total: int(count),
	}
}