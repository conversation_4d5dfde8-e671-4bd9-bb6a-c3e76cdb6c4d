package services

import (
	"errors"
	"fmt"
	"mulazim-api/models"
	"mulazim-api/requests/cms/advert/materialRequest/batchRequest"
	"mulazim-api/tools"
)

// 宣传材料批次服务

type AdvertMaterialPrintBatchService struct {
}

// Create 创建
func (s *AdvertMaterialPrintBatchService) Create(admin models.Admin, material models.AdvertMaterial, request batchRequest.CreateRequest) (models.AdvertMaterialPrintBatch, error) {
	var batchNo int64
	tools.GetDB().Model(models.AdvertMaterialPrintBatch{}).
		Where("advert_material_id = ?", material.ID).
		Unscoped().
		Count(&batchNo)
	batch := models.AdvertMaterialPrintBatch{
		AdvertMaterialId: material.ID,
		BatchNo:          int(batchNo) + 1,
		Count:            request.Count,
		CreatorId:        admin.ID,
		State:            models.AdvertMaterialPrintBatchStateWait,
	}
	rs := tools.GetDB().Create(&batch)
	if rs.Error != nil {
		msg := fmt.Sprintf("创建宣传材料批次失败: %s", rs.Error.Error())
		tools.Logger.Warn(msg)
		return models.AdvertMaterialPrintBatch{}, errors.New("failed")
	}
	return batch, nil
}

// List 列表
func (s *AdvertMaterialPrintBatchService) List(material models.AdvertMaterial, page int, limit int, state int) ([]models.AdvertMaterialPrintBatch, int64, error) {
	var (
		batches []models.AdvertMaterialPrintBatch
		total   int64
	)
	query := tools.GetDB().Model(models.AdvertMaterialPrintBatch{}).
		Where("advert_material_id = ?", material.ID)
	if state != 0 {
		query.Where("state = ?", state)
	}
	rs := query.Count(&total)
	if rs.Error != nil {
		msg := fmt.Sprintf("宣传材料批次列表： 获取宣传材料打印批次总数失败: %s", rs.Error.Error())
		tools.Logger.Error(msg)
		return nil, 0, errors.New("failed")
	}
	query.Order("id desc")
	rs = query.Offset((page - 1) * limit).Limit(limit).Find(&batches)
	if rs.Error != nil {
		msg := fmt.Sprintf("宣传材料批次列表： 获取宣传材料打印批次列表失败: %s", rs.Error.Error())
		tools.Logger.Error(msg)
		return nil, 0, errors.New("failed")
	}
	return batches, total, nil
}

// FindById 根据ID查找
func (s *AdvertMaterialPrintBatchService) FindByIdWithMaterial(id int) (models.AdvertMaterialPrintBatch, error) {
	var batch models.AdvertMaterialPrintBatch
	rs := tools.GetDB().
		Preload("AdvertMaterial").
		First(&batch, id)
	if rs.Error != nil {
		msg := fmt.Sprintf("根据ID查找宣传材料打印批次失败: ID:%d, %s", id, rs.Error.Error())
		tools.Logger.Error(msg)
		return models.AdvertMaterialPrintBatch{}, errors.New("failed")
	}
	return batch, nil
}
