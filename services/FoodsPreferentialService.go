package services

import (
	"errors"
	"fmt"
	"math"
	"mulazim-api/configs"
	customError "mulazim-api/errors"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/requests/merchantRequest/foodsPreferentRequest"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type FoodsPreferentialService struct {
	langUtil *lang.LangUtil
	language string
	BaseService
}

func NewFoodsPreferentialService(c *gin.Context) *FoodsPreferentialService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := FoodsPreferentialService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &service
}
// List 列表
func (c *FoodsPreferentialService) List(restaurant models.Restaurant, state, page, limit int) (foods []models.FoodsPreferential, total int64, err error) {
	foods = make([]models.FoodsPreferential, 0)
	db := tools.GetDB()
	query := db.Model(&models.FoodsPreferential{}).
		Preload("RestaurantFood.ComboFoodItems.RestaurantFood"). // 套餐子美食
		Preload("RestaurantFood.ComboFoodItems.SelectedSpec.FoodSpecOptions"). // 套餐子美食，以及子美食已选规格
		Preload("SelectedSpec.FoodSpecOptions").                               // 已选规格
		Where("restaurant_id = ?", restaurant.ID)
	if state != -1 {
		query = query.Where("state = ?", state)
	}
	rs := query.Count(&total)
	offset := (page - 1) * limit

	if rs.RowsAffected < 0 {
		msg := fmt.Sprintf("查询美食优惠信息失败,res id : %d, page: %d, limit: %d 错误信息：%s", restaurant.ID, page, limit, rs.Error.Error())
		tools.Logger.Error(msg)
		return foods, 0, errors.New("system_error")
	}

	if total > 0 && int(total) > offset {
		query.Offset(offset).Limit(limit).
			Preload("PreferentialType").
			Preload("RestaurantFood").
			Order("id desc").
			Find(&foods)
	}
	return foods, total, nil

}

// CreateSingleFood Create 创建
// @param restaurant 餐厅
// @param admin 管理员
// @param food 美食
// @param discountPrice 折扣价格
// @param maxOrderCount 一个订单里最多能购买的数量
// @param orderCountPerDay  一天内的订购次数（0表示能购买无数，1表示智能买一个）
// @param startTimeStr 优惠开始时间
// @param endTimeStr 优惠结束时间
// @param startDatetimeStr 优惠开始日期
// @param endDateTimeStr 优惠结束日期
// @return
func (c *FoodsPreferentialService) CreateSingleFood(
	restaurant models.Restaurant,
	admin models.Admin,
	food models.RestaurantFoods, params foodsPreferentRequest.CreateRequest,
) (err error) {
	//loc, err := time.LoadLocation("Asia/Shanghai")
	//if err != nil {
	//	fmt.Println("Error loading location:", err)
	//	return
	//}

	foodName := c.BaseService.CheckFoodsMultipleDiscountForPref(c.langUtil.Lang, food.ID,
		params.StartDateTime, params.EndDateTime, params.StartTime, params.EndTime,
	)
	if len(foodName) > 0 {
		//存在正在进行的多分打折活动 不允许创建
		return errors.New(fmt.Sprintf(c.langUtil.T("food_has_multiple_discount"), foodName))
	}

	startDateTime, _ := time.Parse("2006-01-02 15:04:05 -0700", params.StartDateTime+" 00:00:00 +0800")
	endDateTime, _ := time.Parse("2006-01-02 15:04:05 -0700", params.EndDateTime+" 00:00:00 +0800")
	db := tools.GetDB()
	var foodsPreferentials []models.FoodsPreferential
	db.Model(&models.FoodsPreferential{}).
		Where("restaurant_id = ? AND restaurant_foods_id = ? AND state = ?", restaurant.ID, food.ID,
			models.FOODSPREFERENTIAL_STATE_ON).
		Find(&foodsPreferentials)
	if len(foodsPreferentials) > 0 {
		for _, foodsPreferential := range foodsPreferentials {
			if tools.DateOverlap(
				params.StartDateTime,
				params.EndDateTime,
				foodsPreferential.StartDateTime.Format("2006-01-02"),
				foodsPreferential.EndDateTime.Format("2006-01-02")) {
				if tools.TimelineOverlap(params.StartTime, params.EndTime, foodsPreferential.StartTime, foodsPreferential.EndTime) {
					return errors.New("food_referential_time_overlap")
				}
			}
		}
	}

	specId := 0
	if params.FoodType == models.FoodsComboItemFoodTypeSpec {
		_specID, err := c.SaveFoodSpec(restaurant.ID, food.ID, params.OptionIds)
		if err != nil {
			return errors.New("failed")
		}
		specId = _specID
	}

	var foodsPreferential models.FoodsPreferential = models.FoodsPreferential{
		CityID:            restaurant.CityID,
		AreaID:            restaurant.AreaID,
		RestaurantID:      restaurant.ID,
		RestaurantFoodsID: food.ID,
		Type:              models.FOODSPREFERENTIAL_TYPE_PRICE_DISCOUNT,
		StartDateTime:     startDateTime,
		EndDateTime:       endDateTime,
		StartTime:        params.StartTime,
		EndTime:          params.EndTime,
		Level:             1,
		Percent:          (food.Price - params.DiscountPrice) * 100 / food.Price,
		DiscountPrice:    params.DiscountPrice,
		MaxOrderCount:    &params.MaxOrderCount,
		OrderCountPerDay: params.OrderCountPerDay,
		SendPlatform:      0,
		ShowHome:          0,
		State:             models.FOODSPREFERENTIAL_STATE_ON,
		CreatorID:         &admin.ID,

		FoodType: params.FoodType,
		SpecID:   specId,
	}
	rs := db.Create(&foodsPreferential)
	if rs.RowsAffected == -1 {
		msg := fmt.Sprintf("创建美食优惠信息失败, restaurant id : %d, food id : %d, admin id : %d, 错误信息：%s",
			restaurant.ID, food.ID, admin.ID, rs.Error.Error())
		tools.Logger.Error(msg)
		return errors.New("system_error")
	}
	return nil
}

// CreateRestaurantFoodsPreferential 创建餐厅美食优惠
// @param restaurant 餐厅
// @param admin 管理员
// @params preferentialPercentage 折扣百分比
// @param maxOrderCount 一个订单里最多能购买的数量
// @param orderCountPerDay  一天内的订购次数（0表示能购买无数，1表示智能买一个）
// @param startTimeStr 优惠开始时间
// @param endTimeStr 优惠结束时间
// @param startDatetimeStr 优惠开始日期
// @param endDateTimeStr 优惠结束日期
// @return error
func (c *FoodsPreferentialService) CreateRestaurantFoodsPreferential(
	restaurant models.Restaurant, admin models.Admin, params foodsPreferentRequest.CreateRequest,
) error {
	var (
		startDateTime, _ = time.Parse("2006-01-02 15:04:05 -0700", params.StartDateTime+" 00:00:00 +0800")
		endDateTime, _   = time.Parse("2006-01-02 15:04:05 -0700", params.EndDateTime+" 00:00:00 +0800")
		foods            []models.RestaurantFoods
	)

	db := tools.GetDB()
	// 获取餐厅美食
	db.Model(&models.RestaurantFoods{}).
		Where("restaurant_id = ?", restaurant.ID).
		Where("state = ?", models.RESTAURANTFOODS_STATE_OPEN).
		Find(&foods)
	if len(foods) == 0 {
		return errors.New("restaurant_foods_not_found")
	}

	rs := db.Model(&models.FoodsPreferential{}).
		Where("restaurant_id = ? AND state = ?", restaurant.ID, models.FOODSPREFERENTIAL_STATE_ON).
		Update("state", models.FOODSPREFERENTIAL_STATE_OFF)
	if rs.RowsAffected == -1 {
		msg := fmt.Sprintf("关闭餐厅美食优惠失败, restaurant id : %d, admin id : %d, 错误信息：%s", restaurant.ID, admin.ID, rs.Error.Error())
		tools.Logger.Error(msg)
		return errors.New("system_error")
	}

	var foodsPreferentials = make([]models.FoodsPreferential, 0)
	for _, food := range foods {
		discountPrice := food.Price - food.Price*params.PreferentialPercentage/100
		foodName := c.BaseService.CheckFoodsMultipleDiscountForPref(c.langUtil.Lang, int(food.ID),
			params.StartDateTime, params.EndDateTime, params.StartTime, params.EndTime,
		)
		if len(foodName) > 0 {
			//存在正在进行的多分打折活动 不允许创建
			return errors.New(fmt.Sprintf(c.langUtil.T("food_has_multiple_discount"), foodName))
		}

		// 规格活动
		specId := 0
		if food.FoodType == models.RestaurantFoodsTypeSpec {
			_specID, err := c.SaveFoodSpec(restaurant.ID, food.ID, params.OptionIds)
			if err != nil {
				return errors.New("failed")
			}
			specId = _specID
		}
		foodsPreferentials = append(foodsPreferentials, models.FoodsPreferential{
			CityID:            restaurant.CityID,
			AreaID:            restaurant.AreaID,
			RestaurantID:      restaurant.ID,
			RestaurantFoodsID: food.ID,
			Type:              models.FOODSPREFERENTIAL_TYPE_PRICE_DISCOUNT,
			StartDateTime:     startDateTime,
			EndDateTime:       endDateTime,
			StartTime:        params.StartTime,
			EndTime:          params.EndTime,
			Level:             1,
			Percent:          params.PreferentialPercentage,
			DiscountPrice:     discountPrice,
			MaxOrderCount:    &params.MaxOrderCount,
			OrderCountPerDay: params.OrderCountPerDay,
			SendPlatform:      0,
			ShowHome:          0,
			State:             models.FOODSPREFERENTIAL_STATE_ON,
			CreatorID:         &admin.ID,

			FoodType: params.FoodType,
			SpecID: specId,
		})
	}
	rs = db.CreateInBatches(&foodsPreferentials, len(foodsPreferentials))
	if rs.RowsAffected == -1 {
		msg := fmt.Sprintf("创建餐厅美食优惠失败, restaurant id : %d, admin id : %d, 错误信息：%s",
			restaurant.ID, admin.ID, rs.Error.Error())
		tools.Logger.Error(msg)
		return errors.New("system_error")
	}
	return nil
}

// Update 更新
// @param preferential
// @param discountPrice
// @param maxOrderCount
// @param orderCountPerDay
// @param startTime
// @param endTime
// @param startDateTime
// @param endDateTime
func (c *FoodsPreferentialService) Update(
	preferential models.FoodsPreferential,
	discountPrice uint,
	maxOrderCount int,
	orderCountPerDay int,
	startTime string,
	endTime string,
	startDateTime string,
	endDateTime string,
	state int) (models.FoodsPreferential, error) {
	var (
		oldPreferentials []models.FoodsPreferential
		db               = tools.GetDB()
	)
	if state == models.FOODSPREFERENTIAL_STATE_ON {
		rs := db.Model(models.FoodsPreferential{}).
			Where("restaurant_foods_id = ? AND state = ?",
				preferential.RestaurantFoodsID, models.FOODSPREFERENTIAL_STATE_ON).
			Find(&oldPreferentials)
		if rs.RowsAffected == -1 {
			msg := fmt.Sprintf("查询美食优惠信息失败, restaurant_foods_id : %d, 错误信息：%s",
				preferential.RestaurantFoodsID, rs.Error.Error())
			tools.Logger.Error(msg)
			return preferential, errors.New("system_error")
		}
		// 循环 oldPreferentials
		for _, oldPreferential := range oldPreferentials {
			if oldPreferential.ID != preferential.ID {
				if tools.DateOverlap(startDateTime, endDateTime,
					oldPreferential.StartDateTime.Format("2006-01-02"),
					oldPreferential.EndDateTime.Format("2006-01-02")) {
					if tools.TimelineOverlap(startTime, endTime, oldPreferential.StartTime, oldPreferential.EndTime) {
						return preferential, errors.New("food_referential_time_overlap")
					}
				}
			}
		}
	}

	// 更新
	rs := db.Model(models.FoodsPreferential{}).
		Where("id = ?", preferential.ID).
		Updates(map[string]interface{}{
			"discount_price":      discountPrice,
			"max_order_count":     maxOrderCount,
			"order_count_per_day": orderCountPerDay,
			"start_time":          startTime,
			"end_time":            endTime,
			"start_date_time":     startDateTime + " 00:00:00",
			"end_date_time":       endDateTime + " 00:00:00",
			"state":               state,
		})
	if rs.RowsAffected == -1 {
		msg := fmt.Sprintf("更新美食优惠信息失败, preferential id : %d, 错误信息：%s",
			preferential.ID, rs.Error.Error())
		tools.Logger.Error(msg)
		return preferential, errors.New("system_error")
	}
	return preferential, nil

}

// UpdateState 更新状态
// @param state 状态
func (c *FoodsPreferentialService) UpdateState(preferential models.FoodsPreferential, state int) (models.FoodsPreferential, error) {
	var (
		db               = tools.GetDB()
		oldPreferentials []models.FoodsPreferential
	)
	if state == models.FOODSPREFERENTIAL_STATE_ON {
		rs := db.Model(models.FoodsPreferential{}).
			Where("restaurant_foods_id = ? AND state = ?",
				preferential.RestaurantFoodsID, models.FOODSPREFERENTIAL_STATE_ON).
			Find(&oldPreferentials)
		if rs.RowsAffected == -1 {
			msg := fmt.Sprintf("查询美食优惠信息失败, restaurant_foods_id : %d, 错误信息：%s",
				preferential.RestaurantFoodsID, rs.Error.Error())
			tools.Logger.Error(msg)
			return preferential, errors.New("system_error")
		}

		// 循环 oldPreferentials
		for _, oldPreferential := range oldPreferentials {
			if oldPreferential.ID != preferential.ID {
				if tools.DateOverlap(preferential.StartDateTime.Format("2006-01-02"), preferential.EndDateTime.Format("2006-01-02"),
					oldPreferential.StartDateTime.Format("2006-01-02"),
					oldPreferential.EndDateTime.Format("2006-01-02")) {
					if tools.TimelineOverlap(preferential.StartTime, preferential.EndTime, oldPreferential.StartTime, oldPreferential.EndTime) {
						return preferential, errors.New("food_referential_time_overlap")
					}
				}
			}
		}
		foodName := c.BaseService.CheckFoodsMultipleDiscountForPref(c.langUtil.Lang, preferential.RestaurantFoodsID, preferential.StartDateTime.Format("2006-01-02"), preferential.EndDateTime.Format("2006-01-02"), preferential.StartTime, preferential.EndTime)
		if len(foodName) > 0 {
			//存在正在进行的多分打折活动 不允许创建
			return preferential, fmt.Errorf(c.langUtil.T("food_has_multiple_discount"), foodName)
		}
	}

	// 更新
	rs := db.Model(models.FoodsPreferential{}).
		Where("id = ?", preferential.ID).
		Updates(map[string]interface{}{
			"state": state,
		})
	if rs.RowsAffected == -1 {
		msg := fmt.Sprintf("更新美食优惠信息失败, preferential id : %d, 错误信息：%s",
			preferential.ID, rs.Error.Error())
		tools.Logger.Error(msg)
		return preferential, errors.New("system_error")
	}
	return preferential, nil
}

// Delete 删除
func (s *FoodsPreferentialService) Delete(preferential models.FoodsPreferential) error {
	rs := tools.GetDB().Delete(&preferential)
	if rs.RowsAffected == -1 {
		msg := fmt.Sprintf("删除优惠活动失败：活动ID: %d", preferential.ID)
		tools.Logger.Error(msg)
		return errors.New("system_error")
	}
	return nil
}

// GetFoodsPreferentialList
//
//	@Author: YaKupJan
//	@Date: 2024-09-10 12:44:35
//	@Description: 查询 美食优惠信息表
//	@receiver fps
//	@param c
//	@param admin
//	@param limit
//	@param page
//	@param sortColumns
//	@param search
//	@param restaurantID
//	@param cityID
//	@param areaID
//	@param startDate
//	@param endDate
//	@return []models.FoodsPreferential
//	@return int64
func (fps *FoodsPreferentialService) GetFoodsPreferentialList(c *gin.Context, admin models.Admin, limit int, page int, sortColumns string, search string, restaurantID int, cityID int, areaID int, startDate string, endDate string, stateType int) ([]models.FoodsPreferential, int64) {
	var total int64                                      // 页数
	var foodsPreferentialList []models.FoodsPreferential // 列表
	// 查询 美食优惠信息表
	query := tools.Db.Model(models.FoodsPreferential{}).
		Select(
			"t_foods_preferential.id",
			"t_foods_preferential.restaurant_id",
			"t_foods_preferential.restaurant_foods_id",
			"t_foods_preferential.gift_restaurant_foods_id",
			"t_foods_preferential.type",
			"t_foods_preferential.start_date_time",
			"t_foods_preferential.end_date_time",
			"t_foods_preferential.start_time",
			"t_foods_preferential.end_time",
			"t_foods_preferential.level",
			"t_foods_preferential.percent",
			"t_foods_preferential.discount_price",
			"t_foods_preferential.original_price",
			"t_foods_preferential.count",
			"t_foods_preferential.max_order_count",
			"t_foods_preferential.order_count_per_day",
			"t_foods_preferential.gift_count",
			"t_foods_preferential.send_platform",
			"t_foods_preferential.creator_id",
			"t_foods_preferential.state",
			"t_foods_preferential.created_at",
			"t_foods_preferential.updated_at",
			"t_foods_preferential.price_markup_type",
			"t_foods_preferential.price_markup_id",
			"t_foods_preferential.price_markup_count",
			"t_foods_preferential.price_markup_saled_count",
			"t_foods_preferential.food_type",
			"t_foods_preferential.spec_id",
			"t_foods_preferential.weight",
		).
		Joins("LEFT JOIN t_restaurant ON t_restaurant.id = t_foods_preferential.restaurant_id").
		Where("t_restaurant.deleted_at IS NULL")
	if !admin.IsOwner() {
		query = query.Joins("INNER JOIN (SELECT store_id FROM b_admin_store WHERE admin_id = ? AND deleted_at IS NULL) AS valid_restaurants ON t_foods_preferential.restaurant_id = valid_restaurants.store_id", admin.ID)
	}
	if restaurantID != 0 {
		query = query.Where("t_foods_preferential.restaurant_id = ?", restaurantID)
	} else {
		if areaID != 0 {
			query = query.Where("t_foods_preferential.area_id = ?", areaID)
		}
		if cityID != 0 {
			query = query.Where("t_foods_preferential.city_id = ?", cityID)
		}
	}
	// 开始时间
	if startDate != "" {
		query = query.Where("t_foods_preferential.start_date_time >= ?", startDate)
	}
	// 结束时间
	if endDate != "" {
		query = query.Where("t_foods_preferential.end_date_time <= ?", endDate)
	}
	// 搜索功能
	if search != "" {
		query = query.Joins("JOIN t_restaurant_foods ON t_foods_preferential.restaurant_foods_id = t_restaurant_foods.id").
			Where("t_restaurant_foods.name_ug LIKE ? OR t_restaurant_foods.name_zh LIKE ?", "%"+search+"%", "%"+search+"%")
	}
	// 类型
	now := time.Now()
	currentDate := now.Format("2006-01-02")
	currentTime := now.Format("15:04:05")
	switch stateType {
	case 1: // 未开始的活动
		// 活动的开始日期未到，或者今天的活动还没到开始时间
		query = query.Where("t_foods_preferential.start_date_time > ? OR (t_foods_preferential.start_date_time = ? AND t_foods_preferential.start_time > ?)", currentDate, currentDate, currentTime)

	case 2: // 正在进行的活动
		// 活动在日期范围内，且当前时间在活动当天的时间段内
		query = query.Where("t_foods_preferential.start_date_time <= ? AND t_foods_preferential.end_date_time >= ?", currentDate, currentDate).
			Where("(t_foods_preferential.start_date_time < ? OR (t_foods_preferential.start_date_time = ? AND t_foods_preferential.start_time <= ?))", currentDate, currentDate, currentTime).
			Where("(t_foods_preferential.end_date_time > ? OR (t_foods_preferential.end_date_time = ? AND t_foods_preferential.end_time >= ?))", currentDate, currentDate, currentTime)

	case 3: // 已结束的活动
		// 活动的日期已经结束，或者今天的活动已经超过了结束时间
		query = query.Where("t_foods_preferential.end_date_time < ? OR (t_foods_preferential.end_date_time = ? AND t_foods_preferential.end_time < ?)", currentDate, currentDate, currentTime)
	} // 获取优惠的美食名称
	query = query.Preload("RestaurantFood").
		// 获取优惠类型
		Preload("PreferentialType").
		// 获取创建者
		Preload("Creator").
		// 获取餐厅
		Preload("Restaurant").
		// 获取加价
		Preload("PriceMarkupFood").
		Preload("SelectedSpec.FoodSpecOptions").
		Preload("RestaurantFood.ComboFoodItems.RestaurantFood").
		Preload("RestaurantFood.ComboFoodItems.SelectedSpec.FoodSpecOptions").
		Preload("PriceMarkupFood.PriceMarkupFoodLogSum", func(d *gorm.DB) *gorm.DB {
			return d.Select("price_markup_id,COALESCE(SUM(saled_count), 0) AS price_markup_food_saled_total_count").
				Where("state IN ?", []int{models.PriceMarkupFoodLogStateComplete, models.PriceMarkupFoodLogStateHavePaid}).
				Where("activity_type = ?", models.PriceMarkupFoodLogActivityTypePreferential).
				Group("price_markup_id")
		}).
		// 获取送东西的美食
		Preload("GiftFood", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "name_ug", "name_zh")
		})
	// 排序
	if sortColumns != "" {
		sortColumnsArray := strings.Split(sortColumns, ",")
		for _, sortColumn := range sortColumnsArray {
			query = query.Order(sortColumn)
		}
	} else {
		// 默认按时间倒序排序
		query = query.Order("updated_at desc")
	}
	// 分页 合计总数
	query.Count(&total)
	query.Scopes(scopes.Page(page, limit)).Find(&foodsPreferentialList)
	return foodsPreferentialList, total
}

// PreferentialTypeList
//
//	@Author: YaKupJan
//	@Date: 2024-09-10 13:09:20
//	@Description: 优惠类型列表
//	@receiver fps
//	@return []models.PreferentialType
func (fps *FoodsPreferentialService) PreferentialTypeList() []map[string]interface{} {
	preferentialTypeList := []models.PreferentialType{}
	tools.Db.Model(models.PreferentialType{}).Select("id", "name_zh", "name_ug").Find(&preferentialTypeList)
	var items []map[string]interface{}
	for _, item := range preferentialTypeList {
		name := tools.GetNameByLang(item, fps.language)
		items = append(items, map[string]interface{}{
			"id":   item.ID,
			"name": name,
		})
	}
	return items
}

// HasPreferential
//
//	@Author: YaKupJan
//	@Date: 2024-09-10 13:24:17
//	@Description: 判断是否有优惠
//	@receiver fps
//	@param restaurantId
//	@return bool
func (fps *FoodsPreferentialService) HasPreferential(restaurantId int) bool {
	curDate := carbon.Now().Format("Y-m-d")
	var count int64
	tools.Db.Model(models.FoodsPreferential{}).Where("restaurant_id = ?", restaurantId).Where("end_date_time >= ?", curDate).Where("state = ?", 1).Count(&count)
	return count > 0
}

// CreateFoodsPreferential
//
//	@Author: YaKupJan
//	@Date: 2024-09-10 18:57:23
//	@Description: 创建优惠
//	@receiver fps
//	@param params
//	@param OriginPrice
//	@param DiscountPrice
//	@return bool
//	@return string
func (fps *FoodsPreferentialService) CreateFoodsPreferential(params models.FoodsPreferential, OriginPrice *uint, DiscountPrice *uint,optionIds []int) (bool, string, string) {
	var db = tools.GetDB()
	var restaurantFood models.RestaurantFoods
	db.Model(models.RestaurantFoods{}).Where("id = ?", params.RestaurantFoodsID).Where("restaurant_id = ?", params.RestaurantID).Find(&restaurantFood)
	if restaurantFood.ID == 0 {
		return false, "بۇ تاماق بۇ ئاشخانىغا تەۋە ئەمەس قايتا سىناپ بېقىڭ", "这个美食不属于这个餐厅,请重试"
	}
	var alreadyExistList []models.FoodsPreferential
	// 判断是否已经存在优惠
	queryConfilict := db.Model(models.FoodsPreferential{}).Where("restaurant_foods_id = ?", params.RestaurantFoodsID)
	if restaurantFood.FoodType == models.RestaurantFoodsTypeSpec {
		specID := fps.GetSpecIdByOptions(params.RestaurantFoodsID, optionIds)
		queryConfilict.Where("spec_id = ?",specID)
	}
	queryConfilict.Where("state = ?", 1).Find(&alreadyExistList)
	if len(alreadyExistList) > 0 {
		for _, item := range alreadyExistList {
			if fps.conflictDateTimeLine(params.StartDateTime, params.EndDateTime, item.StartDateTime, item.EndDateTime) {
				// 验证时间冲突
				if fps.conflictTimeLine(item.StartTime, item.EndTime, params.StartTime, params.EndTime) {
					return false, "بۇ ۋاقىت دائىرىسىگە مۇشۇ تاماقنىڭ ئېتىبار ئۇچۇرى قوشۇلۇپ بولغان", "此时间范围内已经添加过这个美食的优惠信息"
				}
			}
		}
	}
	// 计算折扣
	var percent uint // 定义一个整数
	ratio := float64(*DiscountPrice) / float64(params.OriginalPrice)
	if ratio > 1 {
		percent = 0 // 如果优惠价格大于原价 相当于没有优惠 算是加价了
	} else {
		percent = uint(math.Round((1 - ratio) * 100))
	}
	params.Percent = percent
	// 默认为1级优惠
	params.Level = 1
	var restaurantFoods []models.RestaurantFoods
	// 获取美食信息
	db.Model(models.RestaurantFoods{}).Where("id = ?", params.RestaurantFoodsID).
		Preload("Restaurant").Find(&restaurantFoods)
	if len(restaurantFoods) == 0 {
		return false, "مۇناسىۋەتلىك تاماق ئۇچۇرى يوقكەن", "没有相关的美食信息。"
	}
	// 判断是否在营业时间内
	restaurant := restaurantFoods[0].Restaurant
	beginTime := restaurant.BeginTime
	endTime := restaurant.EndTime
	if !tools.TimeLineInTimeLine(params.StartTime, params.EndTime, beginTime, endTime) {
		return false, fmt.Sprintf("ئېتىبار ۋاقتى ئاشخانا تىجارەت ۋاقتى دائىرىسى ( %s دىن %s گىچە) ئىچىدە ئەمەسكەن", beginTime, endTime),
			fmt.Sprintf("优惠时间不在餐厅营业时间范围（%s到%s）之内。", beginTime, endTime)
	}
	foodName := fps.BaseService.CheckFoodsMultipleDiscountForPref(fps.langUtil.Lang, params.RestaurantFoodsID, carbon.Time2Carbon(params.StartDateTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d"), carbon.Time2Carbon(params.EndDateTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d"), params.StartTime, params.EndTime)
	if len(foodName) > 0 {
		//存在正在进行的多分打折活动 不允许创建
		return false, fmt.Sprintf(fps.langUtil.TUg("food_has_multiple_discount"), foodName), fmt.Sprintf(fps.langUtil.TZh("food_has_multiple_discount"), foodName)
	}

	params.CityID = restaurant.CityID
	params.AreaID = restaurant.AreaID
	if err := db.Create(&params).Error; err != nil {
		return false, err.Error(), err.Error()
	}
	return true, "", ""
}

// conflictDateTimeLine
//
//	@Author: YaKupJan
//	@Date: 2024-09-10 19:59:33
//	@Description: 验证日期是否冲突
//	@param start1
//	@param end1
//	@param start2
//	@param end2
//	@return bool
func (fps *FoodsPreferentialService) conflictDateTimeLine(start1, end1, start2, end2 time.Time) bool {
	// 解析日期时间格式
	format := "2006-01-02 15:04:05"
	startTime1 := start1.Format(format)
	endTime1 := end1.Format(format)
	startTime2 := start2.Format(format)
	endTime2 := end2.Format(format)
	// 检查是否有交集
	if (startTime1 <= endTime2) && (startTime2 <= endTime1) {
		return true
	}
	return false
}

// conflictTimeLine
//
//	@Author: YaKupJan
//	@Date: 2024-09-10 19:59:23
//	@Description: 验证时间是否冲突
//	@param start1 范围大的开始时间
//	@param end1 范围大的结束时间
//	@param start2 范围小的开始时间
//	@param end2 范围小的结束时间
//	@return bool
func (fps *FoodsPreferentialService) conflictTimeLine(start1, end1, start2, end2 string) bool {
	// 情况1: 如果开始和结束时间相同，视为冲突
	if start1 == end1 || start2 == end2 {
		return true
	}
	// 情况2: 两个时间段都跨天
	if start1 > end1 && start2 > end2 {
		return true
	}
	// 情况3: 两个时间段不跨天
	if start1 < end1 && start2 < end2 {
		// 不会交叉的情况: start1 > end2 或 start2 > end1
		if start1 > end2 || start2 > end1 {
			return false
		}
		return true
	}
	// 情况4: 其中一个时间段跨天
	if start1 > end1 || start2 > end2 {
		if start1 > end1 {
			start1Before := start1
			end1Before := "23:59:59"
			start1After := "00:00:00"
			end1After := end1
			if (start1Before > end2 || start2 > end1Before) && (start1After > end2 || start2 > end1After) {
				return false
			}
			return true
		} else {
			start2Before := start2
			end2Before := "23:59:59"
			start2After := "00:00:00"
			end2After := end2
			if (start2Before > end1 || start1 > end2Before) && (start2After > end1 || start1 > end2After) {
				return false
			}
			return true
		}
	}
	return false
}

// CreateFoodsPreferentialForAllFoods
//
//	@Author: YaKupJan
//	@Date: 2024-09-10 18:58:26
//	@Description: 创建优惠 批量创建
//	@receiver fps
//	@param params
//	@param percentage
func (fps *FoodsPreferentialService) CreateFoodsPreferentialForAllFoods(params models.FoodsPreferential, percentage *uint) (bool, string, string) {
	db := tools.GetDB()
	var restaurant models.Restaurant
	// 获取餐厅以及美食信息
	db.Model(models.Restaurant{}).Where("id = ?", params.RestaurantID).Preload("Foods", func(db *gorm.DB) *gorm.DB {
		return db.Where("state = ?", 1).Where("deleted_at IS NULL")
	}).First(&restaurant)

	//把现在进行中的优惠活动取消掉
	db.Model(models.FoodsPreferential{}).Where("restaurant_id = ?", params.RestaurantID).Update("state", 0)
	foods := restaurant.Foods

	// 循环创建优惠
	for _, food := range foods {
		beginTime := restaurant.BeginTime
		endTime := restaurant.EndTime
		if !tools.TimeLineInTimeLine(params.StartTime, params.EndTime, beginTime, endTime) {
			return false, fmt.Sprintf("%s نىڭ  ئېتىبار ۋاقتى ئاشخانا تىجارەت ۋاقتى دائىرىسى ( %s دىن %s گىچە)  ئىچىدە ئەمەسكەن", food.NameUg, beginTime, endTime),fmt.Sprintf("%s优惠时间不在餐厅营业时间范围（%s到%s）之内。",food.NameZh,beginTime, endTime)
		}

		newParams := params

		newParams.CityID = restaurant.CityID
		newParams.AreaID = restaurant.AreaID
		newParams.RestaurantFoodsID = food.ID

		foodName := fps.BaseService.CheckFoodsMultipleDiscountForPref(fps.langUtil.Lang, params.RestaurantFoodsID, params.StartDateTime.Format("2006-01-02"), params.EndDateTime.Format("2006-01-02"), params.StartTime, params.EndTime)
		if len(foodName) > 0 {
			//存在正在进行的多分打折活动 不允许创建
			return false, fmt.Sprintf(fps.langUtil.TUg("food_has_multiple_discount"), foodName), fmt.Sprintf(fps.langUtil.TZh("food_has_multiple_discount"), foodName)
		}
		// 检查 percentage 是否为空
		if percentage != nil {
			newParams.Percent = *percentage
		} else {
			newParams.Percent = 0
		}
		newParams.DiscountPrice = uint(math.Round((1-(float64(*percentage) / 100.0)) * float64(food.Price)))
		newParams.OriginalPrice = uint(food.Price)
		if err := db.Model(models.FoodsPreferential{}).Create(&newParams).Error; err != nil {
			return false, "", ""
		}
	}
	return true, "", ""
}

// CheckPriceMarkupTotalCountIsEnough
//
//  @Author: YaKupJan
//  @Date: 2024-10-28 15:39:31
//  @Description: 查询加价美食是否足够
//  @receiver fps
//  @param priceMarkupId
//  @param needPriceMarkupCount
func (fps *FoodsPreferentialService) CheckPriceMarkupTotalCountIsEnough(priceMarkupId int, needPriceMarkupCount int) {
	db := tools.GetDB()
	// 查询可以使用的数量
	var priceMarkup models.PriceMarkupFood
	db.Model(models.PriceMarkupFood{}).Where("id = ?", priceMarkupId).Find(&priceMarkup)
	// 如果 总数量 大于 可以使用的数量 那就不够
	if needPriceMarkupCount > priceMarkup.CanUseCount() {
		// 抛出异常
		msg := fmt.Sprintf(fps.langUtil.T("price_markup_total_count_is_not_enough"), priceMarkup.CanUseCount())
		err := customError.CustomError{
			Msg: msg,
		}
		panic(err)
	}

}

// GetPriceMarkupFoodInPrice
//
//  @Author: YaKupJan
//  @Date: 2024-10-31 17:34:52
//  @Description: 获取加价美食进价
//  @receiver fps
//  @param priceMarkupId
func (fps *FoodsPreferentialService) GetPriceMarkupFoodInPrice(priceMarkupId int) models.PriceMarkupFood {
	db := tools.GetDB()
	var priceMarkupFood models.PriceMarkupFood
	db.Model(models.PriceMarkupFood{}).Where("id = ?", priceMarkupId).First(&priceMarkupFood)
	// 如果 running state != 1   或者 不存在 那就报错
	if priceMarkupFood.RunningState != models.PriceMarkupRunningStateNormal || priceMarkupFood.ID == 0 {
		msg := fmt.Sprintf(fps.langUtil.T("price_markup_food_is_not_running"))
		err := customError.CustomError{Msg: msg}
		panic(err)
	}
	return priceMarkupFood
}

// CheekFoodsPreferentialTimeIsInPriceMarkupFoodTime
//
//  @Author: YaKupJan
//  @Date: 2024-11-15 11:04:14
//  @Description: 判断 优惠活动是不是在加价活动时间范围内
//  @receiver fps
//  @param preferentialStartDate
//  @param preferentialEndDate
//  @param preferentialStartTime
//  @param preferentialEndTime
//  @param markupFoodStartDate
//  @param markupFoodEndDate
func (fps *FoodsPreferentialService) CheekFoodsPreferentialTimeIsInPriceMarkupFoodTime(preferentialStartDate string, preferentialEndDate string, preferentialStartTime string, preferentialEndTime string, markupFoodStartDate *time.Time, markupFoodEndDate *time.Time) {
	// 将日期和时间组合成完整的日期时间
	preferentialStartDateTimeFull := fmt.Sprintf("%s %s:00", preferentialStartDate, preferentialStartTime)
	preferentialEndDateTimeFull := fmt.Sprintf("%s %s:00", preferentialEndDate, preferentialEndTime)

	// 调整加价活动时间范围
	markupFoodStartOfDay := time.Date(markupFoodStartDate.Year(), markupFoodStartDate.Month(), markupFoodStartDate.Day(), 0, 0, 0, 0, markupFoodStartDate.Location())
	markupFoodEndOfDay := time.Date(markupFoodEndDate.Year(), markupFoodEndDate.Month(), markupFoodEndDate.Day(), 23, 59, 59, 0, markupFoodEndDate.Location())

	inDateTimeLine := tools.DateTimeLineInDateTimeLine(
		preferentialStartDateTimeFull,
		preferentialEndDateTimeFull,
		markupFoodStartOfDay.Format("2006-01-02 15:04:05"),
		markupFoodEndOfDay.Format("2006-01-02 15:04:05"))

	// 检查结束时间是否在加价活动范围内
	if !inDateTimeLine {
		startTimeStr := markupFoodStartOfDay.Format("2006-01-02 15:04:05")
		endTimeStr := markupFoodEndOfDay.Format("2006-01-02 15:04:05")
		msg := fps.langUtil.T("time_enable_out_price_markup_time_range") + "(" + startTimeStr + " --- " + endTimeStr + ")"
		customErr := customError.CustomError{
			Msg: msg,
		}
		panic(customErr)
	}
}

// ChangePreferentialState
//
//  @Author: YaKupJan
//  @Date: 2024-09-11 15:47:45
//  @Description: 修改优惠状态
//  @receiver fps
//  @param ids
//  @param state
//  @return bool
//  @return string
func (fps *FoodsPreferentialService) ChangePreferentialState(ids []int, state *int) (bool, string, string) {
	db := tools.Db
	// 获取优惠列表
	var foodsPreferentialList []models.FoodsPreferential
	db.Model(models.FoodsPreferential{}).Where("id in ?", ids).Find(&foodsPreferentialList)
	for _, item := range foodsPreferentialList {
		item.State = *state
		if *state == 1 {
			// 验证规格信息
			if item.FoodType == models.RestaurantFoodsTypeSpec && !fps.CheckChangeStateSpecInfo(item.SpecID, item.RestaurantFoodsID) {
				return false, "تەڭشىگەن ئۆلچەم ئۇچۇرى ئ‍ۆزگەرتىۋېتىلگەن،بۇ پائالىيەتنى ئاچالمايسىز،قايتىدىن قۇرۇپ ئىشلىتىڭ", "设置的规格信息已经有更改，不能开启状态，请重新创建使用"
			}
			// 检查是否有冲突
			conflictItem := fps.getConflictItem(item)
			if conflictItem {
				return false, fmt.Sprintf("بۇ تاماققا ئوخشاش ۋاقتى بۆلىكىدە پائالىيەت تەڭشىلىپ بولغان"), fmt.Sprintf("在相同时间段内这个美食已经有了优惠信息")
			}
			foodName := fps.BaseService.CheckFoodsMultipleDiscountForPref(fps.langUtil.Lang, item.RestaurantFoodsID, carbon.Time2Carbon(item.StartDateTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d"), carbon.Time2Carbon(item.EndDateTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d"), item.StartTime, item.EndTime)
			if len(foodName) > 0 {
				//存在正在进行的多分打折活动 不允许创建
				return false, fmt.Sprintf(fps.langUtil.TUg("food_has_multiple_discount"), foodName), fmt.Sprintf(fps.langUtil.TZh("food_has_multiple_discount"), foodName)
			}
		}
		if item.PriceMarkupId > 0 && item.State == 1 {
			var markup models.PriceMarkupFood
			tools.Db.Model(markup).Where("id = ?", item.PriceMarkupId).First(&markup)
			// 如果当前活动占用库存大于加价活动目前能用到的库存时，不能修改状态
			openStateUseCount := item.PriceMarkupCount - item.PriceMarkupSaledCount
			if openStateUseCount > markup.CanUseCount() && item.State == 1 {
				return false, "بۇ پائالىيەتنى ئاچسىڭىز،سېتىۋالغان تاماقنىڭ جەمئىي ئامبار سانىدىن ئېشىپ كىتىدىكەن،تەڭشىگەن پائالىيەتلىرىڭىزنىڭ ئامبار ئىشلىتىش ئەھۋالىنى تەكشۈرۈڭ", "开启本活动，购买的加价美食超过仓库数量，查看您已创建的活动的仓库使用情况。"
			}
		}
		err := db.Model(models.FoodsPreferential{}).Where("id = ?", item.ID).Update("state", item.State).Error
		if err != nil {
			return false, "ھالەت ئۆزگەرتىش مەغلۇپ بولدى", "修改状态失败"
		}

	}
	return true, "", ""
}

func (fps *FoodsPreferentialService) UpdateWeight(id int, weight *int) error {
	db := tools.Db
	// 获取优惠列表
	err := db.Model(models.FoodsPreferential{}).Where("id = ?", id).Update("weight", weight).Error
	if err != nil {
		return errors.New("fail")
	}
	return nil
}

// getConflictItem
//
//  @Author: YaKupJan
//  @Date: 2024-09-11 15:47:52
//  @Description: 获取冲突优惠
//  @receiver fps
//  @param preferential
//  @return bool
func (fps *FoodsPreferentialService) getConflictItem(preferential models.FoodsPreferential) bool {
	db := tools.Db
	var foodsPreferentialList []models.FoodsPreferential
	db.Model(models.FoodsPreferential{}).
		Where("state = ?", 1).
		Where("restaurant_foods_id = ?", preferential.RestaurantFoodsID).
		Find(&foodsPreferentialList)
	if len(foodsPreferentialList) < 2 {
		return false
	}
	for _, item := range foodsPreferentialList {
		if preferential.ID == item.ID {
			continue
		}
		if !fps.conflictDateTimeLine(preferential.StartDateTime, preferential.EndDateTime, item.StartDateTime, item.EndDateTime) {
			continue
		}
		if fps.conflictTimeLine(item.StartTime, item.EndTime, preferential.StartTime, preferential.EndTime) {
			return true
		}
	}
	return false
}

// EditPreferential
//
//  @Author: YaKupJan
//  @Date: 2024-09-11 15:48:02
//  @Description: 修改优惠
//  @receiver fps
//  @param preferential
//  @param originPrice
//  @param discountPrice
//  @return bool
//  @return string
func (fps *FoodsPreferentialService) EditPreferential(params models.FoodsPreferential, originPrice *uint, discountPrice *uint) (bool, string, string) {
	db := tools.Db
	// 获取优惠信息
	var oldPreferential models.FoodsPreferential
	if err := db.Model(models.FoodsPreferential{}).Where("id = ?", params.ID).First(&oldPreferential).Error; err != nil {
		return false, "مۇناسىۋەتلىك ئېتىبار ئۇچۇرى بوش قاپتۇ", "没有找到优惠信息"
	}
	// 验证规格信息
	if params.State == 1 && oldPreferential.FoodType == models.RestaurantFoodsTypeSpec && !fps.CheckChangeStateSpecInfo(oldPreferential.SpecID, oldPreferential.RestaurantFoodsID) {
		return false, "تەڭشىگەن ئۆلچەم ئۇچۇرى ئ‍ۆزگەرتىۋېتىلگەن،بۇ پائالىيەتنى ئاچالمايسىز،قايتىدىن قۇرۇپ ئىشلىتىڭ", "设置的规格信息已经有更改，不能开启状态，请重新创建使用"
	}
	// 检查是否有冲突
	var alreadyExistList []models.FoodsPreferential
	confilictQuery := db.Model(models.FoodsPreferential{}).
		Where("restaurant_foods_id = ?", params.RestaurantFoodsID).
		Where("id <> ? ", params.ID)
	if oldPreferential.FoodType == models.RestaurantFoodsTypeSpec {
		confilictQuery.Where("spec_id = ?", oldPreferential.SpecID)
	}
	confilictQuery.Find(&alreadyExistList)

	if len(alreadyExistList) > 0 {
		for _, item := range alreadyExistList {
			if oldPreferential.ID == item.ID {
				continue
			}
			if !fps.conflictDateTimeLine(params.StartDateTime, params.EndDateTime, item.StartDateTime, item.EndDateTime) {
				continue
			}
			if fps.conflictTimeLine(item.StartTime, item.EndTime, params.StartTime, params.EndTime) {
				return false, "بۇ ۋاقىت دائىرىسىگە مۇشۇ تاماقنىڭ ئېتىبار ئۇچۇرى قوشۇلۇپ بولغان", "此时间范围内已经添加过这个美食的优惠信息"
			}
		}
	}
	foodName := fps.BaseService.CheckFoodsMultipleDiscountForPref(fps.langUtil.Lang, params.RestaurantFoodsID, carbon.Time2Carbon(params.StartDateTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d"), carbon.Time2Carbon(params.EndDateTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d"), params.StartTime, params.EndTime)
	if len(foodName) > 0 {
		//存在正在进行的多分打折活动 不允许创建
		return false, fmt.Sprintf(fps.langUtil.TUg("food_has_multiple_discount"), foodName), fmt.Sprintf(fps.langUtil.TZh("food_has_multiple_discount"), foodName)
	}
	// 获取美食信息
	var restaurantFoods []models.RestaurantFoods
	db.Model(models.RestaurantFoods{}).Where("id = ?", params.RestaurantFoodsID).
		Preload("Restaurant").
		Find(&restaurantFoods)
	if len(restaurantFoods) == 0 {
		return false, "مۇناسىۋەتلىك تاماق ئۇچۇرى يوقكەن", "没有相关的美食信息"
	}
	restaurant := restaurantFoods[0]
	beginTime := restaurant.BeginTime
	endTime := restaurant.EndTime
	// 检查时间是否在餐厅时间范围内
	if !tools.TimeLineInTimeLine(params.StartTime, params.EndTime, beginTime, endTime) {
		return false, fmt.Sprintf("ئېتىبار ۋاقتى ئاشخانا تىجارەت ۋاقتى دائىرىسى ( %s دىن %s گىچە) ئىچىدە ئەمەسكەن", beginTime, endTime),
			fmt.Sprintf("优惠时间不在餐厅营业时间范围（%s到%s）之内。", beginTime, endTime)

	}
	// 检查时间是否在今天之后
	fpStartDate := oldPreferential.StartDateTime.Format("2006-01-02")
	fpEndDate := oldPreferential.EndDateTime.Format("2006-01-02")
	today := time.Now().Format("2006-01-02")
	if fpEndDate <= today {
		return false, "ئاخىرلاشقان ئېتىبارنى ئۆزگەرتەلمەيسىز", "无法修改已经结束的优惠。"
	}
	// 更新优惠信息
	var updateData map[string]interface{}
	if fpStartDate <= today {
		updateData = map[string]interface{}{
			"end_date_time":       params.EndDateTime,
			"start_time":          params.StartTime,
			"end_time":            params.EndTime,
			"discount_price":      params.DiscountPrice,
			"state":               params.State,
			"send_platform":       params.SendPlatform,
			"max_order_count":     params.MaxOrderCount,
			"order_count_per_day": params.OrderCountPerDay,
			"weight":              params.Weight,
		}
	} else {
		updateData = map[string]interface{}{
			"restaurant_id":       params.RestaurantID,
			"restaurant_foods_id": params.RestaurantFoodsID,
			"type":                params.Type,
			"start_date_time":     params.StartDateTime,
			"end_date_time":       params.EndDateTime,
			"start_time":          params.StartTime,
			"end_time":            params.EndTime,
			"discount_price":      params.DiscountPrice,
			"max_order_count":     params.MaxOrderCount,
			"order_count_per_day": params.OrderCountPerDay,
			"send_platform":       params.SendPlatform,
			"state":               params.State,
			"weight":              params.Weight,
		}
	}
	// 判断是不是存在price_markup_count
	if params.PriceMarkupCount > 0 {
		updateData["price_markup_count"] = params.PriceMarkupCount
	}
	// 计算折扣率
	var percent uint // 定义一个整数
	ratio := float64(*discountPrice) / float64(*originPrice)
	if ratio > 1 {
		percent = 0 // 如果优惠价格大于原价 相当于没有优惠 算是加价了
	} else {
		percent = uint(math.Round((1 - ratio) * 100))
	}
	updateData["percent"] = percent
	updateData["updated_at"] = carbon.Now().ToDateTimeString()
	if err := db.Model(models.FoodsPreferential{}).Where("id = ?", params.ID).Updates(updateData).Error; err != nil {
		return false, "تەھرىرلەش  مەغلۇپ بولدى ", "修改失败"
	}
	return true, "تەھرىرلەش  مۇۋەپپەقىيەتلىك بولدى", "修改成功"
}

// DeletePreferential
//
//  @Author: YaKupJan
//  @Date: 2024-09-11 15:55:44
//  @Description: 删除优惠
//  @receiver fps
//  @param id
//  @return bool
//  @return string
func (fps *FoodsPreferentialService) DeletePreferential(ids []int) (bool, string) {
	tx := tools.Db.Begin()
	now := time.Now().Format("2006-01-02")
	// 获取优惠信息
	var foodsPreferentialList []models.FoodsPreferential
	tx.Model(models.FoodsPreferential{}).Where("id in ?", ids).Find(&foodsPreferentialList)
	if len(foodsPreferentialList) == 0 {
		return false, "مۇناسىۋەتلىك ئۇچۇر تېپىلمىدى"
	}
	// 检查优惠是否过期
	for _, item := range foodsPreferentialList {
		startDate := item.StartDateTime.Format("2006-01-02")
		endDate := item.EndDateTime.Format("2006-01-02")
		if now > endDate {
			name := tools.GetNameByLang(item.RestaurantFood, fps.language)
			tx.Rollback()
			return false, fmt.Sprintf("%sنىڭ ئېتىبار ۋاقتى ئۆتۈپ كەتكەن، ئۆچۈرگىلى بولمايدۇ", name)
		}

		// 当前时间在优惠的有效期内
		if startDate <= now && now <= endDate {
			name := tools.GetNameByLang(item.RestaurantFood, fps.language)
			tx.Rollback()
			return false, fmt.Sprintf("%s ئېتىبار قىلىنىۋاتىدۇ، ئۆچۈرۈشكە بولمايدۇ", name)
		}
	}
	// 删除优惠
	if err := tx.Model(models.FoodsPreferential{}).Where("id IN ?", ids).Delete(&models.FoodsPreferential{}).Error; err != nil {
		tx.Rollback()
		return false, "يۇيىۋېتىش مەغلۇپ بولدى "
	} else {
		tx.Commit()
		return true, ""
	}

}

// RestaurantHasSpecFoods
//
//  @Author: Rixat
//  @Date: 2025-07-17 15:00:00
//  @Description: 判断餐厅是否存在规格美食
//  @receiver fps
//  @param restaurantID
//  @return bool
func (fps *FoodsPreferentialService) RestaurantHasSpecFoods(restaurantID int) bool {
	var specCount int64
	tools.Db.Model(models.RestaurantFoods{}).Where("food_type = 1 and restaurant_id = ?",restaurantID).Count(&specCount)
	return specCount > 0
}