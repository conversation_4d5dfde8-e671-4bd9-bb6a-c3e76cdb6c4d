package services

import (
	"errors"
	"fmt"
	"math"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources"
	"mulazim-api/tools"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/ivpusic/grpool"
)

type RestaurantService struct {
	langUtil *lang.LangUtil
	language string
}

// GetOpenRestaurantsByAreaId 获取区域开启的餐厅
// @param areaId int 区域ID
// @param keyword string 关键字
// @param state int 状态 -1:全部 0:关闭 1:开启, 2: 暂停
// @param page int 页码 如等于0 则不分页
// @param limit int 每页数量 默认： 10
func (r *RestaurantService) GetOpenRestaurantsByAreaId(areaId int) []models.Restaurant {
	var restaurants []models.Restaurant
	tools.GetDB().
		Model(&models.Restaurant{}).
		Scopes(models.ActiveRestaurant()).
		Where("area_id = ?", areaId).
		Find(&restaurants)

	return restaurants
}

// GetOpenRestaurantsByAreaIdPaginate 获取区域开启的餐厅
// @param areaId int 区域ID
// @param keyword string 关键字
// @param state int 状态 -1:全部 0:关闭 1:开启, 2: 暂停
// @param page int 页码 如等于0 则不分页
// @param limit int 每页数量 默认： 10
func (r *RestaurantService) GetOpenRestaurantsByAreaIdPaginate(areaId int, keyword string, state int, page int, limit int, templateId int,openStates string) ([]models.Restaurant, int64) {
	var (
		restaurants []models.Restaurant
		total       int64
	)

	query := tools.GetDB().
		Model(&models.Restaurant{}).
		//Scopes(models.ActiveRestaurant()).
		Where("area_id = ?", areaId)
	if templateId > 0 {
		var resIds []int
		tools.GetDB().Model(&models.MarketingGroupTemplateAttendance{}).Where("template_id = ?", templateId).Pluck("restaurant_id", &resIds)
		query.Where("id in ?", resIds)
	}
	if 0 < len(keyword) {
		query.Where("(name_zh like ? or name_ug like ?)", "%"+keyword+"%", "%"+keyword+"%")
	}
	if len(openStates) == 0 {
		if -1 != state {
			query.Where("state = ?", state)
		}
	}else{
		states := tools.StringToIntArr(openStates,",")
		if len(states) > 0{
			query.Where("state in (?)", states)
		}else{
			query.Where("state = ?", 1)
		}
	}
	query.Count(&total)
	if page > 0 {
		if limit == 0 {
			limit = 10
		}
		query.Offset((page - 1) * limit).Limit(limit)
	}

	query.Find(&restaurants)

	return restaurants, total
}

// GetRestaurantsByIds 获取餐厅
func (r *RestaurantService) GetRestaurantsByIds(ids []int) []models.Restaurant {
	var restaurants []models.Restaurant
	if len(ids) == 0 {
		return restaurants
	}
	tools.GetDB().
		Model(&models.Restaurant{}).
		Scopes(models.ActiveRestaurant()).
		Where("id in (?)", ids).
		Find(&restaurants)
	return restaurants
}

func (r *RestaurantService) GetAreaRestaurantByIds(areaId int, ids []int) []models.Restaurant {
	var restaurants []models.Restaurant
	if len(ids) == 0 {
		return restaurants
	}
	tools.GetDB().
		Model(&models.Restaurant{}).
		Scopes(models.ActiveRestaurant()).
		Where("area_id = ?", areaId).
		Where("id in (?)", ids).
		Find(&restaurants)
	return restaurants
}

func (r *RestaurantService) GetAreaValidRestaurantByIds(areaId int, ids []int) []models.Restaurant {
	var restaurants []models.Restaurant
	if len(ids) == 0 {
		return restaurants
	}
	tools.GetDB().
		Model(&models.Restaurant{}).
		Scopes(models.ValidRestaurant()).
		Where("area_id = ?", areaId).
		Where("id in (?)", ids).
		Find(&restaurants)
	return restaurants
}

var mutex sync.Mutex
var counter = 0
//阶梯 与 距离数据 
type StepDistance struct {
	StepNumber int
	Index int 
	ShippingTime int
	FixedShippingPrice uint
	MinShippingPrice uint
	RestaurantId int
	Distance uint
	BuildingId int
	Lat string
	Lng string
}
//批量创建 配送区域
func (r *RestaurantService) CreateShippingArea(ids string,mode int,steps []resources.BuildingShippingAreaSteps) (map[string]interface{},error) {

	
	result :=make(map[string]interface{})
	result["success"]=false
	result["key"]=""
	
	//检查高德地图配置
	if len(configs.MyApp.AMapKey) == 0 {
		return result,errors.New("amap_key_error")
	}

	maxDistance :=0
	for k, st := range steps {
		if st.FixedShippingPrice == 0 && st.MinShippingPrice == 0 {
			return result,errors.New("shipping_area_steps_error")
		}
		if k > 0 {
			if steps[k-1].End < st.Start {
				return result,errors.New("shipping_area_steps_error")
			}
		}
		maxDistance=st.End
		
	}

	if maxDistance <= 0 {
		 
		return result,errors.New("shipping_area_steps_error")
	}

	//判断平台 高峰期 时间 早上 ( 8 ~ 10)  中午 (12 ~ 16) 晚上 (18 ~ 21) 
	if  tools.IsRushHourTime() {
		return result,errors.New("rush_hour_can_not_operate")
	}

	

	var restaurants []models.Restaurant
	db :=tools.GetDB()
	db.Model(&models.Restaurant{}).
		Where("id in ?",tools.StringToIntArr(ids,",")).
		Find(&restaurants)

	redisAreaId :=0
	for _, v := range restaurants {
		redisAreaId = v.AreaID
		break;
	}
	redisKey := fmt.Sprintf("c_s_a_%d",redisAreaId)
	ret := tools.RedisLock(redisKey, 5*60) //5分钟内只发送一次
	if !ret {
		return result,fmt.Errorf(fmt.Sprintf(r.langUtil.T("retry_after_minute"), 5))
	}	

	type Buildings struct {
		
		Id int 
		Distance float64 
		Lat float64 
		Lng float64
	}
	var buildingsMap []Buildings

	//1.根据 条件搜选出 本地区的  符合条件的全部地址 
	//2.根据 条件 计算出 时间 
	//3.加入到指定店铺的 配送区域中
	
	redisProcessKey := "c_s_a_"+ids
	
	dataResultKey :="c_s_a_"+ids+"_count"

	tools.RedisSetValue(dataResultKey,0,5*60)

	ret2 := tools.RedisLock(redisProcessKey, 5*60) //5分钟内只发送一次
	if !ret2 {
		return result,fmt.Errorf(fmt.Sprintf(r.langUtil.T("retry_after_minute"), 5))
	}	
	result["success"]=true
	result["key"]=redisProcessKey
	go func ()  {
		defer 	func() {
			if err := recover(); err != nil {
				tools.Logger.Error("批量设置店铺配送区域失败",err)
			}
			tools.RedisUnlock(redisKey)
			tools.RedisUnlock(redisProcessKey)
		}()
		meanRadius := tools.GetGeoDistanceMeters("meters")
		for _, res := range restaurants {
			var buildingSelected  []int 
			//已分配的地址
			db.Model(&models.RestaurantBuilding{}).Where("restaurant_id = ?",res.ID).Pluck("building_id",&buildingSelected)
			lng := tools.ToString(res.Lng)
			lat := tools.ToString(res.Lat)

			maxLat := tools.ToString(res.Lat + tools.Rad2Deg(float64(maxDistance)/meanRadius))
			minLat := tools.ToString(res.Lat - tools.Rad2Deg(float64(maxDistance)/meanRadius))
			
			maxLng := tools.ToString(res.Lng + tools.Rad2Deg(float64(maxDistance)/meanRadius/math.Cos(tools.Deg2Rad(res.Lat))))
			minLng := tools.ToString(res.Lng - tools.Rad2Deg(float64(maxDistance)/meanRadius/math.Cos(tools.Deg2Rad(res.Lat))))

			latColumn :="t_building.lat"
			lngColumn :="t_building.lng"
					
			//符合条件的最大距离以内的所有地址 
			sql :=""
			
			
			originStrArr :=make([]string, 0)
			index :=0
			switch(mode){
				case 1://未分配的地址 
					if len(buildingSelected) > 0 {			
						sql =` select 	*,  ( `+tools.ToString(meanRadius)+` * acos( cos( radians(`+lat+`) ) * cos( radians( `+latColumn+` ) ) * cos( radians( `+lngColumn+` ) - radians(`+lng+`) ) + sin( radians(`+lat+`) ) * sin( radians( `+latColumn+` ) ) ) ) AS distance
					from (Select t_building.* From t_building
							LEFT JOIN b_street  ON t_building.street_id = b_street.id Where b_street.area_id = `+tools.ToString(res.AreaID)+` 
							AND t_building.lat Between `+minLat+` And `+maxLat+` 
							And t_building.lng Between `+minLng+` And `+maxLng+`
							AND  t_building.id not in (`+tools.IntArrToString(buildingSelected,",")+`)
							AND b_street.deleted_at is null
							AND t_building.deleted_at is null
						)
						As t_building 
						having distance <= `+ tools.ToString(maxDistance)+` order by distance asc `
					}else{//全部
						sql =` select 	*,  ( `+tools.ToString(meanRadius)+` * acos( cos( radians(`+lat+`) ) * cos( radians( `+latColumn+` ) ) * cos( radians( `+lngColumn+` ) - radians(`+lng+`) ) + sin( radians(`+lat+`) ) * sin( radians( `+latColumn+` ) ) ) ) AS distance
						from (Select t_building.* From t_building
							LEFT JOIN b_street  ON t_building.street_id = b_street.id Where b_street.area_id = `+tools.ToString(res.AreaID)+` 
							AND t_building.lat Between `+minLat+` And `+maxLat+` 
							And t_building.lng Between `+minLng+` And `+maxLng+`
							AND b_street.deleted_at is null
							AND t_building.deleted_at is null
						)
						As t_building 
						having distance <= `+ tools.ToString(maxDistance)+` order by distance asc `
					}
					
				case 2://已分配的地址 
					if len(buildingSelected) > 0 {
						sql =` select 	*,  ( `+tools.ToString(meanRadius)+` * acos( cos( radians(`+lat+`) ) * cos( radians( `+latColumn+` ) ) * cos( radians( `+lngColumn+` ) - radians(`+lng+`) ) + sin( radians(`+lat+`) ) * sin( radians( `+latColumn+` ) ) ) ) AS distance
					from (Select t_building.* From t_building
							LEFT JOIN b_street  ON t_building.street_id = b_street.id Where b_street.area_id = `+tools.ToString(res.AreaID)+` 
							AND t_building.lat Between `+minLat+` And `+maxLat+` 
							And t_building.lng Between `+minLng+` And `+maxLng+`
							AND  t_building.id  in (`+tools.IntArrToString(buildingSelected,",")+`)
							AND b_street.deleted_at is null
							AND t_building.deleted_at is null
						)
						As t_building 
						having distance <= `+ tools.ToString(maxDistance)+` order by distance asc `
							
					}
				case 3,4://全部地址 
					sql =` select 	*,  ( `+tools.ToString(meanRadius)+` * acos( cos( radians(`+lat+`) ) * cos( radians( `+latColumn+` ) ) * cos( radians( `+lngColumn+` ) - radians(`+lng+`) ) + sin( radians(`+lat+`) ) * sin( radians( `+latColumn+` ) ) ) ) AS distance
					from (Select t_building.* From t_building
							LEFT JOIN b_street  ON t_building.street_id = b_street.id Where b_street.area_id = `+tools.ToString(res.AreaID)+` 
							AND t_building.lat Between `+minLat+` And `+maxLat+` 
							And t_building.lng Between `+minLng+` And `+maxLng+`
							AND b_street.deleted_at is null
							AND t_building.deleted_at is null
						)
						As t_building 
						having distance <= `+ tools.ToString(maxDistance)+` order by distance asc `
						

						
			}
			if len(sql) == 0 {
				break
			}
			db.Raw(sql).Scan(&buildingsMap)	
			tools.Logger.Info("批量设置店铺配送区域开始 res_id:",res.ID)

			var stepDistance []StepDistance
				
			// massOriginsStr = ""
			stepDistance = []StepDistance{}
			for k, bb := range buildingsMap {

					
				//需从高德获取实际时间  批量处理
				originStrArr = append(originStrArr,tools.ToString(bb.Lng)+","+tools.ToString(bb.Lat))
				stepDistance = append(stepDistance,StepDistance{
					// StepNumber:num,
					Index:k,
					// ShippingTime:step.ShippingTime,
					// FixedShippingPrice:uint(step.FixedShippingPrice),
					// MinShippingPrice:uint(step.MinShippingPrice),
					RestaurantId:res.ID,
					Distance:uint(bb.Distance),
					BuildingId:bb.Id,
					Lat:tools.ToString(bb.Lat),
					Lng: tools.ToString(bb.Lng),
				})
				index++
					
				
			}

			
			index = 0	
			isRiding :=false //是否取 骑行数据
			tools.Logger.Info("批量设置店铺配送区域处理中 res_id:",res.ID,",楼栋数据数量:",len(stepDistance))
			if len(originStrArr) > 0 {
				var wg sync.WaitGroup
				pool := grpool.NewPool(8, 8)
				defer pool.Release()
				arrSize :=100
				//拆分
				originStrAtt2 :=tools.ChunkStringArray(originStrArr,arrSize)
				for k, oo := range originStrAtt2 {
					wg.Add(1)
					jj := k
					ooCopy := oo
					pool.JobQueue <- func() {
						defer func() {
							if err := recover(); err != nil {
								tools.Logger.Error("批量设置店铺配送区域处理中"," 错误:",err)
								//出现错误可以继续重试
								tools.RedisUnlock(redisKey)
								tools.RedisUnlock(redisProcessKey)
							}
							
						}()
						r.GetDataFromAMap(jj,ooCopy,isRiding,stepDistance,arrSize,res)
						wg.Done()
					}

				}
				wg.Wait()
				var resBuildingArr []models.RestaurantBuilding
				dataCount :=0
				for _, stepD := range stepDistance { //批量处理 
					if stepD.Distance > uint(maxDistance) {
						//tools.Logger.Info("批量设置店铺配送区域处理中"," 数据插入 [超过最大距离] 跳过:阶梯最大距离:",maxDistance,",当前距离:",stepD.Distance,",res_id:",res.ID,"building_id:",stepD.BuildingId)
						continue
					}
					if mode > 1 { //已分配的或全部地址 需要查询
						
						resBuildingCt :=int64(0)
						db.Model(&models.RestaurantBuilding{}).Where("restaurant_id =? and building_id = ?",res.ID,stepD.BuildingId).Count(&resBuildingCt)
						if resBuildingCt == 0 {//不存在
							
							for _, st := range steps {
								if stepD.Distance >= uint(st.Start) && stepD.Distance <= uint(st.End) { //符合条件的 数据
									//直接录入指定的数据
									resBuilding :=models.RestaurantBuilding{
										RestaurantID:res.ID,
										BuildingID:stepD.BuildingId,
										Distance:uint(stepD.Distance),
										Time:uint(stepD.ShippingTime),
										State: 1,
									}
									resBuilding.FixedShipment = uint(st.FixedShippingPrice)
									resBuilding.Shipment=uint(st.MinShippingPrice)
									if st.ShippingTime > 0 {
										resBuilding.Time = uint(st.ShippingTime)
									}
									resBuildingArr = append(resBuildingArr, resBuilding)
									dataCount++
									
									// db.Model(&models.RestaurantBuilding{}).Create(&resBuilding)
									
									break
								}	
							}
							
							
						}else{//存在数据
							
							for _, st := range steps {
								if stepD.Distance >= uint(st.Start) && stepD.Distance <= uint(st.End) { //符合条件的 数据			
									updateMap :=make(map[string]interface{})
									updateMap["distance"]=uint(stepD.Distance)
									updateMap["time"]=uint(stepD.ShippingTime)
									updateMap["state"]=1
									updateMap["fixed_shipment"]=uint(st.FixedShippingPrice)
									updateMap["shipment"]=uint(st.MinShippingPrice)
									if st.ShippingTime > 0 {
										updateMap["time"]=uint(st.ShippingTime)
									}
									db.Model(&models.RestaurantBuilding{}).Where("restaurant_id =? and building_id = ?",res.ID,stepD.BuildingId).Updates(&updateMap)
									dataCount++
									break
								}	
							}
							
						}
					}else{ //未分配的地址 可以直接插入
						for _, st := range steps {
							if stepD.Distance >= uint(st.Start) && stepD.Distance <= uint(st.End) { //符合条件的 数据
								//直接录入指定的数据
								resBuilding :=models.RestaurantBuilding{
									RestaurantID:res.ID,
									BuildingID:stepD.BuildingId,
									Distance:uint(stepD.Distance),
									Time:uint(stepD.ShippingTime),
									State: 1,
								}
								resBuilding.FixedShipment = uint(st.FixedShippingPrice)
								resBuilding.Shipment=uint(st.MinShippingPrice)
								if st.ShippingTime > 0 {
									resBuilding.Time = uint(st.ShippingTime)
								}
								resBuildingArr = append(resBuildingArr, resBuilding)
								dataCount++
								// db.Model(&models.RestaurantBuilding{}).Create(&resBuilding)
								
								break
							}	
						}
					}	
				}
				tools.Logger.Info("批量设置店铺配送区域处理中 res_id:",res.ID,",符合条件的的数据数量:",dataCount)
				tools.RedisSetValue(dataResultKey,dataCount,5*60)
				tools.Logger.Info("批量设置店铺配送区域处理中 res_id:",res.ID,",符合条件的要插入的数据数量:",len(resBuildingArr))
				if len(resBuildingArr) > 0 {
					tools.Logger.Info("批量设置店铺配送区域处理中"," 数据插入开始:",len(resBuildingArr),",res_lat:"+tools.ToString(res.Lat),",res_lng:",tools.ToString(res.Lng))
					// 批量插入，每批100个
					db.CreateInBatches(&resBuildingArr, 100)
					tools.Logger.Info("批量设置店铺配送区域处理中"," 数据插入结束:",len(resBuildingArr),",res_lat:"+tools.ToString(res.Lat),",res_lng:",tools.ToString(res.Lng))
				}
				
			}
				
			
			tools.Logger.Info("批量设置店铺配送区域结束 res_id:",res.ID)
		}
	}()


	return result,nil
}
//从高德获取数据
func (r *RestaurantService) GetDataFromAMap(jj int,oo []string,isRiding bool,stepDistance []StepDistance,arrSize int,res models.Restaurant){

	ridingTime :=0.5 //骑行时间 按步行时间的 1/2 来计算
	//参数生成
	massOriginsStr1 :=strings.Join(oo,"|")
	//取步行距离
	if(!isRiding){
		massOrigins, _ := tools.GetDistanceFromAMapMassWalking(massOriginsStr1,tools.ToString(res.Lat),tools.ToString(res.Lng))
		// tools.Logger.Info("批量设置店铺配送区域开始"," 高德回复数据数量:",len(massOrigins.Results),",res_lat:"+tools.ToString(res.Lat),",res_lng:",tools.ToString(res.Lng))
		if len(massOrigins.Results) > 0 {//能获取批量数据
			for kk, vv := range massOrigins.Results {
				arrIndex :=(jj*arrSize)+kk
				if len(stepDistance) > arrIndex {
					if tools.ToInt(vv.Duration) == 0 {	//没有回复数据 一个一个取
						tools.Logger.Info("批量设置店铺配送区域开始"," 高德批量回复没有数据，要一个一个取,",stepDistance[arrIndex].Lat,stepDistance[arrIndex].Lng,tools.ToString(res.Lat),tools.ToString(res.Lng))
						origins, _ := tools.GetDistanceFromAMap(stepDistance[arrIndex].Lat,stepDistance[arrIndex].Lng,tools.ToString(res.Lat),tools.ToString(res.Lng))
						if len(origins.Route.Paths) > 0 {
							if stepDistance[arrIndex].ShippingTime == 0 {
								du :=tools.ToInt(origins.Route.Paths[0].Duration)
								tm :=0
								if du > 0 && du < 60 {
									tm  = 1
								}else{
									tm =du/60
								}
								if tm > 0 {
									tm = int(float64(tm) * ridingTime)
									if tm < 1 {
										tm = 1
									}
								}
								stepDistance[arrIndex].ShippingTime = tm
							}
							stepDistance[arrIndex].Distance = uint(tools.ToInt(origins.Route.Paths[0].Distance))
						}else{//数据获取失败
							tools.Logger.Info("数据出现错误,单个获取,jj:",jj,",arrIndex:",arrIndex)
						}
					}else{
						if stepDistance[arrIndex].ShippingTime == 0 {
							du :=tools.ToInt(vv.Duration)
							tm :=0
							if du > 0 && du < 60 {
								tm  = 1
							}else{
								tm =du/60
							}
							if tm > 0 {
								tm = int(float64(tm) * ridingTime)
								if tm < 1 {
									tm = 1
								}
							}
							stepDistance[arrIndex].ShippingTime = tm
						}
						stepDistance[arrIndex].Distance = uint(tools.ToInt(vv.Distance))

					}
				}

				if stepDistance[arrIndex].ShippingTime == 0 {
					//数据出现错误 
					tools.Logger.Info("数据出现错误,jj:",jj,",arrIndex:",arrIndex,fmt.Sprintf(",result:%v",vv))
				}
			}
		}else{//没有回复数据 一个一个取
			for kk, _ := range oo {
				arrIndex :=(jj*arrSize)+kk
				if len(stepDistance) > arrIndex {
					tools.Logger.Info("批量设置店铺配送区域开始"," 高德批量回复没有数据，要一个一个取,",stepDistance[arrIndex].Lat,stepDistance[arrIndex].Lng,tools.ToString(res.Lat),tools.ToString(res.Lng))
					origins, _ := tools.GetDistanceFromAMap(stepDistance[arrIndex].Lat,stepDistance[arrIndex].Lng,tools.ToString(res.Lat),tools.ToString(res.Lng))
					if len(origins.Route.Paths) > 0 {
						if stepDistance[arrIndex].ShippingTime == 0 {
							du :=tools.ToInt(origins.Route.Paths[0].Duration)
							tm :=0
							if du > 0 && du < 60 {
								tm  = 1
							}else{
								tm =du/60
							}
							if tm > 0 {
								tm = int(float64(tm) * ridingTime)
								if tm < 1 {
									tm = 1
								}
							}
							stepDistance[arrIndex].ShippingTime = tm
						}
						stepDistance[arrIndex].Distance = uint(tools.ToInt(origins.Route.Paths[0].Distance))
					}else{//数据获取失败
						tools.Logger.Info("数据出现错误,jj:",jj,",arrIndex:",arrIndex,fmt.Sprintf(",result:%v",origins))
					}
				}
				if stepDistance[arrIndex].ShippingTime == 0 {
					//数据出现错误 
					tools.Logger.Info("数据出现错误,单个获取,jj:",jj,",arrIndex:",arrIndex)
				}
			}
		}
	}else{ //取骑行数据
		for kk, origin := range oo {
			ridingDistance,_:=tools.GetDistanceFromAMapMassRiding(origin,tools.ToString(res.Lat),tools.ToString(res.Lng))
			arrIndex :=(jj*arrSize)+kk
			if len(stepDistance) > arrIndex {
				if len(ridingDistance.Route.Paths) > 0 {
					if stepDistance[arrIndex].ShippingTime == 0 {
						du :=tools.ToInt(ridingDistance.Route.Paths[0].Duration)
							tm :=0
							if du > 0 && du < 60 {
								tm  = 1
							}else{
								tm =du/60
							}
							if tm > 0 {
								tm = int(float64(tm) * ridingTime)
								if tm < 1 {
									tm = 1
								}
							}
							stepDistance[arrIndex].ShippingTime = tm
					}
					stepDistance[arrIndex].Distance = uint(tools.ToInt(ridingDistance.Route.Paths[0].Distance))
				}else{//数据获取失败

				}
			}

		}

	}
}


//批量创建 配送区域 是否完成
func (r *RestaurantService) CreateShippingAreaCheck(key string) map[string]interface{} {
	result :=make(map[string]interface{})
	success :=false
	count :=0 
	if tools.RedisGetValue(key) == "" {
		success = true
		count = tools.ToInt(tools.RedisGetValue(key+"_count"))
		tools.RedisDel2(key+"_count")
	}
	result["success"] = success
	result["count"] = count
	return result
}

// 按 ID 获取餐厅数据
func (svc *RestaurantService) GetByID(restaurantID int) (*models.Restaurant, error) {
	var (
		restaurant models.Restaurant
		db         = tools.GetDB()
	)
	if err := db.Where("id = ?", restaurantID).Preload("Area").Find(&restaurant).Error; err != nil {
		return nil, err
	}
	return &restaurant, nil
}

func NewRestaurantService(c *gin.Context) *RestaurantService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	collection := RestaurantService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &collection
}