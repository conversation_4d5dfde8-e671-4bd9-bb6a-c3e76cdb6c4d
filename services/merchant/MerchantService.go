package merchant

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"mulazim-api/configs"
	errors2 "mulazim-api/errors"
	"mulazim-api/factory"
	"mulazim-api/jobs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/models/Comments"
	Other "mulazim-api/models/other"
	"mulazim-api/requests/marketingRequest"
	"mulazim-api/requests/merchantRequest/food"
	"mulazim-api/resources/merchant"
	"mulazim-api/services"
	"mulazim-api/tools"

	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	merchantTransformer "mulazim-api/transformers/merchant"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"github.com/silenceper/wechat/v2/miniprogram/qrcode"
	"gorm.io/gorm"
)

type MerchantService struct {
	langUtil *lang.LangUtil
	language string
	services.BaseService
}

// NewMerchantService
//
//	@Description: 初始化商家端service
//	@param c
//	@return *MerchantService
func NewMerchantService(c *gin.Context) *MerchantService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	merchantTransformer := MerchantService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &merchantTransformer
}
func UploadFile() {

}

// GetRestaurant
//
//	@Description: 获取餐厅信息
//	@receiver s
//	@param restaurantId
//	@return models.Restaurant
func (s *MerchantService) GetRestaurant(restaurantId string) models.Restaurant {
	db := tools.GetDB()
	var restaurant models.Restaurant
	db.Where("id=?", restaurantId).First(&restaurant)
	return restaurant
}

// GetArea
//
//	@Description: 获取区域信息
//	@receiver s
//	@param id
//	@return models.Area
func (s *MerchantService) GetArea(id string) models.Area {
	db := tools.GetDB()
	var area models.Area
	db.Where("id=?", id).First(&area)
	return area
}

// GetNewOrders
//
//	@Description: 获取新订单
//	@author: Alimjan
//	@Time: 2022-10-14 17:45:55
//	@receiver s *MerchantService
//	@param restaurantId string 餐厅ID
//	@param receiveOrderTimeLimit int 当前区域实时订单接单时限
//	@param c *gin.Context gin 环境
//	@return []models.OrderToday
func (s *MerchantService) GetNewOrders(restaurantId string, receiveOrderTimeLimit int, orderType string, c *gin.Context) []models.OrderToday {
	db := tools.GetDB()
	var orderToday []models.OrderToday
	currentTimeFormat := carbon.Now("Asia/Shanghai").AddMinutes(15).Format("Y-m-d H:i:s")

	dbQuery := db.
		Select("id,serial_number,order_id,timezone,booking_time,pay_time,print_time,price,original_price,pay_type,user_id,name,category_id,building_id,original_shipment,shipment,lunch_box_fee,store_id,mobile,order_address,send_notify,order_type,state,description,created_at,consume_type,shipper_id,delivery_type,res_profit as res_income,actual_paid,total_discount_amount,order_price_res").
		Where("store_id=?", restaurantId).
		Where("state = ?", models.ORDER_STATE_WAITING_ACCEPT)
	//Where("(`print_time`<=?) OR (`print_time`>=? AND `send_notify`=1) OR `order_type`=1", currentTimeFormat, currentTimeFormat)
	// 根据 order_type 过滤订单
	switch orderType {
	case "0": // 预订单
		dbQuery = dbQuery.Where("`print_time` > ? AND (`print_time` < ? OR send_notify != 1) AND `order_type` = 0", currentTimeFormat, currentTimeFormat)

	default: // 所有订单（默认）
		dbQuery = dbQuery.Where("(`print_time`<=?) OR (`print_time`>=? AND `send_notify`=1) OR `order_type`=1", currentTimeFormat, currentTimeFormat)
	}

	dbQuery.
		Preload("OrderDetail.RestaurantFoods", func(db *gorm.DB) *gorm.DB {
			return db.Select("id,name_ug,name_zh,image")
		}).
		Preload("OrderDetail.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取美食已选规格数据
		Preload("OrderDetail.RestaurantFoods.ComboFoodItems.RestaurantFood"). // 获取套餐中的美食数据
		Preload("OrderDetail.RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取套餐中的已选规格数据

		Preload("AddressView", func(db *gorm.DB) *gorm.DB {
			return db.Select("address_view.city_name_" + s.language + " as city_name," +
				"address_view.area_name_" + s.language + " as area_name," +
				"address_view.building_name_" + s.language + " as building_name," +
				"address_view.street_name_" + s.language + " as street_name, " +
				"address_view.building_id as building_id")
		}).
		Preload("LunchBox", func(db *gorm.DB) *gorm.DB {
			return db.Select(`	
	0 AS food_id,
	b_lunch_box.unit_price AS original_price,
	b_lunch_box.unit_price AS price,
	sum( t_order_detail.lunch_box_count ) AS count,
	b_lunch_box.name_` + s.language + ` AS name,
	t_order_detail.order_id `).Joins("INNER JOIN b_lunch_box ON t_order_detail.lunch_box_id = b_lunch_box.id ").
				Where("t_order_detail.deleted_at is null").
				Group("t_order_detail.order_id,t_order_detail.lunch_box_id")
		}).
		Preload("MarketingList", func(db *gorm.DB) *gorm.DB {
			return db.Select(`
					type ,
		            name_` + s.language + ` AS name,
					step_reduce ,id,order_id`)
		}).
		Preload("Coupon", func(db *gorm.DB) *gorm.DB {
			return db.Select(`
			t_coupon.id,
			t_coupon_log.order_id,
			t_coupon.name_` + s.language + ` AS name,
			t_coupon.price
			`).Joins("left join t_coupon on t_coupon.id = t_coupon_log.coupon_id")
		}).
		Preload("MiniGameUserLog").
		Preload("OrderExtend").
		Preload("Shipper").
		Preload("Restaurant").
		Preload("OrderPartRefund.OrderPartRefundDetail.LunchBox").
		Preload("OrderPartRefund.RefundReason", func(db *gorm.DB) *gorm.DB {
			return db.Where("type = ?", models.DICTIONARY_TYPE_PART_REFUND_REASON).
				Where("state = ?", models.DictionaryStateOpen)
		}).
		Preload("OrderPartRefund", func(db *gorm.DB) *gorm.DB {
			return db.Order("created_at") // 根据 OrderPartRefund 的创建时间排序
		}).
		Preload("OrderPartRefund.OrderPartRefundDetail.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取美食已选规格数据
		Preload("OrderPartRefund.OrderPartRefundDetail.RestaurantFoods.ComboFoodItems.RestaurantFood"). // 获取套餐中的美食数据
		Preload("OrderPartRefund.OrderPartRefundDetail.RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取套餐中的已选规格数据
		Where("`t_order_today`.`deleted_at` IS NULL ").
		Group("t_order_today.id").
		Order("serial_number desc").
		Order("order_type desc").
		Find(&orderToday)

	for i := 0; i < len(orderToday); i++ {
		var count int64 = 0
		var totalCount int64 = 0
		//  当前用户的历史订单数基本不变，所以制作缓存
		cacheKey := fmt.Sprintf("uoc_%d_%s", orderToday[i].UserID, restaurantId)
		hisCountStr := tools.Remember(c, cacheKey, 1*time.Hour, func() interface{} {
			var hisCount int64 = 0
			db.Model(models.Order{}).Where("store_id = ?", tools.ToInt64(restaurantId)).Where("created_at > ?",carbon.Now().SubMonths(6).ToDateString()).
				Where("user_id = ?", orderToday[i].UserID).Where("state > 2").Count(&hisCount)
			return hisCount
		})
		hisCount, _ := strconv.Atoi(hisCountStr)
		//  当前用户的今日订单数
		db.Model(models.OrderToday{}).Where("store_id = ?", tools.ToInt64(restaurantId)).Where("user_id = ?", orderToday[i].UserID).
			Where("id <= ?", orderToday[i].ID).
			Where("state > 2").Count(&count)
		totalCount = count + int64(hisCount)
		//
		orderToday[i].OrderTime = int(totalCount)
		if orderToday[i].DeliveryType == 2 { //自取订单,要显示客户的手机号
			userMobile := make(map[string]interface{})
			db.Model(models.User{}).Where("id =?", orderToday[i].UserID).Select("mobile").Scan(&userMobile)
			if userMobile["mobile"] != nil {
				orderToday[i].Mobile = tools.ToString(userMobile["mobile"])
			}
		}
		//
		//  删除之前的lunchbox 计算的地方
	}

	return orderToday
}

// GetVoiceOrderCount
//
//	@Description: 获取是否有新的订单需要播放声音
//	@receiver s
//	@param restaurantId 餐厅ID
//	@param businessType
//	@param t
//	@return []models.RestaurantVoiceNotify
func (s *MerchantService) GetVoiceOrderCount(restaurantId string, businessType int, t *merchant.NewOrderEntity) []int {
	db := tools.GetDB()
	var now = carbon.Now("Asia/Shanghai").ToDateTimeString()
	orderIdList := new([]int)
	db.Table("t_restaurant_voice_notify as v").
		Select("v.id").
		Joins("LEFT JOIN t_order_today o ON o.id = v.order_id ").
		Where("v.restaurant_id = ?", restaurantId).
		Where("? BETWEEN v.query_notice_begin and v.query_notice_end", now).
		Where("((o.state=3 AND v.state=0) OR (o.state=4 AND v.query_time<=1))").
		Pluck("name", &orderIdList)
	return *orderIdList
}

// GetOrderCountForHeader
//
//	@Description:  获取新订单，已接收订单数量，失败订单，已完成订单，
//	@receiver s
//	@param restaurantId 餐厅ID
//	@param businessType
//	@param t
func (s *MerchantService) GetOrderCountForHeader(restaurantId string, businessType int, t *merchant.NewOrderEntity) {
	db := tools.GetDB()

	var count int64
	now := carbon.Now("Asia/Shanghai").AddMinutes(15).ToDateTimeString()
	// db.Debug().Model(&models.OrderToday{}).
	// 	Where("store_id = ?", restaurantId).
	// 	Where("state = ?", 3).
	// 	Where("((`print_time` <= ? ) or (`print_time` >= ? and `send_notify` = 1)) ", now, now).
	// 	Where("`t_order_today`.`deleted_at` is null").Count(&count)
	// t.NewOrderCount.NewOrderCount = int(count)

	//查询实时订单和预订单数量start
	var orderCount merchant.RealAndPreOrderCount
	// 执行查询并将结果映射到结构体中
	db.Raw(`
		SELECT 
			SUM(IF((print_time <= ? ) OR (print_time >= ? AND send_notify = 1) OR order_type=1, 1, 0)) AS new_order_count,
			SUM(IF(print_time > ? AND (print_time < ? OR send_notify != 1) AND order_type = 0, 1, 0)) AS pre_order_count
		FROM 
			t_order_today
		WHERE 
			store_id = ? 
			AND state = 3 
			AND t_order_today.deleted_at IS NULL
	`, now, now, now, now, restaurantId).Scan(&orderCount)

	// 将查询结果赋值给 NewOrderCount
	t.NewOrderCount.NewOrderCount = int(orderCount.NewOrderCount)
	t.NewOrderCount.PreOrderCount = int(orderCount.PreOrderCount)
	//查询实时订单和预订单数量end

	//select count(*) as aggregate from `t_order_today` where
	//`store_id` = "721" and `state` in (4, 5)bcom.almas.manager
	//and `t_order_today`.`deleted_at` is null
	db.Model(&models.OrderToday{}).
		Where("store_id = ?", restaurantId).
		Where("state in (?,?)", 4, 5).
		Where("`t_order_today`.`deleted_at` is null").Count(&count)

	t.NewOrderCount.ReceivedOrderCount = int(count)

	db.Model(&models.OrderToday{}).
		Where("store_id = ?", restaurantId).
		Where("state in (?,?,?)", 6, 7, 10).
		Where("`t_order_today`.`deleted_at` is null").Count(&count)

	t.NewOrderCount.FinishedOrderCount = int(count)

	db.Model(&models.OrderToday{}).
		Where("store_id = ?", restaurantId).
		Where("state in (?,?)", 8, 9).
		Where("`t_order_today`.`deleted_at` is null").Count(&count)

	t.NewOrderCount.CanceledOrderCount = int(count)

}

func (s *MerchantService) UpdateResLoginTimeAndOrderTime(restaurantId string, deviceToken string, notifications []int) {

	go func() {
		db := tools.GetDB()
		if len(notifications) > 0 {
			db.Exec("UPDATE t_restaurant_voice_notify SET query_last_time=?,query_time=query_time+1 WHERE id IN (?)", carbon.Now().ToDateTimeString(), notifications)
		}
		var lastLogin models.RestaurantLastLogin
		restaurantId, _ := strconv.Atoi(restaurantId)
		db.Model(models.RestaurantLastLogin{}).Where("restaurant_id = ?", restaurantId).First(&lastLogin)
		lastLogin.RestaurantID = restaurantId
		lastLogin.DeviceToken = deviceToken
		var t = carbon.Now().Carbon2Time()
		lastLogin.LastQueryTime = &t

		if lastLogin.ID == 0 {
			db.Create(&lastLogin)
		} else {
			db.Save(lastLogin)
		}
	}()
}

// RestaurantPersonal
//
//	@Description: 获取商家端信息
//	@receiver s
//	@param restaurantId
//	@return models.Restaurant
func (s *MerchantService) RestaurantPersonal(restaurantId string) models.Restaurant {
	db := tools.GetDB()

	var restaurant models.Restaurant
	db.Model(models.Restaurant{}).Select("id,logo,state,last_comment_readed,name_"+s.language+" as name").Where("id = ?", restaurantId).First(&restaurant)
	if restaurant.LastCommentReaded.IsZero() {
		now := carbon.Now("Asia/Shanghai")
		db.Model(&models.Restaurant{}).Where("id = ?", restaurantId).Update("last_comment_readed", now.ToDateTimeString())
		restaurant.LastCommentReaded = now.Carbon2Time()
	}
	cashPlatformCheck := int64(0)
	chkErr := db.Table("t_restaurant").Where("id = ? and  cash_platform = ?  and deleted_at is null", restaurantId, 2).Count(&cashPlatformCheck).Error
	if chkErr == nil {
		if cashPlatformCheck > 0 {
			restaurant.CashPlatform = 2
		} else {
			restaurant.CashPlatform = 1
		}
	} else {
		restaurant.CashPlatform = 1
	}

	return restaurant
}

// GetCommentCountByResIdAndTime
//
//	@Description: 获取评论数，先记录上次读取时间，按照时间获取之后的评论
//	@receiver s
//	@param result
//	@return int64
func (s *MerchantService) GetCommentCountByResIdAndTime(result models.Restaurant) int64 {
	db := tools.GetDB()
	var count int64
	db.Model(Comments.Comment{}).Joins("LEFT JOIN `t_restaurant_foods` ON `t_restaurant_foods`.`id` = `t_comment`.`food_id` ").
		Where("`t_comment`.`type` = 2 ").
		Where("`t_comment`.`state` = 1  ").
		Where("`t_restaurant_foods`.`restaurant_id` = ?  ", result.ID).
		Where("`t_comment`.`created_at` > ?", result.LastCommentReaded.Format("2006-01-02 15:04:05")).
		Where("`t_comment`.`deleted_at` IS NULL").Count(&count)
	return count
}

// LoginCheck
//
//	@Description: 商家登录验证
//	@author: Alimjan
//	@Time: 2022-09-23 17:17:09
//	@receiver s *MerchantService
//	@param name string
//	@param password string
//	@param id string
//	@param secret string
//	@param grantType string
//	@param number string
//	@return bool
//	@return models.Admin
//	@return string
func (s *MerchantService) LoginCheck(name string, password string, id string, secret string, grantType string, number string) (bool, models.Admin, string) {
	db := tools.Db
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Printf("登录日志 error: %s\n", err)
			}
		}()
		tools.Log("商家登录参数 用户名:" + name + ",密码:" + password)
	}()
	var admin models.Admin
	db.Model(admin).Where("name = ?", name).
		Where("type in (5,6)").Where("state = 1").
		Where("deleted_at IS NULL").First(&admin)
	if admin.ID == 0 {
		db.Model(admin).Where("mobile = ?", name).
			Where("type in (5,6)").Where("state = 1").
			Where("deleted_at IS NULL").First(&admin)
		if admin.ID == 0 {
			return false, admin, "admin_is_not_active"
		}
	}
	attemptedHashValue := tools.PasswordToHash(password)
	if attemptedHashValue == admin.Password {
		return true, admin, ""
	}
	return false, admin, "admin_password_error"

}

// GenerateToken
//
//	@Description: 生成token 并把token 存储
//	@author: Alimjan
//	@Time: 2022-09-23 17:16:29
//	@receiver shipper ShipperService
//	@param admin models.Admin
//	@param clientId string
//	@param searialNumber string
//	@return map[string]interface{}
func (s *MerchantService) GenerateToken(admin models.Admin, clientId string, searialNumber string) map[string]interface{} {
	accessToken := tools.RandStr(40)
	var db = tools.Db
	var oldSessionIds []int
	db.Table("oauth_sessions").
		Where("oauth_sessions.owner_id = ? ", admin.ID).
		Where("oauth_sessions.owner_type = 'merchant' ").Pluck("oauth_sessions.id", &oldSessionIds)

	db.Table("oauth_access_tokens").
		Where("oauth_access_tokens.session_id in ? ", oldSessionIds).
		Delete(models.OauthAccessTokens{})

	db.Table("oauth_sessions").
		Where("oauth_sessions.owner_id = ? ", admin.ID).
		Where("oauth_sessions.owner_type = 'merchant' ").Delete(models.OauthSessions{})

	db.Model(&admin).Updates(map[string]interface{}{
		"login_time":     carbon.Now("Asia/Shanghai").ToDateTimeString(),
		"searial_number": searialNumber,
	})
	sessions := models.OauthSessions{
		ClientID:          clientId,
		OwnerID:           fmt.Sprintf("%d", admin.ID),
		OwnerType:         "merchant",
		ClientRedirectUri: "",
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}
	db.Create(&sessions)
	tokens := models.OauthAccessTokens{
		ID:         accessToken,
		SessionID:  sessions.ID,
		ExpireTime: int(carbon.Now().AddYears(1).Timestamp()),
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}
	db.Create(&tokens)

	return map[string]interface{}{
		"access_token": accessToken,
	}

}

// GetRestaurantByAdmin
//
//	@Description: 获取餐厅代理 区域信息
//	@author: Alimjan
//	@Time: 2022-09-24 17:42:18
//	@receiver s *MerchantService
//	@param c *gin.Context
//	@param admin models.Admin
//	@return resInfo map[string]interface{}
//	@return dealerInfo map[string]interface{}
//	@return areaInfo map[string]interface{}
func (s *MerchantService) GetRestaurantByAdmin(c *gin.Context, admin models.Admin) (resInfo map[string]interface{}, dealerInfo map[string]interface{}, areaInfo map[string]interface{}) {
	var db = tools.Db
	language, _ := c.Get("lang")
	//var resInfo map[string]interface{}
	langTag := language.(string)
	resDB := db.Model(models.Restaurant{}).Select("t_restaurant.id,"+
		"t_restaurant.city_id,"+
		"t_restaurant.area_id,"+
		"t_restaurant.logo,"+
		"t_restaurant.type,"+
		"t_restaurant.state,"+
		"t_restaurant.name_"+langTag+" as name").
		Joins("INNER JOIN b_admin_store ON t_restaurant.id = b_admin_store.store_id").
		Joins("LEFT JOIN t_admin ON t_admin.id = b_admin_store.admin_id").
		Where("b_admin_store.admin_id=?", admin.ID).
		Where("t_admin.type in (5,6)"). //商家端登录 角色 5:店铺管理员 6:店铺副管理员
		Where("t_restaurant.deleted_at IS NULL")

	resDB.First(&resInfo)

	if resInfo == nil {
		tools.Logger.Error("店铺信息不存在 管理员id:", admin.ID)
		return nil, nil, nil
	}

	resCount := int64(1)
	//该管理员账户下的店铺数量
	resDB.Count(&resCount)
	resInfo["res_count"] = resCount
	if resCount > 1 {
		var resinfos []models.Restaurant
		resDB.Find(&resinfos)
		resNames := "["
		for _, v := range resinfos {
			resNames += v.Name + ","
		}
		resNames = resNames[:len(resNames)-1] + "]"
		resInfo["res_names"] = resNames
	}

	db.Model(models.Admin{}).Select("type,real_name,mobile").
		Joins("INNER JOIN b_admin_store ON t_admin.id = b_admin_store.admin_id ").
		Where("b_admin_store.store_id = ? ", resInfo["id"]).
		Where("state = 1").
		Where("type IN ( '1', '2', '3' ) ").
		Where("t_admin.deleted_at IS NULL").Order("type desc").First(&dealerInfo)

	db.Model(models.Area{}).
		Where("id  = ? ", resInfo["area_id"]).
		Where("b_area.deleted_at IS NULL").
		First(&areaInfo)
	return resInfo, dealerInfo, areaInfo
}

// GetResByAdmin 获取商家信息
func (s *MerchantService) GetResByAdmin(admin models.Admin) (models.Restaurant, error) {
	if admin.Type != 5 && admin.Type != 6 {
		return models.Restaurant{}, errors.New("not_fund_merchant")
	}
	var res models.Restaurant
	resDB := tools.GetDB().Model(models.Restaurant{}).Select(
		"t_restaurant.*").
		Joins("INNER JOIN b_admin_store ON t_restaurant.id = b_admin_store.store_id").
		Joins("LEFT JOIN t_admin ON t_admin.id = b_admin_store.admin_id").
		Where("b_admin_store.admin_id=?", admin.ID).
		Where("t_admin.type in (5,6)"). //商家端登录 角色 5:店铺管理员 6:店铺副管理员
		Where("t_restaurant.deleted_at IS NULL")

	rs := resDB.First(&res)
	if rs.RowsAffected < 1 {
		return models.Restaurant{}, errors.New("not_fund_merchant")
	}
	return res, nil
}

// ValidateSmartAppCodeParams
//
//	@Description: 生成采集餐厅二维码前进行商户密码验证
//	@author: Captain
//	@Time: 2022-11-03 16:00:08
//	@receiver s *MerchantService
//	@param adminID int 餐厅管理员编号
//	@param password string 餐厅管理员密码
//	@param restaurantId int 餐厅编号
//	@param roleId int 角色编号
//	@return bool 验证通过时返回true,验证不通过时返回false
//	@return string 验证通过时返回空字符串,验证不通过时返回错误信息国际化字段
func (s *MerchantService) ValidateSmartAppCodeParams(adminID int, password string, restaurantId int, roleId int) (bool, string) {
	var db = tools.Db
	var merchantAdmin map[string]interface{}
	var restaurant map[string]interface{}
	db.Model(models.Restaurant{}).Select("id").Where("id = ?", restaurantId).First(&restaurant)
	//fmt.Println(restaurant)
	db.Model(models.Admin{}).Select("id", "password").Where("id = ?", adminID).First(&merchantAdmin)
	if merchantAdmin == nil {
		return false, "merchant_id_not_found"
	} else if merchantAdmin != nil {
		//fmt.Println(merchantAdmin["password"])
		attemptedHashValue := tools.PasswordToHash(password)
		//fmt.Println(attemptedHashValue)

		if merchantAdmin["password"] != attemptedHashValue {
			return false, "merchant_password_error"
		} else if roleId < 1 || roleId > 3 { //验证角色
			return false, "merchant_role_id_error"

		} else if restaurant == nil {
			return false, "merchant_not_found"
		}
	}
	return true, ""
}

// GetQrCode
//
//	@Description: 生成采集餐厅二维码(小程序码)
//	@author: Captain
//	@Time: 2022-11-03 10:10:21
//	@receiver s *MerchantService
//	@param c *gin.Context
//	@param restaurantId int 餐厅编号
//	@param roleId int	要绑定的员工角色（1.管理员、2.订单管理员、3.收款人）
//	@param lang string	要绑定操作的语言
//	@return bool	操作成功返回true，否则返回false
//	@return string 操作成功时返回二维码url，失败时返回错误提示或空字符串
func (s *MerchantService) GetQrCode(c *gin.Context, restaurantId int, roleId int, lang string) (bool, string) {
	db := tools.GetDB()
	expireAt := carbon.Now("Asia/Shanghai").AddMinutes(15).Format("Y-m-d H:i:s")
	var langId int
	if lang == "ug" {
		langId = 1
	} else {
		langId = 2
	}
	rs, qrCode := saveQrCode(c, restaurantId, roleId, langId, expireAt)
	//fmt.Println(rs, qrCode)
	if rs {
		wechatPayConfig := *configs.NewWechatPayConfig("wechat_mini")
		updates := map[string]interface{}{}
		var merchantStuff models.MerchantStuff
		db.Model(models.MerchantStuff{}).Where("restaurant_id=?", restaurantId).Where("stuff_type = ?", roleId).Where("app_id = ?", wechatPayConfig.AppID).First(&merchantStuff)
		//fmt.Println(merchantStuff)
		if merchantStuff.ID != 0 {
			updates["state"] = 0
			updates["session_key"] = nil
			updates["openid"] = nil
			updates["unionid"] = nil
			updates["user_id"] = nil
			updates["mobile"] = nil
			updates["password"] = nil
			updates["real_name"] = nil
			updates["id_number"] = nil
			updates["updated_at"] = carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s")
			db.Model(&merchantStuff).Updates(updates)
		} else {
			var restaurant models.Restaurant
			err := db.Model(models.Restaurant{}).Where("id = ?", restaurantId).First(&restaurant).Error
			if err != nil {
				return false, err.Error()
			}
			merchantStuff.CityID = restaurant.CityID
			merchantStuff.AreaID = restaurant.AreaID
			merchantStuff.StreetID = restaurant.StreetID
			merchantStuff.RestaurantID = restaurantId
			merchantStuff.StuffType = roleId
			merchantStuff.AppID = wechatPayConfig.AppID
			merchantStuff.State = 0
			err = db.Create(&merchantStuff).Error
			if err != nil {
				return false, err.Error()
			}
			return true, qrCode
		}
		return true, qrCode
	}
	return false, "generate_merchant_qrCode_failed"
}

// saveQrCode
//
//	@Description: 生成餐厅员工绑定二维码，并保存到服务端
//	@author: Captain
//	@Time: 2022-11-03 10:23:53
//	@param c *gin.Context
//	@param restaurantId int 餐厅编号
//	@param roleId int   要绑定的员工角色（1.管理员、2.订单管理员、3.收款人）
//	@param langId int 语言编号
//	@param expireAt string 二维码过期时间（暂时不用）
//	@return bool 操作成功返回true，否则返回false
//	@return string 操作成功时返回二维码url，失败时返回错误提示或空字符串
func saveQrCode(c *gin.Context, restaurantId int, roleId int, langId int, expireAt string) (bool, string) {
	fileName := strconv.Itoa(restaurantId) + "_" + strconv.Itoa(roleId) + ".jpg"
	filePath := "upload/merchant-stuff/qrcode/" + fileName
	fullPath := configs.MyApp.UploadRootDir + filePath
	fileDir := filepath.Dir(fullPath)
	rs, err := tools.PathExists(fileDir)
	if err != nil {
		return false, err.Error()
	}
	if !rs {
		err := os.MkdirAll(fileDir, os.ModePerm)
		if err != nil {
			return false, err.Error()
		}
	}
	var coderParams qrcode.QRCoder
	coderParams.Path = "pages/merchant/merchantStaff"
	coderParams.Scene = "lang=" + strconv.Itoa(langId) + "&resId=" + strconv.Itoa(restaurantId) + "&role=" + strconv.Itoa(roleId)
	coderParams.Width = 430
	_, err1 := tools.GetWXMiniQrCodeForMerchant(c, coderParams, fullPath)
	if err1 != nil {
		//fmt.Println(err1)
		return false, err1.Error()
	}
	return true, filePath
}

// GetNoMontionList
//
//	@Description: 获取商家已提现列表
//	@author: Alimjan
//	@Time: 2023-01-16 12:20:29
//	@receiver s *MerchantService
//	@param restaurantId int
//	@return bool
//	@return int64
//	@return []map[string]interface{}
func (s *MerchantService) GetNoMontionList(restaurantId int) (bool, int64, []map[string]interface{}) {
	db := tools.GetDB()
	var noMotionList = make([]map[string]interface{}, 0)
	err := db.Model(models.Mulazimpaytoresagent{}).Select("id, trans_date, res_income").Where("res_id = ?", restaurantId).Where("payment_state = 0").Order("trans_date asc").Scan(&noMotionList).Error
	if err != nil {
		//fmt.Println(err)
		return false, 0, noMotionList
	}
	if len(noMotionList) < 1 {
		return true, 0, noMotionList
	}
	var totalAmount int64
	err = db.Model(models.Mulazimpaytoresagent{}).Select("SUM(res_income) as totalAmount").Where("res_id = ?", restaurantId).Where("payment_state = 0").Scan(&totalAmount).Error
	if err != nil {
		//fmt.Println(err)
		return false, 0, noMotionList
	}
	return true, totalAmount, noMotionList
}

/***
 * @Author: [rozimamat]
 * @description: 判断提现 是否 走 拉卡拉通道
 * @Date: 2023-07-27 11:45:53
 * @param {int} restaurantId
 */
func (s *MerchantService) IsLakalaCashOut(restaurantId int) bool {
	db := tools.GetDB()

	cashPlatformCheck := int64(0)
	chkErr := db.Table("t_restaurant").Where("id = ? and  cash_platform = ? and deleted_at is null", restaurantId, 2).Count(&cashPlatformCheck).Error
	if chkErr == nil {
		if cashPlatformCheck > 0 {
			return true
		}
	} else {
		tools.Logger.Error("走拉卡拉通道检查错误提示", chkErr)
	}
	return false
}


// GetRestaurantByAdmin1
//
//	@Time 2022-11-01 17:52:43
//	<AUTHOR>
//	@Description: 获取admin的餐厅信息
//	@receiver s *MerchantService
//	@param c
//	@param admin
func (s *MerchantService) GetRestaurantByAdmin1(admin models.Admin) (restaurant models.Restaurant) {
	db := tools.GetDB()
	var restaurantInfo = models.Restaurant{}
	db.Model(admin).
		Select("t_restaurant.*").
		Joins("RIGHT JOIN b_admin_store ON b_admin_store.admin_id =t_admin.id").
		Joins("LEFT JOIN t_restaurant ON t_restaurant.id = b_admin_store.store_id").
		Where("t_admin.id = ?", admin.ID).
		Where("t_restaurant.state in ?", []int{1, 2, 3}).
		Where("t_restaurant.deleted_at is null").
		First(&restaurantInfo)
	return restaurantInfo
}

// GetRestaurantOrderListByState
//
//	@Time 2022-09-23 16:22:26
//	<AUTHOR>
//	@Description: 根据餐厅和订单状态获取订单列表
//	@receiver s *MerchantService
//	@param id
//	@param ints
func (s *MerchantService) GetRestaurantOrderListByState(c *gin.Context, restaurantId string, orderStates []int) []models.OrderToday {
	db := tools.GetDB()
	var results []models.OrderToday
	db.Model(models.OrderToday{}).
		Select("id, serial_number, order_id, timezone, booking_time, price, original_price, pay_type,consume_type, user_id, name, category_id, building_id, shipment,lunch_box_fee, store_id, mobile, order_address, send_notify,refund_chanel, state, description,order_type, created_at,pay_time,shipper_id,delivery_type,res_profit as res_income,actual_paid,total_discount_amount,order_price_res").

		Where("store_id = ?", restaurantId).
		Where("state IN ?", orderStates).
		Preload("OrderDetail.RestaurantFoods", func(db *gorm.DB) *gorm.DB { // OrderDetail 需要拿到 food_type / spec_id，所以改成获取所有字段了
			return db.Select("id,name_ug,name_zh,image")
		}).
		Preload("OrderDetail.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取美食已选规格数据
		Preload("OrderDetail.RestaurantFoods.ComboFoodItems.RestaurantFood"). // 获取套餐中的美食数据
		Preload("OrderDetail.RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取套餐中的已选规格数据
		Preload("LunchBox", func(db *gorm.DB) *gorm.DB {
			return db.Select(`	
	0 AS food_id,
	b_lunch_box.unit_price AS original_price,
	b_lunch_box.unit_price AS price,
	sum( t_order_detail.lunch_box_count ) AS count,
	b_lunch_box.name_` + s.language + ` AS name,
	t_order_detail.order_id `).Joins("INNER JOIN b_lunch_box ON t_order_detail.lunch_box_id = b_lunch_box.id ").
				Where("t_order_detail.deleted_at is null").
				Group("t_order_detail.order_id,t_order_detail.lunch_box_id")
		}).
		Preload("MarketingList", func(db *gorm.DB) *gorm.DB {
			return db.Select(`
					type ,
		            name_` + s.language + ` AS name,
					step_reduce ,id,order_id`)
		}).
		Preload("Coupon", func(db *gorm.DB) *gorm.DB {
			return db.Select(`
			t_coupon.id,
			t_coupon_log.order_id,
			t_coupon.name_` + s.language + ` AS name,
			t_coupon.price
			`).Joins("left join t_coupon on t_coupon.id = t_coupon_log.coupon_id")
		}).
		Preload("MiniGameUserLog").
		Preload("OrderExtend").
		Preload("AddressView").
		Preload("Shipper").
		Preload("Restaurant").
		Preload("OrderPartRefund.OrderPartRefundDetail.LunchBox").
		Preload("OrderPartRefund.RefundReason", func(db *gorm.DB) *gorm.DB {
			return db.Where("type = ?", models.DICTIONARY_TYPE_PART_REFUND_REASON).
				Where("state = ?", models.DictionaryStateOpen)
		}).
		Preload("OrderPartRefund", func(db *gorm.DB) *gorm.DB {
			return db.Order("created_at") // 根据 OrderPartRefund 的创建时间排序
		}).
		Preload("OrderPartRefund.OrderPartRefundDetail.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取美食已选规格数据
		Preload("OrderPartRefund.OrderPartRefundDetail.RestaurantFoods.ComboFoodItems.RestaurantFood"). // 获取套餐中的美食数据
		Preload("OrderPartRefund.OrderPartRefundDetail.RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取套餐中的已选规格数据
		Group("t_order_today.id").
		Order("serial_number desc").
		Find(&results)

	//for k := range results {
	//	var his_count int64
	//	var today_count int64
	//	db.Model(models.Order{}).Where("user_id", results[k].UserID).Where("store_id", results[k].StoreID).Where("state > ?", 2).Count(&his_count)
	//	db.Model(models.OrderToday{}).Where("user_id", results[k].UserID).Where("store_id", results[k].StoreID).Where("id <= ?", results[k].ID).Where("state > ?", 2).Count(&today_count)
	//	results[k].OrderTime = int(his_count + today_count)
	//
	//	db.Model(models.OrderDetail{}).Select("0 as food_id,b_lunch_box.unit_price as original_price,b_lunch_box.unit_price as price,sum(t_order_detail.lunch_box_count) as count,b_lunch_box.name_"+language+" as name").
	//		Joins("join b_lunch_box on t_order_detail.lunch_box_id = b_lunch_box.id").
	//		Group("t_order_detail.lunch_box_id").
	//		Where("t_order_detail.order_id = ?", results[k].ID).
	//		Find(&results[k].LunchBoxDetail)
	//
	//}
	for i := 0; i < len(results); i++ {
		var totalCount int64 = 0
		//  当前用户的历史订单数基本不变，所以制作缓存
		cacheKey := fmt.Sprintf("uoc_%d_%s", results[i].UserID, restaurantId)
		hisCountStr := tools.Remember(c, cacheKey, 8*time.Hour, func() interface{} {
			var hisCount int64 = 0
			db.Model(models.Order{}).Where("store_id = ?", restaurantId).
				Where("user_id = ?", results[i].UserID).Where("state > 2").Count(&hisCount)
			return hisCount
		})
		hisCount, _ := strconv.Atoi(hisCountStr)
		//  当前用户的今日订单数
		cacheTodayKey := fmt.Sprintf("tuoc_%d_%s_%d", results[i].UserID, restaurantId,results[i].ID)

		todayCountStr := tools.Remember(c, cacheTodayKey, 8*time.Hour, func() interface{} {
			var todayCount int64 = 0
			db.Model(models.OrderToday{}).Where("store_id = ?", restaurantId).Where("user_id = ?", results[i].UserID).
				Where("id <= ?", results[i].ID).
				Where("state > 2").Count(&todayCount)
			return todayCount
		})
		todayCount, _ := strconv.Atoi(todayCountStr)
		totalCount = int64(todayCount) + int64(hisCount)
		//
		results[i].OrderTime = int(totalCount)
		if results[i].DeliveryType == 2 { //自取订单,要显示客户的手机号
			userMobile := make(map[string]interface{})
			db.Model(models.User{}).Where("id =?", results[i].UserID).Select("mobile").Scan(&userMobile)
			if userMobile["mobile"] != nil {
				results[i].Mobile = tools.ToString(userMobile["mobile"])
			}
		}
		//
		//  删除之前的lunchbox 计算的地方
	}
	return results
}

// BusinessStatistics
//
//	@Time 2022-09-30 12:37:26
//	<AUTHOR>
//	@Description: 商家客户端统计
//	@receiver merchant *MerchantService
//	@param restaurantId
//	@param startDate
//	@param endDate
func (s *MerchantService) BusinessStatistics(restaurantId string, startDate string, endDate string) merchant.BusinessStatistics {

	startDate = strings.Trim(startDate, " ")
	endDate = strings.Trim(endDate, " ")

	var totalInfo []merchant.TotalInfo
	var byPayType []merchant.ByPayType
	var foodSales []merchant.FoodSales
	var lunchBox []merchant.FoodSales
	// db.Raw("CALL restaurant_business_overview(?,?,?)", restaurantId, startDate, endDate).
	// 	Scan(&totalInfo)
	s.GetRestaurantBusinessOverview(restaurantId, startDate, endDate).
		Scan(&totalInfo)

	// db.Raw("CALL restaurant_business_by_payType(?,?,?,?)", restaurantId, startDate, endDate, 1).
	// 	Scan(&byPayType)
	lang := 1
	if s.language == "zh" {
		lang = 2
	}
	s.GetRestaurantBusinessByPayType(restaurantId, startDate, endDate, lang).
		Scan(&byPayType)

	// db.Raw("CALL food_sales_by_restaurant(?,?,?,?)", restaurantId, startDate, endDate, 1).
	// 	Scan(&foodSales)
	s.GetFoodSalesByRestaurant(restaurantId, startDate, endDate, lang).
		Scan(&foodSales)

	// db.Raw("CALL lunch_box_sales_by_restaurant(?,?,?,?)", restaurantId, startDate, endDate, 1).
	// 	Scan(&lunchBox)
	s.GetLunchBoxSalesByRestaurant(restaurantId, startDate, endDate, lang).
		Scan(&lunchBox)
	var food_sales = append(foodSales, lunchBox...)

	var data merchant.BusinessStatistics
	data.Total = totalInfo
	data.ByPayType = byPayType
	data.FoodSales = food_sales
	return data

}

// SearchCategory
//
//	@Time 2022-10-01 23:51:24
//	<AUTHOR>
//	@Description: 分类搜索
//	@receiver s *MerchantService
//	@param keyword
func (s *MerchantService) SearchCategory(c *gin.Context, keyword string,resID int)[]map[string]interface{} {
	db := tools.GetDB()
	var groupList []models.FoodsGroup
	query := db.Model(models.FoodsGroup{}).Where("restaurant_id =?",resID).Where("state = 1 and review_state = 2").Order("weight")
	if len(keyword) > 0 {
		query = query.Where("name_ug like ? or name_zh like ?", "%"+keyword+"%","%"+keyword+"%")
	}
	query.Find(&groupList)
	
	result := make([]map[string]interface{}, 0)
	for _,group := range groupList {
		groupMap := make(map[string]interface{})
		groupMap["id"] = group.ID
		groupMap["name"] = tools.GetNameByLang(group, s.language)
		groupMap["name_ug"] = group.NameUg
		groupMap["name_zh"] = group.NameZh
		groupMap["image"] = ""
		groupMap["type"] = group.ID
		groupMap["description"] = group.ID
		result = append(result, groupMap)
	}
	return result
}

// FoodsCategoryList
//
//	@Time 2022-10-01 23:51:39
//	<AUTHOR>
//	@Description: 美食分类列表
//	@receiver s *MerchantService
func (s *MerchantService) FoodsCategoryList() []map[string]interface{} {
	db := tools.GetDB()
	//var AllFoods []models.BAllFoods
	var AllFoods []map[string]interface{}
	db.Model(models.AllFoods{}).Select("id, type, name_" + s.language + " as name ,name_ug,name_zh, description, image").Find(&AllFoods)
	for i := range AllFoods {
		i2 := AllFoods[i]["image"]
		AllFoods[i]["image"] = configs.MyApp.CdnUrl + fmt.Sprintf("%v", i2)
	}
	if len(AllFoods) == 0 {
		return AllFoods
	}
	return AllFoods
}


// GetRestaurantStuff
//
//	@Time 2022-10-03 15:33:17
//	<AUTHOR>
//	@Description: 获取餐厅员工绑定信息
//	@receiver s *MerchantService
//	@param id
func (s *MerchantService) GetRestaurantStuff(restaurantId string) []map[string]interface{} {
	db := tools.GetDB()
	var merchantStuff []map[string]interface{}
	db.Table("t_merchant_stuff").
		Select("stuff_type, mobile").
		Where("restaurant_id = ?", restaurantId).
		Find(&merchantStuff)
	return merchantStuff
}

// DeleteReply
//
//	@Time 2022-10-03 18:48:22
//	<AUTHOR>
//	@Description: 删除商家评论
//	@receiver s *MerchantService
//	@param id
func (s *MerchantService) DeleteReply(id string) {
	db := tools.GetDB()
	db.Exec("update t_comment_reply set deleted_at = now() where id = ? and type = ?", id, 1)
}

// UpdateCommentTime
//
//	@Time 2022-10-03 22:30:21
//	<AUTHOR>
//	@Description: 更新商家阅读评论时间
//	@receiver s *MerchantService
//	@param id
func (s *MerchantService) UpdateCommentTime(id string) {
	db := tools.GetDB()
	db.Exec("UPDATE t_restaurant SET last_comment_readed = ? WHERE id = ?", carbon.Now().ToDateTimeString(), id)
}

// UpdateFoodState
//
//	@Time 2022-12-15 10:48:02
//	<AUTHOR>
//	@Description: 更新美食状态
//	@receiver s *MerchantService
//	@param id
//	@param restaurantId
//	@return interface{}
func (s *MerchantService) UpdateFoodState(id string, restaurantId int) (bool,string, interface{}) {
	db := tools.GetDB()
	var food models.RestaurantFoods
	db.Model(food).Where("id = ? ", id).First(&food)
	if food.ID == 0 {
		return false, "foods.no_foods",nil
	}
	if restaurantId != food.RestaurantID {
		return false, "forbidden",nil
	}
	if food.State == 3 || food.State == 0 {
		return false, "food_state_error",nil
	}
	state := tools.If(food.State == 1, 2, 1)
	err := db.Model(&food).Where("id = ?", id).Update("state", state).Error
	if err == nil {
		m := make(map[string]interface{})
		m["state"] = food.State
		return true,"msg", m
	}
	return false, "failed",nil
}

// CloseFoodState
//
//	@Time 2024-8-14 15:51:02
//	<AUTHOR>
//	@Description: 将美食的state设为0
//	@receiver s *MerchantService
//	@param id
//	@param restaurantId
//	@return interface{}
func (s *MerchantService) ChangeFoodState(id string, restaurantId int,foodState int) (bool,string, interface{}) {
	db := tools.GetDB()
	var food models.RestaurantFoods
	db.Model(food).Where("id = ? ", id).Find(&food)
	if food.ID == 0 {
		return false, "foods.no_foods",nil
	}
	if restaurantId != food.RestaurantID {
		return false, "forbidden",nil
	}
	// if food.State ==0{
	// 	return false,"food_state_error_0",nil
	// }
	if food.State == 3 {
		return false, "food_state_under_review",nil
	}

	// if food.State == 0 {
	// 	foodState = 3
	// }

	oldFoodMap := make(map[string]interface{})
	db.Model(&models.RestaurantFoods{}).Where("id =?", id).Find(&oldFoodMap)

	err := db.Model(&models.RestaurantFoods{}).Where("id = ?", id).Update("state", foodState).Error
	if err == nil {
		m := make(map[string]interface{})
		m["state"] = foodState
		return true,"msg", m
	}

	return false, "failed",nil
}

// UpdateRestaurantState
//
//	@Time 2022-12-15 19:02:33
//	<AUTHOR>
//	@Description: 个人中心页面 ， 更新商家状态
//	@receiver s *MerchantService
//	@param restaurantId
//	@return interface{}
//	@return interface{}
func (s *MerchantService) UpdateRestaurantState(restaurantId string) (bool, map[string]interface{}) {
	db := tools.GetDB()
	var restaurant models.Restaurant
	db.Model(&restaurant).Where("id = ? ", restaurantId).First(&restaurant)

	if restaurant.ID == 0 {
		return false, make(map[string]interface{})
	}

	//状态（0关闭，1开通，2休息）
	if restaurant.State == models.RESTAURANTFOODS_STATE_CLOSE {
		m := make(map[string]interface{})
		m["state"] = restaurant.State
		return true, m
	}
	if restaurant.State ==  models.RESTAURANTFOODS_STATE_OPEN {
		var orders []models.OrderToday
		db.Model(orders).Where("store_id = ? ", restaurantId).
			Where("state IN ?", []int{
				//3, 4, 5,
				models.ORDER_STATE_WAITING_ACCEPT,
				models.ORDER_STATE_ACCEPTED,
				models.ORDER_STATE_READY,
			}).
			Find(&orders)
		if len(orders) > 0 {
			m := make(map[string]interface{})
			m["state"] = -1
			return true, m
		}
	}
	if restaurant.State == models.RESTAURANTFOODS_STATE_SOLDOUT {
		//判断是否拉卡拉入住 select * from t_self_sign_merchant_info_archive where lakala_verify_state = 2  and state = 10
		var selfSignMerchantInfoArchive models.SelfSignMerchantInfoArchive
		db.Model(&models.SelfSignMerchantInfoArchive{}).
		Where("lakala_verify_state = 2  and state = 10").
		Where("restaurant_id = ?", restaurantId).
		Find(&selfSignMerchantInfoArchive)
		if selfSignMerchantInfoArchive.RestaurantId == 0 {
			m := make(map[string]interface{})
			m["state"] = -2
			return true, m
		}
	}

	restaurant.State = tools.If(restaurant.State ==  models.RESTAURANTFOODS_STATE_SOLDOUT, models.RESTAURANTFOODS_STATE_OPEN, models.RESTAURANTFOODS_STATE_SOLDOUT)
	err := db.Model(&models.Restaurant{}).Where("id = ? ",restaurantId).Update("state", restaurant.State).Error
	if err == nil {
		m := make(map[string]interface{})
		m["state"] = restaurant.State
		return true, m
	}
	return false, make(map[string]interface{})
}

// RestaurantCategory
//
//	@Time 2022-12-15 22:24:34
//	<AUTHOR>
//	@Description: 商家分类
//	@receiver s *MerchantService
//	@return interface{}
func (s *MerchantService) RestaurantCategory() ([]map[string]interface{}, error) {
	db := tools.GetDB()
	var category []models.Category
	err := db.Model(category).Where("parent_id = ?", 1).Find(&category).Error
	m := make([]map[string]interface{}, len(category))
	for i, v := range category {
		m[i] = make(map[string]interface{})
		m[i]["id"] = v.ID
		m[i]["name"] = tools.GetNameByLang(v, s.language)
		m[i]["icon"] = v.Icon
		m[i]["parent_id"] = v.ParentID
		m[i]["rgb"] = v.Rgb
		m[i]["state"] = v.State
	}

	return m, err
}

// RestaurantInformation
//
//	@Time 2022-12-17 11:09:57
//	<AUTHOR>
//	@Description: 商家信息
//	@receiver s *MerchantService
//	@param restaurantId
//	@return map[string]interface{}
func (s *MerchantService) RestaurantInformation(restaurantId int) map[string]interface{} {
	db := tools.GetDB()
	restaurant := models.Restaurant{}
	db.Model(models.Restaurant{}).Where("id = ? and deleted_at is null", restaurantId).Find(&restaurant)
	if restaurant.ID == 0 {
		return make(map[string]interface{})
	}
	// 结构体转map param是需要转换的结构体
	data, _ := json.Marshal(&restaurant)
	restaurantMap := make(map[string]interface{})
	json.Unmarshal(data, &restaurantMap)
	restaurantImages := make([]map[string]interface{}, 0)
	db.Model(models.Restaurant{}).Select("b_restaurant_images.*").Joins("inner join b_restaurant_images on b_restaurant_images.restaurant_id = t_restaurant.id").Where("t_restaurant.id = ?", restaurant.ID).Where("b_restaurant_images.deleted_at is null").Scan(&restaurantImages)

	restaurantLicense := make([]map[string]interface{}, 0)
	db.Model(models.Restaurant{}).Select("b_restaurant_license.*").Joins("inner join b_restaurant_license on b_restaurant_license.restaurant_id = t_restaurant.id").Where("t_restaurant.id = ?", restaurant.ID).Where("b_restaurant_license.deleted_at is null").Scan(&restaurantLicense)

	categories := make([]map[string]interface{}, 0)
	db.Model(models.Category{}).Select("b_category.*,t_restaurant.id as t_restaurant_id ").Joins("INNER JOIN t_restaurant_category ON  b_category.id = t_restaurant_category.category_id").
		Joins("INNER JOIN t_restaurant ON  t_restaurant.id = t_restaurant_category.restaurant_id").Where("t_restaurant.id = ?", restaurant.ID).
		Scan(&categories)

	category, _ := s.RestaurantCategory()

	restaurantMap["restaurant_images"] = restaurantImages
	restaurantMap["restaurant_license"] = restaurantLicense
	restaurantMap["restaurant_category"] = categories
	restaurantMap["category"] = category
	restaurantMap["can_self_take"] = restaurant.CanSelfTake
	restaurantMap["restaurant_type"] = restaurant.RestaurantType

	restaurantMap["id"] = restaurant.ID
	restaurantMap["name_ug"] = restaurant.NameUg
	restaurantMap["name_zh"] = restaurant.NameZh
	restaurantMap["description_ug"] = restaurant.DescriptionUg
	restaurantMap["description_zh"] = restaurant.DescriptionZh
	restaurantMap["address_ug"] = restaurant.AddressUg
	restaurantMap["address_zh"] = restaurant.AddressZh
	restaurantMap["open_time"] = restaurant.OpenTime
	restaurantMap["close_time"] = restaurant.CloseTime
	restaurantMap["begin_time"] = restaurant.BeginTime
	restaurantMap["end_time"] = restaurant.EndTime
	restaurantMap["restaurant_logo"] = restaurant.Logo
	restaurantMap["street_id"] = restaurant.StreetID
	restaurantMap["tag"] = restaurant.Tag
	restaurantMap["tel"] = restaurant.Tel
	restaurantMap["tel2"] = restaurant.Tel2
	restaurantMap["tel3"] = restaurant.Tel3
	restaurantMap["tel4"] = restaurant.Tel4
	restaurantMap["tel5"] = restaurant.Tel5
	//美食最早出餐时间和最晚结束出餐时间
	today := time.Now().Format("2006-01-02")
	tomorrow := time.Now().AddDate(0, 0, 1).Format("2006-01-02")
	food_max_and_min_time := make(map[string]interface{})
	db.Model(models.RestaurantFoods{}).Select("min(concat(?,begin_time)) begin_time, max(if(begin_time<end_time,concat(?,end_time), concat(?,end_time))) end_time ", today, today, tomorrow).
		Where("restaurant_id = ?", restaurant.ID).Scan(&food_max_and_min_time)
	food_begin_time := ""
	food_end_time := ""
	if food_max_and_min_time["begin_time"] != nil {
		bgt := food_max_and_min_time["begin_time"].(string)
		bgtime := bgt[:10] + " " + bgt[10:]
		food_begin_time = carbon.Parse(bgtime).Format("H:i")
	}
	if food_max_and_min_time["end_time"] != nil {
		endt := food_max_and_min_time["end_time"].(string)
		endtime := endt[:10] + " " + endt[10:]
		food_end_time = carbon.Parse(endtime).Format("H:i")
	}
	restaurantMap["food_begin_time"] = food_begin_time
	restaurantMap["food_end_time"] = food_end_time
	// 获取上传图片审核状态
	image_verify := [](map[string]interface{}){}
	tools.Db.Table("t_merchant_process").Select("type,state,new_value").Where("restaurant_id", restaurant.ID).Scan(&image_verify)
	restaurantMap["image_verify"] = image_verify
	return restaurantMap
}


// BankCardList
//
//	@Time 2022-12-24 19:19:15
//	<AUTHOR>
//	@Description: 银行卡列表
//	@receiver s *MerchantService
//	@param id
//	@return interface{}
func (s *MerchantService) BankCardList(id string) []map[string]interface{} {
	language := s.language
	db := tools.GetDB()
	result := make([]map[string]interface{}, 0)

	if id == "0" {
		db.Raw("select  id,name_" + language + " as name, logo ,state from b_bank").Scan(&result)
	} else {
		db.Raw("select b.id ,name_"+language+" as name,b.logo from b_finance_info as a, b_bank as b where a.bank_id=b.id and a.restaurant_id=?", id).Scan(&result)
	}
	return result
}

// FinanceInfo
//
//	@Time 2022-12-24 19:21:47
//	<AUTHOR>
//	@Description: 财务信息
//	@receiver s *MerchantService
//	@param id
//	@return interface{}
func (s *MerchantService) FinanceInfo(id string) []map[string]interface{} {
	db := tools.GetDB()
	result := make([]map[string]interface{}, 0)
	db.Raw("select id,restaurant_id,account_name,card_number,account_address,state from b_finance_info where restaurant_id=?", id).Scan(&result)
	return result
}

/***
 * @Author: [rozimamat]
 * @description: 已提现列表
 * @Date: 2023-08-02 13:20:30
 * @param {int} resId
 */
func (s *MerchantService) GetCashoutList(resId int) []models.MerchantCashoutLog {
	db := tools.GetDB()
	//默认查询6个月内的信息
	startTime := carbon.Now("Asia/Shanghai").AddMonths(-6).Format("Y-m-d") + " 00:00:00"
	endTime := carbon.Now("Asia/Shanghai").Format("Y-m-d") + " 23:59:59"
	var merchantCashoutLogs []models.MerchantCashoutLog
	db.Model(&models.MerchantCashoutLog{}).Preload("CashoutDetails").
		Select("wechat_payment_no, id, mobile, cashout_amount, cashout_time").
		Where("restaurant_id = ?", resId).
		Where("deleted_at is null").
		Where("created_at >= ? AND created_at <= ?", startTime, endTime).
		Order("created_at desc").
		Find(&merchantCashoutLogs)

	return merchantCashoutLogs

}

/***
 * @Author: [rozimamat]
 * @description: 已提现列表 拉卡拉和微信
 * @Date: 2023-08-02 13:20:47
 * @param {int} resId
 */
func (s *MerchantService) GetCashoutListLakala(resId int, startTime string, endTime string) ([]models.MerchantCashoutLogLakala, float64, float64, float64) {
	db := tools.GetDB()

	if len(startTime) > 0 {
		startTime = carbon.Parse(startTime, "Asia/Shanghai").Format("Y-m-d") + " 00:00:00"
	}
	if len(endTime) > 0 {
		endTime = carbon.Parse(endTime, "Asia/Shanghai").Format("Y-m-d") + " 23:59:59"
	}

	var merchantCashoutLogs []models.MerchantCashoutLogLakala

	fields := "t_merchant_cashout_log.wechat_payment_no, t_merchant_cashout_log.id,"
	fields += "t_merchant_cashout_log.mobile, t_merchant_cashout_log.cashout_amount, t_merchant_cashout_log.cashout_time,"
	fields += "t_merchant_cashout_log.cash_platform,t_merchant_cashout_log.created_at,5 as state"

	log1 := db.Table("t_merchant_cashout_log").
		Select(fields).
		Where("restaurant_id = ?", resId).
		Where("deleted_at is null")
	if len(startTime) > 0 && len(endTime) > 0 {
		log1.Where("t_merchant_cashout_log.created_at >= ? AND t_merchant_cashout_log.created_at <= ?", startTime, endTime)
	} else {
		//默认查询6个月内的信息
		startTime = carbon.Now("Asia/Shanghai").AddMonths(-6).Format("Y-m-d") + " 00:00:00"
		endTime = carbon.Now("Asia/Shanghai").Format("Y-m-d") + " 23:59:59"
		log1.Where("t_merchant_cashout_log.created_at >= ? AND t_merchant_cashout_log.created_at <= ?", startTime, endTime)
	}
	log1.Where("(t_merchant_cashout_log.cash_platform =1 or  t_merchant_cashout_log.cash_platform is null) ").
		Order("t_merchant_cashout_log.created_at desc").
		Scan(&merchantCashoutLogs)

	var merchantCashoutLogsLakala []models.MerchantCashoutLogLakala
	fields2 := "t_lakala_withdraw_service.id,t_lakala_withdraw_service.detail_json,"
	fields2 += "t_lakala_withdraw_service.amount cashout_amount,t_lakala_withdraw_service.state,"
	fields2 += "t_lakala_withdraw_service.created_at as cashout_time,2 as cash_platform,t_lakala_withdraw.state as withdraw_state,"
	fields2 += "t_lakala_withdraw.created_at as withdraw_time"

	logs2 := db.Table("t_lakala_withdraw_service").
		Joins("left join t_lakala_withdraw on t_lakala_withdraw.service_id = t_lakala_withdraw_service.id ").
		Select(fields2)
	if len(startTime) > 0 && len(endTime) > 0 {
		logs2.Where("t_lakala_withdraw_service.created_at >= ? AND t_lakala_withdraw_service.created_at <= ?", startTime, endTime)
	} else {
		//默认查询6个月内的信息
		startTime = carbon.Now("Asia/Shanghai").AddMonths(-6).Format("Y-m-d") + " 00:00:00"
		endTime = carbon.Now("Asia/Shanghai").Format("Y-m-d") + " 23:59:59"
		logs2.Where("t_lakala_withdraw_service.created_at >= ? AND t_lakala_withdraw_service.created_at <= ?", startTime, endTime)
	}
	logs2.Where("t_lakala_withdraw_service.type = 1 and t_lakala_withdraw_service.opt_type = ? and t_lakala_withdraw_service.opt_id = ?", "merchant", resId).
		Order("t_lakala_withdraw_service.created_at desc").
		Find(&merchantCashoutLogsLakala)

	for k, v := range merchantCashoutLogsLakala {

		if v.State == 5 { //提现成功  时间替换成提现成功时间
			v.CashoutTime = v.WithdrawTime
		}

		detailMap := make(map[string]interface{})
		json.Unmarshal([]byte(v.DetailJson), &detailMap)

		if detailMap["bank_name"] != nil {
			merchantCashoutLogsLakala[k].BankName = tools.ToString(detailMap["bank_name"])
			merchantCashoutLogsLakala[k].CardId = tools.ToString(detailMap["card_id"])
			merchantCashoutLogsLakala[k].CardNo = tools.ToString(detailMap["card_no"])
			merchantCashoutLogsLakala[k].BankLogo = s.GetbankLogo(tools.ToString(detailMap["bank_name"]))
		}

	}
	merchantCashoutLogs = append(merchantCashoutLogsLakala, merchantCashoutLogs...)

	totalAmountMap := make(map[string]interface{})
	db.Model(models.Mulazimpaytoresagent{}).Select("SUM(res_income) totalAmount").Where("res_id = ?", resId).Where("payment_state = 0").Scan(&totalAmountMap)

	totalAmount := float64(0)
	if totalAmountMap != nil && totalAmountMap["totalAmount"] != nil {
		totalAmount = tools.ToFloat64(totalAmountMap["totalAmount"]) / 100
	}

	var totalInfoToday []merchant.TotalInfoStat
	todayStart := tools.Today("Asia/Shanghai").Format("Y-m-d") + " 00:00:00"
	todayNow := tools.Today("Asia/Shanghai").Format("Y-m-d") + " 23:59:59"
	// db.Raw("CALL restaurant_business_overview(?,?,?)", resId, todayStart, todayNow).
	// 	Scan(&totalInfoToday)
	s.GetRestaurantBusinessOverview(tools.ToString(resId), todayStart, todayNow).
		Scan(&totalInfoToday)
	var totalInfoYesterday []merchant.TotalInfoStat
	yesterdayStart := tools.Yesterday("Asia/Shanghai").Format("Y-m-d") + " 00:00:00"
	yesterdayEnd := tools.Yesterday("Asia/Shanghai").Format("Y-m-d") + " 23:59:59"
	// db.Raw("CALL restaurant_business_overview(?,?,?)", resId, yesterdayStart, yesterdayEnd).
	// 	Scan(&totalInfoYesterday)
	s.GetRestaurantBusinessOverview(tools.ToString(resId), yesterdayStart, yesterdayEnd).
		Scan(&totalInfoYesterday)

	todayTotal := tools.ToFloat64(totalInfoToday[0].TotalPrice)
	yesterdayTotal := tools.ToFloat64(totalInfoYesterday[0].TotalPrice)
	return merchantCashoutLogs, totalAmount, todayTotal, yesterdayTotal

}

// LangList
//
//	@Description: 获取客户端语言包
//	@author: Alimjan
//	@Time: 2023-02-09 12:02:00
//	@receiver s *MerchantService
//	@param c *gin.Context
//	@return interface{}
func (s *MerchantService) LangList(c *gin.Context) []map[string]interface{} {
	db := tools.GetDB()
	result := make([]map[string]interface{}, 0)
	db.Table("t_lang_message").Where("terminal_id", 5).
		Where("deleted_at is null").
		Where("state", 1).Select("`key`,value_" + s.language).
		Scan(&result)
	return result
}

/***
 * @Author: [rozimamat]
 * @description: 获取银行logo
 * @Date: 2023-08-02 16:18:59
 * @param {string} bank_name
 */
func (s *MerchantService) GetbankLogo(bank_name string) string {
	db := tools.GetDB()
	bankMap := make(map[string]interface{})
	db.Table("b_self_sign_bank").Where("name_zh like ?", "%"+bank_name+"%").Select("logo").Scan(&bankMap)
	if bankMap["logo"] != nil {
		return configs.MyApp.CdnUrl + tools.ToString(bankMap["logo"])
	}
	return ""
}

// GetShowCustomerInfo
//
//	@Description: 获取是否给商家显示用户信息 redis 缓存
//	@author: Alimjan
//	@Time: 2023-03-01 16:02:09
//	@receiver s *MerchantService
//	@param c *gin.Context
//	@param restaurantId string
//	@return bool
func (s *MerchantService) GetShowCustomerInfo(c *gin.Context, restaurantId string) bool {
	var (
		//  show customer mobile-----
		resShowCustomerKey = "scmb_" + restaurantId
		redisHelper        = tools.GetRedisHelper()
	)

	if exists, _ := redisHelper.Exists(c, resShowCustomerKey).Result(); exists == 0 {
		restaurant := s.GetRestaurant(restaurantId)
		area := s.GetArea(fmt.Sprintf("%d", restaurant.AreaID))
		redisHelper.Set(c, resShowCustomerKey, area.ShowCustomContact, 15*time.Minute)
	}
	resShowCustomer, _ := redisHelper.Get(c, resShowCustomerKey).Int64()

	return resShowCustomer > 0
}

// checkPassword
//
//	@Time 2022-10-03 15:22:42
//	<AUTHOR>
//	@Description: 检查密码是否正确
//	@param currentPassword
//	@param oldPassword
//	@return bool
func checkPassword(currentPassword string, oldPassword string) bool {
	data := []byte(oldPassword)                    //切片
	hashPassword := md5.Sum(data)                  //[16]byte
	md5Password := fmt.Sprintf("%x", hashPassword) //将[]byte转成16进制 string
	if md5Password == currentPassword {
		return true
	} else {
		return false
	}
}

/***
 * @Author: [rozimamat]
 * @description: 完成自取订单
 * @Date: 2023-05-10 19:26:51
 * @param {string} resId
 * @param {string} selfTakeNumber
 */
func (s *MerchantService) SelfTakeOrderReceive(resId string, selfTakeNumber string) (succss bool, err string) {
	db := tools.GetDB()
	var todayOrder models.OrderToday
	db.Where("store_id = ? and self_take_number = ?", resId, selfTakeNumber).
		Where("deleted_at is null").
		Where("state in (4,5,7)").
		Select("id,state").
		First(&todayOrder)
	//1.获取订单 判断状态
	//2.接收订单 修改状态
	//3.写日志
	if todayOrder.ID == 0 {
		return false, "order_not_found"
	} else if todayOrder.State == 7 {
		return false, "order_been_taken"
	}
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	updateMap := make(map[string]interface{})
	updateMap["state"] = 7

	er1 := tx.Table("t_order_today").Where("id = ?", todayOrder.ID).Updates(&updateMap).Error
	if er1 != nil {
		tx.Rollback()
		fmt.Println("自取订单 更新失败", er1.Error())
		return false, "server_error"
	}
	//写入t_order_State_Log
	orderStateLog := models.OrderStateLog{}
	orderStateLog.OrderID = todayOrder.ID
	orderStateLog.OrderStateID = 7 //完成订单
	err2 := tx.Create(&orderStateLog).Error
	if err2 != nil {
		tx.Rollback()
		fmt.Println("自取订单 写入订单日志失败", err2.Error())
		return false, "server_error"
	}
	tx.Commit() // 提交事务
	// 更新自取订单加价活动日志状态
	db.Model(models.PriceMarkupFoodLog{}).Where("order_id =?", todayOrder.ID).Update("state", 3)
	return true, ""
}

/***
 * @Author: [rozimamat]
 * @description: 自取订单获取
 * @Date: 2023-05-18 10:02:38
 * @param {string} resId
 * @param {string} selfTakeNumber
 */
func (s *MerchantService) SelfTakeOrderCheck(resId string, selfTakeNumber string) (order models.OrderToday, succss bool, err string) {
	db := tools.GetDB()
	db.
		Select("id,serial_number,order_id,timezone,booking_time,pay_time,print_time,price,original_price,pay_type,user_id,name,category_id,building_id,shipment,lunch_box_fee,store_id,mobile,order_address,send_notify,order_type,state,description,created_at,consume_type,shipper_id,delivery_type").
		Where("store_id = ? and self_take_number = ?", resId, selfTakeNumber).
		Where("state in (4,5,7)").
		Preload("OrderDetail.RestaurantFoods", func(db *gorm.DB) *gorm.DB {
			return db.Select("id,name_ug,name_zh,image")
		}).
		Preload("AddressView", func(db *gorm.DB) *gorm.DB {
			return db.Select("address_view.city_name_" + s.language + " as city_name," +
				"address_view.area_name_" + s.language + " as area_name," +
				"address_view.building_name_" + s.language + " as building_name," +
				"address_view.street_name_" + s.language + " as street_name, " +
				"address_view.building_id as building_id")
		}).
		Preload("LunchBox", func(db *gorm.DB) *gorm.DB {
			return db.Select(`	
	0 AS food_id,
	b_lunch_box.unit_price AS original_price,
	b_lunch_box.unit_price AS price,
	sum( t_order_detail.lunch_box_count ) AS count,
	b_lunch_box.name_` + s.language + ` AS name,
	t_order_detail.order_id `).Joins("INNER JOIN b_lunch_box ON t_order_detail.lunch_box_id = b_lunch_box.id ").
				Where("t_order_detail.deleted_at is null").
				Group("t_order_detail.order_id,t_order_detail.lunch_box_id")
		}).
		Preload("MarketingList", func(db *gorm.DB) *gorm.DB {
			return db.Select(`
		            name_` + s.language + ` AS name,
					step_reduce ,id,order_id`)
		}).
		Preload("Coupon", func(db *gorm.DB) *gorm.DB {
			return db.Select(`
			t_coupon.id,
			t_coupon_log.order_id,
			t_coupon.name_` + s.language + ` AS name,
			t_coupon.price
			`).Joins("left join t_coupon on t_coupon.id = t_coupon_log.coupon_id")
		}).
		Preload("MiniGameUserLog").
		Preload("Shipper").
		Where("`t_order_today`.`deleted_at` IS NULL ").
		Order("serial_number desc").
		Order("order_type desc").
		Find(&order)
	//1.获取订单 判断状态
	if order.ID == 0 {
		return order, false, "order_not_found"
	} else if order.State == 7 {
		return order, false, "order_been_taken"
	}
	return order, true, ""
}

/* @description: 启动页和首页广告
 * @Date: 2023-05-06 13:54:39
 * @param {string} res_id
 * @param {string} tp
 */
func (s *MerchantService) Adver(res_id string, tp string,admin models.Admin,version int) map[string]interface{} {
	db := tools.GetDB()
	areaId := 0
	var result map[string]interface{}
	//获取区域编号
	if len(res_id) > 0 {
		var res models.Restaurant
		db.Where("id = ?", res_id).Select("id,area_id").First(&res)
		if res.ID > 0 {
			areaId = res.AreaID
		}
	}
	if areaId == 0 {
		return result
	}
	today := carbon.Now("Asia/Shanghai").Format("Y-m-d")

	q :=db.Model(&models.MerchantAdver{}).
		Where("area_id =? and start_time <= ? and end_time >= ?  and state = ?", areaId, today, today, 1)

	minAppVersion := tools.ToInt(s.GetAppConfig("merchant_lottery_alert_min_version"))
	newVersionFlag :=false
	if tp == "3" && (version >0 && minAppVersion > 0 && version >=minAppVersion ){ //抽奖弹窗显示条件
		q=q.Where("type in (3,4)")
		newVersionFlag =true
	}else{
		q=q.Where("type = ?",tp)
	}	
	q.Select("id,type,link_type,link_url,app_id,image_" + s.language + " as image,title_" + s.language + " as title,content_" + s.language + " as content,show_time").
	Scan(&result)
	if newVersionFlag && result !=nil && tools.ToInt(result["type"]) == 4 && admin.Type == 5 {	 //首页弹窗  抽奖活动  只给店铺管理员显示 
		redisHelper := tools.GetRedisHelper()
		var agent models.Admin
		db.Model(&models.Admin{}).Where("admin_area_id = ? and type = ?",areaId,3).Find(&agent)
		contact := agent.Mobile	
		msgId :=tools.ToInt(result["id"])
		typeId :=4

		state := 0
		

		cacheKey := fmt.Sprintf("activity_%d_%d_%d_%d",typeId, msgId,tools.ToInt(res_id),admin.ID)
		cacheKey2 := fmt.Sprintf("activity_%d_%d_%d_%d_read", typeId, msgId,tools.ToInt(res_id),admin.ID)
		//2分钟 能获取一次
		exists, _ := redisHelper.Exists(context.Background(), cacheKey).Result()
		if exists == 0 {
			redisHelper.Set(context.Background(), cacheKey, 1, 5*24*time.Hour)
		} else {
			exists2, _ := redisHelper.Exists(context.Background(), cacheKey2).Result()
			if exists2 == 0 {
				state = 0
			}else{
				state = 1
			}
		}

		linkUrl :=tools.ToString(result["link_url"])
		linkUrl+="?lang="+s.language
		result["link_url"]=linkUrl
		
		result["contact"]=contact
		result["state"]=state//state 0:未处理 1:已处理
		
		result["msg_id"]=msgId
		result["type"]=typeId
		if state == 1 {
			result = nil
		}
	}

	return result
}

func (s *MerchantService) UpdateRestaurantViewType(res_id string, restaurant_type string) (bool, string) {
	db := tools.GetDB()

	resMap := make(map[string]interface{})
	resMap["restaurant_type"] = restaurant_type
	err := db.Table("t_restaurant").Where("id = ?", res_id).Updates(&resMap).Error
	if err != nil {
		return false, err.Error()
	}

	return true, ""
}

func (s *MerchantService) GetRestaurantViewType(res_id string) map[string]interface{} {
	db := tools.GetDB()

	resMap := make(map[string]interface{})

	db.Table("t_restaurant").Where("id = ?", res_id).Select("id,restaurant_type").Scan(&resMap)

	return resMap
}

func (s *MerchantService) GetCashoutDetail(cashId int) []models.MerchantCashoutLog {
	db := tools.GetDB()
	var merchantCashoutLogs []models.MerchantCashoutLog
	db.Model(&models.MerchantCashoutLog{}).
		Preload("CashoutDetails").
		Select("wechat_payment_no, id, mobile, cashout_amount, cashout_time").
		Where("restaurant_id = ?", cashId).
		Where("deleted_at is null").
		Order("created_at desc").
		Find(&merchantCashoutLogs)
	//println(len(merchantCashoutLogs[0].CashoutDetails))
	return merchantCashoutLogs
}

/***
 * @LastEditTime: Do not edit
 * @Author: [rozimamat]
 * @description: restaurant_business_overview 函数迁移
 * @Date: 2023-10-25 12:16:53
 * @param {string} storeId
 * @param {string} beginDate
 * @param {string} endDate
 */
func (s *MerchantService) GetRestaurantBusinessOverview(storeId string, beginDate string, endDate string) *gorm.DB {
	db := tools.GetDB()

	//获取商家客户端统计头部数据
	sql := `
		select 
		count(id) as total_count,
		count(if(delivery_type is null or delivery_type =1,true,null)) as total_count_mp,
		count(if(delivery_type=2,true,null)) as total_count_self,
		FORMAT(CONVERT(SUM(price+lunch_box_fee)/100,DECIMAL ( 12, 2 )),2) as total_price,
		FORMAT(CONVERT(SUM(if( delivery_type is null or delivery_type =1  ,price+lunch_box_fee,0))/100,DECIMAL ( 12, 2 )),2) AS total_price_mp,
		FORMAT(CONVERT(SUM(if( delivery_type  = 2  ,price+lunch_box_fee,0))/100,DECIMAL ( 12, 2 )),2) AS total_price_self
	
	  	from 
		(
		select id, if(order_price_res>0,order_price_res,price+lunch_box_fee) as price,0 as lunch_box_fee,delivery_type from t_order where store_id= ? and created_at between ? and ? and state in (4,5,6,7,10)
		UNION ALL
		select id, if(order_price_res>0,order_price_res,price+lunch_box_fee) as price,0 as lunch_box_fee,delivery_type from t_order_today where store_id= ? and created_at between ? and ? and state in (4,5,6,7,10)
		) as Z
	`
	res := db.Raw(sql, storeId, beginDate, endDate, storeId, beginDate, endDate)

	return res

}

/***
 * @LastEditTime: Do not edit
 * @Author: [rozimamat]
 * @description: restaurant_business_by_payType 函数迁移
 * @Date: 2023-10-25 13:03:25
 * @param {string} storeId
 * @param {string} beginDate
 * @param {string} endDate
 */
func (s *MerchantService) GetRestaurantBusinessByPayType(storeId string, beginDate string, endDate string, lang int) *gorm.DB {
	db := tools.GetDB()

	//获取商家客户端统计模块按支付类型统计数据
	sql := `
	
	select pay_type_id , pay_type_name,icon , count(id) as 'order_count' , FORMAT(CONVERT(SUM(price+lunch_box_fee)/100,DECIMAL ( 12, 2 )),2) AS 'price' FROM
		(
			(
				SELECT a.id , a.pay_type AS 'pay_type_id' , if(?=1,b.name_ug,b.name_zh)  as 'pay_type_name',b.icon ,if(a.order_price_res>0,a.order_price_res,a.price+a.lunch_box_fee) as price,0 as lunch_box_fee
				FROM t_order AS a ,b_pay_type AS b WHERE a.pay_type=b.id AND a.store_id=? AND a.created_at BETWEEN ? AND ? and a.state in(4,5,6,7,10)
			)
		UNION All
			(
				SELECT f.id , f.pay_type AS 'pay_type_id' , if(?=1,b.name_ug,b.name_zh)  as 'pay_type_name' ,b.icon ,if(f.order_price_res>0,f.order_price_res,f.price+f.lunch_box_fee) as price,0 as lunch_box_fee
				FROM t_order_today AS f ,b_pay_type AS b WHERE f.pay_type=b.id AND f.store_id=? AND f.created_at BETWEEN ? AND ? and f.state in(4,5,6,7,10)
			)
		)	as Z
	GROUP BY pay_type_id

	`
	res := db.Raw(sql, lang, storeId, beginDate, endDate, lang, storeId, beginDate, endDate)

	return res

}

/***
 * @LastEditTime: Do not edit
 * @Author: [rozimamat]
 * @description: food_sales_by_restaurant 函数迁移
 * @Date: 2023-10-25 13:13:29
 * @param {string} storeId
 * @param {string} beginDate
 * @param {string} endDate
 * @param {int} lang
 */
func (s *MerchantService) GetFoodSalesByRestaurant(storeId string, beginDate string, endDate string, lang int) *gorm.DB {
	db := tools.GetDB()

	//计算商家在某个时间段的美食销售情况
	sql := `

	SELECT id as 'food_id',name,sum(number) as 'foods_count' ,FORMAT(CONVERT(SUM(price)/100,DECIMAL ( 12, 2 )),2) AS 'price' FROM
	(
			SELECT b.id ,if(?=1,b.name_ug,b.name_zh)  AS name,a.number,  (a.number * if(a.price_markup_price>0,a.price_markup_price,a.price)) as price
			FROM t_order_detail AS a,t_restaurant_foods AS b 
			WHERE a.store_foods_id = b.id 	AND a.order_id IN (SELECT id FROM t_order WHERE store_id = ? AND created_at BETWEEN ? AND ? and state in(4,5,6,7,10) )
			UNION All
			SELECT b.id ,if(?=1,b.name_ug,b.name_zh)  AS name,f.number,  (f.number * if(f.price_markup_price>0,f.price_markup_price,f.price)) as price
			FROM t_order_detail AS f,t_restaurant_foods AS b
			WHERE f.store_foods_id = b.id AND f.order_id IN (SELECT id FROM t_order_today WHERE store_id = ? AND created_at BETWEEN ? AND ? and state in(4,5,6,7,10))
	) Z
	GROUP BY id
	ORDER BY foods_count DESC


	`
	res := db.Raw(sql, lang, storeId, beginDate, endDate, lang, storeId, beginDate, endDate)

	return res

}

/***
 * @LastEditTime: Do not edit
 * @Author: [rozimamat]
 * @description: lunch_box_sales_by_restaurant 函数迁移
 * @Date: 2023-10-25 13:20:09
 * @param {string} storeId
 * @param {string} beginDate
 * @param {string} endDate
 * @param {int} lang
 */
func (s *MerchantService) GetLunchBoxSalesByRestaurant(storeId string, beginDate string, endDate string, lang int) *gorm.DB {
	db := tools.GetDB()

	//计算商家在某个时间段的美食销售情况
	sql := `

	SELECT id as 'food_id',name,sum(count) as 'foods_count' ,FORMAT(CONVERT(SUM(price)/100,DECIMAL ( 12, 2 )),2) AS 'price' FROM
	
		(
			SELECT b.id ,IF(? =1,b.name_ug,b.name_zh) AS name,a.lunch_box_count as count,  (a.lunch_box_count * a.lunch_box_fee) as price
			FROM t_order_detail AS a,b_lunch_box AS b 
			WHERE a.lunch_box_id = b.id 	AND a.order_id IN (SELECT id FROM t_order WHERE store_id = ? AND created_at BETWEEN ? AND ? and state in(4,5,6,7,10) )
			UNION All
			SELECT b.id ,IF(? =1,b.name_ug,b.name_zh) AS name,f.lunch_box_count as count,  (f.lunch_box_count * f.lunch_box_fee) as price
			FROM t_order_detail AS f,b_lunch_box AS b
			WHERE f.lunch_box_id = b.id AND f.order_id IN (SELECT id FROM t_order_today WHERE store_id = ? AND created_at BETWEEN ? AND ? and state in(4,5,6,7,10))
		)
		AS Z
		
	GROUP BY id
	ORDER BY foods_count DESC

	`
	res := db.Raw(sql, lang, storeId, beginDate, endDate, lang, storeId, beginDate, endDate)

	return res

}


// 检查订单是否能打印
func (s *MerchantService) CheckPrintOrder(orderId int64) (models.OrderToday, error) {
	db := tools.GetDB()
	var todayOrder models.OrderToday

	err := db.Model(&models.OrderToday{}).
		Select("id, state").
		Where("id = ? AND deleted_at is null AND state IN (?)", orderId, []int{4, 5, 6}).
		First(&todayOrder).Error

	return todayOrder, err
}

func (s *MerchantService) OrderReadyToSend(RestaurantId string, orderID string,fromState int64,toState int64) (*models.OrderToday, string) {

	db := tools.Db
	var order models.OrderToday
	err := db.Model(&models.OrderToday{}).Where("id = ? AND state = ?", orderID, fromState).First(&order).Error
	if err != nil {
		return nil, "order_not_found"
	}
	if toState == models.ORDER_STATE_RESTAURANT_REJECTED {
		if order.Taked == 1{
			var takedOrder models.TakeOrder
			db.Model(&models.TakeOrder{}).Where("order_id = ?", orderID).First(&takedOrder)
			if takedOrder.ID != 0 {
				db.Model(&takedOrder).Updates(map[string]interface{}{"state": 0, "reason_id": 2})
			}
		}
	}
	orderUpdateState  := map[string]interface{}{"state": toState}
	if toState == models.ORDER_STATE_ACCEPTED {
		orderUpdateState["printed_time"] = time.Now().Format("2006-01-02 15:04:05")
	}
	db.Model(&order).Updates(orderUpdateState)

	var orderStateLog models.OrderStateLog
	orderStateLog.OrderID = order.ID
	orderStateLog.OrderStateID = int(toState)
	db.Create(&orderStateLog)
	return &order,""
}
//
// GetTerminalShowAdver
//  @Description: 老版本升级
//  @receiver s
//  @param osType
//  @return models.Terminal
//
func (s *MerchantService) GetTerminalShowAdver(osType int64) models.Terminal {
	terminal := models.Terminal{}
	db := tools.GetDB()
	db.Model(&models.Terminal{}).Where("state = ? AND terminal_type = ? AND os_type = ?", 1, 5, osType).Order("version_code desc").First(&terminal)

	return terminal
}
func generateVerificationCode() string {
	rand.Seed(time.Now().UnixNano())
	return fmt.Sprintf("%06d", rand.Intn(1000000))
}
func (s *MerchantService) SendCheckCode(mobile string) (bool, string) {

	// 查询最近发送的验证码记录
	var lastCode models.CheckCode
	db := tools.Db
	result := db.Where("mobile = ?", mobile).
		Where("type = ?", "2").
		Order("created_at DESC").
		First(&lastCode)
	if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return false,"查询验证码失败"
	}
	// 检查是否在两分钟内已发送过验证码
	if !lastCode.CreatedAt.IsZero() && time.Since(lastCode.CreatedAt) < 1*time.Second {
		return false,"please_try_again_later"
	}
	//update `t_check_code` set `state` = '0', `updated_at` = '2024-06-25 17:38:48' where `state` = '1' and `mobile` = '18129290467' and `type` = '2' and `t_check_code`.`deleted_at` is null
	// 更新之前的验证码为不可用
	db.Model(&models.CheckCode{}).Where("mobile = ?", mobile).Where("type = ?", "2").Where("state = ?", 1).Update("state", 0)
	code := generateVerificationCode()
	// 创建验证码记录
	newCode := models.CheckCode{
		Type:      "2",
		Mobile:    mobile,
		Code:      code,
		State:     1, // 可用状态
		CreatedAt: time.Now(),
	}
	//contentMap := map[string]string{
	//	"code": code,
	//}
	//dataType, _ := json.Marshal(contentMap)
	//content := string(dataType)
	//tools.AliSmsSend(mobile, content, "Mulazim","SMS_101255082")
	// 发送验证码
	smsServiceFactory := &factory.SmsServiceFactory{}
	service := smsServiceFactory.CreateSmsService()
	codeResp, err := service.SendVerificationCode(mobile, code)
	if err != nil {
		fmt.Printf("发送验证码失败: %v \n",err)
	}
	fmt.Printf("发送验证码成功 结果: %v \n",codeResp)
	if err := db.Create(&newCode).Error; err != nil {
		return false,"send_code_fail"
	}

	return true,"send_code_success"
}
//
// PostChangePasswordCodeVerify
//  @Description: 验证修改密码验证码
//  @receiver s
//  @param mobile
//  @param code
//  @return bool
//  @return string
//
func (s *MerchantService) PostChangePasswordCodeVerify(mobile string, code string) (bool, string)  {
	var codeModal models.CheckCode
	db := tools.Db
	result := db.Where("mobile = ? AND state = 1 AND type = 2", mobile).
		Order("created_at DESC").
		First(&codeModal)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return false,"verify_code_not_found"
		}
		return false,"verify_code_fail"
	}
	if code != codeModal.Code {
		return false,"verify_code_error"
	}
	// 修改验证码令牌存储
	key := fmt.Sprintf("change_password_mobile_%s", mobile)
	tools.RedisSetValue(key, mobile, 5*60)
	//  更新codeModal的状态
	db.Model(&codeModal).Update("state", 0)
	return true,"verify_success"
}

func (s *MerchantService) PostChangePassword(c *gin.Context,old_password string,password string,password_confirm string,admin_id int)  (bool, string){
	if password != password_confirm {
		return false,"password_not_match"
	}
	oldHexPassword := tools.PasswordToHash(old_password)
	db := tools.Db
	var admin models.Admin
	db.Where("id = ? AND password = ?", admin_id, oldHexPassword).First(&admin)
	if admin.ID == 0 {
		return false,"old_password_error"
	}
	hexPassword := tools.PasswordToHash(password)
	db.Model(&models.Admin{}).Where("id = ?", admin_id).Update("password", hexPassword)

	//delete from
	//delete  from oauth_sessions where owner_id = 2046 and owner_type = 'merchant';
	//delete  from oauth_access_tokens where session_id = 2251518;
	var session models.OauthSessions
	db.Model(session).Where("owner_id = ? AND owner_type = ?", admin_id, "merchant").First(&session)
	if(session.ID > 0 ){
		// 删除session
		db.Model(&session).Where("owner_id = ? AND owner_type = ?", admin_id, "merchant").Delete(&session)
		db.Model(models.OauthAccessTokens{}).Where("session_id = ?", session.ID).Delete(&models.OauthAccessTokens{})
	}
	// 清空jwt_serial_number
	db.Model(models.Admin{}).Where("id=?",admin_id).UpdateColumn("jwt_serial_number", nil)
	return true,"password_update_success"
}
//
// PostChangeSMSPassword
//  @Description: 短信修改密码
//  @receiver s
//  @param c
//  @param mobile
//  @param password
//  @return bool
//  @return string
//
func (s *MerchantService) PostChangeSMSPassword(c *gin.Context,mobile string,password string)  (bool, string){
	key := fmt.Sprintf("change_password_mobile_%s", mobile)
	redisHelper := tools.GetRedisHelper()
	//  如果这个key 存在 则说明，可以修改，否则短信验证失败或者重新验证、
	if redisHelper.Exists(c,key).Val()==1 {
		//计算hex 值 password 的
		hexPassword := tools.PasswordToHash(password)
		db := tools.Db
		//update t_admin set password = 'e10adc3949ba59abbe56e057f20f883e' where mobile = '18129290467'
		db.Model(&models.Admin{}).Where("mobile = ?", mobile).Update("password", hexPassword)
		redisHelper.Del(c,key)
		return true,"password_update_success"
	}else{
		return false,"verify_code_not_verify"
	}
}
//
// PostReceiveOrder
//  @Description: 商家接单
//  @receiver s
//  @param orderID
//  @return bool
//  @return string
//
func (s *MerchantService) PostReceiveOrder(orderID string) (bool, string){

	db := tools.Db
	var order models.OrderToday
	err := db.Model(&models.OrderToday{}).Where("id = ?", orderID).First(&order).Error
	if err != nil || order.ID == 0{
		return false,"order_not_found"
	}
	if order.State != models.ORDER_STATE_WAITING_ACCEPT {
		return false,"order_state_error"
	}
	//  现金支付
	var orderUpdateMap = map[string]interface{}{
		"state": models.ORDER_STATE_ACCEPTED,
		"printed_time": carbon.Now("Asia/Shanghai").ToDateTimeString(),
	}

	if order.PayType == models.PAYMENT_CASH {
		orderUpdateMap["pay_type"] = models.PAYMENT_AGENT_WECHAT
		orderUpdateMap["consume_type"] = models.CONSUME_AGENT
		db.Model(&models.TakeOrder{}).Where("order_id = ?", order.ID).Update("consume_type", models.CONSUME_AGENT)
	}

	var orderStateLog models.OrderStateLog
	orderStateLog.OrderID = order.ID
	orderStateLog.OrderStateID = models.ORDER_STATE_ACCEPTED
	db.Create(&orderStateLog)
	db.Model(&models.OrderToday{}).Where("id = ?", orderID).Updates(orderUpdateMap)
	//TODO  发送订单确认消息

	go func() {
		job := jobs.NewSendWechatMiniMessageJob()
		job.ProduceMessageToConsumer(map[string]interface{}{
			"order_id": orderID,
			"type":     2,
		})
		s.SendOrderCancelSms(tools.ToInt(orderID),1,services.TYPE_ADMIN_MERCHANT)
	}()
	return true,""
}
//
// UpdateMerchantInfo
//  @Description: 更新商家信息
//  @receiver s
//  @param request
//  @return bool
//  @return string
//
func (s *MerchantService) UpdateMerchantInfo(request marketingRequest.MerchantInfoUpdateRequest) (bool, string) {
	db := tools.Db
	var restaurant models.Restaurant
	updateMap := map[string]interface{}{
		"name_ug": request.NameUG,
		"address_ug": request.AddressUG,
		"description_ug": request.DescriptionUG,
		"name_zh": request.NameZH,
		"address_zh": request.AddressZH,
		"description_zh": request.DescriptionZH,
	}
	if request.OpenTime != nil {
		updateMap["open_time"] = *request.OpenTime + ":00"
	}
	if request.CloseTime != nil {
		updateMap["close_time"] = *request.CloseTime + ":00"
	}
	if request.Tel != nil {
		updateMap["tel"] = strings.ReplaceAll(*request.Tel," ","")
	}
	if request.Tel2 != "" {
		updateMap["tel2"] = strings.ReplaceAll(request.Tel2," ","")
	}
	if request.Tel3 != "" {
		updateMap["tel3"] = strings.ReplaceAll(request.Tel3," ","")
	}
	if request.Tel4 != "" {
		updateMap["tel4"] = strings.ReplaceAll(request.Tel4," ","")
	}
	if request.Tel5 != "" {
		updateMap["tel5"] = strings.ReplaceAll(request.Tel5," ","")
	}

	if request.CanSelfTake!= nil {
		updateMap["can_self_take"] = *request.CanSelfTake
	}

	//更新restaurant
	db.Model(&restaurant).Where("id = ?", request.RestaurantID).Updates(updateMap)

	//设置餐厅分类
	db.Where("restaurant_id = ?", request.RestaurantID).Delete(&models.RestaurantCategory{})
	//  创建类型 type,res_id
	db.Create(&models.RestaurantCategory{
		RestaurantId: int64(request.RestaurantID),
		CategoryId: int64(request.Type),
	})
	return true,""
}
//
// GetComment
//  @Description: 商家获取评论
//  @receiver s
//  @param commentType
//  @param page
//  @param restaurantId
//  @return bool
//  @return string
//  @return []Other.CommentModel
//  @return int
//  @return int
//
func (s *MerchantService) GetComment(commentType int64, page int64, restaurantId int64) (bool,string,[]Other.CommentModel,int,int) {
	//tools.Logger.Infof("commentType:%d,page:%d,restaurantId:%d",commentType,page,restaurantId)
	db := tools.Db

	var comments []Other.CommentModel
	query := db.Model(&comments).
		Select("t_comment.id, t_comment.user_id, t_comment.order_id, t_comment.type, t_comment.order_detail_id, t_comment.food_id, t_order_detail.number as food_number, if(t_order_detail.price_markup_price>0,t_order_detail.price_markup_price,t_order_detail.price) / 100 as food_price, t_restaurant_foods.name_" + s.language + " as food_name, t_restaurant_foods.image as food_image, t_comment.star, t_comment.food_star, t_comment.box_star, t_comment.text, t_comment.is_anonymous, t_comment.is_satisfied, t_comment.created_at," +
			"(select count(1) from t_comment_image where t_comment_image.comment_id = t_comment.id and t_comment_image.deleted_at is null) as comment_image_count," +
			"(select count(1) from t_comment_reply where t_comment_reply.comment_id = t_comment.id and t_comment_reply.deleted_at is null) as comment_reply_count").
		Joins("left join t_order_detail on t_order_detail.id = t_comment.order_detail_id").
		Joins("left join t_restaurant_foods on t_restaurant_foods.id = t_comment.food_id").
		Where("t_comment.type = ?", 2).
		Where("t_comment.state = ?", 1).
		Where("t_restaurant_foods.restaurant_id = ?", restaurantId).
		Preload("Images").
		Preload("OrderDetail.RestaurantFoods.ComboFoodItems.RestaurantFood"). // 套餐子美食
		Preload("OrderDetail.RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions"). // 如果是套餐美食
		Preload("OrderDetail.SelectedSpec.FoodSpecOptions"). // 如果有已选规格数据
		Preload("User").
		Preload("CommentReply", func(db *gorm.DB) *gorm.DB {
			return db.Order("created_at desc")
		})

	from := time.Now().AddDate(-1, 0, 0).Truncate(time.Hour * 24).Add(-time.Hour * time.Duration(time.Now().Day()-1))
	query = query.Where("t_comment.created_at > ?", from)

	switch commentType {
	case 1:
		// do nothing
	case 2:
		query = query.Where("star > ?", 2)
	case 3:
		query = query.Where("star < ?", 3)
	case 4:
		//query.Select("")
		query.Having("comment_image_count > ?",0)
	case 5:
		query.Having("comment_reply_count > ?",0)
	}

	query = query.Order("t_comment.created_at desc")

	limit := 30
	offset := (page - 1) * int64(limit)


	if query.Error != nil {
		return false, fmt.Sprintf("query error: %v", query.Error),nil,0,0
	}
	//获取总数据数
	var totalCount int64
	query.Find(&comments)
	totalCount = int64(len(comments))
	//获取总页数
	lastPage := int64(math.Ceil(float64(totalCount) / float64(limit)))

	query.Limit(limit).Offset(int(offset)).Find(&comments)

	return true, "",comments, int(lastPage), (limit)
	//return true,""
}
//
// GetCommentStar
//  @Description: 获取评论星级
//  @receiver s
//  @param restaurantId
//  @return models.Restaurant
//
func (s *MerchantService) GetCommentStar(restaurantId int64) models.Restaurant {
	db := tools.Db
	var restaruant models.Restaurant
	db.Model(&restaruant).Where("id = ?",restaurantId).First(&restaruant)
	return restaruant
}
//
// GetRestaurantCommentsCountByType
//  @Description: 获取餐厅评论数量
//  @receiver s
//  @param c
//  @param restaurantID
//  @return []map[string]interface{}
//
func (s *MerchantService) GetRestaurantCommentsCountByType(c *gin.Context,restaurantID int64) []map[string]interface{}{
	db := tools.Db
	cacheKey := "restaurant_comment_type_" + strconv.Itoa(int(restaurantID))
	typeCount := tools.Remember(c, cacheKey, time.Minute*60, func() interface{} {
		from := time.Now().AddDate(-1, 0, 0)
		var count1, count2, count3, count4, count5 int64

		db.Model(&Other.CommentModel{}).
			Joins("left join t_restaurant_foods on t_restaurant_foods.id = t_comment.food_id").
			Where("t_comment.type = ? AND t_comment.state = ? AND t_comment.created_at > ? AND t_restaurant_foods.restaurant_id = ?", 2, 1, from, restaurantID).
			Count(&count1)

		db.Model(&Other.CommentModel{}).
			Joins("left join t_restaurant_foods on t_restaurant_foods.id = t_comment.food_id").
			Where("t_comment.type = ? AND t_comment.state = ? AND t_comment.star > ? AND t_comment.created_at > ? AND t_restaurant_foods.restaurant_id = ?", 2, 1, 2, from, restaurantID).
			Count(&count2)

		db.Model(&Other.CommentModel{}).
			Joins("left join t_restaurant_foods on t_restaurant_foods.id = t_comment.food_id").
			Where("t_comment.type = ? AND t_comment.state = ? AND t_comment.star < ? AND t_comment.created_at > ? AND t_restaurant_foods.restaurant_id = ?", 2, 1, 3, from, restaurantID).
			Count(&count3)

		var comments []Other.CommentModel
		subQuery := db.Table("t_comment_image").
			Select("count(1)").
			Where("t_comment_image.comment_id = t_comment.id")
		db.Table("t_comment").
			Select("t_comment.*, (?) as image_count", subQuery).
			Joins("left join t_restaurant_foods on t_restaurant_foods.id = t_comment.food_id").
			Where("t_comment.type = ? AND t_comment.state = ? AND t_comment.created_at > ? AND t_restaurant_foods.restaurant_id = ?", 2, 1, from, restaurantID).
			Having("image_count > 0").
			Find(&comments)
		count4 = int64(len(comments))



		subQuery = db.Table("t_comment_reply").
			Select("count(1)").
			Where("t_comment_reply.comment_id = t_comment.id and t_comment_reply.deleted_at is null")
		db.Table("t_comment").
			Select("t_comment.*, (?) as image_count", subQuery).
			Joins("left join t_restaurant_foods on t_restaurant_foods.id = t_comment.food_id").
			Where("t_comment.type = ? AND t_comment.state = ? AND t_comment.created_at > ? AND t_restaurant_foods.restaurant_id = ?", 2, 1, from, restaurantID).
			Having("image_count > 0").
			Find(&comments)
		count5 = int64(len(comments))

		typeCount := []map[string]interface{}{
			{"name_ug": "ھەممىسى", "name_zh": "全部", "type": 1, "count": count1},
			{"name_ug": "ياخشى", "name_zh": "好评", "type": 2, "count": count2},
			{"name_ug": "ناچار", "name_zh": "差评", "type": 3, "count": count3},
			{"name_ug": "رەسىملىك", "name_zh": "有图", "type": 4, "count": count4},
			{"name_ug": "ئېنكاس قايتۇرۇلغىنى", "name_zh": "商家回复", "type": 5, "count": count5},
		}

		return typeCount
	})
	var rtnTypeCount []map[string]interface{}
	json.Unmarshal([]byte(typeCount), &rtnTypeCount)
	return rtnTypeCount
}
//
// PostReply
//  @Description: 回复评论
//  @receiver s
//  @param commentId
//  @param text
//  @param adminID
//  @return bool
//  @return string
//  @return map[string]interface{}
//
func (s *MerchantService) PostReply(commentId int64, text string,adminID int) (bool, string, map[string]interface{}) {
	//insert into `t_comment_reply` (`comment_id`, `type`, `text`, `admin_id`, `updated_at`, `created_at`) values ('252370', '1', 'rahmat', '2046', '2024-06-28 12:58:11', '2024-06-28 12:58:11')
	// CommentReplay
	db := tools.Db
	var commentReply Other.CommentReply
	commentReply.CommentID = int(commentId)
	commentReply.Type = 1
	commentReply.Text = text
	commentReply.AdminID = adminID
	commentReply.UpdatedAt = time.Now()
	commentReply.CreatedAt = time.Now()
	db.Create(&commentReply)
	var rtnMap map[string]interface{}
	rtnStr,_ := json.Marshal(commentReply)
	json.Unmarshal([]byte(rtnStr), &rtnMap)
	rtnMap["created_at"] = carbon.Time2Carbon(commentReply.CreatedAt).ToDateTimeString()
	rtnMap["updated_at"] = carbon.Time2Carbon(commentReply.UpdatedAt).ToDateTimeString()
	return true,"",rtnMap
}
//
// PostCreateFood
//  @Description: 创建菜品
//  @receiver s
//  @param request
//  @return bool
//  @return string
//
func (s *MerchantService) PostCreateFood(request food.FoodCreateRequest) (bool, string ,*models.RestaurantFoods) {
	db := tools.Db

	imageUrl := strings.Replace(request.Image,configs.MyApp.CdnUrl,"",1)
	food := models.RestaurantFoods{
		NameUg :       request.NameUg,
		NameZh :       request.NameZh,
		DescriptionUg: request.DescriptionUg,
		DescriptionZh: request.DescriptionZh,
		Image:         imageUrl,
		BeginTime: request.BeginTime +":00",
		EndTime: request.EndTime+":00",
		ReadyTime: int(request.ReadyTime),
		Price: uint(request.Price*100),
		RestaurantID:  int(request.RestaurantID),
		FoodsGroupId: int(request.AllFoodsId),
		State: models.RESTAURANTFOODS_STATE_INPREVIEW,
		IsRecommend: request.IsRecommend,
		Weight: request.Weight,
		WeightInGroup: request.Weight,
	}
	restaruant := models.Restaurant{}
	db.Model(&restaruant).Where("id = ?",request.RestaurantID).First(&restaruant)
	if restaruant.ID == 0 {
		return false,"餐厅不存在",nil
	}
	area := models.Area{}
	db.Model(&area).Where("id = ?",restaruant.AreaID).First(&area)
	if area.ID == 0 {
		return false,"区域不存在",nil
	}
	//foods 中查找最大的dealer_profit
	//var maxDealerProfitInteface interface{}
	var maxDealerProfit float64
	db.Table("t_restaurant_foods").
		Select("IFNULL(max(dealer_percent), 0)").
		Where("restaurant_id = ?", request.RestaurantID).
		Scan(&maxDealerProfit)
	if maxDealerProfit<0.01 {
		maxDealerProfit = 20
	}
	food.DealerPercent = maxDealerProfit
	food.MpPercent = area.MpPercent

	food.MpYourselfTakePercent = 10
	food.DealerYourselfTakePercent = 10
	db.Create(&food)
	categoryIds := strings.Split(request.CategoryIds,",")
	for _,v := range categoryIds {
		// 美食分类中间表
		db.Model(&models.RestaurantFoodsCategory{}).Create(&models.RestaurantFoodsCategory{
			RestaurantFoodsID: food.ID,
			FoodsCategoryID: tools.ToInt(v),
		})
	}
	
	db.Table("t_notification").Create(&map[string]interface{}{
		"type":       2,
		"area_id":    area.ID,
		"content_ug": fmt.Sprintf("«%s» نىڭ %s تاماق ئۇچۇرى يېڭىلاندى، تەستىق ساقلاۋاتىدۇ", restaruant.NameUg, food.NameUg),
		"content_zh": fmt.Sprintf("«%s» نىڭ %s تاماق ئۇچۇرى يېڭىلاندى، تەستىق ساقلاۋاتىدۇ", restaruant.NameUg, food.NameUg),
		"link":       "/ug/restaurant/foods/to-edit?id="+strconv.Itoa(food.ID),
		"state":      0,
		"item_id":    food.ID,
		"created_at": time.Now(),
		"updated_at": time.Now(),
	})
	return true,"msg",&food
}


//
// PostCreateFood
//  @Description: 创建菜品
//  @receiver s
//  @param request
//  @return bool
//  @return string
//
func (s *MerchantService) PostCreateFoodNew(request food.FoodCreateRequestNew) (bool, string ,*models.RestaurantFoods) {
	db := tools.Db

	imageUrl := strings.Replace(request.Image,configs.MyApp.CdnUrl,"",1)
	food := models.RestaurantFoods{
		NameUg :       request.NameUg,
		NameZh :       request.NameZh,
		DescriptionUg: request.DescriptionUg,
		DescriptionZh: request.DescriptionZh,
		Image:         imageUrl,
		BeginTime: request.BeginTime +":00",
		EndTime: request.EndTime+":00",
		ReadyTime: int(request.ReadyTime),
		Price: uint(request.Price*100),
		RestaurantID:  int(request.RestaurantID),
		State: models.RESTAURANTFOODS_STATE_INPREVIEW,
		IsRecommend: request.IsRecommend,
		MinCount: request.MinCount,
		FoodsGroupId: request.FoodsGroupId,
		WeightInGroup: request.Weight,
	}
	restaruant := models.Restaurant{}
	db.Model(&restaruant).Where("id = ?",request.RestaurantID).First(&restaruant)
	if restaruant.ID == 0 {
		return false,"餐厅不存在",nil
	}
	area := models.Area{}
	db.Model(&area).Where("id = ?",restaruant.AreaID).First(&area)
	if area.ID == 0 {
		return false,"区域不存在",nil
	}
	//foods 中查找最大的dealer_profit
	//var maxDealerProfitInteface interface{}
	var maxDealerProfit float64
	db.Table("t_restaurant_foods").
		Select("IFNULL(max(dealer_percent), 0)").
		Where("restaurant_id = ?", request.RestaurantID).
		Scan(&maxDealerProfit)
	if maxDealerProfit<0.01 {
		maxDealerProfit = 20
	}
	food.DealerPercent = maxDealerProfit
	food.MpPercent = area.MpPercent

	food.MpYourselfTakePercent = 10
	food.DealerYourselfTakePercent = 10
	db.Create(&food)
	// 添加分类
	if request.CategoryIds != "" && len(request.CategoryIds) > 0 && request.CategoryIds != ","{
		categoryIds := strings.Split(request.CategoryIds,",")
		for _,v := range categoryIds {
			// 美食分类中间表
			db.Model(&models.RestaurantFoodsCategory{}).Create(&models.RestaurantFoodsCategory{
				RestaurantFoodsID: food.ID,
				FoodsCategoryID: tools.ToInt(v),
			})
		}
	}
	db.Table("t_notification").Create(&map[string]interface{}{
		"type":       2,
		"area_id":    area.ID,
		"content_ug": fmt.Sprintf("«%s» نىڭ %s تاماق ئۇچۇرى يېڭىلاندى، تەستىق ساقلاۋاتىدۇ", restaruant.NameUg, food.NameUg),
		"content_zh": fmt.Sprintf("«%s» نىڭ %s تاماق ئۇچۇرى يېڭىلاندى، تەستىق ساقلاۋاتىدۇ", restaruant.NameUg, food.NameUg),
		"link":       "/ug/restaurant/foods/to-edit?id="+strconv.Itoa(food.ID),
		"state":      0,
		"item_id":    food.ID,
		"created_at": time.Now(),
		"updated_at": time.Now(),
	})
	return true,"msg",&food
}


//
// PostUpdateFood
//  @Description: 更新菜品
//  @receiver s
//  @param request
//  @return bool
//  @return string
//
func (s *MerchantService) PostUpdateFood(request food.FoodUpdateRequest) (bool, string ) {
	db := tools.Db
	var oldFoodMap map[string]interface{}
	db.Model(&models.RestaurantFoods{}).Where("id = ?",request.ID).First(&oldFoodMap)
	imageUrl := strings.Replace(request.Image,configs.MyApp.CdnUrl,"",1)

	foodUpdateMap := map[string]interface{}{
		"name_ug":       request.NameUg,
		"name_zh":       request.NameZh,
		"description_ug": request.DescriptionUg,
		"description_zh": request.DescriptionZh,
		"image":         imageUrl,
		"begin_time": request.BeginTime +":00",
		"end_time": request.EndTime+":00",
		"ready_time": int(request.ReadyTime),
		"price": uint(request.Price*100),
		"foods_group_id": int(request.AllFoodsId),
		// "state": models.RESTAURANTFOODS_STATE_INPREVIEW,
		"is_recommend": request.IsRecommend,
		"weight": request.Weight,
		"weight_in_group": request.Weight,
	}
	// 如果修改需要审核的内容，状态修改审核状态
	if s.isReviewEditFood(request,oldFoodMap) {
			foodUpdateMap["state"] = models.RESTAURANTFOODS_STATE_INPREVIEW
	}
	db.Model(&models.RestaurantFoods{}).Where("id = ?",request.ID).Updates(foodUpdateMap)
	restaruant := models.Restaurant{}
	db.Model(&restaruant).Where("id = ?",request.RestaurantID).First(&restaruant)
	if restaruant.ID == 0 {
		return false,"餐厅不存在"
	}
	area := models.Area{}
	db.Model(&area).Where("id = ?",restaruant.AreaID).First(&area)
	if area.ID == 0 {
		return false,"区域不存在"
	}
	// 更新分类
	if request.CategoryIds != "" && len(request.CategoryIds) > 0 && request.CategoryIds != ","{
		categoryIds := strings.Split(request.CategoryIds,",")
		db.Model(&models.RestaurantFoodsCategory{}).Where("restaurant_foods_id =? and foods_category_id in(?)", request.ID,categoryIds).Delete(&models.RestaurantFoodsCategory{})
		for _,v := range categoryIds {
			// 美食分类中间表
			db.Model(&models.RestaurantFoodsCategory{}).Create(&models.RestaurantFoodsCategory{
				RestaurantFoodsID:tools.ToInt( request.ID),
				FoodsCategoryID: tools.ToInt(v),
			})
		}
	}
	

	// 如果修改需要审核的内容，推送审核通知
	if s.isReviewEditFood(request,oldFoodMap) {
		oldFoodMap["price"] = oldFoodMap["price"].(uint)/100
		oldFoodByte,_:=json.Marshal(oldFoodMap)
		db.Table("t_notification").Create(&map[string]interface{}{
			"type":       2,
			"area_id":    area.ID,
			"content_ug": fmt.Sprintf("«%s» نىڭ %s تاماق ئۇچۇرى يېڭىلاندى، تەستىق ساقلاۋاتىدۇ", restaruant.NameUg, request.NameUg),
			"content_zh": fmt.Sprintf("«%s» نىڭ %s تاماق ئۇچۇرى يېڭىلاندى، تەستىق ساقلاۋاتىدۇ", restaruant.NameUg, request.NameUg),
			"link":       "/ug/restaurant/foods/to-edit?id="+ strconv.Itoa(int(request.ID)),
			"item_id":    int(request.ID),
			"state":      0,
			"update_content": string(oldFoodByte),
			"created_at": time.Now(),
			"updated_at": time.Now(),
		})
	}
	
	return true,"msg"
}


func (s *MerchantService) isReviewEditFood(request food.FoodUpdateRequest,oldFoodMap map[string]interface{}) bool{
	if tools.ToInt(oldFoodMap["price"]) != tools.ToInt((request.Price*100)) ||  
	oldFoodMap["description_ug"] != request.DescriptionUg || 
	oldFoodMap["description_zh"] != request.DescriptionZh || 
	oldFoodMap["image"] != request.Image || 
	oldFoodMap["name_ug"] != request.NameUg || 
	oldFoodMap["name_ug"] != request.NameZh {
		return true
	}
	return false
}


//
// PostUpdateFood
//  @Description: 更新菜品
//  @receiver s
//  @param request
//  @return bool
//  @return string
//
func (s *MerchantService) PostUpdateFoodNew(request food.FoodUpdateRequestNew) (bool, string ) {
	db := tools.Db
	var oldFoodMap map[string]interface{}
	db.Model(&models.RestaurantFoods{}).Where("id = ?",request.ID).First(&oldFoodMap)
	imageUrl := strings.Replace(request.Image,configs.MyApp.CdnUrl,"",1)

	foodUpdateMap := map[string]interface{}{
		"name_ug":       request.NameUg,
		"name_zh":       request.NameZh,
		"description_ug": request.DescriptionUg,
		"description_zh": request.DescriptionZh,
		"image":         imageUrl,
		"begin_time": request.BeginTime +":00",
		"end_time": request.EndTime+":00",
		"ready_time": int(request.ReadyTime),
		"price": uint(request.Price*100),
		"state": models.RESTAURANTFOODS_STATE_INPREVIEW,
		"is_recommend": request.IsRecommend,
		"min_count": request.MinCount,
		"foods_group_id": request.FoodsGroupId,
		"weight": request.Weight,
	}
	db.Model(&models.RestaurantFoods{}).Where("id = ?",request.ID).Updates(foodUpdateMap)

	categoryIds := strings.Split(request.CategoryIds,",")
	db.Model(&models.RestaurantFoodsCategory{}).Where("restaurant_foods_id =? and foods_category_id in(?)", request.ID,categoryIds).Delete(&models.RestaurantFoodsCategory{})
	for _,v := range categoryIds {
		// 美食分类中间表
		db.Model(&models.RestaurantFoodsCategory{}).Create(&models.RestaurantFoodsCategory{
			RestaurantFoodsID: request.ID,
			FoodsCategoryID: tools.ToInt(v),
		})
	}

	restaruant := models.Restaurant{}
	db.Model(&restaruant).Where("id = ?",request.RestaurantID).First(&restaruant)
	if restaruant.ID == 0 {
		return false,"餐厅不存在"
	}
	area := models.Area{}
	db.Model(&area).Where("id = ?",restaruant.AreaID).First(&area)
	if area.ID == 0 {
		return false,"区域不存在"
	}
	oldFoodMap["price"] = oldFoodMap["price"].(uint)/100
	oldFoodByte,_:=json.Marshal(oldFoodMap)
	db.Table("t_notification").Create(&map[string]interface{}{
		"type":       2,
		"area_id":    area.ID,
		"content_ug": fmt.Sprintf("«%s» نىڭ %s تاماق ئۇچۇرى يېڭىلاندى، تەستىق ساقلاۋاتىدۇ", restaruant.NameUg, request.NameUg),
		"content_zh": fmt.Sprintf("«%s» نىڭ %s تاماق ئۇچۇرى يېڭىلاندى، تەستىق ساقلاۋاتىدۇ", restaruant.NameUg, request.NameUg),
		"link":       "/ug/restaurant/foods/to-edit?id="+ strconv.Itoa(int(request.ID)),
		"item_id":    int(request.ID),
		"state":      0,
		"update_content": string(oldFoodByte),
		"created_at": time.Now(),
		"updated_at": time.Now(),
	})
	return true,"msg"
}

//
// PostUploadBusinessLicense
//  @Description: 上传营业执照
//  @receiver s
//  @param imagePath
//  @param resId
//  @param licenseTypeId
//  @param adminId
//  @return bool
//  @return string
//  @return map[string]interface{}
//
func (s *MerchantService) PostUploadBusinessLicense(imagePath string, resId int, licenseTypeId string, adminId int) (bool, string,map[string]interface{}) {
	db := tools.Db
	listenceType := 1
	if licenseTypeId == "1" {
		listenceType = 3
	}
	if licenseTypeId == "2" {
		listenceType = 2
	}

	var merchantProcess Other.MerchantProcess
	var restaurant models.Restaurant
	db.Model(&restaurant).Where("id = ?",resId).First(&restaurant)
	db.Model(&merchantProcess).Where("restaurant_id = ? and type = ?", resId,listenceType).First(&merchantProcess)
	if merchantProcess.ID!=0 && merchantProcess.State == 1 {
		return false,"info_in_review",nil
	}
	if merchantProcess.ID == 0  {
		merchantProcess = Other.MerchantProcess{
			Type: listenceType,
			CityID: restaurant.CityID,
			AreaID: restaurant.AreaID,
			RestaurantID: resId,
			RestaurantName: restaurant.NameUg,
		}
	}
	var license Other.RestaurantLicense
	db.Model(&license).Where("restaurant_id = ? and license_type_id = ?", resId,licenseTypeId).First(&license)
	if license.ID > 0{
		merchantProcess.OldValue = license.ImageUrl
	}
	merchantProcess.NewValue = imagePath
	merchantProcess.State = 1
	db.Save(&merchantProcess)
	//$content = $license_type_id==1?"ئاشخانا يېمەكلىك ئىجازەتنامىسى يوللىدى، ۋاقتىدا بىر تەرەپ قىلىڭ":"ئاشخانا تىجارەت كىىشكىسى يوللىدى، ۋاقتىدا بىر تەرەپ قىلىڭ";
	//                $this->adminService->addNotification(8,"/ug/merchant-process",$content);
	//db.Create(&license)
	content := "ئاشخانا تىجارەت كىىشكىسى يوللىدى، ۋاقتىدا بىر تەرەپ قىلىڭ"
	if licenseTypeId == "1" {
		content = "ئاشخانا يېمەكلىك ئىجازەتنامىسى يوللىدى، ۋاقتىدا بىر تەرەپ قىلىڭ"
	}
	s.AddNotification(adminId,8,"/ug/merchant-process",content)
	return true,"msg", map[string]interface{}{
		"image_url":tools.AddCdn(imagePath),
		"restaurant_id":resId,
	}
}
//
// PostUploadRestaurantAvatar
//  @Description: 上传餐厅头像
//  @receiver s
//  @param imagePath
//  @param resId
//  @param adminID
//  @return bool
//  @return string
//  @return map[string]interface{}
//
func (s *MerchantService) PostUploadRestaurantAvatar(imagePath string, resId int, adminID int) (bool, string,map[string]interface{}) {
	db := tools.Db

	var merchantProcess Other.MerchantProcess
	var restaurant models.Restaurant
	db.Model(&restaurant).Where("id = ?",resId).First(&restaurant)
	db.Model(&merchantProcess).Where("restaurant_id = ? and type = ?", resId,1).First(&merchantProcess)
	if merchantProcess.ID!=0 && merchantProcess.State == 1 {
		return false,"info_in_review",nil
	}
	if merchantProcess.ID == 0  {
		merchantProcess = Other.MerchantProcess{
			Type: 1,
			CityID: restaurant.CityID,
			AreaID: restaurant.AreaID,
			RestaurantID: resId,
			RestaurantName: restaurant.NameUg,
		}
	}
	if restaurant.ID > 0{
		merchantProcess.OldValue = restaurant.Logo
	}
	merchantProcess.NewValue = imagePath
	merchantProcess.State = 1
	db.Save(&merchantProcess)
	//$content = $license_type_id==1?"ئاشخانا يېمەكلىك ئىجازەتنامىسى يوللىدى، ۋاقتىدا بىر تەرەپ قىلىڭ":"ئاشخانا تىجارەت كىىشكىسى يوللىدى، ۋاقتىدا بىر تەرەپ قىلىڭ";
	//                $this->adminService->addNotification(8,"/ug/merchant-process",$content);
	//db.Create(&license)
	content := "ئاشخانا دۇكان رەسىمى يوللىدى، ۋاقتىدا بىر تەرەپ قىلىڭ!"
	s.AddNotification(adminID,8,"/ug/merchant-process",content)
	return true,"msg", map[string]interface{}{
		"image_url":tools.AddCdn(imagePath),
		"restaurant_id":resId,
	}
}
//
// PostUploadRestaurantAvatar
//  @Description: 上传餐厅内部照片
//  @receiver s
//  @param imagePath
//  @param resId
//  @param adminID
//  @return bool
//  @return string
//  @return map[string]interface{}
//
func (s *MerchantService) PostUploadRestaurantImage(imagePath string, resId int, adminID int) (bool, string,map[string]interface{}) {
	db := tools.Db

	var merchantProcess Other.MerchantProcess
	var restaurant models.Restaurant
	db.Model(&restaurant).Where("id = ?",resId).First(&restaurant)
	db.Model(&merchantProcess).Where("restaurant_id = ? and type = ?", resId,4).First(&merchantProcess)
	if merchantProcess.ID!=0 && merchantProcess.State == 1 {
		return false,"info_in_review",nil
	}
	// Fetch old images
	var oldImages []models.RestaurantImages
	db.Where("restaurant_id = ?", resId).Find(&oldImages)
	// Extract image URLs
	var oldImageArr []string
	for _, oldImage := range oldImages {
		oldImageArr = append(oldImageArr, oldImage.ImageUrl)
	}

	// Create new image array with the new image path
	newImageArr := append(oldImageArr, imagePath)

	if merchantProcess.ID == 0  {
		merchantProcess = Other.MerchantProcess{
			Type: 4,
			CityID: restaurant.CityID,
			AreaID: restaurant.AreaID,
			RestaurantID: resId,
			RestaurantName: restaurant.NameUg,
		}
	}
	if restaurant.ID > 0{
		merchantProcess.OldValue = strings.Join(oldImageArr, ",")
	}
	merchantProcess.NewValue = strings.Join(newImageArr, ",")
	merchantProcess.State = 1
	db.Save(&merchantProcess)
	//$content = $license_type_id==1?"ئاشخانا يېمەكلىك ئىجازەتنامىسى يوللىدى، ۋاقتىدا بىر تەرەپ قىلىڭ":"ئاشخانا تىجارەت كىىشكىسى يوللىدى، ۋاقتىدا بىر تەرەپ قىلىڭ";
	//                $this->adminService->addNotification(8,"/ug/merchant-process",$content);
	//db.Create(&license)
	content := "ئاشخانا دۇكان رەسىمى يوللىدى، ۋاقتىدا بىر تەرەپ قىلىڭ!"
	s.AddNotification(adminID,8,"/ug/merchant-process",content)
	return true,"msg", map[string]interface{}{
		"image_url":tools.AddCdn(imagePath),
		"restaurant_id":resId,
	}
}

// 描述：验证码登录
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2023/09/27 17:11
func (s *MerchantService) CheckSMSLogin(mobile string) (models.Admin, error) {
	db := tools.Db
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Printf("登录日志【验证码】 error: %s\n", err)
			}
		}()
	}()
	var admin models.Admin
	db.Model(admin).Where("mobile = ?", mobile).
		Where("type in (5,6)").Where("state = 1").
		Where("deleted_at IS NULL").First(&admin)
	if admin.ID == 0 {
		return admin, errors.New("admin_is_not_active")
	}
	if (admin.Type != models.AdminTypeRestaurantAdmin) && (admin.Type != models.AdminTypeRestaurantAdminSub) {
		return admin, errors.New("no_permission")
	}
	return admin, nil
}

// 描述：发送短信验证码
// 作者：Qurbanjan
// 文件：MerchantService.go
// 修改时间：2024/08/15 16:29
func (S *MerchantService) SendSMSCode(c *gin.Context, mobile string) error{
	redisHelper := tools.GetRedisHelper()
	cacheKey := fmt.Sprintf("merchant_mobile_%s", mobile)
	//1分钟 能获取一次
	exists, _ := redisHelper.Exists(c, cacheKey).Result()
	if exists != 0 {
		expireTime := redisHelper.TTL(c,cacheKey).Val().Seconds()
		msg := fmt.Sprintf(S.langUtil.T("try_again_after_period_time"),tools.ToString(expireTime))
		return errors.New(msg)
	} 
	// 生成4位随机验证码
	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))
	code := fmt.Sprintf("%04d", rnd.Int31n(9999))
	//验证码保存 1 分钟
	redisHelper.Set(c, cacheKey, code, time.Minute) 

	//contentMap := map[string]string{
	//	"code": code,
	//}
	//dataType, _ := json.Marshal(contentMap)
	//content := string(dataType)
	//// 通过阿里云短信接口发送验证码
	//err := tools.AliSmsSend(mobile, string(content))
	//if err != nil {
	//	tools.Logger.Error("发送验证码出错:",err)
	//	return err
	//}
	// 发送验证码
	smsServiceFactory := &factory.SmsServiceFactory{}
	smsService := smsServiceFactory.CreateSmsService()
	codeResp, err := smsService.SendVerificationCode(mobile, code)
	if err != nil {
		tools.Logger.Errorf("发送验证码失败: %v \n",err)
		return err
	}
	tools.Logger.Infof("发送验证码成功 结果: %v \n",codeResp)
	return nil
}

// 描述：获取登录后的信息
// 作者：Qurbanjan
// 文件：MerchantService.go
// 修改时间：2024/08/15 13:32
func (s *MerchantService) GetLoginData(c *gin.Context, admin models.Admin, searialNumber string) (merchant.MerchantLoginEntity, error) {
	var loginEntity merchant.MerchantLoginEntity

	// 获取餐厅信息
	resInfo, dealerInfo, areaInfo := s.GetRestaurantByAdmin(c, admin)
	if resInfo == nil {
		// 如果没有找到餐厅信息，返回错误
		return loginEntity, errors.New(s.langUtil.T("merchant_not_found"))
	}

	// 生成 JWT Token
	token, jwtSerialNumber := tools.NewJWTTools().GenerateToken(map[string]interface{}{
		"id":               admin.ID,
		"name":             admin.Name,
		"mobile":           admin.Mobile,
		"type":             admin.Type,
		"searialNumber":    searialNumber,
		"area_id":          admin.AdminAreaID,
		"restaurant_id":    tools.ToInt64(resInfo["id"]),
	})

	// 更新 t_admin 的 jwt_serial_number 字段
	if err := tools.Db.Model(&models.Admin{}).Where("id = ?", admin.ID).Update("jwt_serial_number", jwtSerialNumber).Error; err != nil {
		tools.Logger.Error("无法更新jwt_serial_number字段:", err)
		return loginEntity, fmt.Errorf("无法更新jwt_serial_number字段 %w", err)
	}

	// 检查餐厅状态
	if tools.ToInt(resInfo["state"]) == 0 {
		return loginEntity, errors.New(s.langUtil.T("restaurant_state_close"))
	}

	// 处理登录数据
	merchantTransformer := merchantTransformer.NewMerchantTransformer(c)
	loginEntity = merchantTransformer.Login(token, admin, resInfo, dealerInfo, areaInfo)

	return loginEntity, nil
}

// 描述：验证找回密码时使用的短信验证码
// 作者：Qurbanjan
// 文件：MerchantService.go
// 修改时间：2024/08/19 13:32
func (s *MerchantService) CheckSMSCode(c *gin.Context, mobile string, code string) (string, error){
	redisHelper := tools.GetRedisHelper()
	// 获取缓存的key并判断验证码是否正确
	cacheKey := fmt.Sprintf("merchant_mobile_%s", mobile)
	get := redisHelper.Get(c, cacheKey)
	if get.Val() != code {
		return "", errors.New(s.langUtil.T("captcha_incorrect"))
	}
	// 生成随机字符串并缓存用于找回密码时的二次验证
	randStr := generateRandomString(28)
	cacheRandStr := fmt.Sprintf("merchant_mobile_%s_%s", mobile,randStr)
	redisHelper.Set(c, cacheRandStr, randStr, time.Minute * 5) 
	return randStr , nil
	
}

func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	seededRand := rand.New(rand.NewSource(time.Now().UnixNano())) // 使用当前时间作为种子
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[seededRand.Intn(len(charset))]
	}
	return string(b)
}

// 描述：重置商家端密码
// 作者：Qurbanjan
// 文件：MerchantService.go
// 修改时间：2024/08/19 13:45
func (s *MerchantService) ResetPassword(c *gin.Context,mobile string,password string, randStr string)  (bool, error){
	cacheKey := fmt.Sprintf("merchant_mobile_%s_%s", mobile,randStr)
	redisHelper := tools.GetRedisHelper()
	fmt.Println(cacheKey)
	if redisHelper.Get(c, cacheKey).Val() != randStr {
		return false, errors.New(s.langUtil.T("the_random_string_has_expired_please_resend_the_verification_code"))
	}
	hexPassword := tools.PasswordToHash(password)

	err := tools.GetDB().
		Model(&models.Admin{}).
		Where("mobile = ?", mobile).
		Where("type in (?,?) ", models.AdminTypeRestaurantAdmin, models.AdminTypeRestaurantAdminSub).
		Where("state = 1").
		Update("password", hexPassword).Error;
	if err != nil {
		return false, err
	}

	return true, nil
}

// 描述：删除旧版本token和session
// 作者：Qurbanjan
// 文件：MerchantService.go
// 修改时间：2024/08/20 13:23
func (s *MerchantService) CleanOldSessionAndToken(admin models.Admin) error{
	var db = tools.Db
	var oldSessionIds []int
	if err := db.Table("oauth_sessions").
		Where("oauth_sessions.owner_id = ? ", admin.ID).
		Where("oauth_sessions.owner_type = 'merchant' ").Pluck("oauth_sessions.id", &oldSessionIds).Error; err != nil {
			return err
		}

	if err := db.Table("oauth_access_tokens").
		Where("oauth_access_tokens.session_id in ? ", oldSessionIds).
		Delete(models.OauthAccessTokens{}).Error; err != nil {
			return err
		}
	return nil
}

//弹窗确认
func (s *MerchantService) AlertConfirm(admin models.Admin,resInfo models.Restaurant,typeId int,msgId int) map[string]interface{}{
	
	redisHelper := tools.GetRedisHelper()
	cacheKey := fmt.Sprintf("activity_%d_%d_%d_%d_read", typeId, msgId,resInfo.ID,admin.ID)

	tools.ImportantLog(
		"merchant_advert_confirm",
		admin.ID,
		msgId,
		resInfo.ID,
		"同意抽奖弹窗",
	)
	//2分钟 能获取一次
	exists, _ := redisHelper.Exists(context.Background(), cacheKey).Result()
	if exists == 0 {
		redisHelper.Set(context.Background(), cacheKey, 1, 5*24*time.Hour)
	}
		 
	return nil
}

//抽奖活动 优惠券记录
func (s *MerchantService) LotteryCouponLog(resId int,lang string) []map[string]interface{}{
	
	db :=tools.ReadDb1
	now :=carbon.Now(configs.AsiaShanghai)
	yearStart :=now.AddYears(-1).Format("Y-01-01") //2年开始 
	type LogItem struct {
		OrderPrice int `gorm:"column:order_price"`
		Count int  `gorm:"column:count"`
		OrderCouponPrice int `gorm:"column:order_coupon_price"`
		CouponCount int `gorm:"column:coupon_count"`
		StartUseTime string `gorm:"column:start_use_time"`
		EndUseTime string `gorm:"column:end_use_time"`
	}
	var activities []models.LotteryActivity
	db.Model(&models.LotteryActivity{}).Where("created_at > ?",yearStart).Order("id desc").Find(&activities)
	var results []map[string]interface{}
	for _,activity := range activities{
		var couponIds []int
		db.Model(&models.LotteryActivityLevelCoupon{}).Where("created_at > ?",yearStart).Where("lottery_id = ?",activity.ID).Pluck("coupon_id",&couponIds)
		var result map[string]interface{}
		var logItem LogItem
		fields :=`
			if(sum(t_order.order_price)>0,sum(t_order.order_price),0) as order_price
			,
			if(count(t_coupon_log.id)>0,count(t_coupon_log.id),0) as count
			,
			if(sum(t_coupon.price)>0,sum(t_coupon.price),0) as order_coupon_price
			,
			if(sum(t_coupon.count)>0,sum(t_coupon.count),0) as coupon_count,
			t_coupon.start_use_time,
			t_coupon.end_use_time
		
		`
		db.Model(&models.CouponLog{}).
			Joins("left join t_order on t_order.id=t_coupon_log.order_id").
			Joins("left join t_coupon on t_coupon.id=t_coupon_log.coupon_id").
			Where("t_coupon_log.coupon_id in (?)",couponIds).
			Where("t_coupon_log.state = ?",1).
			Where("t_order.store_id = ? ",resId).
			Where("t_coupon_log.created_at between ? and ?",yearStart,now.Format("Y-m-d H:i:s")).
			Select(fields).
			Scan(&logItem)
		
		result=map[string]interface{}{
			"order_price":logItem.OrderPrice,
			"count":logItem.Count,
			"order_coupon_price":logItem.OrderCouponPrice,
			"coupon_count":logItem.Count,
			"order_coupon_for_dealer":logItem.OrderCouponPrice/2,
			"order_coupon_for_res":logItem.OrderCouponPrice/2,
			"coupon_start_use_time":carbon.Parse(logItem.StartUseTime,configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			"coupon_end_use_time":carbon.Parse(logItem.EndUseTime,configs.AsiaShanghai).Format("Y-m-d H:i:s"),
		}
		lotteryChangeTime :="2024-12-31 23:59:59"
		if carbon.Parse(logItem.StartUseTime,configs.AsiaShanghai).Gt(carbon.Parse(lotteryChangeTime,configs.AsiaShanghai)){
			result["order_coupon_for_dealer"]=logItem.OrderCouponPrice
			result["order_coupon_for_res"]=0
		}
		activityName :=activity.NameUg
		if s.language != "ug" {
			activityName =activity.NameUg
		}
		
		result["activity_name"] = activityName
		result["activity_start_time"] = activity.StartTime.Format("2006-01-02 15:04:05")
		result["activity_end_time"] = activity.EndTime.Format("2006-01-02 15:04:05")
		results = append(results, result)
	}

	
            
	return results
}
// GetConfig 获取AppConfig的值，发送keys ，逗号区分
//  @receiver s
//  @param keys
//  @return []map[string]interface{}
//
func (s *MerchantService) GetConfig(keys string,brand string,deviceAppVersion int64) []map[string]interface{} {
	db := tools.Db
	var rtns []models.AppConfig
	keysArr := strings.Split(keys,",")
	db.Model(&models.AppConfig{}).Where("`key` in ?",keysArr).Find(&rtns)
	rtnsMap := []map[string]interface{}{}
	for _,v := range rtns {
		value :=v.Value
		version := tools.ToInt64(tools.ReplaceString(v.Version,".",""))
		if deviceAppVersion < version {
			value = ""
		}
		if v.Key == "merchant_hide_forground_task" { //华为应用市场要求 隐藏应用
			if strings.ToUpper(brand) != "HUAWEI" {//华为的直接返回 其他的返回0
				value = "0"
			}
		}
		rtnsMap = append(rtnsMap, map[string]interface{}{
			"key":   v.Key,
			"value": value,
		})

	}
	return rtnsMap
}

// GetPartRefundList
//
//  @Author: YaKupJan
//  @Date: 2024-11-29 18:29:37
//  @Description: 部分退款返回信息
//  @receiver s
//  @param orderID
//  @return models.OrderToday
func (s *MerchantService) GetPartRefundList(orderID int) models.OrderToday {
	db := tools.GetDB()
	var order  models.OrderToday
	db.Model(models.OrderToday{}).
		Where("id = ?",orderID).
		Preload("LunchBoxDetail").
		Preload("OrderDetail.RestaurantFoods").
		Preload("OrderDetail.SelectedSpec.FoodSpecOptions").
		Preload("OrderDetail.RestaurantFoods.ComboFoodItems.RestaurantFood").
		Preload("OrderDetail.RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions").
		Find(&order)
	return order
}

// BatchSoldFoods
//
//  @Author: YaKupJan
//  @Date: 2024-12-02 17:32:45
//  @Description: 批量售完美食
//  @receiver s
//  @param foodIds
func (s *MerchantService) BatchSoldFoods(foodIds string, restaurantID int) {
	// 将foodIds按照逗号分隔为ID数组
	ids := strings.Split(foodIds, ",")
	// 将字符串ID数组转换为整数数组（如果需要）
	var intIds []int
	for _, id := range ids {
		parsedId, err := strconv.Atoi(strings.TrimSpace(id))
		if err != nil {
			panic(errors2.NewCustomError(fmt.Sprintf("解析ID失败: %v", err)))
			return
		}
		intIds = append(intIds, parsedId)
	}

	// 更新数据库中的状态，假设状态字段为 `state`，售完的状态为 2
	db := tools.GetDB() // 假设MerchantService中包含一个DB实例
	err := db.Model(models.RestaurantFoods{}).
		Where("id IN (?)", intIds).
		Where("restaurant_id = ?", restaurantID).
		Update("state", models.RESTAURANTFOODS_STATE_SOLDOUT).Error
	if err != nil {
		panic(errors2.NewCustomError("update_fail"))
		return
	}

}

// BatchSoldFoodsV2
//
//  @Author: Salam
//  @Date: 2025-06-02 12:09:45
//  @Description: 批量售完美食V2
//  @receiver svc
//  @param foodsJson
func (svc *MerchantService) BatchSoldFoodsV2(foodsJson []food.FoodItem, restaurantID int) {
	// 将字符串ID数组转换为整数数组（如果需要）
	var (
		db        = tools.GetDB()
		foodIds   []int // 普通美食/套餐美食 ID 列表
		optionIds []int // 规格美食 - 子选项 ID 列表
	)

	// 收集两类 ID
	for _, _food := range foodsJson {
		// 所有美食类型，必须有 FoodID
		if _food.FoodID == 0 {
			panic(errors2.NewCustomError("update_fail"))
		}

		if _food.FoodType == models.FoodsComboItemFoodTypeSpec {
			// 规格美食，必须有 OptionIds
			if len(_food.OptionIds) == 0 {
				panic(errors2.NewCustomError("update_fail"))
			}
			optionIds = append(optionIds, _food.OptionIds...)
		} else {
			foodIds = append(foodIds, _food.FoodID)
		}
	}

	// 下架 普通美食 / 套餐美食
	if len(foodIds) > 0 {
		// 更新数据库中的状态，假设状态字段为 `state`，售完的状态为 2
		db := tools.GetDB() // 假设MerchantService中包含一个DB实例
		err := db.Model(models.RestaurantFoods{}).
			Where("id IN (?)", foodIds).
			Where("restaurant_id = ?", restaurantID).
			Update("state", models.RESTAURANTFOODS_STATE_SOLDOUT).Error
		if err != nil {
			panic(errors2.NewCustomError("update_fail"))
			return
		}
	}

	// 关闭 规格美食 - 子选项的状态
	if len(optionIds) > 0 {
		// 更新数据库中的状态，假设状态字段为 `state`，售完的状态为 2
		err := db.Model(&models.FoodSpecOption{}).
			Where("id IN (?)", optionIds).
			Where("restaurant_id = ?", restaurantID).
			Update("state", models.FoodSpecOptionItemStateOff).Error
		if err != nil {
			panic(errors2.NewCustomError("update_fail"))
			return
		}
	}
}

