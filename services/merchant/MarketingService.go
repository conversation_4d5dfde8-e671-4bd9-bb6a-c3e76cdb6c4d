package merchant

import (
	"encoding/json"
	"errors"
	"math"
	"sort"
	"time"

	"fmt"
	"mulazim-api/configs"
	"mulazim-api/inits"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/services"
	"mulazim-api/tools"
	"reflect"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type MarketingService struct {
	langUtil *lang.LangUtil
	language string
	baseService services.BaseService
}

func NewMarketingService(c *gin.Context) *MarketingService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	marketing := MarketingService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &marketing
}

/***
 * @Author: [rozimamat]
 * @description: 活动首页数据
 * @Date: 2023-03-04 17:05:39
 * @param {*gin.Context} c
 */
func (marketing MarketingService) GetMarketing(c *gin.Context) map[string]interface{} {

	result := make(map[string]interface{})
	//临时静态资源返回
	adv := make([]map[string]interface{}, 0)

	adv = append(adv, map[string]interface{}{
		"image_url":   tools.AddCdn("restaurant/smart_1.jpg"),
		"image_title": "marketing1",
	})
	adv = append(adv, map[string]interface{}{
		"image_url":   tools.AddCdn("restaurant/smart_2.jpg"),
		"image_title": "marketing2",
	})

	version := 200 //最低版本 2.0.0
	if c.GetHeader("Version-Name") != ""{
		version2 :=tools.ToInt(tools.ReplaceString(c.GetHeader("Version-Name"),".",""))
		if version2 > 0 {
			version = version2
		}
	}
	osType       :=1 //默认 android
	if c.GetHeader("Ostype") != "" {
		osType       = tools.ToInt(c.GetHeader("Ostype"))
	}
	var marketTypes []map[string]interface{}
	query :=tools.Db.Table("b_marketing_type").Where("state=1")
	if osType == 1 {
		query=query.Where("android_version < ?",version)
	}else{
		query=query.Where("ios_version < ?",version)
	}
	query.Order("sort asc").Scan(&marketTypes)
	mkt := make([]map[string]interface{}, 0)
	for _, v := range marketTypes {
		mkt = append(mkt, map[string]interface{}{
			"marketing_type":  v["id"], //满减
			"marketing_image": tools.If(marketing.language == "ug", v["img_ug"], v["img_zh"]),
			"marketing_title": tools.If(marketing.language == "ug", v["name_ug"], v["name_zh"]),                 // marketing.langUtil.T("marketing_type1_description"),
			"marketing_des":   tools.If(marketing.language == "ug", v["description_ug"], v["description_zh"]),   // marketing.langUtil.T("marketing_type1_rule"),
			"marketing_rule":  tools.If(marketing.language == "ug", v["market_rules_ug"], v["market_rules_zh"]), // marketing.langUtil.T("marketing_type1_rule"),
			"agreement_url":  tools.If(marketing.language == "ug", v["agreement_url_ug"], v["agreement_url_zh"]), 
		})

	}

	// mk_img := tools.AddCdn("merchant/activity/manjian_zh.png")
	// if marketing.language == "ug" {
	// 	mk_img = tools.AddCdn("merchant/activity/manjian_ug.png")
	// }

	stat := make([]map[string]interface{}, 0)
	stat = append(stat, map[string]interface{}{
		"stastic_panel_image": tools.AddCdn("restaurant/price_discount.jpg"),
	})

	result["adver_list"] = adv
	result["notify_content"] = marketing.GetMarketingHomeInformation("home_page_notify_content") // marketing.langUtil.T("marketing_home_page_notify_content")
	result["marketing"] = mkt
	result["statstic"] = stat
	return result
}

// GetMarketingList
//
// @Description: 获取营销活动列表(分页)
// @Author: Rixat
// @Time: 2023-03-02 10:40:52
// @receiver
// @param c *gin.Context
func (marketing MarketingService) GetMarketingList(resId int, marketingType int, state int, tp int, page int, limit int) map[string]interface{} {
	if limit == 0 {
		limit = 10
	}
	if page == 0 {
		page = 1
	}
	result := map[string]interface{}{
		"page":  page,
		"limit": limit,
	}
	// 根据餐厅和活动类型查询活动
	if tp == 0 {
		tp = 1
	}
	marketingModel := tools.Db.Table("t_marketing").Where("restaurant_id=? and type = ?", resId, tp).Where("marketing_type", marketingType)
	if state != 0 {
		marketingModel.Where("state", state)
	}
	var total int64
	marketingModel.Count(&total) // 总数
	result["total"] = total
	var marketingList []models.Marketing // 活动列表
	marketingModel.Select("id", "restaurant_id", "name_ug", "name_zh", "type", "begin_date", "end_date", "state", "creator_type", "creator_id", "customer_type").
		Order("id desc").Limit(limit).Offset(limit * (page - 1)).Find(&marketingList)

	// 返回结果格式化
	var marketingMapArr []map[string]interface{}
	for _, value := range marketingList {
		valueMap := make(map[string]interface{})
		valueMap["id"] = value.ID
		if marketing.language == "ug" {
			valueMap["name"] = value.NameUg
		} else {
			valueMap["name"] = value.NameZh
		}
		valueMap["type"] = value.Type
		valueMap["begin_date"] = value.BeginDate.Format("2006-01-02")
		valueMap["end_date"] = value.EndDate.Format("2006-01-02")
		valueMap["state"] = value.State
		valueMap["creator_id"] = value.CreatorID
		valueMap["creator_type"] = value.CreatorType
		if value.CreatorType == 2 {
			valueMap["creator_type_name"] = marketing.langUtil.T("created_by_owner")
		} else if value.CreatorType == 1 {
			valueMap["creator_type_name"] = marketing.langUtil.T("created_by_admin")
		}

		now := time.Now()
		dif := (value.EndDate.Sub(time.Now()).Hours() / 24)
		fmt.Println(now)
		fmt.Println(dif)
		valueMap["remaining_date"] = math.Ceil((value.EndDate.Sub(time.Now()).Hours() / 24)) + 1
		if tools.ToInt(valueMap["remaining_date"]) <= 0 {
			valueMap["state"] = 3
		}
		marketingMapArr = append(marketingMapArr, valueMap)
	}
	if marketingMapArr == nil {
		result["items"] = make([]map[string]interface{}, 0)
	} else {
		result["items"] = marketingMapArr
	}

	return result
}

// DeleteMarketing
//
// @Description: 删除活动
// @Author: Rixat
// @Time: 2023-03-04 10:30:07
// @receiver
// @param c *gin.Context
func (marketing MarketingService) DeleteMarketing(marketId int) error {
	// 没有下单的所有活动可以删除
	market := models.Marketing{}
	tools.Db.Table("t_marketing").Where("id", marketId).First(&market)
	if market.ID == 0 {
		return errors.New("marketing_not_found")
	}
	// 有订单记录的活动不能删除
	marketLog := models.MarketingOrderLog{}
	tools.Db.Table("t_marketing_order_log").Where("marketing_id", market.ID).First(&marketLog)
	if marketLog.ID != 0 {
		return errors.New("marketing_not_deletable")
	}
	//删除前面 给一个状态
	updateMap := make(map[string]interface{})
	updateMap["state"] = 4
	updateMap["running_state"] = 0
	err1 := tools.Db.Table("t_marketing").Where("id", marketId).Updates(&updateMap).Error
	if err1 != nil {
		return errors.New("marketing_delet_failed")
	}
	err := tools.Db.Table("t_marketing").Where("id", marketId).Delete(&market).Error
	if err != nil {
		return errors.New("marketing_delet_failed")
	}
	return nil
}

/***
 * @Author: [rozimamat]
 * @description: 创建满减活动
 * @Date: 2023-03-04 11:19:16
 * @param {models.MarketingParam} params
 */
func (marketing MarketingService) CreateMarketing(params models.MarketingParam, apiVersion uint8) (bool, string) {

	success, msg, rs := marketing.CheckMarketing(params, apiVersion)

	if !success {
		return success, msg
	}

	db := tools.Db

	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	success, err, marketingId := marketing.MarketingWrite(tx, rs)
	if len(err) > 0 {
		tx.Rollback()
		return false, err
	}

	tx.Commit()
	// 异步处理
	go func() {

		defer func() {
			if err := recover(); err != nil { //用于捕获panic
				// fmt.Println("xxx")
			}
		}()
		marketing.UpadateMarketingState(marketingId, 1) //创建活动后默认开启活动

	}()
	return success, msg
}

func (marketing MarketingService) MarketingWrite(tx *gorm.DB, rs map[string]interface{}) (bool, string, int) {

	var marketState models.MarketingRestaurantState
	tx.Where("restaurant_id = ?", rs["restaurant_id"]).First(&marketState)

	flag := true
	for k := range rs {
		if strings.EqualFold(k, "createdat") || strings.EqualFold(k, "created_at") {
			flag = false
		}
	}
	if flag {
		rs["created_at"] = carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s")
		rs["updated_at"] = carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s")
	}

	err := tx.Model(models.Marketing{}).Create(rs).Error

	if err != nil {

		return false, err.Error(), 0
	}
	marketStateMap := make(map[string]interface{})
	var resC int64
	tx.Model(models.MarketingRestaurantState{}).Where("restaurant_id = ?", rs["restaurant_id"]).Select("id").Count(&resC)
	if resC == 0 {
		marketStateMap["city_id"] = rs["city_id"]
		marketStateMap["area_id"] = rs["area_id"]
		marketStateMap["restaurant_id"] = rs["restaurant_id"]
		marketStateMap["is_have_market"] = 1
		cr := tx.Model(models.MarketingRestaurantState{}).Create(marketStateMap)
		err = cr.Error
		if err != nil {

			return false, err.Error(), 0
		}
	}
	var m models.Marketing
	if err := tx.Last(&m).Error; err != nil {
		return false, err.Error(), 0
	}
	return true, "", m.ID

}

/***
 * @Author: [rozimamat]
 * @description: 监测满减活动参数
 * @Date: 2023-03-04 11:18:59
 * @param {models.MarketingParam} params
 */
func (marketing MarketingService) CheckMarketing(params models.MarketingParam, apiVersion uint8) (bool, string, map[string]interface{}) {

	db := tools.Db
	success := true
	msg := ""

	if params.Type == 2 {
		// api V1 逻辑
		if apiVersion == 1 && len(params.Foods) == 0 {
			//选择 商品活动的话 至少存在一个美食
			return false, "at_least_one_food_required", nil
		} else if apiVersion == 2 && len(params.FoodsJson) == 0 {
			// api V2 逻辑
			return false, "at_least_one_food_required", nil
		}
	}

	/**
	 * 主要规则

	 * 1.全店活动
	 *          1.同一个时间段只能有一个活动
	 *              1.全星期模式
	 *                  1.全时间段模式
	 *                  2.分时间段模式
	 *
	 *              2.分天模式
	 *                  1.全时间段模式
	 *                  2.分时间段模式
	 * 2.全星期
	 *          1.至少选择一天
	 * 3.全时间段
	 *          1.至少选择一个时间段 ，结束必须在开始后
	 * 4.价格阶梯
	 *          1.下一个价格必须大于上一个，满减金额不能大于 开始价格
	 *

	 *
	 */
	BeginDate := params.BeginDate
	EndDate := params.EndDate
	/**
	 * 日期检测
	 * 结束日期不能大于开始日期
	 */

	today := carbon.Now().Format("Y-m-d")

	d := carbon.Parse(today).DiffInDays(carbon.Parse(BeginDate))
	if d < 0 {
		success = false
		msg = "begin_time_must_be_after_today"
		return success, msg, nil

	}

	d = carbon.Parse(BeginDate).DiffInDays(carbon.Parse(EndDate))
	if d < 0 {
		success = false
		msg = "second_time_must_be_after_first"
		return success, msg, nil

	}

	resInfo := models.Restaurant{} // 店铺信息
	db.Model(models.Restaurant{}).Where("id = ?", params.RestaurantID).Select("id,city_id,area_id").First(&resInfo)
	if resInfo.ID == 0 {
		success = false
		msg = "restaurant_not_found"
		return success, msg, nil
	}

	FullWeekState := params.FullWeekState
	FullTimeState := params.FullTimeState

	weekMap := make(map[string]interface{})
	timeMap := make(map[string]interface{})

	steps := tools.StringToMapArr(params.Steps)

	if len(steps) > 5 {
		return false, "only_5_level_are_allowed", nil
	}

	t := reflect.TypeOf(params)
	v := reflect.ValueOf(params)

	for i := 0; i < t.NumField(); i++ {
		key := strings.ToLower(t.Field(i).Name)
		vl := v.Field(i).Interface()

		if strings.HasPrefix(key, "day") {
			weekMap[key] = vl

		}
		if strings.HasPrefix(key, "time") {
			timeMap[key] = vl
		}

	}

	/**
	 * 4.全星期
	 *          1.至少选择一天
	 */
	Day := ""
	if FullWeekState == 1 {

		DayCount := 0
		var dayData []map[string]interface{}
		//星期选择的话要检查是否全部是空的
		Days := make(map[string]interface{})
		for i := 1; i < 8; i++ {
			v := 0
			key := "day" + strconv.Itoa(i)
			if weekMap[key] == 1 {
				v = 1
				DayCount++
			}
			Days["day"+strconv.Itoa(i)] = v
			Day += strconv.Itoa(v)
			dayData = append(dayData, map[string]interface{}{
				"w": i,
				"v": strconv.Itoa(v),
			})
		}
		if DayCount == 0 {

			success = false
			msg = "full_week_empty_days"
			return success, msg, nil
		}
		//判断 选择的开始和结束日期 时间段内是否存在 周一到周天的时间  WTF
		if d < 7 {
			//日期的开始和结束小于7
			var dayDays []int
			ds := tools.ToInt(d + 1)
			for i := 0; i < ds; i++ {
				startDate := carbon.Parse(BeginDate).AddDays(tools.ToInt(i))
				dayOfWeek := startDate.DayOfWeek()
				if dayOfWeek == 0 {
					dayOfWeek = 7 //星期天
				}
				dayDays = append(dayDays, dayOfWeek)

			}
			sort.Ints(dayDays)
			for i := 0; i < len(dayData); i++ {
				w := tools.ToInt(dayData[i]["w"])

				v := tools.ToInt(dayData[i]["v"])
				if v == 1 {
					flag := true
					for j := 0; j < len(dayDays); j++ {
						if w == tools.ToInt(dayDays[j]) {
							flag = false
						}
					}
					if flag {
						success = false
						msg = "day_" + strconv.Itoa(w) + "_not_in_range"
						return success, msg, nil
					}
				}

			}
		}

	}
	if len(Day) == 0 {
		Day = "1111111"
	}

	/**
	 * 5.全时间段
	 *          1.至少选择一个时间段 ，结束必须在开始后
	 */

	Times := make(map[string]interface{})
	TimeCount := 1

	PriceCount := 0

	if FullTimeState == 1 { //分时间段

		//参数是否存在
		//至少一个时间段会存在而且不能为空
		if len(params.Time1Start) == 0 || len(params.Time1End) == 0 {
			success = false
			msg = "full_time_empty_time"
			return success, msg, nil

		}
		t1Start := params.Time1Start
		t1End := params.Time1End
		t1StartStr := params.BeginDate + " " + t1Start + ":00"
		t1EndStr := params.BeginDate + " " + t1End + ":00"

		mi := carbon.Parse(t1StartStr).DiffInMinutes(carbon.Parse(t1EndStr))
		if mi <= 0 {
			success = false
			msg = "second_time_must_be_after_first"
			return success, msg, nil
		}

		Times["time1start"] = params.Time1Start
		Times["time1end"] = params.Time1End
		TimeCount = 1
		for i := 2; i < 4; i++ { //第二个开始 开始填写的话 必须填写结束 ,结束必须大于开始,下一个阶梯的开始必须大于上一个的结束
			TimeStart := ""
			TimeEnd := ""
			if timeMap["time"+strconv.Itoa(i)+"start"] != nil {
				if len(tools.ToString(timeMap["time"+strconv.Itoa(i)+"start"])) == 0 {
					Times["time"+strconv.Itoa(i)+"start"] = TimeStart
					Times["time"+strconv.Itoa(i)+"end"] = TimeEnd
					continue

				}
				TimeStart = tools.ToString(timeMap["time"+strconv.Itoa(i)+"start"])
				TimeEnd = tools.ToString(timeMap["time"+strconv.Itoa(i)+"end"])

				mi := carbon.Parse(params.BeginDate + " " + TimeStart + ":00").DiffInMinutes(carbon.Parse(params.BeginDate + " " + TimeEnd + ":00"))

				if mi <= 0 {
					success = false
					msg = strconv.Itoa(i) + "second_time_must_be_after_first"
					return success, msg, nil
				}

				//下一个阶梯的开始必须大于上一个的结束

				if len(tools.ToString(timeMap["time"+strconv.Itoa(i-1)+"end"])) > 0 {
					FirstTimeEnd := tools.ToString(timeMap["time"+strconv.Itoa(i-1)+"end"])
					mi = carbon.Parse(params.BeginDate + " " + FirstTimeEnd + ":00").DiffInMinutes(carbon.Parse(params.BeginDate + " " + TimeStart + ":00"))
					if mi < 0 {
						success = false
						msg = strconv.Itoa(i) + "second_time_must_be_after_first"
						return success, msg, nil
					}
				}

				TimeCount = i
			}
			Times["time"+strconv.Itoa(i)+"start"] = TimeStart
			Times["time"+strconv.Itoa(i)+"end"] = TimeEnd
		}
	} else {
		Times["time1start"] = "00:00"
		Times["time1end"] = "23:59"
		TimeCount = 1
	}

	/**
	 * 6.价格阶梯
	 *          1.下一个价格必须大于上一个，满减金额不能大于 开始价格
	 *          2.下一个减免金额必须大于上一个减免金额
	 */
	PriceMax := 0
	PriceReduceMax := 0

	stepItems := make([]map[string]interface{}, 0)

	for i := 0; i < len(steps); i++ {

		if i == 0 {

			//第一个阶梯需要填写
			ps := steps[i]["price_start"]
			pr := steps[i]["price_reduce"]
			if ps == nil {
				success = false
				msg = "price_must_not_empty"
				return success, msg, nil

			}
			if pr == nil {
				success = false
				msg = "price_reduce_must_not_empty"
				return success, msg, nil

			}

			p1Start, _ := strconv.ParseFloat(tools.ToString(ps), 64)

			p1Reduce, _ := strconv.ParseFloat(tools.ToString(pr), 64)

			if p1Start <= 0 {
				success = false
				msg = "price_must_be_plus"
				return success, msg, nil
			}
			if p1Reduce <= 0 {
				success = false
				msg = "price_must_be_plus"
				return success, msg, nil
			}
			if p1Start <= p1Reduce {
				success = false
				msg = "price_must_not_equal_to_reduce"
				return success, msg, nil
			}

			PriceMax = tools.ToInt(p1Start * 100)
			PriceReduceMax = tools.ToInt(p1Reduce * 100)

			steps[i]["price_start"] = tools.ToInt(p1Start * 100)
			steps[i]["price_reduce"] = tools.ToInt(p1Reduce * 100)
			stepItems = append(stepItems, steps[i])
			PriceCount = 1
		} else {

			ps := steps[i]["price_start"]
			pr := steps[i]["price_reduce"]

			if ps == nil {

				continue

			}

			pStart, _ := strconv.ParseFloat(tools.ToString(ps), 64)

			pEnd, _ := strconv.ParseFloat(tools.ToString(pr), 64)

			//第二个开始 验证 是否符合条件

			//不为空的话 得守规则

			if pStart <= 0 {
				success = false
				msg = "price_must_be_plus"
				return success, msg, nil

			}
			if pEnd <= 0 {
				success = false
				msg = "price_must_be_plus"
				return success, msg, nil

			}
			if pStart <= pEnd {
				// throw new ClientException(trans("merchant.price_must_not_equal_to_reduce"), 1);
				success = false
				msg = "price_must_not_equal_to_reduce"
				return success, msg, nil

			}
			//下一个阶梯的开始必须大于上一个

			pPreStartStr := steps[i-1]["price_start"]
			pPreEndStr := steps[i-1]["price_reduce"]

			pPreStart := tools.ToInt(pPreStartStr)

			pPreEnd := tools.ToInt(pPreEndStr)

			//下一个阶梯的减免金额必须大于上一个
			if pPreStart >= tools.ToInt(pStart*100) {
				success = false
				msg = "next_price_must_bigger_than_previous"
				return success, msg, nil
			}
			if pPreEnd >= tools.ToInt(pEnd*100) {
				success = false
				msg = "next_price_reduce_must_bigger_than_previous"
				return success, msg, nil
			}

			PriceMax = tools.ToInt(pStart * 100)
			PriceReduceMax = tools.ToInt(pEnd * 100)

			steps[i]["price_start"] = tools.ToInt(pStart * 100)
			steps[i]["price_reduce"] = tools.ToInt(pEnd * 100)
			stepItems = append(stepItems, steps[i])
			PriceCount = i + 1

		}
	}

	resultMap := make(map[string]interface{})
	resultMap["creator_id"] = marketing.GetAdminByResid(params.RestaurantID)
	resultMap["city_id"] = resInfo.CityID
	resultMap["area_id"] = resInfo.AreaID
	resultMap["marketing_type"] = params.MarketingType
	resultMap["restaurant_id"] = params.RestaurantID
	resultMap["name_ug"] = params.NameUg
	resultMap["name_zh"] = params.NameZh
	resultMap["type"] = params.Type
	resultMap["begin_date"] = params.BeginDate
	resultMap["end_date"] = params.EndDate
	resultMap["full_week_state"] = params.FullTimeState
	resultMap["day"] = tools.Str2DEC(Day)
	resultMap["full_time_state"] = params.FullTimeState
	resultMap["time1_start"] = Times["time1start"]
	resultMap["time1_end"] = Times["time1end"]
	resultMap["time2_start"] = Times["time2start"]
	resultMap["time2_end"] = Times["time2end"]
	resultMap["time3_start"] = Times["time3start"]
	resultMap["time3_end"] = Times["time3end"]
	resultMap["time_count"] = TimeCount
	resultMap["auto_continue"] = params.AutoContinue

	resultMap["steps"] = tools.MapArrayToString(stepItems)

	resultMap["price_max"] = PriceMax
	resultMap["price_reduce_max"] = PriceReduceMax

	resultMap["price_count"] = PriceCount

	if params.Type == 2 {
		// api V1 逻辑
		if apiVersion == 1 {
			resultMap["foods"] = params.Foods
		} else if apiVersion == 2 {
			// api V2 逻辑
			foodsJson, _ := json.Marshal(params.FoodsJson)
			resultMap["foods_json"] = string(foodsJson)
		}
	}

	return success, msg, resultMap
}

/***
 * @Author: [rozimamat]
 * @description: 编辑满减
 * @Date: 2023-03-04 11:18:16
 * @param {int} id
 */
func (marketing MarketingService) EditMarketing(id int) (bool, string, map[string]interface{}) {
	db := tools.Db

	var mk models.MarketingMap
	db.Where("id = ?", id).First(&mk)
	if mk.ID == 0 {
		return false, "data_not_exists", nil
	}
	mkt := make(map[string]interface{})
	mkContent, _ := json.Marshal(mk)
	json.Unmarshal(mkContent, &mkt)

	mm := marketing.FormatMarketing(mkt)

	foodsList := make([]map[string]interface{}, 0)
	if len(tools.ToString(mm["foods"])) > 0 {
		foodIds := strings.Split(tools.ToString(mm["foods"]), ",")
		var foods []models.RestaurantFoods
		db.Model(foods).Where("id in (?)", foodIds).Scan(&foods)
		for _, food := range foods {
			foodsList = append(foodsList, map[string]interface{}{
				"id":    food.ID,
				"name":  tools.GetNameByLang(food, marketing.language),
				"image": tools.CdnUrl(food.Image),
			})
		}
	}
	mm["foods_list"] = foodsList

	return true, "msg", mm
}

/***
 * @Author: [Salam]
 * @description: 满减详情
 * @Date: 2025-05-28 15:37:16
 * @param {int} id
 */
func (marketing MarketingService) GetOne(id int) (*models.Marketing, error) {
	db := tools.Db

	var mkt models.Marketing
	db.Where("id = ?", id).First(&mkt)
	if mkt.ID == 0 {
		return nil, errors.New("data_not_exists")
	}
	return &mkt, nil
}

/***
 * @Author: [rozimamat]
 * @description: 格式化满减活动
 * @Date: 2023-03-04 11:17:53
 * @param {models.MarketingMap} mk
 */
func (marketing MarketingService) FormatMarketing(mk map[string]interface{}) map[string]interface{} {

	if mk["begin_date"] != nil {
		mk["begin_date"] = carbon.Parse(mk["begin_date"].(string)).Format("Y-m-d")
	}
	if mk["end_date"] != nil {
		mk["end_date"] = carbon.Parse(mk["end_date"].(string)).Format("Y-m-d")
	}
	if mk["created_at"] != nil {
		mk["created_at"] = carbon.Parse(mk["created_at"].(string)).Format("Y-m-d H:i:s")
	}

	marketTime := make([]map[string]interface{}, 0)
	for i := 1; i < 4; i++ {
		if mk["time"+strconv.Itoa(i)+"_start"] != nil {
			if len(mk["time"+strconv.Itoa(i)+"_start"].(string)) > 0 {
				marketTime = append(marketTime, map[string]interface{}{
					"time_start": mk["time"+strconv.Itoa(i)+"_start"].(string),
					"time_end":   mk["time"+strconv.Itoa(i)+"_end"].(string),
				})
			}
		}
	}

	mk["market_time"] = marketTime

	if mk["day"] != nil {
		ds, _ := strconv.Atoi(mk["day"].(string))
		days := tools.DecimalToBinary(ds)
		if len(days) < 7 {
			zeros := ""
			for i := 0; i < (7 - len(days)); i++ {
				zeros += "0"
			}
			days = zeros + days
		}
		mk["day1"], _ = strconv.Atoi(days[0:1])
		mk["day2"], _ = strconv.Atoi(days[1:2])
		mk["day3"], _ = strconv.Atoi(days[2:3])
		mk["day4"], _ = strconv.Atoi(days[3:4])
		mk["day5"], _ = strconv.Atoi(days[4:5])
		mk["day6"], _ = strconv.Atoi(days[5:6])
		mk["day7"], _ = strconv.Atoi(days[6:7])
	}
	if mk["steps"] != nil {
		steps := tools.StringToMapArr(mk["steps"].(string))

		for i := 0; i < len(steps); i++ {

			ps := float64(tools.ToInt(steps[i]["price_start"]))
			pr := float64(tools.ToInt(steps[i]["price_reduce"]))

			price_start, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", ps/100), 64)
			price_end, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", pr/100), 64)

			steps[i]["price_start"] = price_start
			steps[i]["price_reduce"] = price_end

			ti := ""

			if marketing.language == "ug" {
				ti = " پەشتاق [" + strconv.Itoa(i+1) + "] " + tools.ToString(price_start) + " يۈەن گە توشسا " + tools.ToString(price_end) + " يۈەن ئېتىبار "
			} else {
				ti = " 阶梯 [" + strconv.Itoa(i+1) + "] 满" + tools.ToString(price_start) + " 减 " + tools.ToString(price_end) + ""
			}
			steps[i]["step_title"] = ti

		}

		mk["steps"] = steps

		mk["step_items"] = steps

	}
	if mk["price_max"] != nil {

		mk["price_max"] = mk["price_max"].(float64) / 100
	}
	if mk["price_reduce_max"] != nil {

		mk["price_reduce_max"] = mk["price_reduce_max"].(float64) / 100
	}
	if mk["creator_type"] != nil {

		if mk["creator_type"].(float64) == 1 {
			mk["creator_type_name"] = marketing.langUtil.T("created_by_admin")
		} else if mk["creator_type"] == 2 {
			mk["creator_type_name"] = marketing.langUtil.T("created_by_owner")
		}
	}
	if mk["state"] != nil {
		stt := tools.ToString(mk["state"])
		mk["state_name"] = marketing.langUtil.T("marketing_state_" + stt)

	}

	return mk
}

// UpdateMarketing
//
// @Description: 更新活动信息
// @Author: Rixat
// @Time: 2023-03-03 17:14:42
// @receiver
// @param c *gin.Context
func (marketing MarketingService) UpdateMarketing(params models.MarketingParam, id int, apiVersion uint8) error {
	var market models.Marketing
	tools.Db.Table("t_marketing").Where("id=?", id).First(&market)
	if market.ID == 0 {
		return errors.New("marketing_not_found")
	}
	if market.State != 2 {
		//
		var orderCount int64 // 没下过单的 活动可以编辑
		tools.Db.Table("t_marketing_order_log").Where("marketing_id = ?", id).Select("id").Count(&orderCount)
		if orderCount > 0 {
			return errors.New("marketing_not_editable")
		}

	}
	// 检查更新内容是否能更新
	ok, msg, updateParams := marketing.CheckMarketing(params, apiVersion)
	if !ok {
		return errors.New(msg)
	}
	// 更新活动信息
	err := tools.Db.Table("t_marketing").Where("id", id).Updates(&updateParams).Error
	if err != nil {
		return errors.New("failed")
	}
	return nil
}

// UpadateMarketingState
//
// @Description: 修改活动状态
// @Author: Rixat
// @Time: 2023-03-03 17:15:40
// @receiver
// @param c *gin.Context
func (marketing MarketingService) UpadateMarketingState(id int, state int) error {
	var market models.Marketing
	tools.Db.Table("t_marketing").Where("id=?", id).First(&market)
	if market.ID == 0 {
		return errors.New("marketing_not_found")
	}
	if state == 0 {
		return errors.New("marketing_enable_change")
	}
	mName :=market.NameUg
	if marketing.language !="ug" {
		mName =market.NameZh
	}

	if state == 1 {
		if market.State == 3 || market.State == 4 {
			return errors.New("marketing_not_editable")
		}
		if market.State == 1 {
			return nil
		}

		// 验证同一个时间端是否存在活动
		conflict := marketing.baseService.IsMarketingTimeConflict(market,marketing.language)
		if conflict != nil {
			return errors.New(fmt.Sprintf(marketing.langUtil.T("merketing_conflict_not_editable"), conflict.Error()))
		}
		if len(market.Foods) > 0{
			foods :=  strings.Split(market.Foods,",")
			multiDiscountConflict :=false
			multiDiscountConflictFoodName :=""
			var markets []models.Marketing
			markets = append(markets, market)
			timesMarkets :=  marketing.baseService.GetMarketArrTimeMap(markets,marketing.language)
			

			for _, ff := range foods {
				// 监测多份打折
				// 获取该餐厅正在运行的满减活动列表
				for _, inValue := range timesMarkets {
					inBeginTime := inValue["start"]
					inEndTime := inValue["end"]
					_,foodName :=marketing.baseService.CheckFoodsMultipleDiscount(marketing.language,tools.ToInt(ff),inBeginTime,inEndTime)	
					if len(foodName) >0 {
						multiDiscountConflict =true
						multiDiscountConflictFoodName =foodName
					}
				}
				if multiDiscountConflict {
					return fmt.Errorf(fmt.Sprintf("[%s] %s [%s] %s",mName,marketing.langUtil.T("and"),multiDiscountConflictFoodName,marketing.langUtil.T("time_conflict_in_multi_discount")))
				}
			}
				
			
		}
	}
	// 更新状态
	updateMap := make(map[string]interface{})
	updateMap["state"] = state
	updateMap["running_state"] = 0
	err := tools.Db.Table("t_marketing").Where("id", id).Updates(updateMap).Error
	if err != nil {
		return errors.New("failed")
	}
	return nil
}

/***
 * @Author: [rozimamat]
 * @description: 满减详细页面统计
 * @Date: 2023-03-04 11:17:26
 * @param {int} id
 */
func (marketing MarketingService) MarketingDetail(id int) (bool, string, map[string]interface{}) {

	m := marketing.MarketSteps(id)
	m["share_image"] = configs.MyApp.CdnUrl + "upload/bfoods/201701/377.jpg" //活动海报 分享用的
	return true, "", m
}



/***
 * @Author: [rozimamat]
 * @description: 满减阶梯
 * @Date: 2023-03-04 11:17:06
 * @param {int} id
 */
func (marketing MarketingService) MarketSteps(id int) map[string]interface{} {

	var ViewCount int64
	OrderCount := 0
	OrderSuccess := 0
	OrderCancel := 0

	BuyCount := 0
	ReduceAmount := 0.0

	OrderPrice := 0.0

	db := tools.Db

	var m models.MarketingMap
	db.Where("id = ?", id).First(&m)

	db.Where("marketing_id = ?", id).Select("order_id").Count(&ViewCount)

	var stepCount int

	db.Select("sum(JSON_LENGTH(steps)) as stepCount").Table("t_marketing").
		Where(" id = ?", id).Scan(&stepCount)

	//获取头部总的优惠价格，总订单量，成功订单量，失败订单量
	var headersByState []map[string]interface{}
	db.Select(
		"count(t_marketing_order_log.order_id) as order_count,"+
			"sum(step_reduce)/100 as step_reduce,"+
			"if(ISNULL(t_order_today.state), t_order.state,t_order_today.state) as state,"+
			"sum(IF(ISNULL(t_order_today.price),t_order.price+t_order.lunch_box_fee,t_order_today.price+t_order_today.lunch_box_fee))/100 as price").
		Table("t_marketing_order_log").
		Joins("left join t_order on t_order.id = t_marketing_order_log.order_id").
		Joins("left join t_order_today on t_order_today.id = t_marketing_order_log.order_id").
		Where("t_marketing_order_log.marketing_id = ?", id).
		Where("(t_order.state >= 7 or t_order_today.state >= 7)").
		Group("state").
		Scan(&headersByState)
	var headers = make(map[string]interface{})
	headers["marketing_count"] = stepCount
	headers["order_cancel"] = 0.0
	headers["order_success"] = 0.0
	headers["order_total"] = 0.0
	headers["reduce_all"] = 0.0
	headers["order_count"] = 0
	headers["step_count"] = stepCount
	for _, v := range headersByState {
		if tools.ToInt(v["state"]) == 7 {
			headers["order_success"] = tools.ToFloat64(headers["order_success"]) + tools.ToFloat64(v["order_count"])
			headers["order_total"] = tools.ToFloat64(headers["order_total"]) + tools.ToFloat64(v["price"])
			headers["reduce_all"] = tools.ToFloat64(headers["reduce_all"]) + tools.ToFloat64(v["step_reduce"])
		} else {
			headers["order_cancel"] = tools.ToFloat64(headers["order_cancel"]) + tools.ToFloat64(v["order_count"])
		}

		headers["order_count"] = headers["order_count"].(int) + tools.ToInt(v["order_count"])
	}

	OrderCount = tools.ToInt(headers["order_count"])
	OrderSuccess = tools.ToInt(headers["order_success"])
	OrderCancel = tools.ToInt(headers["order_cancel"])

	BuyCount = tools.ToInt(headers["order_success"])
	ReduceAmount = tools.ToFloat64(headers["reduce_all"])

	OrderPrice = tools.ToFloat64(tools.ToPrice(headers["order_total"]))

	var allSteps []map[string]interface{}
	db.Select("jt.price_start,jt.price_reduce").Table(`	t_marketing,
	JSON_TABLE (
		steps -> '$',
	'$[*]' COLUMNS ( price_start INT PATH '$.price_start', price_reduce INT PATH '$.price_reduce' )) AS jt `).
		Where(" id = ?", id).
		Group(`jt.price_start,jt.price_reduce `).
		Order(`jt.price_start,jt.price_reduce`).Scan(&allSteps)
	var curSteps []map[string]interface{}
	db.Select(`step_start,
step_reduce,
t_marketing_order_log.order_id,
count( t_marketing_order_log.order_id ) AS order_count,
sum( step_reduce )/ 100 AS total_step_reduce,
sum(IF (ISNULL(t_order.price),t_order_today.price+t_order_today.lunch_box_fee,t_order.price+t_order.lunch_box_fee))/100 AS price`).
		Table("t_marketing_order_log").
		Joins("LEFT JOIN t_order ON t_order.id = t_marketing_order_log.order_id").
		Joins("LEFT JOIN t_order_today ON t_order_today.id = t_marketing_order_log.order_id ").
		Where("t_marketing_order_log.marketing_id = ?", id).
		Where("t_order.state=7 OR t_order_today.state=7").
		Group("step_start,step_reduce ").
		Order("step_start,step_reduce ").
		Scan(&curSteps)

	for _, v := range allSteps {
		has := false
		for _, vv := range curSteps {
			if (tools.ToInt(v["price_start"]) == tools.ToInt(vv["step_start"])) && (tools.ToInt(v["price_reduce"]) == tools.ToInt(vv["step_reduce"])) {
				v["order_count"] = vv["order_count"]
				v["price_all"] = tools.ToFloat64(vv["price"])
				v["reduce_all"] = tools.ToFloat64(vv["total_step_reduce"])
				if marketing.language == "ug" {
					v["step_title"] = fmt.Sprintf("%s يۈەن گە توشسا %s يۈەن ئېتىبار", tools.ToPrice(tools.ToFloat64(v["price_start"].(int64))/100), tools.ToPrice(tools.ToFloat64(v["price_reduce"].(int64))/100))
				} else {
					v["step_title"] = fmt.Sprintf("满%s元减%s元", tools.ToPrice(tools.ToFloat64(v["price_start"].(int64))/100), tools.ToPrice(tools.ToFloat64(v["price_reduce"].(int64))/100))
				}
				has = true
				break
			}
		}
		if !has {
			v["order_count"] = 0
			v["price_all"] = 0
			v["reduce_all"] = 0
			if marketing.language == "ug" {
				v["step_title"] = fmt.Sprintf("%s يۈەن گە توشسا %s يۈەن ئېتىبار", tools.ToPrice(tools.ToFloat64(v["price_start"].(int64))/100), tools.ToPrice(tools.ToFloat64(v["price_reduce"].(int64))/100))
			} else {
				v["step_title"] = fmt.Sprintf("满%s元减%s元", tools.ToPrice(tools.ToFloat64(v["price_start"].(int64))/100), tools.ToPrice(tools.ToFloat64(v["price_reduce"].(int64))/100))
			}
		}
		v["price_start"] = tools.ToFloat64(v["price_start"]) / 100
		v["price_reduce"] = tools.ToFloat64(v["price_reduce"]) / 100
	}
	if allSteps == nil {
		allSteps = []map[string]interface{}{}
	}

	m.ViewCount = tools.ToInt(ViewCount)
	m.BuyCount = BuyCount
	m.OrderCount = OrderCount
	m.ReduceAmount = tools.ToFloat64(tools.ToPrice(ReduceAmount))
	m.OrderPrice = tools.ToFloat64(OrderPrice)
	m.OrderSuccess = OrderSuccess
	m.OrderCancel = OrderCancel

	mkt := make(map[string]interface{})
	mkContent, _ := json.Marshal(m)
	json.Unmarshal(mkContent, &mkt)

	mm := marketing.FormatMarketing(mkt)

	mm["steps"] = allSteps
	mm["step_items"] = allSteps
	mm["show_top"] = false

	var foodItems []map[string]interface{}

	if len(m.Foods) > 0 {

		// 无论 下单与否  美食数据都要存在
		var originalFoodItems []map[string]interface{}
		originalFoods := make([]map[string]interface{}, 0)

		fields := "id,name_" + marketing.language + ",image,price"

		ffs := tools.ToString(mm["foods"])

		db.Model(models.RestaurantFoods{}).Table("t_restaurant_foods").Where("id IN (?)", strings.Split(ffs, ",")).Select(fields).Find(&originalFoods)

		for i := 0; i < len(originalFoods); i++ {

			foodItem := make(map[string]interface{})
			for k, v := range originalFoods[i] {
				key := strings.ToLower(k)

				switch key {

				case "id":
					foodItem["id"] = tools.ToString(v)
				case "name_" + marketing.language:
					foodItem["name"] = tools.ToString(v)
				case "image":
					img := tools.ToString(v)
					img = strings.TrimPrefix(img, "/")
					foodItem["image"] = configs.MyApp.CdnUrl + img

				case "price":
					foodItem["price"] = tools.ToPrice(tools.ToFloat64(v) / 100)
				}
				foodItem["seled"] = "0"
				foodItem["order_total_num"] = "0"

			}
			originalFoodItems = append(originalFoodItems, foodItem)

		}

		var fds []map[string]interface{}
		db.Select(
			"t_restaurant_foods.name_"+marketing.language+" ,"+
				"t_restaurant_foods.id, "+
				"t_restaurant_foods.image, "+
				"t_marketing_order_log.order_id,"+
				"jt.id,"+
				"sum( jt.count ) AS order_total_num,"+
				"jt.price as food_price,"+
				"sum( jt.count * jt.price ) AS order_total_price ").
			Table("t_marketing_order_log ").
			Joins("LEFT JOIN t_order ON t_order.id = t_marketing_order_log.order_id").
			Joins("LEFT JOIN t_order_today ON t_order_today.id = t_marketing_order_log.order_id").
			Joins(",JSON_TABLE (foods,'$[*]' COLUMNS (id INT PATH '$.id',count INT PATH '$.count',price INT PATH '$.price')) AS jt  LEFT JOIN t_restaurant_foods ON t_restaurant_foods.id = jt.id").
			Where(" (t_order.state >= 7 OR t_order_today.state >= 7 ) AND t_marketing_order_log.marketing_id = ?", id).
			Group("jt.id").Scan(&fds)

		for i := 0; i < len(fds); i++ {

			foodItem := make(map[string]interface{})
			for k, v := range fds[i] {
				key := strings.ToLower(k)

				switch key {
				case "order_total_price":
					foodItem["seled"] = tools.ToString(v)

				case "order_total_num":
					foodItem["order_total_num"] = tools.ToString(v)
				case "id":
					foodItem["id"] = tools.ToString(v)
				case "name_" + marketing.language:
					foodItem["name"] = tools.ToString(v)
				case "image":
					img := tools.ToString(v)
					img = strings.TrimPrefix(img, "/")
					foodItem["image"] = configs.MyApp.CdnUrl + img

				case "food_price":
					foodItem["price"] = tools.ToPrice(tools.ToFloat64(v))
				}
			}
			foodItems = append(foodItems, foodItem)

		}
		//没有下单的情况下 美食数据为空 处理
		if len(fds) == 0 {
			foodItems = originalFoodItems
		} else {
			// 统计中 只出现 下过单的 美食的数据 ，所以要加上 没有下过单的 美食数据
			if len(fds) < len(originalFoodItems) {
				for i := 0; i < len(originalFoodItems); i++ {
					flag := false
					for j := 0; j < len(fds); j++ {
						if tools.ToInt(originalFoodItems[i]["id"]) == tools.ToInt(fds[j]["id"]) {
							flag = true
							break
						}
					}
					if !flag {
						foodItems = append(foodItems, originalFoodItems[i])
					}
				}
			}
		}

	}

	mm["foods"] = foodItems

	return mm
}

/***
 * @Author: [rozimamat]
 * @description: 满减统计
 * @Date: 2023-03-04 11:13:40
 * @param {string} startDate
 * @param {string} endDate
 */
func (marketing MarketingService) MarketingStatistics(beginDate string, endDate string, resId int, target int) (bool, string, map[string]interface{}) {
	beginDate += " 00:00:00"
	endDate += " 23:59:59"
	db := tools.Db
	//获取相关活动IDS

	var marketIdsInt []int
	marketsIdsQuery := db.Select("id").Table("t_marketing").Where("t_marketing.marketing_type = 1").
		Where("((t_marketing.begin_date BETWEEN ? and ? )"+
			" or (t_marketing.end_date BETWEEN ? and ? )"+
			" or (t_marketing.begin_date<=? and t_marketing.end_date >= ?))", beginDate, endDate, beginDate, endDate, beginDate, endDate).
		Where(" restaurant_id = ?", resId)
	if target == 2 {
		marketsIdsQuery.Where("t_marketing.type = 1")
	} else if target == 3 {
		marketsIdsQuery.Where("t_marketing.type = 2")
	}
	marketsIdsQuery.Pluck("id", &marketIdsInt)

	if target == 1 || target == 2 || target == 3 {
		//获取阶梯数量
		var stepCount int

		if len(marketIdsInt) > 0 {
			db.Select("sum(JSON_LENGTH(steps)) as stepCount").Table("t_marketing").
				Where(" id in (?)", marketIdsInt).Scan(&stepCount)
		}

		//获取头部总的优惠价格，总订单量，成功订单量，失败订单量
		var headersByState []map[string]interface{}
		db.Select(
			"sum(step_reduce)/100 as step_reduce,"+
				"if(ISNULL(t_order_today.state), t_order.state,t_order_today.state) as state,"+
				"sum(IF(ISNULL(t_order_today.price),t_order.price+t_order.lunch_box_fee,t_order_today.price+t_order_today.lunch_box_fee))/100 as price").
			Table("t_marketing_order_log").
			Joins("left join t_order on t_order.id = t_marketing_order_log.order_id").
			Joins("left join t_order_today on t_order_today.id = t_marketing_order_log.order_id").
			Where("t_marketing_order_log.marketing_id in (?)", marketIdsInt).
			Where("t_marketing_order_log.created_at BETWEEN ? and ?", beginDate, endDate).
			Where("(t_order.state >= 7 or t_order_today.state >= 7)").
			Group("state").
			Scan(&headersByState)
		var headers = make(map[string]interface{})
		headers["marketing_count"] = len(marketIdsInt)
		headers["order_cancel"] = 0.0
		headers["order_success"] = 0.0
		headers["order_total"] = 0.0
		headers["reduce_all"] = 0.0
		headers["step_count"] = stepCount
		for _, v := range headersByState {
			if tools.ToInt(v["state"]) == 7 {
				headers["order_success"] = tools.ToFloat64(headers["order_success"]) + tools.ToFloat64(v["price"])
				headers["reduce_all"] = tools.ToFloat64(headers["reduce_all"]) + tools.ToFloat64(v["step_reduce"])
			} else {
				headers["order_cancel"] = tools.ToFloat64(headers["order_cancel"]) + tools.ToFloat64(v["price"])
			}
			headers["order_total"] = tools.ToFloat64(headers["order_total"]) + tools.ToFloat64(v["price"])

		}
		headers["reduce_all"] = tools.ToFloat64(tools.ToPrice(headers["reduce_all"]))
		headers["order_total"] = tools.ToFloat64(tools.ToPrice(headers["order_total"]))
		headers["order_success"] = tools.ToFloat64(tools.ToPrice(headers["order_success"]))
		headers["order_cancel"] = tools.ToFloat64(tools.ToPrice(headers["order_cancel"]))
		// 列表数据获取
		var rtnList []map[string]interface{}
		db.Select("t_marketing.id,t_marketing.name_"+marketing.language+" as name,t_marketing.state,t_marketing.running_state,DATE_FORMAT(t_marketing.begin_date,'%Y-%m-%d') as begin_date,DATE_FORMAT(t_marketing.end_date,'%Y-%m-%d') as end_date,if(isnull(Z.price),0,Z.price) as price,if(isnull(Z.step_reduce),0,Z.step_reduce) as step_reduce").
			Table("t_marketing").
			Joins(`left join (SELECT
	t_marketing_order_log.marketing_id,
	sum( step_reduce )/ 100 as step_reduce,
		sum(
	IF
		(
			ISNULL( t_order_today.price ),
			t_order.price + t_order.lunch_box_fee,
			t_order_today.price + t_order_today.lunch_box_fee 
		))/ 100 AS price 
FROM
	t_marketing_order_log
	LEFT JOIN t_order ON t_order.id = t_marketing_order_log.order_id
	LEFT JOIN t_order_today ON t_order_today.id = t_marketing_order_log.order_id 
WHERE
	marketing_id IN  ?  
	AND (
		t_order.state = 7 
		OR t_order_today.state = 7 
	) 
	AND t_marketing_order_log.created_at BETWEEN ? and ?
GROUP BY
	t_marketing_order_log.marketing_id)
as Z  on Z.marketing_id = t_marketing.id`, marketIdsInt, beginDate, endDate).
			Where("t_marketing.id in ?", marketIdsInt).
			Scan(&rtnList)

		data := make(map[string]interface{})
		for _, v := range rtnList {
			v["price"] = tools.ToFloat64(v["price"])
			v["step_reduce"] = tools.ToFloat64(v["step_reduce"])
		}
		if rtnList == nil {
			rtnList = []map[string]interface{}{}
		}
		data["items"] = rtnList
		data["head"] = headers
		return true, "", data
	} else {
		var allSteps []map[string]interface{}
		db.Select("jt.price_start,jt.price_reduce").Table(`	t_marketing,
	JSON_TABLE (
		steps -> '$',
	'$[*]' COLUMNS ( price_start INT PATH '$.price_start', price_reduce INT PATH '$.price_reduce' )) AS jt `).
			Where(" restaurant_id = ?", resId).
			Where(" t_marketing.marketing_type = ?", 1).
			Group(`jt.price_start,jt.price_reduce `).
			Order(`jt.price_start,jt.price_reduce`).Scan(&allSteps)
		var curSteps []map[string]interface{}
		db.Select(`step_start,
step_reduce,
t_marketing_order_log.order_id,
count( t_marketing_order_log.order_id ) AS order_count,
sum( step_reduce )/ 100 AS total_step_reduce,
sum(IF (ISNULL(t_order.price),t_order_today.price+t_order_today.lunch_box_fee,t_order.price+t_order.lunch_box_fee))/100 AS price`).
			Table("t_marketing_order_log").
			Joins("LEFT JOIN t_order ON t_order.id = t_marketing_order_log.order_id").
			Joins("LEFT JOIN t_order_today ON t_order_today.id = t_marketing_order_log.order_id ").
			Where("t_marketing_order_log.created_at BETWEEN ? AND ? ", beginDate, endDate).
			Where("t_marketing_order_log.marketing_id in ?", marketIdsInt).
			Where("t_order.state=7 OR t_order_today.state=7").
			Group("step_start,step_reduce ").
			Order("step_start,step_reduce ").
			Scan(&curSteps)

		for _, v := range allSteps {
			has := false
			for _, vv := range curSteps {
				if (tools.ToInt(v["price_start"]) == tools.ToInt(vv["step_start"])) && (tools.ToInt(v["price_reduce"]) == tools.ToInt(vv["step_reduce"])) {
					v["order_count"] = vv["order_count"]
					v["price_all"] = tools.ToFloat64(vv["price"])
					v["reduce_all"] = tools.ToFloat64(vv["total_step_reduce"])
					if marketing.language == "ug" {
						v["step_title"] = fmt.Sprintf("%s يۈەن گە توشسا %s يۈەن ئېتىبار", tools.ToPrice(tools.ToFloat64(v["price_start"].(int64))/100), tools.ToPrice(tools.ToFloat64(v["price_reduce"].(int64))/100))
					} else {
						v["step_title"] = fmt.Sprintf("满%s元减%s元", tools.ToPrice(tools.ToFloat64(v["price_start"].(int64))/100), tools.ToPrice(tools.ToFloat64(v["price_reduce"].(int64))/100))
					}
					has = true
					break
				}
			}
			if !has {
				v["order_count"] = 0
				v["price_all"] = 0
				v["reduce_all"] = 0
				if marketing.language == "ug" {
					v["step_title"] = fmt.Sprintf("%s يۈەن گە توشسا %s يۈەن ئېتىبار", tools.ToPrice(tools.ToFloat64(v["price_start"].(int64))/100), tools.ToPrice(tools.ToFloat64(v["price_reduce"].(int64))/100))
				} else {
					v["step_title"] = fmt.Sprintf("满%s元减%s元", tools.ToPrice(tools.ToFloat64(v["price_start"].(int64))/100), tools.ToPrice(tools.ToFloat64(v["price_reduce"].(int64))/100))
				}
			}
			v["price_start"] = tools.ToFloat64(v["price_start"]) / 100
			v["price_reduce"] = tools.ToFloat64(v["price_reduce"]) / 100
		}
		if allSteps == nil {
			allSteps = []map[string]interface{}{}
		}
		return true, "", map[string]interface{}{
			"items": allSteps,
		}
	}

}

/***
 * @Author: [rozimamat]
 * @description: 参与代理组织的活动
 * @Date: 2023-03-04 11:45:37
 * @param {int} groupId
 * @param {int} adminId
 * @param {string} passWord
 */
func (marketing MarketingService) MarketingJoin(groupId int, adminId int, resId int, passWord string) (bool, string) {
	db := tools.Db

	//验证是否已经参加过
	var t1 models.MarketingStaticMap
	db.Where("group_id =? and restaurant_id = ?", groupId, resId).First(&t1)
	if t1.ID > 0 {
		return false, "marketing_group_added"
	}

	resInfo := models.Restaurant{} // 店铺信息
	db.Model(models.Restaurant{}).Where("id = ?", resId).Select("id,city_id,area_id").First(&resInfo)
	if resInfo.ID == 0 {
		return false, "restaurant_not_found"
	}

	//验证密码
	var admin models.Admin
	db.Model(admin).Where("id = ?", adminId).
		Where("type = 5").Where("state = 1").
		Where("deleted_at IS NULL").First(&admin)
	if admin.ID == 0 {
		return false, "admin_is_not_active"
	}
	attemptedHashValue := tools.PasswordToHash(passWord)
	if attemptedHashValue != admin.Password {
		return true, "admin_password_error"
	}

	//验证活动模板
	var template models.MarketingGroupTemplate
	db.Where("id =?", groupId).First(&template)
	if template.ID == 0 {
		return false, "marketing_group_template_not_found"
	}

	tempMap := tools.Struct2Map(template)
	var mm models.Marketing
	mMap := tools.Struct2Map(mm)

	//开始加入活动
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for k, v := range tempMap {
		for kk := range mMap {
			if strings.EqualFold(k, kk) {
				mMap[kk] = v
			}

		}

	}

	delete(mMap, "ID")
	delete(mMap, "CreatedAt")
	delete(mMap, "UpdatedAt")
	delete(mMap, "DeletedAt")
	mMap["GroupId"] = groupId
	mMap["RestaurantID"] = resId
	mMap["CityID"] = resInfo.CityID
	mMap["AreaID"] = resInfo.AreaID

	_, msg, lastId := marketing.MarketingWrite(tx, mMap)
	if len(msg) > 0 {
		tx.Rollback()
		return false, msg
	}

	marketAttendMap := make(map[string]interface{})

	marketAttendMap["template_id"] = groupId
	marketAttendMap["marketing_id"] = lastId
	marketAttendMap["restaurant_id"] = resId
	marketAttendMap["admin_id"] = adminId
	// 写入参与活动日志
	err := tx.Model(models.MarketingGroupTemplateAttendance{}).Create(marketAttendMap).Error
	if err != nil {
		tx.Rollback()
		return false, err.Error()
	}

	tx.Commit()

	return true, "msg"
}

/***
 * @Author: [rozimamat]
 * @description: 根据店铺id 获取 管理员id
 * @Date: 2023-03-07 11:20:33
 * @param {int} resId
 */
func (marketing MarketingService) GetAdminByResid(resId int) int {

	db := tools.GetDB()
	type admin struct {
		Id int
	}
	var adm admin
	db.Table("t_admin").Select("t_admin.id").
		Joins("LEFT JOIN `b_admin_store` ON `t_admin`.`id`=`b_admin_store`.`admin_id` ").
		Where("t_admin.type = ?", 5). //商家
		Where("t_admin.state = ?", 1).
		Where("b_admin_store.store_id = ?", resId).Find(&adm)

	return adm.Id

}

// 活动 首页 静态内容 内容
func (marketing MarketingService) GetMarketingHomeInformation(name string) string {
	//静态内容 以后要改
	lg := marketing.langUtil.Lang
	//广告内容json
	content := tools.ReadJson(inits.ConfigFilePath + "/data/" + name + "_" + lg + ".txt")
	return content
}
