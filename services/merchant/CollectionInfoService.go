package merchant

import (
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"mime/multipart"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/errors"
	"mulazim-api/inits"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources"
	"mulazim-api/services/merchant/selfsign"
	"net/url"

	"time"

	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"

	// "mulazim-api/resources/ocrEntity"
	// "math/rand"
	"mulazim-api/tools"
	"os"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	// "mulazim-api/services/merchant/selfsign"
)

type CollectionInfoService struct {
	langUtil *lang.LangUtil
	language string
}

// NewCollectionInfoService
//
//	@Time 2023-01-12 10:54:19
//	<AUTHOR>
//	@Description: 初始化
//	@param c
//	@return *MerchantService
func NewCollectionInfoService(c *gin.Context) *CollectionInfoService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	collection := CollectionInfoService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &collection
}

// UploadFile
//
//	@Time 2023-01-10 13:03:01
//	<AUTHOR>
//	@Description: 上传文件
//	@receiver ums ChinaumsService
//	@param file
//	@return interface{}
func (ums CollectionInfoService) UploadFile(c *gin.Context, file *multipart.FileHeader, docType string, resId string) (fileMap map[string]string, errMsg string) {

	basePath := configs.MyApp.UploadRootDir            // 上传文件的基础路径
	filePath := "/images/self-sign/ums/" + resId + "/" // 保存文件路径

	lastStr := basePath[len(basePath)-1:]
	if lastStr == `/` {
		basePath = basePath[:len(basePath)-1]
	}
	fullPath := basePath + filePath // 文件完整路径
	dots := strings.Split(file.Filename, ".")
	fileType := dots[len(dots)-1] // 文件类型

	//随机文件名
	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))
	rndStrNum := fmt.Sprintf("%06v", rnd.Int31n(1000000))
	var now = time.Now()
	rndStr := fmt.Sprintf("%d%d%d%d%d%d%s", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), rndStrNum) //随机数
	fileName := docType + "_" + rndStr + "." + fileType                                                                            // 文件名

	fileFullPath := fullPath + fileName //  文件完整路径+文件名
	// fileSize := strconv.FormatInt(file.Size, 10) //  文件大小

	fileDbName := filePath + fileName         //  文件在数据库中的路径
	err := os.MkdirAll(fullPath, os.ModePerm) // 创建文件夹
	if err != nil {
		errMsg = "创建文件夹失败"
		return
	}

	//判断文件大小 根据ocr 要求 压缩 文件

	// 获取大小的借口

	//0001,1002, 0011,1003  身份证OCR  格式为 jpg或png，宽和⾼大于 8px，⼩于等于4000px，大小不能超过1MB
	//0025 银行卡OCR  格式为 jpg或png，宽和⾼大于 8px，⼩于等于4000px，大小不能超过1MB
	//0002 营业执照OCR  格式为 jpg或png，宽和⾼⼩于等于4000px，大小不能超过1MB，图片不支持旋转
	//1001  食品经营许可证  图像数据，BASE64编码后进行URLENCODE，要求base64编码和URLENCODE后大小不超过2M，最短边至少15PX，最长边最大4096PX,支持JPG/PNG/BMP格式
	// tools.Logger.Info("压缩文件")
	//1 mb
	mb := tools.ToInt64(1024 * 1024)
	// 512 kb
	halfMb := tools.ToInt64(512 * 1024)

	fileSize := tools.ToInt64(file.Size)

	if fileSize > 15*mb {
		errMsg = "file_too_big"
		return
	}

	if fileSize > halfMb { // 超过 512 KB 就得压缩
		tools.Logger.Info("压缩文件")
		//0001,1002, 0011,1003  身份证OCR  格式为 jpg或png，宽和⾼大于 8px，⼩于等于4000px，大小不能超过1MB
		//压缩图片
		// tools.Logger.Info("fileSize", (tools.ToInt64(fileSize) / mb))

		files, fileHeader, _ := c.Request.FormFile("file")
		byteData := make([]byte, fileHeader.Size)
		files.Read(byteData)

		quality := float32(fileSize-mb) / float32(fileSize) * 100
		// tools.Logger.Info("quality", quality)

		qualityStr := strconv.FormatInt(int64(quality), 10)

		fileErr := tools.FileCreate(byteData, fileFullPath, qualityStr) //压缩的时候保存

		if fileErr != nil { // 保存营业执照失败
			// errMsg = "保存营业执照失败"
			errMsg = "save_img_fail"
			return
		}

	}

	fileExists, _ := tools.PathExists(fileFullPath) //压缩的时候保存过的 不用再次保存
	if !fileExists {
		fileSaveErr := c.SaveUploadedFile(file, fileFullPath) // 保存营业执照
		if fileSaveErr != nil {                               // 保存营业执照失败
			// errMsg = "保存营业执照失败"
			errMsg = "save_img_fail"
			return
		}
	}

	mp := map[string]string{
		"fileFullPath": fileFullPath,
		"fileUrl":      fileDbName[1:],
	}

	fileMap = mp

	return

}

// SaveShopLicenseInfo
//
//	@Time 2023-01-09 16:39:19
//	<AUTHOR>
//	@Description:  保存商户信息
//	@receiver ums ChinaumsService
func (ums CollectionInfoService) SaveShopLicenseInfo(restId int, cityId int, areaId int, LicenseImg string, RegMerType string, ShopLicenseNum string, ShopName string, LegalName string, RegAddress string, RegCapital string, BusinessScope string, ShopLicenseLimitedType string, ShopLicenseStart string, ShopLicenseEnd string) (bool, string) {
	if ShopLicenseLimitedType == "1" {
		ShopLicenseEnd = "9999-12-31"
	}
	db := tools.Db
	SelfSignMerchantInfo := models.SelfSignMerchantInfo{} // 商户信息
	db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).First(&SelfSignMerchantInfo)
	ShopLicenseLimitedTypeInt, _ := strconv.Atoi(ShopLicenseLimitedType) //   营业执照类型 转换为int类型 0 短期 1 长期

	//  未保存过 创建
	if SelfSignMerchantInfo.Id == 0 {
		spLicenseStart := carbon.ParseByFormat(ShopLicenseStart, "Y-m-d").Carbon2Time()
		spLicenseEnd := carbon.ParseByFormat(ShopLicenseEnd, "Y-m-d").Carbon2Time()
		// 个体工商户
		SelfSignMerchantInfo = models.SelfSignMerchantInfo{
			Type:           		constants.SELF_SIGN_TYPE_RESTAURANT, // 餐厅id
			RestaurantId:           restId, // 餐厅id
			CityId:                 cityId,
			AreaId:                 areaId,
			RegMerType:             RegMerType,                 // 注册商户类型
			ShopLicenseNum:         ShopLicenseNum,             // 营业执照
			ShopName:               ShopName,                   // 商户名称
			LegalName:              LegalName,                  // 法人姓名
			RegAddress:             RegAddress,                 // 注册地址
			RegCapital:             RegCapital,                 // 注册资金
			BusinessScope:          BusinessScope,              // 经营范围
			ShopLicenseLimitedType: &ShopLicenseLimitedTypeInt, // 营业期限类型
			// ShopLicenseStart:       &spLicenseStart,            // 营业执照开始时间
			// ShopLicenseEnd:         &spLicenseEnd,              // 营业执照结束时间
			VerifyContent: "[]",
			// MerIdcardName: LegalName,
			ShopBusinessName: ShopName, //提交营业执照时覆盖店铺名称
		}

		if RegMerType == "00" || RegMerType == "01" {
			err22 := carbon.Parse(ShopLicenseStart).Error
			if err22 != nil { //营业执照开始时间解析失败
				return false, "ShopLicenseStart_error"
			}
			// err23 := carbon.Parse(ShopLicenseEnd).Error
			// if err23 != nil { //营业执照结束时间解析失败
			// 	if ShopLicenseEnd != "长期" {
			// 		return false,"ShopLicenseEnd_error"
			// 	}

			// }
			SelfSignMerchantInfo.ShopLicenseStart = &spLicenseStart
			SelfSignMerchantInfo.ShopLicenseEnd = &spLicenseEnd
		}

		createErr := db.Create(&SelfSignMerchantInfo).Error
		if createErr != nil {
			tools.Logger.Info("保存商户信息失败", createErr.Error())
			return false, "server_error"
		}
		createErr = ums.savaImageByRestIdAndDocType(db, restId, LicenseImg)
		if createErr != nil {
			tools.Logger.Info("保存营业执照失败", createErr.Error())
			return false, "server_error"
		}

		//  已保存过  修改信息
	} else {

		// 个体工商户
		splStart := carbon.ParseByFormat(ShopLicenseStart, "Y-m-d").Carbon2Time()
		spLicenseEnd := carbon.ParseByFormat(ShopLicenseEnd, "Y-m-d").Carbon2Time()
		if len(ShopLicenseStart) > 0 {
			_,err := time.Parse(time.DateOnly,ShopLicenseStart)
			if err != nil {
				return false, "permit_start_time_err"
			}
		}
		if len(ShopLicenseEnd) > 0 && ShopLicenseEnd!= "9999-12-31" {
			_,err := time.Parse(time.DateOnly,ShopLicenseEnd)
			if err != nil {
				return false, "permit_end_time_err"
			}
		}
		// 修改
		tx := db.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
		SelfSignMerchantInfo.RestaurantId = restId
		SelfSignMerchantInfo.CityId = cityId
		SelfSignMerchantInfo.AreaId = areaId
		SelfSignMerchantInfo.RegMerType = RegMerType
		SelfSignMerchantInfo.ShopLicenseNum = ShopLicenseNum
		SelfSignMerchantInfo.ShopName = ShopName
		SelfSignMerchantInfo.LegalName = LegalName

		SelfSignMerchantInfo.ShopBusinessName = ShopName //提交营业执照时覆盖店铺名称

		SelfSignMerchantInfo.RegAddress = RegAddress
		SelfSignMerchantInfo.RegCapital = RegCapital
		SelfSignMerchantInfo.BusinessScope = BusinessScope
		SelfSignMerchantInfo.ShopLicenseLimitedType = &ShopLicenseLimitedTypeInt
		if RegMerType == "00" || RegMerType == "01" {
			err22 := carbon.Parse(ShopLicenseStart).Error
			if err22 != nil { //营业执照开始时间解析失败
				tx.Rollback()
				return false, "ShopLicenseStart_error"
			}
			// err23 := carbon.Parse(ShopLicenseEnd).Error
			// if err23 != nil { //营业执照结束时间解析失败
			// 	if ShopLicenseEnd != "长期" {
			// 		return false,"ShopLicenseEnd_error"
			// 	}

			// }
			SelfSignMerchantInfo.ShopLicenseStart = &splStart
			SelfSignMerchantInfo.ShopLicenseEnd = &spLicenseEnd
		}
		updateErr := tx.Model(&SelfSignMerchantInfo).Updates(SelfSignMerchantInfo).Error
		if updateErr != nil {
			tx.Rollback()
			tools.Logger.Info("修改商户信息失败", updateErr.Error())
			return false, "server_error"
		}
		// 删除受益人

		deleteBnfErr := tx.Where("mer_info_id = ?", SelfSignMerchantInfo.Id).Delete(models.SelfSignBnf{}).Error

		if deleteBnfErr != nil {
			tx.Rollback()
			tools.Logger.Info("删除受益人失败", deleteBnfErr.Error())
			return false, "server_error"
		}
		createErr := ums.savaImageByRestIdAndDocType(tx, restId, LicenseImg)
		if createErr != nil {
			tx.Rollback()
			tools.Logger.Info("保存营业执照失败", createErr.Error())
			return false, "server_error"
		}
		tx.Commit() // 提交事务
	}
	return true, ""
}

// SaveCompanyLicenseInfo
//
//	@Time 2023-01-16 12:51:20
//	<AUTHOR>
//	@Description: 保存企业信息  第一页信息
//	@receiver s CollectionInfoService
func (s CollectionInfoService) SaveCompanyLicenseInfo(restId int, cityId int, areaId int, LicenseImg string, RegMerType string, ShopLicenseNum string, ShopName string, LegalName string, RegAddress string, RegCapital string, BusinessScope string, ShopLicenseLimitedType string, ShopLicenseStart string, ShopLicenseEnd string, ShareholderName string, ShareholderIdcard string, ShareholderAddress string, ShareholderIdcardLimitedType string, ShareholderIdcardStart string, ShareholderIdcardEnd string, bnf string) bool {
	if ShopLicenseLimitedType == "1" {
		ShopLicenseEnd = "9999-12-31"
	}
	if ShareholderIdcardLimitedType == "1" {
		ShareholderIdcardEnd = "9999-12-31"
	}
	db := tools.Db
	SelfSignMerchantInfo := models.SelfSignMerchantInfo{} // 商户信息
	db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).First(&SelfSignMerchantInfo)
	ShopLicenseLimitedTypeInt, _ := strconv.Atoi(ShopLicenseLimitedType)             //   营业执照类型 转换为int类型 0 短期 1 长期
	ShareholderIdcardLimitedTypeInt, _ := strconv.Atoi(ShareholderIdcardLimitedType) //   股东身份证类型 转换为int类型 0 短期 1 长期
	// 解析params.Bnf 为map
	type Bnf struct {
		BnfName              string `json:"bnf_name" validate:"required"`
		BnfIdcardNum         string `json:"bnf_idcard_num" validate:"required"`
		BnfAddress           string `json:"bnf_address" validate:"required"`
		BnfIdcardLimitedType *int   `json:"bnf_idcard_limited_type" validate:"required"`
		BnfIdcardStart       string `json:"bnf_idcard_start" validate:"required"`
		BnfIdcardEnd         string `json:"bnf_idcard_end" validate:"required"`
	}
	bnfStruc := make([]Bnf, 0)
	jsonToStructErr := json.Unmarshal([]byte(bnf), &bnfStruc)
	if jsonToStructErr != nil {
		tools.Logger.Info("保存企业信息  第一页信息 jsonToStructErr", jsonToStructErr.Error())
		panic(jsonToStructErr)
		return false
	}

	validate := validator.New()
	for i, value := range bnfStruc {
		validateErr := validate.Struct(value)
		if validateErr != nil {
			tools.Logger.Info("保存企业信息  第一页信息 validateErr", validateErr.Error())
			tools.Logger.Info(fmt.Sprintf("第%v个验证失败", i))
			panic(validateErr)
			return false
		}
	}

	if SelfSignMerchantInfo.Id == 0 {
		// 开始事务
		splStart := carbon.ParseByFormat(ShopLicenseStart, "Y-m-d").Carbon2Time()
		splEnd := carbon.ParseByFormat(ShopLicenseEnd, "Y-m-d").Carbon2Time()
		shidStart := carbon.ParseByFormat(ShareholderIdcardStart, "Y-m-d").Carbon2Time()
		shidEnd := carbon.ParseByFormat(ShareholderIdcardEnd, "Y-m-d").Carbon2Time()

		tx := db.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
		SelfSignMerchantInfo = models.SelfSignMerchantInfo{
			Type:constants.SELF_SIGN_TYPE_RESTAURANT,
			RestaurantId:                 restId, // 餐厅id
			CityId:                       cityId,
			AreaId:                       areaId,
			RegMerType:                   RegMerType,                       // 注册商户类型
			ShopLicenseNum:               ShopLicenseNum,                   // 营业执照
			ShopName:                     ShopName,                         // 商户名称
			LegalName:                    LegalName,                        // 法人姓名
			RegAddress:                   RegAddress,                       // 注册地址
			RegCapital:                   RegCapital,                       // 注册资金
			BusinessScope:                BusinessScope,                    // 经营范围
			ShopLicenseLimitedType:       &ShopLicenseLimitedTypeInt,       // 营业期限类型
			ShopLicenseStart:             &splStart,                        // 营业执照开始时间
			ShopLicenseEnd:               &splEnd,                          // 营业执照结束时间
			ShareholderName:              ShareholderName,                  // 股东姓名
			ShareholderIdcard:            ShareholderIdcard,                // 股东身份证
			ShareholderAddress:           ShareholderAddress,               // 股东地址
			ShareholderIdcardLimitedType: &ShareholderIdcardLimitedTypeInt, // 股东身份证类型
			ShareholderIdcardStart:       &shidStart,                       // 股东身份证开始时间
			ShareholderIdcardEnd:         &shidEnd,                         // 股东身份证结束时间
			VerifyContent:                "[]",
			BankAcctName:                 ShopName,
			ShopBusinessName:             ShopName, //提交营业执照时覆盖店铺名称
		}
		companyCreateErr := tx.Create(&SelfSignMerchantInfo).Error
		if companyCreateErr != nil {
			tx.Rollback()
			tools.Logger.Info("保存商户信息失败")
			return false
		}
		var Bfns []models.SelfSignBnf // 受益人
		for _, value := range bnfStruc {
			bnfIdcardLimitedTypeInt := value.BnfIdcardLimitedType // 受益人身份证类型 转换为int类型 0 短期 1 长期

			Bfns = append(Bfns, models.SelfSignBnf{
				MerInfoId: SelfSignMerchantInfo.Id,

				BnfName:              value.BnfName,
				BnfIdcardNum:         value.BnfIdcardNum,
				BnfIdcardLimitedType: *bnfIdcardLimitedTypeInt,
				BnfAddress:           value.BnfAddress,
				BnfIdcardStart:       carbon.ParseByFormat(value.BnfIdcardStart, "Y-m-d").Carbon2Time(),
				BnfIdcardEnd:         carbon.ParseByFormat(value.BnfIdcardEnd, "Y-m-d").Carbon2Time(),
			})
		}
		bnfCreateErr := tx.Create(&Bfns).Error
		if bnfCreateErr != nil {
			tx.Rollback()
			tools.Logger.Info("保存受益人信息失败")
			tools.Logger.Info(bnfCreateErr.Error())
			return false
		}
		ImageSaveErr := s.savaImageByRestIdAndDocType(tx, restId, LicenseImg)
		if ImageSaveErr != nil {
			tx.Rollback()
			tools.Logger.Info("保存营业执照失败")
			return false
		}
		tx.Commit() // 提交事务
	} else { // 更新
		// 开始事务
		splStart := carbon.ParseByFormat(ShopLicenseStart, "Y-m-d").Carbon2Time()
		splEnd := carbon.ParseByFormat(ShopLicenseEnd, "Y-m-d").Carbon2Time()
		shidStart := carbon.ParseByFormat(ShareholderIdcardStart, "Y-m-d").Carbon2Time()
		shidEnd := carbon.ParseByFormat(ShareholderIdcardEnd, "Y-m-d").Carbon2Time()
		tx := db.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
		collection := models.SelfSignMerchantInfo{
			Type:constants.SELF_SIGN_TYPE_RESTAURANT,
			RestaurantId:                 restId,                           // 餐厅id
			RegMerType:                   RegMerType,                       // 注册商户类型
			ShopLicenseNum:               ShopLicenseNum,                   // 营业执照
			ShopName:                     ShopName,                         // 商户名称
			LegalName:                    LegalName,                        // 法人姓名
			RegAddress:                   RegAddress,                       // 注册地址
			RegCapital:                   RegCapital,                       // 注册资金
			BusinessScope:                BusinessScope,                    // 经营范围
			ShopLicenseLimitedType:       &ShopLicenseLimitedTypeInt,       // 营业期限类型
			ShopLicenseStart:             &splStart,                        // 营业执照开始时间
			ShopLicenseEnd:               &splEnd,                          // 营业执照结束时间
			ShareholderName:              ShareholderName,                  // 股东姓名
			ShareholderIdcard:            ShareholderIdcard,                // 股东身份证
			ShareholderAddress:           ShareholderAddress,               // 股东地址
			ShareholderIdcardLimitedType: &ShareholderIdcardLimitedTypeInt, // 股东身份证类型
			ShareholderIdcardStart:       &shidStart,                       // 股东身份证开始时间
			ShareholderIdcardEnd:         &shidEnd,                         // 股东身份证结束时间
			BankAcctName:                 ShopName,
		}
		// 更新商户信息
		companyCreateErr := tx.Model(SelfSignMerchantInfo).Updates(&collection).Error
		if companyCreateErr != nil {
			tx.Rollback()
			tools.Logger.Info("修改商户信息失败", companyCreateErr.Error())
			panic(companyCreateErr.Error())
			return false
		}
		var Bfns []models.SelfSignBnf // 受益人
		for _, value := range bnfStruc {
			bnfIdcardLimitedTypeInt := value.BnfIdcardLimitedType // 受益人身份证类型 转换为int类型 0 短期 1 长期

			Bfns = append(Bfns, models.SelfSignBnf{
				MerInfoId: SelfSignMerchantInfo.Id,

				BnfName:              value.BnfName,
				BnfIdcardNum:         value.BnfIdcardNum,
				BnfIdcardLimitedType: *bnfIdcardLimitedTypeInt,
				BnfAddress:           value.BnfAddress,
				BnfIdcardStart:       carbon.ParseByFormat(value.BnfIdcardStart, "Y-m-d").Carbon2Time(),
				BnfIdcardEnd:         carbon.ParseByFormat(value.BnfIdcardEnd, "Y-m-d").Carbon2Time(),
			})
		}
		// 删除受益人

		deleteBnfErr := tx.Where("mer_info_id = ?", SelfSignMerchantInfo.Id).Delete(models.SelfSignBnf{}).Error

		if deleteBnfErr != nil {
			tx.Rollback()
			tools.Logger.Info("删除受益人失败")
			panic(deleteBnfErr)
			return false
		}
		// 添加受益人
		if len(Bfns) == 0 {
			tx.Rollback()
			tools.Logger.Info("受益人不能为空")
			return false
		}
		createBnfErr := tx.Create(&Bfns).Error
		if createBnfErr != nil {
			tx.Rollback()
			tools.Logger.Info("添加受益人失败")
			panic(createBnfErr)
			return false
		}
		ImageSaveErr := s.savaImageByRestIdAndDocType(tx, restId, LicenseImg)
		if ImageSaveErr != nil {
			tx.Rollback()
			tools.Logger.Info("保存营业执照失败")
			return false
		}
		tx.Commit() // 提交事务
	}
	return true
}

// SaveIdCardInfo

// @Time 2023-01-11 18:23:55
// <AUTHOR>
// @Description: 保存身份证信息
// @receiver ums ChinaumsService
// @param restId
// @param MerIdcardName
// @param MerIdcardNum
// @param MerIdcardStart
// @param MerIdcardEnd
// @param MerIsBnf
// @param MerMobile
// @param LegalSex
func (ums CollectionInfoService) SaveIdCardInfo(restId int, IdcardImg string, MerIdcardName string, MerIdcardNum string, MerIdcardStart string, MerIdcardEnd string, MerIsBnf string, MerMobile string, MerIdcardLimitedType string, legalHomeAddress string, merSex int) (bool, string) {
	if MerIdcardLimitedType == "1" { // 短期
		MerIdcardEnd = "9999-12-31"
	}
	db := tools.Db
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	tx.Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).First(&SelfSignMerchantInfo)

	MerIsBnfInt, _ := strconv.Atoi(MerIsBnf)
	// 修改
	midStart := carbon.ParseByFormat(MerIdcardStart, "Y-m-d").Carbon2Time()
	midEnd := carbon.ParseByFormat(MerIdcardEnd, "Y-m-d").Carbon2Time()
	updateContent := models.SelfSignMerchantInfo{

		MerIdcardName:    MerIdcardName,
		MerIdcardNum:     MerIdcardNum,
		MerIdcardStart:   &midStart,
		MerIdcardEnd:     &midEnd,
		MerIsBnf:         &MerIsBnfInt,
		MerMobile:        MerMobile,
		LegalmanHomeAddr: legalHomeAddress,
		MerSex:           &merSex,
		// LegalName:        MerIdcardName,
	}
	if len(MerMobile) > 0 {
		updateContent.MerMobile = MerMobile
		updateContent.BankBindMobile = MerMobile
	}

	if SelfSignMerchantInfo.RegMerType == "02" {
		updateContent.LegalName = MerIdcardName
	}
	//状态为 10 第一次成功入驻后 修改账户信息
	if SelfSignMerchantInfo.State == 10 {
		midStart := carbon.ParseByFormat(MerIdcardStart, "Y-m-d").Carbon2Time()
		midEnd := carbon.ParseByFormat(MerIdcardEnd, "Y-m-d").Carbon2Time()
		updateContent = models.SelfSignMerchantInfo{

			MerIdcardName:  MerIdcardName,
			MerIdcardNum:   MerIdcardNum,
			MerIdcardStart: &midStart,
			MerIdcardEnd:   &midEnd,
			MerIsBnf:       &MerIsBnfInt,
			MerMobile:      MerMobile,
			MerSex:         &merSex,
			AlterState:     1, // 1: 营业执照和法人信息变更 2: 账户信息变更 3：管理员信息变更 4: 许可证 5：门店信息
			State:          1, //0:未提交,1:待审核 ,2:审核未通过 ,3:后台审核通过 ,4:待提交资料 ,5:已提交资料 ,6:资料未通过,7:对公账户待确认  ,8:待在线签约 ,9:待银商入网审核 ,10:银商入网成功,11:银商入网失败,
		}
	}
	updateErr := tx.Model(&SelfSignMerchantInfo).Updates(updateContent).Error
	if updateErr != nil {
		tx.Rollback()
		tools.Logger.Info("修改商户信息失败", updateErr.Error())
		return false, "server_error"
	}
	ImageSaveErr := ums.savaImageByRestIdAndDocType(tx, restId, IdcardImg)
	if ImageSaveErr != nil {
		tx.Rollback()
		tools.Logger.Info("保存身份证失败", ImageSaveErr.Error())
		return false, "server_error"
	}
	tx.Commit()
	return true, ""
}

func (ums CollectionInfoService) SaveAccountInfo(restId int, AcctImg string, bankAcctType int, bankAcctNum string, bankId int, bankCityId int, bankAreaId int, bankBranchName string, bankBranchCode string, bankProvinceId int, bankBindMobile string) (bool, string) {
	db := tools.Db
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			tools.Logger.Error("提交银行卡信息出错resID:", restId)
		}
	}()
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	tx.Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).First(&SelfSignMerchantInfo)
	if SelfSignMerchantInfo.BankAcctType == nil {
		SelfSignMerchantInfo.BankAcctType = new(int)
	}

	updateContent := models.SelfSignMerchantInfo{

		BankAcctType:   &bankAcctType,
		BankAcctNum:    bankAcctNum,
		BankId:         bankId,
		BankProvinceId: bankProvinceId,
		BankCityId:     bankCityId,
		BankAreaId:     bankAreaId,
		BankBranchName: bankBranchName,
		BankBranchCode: bankBranchCode,
		BankAcctName:   tools.If(*SelfSignMerchantInfo.BankAcctType == 0, SelfSignMerchantInfo.LegalName, SelfSignMerchantInfo.ShopName),
	}
	if len(bankBindMobile) > 0 {
		updateContent.MerMobile = bankBindMobile
		updateContent.BankBindMobile = bankBindMobile
	}
	//监测银行卡号码是否正确
	if bankAcctType == 0 { //个人账户
		if !tools.CheckBankCardNumber(bankAcctNum) {
			tx.Rollback()
			return false, "bank_card_error"
		}
	}

	//状态为 10 第一次成功入驻后 修改账户信息

	if SelfSignMerchantInfo.State == 10 {
		//
		updateContent = models.SelfSignMerchantInfo{

			BankAcctType:   &bankAcctType,
			BankAcctNum:    bankAcctNum,
			BankId:         bankId,
			BankProvinceId: bankProvinceId,
			BankCityId:     bankCityId,
			BankAreaId:     bankAreaId,
			BankBranchName: bankBranchName,
			BankBranchCode: bankBranchCode,
			// BankBindMobile: bankBindMobile,

			BankAcctName: tools.If(*SelfSignMerchantInfo.BankAcctType == 0, SelfSignMerchantInfo.LegalName, SelfSignMerchantInfo.ShopName),

			AlterState: 2, // 1: 营业执照和法人信息变更 2: 账户信息变更 3：管理员信息变更 4: 许可证 5：门店信息
			State:      1, //0:未提交,1:待审核 ,2:审核未通过 ,3:后台审核通过 ,4:待提交资料 ,5:已提交资料 ,6:资料未通过,7:对公账户待确认  ,8:待在线签约 ,9:待银商入网审核 ,10:银商入网成功,11:银商入网失败,
		}
	}
	// 修改
	updateErr := tx.Model(&SelfSignMerchantInfo).Updates(updateContent).Error
	if updateErr != nil {
		tx.Rollback()
		tools.Logger.Info("修改商户信息失败", updateErr.Error())
		panic(updateErr.Error())
		return false, ""
	}
	ImageSaveErr := ums.savaImageByRestIdAndDocType(db, restId, AcctImg)
	if ImageSaveErr != nil {
		tx.Rollback()
		tools.Logger.Info("保存银行卡失败", ImageSaveErr.Error())
		return false, ""
	}
	tx.Commit()
	return true, ""
}

// SaveShopInfo
//
//	@Description: 根据地区码和关键字查询支行列表
//	<AUTHOR>
//	@Time 2023-01-12 13:32:53
//	@receiver ums CollectionInfoService
//	@param restId
//	@param shopBusinessName
//	@param shopBusinessCity
//	@param shopBusinessArea
//	@param shopBusinessAddress
//	@param payProduct

func (ums CollectionInfoService) SaveShopInfo(restId int, ShopImg string, shopBusinessName string, shopBusinessCity int, shopBusinessArea int, shopBusinessAddress string, ShopCategoryCode string, shopBusinessProvinceId string) bool {
	db := tools.Db
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	tx.Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).First(&SelfSignMerchantInfo)
	// 修改
	updateContent := models.SelfSignMerchantInfo{

		ShopBusinessName:       shopBusinessName,
		ShopBusinessCityID:     fmt.Sprintf("%d", shopBusinessCity),
		ShopBusinessCountryID:  fmt.Sprintf("%d", shopBusinessArea),
		ShopBusinessAddress:    shopBusinessAddress,
		ShopCategoryCode:       ShopCategoryCode,
		ShopBusinessProvinceID: shopBusinessProvinceId,
	}
	//状态为 10 第一次成功入驻后 修改账户信息

	if SelfSignMerchantInfo.State == 10 {
		updateContent = models.SelfSignMerchantInfo{

			ShopBusinessName:       shopBusinessName,
			ShopBusinessCityID:     fmt.Sprintf("%d", shopBusinessCity),
			ShopBusinessCountryID:  fmt.Sprintf("%d", shopBusinessArea),
			ShopBusinessAddress:    shopBusinessAddress,
			ShopCategoryCode:       ShopCategoryCode,
			ShopBusinessProvinceID: shopBusinessProvinceId,
			AlterState:             5, // 1: 营业执照和法人信息变更 2: 账户信息变更 3：管理员信息变更 4: 许可证 5：门店信息
			State:                  1, //0:未提交,1:待审核 ,2:审核未通过 ,3:后台审核通过 ,4:待提交资料 ,5:已提交资料 ,6:资料未通过,7:对公账户待确认  ,8:待在线签约 ,9:待银商入网审核 ,10:银商入网成功,11:银商入网失败,
		}
	}
	updateErr := tx.Model(&SelfSignMerchantInfo).Updates(updateContent).Error
	if updateErr != nil {
		tx.Rollback()
		tools.Logger.Info("修改商户信息失败", updateErr.Error())
		panic(updateErr.Error())
		return false
	}
	ImageSaveErr := ums.savaImageByRestIdAndDocType(db, restId, ShopImg)
	if ImageSaveErr != nil {
		tx.Rollback()
		tools.Logger.Info("保存门店照失败", ImageSaveErr.Error())
		return false
	}
	tx.Commit()
	return true
}

// GetShopLicenseInfo
//
//	@Time 2023-01-12 11:22:15
//	<AUTHOR>
//	@Description: 获取营业执照信息 获取第一页信息
//	@receiver s CollectionInfoService
//	@param restId

func (s CollectionInfoService) GetShopLicenseInfo(restId int) (models.SelfSignMerchantInfo, []models.SelfSignBnf, models.SelfSignImages) {

	db := tools.Db
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).
		Where("restaurant_id = ?", restId).
		Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
		First(&SelfSignMerchantInfo)
	// 获取受益人信息

	var collectionMerchantBnf []models.SelfSignBnf
	db.Model(models.SelfSignBnf{}).
		Where("mer_info_id = ?", SelfSignMerchantInfo.Id).
		Find(&collectionMerchantBnf)
	// 获取营业执照图片
	var licenseImg models.SelfSignImages
	if SelfSignMerchantInfo.RegMerType == "02" {
		db.Model(models.SelfSignImages{}).Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Where("doc_type in ('0016','0017','0018','0019','0020')").First(&licenseImg)
	} else {
		db.Model(models.SelfSignImages{}).Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Where("doc_type = ?", "0002").First(&licenseImg)
	}
	return SelfSignMerchantInfo, collectionMerchantBnf, licenseImg

}

// GetIdCardInfo
//
//	@Time 2023-01-12 16:42:42
//	<AUTHOR>
//	@Description: 获取身份证信息
//	@receiver s CollectionInfoService
//	@param restId
//	@return models.SelfSignMerchantInfo
//	@return []models.SelfSignImages
func (s CollectionInfoService) GetIdCardInfo(restId int) (models.SelfSignMerchantInfo, []models.SelfSignImages) {
	db := tools.Db
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).
	    Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
		Where("restaurant_id = ?", restId).
		First(&SelfSignMerchantInfo)
	// 获取身份证照片
	var idcardsImg []models.SelfSignImages
	db.Model(models.SelfSignImages{}).Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
		Where("doc_type IN ?", []string{"0001", "0011", "0007"}).Find(&idcardsImg)
	return SelfSignMerchantInfo, idcardsImg
}

// GetAccountInfo
//
//	@Time 2023-01-12 16:42:48
//	<AUTHOR>
//	@Description: 获取账户信息
//	@receiver s CollectionInfoService
//	@param restId
//	@return interface{}
func (s CollectionInfoService) GetAccountInfo(restId int, areaId int) (models.SelfSignMerchantInfo, []models.SelfSignImages, map[string]interface{}) {
	db := tools.Db
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).
		Where("restaurant_id = ?", restId).
		Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
		First(&SelfSignMerchantInfo)
	card := []string{}
	if SelfSignMerchantInfo.BankAcctType == nil {
		card = []string{}
	} else if *SelfSignMerchantInfo.BankAcctType == 0 { // 银行卡
		card = []string{"0025", "0026"}
	} else { // 开户许可证
		card = []string{"0006"}
	}
	// 获取银行名称
	var bankNames map[string]interface{}

	if SelfSignMerchantInfo.BankId > 0 {
		db.Table("b_self_sign_bank").Select("name_zh,name_"+s.language+" as name").Where("id = ?", SelfSignMerchantInfo.BankId).Scan(&bankNames)
	} else {
		var bankArea map[string]interface{}
		er1 := db.Table("b_self_sign_area").Select("p_code ,code").Where(" mlz_area_id = ?", areaId).Scan(&bankArea).Error
		if er1 != nil {
			tools.Logger.Info("获取银商地区失败", er1.Error())
		} else {
			SelfSignMerchantInfo.BankAreaId = tools.ToInt(bankArea["code"])
			SelfSignMerchantInfo.BankCityId = tools.ToInt(bankArea["p_code"])
			SelfSignMerchantInfo.BankProvinceId = 65 //默认新疆
		}

	}

	// 获取银行卡照片
	var bankCardsImg []models.SelfSignImages
	db.Model(models.SelfSignImages{}).Where("restaurant_id = ?", restId).
		Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
		Where("doc_type IN ?", card).Find(&bankCardsImg)

	return SelfSignMerchantInfo, bankCardsImg, bankNames
}

// 为第五页管理员信息 获取第二页的 身份证信息
func (s CollectionInfoService) GetAccountInfoOne(restId int) map[string]interface{} {
	db := tools.Db
	var dt map[string]interface{}
	type adminData struct {
		IdcardName string `gorm:"column:mer_idcard_name" json:"mer_idcard_name"`
		IdcardNum  string `gorm:"column:mer_idcard_num" json:"mer_idcard_num"`
		MerMobile  string `gorm:"column:mer_mobile" json:"mer_mobile"`
	}
	var ad adminData
	db.Model(models.SelfSignMerchantInfo{}).
		Where("restaurant_id = ?", restId).
		Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
		First(&ad)

	data, _ := json.Marshal(&ad)
	json.Unmarshal(data, &dt)

	return dt
}

func (s CollectionInfoService) GetShopInfo(restId int) (models.SelfSignMerchantInfo, []models.SelfSignImages, []map[string]interface{}) {
	db := tools.Db
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).
		Where("restaurant_id = ?", restId).
		Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
		First(&SelfSignMerchantInfo)

	if len(SelfSignMerchantInfo.ShopBusinessCountryID) == 0 {
		area := make(map[string]interface{})
		err := tools.Db.Table("b_self_sign_area").Where("mlz_area_id=?", SelfSignMerchantInfo.AreaId).Scan(&area).Error
		if err == nil && area != nil {
			SelfSignMerchantInfo.ShopBusinessProvinceID = "65"
			if area["p_code"] != nil {
				SelfSignMerchantInfo.ShopBusinessCityID = area["p_code"].(string)
			}
			if area["code"] != nil {
				SelfSignMerchantInfo.ShopBusinessCountryID = area["code"].(string)
			}
		}
	}
	// 获取门店照片
	var bankCardsImg []models.SelfSignImages
	db.Model(models.SelfSignImages{}).Where("restaurant_id = ?", restId).
		Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
		Where("doc_type IN ?", []string{"0015", "0005"}).Find(&bankCardsImg)
	// 获取分类
	categoryList := make([]map[string]interface{}, 0)

	db.Table("b_self_sign_mcc").Select("code,name_"+s.language+" as name ").Where("state = ?", 1).Scan(&categoryList)
	return SelfSignMerchantInfo, bankCardsImg, categoryList

}

// SaveAdministrator
//
//	@Time 2023-01-12 19:37:23
//	<AUTHOR>
//	@Description: 保存管理员信息
//	@receiver s CollectionInfoService
//	@param restId
//	@param shopManagerType
//	@param shopManagerName
//	@param shopManagerIdcard
//	@param shopManagerMobile
//	@param shopManagerEmail
func (s CollectionInfoService) SaveAdministrator(restId int, AdminImg string, shopManagerType int, shopManagerName string, shopManagerIdcard string, shopManagerMobile string, shopManagerEmail string) bool {
	db := tools.Db
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	tx.Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).First(&SelfSignMerchantInfo)
	// 修改

	updateContent := models.SelfSignMerchantInfo{

		ShopManagerType:   shopManagerType,
		ShopManagerName:   shopManagerName,
		ShopManagerIdcard: shopManagerIdcard,
		ShopManagerMobile: shopManagerMobile,
		ShopManagerEmail:  shopManagerEmail,
	}
	//状态为 10 第一次成功入驻后 修改账户信息

	if SelfSignMerchantInfo.State == 10 {
		updateContent = models.SelfSignMerchantInfo{

			ShopManagerType:   shopManagerType,
			ShopManagerName:   shopManagerName,
			ShopManagerIdcard: shopManagerIdcard,
			ShopManagerMobile: shopManagerMobile,
			ShopManagerEmail:  shopManagerEmail,
			AlterState:        3, // 1: 营业执照和法人信息变更 2: 账户信息变更 3：管理员信息变更 4: 许可证 5：门店信息
			State:             1, //0:未提交,1:待审核 ,2:审核未通过 ,3:后台审核通过 ,4:待提交资料 ,5:已提交资料 ,6:资料未通过,7:对公账户待确认  ,8:待在线签约 ,9:待银商入网审核 ,10:银商入网成功,11:银商入网失败,
		}
	}
	updateErr := tx.Model(&SelfSignMerchantInfo).Updates(updateContent).Error
	if updateErr != nil {
		tx.Rollback()
		tools.Logger.Info("修改管理员信息失败", updateErr.Error())
		panic(updateErr.Error())
		return false
	}
	docTypes := []string{"0001", "0011", "0007"}
	if shopManagerType == 2 {
		docTypes = []string{"1005", "1006", "1007"}
	}
	tools.Logger.Info("shopManagerType", shopManagerType)
	tools.Logger.Info("docTypes", docTypes)
	tools.Logger.Info("AdminImg", AdminImg)
	if len(AdminImg) > 5 { //图片内容包括 图片地址

		// if deleteErr != nil {
		// 	tools.Logger.Info("删除管理照片失败")
		// 	tx.Rollback()
		// 	panic(deleteErr.Error())
		// 	return false
		// }
		// 保存管理员身份证照片
		updateErr = s.savaImageByRestIdAndDocType(tx, restId, AdminImg)
		if updateErr != nil {
			tx.Rollback()
			tools.Logger.Info("保存管理员身份证照片失败", updateErr.Error())
			panic(updateErr.Error())
			return false
		}
	}
	tx.Commit()
	return true
}

// SaveBusinessLicense
//  @Time 2023-01-13 10:03:27
//  <AUTHOR>
//  @Description: 保存营业执照信息
//  @receiver s CollectionInfoService
//  @param restId
//  @param permitStartDate
//  @param permitEndDate

func (s CollectionInfoService) SaveBusinessLicense(restId int, ImgSrc string, permitStartDate string, permitEndDate string, permitShopName string, permitLicNum string, permitCreditCode string, permitLegalName string, permitShopAddr string, permitBusinessPremises string, permitState string, permitBusinessType string, permitSuperviseOrg string, permitSuperviseManagers string, permitIssuingAuthority string) (bool,string){
	db := tools.Db
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	db.Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).First(&SelfSignMerchantInfo)
	// 修改

	updateContent := models.SelfSignMerchantInfo{

		PermitShopName:          permitShopName,
		PermitLicNum:            permitLicNum,
		PermitCreditCode:        permitCreditCode,
		PermitLegalName:         permitLegalName,
		PermitShopAddr:          permitShopAddr,
		PermitBusinessPremises:  permitBusinessPremises,
		PermitState:             permitState,
		PermitBusinessType:      permitBusinessType,
		PermitSuperviseOrg:      permitSuperviseOrg,
		PermitSuperviseManagers: permitSuperviseManagers,
		PermitIssuingAuthority:  permitIssuingAuthority,
	}
	if len(permitStartDate) > 0 {
		pStartDate,err := time.Parse(time.DateOnly,permitStartDate)
		if err == nil {
			updateContent.PermitStartDate = &pStartDate
		}else{
			return false, "permit_start_time_err"
		}
	}
	if len(permitEndDate) > 0 {
		pEndDate,err := time.Parse(time.DateOnly,permitEndDate)
		if err == nil {
			updateContent.PermitEndDate = &pEndDate
		}else{
			return false, "permit_end_time_err"
		}
	}
	//状态为 10 第一次成功入驻后 修改账户信息

	if SelfSignMerchantInfo.State == 10 {
		updateContent = models.SelfSignMerchantInfo{

			PermitShopName:          permitShopName,
			PermitLicNum:            permitLicNum,
			PermitCreditCode:        permitCreditCode,
			PermitLegalName:         permitLegalName,
			PermitShopAddr:          permitShopAddr,
			PermitBusinessPremises:  permitBusinessPremises,
			PermitState:             permitState,
			PermitBusinessType:      permitBusinessType,
			PermitSuperviseOrg:      permitSuperviseOrg,
			PermitSuperviseManagers: permitSuperviseManagers,
			PermitIssuingAuthority:  permitIssuingAuthority,
			AlterState:              4, // 1: 营业执照和法人信息变更 2: 账户信息变更 3：管理员信息变更 4: 许可证 5：门店信息
			State:                   1, //0:未提交,1:待审核 ,2:审核未通过 ,3:后台审核通过 ,4:待提交资料 ,5:已提交资料 ,6:资料未通过,7:对公账户待确认  ,8:待在线签约 ,9:待银商入网审核 ,10:银商入网成功,11:银商入网失败,
		}
	}


	updateErr := db.Model(&SelfSignMerchantInfo).Updates(updateContent).Error
	if updateErr != nil {
		tools.Logger.Info("食品经营许可证信息失败", updateErr.Error())
		return false,"info_update_err"
	}
	// 保存营业执照照片
	str := "[{\"img_src\":\"" + ImgSrc + "\",\"doc_type\":\"1001\"}]"
	tools.Logger.Info(str)
	updateErr = s.savaImageByRestIdAndDocType(db, restId, str)
	if updateErr != nil {
		tools.Logger.Info("保存营业执照照片失败", updateErr.Error())
		return false,"info_update_err"
	}
	return true,""
}

// GetAdministrator
//
//	@Time 2023-01-13 10:20:08
//	<AUTHOR>
//	@Description: 获取管理员信息
//	@receiver s CollectionInfoService
//	@param restId
//	@return models.SelfSignMerchantInfo
//	@return []models.SelfSignImages
func (s CollectionInfoService) GetAdministrator(restId int) (models.SelfSignMerchantInfo, []models.SelfSignImages) {
	db := tools.Db
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).
		Where("restaurant_id = ?", restId).
		Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
		First(&SelfSignMerchantInfo)

	// 获取管理员照片
	var adminImg []models.SelfSignImages
	db.Model(models.SelfSignImages{}).Where("restaurant_id = ?", restId).
		Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
		// Where("doc_type IN ?", []string{"0001", "0011", "0007", "0099"}).Find(&adminImg)
		Where("doc_type IN ?", []string{"1005", "1006", "1007", "0099"}).Find(&adminImg) //新增管理员身份证 文档类型
	return SelfSignMerchantInfo, adminImg
}

func (s CollectionInfoService) GetBusinessLicense(resId int) (models.SelfSignMerchantInfo, models.SelfSignImages) {
	db := tools.Db
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).
		Where("restaurant_id = ?", resId).
		Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
		First(&SelfSignMerchantInfo)

	// 获取营业执照照片
	var adminImg models.SelfSignImages
	db.Model(models.SelfSignImages{}).Where("restaurant_id = ?", resId).
		Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
		Where("doc_type = ?", "1001").Find(&adminImg)
	return SelfSignMerchantInfo, adminImg
}

// 业务分类
type codeData struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

// 业务分类 获取
func (s CollectionInfoService) GetPayProduct() (codes []codeData) {
	db := tools.Db
	var SelfSignMcc []models.SelfSignMcc
	db.Model(models.SelfSignMcc{}).
		Where("state = ?", 1).
		Select("code,name_" + s.langUtil.Lang).
		Find(&SelfSignMcc)

	for i := 0; i < len(SelfSignMcc); i++ {
		var cd codeData
		if s.langUtil.Lang == "ug" {
			cd.Name = SelfSignMcc[i].NameUg
		} else {
			cd.Name = SelfSignMcc[i].NameZh
		}
		cd.Code = SelfSignMcc[i].Code
		codes = append(codes, cd)

	}

	return codes
}

// 银行列表
type bankData struct {
	Id     int    `json:"id"`
	Name   string `json:"name"`
	NameUg string `json:"name_ug"`
	NameZh string `json:"name_zh"`
}

// 银行列表 获取
func (s CollectionInfoService) GetBankList() (codes []bankData) {
	db := tools.Db
	var collectionBank []models.SelfSignBank
	db.Model(models.SelfSignBank{}).
		Where("state = ?", 1).Order("`index` asc").
		Find(&collectionBank)

	for i := 0; i < len(collectionBank); i++ {
		var cd bankData
		if s.langUtil.Lang == "ug" {
			cd.Name = collectionBank[i].NameUg
		} else {
			cd.Name = collectionBank[i].NameZh
		}
		cd.Id = collectionBank[i].ID
		cd.NameUg = collectionBank[i].NameUg
		cd.NameZh = collectionBank[i].NameZh

		codes = append(codes, cd)

	}

	return codes
}

// 更新店铺 员工健康证信息
func (s CollectionInfoService) UpdateMerStaff(id string, staff_name string, staff_idcard string, health_certificate_image string, health_certificate_start string, health_certificate_end string) (errMsg string) {
	db := tools.Db

	var stf models.SelfSignStuff
	db.Model(models.SelfSignStuff{}).Where("id = ? ", id).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Find(&stf)

	if stf.Id == 0 {
		errMsg = "data_not_found"
		return
	}

	if strings.Contains(health_certificate_image, configs.MyApp.CdnUrl) {
		health_certificate_image = strings.Split(health_certificate_image, configs.MyApp.CdnUrl)[1]
	}
	hcStart := carbon.ParseByFormat(health_certificate_start, "Y-m-d").Carbon2Time()
	hcEnd := carbon.ParseByFormat(health_certificate_end, "Y-m-d").Carbon2Time()
	staffInfo := models.SelfSignStuff{
		MerInfoId:              stf.MerInfoId,
		StaffName:              staff_name,
		StaffIdCard:            staff_idcard,
		HealthCertificateImage: health_certificate_image,
		HealthCertificateStart: &hcStart,
		HealthCertificateEnd:   &hcEnd,
	}
	upErr := db.Model(&stf).Where("id = ?", id).Updates(staffInfo).Error
	if upErr != nil {
		tools.Logger.Info("更新健康证信息失败", upErr.Error())
		errMsg = "update_mer_stuf_fail"
		return

	}
	errMsg = ""
	return
}

// 删除店铺 员工健康证信息
func (s CollectionInfoService) DeleteMerStaff(id string) (errMsg string) {
	db := tools.Db
	delErr := db.Table("t_self_sign_stuff").
		Where("id = ? ", id).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Delete(models.SelfSignStuff{}).Error

	if delErr != nil {
		tools.Logger.Info("删除健康证信息失败", delErr.Error())
		errMsg = "del_mer_stuf_fail"
		return

	}
	errMsg = ""
	return
}

// SaveStaffInfo
//
//	@Time 2023-01-16 19:00:56
//	<AUTHOR>
//	@Description: 保存员工信息
//	@receiver s CollectionInfoService
//	@param restId
//	@param stafss
func (s CollectionInfoService) SaveStaffInfo(restId int, stafss string) {
	type Staff struct {
		StaffName              string `json:"staff_name" validate:"required"`
		StaffIdcard            string `json:"staff_idcard" validate:"required"`             // 员工身份证号
		HealthCertificateStart string `json:"health_certificate_start" validate:"required"` // 员工健康证有效期开始时间
		HealthCertificateEnd   string `json:"health_certificate_end" validate:"required"`   // 员工健康证有效期结束时间
		ImgSrc                 string `json:"img_src" validate:"required"`                  // 员工健康证图片
	}
	staffs := make([]Staff, 0)
	JsonToStructErr := json.Unmarshal([]byte(stafss), &staffs)
	if JsonToStructErr != nil {
		tools.Logger.Info("员工信息解析失败", JsonToStructErr.Error())
		panic(JsonToStructErr.Error())
		return
	}
	// 验证参数
	validate := validator.New()
	for _, staff := range staffs {
		err := validate.Struct(staff)
		if err != nil {
			tools.Logger.Info("员工信息验证失败", err.Error())
			panic(errors.CustomError{Msg: "staff_prame_error"})
			return
		}
	}
	// 保存员工信息
	db := tools.Db

	type MccInfo struct {
		Id    int `json:"id"`
		State int `json:"state"`
	}

	var merInfoId MccInfo

	db.Model(models.SelfSignMerchantInfo{}).Select("id,state").Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).First(&merInfoId)
	// 删除员工信息
	db.Model(models.SelfSignStuff{}).Where("mer_info_id = ?", merInfoId.Id).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Delete(models.SelfSignStuff{})
	// 保存员工信息
	for _, staff := range staffs {
		trim := strings.Split(staff.ImgSrc, configs.MyApp.CdnUrl)
		hcStart := carbon.ParseByFormat(staff.HealthCertificateStart, "Y-m-d").Carbon2Time()
		hcEnd := carbon.ParseByFormat(staff.HealthCertificateEnd, "Y-m-d").Carbon2Time()
		staffInfo := models.SelfSignStuff{
			Type : constants.SELF_SIGN_TYPE_RESTAURANT,
			MerInfoId:              merInfoId.Id,
			StaffName:              staff.StaffName,
			StaffIdCard:            staff.StaffIdcard,
			HealthCertificateImage: trim[len(trim)-1],
			HealthCertificateStart: &hcStart,
			HealthCertificateEnd:   &hcEnd,
		}
		createErr := db.Create(&staffInfo).Error
		if createErr != nil {
			tools.Logger.Info("员工信息创建失败", createErr.Error())
			panic(createErr.Error())
			return
		}
	}
	var yinshangimg models.SelfSignImages
	db.Model(models.SelfSignImages{}).Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Where("doc_type = ?", "0034").Find(&yinshangimg)
	if yinshangimg.ID == 0 {
		db.Model(models.SelfSignImages{}).Create(&models.SelfSignImages{
			Type: 		  constants.SELF_SIGN_TYPE_RESTAURANT,
			RestaurantID: restId,
			MlzFilePath:  "images/self-sign/ums/appImage.jpg",
			DocType:      "0034",
			DocTypeName:  "商户网站/APP截图",
		})
	}
	// 修改状态
	if merInfoId.State != 10 { //第一次审核通过后再次提交 不用修改状态
		db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Update("state", 1)
		//写入提交日志
		db.Table("t_self_sign_verify_log").Create(&map[string]interface{}{
			"mer_info_id": merInfoId.Id,
			"varifyer_id": 0,
			"step_num":    0,
			"page_name":   "",
			"remark":      "资料提交",
			"state":       "1",
			"created_at":  carbon.Now().ToDateTimeString(),
			"updated_at":  carbon.Now().ToDateTimeString(),
		})
	}

}

// 获取审核状态数据失败
func (s CollectionInfoService) MerCheckState(restaurant_id int, cityId int, areaId int) (data map[string]interface{}, errMsg string) {
	db := tools.Db

	var inf resources.CheckInfo
	fields := "id,reg_mer_type,shop_name,shop_license_num,shop_business_name,"
	fields += "mer_idcard_num,permit_lic_num,bank_acct_type,bank_acct_num,"
	fields += "bank_acct_name,bank_id,verify_content,state,verify_amount,ums_reg_id,"
	fields += "pay_product,shop_business_city_id,shop_category_code,bank_city_id,"
	fields += "submit_type,lakala_verify_state,lakala_verify_mobile"
	findErr := db.Model(models.SelfSignMerchantInfo{}).Select(fields).Where("restaurant_id = ? ", restaurant_id).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Find(&inf).Error

	if findErr != nil {
		tools.Logger.Info("获取审核状态数据失败", findErr.Error())
		errMsg = "del_mer_stuf_fail"
		return

	}
	var m []map[string]interface{}
	data = make(map[string]interface{})

	if inf.Id == 0 {
		//没有数据的话 给他一个 个体工商户 的数据
		lmtType := 1
		// 个体工商户
		SelfSignMerchantInfo := models.SelfSignMerchantInfo{
			Type:           		constants.SELF_SIGN_TYPE_RESTAURANT,
			RestaurantId:           restaurant_id, // 餐厅id
			CityId:                 cityId,
			AreaId:                 areaId,
			RegMerType:             "01", // 注册商户类型
			ShopLicenseLimitedType: &lmtType,
			VerifyContent:          "[]",
			SubmitType:             2, //拉卡拉
		}
		db.Create(&SelfSignMerchantInfo)
		db.Model(models.SelfSignMerchantInfo{}).Select(fields).Where("restaurant_id = ? ", restaurant_id).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Find(&inf)
	}

	err := json.Unmarshal([]byte(inf.VerifyContent), &m)
	if err != nil {
		tools.Logger.Info("获取审核状态 json 解析失败", err.Error())
		data["verify_content"] = make([]string, 0)

	} else {
		data["verify_content"] = m
	}

	data["state"] = inf.State

	data["bank_acct_type"] = inf.BankAcctType
	data["bank_acct_num"] = inf.BankAcctNum
	bankName := ""

	var collectionBank models.SelfSignBank
	db.Model(models.SelfSignBank{}).
		Where("id = ?", inf.BankId).
		First(&collectionBank)

	if s.langUtil.Lang == "ug" {
		bankName = collectionBank.NameUg
	} else {
		bankName = collectionBank.NameZh
	}
	data["bank_acct_name"] = bankName
	data["reg_mer_type"] = inf.RegMerType
	data["verify_url"] = inf.VerifyUrl
	data["ums_reg_id"] = inf.UmsRegId

	if inf.State == 0 {
		var zeroContent []map[string]interface{}
		zeroState := make(map[string]interface{}, 0)

		zeroState["step_num"] = 2 //身份证信息
		if len(inf.MerIdcardNum) == 0 {
			zeroState["is_done"] = 1
			zeroState["inputs"] = make([]map[string]interface{}, 0)
		} else {
			zeroState["is_done"] = 2
			zeroState["inputs"] = make([]map[string]interface{}, 0)
		}
		zeroContent = append(zeroContent, zeroState)

		zeroState = make(map[string]interface{}, 0)

		zeroState["step_num"] = 3                                                                              //结算账户信息
		if len(inf.BankAcctNum) == 0 || len(inf.BankAcctName) == 0 || inf.BankId == 0 || inf.BankCityId == 0 { //账户信息 {
			zeroState["is_done"] = 1
			zeroState["inputs"] = make([]map[string]interface{}, 0)
		} else {
			zeroState["is_done"] = 2
			zeroState["inputs"] = make([]map[string]interface{}, 0)
		}
		zeroContent = append(zeroContent, zeroState)
		if inf.RegMerType == "00" || inf.RegMerType == "01" {
			zeroState = make(map[string]interface{}, 0)
			zeroState["step_num"] = 1 //营业执照
			if len(inf.ShopName) == 0 {
				zeroState["is_done"] = 1
				zeroState["inputs"] = make([]map[string]interface{}, 0)
			} else {
				zeroState["is_done"] = 2
				zeroState["inputs"] = make([]map[string]interface{}, 0)
			}
			zeroContent = append(zeroContent, zeroState)

			zeroState = make(map[string]interface{}, 0)
			zeroState["step_num"] = 6 //许可证
			if len(inf.PermitLicNum) == 0 {
				zeroState["is_done"] = 1
				zeroState["inputs"] = make([]map[string]interface{}, 0)
			} else {
				zeroState["is_done"] = 2
				zeroState["inputs"] = make([]map[string]interface{}, 0)
			}
			zeroContent = append(zeroContent, zeroState)
		} else {
			zeroState = make(map[string]interface{}, 0)
			zeroState["step_num"] = 1 //营业执照
			if len(inf.ShopName) == 0 {
				zeroState["is_done"] = 1
				zeroState["inputs"] = make([]map[string]interface{}, 0)
			} else {
				zeroState["is_done"] = 2
				zeroState["inputs"] = make([]map[string]interface{}, 0)
			}
			zeroContent = append(zeroContent, zeroState)
		}
		zeroState = make(map[string]interface{}, 0)
		zeroState["step_num"] = 4 //商家信息
		if len(inf.ShopBusinessName) == 0 || len(inf.ShopBusinessCityID) == 0 || len(inf.ShopCategoryCode) == 0 {
			zeroState["is_done"] = 1
			zeroState["inputs"] = make([]map[string]interface{}, 0)
		} else {
			zeroState["is_done"] = 2
			zeroState["inputs"] = make([]map[string]interface{}, 0)
		}
		zeroContent = append(zeroContent, zeroState)

		data["verify_content"] = zeroContent
	}
	data["information"] = s.GetRollingInformation("self_sign_info")            //滚动广告
	data["information_content"] = s.GetRollingInformation("self_sign_content") //广告内容
	data["information_id"] = inf.Id
	data["submit_type"] = inf.SubmitType
	data["lakala_verify_mobile"] = inf.LakalaVerifyMobile
	data["lakala_verify_state"] = inf.LakalaVerifyState
	return data, errMsg
}

// 滚动广告 内容
func (s CollectionInfoService) GetRollingInformation(name string) string {
	//静态内容 以后要改
	lg := s.langUtil.Lang
	//广告内容json
	content := tools.ReadJson(inits.ConfigFilePath + "/data/" + name + "_" + lg + ".txt")
	return content
}

// GetStuffInfo
//
//	@Time 2023-01-16 19:50:31
//	<AUTHOR>
//	@Description: 获取员工信息
//	@receiver s CollectionInfoService
//	@param restId

func (s CollectionInfoService) GetStuffInfo(restId int) []models.SelfSignStuff {
	db := tools.Db
	var merInfo models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).First(&merInfo)
	var stuffs []models.SelfSignStuff
	db.Model(models.SelfSignStuff{}).Where("mer_info_id = ?", merInfo.Id).Find(&stuffs)
	return stuffs

}

// savaImageByRestIdAndDocType
//
//	@Time 2023-01-16 23:26:26
//	<AUTHOR>
//	@Description: 通过餐厅id和文档类型 保存图片
//	@receiver s CollectionInfoService
//	@param tx
//	@param restId
//	@param imgSrcs
//	@return error
func (s CollectionInfoService) savaImageByRestIdAndDocType(tx *gorm.DB, restId int, imgSrcs string) error {
	tools.Logger.Info("图片列表=====>>>>>", imgSrcs)
	type ImgSrcs struct {
		ImgSrc  string `json:"img_src" validate:"required"`
		DocType string `json:"doc_type" validate:"required"`
	}
	imgSrcsArr := make([]ImgSrcs, 0)
	jsonToStructErr := json.Unmarshal([]byte(imgSrcs), &imgSrcsArr)
	if jsonToStructErr != nil {
		tools.Logger.Info("Images json转imgSrcsstruct失败:", jsonToStructErr.Error())
		return jsonToStructErr
	}
	// 验证参数
	validate := validator.New()
	for _, imgSrc := range imgSrcsArr {
		if len(imgSrc.ImgSrc) > 0 {
			validateErr := validate.Struct(imgSrc)
			if validateErr != nil {
				return validateErr
			}
		}
	}
	for _, imgSrc := range imgSrcsArr {
		if imgSrc.DocType == "0099" {

			deleteErr := tx.Model(models.SelfSignImages{}).
				Where("restaurant_id = ? AND doc_type = ?", restId, "0099").
				Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
				Delete(models.SelfSignImages{}).Error

			if deleteErr != nil {
				tools.Logger.Info("删除管理员其他照片失败", deleteErr.Error())
				return deleteErr
			}
			break
		} else if imgSrc.DocType == "0016" || imgSrc.DocType == "0017" || imgSrc.DocType == "0018" || imgSrc.DocType == "0019" || imgSrc.DocType == "0020" {
			deleteErr := tx.Model(models.SelfSignImages{}).
				Where("restaurant_id = ? AND doc_type in ('0016','0017','0018','0019','0020')", restId).
				Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).
				Delete(models.SelfSignImages{}).Error
			if deleteErr != nil {
				tools.Logger.Info("删除管理员其他照片失败", deleteErr.Error())
				return deleteErr
			}
			break
		}
	}

	tools.Logger.Info("Images=>>>>>", imgSrcsArr)
	// 保存图片
	for _, imgSrc := range imgSrcsArr {
		// 去除图片前缀
		trim := strings.Split(imgSrc.ImgSrc, configs.MyApp.CdnUrl)
		var DocTypeName string
		tx.Table("b_self_sign_doc_type").Select(" name_zh ").Where(" `key` = ? ", imgSrc.DocType).Scan(&DocTypeName)
		// 查询图片是否存在
		var img models.SelfSignImages
		tx.Model(models.SelfSignImages{}).Where("restaurant_id = ? AND doc_type = ?", restId, imgSrc.DocType).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).First(&img)
		if img.ID == 0 {
			// 不存在就创建
			img = models.SelfSignImages{
				Type: constants.SELF_SIGN_TYPE_RESTAURANT,
				RestaurantID: restId,
				DocType:      imgSrc.DocType,
				MlzFilePath:  trim[len(trim)-1],
				DocTypeName:  DocTypeName,
			}
			createErr := tx.Create(&img).Error
			if createErr != nil {
				tools.Logger.Info("图片新建写入失败", createErr.Error())
				return createErr
			}
		} else {
			// doc_type 0099  其他资料 保存多张图片
			if imgSrc.DocType == "0099" {
				img = models.SelfSignImages{
					Type:  constants.SELF_SIGN_TYPE_RESTAURANT,
					RestaurantID: restId,
					DocType:      imgSrc.DocType,
					MlzFilePath:  trim[len(trim)-1],
					DocTypeName:  DocTypeName,
				}
				createErr := tx.Create(&img).Error
				if createErr != nil {
					tools.Logger.Info("图片写入失败", createErr.Error())
					return createErr
				}

			} else {
				// 存在就更新
				updateErr := tx.Model(models.SelfSignImages{}).Where("restaurant_id = ? AND doc_type = ?", restId, imgSrc.DocType).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Updates(models.SelfSignImages{MlzFilePath: trim[len(trim)-1], DocTypeName: DocTypeName}).Error
				if updateErr != nil {
					tools.Logger.Info("图片更新失败", updateErr.Error())
					return updateErr
				}
			}

		}
	}
	return nil

}

func (s CollectionInfoService) GetRestaurantInfo(restId int) (data map[string]interface{}) {
	db := tools.Db
	// var merInfo models.SelfSignMerchantInfo
	//企业名称 , 营业执照图片 ,法人姓名 ,法人身份证 , 食品经营许可证 ,食品经营许可证的图片 ( 过期时间要判断 ),银行名称
	//, 银行卡 账户 , 管理员姓名,管理员身份证,管理员手机号,管理员邮箱
	fields := "shop_business_name,reg_mer_type,legal_name,bank_acct_type,bank_acct_num"
	fields += ",bank_id,bank_acct_name,shop_name,shop_category_code,shop_manager_type"
	fields += ",shop_manager_name,shop_manager_idcard,shop_manager_mobile,shop_manager_email"
	fields += ",permit_end_date,state,alter_state"
	fields += ",mer_idcard_name,mer_idcard_num,mer_idcard_start,mer_idcard_end"
	fields += ",mer_mobile,mer_sex"

	db.Model(models.SelfSignMerchantInfo{}).Select(fields).Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).First(&data)

	var bankData map[string]interface{}
	db.Model(models.SelfSignBank{}).Select("name_ug,name_zh").Where("id = ?", data["bank_id"]).First(&bankData)

	var shopImgs []map[string]interface{}
	//营业执照数据
	docTypes := "'1001','0001','0011','0002','0005','0006','0007','0015','0016','0017','0018','0019','0020'"
	docTypes += ",'0025','0026','0034'"

	db.Model(models.SelfSignImages{}).Select("mlz_file_path,doc_type").Where("restaurant_id = ? and doc_type in ("+docTypes+")", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Find(&shopImgs)

	shopLicenseImg := ""
	shopFoosLicenseImg := ""

	shopFrontImg := ""
	shopInsideImg := ""

	merIdFrontImg := ""
	merIdBackImg := ""
	merIdSelfImg := ""

	bankFrontImg := "" //银行卡前面
	bankBackImg := ""  //银行卡背面
	bankSignImg := ""  //开户许可证

	for i := 0; i < len(shopImgs); i++ {
		docTypeStr := shopImgs[i]["doc_type"]
		imgSrc := configs.MyApp.CdnUrl + shopImgs[i]["mlz_file_path"].(string)
		switch docTypeStr {
		case "0002":
			shopLicenseImg = imgSrc
			data["shop_license_img_type"] = docTypeStr
		case "1001":
			shopFoosLicenseImg = imgSrc

		case "0005":
			shopFrontImg = imgSrc

		case "0006":
			bankSignImg = imgSrc

		case "0015":
			shopInsideImg = imgSrc

		case "0016", "0017", "0018", "0019", "0020":

			shopLicenseImg = imgSrc
			data["shop_license_img_type"] = docTypeStr

		case "0001":
			merIdFrontImg = imgSrc

		case "0011":
			merIdBackImg = imgSrc

		case "0007":
			merIdSelfImg = imgSrc

		case "0025":
			bankFrontImg = imgSrc

		case "0026":
			bankBackImg = imgSrc

		}
	}

	data["shop_license_img"] = shopLicenseImg

	data["shop_front_img"] = shopFrontImg

	data["shop_inside_img"] = shopInsideImg

	data["mer_id_front_img"] = merIdFrontImg
	data["mer_id_back_img"] = merIdBackImg
	data["mer_id_self_img"] = merIdSelfImg
	data["bank_front_img"] = bankFrontImg
	data["bank_bank_img"] = bankBackImg
	data["bank_sign_img"] = bankSignImg

	idLimitType := 0
	cEnd := tools.ToString(data["mer_idcard_end"])

	idcardEnd := ""
	if len(cEnd) > 0 {
		idcardEnd = tools.ToTime(cEnd).Format("2006-01-02")
		if idcardEnd == "9999-12-31" {
			idLimitType = 1
			idcardEnd = "长期"
		}
	}

	midStartStr := ""
	midStart := tools.ToString(data["mer_idcard_start"])

	if len(midStart) > 0 {
		midStartStr = tools.ToTime(midStart).Format("2006-01-02")
	}

	data["mer_idcard_limited_type"] = idLimitType
	data["mer_idcard_start"] = midStartStr
	data["mer_idcard_end"] = idcardEnd

	var category string

	db.Model(models.SelfSignMcc{}).
		Where("code = ?", data["shop_category_code"]).
		Select("name_" + s.langUtil.Lang + " as name").
		First(&category)

	categoryName := ""

	if len(category) > 0 {
		categoryName = category
	}

	data["shop_category_name"] = categoryName

	data["shop_food_license_img"] = shopFoosLicenseImg

	bankName := ""

	if bankData != nil {
		bankName = bankData["name_"+s.language].(string)
	}

	data["bank_name"] = bankName

	//食品经营许可证是否过期
	flag := false

	permitEnd := tools.ToString(data["permit_end_date"])

	er1 := carbon.Parse(permitEnd).Error
	if er1 == nil {

		flag = carbon.Parse(time.Now().Format("1990-01-10")).Gt(carbon.Parse(permitEnd))

		data["permit_end_date"] = carbon.Parse(permitEnd).Format("1990-01-10")

	}

	data["permit_overdue"] = flag

	return

}

// UpdateApplyResultInfo
//
//	@Description: 更新入网状态和记录审核日志
//	<AUTHOR>
//	@Time 2023-01-18 19:33:03
//	@receiver s CollectionInfoService
//	@param resId
//	@param infoId
//	@param verifyState
//	@param remark
//	@param updateMerchantInfoMap
func (s CollectionInfoService) UpdateApplyResultInfo(resId int, infoId int, verifyState int, remark string, updateMerchantInfoMap map[string]interface{}) {
	db := tools.Db
	updateMerchantInfoMap["state"] = verifyState
	db.Table("t_self_sign_merchant_info").Where("restaurant_id = ?", resId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).UpdateColumns(updateMerchantInfoMap)
	db.Table("t_self_sign_verify_log").Create(&map[string]interface{}{
		"mer_info_id": infoId,
		"varifyer_id": 0,
		"step_num":    0,
		"page_name":   "",
		"remark":      remark,
		"state":       verifyState,
		"created_at":  carbon.Now().ToDateTimeString(),
		"updated_at":  carbon.Now().ToDateTimeString(),
	})
}

/***
 * @Author: [rozimamat]
 * @description: 提交前监测状态
 * @Date: 2023-05-25 11:48:14
 * @param {int} restId
 */
func (s CollectionInfoService) CheckColMerInfoState(restId int) (bool, int) {
	//0:未提交, //1:待审核 , //2:审核未通过 , //3:后台审核通过 , //4:待提交资料 , //5:已提交资料 ,
	//6:资料未通过, //7:对公账户待确认, //8:待在线签约 , //9:待银商入网审核 , //10:银商入网成功, //11:银商入网失败,
	db := tools.Db
	var merInfo models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).First(&merInfo)
	if merInfo.State == 0 || merInfo.State == 2 {
		return true, 0
	} else {
		return false, merInfo.State
	}

}

// 审核失败后的再次提交
func (s CollectionInfoService) UpdateInfoState(restId int) (errMsg string) {
	//????
	//0:未提交, //1:待审核 , //2:审核未通过 , //3:后台审核通过 , //4:待提交资料 , //5:已提交资料 ,
	//6:资料未通过, //7:对公账户待确认, //8:待在线签约 , //9:待银商入网审核 , //10:银商入网成功, //11:银商入网失败,
	db := tools.Db

	var stf models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id = ? ", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Find(&stf)
	// db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id = ? ", restId).Find(&stf)

	if stf.Id == 0 {
		errMsg = "data_not_found"
		return
	}

	upErr := db.Model(&stf).Where("id = ?", stf.Id).Omit("deleted_at").Updates(map[string]interface{}{"state": 1}).Error
	if upErr != nil {
		tools.Logger.Info("更新收集的信息状态失败", upErr.Error())
		errMsg = "update_info_state"
		return

	}
	//写入重新提交日志
	db.Table("t_self_sign_verify_log").Create(&map[string]interface{}{
		"mer_info_id": stf.Id,
		"varifyer_id": 0,
		"step_num":    0,
		"page_name":   "",
		"remark":      "资料重新提交",
		"state":       "1",
		"created_at":  carbon.Now().ToDateTimeString(),
		"updated_at":  carbon.Now().ToDateTimeString(),
	})
	errMsg = ""
	return

}

// GetApplyStateAndDoSomeUpdate
//
//	@Description: 从银商查询状态 如果与数据库的状态不一致 则更新
//	@author: Alimjan
//	@Time: 2023-01-31 17:12:44
//	@receiver ums CollectionInfoService

// @param merchantInfo models.SelfSignMerchantInfo
// @param resId int
// @return map[string]interface{}
// @return error
func (ums CollectionInfoService) GetApplyStateAndDoSomeUpdate(merchantInfo models.SelfSignMerchantInfo, resId int) (map[string]interface{}, error) {

	var (
		cService = selfsign.SelfSignFactory("ums")
	)
	// 全量状态流程——18：资料填写中（前端对接流程状态）--05：对公账户待验证或异常（对公账户状态）--00：签约中--01：签约成功--06：风控审核中（系统审核状态）/02：入网审核中--03：入网成功/04：入网失败
	// 返回电子签约链接
	res := cService.GetApplyState(merchantInfo.UmsRegId)
	tools.Logger.Info("签约状态", tools.MapToString(res))
	if res["res_code"] != "0000" {
		panic(errors.CustomError{Msg: "获取状态失败"})
	}

	//var oldInfo map[string]interface{}
	oldInfo := cService.GetMerchantInfoByResId(resId)

	var umsVerifyState = oldInfo.UmsVerifyState
	// 待签约（最终成功状态）
	if res["apply_status"] == "00" && umsVerifyState != "00" {
		//第一次审核成功
		ums.UpdateApplyResultInfo(resId, oldInfo.Id, 8, "待在线签约", map[string]interface{}{
			"ums_verify_state": "00",
			"state":            8,
		})
		return res, nil
	}
	// 入网成功（最终成功状态）
	if res["apply_status"] == "03" && umsVerifyState != "03" {
		//第一次审核成功
		ums.UpdateApplyResultInfo(resId, oldInfo.Id, 10, "入网成功", map[string]interface{}{
			"ums_verify_state": "03",
			"state":            10,
		})
		// 记录入网信息
		cService.SaveAppliedInfo(res)
		// 入网成功,数据归档
		cService.ArchiveInfo(resId)
		return res, nil
	}

	// 入网失败
	if (res["apply_status"] == "04" && umsVerifyState != "04") ||
		(res["apply_status"] == "28" && umsVerifyState != "28") ||
		(res["apply_status"] == "99" && umsVerifyState != "99") ||
		(res["apply_status"] == "31" && umsVerifyState != "31") {
		//第一次审核成功
		ums.UpdateApplyResultInfo(resId, oldInfo.Id, 11, res["fail_reason"].(string), map[string]interface{}{
			"ums_verify_state": res["apply_status"],
			"state":            11,
		})
		return res, nil
	}
	// 待审核
	if (res["apply_status"] == "01" && umsVerifyState != "01") ||
		(res["apply_status"] == "02" && umsVerifyState != "02") ||
		(res["apply_status"] == "06" && umsVerifyState != "06") {
		//第一次审核成功
		ums.UpdateApplyResultInfo(resId, oldInfo.Id, 9, res["fail_reason"].(string), map[string]interface{}{
			"ums_verify_state": res["apply_status"],
			"state":            9,
		})
		return res, nil
	}

	// 提示对公账户未通过 但是数据库状态显示已经验证过 对公账户
	if res["apply_status"] == "05" && oldInfo.State == 8 {
		ums.UpdateApplyResultInfo(resId, oldInfo.Id, 7, res["fail_reason"].(string), map[string]interface{}{
			"ums_verify_state": res["apply_status"],
			"state":            7, //对公账户确认
		})
		db := tools.Db
		//写入未通过该提交日志
		db.Table("t_self_sign_verify_log").Create(&map[string]interface{}{
			"mer_info_id": oldInfo.Id,
			"varifyer_id": 0,
			"step_num":    0,
			"page_name":   "",
			"remark":      "在签约状态的时候发现对公账户未通过",
			"state":       "8",
			"created_at":  carbon.Now().ToDateTimeString(),
			"updated_at":  carbon.Now().ToDateTimeString(),
		})
		return res, nil
	}
	/**
		0:未提交,
	1:待审核 ,
	2:审核未通过 ,
	3:后台审核通过 ,
	4:待提交资料 ,
	5:已提交资料 ,
	6:资料未通过,
	7:对公账户待确认  ,
	8:待在线签约 ,
	9:待银商入网审核 ,
	10:银商入网成功,
	11:银商入网失败,
	*/
	// 入网成功（最终成功状态）
	if res["apply_status"] != umsVerifyState {
		tools.Logger.Info("入网检查结果：入网失败")
		remarks := map[string]string{
			"00": "签约中",
			"01": "签约成功（中间状态）",
			"02": "入网审核中（人工审 核流程） ",
			"03": "入网成功（最终成功状态）",
			"04": "入网失败",
			"05": "对公账户待验证或异常（对公账户状态）",
			"06": "风控审核中（系统审 核状态）",
			"11": "短信签生成合同成功（短信签约流程）",
			"18": "资料填写中（前端流 程状态）",
			"28": "资料验证失败",
			"31": "冻结账户",
			"99": "其它错误",
		}
		tools.Logger.Info("入网检查状态：" + res["apply_status"].(string))
		curRemark := "其它错误"
		if _, ok := remarks[res["apply_status"].(string)]; ok {
			// 存在
			curRemark = remarks[res["apply_status"].(string)]
		}
		ums.UpdateApplyResultInfo(resId, oldInfo.Id, 11, curRemark, map[string]interface{}{
			"ums_verify_state": res["apply_status"],
		})
	}
	return res, nil
}

// GetSelfSignInfoByRegId
//
//	@Description: 根据RegID获取商家提交信息信息
//	@author: Rixat
//	@Time: 2023-02-07
//	@receiver ums *CollectionInfoService
//	@param regId string
func (ums CollectionInfoService) GetSelfSignInfoByRegId(regId string) models.SelfSignMerchantInfo {
	var merchantInfo models.SelfSignMerchantInfo
	db := tools.Db
	db.Model(merchantInfo).Where("ums_reg_id = ?", regId).Scan(&merchantInfo)
	return merchantInfo
}

// 审核失败后的再次提交
func (s CollectionInfoService) Submit(restId int) (errMsg string) {
	//????
	//0:未提交, //1:待审核 , //2:审核未通过 , //3:后台审核通过 , //4:待提交资料 , //5:已提交资料 ,
	//6:资料未通过, //7:对公账户待确认, //8:待在线签约 , //9:待银商入网审核 , //10:银商入网成功, //11:银商入网失败,
	db := tools.Db

	var stf models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id = ? ", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Find(&stf)
	// db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id = ? ", restId).Find(&stf)

	if stf.Id == 0 {
		errMsg = "info_incomplete"
		return errMsg
	}

	//监测是否真的填写了相关信息

	//00：企业商户
	// 01：个人工商户
	// 02：小微商户

	//监测必填项目中的一个
	if len(stf.MerIdcardNum) == 0 { //身份证信息
		errMsg = "idcard_incomplete"
		return errMsg
	}

	if len(stf.BankAcctNum) == 0 || len(stf.BankAcctName) == 0 || stf.BankId == 0 || stf.BankCityId == 0 { //账户信息
		errMsg = "bank_incomplete"
		return errMsg
	}

	if stf.RegMerType == "00" || stf.RegMerType == "01" {
		if len(stf.ShopLicenseNum) == 0 { //营业执照信息
			errMsg = "shop_licence_incomplete"
			return errMsg
		}
	}
	if len(stf.ShopName) == 0 { //店铺信息
		errMsg = "shop_licence_incomplete"
		return errMsg
	}

	if len(stf.ShopBusinessName) == 0 || len(stf.ShopBusinessCityID) == 0 || len(stf.ShopCategoryCode) == 0 { //店铺信息
		errMsg = "shop_incomplete"
		return errMsg
	}

	if stf.State == 1 { //数据已提交
		errMsg = "submitted_wait_for_check"
		return errMsg
	}

	if len(stf.LegalName) > 0 && len(stf.MerIdcardName) > 0 { //已存在身份证信息 的话要判断 法人名称和身份证上的姓名是否一致
		if stf.LegalName != stf.MerIdcardName {
			return "legal_name_and_idcard_name_not_same"
		}
	}

	//银行卡四要素验证
	if *stf.BankAcctType == 0 {
		chk, msg := tools.BankVerify(stf.LegalName, stf.BankAcctNum, stf.MerMobile, stf.MerIdcardNum)
		if !chk {
			return s.langUtil.TArr("bank_verify_result")[tools.ToInt(msg)]
		}
	}

	var yinshangimg models.SelfSignImages
	db.Model(models.SelfSignImages{}).Where("restaurant_id = ?", restId).Where("doc_type = ?", "0034").Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Find(&yinshangimg)
	if yinshangimg.ID == 0 {
		db.Model(models.SelfSignImages{}).Create(&models.SelfSignImages{
			Type:1,
			RestaurantID: restId,
			MlzFilePath:  "images/self-sign/ums/appImage.jpg",
			DocType:      "0034",
			DocTypeName:  "商户网站/APP截图",
		})
	}
	if stf.RegMerType == "02" {
		db.Model(models.SelfSignImages{}).Where("restaurant_id = ?", restId).Where("doc_type = ?", "0021").Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Find(&yinshangimg)
		if yinshangimg.ID == 0 {
			db.Model(models.SelfSignImages{}).Create(&models.SelfSignImages{
				Type:1,
				RestaurantID: restId,
				MlzFilePath:  "images/self-sign/ums/product.jpg",
				DocType:      "0021",
				DocTypeName:  "商品图片",
			})
		}
	}

	// 修改状态
	if stf.State != 10 { //第一次审核通过后再次提交 不用修改状态

		updateMap := make(map[string]interface{})
		updateMap["state"] = 1
		updateMap["submit_type"] = 2 //拉卡拉 提交

		db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id = ?", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Updates(&updateMap)
		//写入提交日志
		db.Table("t_self_sign_verify_log").Create(&map[string]interface{}{
			"mer_info_id": stf.Id,
			"varifyer_id": 0,
			"step_num":    0,
			"page_name":   "",
			"remark":      "资料提交",
			"state":       "1",
			"created_at":  carbon.Now().ToDateTimeString(),
			"updated_at":  carbon.Now().ToDateTimeString(),
		})
	}
	// 发送钉钉消息
	var area models.Area
	tools.Db.Model(area).Where("id", stf.AreaId).First(&area)
	if area.ID > 0 {
		SmsContent := "【" + area.NameZh + "】【" + "" + stf.ShopName + "】已提交资料，请按时处理！"
		tools.SendDingDingMsg(SmsContent)
	}
	errMsg = ""
	return errMsg

}

// 清空数据
func (s CollectionInfoService) Clear(restId int, cityId, areaId int, regMerType string) (errMsg string) {

	db := tools.Db

	var stf models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id = ? ", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Find(&stf)

	if stf.Id == 0 {
		errMsg = ""
		return
	}

	if stf.State == 0 || stf.State == 2 { //新提交资料和 审核驳回时可以切换注册类型
		tx := db.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()

		err := tx.Model(models.SelfSignImages{}).Where("restaurant_id = ? ", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Delete(models.SelfSignImages{}).Error
		err3 := tx.Model(models.SelfSignBnf{}).Where("mer_info_id = ? ", stf.Id).Delete(models.SelfSignBnf{}).Error
		err4 := tx.Model(models.SelfSignVerifyLog{}).Where("mer_info_id = ? ", stf.Id).Delete(models.SelfSignVerifyLog{}).Error
		er := tx.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id = ? ", restId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Unscoped().Delete(&models.SelfSignMerchantInfo{}).Error

		if er != nil {
			tools.Logger.Info("注册类型切换时 数据删除失败", er.Error())
			tx.Rollback()
			return "server_error"
		}
		if err != nil {
			tools.Logger.Info("注册类型切换时 图片数据删除失败", err.Error())
			tx.Rollback()
			return "server_error"
		}

		if err3 != nil {
			tools.Logger.Info("注册类型切换时 股东删除失败", err3.Error())
			tx.Rollback()
			return "server_error"
		}

		if err4 != nil {
			tools.Logger.Info("注册类型切换时 审核信息删除失败", err4.Error())
			tx.Rollback()
			return "server_error"
		}

		tx.Commit()
	}

	//没有数据的话 给他一个 数据
	// 个体工商户
	SelfSignMerchantInfo := models.SelfSignMerchantInfo{
		Type:  constants.SELF_SIGN_TYPE_RESTAURANT, // 餐厅id
		RestaurantId:  restId, // 餐厅id
		CityId:        cityId,
		AreaId:        areaId,
		RegMerType:    regMerType, // 注册商户类型
		VerifyContent: "[]",
	}
	er := db.Create(&SelfSignMerchantInfo).Error
	if er != nil {
		tools.Logger.Info("注册类型切换时 写入新数据失败", er.Error())
	}
	errMsg = ""
	return

}

/***
 * @Author: [rozimamat]
 * @description: 支行列表获取  数脉数据
 * @Date: 2023-04-18 17:35:44
 * @param {string} areaCode
 * @param {string} keyword
 * @param {string} bankId
 * @param {string} page
 */
func (s CollectionInfoService) BankBranchShumai(areaCode string, keyword string, bankId string, page string) map[string]interface{} {

	db := tools.Db
	bankName := ""
	if len(bankId) > 0 {
		bankMap := make(map[string]interface{}, 0)
		db.Table("b_self_sign_bank").Where("id = ?", bankId).Select("name_zh as name").Scan(&bankMap)
		if bankMap != nil {
			bankName = tools.ToString(bankMap["name"])
			if strings.Contains(bankName, "信用") || strings.Contains(bankName, "合作") || strings.Contains(bankName, "联社") { //都按信用社的走
				bankName = "信用社"
			}
		}
	}
	province := ""
	city := ""

	cityMap := make(map[string]interface{}, 0)
	db.Table("b_self_sign_area").Where("code = ?", areaCode).Select("level,name_zh,code,p_code").Scan(&cityMap)
	if cityMap != nil {
		level := tools.ToInt(cityMap["level"])
		pCode := tools.ToString(cityMap["p_code"])
		switch level {
		case 2: //城市
			provinceMap := make(map[string]interface{}, 0)
			db.Table("b_self_sign_area").Where("code = ?", pCode).Select("name_zh,code,p_code").Scan(&provinceMap)
			if provinceMap != nil {
				province = tools.ToString(provinceMap["name_zh"])
			}
			city = tools.ToString(cityMap["name_zh"])
		case 3: //县城
			city2Map := make(map[string]interface{}, 0)
			db.Table("b_self_sign_area").Where("code = ?", pCode).Select("name_zh,code,p_code").Scan(&city2Map)
			if city2Map != nil {
				city = tools.ToString(city2Map["name_zh"])

				provinceCode := tools.ToString(city2Map["p_code"])
				provinceMap := make(map[string]interface{}, 0)
				db.Table("b_self_sign_area").Where("code = ?", provinceCode).Select("name_zh,code,p_code").Scan(&provinceMap)
				if provinceMap != nil {

					province = tools.ToString(provinceMap["name_zh"])
				}
			}

		}
	}

	params := ""

	if len(bankName) > 0 {

		params += "&bank=" + url.QueryEscape(bankName)
	}

	if len(province) > 0 {
		if strings.Contains(province, "新疆") {
			province = "新疆"
		}

		params += "&province=" + url.QueryEscape(province)
	}
	if len(city) > 0 {
		// tools.Logger.Info("city", city)
		if strings.Contains(city, "地区") {
			city = strings.Split(city, "地区")[0]
		} else if strings.Contains(city, "县") {
			city = strings.Split(city, "县")[0]
		} else if strings.Contains(city, "市") {
			city = strings.Split(city, "市")[0]
		} else if strings.Contains(city, "区") {
			city = strings.Split(city, "区")[0]
		}

		params += "&city=" + url.QueryEscape(city)
	}

	if len(keyword) > 0 {
		if strings.Contains(keyword, "地区") {
			keyword = strings.Split(keyword, "地区")[0]
		} else if strings.Contains(keyword, "县") {
			keyword = strings.Split(keyword, "县")[0]
		} else if strings.Contains(keyword, "市") {
			keyword = strings.Split(keyword, "市")[0]
		} else if strings.Contains(keyword, "区") {
			keyword = strings.Split(keyword, "区")[0]
		}

		if strings.Contains(keyword, "银行") {
			if len(bankName) == 0 {
				params += "&bank=" + url.QueryEscape(keyword)
			}
		} else {
			if strings.Contains(keyword, "信用") || strings.Contains(keyword, "合作") || strings.Contains(keyword, "联社") { //都按信用社的走
				keyword = "信用社"
			}
			params += "&key=" + url.QueryEscape(keyword)
		}
	}
	results, totalCount := tools.GetShumaiBankData(params, page)

	pageCount := int(math.Ceil(float64(totalCount) / float64(10)))

	dataMap := make(map[string]interface{}, 0)
	dataMap["total"] = totalCount
	dataMap["current_page"] = tools.ToInt(page)
	dataMap["total_page"] = pageCount
	dataMap["data"] = results

	return dataMap
}
