package lakala

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math/rand"
	"mime/multipart"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/factory"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/resources/LakalaEntity"
	"mulazim-api/services/sms"
	"os"

	"strings"

	"time"

	// "mulazim-api/models"

	"mulazim-api/tools"

	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type LakalaService struct {
	langUtil *lang.LangUtil
	language string
}

const (
	ServerIp =	"**************"
	ORDER_NOT_PAID   = 1001 //未支付
	ORDER_PART_PAID  = 1002 //部分支付
	ORDER_PROCESSING = 1003 //进行中
	ORDER_PAID       = 1004 //已付款
	ORDER_COMPLETE   = 1006 //交易完成
	ORDER_PART_DONE  = 1007 //部分确权
	ORDER_FAIL       = 2002 //关闭（失败）
	ORDER_REFUND     = 2003 //已退款

	ORDER_PAY_NOT_PAID   = 1001 //未支付
	ORDER_PAY_PROCESSING = 1002 //进行中
	ORDER_PAY_PAID       = 1004 //已付款
	ORDER_PAY_CLOSED     = 2002 //关闭
	ORDER_PAY_REFUND     = 2003 //已退款

	WITHDRAW_SERVICE_WAITING              = 1 // 新提交
	WITHDRAW_SERVICE_SPLIT_WAITING        = 2 // 待分账
	WITHDRAW_SERVICE_SPLIT_SUCCESS        = 3 // 分账成功
	WITHDRAW_SERVICE_SPLIT_FAIL           = 4 // 分账失败
	WITHDRAW_SERVICE_WITHDRAW_SUCCESS     = 5 // 提现成功
	WITHDRAW_SERVICE_WITHDRAW_FAIL        = 6 // 提现失败
	WITHDRAW_SERVICE_SPLIT_CANCEL_SUCCESS = 7 // 分账撤回成功
	WITHDRAW_SERVICE_SPLIT_CANCEL_FAIL    = 8 // 分账撤回失败

)

func NewLakalaService(c *gin.Context) *LakalaService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	collection := LakalaService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &collection
}
func GetLakalaService() *LakalaService {
	collection := LakalaService{}
	return &collection
}

/***
 * @Author: [rozimamat]
 * @description: 查询银联商务商户并迁移到拉卡拉
 * @Date: 2023-07-10 18:22:15
 * @param {string} id
 */
func (la LakalaService) MigrateUMS(c *gin.Context, id string, smsMobile string) (map[string]interface{}, error) {

	db := tools.GetDB()

	selfSignInfoMap := make(map[string]interface{})

	db.Table("t_self_sign_merchant_info").Where("id = ?", id).Scan(&selfSignInfoMap)

	if selfSignInfoMap == nil {
		return nil, errors.New("data_not_found")
	}

	return la.Api21(c, id, selfSignInfoMap, smsMobile)

}

/***
 * @Author: [rozimamat]
 * @description: 获取商户资料
 * @Date: 2023-07-17 12:16:32
 * @param {string} resid
 */
func (la LakalaService) GetMerchantInfo(c *gin.Context, resid string) map[string]interface{} {
	db := tools.GetDB()
	selfSignInfoMap := make(map[string]interface{})

	db.Table("t_self_sign_merchant_info").Where("restaurant_id = ?", resid).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Scan(&selfSignInfoMap)
	if selfSignInfoMap["id"] != nil {
		//一些特殊信息处理部分为

		selfSignInfoMap["bank_name"] = la.GetBankName(c, tools.ToString(selfSignInfoMap["bank_id"]))
		BankAcctType := tools.ToString(selfSignInfoMap["bank_acct_type"])
		if len(BankAcctType) > 0 {
			if tools.ToInt(BankAcctType) == 1 { //对公账户
				selfSignInfoMap["bank_acct_type_name"] = "对私"
			} else { //对私 账户
				selfSignInfoMap["bank_acct_type_name"] = "对公"
			}
		} else {
			selfSignInfoMap["bank_acct_type_name"] = ""
		}
		if selfSignInfoMap["shop_business_province_id"] != nil {
			prMap := make(map[string]interface{})
			db.Table("b_self_sign_area").Where("code = ?", selfSignInfoMap["shop_business_province_id"]).Select("id,name_zh").Scan(&prMap)
			if prMap["id"] != nil {
				selfSignInfoMap["shop_business_province"] = prMap["name_zh"]
			}
		}
		if selfSignInfoMap["shop_business_city_id"] != nil {
			prMap := make(map[string]interface{})
			db.Table("b_self_sign_area").Where("code = ?", selfSignInfoMap["shop_business_city_id"]).Select("id,name_zh").Scan(&prMap)
			if prMap["id"] != nil {
				selfSignInfoMap["shop_business_city"] = prMap["name_zh"]
			}
		}
		if selfSignInfoMap["shop_business_country_id"] != nil {
			prMap := make(map[string]interface{})
			db.Table("b_self_sign_area").Where("code = ?", selfSignInfoMap["shop_business_country_id"]).Select("id,name_zh").Scan(&prMap)
			if prMap["id"] != nil {
				selfSignInfoMap["shop_business_country"] = prMap["name_zh"]
			}
		}

	}
	return selfSignInfoMap

}

/***
 * @Author: [rozimamat]
 * @description: 获取签约需要的字段
 * @Date: 2023-07-17 12:40:06
 * @param {*gin.Context} c
 * @param {string} res_id
 */
func (la LakalaService) GetCopyContent(c *gin.Context, res_id string) []map[string]string {

	copyFields := la.langUtil.TArrString("lakala_copy_content")
	copyContent := make([]map[string]string, 0)
	merInfo := la.GetMerchantInfo(c, res_id)
	for k, v := range merInfo {
		if v != nil {
			for kk, vv := range copyFields {
				if kk == k {
					copyItem := make(map[string]string, 0)
					copyItem["key"] = vv
					copyItem["value"] = tools.ToString(v)
					copyContent = append(copyContent, copyItem)
				}
			}
		}
	}

	return copyContent

}

/***
 * @Author: [rozimamat]
 * @description: 拉卡拉通知
 * @Date: 2023-07-17 12:16:43
 * @param {*gin.Context} c
 * @param {LakalaEntity.LakalaNotify} params
 */
func (la LakalaService) MemberNotify(c *gin.Context, params LakalaEntity.LakalaNotify) error {

	db := tools.GetDB()

	var ev LakalaEntity.Event
	json.Unmarshal([]byte(params.Event), &ev)
	prm, _ := json.Marshal(params)
	tools.Logger.Info("拉卡拉入驻通知 开始")
	tools.Logger.Info(string(prm))
	tools.Logger.Info("拉卡拉入驻通知 结束")
	memberMap := make(map[string]interface{})
	db.Table("t_self_sign_merchant_info").Where("out_member_no = ?", ev.OutMemberNo).Select("id,member_no,restaurant_id").Scan(&memberMap)
	if memberMap["id"] != nil {
		restaurant_id := tools.ToString(memberMap["restaurant_id"])
		if memberMap["member_no"] == nil && len(ev.MemberNo) > 0 {

			tx := db.Begin()
			defer func() {
				if r := recover(); r != nil {
					tx.Rollback()
				}
			}()

			selfSignMap := make(map[string]interface{})
			selfSignMap["lakala_verify_state"] = 2
			selfSignMap["member_no"] = ev.MemberNo
			selfSignMap["submit_type"] = 2 //拉卡拉入驻
			updateErr2 := tx.Table("t_self_sign_merchant_info").Where("restaurant_id = ?", restaurant_id).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Updates(&selfSignMap).Error
			if updateErr2 != nil {
				tools.Logger.Info("修改银商商户信息失败", updateErr2.Error())
				tx.Rollback()
				return nil
			}
			tx.Commit()

		}
		go func() {
			defer func() {
				if err := recover(); err != nil {
					tools.Logger.Error("手动查询是否通过实名认证 error: %s\n", err)
				}
			}()
			//手动查询是否通过实名认证
			la.Api25(c, restaurant_id, ev.OutMemberNo, "2")
		}()
	}

	return nil

}

/***
 * @Author: [rozimamat]
 * @description: 生成商户号
 * @Date: 2023-07-12 18:22:46
 * @param {string} resid
 */
func (la LakalaService) GenerateMerchantNumber(resid string) string {
	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))
	rndStrNum := fmt.Sprintf("%04v", rnd.Int31n(10000))
	var now = time.Now()
	rndStr := fmt.Sprintf("%d%d%d%d%d%d%s", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), rndStrNum) //随机数
	db := tools.GetDB()
	resMap := make(map[string]interface{})
	db.Table("t_restaurant").Where("id=?", resid).Select("city_id").Scan(&resMap)
	areaIdStr := ""
	if resMap["city_id"] != nil {
		areaMap := make(map[string]interface{})
		db.Table("b_self_sign_area").Where("mlz_city_id = ?", resMap["city_id"]).Select("code").Scan(&areaMap)
		if areaMap["code"] != nil {
			areaIdStr = tools.ToString(areaMap["code"])
		}
	}
	//拉卡拉商户号规则  la+城市code+店铺id+随机数
	fileName := "la_" + areaIdStr + "_" + resid + "_" + rndStr
	return fileName
}

/***
 * @Author: [rozimamat]
 * @description: 获取银行名称
 * @Date: 2023-07-12 18:22:31
 * @param {*gin.Context} c
 * @param {string} bkid
 */
func (la LakalaService) GetBankName(c *gin.Context, bkid string) string {
	var bnk models.SelfSignBank
	tools.Db.Where("id = ?", bkid).First(&bnk)
	return bnk.NameZh
}

/***
 * @Author: [rozimamat]
 * @description: 查询余额
 * @Date: 2023-07-12 18:22:19
 * @param {*gin.Context} c
 * @param {string} id
 * @param {string} member_no
 */
//Api31
func (la LakalaService) AccountBalanceQuery(c *gin.Context, member_no string, special_account_no string) (string, error) {

	param := make(map[string]interface{})
	param["member_no"] = member_no
	param["account_type"] = "1000" //默认值

	if len(special_account_no) > 0 {
		param["special_account_no"] = special_account_no
	}

	api := "account.balance.get"

	result, err := la.Send(c, param, api)
	if err != nil {
		return "", err
	}
	return result, nil
}

/***
 * @Author: [rozimamat]
 * @description: 拉卡拉入驻信息发送接口
 * @Date: 2023-07-13 13:18:20
 * @param {*gin.Context} c
 * @param {string} resId
 * @param {map[string]interface{}} merInfo
 * @param {string} smsMobile
 */
func (la LakalaService) Api21(c *gin.Context, id string, merInfo map[string]interface{}, smsMobile string) (map[string]interface{}, error) {

	//拉卡拉文档地址  http://*************:6524/docs/qzt/qzt-1dt3fhr2456as

	idcardNo := tools.ToString(merInfo["mer_idcard_num"])
	if len(idcardNo) == 0 {
		return nil, errors.New("id_card_no_empty")
	}

	db := tools.GetDB()

	mInfo := make(map[string]interface{})

	db.Table("t_self_sign_merchant_info").Where("id = ?", id).Scan(&mInfo)

	param := make(map[string]interface{})
	
	outMemberNo := la.GenerateMerchantNumber(tools.ToString(mInfo["restaurant_id"])) //生成商户号

	if mInfo != nil && mInfo["out_member_no"] == nil {
		updateMap := make(map[string]interface{})
		updateMap["lakala_verify_mobile"] = smsMobile
		updateMap["out_member_no"] = outMemberNo
		db.Table("t_self_sign_merchant_info").Where("id = ?", id).Updates(&updateMap)

	} else {

		state := tools.ToInt(mInfo["state"])
		if state == 10 { //入驻成功
			return nil, errors.New("sign_in_success")
		}
		if mInfo["verify_url_start"] != nil {
			//链接2小时候失效
			nw := carbon.Now()

			verifyStart := tools.ToString(mInfo["verify_url_start"])

			expTime := carbon.Parse(verifyStart, "Asia/Shanghai").AddHours(2) //默认2小时候过期  过期的话重新获取

			if expTime.Gt(nw) {
				//没有过期
				strMap := make(map[string]interface{})
				strMap["url"] = mInfo["verify_url"]
				return strMap, nil
			}
		}
		//存在商户号
		if mInfo["out_member_no"] != nil {
			outMemberNo = tools.ToString(mInfo["out_member_no"])
		}

	}

	param["out_member_no"] = outMemberNo
	param["out_merchant_no"] = outMemberNo

	param["open_part"] = "1"
	//1：普通会员
	//2：收单商户

	param["back_url"] = configs.MyApp.LakalaConfig.RootUrl + "/ug/merchant/v1/lakala/memberNotify"
	param["front_url"] = configs.MyApp.LakalaConfig.RootUrl + "/ug/merchant/v1/lakala/memberNotify"
	param["member_category"] = "0"
	param["member_role"] = "R001"

	member_type := "2"

	name := tools.ToString(merInfo["legal_name"])
	// //00:企业，01:店铺：02：小微商户
	merType := tools.ToString(merInfo["reg_mer_type"])

	switch merType {
	case "00":
		//企业
		member_type = "3"
		//名称是企业的名称
		name = tools.ToString(merInfo["shop_business_name"])
		//个体户和商户商上传营业执照号码
		shopBusinessNum := tools.ToString(merInfo["shop_license_num"])
		if len(shopBusinessNum) == 0 {
			return nil, errors.New("business_license_no_empty")
		}
		param["business_license"] = shopBusinessNum
		BankAcctType := tools.ToString(merInfo["bank_acct_type"])
		if len(BankAcctType) > 0 {
			if tools.ToInt(BankAcctType) == 1 { //对公账户
				if merInfo["shop_business_province_id"] != nil {
					param["merchant_province_code"] = tools.ToString(merInfo["shop_business_province_id"]) + "0000"
				}
				if merInfo["shop_business_city_id"] != nil {
					param["merchant_city_code"] = tools.ToString(merInfo["shop_business_city_id"]) + "00"
				}
				if merInfo["shop_business_country_id"] != nil {
					param["merchant_city_code"] = tools.ToString(merInfo["shop_business_country_id"])
				}

			} else { //对私 账户
				param["bank_contact_name"] = tools.ToString(merInfo["bank_acct_name"])
				param["bank_contact_name"] = la.GetBankName(c, tools.ToString(merInfo["bank_id"]))
			}
		}
		// param["merchant_nature"]=8 //个体户

	case "02":
		//小微商户
		member_type = "2"
		// param["merchant_nature"]=13 //小微商户
	case "01":
		//普通商户
		// member_type ="4"

		member_type = "2" //个体户走小微

		BankAcctType := tools.ToString(merInfo["bank_acct_type"])

		if len(BankAcctType) > 0 {

			if tools.ToInt(BankAcctType) == 1 { //对公账户
				member_type = "4"
				//名称是店铺的名称
				name = tools.ToString(merInfo["shop_business_name"])
				//个体户和商户商上传营业执照号码
				shopBusinessNum := tools.ToString(merInfo["shop_license_num"])
				if len(shopBusinessNum) == 0 {
					return nil, errors.New("business_license_no_empty")
				}
				param["business_license"] = shopBusinessNum

			}
		}
		// param["merchant_nature"]=8 //个体户
	}
	//小微商户
	// member_type ="2" //全部按小微商户走
	//个人 2
	// param["member_type"] = "4" //个人工商户
	// param["member_type"] = "3" //企业会员

	param["member_type"] = member_type

	param["phone"] = smsMobile //迁移时用公司的手机号

	param["name"] = name //

	param["identity_type"] = 1
	param["identity_name"] = tools.ToString(merInfo["legal_name"])         //
	param["merchant_name"] = tools.ToString(merInfo["shop_business_name"]) // merInfo.ShopBusinessName// "测试二号餐厅"

	param["identity_no"] = tools.RsaEnctypt(idcardNo)                                  //身份证号
	param["bank_card_no"] = tools.RsaEnctypt(tools.ToString(merInfo["bank_acct_num"])) //银行卡

	param["bank_contact_no"] = la.GetBankName(c, tools.ToString(tools.ToString(merInfo["bank_id"]))) //  "建设银行"
	param["bank_contact_name"] = la.GetBankName(c, tools.ToString(tools.ToString(merInfo["bank_id"])))

	esignInfo := make(map[string]interface{})
	//合同内容
	esignInfo["A7"] = "0.02"                                            //内卡类支付业务：借记卡费率	机构上送，如果没有送，就默认“/”
	esignInfo["A8"] = "0.02"                                            //A8	境内卡类支付业务：借记卡费率封顶值	机构上送，如果没有送，就默认“/”
	esignInfo["A10"] = "0.02"                                           //A10	境内卡类支付业务：贷记卡费率	机构上送，如果没有送，就默认“/”
	esignInfo["A19"] = "0.02"                                           //A19	其他线下扫码业务：收单手续费费率
	esignInfo["A21"] = "1"                                              //A21	单笔代付：对公单笔费用	机构上送，如果没有送，就默认“/”
	esignInfo["A22"] = "1"                                              //A22	单笔代付：对私单笔费用	机构上送，如果没有送，就默认“/”
	esignInfo["A24"] = "1"                                              //A22	单笔代付：对私单笔费用	机构上送，如果没有送，就默认“/”
	esignInfo["A25"] = "1"                                              //A25	批量代付：对私单笔费用	机构上送，如果没有送，就默认“/”
	esignInfo["A27"] = "0.02"                                           //A27	互联网快捷支付：借记卡费率	机构上送，如果没有送，就默认“/”
	esignInfo["A28"] = "0.02"                                           //A28	互联网快捷支付：贷记卡费率	机构上送，如果没有送，就默认“/”
	esignInfo["A30"] = "0.02"                                           //A30	网银B2C：借记卡费率	机构上送，如果没有送，就默认“/”
	esignInfo["A31"] = "0.02"                                           //A31	网银B2C：贷记卡费率	机构上送，如果没有送，就默认“/”
	esignInfo["A32"] = "1"                                              //A32	网银B2C：技术服务费单笔条件值	机构上送，如果没有送，就默认“/”
	esignInfo["A33"] = "1"                                              //A33	网银B2C：技术服务费单笔条件值的等值	机构上送，如果没有送，就默认“/”
	esignInfo["A35"] = "1"                                              //A35	网银B2B：技术服务费单笔费用	机构上送，如果没有送，就默认“/”
	esignInfo["A37"] = "0.02"                                           //A37	微信支付：技术服务费费率	机构上送，如果没有送，就默认“/”
	esignInfo["A39"] = "0.02"                                           //A39	支付宝支付：技术服务费费率	机构上送，如果没有送，就默认“/”
	esignInfo["A41"] = "0.02"                                           //A41	银联线上统一收银台：借记卡费率	机构上送，如果没有送，就默认“/”
	esignInfo["A42"] = "0.02"                                           //A42	银联线上统一收银台：贷记卡费率	机构上送，如果没有送，就默认“/”
	esignInfo["A44"] = "0.02"                                           //A44	B2B订单：技术服务费费率	机构上送，如果没有送，就默认“/”
	esignInfo["A58"] = "2"                                              //A58	收单手续费结算方式选择	机构上送，不送默认就选择“2”
	esignInfo["B12"] = "https://www.xxx.com"                            //B12	网站地址	机构上送 必填
	esignInfo["B14"] = tools.ToString(merInfo["shop_business_address"]) //B14	办公地址	机构上送 必填
	esignInfo["B15"] = "2324423423423432423"                            //B15	营业执照注册号	机构上送 必填
	esignInfo["B16"] = "127.0.0.1"                                      //B16	交易IP地址	机构上送 必填
	esignInfo["C1"] = "某某某有限公司"                                         //C1	特约商户对账信息授权：合作公司名称	机构上送 必填
	esignInfo["C2"] = "某某某有限公司"                                         //C2	特约商户对账信息授权：与合作公司签署协议名称	机构上送 必填
	esignInfo["C3"] = "某某某有限公司"                                         //C3	特约商户对账信息授权：提供服务名称	机构上送 必填
	esignInfo["E1"] = "某某某有限公司"                                         //E1	结算授权委托书：合作公司名称	机构上送 必填
	esignInfo["E2"] = "某某某有限公司"                                         //E2	结算授权委托书：与合作方签署协议名称	机构上送 必填
	esignInfo["E3"] = "1"                                               //E3	结算授权委托书：清分结算方式选择	机构上送 必填
	esignInfo["E4"] = "某某某有限公司"                                         //E4	结算授权委托书：支付服务费承担方名称	机构上送 必填
	esignInfo["E5"] = "0.10"                                            //E5	结算授权委托书：商户分得最低分账比例	机构上送，E3选择第1种时，必填
	esignInfo["E6"] = ""                                                //E6	结算授权委托书：商户固定分账比例	机构上送，E3选择第2种时，必填
	esignInfo["E7"] = ""                                                //E7	结算授权委托书：合作方1固定分账比例	机构上送，E3选择第2种时，必填
	esignInfo["E8"] = ""                                                //E8	结算授权委托书：合作方2固定分账比例	机构上送，E3选择第2种时，必填
	esignInfo["E9"] = ""                                                //E9	结算授权委托书：合作方3固定分账比例	机构上送，E3选择第2种时，必填
	esignInfo["E10"] = "某某某有限公司"                                        //E10	结算授权委托书：合作公司名称	机构上送，E3选择第3种时，必填
	esignInfo["E11"] = "0.01"                                           //E11	结算授权委托书：商户分得最低分账比例	机构上送，E3选择第3种时，必填
	esignInfo["E12"] = "√"                                              //E12	结算授权委托书：结算周期方式选择	必填，E12、E13二选一
	esignInfo["E13"] = ""                                               //E13	结算授权委托书：结算周期方式选择	必填，E12、E13二选一
	esignInfo["E14"] = "某某某有限公司"                                        //E14	结算授权委托书：合作公司名称	机构上送，E13勾选时必填，如果没有送，就默认”与E1填写内容一致”
	esignInfo["E15"] = "30"                                             //E15	结算授权委托书：结算日期期限	机构上送，E13勾选时必填，如果没有送，就默认填“30”

	esignInfoStr, _ := json.Marshal(esignInfo)
	param["esign_info"] = string(esignInfoStr) //合同内容

	api := "member.open.part.page.url.get"

	tools.Logger.Info("拉卡拉入驻发送参数:api:", api)
	tools.Logger.Info(param)
	result, err := la.Send(c, param, api)
	if err != nil {
		return nil, err
	}

	var memberAddRes LakalaEntity.MemberResponse

	json.Unmarshal([]byte(result), &memberAddRes)

	var memberAdd LakalaEntity.MemberAdd

	json.Unmarshal([]byte(memberAddRes.Response), &memberAdd)

	if memberAdd.Status == "OK" {

		rsmap := make(map[string]interface{})

		rsmap["url"] = memberAdd.Result.Url
		lakaInfoUpdateMap := make(map[string]interface{})
		lakaInfoUpdateMap["lakala_verify_state"] = 1           //表示已经给拉卡拉发送过了
		lakaInfoUpdateMap["verify_url"] = memberAdd.Result.Url //链接也记录下来 以防 不点击
		lakaInfoUpdateMap["verify_url_start"] = carbon.Now().ToDateTimeString()
		er3 := db.Table("t_self_sign_merchant_info").Where("out_member_no = ?", outMemberNo).Updates(&lakaInfoUpdateMap).Error
		if er3 != nil {
			tools.Logger.Info("t_self_sign_merchant_info 更新失败", er3)
		}
		return rsmap, nil
	} else {
		return nil, errors.New(memberAdd.Message)
	}
}

/***
 * @Author: [rozimamat]
 * @description: 签名内容获取
 * @Date: 2023-07-17 13:43:12
 * @param {map[string]string} params
 */
func (la LakalaService) GetSignContent(params map[string]string) string {
	stringToBeSigned := ""
	if val, ok := params["app_id"]; ok {
		stringToBeSigned += val
	}
	if val, ok := params["timestamp"]; ok {
		stringToBeSigned += val
	}
	if val, ok := params["version"]; ok {
		stringToBeSigned += val
	}
	if val, ok := params["service"]; ok {
		stringToBeSigned += val
	}
	if val, ok := params["params"]; ok {
		stringToBeSigned += val
	}
	if val, ok := params["image_type"]; ok {
		stringToBeSigned += val
	}
	if val, ok := params["image_name"]; ok {
		stringToBeSigned += val
	}
	stringToBeSigned = strings.ReplaceAll(stringToBeSigned, "\r\n", "")
	return stringToBeSigned
}

/***
 * @Author: [rozimamat]
 * @description: 发送数据
 * @Date: 2023-07-10 19:12:47
 * @param {string} param
 * @param {string} api
 */
func (la LakalaService) Send(c *gin.Context, bizContent map[string]interface{}, api string) (string, error) {
	return la.SendData(tools.GetRealIp(c), bizContent, api)
}

/***
 * @Author: [rozimamat]
 * @description: 发送数据
 * @Date: 2023-07-10 19:12:47
 * @param {string} param
 * @param {string} api
 */
func (la LakalaService) SendData(ip string, bizContent map[string]interface{}, api string) (string, error) {
	currentTime :=fmt.Sprintf("%d", time.Now().Unix())
	paramsStr1, _ := json.Marshal(bizContent)
	params := map[string]string{
		"app_id":    configs.MyApp.LakalaConfig.AppId, // "7076471896390946817",//正式环境appid
		"timestamp": currentTime,
		"version":   "3.0",
		"service":   api,
		"params":    string(paramsStr1),
	}

	url := configs.MyApp.LakalaConfig.ServerUrl

	method := "POST"
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)

	for k, v := range params {
		_ = writer.WriteField(k, v)
	}
	signContent := la.GetSignContent(params)
	sign := tools.DsaSign(signContent)

	if sign == "" {
		return "", errors.New("sign_empty")
	}
	_ = writer.WriteField("sign", sign)
	err := writer.Close()
	if err != nil {
		tools.Logger.Error("FATAL拉卡拉发送错误1", err,params)
		return "", err
	}

	params["sign"] = sign
	strBytes, _ := json.Marshal(params)
	
	requestMap :=map[string]interface{}{
		"api": api,
		"ip":  ip,
		"content": string(strBytes),
	}
	rsb,_:=json.Marshal(requestMap)
	tools.Logger.Info("拉卡拉发送数据", string(rsb))

	client := &http.Client{
		Timeout: time.Second * 30,
	}
	req, err := http.NewRequest(method, url, payload)

	if err != nil {
		tools.Logger.Error("FATAL拉卡拉发送错误2", err,params)
		return "", err
	}
	req.Header.Add("User-Agent", "Apifox/1.0.0 (https://www.apifox.cn)")

	req.Header.Set("Content-Type", writer.FormDataContentType())
	res, err := client.Do(req)
	if err != nil {
		tools.Logger.Error("FATAL拉卡拉发送错误3", err,params)
		return "", err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	result := string(body)
	tools.Logger.Info("拉卡拉回复的数据:", result)
	if err != nil {
		tools.Logger.Error("FATAL拉卡拉发送错误解析返回数据失败4", err,result)
		return "", err
	}
	return result, nil

}

type SignResult struct {
	Status int
	Data   SignData
}
type SignData struct {
	Sign string
}

/***
 * @Author: [rozimamat]
 * @description: 图片上传
 * @Date: 2023-07-17 13:43:29
 * @param {string} ImagePath
 * @param {string} ImageName
 */
func (la *LakalaService) UploadImage(ImagePath string, ImageName string) (map[string]interface{}, error) {
	param := make(map[string]interface{})
	// 读取图片文件
	param["image_type"] = "jpg"
	param["image_name"] = ImageName
	imageData, err := ioutil.ReadFile(ImagePath)
	if err != nil {
		tools.Logger.Info("无法读取图片文件:", err)
		return nil, err
	}
	param["image_content"] = imageData
	// paramStr := tools.MapToString(param)
	// res, err := la.Send(paramStr, "image.upload")
	// if err != nil {
	// 	tools.Logger.Info("无法读取图片文件:", err)
	// 	return nil, err
	// }
	return nil, nil
}

/***
 * @Author: [rozimamat]
 * @description: 查询会员信息    http://*************:6524/docs/qzt/qzt-1d4ranrhtk1r4
 * @Date: 2023-07-13 18:06:45
 * @param {*gin.Context} c
 * @param {string} res_id
 */
func (la LakalaService) MemberQuery(c *gin.Context, res_id string) (map[string]interface{}, error) {

	infoMap, err := la.GetLakalaMerchantInfo(res_id)
	if err != nil {
		return nil, errors.New("data_not_exists") //数据不存在
	}

	if infoMap["id"] == nil {
		return nil, errors.New("data_not_exists") //数据不存在
	}
	out_member_no := tools.ToString(infoMap["out_member_no"])
	state := tools.ToString(infoMap["state"])
	resMap := make(map[string]interface{})
	res25, _ := la.Api25(c, res_id, out_member_no, state)
	resMap["member"] = res25
	if infoMap["member_no"] != nil {
		member_no := tools.ToString(infoMap["member_no"])
		res27, _ := la.BankCardCheck(c, member_no)
		resultMap, _ := tools.StringToMap(res27)
		resMap["card"] = resultMap
	}

	return resMap, nil

}

/***
 * @Author: [rozimamat]
 * @description: 查询商户信息
 * @Date: 2023-07-18 16:39:27
 * @param {*gin.Context} c
 * @param {string} member_no
 */
func (la LakalaService) MerchantQuery(c *gin.Context, member_no string) (map[string]interface{}, error) {

	resMap, _ := la.CheckMember(c, member_no)

	return resMap, nil

}

/***
 * @Author: [rozimamat]
 * @description: 会员信息查询 内部调用
 * @Date: 2023-07-17 13:43:40
 * @param {string} res_id
 */
func (la LakalaService) MemberQueryInternal(c *gin.Context, res_id string) (map[string]interface{}, error) {

	infoMap, err := la.GetLakalaMerchantInfo(res_id)
	if err != nil {
		return nil, errors.New("data_not_exists") //数据不存在
	}

	if infoMap["id"] == nil {
		return nil, errors.New("data_not_exists") //数据不存在
	}
	out_member_no := tools.ToString(infoMap["out_member_no"])
	state := tools.ToString(infoMap["state"])

	return la.Api25(c, res_id, out_member_no, state)

}

/***
 * @Author: [rozimamat]
 * @description: 获取拉卡拉入驻信息
 * @Date: 2023-07-17 13:44:04
 * @param {string} res_id
 */
func (la LakalaService) GetLakalaMerchantInfo(res_id string) (map[string]interface{}, error) {

	db := tools.GetDB()

	infoMap := make(map[string]interface{})

	db.Table("t_self_sign_merchant_info").Where("restaurant_id = ?", res_id).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Scan(&infoMap)

	if infoMap["id"] == nil {
		return nil, errors.New("data_not_exists") //数据不存在
	}
	// out_member_no :=tools.ToString(infoMap["out_member_no"])
	// state :=tools.ToString(infoMap["state"])
	return infoMap, nil
}

/***
 * @Author: [rozimamat]
 * @description: 会员信息查询接口
 * @Date: 2023-07-17 13:44:16
 * @param {string} res_id
 * @param {string} out_member_no
 * @param {string} state
 */
func (la LakalaService) Api25(c *gin.Context, res_id string, out_member_no string, state string) (map[string]interface{}, error) {

	//文档地址  http://*************:6524/docs/qzt//168

	db := tools.GetDB()

	param := make(map[string]interface{})
	param["out_member_no"] = out_member_no

	api := "member.info.query"

	tools.Logger.Info("拉卡拉 查询会员信息 发送参数:api:", api)
	tools.Logger.Info(param)
	result, err := la.Send(c, param, api)
	if err != nil {
		tools.Logger.Error("拉卡拉 查询会员信息失败:", err.Error())
		return nil, err
	}
	tools.Logger.Info(result)
	var mq LakalaEntity.MemberQueryResult
	json.Unmarshal([]byte(result), &mq)

	var mqr LakalaEntity.MemberQueryResultResponse
	json.Unmarshal([]byte(mq.Response), &mqr)

	st := tools.ToInt(state)

	if len(mqr.Result.MemberNo) > 0 && (st >= 1 && st < 3) {
		//查询时修改商户状态

		if mqr.Result.MemberStatus == 1 && mqr.Result.RealStatus == 1 { //只有通过实名认证和会员认证后才记录商户信息
			state := 2 //入驻成功

			tx := db.Begin()
			defer func() {
				if r := recover(); r != nil {
					tx.Rollback()
					tools.Logger.Error("拉卡拉会员查询错误:", r)
				}
			}()

			selfSignMap := make(map[string]interface{})

			selfSignMap["lakala_verify_state"] = state //入驻成功
			selfSignMap["member_no"] = mqr.Result.MemberNo
			selfSignMap["submit_type"] = 2 //拉卡拉入驻
			if state == 2 {
				selfSignMap["state"] = 10 //入驻成功
			}
			updateErr2 := tx.Table("t_self_sign_merchant_info").Where("restaurant_id = ?", res_id).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Updates(&selfSignMap).Error
			if updateErr2 != nil {
				tools.Logger.Error("修改银商商户信息失败:", updateErr2.Error())
				tx.Rollback()
				return nil, errors.New("server_error")
			}
			tx.Commit()

			//归档
			la.ArchiveData(res_id, mqr.Result.MemberNo)

		}

	}

	prm, _ := json.Marshal(mq)
	tools.Logger.Info("member-query-result")
	tools.Logger.Info(string(prm))
	resultMap, _ := tools.StringToMap(result)
	return resultMap, nil

}

// 分账
// 接口号码115
func (la LakalaService) SplitBalance(c *gin.Context, member_no string, amount int, order_desc string, out_order_no string) (string, error) {

	//文档地址  http://*************:6524/docs/qzt/qzt-1ekbcqhkp9v4c

	param := make(map[string]interface{})
	param["member_no"] = configs.MyApp.LakalaConfig.SystemMerNo
	param["account_type"] = "1000"
	param["out_order_no"] = out_order_no
	param["amount"] = amount
	split_rule_data := make(map[string]interface{})
	split_list := make([]map[string]interface{}, 0)
	split_item := make(map[string]interface{})
	split_item["member_no"] = member_no
	split_item["amount"] = amount
	split_list = append(split_list, split_item)
	split_rule_data["split_list"] = split_list
	param["split_rule_data"] = split_rule_data
	param["order_desc"] = order_desc

	api := "order.balance.split"

	tools.Logger.Info("拉卡拉 分账给会员 发送参数:api:", api)
	tools.Logger.Info(param)
	result, err := la.Send(c, param, api)
	if err != nil {
		tools.Logger.Error("拉卡拉 分账给会员 失败:", err.Error())
		return "", err
	}
	tools.Logger.Info(result)
	return result, nil

}

// 分账 撤销
// api 号码 116
func (la LakalaService) SplitCancel(c *gin.Context, order_no string, split_seq_no string, amount int) (string, string, error) {

	//文档地址  http://*************:6524/docs/qzt/qzt-1ekbcr9mjaici

	param := make(map[string]interface{})
	param["order_no"] = order_no
	param["split_seq_no"] = split_seq_no
	param["amount"] = amount

	api := "order.split.revoke"

	tools.Logger.Info("拉卡拉 分账撤销 发送参数:api:", api)
	tools.Logger.Info(param)
	result, err := la.Send(c, param, api)
	if err != nil {
		tools.Logger.Error("拉卡拉 分账撤销 失败:", err.Error())
		return "", "", err
	}
	tools.Logger.Info(result)

	var mq LakalaEntity.LakalaDefaultResult
	json.Unmarshal([]byte(result), &mq)

	var mqr LakalaEntity.BalanceCancelResponse
	json.Unmarshal([]byte(mq.Response), &mqr)

	if mqr.Status != "OK" {

		return "", "", errors.New(mq.Message)

	}

	return mqr.Result.OrderNo, mqr.Result.SplitSeqNo, nil

}

// 提现
// Api 号码 12
func (la LakalaService) Withdraw(c *gin.Context, member_no string, out_order_no string, amount int, bank_card_no string, withdraw_type string, order_name string, exts string) (string, error) {

	//文档地址  http://*************:6524/docs/qzt//340

	param := make(map[string]interface{})

	param["member_no"] = member_no
	param["out_order_no"] = out_order_no
	param["account_type"] = "1000"
	param["amount"] = amount
	param["billing_fee"] = 0
	param["bank_card_no"] = bank_card_no
	param["withdraw_type"] = withdraw_type
	param["back_url"] = configs.MyApp.LakalaConfig.RootUrl + "/ug/merchant/v1/lakala/withdrawNotify"
	param["order_name"] = order_name
	param["exts"] = exts

	api := "order.withdraw.request"

	//tools.Logger.Info("拉卡拉 提现 发送参数:api:", api)
	//tools.Logger.Info(param)
	result, err := la.Send(c, param, api)
	if err != nil {
		tools.Logger.Error("拉卡拉 提现 失败:", err.Error())
		return "", err
	}
	//tools.Logger.Info(result)
	return result, nil

}

// 订单查询
// Api 19
func (la LakalaService) OrderCheck(c *gin.Context, order_no string) (string, error) {

	//文档地址  http://*************:6524/docs/qzt/qzt-1ekbcqhkp9v4c

	param := make(map[string]interface{})
	param["order_no"] = order_no

	api := "order.status.get"

	//tools.Logger.Info("拉卡拉 订单查询 发送参数:api:", api)
	//tools.Logger.Info(param)
	result, err := la.Send(c, param, api)
	if err != nil {
		tools.Logger.Error("拉卡拉 订单查询 失败:", err.Error())
		return "", err
	}
	//tools.Logger.Info(result)
	return result, nil

}

/***
 * @Author: [rozimamat]
 * @description: 归档 拉卡拉数据
 * @Date: 2023-07-14 19:58:06
 * @param {string} res_id
 */
func (la LakalaService) ArchiveData(res_id string, member_no string) {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				tools.Logger.Error("归档 拉卡拉数据 error: %s\n", err)
			}
		}()
		db := tools.GetDB()
		lkMap := make(map[string]interface{})
		db.Table("t_self_sign_merchant_info").Where("restaurant_id = ?", res_id).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Scan(&lkMap)
		if lkMap["id"] != nil && lkMap["member_no"] != nil {
			state := tools.ToInt(lkMap["lakala_verify_state"])
			if state == 2 {
				// 入驻成功
				//归档数据
				ct0 := int64(0)
				db.Table("t_self_sign_merchant_info_archive").Where("restaurant_id = ? and submit_type = ? and member_no = ? and type = 1", res_id, 2, member_no).Count(&ct0) //查询拉卡拉归档数据
				if ct0 == 0 {

					if lkMap["id"] != nil {
						infoId := lkMap["id"]
						delete(lkMap, "id")
						delete(lkMap, "created_at")
						delete(lkMap, "updated_at")

						delete(lkMap, "wechat_verify_state")
						delete(lkMap, "alipay_verify_state")
						delete(lkMap, "ums_verify_state")
						delete(lkMap, "alter_state")
						delete(lkMap, "review_count")
						delete(lkMap, "verify_content")

						lkMap["created_at"] = carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s")
						lkMap["state"] = 10
						lkMap["enable"] = 0      //手动开启
						lkMap["submit_type"] = 2 //拉卡拉入驻

						var tmpImages []map[string]interface{}
						db.Table("t_self_sign_images").Where("restaurant_id = ?", res_id).Scan(&tmpImages)
						var tmpBnfs []map[string]interface{}

						db.Table("t_self_sign_bnf").Where("mer_info_id = ?", infoId).Scan(&tmpBnfs)

						lkMap["ums_review_imgs"], _ = json.Marshal(tmpImages)
						lkMap["ums_review_bnfs"], _ = json.Marshal(tmpBnfs)

						tx := db.Begin()
						defer func() {
							if r := recover(); r != nil {
								tx.Rollback()
								tools.Logger.Error("拉卡拉归档错误:", r)
							}
						}()
						er1 := tx.Table("t_self_sign_merchant_info_archive").Create(&lkMap).Error
						if er1 != nil {
							tx.Rollback()
							tools.Logger.Error("拉卡拉归档失败1:", er1)
							return
						}
						updateMap := make(map[string]interface{})
						updateMap["state"] = 10      //入户中成功
						updateMap["submit_type"] = 2 //拉卡拉入驻
						er2 := tx.Table("t_self_sign_merchant_info").Where("restaurant_id = ? ", res_id).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Updates(&updateMap).Error
						if er2 != nil {
							tx.Rollback()
							tools.Logger.Error("拉卡拉归档失败2:", er2)
							return
						}

						tx.Commit()

					}
				}
			}
		}

	}()
}

/***
 * @Author: [rozimamat]
 * @description: 查询绑定银行卡列表
 * @Date: 2023-07-28 13:15:01
 * @param {*gin.Context} c
 * @param {string} res_id
 * @param {string} member_no
 */
// Api 27
func (la LakalaService) BankCardCheck(c *gin.Context, member_no string) (string, error) {

	//文档地址  http://*************:6524/docs/qzt/qzt-1d4rap36c09s0

	param := make(map[string]interface{})
	param["member_no"] = member_no

	api := "member.bank.card.bind.query"

	tools.Logger.Info("拉卡拉 查询绑定银行卡列表 发送参数:api:", api)
	tools.Logger.Info(param)
	result, err := la.Send(c, param, api)
	if err != nil {
		tools.Logger.Error("拉卡拉 查询绑定银行卡列表 失败:", err.Error())
		return "", err
	}
	tools.Logger.Info("拉卡拉 查询绑定银行卡列表 结果")
	tools.Logger.Info(result)

	return result, nil

}

/***
 * @Author: [rozimamat]
 * @description: 会员绑卡相关页面
 * @Date: 2023-07-28 13:16:03
 * @param {*gin.Context} c
 * @param {string} member_no
 */
//api 23
func (la LakalaService) BankCardBindApi(c *gin.Context, member_no string) (string, error) {

	//文档地址  http://*************:6524/docs/qzt/qzt-1cj3r5ovfo3ek

	param := make(map[string]interface{})
	param["member_no"] = member_no
	param["back_url"] = configs.MyApp.LakalaConfig.RootUrl + "/ug/merchant/v1/lakala/cardNotify"

	api := "member.bank.card.page.url.get"

	tools.Logger.Info("拉卡拉 会员绑卡相关页面 发送参数:api:", api)
	tools.Logger.Info(param)
	result, err := la.Send(c, param, api)
	if err != nil {
		tools.Logger.Error("拉卡拉 会员绑卡相关页面 失败:", err.Error())
		return "", err
	}
	tools.Logger.Info("拉卡拉 会员绑卡相关页面 结果")
	tools.Logger.Info(result)

	return result, nil

}

/***
 * @Author: [rozimamat]
 * @description: 查询会员商户
 * @Date: 2023-07-18 16:28:47
 * @param {string} res_id
 * @param {string} member_no
 * @param {string} state
 */
// api 26
func (la LakalaService) CheckMember(c *gin.Context, member_no string) (map[string]interface{}, error) {

	//文档地址  http://*************:6524/docs/qzt/qzt-1d4raol7h83o8

	param := make(map[string]interface{})
	param["member_no"] = member_no

	api := "member.merchant.query"

	tools.Logger.Info("拉卡拉 查询会员商户 发送参数:api:", api)
	tools.Logger.Info(param)
	result, err := la.Send(c, param, api)
	if err != nil {
		tools.Logger.Error("拉卡拉 查询会员商户 失败:", err.Error())
		return nil, err
	}
	tools.Logger.Info("拉卡拉 查询会员商户 结果")
	tools.Logger.Info(result)
	resultMap, _ := tools.StringToMap(result)
	return resultMap, nil

}

/***
 * @Author: [rozimamat]
 * @description: 获取签名,测试使用
 * @Date: 2023-07-18 16:53:30
 * @param {*gin.Context} c
 * @param {string} sign_type
 * @param {string} api
 * @param {string} content
 */
func (la LakalaService) Sign(c *gin.Context, sign_type string, api string, content string) (map[string]interface{}, error) {

	signMap := make(map[string]interface{})
	sign := ""
	tm := fmt.Sprintf("%d", time.Now().Unix())
	if sign_type == "dsa" {

		params := map[string]string{
			"app_id":    configs.MyApp.LakalaConfig.AppId, // "7076471896390946817",//正式环境appid
			"timestamp": tm,
			"version":   "3.0",
			"service":   api,
			"params":    content,
		}
		payload := &bytes.Buffer{}
		writer := multipart.NewWriter(payload)
		for k, v := range params {
			_ = writer.WriteField(k, v)
		}
		tools.Logger.Info("sign params:")
		tools.Logger.Info(params)
		signContent := la.GetSignContent(params)
		sign = tools.DsaSign(signContent)
	} else if sign_type == "rsa" {
		sign = tools.RsaEnctypt(content)
	}
	signMap["sign"] = sign
	signMap["timestamp"] = sign
	return signMap, nil
}

// CheckDownload
//
//	@Description: 拉卡拉对账单下载
//	@author: Alimjan
//	@Time: 2023-08-12 10:47:26
//	@receiver la LakalaService
//	@param c *gin.Context
//	@param billDate string
//	@return string
//	@return error
func (la LakalaService) CheckDownload(c *gin.Context, billDate string) (string, error) {
	api := "order.check.download"
	param := make(map[string]interface{})
	param["bill_date"] = billDate // 原订单（需要退款的订单编号）

	paramsStr1, _ := json.Marshal(param)
	params := map[string]string{
		"app_id":    configs.MyApp.LakalaConfig.AppId, // "7076471896390946817",//正式环境appid
		"timestamp": fmt.Sprintf("%d", time.Now().Unix()),
		"version":   "3.0",
		"service":   api,
		"params":    string(paramsStr1),
	}

	url := configs.MyApp.LakalaConfig.ServerUrl

	method := "POST"
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)

	for k, v := range params {
		_ = writer.WriteField(k, v)
	}
	signContent := la.GetSignContent(params)
	sign := tools.DsaSign(signContent)

	if sign == "" {
		return "", errors.New("sign_empty")
	}
	_ = writer.WriteField("sign", sign)
	err := writer.Close()
	if err != nil {
		tools.Logger.Error("FATAL拉卡拉发送错误1", err,params)
		return "", err
	}
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	req, err := http.NewRequest(method, url, payload)

	if err != nil {
		tools.Logger.Error("FATAL拉卡拉发送错误2", err,params)
		return "", err
	}
	req.Header.Add("User-Agent", "Apifox/1.0.0 (https://www.apifox.cn)")

	req.Header.Set("Content-Type", writer.FormDataContentType())
	res, err := client.Do(req)
	if err != nil {
		tools.Logger.Error("FATAL拉卡拉发送错误3", err,params)
		return "", err
	}
	defer res.Body.Close()

	// 创建本地文件
	file, err := os.Create(configs.MyApp.ArchivedFilePath + "/" + billDate + ".zip")
	if err != nil {
		tools.Logger.Error("Create file error:", err)
		return "", err
	}
	//defer file.Close()

	// 将文件流保存到本地文件
	_, err = io.Copy(file, res.Body)
	if err != nil {
		tools.Logger.Error("Save file error:", err)
		return "", err
	}
	return "", err
}

func (la LakalaService) RefundLakala(ip string, lakala map[string]interface{},isPartRefund bool) (map[string]interface{}, error) {
	// 拉卡拉退款请求体包装
	outRequestNo := la.GenerateOutOrderNo(context.Background(), "RF")
	api := "order.consume.refund"
	param := make(map[string]interface{})
	param["order_no"] = lakala["order_no"] // 原订单（需要退款的订单编号）
	param["member_no"] = lakala["seller_member_no"]

	param["out_request_no"] = outRequestNo                     // 应用平台请求号
	param["refund_amount"] = tools.ToInt(lakala["pay_amount"]) // 退款金额
	param["pay_seq_no"] = lakala["pay_seq_no"]                 // 支付流水号

	refundRule := make([]map[string]interface{}, 0)
	refundRule = append(refundRule, map[string]interface{}{ // 支付流水号
		"special_account_no": "S005",
		"member_no":          lakala["seller_member_no"],
		"amount":             tools.ToInt(lakala["pay_amount"]),
	})
	param["refund_rule"] = refundRule

	refundRules := []models.RefundRule{
		{
			MemberNo:      tools.ToString(lakala["seller_member_no"]),
			AccountTypeNo: "S005",
			Amount:        tools.ToInt(lakala["pay_amount"]),
		},
	}

	var lakalaRefund = models.PayLakalaRefund{
		OrderNo:      tools.ToString(lakala["order_no"]),
		PayLakalaID:  tools.ToInt(lakala["id"]),
		RefundRule:   refundRules,
		OutRequestNo: outRequestNo,
		RefundAmount: tools.ToInt(lakala["pay_amount"]),
		PaySeqNo:     tools.ToString(lakala["pay_seq_no"]),
	}
	if lakala["order_id"] !=nil {
		lakalaRefund.ObjectID = tools.ToInt64(lakala["order_id"])
	}
	r := tools.Db.Model(&models.PayLakalaRefund{}).Create(&lakalaRefund)
	tools.Logger.Info("result:", r)

	result, err := la.SendData(ip, param, api)
	if err != nil {
		msg := fmt.Sprintf("拉卡拉退款失败， 返回数据为空： 拉卡拉支付ID: %s, 错误内容：", lakala["id"], err.Error())
		tools.Logger.Error(msg)
		tools.AliDeveloperDingdingMsg(msg)
		return nil, err
	}
	resultMap, _ := tools.StringToMap(result)
	response := resultMap["response"].(string)
	tools.Logger.Info("拉卡拉退款结果：", response)
	resData, _ := tools.StringToMap(response)
	if resData["status"] != "OK" {
		if r, err := la.isLakalaRefunded(ip, lakalaRefund.OrderNo, lakalaRefund.PaySeqNo); err == nil {
			if r {
				msg := fmt.Sprintf("该订单已退款,订单号: %s, 拉卡拉支付ID: %d",
					tools.ToString(lakala["order_id"]),
					lakalaRefund.PayLakalaID,
				)
				tools.AliDeveloperDingdingMsg(msg)
				return nil, nil
			}
		} else {
			tools.Logger.Error(err)
		}
		msg := fmt.Sprintf("拉卡拉退款失败,订单号: %s, 拉卡拉支付ID: %d，退款记录ID: %d, 错误码: %s， 错误信息：%s",
			tools.ToString(lakala["order_id"]),
			lakalaRefund.PayLakalaID,
			lakalaRefund.ID,
			resData["error_code"],
			resData["message"],
		)
		tools.AliDeveloperDingdingMsg(msg)
		tools.Logger.Error(msg)
		return nil, errors.New("拉卡拉退款失败")
	}
	resMap := resData["result"].(map[string]interface{})
	if tools.ToInt(resMap["refund_status"]) != 1004 {
		if v, ok := resMap["error_message"]; ok {
			lakalaRefund.ErrorMessage = v.(string)
			tools.GetDB().Updates(&lakalaRefund)
		}
		errMessage := "拉卡拉退款失败,订单号：" + tools.ToString(lakala["order_id"]) + " " + resMap["error_message"].(string)
		//判断当前的时间是否是00:00-00:20之间
		if time.Now().Hour() == 0 && time.Now().Minute() < 20 &&  strings.Contains(errMessage,"0006-退货账户模式退货记账失败"){
			//经常错误，就不触发了
		}else{
			tools.AliDeveloperDingdingMsg(errMessage)
		}
		tools.Logger.Error(errMessage)
		return nil, errors.New("拉卡拉退款失败")
	}

	lakalaRefund.RefundSeqNo = resMap["refund_seq_no"].(string)
	lakalaRefund.OrderStatus = tools.ToInt(resMap["order_status"])
	lakalaRefund.RefundStatus = tools.ToInt(resMap["refund_status"])

	lakalaRefund.QztChannelPayRequestNo = resMap["qzt_channel_pay_request_no"].(string)
	lakalaRefund.ChannelTradeNo = resMap["channel_trade_no"].(string)
	lakalaRefund.ChannelSeqNo = resMap["channel_seq_no"].(string)
	if v, ok := resMap["pay_channel_trade_no"]; ok {
		lakalaRefund.PayChannelTradeNo = v.(string)
	}
	refundTime := carbon.Parse(resMap["refund_time"].(string), "Asia/Shanghai").Carbon2Time()
	lakalaRefund.RefundTime = &refundTime
	rs := tools.GetDB().Updates(&lakalaRefund)
	if rs.Error != nil {
		//tools.Logger.Error("更新拉卡拉退款失败：", rs.Error)
		errMessage := "拉卡拉退款日志记录出错,订单号：" + tools.ToString(lakala["order_id"]) + " " + resMap["error_message"].(string)
		tools.AliDeveloperDingdingMsg(errMessage)
		tools.Logger.Error("拉卡拉退款日志记录出错订单号:", lakala["order_id"], err.Error())
	}
	// 更新支付记录状态
	if !isPartRefund {	
		updateParams := map[string]interface{}{"pay_status": 2003, "order_status": 2003, "updated_at": carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s")}
		err = tools.Db.Table("t_pay_lakala").Where("id", tools.ToInt(lakala["id"])).Updates(&updateParams).Error
		if err != nil {
			errMessage := "退款成功时支付记录日志更新出错,订单号：" + tools.ToString(lakala["order_id"]) + " " + resMap["error_message"].(string)
			tools.AliDeveloperDingdingMsg(errMessage)
			tools.Logger.Error("退款成功时支付记录日志更新出错:", lakala["order_id"], err.Error())
		}
	}
	return resultMap, nil
}

func (la LakalaService) isLakalaRefunded(ip string, orderNo string, paySeqNo string) (bool, error) {
	api := "order.status.get"
	param := make(map[string]interface{})
	param["order_no"] = orderNo // 原订单（需要退款的订单编号
	result, err := la.SendData(ip, param, api)
	if err != nil {
		return false, err
	}
	resultMap, _ := tools.StringToMap(result)
	response := resultMap["response"].(string)
	tools.Logger.Info("拉卡拉退款结果：", response)
	resData, _ := tools.StringToMap(response)
	status, ok := resData["status"]
	if !ok {
		return false, errors.New("查询失败")
	}
	if status == "ERROR" {
		msg := fmt.Sprintf("拉卡拉订单查询错误：订单号： %s 错误码：%s 错误信息：%s", orderNo, resData["error_code"], resData["message"])
		return false, errors.New(msg)
	}
	resMap := resData["result"].(map[string]interface{})
	if v, ok := resMap["order_status"]; ok && 2003 == tools.ToInt(v) {
		return true, nil
	}
	return false, nil
}

/***
 * @Author: [rozimamat]
 * @description: 添加分账记录
 * @Date: 2023-07-31 15:52:17
 * @param {*gorm.DB} db
 * @param {int} restaurantId
 * @param {string} toMerNo
 * @param {int} withdrawalAmount
 * @param {string} desc
 */
func (la LakalaService) AddSplitRecord(c *gin.Context, db *gorm.DB, serviceId int, restaurantId int, toMerNo string, withdrawalAmount int, desc string, outOrderNo string) (models.LakalaSplit, error) {

	splitRuleData := make(map[string]interface{})
	split_list := make([]map[string]interface{}, 0)
	split_item := make(map[string]interface{})
	split_item["member_no"] = toMerNo
	split_item["amount"] = withdrawalAmount
	split_list = append(split_list, split_item)
	splitRuleData["split_list"] = split_list
	splitRuleDataByte, _ := json.Marshal(splitRuleData)

	sp := models.LakalaSplit{
		ServiceId:     serviceId,
		ResId:         restaurantId,
		FromMerno:     configs.MyApp.LakalaConfig.SystemMerNo,
		ToMerno:       toMerNo,
		Amount:        withdrawalAmount,
		OutOrderNo:    outOrderNo,
		AccountType:   "1000", //默认值
		SplitRuleData: string(splitRuleDataByte),
		OrderDesc:     desc,
		State:         0,
	}

	er1 := db.Create(&sp).Error
	if er1 != nil {
		tools.Logger.Error("FATAL分账数据写入失败:", er1)
		// la.EnableReCash(restaurantId, 0)
		return sp, errors.New("system_error")
	}

	return sp, nil

}

func (la LakalaService) GetBankCardDetail(c *gin.Context, card_id string, member_no string, bills []int) map[string]interface{} {
	db := tools.GetDB()
	var bk models.LakalaBankCard
	db.Where("card_id=?", card_id).First(&bk)
	detailMap := make(map[string]interface{})

	if bk.ID == 0 { //
		cardList, _ := la.GetBankCards(c, member_no)
		for _, v := range cardList {
			if tools.ToString(v["card_id"]) == card_id {
				detailMap["card_id"] = v["card_id"]
				detailMap["card_no"] = v["card_no"]
				detailMap["bank_name"] = v["bank_name"]

			}
		}
	} else {
		detailMap["card_id"] = bk.CardId
		detailMap["card_no"] = bk.CardNo
		detailMap["bank_name"] = bk.BankName
	}
	var bls []models.Mulazimpaytoresagent
	db.Model(models.Mulazimpaytoresagent{}).Where("id in (?)", bills).Select("id,trans_date,res_income").Find(&bls)
	billArr := make([]map[string]interface{}, 0)
	for _, v := range bls {
		bill := make(map[string]interface{})
		bill["id"] = v.ID
		bill["trans_date"] = v.TransDate
		bill["res_income"] = v.ResIncome

		billArr = append(billArr, bill)
	}
	detailMap["cashout_details"] = billArr

	return detailMap

}

/***
 * @Author: [rozimamat]
 * @description:
 * @Date: 2023-07-31 16:07:12
 * @param {LakalaService} la
 */
func (la LakalaService) AddServiceRecord(c *gin.Context, db *gorm.DB,
	tp int, //1：提现 2：充值
	optType string, // 操作类型  'merchant','mulazim','agent','shipper'
	adminId int,
	itemId int,
	outTradeNo string,
	memberNo string,
	bankCardId string,
	amount int,
	remark string,
	state int,
	bills []int) (models.LakalaWithdrawService, error) {

	detailMap := la.GetBankCardDetail(c, bankCardId, memberNo, bills)

	logsMap := make([]map[string]interface{}, 0)
	logMap := make(map[string]interface{})
	logMap["state"] = state
	logMap["time"] = carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s")
	logsMap = append(logsMap, logMap)

	logs, _ := json.Marshal(logsMap)

	detailJson, _ := json.Marshal(detailMap)
	billsStr := ""
	if len(bills) > 0 {
		for _, v := range bills {
			billsStr += tools.ToString(v) + ","
		}
		billsStr = billsStr[:len(billsStr)-1]
	}
	sp := models.LakalaWithdrawService{
		Type:       tp,      // 1：提现 2：充值
		OptType:    optType, //操作类型 'merchant','mulazim','agent','shipper',''
		AdminId:    adminId, //操作人员
		OptId:      itemId,
		OutTradeNo: outTradeNo, //平台交易号
		MemberNo:   memberNo,   //商户号
		BankCardId: bankCardId, //银行账户id
		Amount:     amount,     //金额
		Remark:     remark,
		Logs:       string(logs),
		State:      state,
		DetailJson: string(detailJson),
		Bills:      billsStr,
	}

	if optType == "merchant" { //商家提现 需要记录地区 城市
		resMap := make(map[string]interface{})
		db.Table("t_restaurant").Where("id = ? and deleted_at is null", itemId).Select("id,city_id,area_id").Scan(&resMap)
		if resMap["city_id"] != nil {
			sp.CityId = tools.ToInt(resMap["city_id"])
		}
		if resMap["area_id"] != nil {
			sp.AreaId = tools.ToInt(resMap["area_id"])
		}
	}

	er1 := db.Create(&sp).Error
	if er1 != nil {
		tools.Logger.Error("提现流水数据写入失败:", er1)
		return sp, errors.New("system_error")
	}
	return sp, nil

}

/***
 * @Author: [rozimamat]
 * @description: 分账撤销结果更新
 * @Date: 2023-08-02 17:28:56
 * @param {*gin.Context} c
 * @param {string} splitOrderNo
 * @param {string} splitSeqNo
 * @param {int} serviceId
 * @param {int} withdrawalAmount
 */
func (la LakalaService) UpdateSplitCancelRecord(c *gin.Context, splitOrderNo string, splitSeqNo string, serviceId int, withdrawalAmount int) error {
	_, _, ere := la.SplitCancel(c, splitOrderNo, splitSeqNo, withdrawalAmount) //撤销分账
	if ere != nil {
		la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_FAIL, ere.Error()) //分账撤回失败
	} else {
		la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_SUCCESS) //分账撤回成功
		db :=tools.GetDB()
		var wdServiceModel models.LakalaWithdrawService
		db.Model(&models.LakalaWithdrawService{}).Where("id = ?",serviceId).Find(&wdServiceModel)
		if wdServiceModel.ID > 0 {
			la.EnableReCash(wdServiceModel.OptId,0) //设置可以提现
		}
		
	}
	return ere
}

/***
 * @Author: [rozimamat]
 * @description: 更新拉卡拉业务日志
 * @Date: 2023-08-03 10:42:11
 * @param {int} serviceId
 * @param {map[string]interface{}} updateMap
 */
func (la LakalaService) UpdateServiceRecord(serviceId int, state int, errors ...interface{}) {
	updateMap := make(map[string]interface{})
	updateMap["state"] = state
	logMap := make(map[string]interface{})
	logMap["state"] = state
	logMap["time"] = carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s")
	if len(errors) > 0 {
		erStr := ""
		for _, v := range errors {
			erStr += tools.ToString(v) + ","
		}
		logMap["error"] = erStr
	}
	db := tools.GetDB()
	var ss models.LakalaWithdrawService
	db.Model(models.LakalaWithdrawService{}).Where("id =?", serviceId).Select("logs").First(&ss)
	logsMap := make([]map[string]interface{}, 0)
	json.Unmarshal([]byte(ss.Logs), &logsMap)
	logsMap = append(logsMap, logMap)
	logs, _ := json.Marshal(logsMap)
	updateMap["logs"] = logs
	db.Model(models.LakalaWithdrawService{}).Where("id =?", serviceId).Updates(&updateMap) //分账撤回失败

}

/***
 * @Author: [rozimamat]
 * @description: 拉卡拉提现
 * @Date: 2023-07-27 11:46:58
 * @param {int} restaurantId
 * @param {string} presentationPassword
 * @param {int} withdrawalAmount
 * @param {[]int} bills
 * @param {string} lang
 */
func (la LakalaService) CashOutLakala(c *gin.Context, adminId int, restaurantId int, withdrawalAmount int, bills []int, lang string, cardId string) (bool, string) {

	db :=tools.GetDB()
	appConfigMap := make(map[string]interface{})
	db.Table("app_config").Where("`key` = ? and `state` = ?","stop_cashout",1).Scan(&appConfigMap)
	if appConfigMap["value"] !=nil {
		if tools.ToInt(appConfigMap["value"])==1 {
			return false, "system_maintaining_in_progress"
		}
	}
	//1.发送提现请求
	//2.写入提现中 状态  在本次提现结果出来之前不能发起提现请求
	//3.验证发送的数据
	//4.给拉卡拉发送 分账请求  记录下来
	//5.给拉卡拉发送 提现请求,请求不成功的话 撤回分账   记录下来
	//6.定时查询是否到账  48小时内
	//7.到账了  标记商户可以继续提现 标记那些记录为 已提现

	//========================1.防止多次点击========开始==========================//
	//限流  10次请求 放行一次
	key := "cash_out_" + tools.ToString(restaurantId)
	waitTimeOut := 120                                  //每 120 秒 提现一次
	if !tools.AccessLimit(c, key, 10, 1, waitTimeOut) { //每120秒的请求只能通过一次
		return false, fmt.Sprintf(la.langUtil.T("retry_after_second"), waitTimeOut)
		// return false, "retry_after_10_second"
	}
	//========================1.防止多次点击========结束==========================//

	//=================2.写入提现中 状态  在本次提现结果出来之前不能发起提现请求=========开始===========//
	//监测是否可以提现 提现开始的时候 要 设置正在提现中
	can, msg := la.CanCashOut(restaurantId, withdrawalAmount, bills)

	if !can {
		return can, msg
	}

	billStr := ""
	for _, v := range bills {
		billStr += tools.ToString(v) + ","
	}
	tools.Logger.Info("商家提现开始 res_id:" + tools.ToString(restaurantId) +
		",提现金额:" + tools.ToString(withdrawalAmount) + "分,提现详细内容:" + billStr)

	//=================2.写入提现中 状态  在本次提现结果出来之前不能发起提现请求=========结束===========//
	//=================4.给拉卡拉发送 分账请求  记录下来=========开始===========//
	defer func() {
		if r := recover(); r != nil {
			tools.Logger.Error("FATAL提现失败异常:", r,",res_id",restaurantId)
			// la.EnableReCash(restaurantId, 0)
		}
	}()

	//检查佣金余额
	res, er1 := la.AccountBalanceQuery(c, configs.MyApp.LakalaConfig.SystemMerNo, "S005")
	if er1 != nil {
		tools.Logger.Error("FATAL佣金账户余额查询失败", er1,res)
		return false, "system_error"
	}

	var mq LakalaEntity.LakalaResult
	json.Unmarshal([]byte(res), &mq)

	var balanceResult LakalaEntity.BalanceResultResponse
	json.Unmarshal([]byte(mq.Response), &balanceResult)
	//提现金额大于 佣金账户里面的金额时 要发送短信提醒
	if balanceResult.Status == "OK" && (balanceResult.Result.AvailableAmount < configs.MyApp.LakalaConfig.SystemMinReserveAmount || balanceResult.Result.AvailableAmount < withdrawalAmount) {

		tools.Logger.Error("FATAL佣金账户余额余额不足:", res)
		tools.Logger.Error("FATAL佣金账户余额余额 可用余额:" + tools.ToPrice(tools.ToFloat64(balanceResult.Result.AvailableAmount)/100) + "元,最低储备:" + tools.ToPrice(tools.ToFloat64(configs.MyApp.LakalaConfig.SystemMinReserveAmount)/100) + "元")
		la.SendFailMessage(c, restaurantId, adminId, "系统佣金账户余额不足", 1007)
		return false, "system_maintaining_in_progress"

	}

	//开始提现

	go func() {
		defer func() {
			if r := recover(); r != nil {
				tools.Logger.Error("FATAL提现失败异常:", r)
				la.SendFailMessage(c, restaurantId, adminId, "提现失败异常", 1006)
				// la.EnableReCash(restaurantId, 0)
			}
		}()
		tools.Logger.Info("提现发送", "restaurantId:",restaurantId, "withdrawalAmount:",withdrawalAmount, "adminId:",adminId, "cardId:",cardId, "bills:",bills)
		la.CashOutSend(c, restaurantId, withdrawalAmount, adminId, cardId, bills)

	}()

	resultMap := make(map[string]interface{})
	resultMap["code"] = 200
	resultMap["message"] = la.langUtil.T("withdraw_request_sent")
	bytes, _ := json.Marshal(resultMap)

	return true, string(bytes)

}

/***
 * @Author: [rozimamat]
 * @description: 提现发送和记录
 * @Date: 2023-08-03 10:37:29
 * @param {*gin.Context} c
 * @param {int} restaurantId
 * @param {int} withdrawalAmount
 * @param {int} adminId
 * @param {string} cardId
 * @param {[]int} bills
 */
func (la LakalaService) CashOutSend(c *gin.Context, restaurantId int, withdrawalAmount int, adminId int, cardId string, bills []int) {

	db := tools.GetDB()
	//设置正在提现
	la.EnableReCash(restaurantId, 1)

	tools.Logger.Info("提现进行中", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills)

	resId := tools.ToString(restaurantId)
	_, toMerNo := la.GetMerNo(resId)

	if configs.CurrentEnvironment != "production" {
		if withdrawalAmount > 10 {
			withdrawalAmount = tools.RandInt(1, 10) // 测试平台提现金额 换成 1 分钱到 10分钱 ，因为 拉卡拉提现 会阻止 金额相同的交易 只能测试5次
		}
	}

	outTradeNo := la.GenerateOutOrderNo(c, "W")
	tools.Logger.Info("提现进行中 写入service记录 ", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills)
	wds, er0 := la.AddServiceRecord(c, db, 1, "merchant", adminId, restaurantId, outTradeNo, toMerNo, cardId, withdrawalAmount, "提现", 1, bills)

	if er0 != nil {
		tools.Logger.Error("FATAL提现流水 数据写入失败:res_id:", resId, ",admin_id:", adminId, er0)
		// la.EnableReCash(restaurantId, 0)
		return
	}
	serviceId := wds.ID

	outOrderNo := la.GenerateOutOrderNo(c, "S")

	desc := "split_" + resId + "_amt_" + tools.ToString(withdrawalAmount) //分账备注
	//写入分账记录
	tools.Logger.Info("提现进行中 写入分账记录 ", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills)
	sp, er1 := la.AddSplitRecord(c, db, serviceId, restaurantId, toMerNo, withdrawalAmount, desc, outOrderNo)
	if er1 != nil {
		tools.Logger.Error("分账数据写入失败:res_id:", resId, ",admin_id:", adminId, er1)
		// la.EnableReCash(restaurantId, 0)
		return
	}
	la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_WAITING) //代分账
	//分账给商户
	tools.Logger.Info("提现进行中 分账给商户 ", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills)
	splitResult, er2 := la.SplitBalance(c, toMerNo, withdrawalAmount, desc, outOrderNo) //分账
	if er2 != nil {
		tools.Logger.Error("FATAL分账失败:res_id:", resId, ",admin_id:", er2)
		la.SendFailMessage(c, restaurantId, adminId, "分账失败", 1001)
		// la.EnableReCash(restaurantId, 0)

		return
	}

	splitId := sp.ID
	tools.Logger.Info("提现进行中 更新分账数据 ", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills)
	//更新分账数据
	splitOrderNo, splitSeqNo, er3 := la.UpdateSplitResult(c, db, splitResult, restaurantId, splitId, withdrawalAmount)
	if er3 != nil {
		tools.Logger.Error("FATAL分账记录更新失败:res_id:", resId, ",admin_id:", er3)
		//撤销分账
		la.UpdateSplitCancelRecord(c, splitOrderNo, splitSeqNo, serviceId, withdrawalAmount)
		// la.EnableReCash(restaurantId, 0)
		return
	}
	tools.Logger.Info("提现进行中 分账订单查询 ", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills)
	spRes, er3_1 := la.OrderCheck(c, splitOrderNo) //查询订单状态  查询提现状态
	// spRes :="{\"response\":\"{\\\"status\\\":\\\"OK\\\",\\\"result\\\":{\\\"order_no\\\":7090288101941235712,\\\"order_status\\\":2003,\\\"amount\\\":1,\\\"out_order_no\\\":\\\"sp_5049_mGcsKBFoDmwZCehRsXfwvjpA\\\"}}\",\"sign\":\"MCwCFEugQgp3LVfspOG+cASPEfETKlESAhRrgHVUSdSCXMzn6XpreNwBlC1W+w==\"}"

	if er3_1 != nil {
		//分账查询失败
		tools.Logger.Error("FATAL分账查询失败:res_id:", resId, ",admin_id:", adminId,",err:",er3_1)
		la.SendFailMessage(c, restaurantId, adminId, "分账查询失败", 1002)
		//撤销分账
		la.UpdateSplitCancelRecord(c, splitOrderNo, splitSeqNo, serviceId, withdrawalAmount)
		// la.EnableReCash(restaurantId, 0)
		return
	}

	var spResult LakalaEntity.LakalaResult
	json.Unmarshal([]byte(spRes), &spResult)

	var spResponse LakalaEntity.WithDrawResultResponse
	json.Unmarshal([]byte(spResult.Response), &spResponse)

	if spResponse.Result.OrderStatus == ORDER_COMPLETE {
		splitMap := make(map[string]interface{})
		splitMap["state"] = 1 //分账成功
		er3_2 := db.Model(models.LakalaSplit{}).Where("id = ?", splitId).Updates(&splitMap).Error

		if er3_2 != nil {
			//分账查询失败
			tools.Logger.Error("分账 结果更新 失败:res_id:", resId, ",admin_id:", er3_2)

			// la.EnableReCash(restaurantId, 0)

			la.UpdateSplitCancelRecord(c, splitOrderNo, splitSeqNo, serviceId, withdrawalAmount)

			return
		}
		la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_SUCCESS) //分账成功

	} else {
		//分账失败
		tools.Logger.Error("FATAL分账查询失败:res_id:", resId, ",admin_id:", spRes)
		la.SendFailMessage(c, restaurantId, adminId, "分账查询失败", 1002)
		// la.EnableReCash(restaurantId, 0)
		la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_FAIL, "订单状态:"+tools.ToString(spResponse.Result.OrderStatus)) //分账失败
		return
	}
	tools.Logger.Info("提现进行中 分账成功 ", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills)
	//=================4.给拉卡拉发送 分账请求  记录下来=========结束===========//
	//===============5.给拉卡拉发送 提现请求,请求不成功的话 撤回分账   记录下来========开始==============//

	withdrawOutOrderNo := la.GenerateOutOrderNo(c, "W")

	withdrawOrderName := withdrawOutOrderNo

	exts := ""
	tools.Logger.Info("提现进行中 写入提现记录 ", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills)
	withdrawId, er4 := la.AddWithdrawRecord(c, db, wds.ID, splitId, restaurantId, withdrawalAmount, toMerNo, withdrawOutOrderNo, cardId, splitOrderNo, splitSeqNo, bills, exts)
	// er4 :=db.Create(&wd).Error
	if er4 != nil {
		tools.Logger.Error("FATAL提现数据写入失败:res_id:", resId, ",admin_id:", adminId, ",结果:", er4)
		//撤销分账
		la.UpdateSplitCancelRecord(c, splitOrderNo, splitSeqNo, serviceId, withdrawalAmount)
		//设置可以提现
		// la.EnableReCash(restaurantId, 0)
		return
	}
	//提现发起
	// withdrawResult :="{\"response\":\"{\\\"status\\\":\\\"OK\\\",\\\"result\\\":{\\\"order_no\\\":\\\"7090632572021166080\\\",\\\"order_status\\\":1006}}\",\"sign\":\"MC0CFCnEG17Y3Mak5ogJG3AkRXbwwjf8AhUAgbCohPuA+Pwzq7jqQ7dgjRWc7wc=\"}"
	// 提现错误 2002 实例
	//withdrawResult :="{\"response\":\"{\\\"status\\\":\\\"OK\\\",\\\"result\\\":{\\\"order_no\\\":\\\"7090632572021166080\\\",\\\"order_status\\\":2002,\\\"error_message\\\":\\\"WTF\\\"}}\",\"sign\":\"MC0CFCnEG17Y3Mak5ogJG3AkRXbwwjf8AhUAgbCohPuA+Pwzq7jqQ7dgjRWc7wc=\"}"
	// withdrawResult :="{\"response\":\"{\\\"status\\\":\\\"ERROR\\\",\\\"error_code\\\":\\\"AI000000\\\",\\\"message\\\":\\\"For input string: \\\\\\\"708943275596191334400\\\\\\\"\\\"}\",\"sign\":\"MCwCFBaBf/+0k83UcbrdieOwIt5/HmQeAhQTWkovJZQJ195EY8NmfqoFh7kgRg==\"}"
	tools.Logger.Info("提现进行中 发送提现请求 ", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills)
	withdrawResult, er5 := la.Withdraw(c, toMerNo, withdrawOutOrderNo, withdrawalAmount, cardId, "D0", withdrawOrderName, exts)
	if er5 != nil {
		tools.Logger.Error("FATAL提现发起失败:res_id:", resId, ",admin_id:", er5)
		la.SendFailMessage(c, restaurantId, adminId, "提现发起失败", 1004)
		la.UpdateSplitCancelRecord(c, splitOrderNo, splitSeqNo, serviceId, withdrawalAmount)
		// la.EnableReCash(restaurantId, 0)
		return
	}
	tools.Logger.Info("提现进行中 提现结果 ", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills)
	tools.Logger.Info("提现结果 res_id:", resId, ",admin_id:", adminId, "结果:", withdrawResult)

	var mq_0 LakalaEntity.LakalaResult
	json.Unmarshal([]byte(withdrawResult), &mq_0)

	var mqr_0 LakalaEntity.WithDrawResultResponse
	json.Unmarshal([]byte(mq_0.Response), &mqr_0)

	if mqr_0.Status != "OK" { //提现发起成功，但是提现失败

		tools.Logger.Error("提现发起成功，但是提现失败:res_id:", resId, ",admin_id:", adminId, ",结果:", withdrawResult)
		splitCancelError := la.UpdateSplitCancelRecord(c, splitOrderNo, splitSeqNo, serviceId, withdrawalAmount)
		if splitCancelError != nil { //分账撤销失败时发送通知
			la.SendFailMessage(c, restaurantId, adminId, "提现发起成功，但是提现失败", 1003)
		}
		// la.EnableReCash(restaurantId, 0)
		//写入真实的提现失败原因
		if len(mqr_0.Result.ErrorMessage) > 0 {
			tools.Logger.Error("提现发起成功，但是提现失败:res_id:", resId, ",admin_id:", adminId, ",提现失败原因:", mqr_0.Result.ErrorMessage)
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_WITHDRAW_FAIL, mqr_0.Result.ErrorMessage) //记录真实的提现失败原因
		}
		return

	}
	tools.Logger.Info("提现进行中 提现结果订单查询 ", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills)
	// widthdrawRes :="{\"response\":\"{\\\"status\\\":\\\"OK\\\",\\\"result\\\":{\\\"order_no\\\":7090632572021166080,\\\"order_status\\\":1006,\\\"amount\\\":1,\\\"out_order_no\\\":\\\"withdraw_5049_VyOQcWltoPCKHTruPx\\\"}}\",\"sign\":\"MCwCFD4mJ8GQK6bxu7t4cUuP9sS1Sj4MAhR9HSlLFGHJQQD/BDFElvJ2frsrOQ==\"}"
	widthdrawRes, er7 := la.OrderCheck(c, mqr_0.Result.OrderNo) //查询订单状态  查询提现状态
	if er7 != nil {
		//订单查询错误
		tools.Logger.Error("FATAL提现订单查询错误", er7)
		la.SendFailMessage(c, restaurantId, adminId, "提现订单查询错误", 1005)
		// la.EnableReCash(restaurantId, 0)
		return
	}

	tools.Logger.Info("提现进行中 提现结果订单订单状态结果 ", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills, widthdrawRes)

	var widthdrawResult LakalaEntity.LakalaResult
	json.Unmarshal([]byte(withdrawResult), &widthdrawResult)

	var widthdrawResponse LakalaEntity.WithDrawResultResponse
	json.Unmarshal([]byte(widthdrawResult.Response), &widthdrawResponse)
	orderNoFromLakala := mqr_0.Result.OrderNo
	if widthdrawResponse.Result.OrderStatus == ORDER_COMPLETE {
		wduMap := make(map[string]interface{})
		wduMap["order_no"] = orderNoFromLakala
		wduMap["state"] = 1
		db.Model(models.LakalaWithdraw{}).Where("id=?", withdrawId).Updates(&wduMap)
		la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_WITHDRAW_SUCCESS) //提现成功
	} else { //提现失败

		tools.Logger.Error("提现发起成功，但是提现失败:res_id:", resId, ",admin_id:", adminId, ",结果:", withdrawResult)

		//写入真实的提现失败原因
		la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_WITHDRAW_FAIL, tools.ToString(widthdrawResponse.Result.ErrorMessage)) //提现失败 记录原因

		splitCancelError := la.UpdateSplitCancelRecord(c, splitOrderNo, splitSeqNo, serviceId, withdrawalAmount)
		if splitCancelError != nil { //分账撤销失败时发送通知
			la.SendFailMessage(c, restaurantId, adminId, "提现发起成功，但是提现失败", 1003)
		}
		// la.EnableReCash(restaurantId, 0)

		return

	}
	tools.Logger.Info("提现进行中 写入提现记录 ", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills)
	er6 := la.AddCashOutLog(db, restaurantId, withdrawalAmount, serviceId, bills, orderNoFromLakala)

	if er6 != nil {
		tools.Logger.Error("FATAL提现 日志 写入失败:res_id:", resId, ",admin_id:", adminId, ",结果:", er6)
		// la.EnableReCash(restaurantId, 0)
		return
	}
	//设置可以提现
	la.EnableReCash(restaurantId, 0)
	tools.Logger.Info("提现结束", restaurantId, ",", withdrawalAmount, ",", adminId, ",", cardId, ",", bills)

}

/***
 * @Author: [rozimamat]
 * @description: 发送错误信息短信
 * @Date: 2023-08-02 19:40:52
 * @param {*gin.Context} c
 * @param {int} restaurantId
 * @param {int} adminId
 * @param {string} message
 */
func (la LakalaService) SendFailMessage(c *gin.Context, restaurantId int, adminId int, message string, error_number int) {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				tools.Logger.Error("发送错误信息短信失败:", err)
			}
		}()
		//"分账失败",1001
		//"分账查询失败",1002
		//"提现发起成功，但是提现失败",1003
		// "提现发起失败",1004
		//"提现订单查询错误",1005
		//"提现失败异常" 1006
		//"系统佣金账户余额不足",1007
		//"订单状态查询错误",1008
		//"商户余额账户错误",1009

		SmsContent := " 拉卡拉提现出现错误！" + tools.ToString(error_number) + "_" + message
		tools.AliDeveloperDingdingMsg(SmsContent)

		tools.Logger.Error("提现错误信息 :res_id:", restaurantId, ",admin_id:", adminId, ",错误编号:", tools.ToString(error_number), ",信息:", message)
		contentMap := map[string]interface{}{
			"code": tools.ToString(error_number),
		}
		//dataType, _ := json.Marshal(contentMap)
		//content := string(dataType)
		//er1 := tools.AliSmsSend(configs.MyApp.LakalaConfig.WarningAdminNumber, content, "Mulazim", "SMS_462460169")
		//if er1 != nil {
		//	fmt.Println("发送短信失败", er1)
		//}
		// 发送验证码
		smsServiceFactory := &factory.SmsServiceFactory{}
		smsService := smsServiceFactory.CreateSmsService()
		smsResp, err := smsService.SendSMSUsingTemplate(configs.MyApp.LakalaConfig.WarningAdminNumber,sms.ZhuTongTemplateTechnicalErrorNotification, contentMap)
		if err != nil {
			tools.Logger.Errorf("发送[告知技术人员平台运行错误]短信失败: %v \n",err)
		}
		tools.Logger.Infof("发送[告知技术人员平台运行错误]短信成功 结果: %v \n",smsResp)

	}()
}

/***
 * @Author: [rozimamat]
 * @description: 分账和提现合并记录
 * @Date: 2023-08-02 10:53:20
 * @param {*gin.Context} c
 * @param {string} admin_type
 * @param {int} withdrawalAmount
 * @param {int} adminId
 * @param {string} memberNo
 * @param {string} cardId
 * @param {[]int} bills
 */
func (la LakalaService) SplitAndWithdraw(c *gin.Context, admin_type string, withdrawalAmount int, adminId int, optId int, memberNo string, cardId string, bills []int) (bool, string, models.LakalaWithdrawService) {

	db := tools.GetDB()
	var ww models.LakalaWithdrawService
	//检查佣金余额
	res, er1 := la.AccountBalanceQuery(c, configs.MyApp.LakalaConfig.SystemMerNo, "S005")
	if er1 != nil {

		tools.Logger.Error("佣金账户余额查询失败", er1)
		return false, "system_error", ww
	}

	var mq LakalaEntity.LakalaResult
	json.Unmarshal([]byte(res), &mq)

	var balanceResult LakalaEntity.BalanceResultResponse
	json.Unmarshal([]byte(mq.Response), &balanceResult)
	//提现金额大于 佣金账户里面的金额时 要发送短信提醒
	if balanceResult.Status == "OK" && (balanceResult.Result.AvailableAmount < configs.MyApp.LakalaConfig.SystemMinReserveAmount || balanceResult.Result.AvailableAmount < withdrawalAmount) {

		tools.Logger.Error("佣金账户余额余额不足:", res)
		tools.Logger.Error("佣金账户余额余额 可用余额:" + tools.ToPrice(tools.ToFloat64(balanceResult.Result.AvailableAmount)/100) + "元,最低储备:" + tools.ToPrice(tools.ToFloat64(configs.MyApp.LakalaConfig.SystemMinReserveAmount)/100) + "元")
		la.SendFailMessage(c, 0, adminId, "系统佣金账户余额不足", 1007)
		return false, "system_maintaining_in_progress", ww

	}

	outTradeNo := la.GenerateOutOrderNo(c, "W")

	// wds,er0:=la.AddServiceRecord(c,db,1,"merchant",adminId,restaurantId,outTradeNo,memberNo,cardId,withdrawalAmount,"提现",1,bills)
	wds, er0 := la.AddServiceRecord(c, db, 1, admin_type, adminId, optId, outTradeNo, memberNo, cardId, withdrawalAmount, "提现", 1, bills)

	if er0 != nil {
		tools.Logger.Error("提现流水 数据写入失败:", er0)

		return false, "system_error", wds
	}
	serviceId := wds.ID

	outOrderNo := la.GenerateOutOrderNo(c, "S")

	desc := "split_" + "_amt_" + tools.ToString(withdrawalAmount) //分账备注
	//写入分账记录
	sp, er1 := la.AddSplitRecord(c, db, serviceId, 0, memberNo, withdrawalAmount, desc, outOrderNo)
	// sp,er1:=la.AddSplitRecord(db,serviceId,restaurantId,memberNo,withdrawalAmount,desc)
	if er1 != nil {
		tools.Logger.Error("分账数据写入失败:", er1)
		return false, "system_error", wds
	}
	la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_WAITING) //带分账
	// //分账给商户
	splitResult, er2 := la.SplitBalance(c, memberNo, withdrawalAmount, desc, outOrderNo) //分账
	if er2 != nil {
		tools.Logger.Error("分账失败:", er2)

		return false, "system_error", wds
	}

	splitId := sp.ID

	//更新分账数据
	splitOrderNo, splitSeqNo, er3 := la.UpdateSplitResult(c, db, splitResult, 0, splitId, withdrawalAmount)
	// er3,splitOrderNo,splitSeqNo :=la.UpdateSplitResult(c,db,splitResult,restaurantId,splitId,withdrawalAmount)
	if er3 != nil {
		tools.Logger.Error("分账记录更新失败:", er3)

		//撤销分账
		_, _, ere := la.SplitCancel(c, splitOrderNo, splitSeqNo, withdrawalAmount)
		if ere != nil {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_FAIL, ere.Error()) //分账失败
		} else {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_SUCCESS) //分账成功
		}
		return false, "system_error", wds
	}

	spRes, er3_1 := la.OrderCheck(c, splitOrderNo) //查询订单状态  查询提现状态
	// spRes :="{\"response\":\"{\\\"status\\\":\\\"OK\\\",\\\"result\\\":{\\\"order_no\\\":7090288101941235712,\\\"order_status\\\":2003,\\\"amount\\\":1,\\\"out_order_no\\\":\\\"sp_5049_mGcsKBFoDmwZCehRsXfwvjpA\\\"}}\",\"sign\":\"MCwCFEugQgp3LVfspOG+cASPEfETKlESAhRrgHVUSdSCXMzn6XpreNwBlC1W+w==\"}"

	if er3_1 != nil {
		//分账查询失败
		tools.Logger.Error("分账查询失败:", er3_1)
		//撤销分账
		_, _, ere := la.SplitCancel(c, splitOrderNo, splitSeqNo, withdrawalAmount)
		if ere != nil {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_FAIL, ere.Error()) //分账失败
		} else {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_SUCCESS) //分账成功
		}

		return false, "system_error", wds
	}

	var spResult LakalaEntity.LakalaResult
	json.Unmarshal([]byte(spRes), &spResult)

	var spResponse LakalaEntity.WithDrawResultResponse
	json.Unmarshal([]byte(spResult.Response), &spResponse)

	if spResponse.Result.OrderStatus == ORDER_COMPLETE {
		splitMap := make(map[string]interface{})
		splitMap["state"] = 1 //分账成功
		er3_2 := db.Model(models.LakalaSplit{}).Where("id = ?", splitId).Updates(&splitMap).Error

		if er3_2 != nil {
			//分账查询失败
			tools.Logger.Error("分账 结果更新 失败:", er3_2)

			_, _, ere := la.SplitCancel(c, splitOrderNo, splitSeqNo, withdrawalAmount)
			if ere != nil {
				la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_FAIL, ere.Error()) //分账撤销失败
			} else {
				la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_SUCCESS) //分账撤销成功
			}

			return false, "system_error", wds
		}
		la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_SUCCESS) //分账成功

	} else {
		//分账失败
		tools.Logger.Error("分账查询失败:", spRes)

		la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_FAIL, "订单状态:"+tools.ToString(spResponse.Result.OrderStatus)) //分账失败
		return false, "system_error", wds
	}

	//=================4.给拉卡拉发送 分账请求  记录下来=========结束===========//
	//===============5.给拉卡拉发送 提现请求,请求不成功的话 撤回分账   记录下来========开始==============//

	withdrawOutOrderNo := la.GenerateOutOrderNo(c, "W")

	withdrawOrderName := withdrawOutOrderNo

	exts := ""

	withdrawId, er4 := la.AddWithdrawRecord(c, db, wds.ID, splitId, 0, withdrawalAmount, memberNo, withdrawOutOrderNo, cardId, splitOrderNo, splitSeqNo, bills, exts)
	// withdrawId,er4 :=la.AddWithdrawRecord(c,db,wds.ID,splitId,restaurantId,withdrawalAmount,toMerNo,withdrawOutOrderNo,cardId,splitOrderNo,splitSeqNo,bills,exts)
	// er4 :=db.Create(&wd).Error
	if er4 != nil {
		tools.Logger.Error("提现数据写入失败:", er4)

		//撤销分账
		_, _, ere := la.SplitCancel(c, splitOrderNo, splitSeqNo, withdrawalAmount)
		if ere != nil {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_FAIL, ere.Error()) //分账失败
		} else {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_SUCCESS) //分账成功
		}

		return false, "system_error", wds
	}
	la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_SUCCESS) //记录状态 分账成功
	//提现发起
	// withdrawResult :="{\"response\":\"{\\\"status\\\":\\\"OK\\\",\\\"result\\\":{\\\"order_no\\\":\\\"7090632572021166080\\\",\\\"order_status\\\":1006}}\",\"sign\":\"MC0CFCnEG17Y3Mak5ogJG3AkRXbwwjf8AhUAgbCohPuA+Pwzq7jqQ7dgjRWc7wc=\"}"
	// withdrawResult :="{\"response\":\"{\\\"status\\\":\\\"ERROR\\\",\\\"error_code\\\":\\\"AI000000\\\",\\\"message\\\":\\\"For input string: \\\\\\\"708943275596191334400\\\\\\\"\\\"}\",\"sign\":\"MCwCFBaBf/+0k83UcbrdieOwIt5/HmQeAhQTWkovJZQJ195EY8NmfqoFh7kgRg==\"}"
	withdrawResult, er5 := la.Withdraw(c, memberNo, withdrawOutOrderNo, withdrawalAmount, cardId, "D0", withdrawOrderName, exts)
	if er5 != nil {
		tools.Logger.Error("提现发起失败:", er5)

		_, _, ere := la.SplitCancel(c, splitOrderNo, splitSeqNo, withdrawalAmount) //撤销分账
		if ere != nil {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_FAIL, ere.Error()) //分账失败
		} else {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_SUCCESS) //分账成功
		}

		return false, "system_error", wds
	}

	var mq_0 LakalaEntity.LakalaResult
	json.Unmarshal([]byte(withdrawResult), &mq_0)

	var mqr_0 LakalaEntity.WithDrawResultResponse
	json.Unmarshal([]byte(mq_0.Response), &mqr_0)

	if mqr_0.Status != "OK" { //提现发起成功，但是提现失败

		_, _, ere := la.SplitCancel(c, splitOrderNo, splitSeqNo, withdrawalAmount) //撤销分账
		if ere != nil {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_FAIL, ere.Error()) //分账失败
		} else {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_SUCCESS)
		}
		//写入真实的提现失败原因
		if len(mqr_0.Result.ErrorMessage) > 0 {
			tools.Logger.Error("提现发起成功，但是提现失败:mulazim:,admin_id:", adminId, ",提现失败原因:", mqr_0.Result.ErrorMessage)
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_WITHDRAW_FAIL, mqr_0.Result.ErrorMessage) //记录真实的提现失败原因
		}
		return false, "system_error", wds

	}

	// widthdrawRes :="{\"response\":\"{\\\"status\\\":\\\"OK\\\",\\\"result\\\":{\\\"order_no\\\":7090632572021166080,\\\"order_status\\\":1006,\\\"amount\\\":1,\\\"out_order_no\\\":\\\"withdraw_5049_VyOQcWltoPCKHTruPx\\\"}}\",\"sign\":\"MCwCFD4mJ8GQK6bxu7t4cUuP9sS1Sj4MAhR9HSlLFGHJQQD/BDFElvJ2frsrOQ==\"}"
	widthdrawRes, er7 := la.OrderCheck(c, mqr_0.Result.OrderNo) //查询订单状态  查询提现状态
	if er7 != nil {
		//订单查询错误
		tools.Logger.Error("提现订单查询错误", er7)
		return false, "system_error", wds
	}
	tools.Logger.Info("订单状态结果")

	tools.Logger.Info(widthdrawRes)

	var widthdrawResult LakalaEntity.LakalaResult
	json.Unmarshal([]byte(withdrawResult), &widthdrawResult)

	var widthdrawResponse LakalaEntity.WithDrawResultResponse
	json.Unmarshal([]byte(widthdrawResult.Response), &widthdrawResponse)

	if widthdrawResponse.Result.OrderStatus == ORDER_COMPLETE {
		wduMap := make(map[string]interface{})
		wduMap["order_no"] = mqr_0.Result.OrderNo
		wduMap["state"] = 1
		db.Model(models.LakalaWithdraw{}).Where("id=?", withdrawId).Updates(&wduMap)
		la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_WITHDRAW_SUCCESS) //提现成功
	} else {
		tools.Logger.Error("提现发起成功，但是提现失败:mulazim:", 0, ",admin_id:", adminId, ",结果:", withdrawResult)

		//写入真实的提现失败原因
		la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_WITHDRAW_FAIL, tools.ToString(widthdrawResponse.Result.ErrorMessage)) //提现失败 记录原因

		splitCancelError := la.UpdateSplitCancelRecord(c, splitOrderNo, splitSeqNo, serviceId, withdrawalAmount)
		if splitCancelError != nil { //分账撤销失败时发送通知
			la.SendFailMessage(c, 0, adminId, "mulazim提现发起成功，但是提现失败", 1003)
		}
		return false, "system_error", wds
	}

	return true, "", wds

}

func (la LakalaService) AddCashOutLog(db *gorm.DB, restaurantId int, withdrawalAmount int, withdrawId int, bills []int, orderNoFromLakala string) error {

	cashOutTime := carbon.Now("Asia/Shanghai").Carbon2Time()
	//记录提现
	mcl := models.MerchantCashoutLog{
		RestaurantID:   restaurantId,
		CashoutAmount:  fmt.Sprintf("%d", withdrawalAmount),
		CashoutTime:    cashOutTime,
		CashPlatform:   2,
		WithdrawId:     withdrawId,
		PartnerTradeNo: orderNoFromLakala, //拉卡拉订单号
	}
	er10 := db.Create(&mcl).Error
	if er10 != nil {
		tools.Logger.Error("FATAL提现 日志 写入失败:", er10)
		// la.SplitCancel(c,mqr.Result.OrderNo,mqr.Result.SplitSeqNo,withdrawalAmount)
		// la.EnableReCash(restaurantId, 0)
		return errors.New("system_error")
	}
	//发送提现请求
	updateMap := make(map[string]interface{})
	updateMap["payment_type"] = 3 //Mulazim打款方式（1：企业付款到零钱，2：企业付款到银行卡,3:拉卡拉）
	updateMap["withdraw_id"] = withdrawId
	updateMap["payment_time"] = cashOutTime
	updateMap["payment_state"] = 1                    //0未付款，1已付款
	updateMap["partner_trade_no"] = orderNoFromLakala //拉卡拉订单号
	er6 := db.Model(models.Mulazimpaytoresagent{}).Where("id in (?)", bills).Updates(&updateMap).Error

	return er6

}

/***
 * @Author: [rozimamat]
 * @description: 写提现记录
 * @Date: 2023-08-01 12:25:41
 * @param {LakalaService} la
 */
func (la LakalaService) AddWithdrawRecord(c *gin.Context, db *gorm.DB,
	serviceId int,
	split_id int, restaurantId int, withdrawalAmount int,
	toMerNo string, withdrawOutOrderNo string, cardId string,
	splitOrderNo string, splitSeqNo string, bills []int, exts string) (int, error) {

	withdrawDate := carbon.Now("Asia/Shanghai").Format("Y-m-d")

	wd := models.LakalaWithdraw{
		ServiceId:    serviceId,
		SplitId:      split_id,
		ResId:        restaurantId,
		Date:         withdrawDate,
		Amount:       withdrawalAmount,
		MemberNo:     toMerNo,
		OutOrderNo:   withdrawOutOrderNo,
		AccountType:  "1000",
		BankCardId:   cardId,
		OrderName:    withdrawOutOrderNo,
		WithdrawType: "D0",
		Exts:         exts,
	}
	er4 := db.Create(&wd).Error
	if er4 != nil {
		tools.Logger.Error("FATAL提现数据写入失败:", er4)
		//设置正在提现
		// la.EnableReCash(restaurantId, 0)
		la.SplitCancel(c, splitOrderNo, splitSeqNo, withdrawalAmount)
		return 0, errors.New("system_error")
	}
	return wd.ID, nil

}

/***
 * @Author: [rozimamat]
 * @description: 更新分账结果
 * @Date: 2023-08-02 10:23:42
 * @param {LakalaService} la
 */
func (la LakalaService) UpdateSplitResult(c *gin.Context, db *gorm.DB,

	splitResult string, restaurantId int, splitId int, withdrawalAmount int) (string, string, error) {

	// splitResult :="{\"response\":\"{\\\"status\\\":\\\"OK\\\",\\\"result\\\":{\\\"order_no\\\":\\\"7091012366965592064\\\",\\\"split_seq_no\\\":\\\"7091012367053672448\\\",\\\"split_rule_result\\\":[{\\\"member_id\\\":\\\"7078569877996437504\\\",\\\"total_amount\\\":1}],\\\"is_confirm\\\":false}}\",\"sign\":\"MCwCFBQsCW4Y7ccTgsWxnt3j9UnK6AhPAhQ9ugIpXe71f1UT8I5O/TsShxuHRg==\"}"
	// splitResult :="{\"response\":\"{\\\"status\\\":\\\"ERROR\\\",\\\"error_code\\\":\\\"AT000000\\\",\\\"message\\\":\\\"For input string: \\\\\\\"707856987799643750400\\\\\\\"\\\"}\",\"sign\":\"MCwCFG3Qa3uxJQW6WbJMVjDIpT+hBYuKAhRaIIefMOqDcPtFhG+3cyGbLmjB5Q==\"}"
	var mq LakalaEntity.BalanceSplitResult
	json.Unmarshal([]byte(splitResult), &mq)

	var mqr LakalaEntity.BalanceSplitResultResponse
	json.Unmarshal([]byte(mq.Response), &mqr)
	splitUpdateMap := make(map[string]interface{})
	if mqr.Status != "OK" { //分账失败
		// la.EnableReCash(restaurantId, 0)
		tools.Logger.Error("FATAL分账 发起 失败:", splitResult)

		splitUpdateMap["error"] = mqr.Message
		splitUpdateMap["error_code"] = mqr.ErrorCode
		splitUpdateMap["state"] = 2 //分账失败
		er3 := db.Model(models.LakalaSplit{}).Where("id = ?", splitId).Updates(&splitUpdateMap).Error
		if er3 != nil {
			tools.Logger.Error("FATAL分账记录更新失败:", er3)
			return "", "", errors.New("system_error")
		}
		return "", "", errors.New("system_error")
	}

	//分账结果记录下来
	splitUpdateMap["order_no"] = mqr.Result.OrderNo
	splitUpdateMap["split_seq_no"] = mqr.Result.SplitSeqNo
	splitRuleResultByte, _ := json.Marshal(mqr.Result.SplitRuleResult)
	splitUpdateMap["split_rule_result"] = string(splitRuleResultByte)
	er3 := db.Model(models.LakalaSplit{}).Where("id = ?", splitId).Updates(&splitUpdateMap).Error

	return mqr.Result.OrderNo, mqr.Result.SplitSeqNo, er3

}

/***
 * @Author: [rozimamat]
 * @description: 获取拉卡拉商户号
 * @Date: 2023-07-27 18:35:06
 * @param {string} res_id
 */
func (la LakalaService) GetMerNo(res_id string) (bool, string) {
	db := tools.GetDB()
	merMap := make(map[string]interface{})
	db.Table("t_self_sign_merchant_info_archive").
		Where("restaurant_id = ? and lakala_verify_state = ? and type=1 and deleted_at is null", res_id, 2).
		Select("id,member_no").
		Order("id desc").
		Scan(&merMap)
	if merMap["member_no"] != nil && len(tools.ToString(merMap["member_no"])) > 0 {
		return true, tools.ToString(merMap["member_no"])
	}
	return false, "lakala_archive_not_found"
}

/***
 * @Author: [rozimamat]
 * @description: 是否可以提现
 * @Date: 2023-07-27 11:53:14
 * @param {int} restaurantId
 * @param {string} presentationPassword
 * @param {int} withdrawalAmount
 * @param {[]int} bills
 * @param {string} lang
 */
func (la LakalaService) CanCashOut(restaurantId int, withdrawalAmount int, bills []int) (bool, string) {
	db := tools.GetDB()

	//查询是否在提现中  解决 高并发 环境下 数据库速度 跟不上问题
	redisKey := "cash_out_" + tools.ToString(restaurantId) + "_is_cashouting"
	ret := tools.RedisLock(redisKey, 5*60)
	if !ret { //第二次设置要提现的话要抛出异常
		return false, "cashout_in_progress"
	}

	var count int64
	//验证提现参数
	db.Model(&models.Restaurant{}).Where("id = ?", restaurantId).Where("deleted_at is null").Count(&count)
	if count < 1 {
		return false, "merchant_not_found"
	}

	if withdrawalAmount < 1 {
		return false, "cashout_amount_error"
	} else {
		db.Model(&models.Mulazimpaytoresagent{}).Where("id IN ?", bills).Where("payment_state = 0").Count(&count)
		if int(count) != len(bills) {
			return false, "cashout_bill_id_not_found"
		}
	}

	resMap := make(map[string]interface{})
	db.Table("t_restaurant").Where("id = ? and deleted_at is null", restaurantId).Select("id,is_cashouting,cash_per_day").Scan(&resMap)
	if resMap["id"] != nil && tools.ToInt(resMap["is_cashouting"]) == 1 {
		return false, "cashout_in_progress"
	}
	td := tools.Today("Asia/Shanghai").Format("Y-m-d")
	todayAlreadyCashAmount := 0
	//今天已经提现额度
	aCashAmount := make(map[string]interface{})
	db.Model(models.LakalaWithdraw{}).Where("res_id = ? and state = ? and date = ?", restaurantId, 1, td).Select("sum(amount) as amount").Scan(&aCashAmount)
	if aCashAmount["amount"] != nil {
		todayAlreadyCashAmount = tools.ToInt(aCashAmount["amount"])
	}
	dayCashoutLimit := configs.MyApp.DayCashoutLimit //商户每天累计提现额度

	if resMap["cash_per_day"] != nil {
		resCashPerDay := tools.ToInt(resMap["cash_per_day"])
		if resCashPerDay > 0 {
			dayCashoutLimit = resCashPerDay
		}
	}
	//今天累计提现金额 大于 配置中的一天可提现金额 或 累计金额加要提现的金额超过提现金额限制
	if todayAlreadyCashAmount > dayCashoutLimit || (todayAlreadyCashAmount+withdrawalAmount) > dayCashoutLimit {
		allMoney := (todayAlreadyCashAmount + withdrawalAmount)
		dayCashoutLimitRMB := tools.ToPrice(tools.ToFloat64(dayCashoutLimit) / 100)
		todayAlreadyCashAmountRMB := tools.ToPrice(tools.ToFloat64(todayAlreadyCashAmount) / 100)
		withdrawalAmountRMB := tools.ToPrice(tools.ToFloat64(withdrawalAmount) / 100)
		overAmountRMB := tools.ToPrice(tools.ToFloat64((allMoney - dayCashoutLimit)) / 100) //超过金额
		//总提现金额xx 元，已提现 xx 元 ,超过 xx 元
		msg := fmt.Sprintf(la.langUtil.T("cash_per_day"), dayCashoutLimitRMB, todayAlreadyCashAmountRMB, withdrawalAmountRMB, overAmountRMB)

		return false, msg
	}
	//判断平台维护时间
	startTime := carbon.CreateFromTime(configs.MyApp.CashOutStartTime, 0, 0, "Asia/Shanghai")
	endTime := carbon.CreateFromTime(configs.MyApp.CashOutEndTime, 30, 0, "Asia/Shanghai")
	now := carbon.Now("Asia/Shanghai")
	if now.Lt(startTime) || now.Gt(endTime) {
		if la.language == "ug" {
			return false, configs.MyApp.CashOutNotificationUg
		}
		return false, configs.MyApp.CashOutNotificationZh
	}

	//判断是否在区间内
	diifDate := 3 //三天内的记录
	beginDate := now.SubDays(diifDate)
	transDate := beginDate.Format("Y-m-d", "Asia/Shanghai")
	billCount := int64(0)

	db.Model(&models.Mulazimpaytoresagent{}).Where("id IN (?) and trans_date > ?", bills, transDate).Count(&billCount)
	if billCount > 0 {
		return false, "illiegal_bill"
	}
	billMap := make(map[string]interface{})
	db.Model(&models.Mulazimpaytoresagent{}).Where("id IN (?) and payment_state = ? and res_id= ? ", bills, 0, restaurantId).Select("sum(res_income) as res_income").Scan(&billMap)
	if billMap["res_income"] == nil {
		return false, "cashout_record_not_found"
	} else if billMap["res_income"] != nil && tools.ToInt(billMap["res_income"]) != withdrawalAmount {
		return false, "cashout_amount_incorrect"
	}

	return true, ""
}

/***
 * @Author: [rozimamat]
 * @description: 提现状态修改
 * @Date: 2023-07-27 12:43:23
 * @param {int} restaurantId
 * @param {int} is_cashouting
 */
func (la LakalaService) EnableReCash(restaurantId int, is_cashouting int) error {
	db := tools.GetDB()
	er0 := db.Table("t_restaurant").Where("id = ? and deleted_at is null", restaurantId).Update("is_cashouting", is_cashouting).Error
	if er0 != nil {
		//数据库出现错误的情况
		return errors.New("database_出现异常:" + er0.Error())
	}
	redisKey := "cash_out_" + tools.ToString(restaurantId) + "_is_cashouting"
	if is_cashouting == 0 {
		//操作结束后 删除key
		tools.RedisUnlock(redisKey)
	}
	return nil

}

/***
 * @Author: [rozimamat]
 * @description: 获取银行卡列表
 * @Date: 2023-07-28 13:18:24
 * @param {*gin.Context} c
 * @param {string} id
 */
func (la LakalaService) GetBankCards(c *gin.Context, merno string) ([]map[string]interface{}, error) {

	cardMapStr, er2 := la.BankCardCheck(c, merno)

	if er2 != nil {
		return nil, er2
	}

	cardList := make([]map[string]interface{}, 0)
	var mq LakalaEntity.LakalaResult
	json.Unmarshal([]byte(cardMapStr), &mq)

	resMap := make(map[string]interface{})
	json.Unmarshal([]byte(mq.Response), &resMap)

	if resMap["result"] != nil {
		rs, _ := json.Marshal(resMap["result"])

		res1 := make(map[string]interface{})
		json.Unmarshal(rs, &res1)

		rs3, _ := json.Marshal(res1["bank_card_list"])

		json.Unmarshal(rs3, &cardList)

		for k, v := range cardList {
			cardList[k]["bank_logo"] = la.GetbankLogo(tools.ToString(v["bank_name"]))
		}

	}

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Printf("error: %s\n", err)
			}
		}()
		db := tools.GetDB()
		for _, v := range cardList {
			cardCount := int64(0)
			db.Table("t_lakala_bank_cards").Where("member_no = ? and card_id=? and card_no=?", merno, v["card_id"], v["card_no"]).Count(&cardCount)
			if cardCount == 0 {
				bkMap := make(map[string]interface{})
				bkMap["member_no"] = merno
				bkMap["card_no"] = v["card_no"]
				bkMap["card_id"] = v["card_id"]
				bkMap["bank_code"] = v["bank_code"]
				bkMap["bank_name"] = v["bank_name"]
				bkMap["bind_time"] = v["bind_time"]
				bkMap["card_type"] = v["card_type"]
				bkMap["bank_account_prop"] = v["bank_account_prop"]
				bkMap["bind_channel_code"] = v["bind_channel_code"]
				b := tools.ToBool(v["is_settle_card"])
				IsSettleCard := 0
				if b {
					IsSettleCard = 1
				}
				bkMap["is_settle_card"] = IsSettleCard
				bkMap["created_at"] = carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s")
				bkMap["updated_at"] = carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s")
				bkMap["card_type"] = v["card_type"]
				db.Table("t_lakala_bank_cards").Create(&bkMap)
			}
		}

	}()

	return cardList, nil

}

/***
 * @Author: [rozimamat]
 * @description: 银行卡绑定
 * @Date: 2023-07-28 16:53:53
 * @param {*gin.Context} c
 * @param {string} id
 */
func (la LakalaService) BankCardBind(c *gin.Context, id string) (map[string]interface{}, error) {

	er, merno := la.GetMerNo(id)

	if !er {
		return nil, errors.New("data_not_found")
	}

	cardMapStr, er2 := la.BankCardBindApi(c, merno)

	if er2 != nil {
		return nil, er2
	}

	cardList := make(map[string]interface{}, 0)
	var mq LakalaEntity.LakalaResult
	json.Unmarshal([]byte(cardMapStr), &mq)

	resMap := make(map[string]interface{})
	json.Unmarshal([]byte(mq.Response), &resMap)

	if resMap["result"] != nil {
		rs, _ := json.Marshal(resMap["result"])

		json.Unmarshal(rs, &cardList)

	}
	return cardList, nil

}

/***
 * @Author: [rozimamat]
 * @description: 提现明细
 * @Date: 2023-08-02 10:12:04
 * @param {*gin.Context} c
 * @param {string} id
 */
func (la LakalaService) WithdrawDetail(c *gin.Context, id int, cash_platform string) (map[string]interface{}, error) {

	cp := 1
	if len(cash_platform) > 0 {
		cp = tools.ToInt(cash_platform)
	}

	db := tools.GetDB()

	log := make(map[string]interface{}, 0)

	if cp == 1 {

		fields := "t_merchant_cashout_log.wechat_payment_no, t_merchant_cashout_log.id,"
		fields += "t_merchant_cashout_log.mobile, t_merchant_cashout_log.cashout_amount, t_merchant_cashout_log.cashout_time,"
		fields += "1 as cash_platform,t_merchant_cashout_log.created_at,5 as state"

		db.Table("t_merchant_cashout_log").
			Select(fields).
			Where("t_merchant_cashout_log.id=?", id).
			Where("deleted_at is null").Scan(&log)

		log["bank_name"] = ""
		log["bank_logo"] = ""
		log["card_no"] = ""

		detailArr := make([]map[string]interface{}, 0)
		if log["wechat_payment_no"] != nil {
			db.Table("t_mulazimpaytoresagent").Where("payment_no = ?", log["wechat_payment_no"]).Select("id,res_income,trans_date").Scan(&detailArr)
			for k, v := range detailArr {
				detailArr[k]["trans_date"] = carbon.Parse(tools.ToString(v["trans_date"]), "Asia/Shanghai").Format("Y-m-d")
			}
		}
		log["details"] = detailArr
		emptyLogs := make([]map[string]interface{}, 0)
		log["logs"] = emptyLogs

	} else {

		fields := "t_lakala_withdraw_service.id,t_lakala_withdraw_service.amount as cashout_amount,t_lakala_withdraw_service.logs,"
		fields += "t_lakala_withdraw_service.created_at as cashout_time,t_lakala_withdraw_service.out_trade_no,"
		fields += "2 as cash_platform ,t_lakala_withdraw_service.created_at as cash_request_time,t_lakala_withdraw_service.state,t_lakala_withdraw_service.detail_json"

		db.Table("t_lakala_withdraw_service").
			Joins("left join t_merchant_cashout_log on t_lakala_withdraw_service.id=t_merchant_cashout_log.withdraw_id").
			Where("t_lakala_withdraw_service.id=?", id).
			Select(fields).Scan(&log)

		if log["logs"] != nil {

			logsStr := tools.ToString(log["logs"])
			emptyLogs := make([]map[string]interface{}, 0)
			json.Unmarshal([]byte(logsStr), &emptyLogs)
			for k, v := range emptyLogs {

				st := tools.ToInt(v["state"])
				showStateName := ""
				showState := 0
				switch st {
				case 1, 2, 3:
					showStateName = la.langUtil.T("lakala_withdraw_state_processing")
					showState = 1
				case 5:
					showStateName = la.langUtil.T("lakala_withdraw_state_success")
					showState = 2
				case 4, 6, 7, 8:
					showStateName = la.langUtil.T("lakala_withdraw_state_fail")
					showState = 3
				}
				stateNames := la.langUtil.TArr("lakala_withdraw_states")
				stateNameReal := ""
				stateError := tools.ToString(v["error"])
				if st == 6 && len(stateError) > 0 && !strings.Contains(stateError, "订单状态") { //以前的只记录订单状态台的记录 不要显示
					stateNameReal = stateError
				} else {
					stateNameReal = stateNames[st]

				}
				emptyLogs[k]["state_name"] = stateNameReal
				emptyLogs[k]["show_state_name"] = showStateName
				emptyLogs[k]["show_state"] = showState
			}
			log["logs"] = emptyLogs
		}
	}

	if log["detail_json"] != nil {

		js := tools.ToString(log["detail_json"])

		detailMap, _ := tools.StringToMap(js)

		if detailMap["bank_name"] != nil {

			log["bank_name"] = detailMap["bank_name"]
			log["card_no"] = detailMap["card_no"]
			log["bank_logo"] = la.GetbankLogo(tools.ToString(detailMap["bank_name"]))

		}

		if detailMap["cashout_details"] != nil {
			list := detailMap["cashout_details"]

			detailArr := make([]map[string]interface{}, 0)

			bb, _ := json.Marshal(list)
			json.Unmarshal(bb, &detailArr)

			for k, v := range detailArr {
				detailArr[k]["trans_date"] = carbon.Parse(tools.ToString(v["trans_date"]), "Asia/Shanghai").Format("Y-m-d")
			}
			log["details"] = detailArr
		}

		delete(log, "detail_json")

	}

	log["cashout_amount"] = tools.ToInt(log["cashout_amount"])
	log["cashout_time"] = carbon.Parse(tools.ToString(log["cashout_time"]), "Asia/Shanghai").Format("Y-m-d H:i:s")
	log["cash_request_time"] = carbon.Parse(tools.ToString(log["cash_request_time"]), "Asia/Shanghai").Format("Y-m-d H:i:s")

	return log, nil

}

/***
 * @Author: [rozimamat]
 * @description:
 * @Date: 2023-08-04 11:05:51
 * @param {string} bank_name
 */
func (la LakalaService) GetbankLogo(bank_name string) string {
	db := tools.GetDB()
	bankMap := make(map[string]interface{})
	//bank_name
	if strings.Contains(bank_name, "信用") || strings.Contains(bank_name, "合作") || strings.Contains(bank_name, "联社") { //都按信用社的走
		bank_name = "信用社"
	}
	db.Table("b_self_sign_bank").Where("name_zh like ?", "%"+bank_name+"%").Select("logo").Scan(&bankMap)
	if bankMap["logo"] != nil {
		return configs.MyApp.CdnUrl + tools.ToString(bankMap["logo"])
	}
	return ""
}

/***
 * @Author: [rozimamat]
 * @description: 会员修改手机号
 * @Date: 2023-08-02 12:09:25
 * @param {*gin.Context} c
 * @param {string} member_no
 * @param {string} mobile
 */
func (la LakalaService) MemberMobileModify(c *gin.Context, member_no string, mobile string) (string, error) {

	param := make(map[string]interface{})
	param["member_no"] = member_no
	param["mobile"] = mobile //默认值

	api := "member.mobile.modify.apply"

	result, err := la.Send(c, param, api)
	if err != nil {
		return "", err
	}
	return result, nil

}

/***
 * @Author: [rozimamat]
 * @description: 提现结果通知
 * @Date: 2023-08-02 12:17:08
 * @param {*gin.Context} c
 * @param {LakalaEntity.LakalaNotify} params
 */
func (la LakalaService) WithdrawNotify(c *gin.Context, params LakalaEntity.LakalaNotify) error {

	// db :=tools.GetDB()

	var ev LakalaEntity.Event
	json.Unmarshal([]byte(params.Event), &ev)
	prm, _ := json.Marshal(params)
	tools.Logger.Info("拉卡拉 提现  开始")
	tools.Logger.Info(string(prm))
	tools.Logger.Info("拉卡拉 提现  结束")

	return nil

}

// ArchiveZipFile
//
//	@Description: 解压ZIP文件（对账文件）
//	@author: Alimjan
//	@Time: 2023-08-12 10:47:36
//	@receiver la LakalaService
//	@param billData string
func (la LakalaService) ArchiveZipFile(billData string) {
	// 打开ZIP文件
	zipFile, err := zip.OpenReader(configs.MyApp.ArchivedFilePath + "/" + billData + ".zip")
	if err != nil {
		tools.Logger.Info("Open ZIP file error:", err)
		return
	}
	defer zipFile.Close()

	dirName := configs.MyApp.ArchivedFilePath + "/" + billData

	// 检查目录是否已经存在
	if _, err := os.Stat(dirName); os.IsNotExist(err) {
		// 目录不存在，创建目录
		err := os.Mkdir(dirName, 0755)
		if err != nil {
			tools.Logger.Error("Create directory error:", err)
			return
		}

	}

	// 遍历ZIP文件中的文件
	for _, file := range zipFile.File {
		// 打开文件
		fileReader, err := file.Open()
		if err != nil {
			tools.Logger.Error("Open file in ZIP error:", err)
			return
		}
		defer fileReader.Close()

		// 创建本地文件
		localFile, err := os.Create(configs.MyApp.ArchivedFilePath + "/" + billData + "/" + file.Name)
		if err != nil {
			tools.Logger.Error("Create local file error:", err)
			return
		}

		// 将ZIP文件中的文件内容复制到本地文件
		_, err = io.Copy(localFile, fileReader)
		if err != nil {
			tools.Logger.Error("Extract file error:", err)
			return
		}
	}
	tools.Logger.Info("ZIP file extracted successfully.")
}

// CSVOrderCheck
//
//	@Description: 拉卡拉对账Order csv
//	@author: Alimjan
//	@Time: 2023-08-03 10:47:03
//	@receiver la LakalaService
//	@param context *gin.Context
//	@param billDate string
func (la LakalaService) CSVOrderCheck(context *gin.Context, billDate string) {
	filePath := configs.MyApp.ArchivedFilePath + "/" + billDate + "/" + "order_" + strings.ReplaceAll(billDate, "-", "") + ".csv"

	file, err := os.Open(filePath)
	if err != nil {
		tools.Logger.Error("无法打开CSV文件：", err)
		return
	}

	// 创建一个CSV Reader
	reader := csv.NewReader(file)

	// 读取所有行
	rows, err := reader.ReadAll()
	if err != nil {
		tools.Logger.Error("无法读取CSV文件：", err)
		return
	}
	resultErr := []string{}
	// 打印每一行的内容
	for i, row := range rows {

		if i == 0 {
			continue
		}
		if strings.HasPrefix(row[2], "DEV_") {
			continue
		}
		//  是否是消费订单
		//名称	取值	类型

		//充值订单	0	整型
		//提现	1	整型
		//消费订单	2	整型
		//平台转账	3	整型
		//余额分账	4	整型
		//转账	5	整型

		orderType := tools.ToInt(row[3])
		orderState := tools.ToInt(row[12])
		if orderType == OrderTypeConsumptionOrder {
			if orderState == OrderStatusRefunded {
				//失败订单
				if s, err := la.CheckRefundOrder(row); err != nil {
					resultErr = append(resultErr, s)
				}
			} else if orderState == OrderStatusCompleted {
				//成功订单
				if s, err := la.CheckCompleteOrder(row); err != nil {
					resultErr = append(resultErr, s)
				}
			} else {
				resultErr = append(resultErr, "没有处理的订单状态:"+strings.Join(row, ","))
			}
		} else if orderType == OrderTypeRechargeOrder {
			//  充值
			if s, err := la.CheckRecharge(row); err != nil {
				resultErr = append(resultErr, s)
			}
		} else if orderType == OrderTypeBalanceAccount {
			//  分账
			if s, err := la.CheckSplit(row); err != nil {
				resultErr = append(resultErr, s)
			}
		} else if orderType == OrderTypeWithdraw {
			//  提现
			if s, err := la.CheckWithdraw(row); err != nil {
				resultErr = append(resultErr, s)
			}
		} else {
			resultErr = append(resultErr, "订单类型错误:"+strings.Join(row, ","))
		}
	}

	tools.Logger.Error("拉卡拉订单对账结束", "错误数：", len(resultErr))
	tools.Logger.Error("拉卡拉订单对账结束", strings.Join(resultErr, "\n"))
	//fmt.Println(strings.Join(resultErr, "\n"))
	if len(resultErr) != 0 {
		tools.Logger.Error(strings.Join(resultErr, "\n"))
		tools.AliDeveloperDingdingMsg("order 对账失败")
		tools.AliDeveloperDingdingMsg(strings.Join(resultErr, "\n"))

	}

}

// CheckRefundOrder
//
//	@Description: 对账退回订单，用户下的订单
//	@author: Alimjan
//	@Time: 2023-08-05 17:20:31
//	@receiver la LakalaService
//	@param row []string
//	@return string
//	@return error
func (la LakalaService) CheckRefundOrder(row []string) (string, error) {
	db := tools.Db
	//  退款订单
	var count int64
	type PayLakala struct {
		Id      int `gorm:"column:id;primary_key;AUTO_INCREMENT"`
		OrderId int `gorm:"column:order_id"`
	}
	var payLakala []PayLakala
	db.Table("t_pay_lakala").Where("order_no = ?", row[1]).
		Where("order_status = ? or order_status = ?", OrderStatusRefunded, OrderStatusCompleted).Scan(&payLakala)
	if len(payLakala) != 1 {
		return "退款订单对账失败 t_pay_lakala:" + strings.Join(row, ","), errors.New("对账失败")
	}

	db.Table("t_pay_lakala_refund").Where("order_no = ?", row[1]).
		Where("order_status = ?", OrderStatusRefunded).Count(&count)

	if count != 1 {
		//优惠券退款对账
		db.Table("t_admin_market_refund_log").Where("order_no = ?", row[1]).
			Where("refund_status = ?", RefundStatusSuccess).Count(&count)
		if count != 1 {
			return "退款订单对账失败 t_pay_lakala_refund，t_admin_market_refund_log:" + strings.Join(row, ","), errors.New("对账失败")
		}
	}

	db.Table("t_order_today").Where("id = ?", payLakala[0].OrderId).
		Where("state > 7").Count(&count)
	if count != 1 {


		db.Table("t_order").Where("id = ?", payLakala[0].OrderId).
			Where("state > 7").Count(&count)
		if count != 1 {
			//判断重复支付情况
			otherSuccesedPayed := PayLakala{}
			db.Table("t_pay_lakala").
				Where("object_type = ? ", models.PAY_LAKALA_OBJECT_TYPE_ORDER).
				Where("order_id = ? ",payLakala[0].OrderId).
				Where("id not in (?)",payLakala[0].Id).
				Scan(&otherSuccesedPayed)
			if otherSuccesedPayed.Id==0 {
				return "退款订单对账失败 t_order,t_order_today:" + strings.Join(row, ","), errors.New("对账失败")
			}

		}
	}
	return "", nil
}

// CheckCompleteOrder
//
//	@Description: 完成订单查询
//	@author: Alimjan
//	@Time: 2023-08-05 17:23:55
//	@receiver la LakalaService
//	@param row []string
//	@return string
//	@return error
func (la LakalaService) CheckCompleteOrder(row []string) (string, error) {
	db := tools.Db
	//  退款订单
	var count int64
	type PayLakala struct {
		Id      int `gorm:"column:id;primary_key;AUTO_INCREMENT"`
		OrderId int `gorm:"column:order_id"`
		ObjectType string `gorm:"column:object_type"`
	}
	type PayLakalaAgent struct {
		Id       int `gorm:"column:id;primary_key;AUTO_INCREMENT"`
		ChargeId int `gorm:"column:charge_id"`
	}
	var payLakala []PayLakala
	var payAgentLakala []PayLakalaAgent
	db.Table("t_pay_lakala").Where("order_no = ?", row[1]).
		Where("order_status = ? or order_status = ?", OrderStatusRefunded, OrderStatusCompleted).Scan(&payLakala)
	db.Table("t_pay_agent_lakala").Where("order_no = ?", row[1]).
		Where("order_status = ? or order_status = ?", OrderStatusRefunded, OrderStatusCompleted).Scan(&payAgentLakala)
	if len(payLakala) == 0 && len(payAgentLakala) == 0 {
		return "退款订单对账失败 t_pay_lakala:" + strings.Join(row, ","), errors.New("对账失败")
	}

	if len(payLakala) > 0 {
		orderId := payLakala[0].OrderId
		if payLakala[0].ObjectType == PayLakalaOrderTypePriceMarkup{
			db.Table("t_price_markup_food").Where("id = ?", orderId).
				Where("state > 1 and payed =1").Count(&count)
			if count!=1 {
				return "成功订单加价支付对账失败 t_price_markup_food" + strings.Join(row, ","), errors.New("对账失败")
			}
			return "", nil
		}


		db.Table("t_order_today").Where("id = ?", orderId).
			Where("state > 2 and state < 8 and pay_platform = 1").Count(&count)
		if count != 1 {
			//优惠券充值对账
			db.Table("t_order").Where("id = ?", orderId).
				Where("state > 2 and state < 8 and pay_platform = 1").Count(&count)
			if count != 1 {
				//判断过夜退款情况
				//select created_at
				//from t_pay_lakala_refund
				//where pay_lakala_id in (select id from t_pay_lakala where order_id = 9542381);
				var mapResult map[string]interface{}
				db.Raw("select created_at from t_pay_lakala_refund where pay_lakala_id in (select id from t_pay_lakala where order_id = ?)", orderId).First(&mapResult)
				//判断是否是过夜退款
				//判断是否是mapResult  null 或者是否存在created_at
				if mapResult!=nil {
					if _,ok := mapResult["created_at"];ok{
						//获取time 类型的created_at
						createdAt := mapResult["created_at"].(time.Time)
						//判断时间是否是今日凌晨00：:00：00 之后
						if carbon.Time2Carbon(createdAt).Gt(carbon.Now("Asia/Shanghai").StartOfDay()) {
							return "", nil
						}
					}
				}
				return "成功订单对账失败 t_order,t_order_today:" + strings.Join(row, ","), errors.New("对账失败")
			}
		}
	} else if len(payAgentLakala) > 0 {
		chargeId := payAgentLakala[0].ChargeId
		db.Table("t_admin_recharge_log").Where("id = ?", chargeId).
			Where("payed = 1 and platform = 2").Count(&count)
		if count != 1 {
			db.Table("t_admin_packet_recharge_log").Where("id = ?", chargeId).
				Where("payed = 1 and platform = 2 and type = 11").Count(&count)
			if count != 1 {
				return "成功订单对账失败 t_admin_recharge_log,t_admin_packet_recharge_log:" + strings.Join(row, ","), errors.New("对账失败")
			}
		}
	}

	return "", nil
}

// CheckRecharge
//
//	@Description: 拉卡拉充值对账
//	@author: Alimjan
//	@Time: 2023-08-05 17:49:21
//	@receiver la LakalaService
//	@param row []string
//	@return string
//	@return error
func (la LakalaService) CheckRecharge(row []string) (string, error) {
	db := tools.Db
	var count int64
	db.Table("t_lakala_deposit").
		Where("out_order_no = ?", row[2]).
		Where("order_status = ?", row[12]).
		Count(&count)
	if count != 1 {
		return "充值订单对账失败 t_lakala_deposit:" + strings.Join(row, ","), errors.New("对账失败")
	}

	type PayLakala struct {
		Id      int `gorm:"column:id;primary_key;AUTO_INCREMENT"`
		OrderId int `gorm:"column:order_id"`
		State   int `gorm:"column:state"`
	}
	var payLakala []PayLakala
	db.Table("t_lakala_withdraw_service").
		Where("opt_type = ?", "mulazim").
		Where("type = 2").
		Where("out_trade_no = ?", row[2]).Scan(&payLakala)
	if len(payLakala) != 1 {
		return "充值订单对账失败 t_lakala_withdraw_service:" + strings.Join(row, ","), errors.New("对账失败")
	}
	if row[12] == fmt.Sprintf("%d", OrderStatusCompleted) {
		if payLakala[0].State != 22 {
			return "充值订单对账失败 ,状态错误 t_lakala_withdraw_service:" + strings.Join(row, ","), errors.New("对账失败")
		}
	}
	return "", nil
}

// CheckSplit
//
//	@Description: 对账分账
//	@author: Alimjan
//	@Time: 2023-08-05 17:51:28
//	@receiver la LakalaService
//	@param row []string
//	@return string
//	@return error
func (la LakalaService) CheckSplit(row []string) (string, error) {
	db := tools.Db
	var count int64
	type Split struct {
		Id         int    `gorm:"column:id;primary_key;AUTO_INCREMENT"`
		ServiceId  int    `gorm:"column:service_id"`
		OutTradeNo string `gorm:"column:out_order_no"`
	}
	var splits []Split
	db.Table("t_lakala_split").
		Where("out_order_no = ?", row[2]).
		Scan(&splits)
	if len(splits) != 1 {
		return "分账订单对账失败 t_lakala_split:" + strings.Join(row, ","), errors.New("对账失败")
	}
	if row[12] == fmt.Sprintf("%d", OrderStatusCompleted) {
		db.Table("t_lakala_withdraw_service").Where("id = ?", splits[0].ServiceId).
			Where("state = 3 and type = 3").Where("opt_type = ?", "mulazim").Count(&count)
		if count == 1 {
			//对账成功
		} else {
			db.Table("t_lakala_withdraw_service").Where("id = ?", splits[0].ServiceId).
				Where("state = 5").Count(&count)
			if count != 1 {
				return "分账订单对账失败，状态错误 t_lakala_withdraw_service:" + strings.Join(row, ","), errors.New("对账失败")
			}
		}
	}
	if row[12] == fmt.Sprintf("%d", OrderStatusRefunded) {
		db.Table("t_lakala_withdraw_service").Where("id = ?", splits[0].ServiceId).
			Where("state = 7").Count(&count)
		if count != 1 {
			return "分账订单对账失败,分账没有被撤回 t_lakala_withdraw_service:" + strings.Join(row, ","), errors.New("对账失败")
		}
	}
	return "", nil
}

// CheckWithdraw
//
//	@Description: 对账提现订单对账失败 t_lakala_split
//	@author: Alimjan
//	@Time: 2023-08-05 18:03:14
//	@receiver la LakalaService
//	@param row []string
//	@return string
//	@return error
func (la LakalaService) CheckWithdraw(row []string) (string, error) {
	db := tools.Db
	var count int64
	type WithDraw struct {
		Id         int    `gorm:"column:id;primary_key;AUTO_INCREMENT"`
		ServiceId  int    `gorm:"column:service_id"`
		OutTradeNo string `gorm:"column:out_order_no"`
		State      int    `gorm:"column:state"`
	}
	var withDraws []WithDraw
	db.Table("t_lakala_withdraw").
		Where("out_order_no = ?", row[2]).
		Scan(&withDraws)
	if len(withDraws) != 1 {
		return "提现订单对账失败 t_lakala_split:" + strings.Join(row, ","), errors.New("对账失败")
	}
	if row[12] == fmt.Sprintf("%d", OrderStatusCompleted) {
		db.Table("t_lakala_withdraw_service").Where("id = ?", withDraws[0].ServiceId).
			Where("state = 5").Count(&count)
		if count != 1 {
			return "提现订单对账失败，状态错误 t_lakala_withdraw_service:" + strings.Join(row, ","), errors.New("对账失败")
		}
	}
	if row[12] == fmt.Sprintf("%d", OrderStatusRefunded) {
		db.Table("t_lakala_withdraw_service").Where("id = ?", withDraws[0].ServiceId).
			Where("state = 7").Count(&count)
		if count != 1 {
			return "提现订单对账失败,分账没有被撤回 t_lakala_withdraw_service:" + strings.Join(row, ","), errors.New("对账失败")
		}
	}
	return "", nil
}

// CSVDetailCheck
//
//		@Description: 对账明细
//		@author: Alim Kirem
//		@Time: 2023-08-07
//		@receiver la LakalaService
//	 @param billDate string 处理日期
//		@return void
func (la LakalaService) CSVDetailCheck(c *gin.Context, billDate string) {
	filePath := configs.MyApp.ArchivedFilePath + "/" + billDate + "/" + "detail_" + strings.ReplaceAll(billDate, "-", "") + ".csv"
	file, err := os.Open(filePath)
	if err != nil {
		tools.Logger.Error("无法打开CSV文件：", err)
		return
	}

	// 创建一个CSV Reader
	reader := csv.NewReader(file)
	index := 0
	db := tools.GetDB()
	count := int64(0)
	resultErr := []string{}
	for {
		index++
		record, err := reader.Read()

		if err == io.EOF {
			break
		}
		if err != nil {
			tools.Logger.Error("无法读取CSV文件：", err)
			return
		}
		if index == 1 {
			// 首行不用检查
			continue
		}
		if strings.HasPrefix(record[3], "DEV_") {
			continue
		}
		switch tools.ToInt(record[1]) {
		case DetailTypePay:
			if strings.Contains(record[3], "D2") {
				db.Table("t_lakala_deposit").Where("order_no = ?", record[2]).Count(&count)
				if count == 0 {
					resultErr = append(resultErr, "支付订单对账失败 t_lakala_deposit: "+strings.Join(record, ","))
				}
			} else if strings.HasPrefix(record[3], "2") {
				db.Table("t_pay_lakala").Where("order_no = ?", record[2]).Count(&count)
				if count == 0 {
					db.Table("t_pay_agent_lakala").Where("order_no = ?", record[2]).Count(&count)
				}
				if count == 0 {
					resultErr = append(resultErr, "支付订单对账失败 t_pay_lakala: "+strings.Join(record, ","))
				}
			}
		case DetailTypeRefund:
			db.Table("t_pay_lakala_refund").Where("order_no = ?", record[2]).Count(&count)
			if count == 0 {
				//优惠券退款对账
				db.Table("t_admin_market_refund_log").Where("order_no = ?", record[2]).
					Where("refund_status = ?", RefundStatusSuccess).Count(&count)
				if count == 0 {
					resultErr = append(resultErr, "退款订单对账失败 t_pay_lakala_refund，t_admin_market_refund_log: "+strings.Join(record, ","))
				}

			}
		case DetailTypeWithdraw:
			db.Table("t_lakala_withdraw").Where("out_order_no = ?", record[3]).Count(&count)
			if count == 0 {
				resultErr = append(resultErr, "提现订单对账失败 t_lakala_withdraw: "+strings.Join(record, ","))
			}
		default:
			resultErr = append(resultErr, "核对detail账单存在无效记录"+strings.Join(record, ","))
		}
	}
	tools.Logger.Error("拉卡拉订单明细对账结束", "错误数：", len(resultErr))
	//fmt.Println(strings.Join(resultErr, "\n"))
	if len(resultErr) != 0 {
		tools.Logger.Error(strings.Join(resultErr, "\n"))
		tools.AliDeveloperDingdingMsg("order 对账失败")
		tools.AliDeveloperDingdingMsg(strings.Join(resultErr, "\n"))

	}
}

// 名称	取值	类型
// 充值订单	0	整型
// 提现	1	整型
// 消费订单	2	整型
// 平台转账	3	整型
// 余额分账	4	整型
// 转账	5	整型
const (
	OrderTypeRechargeOrder    = 0
	OrderTypeWithdraw         = 1
	OrderTypeConsumptionOrder = 2
	OrderTypePlatformTransfer = 3
	OrderTypeBalanceAccount   = 4
	OrderTypeTransfer         = 5
)

// 未支付	1001	整型
// 部分支付	1002	整型
// 进行中	1003	整型
// 已付款	1004	整型
// 交易完成	1006	整型
// 部分确权	1007	整型
// 关闭（失败）	2002	整型
// 已退款	2003	整型
const (
	OrderStatusUnpaid             = 1001
	OrderStatusPartiallyPaid      = 1002
	OrderStatusInProgress         = 1003
	OrderStatusPaid               = 1004
	OrderStatusCompleted          = 1006
	OrderStatusPartiallyConfirmed = 1007
	OrderStatusClosed             = 2002
	OrderStatusRefunded           = 2003
	RefundStatusSuccess           = 1004 //已退款
)

const (
	DetailTypePay      = 1 // 支付
	DetailTypeRefund   = 2 // 退款
	DetailTypeWithdraw = 3 // 手动提现
)
const (
	PayLakalaOrderTypeOrder      = "order" // 支付
	PayLakalaOrderTypeLotterOrder   = "lottery_order" // 退款
	PayLakalaOrderTypePriceMarkup = "price_markup" // 手动提现
)
/***
 * @Author: [rozimamat]
 * @description:
 * @Date: 2023-08-07 11:28:24
 * @param {string} prefix
 */
func (la LakalaService) GenerateOutOrderNo(c context.Context, prefix string) string {
	return tools.GenerateLakalaOutOrderNo(prefix)
}

/***
 * @Author: [rozimamat]
 * @description: 归档 因为错误原因 不能归档的数据
 * @Date: 2023-08-11 15:34:31
 */
func (la *LakalaService) ArchiveErrorItems() {

	db := tools.GetDB()

	lkMap := make([]map[string]interface{}, 0)
	fields := "t_self_sign_merchant_info.restaurant_id,t_self_sign_merchant_info.member_no"
	db.Table("t_self_sign_merchant_info").
		Joins("LEFT JOIN t_self_sign_merchant_info_archive ON t_self_sign_merchant_info.member_no = t_self_sign_merchant_info_archive.member_no").
		Where("t_self_sign_merchant_info.lakala_verify_state = 2 AND t_self_sign_merchant_info_archive.id IS NULL").
		Select(fields).
		Group("t_self_sign_merchant_info.member_no").
		Scan(&lkMap)

	for k, v := range lkMap {
		resId := tools.ToString(v["restaurant_id"])
		member_no := tools.ToString(v["member_no"])
		fmt.Println(len(lkMap), k, resId, member_no)
		la.ArchiveData(resId, member_no)
	}
}

/***
 * @Author: [rozimamat]
 * @description: 手续费查询
 * @Date: 2023-09-02 15:34:31
 */
func (la LakalaService) FeeList(c *gin.Context, start_time string, end_time string, start_index int, end_index int) (string, error) {

	//文档地址  http://*************:6524/docs/qzt/qzt-1che8ik1p2jun

	param := make(map[string]interface{})
	param["member_no"] = configs.MyApp.LakalaConfig.SystemMerNo
	param["start_time"] = start_time
	param["end_time"] = end_time
	param["start_index"] = start_index
	param["end_index"] = end_index

	api := "account.fee.list"

	tools.Logger.Info("拉卡拉 手续费查询 发送参数:api:", api)
	tools.Logger.Info(param)
	result, err := la.Send(c, param, api)
	if err != nil {
		tools.Logger.Error("拉卡拉 手续费查询 失败:", err.Error())
		return "", err
	}
	tools.Logger.Info(result)
	return result, nil

}

/***
 * @Author: [rozimamat]
 * @description: 手续费欠费查询
 * @Date: 2023-09-02 15:34:31
 */
func (la LakalaService) FeeUNcollect(c *gin.Context) (string, error) {

	//文档地址  http://*************:6524/docs/qzt//37

	param := make(map[string]interface{})
	param["member_no"] = configs.MyApp.LakalaConfig.SystemMerNo

	api := "account.fee.uncollect.query"

	tools.Logger.Info("拉卡拉 手续费欠费查询 发送参数:api:", api)
	tools.Logger.Info(param)
	result, err := la.Send(c, param, api)
	if err != nil {
		tools.Logger.Error("拉卡拉 手续费欠费查询 失败:", err.Error())
		return "", err
	}
	tools.Logger.Info(result)
	return result, nil

}

/***
 * @Author: [转到余额]
 * @description: 手续费欠费查询
 * @Date: 2023-09-04 15:34:31
 */
func (la LakalaService) ToBalance(c *gin.Context, admin_type string, withdrawalAmount int, adminId int, optId int, memberNo string) (bool, string, models.LakalaWithdrawService) {

	tools.Logger.Info("佣金账户转到余额开始 adminId:", adminId, ",金额:", withdrawalAmount, "分钱")
	db := tools.GetDB()
	var ww models.LakalaWithdrawService
	//检查佣金余额
	res, er1 := la.AccountBalanceQuery(c, configs.MyApp.LakalaConfig.SystemMerNo, "S005")
	if er1 != nil {

		tools.Logger.Error("佣金账户余额查询失败", er1)
		return false, "system_error", ww
	}

	var mq LakalaEntity.LakalaResult
	json.Unmarshal([]byte(res), &mq)

	var balanceResult LakalaEntity.BalanceResultResponse
	json.Unmarshal([]byte(mq.Response), &balanceResult)
	//提现金额大于 佣金账户里面的金额时 要发送短信提醒
	if balanceResult.Status == "OK" && (balanceResult.Result.AvailableAmount < configs.MyApp.LakalaConfig.SystemMinReserveAmount || balanceResult.Result.AvailableAmount < withdrawalAmount) {

		tools.Logger.Error("佣金账户余额余额不足:", res)
		tools.Logger.Error("佣金账户余额余额 可用余额:" + tools.ToPrice(tools.ToFloat64(balanceResult.Result.AvailableAmount)/100) + "元,最低储备:" + tools.ToPrice(tools.ToFloat64(configs.MyApp.LakalaConfig.SystemMinReserveAmount)/100) + "元")
		la.SendFailMessage(c, 0, adminId, "系统佣金账户余额不足", 1007)
		return false, "system_maintaining_in_progress", ww

	}

	outTradeNo := la.GenerateOutOrderNo(c, "W")

	var bills []int
	wds, er0 := la.AddServiceRecord(c, db, 3, admin_type, adminId, optId, outTradeNo, memberNo, "", withdrawalAmount, "转到余额", 1, bills)

	if er0 != nil {
		tools.Logger.Error("提现流水 数据写入失败:", er0)

		return false, "system_error", wds
	}
	serviceId := wds.ID

	outOrderNo := la.GenerateOutOrderNo(c, "S")

	desc := "split_" + "_amt_" + tools.ToString(withdrawalAmount) //分账备注
	//写入分账记录
	sp, er1 := la.AddSplitRecord(c, db, serviceId, 0, memberNo, withdrawalAmount, desc, outOrderNo)
	// sp,er1:=la.AddSplitRecord(db,serviceId,restaurantId,memberNo,withdrawalAmount,desc)
	if er1 != nil {
		tools.Logger.Error("分账数据写入失败:", er1)
		return false, "system_error", wds
	}
	la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_WAITING) //带分账
	// //分账给商户
	splitResult, er2 := la.SplitBalance(c, memberNo, withdrawalAmount, desc, outOrderNo) //分账
	if er2 != nil {
		tools.Logger.Error("分账失败:", er2)

		return false, "system_error", wds
	}

	splitId := sp.ID

	//更新分账数据
	splitOrderNo, splitSeqNo, er3 := la.UpdateSplitResult(c, db, splitResult, 0, splitId, withdrawalAmount)
	// er3,splitOrderNo,splitSeqNo :=la.UpdateSplitResult(c,db,splitResult,restaurantId,splitId,withdrawalAmount)
	if er3 != nil {
		tools.Logger.Error("分账记录更新失败:", er3)

		//撤销分账
		_, _, ere := la.SplitCancel(c, splitOrderNo, splitSeqNo, withdrawalAmount)
		if ere != nil {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_FAIL, ere.Error()) //分账失败
		} else {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_SUCCESS) //分账成功
		}
		return false, "system_error", wds
	}

	spRes, er3_1 := la.OrderCheck(c, splitOrderNo) //查询订单状态  查询提现状态
	// spRes :="{\"response\":\"{\\\"status\\\":\\\"OK\\\",\\\"result\\\":{\\\"order_no\\\":7090288101941235712,\\\"order_status\\\":2003,\\\"amount\\\":1,\\\"out_order_no\\\":\\\"sp_5049_mGcsKBFoDmwZCehRsXfwvjpA\\\"}}\",\"sign\":\"MCwCFEugQgp3LVfspOG+cASPEfETKlESAhRrgHVUSdSCXMzn6XpreNwBlC1W+w==\"}"

	if er3_1 != nil {
		//分账查询失败
		tools.Logger.Error("分账查询失败:", er3_1)
		//撤销分账
		_, _, ere := la.SplitCancel(c, splitOrderNo, splitSeqNo, withdrawalAmount)
		if ere != nil {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_FAIL, ere.Error()) //分账失败
		} else {
			la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_SUCCESS) //分账成功
		}

		return false, "system_error", wds
	}

	var spResult LakalaEntity.LakalaResult
	json.Unmarshal([]byte(spRes), &spResult)

	var spResponse LakalaEntity.WithDrawResultResponse
	json.Unmarshal([]byte(spResult.Response), &spResponse)

	if spResponse.Result.OrderStatus == ORDER_COMPLETE {
		splitMap := make(map[string]interface{})
		splitMap["state"] = 1 //分账成功
		er3_2 := db.Model(models.LakalaSplit{}).Where("id = ?", splitId).Updates(&splitMap).Error

		if er3_2 != nil {
			//分账查询失败
			tools.Logger.Error("分账 结果更新 失败:", er3_2)

			_, _, ere := la.SplitCancel(c, splitOrderNo, splitSeqNo, withdrawalAmount)
			if ere != nil {
				la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_FAIL, ere.Error()) //分账撤销失败
			} else {
				la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_CANCEL_SUCCESS) //分账撤销成功
			}

			return false, "system_error", wds
		}
		la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_SUCCESS) //分账成功

	} else {
		//分账失败
		tools.Logger.Error("分账查询失败:", spRes)

		la.UpdateServiceRecord(serviceId, WITHDRAW_SERVICE_SPLIT_FAIL, "订单状态:"+tools.ToString(spResponse.Result.OrderStatus)) //分账失败
		return false, "system_error", wds
	}
	tools.Logger.Info("佣金账户转到余额结束 adminId:", adminId, ",金额:", withdrawalAmount, "分钱")
	return true, "", wds
}

/***
 * @Author: rozimamat
 * @description: 优惠券结束后退款给代理
 * @Date: 2023-09-13 15:34:31
 */
func (la LakalaService) CouponRefund(c *gin.Context, couponId int) (map[string]interface{}, error) {

	startTime := carbon.CreateFromTime(0, 0, 0, "Asia/Shanghai")
	endTime := carbon.CreateFromTime(8, 30, 0, "Asia/Shanghai")
	now := carbon.Now("Asia/Shanghai")
	flag := now.Gt(startTime) && now.Lt(endTime)
	//如果在 00:00 和 08:30 之间 优惠券退款的话 要提示 系统维护中  防止出现余额不足
	if flag {
		return nil, errors.New(la.langUtil.T("system_maintaining_in_progress"))
	}
	//限流  10次请求 放行一次
	//防止多次点击
	key := "coupon_refund_" + tools.ToString(couponId)
	if !tools.AccessLimit(c, key, 10, 1, 60*5) { //每5分钟的请求只能通过一次
		return nil, fmt.Errorf(fmt.Sprintf(la.langUtil.T("retry_after_minute"), 5))
	}
	tools.Logger.Info("优惠券过期后给代理退款开始 优惠券id:", couponId)
	/***
	优惠券结束后退款给代理
	1.检查是否可以退款
	2.写入退款记录
	3.退款
	4.写入退款结果
	5.优惠券状态写成退款完成
	**/

	//查询是否可以退款
	//退款金额是否正确
	//是否已经退款

	db := tools.GetDB()

	//--------------1.检查是否可以退款-----开始---------------//

	type Coupon struct {
		Id             int    `gorm:"column:id"`
		Count          int    `gorm:"column:count"`
		SaledCount     int    `gorm:"column:saled_count"`
		StartTime      string `gorm:"column:start_time"`
		EndTime        string `gorm:"column:end_time"`
		StartUseTime   string `gorm:"column:start_use_time"`
		EndUseTime     string `gorm:"column:end_use_time"`
		ChargeId       int    `gorm:"column:charge_id"`
		CityId         int    `gorm:"column:city_id"`
		AreaId         int    `gorm:"column:area_id"`
		Price          int    `gorm:"column:price"`
		Amount         int    `gorm:"column:amount"`
		AdminId        int    `gorm:"column:admin_id"`
		OrderNo        string `gorm:"column:order_no"`
		PaySeqNo       string `gorm:"column:pay_seq_no"`
		SellerMemberNo string `gorm:"column:seller_member_no"`
	}
	var coupon Coupon
	fields := "t_coupon.id,t_coupon.count,t_coupon.saled_count,t_coupon.start_time,t_coupon.end_time,"
	fields += "t_coupon.start_use_time,t_coupon.end_use_time,t_pay_agent_lakala.order_no,t_pay_agent_lakala.pay_seq_no,"
	fields += "t_pay_agent_lakala.seller_member_no,t_admin_packet_recharge_log.recharge_amount as amount,t_coupon.admin_id,"
	fields += "t_coupon.city_id,t_coupon.area_id,t_coupon.price"

	db.Table("t_coupon").
		Joins("left join t_admin_packet_recharge_log on t_admin_packet_recharge_log.item_id=t_coupon.id").
		Joins("left join t_pay_agent_lakala on t_pay_agent_lakala.charge_id=t_admin_packet_recharge_log.id").
		Where("t_coupon.id = ? ", couponId).
		Where("t_admin_packet_recharge_log.type = ? and t_admin_packet_recharge_log.payed = ? ",11,1).//类型是充值优惠券的 和 支付完成的
		Select(fields).
		Scan(&coupon)

	amount := int(0)

	if coupon.Id == 0 {
		return nil, errors.New(la.langUtil.T("coupon_not_found"))
	}
	refundCount :=int64(0)
	db.Table("t_admin_packet_recharge_log").Where("item_id = ? and payed = ? and type = ?",coupon.Id,1,12).Count(&refundCount)
	if refundCount > 0 {
		return nil, errors.New(la.langUtil.T("coupon_refunded"))
	}

	flag1 := carbon.Now("Asia/Shanghai").Gt(carbon.Parse(coupon.StartTime)) && carbon.Now("Asia/Shanghai").Gt(carbon.Parse(coupon.EndTime))       //当前时间大于开始领取时间和结束领取时间
	flag2 := carbon.Now("Asia/Shanghai").Gt(carbon.Parse(coupon.StartUseTime)) && carbon.Now("Asia/Shanghai").Gt(carbon.Parse(coupon.EndUseTime)) //当前时间大于开始使用时间和结束使用时间

	if !(flag1 && flag2) {
		return nil, errors.New(la.langUtil.T("coupon_refund_no_amount"))
	}

	couponSaledCount := int64(0) //优惠券实际使用数量

	db.Table("t_coupon_log").
		Where("t_coupon_log.coupon_id = ? and state = ? ", couponId, models.COUPON_LOG_STATE_USED).
		Count(&couponSaledCount)

	leftCount := coupon.Count - int(couponSaledCount)
	if leftCount <= 0 {

		return nil, errors.New(la.langUtil.T("coupon_refund_no_amount"))
	}

	amount = tools.ToInt(leftCount) * tools.ToInt(coupon.Price)

	if amount > coupon.Amount {
		return nil, errors.New(la.langUtil.T("coupon_refund_no_amount"))
	}

	ct := int64(0)
	db.Table("t_admin_market_refund_log").Where("item_id = ? and refunded = ?", couponId, 1).Count(&ct)
	if ct > 0 {
		return nil, errors.New(la.langUtil.T("coupon_refunded"))
	}

	//--------------1.检查是否可以退款-----结束---------------//

	//--------------2.写入退款记录--------开始-------//

	outTradeNo := la.GenerateOutOrderNo(c, "RF") // 应用平台请求号
	var refundLog models.AdminMarketRefundLog
	refundLog.CityID = coupon.CityId
	refundLog.AreaID = coupon.AreaId
	refundLog.RechargeID = coupon.ChargeId
	refundLog.AdminID = coupon.AdminId
	refundLog.ItemID = couponId
	refundLog.Type = 1
	refundLog.TotalAmount = coupon.Amount
	refundLog.RefundAmount = amount
	refundLog.OutTradeNo = outTradeNo
	refundLog.Refunded = 0 //未完成
	refundLog.SplitRuleResult = "{}"
	er1 := db.Table("t_admin_market_refund_log").Create(&refundLog).Error
	if er1 != nil {
		tools.Logger.Error("优惠券退款失败 写入t_admin_market_refund_log失败,id:", couponId, er1)
		tools.AliDeveloperDingdingMsg("优惠券退款失败 写入t_admin_market_refund_log失败,id:" + tools.ToString(couponId) + " " + er1.Error())
		return nil, errors.New(la.langUtil.T("coupon_refunded"))
	}

	packetMap := make(map[string]interface{})
	packetMap["city_id"] = coupon.CityId
	packetMap["area_id"] = coupon.AreaId
	packetMap["admin_id"] = coupon.AdminId
	packetMap["recharger_id"] = coupon.AdminId
	packetMap["item_id"] = coupon.Id //优惠券id
	packetMap["type"] = 12           //优惠券退款
	packetMap["recharge_amount"] = amount
	packetMap["out_trade_no"] = outTradeNo
	packetMap["prepay_id"] = ""
	packetMap["nonce_str"] = ""
	packetMap["trade_type"] = "优惠券退款"
	packetMap["payed"] = 1
	packetMap["created_at"] = carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s")
	packetMap["updated_at"] = carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s")
	er2 := db.Table("t_admin_packet_recharge_log").Create(&packetMap).Error

	if er2 != nil {
		tools.Logger.Error("t_admin_packet_recharge_log 更新失败:", er2.Error())
		tools.AliDeveloperDingdingMsg("t_admin_packet_recharge_log 更新失败,id:" + tools.ToString(couponId) + " " + er2.Error())
		return nil, errors.New(la.langUtil.T("coupon_refunded"))
	}

	db.Table("t_coupon").Where("id = ?", couponId).Updates(&map[string]interface{}{
		"state": 5, //5:彻底结束等待退剩余的钱
	})

	//--------------2.写入退款记录--------结束-------//

	//--------------3.退款--------开始-------//

	api := "order.consume.refund"
	param := make(map[string]interface{})
	param["order_no"] = coupon.OrderNo // 原订单（需要退款的订单编号）
	param["member_no"] = coupon.SellerMemberNo

	param["out_request_no"] = outTradeNo
	param["refund_amount"] = amount       // 退款金额
	param["pay_seq_no"] = coupon.PaySeqNo // 支付流水号
	refundRule := make([]map[string]interface{}, 0)
	refundRule = append(refundRule, map[string]interface{}{ // 支付流水号
		"special_account_no": "S005",
		"member_no":          coupon.SellerMemberNo,
		"amount":             amount,
	})
	param["refund_rule"] = refundRule

	result, err := la.Send(c, param, api)
	if err != nil {
		tools.Logger.Error("拉卡拉优惠券退款失败,优惠券id:", couponId, err.Error())
		tools.AliDeveloperDingdingMsg("拉卡拉优惠券退款失败,优惠券id:" + tools.ToString(couponId) + " " + err.Error())
		return nil, err
	}
	resultMap, _ := tools.StringToMap(result)
	response := resultMap["response"].(string)
	tools.Logger.Info("拉卡拉优惠券退款 拉卡拉退款结果：", response)
	resData, _ := tools.StringToMap(response)
	if resData["status"] != "OK" {
		tools.AliDeveloperDingdingMsg("拉卡拉 优惠券 退款失败,优惠券id:" + tools.ToString(couponId))
		tools.Logger.Error("拉卡拉 优惠券 退款失败,优惠券id:" + tools.ToString(couponId))
		return nil, errors.New("拉卡拉退款失败")
	}
	resMap := resData["result"].(map[string]interface{})
	if tools.ToInt(resMap["refund_status"]) != 1004 {
		errorMsg :=""
		if resMap["error_message"] !=nil {
			errorMsg = tools.ToString(resMap["error_message"])
		}
		errMessage := "拉卡拉 优惠券 退款失败,优惠券id:" + tools.ToString(couponId) + " " + errorMsg+" response:"+response
		tools.AliDeveloperDingdingMsg(errMessage)
		tools.Logger.Error(errMessage)
		return nil, errors.New("拉卡拉退款失败")
	}
	refundRuleInsert, _ := json.Marshal(param["refund_rule"])
	//--------------3.退款--------结束-------//

	//--------------4.写入退款结果--------开始-------//

	// 记录退款日志
	er4 := db.Table("t_admin_market_refund_log").Where("id = ?", refundLog.ID).Updates(&map[string]interface{}{
		"refunded":                   1,
		"order_no":                   resMap["order_no"],
		"out_request_no":             param["out_request_no"],
		"refund_amount":              amount,
		"pay_seq_no":                 coupon.PaySeqNo,
		"refund_rule":                string(refundRuleInsert),
		"order_status":               resMap["order_status"],
		"refund_status":              resMap["refund_status"],
		"error_message":              resMap["error_message"],
		"refund_seq_no":              resMap["refund_seq_no"],
		"split_rule_result":          resMap["split_rule_result"],
		"qzt_channel_pay_request_no": resMap["qzt_channel_pay_request_no"],
		"channel_trade_no":           resMap["channel_trade_no"],
		"channel_seq_no":             resMap["channel_seq_no"],
		"pay_channel_trade_no":       resMap["pay_channel_trade_no"],
		"refund_time":                resMap["refund_time"],
		"created_at":                 carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s"),
		"updated_at":                 carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s"),
	}).Error
	if er4 != nil {
		//记录下来 手动处理
		errMessage := "拉卡拉 优惠券 退款发送后 t_admin_market_refund_log 更新失败,优惠券id:" + tools.ToString(couponId) + " " + resMap["error_message"].(string)
		tools.AliDeveloperDingdingMsg(errMessage)
		tools.Logger.Error(er4)
		tools.Logger.Error(errMessage)
		return nil, errors.New("拉卡拉退款失败")
	}
	//--------------4.写入退款结果--------结束-------//

	//--------------5.优惠券状态写成退款完成--------开始-------//
	db.Table("t_coupon").Where("id = ?", couponId).Updates(&map[string]interface{}{
		"state":    6, //6:彻底结束已退剩余的钱
		"refunded": 1,
	})
	//--------------5.优惠券状态写成退款完成--------结束-------//

	return resultMap, nil
}
//拉卡拉查询支付结果
func (la LakalaService) QueryLakala(outOrderNo string) (LakalaEntity.QueryResultResult, error) {

	
	param := make(map[string]interface{})
	param["out_order_no"] = outOrderNo // 订单号

	api := "order.detail.info.get"

	tools.Logger.Info("拉卡拉 查询支付信息 发送参数:api:", api)
	tools.Logger.Info(param)
	result, err := la.SendData("**************", param, api)
	tools.Logger.Info("拉卡拉支付结果查询",result)
	var r LakalaEntity.QueryResultResponse
	var qr LakalaEntity.PayQueryResult
	if err != nil {
		return r.Result,err
	}
	json.Unmarshal([]byte(result), &qr)

	json.Unmarshal([]byte(qr.Response), &r)
	
	return r.Result,nil 
}

//支付状态查询
func (la LakalaService) PayStatusCheck(orderNo string,paySeqNo string) (map[string]interface{}, error) {
	api := "order.pay.status.get"
	param := make(map[string]interface{})
	param["order_no"] = orderNo // 原订单（需要退款的订单编号
	param["pay_seq_no"] = paySeqNo // 原订单（需要退款的订单编号
	result, err := la.SendData(ServerIp, param, api)
	if err != nil {
		return nil, err
	}
	resultMap, _ := tools.StringToMap(result)
	response := resultMap["response"].(string)
	// tools.Logger.Info("拉卡拉支付状态结果：", response)
	responseMap, _ := tools.StringToMap(response)
	return responseMap, nil
}


//订单状态查询
func (la LakalaService) OrderStatusCheck(orderNo string) (map[string]interface{}, error) {
	api := "order.status.get"
	param := make(map[string]interface{})
	param["order_no"] = orderNo // 原订单（需要退款的订单编号
	result, err := la.SendData(ServerIp, param, api)
	if err != nil {
		return nil, err
	}
	resultMap, _ := tools.StringToMap(result)
	response := resultMap["response"].(string)
	// tools.Logger.Info("拉卡拉订单状态结果：", response)
	responseMap, _ := tools.StringToMap(response)
	return responseMap, nil
}

//订单状态查询
func (la LakalaService) RefundCheck(orderNo string,refundSeqNo string) (map[string]interface{}, error) {
	api := "order.refund.status.get"
	param := make(map[string]interface{})
	param["order_no"] = orderNo // 原订单（需要退款的订单编号
	param["refund_seq_no"] = refundSeqNo // 原订单（需要退款的订单编号
	result, err := la.SendData(ServerIp, param, api)
	if err != nil {
		return nil, err
	}
	resultMap, _ := tools.StringToMap(result)
	response := resultMap["response"].(string)
	// tools.Logger.Info("拉卡拉订单状态结果：", response)
	responseMap, _ := tools.StringToMap(response)
	return responseMap, nil
}


//提现修复
func (l *LakalaService) WithdrawFix() (map[string]interface{}, error) {
	db :=tools.GetDB()

	realUpdate :=true
	//1.找出正在体现的店铺数据
	var storeIds []int64
	db.Model(&models.Restaurant{}).Where("is_cashouting = ? and deleted_at is null",1).Debug().Pluck("id",&storeIds)
	for _,storeId := range storeIds {
		tools.Logger.Info("正在处理提现失败的店铺:",storeId)
		//2.:提现失败的数据集合
		var services models.LakalaWithdrawService
		db.Model(&models.LakalaWithdrawService{}).Where("opt_type = ? and `type` = ? and opt_id =? and state not in (?)","merchant",1,storeId,[]int{7,5}).Debug().Find(&services)
		tools.Logger.Info("正在处理提现失败的店铺,失败数据service_id:",services.ID)
		if services.ID == 0 {
			continue
		}
		//3.找出提现失败的店铺数据
		var withdraws models.LakalaWithdraw
		db.Model(&models.LakalaWithdraw{}).Where("service_id = ? ",services.ID).Debug().Find(&withdraws)

		//4.找出要提现的数据合计
		var billItems []models.Mulazimpaytoresagent
		db.Model(&models.Mulazimpaytoresagent{}).Where("id in (?)",services.Bills).Debug().Find(&billItems)
	
		//5.查询提现是否成功
		queryResult, _ := l.QueryLakala(withdraws.OutOrderNo)
		if queryResult.OrderStatus == 1006 {  //状态是成功的 可以继续处理 
			tools.Logger.Info("正在处理提现失败的店铺,失败数据service_id:",services.ID,",提现成功了，要修改数据了")
			if realUpdate {
				tx := db.Begin()
				defer func() { 
					if err := recover(); err != nil { 
						tools.Logger.Error("更新提现失败数据出错",err)
						tx.Rollback() 
					}
				}()
				//1.更新 t_lakala_withdraw 的 order_no 字段 
				er1:=tx.Model(&models.LakalaWithdraw{}).Where("id = ?",withdraws.ID).Debug().Update("order_no",queryResult.OrderNo).Error
				if er1 != nil {
					tx.Rollback()
					tools.Logger.Error("更新提现失败数据出错:", er1)
					return nil, errors.New("更新提现失败数据出错"+er1.Error())
				}
				//2.写入cash_out_log
				cashOutTime := queryResult.PayList[0].PayTime
				//记录提现
				mcl := models.MerchantCashoutLog{
					RestaurantID:   int(storeId),
					CashoutAmount:  fmt.Sprintf("%d", services.Amount),
					CashoutTime:   carbon.Parse(cashOutTime,configs.AsiaShanghai).Carbon2Time(),
					CashPlatform:   2,
					WithdrawId:     services.ID,
					PartnerTradeNo: queryResult.OrderNo, //拉卡拉订单号
				}
				ct :=int64(0)
				tx.Model(&models.MerchantCashoutLog{}).Where("withdraw_id = ?",services.ID).Count(&ct)
				if ct == 0 {
					er3 := tx.Debug().Create(&mcl).Error
					if er3 != nil {
						tx.Rollback()
						tools.Logger.Error("提现 日志 写入失败:", er3)
						return nil,errors.New("提现 日志 写入失败:"+er3.Error())
					}
				}
				//3.更新 t_mulazimpaytoresagent 的 四个字段 
				updateMap := make(map[string]interface{})
				updateMap["payment_type"] = 3 //Mulazim打款方式（1：企业付款到零钱，2：企业付款到银行卡,3:拉卡拉）
				updateMap["withdraw_id"] = services.ID
				updateMap["payment_time"] = carbon.Parse(cashOutTime,configs.AsiaShanghai).Format("Y-m-d H:i:s")
				updateMap["payment_state"] = 1                    //0未付款，1已付款
				updateMap["partner_trade_no"] = queryResult.OrderNo //拉卡拉订单号
				er4 := tx.Model(&models.Mulazimpaytoresagent{}).Where("id in (?)", services.Bills).Debug().Updates(&updateMap).Error
				if er4 != nil {
					tools.Logger.Error("更新提现失败数据出错",er4)
					tx.Rollback()
					return nil,errors.New("更新提现失败数据出错"+er4.Error())
				}
			
				//4.更新 t_lakala_withdraw_service 的 state 字段
				logs :=services.Logs
				type LogResult struct {
					Error string `json:"error"`
					State int    `json:"state"`
					Time  string `json:"time"`
				}
				var logResults []LogResult
				json.Unmarshal([]byte(logs), &logResults)
				logResults = append(logResults,LogResult{Error:"",State:5,Time:carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s")})
				logsStr := tools.JsonEncode(logResults)
				tx5 :=tx.Model(&models.LakalaWithdrawService{}).Where("id = ?",services.ID).Debug().Updates(&map[string]interface{}{
					"state":5,
					"logs":logsStr,
				}).Error
				if tx5 != nil { 
					tx.Rollback()
					tools.Logger.Error("更新提现失败数据出错",tx5)
					return nil,errors.New("更新提现失败数据出错"+tx5.Error())
				}
				//TODO 手动处理 店铺的 提现中状态 
				// tx6 :=tx.Model(&models.Restaurant{}).Where("id = ?",services.OptId).Debug().Updates(&map[string]interface{} {
				// 	"is_cashouting":0,
				// }).Error
				// if tx6 != nil { 
				// 	tx.Rollback()
				// 	tools.Logger.Error("更新店铺提现中状态出错",tx6)
				// 	return nil,errors.New("更新店铺提现中状态出错"+tx6.Error())
				// }

				tx.Commit()

			}
		}else{
			tools.Logger.Info("正在处理提现失败的店铺,失败数据service_id:",services.ID,",提现还没有到账，需要稍后再次执行")
		}

	}
	return nil,nil
	

}