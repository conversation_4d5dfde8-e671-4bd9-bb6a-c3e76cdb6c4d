package merchant

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type FoodsCategoryService struct {
	langUtil *lang.LangUtil
	language string
}
func NewFoodsCategoryService(c *gin.Context) *FoodsCategoryService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := FoodsCategoryService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &service
}

// List
//
// @Description: 状态列表
// @Author: Rixat
// @Time: 2024-09-25 13:24:49
// @receiver 
// @param c *gin.Context
func (s FoodsCategoryService) List(kw string, state *int) []models.FoodsCategory {
	var categoryList []models.FoodsCategory
	query := tools.Db.Model(categoryList).Order("weight")
	if len(kw) > 0 {
		query.Where("name_"+s.language+" LIKE ?", "%"+kw+"%")
	}
	if state != nil {
		query.Where("state = ?", state)
	}
	query.Find(&categoryList)
	return categoryList
}