package merchant

import (
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	merchantSeckillRequest "mulazim-api/requests/merchantRequest/Seckill"
	"mulazim-api/resources/merchant"
	"mulazim-api/services"
	"mulazim-api/tools"
	merchantTransformers "mulazim-api/transformers/merchant"
	"slices"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type SeckillService struct {
	langUtil *lang.LangUtil
	language string
	services.BaseService
}

func NewSeckillService(c *gin.Context) *SeckillService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	seckill := SeckillService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &seckill
}

// GetSeckillList
//
// @Description: 秒杀列表
// @Author: Rozimamat
// @Time: 2024-03-12 10:40:52
// @receiver
// @param c *gin.Context
func (seckill SeckillService) GetSeckillList(adminId int, resId int, state int, page int, limit int, deleted int, startDate string, endDate string) merchant.SeckillListResponse {
	if(strings.Contains(endDate,"13-01")){
		currentYear := time.Now().Year()
		endDate = tools.ToString(currentYear) +"-12-31"
	}
	db :=tools.GetDB()

	if limit == 0 {
		limit = 10
	}
	if page == 0 {
		page = 1
	}
	result := merchant.SeckillListResponse{
		Page:  page,
		Limit: limit,
	}
	if startDate == "" && endDate == "" {
		startDate = carbon.Now(configs.AsiaShanghai).AddDays(-30).Format("Y-m-d 00:00:00")
		endDate = carbon.Now(configs.AsiaShanghai).Format("Y-m-d 23:59:59")
	}else{
		startDate = carbon.Parse(startDate,configs.AsiaShanghai).Format("Y-m-d 00:00:00")
		endDate = carbon.Parse(endDate,configs.AsiaShanghai).Format("Y-m-d 23:59:59")
	}

	seckillModel := db.Model(&models.Seckill{}).
		Where("created_at between ? and ?",startDate,endDate).
		Where("restaurant_id=? and type = ?", resId, 1)
	switch(state){
		case 1:
			seckillModel=seckillModel.Where("state = ?", state)
		case 2: //停止
			seckillModel=seckillModel.Where("state = ? ", 0)
		case 3: //失效
			seckillModel=seckillModel.Where(" end_time < ?", carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"))
	}

	if deleted != 0 {
		seckillModel.Unscoped().Where("deleted_at is not null")
	}
	var total int64
	seckillModel.Count(&total) // 总数
	result.Total = total
	var seckillList []models.Seckill // 活动列表
	seckillModel.
		Preload("RestaurantFoods.ComboFoodItems.RestaurantFood").
		Preload("RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions").
		Preload("SelectedSpec.FoodSpecOptions").
		Order("created_at desc").Limit(limit).Offset(limit * (page - 1)).Find(&seckillList)

	var items []merchant.SeckillListEntity
	for _, value := range seckillList {
		comboItems, selectedSpec := merchantTransformers.GetFormattedFoodSpecCombo(value, seckill.language)
		item := merchant.SeckillListEntity{
			ID:                int(value.ID),
			FoodID:            value.FoodId,
			FoodName:          tools.GetNameByLang(value.RestaurantFoods, seckill.language),
			FoodImg:           tools.AddCdn(value.RestaurantFoods.Image),
			OriginalPrice:     tools.ToInt(value.RestaurantFoods.Price),
			Price:             value.Price,
			TotalCount:        value.TotalCount,
			UserMaxOrderCount: value.UserMaxOrderCount,
			SaledCount:        value.SaledCount,
			BeginTime:         carbon.Parse(value.BeginTime, configs.AsiaShanghai).Format("Y-m-d H:i:00"),
			EndTime:           carbon.Parse(value.EndTime, configs.AsiaShanghai).Format("Y-m-d H:i:00"),
			Order:             int(value.Order),
			State:             value.State,
			StateName:         seckill.langUtil.TArr("seckill_states")[tools.ToInt(value.State)],
			ReviewState:       value.ReviewState,
			ReviewStateName:   seckill.langUtil.TArr("seckill_review_states")[tools.ToInt(value.ReviewState)],
			CreatedAt:         value.CreatedAt.Format("2006-01-02 15:04:05"),
			FoodType:          value.FoodType,
			SelectedSpec:      selectedSpec,
			ComboFoodItems:    comboItems,
		}

		if deleted != 0 {
			item.DeletedAt = value.DeletedAt.Time.Format("2006-01-02 15:04:05")
		}
		item.Creator = tools.If(value.AdminId != int64(adminId), seckill.langUtil.T("created_by_admin"), seckill.langUtil.T("created_by_owner"))

		items = append(items, item)
	}

	result.Items = tools.If(items == nil, make([]merchant.SeckillListEntity, 0), items)
	return result
}

// Create
//
// @Description: 秒杀创建
// @Author: Rozimamat
// @Time: 2024-03-12 10:40:52
// @receiver
// @param c *gin.Context
func (seckill SeckillService) Create(
	resId int, params merchantSeckillRequest.SeckillCreateParams, adminId int,
) (bool, string) {

	if params.UserMaxOrderCount <= 0 {
		return false, "user_max_order_count_is_zero"
	}
	if params.TotalCount <= 0 {
		params.TotalCount = 100 //默认值100
	}
	if params.TotalCount < params.UserMaxOrderCount {
		return false, "total_count_is_less_than_user_max_order_count"
	}

	now := carbon.Now(configs.AsiaShanghai)
	begin := carbon.ParseByFormat(params.BeginTime, "Y-m-d H:i:00", configs.AsiaShanghai)
	multipleDiscount, foodName := seckill.CheckFoodsMultipleDiscount(seckill.langUtil.Lang, int(params.FoodId), begin.Format("Y-m-d H:i:00"), begin.AddHour().Format("Y-m-d H:i:00"))
	if multipleDiscount.ID > 0 {
		//存在正在进行的多分打折活动 不允许创建
		return false, fmt.Sprintf(seckill.langUtil.T("food_has_multiple_discount"), foodName)
	}

	diff :=now.DiffInMinutes(begin)
	if diff < 0 {
		return false, "begin_time_is_not_valid"
	}

	params.BeginTime = begin.Format("Y-m-d H:i:00")
	endTime := begin.AddHour().Format("Y-m-d H:i:00")

	db :=tools.GetDB()
	var resInfo models.Restaurant
	db.Model(&models.Restaurant{}).Where("id = ?",resId).Find(&resInfo)
	if resInfo.State != 1 {
		return false, "restaurant_state_error"
	}

	if tools.InArray(resInfo.AreaID,configs.MyApp.SeckillAndPrefStopArea){//墨玉县活动 暂停
		return false, "promotion_create_disabled"
	}

	var foodInfo models.RestaurantFoods

	db.Model(&models.RestaurantFoods{}).
		Where("id = ?", params.FoodId).Where("restaurant_id = ?", resId).
		Preload("FoodSpecTypes.FoodSpecOptions").
		Find(&foodInfo)
	if foodInfo.ID == 0 {
		return false, "food_not_exist"
	}
	if foodInfo.State != 1 {
		return false, "food_state_error"
	}
	//判断美食开始和技术时间是否在营业时间段内
	if !tools.TimeLineInTimeLine(begin.Format("H:i:00"),begin.AddHour().Format("H:i:00"),foodInfo.BeginTime, foodInfo.EndTime) {
		return false, fmt.Sprintf(seckill.langUtil.T("food_not_in_activity_range"),foodInfo.BeginTime,foodInfo.EndTime)
	}

	// 美食价格 - 如果是规格美食，需要从已选的规格子项中获取最终价格
	foodPrice := int(foodInfo.Price)
	if params.FoodType == models.RestaurantFoodsTypeSpec {
		if len(params.OptionIds) == 0 {
			return false, "option_ids_must_not_empty"
		}

		_foodPrice := 0
		for _, fType := range foodInfo.FoodSpecTypes {
			for _, _specOption := range fType.FoodSpecOptions {
				if slices.Contains(params.OptionIds, _specOption.ID) {
					_foodPrice += _specOption.Price
				}
			}
		}
		if _foodPrice <= 0 {
			return false, "price_is_less_than_food_price" // TODO: 应该是规格项价格不对的提示
		}
		foodPrice = _foodPrice
	}

	if params.Price >= int64(foodPrice) {
		return false, "price_is_less_than_food_price"
	}

	var ct int64
	db.Model(&models.Seckill{}).
		Where("restaurant_id = ? and begin_time  = ? and food_id = ? and state = ?", resId, params.BeginTime, params.FoodId, 1).
		Count(&ct)
	if ct > 0 {
		return false, "seckill_is_exist"
	}

	// 规格
	specId := 0
	if foodInfo.FoodType == models.RestaurantFoodsTypeSpec {
		SpecID, err := seckill.SaveFoodSpec(foodInfo.RestaurantID, foodInfo.ID, params.OptionIds)
		if err != nil {
			return false, "create_seckill_failed"
		}
		specId = SpecID
	}

	err :=db.Table("b_seckill").Create(&map[string]interface{}{
		"admin_id":             adminId,
		"city_id":              resInfo.CityID,
		"area_id":              resInfo.AreaID,
		"restaurant_id":        resId,
		"food_id":              params.FoodId,
		"total_count":          params.TotalCount,
		"price":                params.Price,
		"begin_time":           params.BeginTime,
		"end_time":             endTime,
		"created_at":           now,
		"updated_at":           now,
		"state":                1,
		"review_state":         2,
		"user_max_order_count": params.UserMaxOrderCount,
		"food_type":            params.FoodType,
		"spec_id": specId,
	}).Error
	if err != nil {
		tools.Logger.Errorf("秒杀创建失败",err)
		return false, "create_seckill_failed"
	}

	return true,""
}

// edit
//
// @Description: 秒杀编辑
// @Author: Rozimamat
// @Time: 2024-03-13 10:40:52
// @receiver
// @param c *gin.Context
func (seckill SeckillService) Detail(resId, Id int) (*merchant.SeckillDetailResponse, string) {
	db :=tools.GetDB()
	var seck models.Seckill
	db.Model(&models.Seckill{}).
		Preload("RestaurantFoods.ComboFoodItems.RestaurantFood"). // 套餐子美食
		Preload("RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions"). // 套餐子美食规格
		Preload("SelectedSpec.FoodSpecOptions"). // 已选规格
		Where("id = ? and restaurant_id = ?", Id, resId).
		Take(&seck)
	if seck.ID ==0 {
		return nil,"seckill_not_exist"
	}
	if seck.PriceMarkupID != 0 {
		return nil,"price_markup_seckill_not_allow_edit"
	}
	if tools.ToInt(seck.UserMaxOrderCount) == 0 {
		seck.UserMaxOrderCount = seck.TotalCount
	}

	comboItems, selectedSpec := merchantTransformers.GetFormattedFoodSpecCombo(seck, seckill.language)
	result := merchant.SeckillDetailResponse{
		ID:                seck.ID,
		FoodID:            seck.FoodId,
		FoodName:          tools.If(seckill.language == "zh", seck.RestaurantFoods.NameZh, seck.RestaurantFoods.NameUg),
		FoodPrice:         seck.RestaurantFoods.Price,
		FoodImg:           tools.AddCdn(seck.RestaurantFoods.Image),
		TotalCount:        seck.TotalCount,
		Price:             seck.Price,
		BeginTime:         carbon.Parse(seck.BeginTime, configs.AsiaShanghai).Format("Y-m-d H:i:00"),
		EndTime:           carbon.Parse(seck.EndTime, configs.AsiaShanghai).Format("Y-m-d H:i:00"),
		CreatedAt:         seck.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:         seck.UpdatedAt.Format("2006-01-02 15:04:05"),
		State:             seck.State,
		ReviewState:       seck.ReviewState,
		UserMaxOrderCount: seck.UserMaxOrderCount,
		StateName:         seckill.langUtil.TArr("seckill_states")[tools.ToInt(seck.State)],
		ReviewStateName:   seckill.langUtil.TArr("seckill_review_states")[tools.ToInt(seck.ReviewState)],

		FoodType:       seck.FoodType,
		SelectedSpec:   selectedSpec,
		ComboFoodItems: comboItems,
	}

	return &result, ""
}



// UpdateState
//
// @Description: 秒杀 更改状态
// @Author: Rozimamat
// @Time: 2024-03-13 10:40:52
// @receiver
// @param c *gin.Context
func (seckill SeckillService) UpdateState(
	adminId int,
	resId int,
	Id         int,
	State         int,
) (bool,string) {

	db :=tools.GetDB()
	var seck models.Seckill
	db.Model(&models.Seckill{}).Where("id = ? and restaurant_id = ?",Id,resId).Find(&seck)
	if seck.ID ==0 {
		return false,"seckill_not_exist"
	}

	if seck.PriceMarkupID != 0 {
		return false,"price_markup_seckill_not_allow_edit"
	}
	var resInfo models.Restaurant
	db.Model(&models.Restaurant{}).Where("id = ?",resId).Find(&resInfo)
	if resInfo.State != 1 {
		return false, "restaurant_state_error"
	}

	if tools.InArray(resInfo.AreaID,configs.MyApp.SeckillAndPrefStopArea){//墨玉县活动 暂停
		return false, "promotion_update_disabled"
	}
	if seck.State != State {
		if State == 0 {
			tools.Logger.Info(fmt.Sprintf("秒杀更改状态 id:%d adminId:%d 原来的状态 %d 新状态 %d ",Id,adminId,seck.State,State))
			err :=db.Model(&models.Seckill{}).Where("id = ? ",Id).Updates(&map[string]interface{}{
				"state":State,
				"updated_at":carbon.Now(configs.AsiaShanghai),
			}).Error
			if err != nil {
				tools.Logger.Errorf("秒杀编辑失败",err)
				return false,"seckill_update_state_failed"
			}
		}
		end :=carbon.Parse(seck.EndTime,configs.AsiaShanghai)
		now :=carbon.Now(configs.AsiaShanghai)
		multipleDiscount, foodName := seckill.CheckFoodsMultipleDiscount(seckill.langUtil.Lang, int(seck.FoodId), end.AddHours(-1).Format("Y-m-d H:i:00"), end.Format("Y-m-d H:i:00"))
		if multipleDiscount.ID > 0 {
			//存在正在进行的多分打折活动 不允许创建
			return false, fmt.Sprintf(seckill.langUtil.T("food_has_multiple_discount"), foodName)
		}

		diff :=now.DiffInMinutes(end)
		if diff < 0 {
			return false, "seckill_end_time_is_over"
		}
		var ct int64
		db.Model(&models.Seckill{}).Where("restaurant_id = ? and begin_time  = ? and food_id = ? and state = ? ",resId,seck.BeginTime,seck.FoodId,1).Count(&ct)
		if ct > 0 {
			return false, "seckill_is_exist"
		}

		tools.Logger.Info(fmt.Sprintf("秒杀更改状态 id:%d adminId:%d 原来的状态 %d 新状态 %d ",Id,adminId,seck.State,State))
		err :=db.Model(&models.Seckill{}).Where("id = ? ",Id).Updates(&map[string]interface{}{
			"state":State,
			"updated_at":carbon.Now(configs.AsiaShanghai),
		}).Error
		if err != nil {
			tools.Logger.Errorf("秒杀编辑失败",err)
			return false,"seckill_update_state_failed"
		}
	}

	return true,""
}



// Delete
//
// @Description: 秒杀 删除
// @Author: Rozimamat
// @Time: 2024-03-13 10:40:52
// @receiver
// @param c *gin.Context
func (seckill SeckillService) Delete(
	adminId int,
	resId int,
	Id         int,
) (map[string]interface{},string) {

	db :=tools.GetDB()
	var seck models.Seckill
	db.Model(&models.Seckill{}).Where("id = ? and restaurant_id = ?",Id,resId).Select("id,state").Find(&seck)
	if seck.ID ==0 {
		return nil,"seckill_not_exist"
	}
	if seck.PriceMarkupID != 0 {
		return nil,"price_markup_seckill_not_allow_edit"
	}

	// 秒杀活动删除的时候先自动进入到此方法、并先判断是否已经销售美食、如果有则不能删除活动
	var ct int64
	db.Model(&models.SeckillLog{}).Where("seckill_id = ? ",Id).Count(&ct)
	if ct > 0 {
		return nil,"seckill_is_sold"
	}
	err :=db.Delete(&seck).Error
	if err != nil {
		tools.Logger.Errorf("秒杀删除失败",err)
		return nil,"seckill_delete_failed"
	}
	return nil,""
}



// Log
//
// @Description: 秒杀日志
// @Author: Rozimamat
// @Time: 2024-03-14 16:40:52
// @receiver
// @param c *gin.Context
func (seckill SeckillService) Log(
	Id         int,
) (map[string]interface{},bool,string) {
	result :=make(map[string]interface{})

	type HeaderData struct{
		OrderCount int64 `gorm:"column:order_count"`
		SaledCount int64 `gorm:"column:saled_count"`
	}
	var headerData HeaderData

	db :=tools.GetDB()
	var logs []models.SeckillLog
	s :=db.Model(&models.SeckillLog{}).
		Preload("User").
		Preload("Food.Restaurant").
		Preload("Seckill.Area").
		Preload("OrderDetail.Order").
		Preload("OrderDetail.OrderToday").
		Where("seckill_id = ? and state = ?",Id,1)
	s.Select("count(1) as order_count,sum(saled_count) as saled_count").Scan(&headerData)
	s.Select("b_seckill_log.*").Find(&logs)
	items :=make([]map[string]interface{},0)
	userName :=""
	orderId :=""
	areaName :=""
	foodName :=""
	for _, v := range logs {
		userName =v.User.Name
		if v.OrderDetail.Order.ID > 0 {
			orderId = v.OrderDetail.Order.OrderID
		}else if v.OrderDetail.OrderToday.ID > 0 {
			orderId = v.OrderDetail.OrderToday.OrderID
		}
		if seckill.language == "ug" {
			areaName = v.Seckill.Area.NameUg
		}else{
			areaName = v.Seckill.Area.NameZh
		}
		if seckill.language == "ug" {
			foodName = v.Food.NameUg
		}else{
			foodName = v.Food.NameZh
		}
		items = append(items, map[string]interface{}{
			"id":v.ID,
			"user_name":userName,
			"order_id":orderId,
			"area_name":areaName,
			"food_name":foodName,
			"saled_count":v.SaledCount,
			"seckill_price":v.SeckillPrice,
			"created_at":v.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	result["head"]=map[string]interface{}{
		"order_count":headerData.OrderCount,
		"saled_count":headerData.SaledCount,
	}
	result["items"]=items
	return result,true,""
}