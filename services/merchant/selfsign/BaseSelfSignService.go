package selfsign

import (
	"mulazim-api/constants"
	"mulazim-api/models"
	"mulazim-api/tools"
)

type BaseSelfSignService struct {
}

// UpdateMerchantState
//
//	@Description: 更新状态
//
// 0:未提交,
// 1:待审核 ,
// 2:审核未通过 ,
// 3:后台审核通过 ,
// 4:待提交资料 ,
// 5:已提交资料 ,
// 6:资料未通过,
// 7:对公账户待确认,
// 8:待在线签约 ,
// 9:待银商入网审核 ,
// 10:银商入网成功,
// 11:银商入网失败,
//
//	@author: Ali<PERSON>jan
//	@Time: 2023-01-18 13:06:21
//	@receiver s *BaseSelfSignService
//	@param resId int
//	@param state int
func (s *BaseSelfSignService) UpdateMerchantState(resId int, state int, pageName string, remark string) {
	db := tools.Db
	db.Model(&models.SelfSignMerchantInfo{}).Where("restaurant_id = ?", resId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).UpdateColumn("state", state)

	infoId := 0
	db.Model(&models.SelfSignMerchantInfo{}).Where("restaurant_id = ?", resId).Select("id").Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Scan(&infoId)

	log := models.SelfSignVerifyLog{
		MerInfoID:  infoId,
		VarifyerID: 0,
		StepNum:    20,
		PageName:   pageName,
		Remark:     remark,
		State:      state,
	}
	db.Create(&log)
}

// GetMerchantInfoByResId
//
//	@Description: 根据餐厅ID获取商家提交信息信息
//	@author: Rixat
//	@Time: 2023-01-18 13:06:21
//	@receiver s *BaseSelfSignService
//	@param resId int
//	@param state int
func (s *BaseSelfSignService) GetMerchantInfoByResId(resId int) models.SelfSignMerchantInfo {
	var merchantInfo models.SelfSignMerchantInfo
	db := tools.Db
	db.Model(merchantInfo).Where("restaurant_id = ?", resId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Scan(&merchantInfo)
	return merchantInfo
}
