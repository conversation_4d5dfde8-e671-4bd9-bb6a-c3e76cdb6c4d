package selfsign

import "mulazim-api/models"

//
//  SelfSignInterface
//  @Description: 商家自助注册
//
type SelfSignInterface interface {
	//
	// UploadDoc
	//  @Description: 上传资料 1.里边第一步上传照片 2.第二部上传信息
	//  @param resId
	//  @return error
	//
	UploadDoc(resId int64) error
	//
	// UploadImages
	//  @Description: 上传所有信息
	//  @param merchantInfo
	//  @return error
	//  @return []map[string]string
	//
	UploadImages(merchantInfo models.SelfSignMerchantInfo) (error, []map[string]string)
	//
	// UploadResInfo
	//  @Description: 上传餐厅信息
	//  @param info
	//  @param picList
	//  @return error
	//
	UploadResInfo(info models.SelfSignMerchantInfo, picList []map[string]string) error
	//
	// ImageUploadToUms
	//  @Description: 单个上传每个信息
	//  @param imageFullPath
	//  @return map[string]interface{}
	//
	ImageUploadToUms(imageFullPath string) map[string]interface{}

	//
	// RequestAccountVerify
	//  @Description: 请求打款验证
	//  @param umsRegId
	//  @param companyAccount
	//  @return map[string]interface{}
	//
	RequestAccountVerify(umsRegId string, companyAccount string) map[string]interface{}

	//
	// CompanyAccountVerify
	//  @Description: 验证对公账户
	//  @param umsRegId
	//  @param transAmt
	//  @param companyAccount
	//  @return interface{}
	//
	CompanyAccountVerify(umsRegId string, transAmt string, companyAccount string) map[string]interface{}

	//
	// GetAgreementSign
	//  @Description: 获取电签接口
	//  @param umsRegId
	//  @return interface{}
	//
	GetAgreementSign(umsRegId string) map[string]interface{}

	//
	// GetBankList
	//  @Description: 获取银行列表
	//  @return interface{}
	//
	GetBankList(areaCode string, key string) map[string]interface{}
	//
	// GetApplyState
	//  @Description: 入网状态查询
	//  @param umsRegId
	//  @return interface{}
	//
	GetApplyState(umsRegId string) map[string]interface{}

	//
	// AlterSign
	//  @Description: 变更签约
	//  @return interface{}
	//
	AlterSign() interface{}
	//
	// AlterQueryState
	//  @Description: 变更查询
	//  @return interface{}
	//
	AlterQueryState() interface{}

	//
	// UpdateMerchantState
	//  @Description: 修改状态
	//  @param resId
	//  @param state
	//
	UpdateMerchantState(resId int, state int, pageName string, remark string)

	//
	// SaveAppliedInfo
	//  @Description: 存储审核通过后的数据
	//  @param info
	//
	SaveAppliedInfo(info map[string]interface{})

	//
	// ArchiveInfo
	//  @Description: 归档审核通过后的信息
	//  @param resId
	//
	ArchiveInfo(resId int)

	//
	// GetMerchantInfoByResId
	//  @Description: 根据餐厅ID获取商家提交信息信息
	//  @param resId
	//
	GetMerchantInfoByResId(resId int) models.SelfSignMerchantInfo

	//
	// AlterAccountInfo
	//  @Description: 账户信息变更
	//  @param resId
	//
	AlterAccountInfo(resId int) error
}

//
// SelfSignFactory
//  @Description:
//  @author: Alimjan
//  @Time: 2023-01-18 13:24:19
//  @param thirdPartyType string
//  @return SelfSignInterface
//
func SelfSignFactory(thirdPartyType string) SelfSignInterface {
	if thirdPartyType == "ums" {
		return new(UmsSelfSignService)
	} else {
		return new(WechatSelfSignService)
	}
}
