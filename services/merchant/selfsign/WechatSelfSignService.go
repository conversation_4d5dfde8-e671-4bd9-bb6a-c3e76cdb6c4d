package selfsign

import (
	"mulazim-api/models"
)

type WechatSelfSignService struct {
	BaseSelfSignService
}

func (w WechatSelfSignService) SaveAppliedInfo(info map[string]interface{}) {

	panic("implement me")
}

func (w WechatSelfSignService) ArchiveInfo(resId int) {

	panic("implement me")
}

func (w WechatSelfSignService) UploadDoc(resId int64) error {

	panic("implement me")
}

func (w WechatSelfSignService) UploadImages(merchantInfo models.SelfSignMerchantInfo) (error, []map[string]string) {

	panic("implement me")
}

func (w WechatSelfSignService) UploadResInfo(info models.SelfSignMerchantInfo, picList []map[string]string) error {

	panic("implement me")
}

func (w WechatSelfSignService) ImageUploadToUms(imageFullPath string) map[string]interface{} {

	panic("implement me")
}

func (w WechatSelfSignService) RequestAccountVerify(umsRegId string, companyAccount string) map[string]interface{} {

	panic("implement me")
}

func (w WechatSelfSignService) CompanyAccountVerify(umsRegId string, transAmt string, companyAccount string) map[string]interface{} {

	panic("implement me")
}

func (w WechatSelfSignService) GetAgreementSign(umsRegId string) map[string]interface{} {

	panic("implement me")
}

func (w WechatSelfSignService) GetBankList(areaCode string, key string) map[string]interface{} {

	panic("implement me")
}

func (w WechatSelfSignService) GetApplyState(umsRegId string) map[string]interface{} {

	panic("implement me")
}

func (w WechatSelfSignService) AlterAccountInfo(resId int) error {

	panic("implement me")
}

func (w WechatSelfSignService) AlterSign() interface{} {

	panic("implement me")
}

func (w WechatSelfSignService) AlterQueryState() interface{} {

	panic("implement me")
}
