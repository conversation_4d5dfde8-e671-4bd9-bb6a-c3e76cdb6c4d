package selfsign

import (
	"encoding/json"
	"errors"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/models"
	"mulazim-api/tools"
	"net/url"
	"strings"
	"time"

	"github.com/golang-module/carbon/v2"
)

type UmsSelfSignService struct {
	BaseSelfSignService
}

func (s *UmsSelfSignService) GetBankList(areaCode string, key string) map[string]interface{} {
	// 组成银联商务参数
	params := make(map[string]interface{})
	// 配置信息
	params["service"] = configs.UmsConfig.Services.BranchBankListService
	params["accesser_user_id"] = tools.RandStr(16)
	params["areaCode"] = areaCode
	params["key"] = key
	postUrl := configs.UmsConfig.Urls.InterfaceURL
	res := s.umsPostForm(postUrl, params)
	return res
}

func (s *UmsSelfSignService) AlterSign() interface{} {

	panic("implement me")
}

func (s *UmsSelfSignService) AlterQueryState() interface{} {

	panic("implement me")
}

// UploadDoc
//
//	@Description: 上传文档
//	@author: Alimjan
//	@Time: 2023-01-18 13:28:22
//	@receiver s *UmsSelfSignService
//	@param resId int64
//	@return error
func (s *UmsSelfSignService) UploadDoc(resId int64) error {
	db := tools.Db
	//  查询餐厅信息
	var merchantInfo models.SelfSignMerchantInfo
	db.Model(merchantInfo).Where("restaurant_id = ?", resId).Scan(&merchantInfo)
	if merchantInfo.State == 0 ||
		merchantInfo.State == 1 ||
		merchantInfo.State == 2 ||
		merchantInfo.State == 5 ||
		merchantInfo.State == 7 ||
		merchantInfo.State == 8 ||
		merchantInfo.State == 9 ||
		merchantInfo.State == 10 {
		return errors.New("该商家信息没有后台审核通过，或者未找到对应信息")
	}

	// 待提交资料
	s.UpdateMerchantState(merchantInfo.RestaurantId, 4, "银商上传资料步骤", "开始银联商务提交商户资料")
	var area models.Area
	tools.Db.Model(area).Where("id", merchantInfo.AreaId).First(&area)
	// 上传照片
	err, imageList := s.UploadImages(merchantInfo)
	if err != nil {
		// 图片上传失败
		s.UpdateMerchantState(merchantInfo.RestaurantId, 4, "银商上传资料步骤", err.Error())
		if area.ID > 0 {
			msgContent := "【" + area.NameZh + "】【" + merchantInfo.ShopName + "】【上传图片】错误内容：" + err.Error()
			tools.SendDingDingMsg(msgContent)
		}
		return err
	}

	// 上传门店信息
	err = s.UploadResInfo(merchantInfo, imageList)
	if err != nil {
		s.UpdateMerchantState(merchantInfo.RestaurantId, 6, "银商上传资料步骤", err.Error())
		if area.ID > 0 {
			msgContent := "【" + area.NameZh + "】【" + merchantInfo.ShopName + "】【上传资料】错误内容：" + err.Error()
			tools.SendDingDingMsg(msgContent)
		}
		return err
	}
	s.UpdateMerchantState(merchantInfo.RestaurantId, 5, "银商上传资料步骤", "银联商务商户资料提交成功")
	// 发起对公账户打款验证
	if *(merchantInfo.BankAcctType) == 1 {
		db.Model(merchantInfo).Where("restaurant_id = ?", resId).Scan(&merchantInfo)
		res := s.RequestAccountVerify(merchantInfo.UmsRegId, merchantInfo.BankAcctNum)
		if res["res_code"].(string) == "0000" {
			s.UpdateMerchantState(merchantInfo.RestaurantId, 7, "发起对公账户打款验证", "发起对公账户打款验证成功")
		} else {
			s.UpdateMerchantState(merchantInfo.RestaurantId, 7, "发起对公账户打款验证", res["res_msg"].(string))
		}
	} else {
		// 状态更新到待在线签约
		s.UpdateMerchantState(merchantInfo.RestaurantId, 8, "银商上传资料步骤", "银商上传资料成功，待在线签约")
		if area.ID > 0 {
			msgContent := "【" + area.NameZh + "】【" + merchantInfo.ShopName + "】银联商务上传资料通过！"
			tools.SendDingDingMsg(msgContent)
		}
	}
	return nil
}

// UploadImages
//
//	@Description: 上传所有文档
//	@author: Alimjan
//	@Time: 2023-01-18 13:28:35
//	@receiver s *UmsSelfSignService

// @param merchantInfo models.SelfSignMerchantInfo
// @return error
// @return []map[string]string
func (s *UmsSelfSignService) UploadImages(merchantInfo models.SelfSignMerchantInfo) (error, []map[string]string) {

	//  情况1 .个体户，私人银行卡
	//  情况2 .个体户，对公
	//  情况3 .企业 对公
	////0001：法人身份证 、0011：身份证反面、 0002：商户营业执照、 0003：商户税务登记证、 0004：组织机构代码证 0005 门头照片 0007 手持身份证自拍照 0008 辅助证明材料 0013 辅助证明材料 1 0014 辅助证明材料 2 0015 室内照片 0099 其他材料 0025 银行卡正面照 0026 银行卡背面照 0032 民办非登记证 0006 开户许可证 1001 食品经营许可证

	db := tools.Db
	var SelfSignImages []models.SelfSignImages
	if merchantInfo.RegMerType == "01" && (*merchantInfo.BankAcctType) == 0 {
		fmt.Println(merchantInfo.RestaurantId)
		//  情况1 .个体户，私人银行卡
		doctType := [...]string{"0001", "0011", "0002", "0005", "0007", "0015", "0025", "0026", "0034"}
		db.Model(models.SelfSignImages{}).
			Select("t_self_sign_images.*").
			Where("restaurant_id = ?", merchantInfo.RestaurantId).Where("doc_type in (?)", doctType).Scan(&SelfSignImages)
		if len(SelfSignImages) < len(doctType) {
			return s.getImageErr(doctType[:], SelfSignImages), nil

		}
	} else if merchantInfo.RegMerType == "01" && *merchantInfo.BankAcctType == 1 {
		//情况2 .个体户，对公
		docType := [...]string{"0001", "0011", "0002", "0005", "0007", "0015", "0006", "0034"}
		db.Model(models.SelfSignImages{}).
			Select("t_self_sign_images.*").
			Where("restaurant_id = ?", merchantInfo.RestaurantId).Where("doc_type in (?)", docType).Scan(&SelfSignImages)
		if len(SelfSignImages) < len(docType) {
			return s.getImageErr(docType[:], SelfSignImages), nil

		}
	} else if merchantInfo.RegMerType == "00" {
		//情况3 .企业 对公
		doctType := [...]string{"0001", "0011", "0002", "0005", "0015", "0006", "0034"}
		db.
			Select("t_self_sign_images.*").
			Model(models.SelfSignImages{}).Where("restaurant_id = ?", merchantInfo.RestaurantId).Where("doc_type in (?)", doctType).Scan(&SelfSignImages)
		if len(SelfSignImages) < len(doctType) {
			return s.getImageErr(doctType[:], SelfSignImages), nil

		}
	} else if merchantInfo.RegMerType == "02" {
		var miniShopDocType models.SelfSignImages
		db.
			Select("t_self_sign_images.*").
			Model(models.SelfSignImages{}).Where("restaurant_id = ?", merchantInfo.RestaurantId).Where("doc_type in ('0016','0017','0018','0019','0020')").First(&miniShopDocType)
		//情况4 小微企业
		doctType := [...]string{"0016", "0001", "0011", "0005", "0007", "0015", "0034", "0025", "0026", "0021"}
		if miniShopDocType.DocType != "" {
			doctType[0] = miniShopDocType.DocType
		}
		db.
			Select("t_self_sign_images.*").
			Model(models.SelfSignImages{}).Where("restaurant_id = ?", merchantInfo.RestaurantId).Where("doc_type in (?)", doctType).Scan(&SelfSignImages)
		if len(SelfSignImages) < len(doctType) {
			return s.getImageErr(doctType[:], SelfSignImages), nil

		}
	}
	var imageList []map[string]string
	for _, item := range SelfSignImages {

		// 图片转换base64
		imageFullPath := configs.MyApp.UploadRootDir + item.MlzFilePath
		fmt.Printf("图片发送开始------%s\n", imageFullPath)
		fileExists, _ := tools.PathExists(imageFullPath)
		if !fileExists {
			fmt.Println(imageFullPath)
			return errors.New(item.DocTypeName + "图片不存在!"), nil
		}

		//需要删除
		var docTypeInterface []map[string]interface{}
		db.Table("b_self_sign_doc_type").Where("`key` = ?", item.DocType).Select("name_zh").Scan(&docTypeInterface)

		rtn := s.ImageUploadToUms(imageFullPath)
		if rtn == nil {
			return errors.New("上传失败"), nil
		}
		item.UmsFilePath = rtn["file_path"].(string)
		item.FileType = rtn["file_type"].(string)
		item.FileSize = rtn["file_size"].(string)

		//需要删除
		item.DocTypeName = docTypeInterface[0]["name_zh"].(string)
		//结束删除

		db.Model(item).Omit("id", "deleted_at").Updates(&item)
		//TODELTE
		//更新
		imageList = append(imageList, map[string]string{
			"document_name": docTypeInterface[0]["name_zh"].(string),
			"file_path":     item.UmsFilePath,
			"file_size":     item.FileSize,
			"document_type": item.DocType,
		})
	}
	return nil, imageList
}

// UploadResInfo
//
//	@Description: 上传餐厅信息
//	@author: Alimjan
//	@Time: 2023-01-18 13:28:10
//	@receiver s *UmsSelfSignService

// @param info models.SelfSignMerchantInfo
// @param picList []map[string]string
// @return error
func (s *UmsSelfSignService) UploadResInfo(info models.SelfSignMerchantInfo, picList []map[string]string) error {

	params := make(map[string]interface{})
	// 配置信息
	params["service"] = configs.UmsConfig.Services.ComplexUploadService
	params["accesser_id"] = configs.UmsConfig.AccesserID
	params["sign_type"] = configs.UmsConfig.SignType
	params["request_date"] = carbon.Now().Format("YmdHis")
	params["request_seq"] = tools.RandStr(20)
	params["accesser_user_id"] = tools.RandStr(16)

	// 必填信息(统一)
	params["reg_mer_type"] = info.RegMerType                               // 注册类型：00：企业商户 01：个人工商户 02：小微商户
	params["legal_name"] = info.LegalName                                  // 法人身份证姓名
	params["legal_idcard_no"] = info.MerIdcardNum                          // 法人身份证号
	params["legal_mobile"] = info.MerMobile                                // 法人手机号
	params["legal_card_deadline"] = info.MerIdcardEnd.Format("2006-01-02") // 法人代表证件截止日期

	params["legalmanHomeAddr"] = info.LegalmanHomeAddr // 法人家庭地址
	params["shop_name"] = info.ShopName                // 商户营业名称
	params["bank_no"] = info.BankBranchCode            // 开户行行号:所属支行查询接口返回
	params["bank_acct_type"] = info.BankAcctType       // 账户类型:0:个人账户  1:公司账户
	params["bank_acct_no"] = info.BankAcctNum          // 开户行帐号

	// // 去掉省市
	shopBusinessAddress := info.ShopBusinessAddress
	if strings.Contains(shopBusinessAddress, "地区") {
		subStr := strings.Split(shopBusinessAddress, "地区")[0]
		shopBusinessAddress = strings.Replace(shopBusinessAddress, subStr+"地区", "", 1)
	}
	if strings.Contains(shopBusinessAddress, "市") {
		subStr := strings.Split(shopBusinessAddress, "市")[0]
		shopBusinessAddress = strings.Replace(shopBusinessAddress, subStr+"市", "", 1)
	}
	if strings.Contains(shopBusinessAddress, "县") {
		subStr := strings.Split(shopBusinessAddress, "县")[0]
		shopBusinessAddress = strings.Replace(shopBusinessAddress, subStr+"县", "", 1)
	}
	// else if strings.Contains(shopBusinessAddress, "区") {
	// 	subStr := strings.Split(shopBusinessAddress, "区")[0]
	// 	shopBusinessAddress = strings.Replace(shopBusinessAddress, subStr+"区", "", 1)
	// }
	tools.Log("截取后的营业地址=>" + shopBusinessAddress)
	params["shop_addr_ext"] = shopBusinessAddress            // 开户行帐号
	params["shop_province_id"] = info.ShopBusinessProvinceID // 营业省份id
	params["shop_city_id"] = info.ShopBusinessCityID         // 营业市id
	params["shop_country_id"] = info.ShopBusinessCountryID   // 营业区id
	params["mccCode"] = info.ShopCategoryCode                //  行业类别编码
	if info.RegMerType != "02" {
		params["shop_lic"] = info.ShopLicenseNum // 社会信用统一代码/营业执照号
	}

	// 开通业务(固定)
	params["product"] = []interface{}{ // 开通业务
		map[string]string{"receipt2Line": "0", "product_id": "2"}, // 0:是否开通收支双线,0:银联卡
		map[string]string{"receipt2Line": "0", "product_id": "8"}, // 0:是否开通收支双线,0:公共支付-通用
	}
	db := tools.Db
	// 上传图片列表
	params["pic_list"] = picList
	// 条件选填参数
	if info.RegMerType == "00" {
		params["bank_acct_name"] = info.ShopName // 开户行帐号
		// 情况3 企业，对公
		params["shareholderName"] = info.ShareholderName            // 控股股东姓名
		params["shareholderCertType"] = "1"                         // 控股股东证件证件类型
		params["shareholderCertno"] = info.ShareholderIdcard        // 控股股东证件号
		params["shareholderCertExpire"] = info.ShareholderIdcardEnd // 控股股东证件有效期
		params["shareholderHomeAddr"] = info.ShareholderAddress     // 控股股东证件有效期

		var bnfList []map[string]interface{}
		db.Table("t_self_sign_bnf").
			Select("bnf_name as bnfName,bnf_idcard_num as bnfCertno,bnf_address as bnfHomeAddr,DATE_FORMAT(bnf_idcard_end,'%Y-%m-%d') as bnfCertExpire,'1' as bnfCertType").
			Where("mer_info_id=?", info.Id).
			Scan(&bnfList)
		params["bnfList"] = bnfList

	} else if info.RegMerType == "01" {
		if *info.BankAcctType == 0 {
			params["bank_acct_name"] = info.LegalName // fa
		} else {
			params["bank_acct_name"] = info.ShopName // 开户行帐号
		}
	} else if info.RegMerType == "02" { // 小微商户
		params["bank_acct_name"] = info.LegalName // 开户行帐号
		params["legal_sex"] = 1                   // 法人性别   0-未知的性别  1-男性 2-女性 5-女性改（变）为男性    6-男性改（变）为女性    9-未说明的性别
		params["having_fixed_busi_addr"] = 0      //是否有营业场所
		params["legal_occupation"] = 4            //  0-各类专业、技术人员 1-国家机关、党群组织、企事业单位的负责人 2-办事人员和有关人员 3-商业工作人员 4-服务性工作人员 5-农林牧渔劳动者 6-生产工作、运输工作和部分体力劳动者 7-不便分类的其他劳动者

	}
	postUrl := configs.UmsConfig.Urls.InterfaceURL
	jsonByte, _ := json.Marshal(params)
	jsonStr := string(jsonByte)
	tools.Log("jsonStr=>" + jsonStr)
	res := s.umsPostForm(postUrl, params)
	// 如果上传不通过返回错误提示.
	if res["res_code"] != "0000" {
		return errors.New(res["res_msg"].(string))
	}
	// 更新商家信息表中的自助签约平台流水号(ums_reg_id)
	ums_reg_id := res["ums_reg_id"].(string)
	err := db.Model(&info).UpdateColumn("ums_reg_id", ums_reg_id).Error
	if err != nil {
		return errors.New("上传资料失败，请重新上传！")
	}
	return nil
}

// getImageErr
//
//	@Description: 获取照片错误，判断具体缺少哪个照片
//	@author: Alimjan
//	@Time: 2023-01-18 13:28:50
//	@receiver s *UmsSelfSignService
//	@param docTypeInterface interface{}

// @param yinShangSignImages []models.SelfSignImages
// @return error
func (s *UmsSelfSignService) getImageErr(docTypeInterface []string, yinShangSignImages []models.SelfSignImages) error {

	docType := docTypeInterface
	errMessage := ""
	for i := 0; i < len(docType); i++ {
		has := false
		for _, image := range yinShangSignImages {
			if image.DocType == (docType)[i] {
				has = true
				break
			}
		}
		if has == false {
			errMessage += (docType)[i] + ","
		}
	}
	return errors.New("照片信息不全" + errMessage)
}

// RequestAccountVerify
//
//	@Description: 请求打款验证
//	@author: Alimjan
//	@Time: 2023-01-18 13:29:35
//	@receiver s *UmsSelfSignService
//	@param umsRegId string
//	@param companyAccount string
//	@return map[string]interface{}
func (s *UmsSelfSignService) RequestAccountVerify(umsRegId string, companyAccount string) map[string]interface{} {
	// 组成银联商务参数
	params := make(map[string]interface{})
	// 配置信息
	params["service"] = configs.UmsConfig.Services.RequestAccountVerifyService
	params["accesser_user_id"] = tools.RandStr(16)
	params["ums_reg_id"] = umsRegId
	params["company_account"] = companyAccount
	postUrl := configs.UmsConfig.Urls.InterfaceURL
	res := s.umsPostForm(postUrl, params)
	return res
}

func (s *UmsSelfSignService) umsPostForm(httpUrl string, params map[string]interface{}) map[string]interface{} {
	accesser_id := configs.UmsConfig.AccesserID
	// 平台配置信息
	params["accesser_id"] = accesser_id
	params["sign_type"] = configs.UmsConfig.SignType
	params["request_date"] = carbon.Now().Format("YmdHis")
	params["request_seq"] = tools.RandStr(20)
	jsonByte, _ := json.Marshal(params)
	jsonStr := string(jsonByte)
	signData := tools.ChinaUmsSignSHA256(jsonStr)
	jsonData := tools.ChinaUmsDES3Encrypt(jsonStr, configs.UmsConfig.Key)
	values := url.Values{
		"accesser_id": {accesser_id},
		"json_data":   {jsonData},
		"sign_data":   {signData},
	}
	res, err := tools.HttpPostForm(httpUrl, values, 5*time.Second)
	if err != nil {
		//fmt.Println(httpUrl, values)
		fmt.Printf("postForm请求失败 error: %+v", err)
	}
	//tools.Log("商家入驻响应报文：" + tools.MapToString(params))
	return res
}

// 图片上传到银联商务接口
func (s *UmsSelfSignService) ImageUploadToUms(imageFullPath string) map[string]interface{} {
	// 图片转换base64
	//imageFullPath := configs.MyApp.UploadRootDir + filePath
	//fileExists, _ := tools.PathExists(imageFullPath)
	//if !fileExists {
	//	fmt.Printf("文件不存在：" + imageFullPath)
	//}
	base64Data, _ := tools.ImageToBase64(imageFullPath)
	// 组成银联商务参数
	params := make(map[string]interface{})
	params["service"] = configs.UmsConfig.Services.PicUploadService
	params["pic_base64"] = base64Data
	postUrl := configs.UmsConfig.Urls.InterfaceURL
	res := s.umsPostForm(postUrl, params)
	return res
}

// CompanyAccountVerify
//
//	@Description: 验证对公账户
//	@author: Alimjan
//	@Time: 2023-01-18 13:37:06
//	@receiver s *UmsSelfSignService
//	@param umsRegId string
//	@param transAmt string
//	@param companyAccount string
//	@return interface{}
func (s *UmsSelfSignService) CompanyAccountVerify(umsRegId string, transAmt string, companyAccount string) map[string]interface{} {

	// 组成银联商务参数
	params := make(map[string]interface{})
	// 配置信息
	params["service"] = configs.UmsConfig.Services.CompanyAccountVerifyService
	params["accesser_user_id"] = tools.RandStr(16)
	params["ums_reg_id"] = umsRegId
	params["trans_amt"] = transAmt
	params["company_account"] = companyAccount
	postUrl := configs.UmsConfig.Urls.InterfaceURL
	res := s.umsPostForm(postUrl, params)
	return res
}

// GetAgreementSign
//
//	@Description: 银商获取电签页面地址接口
//	@author: Alimjan
//	@Time: 2023-01-18 13:39:47
//	@receiver s *UmsSelfSignService
//	@param c *gin.Context
//	@return interface{}
func (s *UmsSelfSignService) GetAgreementSign(umsRegId string) map[string]interface{} {
	// 组成银联商务参数
	params := make(map[string]interface{})
	accesser_id := configs.UmsConfig.AccesserID
	url := configs.UmsConfig.Urls.InterfaceURL
	// 配置信息
	params["service"] = configs.UmsConfig.Services.AgreementSignService
	params["ums_reg_id"] = umsRegId
	// 平台配置信息
	params["accesser_id"] = accesser_id
	params["sign_type"] = configs.UmsConfig.SignType
	params["request_date"] = carbon.Now().Format("YmdHis")
	params["request_seq"] = tools.RandStr(20)
	jsonByte, _ := json.Marshal(params)
	jsonStr := string(jsonByte)
	signData := tools.ChinaUmsSignSHA256(jsonStr)
	jsonData := tools.ChinaUmsDES3Encrypt(jsonStr, configs.UmsConfig.Key)
	url = url + "?accesser_id=" + accesser_id + "&json_data=" + jsonData + "&sign_data=" + signData
	tools.Log("agrementSignUrl=>" + url)
	res := map[string]interface{}{
		"url": url,
	}
	return res
}

// GetApplyState
//
//	@Description: 入网状态查询
//	@author: Alimjan
//	@Time: 2023-01-18 13:45:12
//	@receiver s *UmsSelfSignService
//	@param umsRegId string
//	@return interface{}
func (s *UmsSelfSignService) GetApplyState(umsRegId string) map[string]interface{} {
	// 组成银联商务参数
	params := make(map[string]interface{})
	// 配置信息
	params["service"] = configs.UmsConfig.Services.ApplyQryService
	params["accesser_user_id"] = tools.RandStr(16)
	params["ums_reg_id"] = umsRegId
	postUrl := configs.UmsConfig.Urls.InterfaceURL
	res := s.umsPostForm(postUrl, params)
	return res
}

// SaveAppliedInfo
//
//	@Description: 存储审核通过后的数据
//	@param info
func (s *UmsSelfSignService) SaveAppliedInfo(info map[string]interface{}) {

	//panic("implement me")
	//todelete
	db := tools.Db
	var tmpMerchantInfo map[string]interface{}
	db.Table("t_self_sign_merchant_info").Where("ums_reg_id = ?", info["ums_reg_id"]).Scan(&tmpMerchantInfo)
	strJson, _ := json.Marshal(info)
	db.Table("t_self_sign_merchant_info").Where("ums_reg_id = ?", info["ums_reg_id"]).UpdateColumns(&map[string]interface{}{
		"ums_mer_no":           info["mer_no"],
		"ums_company_no":       info["company_no"],
		"ums_review_content":   string(strJson),
		"ums_term_app_no_list": info["term_app_no_list"],
	})

}

// ArchiveInfo
//
//	@Description: 归档审核通过后的信息
//	@author: Alimjan
//	@Time: 2023-01-18 16:46:05
//	@receiver s *UmsSelfSignService
//	@param resId int
func (s *UmsSelfSignService) ArchiveInfo(resId int) {

	var tmpMerchantInfo map[string]interface{}
	db := tools.Db
	db.Table("t_self_sign_merchant_info").Where("restaurant_id = ?", resId).Where("type = ?",constants.SELF_SIGN_TYPE_RESTAURANT).Scan(&tmpMerchantInfo)
	infoId := tmpMerchantInfo["id"]
	delete(tmpMerchantInfo, "id")
	var tmpImages []map[string]interface{}
	db.Table("t_self_sign_images").Where("restaurant_id = ?", tmpMerchantInfo["restaurant_id"]).Scan(&tmpImages)
	var tmpBnfs []map[string]interface{}

	db.Table("t_self_sign_bnf").Where("mer_info_id = ?", infoId).Scan(&tmpBnfs)

	tmpMerchantInfo["ums_review_imgs"], _ = json.Marshal(tmpImages)
	tmpMerchantInfo["ums_review_bnfs"], _ = json.Marshal(tmpBnfs)

	db.Table("t_self_sign_merchant_info_archive").Where("restaurant_id = ?", tmpMerchantInfo["restaurant_id"]).UpdateColumns(&map[string]interface{}{

		"enable": 0,
	})
	tmpMerchantInfo["enable"] = 1
	db.Table("t_self_sign_merchant_info_archive").Create(&tmpMerchantInfo)
}

// AlterAccountInfo
//
//	@Description: 变更账户信息
//	@author: Alimjan
//	@Time: 2023-01-18 16:46:05
//	@receiver s *UmsSelfSignService
//	@param resId int
func (s *UmsSelfSignService) AlterAccountInfo(resId int) error {
	db := tools.Db
	//  查询餐厅信息
	var merchantInfo models.SelfSignMerchantInfo
	db.Model(merchantInfo).Where("restaurant_id = ?", resId).First(&merchantInfo)
	if merchantInfo.State == 0 ||
		merchantInfo.State == 1 ||
		merchantInfo.State == 2 ||
		merchantInfo.State == 5 ||
		merchantInfo.State == 7 ||
		merchantInfo.State == 8 ||
		merchantInfo.State == 9 ||
		merchantInfo.State == 10 {
		return errors.New("该商家信息没有后台审核通过，或者未找到对应信息")
	}
	s.UpdateMerchantState(merchantInfo.RestaurantId, 4, "变更账户信息", "变更账户信息")

	// 需要上传信息：商户号，变更后开户行账户，变更后开户行行号，上传图片列表

	// 上传照片
	err, imageList := s.UploadAlterImages(merchantInfo, 1)
	if err != nil {
		return err
	}

	// 上传变更信息
	err = s.UploadAccountInfo(merchantInfo, imageList)
	if err != nil {
		s.UpdateMerchantState(merchantInfo.RestaurantId, 6, "变更账户信息", "变更账户信息")
		return err
	}
	s.UpdateMerchantState(merchantInfo.RestaurantId, 5, "变更账户信息", "变更账户信息")
	if *(merchantInfo.BankAcctType) == 1 {
		db.Model(merchantInfo).Where("restaurant_id = ?", resId).Scan(&merchantInfo)
		res := s.RequestAccountVerify(merchantInfo.UmsRegId, merchantInfo.BankAcctNum)
		if res["res_code"].(string) == "0000" {
			s.UpdateMerchantState(merchantInfo.RestaurantId, 7, "变更账户信息", "变更账户信息")
		}
	} else {
		s.UpdateMerchantState(merchantInfo.RestaurantId, 8, "变更账户信息", "变更账户信息")
	}
	return nil
}

func (s *UmsSelfSignService) UploadAlterImages(merchantInfo models.SelfSignMerchantInfo, alterType int) (error, []map[string]string) {
	//  情况1 账户信息（alterType=1）
	//  情况2 商户+法人（alterType=2）
	db := tools.Db
	var yinShangSignImages []models.SelfSignImages
	if alterType == 1 {
		//  个人账户，对公账户
		if (*merchantInfo.BankAcctType) == 0 {
			docType := [...]string{"0025", "0026"}
			db.Model(models.SelfSignImages{}).
				Select("t_self_sign_images.*").
				Where("restaurant_id = ?", merchantInfo.RestaurantId).Where("doc_type in (?)", docType).Scan(&yinShangSignImages)
			if len(yinShangSignImages) < len(docType) {
				return s.getImageErr(docType[:], yinShangSignImages), nil
			}
		} else {
			docType := [...]string{"0006"}
			db.Model(models.SelfSignImages{}).
				Select("t_self_sign_images.*").
				Where("restaurant_id = ?", merchantInfo.RestaurantId).Where("doc_type in (?)", docType).Scan(&yinShangSignImages)
			if len(yinShangSignImages) < len(docType) {
				fmt.Println(len(yinShangSignImages), len(docType))
				return s.getImageErr(docType[:], yinShangSignImages), nil
			}
		}
	} else if alterType == 2 {
		// 营业执照+法人
		docType := [...]string{"0001", "0011", "0002"}

		db.Model(models.SelfSignImages{}).
			Select("t_self_sign_images.*").
			Where("restaurant_id = ?", merchantInfo.RestaurantId).Where("doc_type in (?)", docType).Scan(&yinShangSignImages)
		if len(yinShangSignImages) < len(docType) {
			return s.getImageErr(docType[:], yinShangSignImages), nil
		}
	}
	var imageList []map[string]string
	for _, item := range yinShangSignImages {
		// 图片转换base64
		imageFullPath := configs.MyApp.UploadRootDir + item.MlzFilePath
		fileExists, _ := tools.PathExists(imageFullPath)
		if !fileExists {
			fmt.Println(imageFullPath)
			return errors.New(item.DocTypeName + "图片不存在!"), nil
		}

		//需要删除
		var docTypeInterface []map[string]interface{}
		db.Table("b_self_sign_doc_type").Where("`key` = ?", item.DocType).Select("name_zh").Scan(&docTypeInterface)

		rtn := s.ImageUploadToUms(imageFullPath)
		if rtn["res_code"] != "0000" {
			return errors.New(rtn["res_msg"].(string)), nil
		}
		item.UmsFilePath = rtn["file_path"].(string)
		item.FileType = rtn["file_type"].(string)
		item.FileSize = rtn["file_size"].(string)

		//需要删除
		item.DocTypeName = docTypeInterface[0]["name_zh"].(string)
		//结束删除

		db.Model(item).Omit("id", "deleted_at").Updates(&item)
		//TODELTE
		//更新
		imageList = append(imageList, map[string]string{
			"document_name": docTypeInterface[0]["name_zh"].(string),
			"file_path":     item.UmsFilePath,
			"file_size":     item.FileSize,
			"document_type": item.DocType,
		})
	}
	return nil, imageList
}

// UploadResInfo
//
//	@Description: 上传账户信息
//	@author: Alimjan
//	@Time: 2023-01-18 13:28:10
//	@receiver s *UmsSelfSignService

// @param info models.SelfSignMerchantInfo
// @param picList []map[string]string
// @return error
func (s *UmsSelfSignService) UploadAccountInfo(info models.SelfSignMerchantInfo, picList []map[string]string) error {

	db := tools.Db
	params := make(map[string]interface{})
	// 配置信息
	params["service"] = configs.UmsConfig.Services.ComplexAlterAcctInfo
	params["mer_no"] = info.UmsMerNo
	// 账户信息：商户号，变更后开户行账户，变更后开户行行号，上传图片列表
	params["alter_bank_no"] = info.BankBranchCode   // 开户行行号:所属支行查询接口返回
	params["alter_bank_acct_no"] = info.BankAcctNum // 开户行帐号
	params["pic_list"] = picList
	// 发送
	postUrl := configs.UmsConfig.Urls.InterfaceURL
	res := s.umsPostForm(postUrl, params)
	// 如果上传不通过返回错误提示.
	if res["res_code"] != "0000" {
		return errors.New(res["res_msg"].(string))
	}
	// 更新商家信息表中的自助签约平台流水号(ums_reg_id)
	ums_reg_id := res["ums_reg_id"].(string)
	err := db.Model(&info).UpdateColumn("ums_reg_id", ums_reg_id).Error
	if err != nil {
		return errors.New("上传资料失败，请重新上传！")
	}
	return nil
}

// UploadResInfo
//
//	@Description: 上传商户和法人变更信息
//	@author: Alimjan
//	@Time: 2023-01-18 13:28:10
//	@receiver s *UmsSelfSignService

// @param info models.SelfSignMerchantInfo
// @param picList []map[string]string
// @return error
func (s *UmsSelfSignService) UploadResAndLegalInfo(info models.SelfSignMerchantInfo, picList []map[string]string) error {

	db := tools.Db
	params := make(map[string]interface{})
	// 配置信息
	params["service"] = configs.UmsConfig.Services.ComplexAlterAcctInfo
	params["mer_no"] = info.UmsMerNo
	// 商户信息：商户号，商户证照图片列表，商户证件有效期，商户注册名称，商户营业所在省，商户营业所在市，商户营业所在区，商户详细营业地址，

	//params["merchantCardPics"] = picList // 商户证照图片列表
	params["shop_name"] = info.ShopName // 商户营业名称
	params["shop_addr_ext"] = info.ShopBusinessAddress
	params["shop_province_id"] = info.ShopBusinessProvinceID // 营业省份id
	params["shop_city_id"] = info.ShopBusinessCityID         // 营业市id
	params["shop_country_id"] = info.ShopBusinessCountryID   // 营业区id
	params["mccCode"] = info.ShopCategoryCode                //  行业类别编码
	params["shop_lic"] = info.ShopLicenseNum                 // 社会信用统一代码/营业执照号

	// 法人信息: 法人证照图片列表，法人证件有效期，法人证件类型，法人姓名，法人证件号码

	params["legalmanCardPics"] = picList                                      // 商户证照图片列表
	params["legal_name"] = info.LegalName                                     // 法人身份证姓名
	params["legal_idcard_no"] = info.MerIdcardNum                             // 法人身份证号
	params["legalmanCardType"] = "1"                                          // 法人身份证号
	params["legalmanCardExpireDate"] = info.MerIdcardEnd.Format("2006-01-02") // 法人代表证件截止日期

	postUrl := configs.UmsConfig.Urls.InterfaceURL
	res := s.umsPostForm(postUrl, params)
	// 如果上传不通过返回错误提示.
	if res["res_code"] != "0000" {
		return errors.New(res["res_msg"].(string))
	}
	// 更新商家信息表中的自助签约平台流水号(ums_reg_id)
	ums_reg_id := res["ums_reg_id"].(string)
	err := db.Model(&info).UpdateColumn("ums_reg_id", ums_reg_id).Error
	if err != nil {
		return errors.New("上传资料失败，请重新上传！")
	}
	return nil
}
