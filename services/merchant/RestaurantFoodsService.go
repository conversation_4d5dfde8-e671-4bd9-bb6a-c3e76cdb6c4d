package merchant

import (
	"errors"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type RestaurantFoodsService struct {
	langUtil *lang.LangUtil
	language string
}

func NewRestaurantFoodsService(c *gin.Context) *RestaurantFoodsService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	restaurantFoods := RestaurantFoodsService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &restaurantFoods
}

// GetRestaurantFoodsByCategory
//
// @Description: 根据美食分类获取美食列表
// @Author: Rixat
// @Time: 2024-06-21 16:41:29
// @receiver 
// @param c *gin.Context
func (s *RestaurantFoodsService) GetRestaurantFoodsByCategory(restaurantId int, foodsCategoryId int) []models.RestaurantFoodsHasPreferential {
	db := tools.GetDB()
	restaurantFoods := []models.RestaurantFoodsHasPreferential{}
	// var allFoodsId []int
	// db.Model(models.AllFoods{}).Select("id").Where("type=?",foodsCategoryId).Scan(&allFoodsId)

	startDateTimeStart := carbon.Now().Format("Y-m-d")+" 00:00:00"
	now := carbon.Now().Format("Y-m-d H:i:s")
	db.Model(restaurantFoods).
		Joins("left join t_foods_group on t_foods_group.id = t_restaurant_foods.foods_group_id").
		Where("t_restaurant_foods.restaurant_id=?",restaurantId).Where("t_restaurant_foods.foods_group_id = ? ",foodsCategoryId).
		Where("t_restaurant_foods.deleted_at is null").
		Preload("FoodsPreferential","state=1 and start_date_time <= ? and end_date_time >= ? and start_time < ? and end_time > ?",startDateTimeStart,startDateTimeStart,now,now).
		Order(`case 
     when (t_restaurant_foods.state=1) then 3  
     when (t_restaurant_foods.state=2) then 2 
     when (t_restaurant_foods.state=3) then 1 
     else 0 end desc,t_foods_group.weight,t_restaurant_foods.weight_in_group,t_restaurant_foods.weight asc,t_restaurant_foods.id asc`).
		Find(&restaurantFoods)
	return restaurantFoods
}

// GetFoodsCategoryByRestaurant
//
// @Description: 根据餐厅获取分类列表
// @Author: Rixat
// @Time: 2024-06-21 16:41:55
// @receiver 
// @param c *gin.Context
func (s *RestaurantFoodsService) GetFoodsCategoryByRestaurant(restaurantId int) []map[string]interface{} {
	var foodsGroup []models.FoodsGroup
	tools.GetDB().Model(foodsGroup).Where("restaurant_id=? and state = 1 and review_state = 2", restaurantId).Order("weight").Find(&foodsGroup)
	res := make([]map[string]interface{},0)
	for _, v := range foodsGroup {
		res = append(res,map[string]interface{}{
			"id": v.ID,
			"name": tools.GetNameByLang(v,s.language),
			"state": v.State,
		})
	}
	return res
}

// GetRestaurantFoodInfoById
//
// @Description: 根据ID获取美食信息
// @Author: Rixat
// @Time: 2024-06-21 16:42:27
// @receiver 
// @param c *gin.Context
func (s *RestaurantFoodsService) GetRestaurantFoodInfoById(foodId int) (models.RestaurantFoods,error) {
	var food models.RestaurantFoods
	db := tools.GetDB()
	db.Model(food).
		Where("id=?", foodId).
		Preload("Categories").
		Preload("Restaurant").
		Preload("FoodsGroup").
		Preload("ComboFoodItems.RestaurantFood").
		Preload("ComboFoodItems.SelectedSpec.FoodSpecOptions").
		Find(&food)
	if(food.ID == 0){
		return food,errors.New("not_found")
	}
	return food,nil
}
