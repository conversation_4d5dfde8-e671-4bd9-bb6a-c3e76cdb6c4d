package merchant

import (
	"errors"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/tools"
	"strings"
	"unicode"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type PermissionService struct {
	langUtil *lang.LangUtil
	language string
}

func NewPermissionService(c *gin.Context) *PermissionService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	permission := PermissionService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &permission
}

// GetStaffRules
//
// @Description: 获取角色列表
// @Author: Rixat
// @Time: 2024-05-17 16:19:09
// @receiver 
// @param c *gin.Context
func (s PermissionService) GetStaffRules(admin models.Admin) []map[string]interface{} {
	restaurant := admin.GetAdminRestaurant()
	var roleMap []map[string]interface{}
	var adminIds []int
	tools.Db.Model(&models.AdminStore{}).Select("admin_id").Where("store_id = ?", restaurant.ID).Scan(&adminIds)
	subMerchantsTypes := make([]map[string]interface{}, 0)
	tools.Db.Model(&models.Admin{}).Select("merchant_type,count(id) as count").Where("id in (?)", adminIds).Order("merchant_type").Group("merchant_type").Scan(&subMerchantsTypes)
	ruleNames := s.langUtil.TArr("merchant_type_name")
	for key, value := range ruleNames {
		roleMap = append(roleMap, map[string]interface{}{
			"type":  key,
			"name":  value,
			"count": s.GetTypeCount(key, subMerchantsTypes),
		})
	}
    return roleMap
}
// 每个角色员工数量
func (s PermissionService) GetTypeCount(merchantType int, subMerchantsTypes []map[string]interface{}) int {
	for _, value := range subMerchantsTypes {
		if tools.ToInt(value["merchant_type"]) == merchantType {
			return tools.ToInt(value["count"])
		}
	}
	return 0
}

// GetPermissions
//
// @Description: 获取所有权限(二级权限)
// @Author: Rixat
// @Time: 2024-05-17 16:22:59
// @receiver 
// @param c *gin.Context
func (s PermissionService) GetPermissions() []map[string]interface{}{
	permissions := make([]map[string]interface{}, 0)
	merchantPermissions := make([]string, 0)
	var permissionCategory []models.MerchantPermissionCategory
	tools.Db.Model(permissionCategory).Where("pid=0").Preload("SubPermissions").Find(&permissionCategory)
	for _, value := range permissionCategory {
		permissions = append(permissions, map[string]interface{}{
			"id":          value.ID,
			"name":        tools.If(s.language=="ug",value.NameUg,value.NameZh),
			"permission":  s.FormatPermission(value.SubPermissions,merchantPermissions),
		})
	}
	return permissions
}
// 格式化权限
func  (s PermissionService) FormatPermission(permissions []models.MerchantPermissionCategory,merchantPermissions []string) []map[string]interface{} {
	res := make([]map[string]interface{}, 0)
	for _, perm := range permissions {
		hasPermission := 0
		if tools.InArray(perm.Name, merchantPermissions){
			hasPermission = 1
		}
		res = append(res, map[string]interface{}{
			"id":    perm.ID,
			"name":  tools.If(s.language=="ug",perm.NameUg,perm.NameZh),
			"state": hasPermission,
			"permission": perm.Name,
		})
	}
	return res
}

// GetRuleTypes
//
// @Description: 获取角色类型
// @Author: Rixat
// @Time: 2024-05-25 11:09:16
// @receiver 
// @param c *gin.Context
func (s PermissionService) GetRuleTypes() []map[string]interface{}{
	var roleMap []map[string]interface{}
	ruleNames := s.langUtil.TArr("merchant_type_name")
	for key, value := range ruleNames {
		roleMap = append(roleMap, map[string]interface{}{
			"type":  key,
			"name":  value,
		})
	}
	return roleMap
}

// GetStaffsByAdmin
//
// @Description: 更具管理员获取员工
// @Author: Rixat
// @Time: 2024-05-25 11:09:36
// @receiver 
// @param c *gin.Context
func (s PermissionService) GetStaffsByAdmin(admin models.Admin,merchantType int) []models.Admin{
	restaurant := admin.GetAdminRestaurant()
	var adminIds []int
	tools.Db.Model(&models.AdminStore{}).Select("admin_id").Where("store_id = ?", restaurant.ID).Scan(&adminIds)
	var staffs []models.Admin
	if merchantType > 0 {
		tools.Db.Model(staffs).Where("id in (?)", adminIds).Where("type in(5,6) and merchant_type=? and deleted_at is NULL",merchantType).Scan(&staffs)
	}else{
		tools.Db.Model(staffs).Where("id in (?) and type in(5,6)", adminIds).Where("deleted_at is NULL").Scan(&staffs)
	}
	return staffs
}

/// GetStaffInfo
//
// @Description: 获取员工ID
// @Author: Rixat
// @Time: 2024-05-17 16:40:02
// @receiver 
// @param c *gin.Context
func (s PermissionService) GetStaffInfo(admin models.Admin) map[string]interface{}{
	var permissionCategory []models.MerchantPermissionCategory
	permissions := make([]map[string]interface{}, 0)
	tools.Db.Model(permissionCategory).Where("pid=0").Preload("SubPermissions").Find(&permissionCategory)
	merchantPermissions := tools.GetMerchantPermissions(admin.ID)
	if admin.MerchantType == 1 {
		tools.GetDB().Model(permissionCategory).Select("name").Where("pid>0").Scan(&merchantPermissions)
	}
	for _, value := range permissionCategory {
		permissions = append(permissions, map[string]interface{}{
			"id":          value.ID,
			"name":        tools.If(s.language=="ug",value.NameUg,value.NameZh),
			"permissions": s.FormatPermission(value.SubPermissions,merchantPermissions),
		})
	}
	// 格式化结果
	typeNames := s.langUtil.TArr("merchant_type_name")
	res := map[string]interface{}{
		"info": map[string]interface{}{
			"id":        admin.ID,
			"type_name": typeNames[admin.MerchantType],
			"name": admin.Name,
			"real_name": admin.RealName,
			"mobile":    admin.Mobile,
			"state":     admin.State,
			"type":     admin.MerchantType,
			"created_at": tools.TimeFormatYmdHis(&admin.CreatedAt),
		},
		"permissions": permissions,
	}
	return res
}


// CheckAdminNameAndMobile
//
// @Description: 验证员工姓名和手机号是否已存在
// @Author: Rixat
// @Time: 2024-05-17 16:44:32
// @receiver 
// @param c *gin.Context
func (s PermissionService) CheckAdminNameAndMobile(name string,mobile string) models.Admin{
	var admin models.Admin
	if len(name) == 0  && len(mobile) == 0{
		return admin
	}
	query := tools.Db.Model(models.Admin{})
	if len(name) > 0 {
		query.Where("name",name)
	}
	if len(mobile) > 0 {
		query.Or("mobile",mobile)
	}
	query.First(&admin)
	return admin
}

/// 创建员工
//
// @Description: StaffCreate
// @Author: Rixat
// @Time: 2024-05-17 16:49:36
// @receiver 
// @param c *gin.Context
func (s PermissionService) StaffCreate(c *gin.Context,merchantType int,name string,mobile string,password string,permission string) error {
	parentAdmin := permissions.GetAdmin(c)
	// 创建员工
	admin := models.Admin{
		MerchantType: merchantType,
        Type:         6,  // （OWNER:1,ADMIN:2,DEALER:3,DEALER_SUB:4,RESTAURANT_ADMIN:5,RESTAURANT_ADMIN_SUB:6,CENTURION:7,SHIPPER_ADMIN:8,SHIPPER:9）
		AdminCityID: parentAdmin.AdminCityID,
		AdminAreaID: parentAdmin.AdminAreaID,
		ParentID: parentAdmin.GetParentId(),

        Name:         name,
        Mobile:       mobile,
        Password:      tools.MakeMd5(password),
        State:        1,
	}
	err := tools.Db.Model(&models.Admin{}).Omit("area_name","deleted_at","login_time","last_comment_readed").Create(&admin).Error
	if err != nil {
		return err
	}
	// admin_store
	restaurant := parentAdmin.GetAdminRestaurant()
	adminStore := models.AdminStore{
        AdminID: admin.ID,
        StoreID: restaurant.ID,
    }
	err = tools.Db.Model(&models.AdminStore{}).Omit("deleted_at").Create(&adminStore).Error
	if err != nil {
        return err
    }
	// 添加通用权限
	enforcer := tools.GetCasbinEnforcer()
	enforcer.AddGroupingPolicy(tools.ToString(admin.ID),"common")
	permissionNames := strings.Split(permission, ",")
	for _,name := range permissionNames{
		enforcer.AddGroupingPolicy(tools.ToString(admin.ID),name)
	}
	return nil
}

// 更新员工信息
//
// @Description: StaffUpdate
// @Author: Rixat
// @Time: 2024-05-17 16:57:45
// @receiver 
// @param c *gin.Context
func (s PermissionService) StaffUpdate(c *gin.Context,ID int,merchantType int,name string,mobile string,password string,permission string) error {

	var staff models.Admin
	tools.GetDB().Model(staff).Where("id=?",ID).First(&staff)
	if staff.ID == 0{
		return errors.New("not_found")
	}

	// 更新员工信息
	if merchantType > 0 {
		staff.MerchantType = merchantType
	}
	if len(name)>0 {
		staff.Name = name
	}
	if len(mobile) > 0 {
		staff.Mobile = mobile
	}
	if len(password) > 0 {
		staff.Password = tools.MakeMd5(password)
	}
	err := tools.GetDB().Model(&models.Admin{}).Where("id=?",staff.ID).Updates(&staff).Error
	if err != nil {
		return errors.New("fail")
	}
	
	// 创建AdminStore信息
	// restaurant := staff.GetAdminRestaurant()
	// adminStore := models.AdminStore{
    //     AdminID: staff.ID,
    //     StoreID: restaurant.ID,
    // }
	// err = tools.Db.Model(&models.AdminStore{}).Omit("deleted_at").Create(&adminStore).Error
	// if err != nil {
    //     return errors.New("fail")
    // }
	// 添加通用权限(先删除所有权限，让添加common和前端发送的权限)
	enforcer := tools.GetCasbinEnforcer()
	enforcer.RemoveFilteredGroupingPolicy(0,tools.ToString(staff.ID))
	enforcer.AddGroupingPolicy(tools.ToString(staff.ID),"common")
	permissionNames := strings.Split(permission, ",")
	for _,name := range permissionNames{
		enforcer.AddGroupingPolicy(tools.ToString(staff.ID),name)
	}
	// 清理登录token
	s.ClearMerchantToken(staff.ID)
	return nil
}

// CheckCanDelete
//
// @Description: 验证员工能否删除
// @Author: Rixat
// @Time: 2024-05-17 17:35:34
// @receiver 
// @param c *gin.Context
func (s PermissionService) CheckCanDelete(ID int) error{
	var admin models.Admin
	tools.Db.Model(models.Admin{}).Where("id",ID).Preload("Restaurants").Find(&admin)
	if admin.ID == 0{
        return errors.New("not_found");
    }
	if len(admin.Restaurants) > 0 {
		for _,value := range admin.Restaurants{
			count := 0
			tools.Db.Model(&models.OrderToday{}).Select("count(id) as count").Where("store_id = ?", value.ID).Scan(&count)
			if count > 0 {
				return errors.New("shop_have_order_enable_delete");
            }
		}
	}
	return nil
}

// DeleteStaff
//
// @Description: 删除员工
// @Author: Rixat
// @Time: 2024-05-17 17:35:21
// @receiver 
// @param c *gin.Context
func (s PermissionService) DeleteStaff(ID int) error{
	// 删除该员工已分配的权限
	enforcer := tools.GetCasbinEnforcer()
	enforcer.RemoveFilteredGroupingPolicy(0,tools.ToString(ID))
	// 删除管理员
	err := tools.Db.Model(&models.Admin{}).Where("id=?",ID).Update("deleted_at",carbon.Now()).Error
    if err != nil {
        return errors.New("fail")
    }
	// 删除admin_store
	err = tools.Db.Model(&models.AdminStore{}).Where("admin_id=?", ID).Delete(&models.AdminStore{}).Error
	if err != nil {
		return errors.New("fail")
    }
	s.ClearMerchantToken(ID)
	return nil
}

func (s PermissionService) ClearMerchantToken(merchantID int){
	var authSession []models.OauthSessions
	tools.Db.Model(&authSession).Where("owner_id",merchantID).Where("owner_type in (?,?)","merchant","shipper").Scan(&authSession)
	for _,value := range authSession{
        tools.Db.Model(&models.OauthAccessTokens{}).Where("session_id =?",value.ID).Delete(&models.OauthAccessTokens{})
        tools.Db.Model(&models.OauthSessions{}).Where("id =?",value.ID).Delete(&models.OauthSessions{})
    }
}

func (s PermissionService) ValidatePassword(password string) bool {
    if len(password) < 6 {
        return false
    }

    charCount := 0
    for _, r := range password {
        if unicode.IsLetter(r) {
            charCount++
            if charCount >= 2 {
                return true
            }
        }
    }
    return false
}