package merchant

import (
	"errors"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type FoodsGroupService struct {
	langUtil *lang.LangUtil
	language string
}


func NewFoodsGroupService(c *gin.Context) *FoodsGroupService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := FoodsGroupService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &service
}



// RestaurantList
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:49:00
//	@Description: 餐厅列表
//	@receiver s
//	@param pagination
//	@param cityId
//	@param areaId
//	@param search
//	@param sortColumns
//	@return []models.Restaurant
//	@return int64
func (s FoodsGroupService) RestaurantList(pagination tools.Pagination, cityId int, areaId int, search string, sortColumns string) ([]models.Restaurant, int64) {
	db := tools.Db
	language := s.language
	tx := db.Model(models.Restaurant{})
	if cityId != 0 {
		tx = tx.Where("city_id = ?", cityId)
	}
	if areaId != 0 {
		tx = tx.Where("area_id = ?", areaId)
	}
	if search != "" {
		tx = tx.Where("name_"+language+" LIKE ?", "%"+search+"%")
	}
	split := strings.Split(sortColumns, ",")
	for _, sort := range split {
		tx = tx.Order(sort)
	}
	var restaurants []models.Restaurant
	var total int64
	tx.Count(&total)
	tx.Scopes(scopes.Page(pagination.Page, pagination.Limit)).Find(&restaurants)
	return restaurants, total
}

// Create
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:48:37
//	@Description: 新增美食分组
//	@receiver s
//	@param c
//	@param restaurantId
//	@param nameUg
//	@param nameZh
//	@param weight
//	@return error
func (s FoodsGroupService) Create(c *gin.Context, restaurantId int, nameUg string, nameZh string, weight int) error {
	admin := permissions.GetAdmin(c)
	db := tools.Db
	now := time.Now()
	var restaurant models.Restaurant
	if err := db.Model(models.Restaurant{}).Where("id = ?", restaurantId).First(&restaurant).Error; err != nil {
		return err
	}
	err := db.Create(&models.FoodsGroup{
		RestaurantID: restaurantId,
		NameUg:       nameUg,
		NameZh:       nameZh,
		Weight:       weight,
		CreateBy:     admin.ID,
		CreatedAt:    &now,
	}).Error
	if err != nil {
		return err
	} else {
		return nil
	}
}



func (s FoodsGroupService) ValidateCreateParams(restaurantId int,id int, nameUg string, nameZh string, state int) error {
	db := tools.Db
	// 同一个餐厅下的分组名称不能重复
	var foodsGroup models.FoodsGroup
	db.Model(models.FoodsGroup{}).Where("restaurant_id =? and (name_ug=? or name_zh = ?) and id <> ?", restaurantId,nameUg,nameZh,id).First(&foodsGroup)
	if foodsGroup.ID!= 0 {
		return errors.New("name_already_exist")
	}

	return nil
	
}

// CreateGroup
//
// @Description: 创建分组
// @Author: Rixat
// @Time: 2024-09-25 10:57:20
// @receiver 
// @param c *gin.Context
func (s FoodsGroupService) CreateGroup(admin models.Admin,restaurantId int, nameUg string, nameZh string, state int) error {
	db := tools.Db
	// 查询是否已经存在基础分组表，如果有直接审核通过，如果没有需要审核
	var hasGroupCount int64
	db.Model(&models.Dictionary{}).Where("name_ug like ? and name_zh like ? and type=1", "%"+tools.FilterEmoji(nameUg)+"%","%"+tools.FilterEmoji(nameZh)+"%").Count(&hasGroupCount)
	reviewState := 1 // 待审核
	if hasGroupCount > 0 {
		reviewState = 2 // 审核通过
	}
	// 创建分组
	now := time.Now()
	err := db.Create(&models.FoodsGroup{
		RestaurantID: restaurantId,
		NameUg:       nameUg,
		NameZh:       nameZh,
		State:        state,
		ReviewState: reviewState,
		CreateBy:     admin.ID,
		CreatedAt:    &now,
	}).Error
	if err != nil {
		return err
	} 

	// 发送钉钉通知
	if reviewState == 1 {
		var restaurant  models.Restaurant
		db.Model(models.Restaurant{}).Where("id =?", restaurantId).Preload("Area").Find(&restaurant)
		SmsContent := "【" + restaurant.Area.NameZh + "】【" + "" + restaurant.NameZh + "】提交餐厅分组信息，请按时处理！"
		tools.SendDingDingMsg(SmsContent)
	}
	return nil
}


// Edit
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:48:22
//	@Description: 编辑美食分组
//	@receiver s
//	@param c
//	@param id
//	@param nameUg
//	@param nameZh
//	@param weight
//	@param state
//	@return error
func (s FoodsGroupService) Edit(admin models.Admin,id int, nameUg string, nameZh string, state int) error {
	db := tools.Db
	// 查询是否已经存在基础分组表，如果有直接审核通过，如果没有需要审核
	var hasGroupCount int64
	db.Model(&models.Dictionary{}).Where("name_ug like ? and name_zh like ? and type=1", "%"+tools.FilterEmoji(nameUg)+"%","%"+tools.FilterEmoji(nameZh)+"%").Count(&hasGroupCount)
	reviewState := 1 // 待审核
	if hasGroupCount > 0 {
		reviewState = 2 // 审核通过
	}
	// 更新
	now := carbon.Now().Carbon2Time()
	err := db.Model(models.FoodsGroup{}).Where("id = ?", id).UpdateColumns(map[string]interface{}{
		"name_ug":       nameUg,
		"name_zh":       nameZh,
		"state":       state,
		"updated_at":    &now,
		"update_by":   admin.ID,
		"review_state": reviewState,
	}).Error
	if err!= nil {
		return errors.New("fail")
	}
	// 发送钉钉通知
	if reviewState == 1 {
		var group  models.FoodsGroup
		db.Model(group).Where("id =?", id).Preload("Restaurant.Area").Find(&group)
		SmsContent := "【" + group.Restaurant.Area.NameZh + "】【" + "" + group.Restaurant.NameZh + "】提交餐厅分组信息，请按时处理！"
		tools.SendDingDingMsg(SmsContent)
	} 
	return nil
}

// Delete
//
// @Description: 删除分组
// @Author: Rixat
// @Time: 2024-09-25 12:54:04
// @receiver 
// @param c *gin.Context
func (s FoodsGroupService) Delete(id int) error {
	db := tools.Db
	var foodsGroup models.FoodsGroup
	db.Model(models.FoodsGroup{}).Where("id = ?", id).Preload("RestaurantFoods").First(&foodsGroup)
	if foodsGroup.ID == 0 {
		return errors.New("not_found")
	}
	// 分组已绑定过美食，不允许删除
	if len(foodsGroup.RestaurantFoods) > 0 {
		return errors.New("cant_delete_bind_foods_group")
	}
	// 删除分组
	if err := db.Delete(&foodsGroup).Error; err != nil {
		return errors.New("fail")
	}
	return nil
}

// List
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:47:32
//	@Description: 美食分组列表
//	@receiver s
//	@param restaurantId
//	@return []models.FoodsGroup
func (s FoodsGroupService) List(restaurantId int,state *int) []models.FoodsGroup {
	db := tools.Db
	var foodsGroups []models.FoodsGroup
	queryModel := db.Model(models.FoodsGroup{}).Where("restaurant_id = ?", restaurantId).Order("weight").Preload("RestaurantFoods")
	if state != nil {
		queryModel.Where("state =?", state)
	}
	queryModel.Find(&foodsGroups)
	return foodsGroups
}

// GetRecommendGroup
//
// @Description: 根据关键字推荐分组库中的分组名称
// @Author: Rixat
// @Time: 2024-10-09 10:03:53
// @receiver 
// @param c *gin.Context
func (s FoodsGroupService) GetRecommendGroup(kw string) []models.Dictionary {
	db := tools.Db
	var foodsBaseGroups []models.Dictionary
	queryModel := db.Model(foodsBaseGroups).Where("type = 1")
	if len(kw) > 0{
		kw = tools.FilterEmoji(kw)
		queryModel.Where("name_ug like ? or name_zh like ?","%"+kw+"%","%"+kw+"%")
	}
	queryModel.Find(&foodsBaseGroups)
	return foodsBaseGroups
}

// 获取分组详情
//
// @Description: Detail
// @Author: Rixat
// @Time: 2024-09-25 15:33:38
// @receiver 
// @param c *gin.Context
func (s FoodsGroupService) Detail(ID int) models.FoodsGroup {
	db := tools.Db
	var foodsGroups models.FoodsGroup
	db.Model(models.FoodsGroup{}).Where("id = ?", ID).Find(&foodsGroups)
	return foodsGroups
}

// FoodsListByGroupId
//
// @Description: 根据分组ID获取美食列表
// @Author: Rixat
// @Time: 2024-09-25 13:50:05
// @receiver 
// @param c *gin.Context
func (s FoodsGroupService) FoodsListByGroupId(restaurantID int, groupID int, kw string, pagination tools.Pagination,
	foodTypes []int) ([]models.RestaurantFoodsHasPreferential, int64) {
	var totalCount int64
	var foods []models.RestaurantFoodsHasPreferential
	queryFoods := tools.GetDB().Model(foods).
		Joins("left join t_foods_group on t_foods_group.id = t_restaurant_foods.foods_group_id").
		Where("t_restaurant_foods.restaurant_id = ? and t_restaurant_foods.deleted_at is null",restaurantID)
	if groupID > 0 {
		queryFoods = queryFoods.Where("t_restaurant_foods.foods_group_id =?", groupID)
	}
	if len(kw) > 0 {
		queryFoods = queryFoods.Where("(t_restaurant_foods.name_ug like ? or t_restaurant_foods.name_ug like ?)","%"+kw+"%","%"+kw+"%")
	}
	if foodTypes != nil && len(foodTypes) > 0 {
		if len(foodTypes) == 1 {
			queryFoods.Where("t_restaurant_foods.food_type = ?", foodTypes[0])
		} else {
			queryFoods.Where("t_restaurant_foods.food_type IN (?)", foodTypes)
		}
	}
	queryFoods.Count(&totalCount)
	if totalCount > 0 {
		startDateTimeStart := carbon.Now().Format("Y-m-d")+" 00:00:00"
		now := carbon.Now().Format("Y-m-d H:i:s")
		queryFoods.Preload("FoodsPreferential","state=1 and start_date_time <= ? and end_date_time >= ? and start_time < ? and end_time > ?",startDateTimeStart,startDateTimeStart,now,now).
		Order(`case 
		when (t_restaurant_foods.state=1) then 3  
		when (t_restaurant_foods.state=2) then 2 
		when (t_restaurant_foods.state=3) then 1 
		else 0 end desc,t_foods_group.weight,t_restaurant_foods.weight_in_group,t_restaurant_foods.weight asc,t_restaurant_foods.id asc`)
		queryFoods.Scopes(scopes.Page(pagination.Page,pagination.Limit)).Find(&foods)
	}
	return foods,totalCount
}


func (s FoodsGroupService) EditFoodsGroupWeights(restaurantId int, weights []models.FoodsGroup) error {
	db := tools.Db
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	for index, weight := range weights {
		newWeight := index
		err := tx.Model(&models.FoodsGroup{}).
			Where("id = ?", weight.ID).
			Where("restaurant_id = ?", restaurantId).
			Update("weight",newWeight).Error
		if err != nil {
			tx.Rollback()
			return errors.New("update_fail")

		}
	}
	tx.Commit()
	return nil
}
