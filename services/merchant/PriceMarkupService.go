package merchant

import (
	"errors"
	"fmt"
	"math/rand"
	"mulazim-api/factory"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type PriceMarkupService struct {
	langUtil *lang.LangUtil
	language string
}


func NewPriceMarkupService(c *gin.Context) *PriceMarkupService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	priceMarkupService := PriceMarkupService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &priceMarkupService
}

// HomeShowList
//
//  @Author: YaKupJan
//  @Date: 2024-10-22 19:15:28
//  @Description: 首页显示加价信息列表
//  @receiver s
//  @param restaurantId
//  @return []models.PriceMarkupFood
//  @return int64
func (s PriceMarkupService) HomeShowList(restaurantId int) ([]models.PriceMarkupFood, int64) {
	// 获取db 实例
	db := tools.GetDB()
	var priceMarkup []models.PriceMarkupFood
	var total int64
	// 查询餐厅待确认的美食加价列表
	db.Model(models.PriceMarkupFood{}).
		Where("restaurant_id = ?",restaurantId).
		Where("state = ?",models.PriceMarkupStateWaitForMerchantConfirm). // state == 2  商家待确认状态
		Where("running_state = ?",models.PriceMarkupRunningStateNormal). // running_state == 1
		Preload("Restaurant"). // 预加载餐厅
		Preload("RestaurantFoods.ComboFoodItems.RestaurantFood"). // 套餐子美食
		Preload("RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions"). // 套餐子美食，以及子美食已选规格
		Preload("SelectedSpec.FoodSpecOptions"). // 已选规格
		Order("created_at desc").
		Count(&total).// 计数
		Find(&priceMarkup)
	return priceMarkup, total


}

// Handle
//
//  @Author: YaKupJan
//  @Date: 2024-10-23 12:43:41
//  @Description: 商家处理加价活动
//  @receiver s
//  @param id
//  @param agree
//  @param restaurantId
//  @return error
func (s PriceMarkupService) Handle(id int, agree int, restaurantId int) error {
	db := tools.GetDB()
	// 查询加价活动
	var priceMarkupFood models.PriceMarkupFood
	// 查询餐厅的加价活动 加上RestaurantId 以防其他Restaurant修改
	err := db.Model(models.PriceMarkupFood{}).
		Where("id = ?", id).
		Where("restaurant_id = ?",restaurantId).
		First(&priceMarkupFood).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("data_not_found")
		}
		return err // 返回其他类型的错误
	}
	// 根据agree值更新状态
	state := 0
	switch agree {
	case 1:
		state = models.PriceMarkupStateMerchantHasConfirmed // 商家已确认，不可修改
	case 2:
		state = models.PriceMarkupStateMerchantRefuse // 商家拒绝
	default:
		return errors.New("data_not_found")
	}
	// 更新状态
	err = db.Model(&priceMarkupFood).Updates(map[string]interface{}{
		"state":state,
		"review_at":time.Now(),
	}).Error
	if err != nil {
		return err
	}
	return nil
}


// SendSMSCode
//
//  @Author: YaKupJan
//  @Date: 2024-10-23 12:47:01
//  @Description: 加价活动发送验证码
//  @receiver s
//  @param c
//  @param mobile
//  @return error
func (s PriceMarkupService) SendSMSCode(c *gin.Context, mobile string) error{
	redisHelper := tools.GetRedisHelper()
	cacheKey := fmt.Sprintf("merchant_mobile_%s", mobile)
	//1分钟 能获取一次
	exists, _ := redisHelper.Exists(c, cacheKey).Result()
	if exists != 0 {
		expireTime := redisHelper.TTL(c,cacheKey).Val().Seconds()
		msg := fmt.Sprintf(s.langUtil.T("try_again_after_period_time"),tools.ToString(expireTime))
		return errors.New(msg)
	}
	// 生成4位随机验证码
	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))
	code := fmt.Sprintf("%04d", rnd.Int31n(9999))
	//验证码保存 1 分钟
	redisHelper.Set(c, cacheKey, code, time.Minute*5)
	//// 验证码内容
	//contentMap := map[string]string{
	//	"code": code,
	//}
	//dataType, _ := json.Marshal(contentMap)
	//// 通过阿里云短信接口发送验证码
	//err := tools.AliSmsSend(mobile, string(dataType))
	//if err != nil {
	//	tools.Logger.Error("发送验证码出错:",err)
	//	return err
	//}
	// 发送验证码
	smsServiceFactory := &factory.SmsServiceFactory{}
	smsService := smsServiceFactory.CreateSmsService()
	codeResp, err := smsService.SendVerificationCode(mobile, code)
	if err != nil {
		tools.Logger.Errorf("发送验证码失败: %v \n",err)
		return err
	}
	tools.Logger.Infof("发送验证码成功 结果: %v \n",codeResp)
	return nil
}

// DetailList
//
//  @Author: YaKupJan
//  @Date: 2024-10-24 18:12:44
//  @Description: 加价活动详细列表
//  @receiver s
//  @param restaurantId
//  @param pagination
//  @return []models.PriceMarkupFoodDetailModel
//  @return int64
func (s PriceMarkupService) DetailList(restaurantId int, pagination tools.Pagination) ([]models.PriceMarkupFood, int64) {
	db := tools.GetDB()
	var totalCount int64
	var data []models.PriceMarkupFood
	tx := db.Model(models.PriceMarkupFood{}).
		Where("restaurant_id = ?",restaurantId).
		Preload("RestaurantFoods.ComboFoodItems.RestaurantFood"). // 套餐子美食
		Preload("RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions"). // 套餐子美食，以及子美食已选规格
		Preload("SelectedSpec.FoodSpecOptions"). // 已选规格
		Preload("PriceMarkupFoodLog", func(d *gorm.DB) *gorm.DB{
			return d.Select("price_markup_id,COALESCE(SUM(saled_count), 0) AS price_markup_food_saled_total_count").
				Where("state IN ?",[]int{models.PriceMarkupFoodLogStateComplete,models.PriceMarkupFoodLogStateHavePaid}).
				Group("price_markup_id")
		}).
		Order("created_at desc")

	tx.Count(&totalCount)
	tx.Scopes(scopes.Page(pagination.Page, pagination.Limit))
	tx.Find(&data)
	return data,totalCount
}

// StatisticSailedMarket
//
//  @Author: YaKupJan
//  @Date: 2024-10-24 21:54:45
//  @Description: 统计页面显示美食销售标签
//  @receiver s
//  @param foodsId
//  @param restaurantId
func (s PriceMarkupService) StatisticSailedMarket(restaurantId int,foodsId int, startDate string, endDate string) []map[string]interface{} {
	db := tools.GetDB()
	startDateTime := startDate+" 00:00:00"
	endDateTime := endDate+" 23:59:59"
	var orderIds []int
	// 获取当前商家的订单列表
	db.Raw(`
				    SELECT DISTINCT
				  id 
				FROM
				  t_order 
				WHERE
				  store_id = ? 
				  AND state in (4,5,6,7,10) 
				  AND created_at BETWEEN ? 
				  AND ? UNION ALL
				SELECT DISTINCT
				  id 
				FROM
				  t_order_today 
				WHERE
				  store_id = ? 
				  AND state in (4,5,6,7,10)
				  AND created_at BETWEEN ?
				  AND ?
	`,restaurantId,startDateTime,endDateTime,restaurantId,startDateTime,endDateTime).Scan(&orderIds)
	// 加价活动类型 5 6 7
	priceMarkupTypes := []int{models.OrderDetailActivityTypePriceMarkupPreferential,models.OrderDetailActivityTypePriceMarkupSecKill,models.OrderDetailActivityTypePriceMarkupSpecialPrice}
	// 其他活动类型 1 2 3 4
	merchantTypes := []int{models.OrderDetailActivityTypeOriginPrice,models.OrderDetailActivityTypePreferential,models.OrderDetailActivityTypeSecKill,models.OrderDetailActivityTypeSpecialPrice}
	var priceMarkupData map[string]interface{}
	var merchantData map[string]interface{}
	//  根据订单ids  获取订单详细 计算出总价 和 总数
	db.Model(models.OrderDetail{}).
		Select(
		"COALESCE(FORMAT(CONVERT(SUM(COALESCE(price_markup_price, 0) * COALESCE(number, 0)) / 100, DECIMAL(12, 2)), 2), 0) AS sum_price",
		"COALESCE(SUM(COALESCE(number, 0)), 0) AS sum_number",
			).
		Where("order_id IN ? AND store_foods_id = ? AND created_at BETWEEN ? AND ?", orderIds, foodsId, startDate+" 00:00:00", endDate+" 23:59:59").
		Where("activity_type in ?", priceMarkupTypes).Scan(&priceMarkupData)
	db.Model(models.OrderDetail{}).
		Select(
		"COALESCE(FORMAT(CONVERT(SUM(COALESCE(price, 0) * COALESCE(number, 0)) / 100, DECIMAL(12, 2)), 2), 0) AS sum_price",
		"COALESCE(SUM(COALESCE(number, 0)), 0) AS sum_number",
			).
		Where("order_id IN ? AND store_foods_id = ? AND created_at BETWEEN ? AND ?", orderIds, foodsId, startDate+" 00:00:00", endDate+" 23:59:59").
		Where("activity_type IN ? OR activity_type IS NULL", merchantTypes).
		Scan(&merchantData)
	// 封装查询的数据
	data := []map[string]interface{}{
		{
			"type":"markup_sale",
			"type_name":s.langUtil.T("markup_sale"),
			"sum_price":priceMarkupData["sum_price"],
			"sum_number":priceMarkupData["sum_number"],
		},
		{
			"type":"merchant_sale",
			"type_name":s.langUtil.T("merchant_sale"),
			"sum_price":merchantData["sum_price"],
			"sum_number":merchantData["sum_number"],
		},
	}
	return data
}

// StatisticSailedMarketDetail
//
//  @Author: YaKupJan
//  @Date: 2024-10-25 10:36:41
//  @Description: 统计页面美食加价销售明细
//  @receiver s
//  @param foodsId
//  @param restaurantId
//  @return []models.PriceMarkupFood
func (s PriceMarkupService) StatisticSailedMarketDetail(pagination tools.Pagination, foodsId int, restaurantId int, startDate string, endDate string) ([]models.PriceMarkupFood, int64) {
	db := tools.GetDB()
	var totalCount int64

	var data []models.PriceMarkupFood
	tx := db.Model(models.PriceMarkupFood{}).
		Where("state > ?",models.PriceMarkupStateWaitForMerchantConfirm). // state > 2  大于 商家待确认状态
		Where("restaurant_foods_id = ?",foodsId).
		Where("restaurant_id = ?",restaurantId).
		Preload("RestaurantFoods").
		Preload("PriceMarkupFoodLog", func(d *gorm.DB) *gorm.DB{
			return d.Select("price_markup_id,COALESCE(SUM(saled_count), 0) AS price_markup_food_saled_total_count").
				Where("created_at BETWEEN ? AND ?", startDate+" 00:00:00", endDate+" 23:59:59").
				Where("state IN ?",[]int{models.PriceMarkupFoodLogStateComplete,models.PriceMarkupFoodLogStateHavePaid}).
				Group("price_markup_id")

	})
		tx.Count(&totalCount)
		tx.Scopes(scopes.Page(pagination.Page, pagination.Limit)). // 分页
		 // 分页计数
		Find(&data)
	return data,totalCount
}

// GetRestaurantAdminMobile
//
//  @Author: YaKupJan
//  @Date: 2024-10-25 13:25:39
//  @Description: 获取商家的老板的电话号码
//  @receiver s
//  @param admin
//  @return string
func (s PriceMarkupService) GetRestaurantAdminMobile(admin models.Admin) string {
	if admin.Type == models.AdminTypeRestaurantAdmin{
		return admin.Mobile
	}else {
		parentAdmin, err := permissions.GetAdminFirstParent(admin,models.AdminTypeRestaurantAdmin)
		if err!=nil { //如果没找到上级 就返回当前的手机号
			return admin.Mobile
		}else {
			// 如果找到了 就返回上级的手机号
			return parentAdmin.Mobile

		}
	}
}
