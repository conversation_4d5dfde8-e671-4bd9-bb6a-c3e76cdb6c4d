package services

import (
	"encoding/json"
	
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"time"

	"mulazim-api/resources/response"

	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type OrderRankService struct {
	langUtil *lang.LangUtil
	language string
}

func NewOrderRankService(c *gin.Context) *OrderRankService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	shipperService := OrderRankService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &shipperService
}

func (s OrderRankService) GetOrderRankStat(ctx *gin.Context,langUtil lang.LangUtil, senderID int64, senderType int, cityId int, areaId int, kw string, startDate string, endDate string) (response.OrderRank, error) {
	
	var rnk response.OrderRank
	startTime :=carbon.Parse(startDate, configs.AsiaShanghai)
	endTime :=carbon.Parse(endDate, configs.AsiaShanghai)

	days :=endTime.DiffAbsInDays(startTime)
	if days > 120 { 
		return rnk,fmt.Errorf(fmt.Sprintf(langUtil.T("date_range_can_not_over_x_days"),120))
	}

	now :=carbon.Now(configs.AsiaShanghai)
	daysNow :=now.DiffInDays(endTime)
	if daysNow >= 0   { 
		endDate = now.AddDays(-1).Format("Y-m-d")
	}

	dbMode :=1 //数据库 1:mysql 2:clickhouse
	cacheMode :=2 //缓存 1:没有缓存 2:使用缓存
	joinMode :=2 //是否使用 join 1:没有 2:使用join 3:不使用join 全部数据都在一个表中
	joinSql :="left join t_order on t_order.id = t_auto_dispatch_history.order_id"
	
	dbm :=ctx.DefaultQuery("db-mode","")
	chcmode :=ctx.DefaultQuery("cache-mode","")
	jm :=ctx.DefaultQuery("join-mode","")
	if dbm != "" { 
		dbMode =tools.ToInt(dbm)
	}
	if chcmode != "" { 
		cacheMode =tools.ToInt(chcmode)
	}
	if jm != "" { 
		joinMode =tools.ToInt(jm)
	}

	
	key := "order_rank_"+startDate+"_"+endDate+"_"+tools.ToString(cityId)+"_"+tools.ToString(areaId)+"_"+tools.ToString(senderID)
	cacheTime := time.Minute * 2
	

	if dbMode == 1 { 
		db1 := tools.ReadDb1
		joinSql ="inner join t_order on t_order.id = t_auto_dispatch_history.order_id"
		if joinMode == 1 { 
			if cacheMode == 1 { 
				rnk,err := s.GetOrderRankStatReal(db1,senderID, senderType, cityId, areaId, kw, startDate, endDate)
				return rnk,err	
			}else{
				var rnk response.OrderRank	
				rnkStr := tools.Remember2(key, cacheTime, func() interface{} {
					rnk,_ := s.GetOrderRankStatReal(db1,senderID, senderType, cityId, areaId, kw, startDate, endDate)
					return rnk
				})
				_ = json.Unmarshal([]byte(rnkStr), &rnk)
				return rnk,nil

			}
			
		}else if joinMode == 2 { 
			if cacheMode == 1 {
				var rnk, err = s.GetOrderRankStatJoin(db1, joinSql, senderID, senderType, cityId, areaId, kw, startDate, endDate, dbMode)
				return rnk,err	
			}else{
				var rnk response.OrderRank	
				rnkStr := tools.Remember2(key, cacheTime, func() interface{} {
					rnk,_ := s.GetOrderRankStatJoin(db1,joinSql,senderID, senderType, cityId, areaId, kw, startDate, endDate,dbMode)
					return rnk
				})
				_ = json.Unmarshal([]byte(rnkStr), &rnk)
				return rnk,nil
			}
			

		}else if joinMode == 3 { 
			if cacheMode == 1 { 
				rnk,err := s.GetOrderRankStatJoinNoJoin(db1,senderID, senderType, cityId, areaId, kw, startDate, endDate)
				return rnk,err	
			}else{
				var rnk response.OrderRank	
				rnkStr := tools.Remember2(key, cacheTime, func() interface{} {
					rnk,_ := s.GetOrderRankStatJoinNoJoin(db1,senderID, senderType, cityId, areaId, kw, startDate, endDate)
					return rnk
				})
				_ = json.Unmarshal([]byte(rnkStr), &rnk)
				return rnk,nil
			}
		}
	}else{
		db2 := tools.GetCKDB()
		joinSql ="left any join t_order on t_order.id = t_auto_dispatch_history.order_id"
		joinSql ="inner join t_order on t_order.id = t_auto_dispatch_history.order_id"
		if joinMode == 1 { 
			if cacheMode == 1 { 
				rnk,err := s.GetOrderRankStatReal(db2,senderID, senderType, cityId, areaId, kw, startDate, endDate)
				return rnk,err
			}else{
				var rnk response.OrderRank	
				rnkStr := tools.Remember2(key, cacheTime, func() interface{} {
					rnk,_ := s.GetOrderRankStatReal(db2,senderID, senderType, cityId, areaId, kw, startDate, endDate)
					return rnk
				})
				_ = json.Unmarshal([]byte(rnkStr), &rnk)
				return rnk,nil	
			}
			
		}else if joinMode == 2{
			if cacheMode == 1 { 
				rnk,err := s.GetOrderRankStatJoin(db2,joinSql,senderID, senderType, cityId, areaId, kw, startDate, endDate,dbMode)
				return rnk,err
			}else{
				var rnk response.OrderRank	
				rnkStr := tools.Remember2(key, cacheTime, func() interface{} {
					rnk,_ := s.GetOrderRankStatJoin(db2,joinSql,senderID, senderType, cityId, areaId, kw, startDate, endDate,dbMode)
					return rnk
				})
				_ = json.Unmarshal([]byte(rnkStr), &rnk)
				return rnk,nil
			}
			
		}else if joinMode == 3{
			if cacheMode == 1 { 
				rnk,err := s.GetOrderRankStatJoinNoJoin(db2,senderID, senderType, cityId, areaId, kw, startDate, endDate)	
				return rnk,err
			}else{
				var rnk response.OrderRank	
				rnkStr := tools.Remember2(key, cacheTime, func() interface{} {
					rnk,_ := s.GetOrderRankStatJoinNoJoin(db2,senderID, senderType, cityId, areaId, kw, startDate, endDate)
					return rnk
				})
				_ = json.Unmarshal([]byte(rnkStr), &rnk)
				return rnk,nil
			}
			
		}
	}
	return rnk,nil

	
}

func (s OrderRankService) GetOrderRankStatReal(ckDb *gorm.DB,senderID int64, senderType int, cityId int, areaId int, kw string, startDate string, endDate string) (response.OrderRank, error) {
	var rnk response.OrderRank	


	orderPrice :=int64(0)
	orderCount :=int64(0)
	customerCount :=int64(0)
	shipperCount :=int64(0)
	avgDeliveryTimeSum :=float64(0)
	avgDeliveryTime :=float64(0)

	orderPrice2 :=int64(0)
	orderCount2 :=int64(0)
	customerCount2 :=int64(0)
	shipperCount2 :=int64(0)
	avgDeliveryTimeSum2 :=float64(0)
	avgDeliveryTime2 :=float64(0)

	startTime :=carbon.Parse(startDate, configs.AsiaShanghai)
	endTime :=carbon.Parse(endDate, configs.AsiaShanghai)

	days :=endTime.DiffAbsInDays(startTime)
	startTime2 :=startTime.AddDays(-int(days))
	endTime2 :=endTime.AddDays(-int(days))
	var allOrderIds1 []int64
	var allOrderIds2 []int64
	
	orderQuery :=ckDb.Model(&models.Order{}).Where("area_id = ? and state =? and created_at >=? and created_at < ?",areaId,7,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"))

	orderQuery.Pluck("id",&allOrderIds1)

	var orderData response.OrderDataGorm

	orderQuery.Select("ifnull(sum(order_price),0) as order_price,count(1) as order_count,count(distinct(user_id)) as customer_count,count(distinct(shipper_id)) as shipper_count").Scan(&orderData)
	
	orderPrice = orderData.OrderPrice
	orderCount = orderData.OrderCount
	customerCount = orderData.CustomerCount
	shipperCount = orderData.ShipperCount

	orderQuery.Where("shipper_id > ?",0).Select("ifnull(avg(TIMESTAMPDIFF(SECOND, delivery_start_time, delivery_end_time)),0) as delivery_time").Pluck("delivery_time",&avgDeliveryTimeSum)

	orderQuery2 :=ckDb.Model(&models.Order{}).Where("area_id = ? and state =? and created_at >=? and created_at < ?",areaId,7,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"))

	orderQuery2.Pluck("id",&allOrderIds2)

	var orderData2 response.OrderDataGorm

	orderQuery2.Select("ifnull(sum(order_price),0) as order_price,count(1) as order_count,count(distinct(user_id)) as customer_count,count(distinct(shipper_id)) as shipper_count").Scan(&orderData2)
	

	orderQuery2.Where("shipper_id > ?",0).Select("ifnull(avg(TIMESTAMPDIFF(SECOND, delivery_start_time, delivery_end_time)),0) as delivery_time").Pluck("delivery_time",&avgDeliveryTimeSum2)
	
	orderPrice2 = orderData2.OrderPrice
	orderCount2 = orderData2.OrderCount
	customerCount2 = orderData2.CustomerCount
	shipperCount2 = orderData2.ShipperCount



	

	rnk.OrderPrice = orderPrice
	rnk.OrderCount = orderCount
	rnk.CustomerCount = customerCount
	rnk.ShipperCount = shipperCount
	if orderCount > 0 { 
		avgDeliveryTime = float64(avgDeliveryTimeSum)
	}
	rnk.AvgDeliveryTime = float64(avgDeliveryTime)

	rnk.OrderPriceGrowthRate = 100
	rnk.OrderCountGrowthRate = 100
	rnk.CustomerCountGrowthRate = 100
	rnk.ShipperCountGrowthRate = 100
	rnk.AvgDeliveryTimeGrowthRate = 100

	//升级比例 = (新等级-旧等级)/旧等级
	if orderCount2 != 0 { 
		avgDeliveryTime2 = tools.ToFloat64(float64(avgDeliveryTimeSum2),2)
		rnk.OrderCountGrowthRate=tools.ToFloat64(float64(orderCount-orderCount2)/float64(orderCount2),2)   //升级比例 = (新等级-旧等级)/旧等级
		rnk.OrderPriceGrowthRate=tools.ToFloat64(float64(orderPrice-orderPrice2)/float64(orderPrice2),2)
		rnk.AvgDeliveryTimeGrowthRate=tools.ToFloat64(float64(avgDeliveryTime-avgDeliveryTime2)/float64(avgDeliveryTime2),2)   //升级比例
	}
	if customerCount2 != 0 { 
		rnk.CustomerCountGrowthRate=tools.ToFloat64(float64(customerCount-customerCount2)/float64(customerCount2),2)   //升级比例 = (新等级-旧等级)/旧等级
	}
	if shipperCount2 != 0 { 
		rnk.ShipperCountGrowthRate=tools.ToFloat64(float64(shipperCount-shipperCount2)/float64(shipperCount2),2)   //升级比例 = (新等级-旧等级)/旧等级
	}

	rnk.AvgDeliveryTime = float64(avgDeliveryTime)/60 //按分钟计算

	// 订单等级详情
	var rnkOrderRank response.OrderRankData
	rnkOrderRank.RankOneGrowthRate = 100
	rnkOrderRank.RankTwoGrowthRate = 100
	rnkOrderRank.RankThreeGrowthRate = 100
	rnkOrderRank.RankFourGrowthRate = 100
	rnkOrderRank.RankFiveGrowthRate = 100

	orderRankQuery :=ckDb.Model(&models.AutoDispatchHistory{}).Where("area_id = ? and created_at >=? and created_at < ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59")).Where("order_id in (?)",allOrderIds1)
	orderRankQuery.Count(&rnkOrderRank.OrderCount)
	
	var orderRanks []response.RankGroup
	

	orderRankQuery.Select("count(order_id) as rank_count,order_rank_org as ranks").Group("order_rank_org").Scan(&orderRanks)

	var orderRanks2 []response.RankGroup
	orderRank2Query :=ckDb.Model(&models.AutoDispatchHistory{}).Where("area_id = ? and created_at >=? and created_at < ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59")).Where("order_id in (?)",allOrderIds2)
	orderRank2Query.Select("count(order_id) as order_rank_count,order_rank_org as ranks").Group("order_rank_org").Scan(&orderRanks2)

	for _, v := range orderRanks {
		switch v.Rank { 
		case 1: 
			rnkOrderRank.RankOneCount = v.RankCount
		case 2: 
			rnkOrderRank.RankTwoCount = v.RankCount
		case 3: 
			rnkOrderRank.RankThreeCount = v.RankCount
		case 4: 
			rnkOrderRank.RankFourCount = v.RankCount
		case 5: 
			rnkOrderRank.RankFiveCount = v.RankCount
		}
	}

	for _, v := range orderRanks2 {
		switch v.Rank { 
		case 1: 
			rnkOrderRank.RankOneCount2 = v.RankCount
		case 2: 
			rnkOrderRank.RankTwoCount2 = v.RankCount
		case 3: 
			rnkOrderRank.RankThreeCount2 = v.RankCount
		case 4: 
			rnkOrderRank.RankFourCount2 = v.RankCount
		case 5: 
			rnkOrderRank.RankFiveCount2 = v.RankCount
		}
	}
	if rnkOrderRank.RankOneCount2 != 0 { 
		rnkOrderRank.RankOneGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankOneCount-rnkOrderRank.RankOneCount2)/float64(rnkOrderRank.RankOneCount2),2)
	}
	if rnkOrderRank.RankTwoCount2 != 0 { 
		rnkOrderRank.RankTwoGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankTwoCount-rnkOrderRank.RankTwoCount2)/float64(rnkOrderRank.RankTwoCount2),2)
	}
	if rnkOrderRank.RankThreeCount2 != 0 { 
		rnkOrderRank.RankThreeGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankThreeCount-rnkOrderRank.RankThreeCount2)/float64(rnkOrderRank.RankThreeCount2),2)
	}
	if rnkOrderRank.RankFourCount2 != 0 { 
		rnkOrderRank.RankFourGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankFourCount-rnkOrderRank.RankFourCount2)/float64(rnkOrderRank.RankFourCount2),2)
	}
	if rnkOrderRank.RankFiveCount2 != 0 { 
		rnkOrderRank.RankFiveGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankFiveCount-rnkOrderRank.RankFiveCount2)/float64(rnkOrderRank.RankFiveCount2),2)
	}
	rnk.OrderRankData = rnkOrderRank



	var rnkCustomerRank response.CustomerRankData
	rnkCustomerRank.RankOneGrowthRate = 100
	rnkCustomerRank.RankTwoGrowthRate = 100
	rnkCustomerRank.RankThreeGrowthRate = 100
	rnkCustomerRank.RankFourGrowthRate = 100
	rnkCustomerRank.RankFiveGrowthRate = 100

	customerRankQuery :=ckDb.Model(&models.AutoDispatchHistory{}).Where("area_id = ? and created_at >=? and created_at < ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59")).Where("order_id in (?)",allOrderIds1)
	customerRankQuery.Select("count(distinct(user_id)) as customer_count").Pluck("customer_count",&rnkCustomerRank.CustomerCount)

	var customerRanks []response.RankGroup
	customerRankQueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).Where("area_id = ? and created_at >=? and created_at < ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59")).Where("order_id in (?)",allOrderIds1)
	customerRankQueryNew.Select("count(order_id) as rank_count,user_rank as ranks").Group("user_rank").Scan(&customerRanks)
	for _, v := range customerRanks { 
		switch v.Rank { 
		case 1: 
			rnkCustomerRank.RankOneCount = v.RankCount
		case 2: 
			rnkCustomerRank.RankTwoCount = v.RankCount
		case 3: 
			rnkCustomerRank.RankThreeCount = v.RankCount
		case 4: 
			rnkCustomerRank.RankFourCount = v.RankCount
		case 5: 
			rnkCustomerRank.RankFiveCount = v.RankCount	
		}
	}
	
	customerRank2Query :=ckDb.Model(&models.AutoDispatchHistory{}).Where("area_id = ? and created_at >=? and created_at < ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59")).Where("order_id in (?)",allOrderIds2)
	customerRank2Query.Select("count(distinct(user_id)) as customer_count").Pluck("customer_count",&rnkCustomerRank.CustomerCount2)
	var customerRanks2 []response.RankGroup
	customerRank2QueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).Where("area_id = ? and created_at >=? and created_at < ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59")).Where("order_id in (?)",allOrderIds2)
	customerRank2QueryNew.Select("count(order_id) as rank_count,user_rank as ranks").Group("user_rank").Scan(&customerRanks2)
	for _, v := range customerRanks2 { 
		switch v.Rank { 
		case 1: 
			rnkCustomerRank.RankOneCount2 = v.RankCount
		case 2: 
			rnkCustomerRank.RankTwoCount2 = v.RankCount
		case 3: 
			rnkCustomerRank.RankThreeCount2 = v.RankCount
		case 4: 
			rnkCustomerRank.RankFourCount2 = v.RankCount
		case 5: 
			rnkCustomerRank.RankFiveCount2 = v.RankCount	
	
		}
	}
	if rnkCustomerRank.RankOneCount2 != 0 { 
		rnkCustomerRank.RankOneGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankOneCount-rnkCustomerRank.RankOneCount2)/float64(rnkCustomerRank.RankOneCount2),2)
	}
	if rnkCustomerRank.RankTwoCount2 != 0 { 
		rnkCustomerRank.RankTwoGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankTwoCount-rnkCustomerRank.RankTwoCount2)/float64(rnkCustomerRank.RankTwoCount2),2)
	}
	if rnkCustomerRank.RankThreeCount2 != 0 { 
		rnkCustomerRank.RankThreeGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankThreeCount-rnkCustomerRank.RankThreeCount2)/float64(rnkCustomerRank.RankThreeCount2),2)
	}
	if rnkCustomerRank.RankFourCount2 != 0 { 
		rnkCustomerRank.RankFourGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankFourCount-rnkCustomerRank.RankFourCount2)/float64(rnkCustomerRank.RankFourCount2),2)
	}
	if rnkCustomerRank.RankFiveCount2 != 0 { 
		rnkCustomerRank.RankFiveGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankFiveCount-rnkCustomerRank.RankFiveCount2)/float64(rnkCustomerRank.RankFiveCount2),2)
	}

	//用户等级趋势 
	var customerRankDataDayItems []response.CustomerRankDataDayItems 
	customerRankDayQuery :=ckDb.Model(&models.AutoDispatchHistory{}).Where("area_id = ? and created_at >=? and created_at < ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"))
	customerRankDayQueryFields :=`
		DATE(created_at) AS day,
		COUNT(DISTINCT CASE WHEN user_rank = 1 THEN user_id END) AS rank_one_count,
		COUNT(DISTINCT CASE WHEN user_rank = 2 THEN user_id END) AS rank_two_count,
		COUNT(DISTINCT CASE WHEN user_rank = 3 THEN user_id END) AS rank_three_count,
		COUNT(DISTINCT CASE WHEN user_rank = 4 THEN user_id END) AS rank_four_count,
		COUNT(DISTINCT CASE WHEN user_rank = 5 THEN user_id END) AS rank_five_count
	`
	customerRankDayQuery.Select(customerRankDayQueryFields).Group("DATE(created_at)").Order("day").Scan(&customerRankDataDayItems)

	for k, v := range customerRankDataDayItems {
		customerRankDataDayItems[k].Day = carbon.Parse(v.Day,configs.AsiaShanghai).Format("Y-m-d")
	}

	rnkCustomerRank.DayItems = customerRankDataDayItems

	rnk.CustomerRankData = rnkCustomerRank


	var rnkShipperRank response.ShipperRankData
	rnkShipperRank.RankOneGrowthRate = 100
	rnkShipperRank.RankTwoGrowthRate = 100
	rnkShipperRank.RankThreeGrowthRate = 100
	rnkShipperRank.RankFourGrowthRate = 100
	rnkShipperRank.RankFiveGrowthRate = 100

	shipperRankQuery :=ckDb.Model(&models.AutoDispatchHistory{}).Where("area_id = ? and created_at >=? and created_at < ? and shipper_id > ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0).Where("order_id in (?)",allOrderIds1)
	shipperRankQuery.Select("count(distinct(shipper_id)) as shipper_count").Pluck("shipper_count",&rnkShipperRank.ShipperCount)
	var shipperRanks []response.RankGroup
	shipperRankQueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).Where("area_id = ? and created_at >=? and created_at < ? and shipper_id > ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0).Where("order_id in (?)",allOrderIds1)
	shipperRankQueryNew.Select("count(distinct(shipper_id)) as rank_count,shipper_rank as ranks").Group("shipper_rank").Scan(&shipperRanks)
	for _, v := range shipperRanks { 
		switch v.Rank { 
		case 1: 
			rnkShipperRank.RankOneCount = v.RankCount
		case 2: 
			rnkShipperRank.RankTwoCount = v.RankCount
		case 3: 
			rnkShipperRank.RankThreeCount = v.RankCount
		case 4: 
			rnkShipperRank.RankFourCount = v.RankCount
		case 5: 
			rnkShipperRank.RankFiveCount = v.RankCount	
	
		}
	}

	shipperRank2Query :=ckDb.Model(&models.AutoDispatchHistory{}).Where("area_id = ? and created_at >=? and created_at < ? and shipper_id > ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"),0).Where("order_id in (?)",allOrderIds2)
	shipperRank2Query.Select("count(distinct(shipper_id)) as shipper_count").Pluck("shipper_count",&rnkShipperRank.ShipperCount2)

	var shipperRanks2 []response.RankGroup
	shipperRank2QueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).Where("area_id = ? and created_at >=? and created_at < ? and shipper_id > ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"),0).Where("order_id in (?)",allOrderIds2)
	shipperRank2QueryNew.Select("count(distinct(shipper_id)) as rank_count,shipper_rank as ranks").Group("shipper_rank").Scan(&shipperRanks2)
	for _, v := range shipperRanks2 { 
		switch v.Rank { 
		case 1: 
			rnkShipperRank.RankOneCount2 = v.RankCount
		case 2: 
			rnkShipperRank.RankTwoCount2 = v.RankCount
		case 3: 
			rnkShipperRank.RankThreeCount2 = v.RankCount
		case 4: 
			rnkShipperRank.RankFourCount2 = v.RankCount
		case 5: 
			rnkShipperRank.RankFiveCount2 = v.RankCount	
	
		}
	}
	if rnkShipperRank.RankOneCount2 != 0 { 
		rnkShipperRank.RankOneGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankOneCount-rnkShipperRank.RankOneCount2)/float64(rnkShipperRank.RankOneCount2),2)
	}
	if rnkShipperRank.RankTwoCount2 != 0 { 
		rnkShipperRank.RankTwoGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankTwoCount-rnkShipperRank.RankTwoCount2)/float64(rnkShipperRank.RankTwoCount2),2)
	}
	if rnkShipperRank.RankThreeCount2 != 0 { 
		rnkShipperRank.RankThreeGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankThreeCount-rnkShipperRank.RankThreeCount2)/float64(rnkShipperRank.RankThreeCount2),2)
	}
	if rnkShipperRank.RankFourCount2 != 0 { 
		rnkShipperRank.RankFourGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankFourCount-rnkShipperRank.RankFourCount2)/float64(rnkShipperRank.RankFourCount2),2)
	}
	if rnkShipperRank.RankFiveCount2 != 0 { 
		rnkShipperRank.RankFiveGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankFiveCount-rnkShipperRank.RankFiveCount2)/float64(rnkShipperRank.RankFiveCount2),2)
	}
	//配送员等级趋势 
	var shipperRankDataDayItems []response.CustomerRankDataDayItems 
	
	shipperRankDayQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
	Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? ",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"))
	shipperRankDayQueryFields :=`
		DATE(created_at) AS day,
		COUNT(DISTINCT CASE WHEN shipper_rank = 1 THEN shipper_id END) AS rank_one_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 2 THEN shipper_id END) AS rank_two_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 3 THEN shipper_id END) AS rank_three_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 4 THEN shipper_id END) AS rank_four_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 5 THEN shipper_id END) AS rank_five_count
	`
	shipperRankDayQuery.Select(shipperRankDayQueryFields).Group("DATE(created_at)").Order("day").Scan(&shipperRankDataDayItems)

	for k, v := range shipperRankDataDayItems {
		shipperRankDataDayItems[k].Day = carbon.Parse(v.Day,configs.AsiaShanghai).Format("Y-m-d")
	}

	rnkShipperRank.DayItems = shipperRankDataDayItems

	rnk.ShipperRankData = rnkShipperRank



	var rnkOrderCustomerData response.OrderCustomerData

	orderCustomerQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
		// Joins("left join t_order on t_order.id = t_auto_dispatch_history.order_id").
		Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? ",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59")).Where("order_id in (?)",allOrderIds1)

	
	orderCustomerQuery.Select("count(t_auto_dispatch_history.order_id) as order_count").Pluck("order_count",&rnkOrderCustomerData.OrderCount)
	// orderCustomerQuery.Select("ifnull(sum(t_order.order_price),0) as order_price").Pluck("order_price",&rnkOrderCustomerData.OrderPrice)
	ckDb.Model(&models.Order{}).
		Where("area_id = ? and created_at >=? and created_at < ? and state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),7).
		Select("ifnull(sum(t_order.order_price),0) as order_price").Pluck("order_price",&rnkOrderCustomerData.OrderPrice)

	customerCountFields :=`
		count(distinct(t_auto_dispatch_history.user_id)) as customer_count,
		COUNT(DISTINCT CASE WHEN user_rank = 1 THEN t_auto_dispatch_history.user_id END) AS customer_rank_one_count,
		COUNT(DISTINCT CASE WHEN user_rank = 2 THEN t_auto_dispatch_history.user_id END) AS customer_rank_two_count,
		COUNT(DISTINCT CASE WHEN user_rank = 3 THEN t_auto_dispatch_history.user_id END) AS customer_rank_three_count,
		COUNT(DISTINCT CASE WHEN user_rank = 4 THEN t_auto_dispatch_history.user_id END) AS customer_rank_four_count,
		COUNT(DISTINCT CASE WHEN user_rank = 5 THEN t_auto_dispatch_history.user_id END) AS customer_rank_five_count
	
	`	
	var customerCountData response.OrderCustomerCountData
	orderCustomerQuery.Select(customerCountFields).Scan(&customerCountData)

	rnkOrderCustomerData.CustomerCount = int64(customerCountData.CustomerCount)

	
	
	var orderCustomerDataGorm []response.OrderCustomerDataGorm
	orderCustomerQueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
		// Joins("left join t_order on t_order.id = t_auto_dispatch_history.order_id").
		Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? ",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59")).Where("order_id in (?)",allOrderIds1)

	fields :="t_auto_dispatch_history.user_rank as user_rank,"
	fields +="t_auto_dispatch_history.order_rank_org as order_rank,"
	fields +="count(t_auto_dispatch_history.order_id) as order_count"
	// fields +="ifnull(sum(t_order.order_price),0) as order_price"
	// fields +="IFNULL(SUM((SELECT order_price FROM t_order  WHERE id = t_auto_dispatch_history.order_id)), 0) AS order_price"

	
	var orderRankGormOrderCustomer []response.OrderRankGorm
	orderCustomerQueryNew.
	Where("t_auto_dispatch_history.order_rank_org between ? and ?",1,5).
	Select("user_rank,order_rank_org as order_rank,t_auto_dispatch_history.order_id").
	Scan(&orderRankGormOrderCustomer)
	
	var orderRankGormOrderCustomer2 []response.OrderRankGorm
	var orderRankGormOrderCustomerOrderIds []int64
	for _, v := range orderRankGormOrderCustomer {
		orderRankGormOrderCustomerOrderIds = append(orderRankGormOrderCustomerOrderIds, v.OrderId)
	}
	if len(orderRankGormOrderCustomerOrderIds) > 0 {
		ckDb.Model(&models.Order{}).Where("id in (?)",orderRankGormOrderCustomerOrderIds).Select("id as order_id,order_price").Scan(&orderRankGormOrderCustomer2)
	}
	for k, v := range orderRankGormOrderCustomer {
		for _, vv := range orderRankGormOrderCustomer2 {
			if v.OrderId == vv.OrderId {
				v.OrderPrice = vv.OrderPrice
			}
		}
		orderRankGormOrderCustomer[k] = v
	}



	orderCustomerQueryNew.Where("t_auto_dispatch_history.user_rank between ? and ?",1,5).
	Where("t_auto_dispatch_history.order_rank_org between ? and ?",1,5).
	Group("customer_rank,t_auto_dispatch_history.order_rank_org").
	Order("customer_rank,t_auto_dispatch_history.order_rank_org").
	Select(fields).
	Scan(&orderCustomerDataGorm)


	for i := 1; i < 6; i++ {
		var orderCustomerDataItem response.OrderCustomerDataItems
		orderCustomerDataItem.CustomerRank = i
		switch i { 
			case 1: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankOneCount)
			case 2: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankTwoCount)
			case 3: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankThreeCount)
			case 4: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankFourCount)
			case 5: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankFiveCount)
	
		}
		
		for _, v := range orderCustomerDataGorm { 
			
			for _, vv := range orderRankGormOrderCustomer {
				if vv.UserRank == int64(v.UserRank) && vv.OrderRank == v.OrderRank { 
					v.OrderPrice= vv.OrderPrice
				}
			}
			
			if v.UserRank != i {
				continue
			}
			switch v.OrderRank { 
				case 5: 
					orderCustomerDataItem.OrderRankFiveCount += v.OrderCount
					orderCustomerDataItem.OrderRankFivePrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankFiveCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankFivePrice += v.OrderPrice
				case 4: 
					orderCustomerDataItem.OrderRankFourCount += v.OrderCount
					orderCustomerDataItem.OrderRankFourPrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankFourCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankFourPrice += v.OrderPrice
				case 3: 
					orderCustomerDataItem.OrderRankThreeCount += v.OrderCount
					orderCustomerDataItem.OrderRankThreePrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankThreeCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankThreePrice += v.OrderPrice
				case 2: 
					orderCustomerDataItem.OrderRankTwoCount += v.OrderCount
					orderCustomerDataItem.OrderRankTwoPrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankTwoCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankTwoPrice += v.OrderPrice
				case 1: 
					orderCustomerDataItem.OrderRankOneCount += v.OrderCount
					orderCustomerDataItem.OrderRankOnePrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankOneCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankOnePrice += v.OrderPrice
			}

		}
		rnkOrderCustomerData.Items = append(rnkOrderCustomerData.Items, orderCustomerDataItem)
		
	}

	rnk.OrderCustomerData = rnkOrderCustomerData
	

	var rnkOrderShipperData response.OrderShipperData

	orderShipperQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
		// Joins("left join t_order on t_order.id = t_auto_dispatch_history.order_id").
		Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.shipper_id > ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0).Where("order_id in (?)",allOrderIds1)

	
	orderShipperQuery.Select("count(t_auto_dispatch_history.order_id) as order_count").Pluck("order_count",&rnkOrderShipperData.OrderCount)
	// orderShipperQuery.Select("ifnull(sum(t_order.order_price),0) as order_price").Pluck("order_price",&rnkOrderCustomerData.OrderPrice)
	ckDb.Model(&models.Order{}).
		Where("area_id = ? and created_at >=? and created_at < ? and state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),7).
		Select("ifnull(sum(t_order.order_price),0) as order_price").Pluck("order_price",&rnkOrderShipperData.OrderPrice)

	orderShipperQuery.Select("count(distinct(t_auto_dispatch_history.shipper_id)) as shipper_count").Pluck("shipper_count",&rnkOrderShipperData.ShipperCount)
		
	
	var orderShipperDataGorm []response.OrderShipperDataGorm
	orderShipperQueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
		// Joins("left join t_order on t_order.id = t_auto_dispatch_history.order_id").
		Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.shipper_id > ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0).Where("order_id in (?)",allOrderIds1)
	
		
	
	var orderRankGormOrderShipper []response.OrderRankGorm
	orderShipperQueryNew.
	Where("t_auto_dispatch_history.order_rank_org between ? and ?",1,5).
	Select("user_rank,order_rank_org as order_rank,t_auto_dispatch_history.order_id").
	Scan(&orderRankGormOrderShipper)

	var orderRankGormOrderShipper2 []response.OrderRankGorm
	var orderRankGormOrderShipperOrderIds []int64
	for _, v := range orderRankGormOrderShipper {
		orderRankGormOrderShipperOrderIds = append(orderRankGormOrderShipperOrderIds, v.OrderId)
	}
	if len(orderRankGormOrderShipperOrderIds) > 0 {
		ckDb.Model(&models.Order{}).Where("id in (?)",orderRankGormOrderShipperOrderIds).Select("id as order_id,order_price").Scan(&orderRankGormOrderShipper2)
	}
	for k, v := range orderRankGormOrderShipper {
		for _, vv := range orderRankGormOrderShipper2 {
			if v.OrderId == vv.OrderId {
				v.OrderPrice = vv.OrderPrice
			}
		}
		orderRankGormOrderCustomer[k] = v
	}

	orderShipperFields :="t_auto_dispatch_history.shipper_rank as shipper_rank,"
	orderShipperFields +="t_auto_dispatch_history.order_rank_org as order_rank,"
	orderShipperFields +="count(t_auto_dispatch_history.order_id) as order_count"

	orderShipperQueryNew.Where("t_auto_dispatch_history.user_rank between ? and ?",1,5).
	Where("t_auto_dispatch_history.order_rank_org between ? and ?",1,5).
	Group("t_auto_dispatch_history.shipper_rank,t_auto_dispatch_history.order_rank_org").
	Order("t_auto_dispatch_history.shipper_rank,t_auto_dispatch_history.order_rank_org").
	Select(orderShipperFields).
	Scan(&orderShipperDataGorm)


	shipperCountFields :=`
		
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 1 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_one_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 2 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_two_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 3 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_three_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 4 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_four_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 5 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_five_count
	
	`	
	var shipperCountData response.OrderShipperCountData
	orderCustomerQuery.Select(shipperCountFields).Scan(&shipperCountData)

	rnkOrderShipperData.ShipperCount = int64(shipperCountData.ShipperRankOneCount)+int64(shipperCountData.ShipperRankTwoCount)+int64(shipperCountData.ShipperRankThreeCount)+int64(shipperCountData.ShipperRankFourCount)+int64(shipperCountData.ShipperRankFiveCount)



	for i := 1; i < 6; i++ {
		var orderShipperDataItem response.OrderShipperDataItems
		orderShipperDataItem.ShipperRank = i
		switch i {
			case 1:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankOneCount)
			case 2:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankTwoCount)
			case 3:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankThreeCount)
			case 4:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankFourCount)
			case 5:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankFiveCount)
		}

		for _, v := range orderShipperDataGorm { 
			
			if v.ShipperRank !=i {
				continue
			}

			switch v.OrderRank { 
				case 5: 
					orderShipperDataItem.OrderRankFiveCount += v.OrderCount
					orderShipperDataItem.OrderRankFivePrice += v.OrderPrice
					rnkOrderShipperData.OrderRankFiveCount+=v.OrderCount
					orderShipperDataItem.OrderRankFourPrice += v.OrderPrice


				case 4: 
					orderShipperDataItem.OrderRankFourCount += v.OrderCount
					orderShipperDataItem.OrderRankFourPrice += v.OrderPrice
					rnkOrderShipperData.OrderRankFourCount+=v.OrderCount
					orderShipperDataItem.OrderRankThreePrice += v.OrderPrice
				case 3: 
					orderShipperDataItem.OrderRankThreeCount += v.OrderCount
					orderShipperDataItem.OrderRankThreePrice += v.OrderPrice
					rnkOrderShipperData.OrderRankThreeCount+=v.OrderCount
					orderShipperDataItem.OrderRankTwoPrice += v.OrderPrice
				case 2: 
					orderShipperDataItem.OrderRankTwoCount += v.OrderCount
					orderShipperDataItem.OrderRankTwoPrice += v.OrderPrice
					rnkOrderShipperData.OrderRankTwoCount+=v.OrderCount
					orderShipperDataItem.OrderRankOnePrice += v.OrderPrice

				case 1: 
					orderShipperDataItem.OrderRankOneCount += v.OrderCount
					orderShipperDataItem.OrderRankOnePrice += v.OrderPrice
					rnkOrderShipperData.OrderRankOneCount+=v.OrderCount
					orderShipperDataItem.OrderRankThreePrice += v.OrderPrice

			}

		}
		rnkOrderShipperData.Items = append(rnkOrderShipperData.Items, orderShipperDataItem)
		
	}

	rnk.OrderShipperData = rnkOrderShipperData


	var rnkOrderLateData response.OrderLateData


	orderLateQueryFront :=ckDb.Model(&models.AutoDispatchHistory{})
		// Joins("left join t_order on t_order.id = t_auto_dispatch_history.order_id")

	orderLateQuery :=orderLateQueryFront.Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.shipper_id > ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0).Where("order_id in (?)",allOrderIds1)

	
	orderLateQuery.Where("t_auto_dispatch_history.order_deliver_state >= ?",0).Select("count(t_auto_dispatch_history.order_id) as order_count").Pluck("order_count",&rnkOrderLateData.OrderCount)
	var lateOrderIds []int64
	orderLateQuery.Where("t_auto_dispatch_history.order_deliver_state >= ?",0).
	Pluck("order_id",&lateOrderIds)
	// Pluck("order_id",&rnkOrderLateData.OrderPrice)

	if len(lateOrderIds) > 0 {
	ckDb.Model(&models.Order{}).
		Where("area_id = ? and created_at >=? and created_at < ? and state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),7).
		Where("id in (?)",lateOrderIds).
		Select("ifnull(sum(t_order.order_price),0) as order_price").Pluck("order_price",&rnkOrderLateData.OrderPrice)
	}


	
	var orderLateDataGorm []response.OrderLateDataGorm
	lateFields :="t_auto_dispatch_history.order_deliver_state as late_rank,"
	lateFields +="t_auto_dispatch_history.order_rank_org as order_rank,"
	lateFields +="count(t_auto_dispatch_history.order_id) as order_count"
	// fields +="ifnull(sum(t_order.order_price),0) as order_price"
	// lateFields +="IFNULL(SUM((SELECT order_price FROM t_order o WHERE id = t_auto_dispatch_history.order_id)), 0) AS order_price"

	var orderRankGorm []response.OrderRankGorm
	orderLateQuery.
	Where("t_auto_dispatch_history.order_rank_org between ? and ?",1,5).
	Select("user_rank,order_rank_org as order_rank,t_auto_dispatch_history.order_id").
	Scan(&orderRankGorm)


	var orderRankGorm2 []response.OrderRankGorm
	var orderRankOrderIds []int64
	for _, v := range orderRankGorm {
		orderRankOrderIds = append(orderRankOrderIds, v.OrderId)
	}
	if len(orderRankOrderIds) > 0 {
		ckDb.Model(&models.Order{}).Where("id in (?)",orderRankOrderIds).Select("id as order_id,order_price").Scan(&orderRankGorm2)
	}
	for k, v := range orderRankGorm {
		for _, vv := range orderRankGorm2 {
			if v.OrderId == vv.OrderId {
				v.OrderPrice = vv.OrderPrice
			}
		}
		orderRankGorm[k] = v
	}

	orderLateQuery.
	Where("t_auto_dispatch_history.user_rank between ? and ?",1,5).
	Where("t_auto_dispatch_history.order_rank_org between ? and ?",1,5).
	Group("t_auto_dispatch_history.order_deliver_state,t_auto_dispatch_history.order_rank_org").
	Order("t_auto_dispatch_history.order_deliver_state,t_auto_dispatch_history.order_rank_org").
	Select(lateFields).
	Scan(&orderLateDataGorm)


	
	shipperLateFields :=`
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 0 THEN t_auto_dispatch_history.order_id END) AS late_rank_zero_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 1 THEN t_auto_dispatch_history.order_id END) AS late_rank_one_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 2 THEN t_auto_dispatch_history.order_id END) AS late_rank_two_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 3 THEN t_auto_dispatch_history.order_id END) AS late_rank_three_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 4 THEN t_auto_dispatch_history.order_id END) AS late_rank_four_count
	`	
	var shipperLateData response.OrderLateCountData
	orderLateCountQuery :=ckDb.Model(&models.AutoDispatchHistory{}).Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.shipper_id > ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0).Where("order_id in (?)",allOrderIds1)
	orderLateCountQuery.Select(shipperLateFields).Scan(&shipperLateData)
	rnkOrderLateData.LateMinute = shipperLateData.LateRankOneCount+shipperLateData.LateRankTwoCount+shipperLateData.LateRankThreeCount+shipperLateData.LateRankFourCount
 


	for i := 0; i < 5; i++ {
		var orderLateDataItem response.OrderLateDataItems
		orderLateDataItem.LateState = i
		
		switch i {
			case 0:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankZeroCount)
			case 1:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankOneCount)
			case 2:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankTwoCount)
			case 3:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankThreeCount)
			case 4:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankFourCount)	
			
		}
		
		for _, v := range orderLateDataGorm { 
				if v.LateRank != i {
					continue
				}
				switch v.OrderRank { 
						case 5: 
							orderLateDataItem.OrderRankFiveCount += v.OrderCount
							orderLateDataItem.OrderRankFivePrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankFiveCount+=v.OrderCount
							rnkOrderLateData.OrderRankFivePrice+=v.OrderPrice
							
						case 4: 
							orderLateDataItem.OrderRankFourCount += v.OrderCount
							orderLateDataItem.OrderRankFourPrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankFourCount+=v.OrderCount
							rnkOrderLateData.OrderRankFourPrice+=v.OrderPrice
						case 3: 
							orderLateDataItem.OrderRankThreeCount += v.OrderCount
							orderLateDataItem.OrderRankThreePrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankThreeCount+=v.OrderCount
							rnkOrderLateData.OrderRankThreePrice+=v.OrderPrice
						case 2: 
							orderLateDataItem.OrderRankTwoCount += v.OrderCount
							orderLateDataItem.OrderRankTwoPrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankTwoCount+=v.OrderCount
							rnkOrderLateData.OrderRankTwoPrice+=v.OrderPrice
						case 1: 
							orderLateDataItem.OrderRankOneCount += v.OrderCount
							orderLateDataItem.OrderRankOnePrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankOneCount+=v.OrderCount
							rnkOrderLateData.OrderRankOnePrice+=v.OrderPrice
							
				}

				
			

			

		}
		rnkOrderLateData.Items = append(rnkOrderLateData.Items, orderLateDataItem)
		
	}


	rnk.OrderLateData = rnkOrderLateData



	return rnk, nil

}



func (s OrderRankService) GetOrderRankStatJoin(ckDb *gorm.DB,joinSql string,senderID int64, senderType int, cityId int, areaId int, kw string, startDate string, endDate string,dbMode int) (response.OrderRank, error) {
	var rnk response.OrderRank	
	// db := tools.GetDB()


	orderPrice :=int64(0)
	orderCount :=int64(0)
	customerCount :=int64(0)
	shipperCount :=int64(0)
	avgDeliveryTimeSum :=float64(0)
	avgDeliveryTime :=float64(0)

	orderPrice2 :=int64(0)
	orderCount2 :=int64(0)
	customerCount2 :=int64(0)
	shipperCount2 :=int64(0)
	avgDeliveryTimeSum2 :=float64(0)
	avgDeliveryTime2 :=float64(0)

	startTime :=carbon.Parse(startDate, configs.AsiaShanghai)
	endTime :=carbon.Parse(endDate, configs.AsiaShanghai)

	days :=endTime.DiffAbsInDays(startTime)
	startTime2 :=startTime.AddDays(-int(days))
	endTime2 :=endTime.AddDays(-int(days))
	

	
	orderQuery :=ckDb.Model(&models.Order{}).Where("area_id = ? and state =? and created_at >=? and created_at < ?",areaId,7,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"))
	

	var orderData response.OrderDataGorm

	orderQuery.Select("ifnull(sum(order_price),0) as order_price,count(1) as order_count,count(distinct(user_id)) as customer_count,count(distinct(shipper_id)) as shipper_count").Scan(&orderData)

	
	orderPrice = orderData.OrderPrice
	orderCount = orderData.OrderCount
	customerCount = orderData.CustomerCount
	shipperCount = orderData.ShipperCount

	orderQuery.Where("shipper_id > ?",0).Select("ifnull(avg(TIMESTAMPDIFF(SECOND, delivery_start_time, delivery_end_time)),0) as delivery_time").Pluck("delivery_time",&avgDeliveryTimeSum)

	orderQuery2 :=ckDb.Model(&models.Order{}).Where("area_id = ? and state =? and created_at >=? and created_at < ?",areaId,7,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"))
	

	var orderData2 response.OrderDataGorm

	orderQuery2.Select("ifnull(sum(order_price),0) as order_price,count(1) as order_count,count(distinct(user_id)) as customer_count,count(distinct(shipper_id)) as shipper_count").Scan(&orderData2)
	

	orderQuery2.Where("shipper_id > ?",0).Select("ifnull(avg(TIMESTAMPDIFF(SECOND, delivery_start_time, delivery_end_time)),0) as delivery_time").Pluck("delivery_time",&avgDeliveryTimeSum2)
	
	orderPrice2 = orderData2.OrderPrice
	orderCount2 = orderData2.OrderCount
	customerCount2 = orderData2.CustomerCount
	shipperCount2 = orderData2.ShipperCount



	

	rnk.OrderPrice = orderPrice
	rnk.OrderCount = orderCount
	rnk.CustomerCount = customerCount
	rnk.ShipperCount = shipperCount
	if orderCount > 0 { 
		avgDeliveryTime = float64(avgDeliveryTimeSum)
	}
	rnk.AvgDeliveryTime = float64(avgDeliveryTime)

	rnk.OrderPriceGrowthRate = 100
	rnk.OrderCountGrowthRate = 100
	rnk.CustomerCountGrowthRate = 100
	rnk.ShipperCountGrowthRate = 100
	rnk.AvgDeliveryTimeGrowthRate = 100

	//升级比例 = (新等级-旧等级)/旧等级
	if orderCount2 != 0 { 
		avgDeliveryTime2 = tools.ToFloat64(float64(avgDeliveryTimeSum2),2)
		rnk.OrderCountGrowthRate=tools.ToFloat64(float64(orderCount-orderCount2)/float64(orderCount2),2)   //升级比例 = (新等级-旧等级)/旧等级
		rnk.OrderPriceGrowthRate=tools.ToFloat64(float64(orderPrice-orderPrice2)/float64(orderPrice2),2)
		rnk.AvgDeliveryTimeGrowthRate=tools.ToFloat64(float64(avgDeliveryTime-avgDeliveryTime2)/float64(avgDeliveryTime2),2)   //升级比例
	}
	if customerCount2 != 0 { 
		rnk.CustomerCountGrowthRate=tools.ToFloat64(float64(customerCount-customerCount2)/float64(customerCount2),2)   //升级比例 = (新等级-旧等级)/旧等级
	}
	if shipperCount2 != 0 { 
		rnk.ShipperCountGrowthRate=tools.ToFloat64(float64(shipperCount-shipperCount2)/float64(shipperCount2),2)   //升级比例 = (新等级-旧等级)/旧等级
	}
	rnk.AvgDeliveryTime = tools.ToFloat64(float64(avgDeliveryTime)/60,2)

	var rnkOrderRank response.OrderRankData
	rnkOrderRank.RankOneGrowthRate = 100
	rnkOrderRank.RankTwoGrowthRate = 100
	rnkOrderRank.RankThreeGrowthRate = 100
	rnkOrderRank.RankFourGrowthRate = 100
	rnkOrderRank.RankFiveGrowthRate = 100

	orderRankQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
	Joins(joinSql).
	Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_order.state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),7)
	orderRankQuery.Count(&rnkOrderRank.OrderCount)
	
	var orderRanks []response.RankGroup
	

	orderRankQuery.Select("count(t_auto_dispatch_history.order_id) as rank_count,order_rank_org as ranks").Group("order_rank_org").Scan(&orderRanks)

	var orderRanks2 []response.RankGroup
	orderRank2Query :=ckDb.Model(&models.AutoDispatchHistory{}).
	Joins(joinSql).
	Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_order.state = ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"),7)
	orderRank2Query.Select("count(t_auto_dispatch_history.order_id) as order_rank_count,order_rank_org as ranks").Group("order_rank_org").Scan(&orderRanks2)

	for _, v := range orderRanks {
		switch v.Rank { 
		case 1: 
			rnkOrderRank.RankOneCount = v.RankCount
		case 2: 
			rnkOrderRank.RankTwoCount = v.RankCount
		case 3: 
			rnkOrderRank.RankThreeCount = v.RankCount
		case 4: 
			rnkOrderRank.RankFourCount = v.RankCount
		case 5: 
			rnkOrderRank.RankFiveCount = v.RankCount
		}
	}

	for _, v := range orderRanks2 {
		switch v.Rank { 
		case 1: 
			rnkOrderRank.RankOneCount2 = v.RankCount
		case 2: 
			rnkOrderRank.RankTwoCount2 = v.RankCount
		case 3: 
			rnkOrderRank.RankThreeCount2 = v.RankCount
		case 4: 
			rnkOrderRank.RankFourCount2 = v.RankCount
		case 5: 
			rnkOrderRank.RankFiveCount2 = v.RankCount
		}
	}
	if rnkOrderRank.RankOneCount2 != 0 { 
		rnkOrderRank.RankOneGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankOneCount-rnkOrderRank.RankOneCount2)/float64(rnkOrderRank.RankOneCount2),2)
	}
	if rnkOrderRank.RankTwoCount2 != 0 { 
		rnkOrderRank.RankTwoGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankTwoCount-rnkOrderRank.RankTwoCount2)/float64(rnkOrderRank.RankTwoCount2),2)
	}
	if rnkOrderRank.RankThreeCount2 != 0 { 
		rnkOrderRank.RankThreeGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankThreeCount-rnkOrderRank.RankThreeCount2)/float64(rnkOrderRank.RankThreeCount2),2)
	}
	if rnkOrderRank.RankFourCount2 != 0 { 
		rnkOrderRank.RankFourGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankFourCount-rnkOrderRank.RankFourCount2)/float64(rnkOrderRank.RankFourCount2),2)
	}
	if rnkOrderRank.RankFiveCount2 != 0 { 
		rnkOrderRank.RankFiveGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankFiveCount-rnkOrderRank.RankFiveCount2)/float64(rnkOrderRank.RankFiveCount2),2)
	}
	rnk.OrderRankData = rnkOrderRank



	var rnkCustomerRank response.CustomerRankData
	rnkCustomerRank.RankOneGrowthRate = 100
	rnkCustomerRank.RankTwoGrowthRate = 100
	rnkCustomerRank.RankThreeGrowthRate = 100
	rnkCustomerRank.RankFourGrowthRate = 100
	rnkCustomerRank.RankFiveGrowthRate = 100

	customerRankQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
	Joins(joinSql).
	Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_order.state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),7)
	customerRankQuery.Select("count(distinct(t_auto_dispatch_history.user_id)) as customer_count").Pluck("customer_count",&rnkCustomerRank.CustomerCount)

	var customerRanks []response.RankGroup
	customerRankQueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
	Joins(joinSql).
	Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_order.state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),7)
	customerRankQueryNew.Select("count(t_auto_dispatch_history.order_id) as rank_count,user_rank as ranks").Group("user_rank").Scan(&customerRanks)
	for _, v := range customerRanks { 
		switch v.Rank { 
		case 1: 
			rnkCustomerRank.RankOneCount = v.RankCount
		case 2: 
			rnkCustomerRank.RankTwoCount = v.RankCount
		case 3: 
			rnkCustomerRank.RankThreeCount = v.RankCount
		case 4: 
			rnkCustomerRank.RankFourCount = v.RankCount
		case 5: 
			rnkCustomerRank.RankFiveCount = v.RankCount	
		}
	}
	
	customerRank2Query :=ckDb.Model(&models.AutoDispatchHistory{}).
	Joins(joinSql).
	Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_order.state = ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"),7)
	customerRank2Query.Select("count(distinct(t_auto_dispatch_history.user_id)) as customer_count").Pluck("customer_count",&rnkCustomerRank.CustomerCount2)
	var customerRanks2 []response.RankGroup
	customerRank2QueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
	Joins(joinSql).
	Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_order.state = ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"),7)
	customerRank2QueryNew.Select("count(t_auto_dispatch_history.order_id) as rank_count,user_rank as ranks").Group("user_rank").Scan(&customerRanks2)
	for _, v := range customerRanks2 { 
		switch v.Rank { 
		case 1: 
			rnkCustomerRank.RankOneCount2 = v.RankCount
		case 2: 
			rnkCustomerRank.RankTwoCount2 = v.RankCount
		case 3: 
			rnkCustomerRank.RankThreeCount2 = v.RankCount
		case 4: 
			rnkCustomerRank.RankFourCount2 = v.RankCount
		case 5: 
			rnkCustomerRank.RankFiveCount2 = v.RankCount	
	
		}
	}
	if rnkCustomerRank.RankOneCount2 != 0 { 
		rnkCustomerRank.RankOneGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankOneCount-rnkCustomerRank.RankOneCount2)/float64(rnkCustomerRank.RankOneCount2),2)
	}
	if rnkCustomerRank.RankTwoCount2 != 0 { 
		rnkCustomerRank.RankTwoGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankTwoCount-rnkCustomerRank.RankTwoCount2)/float64(rnkCustomerRank.RankTwoCount2),2)
	}
	if rnkCustomerRank.RankThreeCount2 != 0 { 
		rnkCustomerRank.RankThreeGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankThreeCount-rnkCustomerRank.RankThreeCount2)/float64(rnkCustomerRank.RankThreeCount2),2)
	}
	if rnkCustomerRank.RankFourCount2 != 0 { 
		rnkCustomerRank.RankFourGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankFourCount-rnkCustomerRank.RankFourCount2)/float64(rnkCustomerRank.RankFourCount2),2)
	}
	if rnkCustomerRank.RankFiveCount2 != 0 { 
		rnkCustomerRank.RankFiveGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankFiveCount-rnkCustomerRank.RankFiveCount2)/float64(rnkCustomerRank.RankFiveCount2),2)
	}


	//用户等级趋势 
	var customerRankDataDayItems []response.CustomerRankDataDayItems 
	
	customerRankDayQuery :=ckDb.Model(&models.AutoDispatchHistory{}).Joins(joinSql).
	Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? ",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"))
	customerRankDayQueryFields :=`
		DATE(t_order.created_at) AS day,
		COUNT(DISTINCT CASE WHEN user_rank = 1 THEN t_order.user_id END) AS rank_one_count,
		COUNT(DISTINCT CASE WHEN user_rank = 2 THEN t_order.user_id END) AS rank_two_count,
		COUNT(DISTINCT CASE WHEN user_rank = 3 THEN t_order.user_id END) AS rank_three_count,
		COUNT(DISTINCT CASE WHEN user_rank = 4 THEN t_order.user_id END) AS rank_four_count,
		COUNT(DISTINCT CASE WHEN user_rank = 5 THEN t_order.user_id END) AS rank_five_count
	`
	if dbMode ==2 {
		customerRankDayQueryFields =`
		formatDateTime(t_order.created_at, '%Y-%m-%d') AS day,
		COUNT(DISTINCT CASE WHEN user_rank = 1 THEN t_order.user_id END) AS rank_one_count,
		COUNT(DISTINCT CASE WHEN user_rank = 2 THEN t_order.user_id END) AS rank_two_count,
		COUNT(DISTINCT CASE WHEN user_rank = 3 THEN t_order.user_id END) AS rank_three_count,
		COUNT(DISTINCT CASE WHEN user_rank = 4 THEN t_order.user_id END) AS rank_four_count,
		COUNT(DISTINCT CASE WHEN user_rank = 5 THEN t_order.user_id END) AS rank_five_count
	`
	}
	if dbMode ==1 {
		customerRankDayQuery.Select(customerRankDayQueryFields).Group("DATE(t_order.created_at)").Order("day").Scan(&customerRankDataDayItems)
	}
	if dbMode ==2 {
		customerRankDayQuery.Select(customerRankDayQueryFields).Group("formatDateTime(t_order.created_at, '%Y-%m-%d')").Order("day").Scan(&customerRankDataDayItems)
	}



	for k, v := range customerRankDataDayItems {
		customerRankDataDayItems[k].Day = carbon.Parse(v.Day,configs.AsiaShanghai).Format("Y-m-d")
	}

	rnkCustomerRank.DayItems = customerRankDataDayItems


	rnk.CustomerRankData = rnkCustomerRank


	var rnkShipperRank response.ShipperRankData
	rnkShipperRank.RankOneGrowthRate = 100
	rnkShipperRank.RankTwoGrowthRate = 100
	rnkShipperRank.RankThreeGrowthRate = 100
	rnkShipperRank.RankFourGrowthRate = 100
	rnkShipperRank.RankFiveGrowthRate = 100

	shipperRankQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
	Joins(joinSql).
	Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_order.state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0,7)
	shipperRankQuery.Select("count(distinct(t_auto_dispatch_history.shipper_id)) as shipper_count").Pluck("shipper_count",&rnkShipperRank.ShipperCount)
	var shipperRanks []response.RankGroup
	shipperRankQueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
	Joins(joinSql).
	Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_order.state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0,7)
	shipperRankQueryNew.Select("count(distinct(t_auto_dispatch_history.shipper_id)) as rank_count,shipper_rank as ranks").Group("shipper_rank").Scan(&shipperRanks)
	for _, v := range shipperRanks { 
		switch v.Rank { 
		case 1: 
			rnkShipperRank.RankOneCount = v.RankCount
		case 2: 
			rnkShipperRank.RankTwoCount = v.RankCount
		case 3: 
			rnkShipperRank.RankThreeCount = v.RankCount
		case 4: 
			rnkShipperRank.RankFourCount = v.RankCount
		case 5: 
			rnkShipperRank.RankFiveCount = v.RankCount	
	
		}
	}

	shipperRank2Query :=ckDb.Model(&models.AutoDispatchHistory{}).
	Joins(joinSql).
	Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_order.state = ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"),0,7)
	shipperRank2Query.Select("count(distinct(t_auto_dispatch_history.shipper_id)) as shipper_count").Pluck("shipper_count",&rnkShipperRank.ShipperCount2)

	var shipperRanks2 []response.RankGroup
	shipperRank2QueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
	Joins(joinSql).
	Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_order.state = ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"),0,7)
	shipperRank2QueryNew.Select("count(distinct(t_auto_dispatch_history.shipper_id)) as rank_count,shipper_rank as ranks").Group("shipper_rank").Scan(&shipperRanks2)
	for _, v := range shipperRanks2 { 
		switch v.Rank { 
		case 1: 
			rnkShipperRank.RankOneCount2 = v.RankCount
		case 2: 
			rnkShipperRank.RankTwoCount2 = v.RankCount
		case 3: 
			rnkShipperRank.RankThreeCount2 = v.RankCount
		case 4: 
			rnkShipperRank.RankFourCount2 = v.RankCount
		case 5: 
			rnkShipperRank.RankFiveCount2 = v.RankCount	
	
		}
	}
	if rnkShipperRank.RankOneCount2 != 0 { 
		rnkShipperRank.RankOneGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankOneCount-rnkShipperRank.RankOneCount2)/float64(rnkShipperRank.RankOneCount2),2)
	}
	if rnkShipperRank.RankTwoCount2 != 0 { 
		rnkShipperRank.RankTwoGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankTwoCount-rnkShipperRank.RankTwoCount2)/float64(rnkShipperRank.RankTwoCount2),2)
	}
	if rnkShipperRank.RankThreeCount2 != 0 { 
		rnkShipperRank.RankThreeGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankThreeCount-rnkShipperRank.RankThreeCount2)/float64(rnkShipperRank.RankThreeCount2),2)
	}
	if rnkShipperRank.RankFourCount2 != 0 { 
		rnkShipperRank.RankFourGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankFourCount-rnkShipperRank.RankFourCount2)/float64(rnkShipperRank.RankFourCount2),2)
	}
	if rnkShipperRank.RankFiveCount2 != 0 { 
		rnkShipperRank.RankFiveGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankFiveCount-rnkShipperRank.RankFiveCount2)/float64(rnkShipperRank.RankFiveCount2),2)
	}

	//配送员等级趋势 
	var shipperRankDataDayItems []response.CustomerRankDataDayItems 
	
	shipperRankDayQuery :=ckDb.Model(&models.AutoDispatchHistory{}).Joins(joinSql).
	Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? ",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"))
	shipperRankDayQueryFields :=`
		DATE(t_order.created_at) AS day,
		COUNT(DISTINCT CASE WHEN shipper_rank = 1 THEN t_order.shipper_id END) AS rank_one_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 2 THEN t_order.shipper_id END) AS rank_two_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 3 THEN t_order.shipper_id END) AS rank_three_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 4 THEN t_order.shipper_id END) AS rank_four_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 5 THEN t_order.shipper_id END) AS rank_five_count
	`
	if dbMode ==2 {
		shipperRankDayQueryFields =`
		formatDateTime(t_order.created_at, '%Y-%m-%d') as day,
		COUNT(DISTINCT CASE WHEN shipper_rank = 1 THEN t_order.shipper_id END) AS rank_one_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 2 THEN t_order.shipper_id END) AS rank_two_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 3 THEN t_order.shipper_id END) AS rank_three_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 4 THEN t_order.shipper_id END) AS rank_four_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 5 THEN t_order.shipper_id END) AS rank_five_count
	`
	}

	if dbMode ==1 {
		shipperRankDayQuery.Select(shipperRankDayQueryFields).Group("DATE(t_order.created_at)").Order("day").Scan(&shipperRankDataDayItems)
	}
	if dbMode ==2 {
		shipperRankDayQuery.Select(shipperRankDayQueryFields).Group("formatDateTime(t_order.created_at, '%Y-%m-%d')").Order("day").Debug().Scan(&shipperRankDataDayItems)
	}


	for k, v := range shipperRankDataDayItems {
		shipperRankDataDayItems[k].Day = carbon.Parse(v.Day,configs.AsiaShanghai).Format("Y-m-d")
	}

	rnkShipperRank.DayItems = shipperRankDataDayItems

	rnk.ShipperRankData = rnkShipperRank



	var rnkOrderCustomerData response.OrderCustomerData

	orderCustomerQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
		Joins(joinSql).
		Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_order.state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),7)

	
	orderCustomerQuery.Select("count(t_auto_dispatch_history.order_id) as order_count").Pluck("order_count",&rnkOrderCustomerData.OrderCount)
	orderCustomerQuery.Select("ifnull(sum(t_order.order_price),0) as order_price").Pluck("order_price",&rnkOrderCustomerData.OrderPrice)

	customerCountFields :=`
		count(distinct(t_auto_dispatch_history.user_id)) as customer_count,
		COUNT(DISTINCT CASE WHEN user_rank = 1 THEN t_auto_dispatch_history.user_id END) AS customer_rank_one_count,
		COUNT(DISTINCT CASE WHEN user_rank = 2 THEN t_auto_dispatch_history.user_id END) AS customer_rank_two_count,
		COUNT(DISTINCT CASE WHEN user_rank = 3 THEN t_auto_dispatch_history.user_id END) AS customer_rank_three_count,
		COUNT(DISTINCT CASE WHEN user_rank = 4 THEN t_auto_dispatch_history.user_id END) AS customer_rank_four_count,
		COUNT(DISTINCT CASE WHEN user_rank = 5 THEN t_auto_dispatch_history.user_id END) AS customer_rank_five_count
	
	`	
	var customerCountData response.OrderCustomerCountData
	orderCustomerQuery.Select(customerCountFields).Scan(&customerCountData)

	rnkOrderCustomerData.CustomerCount = int64(customerCountData.CustomerCount)

	
	
	var orderCustomerDataGorm []response.OrderCustomerDataGorm
	orderCustomerQueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
		Joins(joinSql).
		Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_order.state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),7)
	orderCustomerQueryNew.Where("t_auto_dispatch_history.user_rank between ? and ?",1,5).
	Where("t_auto_dispatch_history.order_rank_org between ? and ?",1,5).
	Group("t_auto_dispatch_history.user_rank,t_auto_dispatch_history.order_rank_org").
	Order("t_auto_dispatch_history.user_rank,t_auto_dispatch_history.order_rank_org").
	Select("t_auto_dispatch_history.user_rank as user_rank,t_auto_dispatch_history.order_rank_org as order_rank,count(t_auto_dispatch_history.order_id) as order_count,ifnull(sum(t_order.order_price),0) as order_price").
	Scan(&orderCustomerDataGorm)




	for i := 1; i < 6; i++ {
		var orderCustomerDataItem response.OrderCustomerDataItems
		orderCustomerDataItem.CustomerRank = i

		switch i { 
			case 1: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankOneCount)
			case 2: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankTwoCount)
			case 3: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankThreeCount)
			case 4: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankFourCount)
			case 5: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankFiveCount)
	
		}

		for _, v := range orderCustomerDataGorm { 
			if v.UserRank != i {
				continue
			}
			switch v.OrderRank { 
				case 5: 
					orderCustomerDataItem.OrderRankFiveCount += v.OrderCount
					orderCustomerDataItem.OrderRankFivePrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankFiveCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankFivePrice += v.OrderPrice
				case 4: 
					orderCustomerDataItem.OrderRankFourCount += v.OrderCount
					orderCustomerDataItem.OrderRankFourPrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankFourCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankFourPrice += v.OrderPrice
				case 3: 
					orderCustomerDataItem.OrderRankThreeCount += v.OrderCount
					orderCustomerDataItem.OrderRankThreePrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankThreeCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankThreePrice += v.OrderPrice
				case 2: 
					orderCustomerDataItem.OrderRankTwoCount += v.OrderCount
					orderCustomerDataItem.OrderRankTwoPrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankTwoCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankTwoPrice += v.OrderPrice
				case 1: 
					orderCustomerDataItem.OrderRankOneCount += v.OrderCount
					orderCustomerDataItem.OrderRankOnePrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankOneCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankOnePrice += v.OrderPrice
			}

		}
		rnkOrderCustomerData.Items = append(rnkOrderCustomerData.Items, orderCustomerDataItem)
		
	}

	rnk.OrderCustomerData = rnkOrderCustomerData
	

	var rnkOrderShipperData response.OrderShipperData

	orderShipperQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
		Joins(joinSql).
		Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_order.state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0,7)

	
	orderShipperQuery.Select("count(t_auto_dispatch_history.order_id) as order_count").Pluck("order_count",&rnkOrderShipperData.OrderCount)
	orderShipperQuery.Select("ifnull(sum(t_order.order_price),0) as order_price").Pluck("order_price",&rnkOrderShipperData.OrderPrice)

	shipperCountFields :=`
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 1 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_one_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 2 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_two_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 3 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_three_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 4 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_four_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 5 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_five_count
	
	`	
	var shipperCountData response.OrderShipperCountData
	orderCustomerQuery.Select(shipperCountFields).Scan(&shipperCountData)

	rnkOrderShipperData.ShipperCount = int64(shipperCountData.ShipperRankOneCount)+int64(shipperCountData.ShipperRankTwoCount)+int64(shipperCountData.ShipperRankThreeCount)+int64(shipperCountData.ShipperRankFourCount)+int64(shipperCountData.ShipperRankFiveCount)


	
		
	
	var orderShipperDataGorm []response.OrderShipperDataGorm
	orderShipperQueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
		Joins(joinSql).
		Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_order.state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0,7)
	orderShipperQueryNew.Where("t_auto_dispatch_history.user_rank between ? and ?",1,5).
	Where("t_auto_dispatch_history.order_rank_org between ? and ?",1,5).
	Group("t_auto_dispatch_history.shipper_rank,t_auto_dispatch_history.order_rank_org").
	Order("t_auto_dispatch_history.shipper_rank,t_auto_dispatch_history.order_rank_org").
	Select("t_auto_dispatch_history.shipper_rank as shipper_rank,t_auto_dispatch_history.order_rank_org as order_rank,count(t_auto_dispatch_history.order_id) as order_count,ifnull(sum(t_order.order_price),0) as order_price").
	Scan(&orderShipperDataGorm)




	for i := 1; i < 6; i++ {
		var orderShipperDataItem response.OrderShipperDataItems
		orderShipperDataItem.ShipperRank = i
		switch i {
			case 1:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankOneCount)
			case 2:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankTwoCount)
			case 3:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankThreeCount)
			case 4:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankFourCount)
			case 5:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankFiveCount)
		}

		for _, v := range orderShipperDataGorm { 
			
			if v.ShipperRank !=i {
				continue
			}

			switch v.OrderRank { 
				case 5: 
					orderShipperDataItem.OrderRankFiveCount += v.OrderCount
					orderShipperDataItem.OrderRankFivePrice += v.OrderPrice
					rnkOrderShipperData.OrderRankFiveCount+=v.OrderCount
					orderShipperDataItem.OrderRankFivePrice += v.OrderPrice


				case 4: 
					orderShipperDataItem.OrderRankFourCount += v.OrderCount
					orderShipperDataItem.OrderRankFourPrice += v.OrderPrice
					rnkOrderShipperData.OrderRankFourCount+=v.OrderCount
					orderShipperDataItem.OrderRankFourPrice += v.OrderPrice
				case 3: 
					orderShipperDataItem.OrderRankThreeCount += v.OrderCount
					orderShipperDataItem.OrderRankThreePrice += v.OrderPrice
					rnkOrderShipperData.OrderRankThreeCount+=v.OrderCount
					orderShipperDataItem.OrderRankThreePrice += v.OrderPrice
				case 2: 
					orderShipperDataItem.OrderRankTwoCount += v.OrderCount
					orderShipperDataItem.OrderRankTwoPrice += v.OrderPrice
					rnkOrderShipperData.OrderRankTwoCount+=v.OrderCount
					orderShipperDataItem.OrderRankTwoPrice += v.OrderPrice

				case 1: 
					orderShipperDataItem.OrderRankOneCount += v.OrderCount
					orderShipperDataItem.OrderRankOnePrice += v.OrderPrice
					rnkOrderShipperData.OrderRankOneCount+=v.OrderCount
					orderShipperDataItem.OrderRankOnePrice += v.OrderPrice

			}

		}
		rnkOrderShipperData.Items = append(rnkOrderShipperData.Items, orderShipperDataItem)
		
	}

	rnk.OrderShipperData = rnkOrderShipperData


	var rnkOrderLateData response.OrderLateData


	orderLateQueryFront :=ckDb.Model(&models.AutoDispatchHistory{}).
		Joins(joinSql)

	orderLateQuery :=orderLateQueryFront.Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_order.state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0,7)

	
	orderLateQuery.Where("t_auto_dispatch_history.order_deliver_state >= ?",0).Select("count(t_auto_dispatch_history.order_id) as order_count").Pluck("order_count",&rnkOrderLateData.OrderCount)
	
	orderLateQuery.Where("t_auto_dispatch_history.order_deliver_state >= ?",0).Select("ifnull(sum(t_order.order_price),0) as order_price").Pluck("order_price",&rnkOrderLateData.OrderPrice)
	
	var orderLateDataGorm []response.OrderLateDataGorm
	orderLateQuery.
	Where("t_auto_dispatch_history.user_rank between ? and ?",1,5).
	Where("t_auto_dispatch_history.order_rank_org between ? and ?",1,5).
	Group("t_auto_dispatch_history.order_deliver_state,t_auto_dispatch_history.order_rank_org").
	Order("t_auto_dispatch_history.order_deliver_state,t_auto_dispatch_history.order_rank_org").
	Select("t_auto_dispatch_history.order_deliver_state as late_rank,t_auto_dispatch_history.order_rank_org as order_rank,count(t_auto_dispatch_history.order_id) as order_count,ifnull(sum(t_order.order_price),0) as order_price").
	Scan(&orderLateDataGorm)


	shipperLateFields :=`
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 0 THEN t_auto_dispatch_history.order_id END) AS late_rank_zero_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 1 THEN t_auto_dispatch_history.order_id END) AS late_rank_one_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 2 THEN t_auto_dispatch_history.order_id END) AS late_rank_two_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 3 THEN t_auto_dispatch_history.order_id END) AS late_rank_three_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 4 THEN t_auto_dispatch_history.order_id END) AS late_rank_four_count
	`	
	var shipperLateData response.OrderLateCountData
	orderLateCountQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
		Joins(joinSql).Where("t_auto_dispatch_history.area_id = ? and t_order.created_at >=? and t_order.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_order.state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0,7)
	orderLateCountQuery.Select(shipperLateFields).Scan(&shipperLateData)
	rnkOrderLateData.LateMinute = shipperLateData.LateRankOneCount+shipperLateData.LateRankTwoCount+shipperLateData.LateRankThreeCount+shipperLateData.LateRankFourCount
 


	for i := 0; i < 5; i++ {
		var orderLateDataItem response.OrderLateDataItems
		orderLateDataItem.LateState = i
		
		switch i {
			case 0:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankZeroCount)
			case 1:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankOneCount)
			case 2:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankTwoCount)
			case 3:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankThreeCount)
			case 4:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankFourCount)	
			
		}
		
		for _, v := range orderLateDataGorm { 
				if v.LateRank != i {
					continue
				}
				switch v.OrderRank { 
						case 5: 
							orderLateDataItem.OrderRankFiveCount += v.OrderCount
							orderLateDataItem.OrderRankFivePrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankFiveCount+=v.OrderCount
							rnkOrderLateData.OrderRankFivePrice+=v.OrderPrice
							
						case 4: 
							orderLateDataItem.OrderRankFourCount += v.OrderCount
							orderLateDataItem.OrderRankFourPrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankFourCount+=v.OrderCount
							rnkOrderLateData.OrderRankFourPrice+=v.OrderPrice
						case 3: 
							orderLateDataItem.OrderRankThreeCount += v.OrderCount
							orderLateDataItem.OrderRankThreePrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankThreeCount+=v.OrderCount
							rnkOrderLateData.OrderRankThreePrice+=v.OrderPrice
						case 2: 
							orderLateDataItem.OrderRankTwoCount += v.OrderCount
							orderLateDataItem.OrderRankTwoPrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankTwoCount+=v.OrderCount
							rnkOrderLateData.OrderRankTwoPrice+=v.OrderPrice
						case 1: 
							orderLateDataItem.OrderRankOneCount += v.OrderCount
							orderLateDataItem.OrderRankOnePrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankOneCount+=v.OrderCount
							rnkOrderLateData.OrderRankOnePrice+=v.OrderPrice
							
				}

				
			

			

		}
	rnkOrderLateData.Items = append(rnkOrderLateData.Items, orderLateDataItem)
	
}


	rnk.OrderLateData = rnkOrderLateData



	return rnk, nil
}




func (s OrderRankService) GetOrderRankStatJoinNoJoin(ckDb *gorm.DB,senderID int64, senderType int, cityId int, areaId int, kw string, startDate string, endDate string) (response.OrderRank, error) {
	var rnk response.OrderRank	
	// db := tools.GetDB()


	orderPrice :=int64(0)
	orderCount :=int64(0)
	customerCount :=int64(0)
	shipperCount :=int64(0)
	avgDeliveryTimeSum :=float64(0)
	avgDeliveryTime :=float64(0)

	orderPrice2 :=int64(0)
	orderCount2 :=int64(0)
	customerCount2 :=int64(0)
	shipperCount2 :=int64(0)
	avgDeliveryTimeSum2 :=float64(0)
	avgDeliveryTime2 :=float64(0)

	startTime :=carbon.Parse(startDate, configs.AsiaShanghai)
	endTime :=carbon.Parse(endDate, configs.AsiaShanghai)

	days :=endTime.DiffAbsInDays(startTime)
	startTime2 :=startTime.AddDays(-int(days))
	endTime2 :=endTime.AddDays(-int(days))
	

	
	orderQuery :=ckDb.Model(&models.Order{}).Where("area_id = ? and state =? and created_at >=? and created_at < ?",areaId,7,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"))
	

	var orderData response.OrderDataGorm

	orderQuery.Select("ifnull(sum(order_price),0) as order_price,count(1) as order_count,count(distinct(user_id)) as customer_count,count(distinct(shipper_id)) as shipper_count").Scan(&orderData)

	
	orderPrice = orderData.OrderPrice
	orderCount = orderData.OrderCount
	customerCount = orderData.CustomerCount
	shipperCount = orderData.ShipperCount

	orderQuery.Where("shipper_id > ?",0).Select("ifnull(avg(TIMESTAMPDIFF(SECOND, delivery_start_time, delivery_end_time)),0) as delivery_time").Pluck("delivery_time",&avgDeliveryTimeSum)

	orderQuery2 :=ckDb.Model(&models.Order{}).Where("area_id = ? and state =? and created_at >=? and created_at < ?",areaId,7,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"))
	

	var orderData2 response.OrderDataGorm

	orderQuery2.Select("ifnull(sum(order_price),0) as order_price,count(1) as order_count,count(distinct(user_id)) as customer_count,count(distinct(shipper_id)) as shipper_count").Scan(&orderData2)
	

	orderQuery2.Where("shipper_id > ?",0).Select("ifnull(avg(TIMESTAMPDIFF(SECOND, delivery_start_time, delivery_end_time)),0) as delivery_time").Pluck("delivery_time",&avgDeliveryTimeSum2)
	
	orderPrice2 = orderData2.OrderPrice
	orderCount2 = orderData2.OrderCount
	customerCount2 = orderData2.CustomerCount
	shipperCount2 = orderData2.ShipperCount



	

	rnk.OrderPrice = orderPrice
	rnk.OrderCount = orderCount
	rnk.CustomerCount = customerCount
	rnk.ShipperCount = shipperCount
	if orderCount > 0 { 
		avgDeliveryTime = float64(avgDeliveryTimeSum)
	}
	rnk.AvgDeliveryTime = float64(avgDeliveryTime)

	rnk.OrderPriceGrowthRate = 100
	rnk.OrderCountGrowthRate = 100
	rnk.CustomerCountGrowthRate = 100
	rnk.ShipperCountGrowthRate = 100
	rnk.AvgDeliveryTimeGrowthRate = 100

	//升级比例 = (新等级-旧等级)/旧等级
	if orderCount2 != 0 { 
		avgDeliveryTime2 = tools.ToFloat64(float64(avgDeliveryTimeSum2),2)
		rnk.OrderCountGrowthRate=tools.ToFloat64(float64(orderCount-orderCount2)/float64(orderCount2),2)   //升级比例 = (新等级-旧等级)/旧等级
		rnk.OrderPriceGrowthRate=tools.ToFloat64(float64(orderPrice-orderPrice2)/float64(orderPrice2),2)
		rnk.AvgDeliveryTimeGrowthRate=tools.ToFloat64(float64(avgDeliveryTime-avgDeliveryTime2)/float64(avgDeliveryTime2),2)   //升级比例
	}
	if customerCount2 != 0 { 
		rnk.CustomerCountGrowthRate=tools.ToFloat64(float64(customerCount-customerCount2)/float64(customerCount2),2)   //升级比例 = (新等级-旧等级)/旧等级
	}
	if shipperCount2 != 0 { 
		rnk.ShipperCountGrowthRate=tools.ToFloat64(float64(shipperCount-shipperCount2)/float64(shipperCount2),2)   //升级比例 = (新等级-旧等级)/旧等级
	}
	rnk.AvgDeliveryTime = float64(avgDeliveryTime)/60

	var rnkOrderRank response.OrderRankData
	rnkOrderRank.RankOneGrowthRate = 100
	rnkOrderRank.RankTwoGrowthRate = 100
	rnkOrderRank.RankThreeGrowthRate = 100
	rnkOrderRank.RankFourGrowthRate = 100
	rnkOrderRank.RankFiveGrowthRate = 100

	orderRankQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
	Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.order_state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),7)
	orderRankQuery.Count(&rnkOrderRank.OrderCount)
	
	var orderRanks []response.RankGroup
	

	orderRankQuery.Select("count(t_auto_dispatch_history.order_id) as rank_count,order_rank_org as ranks").Group("order_rank_org").Scan(&orderRanks)

	var orderRanks2 []response.RankGroup
	orderRank2Query :=ckDb.Model(&models.AutoDispatchHistory{}).
	Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.order_state = ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"),7)
	orderRank2Query.Select("count(t_auto_dispatch_history.order_id) as order_rank_count,order_rank_org as ranks").Group("order_rank_org").Scan(&orderRanks2)

	for _, v := range orderRanks {
		switch v.Rank { 
		case 1: 
			rnkOrderRank.RankOneCount = v.RankCount
		case 2: 
			rnkOrderRank.RankTwoCount = v.RankCount
		case 3: 
			rnkOrderRank.RankThreeCount = v.RankCount
		case 4: 
			rnkOrderRank.RankFourCount = v.RankCount
		case 5: 
			rnkOrderRank.RankFiveCount = v.RankCount
		}
	}

	for _, v := range orderRanks2 {
		switch v.Rank { 
		case 1: 
			rnkOrderRank.RankOneCount2 = v.RankCount
		case 2: 
			rnkOrderRank.RankTwoCount2 = v.RankCount
		case 3: 
			rnkOrderRank.RankThreeCount2 = v.RankCount
		case 4: 
			rnkOrderRank.RankFourCount2 = v.RankCount
		case 5: 
			rnkOrderRank.RankFiveCount2 = v.RankCount
		}
	}
	if rnkOrderRank.RankOneCount2 != 0 { 
		rnkOrderRank.RankOneGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankOneCount-rnkOrderRank.RankOneCount2)/float64(rnkOrderRank.RankOneCount2),2)
	}
	if rnkOrderRank.RankTwoCount2 != 0 { 
		rnkOrderRank.RankTwoGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankTwoCount-rnkOrderRank.RankTwoCount2)/float64(rnkOrderRank.RankTwoCount2),2)
	}
	if rnkOrderRank.RankThreeCount2 != 0 { 
		rnkOrderRank.RankThreeGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankThreeCount-rnkOrderRank.RankThreeCount2)/float64(rnkOrderRank.RankThreeCount2),2)
	}
	if rnkOrderRank.RankFourCount2 != 0 { 
		rnkOrderRank.RankFourGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankFourCount-rnkOrderRank.RankFourCount2)/float64(rnkOrderRank.RankFourCount2),2)
	}
	if rnkOrderRank.RankFiveCount2 != 0 { 
		rnkOrderRank.RankFiveGrowthRate=tools.ToFloat64(float64(rnkOrderRank.RankFiveCount-rnkOrderRank.RankFiveCount2)/float64(rnkOrderRank.RankFiveCount2),2)
	}
	rnk.OrderRankData = rnkOrderRank



	var rnkCustomerRank response.CustomerRankData
	rnkCustomerRank.RankOneGrowthRate = 100
	rnkCustomerRank.RankTwoGrowthRate = 100
	rnkCustomerRank.RankThreeGrowthRate = 100
	rnkCustomerRank.RankFourGrowthRate = 100
	rnkCustomerRank.RankFiveGrowthRate = 100

	customerRankQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
	Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.order_state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),7)
	customerRankQuery.Select("count(distinct(t_auto_dispatch_history.user_id)) as customer_count").Pluck("customer_count",&rnkCustomerRank.CustomerCount)

	var customerRanks []response.RankGroup
	customerRankQueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
	Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.order_state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),7)
	customerRankQueryNew.Select("count(t_auto_dispatch_history.order_id) as rank_count,user_rank as ranks").Group("user_rank").Scan(&customerRanks)
	for _, v := range customerRanks { 
		switch v.Rank { 
		case 1: 
			rnkCustomerRank.RankOneCount = v.RankCount
		case 2: 
			rnkCustomerRank.RankTwoCount = v.RankCount
		case 3: 
			rnkCustomerRank.RankThreeCount = v.RankCount
		case 4: 
			rnkCustomerRank.RankFourCount = v.RankCount
		case 5: 
			rnkCustomerRank.RankFiveCount = v.RankCount	
		}
	}
	
	customerRank2Query :=ckDb.Model(&models.AutoDispatchHistory{}).
	Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.order_state = ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"),7)
	customerRank2Query.Select("count(distinct(t_auto_dispatch_history.user_id)) as customer_count").Pluck("customer_count",&rnkCustomerRank.CustomerCount2)
	var customerRanks2 []response.RankGroup
	customerRank2QueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
	Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.order_state = ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"),7)
	customerRank2QueryNew.Select("count(t_auto_dispatch_history.order_id) as rank_count,user_rank as ranks").Group("user_rank").Scan(&customerRanks2)
	for _, v := range customerRanks2 { 
		switch v.Rank { 
		case 1: 
			rnkCustomerRank.RankOneCount2 = v.RankCount
		case 2: 
			rnkCustomerRank.RankTwoCount2 = v.RankCount
		case 3: 
			rnkCustomerRank.RankThreeCount2 = v.RankCount
		case 4: 
			rnkCustomerRank.RankFourCount2 = v.RankCount
		case 5: 
			rnkCustomerRank.RankFiveCount2 = v.RankCount	
	
		}
	}
	if rnkCustomerRank.RankOneCount2 != 0 { 
		rnkCustomerRank.RankOneGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankOneCount-rnkCustomerRank.RankOneCount2)/float64(rnkCustomerRank.RankOneCount2),2)
	}
	if rnkCustomerRank.RankTwoCount2 != 0 { 
		rnkCustomerRank.RankTwoGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankTwoCount-rnkCustomerRank.RankTwoCount2)/float64(rnkCustomerRank.RankTwoCount2),2)
	}
	if rnkCustomerRank.RankThreeCount2 != 0 { 
		rnkCustomerRank.RankThreeGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankThreeCount-rnkCustomerRank.RankThreeCount2)/float64(rnkCustomerRank.RankThreeCount2),2)
	}
	if rnkCustomerRank.RankFourCount2 != 0 { 
		rnkCustomerRank.RankFourGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankFourCount-rnkCustomerRank.RankFourCount2)/float64(rnkCustomerRank.RankFourCount2),2)
	}
	if rnkCustomerRank.RankFiveCount2 != 0 { 
		rnkCustomerRank.RankFiveGrowthRate=tools.ToFloat64(float64(rnkCustomerRank.RankFiveCount-rnkCustomerRank.RankFiveCount2)/float64(rnkCustomerRank.RankFiveCount2),2)
	}

	//用户等级趋势 
	var customerRankDataDayItems []response.CustomerRankDataDayItems 
	
	customerRankDayQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
	Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? ",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"))
	customerRankDayQueryFields :=`
		DATE(created_at) AS day,
		COUNT(DISTINCT CASE WHEN user_rank = 1 THEN user_id END) AS rank_one_count,
		COUNT(DISTINCT CASE WHEN user_rank = 2 THEN user_id END) AS rank_two_count,
		COUNT(DISTINCT CASE WHEN user_rank = 3 THEN user_id END) AS rank_three_count,
		COUNT(DISTINCT CASE WHEN user_rank = 4 THEN user_id END) AS rank_four_count,
		COUNT(DISTINCT CASE WHEN user_rank = 5 THEN user_id END) AS rank_five_count
	`
	customerRankDayQuery.Select(customerRankDayQueryFields).Group("DATE(created_at)").Order("day").Scan(&customerRankDataDayItems)

	for k, v := range customerRankDataDayItems {
		customerRankDataDayItems[k].Day = carbon.Parse(v.Day,configs.AsiaShanghai).Format("Y-m-d")
	}

	rnkCustomerRank.DayItems = customerRankDataDayItems



	rnk.CustomerRankData = rnkCustomerRank


	var rnkShipperRank response.ShipperRankData
	rnkShipperRank.RankOneGrowthRate = 100
	rnkShipperRank.RankTwoGrowthRate = 100
	rnkShipperRank.RankThreeGrowthRate = 100
	rnkShipperRank.RankFourGrowthRate = 100
	rnkShipperRank.RankFiveGrowthRate = 100

	shipperRankQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
	Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_auto_dispatch_history.order_state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0,7)
	shipperRankQuery.Select("count(distinct(t_auto_dispatch_history.shipper_id)) as shipper_count").Pluck("shipper_count",&rnkShipperRank.ShipperCount)
	var shipperRanks []response.RankGroup
	shipperRankQueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
	Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_auto_dispatch_history.order_state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0,7)
	shipperRankQueryNew.Select("count(distinct(t_auto_dispatch_history.shipper_id)) as rank_count,shipper_rank as ranks").Group("shipper_rank").Scan(&shipperRanks)
	for _, v := range shipperRanks { 
		switch v.Rank { 
		case 1: 
			rnkShipperRank.RankOneCount = v.RankCount
		case 2: 
			rnkShipperRank.RankTwoCount = v.RankCount
		case 3: 
			rnkShipperRank.RankThreeCount = v.RankCount
		case 4: 
			rnkShipperRank.RankFourCount = v.RankCount
		case 5: 
			rnkShipperRank.RankFiveCount = v.RankCount	
	
		}
	}

	shipperRank2Query :=ckDb.Model(&models.AutoDispatchHistory{}).
	Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_auto_dispatch_history.order_state = ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"),0,7)
	shipperRank2Query.Select("count(distinct(t_auto_dispatch_history.shipper_id)) as shipper_count").Pluck("shipper_count",&rnkShipperRank.ShipperCount2)

	var shipperRanks2 []response.RankGroup
	shipperRank2QueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
	Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_auto_dispatch_history.order_state = ?",areaId,startTime2.Format("Y-m-d 00:00:00"),endTime2.Format("Y-m-d 23:59:59"),0,7)
	shipperRank2QueryNew.Select("count(distinct(t_auto_dispatch_history.shipper_id)) as rank_count,shipper_rank as ranks").Group("shipper_rank").Scan(&shipperRanks2)
	for _, v := range shipperRanks2 { 
		switch v.Rank { 
		case 1: 
			rnkShipperRank.RankOneCount2 = v.RankCount
		case 2: 
			rnkShipperRank.RankTwoCount2 = v.RankCount
		case 3: 
			rnkShipperRank.RankThreeCount2 = v.RankCount
		case 4: 
			rnkShipperRank.RankFourCount2 = v.RankCount
		case 5: 
			rnkShipperRank.RankFiveCount2 = v.RankCount	
	
		}
	}
	if rnkShipperRank.RankOneCount2 != 0 { 
		rnkShipperRank.RankOneGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankOneCount-rnkShipperRank.RankOneCount2)/float64(rnkShipperRank.RankOneCount2),2)
	}
	if rnkShipperRank.RankTwoCount2 != 0 { 
		rnkShipperRank.RankTwoGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankTwoCount-rnkShipperRank.RankTwoCount2)/float64(rnkShipperRank.RankTwoCount2),2)
	}
	if rnkShipperRank.RankThreeCount2 != 0 { 
		rnkShipperRank.RankThreeGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankThreeCount-rnkShipperRank.RankThreeCount2)/float64(rnkShipperRank.RankThreeCount2),2)
	}
	if rnkShipperRank.RankFourCount2 != 0 { 
		rnkShipperRank.RankFourGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankFourCount-rnkShipperRank.RankFourCount2)/float64(rnkShipperRank.RankFourCount2),2)
	}
	if rnkShipperRank.RankFiveCount2 != 0 { 
		rnkShipperRank.RankFiveGrowthRate=tools.ToFloat64(float64(rnkShipperRank.RankFiveCount-rnkShipperRank.RankFiveCount2)/float64(rnkShipperRank.RankFiveCount2),2)
	}

	//配送员等级趋势 
	var shipperRankDataDayItems []response.CustomerRankDataDayItems 
	
	shipperRankDayQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
	Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? ",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"))
	shipperRankDayQueryFields :=`
		DATE(created_at) AS day,
		COUNT(DISTINCT CASE WHEN shipper_rank = 1 THEN shipper_id END) AS rank_one_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 2 THEN shipper_id END) AS rank_two_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 3 THEN shipper_id END) AS rank_three_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 4 THEN shipper_id END) AS rank_four_count,
		COUNT(DISTINCT CASE WHEN shipper_rank = 5 THEN shipper_id END) AS rank_five_count
	`
	shipperRankDayQuery.Select(shipperRankDayQueryFields).Group("DATE(created_at)").Order("day").Scan(&shipperRankDataDayItems)

	for k, v := range shipperRankDataDayItems {
		shipperRankDataDayItems[k].Day = carbon.Parse(v.Day,configs.AsiaShanghai).Format("Y-m-d")
	}

	rnkShipperRank.DayItems = shipperRankDataDayItems

	rnk.ShipperRankData = rnkShipperRank



	var rnkOrderCustomerData response.OrderCustomerData

	orderCustomerQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
		Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.order_state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),7)

	
	orderCustomerQuery.Select("count(t_auto_dispatch_history.order_id) as order_count").Pluck("order_count",&rnkOrderCustomerData.OrderCount)
	orderCustomerQuery.Select("ifnull(sum(t_auto_dispatch_history.order_price),0) as order_price").Pluck("order_price",&rnkOrderCustomerData.OrderPrice)


	
	customerCountFields :=`
		count(distinct(t_auto_dispatch_history.user_id)) as customer_count,
		COUNT(DISTINCT CASE WHEN user_rank = 1 THEN t_auto_dispatch_history.user_id END) AS customer_rank_one_count,
		COUNT(DISTINCT CASE WHEN user_rank = 2 THEN t_auto_dispatch_history.user_id END) AS customer_rank_two_count,
		COUNT(DISTINCT CASE WHEN user_rank = 3 THEN t_auto_dispatch_history.user_id END) AS customer_rank_three_count,
		COUNT(DISTINCT CASE WHEN user_rank = 4 THEN t_auto_dispatch_history.user_id END) AS customer_rank_four_count,
		COUNT(DISTINCT CASE WHEN user_rank = 5 THEN t_auto_dispatch_history.user_id END) AS customer_rank_five_count
	
	`	
	var customerCountData response.OrderCustomerCountData
	orderCustomerQuery.Select(customerCountFields).Scan(&customerCountData)

	rnkOrderCustomerData.CustomerCount = int64(customerCountData.CustomerCount)

	
	
	var orderCustomerDataGorm []response.OrderCustomerDataGorm
	orderCustomerQueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
		Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.order_state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),7)
	orderCustomerQueryNew.Where("t_auto_dispatch_history.user_rank between ? and ?",1,5).
	Where("t_auto_dispatch_history.order_rank_org between ? and ?",1,5).
	Group("t_auto_dispatch_history.user_rank,t_auto_dispatch_history.order_rank_org").
	Order("t_auto_dispatch_history.user_rank,t_auto_dispatch_history.order_rank_org").
	Select("t_auto_dispatch_history.user_rank as user_rank,t_auto_dispatch_history.order_rank_org as order_rank,count(t_auto_dispatch_history.order_id) as order_count,ifnull(sum(t_auto_dispatch_history.order_price),0) as order_price").
	Scan(&orderCustomerDataGorm)


	for i := 1; i < 6; i++ {
		var orderCustomerDataItem response.OrderCustomerDataItems
		orderCustomerDataItem.CustomerRank = i

		
		switch i { 
			case 1: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankOneCount)
			case 2: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankTwoCount)
			case 3: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankThreeCount)
			case 4: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankFourCount)
			case 5: 
				orderCustomerDataItem.CustomerCount = int(customerCountData.CustomerRankFiveCount)
	
		}

		for _, v := range orderCustomerDataGorm { 
			if v.UserRank != i {
				continue
			}
			switch v.OrderRank { 
				case 5: 
					orderCustomerDataItem.OrderRankFiveCount += v.OrderCount
					orderCustomerDataItem.OrderRankFivePrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankFiveCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankFivePrice += v.OrderPrice
				case 4: 
					orderCustomerDataItem.OrderRankFourCount += v.OrderCount
					orderCustomerDataItem.OrderRankFourPrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankFourCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankFourPrice += v.OrderPrice
				case 3: 
					orderCustomerDataItem.OrderRankThreeCount += v.OrderCount
					orderCustomerDataItem.OrderRankThreePrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankThreeCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankThreePrice += v.OrderPrice
				case 2: 
					orderCustomerDataItem.OrderRankTwoCount += v.OrderCount
					orderCustomerDataItem.OrderRankTwoPrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankTwoCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankTwoPrice += v.OrderPrice
				case 1: 
					orderCustomerDataItem.OrderRankOneCount += v.OrderCount
					orderCustomerDataItem.OrderRankOnePrice += v.OrderPrice
					rnkOrderCustomerData.OrderRankOneCount+=v.OrderCount
					rnkOrderCustomerData.OrderRankOnePrice += v.OrderPrice
			}

		}
		rnkOrderCustomerData.Items = append(rnkOrderCustomerData.Items, orderCustomerDataItem)
		
	}

	rnk.OrderCustomerData = rnkOrderCustomerData
	

	var rnkOrderShipperData response.OrderShipperData

	orderShipperQuery :=ckDb.Model(&models.AutoDispatchHistory{}).
		Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_auto_dispatch_history.order_state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0,7)

	
	orderShipperQuery.Select("count(t_auto_dispatch_history.order_id) as order_count").Pluck("order_count",&rnkOrderShipperData.OrderCount)
	orderShipperQuery.Select("ifnull(sum(t_auto_dispatch_history.order_price),0) as order_price").Pluck("order_price",&rnkOrderShipperData.OrderPrice)


	

	rnkOrderCustomerData.CustomerCount = int64(customerCountData.CustomerCount)

	orderShipperQuery.Select("count(distinct(t_auto_dispatch_history.shipper_id)) as shipper_count").Pluck("shipper_count",&rnkOrderShipperData.ShipperCount)
		
	
	var orderShipperDataGorm []response.OrderShipperDataGorm
	orderShipperQueryNew :=ckDb.Model(&models.AutoDispatchHistory{}).
		Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_auto_dispatch_history.order_state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0,7)
	orderShipperQueryNew.Where("t_auto_dispatch_history.user_rank between ? and ?",1,5).
	Where("t_auto_dispatch_history.order_rank_org between ? and ?",1,5).
	Group("t_auto_dispatch_history.shipper_rank,t_auto_dispatch_history.order_rank_org").
	Order("t_auto_dispatch_history.shipper_rank,t_auto_dispatch_history.order_rank_org").
	Select("t_auto_dispatch_history.shipper_rank as shipper_rank,t_auto_dispatch_history.order_rank_org as order_rank,count(t_auto_dispatch_history.order_id) as order_count,ifnull(sum(t_auto_dispatch_history.order_price),0) as order_price").
	Scan(&orderShipperDataGorm)

	var orderRankGormOrderShipper []response.OrderRankGorm
	orderShipperQueryNew.
	Where("t_auto_dispatch_history.order_rank_org between ? and ?",1,5).
	Select("user_rank,order_rank_org as order_rank,t_auto_dispatch_history.order_id").
	Scan(&orderRankGormOrderShipper)


	shipperCountFields :=`
		
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 1 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_one_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 2 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_two_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 3 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_three_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 4 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_four_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.shipper_rank = 5 THEN t_auto_dispatch_history.shipper_id END) AS shipper_rank_five_count
	
	`	
	var shipperCountData response.OrderShipperCountData
	orderCustomerQuery.Select(shipperCountFields).Scan(&shipperCountData)

	rnkOrderShipperData.ShipperCount = int64(shipperCountData.ShipperRankOneCount)+int64(shipperCountData.ShipperRankTwoCount)+int64(shipperCountData.ShipperRankThreeCount)+int64(shipperCountData.ShipperRankFourCount)+int64(shipperCountData.ShipperRankFiveCount)


	for i := 1; i < 6; i++ {
		var orderShipperDataItem response.OrderShipperDataItems
		orderShipperDataItem.ShipperRank = i
		switch i {
			case 1:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankOneCount)
			case 2:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankTwoCount)
			case 3:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankThreeCount)
			case 4:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankFourCount)
			case 5:
				orderShipperDataItem.ShipperCount = int(shipperCountData.ShipperRankFiveCount)
		}

		for _, v := range orderShipperDataGorm { 
			
			if v.ShipperRank !=i {
				continue
			}

			switch v.OrderRank { 
				case 5: 
					orderShipperDataItem.OrderRankFiveCount += v.OrderCount
					orderShipperDataItem.OrderRankFivePrice += v.OrderPrice
					rnkOrderShipperData.OrderRankFiveCount+=v.OrderCount
					orderShipperDataItem.OrderRankFourPrice += v.OrderPrice


				case 4: 
					orderShipperDataItem.OrderRankFourCount += v.OrderCount
					orderShipperDataItem.OrderRankFourPrice += v.OrderPrice
					rnkOrderShipperData.OrderRankFourCount+=v.OrderCount
					orderShipperDataItem.OrderRankThreePrice += v.OrderPrice
				case 3: 
					orderShipperDataItem.OrderRankThreeCount += v.OrderCount
					orderShipperDataItem.OrderRankThreePrice += v.OrderPrice
					rnkOrderShipperData.OrderRankThreeCount+=v.OrderCount
					orderShipperDataItem.OrderRankTwoPrice += v.OrderPrice
				case 2: 
					orderShipperDataItem.OrderRankTwoCount += v.OrderCount
					orderShipperDataItem.OrderRankTwoPrice += v.OrderPrice
					rnkOrderShipperData.OrderRankTwoCount+=v.OrderCount
					orderShipperDataItem.OrderRankOnePrice += v.OrderPrice

				case 1: 
					orderShipperDataItem.OrderRankOneCount += v.OrderCount
					orderShipperDataItem.OrderRankOnePrice += v.OrderPrice
					rnkOrderShipperData.OrderRankOneCount+=v.OrderCount
					orderShipperDataItem.OrderRankThreePrice += v.OrderPrice

			}

		}
		rnkOrderShipperData.Items = append(rnkOrderShipperData.Items, orderShipperDataItem)
		
	}

	rnk.OrderShipperData = rnkOrderShipperData


	var rnkOrderLateData response.OrderLateData


	orderLateQueryFront :=ckDb.Model(&models.AutoDispatchHistory{})

	orderLateQuery :=orderLateQueryFront.Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_auto_dispatch_history.order_state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0,7)

	
	orderLateQuery.Where("t_auto_dispatch_history.order_deliver_state >= ?",0).Select("count(t_auto_dispatch_history.order_id) as order_count").Pluck("order_count",&rnkOrderLateData.OrderCount)
	
	orderLateQuery.Where("t_auto_dispatch_history.order_deliver_state >= ?",0).Select("ifnull(sum(t_auto_dispatch_history.order_price),0) as order_price").Pluck("order_price",&rnkOrderLateData.OrderPrice)
	
	var orderLateDataGorm []response.OrderLateDataGorm
	orderLateQuery.
	Where("t_auto_dispatch_history.user_rank between ? and ?",1,5).
	Where("t_auto_dispatch_history.order_rank_org between ? and ?",1,5).
	Group("t_auto_dispatch_history.order_deliver_state,t_auto_dispatch_history.order_rank_org").
	Order("t_auto_dispatch_history.order_deliver_state,t_auto_dispatch_history.order_rank_org").
	Select("t_auto_dispatch_history.order_deliver_state as late_rank,t_auto_dispatch_history.order_rank_org as order_rank,count(t_auto_dispatch_history.order_id) as order_count,ifnull(sum(t_auto_dispatch_history.order_price),0) as order_price").
	Scan(&orderLateDataGorm)



	shipperLateFields :=`
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 0 THEN t_auto_dispatch_history.order_id END) AS late_rank_zero_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 1 THEN t_auto_dispatch_history.order_id END) AS late_rank_one_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 2 THEN t_auto_dispatch_history.order_id END) AS late_rank_two_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 3 THEN t_auto_dispatch_history.order_id END) AS late_rank_three_count,
		COUNT(DISTINCT CASE WHEN t_auto_dispatch_history.order_deliver_state = 4 THEN t_auto_dispatch_history.order_id END) AS late_rank_four_count
	`	
	var shipperLateData response.OrderLateCountData
	orderLateQuery.Select(shipperLateFields).Scan(&shipperLateData)
	orderLateCountQuery :=ckDb.Model(&models.AutoDispatchHistory{}).Where("t_auto_dispatch_history.area_id = ? and t_auto_dispatch_history.created_at >=? and t_auto_dispatch_history.created_at < ? and t_auto_dispatch_history.shipper_id > ? and t_auto_dispatch_history.order_state = ?",areaId,startTime.Format("Y-m-d 00:00:00"),endTime.Format("Y-m-d 23:59:59"),0,7)
	orderLateCountQuery.Select(shipperLateFields).Scan(&shipperLateData)
	rnkOrderLateData.LateMinute = shipperLateData.LateRankOneCount+shipperLateData.LateRankTwoCount+shipperLateData.LateRankThreeCount+shipperLateData.LateRankFourCount
 


	for i := 0; i < 5; i++ {
		var orderLateDataItem response.OrderLateDataItems
		orderLateDataItem.LateState = i
		
		switch i {
			case 0:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankZeroCount)
			case 1:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankOneCount)
			case 2:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankTwoCount)
			case 3:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankThreeCount)
			case 4:
				orderLateDataItem.LateCount = int(shipperLateData.LateRankFourCount)	
			
		}
		
		for _, v := range orderLateDataGorm { 
				if v.LateRank != i {
					continue
				}
				switch v.OrderRank { 
						case 5: 
							orderLateDataItem.OrderRankFiveCount += v.OrderCount
							orderLateDataItem.OrderRankFivePrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankFiveCount+=v.OrderCount
							rnkOrderLateData.OrderRankFivePrice+=v.OrderPrice
							
						case 4: 
							orderLateDataItem.OrderRankFourCount += v.OrderCount
							orderLateDataItem.OrderRankFourPrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankFourCount+=v.OrderCount
							rnkOrderLateData.OrderRankFourPrice+=v.OrderPrice
						case 3: 
							orderLateDataItem.OrderRankThreeCount += v.OrderCount
							orderLateDataItem.OrderRankThreePrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankThreeCount+=v.OrderCount
							rnkOrderLateData.OrderRankThreePrice+=v.OrderPrice
						case 2: 
							orderLateDataItem.OrderRankTwoCount += v.OrderCount
							orderLateDataItem.OrderRankTwoPrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankTwoCount+=v.OrderCount
							rnkOrderLateData.OrderRankTwoPrice+=v.OrderPrice
						case 1: 
							orderLateDataItem.OrderRankOneCount += v.OrderCount
							orderLateDataItem.OrderRankOnePrice += v.OrderPrice
							rnkOrderLateData.OrderPrice+=v.OrderPrice
							rnkOrderLateData.OrderCount+=v.OrderCount
							rnkOrderLateData.OrderRankOneCount+=v.OrderCount
							rnkOrderLateData.OrderRankOnePrice+=v.OrderPrice
							
				}

				
			

			

			}
		rnkOrderLateData.Items = append(rnkOrderLateData.Items, orderLateDataItem)
		
	}


	rnk.OrderLateData = rnkOrderLateData



	return rnk, nil
}
