package services

import (
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

// 通用服务类  乱七八糟的函数都写在这个里面
type CommonService struct {
	BaseService
	langUtil *lang.LangUtil
	language string
}

func NewCommonService(c *gin.Context) *CommonService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	commonService := CommonService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &commonService
}
//获取电信优惠券 
func (com *CommonService) GetTelecomCoupon(mobile string,rawParam string) (string,int) {
	lockKey := "coupon_lock_" + mobile
	//添加redis 锁，防止订单被多次重复
	couponLock := tools.NewMyRedisLock(lockKey,"1",30*time.Second)
	defer couponLock.Unlock()
	if !couponLock.Lock() {
		tools.Logger.Info("电信优惠券 " + mobile)
		return "请降低同一个手机号发送频率",201
	}
	rawParam =	strings.ReplaceAll(rawParam," ","+")
	key := []byte(configs.MyApp.TelecomCouponAccessKey)
	iv :=[]byte(configs.MyApp.TelecomCouponAccessIv)
	encMobile, err :=tools.Decrypt(rawParam, key,iv)
    if err != nil {
		tools.Logger.Error("FATAL电信优惠券解密失败 " ,mobile,",rawParam:",rawParam,",error:",err)
        return "解密失败",201
    }

	if len(mobile) == 0 || !tools.VerifyMobileFormat(mobile) || encMobile != mobile {
		return "手机号不符合规则",201
	}
	db :=tools.GetDB()
	var user models.User
	db.Model(&models.User{}).Where("mobile = ?",mobile).Find(&user)
	if user.ID == 0 {//需要查看是否领取过优惠券 
		user = models.User{
			Mobile: mobile,
			Name:   mobile,
			State:  1,
			Source: 5,//来自电信优惠券
		}
		db.Create(&user)
	}

	//优惠券初始化
	today :=carbon.Now(configs.AsiaShanghai)
	var coupons []models.Coupon
	db.Model(&models.Coupon{}).Where("type = ?",5).Find(&coupons) //需要创建2个优惠券
	var couponIds []int
	for _, co := range coupons {
		couponIds = append(couponIds,co.ID)
	}
	
	//判断用户 
	var couponUsers []models.CouponUser
	db.Model(models.CouponUser{}).Where("user_id = ? and coupon_type = ? ",user.ID,5).
		Where("start_use_time >= ? or end_use_time <= ?",today.Format("Y-m-d 00:00:00"),today.AddMonth().Format("Y-m-d 23:59:59")).
		Where("coupon_id in (?)",couponIds).
		Find(&couponUsers)
	if len(couponUsers) > 0 {//有领取记录 不给优惠券 
		return "你已经领取过优惠券了",200
	}
	couponStartUseTime := carbon.Now(configs.AsiaShanghai).Format("Y-m-01 00:00:00")
	couponEndUseTime := carbon.Now(configs.AsiaShanghai).AddMonth().Format("Y-m-01 00:00:00")
	for _, co := range coupons {
		//给用户分配优惠券 	
		couponUser := models.CouponUser{
			UserID: user.ID,
			CouponID: co.ID,
			CouponType: 5,//电信优惠券
			State: 0,
			AddTime: today.Format("Y-m-d H:i:d"),
			StartUseTime:couponStartUseTime,
			EndUseTime:couponEndUseTime,
			NameUg: co.NameUg,
			NameZh: co.NameZh,
			Price: co.Price,
		}
		if user.UserCityId > 0 {
			couponUser.CityID = &user.UserCityId
		}
		if user.UserAreaId > 0 {
			couponUser.AreaID = &user.UserAreaId
		}
		db.Create(&couponUser)
		takenCountPlus :=tools.UpdateCountRedis("telecom_coupon_count",1,10)
		takenCount :=co.TakenCount+int(takenCountPlus)
		db.Model(&models.Coupon{}).Where("id = ?",co.ID).Updates(&map[string]interface{}{
			"taken_count":takenCount,
		})
	}
	return "操作成功",200
}
