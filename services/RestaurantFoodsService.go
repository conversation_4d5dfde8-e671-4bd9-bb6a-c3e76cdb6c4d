package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/inits"
	"mulazim-api/jobs"
	"mulazim-api/lang"
	"mulazim-api/models"
	cmsModels "mulazim-api/models/cms"
	restaurantRequest "mulazim-api/requests/RestaurantRequest"
	cmsResource "mulazim-api/resources/cms"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"strings"
	"time"

	"os"
	"path/filepath"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type RestaurantFoodsService struct {
	langUtil *lang.LangUtil
	language string
	BaseService
}

func NewRestaurantFoodsService(c *gin.Context) *RestaurantFoodsService {
	if c == nil || c.Request == nil {
		return &RestaurantFoodsService{}
	}
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	restaurantFoodsService := RestaurantFoodsService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &restaurantFoodsService
}

func GetRestaurantFoodsService(lg string) *RestaurantFoodsService {
	
	restaurantFoodsService := RestaurantFoodsService{
		langUtil: nil,
		language: lg,
	}
	return &restaurantFoodsService
}

// GetOpenRestaurantsByAreaId 获取区域开启的餐厅
// @param areaId int 区域ID
// @param keyword string 关键字
// @param state int 状态 -1:全部 0:关闭 1:开启, 2: 暂停
// @param page int 页码 如等于0 则不分页
// @param limit int 每页数量 默认： 10
func (r *RestaurantFoodsService) GetList(params restaurantRequest.RestaurantListRequest,fromType int) (int64 , []models.RestaurantFoods) {
	var foods []models.RestaurantFoods
	query := tools.Db.Model(foods).Joins("left join t_restaurant on t_restaurant.id=t_restaurant_foods.restaurant_id").Where("t_restaurant.deleted_at is null")

	if params.CityID > 0 {
		query = query.Where("t_restaurant.city_id=?",params.CityID)
	}
	if params.AreaID > 0 {
		query = query.Where("t_restaurant.area_id=?",params.AreaID)
	}
	// 筛选条件
	if params.RestaurantID > 0 {
		query = query.Where("t_restaurant_foods.restaurant_id=?",params.RestaurantID)
	}
	if params.State != nil {
		query = query.Where("t_restaurant_foods.state=?",&params.State)
	}
	if len(params.Kw) > 0 {
		query = query.Where("t_restaurant_foods.name_ug like ? or t_restaurant_foods.name_zh like ?","%"+params.Kw+"%","%"+params.Kw+"%")
	}
	if params.GroupID > 0 {
		query = query.Where("t_restaurant_foods.foods_group_id  = ?", params.GroupID)
	}
	if params.LunchBoxID > 0 {
		query = query.Where("t_restaurant_foods.lunch_box_id = ?",params.LunchBoxID)
	}
	if params.LunchBoxState != nil {
		if *params.LunchBoxState > 0 {
			query = query.Where("t_restaurant_foods.lunch_box_id > 0")
		}else{
			query = query.Where("(lunch_box_id is null or lunch_box_id = 0)")
		}
	}
	if params.DealerPercent > 0 {
		query = query.Where("(t_restaurant_foods.dealer_percent + t_restaurant_foods.mp_percent) <= ?",params.DealerPercent)
	}
	if params.FoodType != nil {
		query = query.Where("food_type = ?",params.FoodType)
	}
	// 查询结果
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		if params.SortColumns != "" {
			query.Order(params.SortColumns)
		}else if fromType == constants.FromMerchantClient {
			query.Order(`CASE
				WHEN t_restaurant_foods.state = 1 THEN 1
				WHEN t_restaurant_foods.state = 3 THEN 2
				WHEN t_restaurant_foods.state = 2 THEN 3
				WHEN t_restaurant_foods.state = 0 THEN 4
			END
			, id DESC`)
		}else{
			query.Order("t_restaurant_foods.created_at desc")
		}
		
		// state 排序顺序：1 正常 - 3 审核中 - 2 售完 - 0 关闭
		query.Preload("Restaurant.City").
			Preload("Restaurant.Area").
			Preload("FoodsGroup").
			Preload("LunchBox").
			Preload("ComboFoodItems.RestaurantFood").
			Preload("ComboFoodItems.SelectedSpec.FoodSpecOptions").
			Scopes(scopes.Page(params.Page,params.Limit)).
			Find(&foods)
	}
	return totalCount,foods
}

// CreateRestaurantFoods 创建餐厅美食
func (svc *RestaurantFoodsService) CreateRestaurantFoods(
	body restaurantRequest.RestaurantFoodsCreateBody, restaurant models.Restaurant) (
	bool, string, *models.RestaurantFoods) {

	db := tools.GetDB()
	// 构造美食数据
	imageUrl := strings.Replace(body.Image, configs.MyApp.CdnUrl, "", 1)
	mulazimPercent := restaurant.Area.MpPercent
	if body.FoodType == models.RestaurantFoodsTypeCombo {
		mulazimPercent = 0
	}
	food := models.RestaurantFoods{
		FoodsGroupId:        body.FoodsGroupID,
		RestaurantPrinterID: body.RestaurantPrinterID,
		RestaurantID:        int(body.RestaurantID),
		NameUg:              body.NameUg,
		NameZh:              body.NameZh,
		FoodQuantity:        body.FoodQuantity,
		FoodQuantityType:    body.FoodQuantityType,
		DescriptionUg:       body.DescriptionUg,
		DescriptionZh:       body.DescriptionZh,
		State:               body.State,
		IsDistribution:      body.IsDistribution,
		Image:               imageUrl,
		BeginTime:           body.BeginTime + ":00",
		EndTime:             body.EndTime + ":00",
		ReadyTime:           int(body.ReadyTime),
		Price:               uint(body.Price),
		OriginalPrice:       body.OriginalPrice,
		Weight:              body.Weight,
		WeightInGroup:       body.Weight,
		LunchBoxID:          body.LunchBoxID,
		LunchBoxAccommodate: body.LunchBoxAccommodate,
		MinCount:            body.MinCount,
		IsRecommend:         body.IsRecommend,
		MpPercent:                 mulazimPercent,
		DealerPercent:             body.DistributionPercent - mulazimPercent,
		MpYourselfTakePercent:     body.YourSelfTakePercent / 2,
		DealerYourselfTakePercent: body.YourSelfTakePercent / 2,
		FoodType: body.FoodType,
	}

	// 获取饭盒信息
	if body.LunchBoxID > 0 {
		if body.LunchBoxAccommodate == 0 {
			return false, "饭盒容纳量不能为0", nil
		}
		lunchBox := models.LunchBox{}
		if err := db.Where("id = ? AND state = ?", body.LunchBoxID, models.LunchBoxStateOn).
			Take(&lunchBox).Error; err != nil {
			return false, "未找到相关饭盒信息", nil
		}
		food.LunchBoxFee = lunchBox.UnitPrice
	}

	// 创建美食记录
	if err := db.Create(&food).Error; err != nil {
		return false, "创建失败", nil
	}

	// 添加分类
	if body.FoodsCategoryID != nil && len(body.FoodsCategoryID) > 0 {
		for _, v := range body.FoodsCategoryID {
			category := models.RestaurantFoodsCategory{
				RestaurantFoodsID: food.ID,
				FoodsCategoryID:   tools.ToInt(v),
			}
			if err := db.Create(&category).Error; err != nil {
				return false, "分类添加失败", nil
			}
		}
	}

	return true, "创建成功", &food
}

// 代理比例
func (svc *RestaurantFoodsService) GetMaxDealerPercent(restaurant models.Restaurant) float64 {
	db := tools.GetDB()
	var maxDealerProfit float64
	db.Table("t_restaurant_foods").
		Select("IFNULL(max(dealer_percent), 0)").
		Where("restaurant_id = ?", restaurant.ID).
		Scan(&maxDealerProfit)
	if maxDealerProfit<0.01 {
		maxDealerProfit = 20
	}
	return maxDealerProfit
}



// EditRestaurantFoods

// EditRestaurantFoods 创建餐厅美食
func (svc *RestaurantFoodsService) EditRestaurantFoods(ID int,
	body restaurantRequest.RestaurantFoodsCreateBody, restaurant models.Restaurant, fromType int) (
	bool, string, *models.RestaurantFoods) {

	db := tools.GetDB()
	food := models.RestaurantFoods{}
	if err := db.Where("id = ?", ID).Take(&food).Error; err != nil {
		return false, "food_not_fund", nil
	}
	// 验证24小时营业：美食出餐时间段是否在餐厅出餐范围内
	if !tools.TimeLineInTimeLine(body.BeginTime+":00", body.EndTime+":00", restaurant.BeginTime, restaurant.EndTime) {
		return false, fmt.Sprintf("%s (%s - %s)", svc.langUtil.T("foods_time_not_in_restaurant_time_range"), restaurant.BeginTime, restaurant.EndTime), nil
	}

	// 获取代理百分比
	area := models.Area{}
	if err := db.Where("id = ?", restaurant.AreaID).Take(&area).Error; err != nil {
		return false, "area_not_found", nil
	}
	mulazimPercent := area.MpPercent
	if body.FoodType == models.RestaurantFoodsTypeCombo {
		mulazimPercent = 0
	}
	foodCfg := configs.MyApp.Food

	// 美食利润百分比范围验证
	if body.FoodType != models.RestaurantFoodsTypeCombo && (body.DistributionPercent < foodCfg.Percent.MPDealerMin || body.DistributionPercent > foodCfg.Percent.MPDealerMax) {
		return false, fmt.Sprintf("%s (%.2f - %.2f)", svc.langUtil.T("foods_percent_range"), foodCfg.Percent.MPDealerMin, foodCfg.Percent.MPDealerMax), nil
	}

	// 自取订单利润百分比范围验证
	if restaurant.CanSelfTake == 1 { // 店铺开启自取
		if body.FoodType != models.RestaurantFoodsTypeCombo && (body.YourSelfTakePercent < foodCfg.SelfPercent.MPDealerMin || body.YourSelfTakePercent > foodCfg.SelfPercent.MPDealerMax) {
			return false, fmt.Sprintf("%s (%.2f - %.2f)", svc.langUtil.T("self_take_percent_range"),foodCfg.SelfPercent.MPDealerMin, foodCfg.SelfPercent.MPDealerMax), nil
		}
	}

	// 构造美食数据
	imageUrl := strings.Replace(body.Image, configs.MyApp.CdnUrl, "", 1)
	// 创建美食记录
	updateMap := map[string]interface{}{
		"foods_group_id": body.FoodsGroupID,
		"restaurant_printer_id": body.RestaurantPrinterID,
		"restaurant_id": body.RestaurantID,
		"name_ug": body.NameUg,
		"name_zh": body.NameZh,
		"food_quantity": body.FoodQuantity,
		"food_quantity_type": body.FoodQuantityType,
		"description_ug": body.DescriptionUg,
		"description_zh": body.DescriptionZh,
		"state": body.State,
		"is_distribution": body.IsDistribution,
		"image": imageUrl,
		"begin_time": body.BeginTime + ":00",
		"end_time": body.EndTime + ":00",
		"ready_time": body.ReadyTime,
		"price": body.Price,
		"original_price": body.OriginalPrice,
		"weight": body.Weight,
		"lunch_box_id": body.LunchBoxID,
		"lunch_box_accommodate": body.LunchBoxAccommodate,
		"min_count": body.MinCount,
		"mp_percent": mulazimPercent,
		"dealer_percent": body.DistributionPercent - mulazimPercent,
		"mp_yourself_take_percent": body.YourSelfTakePercent / 2,
		"dealer_yourself_take_percent": body.YourSelfTakePercent / 2,
		"food_type": body.FoodType,
	}
	// 获取饭盒信息
	if body.LunchBoxID > 0 {
		lunchBox := models.LunchBox{}
		if err := db.Where("id = ? AND state = ?", body.LunchBoxID, models.LunchBoxStateOn).
			Take(&lunchBox).Error; err != nil {
			return false, "lunch_box_not_found", nil
		}
		updateMap["lunch_box_fee"] = lunchBox.UnitPrice
	} else {
		updateMap["lunch_box_fee"] = 0
	}

	// TODO: 可以优化的点
	if fromType == constants.FromMerchantClient {
		// 商家端无法/不能编辑的字段，去掉以下代码会让商家端编辑美食时，滞空表里的以下字段。
		delete(updateMap, "restaurant_printer_id")
		delete(updateMap, "food_quantity")
		delete(updateMap, "food_quantity_type")
		delete(updateMap, "lunch_box_id")
		delete(updateMap, "lunch_box_accommodate")
	}
	if body.FoodType == models.RestaurantFoodsTypeCombo {
		// 套餐美食不能修改分类
		delete(updateMap, "foods_group_id")
	}

	if err := db.Model(&models.RestaurantFoods{}).Where("id = ?", ID).Updates(updateMap).Error; err != nil {
		return false, "update_foods_info_fail", nil
	}

	// 添加分类
	if body.FoodsCategoryID != nil && len(body.FoodsCategoryID) > 0 {
		// 先删除旧数据
		if err := db.Where("restaurant_foods_id = ?", ID).Delete(&models.RestaurantFoodsCategory{}).Error; err != nil {
			return false, "update_foods_category_fail", nil
		}
		for _, v := range body.FoodsCategoryID {
			category := models.RestaurantFoodsCategory{
				RestaurantFoodsID: ID,
				FoodsCategoryID:   tools.ToInt(v),
			}
			if err := db.Create(&category).Error; err != nil {
				return false, "update_foods_category_fail", nil
			}
		}
	}

	// 套餐编辑
	if food.FoodType == models.RestaurantFoodsTypeCombo {
		// 删除旧数据
		if err := db.Where("food_id = ?", ID).Delete(&models.FoodsComboItem{}).Error; err != nil {
			return false, "update_combo_fail", nil
		}
		
		// 创建修改后端套餐
		for _, v := range body.ComboFoodItems {
			specId := svc.GetSpecIdByOptions(ID, v.OptionIds)
			item := models.FoodsComboItem{
				FoodID: v.FoodID,
				ComboID: ID,
				FoodType: uint8(food.FoodType),
				Count: v.Count,
				State: 1,
				SpecID: specId,
				CreatedAt: time.Now(),
			}
			if err := db.Create(&item).Error; err != nil {
				return false, "update_combo_fail", nil
			}
		}
		
	}

	return true, "msg", &food
}

// 获取餐厅套餐美食
func (svc *RestaurantFoodsService) GetFoodsByID(id int) (*models.RestaurantFoods, error) {
	var (
		db   = tools.GetDB()
		food = models.RestaurantFoods{}
	)
	db.Where("id = ?", id).
		Preload("Restaurant.RestaurantPrinter","state = 1").
		Preload("Restaurant.FoodsGroup","state = 1 and review_state = 2").
		Preload("ComboFoodItems.RestaurantFood").
		Preload("ComboFoodItems.SelectedSpec.FoodSpecOptions").
		Preload("Categories").
		Find(&food)
	return &food, nil
}

func (svc *RestaurantFoodsService) GetOldFoodInfo(food models.RestaurantFoods) *cmsResource.OldFoodsInfo{
	var notification cmsModels.Notification
	tools.GetDB().Model(notification).Where("type = 2 and item_id = ?",food.ID).Order("id desc").Scan(&notification)
	var oldFoodInfo cmsResource.OldFoodsInfo
	json.Unmarshal([]byte(notification.UpdateContent), &oldFoodInfo)
	oldFoodInfo.Image = tools.CdnUrl(oldFoodInfo.Image)
	return &oldFoodInfo
}

// GetComboFoodsByID 获取餐厅套餐美食
func (svc *RestaurantFoodsService) GetComboFoodsByID(id int) (*models.RestaurantFoods, error) {
	var (
		db   = tools.GetDB()
		food = models.RestaurantFoods{}
	)

	// 获取餐厅美食数据
	if err := db.Where("id = ?", id).Take(&food).Error; err != nil {
		return nil, err
	}

	// 如果是套餐，那就获取套餐中的美食数据
	query := db.Model(&food)
	if food.FoodType == models.RestaurantFoodsTypeCombo {
		query.Preload("ComboFoodItems.RestaurantFood", "state = 1"). // 获取套餐中的美食数据
			Preload("ComboFoodItems", func(db *gorm.DB) *gorm.DB {
				return db.Preload("SelectedSpec", func(db *gorm.DB) *gorm.DB {
					return db.Preload("FoodSpecOptions", func(db *gorm.DB) *gorm.DB {
						return db.Preload("FoodSpecType", "is_deleted = 0")
					}, "is_deleted = 0")
				}, "is_deleted = 0")
			}, "state = 1")
		//Preload("ComboFoodItems.SelectedSpec.SpecOptions.FoodSpecType") // 获取到套餐中的规格美食数据
	} else if food.FoodType == models.RestaurantFoodsTypeSpec {
		query.Preload("FoodSpecType.SpecOptions", "is_deleted = 0") // 获取到美食规格数据
	}

	if err := query.Take(&food).Error; err != nil {
			return nil, err
	}
		
	return &food, nil
}

// DeleteRestaurantFood 删除餐厅美食
func (svc *RestaurantFoodsService) DeleteRestaurantFood(foodID int) error {
	db := tools.GetDB()

	err := db.Model(&models.RestaurantFoods{}).
		Where("id = ?", foodID).
		Update("state = ?", models.RESTAURANTFOODS_STATE_CLOSE).Error

	return err
}

// 更新美食信息添加通知
func (svc *RestaurantFoodsService) AddNotificationCreate(food models.RestaurantFoods, restaurant models.Restaurant) {
	link := fmt.Sprintf("/app/lang/restaurant-food/foods-manage/edit/%d",food.ID)
	if food.FoodType == models.RestaurantFoodsTypeCombo {
		link = fmt.Sprintf("/app/lang/restaurant-food/group-manage/combo/edit/%d/%d",food.ID,food.RestaurantID)
	}
	tools.GetDB().Table("t_notification").Create(&map[string]interface{}{
		"type":       2,
		"area_id":    restaurant.AreaID,
		"content_ug": fmt.Sprintf("«%s» نىڭ %s تاماق ئۇچۇرى يېڭىلاندى، تەستىق ساقلاۋاتىدۇ", restaurant.NameUg, food.NameUg),
		"content_zh": fmt.Sprintf("«%s» نىڭ %s تاماق ئۇچۇرى يېڭىلاندى، تەستىق ساقلاۋاتىدۇ", restaurant.NameUg, food.NameUg),
		"link":       link,
		"state":      0,
		"item_id":    food.ID,
		"created_at": time.Now(),
		"updated_at": time.Now(),
	})
}

// 添加通知
func (svc *RestaurantFoodsService) AddNotificationEdit(oldFood models.RestaurantFoods, newFood models.RestaurantFoods, restaurant models.Restaurant) {
	oldFood.Price = oldFood.Price/100
	oldFoodInfo := cmsResource.OldFoodsInfo{
		ID        :oldFood.ID,
		FoodType  :int(oldFood.FoodType),
		NameUg    :oldFood.NameUg,   
		NameZh      :oldFood.NameZh,
		Image        :oldFood.Image,
		DescriptionUg      :oldFood.DescriptionUg,
		DescriptionZh        :oldFood.DescriptionZh,
		Price          : int(oldFood.Price),
	}
	link := fmt.Sprintf("/app/lang/restaurant-food/foods-manage/edit/%d",newFood.ID)
	if newFood.FoodType == models.RestaurantFoodsTypeCombo {
		link = fmt.Sprintf("/app/lang/restaurant-food/group-manage/combo/edit/%d/%d",newFood.ID,newFood.RestaurantID)
	}
	oldFoodByte,_:=json.Marshal(oldFoodInfo)
	tools.GetDB().Table("t_notification").Create(&map[string]interface{}{
		"type":       2,
		"area_id":    restaurant.AreaID,
		"content_ug": fmt.Sprintf("«%s» نىڭ %s تاماق ئۇچۇرى يېڭىلاندى، تەستىق ساقلاۋاتىدۇ", restaurant.NameUg, newFood.NameUg),
		"content_zh": fmt.Sprintf("«%s» نىڭ %s تاماق ئۇچۇرى يېڭىلاندى، تەستىق ساقلاۋاتىدۇ", restaurant.NameUg, newFood.NameUg),
		"link":       link,
		"state":      0,
		"update_content": string(oldFoodByte),
		"item_id":    newFood.ID,
		"created_at": time.Now(),
		"updated_at": time.Now(),
	})
}

// 美食编辑内容是否要审核
func (s *RestaurantFoodsService) IsReviewEditFood(request restaurantRequest.RestaurantFoodsCreateBody,oldFood models.RestaurantFoods) bool{
	if tools.ToInt(oldFood.Price) != tools.ToInt((request.Price*100)) ||  
	oldFood.DescriptionUg != request.DescriptionUg || 
	oldFood.DescriptionZh != request.DescriptionZh || 
	oldFood.Image != request.Image || 
	oldFood.NameUg != request.NameUg || 
	oldFood.NameZh != request.NameZh {
		return true
	}
	return false
}

// 检查规格美食是否设置过活动（秒杀，优惠，加价，特价，满减）
func (s *RestaurantFoodsService) HasActivity(foodID int) error {
	// 秒杀
	var seckillCount int64
	tools.GetDB().Model(&models.Seckill{}).
		Where("food_id = ? and type = 1 and state = 1 and begin_time >= ? ", foodID,time.Now()).
		Count(&seckillCount)
	if seckillCount > 0 {
		return errors.New("spec_food_setting_seckill")
	}
	// 特价
	var specialPriceCount int64
	tools.GetDB().Model(&models.Seckill{}).
		Where("food_id = ? and type = 2 and state = 1 and begin_time >= ? ", foodID,time.Now()).
		Count(&specialPriceCount)
	if specialPriceCount > 0 {
		return errors.New("spec_food_setting_special_price")
	}
	// 优惠
	var preferentialCount int64
	tools.GetDB().Model(&models.FoodsPreferential{}).
		Where("restaurant_foods_id =?  and state = 1 and end_date_time >= ? ", foodID,time.Now()).
		Count(&preferentialCount)
	if preferentialCount > 0 {
		return errors.New("spec_food_setting_preferential")
	}
	// 加价
	var priceMarkupCount int64
	tools.GetDB().Model(&models.PriceMarkupFood{}).
		Where("restaurant_foods_id =?  and state = 1 and start_date >= ? ", foodID,time.Now()).
		Count(&priceMarkupCount)
	if priceMarkupCount > 0 {
		return errors.New("spec_food_setting_price_markup")
	}
	// 满减
	var marketingReduceCount int64
	tools.GetDB().Model(&models.Marketing{}).
		Where("(foods = ? OR FIND_IN_SET(?, foods)) AND state = 1 AND begin_date >= ?", foodID, foodID, time.Now()).
		Count(&marketingReduceCount)
	if marketingReduceCount > 0 {
		return errors.New("spec_food_setting_marketing_reduce")
	}
	return nil
}


func (s *RestaurantFoodsService) GetQuantityList() []models.FoodsQuantity {
	var quantityList []models.FoodsQuantity
	tools.GetDB().Model(&models.FoodsQuantity{}).Where("state = 1").Find(&quantityList)
	return quantityList
}


func (s *RestaurantFoodsService) GetFoodTypes(keyword string) []models.BFoodsType {
	var foodTypes []models.BFoodsType
	query := tools.GetDB().Model(&models.BFoodsType{}).Where("state = 1").Order("id desc")
	if keyword != "" {
		query = query.Where("name_ug LIKE ? OR name_zh LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	query.Find(&foodTypes)
	return foodTypes
}


func (s *RestaurantFoodsService) GetListForSelectTable(allFoodID int, kw string,page int,limit int) ([]models.BFoods,int64) {
	var TotalCount int64
	var foodList []models.BFoods
	query := tools.GetDB().Model(&models.BFoods{}).Where("state = 1")
	if allFoodID > 0 {
		query = query.Where("all_foods_id = ?", allFoodID)
	}
	if kw != "" {
		query = query.Where("name_ug LIKE ? OR name_zh LIKE ?", "%"+kw+"%", "%"+kw+"%")
	}
	query.Preload("AllFoods.BFoodsType").Count(&TotalCount).Scopes(scopes.Page(page, limit)).Find(&foodList)
	return foodList,TotalCount

}
	
	
func (s *RestaurantFoodsService) GetComboFoodTime(foodIds string) (string,string,error) {
	type FoodTime struct{
		ID int
		BeginTime string
	    EndTime string
	}
	ids := strings.Split(foodIds,",")
	var foodTimes []FoodTime
	tools.GetDB().Model(&models.RestaurantFoods{}).Where("id in ?",ids).Scan(&foodTimes)
	var nweBeginTime carbon.Carbon
	var newEndTime carbon.Carbon
	for _, v := range foodTimes {
		isIn := true
		for _, vv := range foodTimes {
			isConflict := tools.TimelineOverlap(v.BeginTime, v.EndTime, vv.BeginTime, vv.EndTime)
			noInline := !(tools.TimeLineInTimeLine(v.BeginTime, v.EndTime, vv.BeginTime, vv.EndTime) || tools.TimeLineInTimeLine(vv.BeginTime, vv.EndTime, v.BeginTime, v.EndTime))
			if v.ID != vv.ID && noInline && !isConflict {
				isIn = false
				break
			}
		}
		if !isIn {
			return "", "", errors.New("combo_food_time_illegal")
		}

		// 获取开始时间最大，结束时间最小
		beginTime := carbon.Parse("2025-01-01 "+v.BeginTime)
		endTime := carbon.Parse("2025-01-01 "+v.EndTime)
		if beginTime.Gt(endTime) {
			endTime = endTime.AddDay()
		}
		if beginTime.Gt(nweBeginTime) || nweBeginTime.IsZero() {
			nweBeginTime = beginTime
		}
		if endTime.Lt(newEndTime) || newEndTime.IsZero() {
			newEndTime = endTime
		}
	}
	return nweBeginTime.Format("H:i:s"),newEndTime.Format("H:i:s"),nil

}
	



func (r *RestaurantFoodsService) MassImportFoodProcess(langUtil lang.LangUtil,key string,adminId int,resId int,filePath string,insert bool)  (map[string]interface{},error) {
	randStr :=key
	if key == "" {
		randStr =fmt.Sprintf("%d-%s",resId,tools.RandStr(6))
	}
	
	fileRedisKey :=fmt.Sprintf("mass_import_file_upload_%s",randStr)
	fileProcessKey :=fmt.Sprintf("mass_import_file_process_%s",randStr)  //文件处理 状态 0：开始处理 1：处理中 2：处理完成 3：处理失败
	redisHelper := tools.GetRedisHelper()
	db :=tools.GetDB()
	var restaurant models.Restaurant
	db.Model(&models.Restaurant{}).Where("id=?",resId).Find(&restaurant)
	if !insert { 
		redisHelper.Set(context.Background(), fileRedisKey, filePath, 10*time.Minute)
	}else{
		fileRedisKey =fmt.Sprintf("mass_import_file_upload_%s",randStr)
		filePath = tools.ToString(redisHelper.Get(context.Background(),fileRedisKey).Val())
		redisHelper.Set(context.Background(), fileProcessKey, 0, 10*time.Minute) //开始处理
	}
	tools.Logger.Info("批量导入美食上传文件 insert ",insert,":",filePath,",时间:",carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),",adminId:",tools.ToString(adminId))
	items := []map[string]interface{}{}
	ext := filepath.Ext(filePath)
	fileType :="excel"
	if ext == ".zip"  {
		fileType ="zip"
		localFilePath :=strings.TrimRight(configs.MyApp.UploadRootDir,"/")+"/uploads/food-import-file/" +randStr
		tools.Unzip(filePath,localFilePath)
		excelFileName :="foods-import.xlsx"
		filesAll,_:=tools.ScanDirectory(localFilePath, 0)
		tools.Logger.Info("zip 解压后的目录",localFilePath)
		excelFileExists :=false
		for _, v := range filesAll {
			if  tools.ToString(v["type"])=="file" && (strings.Contains(tools.ToString(v["name"]),".xlsx") || strings.Contains(tools.ToString(v["name"]),".xls")){
				excelFileExists =true
				excelFileName =tools.ToString(v["name"])
			}
		}
		if !excelFileExists {
			return nil,fmt.Errorf("excel_row_column_zip_illegal")
		}
		items = tools.ExcelToData(localFilePath+"/"+excelFileName)
		for _, v := range filesAll {
			if  tools.ToString(v["type"])=="dir" && strings.Contains(tools.ToString(v["name"]),"images"){ //图片目录
				imgFileItems :=v["children"].([]map[string]interface{})
				for _,vv := range imgFileItems {
					imgName :=strings.Split(tools.ToString(vv["name"]),".")[0]		
					for kkk, vvv := range items {

						if tools.ToString(vvv["美食名称中文"]) == imgName {
							items[kkk]["图片"] = localFilePath+"/images/"+tools.ToString(vv["name"])
						}else if tools.ToString(vvv["图片"]) == "images/"+tools.ToString(vv["name"]) { // image/xx.png 图片 
							items[kkk]["图片"] = localFilePath+"/images/"+tools.ToString(vv["name"])
						}


					}
				}

			}
		}
		
		
		

	}else{
		items = tools.ExcelToData(filePath)
	}
	
	if len(items) ==0 {
		return nil,fmt.Errorf("excel_file_empty")
	}
	storeBeginTime :=restaurant.BeginTime
	storeEndTime :=restaurant.EndTime
	var errors []string
	foodTypes :=map[string]interface{}{} //美食分类
	foodGroups :=map[string]interface{}{} //美食分组
	foodLunchBoxes :=map[string]interface{}{} //美食饭盒

	//美食利润百分比
	// #平台和代理最小比例
	food_percent_mp_dealer_min := configs.MyApp.Food.Percent.MPDealerMin
	// #平台和代理最大比例
	food_percent_mp_dealer_max := configs.MyApp.Food.Percent.MPDealerMax
	// #美食自取利润百分比
	// #平台和代理最小比例
	food_self_percent_mp_dealer_min := configs.MyApp.Food.SelfPercent.MPDealerMin
	// #平台和代理最大比例
	food_self_percent_mp_dealer_max := configs.MyApp.Food.SelfPercent.MPDealerMax


	cacheKey :=fmt.Sprintf("food-quantity-%d",restaurant.AreaID)
	cacheTime :=60*60 * time.Second
	quantityStr := tools.Remember2(cacheKey, cacheTime, func() interface{}{
		jsonStr :=tools.ReadJson(inits.ConfigFilePath+"/configs/food_quantity_list.json")
		var foodsQuantityTypeValue []map[string]interface{}
		json.Unmarshal([]byte(jsonStr),&foodsQuantityTypeValue)
		return foodsQuantityTypeValue
	})
	var foodsQuantityTypeValue []map[string]interface{}
	json.Unmarshal([]byte(quantityStr), &foodsQuantityTypeValue)
	
		
	
	for k, v := range items {
		foodStartTime := ""
		foodEndTime := ""
		foodNameUg :=""
		foodNameZh :=""

		foodTypeNameUg :=""
		foodTypeNameZh :=""
		foodGroupNameUg :=""
		foodGroupNameZh :=""

		lunchBoxNameUg :=""
		lunchBoxNameZh :=""
		dealerFoodPercent :=float64(0)
		dealerFoodSelfPercent :=float64(0)
		foodQuantityNameZh :=""
		foodQuantityZh :=""

		foodQuantityType := 1
		

		if len(v) == 0 {
			errors = append(errors,fmt.Sprintf(langUtil.T("excel_row_empty"),k+1))
			continue
		}
		for kk, vv := range v {
			trimValue := strings.TrimRight(strings.TrimLeft(tools.ToString(vv)," ")," ") //清空左右的空格
			
			kk=strings.TrimSpace(kk)
			if strings.Contains(kk,"(*)") {
				kk=strings.ReplaceAll(kk,"(*)","")
			}
			//一些规则 比如价格,名称不能为空 等
			if 	kk == "美食名称中文" || 
			   	kk == "美食名称维吾尔语" || 
			   	kk == "价格" || 
			   	kk == "美食分组名称中文" || 
			   	kk == "美食分组名称维吾尔语" || 
			   	
			   	kk == "出餐开始时间" ||
			   	kk == "出餐结束时间" ||
			   	kk == "配送百分比" ||  
				kk == "自取百分比" || 
				kk == "支持配送" || 
				kk == "支持自取" {
				if len(trimValue) == 0{
					errors = append(errors,fmt.Sprintf(langUtil.T("excel_row_column_empty"),k+1,kk))
				}
			}
			if kk == "出餐开始时间" {
				// 支持多种时间格式，如 0:00:00, 0:00, 00:00, 00:00:00
				trimValue,_=tools.ParseTimeByFormat(trimValue,"H:i:s","H:i","H:i:s")
				foodStartTime=trimValue
			}
			if kk == "出餐结束时间" { //
				trimValue,_=tools.ParseTimeByFormat(trimValue,"H:i:s","H:i","H:i:s")
				foodEndTime = trimValue
			}
			if kk == "美食名称中文" {
				foodNameZh = trimValue
			}
			if kk == "美食名称维吾尔语" {
				foodNameUg = trimValue
			}
			
			if kk == "美食分组名称中文" {
				foodGroupNameZh = trimValue
			}
			if kk == "美食分组名称维吾尔语" {
				foodGroupNameUg = trimValue
			}
			if kk == "饭盒名称中文" {
				lunchBoxNameZh = trimValue
			}
			if kk == "配送百分比" {
				dealerFoodPercent = tools.ToFloat64(trimValue)
			}
			if kk == "自取百分比" {
				dealerFoodSelfPercent =tools.ToFloat64(trimValue)
			}
			if kk == "美食数量类型中文" {
				foodQuantityNameZh = trimValue
			}
			if kk == "美食数量" {
				foodQuantityZh = trimValue
			}
			if fileType == "excel" && kk == "图片" && len(trimValue)>0 {
				tools.Logger.Info("图片地址=>>>>>",trimValue)
				if !(strings.Contains(trimValue,"http") || strings.Contains(trimValue,"https")) { //excel 上传时要用 https 地址 
					
					errors = append(errors,fmt.Sprintf(langUtil.T("excel_row_column_img_illegal"),k+1))
				}
			}
			v[kk]=trimValue
			
		}
		//监测出餐时间是否在 营业时间范围内
		if len(foodStartTime) > 0 && len(foodEndTime) > 0 {
			if !tools.TimelineOverlap(storeBeginTime,storeEndTime,foodStartTime, foodEndTime) {
				errors = append(errors,fmt.Sprintf(langUtil.T("excel_row_column_illegal"),k+1,storeBeginTime+"~"+storeEndTime))
			}
		}
		//找出分类名称
		if len(foodTypeNameZh) > 0 && !tools.InArray(foodTypeNameZh,tools.MapKeys(foodTypes)){
			foodTypes[foodTypeNameZh]=map[string]interface{}{
				"ug":foodTypeNameUg,
				"zh":foodTypeNameZh,
				"food_name_ug":foodNameUg,
				"food_name_zh":foodNameZh,
			}
		}
		//找出分组名称
		if len(foodGroupNameZh) > 0 &&  !tools.InArray(foodGroupNameZh,tools.MapKeys(foodGroups)){
			foodGroups[foodGroupNameZh]=map[string]interface{}{
				"ug":foodGroupNameUg,
				"zh":foodGroupNameZh,
				"food_name_ug":foodNameUg,
				"food_name_zh":foodNameZh,
			}
			
			var foodsGroup  models.FoodsGroup
			db.Model(&models.FoodsGroup{}).Where("restaurant_id = ? and name_zh=? ",resId,foodGroupNameZh).First(&foodsGroup)
			if foodsGroup.ID > 0 {
				v["foods_group_id"]=foodsGroup.ID
			}
		}
		//找出饭盒名称
		if len(lunchBoxNameZh) > 0 && !tools.InArray(lunchBoxNameZh,tools.MapKeys(foodLunchBoxes)){

			var foodLunchBox  models.LunchBox
			db.Model(&models.LunchBox{}).Where("name_zh=?",lunchBoxNameZh).First(&foodLunchBox)
			if foodLunchBox.ID == 0 {
				//饭盒不存在  ?创建 ?提示错误 
				errors = append(errors,fmt.Sprintf(langUtil.T("excel_row_column_lunch_box_illegal"),k+1,lunchBoxNameZh))
			}

			foodLunchBoxes[lunchBoxNameZh]=map[string]interface{}{
				"ug":lunchBoxNameUg,
				"zh":lunchBoxNameZh,
				"food_name_ug":foodNameUg,
				"food_name_zh":foodNameZh,
				"lunch_box_id":foodLunchBox.ID,
			}
			v["lunch_box_id"]=foodLunchBox.ID
			v["lunch_box_fee"]=foodLunchBox.UnitPrice

		}
		hasQuantity  := false
		quantityExists  := false
		if len(foodQuantityNameZh) > 0 {
			hasQuantity  = true
			
			for _, vv := range foodsQuantityTypeValue {
				if foodQuantityNameZh == strings.TrimSpace(tools.ToString(vv["foods_quantity_type_name_zh"])) {
					foodQuantityType = tools.ToInt(vv["id"])
					v["food_quantity_type"] = foodQuantityType
					v["food_quantity"] = foodQuantityZh
					quantityExists  = true
				}
			}
		}

		if hasQuantity && !quantityExists {
			errors = append(errors,fmt.Sprintf(langUtil.T("excel_row_column_food_quantity_illegal"),k+1,foodQuantityNameZh))
		}

		if dealerFoodPercent > 0 {
			if dealerFoodPercent < food_percent_mp_dealer_min { //不能小于最小百分比
				errors = append(errors,fmt.Sprintf(langUtil.T("excel_row_column_dealer_profit_min_illegal"),k+1,food_percent_mp_dealer_min))
			}else if dealerFoodPercent > food_percent_mp_dealer_max { //不能大于最大百分比、
				errors = append(errors,fmt.Sprintf(langUtil.T("excel_row_column_dealer_profit_max_illegal"),k+1,food_percent_mp_dealer_max))
			}
		}
		if dealerFoodSelfPercent > 0 {
			if dealerFoodSelfPercent < food_self_percent_mp_dealer_min { //不能小于最小百分比
				errors = append(errors,fmt.Sprintf(langUtil.T("excel_row_column_dealer_self_profit_min_illegal"),k+1,food_self_percent_mp_dealer_min))
			}else if dealerFoodSelfPercent > food_self_percent_mp_dealer_max { //不能大于最大百分比
				errors = append(errors,fmt.Sprintf(langUtil.T("excel_row_column_dealer_self_profit_max_illegal"),k+1,food_self_percent_mp_dealer_max))
			}
		}
		
		items[k] = v
	}

	
	
	if len(errors)> 0 { //如果有问题的话 文件不必要保留 redis 也清理掉
		redisHelper.Del(context.Background(),fileRedisKey)
		er :=os.Remove(filePath)
		if er != nil {
			tools.Logger.Error(er)
		}
		return nil,fmt.Errorf("%s",strings.Join(errors,","))

	}

	if !insert {
		resultMap :=map[string]interface{}{
			"items":items,
			"total":len(items),
			"groups":foodGroups,
			"types":foodTypes,
			"lunchBoxes":foodLunchBoxes,
			"file_key":randStr,
			"errors":errors,
		}
		return resultMap,nil
	}else{
		//进入队列处理 队列处理
		redisHelper.Set(context.Background(), fileProcessKey, 1, 30*time.Minute) //开始处理
		jobs.SendFoodImportJob(randStr,resId,items,adminId,foodTypes,foodGroups,foodLunchBoxes,fileType)


	}
	return nil,nil

}

//处理结果 查询
func (r *RestaurantFoodsService) MassImportResultQuery(langUtil lang.LangUtil,resId int,key string)  (map[string]interface{},error) {
	redisHelper := tools.GetRedisHelper()
	fileProcessKey :=fmt.Sprintf("mass_import_file_process_%s",key)
	status := tools.ToInt(redisHelper.Get(context.Background(),fileProcessKey).Val())
	switch(status){
		case 0:
			return map[string]interface{}{
				"status":status,
				"message": langUtil.T("excel_process_start"),//开始处理
			},nil
			case 1:
			return map[string]interface{}{
				"status":status,
				"message":langUtil.T("excel_process_running"),//处理中
			},nil
			case 2:
			return map[string]interface{}{
				"status":status,
				"message":langUtil.T("excel_process_finished"),//"处理完成",
			},nil
			case 3:
				return map[string]interface{}{
					"status":status,
					"message":langUtil.T("excel_process_failed"),//"处理失败",
				},nil
	}
	return nil,nil

}

// 验证套餐
func (r *RestaurantFoodsService) ValidCombo(foodID int) error{
	// 验证美食是否套餐美食,如果不是套餐美食不验证
	var food models.RestaurantFoods
	tools.GetDB().Model(food).Where("id=?",foodID).Scan(&food)
	if(food.FoodType != models.RestaurantFoodsTypeCombo){
		return nil
	}
	var comboItemFoodIds []int
	tools.GetDB().Model(models.FoodsComboItem{}).Select("food_id").Where("combo_id = ?",foodID).Scan(&comboItemFoodIds)
	if len(comboItemFoodIds) == 0{
		return errors.New("foods_combo_items_empty")
	}
	var deletedFood models.RestaurantFoods
	tools.GetDB().Model(&deletedFood).Unscoped().Where("id in ? and deleted_at is not null",comboItemFoodIds).Scan(&deletedFood)
	if deletedFood.ID > 0 {
		return errors.New(fmt.Sprintf(r.langUtil.T("combo_item_deleted"),tools.GetNameByLang(deletedFood,r.language)))
	}
	return nil
}