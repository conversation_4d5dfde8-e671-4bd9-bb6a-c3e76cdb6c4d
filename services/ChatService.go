package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/jobs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/models/chat"
	Other "mulazim-api/models/other"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"strings"
	"time"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"regexp"
)

type ChatService struct {
	langUtil *lang.LangUtil
	language string
}

func NewChatService(c *gin.Context) *ChatService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	shipperService := ChatService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &shipperService
}

// 描述：发送聊天信息
// 作者：Qur<PERSON>jan
// 文件：ChatService.go
// 修改时间：2023/09/18 19:36
func (s ChatService) SendMessage(c chat.ChatSendRequest, adminId int64) (chat.ChatMessage, error) {

	db := tools.GetDB()
	type OrderAttr struct {
		Id           int
		OrderId      string
		SerialNumber string
		Mobile       string
		ResNameUg    string
		ResNameZh    string
	}
	var orderAttr OrderAttr
	var orderToday models.OrderToday
	db.Model(models.OrderToday{}).Preload("Restaurant").Select("id,order_id,serial_number,mobile,store_id").Where("id = ?", c.OrderID).Find(&orderToday)

	if orderToday.ID > 0 {
		orderAttr.Id = orderToday.ID
		orderAttr.OrderId = orderToday.OrderID
		orderAttr.SerialNumber = tools.ToString(orderToday.SerialNumber)
		orderAttr.Mobile = orderToday.Mobile
		orderAttr.ResNameUg = orderToday.Restaurant.NameUg
		orderAttr.ResNameZh = orderToday.Restaurant.NameZh
	} else {
		var order models.Order
		db.Model(models.Order{}).Preload("Restaurant").Select("id,order_id,serial_number,mobile,store_id,booking_time").Where("id = ?", c.OrderID).Find(&order)
		orderAttr.Id = order.ID
		orderAttr.OrderId = order.OrderID
		orderAttr.SerialNumber = tools.ToString(order.SerialNumber)
		orderAttr.Mobile = order.Mobile
		orderAttr.ResNameUg = order.Restaurant.NameUg
		orderAttr.ResNameZh = order.Restaurant.NameZh
		endChatTime := carbon.Parse(order.BookingTime, configs.AsiaShanghai).AddDays(7)
		d := carbon.Now(configs.AsiaShanghai).DiffInDays(endChatTime)
		if d < 0 {
			return chat.ChatMessage{}, errors.New(s.langUtil.T("can_not_send_message_now"))
		}
	}

	if c.ContentType == chat.CHAT_CONTENT_TYPE_TEXT {
		// 创建正则表达式来匹配电话号码中的空格
		re := regexp.MustCompile(`\s+`)

		// 使用正则替换掉空格
		replaceTex :=re.ReplaceAllString(c.Content, "")
		
		if strings.HasPrefix(replaceTex, "+86"){
			replaceTex =strings.ReplaceAll(replaceTex,"+86","")
			replaceTex = strings.TrimSpace(replaceTex)
		}

		if tools.VerifyMobileFormat(replaceTex) {
			c.Content= replaceTex
		}
	}


	chatMsg := chat.OrderChatDetail{
		OrderID:           c.OrderID,
		SenderType:        c.SenderType,
		SenderID:          adminId,
		CardType:          c.CardType,
		ContentType:       c.ContentType,
		CardContent:       c.CardContent,
		Image:             c.Image,
		Content:           c.Content,
		OrderSerialNumber: carbon.Now(configs.AsiaShanghai).Format("m-d") + " #" + tools.ToString(orderAttr.SerialNumber),
		OrderNo:           orderAttr.OrderId,
		ResNameUg:         orderAttr.ResNameUg,
		ResNameZh:         orderAttr.ResNameZh,
		CustomerPhone:     orderAttr.Mobile,
	}

	switch c.ContentType {
	case chat.CHAT_CONTENT_TYPE_TEXT:
		if c.Content == "" {
			return chat.ChatMessage{}, errors.New("content " + s.langUtil.T("not_empty"))
		}
		if s.IsChatForbiddenWord(c.Content) {
			return chat.ChatMessage{}, errors.New(s.langUtil.T("contains_forbidden_word"))
		}
		chatMsg.Content = c.Content
	case chat.CHAT_CONTENT_TYPE_IMAGE:
		if c.Image == "" {
			return chat.ChatMessage{}, errors.New("image " + s.langUtil.T("not_empty"))
		}
		chatMsg.Image = c.Image

	case chat.CHAT_CONTENT_TYPE_CARD:
		if c.CardType == 0 || c.CardContent == "" {
			return chat.ChatMessage{}, errors.New("card_type | card_content " + s.langUtil.T("not_empty"))
		}
		chatMsg.CardType = c.CardType
		chatMsg.CardContent = c.CardContent

	case chat.CHAT_CONTENT_TYPE_REPORT:
		chatMsg.CardType = c.CardType
		chatMsg.CardContent = c.CardContent
	case chat.CHAT_CONTENT_TYPE_ADDRESS:
		chatMsg.CardType = c.CardType
		chatMsg.CardContent = c.CardContent
	default:
		return chat.ChatMessage{}, errors.New("invalid contentType")
	}

	job := jobs.NewSendOrderChatDetailJob()

	byteData, err := json.Marshal(chatMsg)
	if err != nil {
		return chat.ChatMessage{}, errors.New("FATAL json.Marshal(chatMsg) error")
	}
	sendMap := make(map[string]interface{}, 0)
	err = json.Unmarshal(byteData, &sendMap)
	if err != nil {
		return chat.ChatMessage{}, errors.New("FATAL json.Unmarshal(byteData,&byteData) error")
	}
	job.ProduceMessageToConsumer(sendMap)
	return chat.ChatMessage{}, nil
}

// SaveReport
//
// @Description: 保存上报情况记录
// @Author: Rixat
// @Time: 2023-11-27 05:09:35
// @receiver
// @param c *gin.Context
func (s ChatService) SaveReport(shipper models.Admin, order models.OrderToday, content string, imagePath string) error {
	situation := models.OrderSituation{
		CityID:       shipper.AdminCityID,
		AreaID:       shipper.AdminAreaID,
		OrderID:      order.ID,
		OrderNo:      order.OrderID,
		ShipperID:    shipper.ID,
		RestaurantID: order.StoreID,
		ShipperName:  shipper.RealName,
		Content:      content,
		Image:        imagePath,
	}
	err := tools.Db.Model(situation).Create(&situation).Error
	return err
}

// 描述：获取聊天详情
// 作者：Qurbanjan
// 文件：ChatService.go
// 修改时间：2023/09/19 10:54
func (s ChatService) ChatDetail(ctx *gin.Context, orderId int64, senderID int64, senderType int) (chat.ChatResponse, error) {
	db := tools.GetDB()
	var chatDetail []chat.OrderChatDetail
	var totalCount int64
	db.Model(&chat.OrderChatDetail{}).Where("order_id = ?", orderId).Count(&totalCount)
	if err := db.Where("order_id = ?", orderId).Order("id asc").Scopes(scopes.Paginate(ctx)).Find(&chatDetail).Error; err != nil {
		return chat.ChatResponse{}, err
	}

	adminType := ""
	groupPrefix := "group_"
	switch senderType {
		//1:超级管理员 2:管理员  查看聊天记录时 不要清空 未读数据数量
	case 3,4:
		// case 1, 2, 3,4:
		adminType = "_admin_count"
	case 5,6:
		adminType = "_restaurant_count"
	case 8, 9:
		adminType = "_shipper_count"
	case 99:
		adminType = "_user_count"
	}
	if adminType != "" {
		key := groupPrefix + tools.ToString(orderId) + adminType
		tools.RedisKeyValueUpdate(key, 0)
	}

	var chatMsgs []chat.ChatMessage
	var chatResponse chat.ChatResponse

	orderSerialNumber := ""
	orderNo := ""

	for _, chatMsg := range chatDetail {
		userInfo, err := s.GetUserInfo(int(chatMsg.SenderType), chatMsg.SenderID)
		if err != nil {
			return chat.ChatResponse{}, err
		}

		var content string
		if chatMsg.ContentType == 1 {
			content = chatMsg.Content
		} else if chatMsg.ContentType == 2 {
			content = tools.AddCdn(chatMsg.Image)
		}else if chatMsg.ContentType == chat.CHAT_CONTENT_TYPE_ADDRESS {
			content = chatMsg.Content
		}
		if len(chatMsg.OrderSerialNumber) > 0 {
			orderSerialNumber = chatMsg.OrderSerialNumber
		}
		if len(chatMsg.OrderNo) > 0 {
			orderNo = chatMsg.OrderNo
		}

		chatMsgs = append(chatMsgs, chat.ChatMessage{
			Id:          chatMsg.ID,
			Avatar:      userInfo.Avatar,
			Content:     content,
			Type:        getContentTypeMsg(chatMsg.ContentType),
			CardType:    chatMsg.CardType,
			CardContent: chatMsg.CardContent,
			Rule:        chatMsg.SenderType,
			MsgDateTime: chatMsg.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	res, err := s.GetRestaurantInfo(orderId)
	if err != nil {
		return chat.ChatResponse{}, err
	}
	var images []string
	images = append(images, tools.AddCdn(res.Logo))
	images = append(images, tools.GetDefaultShipperImage())
	images = append(images, tools.GetDefaultUserImage())
	chatResponse = chat.ChatResponse{
		ResturantName:     res.Name,
		RestaurantImage:   GetFullUrl(res.Logo),
		ChatMessage:       chatMsgs,
		OrderSerialNumber: orderSerialNumber,
		OrderNo:           orderNo,
		Images:            images,
		TotalCount:        totalCount,
	}

	return chatResponse, nil
}

func GetFullUrl(path string) string {
	return configs.MyApp.CdnUrl + path
}

// 描述：获取用户信息
// 作者：Qurbanjan
// 文件：ChatService.go
// 修改时间：2023/09/19 16:08
func (s ChatService) GetUserInfo(senderType int, senderID int64) (chat.UserInfo, error) {
	db := tools.GetDB()
	var userAvatar string
	if senderType != 1 {
		admin := models.Admin{}
		db.Select("avatar").First(&admin, "id = ?", senderID)
		userAvatar = admin.Avatar
	}
	switch senderType {
	case 1:
		user := models.User{}
		err := db.Select("avatar").First(&user, "id = ?", senderID).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return chat.UserInfo{}, err
		}
		userAvatar = user.Avatar
		if len(user.Avatar) == 0 {
			userAvatar = tools.GetDefaultUserImage()
		}
	case 2:
		if len(userAvatar) == 0 {
			userAvatar = tools.GetDefaultStoreImage()
		}

	case 3:
		if len(userAvatar) == 0 {
			userAvatar = tools.GetDefaultShipperImage()
		}
	case 4:
		if len(userAvatar) == 0 {
			userAvatar = tools.GetDefaultAdminImage()
		}
	case 5:
		if len(userAvatar) == 0 {
			userAvatar = tools.GetDefaultSystemImage()
		}
	}
	return chat.UserInfo{
		Avatar: tools.AddCdn(userAvatar),
	}, nil
}

// 描述：通过OrderId来获取餐厅信息
// 作者：Qurbanjan
// 文件：ChatService.go
// 修改时间：2023/09/20 16:34
func (s ChatService) GetRestaurantInfo(OrderID int64) (models.Restaurant, error) {
	db := tools.GetDB()
	lang := s.language
	var resInfo models.Restaurant
	err := db.Raw(`
		SELECT t_restaurant.id, t_restaurant.name_`+lang+` as name,t_restaurant.address_`+lang+` as address,t_restaurant.tel, t_restaurant.logo
		FROM t_order_today
		JOIN t_restaurant ON t_restaurant.id = t_order_today.store_id
		WHERE t_order_today.id = ?
		UNION
		SELECT t_restaurant.id, t_restaurant.name_`+lang+` as name,t_restaurant.address_`+lang+` as address,t_restaurant.tel, t_restaurant.logo
		FROM t_order
		JOIN t_restaurant ON t_restaurant.id = t_order.store_id
		WHERE t_order.id = ?
		LIMIT 1
	`, OrderID, OrderID).Scan(&resInfo).Error

	if err != nil {
		return models.Restaurant{}, err
	}
	return resInfo, nil
}

// 按照contentType返回字符串
func getContentTypeMsg(contentType int) string {
	var contentTypeMap = map[int]string{
		1: "text",
		2: "image",
		3: "card",
		4: "report",
		5: "reminder",
		6: "address",
	}
	if msg, ok := contentTypeMap[contentType]; ok {
		return msg
	}
	return "unknow"
}

// 描述：获取聊天列表
// 作者：Qurbanjan
// 文件：ChatService.go
// 修改时间：2023/09/20 16:37
func (s ChatService) GetChatList(ctx *gin.Context, senderID int64, senderType int, cityId int, areaId int, kw string, startDate string, endDate string,resId int,shipperId int) ([]chat.ChatListResponse, error) {
	db := tools.GetDB()
	var chatList []Other.OrderChatJoin

	if startDate == "" && endDate == "" {
		startDate = carbon.Now(configs.AsiaShanghai).AddDays(-7).Format("Y-m-d H:i:s")
		endDate = carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s")
	} else {
		startDate = carbon.Parse(startDate, configs.AsiaShanghai).Format("Y-m-d") + " 00:00:00"
		endDate = carbon.Parse(endDate, configs.AsiaShanghai).Format("Y-m-d") + " 23:59:59"
	}

	kw = strings.TrimSpace(kw)

	query := db.Model(&Other.OrderChatJoin{}).
		Where("user_id  = ?", senderID).
		Preload("ChatDetail", func(d *gorm.DB) *gorm.DB {
			return d.Order("id asc").Group("order_id")
		}).
		Where("created_at > ? and created_at <= ? ", startDate, endDate).
		Order("updated_at desc").
		Group("order_id")

	if len(kw) > 0 {
		query.
			Where("res_name_ug like ? or res_name_zh like ? or customer_phone like ? or order_no like ? or order_serial_number like ?",
				kw+"%", kw+"%", kw+"%", kw+"%", kw+"%")
	}
	//聊天室中查找 店铺
	if resId > 0 {
		query = query.Where(" res_id = ? ",resId)
	}
	//聊天室中查找 配送员
	if shipperId > 0 {
		query = query.Where(" user_type = ? and user_id = ? ",3,shipperId)
	}

	if senderType == models.AdminTypeShipperAdmin || senderType == models.AdminTypeShipper{
		query.Where("user_type = 3")
	}
	if senderType == models.AdminTypeRestaurantAdmin || senderType == models.AdminTypeRestaurantAdminSub{
		query.Where("user_type = 2")
	}

	query.Scopes(scopes.Paginate(ctx))
	query.Find(&chatList)

	chatListResponse := make([]chat.ChatListResponse, 0, len(chatList))

	adminType := ""
	groupPrefix := "group_"
	var admin models.Admin
	db.Model(&models.Admin{}).Where("id = ?", senderID).Find(&admin)
	switch senderType { //管理员类型（OWNER:1,ADMIN:2,DEALER:3,DEALER_SUB:4,RESTAURANT_ADMIN:5,RESTAURANT_ADMIN_SUB:6,CENTURION:7,SHIPPER_ADMIN:8,SHIPPER:9）
	case 1, 2, 3 ,4: //后台获取聊天列表
		//OWNER:1,ADMIN:2,DEALER:3,DEALER_SUB:4
		adminType = "_admin_count"
		//该代理区域的所有聊天记录
		if senderType == 1 { //超级管理员
			if areaId == 0 {
				return chatListResponse, errors.New(s.langUtil.T("area_id_is_required"))
			}

		} else if senderType == 2 || senderType == 3 || senderType == 4 { //管理员
			var area models.AdminAreas
			db.Model(&models.AdminAreas{}).Select("admin_areas.area_id").
				Joins("LEFT JOIN t_admin ON t_admin.id = admin_areas.admin_id").
				Where("admin_areas.admin_id=?", admin.ID).
				Where("t_admin.type in (2,3,4)").
				First(&area)
			areaId = area.AreaID
		}
		// 使用子查询获取最新记录的完整信息
		q := db.Model(&Other.OrderChatJoin{}).
			Where("area_id  = ?", areaId).
			Preload("ChatDetail", func(d *gorm.DB) *gorm.DB {
				return d.Order("id asc").Group("order_id")
			}).
			Where("created_at > ? and created_at <= ? ", startDate, endDate).
			Order("updated_at desc").
			Group("order_id").
			Scopes(scopes.Paginate(ctx))
		if len(kw) > 0 {
			q.
				Where("res_name_ug like ? or res_name_zh like ? or customer_phone like ? or order_no like ? or order_serial_number like ?",
					kw+"%", kw+"%", kw+"%", kw+"%", kw+"%")
		}

		//聊天室中查找 店铺
		if resId > 0 {
			q = q.Where(" res_id = ? ",resId)
		}
		//聊天室中查找 配送员
		if shipperId > 0 {
			q = q.Where(" user_type = ? and user_id = ? ",3,shipperId)
		}

		q.Find(&chatList)

	case 5, 6: //商家获取聊天列表
		//4,RESTAURANT_ADMIN:5,RESTAURANT_ADMIN_SUB
		var res models.Restaurant
		db.Model(&models.Restaurant{}).Select("t_restaurant.id,"+
			"t_restaurant.city_id,"+
			"t_restaurant.area_id").
			Joins("INNER JOIN b_admin_store ON t_restaurant.id = b_admin_store.store_id").
			Joins("LEFT JOIN t_admin ON t_admin.id = b_admin_store.admin_id").
			Where("b_admin_store.admin_id=?", admin.ID).
			Where("t_admin.type in (5,6)"). //商家端登录 角色 5:店铺管理员 6:店铺副管理员
			Where("t_restaurant.deleted_at IS NULL").
			First(&res)

		adminType = "_restaurant_count"
		q := db.Model(&Other.OrderChatJoin{}).
			Where("user_id  = ?", res.ID).
			Preload("ChatDetail", func(d *gorm.DB) *gorm.DB {
				return d.Order("id asc").Group("order_id")
			}).
			Where("city_id  = ?", res.CityID).
			Where("area_id  = ?", res.AreaID).
			Where("created_at > ? and created_at <= ? ", startDate, endDate).
			Order("updated_at desc").
			Group("order_id").
			Scopes(scopes.Paginate(ctx))
		if len(kw) > 0 {
			q.
				Where("res_name_ug like ? or res_name_zh like ? or customer_phone like ? or order_no like ? or order_serial_number like ?",
					kw+"%", kw+"%", kw+"%", kw+"%", kw+"%")
		}
		//聊天室中查找 店铺
		if resId > 0 {
			q = q.Where(" res_id = ? ",resId)
		}
		//聊天室中查找 配送员
		if shipperId > 0 {
			q = q.Where(" user_type = ? and user_id = ? ",3,shipperId)
		}
		q.Find(&chatList)

	case 8, 9: //配送员获取聊天列表
		//SHIPPER_ADMIN:8,SHIPPER:9
		adminType = "_shipper_count"

	case 99: //用户获取聊天列表
		adminType = "_user_count"

	}

	for _, list := range chatList {
		res, err := s.GetRestaurantInfo(list.OrderID)
		if err != nil {
			return []chat.ChatListResponse{}, err
		}
		var images []string
		images = append(images, tools.AddCdn(res.Logo))
		images = append(images, tools.GetDefaultShipperImage())
		images = append(images, tools.GetDefaultUserImage())
		key := groupPrefix + tools.ToString(list.OrderID) + adminType
		if len(list.LastContent) == 0 {
			if list.ChatDetail.ContentType == 2 { //图片
				list.LastContent = s.langUtil.T("content_type_img")
			}else if list.ChatDetail.ContentType == 3 { //卡片
				list.LastContent = s.langUtil.T("content_type_card")
			}
		}
		chatListResponse = append(chatListResponse, chat.ChatListResponse{
			OrderId:           list.OrderID,
			RestaurantName:    res.Name,
			RestaurantImage:   tools.AddCdn(res.Logo),
			NewMsg:            list.LastContent,
			MsgCount:          tools.GetGroupChatMsgCount(key),
			MsgTime:           s.FormatTimeAgo(list.CreatedAt),
			ShipperImage:      tools.GetDefaultShipperImage(),
			UserImage:         tools.GetDefaultUserImage(),
			OrderSerialNumber: list.OrderSerialNumber,
			OrderNo:           list.OrderNo,
			Images:            images,
			ResNameUg:         list.ResNameUg,
			ResNameZh:         list.ResNameZh,
			CustomerPhone:     list.CustomerPhone,
		})
	}
	return chatListResponse, nil
}

// 描述：通过type
// 作者：Qurbanjan
// 文件：ChatService.go
// 修改时间：2023/09/20 19:30
func (s ChatService) GetNewMessage(Type int, content string, cardType int8) string {
	switch Type {
	case 1:
		return content
	case 2:
		return s.langUtil.T("ImgSrc")
	case 3:
		return s.langUtil.TArr("order_state")[int(cardType)]
	default:
		return "未知"
	}
}

// 描述：把时间转换刚刚、几个小时前、几天前等
// 作者：Qurbanjan
// 文件：ChatService.go
// 修改时间：2023/09/20 16:38
func (s ChatService) FormatTimeAgo(t time.Time) string {
	now := time.Now()
	diff := now.Sub(t)

	if diff < time.Minute {
		return s.langUtil.T("right_now")
	} else if diff < time.Hour {
		minutes := int(diff.Minutes())
		return fmt.Sprintf(s.langUtil.T("before_minute"), minutes)
	} else if diff < time.Hour*24 {
		hours := int(diff.Hours())
		return fmt.Sprintf(s.langUtil.T("before_hour"), hours)
	} else if diff < time.Hour*24*7 {
		days := int(diff.Hours() / 24)
		return fmt.Sprintf(s.langUtil.T("before_day"), days)
	} else {
		return t.Format("2006-01-02")
	}
}

// 描述：获取CMS后台聊天室订单详情
// 作者：Qurbanjan
// 文件：ChatService.go
// 修改时间：2023/11/16 16:38
func (s ChatService) GetOrderInfo(orderId int64) (chat.OrderInfo, error) {
	db := tools.GetDB()
	lang := s.language

	var order chat.OrderInfo
	var orderLogs []chat.OrderLog
	var orderDetails []chat.FoodsLog
	var shipperInfo chat.ShipperInfo
	var ct int64
	db.Model(&models.OrderToday{}).Where("id = ?", orderId).Count(&ct)
	if ct == 0 {
		var orders models.Order
		db.Model(&models.Order{}).
			Preload("Restaurant").
			Preload("AddressView").
			Preload("OrderExtend").
			Where("t_order.id = ?", orderId).
			Find(&orders)
		order.OrderId = orders.OrderID
		order.UserName = orders.Name
		order.UserMobile = orders.Mobile
		order.Taked = orders.Taked
		order.ShipperId = orders.ShipperID
		if s.langUtil.Lang == "ug" {
			order.RestaurantName = orders.Restaurant.NameUg
			order.RestaurantAddress = orders.Restaurant.AddressUg
			order.BuildingName = orders.AddressView.BuildingNameUg
			order.StreetName = orders.AddressView.StreetNameUg
		} else {
			order.RestaurantName = orders.Restaurant.NameZh
			order.RestaurantAddress = orders.Restaurant.AddressZh
			order.BuildingName = orders.AddressView.BuildingNameZh
			order.StreetName = orders.AddressView.StreetNameZh
		}
		order.RestaurantMobile = orders.Restaurant.Tel
		order.BookingTime = carbon.Parse(orders.BookingTime, configs.AsiaShanghai).Format("Y-m-d H:i:s")
		if !tools.IsZeroTime(orders.DeliveryStartTime) {
			order.DeliveryStartTime = orders.DeliveryStartTime.Format("2006-01-02 15:04:05")
		}
		if !orders.DeliveryTakedTime.IsZero() {
			order.DeliveryTakedTime = orders.DeliveryTakedTime.Format("2006-01-02 15:04:05")
		}
		order.CreatedAt = orders.CreatedAt.Format("2006-01-02 15:04:05")
		if !tools.IsZeroTime(orders.OrderExtend.ShipperArrivedShopAt) {
			order.ShipperArrivedAtShop = orders.OrderExtend.ShipperArrivedShopAt.Format("2006-01-02 15:04:05")
		}
		if !tools.IsZeroTime(orders.OrderExtend.ShipperTakeFoodAt) {
			order.ShipperTakeFoodAt = orders.OrderExtend.ShipperTakeFoodAt.Format("2006-01-02 15:04:05")
		}
		
		if !tools.IsZeroTime(orders.DeliveryEndTime){
			order.DeliveryEndTime = orders.DeliveryEndTime.Format("2006-01-02 15:04:05")
		}
		order.Description = orders.Description

	} else {
		var orders models.OrderToday
		db.Model(&models.OrderToday{}).
			Preload("Restaurant").
			Preload("AddressView").
			Preload("OrderExtend").
			Where("t_order_today.id = ?", orderId).
			Find(&orders)
		order.OrderId = orders.OrderID
		order.UserName = orders.Name
		order.UserMobile = orders.Mobile
		order.Taked = orders.Taked
		order.ShipperId = orders.ShipperID
		if s.langUtil.Lang == "ug" {
			order.RestaurantName = orders.Restaurant.NameUg
			order.RestaurantAddress = orders.Restaurant.AddressUg
			order.BuildingName = orders.AddressView.BuildingNameUg
			order.StreetName = orders.AddressView.StreetNameUg
		} else {
			order.RestaurantName = orders.Restaurant.NameZh
			order.RestaurantAddress = orders.Restaurant.AddressZh
			order.BuildingName = orders.AddressView.BuildingNameZh
			order.StreetName = orders.AddressView.StreetNameZh
		}
		order.RestaurantMobile = orders.Restaurant.Tel
		order.BookingTime = carbon.Parse(orders.BookingTime, configs.AsiaShanghai).Format("Y-m-d H:i:s")
		if !orders.DeliveryStartTime.IsZero() {
			order.DeliveryStartTime = orders.DeliveryStartTime.Format("2006-01-02 15:04:05")
		}
		if !orders.DeliveryTakedTime.IsZero() {
			order.DeliveryTakedTime = orders.DeliveryTakedTime.Format("2006-01-02 15:04:05")
		}
		order.CreatedAt = orders.CreatedAt.Format("2006-01-02 15:04:05")
		if orders.OrderExtend != nil && orders.OrderExtend.ShipperArrivedShopAt != nil {
			order.ShipperArrivedAtShop = orders.OrderExtend.ShipperArrivedShopAt.Format("2006-01-02 15:04:05")
		}
		if orders.OrderExtend != nil && !tools.IsZeroTime(orders.OrderExtend.ShipperTakeFoodAt) {
			order.ShipperTakeFoodAt = orders.OrderExtend.ShipperTakeFoodAt.Format("2006-01-02 15:04:05")
		}
		if !orders.DeliveryEndTime.IsZero() {
			order.DeliveryEndTime = orders.DeliveryEndTime.Format("2006-01-02 15:04:05")
		}
		order.Description = orders.Description
	}

	orderLogQuery := db.Model(&models.OrderStateLog{}).
		Where("order_id = ?", orderId).
		Order("updated_at DESC")

	if err := orderLogQuery.Find(&orderLogs).Error; err != nil {
		return chat.OrderInfo{}, errors.New(s.langUtil.T("error_happened"))
	}

	order.OrderLog = orderLogs

	err := db.Table("t_order_detail").
		Select("t_order_detail.store_foods_id,t_order_detail.price, t_order_detail.number, t_order_detail.lunch_box_fee,t_order_detail.lunch_box_count, t_restaurant_foods.name_"+lang+" as food_name").
		Joins("left join t_restaurant_foods on t_order_detail.store_foods_id = t_restaurant_foods.id").
		Where("t_order_detail.order_id = ?", orderId).
		Find(&orderDetails).Error

	if err != nil {
		return chat.OrderInfo{}, errors.New(s.langUtil.T("error_happened"))
	}
	order.Foods = orderDetails

	if order.Taked == 1 {
		db.Model(models.Admin{}).
			Select("real_name as shipper_name, mobile as shipper_mobile").
			Where("id = ?", order.ShipperId).
			Find(&shipperInfo)
	}

	order.Shipper = shipperInfo

	return order, nil
}

// 描述：在群消息加上群消息的数量
// 作者：rozimamat
// 文件：ChatService.go
// 修改时间：2023/11/15 15:38
func (s ChatService) UpdateGroupChatCount(key string, count int, duration int) int64 {

	redisHelper := tools.GetRedisHelper()
	c := redisHelper.Context()
	if exists, _ := redisHelper.Exists(c, key).Result(); exists == 0 {
		redisHelper.SetNX(c, key, 1, time.Duration(duration)*time.Second).Result()
		return 1
	} else {
		//信息过期时间
		expires := redisHelper.TTL(c, key).Val().Seconds()
		luaScript := `
			local oldStock = tonumber(redis.call('get', KEYS[1]))
			if (oldStock==false)  then
				return 0
			end
			local newStock = oldStock+tonumber(KEYS[2])
			redis.call('set', KEYS[1] ,newStock)
			redis.call('EXPIRE', KEYS[1],KEYS[3])	
			return newStock
		`
		leftStock, _ := redisHelper.Eval(redisHelper.Context(), luaScript, []string{key, tools.ToString(count), tools.ToString(expires)}).Int64()
		return leftStock
	}

}

// 违禁词检查
func (s ChatService) IsChatForbiddenWord(word string) bool {
	flag := false
	db := tools.GetDB()
	key := "forbidden_words"
	var words []chat.ChatForbiddenWords
	wordsStr := tools.Remember2(key, time.Second*60, func() interface{} {
		var words []chat.ChatForbiddenWords
		db.Model(&chat.ChatForbiddenWords{}).Find(&words)
		return words
	})
	_ = json.Unmarshal([]byte(wordsStr), &words)
	aes := tools.NewAESTool()
	for _, vv := range words {
		originalWord := vv.Word
		if vv.State == 1 {
			original, _ := aes.Decrypt(vv.Word)
			originalWord = original
		}
		if strings.Contains(word,originalWord){
			flag = true
			break
		}
	}
	
	return flag
}

// CreateForbiddenWord
//
//  @Author: YaKupJan
//  @Date: 2024-10-11 11:02:15
//  @Description: 添加违禁词
//  @receiver s
//  @param word
func (s ChatService) CreateForbiddenWord(word string) {
	aes := tools.NewAESTool()
	hash := aes.GenerateHash(word) // 生成原始词的哈希值

	db := tools.GetDB()
	var existingWord chat.ChatForbiddenWords
	// 检查数据库中是否存在相同的原始词
	if err := db.Where("word = ? and state = ?", word,0).First(&existingWord).Error; err == nil {
		return
	}
	// 查找数据库中是否有相同的哈希值
	if err := db.Where("hash = ?", hash).First(&existingWord).Error; err == nil {
		return
	}

	// 如果不存在，则加密并创建新记录
	encrypt, encryptErr := aes.Encrypt(word)
	if encryptErr != nil {
		panic(encryptErr)
	}

	newForbiddenWord := chat.ChatForbiddenWords{
		Word:  encrypt,
		Hash:  hash,   // 存储哈希值
		State: 1,      // 1 表示加密的状态
	}

	// 保存新词到数据库
	if err := db.Create(&newForbiddenWord).Error; err != nil {
		panic(err)
	}
}

// EncryptAndUpdateStateZeroWords
//
//  @Author: YaKupJan
//  @Date: 2024-10-11 11:02:50
//  @Description: 将状态为0的禁用词进行加密并更新
//  @receiver s
func (s ChatService) EncryptAndUpdateStateZeroWords() {
	aes := tools.NewAESTool()
	db := tools.GetDB()

	// 查询所有 state = 0 的禁用词
	var forbiddenWords []chat.ChatForbiddenWords
	if err := db.Where("state = ?", 0).Find(&forbiddenWords).Error; err != nil {
		panic(err)
	}

	// 遍历每个禁用词
	for _, fw := range forbiddenWords {
		// 生成哈希值
		hash := aes.GenerateHash(fw.Word)

		// 对原始词进行加密
		encrypt, encryptErr := aes.Encrypt(fw.Word)
		if encryptErr != nil {
			panic(encryptErr)
		}

		// 更新现有记录的哈希值和加密后的词，并将 state 设置为 1
		fw.Word = encrypt
		fw.Hash = hash
		fw.State = 1

		// 保存更新到数据库
		if err := db.Save(&fw).Error; err != nil {
			panic(err)
		}
	}
}

// 修改消息最后一次获取时间
func (s ChatService) UpdateLastReadAt(orderId int64) {
	db := tools.GetDB()
	// 获取之前修改最后一次获取时间
	err := db.Model(models.OrderExtend{}).
		Where("order_id = ?", orderId).
		Update("last_read_at", carbon.Now(configs.AsiaShanghai).ToDateTimeString()).Error
	if err != nil {
		tools.Logger.Infof("修改消息最后一次获取时间失败: ", err.Error())
	}
}