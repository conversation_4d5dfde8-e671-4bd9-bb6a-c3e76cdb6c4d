package payment

import (
	"mulazim-api/resources"
	"mulazim-api/services"

	"github.com/gin-gonic/gin"
)

//
//  WechatECommerceService
//  @Description: 微信渠道API
//
type WechatECommerceService struct {
	services.BaseService
	PaymentInterface
}


// 取消订单
func (l *WechatECommerceService)CancelOrder(ip string, order map[string]interface{}, orderId int, userId int, reasonId int, terminalId int, userType int, from string) error {
	// TODO: 实现取消订单逻辑
	return nil
}

// 检查订单是否可以取消
func (l *WechatECommerceService)CheckCancelOrder(orderId int, userId int, reasonId int, from string, userType int) (map[string]interface{}, error) {
	// TODO: 实现检查订单是否可以取消逻辑
	return nil, nil
}

// 每小时检查订单支付状态
func (l *WechatECommerceService)CheckOrderPayStateHourly(context *gin.Context) {
	// TODO: 实现每小时检查订单支付状态逻辑
}

// 检查重复支付订单
func (l *WechatECommerceService)CheckDupilicatedPayedOrder(context *gin.Context) {
	// TODO: 实现检查重复支付订单逻辑
}

// 检查资源现金
func (l *WechatECommerceService)CheckResCash(context *gin.Context) {
	// TODO: 实现检查资源现金逻辑
}

// 推送取消通知
func (l *WechatECommerceService)PushCancelNotify(from string, order map[string]interface{}) {
	// TODO: 实现推送取消通知逻辑
}

// 获取支付参数（微信支付，拉卡拉，微信收付通）
func (l *WechatECommerceService)GetPayParam(language string, orderId int, userId int, payId int, openId string, realIp map[string]interface{}, terminalId int, payerType int, payFrom int, appId string) (map[string]interface{}, error) {
	// TODO: 实现获取支付参数逻辑
	return nil, nil
}

// 获取代理支付参数
func (l *WechatECommerceService)GetAgentPayParam(language string, id int, openId string, realIp map[string]interface{}, terminalId int) (map[string]interface{}, error) {
	// TODO: 实现获取代理支付参数逻辑
	return nil, nil
}

// 获取代理支付参数
func (l *WechatECommerceService) GetAgentPriceUpPayParam(language string, id int, openId string, realIp map[string]interface{}, terminalId int,appId string) (map[string]interface{}, error) {
	// TODO: 实现获取代理支付参数逻辑
	return nil, nil
}

func (l *WechatECommerceService) GetAgentPriceUpCheck(id int) (map[string]interface{}, error){
	// TODO: 实现获取代理支付参数逻辑
	return nil, nil
}

func (l *WechatECommerceService) GetAgentPriceUpRefund(c *gin.Context,id int) (map[string]interface{}, error){
	// TODO: 实现获取代理支付参数逻辑
	return nil, nil
}


//部分退款
func (l *WechatECommerceService) PostPartRefund(ip string,details resources.PartRefund) (error) {
	return nil
}
//部分退款查询 
func (l *WechatECommerceService) PartRefundCheck(
	refundParams resources.PartRefund,
	) (int64, error){
		return 0,nil
	}

// 支付通知
func (l *WechatECommerceService)Notify(context *gin.Context) {
	// TODO: 实现支付通知逻辑
}

// 小程序支付通知
func (l *WechatECommerceService)MiniNotify(context *gin.Context) (bool, string, int) {
	// TODO: 实现小程序支付通知逻辑
	return false, "", 0
}

func (l WechatECommerceService)  GetOrderStatus(orderId int) ([]map[string]interface{}, error){
	return nil,nil
}