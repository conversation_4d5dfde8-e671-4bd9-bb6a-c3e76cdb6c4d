package payment

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/lang"
	"mulazim-api/models"
	
	"mulazim-api/resources/LakalaEntity"
	"mulazim-api/services"
	"mulazim-api/tools"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/resources"
	
)

//
//  LakalaService
//  @Description: 拉卡拉渠道API
//
type LakalaService struct {
	langUtil *lang.LangUtil
	language string
	services.BaseService
	PaymentInterface
}


// 取消订单
func (l *LakalaService)CancelOrder(ip string, order map[string]interface{}, orderId int, userId int, reasonId int, terminalId int, userType int, from string) error {
	// TODO: 实现取消订单逻辑
	return nil
}

// 检查订单是否可以取消
func (l *LakalaService)CheckCancelOrder(orderId int, userId int, reasonId int, from string, userType int) (map[string]interface{}, error) {
	// TODO: 实现检查订单是否可以取消逻辑
	return nil, nil
}

// 每小时检查订单支付状态
func (l *LakalaService)CheckOrderPayStateHourly(context *gin.Context) {
	// TODO: 实现每小时检查订单支付状态逻辑
}

// 检查重复支付订单
func (l *LakalaService)CheckDupilicatedPayedOrder(context *gin.Context) {
	// TODO: 实现检查重复支付订单逻辑
}

// 检查资源现金
func (l *LakalaService)CheckResCash(context *gin.Context) {
	// TODO: 实现检查资源现金逻辑
}

// 推送取消通知
func (l *LakalaService)PushCancelNotify(from string, order map[string]interface{}) {
	// TODO: 实现推送取消通知逻辑
}

// 获取支付参数（微信支付，拉卡拉，微信收付通）
func (l *LakalaService)GetPayParam(language string, orderId int, userId int, payId int, openId string, realIp map[string]interface{}, terminalId int, payerType int, payFrom int, appId string) (map[string]interface{}, error) {
	
	//判断旧版 微信还是 收付通 然后继续执行下单逻辑
	//1.判断该订单是否可以支付
	//查询是否可以支付
	openId,orderToday,err2 := l.CheckCanPay(orderId,userId,openId)
	if err2 != nil {
		return nil,err2
	}
	//其他参数
	// extraParams :=make(map[string]interface{})
	// orderNum :=orderToday.OrderID
	amount :=orderToday.ActualPaid

	outOrderNo := tools.GenerateLakalaOutOrderNo("O")
	

	payResult,err := l.getPayParams(orderId,outOrderNo,amount,openId,realIp,payerType,payFrom,appId,"order")

	if err != nil {
		return nil,err
	}
	tools.Logger.Info("订单支付参数",payResult)
	if  orderToday.User.ID > 0 { //写入订单发送消息必要的信息
		resName := orderToday.Restaurant.NameUg
		langId :=1
		if language != "ug" {
			resName = orderToday.Restaurant.NameZh
			langId = 2
		}
		//微信通知数据写入
		l.CreateWechatMessage(orderId,orderToday.OrderID,
			orderToday.Restaurant.ID,resName,
			orderToday.Restaurant.Tel,orderToday.ActualPaid,
			orderToday.BookingTime,orderToday.User.OpenID,langId)
	}

	return payResult,nil
}

// 获取代理支付参数
func (l *LakalaService)GetAgentPayParam(language string, id int, openId string, realIp map[string]interface{}, terminalId int) (map[string]interface{}, error) {
	// TODO: 实现获取代理支付参数逻辑
	//判断旧版 微信还是 收付通 然后继续执行下单逻辑
	//1.判断该订单是否可以支付
	db :=tools.GetDB()
	var log models.AdminRechargeLog
	var paid int
	isPacketOrder :=0
	var rechargeAmount int
	var outTradeNo string
	db.Model(&models.AdminRechargeLog{}).
		Where("id = ? and deleted_at is null ",id).
		Find(&log)
	paid 			= log.Payed
	rechargeAmount  = log.RechargeAmount
	outTradeNo      = log.OutTradeNo
	if log.ID == 0 {
		var logPacket models.AdminPacketRechargeLog
		db.Model(&models.AdminPacketRechargeLog{}).
			Where("id = ? and deleted_at is null ",id).
			Find(&logPacket)
		if logPacket.ID == 0 {
			return nil,errors.New("order_is_null")
		}
		isPacketOrder =1
		paid 			= logPacket.Payed
		rechargeAmount  = logPacket.RechargeAmount
		outTradeNo      = logPacket.OutTradeNo

	}

	if paid ==1 {
		return nil,errors.New("order_state_error")
	}

	extraParams :=make(map[string]interface{})
	extraParams["isPacketOrder"] = isPacketOrder
	extraParams["rechargeAmount"] = rechargeAmount
	extraParams["outTradeNo"] = outTradeNo
	payResult,err := l.getAgentPayParams(id,openId,outTradeNo,rechargeAmount,isPacketOrder,realIp)

	if err != nil {
		return nil,err
	}

	return payResult,nil

}

//获取支付参数 代理支付 优惠券
func (la LakalaService) getAgentPayParams(id int,openId string,outOrderNo string,amount int,isPacketOrder int,realIp map[string]interface{}) (map[string]interface{}, error) {
	db :=tools.GetDB()
	var lakalaAgent models.PayAgentLakala
	db.Model(&models.PayAgentLakala{}).Where("charge_id = ? and payer_openid = ? ",id,openId).Order("id desc").First(&lakalaAgent)
	if lakalaAgent.ID > 0 {
		if lakalaAgent.PayStatus  == 2002 { //交易关闭
			return nil,errors.New("pay_status_is_close")
		}
		if lakalaAgent.PayStatus  != 0 { //交易成功
			payInfo,_:=tools.StringToMap(lakalaAgent.PayInfo)
			return payInfo,nil
		}
	}

	orderName := fmt.Sprintf("支付：%d",amount)
	params,splitRuleData,payMethodData :=la.createAgentPayParams(outOrderNo,amount,orderName,realIp,configs.MyApp.LakalaConfig.AgentPayAppId,openId)

	lakalaAgent,err :=la.createAgentPayLog(id,openId,outOrderNo,amount,
		params,realIp,payMethodData,splitRuleData)

	if err != nil {
		tools.Logger.Error("拉卡拉下单失败",err.Error())
		return nil,errors.New("拉卡拉下单失败")
	}

	//发送拉卡拉支付请求 并 更新相关数据
	payReponse,err:=la.sendLakalaPayRequest(params)

	if err != nil {
		tools.Logger.Error("拉卡拉下单失败",err.Error())
		return nil,errors.New("拉卡拉下单失败")
	}
	lakalaAgentId :=lakalaAgent.ID
	err = la.updateAgentPayLog(lakalaAgentId,isPacketOrder,payReponse)
	if err != nil {
		tools.Logger.Error("拉卡拉下单失败",err.Error())
		return nil,errors.New("拉卡拉下单失败")
	}
	payInfoBytes,_:=json.Marshal(payReponse.Result.PayInfo)
	payInfo :=string(payInfoBytes)
	payResult,_:=tools.StringToMap(payInfo)
	return payResult,nil
}

// 支付通知
func (l *LakalaService)Notify(context *gin.Context) {
	// TODO: 实现支付通知逻辑
}

// 小程序支付通知
func (l *LakalaService)MiniNotify(context *gin.Context) (bool, string, int) {
	// TODO: 实现小程序支付通知逻辑
	return false, "", 0
}

//支付前的验证
func (l *LakalaService) CheckCanPay(orderId int,userId int,openId string) (string,models.OrderToday,error){
	db :=tools.GetDB()
	var orderToday models.OrderToday
	db.Model(&models.OrderToday{}).
		Preload("Restaurant").
		Preload("User").
		Where("id = ? and deleted_at is null ",orderId).
		Find(&orderToday)

	
	createdAt :=orderToday.CreatedAt
	orderState := orderToday.State
	consumeType := orderToday.ConsumeType //付费类型（0现金,1在线支付,3现金订单代理支付）
	
	payType :=orderToday.PayType    //用什么付费(1 现金, 2 餐币, 3 支付宝,  4 银联, 5 微信)
	if orderToday.ID == 0 {
		return openId,orderToday,errors.New("order_is_null")
	}
	

	if orderToday.PayPlatform !=1 {
		return openId,orderToday,errors.New("pay_platform_is_not_lakala") //该订单不能走拉卡拉支付通道
	}

	//是否可以付款 当前时间 加10分钟 超过当前时间则不能支付
	nw := carbon.Now(configs.AsiaShanghai)
	expireTime :=  carbon.Time2Carbon(createdAt.Add(10*time.Minute))
	
	if  consumeType == 0 && payType == 0{ ///付费类型（0现金,1在线支付,3现金订单代理支付）
		if expireTime.Lt(nw) {
			return openId,orderToday,errors.New("order_is_overtime")
		}
	}else{//配送员支付
		if orderToday.CashClearState == 1 { //现金订单 已经支付完成
			return openId,orderToday,errors.New("order_state_error")
		}

		if !tools.InArray(orderState,[]int{4,5,6,7}) { //状态错误
			return openId,orderToday,errors.New("order_state_error") //订单状态错误
		}

		if payType != constants.PaymentAgentWechat { //代理支付
			return openId,orderToday,errors.New("order_cannot_repaid") //订单不能重复付款
		}
	}

	//订单关闭后不能支付
	if orderToday.State < 3 && ((orderToday.Coupon.ID > 0 && orderToday.Coupon.State == 2) && orderToday.SeckillState == 2 ){ //订单关闭后不能支付
		return openId,orderToday,errors.New("order_state_error")
	}
	
	var user models.User
	db.Model(&models.User{}).Where("id= ?",userId).Select("id,open_id").Find(&user)
	if len(user.OpenID) > 0 && len(openId) == 0{//朋友支付的时候的判断
		openId = user.OpenID
	}

	return openId,orderToday,nil
}


//获取支付参数
func (l *LakalaService)getPayParams(orderId int,
	outOrderNo string,amount uint,openId string,realIp map[string]interface{},
	payerType int,payFrom int,appId string,tp string) (map[string]interface{}, error) {

	//获取拉卡拉 未过期的下单参数
	exists,rs:=l.getExistingPayParams(orderId,openId,0)
	if exists {
		return rs,nil
	}
	orderName :=fmt.Sprintf("订单：%d",orderId)
	//生成拉卡拉支付参数
	params,splitRuleData,payMethodData:=l.createPayParams(outOrderNo,int(amount),orderName,realIp,appId,openId,tp)

	//记录拉卡拉支付参数
	lakalaPayModel,err :=l.createPayLog(orderId,outOrderNo,openId,
		outOrderNo,int(amount),params,realIp,payerType,payFrom,payMethodData,
		splitRuleData,"order")
	if err != nil {
		tools.Logger.Error("拉卡拉下单失败",err.Error())
		return nil,errors.New("拉卡拉下单失败")
	}

	//发送拉卡拉支付请求 并 更新相关数据
	payReponse,err:=l.sendLakalaPayRequest(params)

	payLakalaId :=lakalaPayModel.ID

	if err != nil {
		tools.Logger.Error("拉卡拉下单失败",err.Error())
		return nil,errors.New("拉卡拉下单失败")
	}

	err = l.updatePayLog(payLakalaId,payReponse)
	if err != nil {
		tools.Logger.Error("拉卡拉下单失败",err.Error())
		return nil,errors.New("拉卡拉下单失败")
	}
	payInfoBytes,_:=json.Marshal(payReponse.Result.PayInfo)
	payInfo :=string(payInfoBytes)
	payResult,_:=tools.StringToMap(payInfo)
	return payResult,nil
}

//获取拉卡拉 未过期的下单参数
func (l *LakalaService) getExistingPayParams(orderId int,openId string,tp int) (bool,map[string]interface{}){

	db :=tools.GetDB()
	var lakalaPayModel models.PayLakala
	q :=db.Model(&models.PayLakala{}).Where("payer_openid = ? and order_id = ?",openId,orderId)
	if tp > 0 {
		q.Where("object_type = ?",tp)
	}
	q.Order("id desc").First(&lakalaPayModel)

	if lakalaPayModel.ID > 0 && lakalaPayModel.PayStatus != 2002  { //订单不是关闭的
		nw := carbon.Now(configs.AsiaShanghai)
		expTime := carbon.Parse(lakalaPayModel.CreatedAt.Format("2006-01-02 15:04:05"), "Asia/Shanghai").AddMinutes(10)

		if expTime.Gt(nw) {//没有过期的
			rs,_:=tools.StringToMap(lakalaPayModel.PayInfo)
			return true,rs
		}
	}
	return false,nil
}

//生成拉卡拉支付参数
func (l *LakalaService) createPayParams(outOrderNo string,
	amount int,
	orderName string,
	realIp map[string]interface{},
	appId string,openId string,
	tp string,
) (map[string]interface{},string,string) {

	notifyUrl := configs.MyApp.SwooleOutUrl+"/ug/v1/payment/lakala/pay/notify"
	if tp == "price_markup" { //加价订单通知地址
		notifyUrl = configs.MyApp.SwooleOutUrl+"/ug/v1/payment/lakala/pay/notify" //TODO 暂时使用同一个地址，需要修改的话 改这里
	}

	params := map[string]interface{}{
		"seller_member_no" : configs.MyApp.LakalaConfig.SystemMerNo,
		"out_order_no" : outOrderNo,
		"out_request_no" : outOrderNo,
		"amount" : amount,
		"pay_amount" : amount,
		"terminal_ip" : tools.ToString(realIp["real_ip"]),
		"order_name" : orderName,
		"split_rule_data" : map[string]interface{}{
			"split_list" : []map[string]interface{}{
				{
					"member_no" : configs.MyApp.LakalaConfig.SystemMerNo,
					"amount" : amount,
					"special_account_no" : "S005",
				},
			},
		},
		"split_mode" : 0, //不分账
		"ext" : "order",
		"front_url" : notifyUrl,
		"back_url" : notifyUrl,
		"pay_method": map[string]interface{}{
			"JSAPI":map[string]interface{}{
				"member_no" : configs.MyApp.LakalaConfig.SystemMerNo,
				"merchant_no" : configs.MyApp.LakalaConfig.SystemMerchantNo,
				"amount" : amount,
				"mch_appid" : appId,
				"openid" : openId,
				"mode" : "wxpay",
				"front_url" : notifyUrl,
				"front_fail_url" : notifyUrl,
				"term_base_station":"00+LAC:6361+CID:58130",
				"timeout_express" : 10,
			},
		},
	}
	splitRuleBytes,_:=json.Marshal(params["split_rule_data"])
	splitRuleData :=string(splitRuleBytes)
	payMethodBytes,_:=json.Marshal(params["pay_method"])
	payMethodData :=string(payMethodBytes)

	return params,splitRuleData,payMethodData

}
//记录拉卡拉支付参数
func (l *LakalaService) createPayLog(orderId int,orderNum string,
	openId string,outOrderNo string,amount int,
	params map[string]interface{},
	realIp map[string]interface{},
	payerType int,payFrom int,
	payMethodData string,
	splitRuleData string,
	tp string,
	) (models.PayLakala,error){
	db :=tools.GetDB()
	lakalaPayModel := models.PayLakala{
		ObjectType: tp,
		OrderID: int64(orderId),
		ObjectID: int64(orderId),
		OrderNo: orderNum,
		PayerOpenid: openId,
		SellerMemberNo: configs.MyApp.LakalaConfig.SystemMerNo,
		OutOrderNo: outOrderNo,
		Amount: int64(amount),
		PayAmount: int64(amount),
		TerminalIP: tools.ToInt64(tools.ToString(realIp["real_ip_int"])),
		OrderName: tools.ToString(params["order_name"]),
		SplitMode: tools.ToInt(params["split_mode"]),
		FrontUrl: tools.ToString(params["front_url"]),
		BackUrl: tools.ToString(params["back_url"]),
		PayMethod: payMethodData,
		SplitRuleData: splitRuleData,
		OutRequestNo: tools.ToString(params["out_request_no"]),
		PayerType: uint(payerType),
		PayFrom: uint(payFrom),
		SplitRuleResult: "{}",//默认空
		PayInfo: "{}",

	}

	err :=db.Model(&models.PayLakala{}).Create(&lakalaPayModel).Error
	if err != nil {
		tools.Logger.Error("拉卡拉下单失败",err.Error())
		return lakalaPayModel,errors.New("拉卡拉下单失败")
	}
	return lakalaPayModel,nil
}
//发送拉卡拉支付请求 并 更新相关数据
func (l *LakalaService) sendLakalaPayRequest(params map[string]interface{}) (LakalaEntity.PayResponseData,error){
	var payReponse LakalaEntity.PayResponseData

	tools.Logger.Info("拉卡拉下单 发送参数:params:", params)
	result, err := l.SendData("**************", params, "order.consume.request")

	if err != nil {
		tools.Logger.Error("拉卡拉下单失败",err.Error())
		return payReponse,errors.New("拉卡拉下单失败")
	}

	var r LakalaEntity.PayResponse
	json.Unmarshal([]byte(result), &r)

	json.Unmarshal([]byte(r.Response), &payReponse)

	return payReponse,nil


}

/***
 * @Author: [rozimamat]
 * @description: 发送数据
 * @Date: 2023-07-10 19:12:47
 * @param {string} param
 * @param {string} api
 */
func (l *LakalaService) SendData(ip string, bizContent map[string]interface{}, api string) (string, error) {
	currentTime :=fmt.Sprintf("%d", time.Now().Unix())
	paramsStr1, _ := json.Marshal(bizContent)
	params := map[string]string{
		"app_id":    configs.MyApp.LakalaConfig.AppId, // "7076471896390946817",//正式环境appid
		"timestamp": currentTime,
		"version":   "3.0",
		"service":   api,
		"params":    string(paramsStr1),
	}

	url := configs.MyApp.LakalaConfig.ServerUrl

	method := "POST"
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)

	for k, v := range params {
		_ = writer.WriteField(k, v)
	}
	signContent := l.GetSignContent(params)
	sign := tools.DsaSign(signContent)

	if sign == "" {
		return "", errors.New("sign_empty")
	}
	_ = writer.WriteField("sign", sign)
	err := writer.Close()
	if err != nil {
		tools.Logger.Error("FATAL拉卡拉发送错误1", err,params)
		return "", err
	}

	params["sign"] = sign
	strBytes, _ := json.Marshal(params)
	requestMap :=map[string]interface{}{
		"api": api,
		"ip":  ip,
		"content": string(strBytes),
	}
	rsb,_:=json.Marshal(requestMap)
	tools.Logger.Info("拉卡拉发送数据:", string(rsb))

	client := &http.Client{
		Timeout: time.Second * 30,
	}
	req, err := http.NewRequest(method, url, payload)

	if err != nil {
		tools.Logger.Error("FATAL拉卡拉发送错误2", err,params)
		return "", err
	}
	req.Header.Add("User-Agent", "Apifox/1.0.0 (https://www.apifox.cn)")

	req.Header.Set("Content-Type", writer.FormDataContentType())
	res, err := client.Do(req)
	if err != nil {
		tools.Logger.Error("FATAL拉卡拉发送错误3", err,params)
		return "", err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	result := string(body)
	tools.Logger.Info("拉卡拉回复的数据:", result)
	if err != nil {
		tools.Logger.Error("FATAL拉卡拉发送错误4:", err,result)
		return "", err
	}
	return result, nil

}

/***
 * @Author: [rozimamat]
 * @description: 签名内容获取
 * @Date: 2023-07-17 13:43:12
 * @param {map[string]string} params
 */
func (l *LakalaService) GetSignContent(params map[string]string) string {
	stringToBeSigned := ""
	if val, ok := params["app_id"]; ok {
		stringToBeSigned += val
	}
	if val, ok := params["timestamp"]; ok {
		stringToBeSigned += val
	}
	if val, ok := params["version"]; ok {
		stringToBeSigned += val
	}
	if val, ok := params["service"]; ok {
		stringToBeSigned += val
	}
	if val, ok := params["params"]; ok {
		stringToBeSigned += val
	}
	if val, ok := params["image_type"]; ok {
		stringToBeSigned += val
	}
	if val, ok := params["image_name"]; ok {
		stringToBeSigned += val
	}
	stringToBeSigned = strings.ReplaceAll(stringToBeSigned, "\r\n", "")
	return stringToBeSigned
}

//更新拉卡拉支付记录
func (l *LakalaService) updatePayLog(id int64,
	payReponse LakalaEntity.PayResponseData) error{

	db :=tools.GetDB()
	payLakalaId :=id

	payInfoBytes,_:=json.Marshal(payReponse.Result.PayInfo)
	payInfo :=string(payInfoBytes)
	if payReponse.Status != "OK" {
		db.Model(&models.PayLakala{}).Where("id = ?",payLakalaId).Updates(&map[string]interface{}{
			"pay_status":payReponse.Result.PayStatus,
			"pay_info":payInfo,
		})
		result,_:=json.Marshal(payReponse.Result)
		tools.Logger.Error("拉卡拉下单失败",result)
		return errors.New("拉卡拉下单失败")
	}

	if payReponse.Result.PayStatus !=1001 {// 订单生成成功并支付状态为待支付

		db.Model(&models.PayLakala{}).Where("id = ?",payLakalaId).Updates(&map[string]interface{}{
			"pay_status":payReponse.Result.PayStatus,
			"pay_info":payInfo,
		})
		result,_:=json.Marshal(payReponse.Result)
		tools.Logger.Error("拉卡拉下单失败",result)
		return errors.New("拉卡拉下单失败")
	}
	db.Model(&models.PayLakala{}).Where("id = ?",payLakalaId).Updates(&map[string]interface{}{
		"pay_status":payReponse.Result.PayStatus,
		"pay_info":payInfo,
		"order_status":payReponse.Result.OrderStatus,
		"pay_seq_no":payReponse.Result.PaySeqNo,
		"order_no":payReponse.Result.OrderNo,

	})
	return nil

}

//生成代理 拉卡拉支付参数
func (la LakalaService) createAgentPayParams(outOrderNo string,
	amount int,
	orderName string,
	realIp map[string]interface{},
	appId string, openId string,
) (map[string]interface{},string,string) {

	params := map[string]interface{}{

		"payer_openid":openId,
		"seller_member_no" : configs.MyApp.LakalaConfig.SystemMerNo,
		"amount" : amount,

		"out_order_no" : outOrderNo,
		"out_request_no" : outOrderNo,

		"pay_amount" : amount,
		"terminal_ip" : tools.ToString(realIp["real_ip"]),
		"order_name" : orderName,
		"split_rule_data" : map[string]interface{}{
			"split_list" : []map[string]interface{}{
				{
					"member_no" : configs.MyApp.LakalaConfig.SystemMerNo,
					"amount" : amount,
					"special_account_no" : "S005",
				},
			},
		},
		"split_mode" : 0, //不分账
		"ext" : "order",
		"front_url" : configs.MyApp.SwooleOutUrl+"/ug/v1/payment/lakala-agent/notify",
		"back_url" : configs.MyApp.SwooleOutUrl+"/ug/v1/payment/lakala-agent/notify",
		"pay_method": map[string]interface{}{
			"JSAPI":map[string]interface{}{
				"amount" : amount,
				"mch_appid" : appId,
				"member_no" : configs.MyApp.LakalaConfig.SystemMerNo,
				"merchant_no" : configs.MyApp.LakalaConfig.SystemMerchantNo,
				"openid" : openId,
				"mode" : "wxpay",
				"front_url" : configs.MyApp.SwooleOutUrl+"/ug/v1/payment/lakala-agent/notify",
				"front_fail_url" : configs.MyApp.SwooleOutUrl+"/ug/v1/payment/lakala-agent/notify",
				"term_base_station":"00+LAC:6361+CID:58130",
				"timeout_express" : 10,
			},
		},
	}
	splitRuleBytes,_:=json.Marshal(params["split_rule_data"])
	splitRuleData :=string(splitRuleBytes)
	payMethodBytes,_:=json.Marshal(params["pay_method"])
	payMethodData :=string(payMethodBytes)



	return params,splitRuleData,payMethodData

}



//记录 代理 拉卡拉支付参数
func (la LakalaService) createAgentPayLog(id int,
	openId string,outOrderNo string,amount int,
	params map[string]interface{},
	realIp map[string]interface{},
	payMethodData string,
	splitRuleData string) (models.PayAgentLakala,error){
	db :=tools.GetDB()


	lakalaAgent := models.PayAgentLakala{
		ChargeID: id,
		PayerOpenID: openId,
		SellerMemberNo: configs.MyApp.LakalaConfig.SystemMerNo,
		OutOrderNo: outOrderNo,
		Amount: amount,
		PayAmount: amount,
		TerminalIP: tools.ToInt(tools.ToString(realIp["real_ip_int"])),
		OrderName: tools.ToString(params["order_name"]),
		SplitMode: tools.ToInt(params["split_mode"]),
		FrontURL: tools.ToString(params["front_url"]),
		BackURL: tools.ToString(params["back_url"]),
		PayMethod: payMethodData,
		SplitRuleData: splitRuleData,
		OutRequestNo: tools.ToString(params["out_request_no"]),
		SplitRuleResult: "{}",//默认空
		PayInfo: "{}",

	}

	err :=db.Model(&models.PayAgentLakala{}).Create(&lakalaAgent).Error
	if err != nil {
		tools.Logger.Error("拉卡拉下单失败",err.Error())
		return lakalaAgent,errors.New("拉卡拉下单失败")
	}
	return lakalaAgent,nil

}

//更新拉卡拉支付记录
func (la LakalaService) updateAgentPayLog(id int,isPacketOrder int,
	payReponse LakalaEntity.PayResponseData) error{

	db :=tools.GetDB()
	result,_:=json.Marshal(payReponse.Result)
	tools.Logger.Info("拉卡拉下单结果",string(result))
	payInfoBytes,_:=json.Marshal(payReponse.Result.PayInfo)
	payInfo :=string(payInfoBytes)
	if payReponse.Status != "OK" {
		db.Model(&models.PayAgentLakala{}).Where("id = ?",id).Updates(&map[string]interface{}{
			"pay_status":payReponse.Result.PayStatus,
			"pay_info":payInfo,
		})
		tools.Logger.Error("拉卡拉下单失败",result)
		return errors.New("拉卡拉下单失败")
	}

	if payReponse.Result.PayStatus !=1001 {// 订单生成成功并支付状态为待支付

		db.Model(&models.PayAgentLakala{}).Where("id = ?",id).Updates(&map[string]interface{}{
			"pay_status":payReponse.Result.PayStatus,
			"pay_info":payInfo,
		})
		result,_:=json.Marshal(payReponse.Result)
		tools.Logger.Error("拉卡拉下单失败",result)
		return errors.New("拉卡拉下单失败")
	}
	db.Model(&models.PayAgentLakala{}).Where("id = ?",id).Updates(&map[string]interface{}{
		"pay_status":payReponse.Result.PayStatus,
		"pay_info":payInfo,
		"order_status":payReponse.Result.OrderStatus,
		"pay_seq_no":payReponse.Result.PaySeqNo,

	})

	if isPacketOrder == 0 { //代理优惠券充值
		db.Model(&models.AdminRechargeLog{}).Where("id = ?",id).Updates(&map[string]interface{}{
			"transaction_id":payReponse.Result.OrderNo,
			"prepay_id":payReponse.Result.PayInfo.PrepayID,
		})
	}else{ //红包充值
		db.Model(&models.AdminPacketRechargeLog{}).Where("id = ?",id).Updates(&map[string]interface{}{
			"transaction_id":payReponse.Result.OrderNo,
			"prepay_id":payReponse.Result.PayInfo.PrepayID,
		})
	}
	return nil

}
//抽奖活动订单参数
func (l *LakalaService)GetLotteryPayParam(language string, orderId int, userId int, payId int, openId string, realIp map[string]interface{}, terminalId int, payerType int, payFrom int, appId string) (map[string]interface{}, error) {
	// TODO: 实现获取支付参数逻辑

	//判断旧版 微信还是 收付通 然后继续执行下单逻辑
	//1.判断该订单是否可以支付
	//查询是否可以支付
	openId,lotteryOrder,err2 := l.CheckLotteryCanPay(orderId,userId,openId)
	if err2 != nil {
		return nil,err2
	}
	//其他参数
	
	outOrderNo :=tools.GenerateLakalaOutOrderNo("LO")
	amount := uint(*lotteryOrder.Price)
	

	payResult,err := l.getPayParams(orderId,outOrderNo,amount,openId,realIp,payerType,payFrom,appId,"lottery_order")

	if err != nil {
		return nil,err
	}
	tools.Logger.Info("订单支付参数",payResult)
	
	return payResult,nil
}

//抽奖订单 是否可以支付
func (l *LakalaService) CheckLotteryCanPay(orderId int,userId int,openId string) (string,models.LotteryOrder,error){
	db :=tools.GetDB()
	
	var lotteryOrder models.LotteryOrder	//抽奖活动订单
	
	db.Model(&models.LotteryOrder{}).
		Where("id = ? and deleted_at is null ",orderId).
		Preload("LotteryActivity").
		Find(&lotteryOrder)
	if lotteryOrder.ID == 0 {
		return openId,lotteryOrder,errors.New("order_is_null")
	}
	if !(lotteryOrder.State>1) {
		return openId,lotteryOrder,errors.New("lottery_activity_is_expired")
	}

	nw := carbon.Now()
	expTime := carbon.Parse(lotteryOrder.LotteryActivity.EndTime.Format("Y-m-d H:i:s"), "Asia/Shanghai")

	if lotteryOrder.LotteryActivity.State !=1 || (lotteryOrder.LotteryActivity.State ==1 || expTime.Lt(nw)) {
		return openId,lotteryOrder,errors.New("lottery_activity_is_expired")
	}

	var user models.User
	db.Model(&models.User{}).Where("id= ?",userId).Select("id,open_id").Find(&user)
	if len(user.OpenID) > 0 && len(openId) == 0{//朋友支付的时候的判断
		openId = user.OpenID
	}
	return openId,lotteryOrder,nil
}




// 获取代理支付参数 加价美食  显示跳转的二维码的接口
func (l *LakalaService) GetAgentPriceUpPayCode(id int) (map[string]interface{}, error) {
	
	url :=strings.TrimRight(configs.MyApp.LakalaConfig.PayQRCodeUrl,"/")+"/priceup-pay?id="+tools.ToString(id)
	db :=tools.GetDB()
	var priceFood  models.PriceMarkupFood
	db.Model(&models.PriceMarkupFood{}).Where("id = ?",id).Find(&priceFood)
	if priceFood.State != 1 {//不可支付
		return nil,errors.New("price_food_is_not_pay")
	}
	amount :=priceFood.InPrice*priceFood.TotalCount
	chargeAmount :=fmt.Sprintf("%d",amount)	
	appData := map[string]interface{}{
		"qrcode_url": url ,
		"recharge_amount":chargeAmount,
		"out_trade_no":id, 
	}
	return appData, nil
}




// 获取代理支付参数 加价美食  支付参数接口
func (l *LakalaService) GetAgentPriceUpPayParam(language string, id int, openId string, realIp map[string]interface{}, terminalId int,appId string) (map[string]interface{}, error) {
	
	//1.判断该订单是否可以支付
	//查询是否可以支付
	db :=tools.GetDB()
	var priceFood models.PriceMarkupFood
	db.Model(&models.PriceMarkupFood{}).Where("id = ?",id).Find(&priceFood)
	if priceFood.State != 1 {//不可支付
		return nil,errors.New("price_food_is_not_pay")
	}
 
	amount :=uint(priceFood.InPrice*priceFood.TotalCount)

	outOrderNo := tools.GenerateLakalaOutOrderNo("PO")
	

	payResult,err := l.getAgentPriceUpPayParams(id,outOrderNo,amount,openId,realIp,0,2,appId)

	if err != nil {
		return nil,err
	}
	tools.Logger.Info("订单支付参数",payResult)
	

	return payResult,nil
}

 



//获取支付参数
func (l *LakalaService) getAgentPriceUpPayParams (id int,
	outOrderNo string,amount uint,openId string,realIp map[string]interface{},
	payerType int,payFrom int,appId string) (map[string]interface{}, error) {

	//获取拉卡拉 未过期的下单参数
	exists,rs:=l.getExistingPayParams(id,openId,3)
	if exists {
		return rs,nil
	}
	orderName :=fmt.Sprintf("加单：%d",id)
	//生成拉卡拉支付参数
	params,splitRuleData,payMethodData:=l.createPayParams(outOrderNo,int(amount),orderName,realIp,appId,openId,"price_markup")

	//记录拉卡拉支付参数
	lakalaPayModel,err :=l.createPayLog(id,outOrderNo,openId,
		outOrderNo,int(amount),params,realIp,payerType,payFrom,payMethodData,
		splitRuleData,"price_markup")
	if err != nil {
		tools.Logger.Error("拉卡拉下单失败",err.Error())
		return nil,errors.New("拉卡拉下单失败")
	}

	//发送拉卡拉支付请求 并 更新相关数据
	payReponse,err:=l.sendLakalaPayRequest(params)

	payLakalaId :=lakalaPayModel.ID

	if err != nil {
		tools.Logger.Error("拉卡拉下单失败",err.Error())
		return nil,errors.New("拉卡拉下单失败")
	}

	err = l.updatePayLog(payLakalaId,payReponse)
	if err != nil {
		tools.Logger.Error("拉卡拉下单失败",err.Error())
		return nil,errors.New("拉卡拉下单失败")
	}
	payInfoBytes,_:=json.Marshal(payReponse.Result.PayInfo)
	payInfo :=string(payInfoBytes)
	payResult,_:=tools.StringToMap(payInfo)
	return payResult,nil
}


//支付结果查询
func (l *LakalaService) GetAgentPriceUpCheck(id int) (map[string]interface{}, error) {
	db :=tools.GetDB()
	var priceFood models.PriceMarkupFood
	db.Model(&models.PriceMarkupFood{}).Where("id = ?",id).Find(&priceFood)
	if priceFood.State == 2 {
		return map[string]interface{}{
			"payed":1,
		},nil
	}
	return nil,nil


}

//退款
func (l LakalaService) GetAgentPriceUpRefund(c *gin.Context,id int) (map[string]interface{}, error) {
	db :=tools.GetDB()
	var priceFood models.PriceMarkupFood
	now :=carbon.Now(configs.AsiaShanghai)

	startTime := carbon.CreateFromTime(0, 0, 0, "Asia/Shanghai")
	endTime := carbon.CreateFromTime(8, 30, 0, "Asia/Shanghai")
	flag := now.Gt(startTime) && now.Lt(endTime)
	//如果在 00:00 和 08:30 之间 优惠券退款的话 要提示 系统维护中  防止出现余额不足
	if flag {
		return nil, errors.New("system_maintaining_in_progress")
	}

	//限流  10次请求 放行一次
	//防止多次点击
	
	redisHelper :=tools.GetRedisHelper()
	
	cacheKey := fmt.Sprintf("priceup_refund_%d", id)
	// 2分钟 能获取一次
	exists, _ := redisHelper.Exists(c, cacheKey).Result()
	if exists == 0 {
		redisHelper.Set(c, cacheKey, 1, 60*time.Second)
	} else {
		return nil,fmt.Errorf("sms_in_two_minutes")
	}
	tools.Logger.Info("加价美食过期后给代理退款开始 加价 id:", id)

	db.Model(&models.PriceMarkupFood{}).Where("id = ? ",id).Find(&priceFood)
	if priceFood.ID  == 0{
		return nil, errors.New("data_not_found")
	}
	if priceFood.RunningState  == 1{ //还在运行中不能退款 
		return nil, errors.New("not_stopped_yet")
	}
	if priceFood.Refunded  == 1{
		return nil, errors.New("refunded")
	}
	
	if priceFood.StopTime == nil {
		return nil, errors.New("not_stopped_yet")
	}

	flag1 := carbon.Now("Asia/Shanghai").Gt(carbon.Parse(priceFood.StopTime.Format("Y-m-d H:i:s"),configs.AsiaShanghai).AddDays(1))
	if flag1 {
		return nil, errors.New("not_stopped_yet")
	}

	var payLog models.PayLakala
	db.Model(&models.PayLakala{}).Where("object_type = ? and order_id = ? and order_status = ?","price_markup",priceFood.ID,1006).
	Find(&payLog)
	if payLog.ID == 0 {//支付记录不存在 成功的支付记录
		return nil, errors.New("no_pay_log")
	}


	amount :=0 //退款金额
	type SoldCountStruct struct {
		Count int64
	}
	var sold SoldCountStruct
	db.Model(&models.PriceMarkupFoodLog{}).Where("price_markup_id = ? and state in (2,3)",priceFood.ID).Select("if(sum(saled_count)>0,sum(saled_count),0) as count").Scan(&sold)

	if int(sold.Count) == priceFood.TotalCount {
		return nil, errors.New("no_refund_amount")
	}
	
	amount =(priceFood.TotalCount-int(sold.Count))*priceFood.InPrice
	tools.Logger.Info("[加价]退款 id:",priceFood.ID,", 总金额:",(priceFood.InPrice * priceFood.TotalCount),",退款金额:",amount)
	if amount <= 0 {
		return nil, errors.New("no_refund_amount")
	}

	//开始退款 
	//--------------3.退款--------开始-------//

	outRequestNo := tools.GenerateLakalaOutOrderNo("RF") //退款编号 

	api := "order.consume.refund"
	param := make(map[string]interface{})
	param["order_no"] = payLog.OrderNo // 原订单（需要退款的订单编号）
	param["member_no"] = payLog.SellerMemberNo

	// param["out_order_no"] = outRequestNo
	param["out_request_no"] = outRequestNo
	param["refund_amount"] = amount       // 退款金额
	param["pay_seq_no"] = payLog.PaySeqNo // 支付流水号
	refundRule := make([]map[string]interface{}, 0)
	refundRule = append(refundRule, map[string]interface{}{ // 支付流水号
		"special_account_no": "S005",
		"member_no":          payLog.SellerMemberNo,
		"amount":             amount,
	})
	param["refund_rule"] = refundRule

	refundRules := []models.RefundRule{
		{
			MemberNo:      payLog.SellerMemberNo,
			AccountTypeNo: "S005",
			Amount:        amount,
		},
	}

	var lakalaRefund = models.PayLakalaRefund{
		ObjectType:   "price_markup",
		ObjectID:     int64(priceFood.ID),
		OrderNo:      payLog.OrderNo,
		PayLakalaID:  int(payLog.ID),
		RefundRule:   refundRules,
		OutRequestNo: outRequestNo,
		RefundAmount: amount,
		PaySeqNo:     payLog.PaySeqNo,
	}
	err :=db.Model(&models.PayLakalaRefund{}).Create(&lakalaRefund).Error
	if err != nil {
		tools.Logger.Error("退款记录失败",err.Error())
		return nil,err
	}

	result, err := l.SendData("**************", param, api)
	if err != nil {
		tools.Logger.Error("拉卡拉[加价]美食 退款失败,加价id:", id, err.Error())
		tools.AliDeveloperDingdingMsg("拉卡拉[加价]美食 款失败,加价id:" + tools.ToString(id) + " " + err.Error())
		return nil, err
	}
	resultMap, _ := tools.StringToMap(result)
	response := resultMap["response"].(string)
	tools.Logger.Info("拉卡拉[加价]退款 拉卡拉退款结果：", response)
	resData, _ := tools.StringToMap(response)
	if resData["status"] != "OK" {
		tools.AliDeveloperDingdingMsg("拉卡拉 加价 退款失败,加价id:" + tools.ToString(id))
		tools.Logger.Error("拉卡拉[加价]退款失败,加价id:" + tools.ToString(id))
		return nil, errors.New("拉卡拉退款失败")
	}
	resMap := resData["result"].(map[string]interface{})
	if tools.ToInt(resMap["refund_status"]) != 1004 {
		errorMsg :=""
		if resMap["error_message"] !=nil {
			errorMsg = tools.ToString(resMap["error_message"])
		}
		errMessage := "拉卡拉[加价]退款失败,加价id:" + tools.ToString(id) + " " + errorMsg+" response:"+response
		tools.AliDeveloperDingdingMsg(errMessage)
		tools.Logger.Error(errMessage)
		return nil, errors.New("拉卡拉退款失败")
	}

	//开始退款 
	db.Model(&models.PriceMarkupFood{}).Where("id = ?",id).Updates(&map[string]interface{}{
		"state":5,//已退款
		"refunded":1,
		"refund_time":carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
	})
	
	//--------------3.退款--------结束-------//

	return nil,nil

}

func (l LakalaService) PostPartRefund(ip string,refundParams resources.PartRefund) (error) {
	return nil
}

//部分退款查询 
func (l LakalaService)  PartRefundCheck(
	refundParams resources.PartRefund,
	) (int64, error){
		return 0,nil
}

func (l LakalaService)  GetOrderStatus(orderId int) ([]map[string]interface{}, error){
	return nil,nil
}