package payment

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"mulazim-api/jobs"
	"mulazim-api/resources/LakalaEntity"
	"strings"
	"sync"
	"time"

	"gorm.io/gorm/clause"

	"mulazim-api/configs"
	"mulazim-api/models"
	"mulazim-api/services"
	"mulazim-api/services/merchant/lakala"

	"mulazim-api/tools"

	"mulazim-api/resources"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type PaymentService struct {
	services.BaseService
	PaymentInterface
}


// # 验证取消订单
//
//	1.管理员取消时必须填写原因(reason)
//	2.验证订单是否存在
//	3.验证是否取消或者退款
//	4.如果代理:验证代理是否存在并且是否改餐厅的管理者
//	5.pay_type > 7 && pay_type < 18
func (p PaymentService) CheckCancelOrder(orderId int, userId int, reasonId int, from string, userType int) (map[string]interface{}, error) {
	order := make(map[string]interface{})
	tools.Db.Table("t_order_today").Where("id", orderId).Scan(&order)
	// 订单是否存在
	if tools.GetMap(order, "id") == nil {
		return nil, errors.New("order_not_found")
	}
	// 订单是否已取消 或者
	if tools.ToInt(order["refunded"]) == 1 || tools.ToInt(order["state"]) == 8 || tools.ToInt(order["state"]) == 9 {
		return nil, errors.New("order_cancel")
	}

	// 客户只能待接单的订单取消操作
	if userType == services.TYPE_USER && tools.ToInt(order["state"]) != 3 {
		return nil, errors.New("order_state_error")
	}

	// 商家已接订单，并配送员已抢过的订单不能取消
	if userType == services.TYPE_ADMIN_MERCHANT && tools.ToInt(order["state"]) == 4 && tools.ToInt(order["shipper_id"]) > 0 {
		return nil, errors.New("order_seized")
	}
	// 商家端已出餐订单不能取消
	if userType == services.TYPE_ADMIN_MERCHANT && tools.ToInt(order["state"]) > 4 {
		return nil, errors.New("order_state_error")
	}

	// 验证订单餐厅是否属于该商家(自动退单不严重)
	if from == "api" {
		resCount := int64(0)
		tools.Db.Table("b_admin_store").Where("store_id", order["store_id"]).Where("admin_id", userId).Count(&resCount)
		if resCount == 0 {
			tools.Logger.Error("验证商家不属于该订单", order["store_id"], userId)
			return nil, errors.New("order_not_found")
		}
	}
	// 验证该订单是否属于退款用户
	if from == "mini" && tools.ToInt(order["user_id"]) != userId {
		tools.Logger.Error("验证该订单是否属于退款用户")
		return nil, errors.New("order_not_found")
	}
	return order, nil
}

//
// CancelOrder3
//  @Description: 取消订单
//  开启事务
// 删除7，10 的order_state_log
// 更新t_order_today
// 更新order_state_log
// 处理take_order

//  结束事务
// 如果是拉卡拉订单，则通过拉卡拉退款
// 如果是微信订单，则微信退款给用户
//  开启事务
// 如果是代理付款，则退款给代理
//  结束事务

// CancelOrder3 发送配送员通知
// 向客户发送订单取消微信通知消息
//
//	@author: Alimjan
//	@Time: 2023-07-21 16:31:05
//	@receiver p PaymentService
//	@param order map[string]interface{}
//	@param orderId int
//	@param userId int
//	@param reasonId int
//	@param terminalId int
//	@param userType int
//	@return error
func (p PaymentService) CancelOrder(ip string, order map[string]interface{}, orderId int, userId int, reasonId int, terminalId int, userType int, from string) error {
	oldOrderState := tools.ToInt(order["state"])
	tx := tools.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tools.Logger.Error("CancelOrder-Error", r)
			tx.Rollback()
		}
	}()

	// 删除
	if err := p.DeleteOrderStateLog(tx, orderId); err != nil {
		tx.Rollback()
		tools.Logger.Error("删除订单状态日志出错,订单号：", orderId, err)
		return err
	}
	// 更新订单信息
	if err := p.UpdateOrderToday(tx, orderId, order, userType, 1); err != nil {
		tx.Rollback()
		tools.Logger.Error("更新今日订单信息错误,订单号：", orderId, err)
		return err
	}
	if err := p.UpdateOrderStateLog(tx, orderId, tools.If(userType == 3, 9, 8), reasonId); err != nil {
		tx.Rollback()
		tools.Logger.Error("更新订单状态日志出错,订单号：", orderId, err)
		return err
	}
	if err := p.CancelTakeOrder(tx, orderId, 4, reasonId); err != nil {
		tx.Rollback()
		tools.Logger.Error("更新配送员接单日志出错,订单号：", orderId, err)
		return err
	}
	
	//部分退款的订单进行回复原始数据 
	isPartRefund,err :=p.partRefundRestoreData(tx, orderId,userId,userType,reasonId)
	if err != nil {
		tx.Rollback()
		tools.Logger.Error("部分退款恢复原始数据出错,订单号：", orderId, err)
		return err
	}

	tx.Commit()


	// 如果是拉卡拉订单，则通过拉卡拉退款
	if isLakalaPay(order) {
		// tools.Logger.Info("取消订单:这是拉卡拉订单",order)
		tools.Logger.Info("开始拉卡拉退款orderId:", order["id"])
		if err := p.LakalaRefund(ip, order,true,isPartRefund); err != nil {
			tools.Logger.Error(err)
			job := jobs.NewLakalaRefundJob()
			refundMap :=map[string]interface{}{
				"order_id": order["id"],
			}
			if isPartRefund {
				refundMap["part_refund"] = 1
			}
			job.ProduceMessageToConsumer(refundMap)
			return err
		}
	} else if isWechatPay(order) {
		// tools.Logger.Info("取消订单:这是微信订单",order)
		tools.Logger.Info("开始微信退款orderId:", order["id"])
		if err := p.WechatRefund(ip, order, userType); err != nil {
			tools.Logger.Error(err)
			return err
		}
	} else if isAgentPay(order) {
		// tools.Logger.Info("取消订单:这是代理代付订单",order)
		tools.Logger.Info("开始代理代付退款给代理orderId:", order["id"])
		if err := p.AgentRefund(orderId); err != nil {
			tools.Logger.Error(err)
			return err
		}
	}

	//  秒杀订单有必要返回库存
	if tools.ToInt(order["seckill_state"]) == 1 {
		p.OrderRefundStockAndAgentFee(ip, orderId)
	}

	

	// 发送通知部分
	// 1.如果商家取消订单要记录后台通知信息
	if userType == services.TYPE_ADMIN_MERCHANT || userType == services.TYPE_AUTO_REJECT {
		p.createNotification(oldOrderState, order)
	}
	// 2.配送员发送通知
	// if err := p.SendShipperSocketEvent(order, userType); err != nil {
	// 	tools.Logger.Error("配送端发送通知出错订单号:", orderId, err)
	// }

	// 3.发送微信通知
	if err := p.SendWechatOrderMessage(userType, orderId, reasonId, order); err != nil {
		tools.Logger.Error("发送微信通知出错:", orderId, err)
		// tools.Logger.Error(err)
	}

	// 4.发送现金订单取消短信
	if err := p.SendOrderCancelSms(orderId, 2, userType); err != nil {
		tools.Logger.Error("现金订单取消短信出错:", orderId, err)
	}

	//加价 日志更新  1:未支付，2：已支付，3:完成，4:退单
	p.PriceFoodLogUpdate(orderId,4)

	return nil
}

// pushCancelNotify 发送配送员通知
func (p PaymentService) PushCancelNotify(from string, order map[string]interface{}) {
	logPrefix :="order_push_"
	tools.Logger.Info(logPrefix+"退单推送：订单ID:",tools.ToString(order["id"]),"from:",from)
	go func ()  {
		defer func() {
			if r := recover(); r != nil {
				tools.Logger.Error("pushCancelNotify 发送配送员通知:", r)
			}
		}()
		
		
	if from == "cms" || from == "api" || from == "mini" {
		if shipperId, ok := order["shipper_id"]; ok {

			shipperId :=tools.ToInt(shipperId)
			tools.Logger.Info(logPrefix+"给配送员 取消 退单推送消息： 订单ID： "+tools.ToString(order["id"])+", 配送员"+tools.ToString(shipperId))
			if shipperId > 0 {
				var restaurant models.Restaurant
				rs := tools.GetDB().Model(models.Restaurant{}).Where("id = ?", tools.ToString(order["store_id"])).Scan(&restaurant)
				if rs.RowsAffected > 0 {
					resMap :=make(map[string]interface{})
					resMap["name_ug"] = restaurant.NameUg
					resMap["name_zh"] = restaurant.NameZh
					jobs.SendShipperCancelPush(order,resMap,tools.ToInt(shipperId),from)
					
				}
			}
		}
		if from == "cms" && order["store_id"] != nil { //后台退单的话需要给商家 激光推送
			storeUsers :=p.GetMerchantUsersByOrder("id = "+tools.ToString(order["id"]))
			//启动队列
			storeJiguang :=make([]int,0)//已发送激光推送列表 避免重复推送
			//订单属于的店铺有几个人登录就给他发送几个通知
			for _, su := range storeUsers {
				if !tools.InArray(su.UserId,storeJiguang){
					storeJiguang = append(storeJiguang,su.UserId)
				}else{ //已经发送过激光推送了 不会再发送了，另一个循环 启动后再次发送
					continue
				}
				
				redisHelper :=tools.GetRedisHelper()
				redisKeyPrefix :="order_push_"
				
				onlineKey :=fmt.Sprintf(redisKeyPrefix+"online_%d_%d",su.StoreId,su.UserId)
				

				exists2, _ := redisHelper.Exists(context.Background(),onlineKey).Result()
				tools.Logger.Info(logPrefix+fmt.Sprint("onlineKey=",onlineKey,"-exists2=",exists2 ))

				send :=false //是否发送请求
				if exists2 != 0 { //商家用户在线
					send =true
				}
				tools.Logger.Info(logPrefix+fmt.Sprint("onlineKey=",onlineKey,"-exists2=",exists2 ,"-send=",send))
				
				printVoiceConfig := p.GetAppConfigInfo("merchant_cancel_order_voice_url")
				cancelVoiceUrl := ""
				sendVoiceSocket := false
				
				printVoiceMinVersion :=252
				if printVoiceConfig.ID > 0 && printVoiceConfig.Value != "" && printVoiceConfig.State == 1 {
					printVoiceMinVersion = tools.ToInt(strings.Replace(printVoiceConfig.Version,".","",-1))
					if len(printVoiceConfig.Value) > 0 && printVoiceMinVersion > 0{
						sendVoiceSocket = true
						cancelVoiceUrl = printVoiceConfig.Value
					}
				}

				if len(cancelVoiceUrl) == 0 {
					jobs.SendAdminCancelPush(su)
					continue
				}

				if send  && sendVoiceSocket &&  su.AppVersion >= printVoiceMinVersion { //后台发送到店铺语音 客户端版本大于等于 配置的客户端版本
					
					orderId :=tools.ToInt(order["id"])
					data := make(map[string]interface{})
					data["order_id"]=orderId
					data["user_id"]=su.UserId
					data["store_id"]=su.StoreId
					data["voice_url"]= cancelVoiceUrl
					url :=configs.MyApp.MerchantVoiceUrl+"/custom-info"
					tools.Logger.Info(logPrefix+fmt.Sprint("发送socket ,orderSendKey=",onlineKey,"-exists2=",exists2 ,"-send=",send))
					rtn :=tools.HttpPost(url,data,5*time.Second)
					if len(rtn) == 0 {
						tools.Logger.Error(logPrefix+"voice_socket_发送失败",orderId)
						continue
					}
					tools.Logger.Info(logPrefix+fmt.Sprint("发送socket 消息成功,orderSendKey=",onlineKey,"-exists2=",exists2 ,"-send=",send,",socket地址=",url,",socket回复结果:",rtn))
				}else{
					jobs.SendAdminCancelPush(su)
				}
			}

			tools.Logger.Info(logPrefix+"order_push后台取消订单 要发送激光推送order_id:"+tools.ToString(order["id"]))
		
		}
	}

	}()
}

// createNotification
//
// @Description: 商家取消订单时记录通知信息
// @Author: Rixat
// @Time: 2023-07-27 10:42:43
// @receiver
// @param c *gin.Context
func (wp PaymentService) createNotification(oldOrderState int, order map[string]interface{}) {
	var content_ug string
	var content_zh string
	if tools.ToInt(order["refund_chanel"]) == 1 {
		content_ug = "ئاشخانا بىر تەرەپ قىلمىغان يېڭى زاكاز ئاپتوماتىك قايتۇرۇلدى. زاكاز نۇمۇرى: " + tools.ToString(order["order_id"]) + " زاكاز سوممىسى:" + tools.ToString((tools.ToInt(order["price"]) + tools.ToInt(order["shipment"]) + tools.ToInt(order["lunch_box_fee"])))
		content_zh = "商家拒绝已接订单。订单号：" + tools.ToString(order["order_id"])
	} else {
		content_ug = "يېڭى زاكازنى ئاشخانا قايتۇرىۋەتتى. زاكاز نۇمۇرى:" + tools.ToString(order["order_id"]) + " زاكاز سوممىسى: " + tools.ToString((tools.ToInt(order["price"]) + tools.ToInt(order["shipment"]) + tools.ToInt(order["lunch_box_fee"])))
		content_zh = "商家未处理的新订单。订单号：" + tools.ToString(order["order_id"])
	}

	if oldOrderState == 4 { // 新订单
		err := tools.Db.Table("t_notification").Create(&map[string]interface{}{
			"type":       3,
			"area_id":    order["area_id"],
			"content_ug": content_ug,
			"content_zh": content_zh,
			"link":       "/ug/today-order",
			"state":      0,
		}).Error
		if err != nil {
			tools.Logger.Error("商家未处理通知信息记录错误")
		}
	}
	if oldOrderState == 5 { // 已接订单
		err := tools.Db.Table("t_notification").Create(&map[string]interface{}{
			"type":       4,
			"area_id":    order["area_id"],
			"content_ug": "قوبۇل قىلىنغان زاكازنى ئاشخانا قايتۇرىۋەتتى. زاكاز نۇمۇرى:" + tools.ToString(order["order_id"]) + " زاكاز سوممىسى:" + tools.ToString((tools.ToInt(order["price"]) + tools.ToInt(order["shipment"]) + tools.ToInt(order["lunch_box_fee"]))),
			"content_zh": "商家拒绝已接订单。订单号：" + tools.ToString(order["order_id"]),
			"link":       "/ug/today-order",
			"state":      0,
		}).Error
		if err != nil {
			tools.Logger.Error("已接订单通知信息记录错误")
		}
	}
}

// isAgentPay
//
//	@Description: 是否是代理付款
//	@author: Alimjan
//	@Time: 2023-07-21 17:05:21
//	@param order map[string]interface{}
//	@return bool
func isAgentPay(order map[string]interface{}) bool {
	// if val, ok := order["pay_platform"]; ok {
	// 存在
	// if tools.ToInt(val) == 0 {
	//pay_type
	if val, ok := order["pay_type"]; ok {
		// 存在
		return tools.ToInt(val) == 6
		// }
		// }
	}
	return false
}

// isWechatPay
//
//	@Description: 是否是微信支付
//	@author: Alimjan
//	@Time: 2023-07-21 17:05:25
//	@param order map[string]interface{}
//	@return bool
func isWechatPay(order map[string]interface{}) bool {
	if tools.ToInt(order["pay_type"]) == 1 { // 现金订单
		return false
	}
	if val, ok := order["pay_platform"]; ok {
		// 存在
		if tools.ToInt(val) == 0 {
			//pay_type
			if val, ok := order["pay_type"]; ok {
				// 存在
				return tools.ToInt(val) == 5
			}
		}
	}
	return false
}

// isLakalaPay
//
//	@Description: 是否是拉卡拉支付
//	@author: Alimjan
//	@Time: 2023-07-21 16:46:48
//	@param order map[string]interface{}
//	@return bool
func isLakalaPay(order map[string]interface{}) bool {
	if tools.ToInt(order["pay_type"]) == 1 { // 现金订单
		return false
	}
	if val, ok := order["pay_platform"]; ok {
		// 存在
		if tools.ToInt(val) == 1 {
			//pay_type
			if val, ok := order["pay_type"]; ok {
				// 存在
				return tools.ToInt(val) != 6
			}
		}
	}
	return false
}

// UpdateOrderStateLog
//
// @Description: 更新订单状态并记录状态记录
// @Author: Rixat
// @Time: 2023-02-14 16:15:36
// @receiver
// @param c *gin.Context
func (wp PaymentService) UpdateOrderStateLog(tx *gorm.DB, orderId int, state int, failReason int) error {
	// 3.订单状态记录表  【确认订单】
	err := tx.Table("t_order_state_log").Create(map[string]interface{}{
		"order_id":       orderId,
		"order_state_id": state,
		"state":          1,
		"fail_reason":    failReason,
		"created_at":     carbon.Now("Asia/Shanghai"),
		"updated_at":     carbon.Now("Asia/Shanghai"),
	}).Error
	if err != nil {
		return errors.New("更新订单状态记录失败")
	}

	return nil
}

// GetMerNo
//
// @Description: 获取商户号   type => 1:代理，2:商家
// @Author: Rixat
// @Time: 2023-05-26 15:56:20
// @receiver
// @param c *gin.Context
func (wp PaymentService) GetMerNo(id int, merType int) string {
	ums_mer_no := ""
	archiveModel := tools.Db.Table("t_self_sign_merchant_info_archive").Select("ums_mer_no")
	if merType == 1 {
		archiveModel.Where("area_id", id)
	} else if merType == 2 {
		archiveModel.Where("restaurant_id", id)
	} else {
		// 返回错误
	}
	archiveModel.Where("type", merType).Scan(&ums_mer_no)
	if ums_mer_no == "" {
		// 返回错误
	}
	return ums_mer_no
}

// DeleteOrderStateLog
//
//	@Description: 删除订单状态记录
//	@author: Alimjan
//	@Time: 2023-07-21 16:35:43
//	@receiver p PaymentService
//	@param tx *gorm.DB
//	@param orderId int
//	@return error
func (p PaymentService) DeleteOrderStateLog(tx *gorm.DB, orderId int) error {
	var orderStateLog models.OrderStateLog
	delErr := tx.Where("order_id = ? and order_state_id in ?", orderId, [2]int{7, 10}).Delete(&orderStateLog).Error
	if delErr != nil {
		return errors.New("can_not_delete_state_log")
	}
	return nil
}

// UpdateOrderToday
//
//	@Description: 更新订单状态记录表(今日订单)状态记录表  【确认订单】
//	@author: Alimjan
//	@Time: 2023-07-21 16:37:28
//	@receiver p PaymentService
//	@param tx *gorm.DB
//	@param orderId int
//	@param state int
//	@param refundType int
//	@param refunded int
//	@return interface{}
func (p PaymentService) UpdateOrderToday(tx *gorm.DB, orderId int, order map[string]interface{}, userType int, refunded int) error {
	//
	state := 8
	refundType := 2 // 1　金额返回美滋来餐币，2　金额返回用户付款账户，3表示退回给代理余额
	if tools.ToInt(order["pay_type"]) == 6 {
		refundType = 3
	}
	if userType == 3 { // 餐厅退款
		state = 9
	}
	// 是否自动取消订单
	isAutoRefund := 0
	// 取消订单渠道 （0表示没取消，1 表示实时订单自动被系统取消  2表示从后台取消 3表示商家端取消 4表示 用户端取消）
	refundChanel := 0
	if userType == services.TYPE_USER {
		refundChanel = 4
	} else if userType == services.TYPE_ADMIN_CMS {
		refundChanel = 2
	} else if userType == services.TYPE_AUTO_REJECT {
		refundChanel = 1
		isAutoRefund = 1
	} else if userType == services.TYPE_ADMIN_MERCHANT {
		refundChanel = 3
	}

	// 更新状态
	orderUpdateErr := tx.Model(models.OrderToday{}).Where("id = ?", orderId).Updates(map[string]interface{}{
		"state":          state,
		"refund_type":    refundType, // 1　金额返回美滋来餐币，2　金额返回用户付款账户，3表示退回给代理余额
		"refunded":       refunded,
		"refund_chanel":  refundChanel,
		"is_auto_refund": isAutoRefund,
	}).Error
	if orderUpdateErr != nil {
		tools.Logger.Error("订单状态更新失败,订单号：", order["id"], orderUpdateErr.Error())
		return errors.New("refund_failed")
	}
	//如果使用优惠券的订单的话 优惠券的状态恢复成未使用
	couponLog := tx.Table("t_coupon_log").Where("order_id = ? and state = ?", order["id"], 1)
	ct := int64(0)
	couponLog.Count(&ct)
	if ct > 0 {
		tools.Logger.Info("订单中有优惠券现在要退单 order_id:", order["id"])
		type CouponLogItem struct {
			Id           int `gorm:"column:id"`
			CouponId     int `gorm:"column:coupon_id"`
			UserId       int `gorm:"column:user_id"`
			UserCouponId int `gorm:"column:user_coupon_id"`
		}
		var cpItem CouponLogItem
		couponLog.Scan(&cpItem)
		tools.Logger.Info("订单中有优惠券现在要退单 order_id:", order["id"], " 恢复状态完成开始")
		if cpItem.Id > 0 {
			tx.Table("t_coupon_log").Where("id = ?", cpItem.Id).Updates(&map[string]interface{}{
				"state": 2, //退款
			})
			if cpItem.UserCouponId > 0 {
				tx.Table("t_coupon_user").Where("id = ? ", cpItem.UserCouponId).Updates(&map[string]interface{}{
					"state":    0,   //可以使用
					"use_time": nil, //使用时间清空
				})
			} else {
				tx.Table("t_coupon_user").Where("user_id = ? and coupon_id = ?", cpItem.UserId, cpItem.CouponId).Updates(&map[string]interface{}{
					"state":    0,   //可以使用
					"use_time": nil, //使用时间清空
				})
			}

		}
		tools.Logger.Info("订单中有优惠券现在要退单 order_id:", order["id"], " 恢复状态完成")

	}
	return nil
}

// CancelTakeOrder
//
//	@Description: 取消接单(退款) take order
//	@author: Alimjan
//	@Time: 2023-07-21 16:42:48
//	@receiver p PaymentService
//	@param tx *gorm.DB
//	@param orderId int
//	@param state int
//	@param reasonId int
//	@return error
func (p PaymentService) CancelTakeOrder(tx *gorm.DB, orderId int, state int, reasonId int) error {
	// 处理take_order
	var tkOrder models.TakeOrder
	tx.Where("order_id = ?", orderId).Order("created_at desc").First(&tkOrder)
	if tkOrder.ID > 0 {
		tkErr := tx.Model(&models.TakeOrder{}).Omit(clause.Associations).Where("order_id = ?", orderId).Updates(map[string]interface{}{
			"state":     4,
			"reason_id": reasonId,
		}).Error
		if tkErr != nil {
			tools.Logger.Error("更新takeOrder失败，订单号：", orderId)
			return errors.New("refund_failed")
		}
	}
	return nil
}

// LakalaRefund
//
//	@Description: 拉卡拉退款接口(退款) take order
//	@author: Alimjan
//	@Time: 2023-07-21 16:57:38
//	@receiver p PaymentService
//	@param order map[string]interface{}
//	@return error
func (p PaymentService) LakalaRefund(ip string, order map[string]interface{},fullRefund bool,isPartRefund bool) error {
	lakalaMap := make(map[string]interface{})
	lq :=tools.Db.Table("t_pay_lakala").
		Where("order_id", tools.ToInt(order["id"])).
		Where("object_type = ? ", models.PAY_LAKALA_OBJECT_TYPE_ORDER)
	// if !isPartRefund {
		lq.Where("pay_status", 1004) //支付成功的记录进行退款
	// }
	lq.Scan(&lakalaMap)
	if lakalaMap == nil || lakalaMap!=nil && tools.ToInt(lakalaMap["id"]) == 0 { 
		//没有支付成功的记录
		tools.Logger.Info("lakala-refund-job : 拉卡拉退款失败 没有成功支付的记录， ", order["id"])
		return errors.New("拉卡拉退款失败")
	}
	bts,_:=json.Marshal(lakalaMap)	
	tools.Logger.Info("lakala-refund-original-data : 拉卡拉部分退款[全额退款][原始数据lakala]， ", string(bts))
	

	//部分退款处理
	var partRefunds models.OrderPartRefund
	tools.Db.Model(&models.OrderPartRefund{}).Where("order_id = ? and part_refund_type = ?",order["id"],2).Find(&partRefunds) 	
	
	if fullRefund {	
		refundAmount := tools.ToInt64(lakalaMap["pay_amount"])-partRefunds.PartRefundAmount
		tools.Logger.Info("lakala-refund-job-part-refund : 拉卡拉部分退款[全额退款]， ", lakalaMap["id"],",orderId:",order["id"],",partRefunds.PartRefundAmount:",partRefunds.PartRefundAmount,",refundAmount:",refundAmount)
		lakalaMap["pay_amount"] = refundAmount
	}else{
		tools.Logger.Info("lakala-refund-job-part-refund : 拉卡拉部分退款[部分退款]， ", lakalaMap["id"],",orderId:",order["id"],",partRefunds.PartRefundAmount:",partRefunds.PartRefundAmount)
		lakalaMap["pay_amount"] = partRefunds.PartRefundAmount
	}
	
	lakalaService := lakala.GetLakalaService()
	_, err := lakalaService.RefundLakala(ip, lakalaMap,isPartRefund)
	return err
}

func (p PaymentService) WechatRefund(ip string, order map[string]interface{}, userType int) error {
	terminalId := tools.ToInt(order["terminal_id"])
	if tools.ToInt(order["cash_clear_state"]) == 1 {
		chanelWithTerminal := [4]int{5, 4, 3, 5}
		// $chanelWithTerminal=[
		// 	5,//JSAPI 公众号
		// 	4,//MWEB H5
		// 	3,//APP 配送员客户端
		// 	5  //NATIVE 公众号
		// ];
		terminalId = chanelWithTerminal[tools.ToInt(order["cash_clear_channel"])-1]
	}
	// 退款使用的配置文件
	configName := ""
	if terminalId == 1 || terminalId == 2 {
		configName = "wechat_api"
	}
	if terminalId == 3 {
		configName = "wechat_shipper"
	}
	if terminalId == 4 {
		configName = "wechat_h5"
	}
	if terminalId == 5 {
		configName = "wechat_wap"
	}
	if terminalId == 8 {
		configName = "wechat_mini"
	}

	wechatPayConfig := *configs.NewWechatPayConfig(configName)
	wechatPay := *tools.NewWechatPay(&wechatPayConfig)
	param := make(map[string]interface{})
	wechatLog := make(map[string]interface{})
	tools.Db.Table("wechat").Where("order_id=?", order["id"]).Where("payed=?", 1).Scan(&wechatLog)
	if wechatLog["id"] == nil {
		return errors.New("order_not_found")
	}
	param["transaction_id"] = wechatLog["transaction_id"]
	param["out_trade_no"] = wechatLog["out_trade_no"]
	param["amount"] = wechatLog["total_fee"]
	param["total"] = wechatLog["total_fee"]
	rs, _ := wechatPay.Refund(param)
	if (rs.Code == 200 || rs.Code == 0) && len(rs.Response.Status) > 0 {
		wechatRefund := models.WechatRefund{
			OrderID:       tools.ToInt(order["id"]),
			TransactionID: rs.Response.TransactionId,
			OutTradeNo:    rs.Response.OutTradeNo,
			RefundFrom:    userType,
			RefundID:      rs.Response.RefundId,
			OutRefundNo:   rs.Response.OutRefundNo,
			TotalFee:      rs.Response.Amount.Total,
			RefundFee:     rs.Response.Amount.Refund,
			RefundDesc:    "退款",
			Refunded:      1,
			CreatedAt:     carbon.Now().Carbon2Time(),
			UpdatedAt:     carbon.Now().Carbon2Time(),
		}
		// 退款日志
		tools.Db.Table("wechat_refund").Create(&wechatRefund)
		tools.Logger.Info("微信退款完成！")
	} else {
		tools.Logger.Error("微信退款失败参数", rs, order, wechatLog)
	}
	return nil
}

// AgentRefund
//
//	@Description: 代理退款接口(退款)
//	@author: Alimjan
//	@Time: 2023-07-21 16:58:30
//	@receiver p PaymentService
//	@param order map[string]interface{}
//	@return error
func (p PaymentService) AgentRefund(orderId int) error {


	db := tools.Db

	order := models.OrderToday{}
	err := db.Model(&models.OrderToday{}).Where("id = ?", orderId).
		First(&order).Error
	if err != nil {
		tools.Logger.Error("查询订单时出现错误:", err.Error())
		return err
	}
	//TODO 只有扣款的才退款
		err = db.Model(&order).Select("state", "refund_type", "refunded").
			Updates(models.OrderToday{
				State:      8,
				RefundType: 3,
				Refunded:   1,
			}).Error
		if err != nil {
			tools.Logger.Error("更新订单状态时出现错误:", err.Error())
			return err
		}
	return nil
}

// SendShipperSocketEvent
//
//	@Description: 发送骑手socket事件(退款)
//	@author: Alimjan
//	@Time: 2023-07-21 16:59:48
//	@receiver p PaymentService
//	@param order map[string]interface{}
//	@return error
func (p PaymentService) SendShipperSocketEvent(order map[string]interface{}, userType int) error {
	// @param userType string 用户类型 1，配送员 2.商家
	// @param userId string 用户id
	// @param msgType string  1."管理员分配" 2."管理员取消订单了" 3."餐厅取消订单了" 4."客户取消订单了" 5."餐厅准备好美食了" 6."餐厅准备好美食了"

	//配送端 socket 声音分类
	//1 有新的订单要抢 
	//2 管理员分配订单 
	//3 管理员取消订单 
	//4 店铺取消订单 
	//5 客户取消订单 
	//6 聊天室 
	//7 餐厅准备美食 
	//8 客户催单
	//9 有会议 要参加
	//10 特殊情况
	//11 管理员切换配送员
	go func() {
		//tools.Logger.Info("要发送订单通知了",order,", 用户类型",userType)
		if order["shipper_id"] != nil {
			if userType == services.TYPE_ADMIN_CMS { // 代理
				p.SendSocketToShipper(tools.ToString(order["id"]), tools.ToString(order["shipper_id"]), "3")
			}
			if userType == services.TYPE_ADMIN_MERCHANT || userType == services.TYPE_AUTO_REJECT { // 餐厅
				p.SendSocketToShipper(tools.ToString(order["id"]), tools.ToString(order["shipper_id"]), "4")
			}
			if userType == services.TYPE_USER { // 客户
				p.SendSocketToShipper(tools.ToString(order["id"]),tools.ToString(order["shipper"]),"5")
			}

		}

	}()
	return nil
}

func (p PaymentService) SendWechatOrderMessage(userType int, orderId int, reasonId int, order map[string]interface{}) error {
	go func() {
		job := jobs.NewSendWechatMiniMessageJob()
		job.ProduceMessageToConsumer(map[string]interface{}{
			"order_id": orderId,
			"type":     3,
		})
	}()
	return nil
}




/***
 * @Author: [rozimamat]
 * @description: 取消秒杀订单时，秒杀库存增加，代理余额退回
 * @Date: 2023-07-22 18:39:07
 * @param {int} orderId
 */
func (p PaymentService) OrderRefundStockAndAgentFee(ip string, orderId int) error {
	db := tools.GetDB()
	var orderMap map[string]interface{}
	db.Table("t_order_today").Where("id=?", orderId).Select("id,area_id,city_id").Scan(&orderMap)
	if orderMap["id"] == nil {
		return errors.New("order_not_found")
	}

	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tools.Logger.Error("秒杀库存增加失败:", r)
			tx.Rollback()
		}
	}()
	fields := "b_seckill_log.state as seckill_state,"
	fields += "b_seckill_log.id as id,"
	fields += "b_seckill_log.seckill_id as seckill_id,"
	fields += "b_seckill_log.saled_count as saled_count,"
	fields += "b_seckill_log.seckill_platform_service_fee as seckill_platform_service_fee"

	var details []map[string]interface{}
	tx.Table("t_order_detail").Joins("left join b_seckill_log on b_seckill_log.id=t_order_detail.seckill_id").
		Select(fields).Where("t_order_detail.order_id =? and t_order_detail.seckill_id is not null and t_order_detail.deleted_at is null", orderId).Scan(&details)
	for _, v := range details {

		if tools.ToInt(v["seckill_state"]) != 1 {
			break
		}
		tools.Logger.Info("开始秒杀订单取消增库orderId:", orderMap["id"])
		er3 := tx.Table("b_seckill_log").Where("id=?", v["id"]).Update("state", 2).Error
		if er3 != nil {
			tools.Logger.Error("b_seckill_log 更新失败:", er3.Error())
			tx.Rollback()
			return errors.New("system_error")
		}

		er4 := p.UpdateSeckillSaledCount(tx, tools.ToString(v["seckill_id"]), tools.ToInt(v["saled_count"]), 2)
		if er4 != nil {
			tools.Logger.Error("b_seckill_log 更新失败:", er4.Error())
			tx.Rollback()
			return errors.New("system_error")
		}

	}

	er4 := tx.Table("t_order_today").Where("id", orderMap["id"]).Update("seckill_state", 2).Error
	if er4 != nil {
		tools.Logger.Error("t_order_detail 更新失败:", er4.Error())
		tx.Rollback()
		return errors.New("system_error")
	}
	tx.Commit()
	tools.Logger.Info("取消秒杀订单库存增加完成！", orderId)
	return nil
}

/***
 * @Author: [rozimamat]
 * @description: 更新秒杀数量
 * @Date: 2023-07-22 19:34:35
 * @param {*gin.Context} c
 * @param {*gorm.DB} tx
 * @param {string} seckill_id
 * @param {int} saled_count
 * @param {int} tp
 */
func (p PaymentService) UpdateSeckillSaledCount(tx *gorm.DB, seckill_id string, saled_count int, tp int) error {

	seck_kill_key_saled := "lumen_database_skill_stock_id_" + seckill_id
	seckillMap := make(map[string]interface{})
	tx.Table("b_seckill").Where("id=?", seckill_id).Scan(&seckillMap)
	redisHelper := tools.GetRedisHelper()
	if exists, _ := redisHelper.Exists(context.Background(), seck_kill_key_saled).Result(); exists == 0 {
		bc, _ := redisHelper.SetNX(context.Background(), seck_kill_key_saled+"key", 10, 30*time.Second).Result()
		if bc {
			tx.Table("b_seckill").Where("id=?", seckill_id).Scan(&seckillMap)
			vv := tools.ToInt(seckillMap["total_count"]) - tools.ToInt(seckillMap["saled_count"])
			redisHelper.Set(context.Background(), seck_kill_key_saled, vv, 30*time.Second)
		}
	}
	luaScript := `
		local oldStock = tonumber(redis.call('get', KEYS[1]))
		if (oldStock==false)  then
			return 0
		end
		local newStock = oldStock+tonumber(KEYS[2])
		redis.call('set', KEYS[1] ,newStock)
		redis.call('EXPIRE', KEYS[1],30)
		return newStock
	`

	leftStock, err := redisHelper.Eval(redisHelper.Context(), luaScript, []string{seck_kill_key_saled, tools.ToString(saled_count)}).Int64()
	if err != nil {
		tools.Logger.Error("UpdateSeckillSaledCount error:redis lua error:", err.Error())
		return errors.New("order.seckill_count_out_remaining")
	}
	if leftStock < 0 || leftStock == 99999 {
		return errors.New("seckill_count_out_remaining") //
	}
	totalCount := tools.ToInt64(seckillMap["total_count"])

	saledCount := totalCount - leftStock
	if saledCount > totalCount {
		return errors.New("order.seckill_count_out_remaining")
	}
	er2 := tx.Table("b_seckill").Where("id=?", seckill_id).Update("saled_count", saledCount).Error

	if er2 != nil {
		return errors.New("update_seckill_seled_cound_fail")
	}

	return nil
}
func (p PaymentService) CheckDupilicatedPayedOrder(context *gin.Context) {
	db := tools.Db
	lakalaService := lakala.GetLakalaService()
	startTime := carbon.Now("Asia/Shanghai").SubMinutes(30).ToDateTimeString()
	endTime := carbon.Now("Asia/Shanghai").SubMinutes(5).ToDateTimeString()
	//SELECT order_id,count(1) AS co FROM t_pay_lakala WHERE order_status=1006 AND created_at> "" GROUP BY order_id HAVING co> 1;
	type PayLakala struct {
		OrderId int `gorm:"column:order_id"`
		Count   int `gorm:"column:co"`
	}
	payLakalas := []PayLakala{}
	db.Table("t_pay_lakala").
		Select("order_id,count(1) AS co").Where("order_status=1006 AND created_at between ? AND ?", startTime, endTime).
		Where("object_type = ? ", models.PAY_LAKALA_OBJECT_TYPE_ORDER).
		Group("order_id").
		Having("co> 1").
		Scan(&payLakalas)
	if len(payLakalas) > 0 {
		str := ""
		for _, item := range payLakalas {
			str += "," + fmt.Sprintf("%d", item.OrderId)
		}
		str = strings.Trim(str, ",")
		lakalaService.SendFailMessage(context, 0, 0, "有拉卡拉重复支付订单"+str, 1009)
	}

	db.Table("wechat").
		Select("order_id,count(1) AS co").Where("payed=1 AND created_at between ? AND ?", startTime, endTime).
		Group("order_id").
		Having("co > 1").
		Scan(&payLakalas)

	if len(payLakalas) > 0 {
		str := ""
		for _, item := range payLakalas {
			str += "," + fmt.Sprintf("%d", item.OrderId)
		}
		str = strings.Trim(str, ",")
		lakalaService.SendFailMessage(context, 0, 0, "有微信重复支付订单:"+str, 1010)
	}
}
func (p PaymentService) CheckOrderPayStateHourly(context *gin.Context) {

	lakalaService := lakala.GetLakalaService()
	startTime := carbon.Now("Asia/Shanghai").SubMinutes(30).ToDateTimeString()
	endTime := carbon.Now("Asia/Shanghai").SubMinutes(5).ToDateTimeString()
	type PayLakala struct {
		ID         int    `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
		OrderId    int    `gorm:"column:order_id"`
		OrderNo    string `gorm:"column:order_no;NOT NULL"`     // 订单唯一编号
		OutOrderNo string `gorm:"column:out_order_no;NOT NULL"` // 订单唯一编号
	}
	type Order struct {
		ID    int `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
		State int `gorm:"column:state"`
	}
	db := tools.Db
	payLakala := []PayLakala{}
	db.Table("t_pay_lakala").
		Where("created_at BETWEEN ? AND ?", startTime, endTime).
		Where("object_type = ? ", models.PAY_LAKALA_OBJECT_TYPE_ORDER).
		Scan(&payLakala)
	isError := false
	errorItems :=[]error{}
	for _, v := range payLakala {
		var order Order
		db.Table("t_order_today").Where("id = ?", v.OrderId).Scan(&order)
		if order.ID == 0 {
			tools.Logger.Error("无法找到订单", v.OrderId)
			isError = true
			errorItems = append(errorItems,fmt.Errorf("无法找到订单%d",v.OrderId))
		} else {
			spRes, err := lakalaService.OrderCheck(context, v.OrderNo)
			if err != nil {
				tools.Logger.Error("订单查询错误", v.OrderId)
				isError = true
				errorItems = append(errorItems,fmt.Errorf("订单查询错误%d",v.OrderId))
			} else {
				var spResult LakalaEntity.LakalaResult
				json.Unmarshal([]byte(spRes), &spResult)

				var spResponse LakalaEntity.WithDrawResultResponse
				json.Unmarshal([]byte(spResult.Response), &spResponse)

				if spResponse.Result.OrderStatus == lakala.OrderStatusCompleted {
					db.Table("t_order_today").Where("id = ?", v.OrderId).Scan(&order)
					if order.State < 3 || (order.State > 7 && order.State < 10) {
						tools.Logger.Error("订单状态错误", v.OrderId)
						isError = true
						errorItems = append(errorItems,fmt.Errorf("订单状态错误%d",v.OrderId))
					}
				} else if spResponse.Result.OrderStatus == lakala.OrderStatusRefunded {
					db.Table("t_order_today").Where("id = ?", v.OrderId).Scan(&order)
					if order.State != 8 && order.State != 9 {
						otherSuccesedPayed := PayLakala{}
						db.Table("t_pay_lakala").
							Where("object_type = ? ", models.PAY_LAKALA_OBJECT_TYPE_ORDER).
							Where("order_id = ? ",order.ID).
							Where("id not in (?)",v.ID).
							Scan(&otherSuccesedPayed)
						if otherSuccesedPayed.ID==0 {
							tools.Logger.Error("订单状态错误", v.OrderId)
							isError = true
							errorItems = append(errorItems,fmt.Errorf("订单状态错误%d",v.OrderId))
						}
					}
				}
			}
		}
		time.Sleep(time.Millisecond * 100)
	}
	tools.Logger.Error("检查订单状态结果成功")
	if isError && len(errorItems) > 1{
		// 错误数据超过2个再输出这个日志
		if(len(errorItems) > 1){
			tools.Logger.Error("FATAL订单状态不同步",errorItems)
		}else{
			tools.Logger.Error("订单状态不同步",errorItems)
		}
	}
}

/***
 * @Author: [rozimamat]
 * @description: 检查商户余额
 * @Date: 2023-08-12 20:38:14
 * @param {*gin.Context} c
 */
func (p PaymentService) CheckResCash(c *gin.Context) {

	// la := lakala.GetLakalaService()

	db := tools.Db

	type Members struct {
		Id           int    `gorm:"id"`
		MemberNo     string `gorm:"member_no"`
		RestaurantId int    `gorm:"restaurant_id"`
	}

	var mm []Members

	db.Table("t_self_sign_merchant_info_archive").Where("lakala_verify_state = 2 ").Select("id,restaurant_id,member_no").Scan(&mm)

	isError := false
	errorItems := []error{}
	//  定义channel
	ch := make(chan int, 4)
	waitGroup := sync.WaitGroup{}
	for _, v := range mm {
		ch <- 1
		go func() {
			waitGroup.Add(1)
			err := p.CheckOneResCash(&ch, c, v)
			if err != nil {
				errorItems = append(errorItems, err)
				isError = true
			}
			waitGroup.Done()
		}()
	}
	waitGroup.Wait()
	tools.Logger.Error("商户余额查询成功")
	if isError {
		tools.Logger.Error("FATAL商户余额错误",errorItems)
	}
	// if isError {
	// 	la.SendFailMessage(c, 0, 0, "商户余额账户余额余额错误", 1009)
	// 	tools.AliDeveloperDingdingMsg("商户余额账户余额余额错误:1009")
	// }

}

func (p PaymentService) CheckOneResCash(c2 *chan int, c *gin.Context, v struct {
	Id           int    `gorm:"id"`
	MemberNo     string `gorm:"member_no"`
	RestaurantId int    `gorm:"restaurant_id"`
}) error {
	la := lakala.GetLakalaService()
	//tools.Logger.Info("商户余额账户余额错误:商户id:", v.RestaurantId)
	//检查佣金余额
	res, er1 := la.AccountBalanceQuery(c, v.MemberNo, "")
	if er1 != nil {
		tools.Logger.Error("佣金账户余额查询失败", er1)
		<-*c2
		return errors.New(fmt.Sprintf("佣金账户余额查询失败:%d :%s", v.RestaurantId, er1.Error()))
	}

	var mq LakalaEntity.LakalaResult
	json.Unmarshal([]byte(res), &mq)

	var balanceResult LakalaEntity.BalanceResultResponse
	json.Unmarshal([]byte(mq.Response), &balanceResult)
	//提现金额大于 佣金账户里面的金额时 要发送短信提醒
	if balanceResult.Result.AvailableAmount > 0 {

		tools.Logger.Error("商户余额账户余额余额错误:商户id:", v.RestaurantId)
		tools.Logger.Error("商户余额账户余额余额 可用余额:" + tools.ToPrice(tools.ToFloat64(balanceResult.Result.AvailableAmount)/100) + "元")
		<-*c2
		return errors.New(fmt.Sprintf("商户余额账户余额余额 商户id:%d 可用余额:%s", v.RestaurantId, tools.ToPrice(tools.ToFloat64(balanceResult.Result.AvailableAmount)/100)))
	}
	<-*c2
	return nil
}


//全额退款时 有过部分退款的 记录进行 数据恢复 
func (p PaymentService) partRefundRestoreData(db *gorm.DB,orderId int,userId int,userType int,reasonId int) (bool,error){
	//1.恢复 t_order_today 
	var partRefundLog models.OrderPartRefund
	db.Model(&models.OrderPartRefund{}).Where("order_id = ? ",orderId).Find(&partRefundLog)
	var order models.OrderToday
	db.Model(&models.OrderToday{}).Where("id = ?",orderId).Find(&order)
	var orderDetails []models.OrderDetail
	
	db.Model(&models.OrderDetail{}).Where("order_id = ? ",orderId).Find(&orderDetails)
	
	
	restore :=false	
	if partRefundLog.ID > 0 {//数据存在 要恢复数据
		db.Model(&models.OrderPartRefund{}).Where("order_id = ?",orderId).Updates(&map[string]interface{}{
			"last_refund":1,
		})
		restore =true
	}

	refundParams :=resources.PartRefund{
		OrderId:order.ID,
		UserId:userId,
		UserType: userType,
		ReasonId:reasonId,
		RefundType:1,
		RefundAmount:int(order.ActualPaid),
	}
	//备份数据   无论第一次或是第二次都要备份数据 
	partRefundOld,_,err:=p.partRefundCopyData(
				db,
				order,
				orderDetails,
				refundParams,
				1,
				1,
				int(partRefundLog.PartRefundAmount),
			)
	if err != nil {
		tools.Logger.Error("数据恢复失败",err)
		return false,err
	}		
	if restore { //数据恢复 
		db.Model(&models.OrderToday{}).Where("id = ?",order.ID).Updates(&map[string]interface{}{
			"res_profit":partRefundLog.ResProfit,
			"dealer_profit":partRefundLog.DealerProfit,
			"mp_profit":partRefundLog.MpProfit,
			"actual_paid":partRefundLog.ActualPaid,
			"order_price":partRefundLog.OrderPrice,
			"order_price_res":partRefundLog.OrderPriceRes,
			"lunch_box_fee":partRefundLog.LunchBoxFee,
			"original_price":partRefundLog.OriginalPrice,
		})

		//删除现有的detail数据
		db.Unscoped().Delete(&models.OrderDetail{},"order_id = ?",orderId)
		
		var orderDetails []models.OrderDetail

		var orderRefundDetails []models.OrderPartRefundDetail
		db.Model(&models.OrderPartRefundDetail{}).Where("part_refund_id = ? and type = ?",partRefundLog.ID,models.PartRefundDetailTypeOriginal).Find(&orderRefundDetails)

		var orderRefundDetailsNew []models.OrderPartRefundDetail

		for _, v := range orderRefundDetails {
			detail :=models.OrderDetail{				
				OrderID          :int(v.OrderId)          ,
				ActivityType     :int(v.ActivityType)     ,
				ActivityId       :int(v.ActivityId)       ,
				StoreFoodsID     :int(v.StoreFoodsId)     ,
				OriginalPrice    :uint(v.OriginalPrice)    ,
				Price            :uint(v.Price)            ,
				MpPercent        :float64(v.MpPercent)        ,
				DiscountPercent  :uint(v.DiscountPercent)  ,
				DealerPercent    :float64(v.DealerPercent)    ,
				MpProfit         :uint(v.MpProfit)         ,
				DealerProfit     :int(v.DealerProfit)     ,
				ResProfit        :int(v.ResProfit)        ,
				Number           :uint(v.Number)           ,
				LunchBoxID       :int(v.LunchBoxId)       ,
				LunchBoxFee      :int(v.LunchBoxFee)      ,
				LunchBoxCount    :int(v.LunchBoxCount)    ,
				SeckillID        :int(v.SeckillId)        ,
				PrefId           :int(v.PrefId)           ,
				PriceMarkupId    :int(v.PriceMarkupId)    ,
				PriceMarkupPrice :int(v.PriceMarkupPrice) ,
				LunchBoxFoodCount: int(v.LunchBoxFoodCount),
				RefundPrice: int(v.RefundPrice),
				LunchBoxRefundPrice: v.LunchBoxRefundPrice,
				FoodType: v.FoodType,
				SpecID: v.SpecID,
				LunchBoxGroupIndex: v.LunchBoxGroupIndex,
			}
			orderDetails = append(orderDetails,detail)
			v.ID = 0
			v.PartRefundId = partRefundOld.ID
			v.CreatedAt = carbon.Now(configs.AsiaShanghai).Carbon2Time()
			v.UpdatedAt = carbon.Now(configs.AsiaShanghai).Carbon2Time()
			orderRefundDetailsNew = append(orderRefundDetailsNew,v)
		}
		if len (orderDetails) > 0 {
			err :=db.Create(&orderDetails).Error
			if err != nil {
				tools.Logger.Error("数据恢复失败",err)
				return false,err
			}
		}
		if len (orderRefundDetailsNew) > 0 {
			db.Model(&models.OrderPartRefundDetail{}).Where("part_refund_id = ?",partRefundOld.ID).Unscoped().Delete(&models.OrderPartRefundDetail{})
			err :=db.Create(&orderRefundDetailsNew).Error
			if err != nil {
				tools.Logger.Error("数据恢复失败",err)
				return false,err
			}
		}
		
	}	
	return restore,nil	
}



//部分退款
func (p PaymentService) PostPartRefund(ip string,refundParams resources.PartRefund) (error) {
	//1. 查询是否退款过
	db :=tools.GetDB()
	//监测是否可以退款 
	
	tools.Logger.Info("开始部分退款orderId", refundParams.OrderId,",退款类型",refundParams.RefundType)
	//2.备份 和 更新 数据 
	tx :=db.Begin()	
	defer func() {
		if r := recover(); r != nil {
			tools.Logger.Error("FATAL PartRefundOrder-Error", r)
			tx.Rollback()
		}
	}()
	var order models.OrderToday
	tx.Model(&models.OrderToday{}).Where("id = ? and pay_time is not null",refundParams.OrderId).Find(&order)
	
	var details []models.OrderDetail
	tx.Model(&models.OrderDetail{}).Preload("RestaurantFoods").Where("order_id = ?",refundParams.OrderId).Find(&details)

	_,err:=p.partRefundLog(tx,order,details,refundParams)
	
	if err != nil {
		tx.Rollback()
		return err
	}
	// if true {
	// 	tx.Rollback()
	// }else{
		tx.Commit()
	// }
	
	orderBytes,_:=json.Marshal(order)
	orderMap :=make(map[string]interface{})
	json.Unmarshal(orderBytes,&orderMap)
	//4. 发起退款 
	// 如果是拉卡拉订单，则通过拉卡拉退款
	tools.Logger.Info("开始部分退款orderId", refundParams.OrderId,",退款类型",refundParams.RefundType,",支付类型",tools.ToString(orderMap["pay_type"]))
	if isLakalaPay(orderMap) {
		tools.Logger.Info("开始部分退款orderId", refundParams.OrderId,",退款类型",refundParams.RefundType,",支付类型",tools.ToString(orderMap["pay_type"]),",开始退款队列")
		if err := p.LakalaRefund(ip, orderMap,false,true); err != nil {
			tools.Logger.Error(err)
			job := jobs.NewLakalaRefundJob()
			job.ProduceMessageToConsumer(map[string]interface{}{
				"order_id": orderMap["id"],
				"part_refund":1,
			})
			return err
		}
	} 
	//5 记录结果

	return nil
}

//部分退款监测
func (p PaymentService) PartRefundCheck(
		refundParams resources.PartRefund,
		) (int64, error) {
	db :=tools.GetDB()
	var order models.OrderToday
	db.Model(&models.OrderToday{}).Where("id = ? and pay_time is not null",refundParams.OrderId).Find(&order)
	
	var details []models.OrderDetail
	db.Model(&models.OrderDetail{}).Where("order_id = ?",refundParams.OrderId).Find(&details)
		
	
	if order.ID == 0{
		return 0,errors.New("order_not_found")
	}
	if order.Refunded == 1{
		return 0,errors.New("order_refunded")
	}
	// if refundParams.UserType == 1 && order.StoreID != refundParams.StoreId { //1:商家 2:后台
	// 	return 0,errors.New("order_not_found") //不能退 不属于自己的店铺的订单 
	// }
	//1. 验证数据（饭盒费,美食）
	foodDetails :=refundParams.FoodDetails
	lunchBoxDetails :=refundParams.LunchBoxDetails
	refundAmount :=int64(0)
	allFoodCount :=0
	allREfundFoodCount :=0
	for _, v := range details {
		allFoodCount+=int(v.Number)
	   for _, vv := range foodDetails {
			if vv.FoodId == v.StoreFoodsID && vv.DetailId == v.ID && vv.Count > 0 {
				if uint(vv.Count) > v.Number {//数量不能超过以前的数量 
					return 0,errors.New("part_refund_food_count_error")
				}
				allREfundFoodCount+=vv.Count
				refundAmount += int64(v.RefundPrice * vv.Count)
			}
	   }
	   for _, vv := range lunchBoxDetails {
			if vv.LunchBoxId == v.LunchBoxID && vv.DetailId == v.ID && vv.Count > 0 {
				if uint(vv.Count) > uint(v.LunchBoxCount) {//数量不能超过以前的数量 
					return 0,errors.New("part_refund_food_count_error")
				}
				refundAmount += int64(v.LunchBoxRefundPrice * vv.Count)
			}
   		}		
	}
	if refundAmount != int64(refundParams.RefundAmount) {
		return 0,errors.New("part_refund_amount_error")
	}
	if allFoodCount == allREfundFoodCount { // 如果全部退款，则不能部分退款
		return 0,errors.New("part_refund_food_count_error")
	}
	var partRefundCount int64
	db.Model(&models.OrderPartRefund{}).Where("order_id = ?",refundParams.OrderId).Count(&partRefundCount)
	return partRefundCount,nil

}

//备份部分退款数据
func (p PaymentService) partRefundLog(
		db *gorm.DB,
		order models.OrderToday,
		details []models.OrderDetail,
		refundParams resources.PartRefund,
		) ([]models.OrderPartRefund,error) {
	
	var partRefund []models.OrderPartRefund
	db.Model(&models.OrderPartRefund{}).Preload("OrderExtend").Where("order_id = ?",refundParams.OrderId).Find(&partRefund)
	
	partRefundType :=refundParams.RefundType//退款类型 1:全额 2:部分退款
	
	//1.备份数据
	orderRefund,_,err:=p.partRefundCopyData(
		db,
		order,
		details,
		refundParams,
		partRefundType,
		0,
		refundParams.RefundAmount,
	)
	if err != nil {
		return nil,err
	}
	//2.更新原来的 detail ,order_today  计算利润 减去
	//3.写入
	//原始记录 
	var refundDetails []models.OrderPartRefundDetail
	
	foodDetails :=refundParams.FoodDetails
	
	lunchBoxDetails :=refundParams.LunchBoxDetails

	foodRefundAmount :=int64(0)
	lunchBoxRefundAmount :=int64(0)
	
	rtnResProfitAll :=float64(0)
	rtnDealerProfitAll :=float64(0)
	rtnMpProfitAll :=float64(0)
	rtnPriceMarkUpPriceAll :=int64(0) //加价美食退款时 退掉的加价美食金额

	
	rtnOriginalPrice :=int64(0)
	rtnPrice :=int64(0)
	rtnPriceMarkUpPrice :=int64(0) //加价美食退款时 退掉的加价美食金额

	var detailsToDelete []int
	updateMap :=make([]map[string]interface{},0)
	// lunchBoxMoveMap :=make([]map[string]interface{},0)

	partRefundRatio :=float64(1)
	if order.OrderExtend !=nil {
		partRefundRatio =order.OrderExtend.PartRefundRatio
	}
	
	//原来的数量[美食]
	foodCountArr := make(map[int]int64)
	//原来的数量[餐盒]
	lunchBoxCountArr := make(map[int]int64)

	for _, v := range details {

		foodCountArr[v.StoreFoodsID] +=int64(v.Number)
		lunchBoxCountArr[v.StoreFoodsID] +=int64(v.LunchBoxCount)

		for kk, lb := range lunchBoxDetails {
			if lb.DetailId == v.ID {
				lunchBoxDetails[kk].FoodId = v.StoreFoodsID
				lunchBoxDetails[kk].LunchBoxAccommodate = v.RestaurantFoods.LunchBoxAccommodate //每个餐盒能放多少个美食
				lunchBoxDetails[kk].LunchBoxOriginalCount = v.LunchBoxCount
	
			}
		}
	}
	//退还的数量[美食]
	foodReturnCountArr := make(map[int]int64)
	//退还的数量[餐盒]
	lunchBoxReturnCountArr := make(map[int]int64)
	for _, vv := range foodDetails {
		foodReturnCountArr[vv.FoodId] +=int64(vv.Count)
	}
	for _, vvv := range lunchBoxDetails {
		lunchBoxReturnCountArr[vvv.LunchBoxGroupIndex] +=int64(vvv.Count)
	}

	// lunchBoxCountMap := make(map[int]int64)

	//部分退款餐盒退款规则 
	//1. 由于 餐盒的费用等 集中在同一个美食的第一行数据 
	// 进行部分退款时 要分几种情况处理 该数据  
	//1.1 找出需要退还的饭盒 找出绑定的 数据 ,从餐盒绑定的数据行减去 指定的餐盒 ，如果餐盒绑定的行也进行退掉的话该行的数据不做删除操作 
	detailReturnBoxMap :=make(map[int]int64)
	detailReturnNumberMap :=make(map[int]int64)
	for _, v := range details {
		if v.LunchBoxCount > 0 && lunchBoxReturnCountArr[v.LunchBoxGroupIndex] >0{
			detailReturnBoxMap[v.ID]=lunchBoxReturnCountArr[v.LunchBoxGroupIndex] //从id为 x 的 存在餐盒的数据行减去 数量为y 的餐盒的数量
		}
		for _, vv := range foodDetails {
			if vv.DetailId == v.ID && vv.Count > 0{ //同一个记录中减少 
				detailReturnNumberMap[v.ID]=int64(vv.Count)
			}
		}
	} 

	for _, v := range details {
		if detailReturnNumberMap[v.ID] == 0 && detailReturnBoxMap[v.ID] == 0 { //该行数据没有被退掉 则跳过
			continue
		}
		returnNumber :=int64(0)
		returnLunchBoxCount :=int64(0)
		if v.LunchBoxCount > 0 && detailReturnNumberMap[v.ID] > 0 && detailReturnBoxMap[v.ID] > 0{ //第一行数据也被退掉
			returnNumber = detailReturnNumberMap[v.ID]
			returnLunchBoxCount = detailReturnBoxMap[v.ID]
		}else if v.LunchBoxCount > 0 && detailReturnNumberMap[v.ID] == 0 && detailReturnBoxMap[v.ID] > 0{ //第一行没有被删除但是有餐盒退掉
			returnLunchBoxCount = detailReturnBoxMap[v.ID]
		}else if v.LunchBoxCount == 0 && detailReturnNumberMap[v.ID] > 0{ //该行数据中没有餐盒但是有退掉的美食数量
			returnNumber = detailReturnNumberMap[v.ID]
		}else{
			returnNumber = detailReturnNumberMap[v.ID]
			returnLunchBoxCount = detailReturnBoxMap[v.ID]
		}
		refundDetailOne :=models.OrderPartRefundDetail{
			Type: 2,//部分退款的数据项 
			OrderDetailId:int64(v.ID),
			PartRefundId     :orderRefund.ID,
			OrderId          :int64(v.OrderID)          ,
			ActivityType     :int64(v.ActivityType)     ,
			ActivityId       :int64(v.ActivityId)       ,
			StoreFoodsId     :int64(v.StoreFoodsID)     ,
			FoodType: v.FoodType,
			SpecID:   v.SpecID,
			OriginalPrice    :int64(v.OriginalPrice)    ,
			Price            :int64(v.RefundPrice)       ,
			MpPercent        :float64(v.MpPercent)        ,
			DiscountPercent  :int64(v.DiscountPercent)  ,
			DealerPercent    :float64(v.DealerPercent)    ,
		
			Number           :returnNumber           ,

			SeckillId        :int64(v.SeckillID)        ,
			PrefId           :int64(v.PrefId)           ,
			PriceMarkupId    :int64(v.PriceMarkupId)    ,
			PriceMarkupPrice :int64(v.PriceMarkupPrice) ,

			MpProfit         :int64(v.MpProfit)           ,
			DealerProfit     :int64(v.DealerProfit)       ,
			ResProfit        :int64(v.ResProfit)          ,
			RefundPrice: int64(v.RefundPrice),
		
		}
		rtnOriginalPrice+=int64(int(v.OriginalPrice) * int(returnNumber))
		rtnPrice+=int64(int(v.Price) * int(returnNumber))
		rtnPriceMarkUpPrice+=int64(int(v.PriceMarkupPrice) * int(returnNumber))

		
		lunchBoxId       :=int64(v.LunchBoxID)       
		lunchBoxFee      :=int64(v.LunchBoxRefundPrice)
		lunchBoxCount    :=returnLunchBoxCount
		lunchBoxRefundPrice := int64(v.LunchBoxRefundPrice)
		
		if lunchBoxCount > 0 {		
			refundDetailOne.LunchBoxId       = lunchBoxId 
			refundDetailOne.LunchBoxFee      =lunchBoxFee      
			refundDetailOne.LunchBoxCount    =lunchBoxCount    
			refundDetailOne.LunchBoxFoodCount= int(v.LunchBoxFoodCount) 
			refundDetailOne.LunchBoxRefundPrice = int(lunchBoxRefundPrice)
		}
		foodRefundAmount += int64(returnNumber) * int64(v.RefundPrice)
		lunchBoxRefundAmount += int64(lunchBoxCount) * lunchBoxRefundPrice
		//计算需要退掉的利润
		resProfit,dealerProfit,mpProfit,rtnResProfit,rtnDealerProfit,rtnMpProfit :=p.partRefundProfitFoodCalc(
			int(v.Number),
			int(v.Price),
			int(returnNumber),
			v.PriceMarkupPrice,
			int(returnNumber),
			v.RefundPrice,
			int(lunchBoxCount),
			int(lunchBoxFee),
			float64(v.ResProfit),
			float64(v.DealerProfit),
			float64(v.MpProfit),
			v.MpPercent,
			v.DealerPercent,
			partRefundRatio,
		)

		refundDetailOne.ResProfit = int64(resProfit)
		refundDetailOne.DealerProfit = int64(dealerProfit)
		refundDetailOne.MpProfit = int64(mpProfit)

		refundDetails = append(refundDetails, refundDetailOne)	

		if(v.Number == uint(returnNumber)) && v.LunchBoxCount == 0{
			detailsToDelete = append(detailsToDelete, v.ID)
		}else{
			updateMap = append(updateMap, map[string]interface{}{
				"id": v.ID,
				"number": v.Number - uint(returnNumber),
				"mp_profit": mpProfit,
				"dealer_profit": dealerProfit,
				"res_profit": resProfit,
				"lunch_box_count": v.LunchBoxCount-int(returnLunchBoxCount), //先不要动餐盒数量,后期要处理
			})
		}


		rtnResProfitAll += rtnResProfit
		rtnDealerProfitAll += rtnDealerProfit
		rtnMpProfitAll += rtnMpProfit
		rtnPriceMarkUpPriceAll +=rtnPriceMarkUpPrice
		
	 }
	
	if len(refundDetails) > 0 {
		err :=db.Create(&refundDetails).Error
		if err != nil {
			return nil,err
		}
	}

	newResProfit,newDealerProfit,newMpProfit,newActualPaid,newOrderPrice,newOrderPriceRes,newLunchBoxFee:=p.partRefundProfitAllCalc(
		
		float64(order.ResProfit),
		float64(order.DealerProfit),
		float64(order.MpProfit),
		float64(order.ActualPaid),
		float64(order.OrderPrice),
		float64(order.OrderPriceRes),
		float64(foodRefundAmount),
		float64(lunchBoxRefundAmount),
		rtnResProfitAll,
		rtnDealerProfitAll,
		rtnMpProfitAll,
		float64(order.LunchBoxFee),
		rtnPriceMarkUpPriceAll,
	)
	newPrice :=int(order.Price)-int(rtnPrice)
	newOriginalPrice :=order.OriginalPrice-uint(rtnOriginalPrice)

	err =db.Model(&models.OrderToday{}).Where("id = ?",order.ID).Updates(&map[string]interface{}{
		"res_profit":newResProfit,
		"dealer_profit":newDealerProfit,
		"mp_profit":newMpProfit,
		"actual_paid":newActualPaid,
		"order_price":newOrderPrice,
		"order_price_res":newOrderPriceRes,
		"lunch_box_fee":newLunchBoxFee,
		"price":newPrice,
		"original_price":newOriginalPrice,
	}).Error
	if err != nil {
		return nil,err
	}

	//3.更新t_order_detail 计算利润 减去 //TODO 更新那些字段，删除那些字段
	if len(detailsToDelete) > 0{
		err :=db.Unscoped().Model(&models.OrderDetail{}).Where("id in ?", detailsToDelete).Delete(&models.OrderDetail{}).Error
		if err != nil {
			return nil,err
		}
	}
	for _, v := range updateMap {
		err :=db.Model(&models.OrderDetail{}).Where("id = ?", v["id"]).Updates(v).Error
		if err != nil {
			return nil,err
		}
	}
	
	

	
	return partRefund,nil
}

//部分退款计算公式  单个美食
func (p PaymentService) partRefundProfitFoodCalc(
		count int,
		price int,
		refundCount int,	
		priceMarkupPrice int,
		refundFoodCount int,
		refundFoodPrice int,
		refundLunchBoxCount int,
		refundLunchBoxPrice int,
		resProfit float64,
		dealerProfit float64,
		mpProfit float64,
		mpPercent float64,
		dealerPercent float64,
		partRefundRatio float64,
		)(float64,float64,float64,float64,float64,float64){

		rtnResProfit :=float64(0)
		rtnDealerProfit :=float64(0)
		//平台需要减掉的利润 = (退款数量*退款价格+退款餐盒数量*退款餐盒价格)*退款比例*平台百分比/100
		rtnMpProfit :=float64(refundFoodCount*refundFoodPrice+refundLunchBoxCount*refundLunchBoxPrice)*(mpPercent)/100
		if priceMarkupPrice > 0 { //加价美食的话
			//餐厅需要减掉的利润 = (退款数量*进价价格)+(退款餐盒数量*退款餐盒价格)*退款比例*(100-品台百分比-代理百分比)/100	
			rtnResProfit = float64(refundFoodCount*priceMarkupPrice)+float64(refundLunchBoxCount*refundLunchBoxPrice)*(100-mpPercent-dealerPercent)/100
			//代理需要减掉的利润 =  (退款数量*退款价格)+退款餐盒数量*退款餐盒价格)*退款比例*代理百分比/100	
			rtnDealerProfit =float64(refundFoodCount*refundFoodPrice)*(100-mpPercent)/100+float64(refundLunchBoxCount*refundLunchBoxPrice)*(dealerPercent)/100
			
			
		}else{
			//餐厅需要减掉的利润 = (退款数量*退款价格+退款餐盒数量*退款餐盒价格)*退款比例*(100-品台百分比-代理百分比)/100	
			rtnResProfit = float64(refundFoodCount*refundFoodPrice+refundLunchBoxCount*refundLunchBoxPrice)*(100-mpPercent-dealerPercent)/100
			//代理需要减掉的利润 = (退款数量*退款价格+退款餐盒数量*退款餐盒价格)*退款比例*代理百分比/100	
			rtnDealerProfit =float64(refundFoodCount*refundFoodPrice+refundLunchBoxCount*refundLunchBoxPrice)*(dealerPercent)/100
			//平台需要减掉的利润 = (退款数量*退款价格+退款餐盒数量*退款餐盒价格)*退款比例*平台百分比/100
			
		}
		
		// if count != refundCount { //数量不一样的话减少
		//平台利润 = 原平台利润-退款的代理利润
		mpProfit =mpProfit-rtnMpProfit
		//代理利润 = 原代理利润-退款的代理利润
		dealerProfit =dealerProfit-rtnDealerProfit
		//最终餐厅利润 = 原利润-退款的利润
		resProfit =resProfit-rtnResProfit

		// }
		// else{//一样的话 全部扣掉
		// 	rtnResProfit = resProfit
		// 	rtnDealerProfit = dealerProfit
		// 	rtnMpProfit = mpProfit
		// }

		return resProfit,dealerProfit,mpProfit,rtnResProfit,rtnDealerProfit,rtnMpProfit
}

//部分退款计算公式  单个订单
func (p PaymentService)  partRefundProfitAllCalc(

	resProfit float64,
	dealerProfit float64,
	mpProfit float64,
	actualPaid float64,
	orderPrice float64,
	orderPriceRes float64,

	foodRefundAmount float64,
	lunchBoxRefundAmount float64,
	rtnResProfit float64,
	rtnDealerProfit float64,
	rtnMpProfit float64,
	lunchBoxFee float64,
	rtnPriceMarkUpPriceAll int64,
	) (int64,int64,int64,int64,int64,int64,int64){

	newResProfit :=resProfit-float64(rtnResProfit)
	newDealerProfit :=dealerProfit-float64(rtnDealerProfit)
	newMpProfit :=mpProfit-float64(rtnMpProfit)
	newActualPaid :=actualPaid-foodRefundAmount-lunchBoxRefundAmount
	newOrderPrice :=orderPrice-foodRefundAmount-lunchBoxRefundAmount
	newOrderPriceRes :=orderPriceRes-foodRefundAmount-lunchBoxRefundAmount
	if rtnPriceMarkUpPriceAll > 0 { 
		newOrderPrice =orderPrice-float64(rtnPriceMarkUpPriceAll)-foodRefundAmount-lunchBoxRefundAmount
		newOrderPriceRes =orderPriceRes-float64(rtnPriceMarkUpPriceAll)-lunchBoxRefundAmount
	}
	newLunchBoxFee :=lunchBoxFee-lunchBoxRefundAmount
	return int64(newResProfit),int64(newDealerProfit),int64(newMpProfit),int64(newActualPaid),int64(newOrderPrice),int64(newOrderPriceRes),int64(newLunchBoxFee)

}

//部分退款数据备份 
func (p PaymentService) partRefundCopyData(
		db *gorm.DB,
		order models.OrderToday,
		details []models.OrderDetail,
		refundParams resources.PartRefund,
		partRefundType int,
		lastRefund int,
		partRefundAmount int,
		) (models.OrderPartRefund,[]models.OrderPartRefundDetail,error){
	
	printTime :=carbon.Parse(order.PrintTime,configs.AsiaShanghai).Carbon2Time()
	printedTime :=carbon.Parse(order.PrintedTime,configs.AsiaShanghai).Carbon2Time()

	if partRefundType == 1 {
		var oldRefund models.OrderPartRefund
		db.Model(&models.OrderPartRefund{}).Where("order_id = ? and part_refund_type = ?",order.ID,models.PartRefundTypePart).Find(&oldRefund)
		partRefundAmount = int(order.ActualPaid)
		if oldRefund.ID > 0 {
			partRefundAmount = int(oldRefund.ActualPaid)-int(oldRefund.PartRefundAmount)
		}
	}
	
	orderRefund :=models.OrderPartRefund{	
		RandomId              :order.RandomID         ,
		TerminalId            :int64(order.TerminalID),
		OrderNumber           :order.OrderID          ,
		OrderId               :int64(order.ID)        ,
		CategoryId            :int64(order.CategoryID),
		CityId                :int64(order.CityID)	  ,
		AreaId                :int64(order.AreaID)                ,
		StoreId               :int64(order.StoreID)               ,
		UserId                :int64(order.UserID)                ,
		BuildingId            :int64(order.BuildingID)            ,
		OrderAddress          :order.OrderAddress          ,
		Name                  :order.Name                  ,
		Mobile                :order.Mobile                ,
		ConsumeType           :int64(order.ConsumeType)           ,
		PayType               :int64(order.PayType)               ,
		OriginalPrice         :int64(order.OriginalPrice)         ,
		Price                 :int64(order.Price)                 ,
		Shipment              :int64(order.Shipment)              ,
		OriginalShipment      :int64(order.OriginalShipment)      ,
		LunchBoxFee           :int64(order.LunchBoxFee)           ,
		
		MpProfit              :int64(order.MpProfit)              ,
		DealerProfit          :int64(order.DealerProfit)          ,
		ResProfit             :int64(order.ResProfit)             ,
		
		Cash                  :int64(order.Cash)                  ,
		Coin                  :int64(order.Coin)                  ,
		Consume               :int64(order.Consume)               ,
		Description           :order.Description           ,
		Timezone              :int(order.Timezone)              ,
		BookingTime           :carbon.Parse(order.BookingTime,configs.AsiaShanghai).Carbon2Time() ,
		
		
		Taked                 :int(order.Taked)                 ,
		ShipperId             :int64(order.ShipperID)             ,
		SentSms               :int(order.SentSms)               ,
		DeleteFlag            :int(order.DeleteFlag)            ,
		RefundType            :int(order.RefundType)            ,
		RefundChanel          :int(order.RefundChanel)          ,
		Refunded              :int(order.Refunded)              ,
		
		SendNotify            :int(order.SendNotify)            ,
		SerialNumber          :int64(order.SerialNumber)          ,
		ShipperCompleteGrant  :int(order.ShipperCompleteGrant)  ,
		
		IsAutoRefund          :int(order.IsAutoRefund)          ,
		IsCommented           :int(order.IsCommented)           ,
		Distance              :float64(order.Distance)              ,
		OrderType             :int(order.OrderType)             ,
		Called                :int(order.Called)                ,
		
		PayPlatform           :int(order.PayPlatform)           ,
		State                 :int(order.State)                 ,
		CreatedAt             :order.CreatedAt             ,
		UpdatedAt             :order.UpdatedAt             ,
		
		CashClearState        :int(order.CashClearState)        ,
		
		CashClearChannel      :int(order.CashClearChannel)      ,
		CashClearAdmin        :int64(order.CashClearAdmin)        ,
		SeckillState          :int(order.SeckillState)          ,
		MarketType            :int(order.MarketType)            ,
		
		SelfTakeNumber        :int64(order.SelfTakeNumber)        ,
		DeliveryType          :int(order.DeliveryType)          ,
		ActualPaid            :int64(order.ActualPaid)            ,
		OrderPrice            :int64(order.OrderPrice)            ,
		TotalDiscountAmount   :int64(order.TotalDiscountAmount)   ,
		ShipperReward         :int64(order.ShipperReward)         ,
		
		PriceMarkupInPrice    :int64(order.PriceMarkupInPrice)    ,
		OrderPriceRes         :int64(order.OrderPriceRes)         ,
		PartRefundCreatorId   :int64(refundParams.UserId),
		PartRefundCreatorType :int64(refundParams.UserType) ,
		PartRefundReasonId    :int64(refundParams.ReasonId)    ,
		PartRefundType        :int64(partRefundType)        ,
		PartRefundAmount      :int64(partRefundAmount)      ,
		PartRefundedTime: carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
		LastRefund: lastRefund,
		PartRefundReasonText:tools.FilterEmoji(refundParams.ReasonText),

	}
	if  !tools.IsZeroTime(&order.PayTime) {
		orderRefund.PayTime=&order.PayTime
	}
	if !tools.IsZeroTime(&printTime) {
		orderRefund.PrintTime = &printTime
	}
	if !tools.IsZeroTime(&printedTime) {
		orderRefund.PrintedTime = &printedTime
	}
	if !tools.IsZeroTime(&order.DeliveryTakedTime) {
		orderRefund.DeliveryTakedTime = &order.DeliveryTakedTime
	}
	if !tools.IsZeroTime(&order.DeliveryStartTime) {
		orderRefund.DeliveryStartTime= &order.DeliveryStartTime
	}
	if !tools.IsZeroTime(&order.DeliveryEndTime) {
		orderRefund.DeliveryEndTime = &order.DeliveryEndTime
	}
	if !tools.IsZeroTime(&order.CashClearTime) {
		orderRefund.CashClearTime = &order.CashClearTime
	}
	var refundDetails []models.OrderPartRefundDetail
	err :=db.Create(&orderRefund).Error
	if err != nil {
		tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
		return orderRefund,refundDetails,err
	}
	//2.更新原来的 detail ,order_today  计算利润 减去
	//3.写入
	//原始记录 
	
	
	for _, v := range details {

		refundDetails = append(refundDetails, models.OrderPartRefundDetail{
			Type: 1,
			PartRefundId     :orderRefund.ID,
			OrderDetailId:int64(v.ID),
			OrderId          :int64(v.OrderID)          ,
			ActivityType     :int64(v.ActivityType)     ,
			ActivityId       :int64(v.ActivityId)       ,
			StoreFoodsId     :int64(v.StoreFoodsID)     ,
			FoodType: v.FoodType,
			SpecID:   v.SpecID,
			OriginalPrice    :int64(v.OriginalPrice)    ,
			Price            :int64(v.Price)            ,
			MpPercent        :float64(v.MpPercent)      ,
			DiscountPercent  :int64(v.DiscountPercent)  ,
			DealerPercent    :float64(v.DealerPercent)  ,
			MpProfit         :int64(v.MpProfit)         ,
			DealerProfit     :int64(v.DealerProfit)     ,
			ResProfit        :int64(v.ResProfit)        ,
			Number           :int64(v.Number)           ,
			LunchBoxId       :int64(v.LunchBoxID)       ,
			LunchBoxFee      :int64(v.LunchBoxFee)      ,
			LunchBoxCount    :int64(v.LunchBoxCount)    ,
			SeckillId        :int64(v.SeckillID)        ,
			PrefId           :int64(v.PrefId)           ,
			PriceMarkupId    :int64(v.PriceMarkupId)    ,
			PriceMarkupPrice :int64(v.PriceMarkupPrice) ,
			RefundPrice: int64(v.RefundPrice),
			LunchBoxFoodCount: v.LunchBoxFoodCount,
			LunchBoxRefundPrice: v.LunchBoxRefundPrice,
			LunchBoxGroupIndex: v.LunchBoxGroupIndex,
			
			
		})

	}

	if len(refundDetails) > 0 {
		err :=db.Create(&refundDetails).Error
		if err != nil {
			tools.Logger.Errorf("FATAL ERROR: %s", err.Error())
			return orderRefund,refundDetails,err
		}
	}
	return orderRefund,refundDetails,nil

}


// 更新订单排行中奖信息
func (p PaymentService) UpdateLotteryChanceInfo(order map[string]interface{}) {
	err := tools.Db.Model(&models.LotteryChance{}).Where("type_id = ? and type = 5", order["id"]).Update("state", 7).Error
	if err != nil {
		tools.Logger.Error("更新订单排行中奖信息失败", err)
	}
}


// 获取订单状态
func (l PaymentService)  GetOrderStatus(orderId int) ([]map[string]interface{}, error){
	db :=tools.GetDB()
	var lks []models.PayLakala
	if err := db.Table("t_pay_lakala").
		Select("id", "order_no", "pay_seq_no").
		Where("order_id = ?", orderId).
		Order("id desc").
		Find(&lks).Error; err != nil {
		return nil, err
	}

	if len(lks) == 0 {
		return nil, errors.New("order_not_found")
	}

	data := []map[string]interface{}{}
	key := fmt.Sprintf("lakala_check_%d", orderId)
	redisHelper := tools.GetRedisHelper()
	exists, _ := redisHelper.Exists(context.Background(), key).Result()
	if exists != 0 {
		return nil, errors.New("order_retry_in_10")
	}

	for _, lk := range lks {
		lakalaService := lakala.GetLakalaService()
		arr, err := lakalaService.PayStatusCheck(lk.OrderNo, lk.PaySeqNo)
		if err != nil {
			tools.Logger.Error("拉卡拉订单状态查询错误", arr)
			continue
		}
		arr2, err := lakalaService.OrderStatusCheck(lk.OrderNo)
		if err != nil {
			tools.Logger.Error("拉卡拉订单状态查询错误", arr2)
			continue
		}

		if arr["status"] != "OK" {
			tools.Logger.Error("拉卡拉订单状态查询错误", arr)
			continue
		}

		payRes :=  arr["result"].(map[string]interface{})
		payMethod := payRes["pay_method"].(map[string]interface{})
		delete(payRes, "pay_method")
		rs := payRes

		if jsapi, ok := payMethod["JSAPI"].(map[string]interface{}); ok {
			rs["amount"] = tools.ToFloat64(jsapi["amount"])/ 100
		}

		rs2 := arr2["result"].(map[string]interface{})
		outOrderNo :=tools.ToString(rs["out_order_no"])
		if len(outOrderNo) >0{
			rs["out_trade_no"] = outOrderNo
		}
		payChannelTradeNo :=tools.ToString(rs["pay_channel_trade_no"])
		if len(payChannelTradeNo) > 0 {
			rs["transaction_id"] = payChannelTradeNo
		}
		if jsapi, ok := payMethod["JSAPI"].(map[string]interface{}); ok {
			rs["mch_id"] = tools.ToString(jsapi["mch_appid"])
		}
		if payStatus, ok := rs["pay_status"].(string); ok {
			rs["trade_state_desc"] = models.PAY_LAKALA_PAY_STATES[tools.ToInt(payStatus)]
		}
		rs["trade_type"] = "JSAPI支付或(小程序支付)"
		if payTime, ok := rs["pay_time"].(string); ok {
			rs["time_end"] = payTime
		}
		rs["order"] = lk
		rs["pay_method"] = payMethod
		// rs2OrderStatus := tools.ToString(rs2["order_status"])
		// if tools.ToInt(rs2OrderStatus) == models.PAY_STATUS_REFUND {
			rs["pay_status"] = tools.ToString(rs2["order_status"])
			var lkRefunds []models.PayLakalaRefund
			if err := db.Model(&models.PayLakalaRefund{}).
				Select("id","order_no","refund_seq_no").
				Where("pay_lakala_id = ?", lk.ID).
				Order("created_at desc").
				Find(&lkRefunds).Error; err == nil {
				if len(lkRefunds) == 0 {
					data = append(data, rs)
					continue
				}
				refunds := []map[string]interface{}{}
				for _, lkRefund := range lkRefunds {
					
					orderNo := lkRefund.OrderNo
					refundSeqNo := lkRefund.RefundSeqNo
					refundArr, err := lakalaService.RefundCheck(orderNo, refundSeqNo)
					if err != nil {
						tools.Logger.Error("拉卡拉退款状态查询错误", refundArr)
						data = append(data, rs)
						continue
					}

					
					refundResultMap,_ :=refundArr["result"].(map[string]interface{})
					refundStateStr :=tools.ToString(refundResultMap["refund_status"])
					refundAmount := tools.ToFloat64(refundResultMap["refund_amount"])/100
					refundState :=models.PAY_LAKALA_REFUND_STATES[tools.ToInt(refundStateStr)]
					refunds =append(refunds,  map[string]interface{}{
						"result_code":     "SUCCESS",
						"refund_count":    1,
						"refund_fee":      refundAmount,
						"refund_status_0": refundState,
						"refund_time":     tools.ToString(refundResultMap["refund_time"]),
					})
				}
				rs["refunds"] = refunds
			}
		// }
		data = append(data, rs)
	}
	return data, nil
}

//部分退款失败的情况处理
func (p PaymentService) FixPartRefundFailedItems(ip string) error{
	
	db :=tools.GetDB()
	var orderIds []int
	type FailedItems struct {
		OrderId int `json:"order_id"`
		ActualPaid int `json:"actual_paid"`
		PartRefundAmount int `json:"part_refund_amount"`
		PayAmount int `json:"pay_amount"`
		RefundAmount int `json:"refund_amount"`
	}
	fields :="t_order_part_refund.order_id,"
	fields +="t_order_part_refund.actual_paid,"
	fields +="part_refund_amount,"
	fields +="t_pay_lakala.pay_amount,"
	fields +="( SELECT sum( refund_amount ) FROM t_pay_lakala_refund WHERE t_pay_lakala_refund.pay_lakala_id = t_pay_lakala.id ) AS refund_amount "
	var failedItems []FailedItems
	now :=carbon.Now(configs.AsiaShanghai)
	// startTime :=now.AddMinutes(-30).Format("Y-m-d H:i:s")
	nowTime :=now.Format("Y-m-d H:i:s")
	var refundOrderIds []int
	 //今天的 有退款记录的 order_id
	db.Model(&models.OrderPartRefund{}).Where(" created_at > ? ",now.Format("Y-m-d 00:00:00")).Pluck("order_id",&refundOrderIds)
	 //今天的 有退款记录的 id 支付类型为拉卡拉的 5 的订单
	db.Model(&models.OrderToday{}).Where("pay_type = ? AND created_at > ? and id in (?)",5,now.Format("Y-m-d 00:00:00"),refundOrderIds).Pluck("id",&orderIds)

	//今天的
	db.Model(&models.OrderPartRefund{}).
	Joins("LEFT JOIN t_pay_lakala ON t_pay_lakala.order_id = t_order_part_refund.order_id").
	Where("part_refund_type",2).
	Where("pay_type",6).
	Where("order_id IN (?)",orderIds).
	Where("created_at > ?",nowTime).
	Select(fields).Scan(&failedItems)

	for _, item := range failedItems {
		orderMap :=make(map[string]interface{})
		orderMap["id"]=item.OrderId
		
		refundType :=2 //部分退款
		tools.Logger.Info("开始部分退款orderId", item.OrderId,",退款类型",refundType,",支付类型",tools.ToString(orderMap["pay_type"]),",开始退款队列")
		if err := p.LakalaRefund(ip, orderMap,false,false); err != nil {
			tools.Logger.Error(err)
			job := jobs.NewLakalaRefundJob()
			job.ProduceMessageToConsumer(map[string]interface{}{
				"order_id": orderMap["id"],
				"part_refund":1,
			})
			return err
		}
		
	}

	
	return nil
}