package payment

import (
	"bytes"
	"encoding/json"
	"encoding/xml"
	"errors"
	"fmt"
	"io"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/factory"
	"mulazim-api/jobs"
	"mulazim-api/models"
	"mulazim-api/resources"
	"mulazim-api/services"
	"mulazim-api/services/sms"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/wechat/v3"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

//
//  WechatService
//  @Description: 微信渠道API
//
type WechatService struct {
	services.BaseService
	PaymentInterface
}


// 取消订单
func (l *WechatService)CancelOrder(ip string, order map[string]interface{}, orderId int, userId int, reasonId int, terminalId int, userType int, from string) error {
	// TODO: 实现取消订单逻辑
	return nil
}

// 检查订单是否可以取消
func (l *WechatService)CheckCancelOrder(orderId int, userId int, reasonId int, from string, userType int) (map[string]interface{}, error) {
	// TODO: 实现检查订单是否可以取消逻辑
	return nil, nil
}

// 每小时检查订单支付状态
func (l *WechatService)CheckOrderPayStateHourly(context *gin.Context) {
	// TODO: 实现每小时检查订单支付状态逻辑
}

// 检查重复支付订单
func (l *WechatService)CheckDupilicatedPayedOrder(context *gin.Context) {
	// TODO: 实现检查重复支付订单逻辑
}

// 检查资源现金
func (l *WechatService)CheckResCash(context *gin.Context) {
	// TODO: 实现检查资源现金逻辑
}

// 推送取消通知
func (l *WechatService)PushCancelNotify(from string, order map[string]interface{}) {
	// TODO: 实现推送取消通知逻辑
}

// 获取支付参数（微信支付，拉卡拉，微信收付通）
func (l *WechatService)GetPayParam(language string, orderId int, userId int, payId int, openId string, realIp map[string]interface{}, terminalId int, payerType int, payFrom int, appId string) (map[string]interface{}, error) {

	//判断旧版 微信还是 收付通 然后继续执行下单逻辑
	//1.判断该订单是否可以支付
	//查询是否可以支付
	openId,orderToday,err2 := l.CheckCanPay(orderId,userId,openId)
	if err2 != nil {
		return nil,err2
	}
	
	//其他参数
	extraParams :=make(map[string]interface{})
	orderNum :=orderToday.OrderID
	amount :=orderToday.ActualPaid

	orderUserId :=orderToday.UserID
	payResult,err := l.getPayParams(orderId,orderNum,int(amount),orderUserId,openId,userId,terminalId,extraParams)



	if err != nil {
		return nil, err
	}
	tools.Logger.Info("订单支付参数",payResult)
	if  orderToday.User.ID > 0 { //写入订单发送消息必要的信息
		resName := orderToday.Restaurant.NameUg
		langId :=1
		if language != "ug" {
			resName = orderToday.Restaurant.NameZh
			langId =2
		}
		//微信通知数据写入
		l.CreateWechatMessage(orderId,orderToday.OrderID,
			orderToday.Restaurant.ID,resName,
			orderToday.Restaurant.Tel,orderToday.ActualPaid,
			orderToday.BookingTime,orderToday.User.OpenID,langId)
	}

	return payResult,nil
}

// 获取代理支付参数
func (l *WechatService)GetAgentPayParam(language string, id int, openId string, realIp map[string]interface{}, terminalId int) (map[string]interface{}, error) {
	// TODO: 实现获取代理支付参数逻辑
	return nil, nil
}

// 支付通知
func (l *WechatService)Notify(context *gin.Context) {
	// TODO: 实现支付通知逻辑
}

// 小程序支付通知
func (l *WechatService)MiniNotify(context *gin.Context) (bool, string, int) {
	
	tools.Logger.Info("微信支付通知处理开始")
	ok, msg,order,version :=  l.notify(context)
	if ok {
		db := tools.GetDB()
		if order.SentSms != 1 { //用户下订单后，给用户发送短信

			var wechatSendMessage models.WechatSendMessage
			db.Model(&models.WechatSendMessage{}).Where("order_id = ?",order.ID).Find(&wechatSendMessage)
			if wechatSendMessage.ID > 0 && order.TerminalID == 8 {
				job := jobs.NewSendWechatMiniMessageJob()
				job.ProduceMessageToConsumer(map[string]interface{}{
					"order_id": order.ID,
					"type":     1,
				})
				db.Model(&models.OrderToday{}).Where("id = ?",order.ID).Updates(&map[string]interface{}{
					"sent_sms":1,
				})

			}else{//发送短信
				go func ()  {
					defer func() {
						if err := recover(); err != nil {
							tools.Logger.Error(err.(error).Error())
						}
					}()
					//阿里云短信发送 
					servicePhone := "400-1111-990"
					if len(order.AddressView.ServicePhone) > 0 {
						servicePhone = order.AddressView.ServicePhone
					}
					deliveryTime :=fmt.Sprintf("%s %s", carbon.Parse(order.BookingTime).Format("Y-m-d H:i"),"[北京]")
					contentMap := map[string]interface{}{
						"peisongshijian" : deliveryTime,
						"jine" : order.ActualPaid,
						"sertel" : servicePhone,
					}
					//dataType, _ := json.Marshal(contentMap)
					//content := string(dataType)
					//resp,er :=tools.AliSmsSendWithResponse(order.Mobile, content, "Mulazim","SMS_102525003")
					//if er != nil {
					//	tools.Logger.Error(er)
					//}
					//ZhuTongTemplateOrderNotification
					// 发送验证码
					smsServiceFactory := &factory.SmsServiceFactory{}
					smsService := smsServiceFactory.CreateSmsService()
					smsResp, err := smsService.SendSMSUsingTemplate(order.Mobile,sms.ZhuTongTemplateOrderNotification, contentMap)
					if err != nil {
						tools.Logger.Errorf("发送[订单通知]短信失败: %v \n",err)
					}
					tools.Logger.Infof("发送[订单通知]短信成功 结果: %v \n",smsResp)

					terminalId :=order.TerminalID
					//var msgs []models.Msg
					//list :=resp.Body.SmsSendDetailDTOs.SmsSendDetailDTO
					//for _, v := range list{
					//	msgs= append(msgs,models.Msg{
					//		Mobiles: *v.PhoneNum,
					//		TerminalID: terminalId,
					//		Type: 2,//订单通知
					//		Content: *v.Content,
					//		Count: tools.ToInt(*resp.Body.TotalCount),
					//		State: 1,
					//		Price: tools.ToFloat64(l.GetOption("aliyun_sms_fee",0.036)),
					//		ContentLength: len(*v.Content),
					//	})
					//}
					//if len(msgs) > 0 {
					//	db.Model(&models.Msg{}).Create(&msgs)
					//}
					msgModel := models.Msg{
						Mobiles: order.Mobile,
						TerminalID: terminalId,
						Type: 2,//订单通知
						Content: smsResp,
						Count: 1,
						State: 1,
						Price: tools.ToFloat64(l.GetOption("aliyun_sms_fee",0.036)),
						ContentLength: len(smsResp),
					}
					db.Model(&models.Msg{}).Create(&msgModel)
				}()


			}
		}
	}
	return ok,msg,version
}
func (l *WechatService) notify(c *gin.Context) (bool, string,models.OrderToday,int) {
	db := tools.Db
	logPrefix :="微信支付通知处理--"
	body, _ := io.ReadAll(c.Request.Body)

	c.Request.Body.Close() // 关闭请求体，避免后续读取问题
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body)) // 重置请求体以便后续可能的使用

	version :=3 //微信v3
	todayOrder := models.OrderToday{}
	wechatPayConfig := configs.NewWechatPayConfig("wechat_mini")
	notifyReq, err := wechat.V3ParseNotify(c.Request)
	var result wechat.V3DecryptResult
	tools.Logger.Info(logPrefix," 通知内容:", string(body))
	if err != nil {
		//不是v3的通知  试试 v2的通知格式

		// tools.Logger.Error(logPrefix,err)
		version =2 //微信v2
		// 获取请求体数据
		var v2Notify resources.WechatPayResponse
		// 解析 XML 数据到结构体
		if err := xml.Unmarshal(body, &v2Notify); err != nil {
			tools.Logger.Error(logPrefix,"微信支付通知v2处理结束",err)
			return false, "接受支付通知失败",todayOrder,version
		}
		if v2Notify.ReturnCode != "SUCCESS" {
			tools.Logger.Error(logPrefix,"微信支付通知V2处理结束",err)
			return false, "接受支付通知失败",todayOrder,version
		}

		tools.Logger.Error(logPrefix,"微信支付通知V2开始处理")
		result = wechat.V3DecryptResult{
			Appid           : v2Notify.AppId,
			Mchid           : v2Notify.MchId,
			OutTradeNo      : v2Notify.OutTradeNo,
			TransactionId   : v2Notify.TransactionId,
			TradeType       : v2Notify.TradeType,
			TradeState      : v2Notify.ResultCode,
			// TradeStateDesc  : v2Notify.TradeStateDesc,
			BankType        : v2Notify.BankType,
			// Attach          : v2Notify.Attach,
			SuccessTime     : carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			Payer           : &wechat.Payer{
				Openid: v2Notify.OpenId,
			},
			Amount          : &wechat.Amount{
				Total: v2Notify.TotalFee,
			},
		}


	}else{
		// 普通支付通知解密
		wxResult, err := notifyReq.DecryptCipherText(wechatPayConfig.MchApiV3Key)
		if err != nil {
			tools.Logger.Error(logPrefix,"微信支付通知处理结束",err)
			return false, "支付通知解密失败",todayOrder,version
		}
		b,_:=json.Marshal(wxResult)
		tools.Logger.Info(logPrefix,"通知解密内容:", string(b))
		result = *wxResult
	}

	logPrefix =logPrefix+"V"+tools.ToString(version)+":"

	//使用通知里的 "微信支付订单号" 或者 "商户订单号" 去自己的数据库找到订单
	wechat := models.Wechat{}
	err = db.Model(&models.Wechat{}).Where("out_trade_no = ?", result.OutTradeNo).First(&wechat).Error
	if err != nil || wechat.Payed == 1 { // 如果订单不存在 或者 订单已经支付过了
		tools.Logger.Info(logPrefix,"支付完成，直接退回去！")
		tools.Logger.Info(logPrefix,"微信支付通知处理结束 支付完成")
		return true, "已接收订单支付成功通知",todayOrder,version // 告诉微信，我已经处理完了，订单没找到，别再通知我了
	}
	// 用户是否支付成功
	langNowAny, _ := c.Get("lang")
	langNow := langNowAny.(string)
	db.Model(&models.OrderToday{}).Where("id = ?", wechat.OrderID).Preload("AddressView", func(db *gorm.DB) *gorm.DB {
		return db.Select("address_view.city_name_" + langNow + " as city_name," +
			"address_view.area_name_" + langNow + " as area_name," +
			"address_view.building_name_" + langNow + " as building_name," +
			"address_view.street_name_" + langNow + " as street_name, " +
			"address_view.building_id as building_id,street_id,service_phone")
	}).First(&todayOrder)
	if todayOrder.PayType == 5 {

		db.Table("wechat").Where("id = ?", wechat.ID).
			Updates(map[string]interface{}{"transaction_id": result.TransactionId, "trade_type": result.TradeType, "Payed": 1, "open_id": result.Payer.Openid, "remark": "支付两次，手动退款处理"})
		tools.Logger.Info(logPrefix,"微信支付通知处理结束支付成功")
		return true, "支付成功",todayOrder,version
	}
	//1.订单写已支付  2.订单状态记录写入  3.微信写入已支付 
	//设置数据库事务所的隔离级别

	tx := tools.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			tools.Logger.Error(logPrefix,"微信支付通知出错", r)
		}
	}()
	todayOrder = models.OrderToday{}
	tx.Table("t_order_today").Where("id = ?", wechat.OrderID).First(&todayOrder)
	if todayOrder.PayType == 5 {
		tools.Logger.Info(logPrefix,"事务锁成功处理")
		tx.Commit()
		tools.Logger.Info(logPrefix,"微信支付通知处理结束支付成功")
		return true, "支付成功",todayOrder,version
	}
	tools.Logger.Info(logPrefix,"---------t_order_today 锁获取成功-----",todayOrder.ID)
	//1.订单写已支付  开始
	orderUpdate := map[string]interface{}{
		"consume_type":       1,
		"pay_type":           5,
		"pay_time":           carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
		"state":3,
		"serial_number":      tools.GetOrderMaxSerialNumber(todayOrder.StoreID),
	}
	err = tx.Table("t_order_today").Where("id = ?", todayOrder.ID).Updates(orderUpdate).Error
	//2.更新订单状态记录
	tx.Model(&models.OrderStateLog{}).Create(&models.OrderStateLog{
		OrderID:       todayOrder.ID,
		OrderStateID:    2,
	})

	//2.更新订单状态记录
	tx.Model(&models.OrderStateLog{}).Create(&models.OrderStateLog{
		OrderID:       todayOrder.ID,
		OrderStateID:    3,
	})


	// 更新wechat
	wechatUpdate := map[string]interface{}{
		"transaction_id": result.TransactionId,
		"trade_type":     result.TradeType,
		"Payed":          1,
		"open_id":        result.Payer.Openid,
	}
	if err !=nil {
		tx.Rollback()
		tools.Logger.Error(logPrefix,"---------微信通知 出错了---", err,",",todayOrder.ID)
		return false,"t_order_today更新失败",todayOrder,version
	}
	//1.订单写已支付  结束 

	//2.微信写入已支付 开始

	err = tx.Table("wechat").Where("id = ?", wechat.ID).Updates(wechatUpdate).Error
	if err !=nil {
		tx.Rollback()
		tools.Logger.Error(logPrefix,"---------微信通知 出错了---", err,",",wechat.ID)
		return false,"wechat更新失败",todayOrder,version
	}
	//2.微信写入已支付 结束

	//黑名单用户处理部分  开始
	var user models.User
	tx.Model(models.User{}).Where("id = ?",todayOrder.UserID).Find(&user)
	var userBlackList models.UserBlacklist

	tx.Model(&models.UserBlacklist{}).Where("user_id = ? and state = ?",user.ID,0).Find(&userBlackList)
	if userBlackList.ID > 0 && userBlackList.OnlinePayCount > 0{//确认在黑名单
		tx.Model(&models.UserBlacklist{}).Where("id = ?",userBlackList.ID).Updates(&map[string]interface{}{
			"online_pay_count":userBlackList.OnlinePayCount-1,
		})
	}
	//黑名单用户处理部分  结束

	tools.Logger.Info(logPrefix,"微信支付通知处理成功",todayOrder.ID)

	tx.Commit()
	tools.Logger.Info(logPrefix,"微信支付通知处理结束支付成功",todayOrder.ID)
	return true, "支付成功",todayOrder,version
}

func (l *WechatService)CheckCanPay(orderId int,userId int,openId string) (string,models.OrderToday,error){
	db :=tools.GetDB()
	var orderToday models.OrderToday
	db.Model(&models.OrderToday{}).
		Preload("Restaurant").
		Preload("User").
		Where("id = ? and deleted_at is null ",orderId).
		Find(&orderToday)

	
	createdAt :=orderToday.CreatedAt
	consumeType := orderToday.ConsumeType //付费类型（0现金,1在线支付,3现金订单代理支付）
	orderState := orderToday.State
	payType := orderToday.PayType
	if orderToday.ID == 0 {
		
		return openId,orderToday,errors.New("order_is_null")
		
	}
	var user models.User
	db.Model(&models.User{}).Where("id= ?",userId).Select("id,open_id").Find(&user)
	if len(user.OpenID) > 0 && len(openId) == 0{ //朋友支付的时候的判断
		openId = user.OpenID
	}


	//是否可以付款 当前时间 加10分钟 超过当前时间则不能支付
	nw := carbon.Now(configs.AsiaShanghai)
	expireTime :=  carbon.Time2Carbon(createdAt.Add(10*time.Minute))
	
	if consumeType == 0 && payType == 0{ ///付费类型（0现金,1在线支付,3现金订单代理支付）
		if expireTime.Lt(nw) {
			return openId,orderToday,errors.New("order_is_overtime")
		}
	}else{//配送员支付
		if orderToday.CashClearState == 1 { //现金订单 已经支付完成
			return openId,orderToday,errors.New("order_state_error")
		}
		if !tools.InArray(orderState,[]int{4,5,6,7}) { //状态错误
			return openId,orderToday,errors.New("order_state_error") //订单状态错误
		}
		if payType != constants.PaymentAgentWechat { //代理支付
			return openId,orderToday,errors.New("order_cannot_repaid") //订单不能重复付款
		}
	}
	
	//订单关闭后不能支付
	if orderToday.State < 3 && ((orderToday.Coupon.ID > 0 && orderToday.Coupon.State == 2) && orderToday.SeckillState == 2 ){ //订单关闭后不能支付
		return openId,orderToday,errors.New("order_state_error")
	}
	
	return openId,orderToday,nil
}

func (l *WechatService) getPayParams(orderId int,orderNum string,totalFee int,orderUserId int,openId string,userId int,terminalId int,extraParams map[string]interface{}) (map[string]interface{}, error) {
	//1. 验证是否可以支付
	//2. 获取第三方支付参数
	//3. 记录支付日志
	//4. 格式化返回参数
	logPrefix :="微信支付参数_"
	db :=tools.GetDB()
	//如果预支付订单号没过期， 直接数据库中读取
	//获取已存在的未过期的下单数据
	exists,result,err :=l.getExistingParameters(orderId,userId,orderUserId,terminalId)
	if exists {
		if err != nil {
			return nil,errors.New("order_is_overtime")
		}
		return result,nil
	}

	expireTime := carbon.Now("Asia/Shanghai").AddMinutes(120).Carbon2Time()
	payResult,appid,mchid,err :=l.createWechatOrder(orderNum,uint(totalFee),openId,terminalId,expireTime)
	if err != nil {
		tools.Logger.Error(logPrefix,"微信支付下单--错误",err)
		return nil,errors.New("order_is_overtime")
	}


	//添加wechat表里， 支付信息
	var wechatModel models.Wechat
	db.Model(&models.Wechat{}).Where("order_id = ? ",orderId).Find(&wechatModel)
	if wechatModel.ID > 0 {
		if wechatModel.Payed == 1 {
			return nil,errors.New("order_is_paid") //已支付
		}
		db.Model(&models.Wechat{}).Where("order_id = ? ",orderId).Updates(&map[string]interface{}{
			"prepay_id" : tools.ToString(payResult["prepay_id"]),
			"expire_time":expireTime,
		})
	}else{
		//记录微信支付参数
		l.logWechatParams(appid,mchid,openId,orderId,orderNum,uint(totalFee),payResult,expireTime)
	}

	return payResult,nil
}


//记录微信支付参数
func (l *WechatService) logWechatParams(appId string,mchId string,
	openId string,orderId int,orderNum string,totalFee uint,
	payResult map[string]interface{},expireTime time.Time) {
	db :=tools.GetDB()
	db.Model(&models.Wechat{}).Create(&models.Wechat{
		AppID: appId,
		MchID: mchId,
		OpenID: openId,
		OrderID : orderId,
		OutTradeNo : orderNum,
		TotalFee : totalFee,
		PrepayID : tools.ToString(payResult["prepay_id"]),
		NonceStr : tools.ToString(payResult["nonceStr"]),
		TradeType : tools.ToString(payResult["trade_type"]),
		ExpireTime: expireTime,
	})

}


//记录微信支付参数
func (l *WechatService) createWechatOrder(orderNum string,
	totalFee uint,openId string,terminalId int,expireTime time.Time) (map[string]interface{},string,string,error){
	var payResult map[string]interface{}
	wechatPayConfig := *configs.NewWechatPayConfig("wechat_mini")
	wechatPay := *tools.NewWechatPayInstance(&wechatPayConfig)
	appid :=wechatPayConfig.AppID
	mchid :=wechatPayConfig.MchID
	orderParams :=make(map[string]interface{})
	orderParams["description"] =fmt.Sprintf("%s %f %s","支付金额为:",tools.ToRound(tools.ToFloat64(totalFee)/100,2)," 元")
	// orderParams["out_trade_no"] = tools.GenerateCommonOutTradeNo("wechat_mini","wechat_mini")
	orderParams["out_trade_no"] = orderNum
	orderParams["time_expire"] = expireTime.Format(time.RFC3339)
	orderParams["total"] = totalFee
	orderParams["openid"] = openId
	if terminalId == 8 { //小程序
		ok,result :=wechatPay.JSAPIPay(orderParams)
		if !ok {
			return nil,appid,mchid,errors.New("order_is_overtime")
		}
		payResult = result

	}else{//APP
		ok,result :=wechatPay.AppPay(orderParams)
		if !ok {
			return nil,appid,mchid,errors.New("order_is_overtime")
		}
		payResult = result
	}
	bts,_:=json.Marshal(payResult)
	tools.Logger.Info("支付参数",string(bts))
	return payResult,appid,mchid,nil
}
//获取已存在的未过期的下单数据
func (l *WechatService) getExistingParameters(orderId int, userId int,
	orderUserId int,terminalId int,
) (bool,map[string]interface{},error) {
	logPrefix :="微信支付参数_"
	db :=tools.GetDB()
	var wechatModelOld models.Wechat
	db.Model(&models.Wechat{}).Where("order_id = ? and expire_time >= ?",orderId,carbon.Now(configs.AsiaShanghai)).Find(&wechatModelOld)
	if wechatModelOld.ID > 0 && userId == orderUserId { //如果预支付订单号没过期， 直接数据库中读取
		if terminalId == 8 {//小程序
			wechatPayConfig := *configs.NewWechatPayConfig("wechat_mini")
			wechatPay := *tools.NewWechatPayInstance(&wechatPayConfig)
			ok,result :=wechatPay.PaySignMini(wechatModelOld.PrepayID)
			if !ok {
				rsBytes ,_:=json.Marshal(result)
				tools.Logger.Error(logPrefix,"微信支付下单--错误",string(rsBytes))
				return true,nil,errors.New("order_is_overtime")
			}
			return true,result,nil
		}else{//APP
			wechatPayConfig := *configs.NewWechatPayConfig("wechat_api")
			wechatPay := *tools.NewWechatPayInstance(&wechatPayConfig)
			ok,result :=wechatPay.PaySignMiniApp(wechatModelOld.PrepayID)
			if !ok {
				rsBytes ,_:=json.Marshal(result)
				tools.Logger.Error(logPrefix,"微信支付下单--错误",string(rsBytes))
				return true,nil,errors.New("order_is_overtime")
			}
			return true,result,nil
		}
	}
	return false,nil,nil

}


//抽奖活动是否可以支付
func (l *WechatService)CheckLotteryCanPay(orderId int,userId int,openId string) (string,models.LotteryOrder,error){
	db :=tools.GetDB()
	
	var lotteryOrder models.LotteryOrder	//抽奖活动订单
	
	
	db.Model(&models.LotteryOrder{}).
		Where("id = ? and deleted_at is null ",orderId).
		Preload("LotteryActivity").
		Find(&lotteryOrder)
	if lotteryOrder.ID == 0 {
		return openId,lotteryOrder,errors.New("order_is_null")
	}
	
	var user models.User
	db.Model(&models.User{}).Where("id= ?",userId).Select("id,open_id").Find(&user)
	if len(user.OpenID) > 0 && len(openId) == 0{ //朋友支付的时候的判断
		openId = user.OpenID
	}


	//是否可以付款 当前时间 加10分钟 超过当前时间则不能支付
	nw := carbon.Now(configs.AsiaShanghai)
	expireTime := carbon.Parse(lotteryOrder.CreatedAt.Format("2006/01/02 15:04:05"), "Asia/Shanghai").AddMinutes(10)
	
	if expireTime.Lt(nw) {
		return openId,lotteryOrder,errors.New("order_is_overtime")
	}
	
	if !(lotteryOrder.State>1) {
		return openId,lotteryOrder,errors.New("lottery_activity_is_expired")
	}

	nw = carbon.Now()
	expTime := carbon.Parse(lotteryOrder.LotteryActivity.EndTime.Format("Y-m-d H:i:s"), "Asia/Shanghai")

	if lotteryOrder.LotteryActivity.State !=1 || (lotteryOrder.LotteryActivity.State ==1 || expTime.Lt(nw)) {
		return openId,lotteryOrder,errors.New("lottery_activity_is_expired")
	}

	
	return openId,lotteryOrder,nil
}



//抽奖活动 获取支付参数（微信支付，拉卡拉，微信收付通）
func (l *WechatService)GetLotteryPayParam(language string, orderId int, userId int, payId int, openId string, realIp map[string]interface{}, terminalId int, payerType int, payFrom int, appId string) (map[string]interface{}, error) {

	//判断旧版 微信还是 收付通 然后继续执行下单逻辑
	//1.判断该订单是否可以支付
	//查询是否可以支付
	openId,lotteryOrder,err2 := l.CheckLotteryCanPay(orderId,userId,openId)
	if err2 != nil {
		return nil,err2
	}
	
	//其他参数
	extraParams :=make(map[string]interface{})
	orderNum :=lotteryOrder.OrderID
	amount :=lotteryOrder.Price

	orderUserId :=lotteryOrder.UserID
	payResult,err := l.getPayParams(orderId,orderNum,int(*amount),*orderUserId,openId,userId,terminalId,extraParams)



	if err != nil {
		return nil, err
	}
	tools.Logger.Info("订单支付参数",payResult)
	

	return payResult,nil
}


// 获取代理支付参数 加价美食
func (l *WechatService) GetAgentPriceUpPayParam(language string, id int, openId string, realIp map[string]interface{}, terminalId int,appId string) (map[string]interface{}, error) {
	// TODO: 实现获取代理支付参数逻辑
	return nil, nil
}
func (l *WechatService) GetAgentPriceUpCheck(id int) (map[string]interface{}, error){
	// TODO: 实现获取代理支付参数逻辑
	return nil, nil
}

func (l *WechatService) GetAgentPriceUpRefund(c *gin.Context,id int) (map[string]interface{}, error){
	// TODO: 实现获取代理支付参数逻辑
	return nil, nil
}


func (l *WechatService) PostPartRefund(ip string,details resources.PartRefund) ( error) {
	return nil
}
//部分退款查询 
func (l *WechatService) PartRefundCheck(
	refundParams resources.PartRefund,
	) (int64, error){
		return 0,nil
}


func (l WechatService)  GetOrderStatus(orderId int) ([]map[string]interface{}, error){
	return nil,nil
}