package payment

import (
	"errors"
	"mulazim-api/models"
	"mulazim-api/resources"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type PaymentInterface interface {
	// 取消订单
	CancelOrder(ip string, order map[string]interface{}, orderId int, userId int, reasonId int, terminalId int, userType int, from string) error
	CheckCancelOrder(orderId int, userId int, reasonId int, from string, userType int) (map[string]interface{}, error)
	CheckOrderPayStateHourly(context *gin.Context)
	CheckDupilicatedPayedOrder(context *gin.Context)
	CheckResCash(context *gin.Context)
	PushCancelNotify(from string, order map[string]interface{})
	UpdateLotteryChanceInfo(order map[string]interface{})

	//支付相关的接口
	//支付参数获取 微信支付，拉卡拉，微信收付通
	GetPayParam(language string,orderId int,userId int, payId int,openId string,realIp map[string]interface{},terminalId int,payerType int ,payFrom int,appId string) (map[string]interface{}, error)

	GetAgentPayParam(language string,id int,openId string,realIp map[string]interface{},terminalId int) (map[string]interface{}, error)
	//支付通知 
	Notify(context *gin.Context)
	//小程序支付通知
	MiniNotify(context *gin.Context) (bool,string,int)

	CheckCanPay(orderId int,userId int,openId string) (string,models.OrderToday,error)


	//[守将活动] 支付参数获取 微信支付，拉卡拉，微信收付通 
	GetLotteryPayParam(language string,orderId int,userId int, payId int,openId string,realIp map[string]interface{},terminalId int,payerType int ,payFrom int,appId string) (map[string]interface{}, error)

	CheckLotteryCanPay(orderId int,userId int,openId string) (string,models.LotteryOrder,error)

	//代理支付 加价美食 支付二维码获取
	GetAgentPriceUpPayCode(id int) (map[string]interface{}, error)
	//支付参数获取 加价 美食 
	GetAgentPriceUpPayParam(language string,id int,openId string,realIp map[string]interface{},terminalId int,appId string) (map[string]interface{}, error)
	//加价 美食支付查询 
	GetAgentPriceUpCheck(id int) (map[string]interface{}, error)
	//加价 美食 退款
	GetAgentPriceUpRefund(c *gin.Context,id int) (map[string]interface{}, error)

	//部分 退款
	PartRefundCheck(
		refundParams resources.PartRefund,
		) (int64, error)
	PostPartRefund(ip string,details resources.PartRefund) (error)

	GetOrderStatus(orderId int) ([]map[string]interface{}, error)
}

func PaymentFactory(thirdPartyType string) PaymentInterface {
	return new(PaymentService)
}
//
// PaymentFactoryByOrderID
//  @Description: 根据订单ID创建支付对象
//  @param orderId
//  @return PaymentInterface
//
func PaymentFactoryByOrderID(orderId int) (PaymentInterface,error) {
	db :=tools.GetDB()
	var orderToday models.OrderToday
	db.Model(&models.OrderToday{}).
		Preload("Restaurant").
		Preload("OrderExtend").
		Where("id = ? and deleted_at is null ",orderId).
		Find(&orderToday)

	var lotteryOrder models.LotteryOrder	//抽奖活动订单
	payPlatform := orderToday.PayPlatform
	if orderToday.ID == 0 {
		db.Model(&models.LotteryOrder{}).
			Where("id = ? and deleted_at is null ",orderId).
			Preload("LotteryActivity").
			Find(&lotteryOrder)
		if lotteryOrder.ID == 0 {
			return nil,errors.New("order_is_null")
		}
		payPlatform = 1
	}else{
		if orderToday.OrderExtend !=nil && orderToday.OrderExtend.IsClosed == 1 { //订单已关闭不能支付 
			return nil,errors.New("order_is_closed_can_not_pay")
		}else if orderToday.State == models.ORDER_STATE_CANCELED || orderToday.State == models.ORDER_STATE_RESTAURANT_REJECTED{ //订单已取消不能支付 
			return nil,errors.New("order_is_canceled_can_not_pay")
		}
	}
	if payPlatform  == models.PAY_PLATFORM_ORIGINAL{
		return new(WechatService),nil
	}else if payPlatform == models.PAY_PLATFORM_LAKALA{
		return new(LakalaService),nil
	}else if payPlatform == models.PAY_PLATFORM_WECHAT_ECOMMERCE{
		return new(WechatECommerceService),nil
	}
	return new(PaymentService),nil
}

func PaymentFactoryByPlatformID(payPlatform int) PaymentInterface {
	if payPlatform  == 0{
		return new(WechatService)
	}else if payPlatform == 1{
		return new(LakalaService)
	}else if payPlatform == 4{
		return new(WechatECommerceService)
	}
	return new(PaymentService)
}

func GetPaymentFactory() PaymentInterface {
	return new(PaymentService)
}
