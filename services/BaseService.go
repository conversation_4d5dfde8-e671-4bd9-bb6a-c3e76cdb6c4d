package services

import (
	"context"

	"errors"

	"fmt"
	"io"
	"log"

	"mulazim-api/jobs"

	"math/rand"
	"mime/multipart"

	"mulazim-api/models/cms"

	"os"
	"strconv"
	"strings"

	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/models"
	shipmentModels "mulazim-api/models/shipment"
	"mulazim-api/tools"

	"time"

	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"

	shipperResource "mulazim-api/resources/shipper"
)

type BaseService struct {
}

// 根据店铺id 找代理
func (b *BaseService) GetAdminByStoreId(storeId int) models.Admin {
	var admin models.Admin
	db := tools.Db
	db.Model(models.Admin{}).
		Joins("INNER JOIN b_admin_store ON t_admin.id = b_admin_store.admin_id ").
		Where("b_admin_store.store_id = ? ", storeId).
		Where("state = 1").
		Where("type = 3 ").
		Where("t_admin.deleted_at IS NULL").Order("type desc").First(&admin)

	return admin
}

// 记录订单状态日志
func (b *BaseService) OrderStateLog(db *gorm.DB, logMap map[string]interface{}) error {

	logMap["created_at"] = carbon.Now()
	odlErr := db.Table("t_order_state_log").Create(logMap).Error

	return odlErr

}

// SendSocketToShipper
//
// @Description: 配送员发送通知
// @Author: Rixat
// @Time: 2023-07-27 10:52:00
// @receiver
// @param orderId string 订单id
// @param userId string 用户id
// @param msgType string  1."管理员分配" 2."管理员取消订单了" 3."餐厅取消订单了" 4."客户取消订单了" 5."餐厅准备好美食了" 6."餐厅准备好美食了"
func (b *BaseService) SendSocketToShipper(orderId string, userId string, msgType string) {
	go func() {
		logPrefix :="order_push_shipper_"
		data := make(map[string]interface{})
		data["order_id"]=orderId
		data["user_id"]=userId
		data["type"]=msgType
		url :=configs.MyApp.ShipperVoiceUrl+"/shipper-order-change"
		tools.Logger.Info(logPrefix+" shipper 发送socket ,url:",url)
		rtn :=tools.HttpPost(url,data,5*time.Second)
		if len(rtn) == 0 {
			tools.Logger.Error(logPrefix+"voice_socket_发送失败")	
		}
		
	}()

}

// 获取配送员这个月的总订单数量
func (b BaseService) GetShipperMonthOrderCount(adminId int, month string) int {

	db := tools.GetDB()

	monthStart, monthEnd := tools.GetMonthStartAndEndWithParam(month, "Y-m")
	var od models.ShipperDailyStatics
	db.Model(&models.ShipperDailyStatics{}).
		Where("shipper_id", adminId).
		Where("trans_date between ? and ?", monthStart, monthEnd).
		Select("shipper_id,sum(finished_order_count) as finished_order_count").
		Find(&od)
	return int(od.FinishedOrderCount)

}

func (b BaseService) GetShipperOrderCount(adminId int, startTime string, endTime string) int {

	db := tools.GetDB()

	var od models.ShipperDailyStatics

	db.Model(&models.ShipperDailyStatics{}).
		Where("shipper_id", adminId).
		Where("trans_date between ? and ?", startTime, endTime).
		Select("shipper_id,sum(finished_order_count) as finished_order_count").
		Find(&od)
	return int(od.FinishedOrderCount)

}

// 这个月的 今天之前的 已经成功的订单数量
func (b BaseService) GetShipperMonthOrderBeforeToday(adminId int, month string,archiveDay string,isArchive bool) int {

	db := tools.GetDB()
	mt := carbon.ParseByFormat(month, "Y-m").Format("Y-m-d")
	monthStart := carbon.Parse(mt)
	yesterday := carbon.Parse(archiveDay,configs.AsiaShanghai)
	dayLength := int(monthStart.DiffInDays(yesterday))
	if dayLength < 0 {
		yesterday = monthStart
	}

	key := "shipper_" + tools.ToString(adminId) + "_month_before_today_order_count_new_" + archiveDay
	if isArchive {
		key = "shipper_" + tools.ToString(adminId) + "_month_before_today_archive_order_count_" + archiveDay
	}
	thisMonthOrderCountStr := tools.Remember2(key, time.Hour*6, func() interface{} {
		var co int64
		db.Model(&shipmentModels.ShipperIncome{}).
			Where("type = ? and shipper_id = ? and date between ? and ? and order_type = 1", 1,
				adminId, monthStart.Format("Y-m-d"), yesterday.Format("Y-m-d")).Count(&co)
		var coSpecial int64
		db.Model(&shipmentModels.ShipperIncome{}).
			Where("type = ? and shipper_id = ? and date between ? and ? and order_type = 2 and amount=0", 1,
				adminId, monthStart.Format("Y-m-d"), yesterday.Format("Y-m-d")).Count(&coSpecial)
		return int(co+coSpecial)
	})
	thisMonthOrderCount := tools.ToInt(thisMonthOrderCountStr)
	return thisMonthOrderCount

}

// 今天已经成功的订单数量
func (b BaseService) GetShipperOrderCountToday(adminId int) int {

	db := tools.GetDB()

	todayStart := carbon.Now(configs.AsiaShanghai).Format("Y-m-d") + " 00:00:00"
	todayEnd := carbon.Now(configs.AsiaShanghai).Format("Y-m-d") + " 23:59:59"

	todayOrderCount := int64(0)
	db.Model(&models.OrderToday{}).
		Where("shipper_id", adminId).
		Where("state = ? and created_at between ? and ?", 7, todayStart, todayEnd).
		Count(&todayOrderCount)
	return int(todayOrderCount)

}
//根据条件获取 该订单属于的 用户登录数据
func(b BaseService) GetMerchantUsersByOrder(where string) []constants.StoreUserOrder{
	db :=tools.ReadDb1
	redisKeyPrefix :="order_push_"
	var orders []models.OrderToday
	db.Model(&models.OrderToday{}).		
		Preload("AdminStore", func(d *gorm.DB) *gorm.DB {
			return d.Select("store_id,admin_id").Preload("Admin", func(dd *gorm.DB) *gorm.DB {
					return dd.Where("t_admin.type in (5,6) and t_admin.state = 1 and t_admin.deleted_at is null").
							Select("*,t_admin.id as str_id").
							Preload("OauthSessionsInt").Preload("MerchantPermissions","v1 = ?","order-list").
							Preload("PushDeviceRestaurant","user_type = ? ","t_restaurant")
			})
		}).
	Where(where).
	Select("id,store_id,serial_number").
	Find(&orders)
	stores :=make([]int,0)
	var storeUsers []constants.StoreUserOrder
	for _, order := range orders {
		
		adminUsers := make([]models.Admin, 0)
		for _, store := range order.AdminStore {
			//属于餐厅管理员 以及 登陆过的 用户 
			if store.StoreID == order.StoreID && (store.Admin.Type == 5 || store.Admin.Type == 6)  {
				if store.Admin.JwtSerialNumber > 0 || store.Admin.OauthSessionsInt.ID > 0 {
					if store.Admin.Type == 5 { //如果是商家的话可以直接加入 
						adminUsers = append(adminUsers, store.Admin)
					}else{ //商家子账户的话要考虑是否有权限 
						if len(store.Admin.MerchantPermissions) > 0 {
							adminUsers = append(adminUsers, store.Admin)
						}
					}
				}
			}
		}
		if !tools.InArray(order.StoreID,stores){
			stores = append(stores, order.StoreID)
			for _, a := range adminUsers {
				userExists :=false
				for _, stu := range storeUsers {
					if stu.UserId == a.ID {
						userExists = true
					}
				}
				if userExists {
					continue
				}
				send :=true //是否需要发送
				orderSendKey :=fmt.Sprintf(redisKeyPrefix+"merchant_%d_%d", order.ID,a.ID)
				su :=constants.StoreUserOrder{
					StoreId: order.StoreID, 
					UserId: a.ID, 
					OrderId: order.ID, 
					Type: a.Type, 
					SerialNumber: int(order.SerialNumber),
					Send: send,
					OrderSendKey:orderSendKey,
				}
				if a.PushDeviceRestaurant.ID > 0{ //加入 商家端 版本信息 
					su.AppVersion=int(a.PushDeviceRestaurant.DeviceAppVersion)
					su.AppBrand=a.PushDeviceRestaurant.Brand
					su.AppPlatform=string(a.PushDeviceRestaurant.Platform)
					su.Lang = string(a.PushDeviceRestaurant.Lang)
				}
				storeUsers = append(storeUsers, su)
			}
		}
	}
	return storeUsers
}

//给商家发送推送 新订单
func(b BaseService) SendSocketToMerchant(logPrefix string,redisKeyPrefix string,storeUsers []constants.StoreUserOrder,printOrder bool){
	redisHelper :=tools.GetRedisHelper()
	sendDuration :=60*time.Second
	storeJiguang :=make([]int,0)//已发送激光推送列表 避免重复推送
	//订单属于的店铺有几个人登录就给他发送几个通知

	printVoiceConfig := b.GetAppConfigInfo("merchant_print_order_voice_url")
	printVoiceConfigZh := b.GetAppConfigInfo("merchant_print_order_voice_url_zh")
	printVoiceUrl := ""
	sendVoiceSocket := false
	
	printVoiceMinVersion :=252
	if printVoiceConfig.ID > 0 && printVoiceConfig.Value != "" && printVoiceConfig.State == 1 {
		printVoiceMinVersion = tools.ToInt(strings.Replace(printVoiceConfig.Version,".","",-1))
		if len(printVoiceConfig.Value) > 0 && printVoiceMinVersion > 0 {
			sendVoiceSocket = true
			printVoiceUrl = printVoiceConfig.Value
		}
	}
	for _, su := range storeUsers {
		//监测 该订单的 推送历史 
		//onlineKey = "order_push_online_"+"店铺id"+"_"+"登录用户id"
		onlineKey :=fmt.Sprintf(redisKeyPrefix+"online_%d_%d",su.StoreId,su.UserId)
		exists2, _ := redisHelper.Exists(context.Background(),onlineKey).Result()
		tools.Logger.Info(logPrefix,"-扫描所有的订单-筛选需要推送的订单-订单存在推送历史",su.OrderId,"-exists2=",exists2,"-send=",su.Send,"onlineKey=",onlineKey)
		// exists2 = 1 //强制设置 设备在线
		if exists2 != 0 { //商家用户在线
			//加入推送队列 
			if sendVoiceSocket && printOrder && su.AppVersion >= printVoiceMinVersion { //后台发送到店铺语音 客户端版本大于等于 配置的客户端版本
				data := make(map[string]interface{})
				data["order_id"]=su.OrderId
				data["user_id"]=su.UserId
				data["store_id"]=su.StoreId
				data["voice_url"]= printVoiceUrl

				if (su.Lang != "ug")&&(printVoiceConfigZh.ID > 0 && printVoiceConfigZh.Value != "" && printVoiceConfigZh.State == 1) { //中文声音
					printVoiceMinVersion = tools.ToInt(strings.Replace(printVoiceConfigZh.Version,".","",-1))
					if len(printVoiceConfigZh.Value) > 0 && printVoiceMinVersion > 0 {
						sendVoiceSocket = true
						printVoiceUrl = printVoiceConfigZh.Value
					}
				}

				url :=configs.MyApp.MerchantVoiceUrl+"/custom-info"
				rtn :=tools.HttpPost(url,data,5*time.Second)
				if len(rtn) == 0 {
					tools.Logger.Error(logPrefix+"voice_socket_发送失败",su.OrderId)
					continue 
				}
				tools.Logger.Error(logPrefix+"voice_socket_发送成功",su.OrderId)
			}else{
				redisHelper.Set(context.Background(),su.OrderSendKey,constants.MerchantOrderPushReady,sendDuration)
				job := jobs.NewOrderPushToMerchant1Job()
				job.ProduceMessageToConsumer(map[string]interface{}{
					"user_id": su.UserId,
					"order_id": su.OrderId,
					"serial_number": su.SerialNumber,
					"store_id": su.StoreId,
					"type":     1,
					"channel_type":constants.ChannelTypeMerchantOrder,
				})
			}


		}else{
			if !tools.InArray(su.UserId,storeJiguang){
				storeJiguang = append(storeJiguang,su.UserId)
			}else{ //已经发送过激光推送了 不会再发送了，另一个循环 启动后再次发送
				continue
			}
			//商家不在线
			// 通过jpush推送
			// 通知 结束不用继续 推送 
			redisHelper.Set(context.Background(),su.OrderSendKey,constants.MerchantOrderPushSendComplete,sendDuration)
			tools.Logger.Info(logPrefix,"-扫描所有的订单-筛选需要推送的订单-订单存在推送历史-socket不在线，要发送激光推送",su.OrderId,"-exists2=",exists2,"-send=",su.Send,"onlineKey=",onlineKey,"-send_jiguang",su.OrderSendKey)

			job := jobs.NewPushJob()
			// subTitle := string(order.SerialNumber)
			//  添加要播放的声音类型
			job.PushData(jobs.PushData{
				UserId: su.StoreId,
				UserType: constants.PushUserTypeMerchant,
				PushContent: jobs.PushContent{
					TitleUg:   constants.TitleMerchantNewOrderUg,
					TitleZh:   constants.TitleMerchantNewOrderZh,
					ContentUg: constants.ContentMerchantNewOrderUg,
					ContentZh: constants.ContentMerchantNewOrderZh,
					Params:    map[string]interface{}{},
				},
				Client: models.PushDeviceClientMerchant,
				Sound:  constants.SoundMerchantNewOrder,
				ChannelType: constants.ChannelTypeMerchantOrder,
				AdminId:su.UserId,
			})

		}
		
	}
}

//根据条件获取 该订单属于的 用户登录数据
func(b BaseService) GetShipperUsersByOrder() []constants.StoreUserOrder{
	db :=tools.ReadDb1
	redisKeyPrefix :="order_push_"
	var orders []models.OrderToday
	db.Model(&models.OrderToday{}).		
		Preload("AdminStore", func(d *gorm.DB) *gorm.DB {
			return d.Select("store_id,admin_id").Preload("Admin", func(dd *gorm.DB) *gorm.DB {
					return dd.Where("t_admin.type in (8,9) and t_admin.state = 1 and t_admin.deleted_at is null").
					Select("*,t_admin.id as str_id").
					Preload("OauthSessionsInt")
			})
		}).
		Preload("PushDetails").
	Where("state in ?",[]int{4,5}).
	Where("order_type=?",1).
	Where("shipper_id is null").
	Where("delivery_type = ?",1).
	Where("created_at > ?",carbon.Now(configs.AsiaShanghai).AddDays(-1).Format("Y-m-d 00:00:00")).
	Select("id,store_id,serial_number").
	Find(&orders)
	stores :=make([]int,0)
	var storeUsers []constants.StoreUserOrder
	for _, order := range orders {
		if len(order.PushDetails) ==0 { //还没开始推送的话 要跳过 
			continue
		}
		adminUsers := make([]models.Admin, 0)
		for _, store := range order.AdminStore {
			//属于配送员 以及 登陆过的 用户 
			if store.StoreID == order.StoreID && (store.Admin.Type == 8 || store.Admin.Type == 9)  {
				if store.Admin.JwtSerialNumber > 0  || store.Admin.OauthSessionsInt.ID > 0{
					adminUsers = append(adminUsers, store.Admin)
				}
			}
		}
		if !tools.InArray(order.StoreID,stores){
			stores = append(stores, order.StoreID)
			for _, a := range adminUsers {
				
				pushedShipper :=false //默认不发送
				for _, p := range order.PushDetails {
					if p.ShipperID == int64(a.ID) { //
						pushedShipper =true
					}
				}
				if !pushedShipper { //不是推送的配送员的话 跳过
					continue
				}
				send :=true //是否需要发送
				orderSendKey :=fmt.Sprintf(redisKeyPrefix+"shipper_%d",a.ID)
				storeUsers = append(storeUsers, constants.StoreUserOrder{
					StoreId: order.StoreID, 
					UserId: a.ID, 
					OrderId: order.ID, 
					Type: a.Type, 
					SerialNumber: int(order.SerialNumber),
					Send: send,
					OrderSendKey:orderSendKey,
				})
			}
		}
	}
	return storeUsers
}
//获取配送员 打卡状态
func (b *BaseService) GetShipperAttendanceState (adminId int) int {
	db :=tools.GetDB()
	var admin models.Admin
	db.Model(&models.Admin{}).
		Where("id =? and state = ? and type in (8,9)",adminId,1).
		Find(&admin)
	return admin.AttendanceState
}

const TYPE_USER = 1           // 用户退单
const TYPE_ADMIN_CMS = 2      // 后台退单
const TYPE_ADMIN_MERCHANT = 3 // 商家退单
const TYPE_AUTO_REJECT = 4    // 自动退单

// SendOrderCancelSms
//
//	@Description: 发送取消订单消息
//	@author: Alimjan
//	@Time: 2023-07-21 17:08:44
//	@receiver p PaymentService
//	@param orderId int
//	@param typeMsg int
//	@return error
func (p BaseService) SendOrderCancelSms(orderId int, typeMsg int, userType int) error {
	go func() {
		var from string
		if userType == TYPE_ADMIN_CMS {
			from = "mulazim-cms"
		}
		if userType == TYPE_ADMIN_MERCHANT {
			from = "mulazim-api"
		}
		if userType == TYPE_AUTO_REJECT {
			from = "mulazim-socket"
		}
		if userType == TYPE_USER { // 客户不能取消短信订单
			from = "mulazim-user"
		}
		url := configs.MyApp.SwooleUrl+"/ug/v1/sms-send?order_id=" + tools.ToString(orderId) + "&type=" + tools.ToString(typeMsg) + "&from=" + from
		code, _ := tools.HttpGetJson(url, 15*time.Second)
		if code != 200 {
			tools.Logger.Error("发送取消订单短信失败", orderId, code)
		}
	}()
	return nil
}

//
// addNotification
//  @Description: 添加通知
//  @receiver p
//  @param admin_id
//  @param notificationType
//  @param link
//  @param content
//  @return error
//
func (p BaseService)  AddNotification(admin_id int, notificationType int, link string, content string) error {
	db := tools.Db
	var area models.AdminAreas
	result := db.Where("admin_id = ?", admin_id).First(&area)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			log.Println("找不到区域")
			return nil
		}
		return result.Error
	}

	notification := cms.Notification{
		Type:    notificationType,
		AreaID:  area.AreaID,
		AdminID: admin_id,
		ContentUg: content,
		Link:    link,
		State:   0,
	}

	if err := db.Create(&notification).Error; err != nil {
		log.Println("发送通知失败", err)
		return err
	}

	return nil
}
func (p BaseService) GetOption(key string,res interface{}) interface{}{

	db := tools.GetDB()
	var option models.Option
	db.Model(&models.Option{}).Where("key = ?",key).Find(&option)
	if len(option.Value) > 0 {
		return tools.ToString(option.Value)
	}
	return res
}
//微信通知数据写入
func (p BaseService) CreateWechatMessage(orderId int,orderNum string,storeId int,
	storeName string,storeTel string,totalFee uint,bookingTime string,openId string,langId int) {
	db := tools.GetDB()
	db.Model(&models.WechatSendMessage{}).Create(&models.WechatSendMessage{
		OrderID : orderId,
		OrderNo: orderNum,
		StoreID: storeId,
		StoreName: storeName,
		StorePhone: storeTel,
		TotalFee: int(totalFee),
		BookingTime: carbon.Parse(bookingTime,configs.AsiaShanghai).Carbon2Time(),
		OpenID: openId,
		LangID: langId,
	})
}

// 配送员是否能配送员订单
//
// @Description: 
// @Author: Rixat
// @Time: 2024-08-21 12:03:12
// @receiver 
// @param c *gin.Context
func (p BaseService) ShipperAttendanceCanTakeOrder(shipperID int) bool{
	db := tools.GetDB()
	useAttendance := "0"
	if tools.RedisGetValue("app_config_use_attendance") != "" {
		useAttendance = tools.RedisGetValue("app_config_use_attendance")
	}else{
		db.Model(&models.AppConfig{}).Select("value").Where("`key` = ?", "use_attendance").Scan(&useAttendance)
		// 5分钟缓存打卡状态配置
		tools.RedisSetValue("app_config_use_attendance",useAttendance,60)
	}
	// 如果没有开启，可以正常配送员
	if useAttendance == "0" {
		return true
	}
	// 区域开启智能派单功能
	var shipper models.Admin
	db.Model(&models.Admin{}).Where("id = ? and state = ? and type in (8,9)",shipperID,1).Preload("Area").Find(&shipper)
	if shipper.Area.AutoDispatchState != 1 {
		// 如果当前时间大于2025-04-03 08:00:00，则不看是否开启了智能派单
		if carbon.Parse("2025-04-03 08:00:00").Gt(carbon.Now()) {
			return true	
		}
	}

	if shipper.AttendanceState == 1 {
		return true
	}
	return false

}
//保险配置
func  (s BaseService) GetInsuranceConfig() shipperResource.ShipperInsurance{
	return shipperResource.ShipperInsurance {
		NameUg: configs.MyApp.InsuranceConfig.NameUg,
		NameZh:configs.MyApp.InsuranceConfig.NameZh,
		Amount:configs.MyApp.InsuranceConfig.Amount,//3元
	}
}

//获取配置 
func  (s BaseService) GetAppConfig(key string) string{
	result :=""
	db :=tools.GetDB()
	var appConfig models.AppConfig
	db.Model(&models.AppConfig{}).Where("`key` = ? and state = ?",key,1).Find(&appConfig)
	if appConfig.ID > 0 {
		result = appConfig.Value
	}
	return result
}
//获取配置 详细信息
func  (s BaseService) GetAppConfigInfo(key string) models.AppConfig{
	db :=tools.GetDB()
	var appConfig models.AppConfig
	db.Model(&models.AppConfig{}).Where("`key` = ? and state = ?",key,1).Find(&appConfig)
	return appConfig
}

//获取该区域是否 开启保险
func  (s BaseService) AreaInsured(areaId int,checkConfig bool) bool{
	db :=tools.GetDB()
	vv :=s.GetAppConfig("insurance_enable")
	var area models.Area
	db.Model(&models.Area{}).Where("id = ?",areaId).Find(&area)
	if checkConfig { // 配置开启保险
		if tools.ToInt(vv) == 1 && area.InsuranceEnable == 1 {
			return true
		}
		return false
	}
	if  area.InsuranceEnable == 1 {
		return true
	}
	
	return false
}



// 上传文件
func (s BaseService) Upload(typeName string, file *multipart.FileHeader, ext string, allowTypes []string) (string,string,string,error){
	if !tools.InArray(ext, allowTypes) {
		str := strings.Join(allowTypes, "，")
		return "","", "", errors.New("只允许"+str+"类型文件")
	}
	// 临时的
	fileName := s.GenerateRandomFilename() + ext
	rootDir := configs.MyApp.UploadRootDir
	imageDir := "uploads/" + typeName
	if _, err := os.Stat(rootDir + imageDir); os.IsNotExist(err) {
		// 目录不存在，创建目录
		err := os.MkdirAll(rootDir+imageDir, 0755)
		if err != nil {
			// 创建目录失败
			fmt.Println("目录创建失败:", err)
			return "","", "", err
		}
	}
	imagePath := imageDir + "/" + fileName
	fullPath := rootDir + imagePath
	if err := s.SaveUploadedFile(file, fullPath); err != nil {
		return "","", "", err
	}

	fileUrl := configs.MyApp.CdnUrl + imagePath

	return fullPath,fileUrl,imagePath,nil
}



// 生成随机图片名称
func (s BaseService) GenerateRandomFilename() string {
	// 生成当前时间的字符串形式
	timestamp := time.Now().Format("20060102150405")
	// 生成五位随机数字（00000 - 99999）
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Intn(100000)
	randomString := strconv.Itoa(randomNumber)
	paddedRandomString := fmt.Sprintf("%05s", randomString)
	// 将随机数字追加到时间戳后面
	return timestamp + paddedRandomString
}

//存储文件
func (s BaseService) SaveUploadedFile(file *multipart.FileHeader, dst string) error {
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	_, err = io.Copy(out, src)
	return err
}


//更新 加价日志 
func (s BaseService) PriceFoodLogUpdate(orderId int,state int) error {
	go func() {
		db :=tools.GetDB()
		db.Model(&models.PriceMarkupFoodLog{}).Where("order_id = ?",orderId).Updates(&map[string]interface{}{
			"state":state,//1:未支付，2：已支付，3:完成，4:退单
		})
	}()
	return nil
}

func (s BaseService) GetFoodById(ID int) models.RestaurantFoods{
	var food models.RestaurantFoods
	tools.Db.Model(&food).Where("id = ?",ID).Preload("Restaurant").Find(&food)
	return food
}
func (s BaseService) GetSpecIdByOptions(foodID int,optionIds []int) int{
	var specId int
	tools.Db.Model(&models.FoodSpecDetail{}).Select("spec_id").
		Where("food_id = ? and foods_spec_option_id in ?",foodID,optionIds).
		Group("spec_id").
		Having("count(id) = ?",len(optionIds)).
		Scan(&specId)
	return specId
}

func (s BaseService) SaveFoodSpec(resID int,foodID int,optionIds []int) (int,error){
	if len(optionIds) == 0 {
		return 0,nil
	}
	specId := s.GetSpecIdByOptions(foodID, optionIds)
	if specId > 0 {
		return specId , nil
	}
	// 创建FoodSpec
	var foodSpec models.FoodSpec
	foodSpec.FoodID = foodID
	foodSpec.RestaurantID = resID
	var specPrice int64
	tools.Db.Model(models.FoodSpecOption{}).Select("IFNULL(sum(price),0) as spec_price").Where("id in ?",optionIds).Scan(&specPrice)
	foodSpec.Price = int(specPrice)
	err := tools.Db.Model(&models.FoodSpec{}).Create(&foodSpec).Error
	if err != nil {
		return 0,err
	}
	// 创建FoodSpecDetail
	for _,optionId := range optionIds{
		err = tools.Db.Model(&models.FoodSpecDetail{}).Create(&models.FoodSpecDetail{
			SpecID: foodSpec.ID,
			RestaurantID: resID,
			FoodID: foodID,
			FoodsSpecOptionID: optionId,
		}).Error
		if err != nil {
			return 0,err
		}
	}
	return foodSpec.ID,nil
}

func (s BaseService) GetFoodSpecByID(specID int) models.FoodSpec{
	var foodSpec models.FoodSpec
	tools.Db.Model(&models.FoodSpec{}).Where("id = ?",specID).Find(&foodSpec)
	return foodSpec
}

// CheckFoodsMultipleDiscount 监测某个美食是否存在多分打折活动
// 参数:
//   - lang: 语言代码，用于决定返回的食品名称语言（"ug"为维吾尔语，其他为中文）
//   - foodId: 美食ID
//   - restaurantId: 餐厅ID，如为0则不限制餐厅
// 返回值:
//   - models.FoodsMultipleDiscount: 查询到的多分打折活动信息，ID>0表示存在有效活动
//   - string: 对应语言的食品名称
func (s BaseService) CheckFoodsMultipleDiscount(lang string, foodId int, startTime string, endTime string) (models.FoodsMultipleDiscount, string) {
	var foodDiscounts []models.FoodsMultipleDiscount
	db := tools.GetDB()

	// 根据是否指定餐厅ID执行不同的查询

	db.Model(&models.FoodsMultipleDiscount{}).
		Preload("Food").
		Where("food_id = ? and ((? >= start_time and ? < end_time) or (? > start_time and ? <=end_time) or ( start_time >=? and  end_time <= ?)) and state = ?", foodId, startTime, startTime, endTime, endTime, startTime, endTime, models.FoodsMultipleDiscountStateOpen).
		Find(&foodDiscounts)

	foodName := ""
	for _, foodDiscount := range foodDiscounts {

		multiDiscountStartTime := carbon.Time2Carbon(foodDiscount.StartTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d H:i:s")
		multiDiscountEndTime := carbon.Time2Carbon(foodDiscount.EndTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d H:i:s")
		if tools.DateTimeLineInDateTimeLine(multiDiscountStartTime, multiDiscountEndTime, startTime, endTime) {
			// 判断是否找到有效的多分打折活动并设置返回的食品名称
			foodName = foodDiscount.Food.NameUg
			if lang != "ug" {
				foodName = foodDiscount.Food.NameZh
			}
			return foodDiscount, foodName

		}
	}

	return models.FoodsMultipleDiscount{}, ""

}

//批量检查 优惠冲突
func (s BaseService) CheckFoodsMultipleDiscountForPref(lang string, foodId int, startDateTime, endDateTime, startTime, endTime string) string {

	prefStartTime := carbon.ParseByFormat(startTime, "H:i:s", configs.AsiaShanghai)
	prefEndTime := carbon.ParseByFormat(endTime, "H:i:s", configs.AsiaShanghai)
	beginDate := carbon.Parse(startDateTime, configs.AsiaShanghai)
	endDate := carbon.Parse(endDateTime, configs.AsiaShanghai).AddDay()
	difDay := beginDate.DiffInDays(endDate)
	foodName := ""
	for i := 0; i < int(difDay); i++ {
		startDate := beginDate.AddDays(i)
		prefStartTimeString := startDate.AddHours(prefStartTime.Hour()).AddMinutes(prefStartTime.Minute()).AddSeconds(prefStartTime.Second()).Format("Y-m-d H:i:s")
		prefEndTimeString := startDate.AddHours(prefEndTime.Hour()).AddMinutes(prefEndTime.Minute()).AddSeconds(prefEndTime.Second()).Format("Y-m-d H:i:s")

		_, foodName = s.CheckFoodsMultipleDiscount(lang, foodId, prefStartTimeString, prefEndTimeString)
		if foodName != "" {
			return foodName
		}
	}

	return ""

}

// 监测某个美食是否存在秒杀活动
func (s BaseService) CheckFoodsSeckill(lang string, foodId int, startTime string, endTime string) (models.Seckill, string, int) {
	var sec models.Seckill
	db := tools.GetDB()
	// now :=carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s")
	foodName := ""
	secType := int(0)
	db.Model(&models.Seckill{}).
		Preload("RestaurantFoods").
		Where("food_id = ? and ((? >= begin_time and ? < end_time) or (? > begin_time and ? <= end_time) or (begin_time >= ? and end_time <=?)) and state = ?", foodId, startTime, startTime, endTime, endTime, startTime, endTime, 1).Find(&sec)

	if sec.ID > 0 {
		foodName = sec.RestaurantFoods.NameUg
		if lang != "ug" {
			foodName = sec.RestaurantFoods.NameZh
		}
		secType = int(sec.Type)
	}
	return sec, foodName, secType
}

// 监测某个美食是否存在优惠活动
func (s BaseService) CheckFoodsPref(lang string, foodId int, startTime string, endTime string) (models.FoodsPreferential, string) {
	var sec models.FoodsPreferential
	db := tools.GetDB()
	foodName := ""
	startDateTime := carbon.Parse(startTime, configs.AsiaShanghai).Format("Y-m-d 00:00:00")
	endDateTime := carbon.Parse(endTime, configs.AsiaShanghai).Format("Y-m-d 00:00:00")
	db.Model(&models.FoodsPreferential{}).
		Preload("RestaurantFood").
		Where("restaurant_foods_id = ? AND (? BETWEEN start_date_time AND end_date_time  or ? BETWEEN start_date_time AND end_date_time )  AND state = ?", foodId, startDateTime, endDateTime, 1).Find(&sec)

	if sec.ID > 0 {
		prefStartTime := carbon.ParseByFormat(sec.StartTime, "H:i:s", configs.AsiaShanghai)
		prefEndTime := carbon.ParseByFormat(sec.EndTime, "H:i:s", configs.AsiaShanghai)
		beginDate := carbon.Time2Carbon(sec.StartDateTime).SetTimezone(configs.AsiaShanghai)
		endDate := carbon.Time2Carbon(sec.EndDateTime).SetTimezone(configs.AsiaShanghai).AddDay()
		difDay := beginDate.DiffInDays(endDate)
		for i := 0; i < int(difDay); i++ {
			startDate := beginDate.AddDays(i)
			prefStartTimeString := startDate.AddHours(prefStartTime.Hour()).AddMinutes(prefStartTime.Minute()).AddSeconds(prefStartTime.Second()).Format("Y-m-d H:i:s")
			prefEndTimeString := startDate.AddHours(prefEndTime.Hour()).AddMinutes(prefEndTime.Minute()).AddSeconds(prefEndTime.Second()).Format("Y-m-d H:i:s")
			if tools.DateTimeLineInDateTimeLine(prefStartTimeString, prefEndTimeString, startTime, endTime) {
				foodName = sec.RestaurantFood.NameUg
				if lang != "ug" {
					foodName = sec.RestaurantFood.NameZh
				}
				break
			} else {
				foodName = ""
			}

		}

	}
	return sec, foodName
}

// CheckMarketTimeConflict
//
// @Description: 验证同一个时间端是否存在活动
// @Author: Rixat
// @Time: 2023-03-03 17:39:44
// @receiver
// @param c *gin.Context
func (s BaseService) IsMarketingTimeConflict(market models.Marketing, language string) error {
	var marketOne []models.Marketing
	marketOne = append(marketOne, market)
	// timesMarket := marketing.GetMarketTimeMap(market)
	timesMarket := s.GetMarketArrTimeMap(marketOne, language)

	var markets []models.Marketing
	tools.Db.Table("t_marketing").Where("state=? ", 1).Where("restaurant_id=?", market.RestaurantID).Where("marketing_type=?", market.MarketingType).Find(&markets)

	timesMarkets := s.GetMarketArrTimeMap(markets, language)
	// 获取该餐厅正在运行的满减活动列表
	for _, inValue := range timesMarket {
		inBeginTime := inValue["start"]
		inEndTime := inValue["end"]
		for _, outValue := range timesMarkets {
			outBeginTime := outValue["start"]
			outEndTime := outValue["end"]
			if tools.DateTimeLineInDateTimeLine(inBeginTime, inEndTime, outBeginTime, outEndTime) {
				return errors.New(outValue["name"])
			}
		}
	}
	return nil
}

// GetMarketArrTimeMap
//
// @Description: 获取多个营销活动事件
// @Author: Rixat
// @Time: 2023-03-07 16:00:49
// @receiver
// @param c *gin.Context
func (s BaseService) GetMarketArrTimeMap(markets []models.Marketing, language string) []map[string]string {
	times := []map[string]string{}
	for _, market := range markets {
		beginDate := carbon.Time2Carbon(market.BeginDate).SetTimezone(configs.AsiaShanghai)
		endDate := carbon.Time2Carbon(market.EndDate).SetTimezone(configs.AsiaShanghai).AddDay()
		difDay := beginDate.DiffInDays(endDate)
		for i := 0; i < int(difDay); i++ {
			startDate := beginDate.AddDays(i)
			dayOfWeek := startDate.DayOfWeek()
			days := tools.DecimalToBinaryString(market.Day)
			isActiveDate := string(days[dayOfWeek-1])
			if isActiveDate == "1" {
				name := market.NameUg
				if language == "zh" {
					name = market.NameZh
				}
				if len(market.Time1Start) > 0 {
					times = append(times, map[string]string{"name": name, "start": startDate.ToDateString() + " " + market.Time1Start + ":00", "end": startDate.ToDateString() + " " + market.Time1End + ":00"})
				}
				if len(market.Time2Start) > 0 {
					times = append(times, map[string]string{"name": name, "start": startDate.ToDateString() + " " + market.Time2Start + ":00", "end": startDate.ToDateString() + " " + market.Time2End + ":00"})
				}
				if len(market.Time3Start) > 0 {
					times = append(times, map[string]string{"name": name, "start": startDate.ToDateString() + " " + market.Time3Start + ":00", "end": startDate.ToDateString() + " " + market.Time3End + ":00"})
				}
			}
		}
	}
	return times
}

//检查当前正在进行的满减活动
func (s BaseService) CheckFoodsMarketing(lang string, foodId int, resId int, startTime string, endTime string) (models.Marketing, string) {
	db := tools.GetDB()

	var markets []models.Marketing

	foodName := ""
	db.Model(&models.Marketing{}).
		Where("state=?  and length(foods) > ?", 1, 0).
		Where("restaurant_id=?", resId).
		Where("marketing_type=?", 1).Find(&markets)
	var foodItem models.RestaurantFoods
	db.Model(&models.RestaurantFoods{}).Where("id = ?", foodId).Find(&foodItem)

	var foodMarkets []models.Marketing

	for _, mm := range markets {
		foods := strings.Split(mm.Foods, ",")
		for _, food := range foods {
			if food == strconv.Itoa(foodId) {
				foodName = foodItem.NameUg
				if lang != "ug" {
					foodName = foodItem.NameZh
				}
				foodMarkets = append(foodMarkets, mm)
				timesMarkets := s.GetMarketArrTimeMap(foodMarkets, lang)
				// 获取该餐厅正在运行的满减活动列表
				flag := false
				for _, outValue := range timesMarkets {
					outBeginTime := outValue["start"]
					outEndTime := outValue["end"]
					if tools.DateTimeLineInDateTimeLine(startTime, endTime, outBeginTime, outEndTime) {

						flag = true
					}
				}
				if flag {
					return models.Marketing{}, foodName
				} else {
					foodName = ""
				}

			}
		}
	}

	return models.Marketing{}, foodName
}

//监测一个店铺同一个时间段是否存在多个 多份打折活动
func (s BaseService) CheckFoodsMultiDiscountCount(resId int,startTime string,endTime string,lang string) (int64,string){

	
	
		db := tools.GetDB()
	
		// 根据是否指定餐厅ID执行不同的查询
		ct :=int64(0)
		var discount models.FoodsMultipleDiscount
		query :=db.Model(&models.FoodsMultipleDiscount{}).
			Where("restaurant_id = ? and ((? >= start_time and ? < end_time) or (? > start_time and ? <=end_time) or ( start_time >=? and  end_time <= ?)) and state = ?",resId,startTime,startTime,endTime,endTime,startTime,endTime,models.FoodsMultipleDiscountStateOpen)

		query.Count(&ct)
		query.Preload("Restaurant").Find(&discount)
		
		if ct > 0 {
			resName :=discount.Restaurant.NameUg
			if lang != "ug" {
				resName =discount.Restaurant.NameZh
			}
			return ct,resName
		}
		
		return ct,""


}


// 设置规格的活动开启状态时，检查规格是否已经有改变
func (s BaseService) CheckChangeStateSpecInfo(specID int, foodID int) bool {
	db := tools.GetDB()
	var optionIds []int
	var specTypeCount int64
	db.Model(models.FoodSpecDetail{}).Select("foods_spec_option_id as option_ids").Where("spec_id = ? and is_deleted = 0", specID).Scan(&optionIds)
	db.Model(models.FoodSpecType{}).Where("food_id = ? and is_deleted = 0", foodID).Count(&specTypeCount)
	return len(optionIds) == tools.ToInt(specTypeCount)
}
