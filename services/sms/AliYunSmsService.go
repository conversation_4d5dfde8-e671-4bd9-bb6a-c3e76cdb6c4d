package sms

import (
	"encoding/json"
	"fmt"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v3/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"mulazim-api/tools"
)

const (
	ALiYunTemplateMerchantOrderConfirmation       = "SMS_461990557" // 商家订单确认通知
	ALiYunTemplateOrderCanceled                   = "SMS_232917774" // 取消订单通知
	ALiYunTemplateDeliveryTimeModified            = "SMS_472295113" // 骑手修改配送时间
	ALiYunTemplateLoginVerificationCode           = "SMS_101255082" // 登录确认验证码
	ALiYunTemplateMerchantInfoVerificationCode    = "SMS_268530025" // 商家信息核对验证码 (通用验证码)
	ALiYunTemplateOrderNotification               = "SMS_102525003" // 订单通知
	ALiYunTemplatePlatformRevenueStatistics       = "SMS_218291696" // 美滋来平台营业额统计
	ALiYunTemplateRegionalBusinessStatistics      = "SMS_228530207" // 全疆营业统计-v1
	ALiYunTemplateMerchantRegistrationFailed      = "SMS_276255405" // 商家入驻信息审核失败
	ALiYunTemplateCustomerRechargeNotification    = "SMS_238975670" // 充值通知客户
	ALiYunTemplateCustomerConsumptionNotification = "SMS_238980755" // 消费通知客户
	ALiYunTemplateTechnicalErrorNotification      = "SMS_462460169" // 告知技术人员平台运行错误
)

type AliYunSmsService struct{}

func (s *AliYunSmsService) SendSMSUsingTemplate(mobile string, templateId string, params map[string]interface{}) (string, error) {
	signature := "Mulazim"

	accessKeyId := tools.GetDefaultValue("", "LTAI4GJf1F5GrTjtzQLk93c9")           // "LTAI4GJf1F5GrTjtzQLk93c9"
	accessKeySecret := tools.GetDefaultValue("", "******************************") //	"******************************"

	config := &openapi.Config{
		AccessKeyId:     tea.String(accessKeyId),
		AccessKeySecret: tea.String(accessKeySecret),
	}
	// 设置访问域名
	config.Endpoint = tea.String("dysmsapi.aliyuncs.com")

	client, err := dysmsapi20170525.NewClient(config)
	if err != nil {
		tools.Logger.Errorf("阿里云短信初始化失败: %v", err)
		return "", err
	}

	// 将参数转换为 JSON 字符串
	mapToString := tools.MapToString(params)

	sendSmsRequest := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers:  tea.String(mobile),
		SignName:      tea.String(signature),
		TemplateCode:  tea.String(templateId),
		TemplateParam: tea.String(mapToString),
	}

	runtime := &util.RuntimeOptions{}

	// 尝试发送短信并捕获响应
	responseStr, err := func() (string, error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				tools.Logger.Errorf("捕获到异常: %v", r)
			}
		}()

		// 发送短信并获取响应
		response, err := client.SendSmsWithOptions(sendSmsRequest, runtime)
		if err != nil {
			return "", err
		}

		// 将响应序列化为 JSON 字符串
		responseJSON, err := json.Marshal(response)
		if err != nil {
			tools.Logger.Errorf("序列化响应内容失败: %v", err)
			return "", err
		}

		return string(responseJSON), nil
	}()

	// 如果发生错误，处理并返回
	if err != nil {
		var sdkErr *tea.SDKError
		if _t, ok := err.(*tea.SDKError); ok {
			sdkErr = _t
		} else {
			return "", fmt.Errorf("发送短信失败: %v", err)
		}

		// 打印 SDK 错误信息
		errorMessage, err := util.AssertAsString(sdkErr.Message)
		if err != nil {
			return "", fmt.Errorf("解析错误信息失败: %v", err)
		}

		return "", fmt.Errorf("阿里云短信服务错误: %s", errorMessage)
	}

	return responseStr, nil
}

func (s *AliYunSmsService) SendVerificationCode(mobile string, code string) (string, error) {
	params := map[string]interface{}{
		"code": code,
	}
	return s.SendSMSUsingTemplate(mobile, ALiYunTemplateMerchantInfoVerificationCode, params)
}
