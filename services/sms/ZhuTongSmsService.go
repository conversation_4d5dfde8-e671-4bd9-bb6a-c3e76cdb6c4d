package sms

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"mulazim-api/tools"
	"net/http"
	"strconv"
	"time"
)

const (
	ZhuTongTemplateMerchantOrderConfirmation       = "191183" // 商家订单确认通知
	ZhuTongTemplateOrderCanceled                   = "191305" // 取消订单通知
	ZhuTongTemplateDeliveryTimeModified            = "191382" // 骑手修改配送时间
	ZhuTongTemplateLoginVerificationCode           = "191395" // 登录确认验证码
	ZhuTongTemplateMerchantInfoVerificationCode    = "191384" // 商家信息核对验证码
	ZhuTongTemplateOrderNotification               = "191385" // 订单通知
	ZhuTongTemplatePlatformRevenueStatistics       = "191386" // 美滋来平台营业额统计
	ZhuTongTemplateRegionalBusinessStatistics      = "191387" // 全疆营业统计-v1
	ZhuTongTemplateMerchantRegistrationFailed      = "191388" // 商家入驻信息审核失败
	ZhuTongTemplateCustomerRechargeNotification    = "191390" // 充值通知客户
	ZhuTongTemplateCustomerConsumptionNotification = "191389" // 消费通知客户
	ZhuTongTemplateTechnicalErrorNotification      = "191391" // 告知技术人员平台运行错误
)

type ZhuTongSmsService struct{}

func (s *ZhuTongSmsService) SendSMSUsingTemplate(mobile string, templateId string, params map[string]interface{}) (string, error) {
	url := "https://api-shss.zthysms.com/v2/sendSmsTp"

	username := "xjjz888hy"
	password := "yH!498fK"
	signature := "【Mulazim】"

	tKey := time.Now().Unix()
	passwordEncrypted := s.encryptWithMD5(s.encryptWithMD5(password) + strconv.FormatInt(tKey, 10))

	// 构造请求数据
	data := map[string]interface{}{
		"tpId":      templateId,
		"username":  username,
		"password":  passwordEncrypted,
		"tKey":      tKey,
		"signature": signature,
		"records": []map[string]interface{}{
			{
				"mobile":    mobile,
				"tpContent": params,
			},
		},
	}

	// 将数据序列化为 JSON
	jsonStr, err := json.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("failed to marshal JSON: %v", err)
	}

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json; charset=utf-8")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()
	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %v", err)
	}


	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}
	tools.Logger.Info("短信发送成功", map[string]interface{}{"mobile": mobile, "response": string(body)})
	return string(body), nil
}

func (s *ZhuTongSmsService) SendVerificationCode(mobile string, code string) (string, error) {
	params := map[string]interface{}{
		"code":code,
	}
	return s.SendSMSUsingTemplate(mobile, ZhuTongTemplateMerchantInfoVerificationCode, params)
}

func (s *ZhuTongSmsService) encryptWithMD5(mstr string) string {
	data := []byte(mstr)
	md5obj := md5.New()
	md5obj.Write(data)
	password := md5obj.Sum(nil)
	return hex.EncodeToString(password)
}
