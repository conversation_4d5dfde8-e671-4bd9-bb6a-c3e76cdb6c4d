package services

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
)

type TerminalService struct {
	BaseService
	langUtil *lang.LangUtil
	language string
}

func (s TerminalService) Show(osType string) models.Terminal {
	db := tools.GetDB()
	var terminal models.Terminal
	db.Select("id,name,os_type,icon,version,version_code,package_name,force_update,url,description,description_ug,description_zh").
		Where("state = 1").
		Where("terminal_type = 2").
		Where("os_type = ?", osType).
		Order("version_code desc").First(&terminal)
	return terminal

}
//
// AppShow
//  @Description: 用户端App 升级
//  @receiver s
//  @param osType
//  @return models.Terminal
//
func (s TerminalService) AppShow(osType string) models.Terminal {
	db := tools.GetDB()
	var terminal models.Terminal
	db.Select("id,name,os_type,icon,version,version_code,package_name,force_update,url,description,description_ug,description_zh").
		Where("state = 1").
		Where("terminal_type = 1").
		Where("os_type = ?", osType).
		Order("version_code desc").First(&terminal)
	return terminal

}
// 版本获取 
func (s TerminalService) NewAppShow(osType string,terminalType int) models.Terminal {
	db := tools.GetDB()
	var terminal models.Terminal
	db.Select("id,name,os_type,icon,version,version_code,package_name,force_update,url,description,description_ug,description_zh").
		Where("state = 1").
		Where("terminal_type = ?",terminalType).
		Where("os_type = ?", osType).
		Order("version_code desc").First(&terminal)
	return terminal

}

func NewTerminalService(c *gin.Context) *TerminalService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	terminalService := TerminalService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &terminalService
}
