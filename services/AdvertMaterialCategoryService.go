package services

import (
	"errors"
	"fmt"
	"mulazim-api/models"
	"mulazim-api/requests/cms/advert/materialRequest/categoryRequest"
	"mulazim-api/tools"
)

type AdvertMaterialCategoryService struct {
}

// Create 创建
func (s *AdvertMaterialCategoryService) Create(request categoryRequest.CreateRequest) (models.AdvertMaterialCategory, error) {
	category := models.AdvertMaterialCategory{
		NameUg: request.NameUg,
		NameZh: request.NameZh,
		State:  request.State,
	}
	rs := tools.GetDB().Create(&category)
	if rs.Error != nil {
		msg := fmt.Sprintf("创建宣传材料分类创建失败: %s", rs.Error.Error())
		tools.Logger.Warn(msg)
		return models.AdvertMaterialCategory{}, errors.New("failed")
	}
	return category, nil
}

// Update 更新
func (s *AdvertMaterialCategoryService) Update(category models.AdvertMaterialCategory, request categoryRequest.CreateRequest) (models.AdvertMaterialCategory, error) {
	category.NameUg = request.NameUg
	category.NameZh = request.NameZh
	category.State = request.State
	rs := tools.GetDB().Save(&category)
	if rs.Error != nil {
		msg := fmt.Sprintf("更新宣传材料分类失败: ID: %d, errors: %s", category.ID, rs.Error.Error())
		tools.Logger.Warn(msg)
		return models.AdvertMaterialCategory{}, errors.New("failed")
	}
	return category, nil
}

// FindById 查看
func (s *AdvertMaterialCategoryService) FindById(id int) (models.AdvertMaterialCategory, error) {
	var category models.AdvertMaterialCategory
	rs := tools.GetDB().First(&category, id)
	if rs.Error != nil {
		msg := fmt.Sprintf("查看宣传材料分类失败: ID: %d, errors: %s", id, rs.Error.Error())
		tools.Logger.Warn(msg)
		return models.AdvertMaterialCategory{}, errors.New("failed")
	}
	return category, nil
}

// FindAll 查看
func (s *AdvertMaterialCategoryService) FindAll(state int, page int, limit int) ([]models.AdvertMaterialCategory, int64, error) {
	var categories []models.AdvertMaterialCategory = make([]models.AdvertMaterialCategory, 0)
	var total int64 = 0
	query := tools.GetDB().Model(models.AdvertMaterialCategory{}).Where("id > 1")
	if state > 0 {
		query.Where("state = ?", state)
		// 选择已删除时
	}
	query.Count(&total)
	query.Order("id desc")
	rs := query.Offset((page - 1) * limit).Limit(limit).Find(&categories)
	if rs.Error != nil {
		msg := fmt.Sprintf("查看宣传材料分类列表失败: State: %d, Page: %d, Size: %d errors: %s", state, page, limit, rs.Error.Error())
		tools.Logger.Warn(msg)
		return make([]models.AdvertMaterialCategory, 0), 0, errors.New("failed")
	}
	return categories, total, nil

}

// UpdateState 更新状态
func (s *AdvertMaterialCategoryService) UpdateState(category models.AdvertMaterialCategory, state int) (models.AdvertMaterialCategory, error) {
	category.State = state

	rs := tools.GetDB().Save(&category)
	if rs.Error != nil {
		msg := fmt.Sprintf("更新宣传材料分类状态失败: ID: %d, errors: %s", category.ID, rs.Error.Error())
		tools.Logger.Warn(msg)
		return models.AdvertMaterialCategory{}, errors.New("failed")
	}
	return category, nil
}

// Delete 删除
func (s *AdvertMaterialCategoryService) Delete(id int) error {
	var total int64
	tools.GetDB().
		Model(models.AdvertMaterial{}).
		Where("advert_material_category_id = ?", id).
		Count(&total)
	if 0 < total {
		return errors.New("advert_material_category_has_material_can_not_delete")
	}
	rs := tools.GetDB().Delete(&models.AdvertMaterialCategory{}, id)
	if rs.Error != nil {
		msg := fmt.Sprintf("删除宣传材料分类失败: ID: %d, errors: %s", id, rs.Error.Error())
		tools.Logger.Warn(msg)
		return errors.New("failed")
	}
	return nil
}
