package cms

import (
	"errors"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	shipmentModels "mulazim-api/models/shipment"
	"mulazim-api/scopes"
	"strings"

	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ShipperSalaryService struct {
	langUtil *lang.LangUtil
	language string
}

func NewShipperSalaryService(c *gin.Context) *ShipperSalaryService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	ShipperSalaryService := ShipperSalaryService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &ShipperSalaryService
}

// Detail
//
// @Description: 配送员收入详情
// @Author: Rixat
// @Time: 2023-11-07 02:33:05
// @receiver
// @param c *gin.Context
func (s ShipperSalaryService) DetailList(ShipperID int, page int, limit int, incomeType int,orderType int, month string, kw string, sort string, export bool) (total int64, all []map[string]interface{}, excelAll []map[string]interface{}) {
	startDate, endDate := tools.GetMonthStartAndEndWithParam(month, "Y-m")
	var incomeList []shipmentModels.ShipperIncome
	var totalCount int64
	incomeQuery := tools.Db.Model(incomeList).Where("type not in(10,11,14,15)") // 失败订单和取消订单只做记录，不扣款，所以不需要查询
	if ShipperID > 0 {
		incomeQuery.Where("shipper_id = ?", ShipperID)
	}

	if incomeType > 0 {
		incomeQuery.Where("type = ?", incomeType)
	}
	if orderType > 0 {
		incomeQuery.Where("order_type = ?", orderType)
	}
	if len(kw) > 3 {
		incomeQuery.Where("order_no", kw)
	}
	if len(startDate) > 0 && len(endDate) > 0 {
		incomeQuery.Where("date between ? and ?", startDate, endDate)
	}
	incomeQuery.Count(&totalCount)
	incomeQuery.Preload("Order.Restaurant")
	if page != 0 && limit != 0 {
		incomeQuery.Scopes(scopes.Page(page, limit))
	}

	incomeQuery.Find(&incomeList)

	items := make([]map[string]interface{}, 0)
	excelItems := make([]map[string]interface{}, 0)
	for _, income := range incomeList {
		typeName := s.langUtil.TArr("income_types")[income.Type]
		resName :=tools.If(s.language=="ug", income.Order.Restaurant.NameUg, income.Order.Restaurant.NameZh)
		createdAt :=tools.TimeFormatYmdHis(&income.Order.CreatedAt)
		if income.Type == 16 {//保险 
			resName = s.langUtil.T("amount_insurance")
			createdAt = carbon.Parse(income.Date,configs.AsiaShanghai).Format("Y-m-d")
		}
		items = append(items, map[string]interface{}{
			"id":                      income.ID,
			"type":               income.Type,
			"type_name":               typeName,
			"user_name":               income.Order.Name,                                     // 用户名称
			"order_id":                income.OrderID,                                        // 订单ID
			"order_no":                income.OrderNo,                                        // 订单编号
			"order_price":             income.OrderPrice,                                     // 订单金额
			"order_type":              income.OrderType,                                      // 1:普通订单，2：特价活动订单
			"shipment_income":         income.Amount,                                         // 配送费收入
			"amount":                  income.Amount,                                         // 配送费收入
			"complaint_type":          income.ComplaintType,                                  // 投诉类型：1：客户投诉，2:商家投诉
			"is_complaint":            tools.If(income.Type == 8, true, false),               // 是否投诉
			"is_late":                 tools.If(income.Type == 9, true, false),               // 是否迟到
			"order_delivery_end_time": tools.TimeFormatYmdHis(&income.Order.DeliveryEndTime), // 配送完毕时间
			"order_created_at":        createdAt,       // 下单时间
			"res_name": 			  resName,
		})
		if export {
			typeName = s.langUtil.TArrZh("income_types")[income.Type]
			excelItems = append(excelItems, map[string]interface{}{
				"ID":   income.ID,
				"类型":   typeName,
				"餐厅名称":tools.If(s.language=="ug", income.Order.Restaurant.NameUg, income.Order.Restaurant.NameZh),
				"客户名称": income.Order.Name,                                       // 用户名称
				"订单ID": income.OrderID,                                          // 订单ID
				"订单号":  income.OrderNo,                                           // 订单编号
				"订单价格": tools.ToPrice(tools.ToFloat64(income.OrderPrice) / 100), // 订单金额
				"配送收入": tools.ToPrice(tools.ToFloat64(income.Amount) / 100),     // 配送费收入
				// "amount":                  income.Amount,                                         // 配送费收入
				"投诉类型":   income.ComplaintType,                                  // 投诉类型：1：客户投诉，2:商家投诉
				"是否投诉":   tools.If(income.Type == 8, true, false),               // 是否投诉
				"是否迟到":   tools.If(income.Type == 9, true, false),               // 是否迟到
				"配送完毕时间": tools.TimeFormatYmdHis(&income.Order.DeliveryEndTime), // 配送完毕时间
				"下单时间":   createdAt,       // 下单时间
			})
		}
	}
	return totalCount, items, excelItems
}

// Detail
//
// @Description: 配送员收入详情
// @Author: Rixat
// @Time: 2023-11-07 02:33:05
// @receiver
// @param c *gin.Context
func (s ShipperSalaryService) DetailHeader(ShipperID int, month string) (map[string]interface{}, error) {
	var shipper models.Admin
	tools.Db.Model(shipper).Preload("ShipperRank").Where("id = ?", ShipperID).First(&shipper)
	if shipper.ID == 0 {
		return nil, errors.New("not_found")
	}
	// 本月和上月时间格式化
	startDate, endDate := tools.GetMonthStartAndEndWithParam(month, "Y-m")
	lastMonthStartDate, lastMonthEndDate := tools.GetMonthStartAndEndWithParam(carbon.ParseByFormat(month, "Y-m").SubMonth().Format("Y-m"), "Y-m")
	var curMonthCommentPercent map[string]interface{}
	var lastMonthCommentPercent map[string]interface{}
	// 配送员评分
	selectFields := `
		avg( comment_star ) as comment_star,
		avg( order_delivery_minute ) AS order_delivery_minute,
		sum( if((success_count >0 or fail_count >0 or cancel_count > 0),1,0) ) AS work_day,
		sum( reward + punishment + other + shipment +amount_invite_user + amount_order_tips+amount_insurance) AS total_salary,
		sum( success_count) AS total_order_count,
		sum( complain_count ) AS complain_count,
		sum( count_comment_bad ) AS count_comment_bad,
		sum( comment_good_count ) AS comment_good_count,
		sum( late_count ) AS late_count,
		sum( amount_reward ) AS amount_reward,
		sum( amount_tips ) AS amount_tips,
		sum( amount_special_time ) AS amount_special_time,
		sum( amount_special_weather ) AS amount_special_weather,
		sum( amount_comment_good ) AS amount_comment_good,
		sum( amount_comment_bad ) AS amount_comment_bad,
		sum( amount_order ) AS amount_order,
		sum( amount_complain ) AS amount_complain,
		sum( amount_late ) AS amount_late,
		sum( punishment ) AS punishment,
		sum(amount_invite_user) as amount_invite_user,
		sum(amount_order_tips) as amount_order_tips,
		sum(amount_special_price_order) as amount_special_price_order,
		sum(count_special_price_order) as count_special_price_order,
		sum(amount_insurance) as amount_insurance,
		sum(punishment+amount_insurance) as amount_reduce
		`
	tools.Db.Model(shipmentModels.ShipperIncomeArchive{}).
		Select(selectFields).Where("shipper_id=?", ShipperID).
		Where("date between ? and ?", startDate, endDate).
		Scan(&curMonthCommentPercent)

	// 配送员评分
	tools.Db.Model(shipmentModels.ShipperIncomeArchive{}).
		Select(selectFields).
		Where("shipper_id=?", ShipperID).
		Where("date between ? and ?", lastMonthStartDate, lastMonthEndDate).
		Scan(&lastMonthCommentPercent)

	// 排名	总工资排序
	rankRes := make([]map[string]interface{}, 0)
	tools.Db.Model(shipmentModels.ShipperIncomeArchive{}).
		Select("shipper_id,sum(reward + punishment + other + shipment +amount_invite_user + amount_order_tips+amount_insurance) salary").
		Where("area_id=?", shipper.AdminAreaID).
		Where("date between ? and ?", startDate, endDate).
		Group("shipper_id").
		Order("salary desc").
		Scan(&rankRes)
		
	salaryStr := "ئالىدىغان مائاش("+tools.ToString(tools.ToFloat64(curMonthCommentPercent["total_salary"])/100)+")="
	salaryStr += "زاكاز كىرىمى("+tools.ToString(tools.ToFloat64(curMonthCommentPercent["amount_order"])/100)+")+"
	salaryStr += "مۇكاپات كىرىمى("+tools.ToString(tools.ToFloat64(curMonthCommentPercent["amount_reward"])/100)+")+"
	salaryStr += "تارتۇقلاش كىرىمى("+tools.ToString(tools.ToFloat64(curMonthCommentPercent["amount_tips"])/100)+")+"
	salaryStr += "ئالاھىدە يەتكۈزۈش كىرىمى("+tools.ToString(tools.ToFloat64(curMonthCommentPercent["amount_special_time"])/100)+")+"
	salaryStr += "ئالاھىدە ھاۋا رايى كىرىمى("+tools.ToString(tools.ToFloat64(curMonthCommentPercent["amount_special_weather"])/100)+")+"
	salaryStr += "ياخشى باھا كىرىمى كىرىمى("+tools.ToString(tools.ToFloat64(curMonthCommentPercent["amount_comment_good"])/100)+")+"
	salaryStr += "خېرىدار كۆپەيتىش كىرىمى("+tools.ToString(tools.ToFloat64(curMonthCommentPercent["amount_invite_user"])/100)+")+"
	salaryStr += "خېرىدار زاكاز چۈشۈرگەن زاكازدىن ئېرىشكەن كىرىمى("+tools.ToString(tools.ToFloat64(curMonthCommentPercent["amount_order_tips"])/100)+")-"
	
	salaryStr += "جەمئىي تۇتۇلغان پۇل("+tools.ToString(tools.ToFloat64(curMonthCommentPercent["amount_reduce"])/100*-1)+")"
	formula := [...]string{
		salaryStr, 
	}
	return map[string]interface{}{
		"formula":formula,
		"top": map[string]interface{}{ // 统计上面
			"shipper_avatar":        tools.CdnUrl(shipper.Avatar),                                                                                                      // 配送员名称
			"shipper_name":          shipper.RealName,                                                                                                                  // 配送员名称
			"shipper_mobile":        shipper.Mobile,                                                                                                                    // 配送员手机号
			"score_percent":         tools.If(tools.ToFloat64(curMonthCommentPercent["comment_star"]) > 0, tools.ToFloat64(curMonthCommentPercent["comment_star"]), 5), // 平均评分
			"score_percent_up":      tools.GrowthRate(curMonthCommentPercent["comment_star"], lastMonthCommentPercent["comment_star"]),                                 // 评分升高率
			"average_delivery_time": curMonthCommentPercent["order_delivery_minute"],                                                                                   // 平均配送时间
			"average_delivery_up":   tools.GrowthRate(curMonthCommentPercent["order_delivery_minute"], lastMonthCommentPercent["order_delivery_minute"]),               // 平均配送时间升高率
			"shipper_ranking":       s.GetShipperRank(ShipperID, rankRes),                                                                                              // 配送员排名
			"total_work_day":        curMonthCommentPercent["work_day"],                                                                                                // 工作日
			"total_salary":          curMonthCommentPercent["total_salary"],                                                                                            // 工资
			"total_salary_up":       tools.GrowthRate(curMonthCommentPercent["total_salary"], lastMonthCommentPercent["total_salary"]),                                 // 工资升高率
			"rank":       			 shipper.ShipperRank.Rank,
			"final_score":           shipper.ShipperRank.FinalScore,
		},
		"bottom": map[string]interface{}{ // 统计下面
			"total_order_count":               curMonthCommentPercent["total_order_count"],                                                                 // 订单数量
			"total_order_count_up":            tools.GrowthRate(curMonthCommentPercent["total_order_count"], lastMonthCommentPercent["total_order_count"]), // 订单数量
			"total_shipment_income":           curMonthCommentPercent["amount_order"],                                                                      // 配送收入
			"total_shipment_income_up":        tools.GrowthRate(curMonthCommentPercent["amount_order"], lastMonthCommentPercent["amount_order"]),           // 配送收入
			"attendance_reward_amount":        curMonthCommentPercent["amount_reward"],                                                                     // 考勤奖励
			"attendance_reward_up":            tools.GrowthRate(curMonthCommentPercent["amount_reward"], lastMonthCommentPercent["amount_reward"]),         // 考勤奖励
			"total_complaint_count":           curMonthCommentPercent["complain_count"],                                                                    // 投诉数量
			"total_complaint_count_up":        tools.GrowthRate(curMonthCommentPercent["complain_count"], lastMonthCommentPercent["complain_count"]),       // 投诉数量
			"count_comment_bad":               curMonthCommentPercent["count_comment_bad"],                                                                 // 投诉数量
			"comment_good_count":               curMonthCommentPercent["comment_good_count"],                                                                 // 好评数量
			"count_comment_bad_up":            tools.GrowthRate(curMonthCommentPercent["count_comment_bad"], lastMonthCommentPercent["count_comment_bad"]), // 投诉数量
			"total_complaint_amount":          curMonthCommentPercent["amount_complain"],                                                                   // 投诉金额
			"total_complaint_amount_up":       tools.GrowthRate(curMonthCommentPercent["amount_complain"], lastMonthCommentPercent["amount_complain"]),     // 投诉金额
			"total_late_order_count":          curMonthCommentPercent["late_count"],                                                                        // 迟到次数
			"total_late_order_count_up":       tools.GrowthRate(curMonthCommentPercent["late_count"], lastMonthCommentPercent["late_count"]),               // 迟到次数
			"total_late_order_amount":         curMonthCommentPercent["amount_late"],                                                                       // 迟到扣款
			"total_late_order_amount_up":      tools.GrowthRate(curMonthCommentPercent["amount_late"], lastMonthCommentPercent["amount_late"]),             // 迟到扣款
			"total_amount_tips":               curMonthCommentPercent["amount_tips"],                                                                       // 迟到扣款
			"total_amount_tips_up":            tools.GrowthRate(curMonthCommentPercent["amount_tips"], lastMonthCommentPercent["amount_tips"]),
			"total_amount_special_time":       curMonthCommentPercent["amount_special_time"], // 迟到扣款
			"total_amount_special_time_up":    tools.GrowthRate(curMonthCommentPercent["amount_special_time"], lastMonthCommentPercent["amount_special_time"]),
			"total_amount_special_weather":    curMonthCommentPercent["amount_special_weather"], // 迟到扣款
			"total_amount_special_weather_up": tools.GrowthRate(curMonthCommentPercent["amount_special_weather"], lastMonthCommentPercent["amount_special_weather"]),
			"total_amount_comment_good":       curMonthCommentPercent["amount_comment_good"], // 迟到扣款
			"total_amount_comment_good_up":    tools.GrowthRate(curMonthCommentPercent["amount_comment_good"], lastMonthCommentPercent["amount_comment_good"]),
			"total_amount_comment_bad":        curMonthCommentPercent["amount_comment_bad"], // 迟到扣款
			"total_amount_comment_bad_up":     tools.GrowthRate(curMonthCommentPercent["amount_comment_bad"], lastMonthCommentPercent["amount_comment_bad"]),
			"total_amount_punishment":         curMonthCommentPercent["punishment"], // 迟到扣款
			"total_amount_punishment_up":      tools.GrowthRate(curMonthCommentPercent["punishment"], lastMonthCommentPercent["punishment"]),
			"total_amount_invite_user": 	   curMonthCommentPercent["amount_invite_user"], // 客户介绍
			"total_amount_invite_user_up": 	   tools.GrowthRate(curMonthCommentPercent["amount_invite_user"], lastMonthCommentPercent["amount_invite_user"]),
			"total_amount_order_tips": 	   curMonthCommentPercent["amount_order_tips"], // 客户 下单
			"total_amount_order_tips_up": 	   tools.GrowthRate(curMonthCommentPercent["amount_order_tips"], lastMonthCommentPercent["amount_order_tips"]),
			"amount_special_price_order":         curMonthCommentPercent["amount_special_price_order"], // 迟到扣款
			"count_special_price_order":         curMonthCommentPercent["count_special_price_order"], // 迟到扣款
			"total_amount_insurance": 	   curMonthCommentPercent["amount_insurance"], // 保险扣费
			"total_amount_insurance_up": 	tools.GrowthRate(curMonthCommentPercent["amount_insurance"], lastMonthCommentPercent["amount_insurance"]),
		},
	}, nil
}

func (s ShipperSalaryService) GetShipperRank(ShipperID int, shipperRanks []map[string]interface{}) int {
	for index, rank := range shipperRanks {
		if ShipperID == tools.ToInt(rank["shipper_id"]) {
			return index + 1
		}
	}
	return 0
}

// List
//
// @Description: 配送员收入列表
// @Author: Rixat
// @Time: 2023-11-07 02:33:23
// @receiver
// @param c *gin.Context
func (s ShipperSalaryService) List(admin models.Admin, page int, limit int, cityId int, areaId int, kw string, month string, sort string, export bool) (total int64, all []map[string]interface{}, allExport []map[string]interface{}, er1 error) {
	var incomeList []shipmentModels.ShipperIncomeArchive
	var totalCount int64
	db := tools.GetDB()

	startDate, endDate := tools.GetMonthStartAndEndWithParam(month, "Y-m")
	myModel := db.Model(incomeList).
		Scopes(scopes.CityArea(cityId, areaId)).
		Where("date between ? and ?", startDate, endDate)
	if cityId > 0 {
		myModel = myModel.Where("city_id =?", cityId)
	}
	if areaId > 0 {
		myModel = myModel.Where("area_id =?", areaId)
	}


	kw = strings.TrimSpace(kw)
	if len(kw) > 0 {
		myModel = myModel.Where("(shipper_name like ? or shipper_mobile like ? )", "%"+kw+"%", "%"+kw+"%")
	}
	// 工资，订单收入，奖励，罚款，特价活动
	myModel.Group("shipper_id").Count(&totalCount)
	fields := `
	id,city_id,
	area_id,
	shipper_id,
	shipper_name,
	shipper_mobile,
	sum(success_count) as success_count,
	sum(reward+other) as other,
	sum(complain_count) as complain_count,
	sum(shipment) as shipment,
	sum(punishment) as punishment,
	sum(late_count) as late_count,
	sum(amount_order) as amount_order,
	sum(amount_reward) as amount_reward,
	sum(amount_tips) as amount_tips,
	sum(amount_special_time) as amount_special_time,
	sum(amount_special_weather) as amount_special_weather,
	sum(amount_comment_good) as amount_comment_good,
	sum(amount_comment_bad) as amount_comment_bad,
	sum(amount_complain) as amount_complain,
	sum(amount_late) as amount_late,
	sum(amount_cancel) as amount_cancel,
	sum(amount_fail) as amount_fail,
	sum(amount_special_price_order) as amount_special_price_order,
	sum(count_special_price_order) as count_special_price_order,
	floor(avg(order_delivery_minute)) as order_delivery_minute,
	floor(avg(comment_star)) as comment_star,
	sum(amount_invite_user) as amount_invite_user,
	sum(amount_order_tips) as amount_order_tips,
	sum(amount_insurance) as amount_insurance
	`
	if totalCount > 0 {
		if page != 0 && limit != 0 {
			myModel = myModel.
				Scopes(scopes.Page(page, limit))
		}
		myModel.
			Order(sort).
			Preload("City").
			Preload("Area").
			Group("shipper_id").
			Select(fields).
			Find(&incomeList)
	}
	// 格式化列表内容
	items := make([]map[string]interface{}, 0)
	excelItems := make([]map[string]interface{}, 0)
	for _, income := range incomeList {
		cityName := income.City.NameUg
		areaName := income.Area.NameUg
		if s.language != "ug" {
			cityName = income.City.NameZh
			areaName = income.Area.NameZh
		}
		reward :=  income.AmountReward+income.AmountTips+income.AmountSpecialTime+income.AmountSpecialWeather+income.AmountCommentGood+income.CommentStar+income.AmountInviteUser+income.AmountOrderTips
		punishment := income.AmountCommentBad+income.AmountComplain+income.AmountLate
		salary := income.AmountOrder+reward+punishment+income.AmountInsurance
		items = append(items, map[string]interface{}{
			"id":                     income.ID,
			"city_name":              cityName,
			"area_name":              areaName,
			"order_count":            income.Success,
			"shipment":               income.Shipment,
			"complain_count":         income.ComplainCount,
			"other":                  reward,
			"punishment":             punishment,
			"late_count":             income.Late,
			"shipper_id":             income.ShipperID,
			"shipper_name":           income.ShipperName,
			"shipper_mobile":         income.ShipperMobile,
			"date":                   startDate + "~" + endDate,
			"amount_order":           income.AmountOrder,
			"amount_reward":          income.AmountReward,
			"amount_tips":            income.AmountTips,
			"amount_special_time":    income.AmountSpecialTime,
			"amount_special_weather": income.AmountSpecialWeather,
			"amount_comment_good":    income.AmountCommentGood,
			"amount_comment_bad":     income.AmountCommentBad,
			"amount_complain":        income.AmountComplain,
			"amount_late":            income.AmountLate,
			"amount_cancel":          income.AmountCancel,
			"amount_fail":            income.AmountFail,
			"order_delivery_minute":  income.OrderDeliveryMinute,
			"comment_star":           income.CommentStar,
			"amount_invite_user" 	: income.AmountInviteUser,
			"amount_order_tips"     : income.AmountOrderTips,
			"amount_insurance"      :income.AmountInsurance,
			"count_special_price_order":  income.CountSpecialPriceOrder,
			"amount_special_price_order": income.AmountSpecialPriceOrder,
			"salary": salary,
		})
		if export {
			cityName = income.City.NameZh
			areaName = income.Area.NameZh
			excelItems = append(excelItems, map[string]interface{}{
				"ID":     income.ID,
				"城市":     cityName,
				"区域":     areaName,
				"订单数量":   income.Success,
				"配送收入":   tools.ToPrice(tools.ToFloat64(income.Shipment) / 100),
				"投诉数量":   income.ComplainCount,
				"其他":     tools.ToPrice(tools.ToFloat64(income.Other) / 100),
				"惩罚":     tools.ToPrice(tools.ToFloat64(income.Punishment) / 100),
				"迟到数量":   income.Late,
				"配送员姓名":  income.ShipperName,
				"配送员手机":  income.ShipperMobile,
				"日期":     startDate + "~" + endDate,
				"订单收入":   tools.ToPrice(tools.ToFloat64(income.AmountOrder) / 100),
				"奖励收入":   tools.ToPrice(tools.ToFloat64(income.AmountReward) / 100),
				"打赏收入":   tools.ToPrice(tools.ToFloat64(income.AmountTips) / 100),
				"特殊时间收入": tools.ToPrice(tools.ToFloat64(income.AmountSpecialTime) / 100),
				"特殊天气收入": tools.ToPrice(tools.ToFloat64(income.AmountSpecialWeather) / 100),
				"好评收入":   tools.ToPrice(tools.ToFloat64(income.AmountCommentGood) / 100),
				"差评惩罚":   tools.ToPrice(tools.ToFloat64(income.AmountCommentBad) / 100),
				"投诉惩罚":   tools.ToPrice(tools.ToFloat64(income.AmountComplain) / 100),
				"迟到惩罚":   tools.ToPrice(tools.ToFloat64(income.AmountLate) / 100),
				"客户介绍收入":   tools.ToPrice(tools.ToFloat64(income.AmountInviteUser) / 100),
				"客户下单收入":   tools.ToPrice(tools.ToFloat64(income.AmountOrderTips) / 100),
				"保险扣费":   tools.ToPrice(tools.ToFloat64(income.AmountInsurance) / 100),
				"最终工资":   tools.ToPrice(tools.ToFloat64(salary) / 100),
			})
		}
	}

	return totalCount, items, excelItems, nil
}

// List
//
// @Description: 配送员收入列表 头部信息
// @Author: rozimamat
// @Time: 2023-11-07 02:33:23
// @receiver
// @param c *gin.Context
func (s ShipperSalaryService) ListHead(admin models.Admin, cityId int, areaId int, month string) map[string]interface{} {

	start := carbon.ParseByFormat(month, "Y-m").Format("Y-m-d")
	end := carbon.ParseByFormat(month, "Y-m").AddMonth().AddDays(-1).Format("Y-m-d")

	oneMonthBeforeStart := carbon.ParseByFormat(month, "Y-m").AddMonths(-1).Format("Y-m-d")
	oneMonthBeforeEnd := carbon.ParseByFormat(month, "Y-m").AddDays(-1).Format("Y-m-d")

	var income shipmentModels.ShipperIncomeArchive
	var incomeOneMonthBefore shipmentModels.ShipperIncomeArchive

	db := tools.GetDB()

	myModel := db.Model(income)

	oneMonthBeforeModel := db.Model(incomeOneMonthBefore)

	if cityId > 0 {
		myModel = myModel.Where("city_id =?", cityId)
		oneMonthBeforeModel = oneMonthBeforeModel.Where("city_id =?", cityId)
	}
	if areaId > 0 {
		myModel = myModel.Where("area_id =?", areaId)
		oneMonthBeforeModel = oneMonthBeforeModel.Where("area_id =?", areaId)
	}
	myModel = myModel.Where("date between ? and ?", start, end)
	oneMonthBeforeModel = oneMonthBeforeModel.Where("date between ? and ?", oneMonthBeforeStart, oneMonthBeforeEnd)

	shipperCount := int64(0)

	shipperCountOneMonthBefore := int64(0)
	oneMonthBeforeModel.Count(&shipperCountOneMonthBefore)
	fields := `
	id,city_id,area_id,shipper_name,
	sum(success_count) as success_count,
	sum(reward+other) as other,
	sum(complain_count) as complain_count,
	sum(shipment) as shipment,
	sum(punishment) as punishment,
	sum(late_count) as late_count,
	sum(amount_order) as amount_order,
	sum(amount_reward) as amount_reward,
	sum(amount_tips) as amount_tips,
	sum(amount_special_time) as amount_special_time,
	sum(amount_special_weather) as amount_special_weather,
	sum(amount_comment_good) as amount_comment_good,
	sum(amount_comment_bad) as amount_comment_bad,
	sum(amount_complain) as amount_complain,
	sum(amount_late) as amount_late,
	sum(amount_cancel) as amount_cancel,
	sum(amount_fail) as amount_fail,
	floor(avg(order_delivery_minute)) as order_delivery_minute,
	floor(avg(comment_star)) as comment_star,
	sum(count_comment_bad) as count_comment_bad,
	sum(count_complain) as count_complain,
	sum(amount_invite_user) as amount_invite_user,
	sum(amount_order_tips) as amount_order_tips,
	sum(count_complain) as count_complain,
	sum(amount_special_price_order) as amount_special_price_order,
	sum(count_special_price_order) as count_special_price_order,
	sum(amount_insurance) as amount_insurance

	`
	myModel.Select(fields).
		Find(&income)

	oneMonthBeforeModel.Select(fields).
		Find(&incomeOneMonthBefore)

	myModel.Group("shipper_id").Count(&shipperCount)
	oneMonthBeforeModel.Group("shipper_id").Count(&shipperCountOneMonthBefore)

	headMap := make(map[string]interface{})

	headMap["id"] = income.ID
	headMap["city_id"] = income.CityID
	headMap["area_id"] = income.AreaID
	headMap["shipper_count"] = shipperCount
	headMap["shipper_count_before"] = shipperCountOneMonthBefore
	var shipperCountRate float64
	if shipperCount != 0 || shipperCountOneMonthBefore != 0 {
		shipperCountRate = tools.GrowthRate(shipperCount, shipperCountOneMonthBefore)
	}
	headMap["shipper_count_rate"] = shipperCountRate

	headMap["order_count"] = income.Success
	headMap["order_count_before"] = incomeOneMonthBefore.Success
	var orderCountRate float64
	if income.Success != 0 || incomeOneMonthBefore.Success != 0 {
		orderCountRate = tools.GrowthRate(income.Success, incomeOneMonthBefore.Success)
	}
	headMap["order_count_rate"] = orderCountRate

	headMap["shipment"] = income.Shipment
	headMap["shipment_before"] = incomeOneMonthBefore.Shipment
	var shipmentRate float64
	if income.Shipment != 0 || incomeOneMonthBefore.Shipment != 0 {
		shipmentRate = tools.GrowthRate(income.Shipment, incomeOneMonthBefore.Shipment)
	}
	headMap["shipment_rate"] = shipmentRate

	headMap["other"] = income.Other
	headMap["other_before"] = incomeOneMonthBefore.Other
	var otherRate float64
	if income.Other != 0 || incomeOneMonthBefore.Other != 0 {
		otherRate = tools.GrowthRate(income.Other, incomeOneMonthBefore.Other)
	}
	headMap["other_rate"] = otherRate

	headMap["complain"] = income.ComplainCount
	headMap["complain_before"] = incomeOneMonthBefore.ComplainCount
	var complainRate float64
	if income.ComplainCount != 0 || incomeOneMonthBefore.ComplainCount != 0 {
		complainRate = tools.GrowthRate(income.ComplainCount, incomeOneMonthBefore.ComplainCount)
	}
	headMap["complain_rate"] = complainRate

	headMap["punishment"] = income.Punishment
	headMap["punishment_before"] = incomeOneMonthBefore.Punishment
	var punishmentRate float64
	if income.Punishment != 0 || incomeOneMonthBefore.Punishment != 0 {
		punishmentRate = tools.GrowthRate(income.Punishment, incomeOneMonthBefore.Punishment)
	}
	headMap["punishment_rate"] = punishmentRate

	headMap["late_count"] = income.Late
	headMap["late_count_before"] = incomeOneMonthBefore.Late
	var lateRate float64
	if income.Late != 0 || incomeOneMonthBefore.Late != 0 {
		lateRate = tools.GrowthRate(income.Late, incomeOneMonthBefore.Late)
	}
	headMap["late_rate"] = lateRate
	headMap["amount_order"] = income.AmountOrder
	headMap["amount_reward"] = income.AmountReward
	headMap["amount_tips"] = income.AmountTips
	headMap["amount_special_time"] = income.AmountSpecialTime
	headMap["amount_special_weather"] = income.AmountSpecialWeather
	headMap["amount_comment_good"] = income.AmountCommentGood
	headMap["amount_comment_bad"] = income.AmountCommentBad
	headMap["amount_complain"] = income.AmountComplain
	headMap["amount_late"] = income.AmountLate
	headMap["amount_cancel"] = income.AmountCancel
	headMap["amount_fail"] = income.AmountFail
	headMap["order_delivery_minute"] = income.OrderDeliveryMinute
	headMap["count_comment_bad"] = income.CountCommentBad
	headMap["count_complain"] = income.CountComplain
	headMap["amount_invite_user"] = income.AmountInviteUser
	headMap["amount_order_tips"] = income.AmountOrderTips
	headMap["amount_insurance"] = income.AmountInsurance
	headMap["amount_special_price_order"] = income.AmountSpecialPriceOrder
	headMap["count_special_price_order"] = income.CountSpecialPriceOrder
	return headMap
}
