package cms

import (
	"context"
	"errors"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/scopes"
	"mulazim-api/services"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type ShipperInsuranceService struct {
	langUtil *lang.LangUtil
	language string
	BaseService services.BaseService
}
// 配送员 保险模块
func (s ShipperInsuranceService) GetShipperRealNameList(cityId int,
														areaId int,
														startDate string,
														endDate string,
														searchField string,
														state int,
														page int,
														limit int,
														sort string,
														) (int64,[]map[string]interface{},map[string]interface{}) {
	 // 查询条件
	 db :=tools.GetDB()
	query := db.Model(models.SelfSignMerchantInfo{}).Where("t_self_sign_merchant_info.type=?",2) //配送员
	query2 := db.Model(models.SelfSignMerchantInfo{}).Where("t_self_sign_merchant_info.type=?",2) //配送员
	if cityId > 0 {
		query.Where("t_self_sign_merchant_info.city_id = ?", cityId)
		query2.Where("t_self_sign_merchant_info.city_id = ?", cityId)
	}
	if areaId > 0 {
		query.Where("t_self_sign_merchant_info.area_id = ?", areaId)
		query2.Where("t_self_sign_merchant_info.area_id = ?", areaId)
	}
	if len(searchField) > 0 {
		query.Where("(t_self_sign_merchant_info.legal_name like ? or t_self_sign_merchant_info.mer_mobile like ? )","%"+searchField+"%","%"+searchField+"%")
		query2.Where("(t_self_sign_merchant_info.legal_name like ? or t_self_sign_merchant_info.mer_mobile like ? )","%"+searchField+"%","%"+searchField+"%")
	}
	if len(startDate)>0 && len(endDate)>0{
		query.Where("t_self_sign_merchant_info.updated_at between ? and ?", startDate+" 00:00:00",endDate+" 23:59:59")
		query2.Where("t_self_sign_merchant_info.updated_at between ? and ?", startDate+" 00:00:00",endDate+" 23:59:59")
	}
	if state > -1 {
		query.Where("t_self_sign_merchant_info.state = ?", state)
	}
	
	
	results :=make([]map[string]interface{},0)	 
	// 获取数据
	var totalCount int64
	query.Count(&totalCount)
	var selfSignList []models.SelfSignMerchantInfo
	headerFields :="sum(if(t_self_sign_merchant_info.state=0,1,0)) as state0"
	headerFields +=",sum(if(t_self_sign_merchant_info.state=1,1,0)) as state1"
	headerFields +=",sum(if(t_self_sign_merchant_info.state=2,1,0)) as state2"
	headerFields +=",sum(if(t_self_sign_merchant_info.state=3,1,0)) as state3"
	type HeaderStruct struct{
		State0 int `gorm:"state0"` 
		State1 int `gorm:"state1"` 
		State2 int `gorm:"state2"` 
		State3 int `gorm:"state3"` 
	}
	var headerItem HeaderStruct
	query2.Joins("left join t_admin ON t_admin.id = t_self_sign_merchant_info.restaurant_id ").Where("t_admin.state = ?",1).Select(headerFields).Scan(&headerItem)
	headers := map[string]interface{}{
		"state0":headerItem.State0,
		"state1":headerItem.State1,
		"state2":headerItem.State2,
		"state3":headerItem.State3,
	}
	if totalCount > 0 {
		query=query.Joins("left join t_admin ON t_admin.id = t_self_sign_merchant_info.restaurant_id ").Where("t_admin.state = ?",1).Select("t_self_sign_merchant_info.*").Scopes(scopes.Page(page, limit)).
			Preload("City").
			Preload("Area")
			// Preload("Admin")
		if len(sort) > 0{
			query.Order("t_self_sign_merchant_info."+sort).
			Find(&selfSignList)
		}else{
			query.Order("t_self_sign_merchant_info.id desc").
			Find(&selfSignList)
		}	
			
	}
	for _, value := range selfSignList {
			itemMap := map[string]interface{}{
				"id":                       value.Id,
				"city_name":                tools.GetNameByLang(value.City, s.language),
				"area_name":                tools.GetNameByLang(value.Area, s.language),
				"legal_name":          		value.LegalName,
				"mer_mobile":				value.MerMobile,
				// "user_name":				value.Admin.Name,
				"sex":						value.MerSex,
				"state":          			value.State,
				"state_name":          		s.langUtil.TArr("real_name_states")[value.State],
				"created_at":     			tools.TimeFormatYmdHis(&value.CreatedAt),
		}
		results = append(results,itemMap)
	}
	return totalCount,results,headers
}


//
// NewShipperInsuranceService
//  @Description: 初始化ShipperInsuranceService
//  @param c
//  @return *ShipperInsuranceService
//
func NewShipperInsuranceService(c *gin.Context) *ShipperInsuranceService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	ShipperInsuranceService := ShipperInsuranceService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &ShipperInsuranceService
}


//实名详情获取
func (s ShipperInsuranceService) GetShipperRealNameDetail(id int) (map[string]interface{}) {
	// 查询条件
	db :=tools.GetDB()
	var selfSignInfo models.SelfSignMerchantInfo
	db.Model(selfSignInfo).Where("id=?",id).
	Preload("BankProvince").
	Preload("BankCity").
	Preload("BankArea").
	// Preload("Admin").
	// Preload("City").
	// Preload("Area").
	Preload("Bank").
	Preload("SelfSignImages","type=2 and deleted_at is null").
	Preload("VerifyLogs",func(db *gorm.DB) *gorm.DB {
		return db.Order("id DESC") // 按照创建时间倒序排序文章
	}).
	Preload("VerifyLogs.Verifyer").
	Find(&selfSignInfo)

	var admin models.Admin
	db.Model(&models.Admin{}).Where("id = ?",selfSignInfo.RestaurantId).Find(&admin)
	
	// 格式化返回内容
	stateNAme :=s.langUtil.TArr("real_name_states")[selfSignInfo.State]
	result := map[string]interface{}{
		"id":selfSignInfo.Id,
		"created_at":tools.TimeFormatYmdHis(&selfSignInfo.CreatedAt),
		"legal_name":selfSignInfo.LegalName,
		"state":selfSignInfo.State,
		"state_name":stateNAme,
		"alter_state":selfSignInfo.AlterState,
		"alter_state_name":s.langUtil.TArr("collection_merchant_alter_state")[selfSignInfo.AlterState],
		
		// 身份证信息
		"idcard_info":map[string]interface{}{
			"idcard_image_front":s.GetImageFullUrlByDocType(selfSignInfo.SelfSignImages,"0001"),
			"idcard_image_back":s.GetImageFullUrlByDocType(selfSignInfo.SelfSignImages,"0011"),
			"idcard_name":selfSignInfo.MerIdcardName,
			"idcard_num":selfSignInfo.MerIdcardNum,
			"idcard_start":tools.TimeFormatYmd(selfSignInfo.MerIdcardStart),
			"idcard_end":tools.TimeFormatYmd(selfSignInfo.MerIdcardEnd),
			"sex":selfSignInfo.MerSex,
			"mer_mobile":selfSignInfo.MerMobile,
			"address":selfSignInfo.LegalmanHomeAddr,
		},
		// 账户信息
		"account_info":map[string]interface{}{
			"bankcard_image_front":s.GetImageFullUrlByDocType(selfSignInfo.SelfSignImages,"0025"),
			"bankcard_image_back":s.GetImageFullUrlByDocType(selfSignInfo.SelfSignImages,"0026"),
			"bank_acct_type": selfSignInfo.BankAcctType,
			"bankcard_num":selfSignInfo.BankAcctNum,
			"bank_name":tools.GetNameByLang(selfSignInfo.Bank,s.language),
			"bank_branch_name":selfSignInfo.BankBranchName,
			"bank_branch_code":selfSignInfo.BankBranchCode,
			"bank_bind_mobile":selfSignInfo.BankBindMobile,
			// "bank_":selfSignInfo.BankArea.Code,
			"bank_province_name":tools.GetNameByLang(selfSignInfo.BankProvince,s.language),
			"bank_city_name":tools.GetNameByLang(selfSignInfo.BankCity,s.language),
			"bank_area_name":tools.GetNameByLang(selfSignInfo.BankArea,s.language),
			"bank_province_code":selfSignInfo.BankProvince.Code,
		},
		"admin":map[string]interface{}{
			"id":admin.ID,
			"name":admin.Name,
			"mobile":admin.Mobile,
		},
	}
	// 审核日志
	resVerifyLog := make([]map[string]interface{},0)
	verifyLogs := selfSignInfo.VerifyLogs
	for _, verifyLog := range verifyLogs {
		stateName := s.langUtil.TArr("real_name_states")[verifyLog.State]
		pageName := s.langUtil.TArr("page_errors")[verifyLog.StepNum]
		if verifyLog.StepNum == 0 {
			pageName = ""
		}
		
		resVerifyLog = append(resVerifyLog, map[string]interface{}{
			"id":verifyLog.ID,
			"verifyer_name":verifyLog.Verifyer.Name,
			"state":verifyLog.State,
			"state_name":stateName,
			"remark":verifyLog.Remark,
			"verifyer_id":verifyLog.VarifyerID,
			"page_name":pageName,
			"step_num":verifyLog.StepNum,
			"operration_type":verifyLog.OperationType,
			"created_at":tools.TimeFormatYmdHis(&verifyLog.CreatedAt),
		})
	}
	result["verifyLogs"] = resVerifyLog
	return result
}
//获取图片
func (t ShipperInsuranceService) GetImageFullUrlByDocType(images []models.SelfSignImages,docType string) string {
	for _,image := range images {
		if image.DocType == docType {
			return tools.AddCdn(image.MlzFilePath)
		}
	}
	return ""
}
//获取推荐词语
func (s ShipperInsuranceService) GetVerifyRecommendText(kw string) []string{
	var text []string
	db :=tools.GetDB()
	db.Model(&models.SelfSignVerifyText{}).Select("content").Where("state = 1 and content like ?", "%"+kw+"%").Limit(10).Scan(&text)
	return text
}
//拒绝审核
func (s ShipperInsuranceService) RefuseRealName(adminID int,ID int,VerifyContent string) error{
	db :=tools.GetDB()
	var selfSignMerchantInfo models.SelfSignMerchantInfo
	db.Model(selfSignMerchantInfo).Where("id=?",ID).Scan(&selfSignMerchantInfo)
	if selfSignMerchantInfo.Id == 0 {
		return errors.New("not_found")
	}
	// 更新审核内容
	res := db.Model(&models.SelfSignMerchantInfo{}).Where("id=?",ID).UpdateColumns(map[string]interface{}{
		"verify_content":VerifyContent,
		"state":2,
		"wechat_verify_state":0,
	})
	if res.Error != nil {
		return errors.New("update_error")
	}
	// 审核日志添加数据
	verifyContentArr := tools.StringToMapArr(VerifyContent)
	for _,v := range verifyContentArr {
		stepNum := tools.ToInt(v["step_num"])
		pageName := v["pageName"]
		inputs := v["inputs"].([]interface{})
		for _,input := range inputs{
			inputMap := input.(map[string]interface{})
			err := db.Model(&models.SelfSignVerifyLog{}).Create(&models.SelfSignVerifyLog{
				MerInfoID:ID,
				VarifyerID:adminID,
				StepNum:stepNum,
				PageName:pageName.(string),
				Remark:inputMap["remark"].(string),
				OperationType:1,
				State: 2,
			}).Error
			if err != nil {
				return errors.New("update_error")
			}
			// 拒绝审核关键字内容更新
			if tools.ToInt(inputMap["remark_state"]) == 1{
				var count int64
				db.Model(&models.SelfSignVerifyText{}).Where("content=?",inputMap["remark"].(string)).Count(&count)
				if count == 0{
					err := db.Model(&models.SelfSignVerifyText{}).Create(&models.SelfSignVerifyText{
						Content:inputMap["remark"].(string),
						State:"1",
						CreatedAt:time.Now(),
					}).Error
					if err != nil {
						return errors.New("failed")
					}
				}
			}
		}
	}
	return nil;

}
//审核通过
func (s ShipperInsuranceService) ApproveRealName(adminID int,ID int) error{
	db :=tools.GetDB()
	var info models.SelfSignMerchantInfo
	db.Model(&models.SelfSignMerchantInfo{}).Where("id=?", ID).Find(&info)
	idCardNum :=info.MerIdcardNum
	var info2 models.SelfSignMerchantInfo
	var shipper models.Admin
	db.Model(&models.Admin{}).Where("id = ?",info.RestaurantId).Find(&shipper)

	if shipper.ID == 0 && shipper.AdminAreaID == 0 {
		return fmt.Errorf("no_shipper_info")
	}
	db.Model(&models.SelfSignMerchantInfo{}).
	Where("restaurant_id != ? and type = ? and mer_idcard_num = ? and state =? and area_id = ?", info.RestaurantId,2,idCardNum,3,shipper.AdminAreaID).Find(&info2)
	if info2.Id > 0{ //存在重复的身份证号
		return fmt.Errorf("idcard_repeat")
	}
	// 日志记录数据
	err := db.Model(models.SelfSignVerifyLog{}).Create(&models.SelfSignVerifyLog{
		MerInfoID: ID,
		VarifyerID: adminID,
		StepNum: 0,
		PageName: "0",
		Remark: "审核通过",
		OperationType: 1,
		State: 3,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}).Error
	if err != nil {
		tools.Logger.Error("实名认证信息保存失败",err)
		return errors.New("fail")
	}
	db.Model(&models.SelfSignMerchantInfo{}).Where("id=?", ID).UpdateColumns(&map[string]interface{}{
		"state":3,
	})
	return nil;

}


// 配送员 保险列表
func (s ShipperInsuranceService) GetList(admin models.Admin,cityId int,areaId int,startDate string,endDate string,searchField string,state int,page int,
	limit int,sort string,export bool,shipperId int) (int64,[]map[string]interface{},map[string]interface{}) {
	// 查询条件
	db :=tools.GetDB()
   query := db.Model(models.ShipperInsuranceLog{})
   query2 := db.Model(models.ShipperInsuranceLog{})
   if cityId > 0 {
	   query.Where("t_shipper_insurance_log.city_id = ?", cityId)
	   query2.Where("t_shipper_insurance_log.city_id = ?", cityId)
   }
   if areaId > 0 {
	   query.Where("t_shipper_insurance_log.area_id = ?", areaId)
	   query2.Where("t_shipper_insurance_log.area_id = ?", areaId)
   }
   if len(searchField) > 0 {
		var shippers []int
		q2 :=db.Model(&models.SelfSignMerchantInfo{}).Where("type = ?",2).
		Where("legal_name like ? or mer_mobile like ? ","%"+searchField+"%","%"+searchField+"%").
			Where("deleted_at is null")
		if cityId > 0 {
			q2.Where("t_self_sign_merchant_info.city_id = ?", cityId)
		}
		if areaId > 0 {
			q2.Where("t_self_sign_merchant_info.area_id = ?", areaId)
		}	
		q2.Pluck("restaurant_id",&shippers)		
		if len(shippers) > 0 {
			query.Where("t_shipper_insurance_log.shipper_id in ? ",shippers)
			query2.Where("t_shipper_insurance_log.shipper_id in ? ",shippers)
		}
   }
   if len(startDate)>0 && len(endDate)>0{
	   query.Where("t_shipper_insurance_log.created_at between ? and ?", startDate+" 00:00:00",endDate+" 23:59:59")
	   query2.Where("t_shipper_insurance_log.created_at between ? and ?", startDate+" 00:00:00",endDate+" 23:59:59")
   }
   
   if state > -1 {
	   query.Where("t_shipper_insurance_log.state = ?", state)
   }

   if shipperId > 0 { //查询一个配送员的数据
		query.Where("t_shipper_insurance_log.shipper_id = ?", shipperId)
		query2.Where("t_shipper_insurance_log.shipper_id = ?", shipperId)
	}
   
   results :=make([]map[string]interface{},0)	 
   // 获取数据
   var totalCount int64
   query.Count(&totalCount)
   var selfSignList []models.ShipperInsuranceLog

   headerFields :="sum(if(t_shipper_insurance_log.state=0,1,0)) as state0"
	headerFields +=",sum(if(t_shipper_insurance_log.state=1,1,0)) as state1"
	headerFields +=",sum(if(t_shipper_insurance_log.state=2,1,0)) as state2"
	headerFields +=",sum(if(t_shipper_insurance_log.state=3,1,0)) as state3"
	headerFields +=",sum(if(t_shipper_insurance_log.state=4,1,0)) as state4"
	headerFields +=",sum(if(t_shipper_insurance_log.state=5,1,0)) as state5"
	type HeaderStruct struct{
		State0 int `gorm:"state0"` 
		State1 int `gorm:"state1"` 
		State2 int `gorm:"state2"` 
		State3 int `gorm:"state3"` 
		State4 int `gorm:"state4"`
		State5 int `gorm:"state5"`
	}
	var headerItem HeaderStruct
	query2.Select(headerFields).Scan(&headerItem)
	headers := map[string]interface{}{
		"state0":headerItem.State0,
		"state1":headerItem.State1,
		"state2":headerItem.State2,
		"state3":headerItem.State3,
		"state4":headerItem.State4,
		"state5":headerItem.State5,
	}

   if totalCount > 0 {
	if export { //excel 导出
		query = query.Select("t_shipper_insurance_log.*").
			Preload("Admin").
			Preload("RealNameInfo","type = ? ",2).
			Preload("City").
			Preload("Area")
			if len(sort)  > 0 {
				query.Order(sort).
				Find(&selfSignList)
			}else{
				query.Order("id desc").
				Find(&selfSignList)
			}
			var ids []int64	
			for _, value := range selfSignList {
				if value.State == 1 { //待上传到保险
					ids = append(ids, int64(value.ID))
				}
			}
			if !(admin.IsDealer() || admin.IsDealerSub()){
				if len(ids) > 0 && shipperId == 0{
					//excel 下载时间 记录未等待发送到保险的时间
					db.Model(models.ShipperInsuranceLog{}).Where("id in ?",ids).Updates(&map[string]interface{}{
						"state":2,//2:等待发送到保险,
						"upload_time":carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
					})
				}
			}
			

	}else{
		query = query.Select("t_shipper_insurance_log.*").
		Scopes(scopes.Page(page, limit)).
		Preload("Admin").
		Preload("RealNameInfo","type = ? ",2).
		Preload("City").
		Preload("Area")
	  if len(sort)  > 0 {
		 query.Order(sort).
		 Find(&selfSignList)
	  }else{
		 query.Order("id desc").
		 Find(&selfSignList)
	  }
	}
	
		   
   }
   for index, value := range selfSignList {
		uploadTime :=""
		if value.UploadTime != nil {
			uploadTime = value.UploadTime.Format("2006-01-02 15:04:05")
		}
		confirmTime :=""
		if value.ConfirmTime != nil {
			confirmTime = value.ConfirmTime.Format("2006-01-02 15:04:05")
		}
		itemMap := map[string]interface{}{
			"id":                       value.ID,
			"city_name":                tools.GetNameByLang(value.City, s.language),
			"area_name":                tools.GetNameByLang(value.Area, s.language),
			"shipper_user_name":          		   value.Admin.Name,
			"shipper_id":               value.ShipperID,
			"name":          		   value.RealNameInfo.MerIdcardName,
			"insurance_name":	      value.InsuranceNameZH,
			"mobile":				value.RealNameInfo.MerMobile,
			"amount":				value.Amount,
			"state":          			value.State,
			"state_name":          		s.langUtil.TArr("insurance_state")[value.State],
			"created_at":      tools.TimeFormatYmdHis(&value.CreatedAt),
			"date":                carbon.Parse(value.Date,configs.AsiaShanghai).Format("Y-m-d"),
			"start_time":                carbon.Parse(value.StartTime,configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			"end_time":                 carbon.Parse(value.EndTime,configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			"upload_time":              uploadTime,
			"confirm_time":             confirmTime,

		}
		if export {
			if shipperId > 0 {
				itemMap = map[string]interface{}{
					"城市":value.City.NameZh,
					"区域":value.Area.NameZh,
					"姓名":value.RealNameInfo.MerIdcardName,
					"身份证号":value.RealNameInfo.MerIdcardNum,
					"手机号":value.RealNameInfo.BankBindMobile,
					"日期":carbon.Parse(value.Date,configs.AsiaShanghai).Format("Y-m-d"),
					"银行卡号":value.RealNameInfo.BankAcctNum,
					"保险名称":value.InsuranceNameZH,
					"保险金额":tools.ToFloat64(value.Amount/100),
					"保险状态":s.langUtil.TArrZh("insurance_state")[value.State],
					"上传时间":uploadTime,
					"确认时间":confirmTime,
					"金额":			tools.ToFloat64(value.Amount)/100,
				}	
			}else{
				
				itemMap = map[string]interface{}{
					// "城市":value.City.NameZh,
					// "区域":value.Area.NameZh,
					// "姓名":value.RealNameInfo.MerIdcardName,
					// "身份证号":value.RealNameInfo.MerIdcardNum,
					// "手机号":value.RealNameInfo.BankBindMobile,
					// "日期":carbon.Parse(value.Date,configs.AsiaShanghai).Format("Y-m-d"),
					// "有效时间开始":carbon.Parse(value.StartTime,configs.AsiaShanghai).Format("Y-m-d H:i:s"),
					// "有效时间结束":carbon.Parse(value.EndTime,configs.AsiaShanghai).Format("Y-m-d H:i:s"),
					// "银行卡号":value.RealNameInfo.BankAcctNum,
					"序号(*)":tools.ToString(index+1),
					"姓名(*)":value.RealNameInfo.MerIdcardName,
					"证件类型(*#)":"01",
					"证件号(*)":value.RealNameInfo.MerIdcardNum,
					"证件有效期（起）":value.RealNameInfo.MerIdcardStart.Format("2006-01-02"),
					"证件有效期（止）":value.RealNameInfo.MerIdcardEnd.Format("2006-01-02"),
					"性别(*#)":"",
					"生日(*)":"",
					"保障层级(*)":"",
					"职业代码(*#)":"",
					"手机":value.RealNameInfo.BankBindMobile,
					"省(#)":"",
					"市(#)":value.City.NameZh,
					"县(#)":value.Area.NameZh,
					"地址":"",
					"办公电话区号":"",
					"办公电话号码":"",
					"办公电话分机":"",
					"家庭电话区号":"",
					"家庭电话号码":"",
					"加保申请日期(*)":carbon.Parse(value.Date,configs.AsiaShanghai).Format("Y-m-d"),
					"国家地区(#)":"",
				}	
			}
			
		}
	   	results = append(results,itemMap)

   }
   return totalCount,results,headers
}



//批量处理保险 
func (s ShipperInsuranceService) ProcessInsurance(adminId int,id string,confirm int,reasonUg string,reasonZh string,BlackList int) (error) {
   db :=tools.GetDB()
   
   tomorrow :=carbon.Now(configs.AsiaShanghai).AddDays(1)
   if confirm == 1 {//保险通过
		db.Model(&models.ShipperInsuranceLog{}).Where("state = ? and date = ?",2,tomorrow.Format("Y-m-d")).Updates(&map[string]interface{}{
			"confirm_time":carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			"state":3,
		})
		 
   }else{
	 //拒绝
	 if len(id) > 0 {
		
		var log models.ShipperInsuranceLog
		db.Model(&models.ShipperInsuranceLog{}).Where(" id = ?",id).Find(&log)

		updateMap :=map[string]interface{}{
			"state":4,
		}
		if BlackList == 1{ //加入黑名单
			updateMap["insurance_state"]="黑名单"
		}
		updateMap["fail_reason_ug"]=reasonUg
		updateMap["fail_reason_zh"]=reasonZh
		db.Model(&models.ShipperInsuranceLog{}).Where("id = ?",id).Updates(&updateMap)
		if BlackList == 1{ //加入黑名单
			db.Model(&models.Admin{}).Where("id = ?",log.ShipperID).Updates(&map[string]interface{}{
				"is_insurance_black":1,//保险黑名单
			})
		}
	
	 }
	 
   }
   
   return nil
}

//自动创建 保险数据
func (s ShipperInsuranceService) CreateInsurance(check bool) (error) {
	db :=tools.GetDB()
	now :=carbon.Now(configs.AsiaShanghai)
	insuranceBeginTime :=s.BaseService.GetAppConfig("insurance_begin_time")
	if len(insuranceBeginTime) > 0 {
		beginTime :=carbon.Parse(insuranceBeginTime,configs.AsiaShanghai)
		if now.Lt(beginTime) { //保险开始时间
			bgTime :=beginTime.Format("Y-m-d H:i:s")
			if s.language == "ug" {
				bgTime =beginTime.Format("d-m-Y H:i:s")
			}
			err := fmt.Errorf(fmt.Sprintf(s.langUtil.T("insurance_before_time_can_not_buy"),bgTime))
			return err
		}
	}
	insuranceStopTime :=s.BaseService.GetAppConfig("insurance_stop_time")
	originalStopTime :=configs.MyApp.InsuranceStopTime
	newTime :=originalStopTime
	if len(insuranceStopTime) > 0 {
		newTime =insuranceStopTime
	}
	stopTime :=carbon.Parse(now.Format("Y-m-d")+" "+newTime,configs.AsiaShanghai)
	if now.Lt(stopTime) && check{ //保险停止时间
		tools.Logger.Info("保险停止时间还没到",stopTime)
		//超过保险停止时间的不能创建
		return nil
	}
	tomorrow :=now.AddDays(1)
	var selfSignInfos []models.SelfSignMerchantInfo
	db.Model(&models.SelfSignMerchantInfo{}).
		Joins("left join t_admin on t_admin.id = t_self_sign_merchant_info.restaurant_id").
		Preload("ShipperInsuranceLogForSelfSign","date = ?",tomorrow.Format("Y-m-d")).
		Preload("Area","insurance_enable = ?",1).
		// Preload("AdminForSelfSign").
		Where("t_self_sign_merchant_info.type = ? and t_self_sign_merchant_info.state = ? and t_admin.state = ? and t_admin.deleted_at is null",2,3,1).
		Select("t_self_sign_merchant_info.restaurant_id,t_self_sign_merchant_info.city_id,t_self_sign_merchant_info.area_id").
		Find(&selfSignInfos)
	
	for _, info := range selfSignInfos {
		if info.Area.ID > 0 && info.ShipperInsuranceLogForSelfSign.ID == 0{
			insuranceMap :=s.BaseService.GetInsuranceConfig()
			db.Create(&models.ShipperInsuranceLog{
				CityID: info.Area.CityID,
				AreaID: info.Area.ID,
				ShipperID:      info.RestaurantId,
				InsuranceNameUG: insuranceMap.NameUg,
				InsuranceNameZH: insuranceMap.NameZh,
				Date:           tomorrow.Format("Y-m-d"),
				StartTime: tomorrow.Format("Y-m-d 00:00:00"),
				EndTime:  tomorrow.Format("Y-m-d 23:59:59"),
				Amount: insuranceMap.Amount,
				State: 1,
			})
		}
	}
	return nil
 }

 //忘记购买保险人数获取
 func (s ShipperInsuranceService) GetInsuranceForgot() int {
	db :=tools.GetDB()

	now :=carbon.Now(configs.AsiaShanghai)
	tomorrow :=now.AddDays(1)
	var selfSignInfos []models.SelfSignMerchantInfo
	db.Model(&models.SelfSignMerchantInfo{}).
		Joins("left join t_admin on t_admin.id = t_self_sign_merchant_info.restaurant_id").
		Preload("ShipperInsuranceLogForSelfSign","date = ?",tomorrow.Format("Y-m-d")).
		Preload("Area","insurance_enable = ?",1).
		// Preload("AdminForSelfSign").
		Where("t_self_sign_merchant_info.type = ? and t_self_sign_merchant_info.state = ? and t_admin.state = ? and t_admin.deleted_at is null",2,3,1).
		Select("t_self_sign_merchant_info.restaurant_id,t_self_sign_merchant_info.city_id,t_self_sign_merchant_info.area_id").
		Find(&selfSignInfos)
	allCount :=0
	for _, info := range selfSignInfos {
		if info.Area.ID > 0 && info.ShipperInsuranceLogForSelfSign.ID == 0{
			allCount++
		}
	}
	return allCount
}


 //购买保险人数获取
 func (s ShipperInsuranceService) GetInsuranceNeedBuy() int64 {
	db :=tools.GetDB()
	tomorrow :=carbon.Now(configs.AsiaShanghai).AddDays(1).Format("Y-m-d")
	allCount :=int64(0)
	db.Model(&models.ShipperInsuranceLog{}).Where(" date = ? and state = ?",tomorrow,2).Count(&allCount)	
	
	return allCount
}



//休息的配送员手动开启购买
func (s ShipperInsuranceService) InsuranceEnableManually(admin models.Admin,id string) (error) {
	db :=tools.GetDB()
	now :=carbon.Now(configs.AsiaShanghai)
	tomorrow :=now.AddDays(1)

	insuranceBeginTime :=s.BaseService.GetAppConfig("insurance_begin_time")
	if len(insuranceBeginTime) > 0 {
		beginTime :=carbon.Parse(insuranceBeginTime,configs.AsiaShanghai)
		if now.Lt(beginTime) { //保险开始时间
			bgTime :=beginTime.Format("Y-m-d H:i:s")
			if s.language == "ug" {
				bgTime =beginTime.Format("d-m-Y H:i:s")
			}
			err := fmt.Errorf(fmt.Sprintf(s.langUtil.T("insurance_before_time_can_not_buy"),bgTime))
			return err
		}
	}
	insuranceStopTime :=s.BaseService.GetAppConfig("insurance_stop_time")
	originalStopTime :=configs.MyApp.InsuranceStopTime
	newTime :=originalStopTime
	if len(insuranceStopTime) > 0 {
		newTime =insuranceStopTime
	}
	stopTime :=carbon.Parse(now.Format("Y-m-d")+" "+newTime,configs.AsiaShanghai)
	if now.Gt(stopTime) && tools.InArray(admin.Type,[]int{constants.ADMIN_TYPE_DEALER,constants.ADMIN_TYPE_DEALER_SUB}){ //保险停止时间  只对 代理有效 管理员可以执行
		//超过保险停止时间的不能创建
		return fmt.Errorf("insurance_over_time_can_not_buy")
	}
	var log models.ShipperInsuranceLog
	db.Model(&models.ShipperInsuranceLog{}).Where(" id = ? and date = ?",id,tomorrow.Format("Y-m-d")).Find(&log)
	if log.ID ==0 {
		err := fmt.Errorf(fmt.Sprintf(s.langUtil.T("insurance_not_exist"),tomorrow.Format("Y-m-d")))
		return err
	}
	if log.State != 5 {
		err := fmt.Errorf(fmt.Sprintf(s.langUtil.T("insurance_state_not_ok"),tomorrow.Format("Y-m-d")))
		return err
	}
	tools.Logger.Info("管理员手动修改配送员的休息状态为购买保险状态",fmt.Sprintf("管理员id:%d,配送员id:%d",admin.ID,log.ShipperID))
	db.Model(&models.ShipperInsuranceLog{}).Where(" id = ? and date = ?",id,tomorrow.Format("Y-m-d")).Updates(&map[string]interface{}{
		"state":1,
	})
	redisHelper := tools.GetRedisHelper()
	cacheKey := fmt.Sprintf("enable_force_%s_%s", id,carbon.Now(configs.AsiaShanghai).Format("Y-m-d"))
	redisHelper.Set(context.Background(), cacheKey, 1, 24*time.Hour)
	return nil
 }
 //上传文件确认保险
 func (s ShipperInsuranceService) UploadConfirm(admin models.Admin,filePath string,insert bool) (map[string]interface{},error) {
	redisHelper :=tools.GetRedisHelper()
	randStr :=tools.RandStr(6)
	fileRedisKey :=fmt.Sprintf("ins_file_upload_%s",randStr)
	if !insert { 
		redisHelper.Set(context.Background(), fileRedisKey, filePath, 10*time.Minute)
	}else{
		fileRedisKey =fmt.Sprintf("ins_file_upload_%s",filePath)
		filePath = tools.ToString(redisHelper.Get(context.Background(),fileRedisKey).Val())
	}
	tools.Logger.Info("保险确认上传文件",filePath,",时间",carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),",adminId:",tools.ToString(admin.ID))
	items := tools.ExcelToData(filePath)
	if len(items) ==0 {
		return nil,fmt.Errorf("excel_file_empty")
	}
	// tools.Logger.Info(items)
	//数据处理
	//方法1:一个一个遍历 一个一个处理 X
	//方法2: 1.先找出不存在的 直接给出数据 2.找出存在的 更新状态 3.找出没有通过的 更改状态  ok
	//按方法2来处理 
	db := tools.GetDB()
	//保险通过的数据
	var insuredNumbers []string
	//数据库中存在的数据
	
	var existingInsuredIds []int
	//保险通过的 但是数据库中不存在的数据
	var addInsuredNumbers []string

	//是数据库中不在的数据的 但是保险数据中存在记录的
	
	var failedInsureIds []int
	
	type ExistingItem struct {
		ShipperId int
		MerIdcardNum string 
	}
	var existingItems []ExistingItem
	var failedItems []ExistingItem
	date :=tools.ToString(items[0]["加保申请日期(*)"])

	//验证日期 
	targetDate :=carbon.Parse(date,configs.AsiaShanghai)
	now :=carbon.Now(configs.AsiaShanghai)
	diff :=now.DiffInDays(targetDate)
	if diff < -1 {
		return nil,fmt.Errorf("insurance_date_error")
	}


	for _, item := range items {
		insuredNumbers = append(insuredNumbers, tools.ToString(item["证件号(*)"]))
	}
	db.Model(&models.SelfSignMerchantInfo{}).
		Joins("left join t_shipper_insurance_log on t_shipper_insurance_log.shipper_id = t_self_sign_merchant_info.restaurant_id").
		Where("t_self_sign_merchant_info.type = ? and t_self_sign_merchant_info.state = ? and  t_shipper_insurance_log.date = ?  and t_self_sign_merchant_info.mer_idcard_num in(?) ",2,3,date,insuredNumbers).
		Group("t_self_sign_merchant_info.mer_idcard_num").
		Select("t_shipper_insurance_log.shipper_id,t_self_sign_merchant_info.mer_idcard_num").
		Scan(&existingItems)

	db.Model(&models.SelfSignMerchantInfo{}).
		Joins("left join t_shipper_insurance_log on t_shipper_insurance_log.shipper_id = t_self_sign_merchant_info.restaurant_id").
		Where("t_self_sign_merchant_info.type = ? and t_self_sign_merchant_info.state = ? and  t_shipper_insurance_log.date = ? and t_self_sign_merchant_info.mer_idcard_num not in(?)",2,3,date,insuredNumbers).
		Group("t_self_sign_merchant_info.mer_idcard_num").
		Select("t_shipper_insurance_log.shipper_id,t_self_sign_merchant_info.mer_idcard_num").
		Scan(&failedItems)
		
	for _, vv := range existingItems {
		existingInsuredIds = append(existingInsuredIds,vv.ShipperId)
	}	

	for _, vv := range failedItems {
		failedInsureIds = append(failedInsureIds,vv.ShipperId)
	}	
		
	for _, v := range insuredNumbers {
		flag :=false
		for _, vv := range existingItems {

			if strings.TrimSpace(v) ==strings.TrimSpace(vv.MerIdcardNum) {
				flag =true
			}
		}
		if !flag {
			addInsuredNumbers = append(addInsuredNumbers,strings.TrimSpace(v))
		}
		
	}
	if !insert {
		
		resultMap :=map[string]interface{}{
			// "file_path":filePath,
			"add_numbers":len(addInsuredNumbers),//excel里面有 数据库没有 
			"existing_numbers":len(existingInsuredIds),//excel有 ，数据库也有 
			"failed_numbers":len(failedInsureIds),//excel有 ，数据库有  更新状态 失败的 
			"excel_data_count":len(insuredNumbers),//excel 里面的数据数量
			"date":date,
			"total":len(items),
			"file_key":randStr,
		}
		return resultMap,nil
	}
	//1.先找出不存在的 直接给出数据
	var selfSignInfos []models.SelfSignMerchantInfo
	db.Model(&models.SelfSignMerchantInfo{}).
		Preload("ShipperInsuranceLogForSelfSign","date = ?",date).
		Preload("Area").
		Where("t_self_sign_merchant_info.type = ? and t_self_sign_merchant_info.state = ? ",2,3).
		Where("t_self_sign_merchant_info.mer_idcard_num in(?)",addInsuredNumbers).
		Select("t_self_sign_merchant_info.restaurant_id,t_self_sign_merchant_info.city_id,t_self_sign_merchant_info.area_id").
		Find(&selfSignInfos)
	var addItems []models.ShipperInsuranceLog
	for _, info := range selfSignInfos {
		if info.Area.ID > 0 && info.ShipperInsuranceLogForSelfSign.ID == 0{
			insuranceMap :=s.BaseService.GetInsuranceConfig()
			uploadTime :=carbon.Now(configs.AsiaShanghai).Carbon2Time()
			ins :=models.ShipperInsuranceLog{
				CityID: info.Area.CityID,
				AreaID: info.Area.ID,
				ShipperID:      info.RestaurantId,
				InsuranceNameUG: insuranceMap.NameUg,
				InsuranceNameZH: insuranceMap.NameZh,
				Date:           date,
				StartTime: date+" 00:00:00",
				EndTime:  date+" 23:59:59",
				Amount: insuranceMap.Amount,
				UploadTime: &uploadTime,
				ConfirmTime: &uploadTime,
				State: 3,
			}
			addItems = append(addItems,ins)
		}
	}
	if len(addItems)>0 {
		db.Create(&addItems)
	}

	//2.找出存在的 更新状态
	if len(existingInsuredIds) > 0 {
		db.Model(&models.ShipperInsuranceLog{}).
			Where("t_shipper_insurance_log.date = ?  and t_shipper_insurance_log.shipper_id in (?) ",date,existingInsuredIds).
			Updates(&map[string]interface{}{
				"state":3,
				"confirm_time":carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
				"updated_at":carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			})
	}
		
	//3.找出没有通过的 更改状态  	
	if len(failedInsureIds) > 0 {
		db.Model(&models.ShipperInsuranceLog{}).
		Where("t_shipper_insurance_log.date = ?  and t_shipper_insurance_log.shipper_id in (?) ",date,failedInsureIds).
			Updates(&map[string]interface{}{
				"state":4,
				// "confirm_time":carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
				"updated_at":carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			})
	}
	return nil,nil
 }
 