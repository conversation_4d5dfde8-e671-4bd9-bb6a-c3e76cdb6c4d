package cms

import (
	"errors"
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type FoodsGroupService struct {
	langUtil *lang.LangUtil
	language string
}

func (s FoodsGroupService) getErrMsg(zhMsg, UgMsg string) string {
	if s.language == "zh" {
		return fmt.Sprintf("%s", zhMsg)
	}
	return fmt.Sprintf("%s", UgMsg)
}

// RestaurantList
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:49:00
//	@Description: 餐厅列表
//	@receiver s
//	@param pagination
//	@param cityId
//	@param areaId
//	@param search
//	@param sortColumns
//	@return []models.Restaurant
//	@return int64
func (s FoodsGroupService) RestaurantList(pagination tools.Pagination, cityId int, areaId int, search string, sortColumns string, canSelfTake string, state string) ([]models.Restaurant, int64) {
	db := tools.Db
	language := s.language
	tx := db.Model(models.Restaurant{}).Preload("City").Preload("Area")
	if cityId != 0 {
		tx = tx.Where("city_id = ?", cityId)
	}
	if areaId != 0 {
		tx = tx.Where("area_id = ?", areaId)
	}
	if search != "" {
		tx = tx.Where("name_"+language+" LIKE ?", "%"+search+"%")
	}
	if canSelfTake != "" {
		tx = tx.Where("can_self_take = ?",canSelfTake)
	}
	if state != "" {
		tx = tx.Where("state = ?",state)
	}
	split := strings.Split(sortColumns, ",")
	for _, sort := range split {
		tx = tx.Order(sort)
	}
	var restaurants []models.Restaurant
	var total int64
	tx.Count(&total)
	tx.Scopes(scopes.Page(pagination.Page, pagination.Limit)).Find(&restaurants)
	return restaurants, total
}

// Create
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:48:37
//	@Description: 新增美食分组
//	@receiver s
//	@param c
//	@param restaurantId
//	@param nameUg
//	@param nameZh
//	@param weight
//	@return error
func (s FoodsGroupService) Create(c *gin.Context, restaurantId int, nameUg string, nameZh string, weight int, state string) error {
	// 获取Admin
	admin := permissions.GetAdmin(c)
	db := tools.GetDB()
	now := time.Now()
	var restaurant models.Restaurant
	// 如果餐厅不存在 返回餐厅信息不存在的错误
	if err := db.Model(models.Restaurant{}).Where("id = ?", restaurantId).First(&restaurant).Error; err != nil {
		return errors.New("restaurant_not_found")
	}
	// 查询是否已经有了相同的名称
	var foodsGroup models.FoodsGroup
	db.Model(models.FoodsGroup{}).
		Where("restaurant_id = ?",restaurantId).
		Where("name_ug = ? or name_zh = ?",nameUg,nameZh).First(&foodsGroup)
	if foodsGroup.NameZh == nameZh{
		return errors.New("already_have_the_same_zh_name")
	}
	if foodsGroup.NameUg == nameUg{
		return errors.New("already_have_the_same_ug_name")
	}
	// 从dictionary 查询 有没有这个名称
	var hasGroupCount int64
	db.Model(&models.Dictionary{}).Where("name_ug like ? and name_zh like ? and type=1", "%"+tools.FilterEmoji(nameUg)+"%","%"+tools.FilterEmoji(nameZh)+"%").Count(&hasGroupCount)
	reviewState := models.FOODS_GROUP_REVIEW_STATE_PENDING  // 默认状态是待审核
	if hasGroupCount > 0 {
		reviewState = models.FOODS_GROUP_REVIEW_STATE_APPROVED // 如果有的话状态设置成 审核通过
	}
	err := db.Create(&models.FoodsGroup{
		RestaurantID: restaurantId,
		NameUg:       nameUg,
		NameZh:       nameZh,
		Weight:       weight,
		State:        tools.ToInt(state),
		ReviewState: reviewState,
		CreateBy:     admin.ID,
		CreatedAt:    &now,
	}).Error
	if err != nil {
		return err
	}
	// 添加成功之后判断 审核状态 如果是待审核 就发送到钉钉群消息
	if reviewState == models.FOODS_GROUP_REVIEW_STATE_PENDING {
		var restaurant  models.Restaurant
		db.Model(models.Restaurant{}).Where("id =?", restaurantId).Preload("Area").Find(&restaurant)
		SmsContent := "【" + restaurant.Area.NameZh + "】【" + "" + restaurant.NameZh + "】提交餐厅分组信息，请按时处理！"
		tools.SendDingDingMsg(SmsContent)
	}
	return nil
}

func (s FoodsGroupService) CreateGroup(admin models.Admin,restaurantId int, nameUg string, nameZh string, weight int) error {
	now := time.Now()
	err := tools.Db.Create(&models.FoodsGroup{
		RestaurantID: restaurantId,
		NameUg:       nameUg,
		NameZh:       nameZh,
		Weight:       weight,
		CreateBy:     admin.ID,
		CreatedAt:    &now,
	}).Error
	if err != nil {
		return err
	} 
	return nil
}

// Edit
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:48:22
//	@Description: 编辑美食分组
//	@receiver s
//	@param c
//	@param id
//	@param nameUg
//	@param nameZh
//	@param weight
//	@param state
//	@return error
func (s FoodsGroupService) Edit(c *gin.Context, id int, nameUg string, nameZh string, weight int, state string,restaurantId int) error {
	// 获取db 实例
	db := tools.GetDB()
	admin := permissions.GetAdmin(c)

	// 查询是否已经有了相同的名称
	var foodsGroup models.FoodsGroup
	db.Model(models.FoodsGroup{}).
		Where("id != ?",id).
		Where("restaurant_id = ?",restaurantId).
		Where("name_ug = ? or name_zh = ?",nameUg,nameZh).First(&foodsGroup)
	if foodsGroup.NameZh == nameZh{
		return errors.New("already_have_the_same_zh_name")
	}
	if foodsGroup.NameUg == nameUg{
		return errors.New("already_have_the_same_ug_name")
	}
	// 从dictionary 查询 有没有这个名称
	var hasGroupCount int64
	db.Model(&models.Dictionary{}).Where("name_ug like ? and name_zh like ? and type=1", "%"+tools.FilterEmoji(nameUg)+"%","%"+tools.FilterEmoji(nameZh)+"%").Count(&hasGroupCount)
	reviewState := models.FOODS_GROUP_REVIEW_STATE_PENDING  // 默认状态是待审核
	if hasGroupCount > 0 {
		reviewState = models.FOODS_GROUP_REVIEW_STATE_APPROVED // 如果有的话状态设置成 审核通过
	}
	updateMap := map[string]interface{}{
		"name_zh":nameZh,
		"name_ug":nameUg,
		"review_state":reviewState,
		"state":state,
		"weight":weight,
		"update_by":admin.ID,
	}
	if err := db.Model(models.FoodsGroup{}).Where("id = ?", id).Updates(updateMap).Error;err!=nil{
		return err
	}
	// 添加成功之后判断 审核状态 如果是待审核 就发送到钉钉群消息
	if reviewState == models.FOODS_GROUP_REVIEW_STATE_PENDING {
		var restaurant  models.Restaurant
		db.Model(models.Restaurant{}).Where("id =?", restaurantId).Preload("Area").Find(&restaurant)
		SmsContent := "【" + restaurant.Area.NameZh + "】【" + "" + restaurant.NameZh + "】修改餐厅分组信息，请按时处理！"
		tools.SendDingDingMsg(SmsContent)
	}
	return nil
}

// Delete
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:48:07
//	@Description: 删除美食分组
//	@receiver s
//	@param id
//	@param isDelete
//	@return error
func (s FoodsGroupService) Delete(id int, isDelete int) error {
	db := tools.Db
	var foodsGroup models.FoodsGroup
	if err := db.Model(models.FoodsGroup{}).Where("id = ?", id).Preload("RestaurantFoods").First(&foodsGroup).Error; err != nil {
		return err
	}
	foods := foodsGroup.RestaurantFoods
	if isDelete == 0 && len(foods) > 0 {
		return fmt.Errorf(s.getErrMsg("这个分组内有美食，强行删除会取消掉原已分组的美食", "گۇرۇپپىغا بۆلۈنگەن تاماق بار، مەجبۇرىي يۇيىۋەتكەندە ئەسلىدىكى گۇرۇپپىغا بۆلۈنگەن تاماقلارنى ئەمەلدىن قالدۇرىدۇ"))
	}
	tx := db.Begin()
	errDelete := tx.Delete(&foodsGroup).Error
	foodIds := []int{}
	for _, food := range foods {
		foodIds = append(foodIds, food.ID)
	}
	errUpdate := tx.Model(models.RestaurantFoods{}).Where("id IN ?", foodIds).Updates(map[string]interface{}{
		"foods_group_id": nil,
	}).Error
	if errDelete != nil {
		tx.Rollback()
		return errDelete
	}
	if errUpdate != nil {
		tx.Rollback()
		return errUpdate
	}
	tx.Commit()
	return nil
}

// List
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:47:32
//	@Description: 美食分组列表
//	@receiver s
//	@param restaurantId
//	@return []models.FoodsGroup
func (s FoodsGroupService) List(restaurantId int) []models.FoodsGroup {
	db := tools.Db
	var foodsGroups []models.FoodsGroup
	db.Model(models.FoodsGroup{}).Where("restaurant_id = ?", restaurantId).Order("weight").Find(&foodsGroups)
	return foodsGroups
}

func (s FoodsGroupService) Detail(ID int) models.FoodsGroup {
	db := tools.Db
	var foodsGroups models.FoodsGroup
	db.Model(models.FoodsGroup{}).Where("id = ?", ID).Find(&foodsGroups)
	return foodsGroups
}


// FoodsList
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:47:04
//	@Description: 根据美食分组id获取美食列表
//	@receiver s
//	@param restaurantId
//	@param foodsGroupId
//	@param pagination
//	@param foodsState
//	@param search
//	@return []models.RestaurantFoods
//	@return []models.FoodsGroup
//	@return map[string]interface{}
func (s FoodsGroupService) FoodsList(restaurantId, foodsGroupId int, pagination tools.Pagination, state, search string,
	foodTypes []int) ([]models.RestaurantFoods, map[string]interface{}) {
	db := tools.GetDB()
	var result struct {
		TotalCount   int64
		State0Count  int64
		State1Count  int64
		State2Count  int64
		State3Count  int64
	}

	db.Model(&models.RestaurantFoods{}).
		Select("COUNT(*) AS total_count, "+
			"SUM(CASE WHEN state = 0 THEN 1 ELSE 0 END) AS state0_count, "+
			"SUM(CASE WHEN state = 1 THEN 1 ELSE 0 END) AS state1_count, "+
			"SUM(CASE WHEN state = 2 THEN 1 ELSE 0 END) AS state2_count, "+
			"SUM(CASE WHEN state = 3 THEN 1 ELSE 0 END) AS state3_count").
		Where("restaurant_id = ?", restaurantId).
		Scan(&result)

	// Now you can access the counts like this:
	foodsTotalCount := result.TotalCount
	foodsState0Count := result.State0Count
	foodsState1Count := result.State1Count
	foodsState2Count := result.State2Count
	foodsState3Count := result.State3Count
	var restaurantFoods []models.RestaurantFoods
	tx := db.Model(models.RestaurantFoods{}).Where("t_restaurant_foods.restaurant_id = ?", restaurantId)
	if foodsGroupId != 0 {
		tx = tx.Where("t_restaurant_foods.foods_group_id = ?", foodsGroupId).Order("t_restaurant_foods.weight_in_group")
	}else {
		tx = tx.Joins("left join t_foods_group on t_restaurant_foods.foods_group_id = t_foods_group.id").
			Order("t_foods_group.weight").Order("t_restaurant_foods.weight_in_group")
	}
	if search != "" {
		tx = tx.Where("t_restaurant_foods.name_"+s.language+" like ?", "%"+search+"%")
	}
	if state != "" {
		tx = tx.Where("t_restaurant_foods.state = ?", state)
	}
	if foodTypes != nil && len(foodTypes) > 0 {
		if len(foodTypes) == 1 {
			tx.Where("t_restaurant_foods.food_type = ?", foodTypes[0])
		} else {
			tx.Where("t_restaurant_foods.food_type IN (?)", foodTypes)
		}
	}
	tx.Scopes(scopes.Page(pagination.Page, pagination.Limit)).Find(&restaurantFoods)
	foodsStateCountMap := map[string]interface{}{
		"total": foodsTotalCount,
		"0":     foodsState0Count,
		"1":     foodsState1Count,
		"2":     foodsState2Count,
		"3":     foodsState3Count,
	}

	return restaurantFoods, foodsStateCountMap
}

// EditFoodsGroupWeights
//
//	@Author: YaKupJan
//	@Date: 2024-09-18 18:46:40
//	@Description: 修改美食分组顺序
//	@receiver s
//	@param restaurantId
//	@param weights
//	@return error
func (s FoodsGroupService) EditFoodsGroupWeights(restaurantId int, weights []models.FoodsGroup) error {
	db := tools.Db
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	for _, weight := range weights {
		if err := tx.Model(&models.FoodsGroup{}).
			Where("id = ?", weight.ID).
			Where("restaurant_id = ?", restaurantId).
			Update("weight", weight.Weight).Error; err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}
// EditFoodsGroups
//
//  @Author: YaKupJan
//  @Date: 2024-09-20 11:23:34
//  @Description: 批量修改美食的分组
//  @receiver s
//  @param foodsGroupId
//  @param foodsIds
//  @return error
func (s FoodsGroupService) EditFoodsGroups(foodsGroupId int, foodsIds []int) error {
	db := tools.Db
	if foodsGroupId != 0 && len(foodsIds) > 0 {
		if err := db.Model(models.RestaurantFoods{}).Where("id in ?", foodsIds).Updates(map[string]interface{}{
			"foods_group_id":foodsGroupId,
		}).Error;err!=nil{
			return err
		}else {
			return nil
		}
	}
	return nil;
}

// EditFoodsState
//
//  @Author: YaKupJan
//  @Date: 2024-09-20 11:23:34
//  @Description: 批量修改美食的状态
//  @receiver s
//  @param foodsGroupId
//  @param foodsIds
//  @return error
func (s FoodsGroupService) EditFoodsState(foodsIds []int, foodState string) error {
	db := tools.GetDB()

	if foodState != "" && len(foodsIds) > 0{
		if err := db.Model(models.RestaurantFoods{}).Where("id in ?", foodsIds).Updates(map[string]interface{}{
			"state":foodState,
		}).Error;err!=nil{
			return err
		}else {
			return nil
		}
	}
	return nil;
}

// EditFoodsInGroupWeights
//
//  @Author: YaKupJan
//  @Date: 2024-10-14 22:05:19
//  @Description: 修改美食分组中的美食顺序
//  @receiver s
//  @param foodsCategoryId
//  @param restaurantFoodsCategory
//  @return error
func (s FoodsGroupService) EditFoodsInGroupWeights(foodsGroupId int, weights []map[string]interface{} ) error {
	db := tools.GetDB()
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	for _, weight := range weights {
		foodsId := weight["FoodsId"]
		weightInGroup:= weight["WeightInGroup"]
		if err := tx.Model(&models.RestaurantFoods{}).
			Where("foods_group_id = ?", foodsGroupId).
			Where("id = ?", foodsId).
			Update("weight_in_group", weightInGroup).Error; err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

// ReviewList
//
//  @Author: YaKupJan
//  @Date: 2024-10-21 16:56:23
//  @Description: 审核美食分组审核列表
//  @receiver s
//  @param cityId
//  @param areaId
//  @param restaurantId
//  @param reviewState
//  @param search
func (s FoodsGroupService) ReviewList(cityId , areaId , restaurantId , reviewState int, search string, pagination tools.Pagination,sortColumns string) ([]models.FoodsGroup, int64) {
	// 获取数据库
	db := tools.GetDB()
	var foodsGroup []models.FoodsGroup
	var total int64
	// 查询
	tx := db.Model(models.FoodsGroup{}).
		Joins("LEFT JOIN t_restaurant ON t_foods_group.restaurant_id = t_restaurant.id").
		Preload("Restaurant.City"). // 预加载City
		Preload("Restaurant.Area"). // 预加载Area
		Preload("ReviewAdmin") // 预加载审核人
	// 排序
	if sortColumns != ""{
		split := strings.Split(sortColumns, ",")
		for i := 0; i < len(split); i++ {
			tx = tx.Order(fmt.Sprintf("t_foods_group.%s",split[i]))
		}
	}
	// 筛选
	if cityId != 0 {
		tx = tx.Where("t_restaurant.city_id = ?", cityId)
	}
	if areaId != 0 {
		tx = tx.Where("t_restaurant.area_id = ?", areaId)
	}
	if reviewState != 0 {
		tx = tx.Where("t_foods_group.review_state = ?", reviewState)
	}
	if restaurantId != 0 {
		tx = tx.Where("t_foods_group.restaurant_id = ?", restaurantId)
	}
	if search != "" {
		tx = tx.Where("t_foods_group.name_ug like ? or t_foods_group.name_zh like ?","%"+search+"%","%"+search+"%")
	}
	// 计数  和  分页
	tx.Count(&total)
	tx.Order("updated_at").Scopes(scopes.Page(pagination.Page, pagination.Limit)).Find(&foodsGroup)

	return foodsGroup, total
}

// Review
//
//  @Author: YaKupJan
//  @Date: 2024-10-21 18:34:49
//  @Description: 审核美食分组
//  @receiver s
//  @param id
//  @return bool
func (s FoodsGroupService) Review(id int, reviewState int, refuseReason string, admin models.Admin, nameUg string, nameZh string) error {
	db := tools.GetDB()
	// 定义美食分组
	group := models.FoodsGroup{}
	// 查询美食分组
	if err := db.Model(models.FoodsGroup{}).Where("id = ?", id).First(&group).Error; err != nil {
		return err
	}
	if group.NameUg != nameUg || group.NameZh != nameZh {
		return errors.New("try_again_refresh_page")
	}
	filterEmojiNameZh := tools.FilterEmoji(group.NameZh)
	filterEmojiNameUg := tools.FilterEmoji(group.NameUg)
	// 先更新状态
	if err := db.Model(models.FoodsGroup{}).Where("id = ?", id).Updates(map[string]interface{}{
		"review_state":  reviewState,
		"refuse_reason": refuseReason,
		"review_by":admin.ID,
		"review_at":time.Now().Format("2006-01-02 15:04:05"),
	}).Error; err != nil {
		return err
	}
	// 如果审核不通过,直接返回,不在 base_group 创建
	if reviewState == models.FOODS_GROUP_REVIEW_STATE_REFUSE {
		return nil
	}
	// 定义美食基本分组
	baseGroup := models.Dictionary{}
	// 查询是否有 base_group 分组
	db.Model(models.Dictionary{}).
		Where("name_zh = ?", filterEmojiNameZh).
		Where("name_ug = ?", filterEmojiNameUg).
		Where("type = ?", models.DICTIONARY_TYPE_GROUP).
		First(&baseGroup)
	// 如果不存在,则创建
	if baseGroup.ID == 0 {
		// 添加 base_group 分组
		if err := db.Model(models.Dictionary{}).Create(&models.Dictionary{
			NameUg:   filterEmojiNameUg,
			NameZh:   filterEmojiNameZh,
			Type: models.DICTIONARY_TYPE_GROUP,
		}).Error; err != nil {
			return err
		}
	}
	return nil
}

// UpdateGroupName
//
//  @Author: YaKupJan
//  @Date: 2024-10-21 20:46:08
//  @Description: 修改美食分组名称
//  @receiver s
//  @param id
//  @param nameZh
//  @param nameUg
//  @return error
func (s FoodsGroupService) UpdateGroupName(id int, nameZh string, nameUg string, admin models.Admin) error {
	db := tools.GetDB()
	if err := db.Model(models.FoodsGroup{}).Where("id = ?", id).
		Where("review_state = ?", models.FOODS_GROUP_REVIEW_STATE_PENDING).
		Updates(map[string]interface{}{
			"name_zh": nameZh,
			"name_ug": nameUg,
			"update_by":admin.ID,
		}).Error; err != nil {
		return err
	}
	return nil
}

// GetRecommendGroup
//
// @Description: 根据关键字推荐分组库中的分组名称
// @Author: Rixat
// @Time: 2024-10-09 10:03:53
// @receiver
// @param c *gin.Context
func (s FoodsGroupService) GetRecommendGroup(kw string) []models.Dictionary {
	db := tools.Db
	var dictionary []models.Dictionary
	queryModel := db.Model(dictionary).Where("type = 1")
	if len(kw) > 0{
		kw = tools.FilterEmoji(kw)
		queryModel.Where("name_ug like ? or name_zh like ?","%"+kw+"%","%"+kw+"%")
	}
	queryModel.Find(&dictionary)
	return dictionary
}

func NewFoodsGroupService(c *gin.Context) *FoodsGroupService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := FoodsGroupService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &service
}
