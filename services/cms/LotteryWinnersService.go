package cms

import (
	"fmt"
	"mulazim-api/jobs"
	"mulazim-api/lang"
	"mulazim-api/models"
	lotteryResource "mulazim-api/resources/cms/lottery"
	"mulazim-api/scopes"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	// "time"
)

type LotteryWinnersService struct {
	langUtil *lang.LangUtil
	language string
}

// List 抽奖列表获取
//
//	@receiver s
//	@param state
//	@param page
//	@param limit
//	@param kw
//	@param sort
//	@return int64
//	@return []models.LotteryActivity
func (s LotteryWinnersService) List(cityId int,areaId int,activityId int,kw string,sort string,page int,limit int,tp int,lv int,onlyWinners bool,all bool,export bool) (map[string]interface{}) {
	list := []models.LotteryChance{}
	sharedUser :=[]models.LotteryShareBind{}

	db := tools.ReadDb1
	// headerMap :=make(map[string]interface{})
	query := db.Model(&list)
	query2 := db.Model(&list)
	query3 := db.Model(&list)
	lo :=db.Model(&models.LotteryOrder{})


	query4 := db.Model(&sharedUser)

	query2.Preload("LotteryActivityLevelPrize")
	
	query.Preload("User")
	query.Preload("City")
	query.Preload("Area")
	query.Preload("LotteryPrize")
	query.Preload("LotteryActivity", "type = ?", models.LotteryActivityTypeLottery) // 必须为抽奖活动
	query.Preload("LotteryActivityLevelPrize")
	
	if export {
		query.Preload("UserBuilding")
	}
	if  !all { //不是全部数据 
		if onlyWinners { //只看中奖者
			query.Where("prize_open_state = ?",2)//中奖的 
			query2.Where("prize_open_state = ?",2)//中奖的 
		}else{
			query.Preload("LotteryOrder").Where("type = ?",1)//购买获得的机会
			query2.Preload("LotteryOrder").Where("type = ?",1)//购买获得的机会
		}
	}else{
		query.Preload("LotteryOrder")
		query2.Preload("LotteryOrder")
	}

	if tp > 0 {
		query.Where("t_lottery_chance.type = ?",tp)
		query2.Where("t_lottery_chance.type = ?",tp)
		// query3.Where("t_lottery_chance.type = ?",tp)
		// lo.Where("t_lottery_order.type = ?",tp)
	}
	
	if cityId > 0 {
		query.Where("t_lottery_chance.city_id = ?",cityId)
		query2.Where("t_lottery_chance.city_id = ?",cityId)
		query3.Where("t_lottery_chance.city_id = ?",cityId)
		lo.Where("t_lottery_order.city_id = ?",cityId)
	}
	if areaId > 0 {
		query.Where("t_lottery_chance.area_id = ?",areaId)
		query2.Where("t_lottery_chance.area_id = ?",areaId)
		query3.Where("t_lottery_chance.area_id = ?",areaId)
		lo.Where("t_lottery_order.area_id = ?",areaId)
	}
	if activityId > 0 {
		query.Where("t_lottery_chance.lottery_activity_id = ?",activityId)
		query2.Where("t_lottery_chance.lottery_activity_id = ?",activityId)
		query3.Where("t_lottery_chance.lottery_activity_id = ?",activityId)
		lo.Where("t_lottery_order.lottery_activity_id = ?",activityId)
		query4.Where("t_lottery_share_bind.lottery_activity_id = ?",activityId)
	}
	if kw != "" {
		var userIds []int
		db.Model(&models.User{}).Where("mobile like ?", "%"+kw+"%").Pluck("id",&userIds)
		query.Where("t_lottery_chance.user_id in ?",userIds)
		query2.Where("t_lottery_chance.user_id in ?",userIds)
		query3.Where("t_lottery_chance.user_id in ?",userIds)
		lo.Where("t_lottery_order.user_id in ?",userIds)
	}

	if len(sort) > 0 {
		query.Order(sort)
		query2.Order(sort)
		query3.Order(sort)
		lo.Order(sort)
	}else{
		if !all && onlyWinners { //不是全部 而且 只查询 中奖者 
			query.Order("t_lottery_chance.prize_open_time desc")
			query2.Order("t_lottery_chance.prize_open_time desc")
			query3.Order("t_lottery_chance.prize_open_time desc")
			lo.Order("t_lottery_order.prize_open_time desc")
		}
	}

	

	var totalCount int64

	var winners []lotteryResource.LotteryWinnersItem

	headList := []models.LotteryChance{}

	query2.Find(&headList)

	chanceCount :=int64(0)
	chanceUserCount :=int64(0)
	query2.Count(&chanceCount)
	query2.Group("user_id").Count(&chanceUserCount)

	shareCount :=int64(0)
	shareDistinctCount :=int64(0)
	query4.Count(&shareCount)
	query4.Group("user_id").Count(&shareDistinctCount)

	
	type  TotalOrder struct {
		Count int `json:"count"`
		Price int `json:"price"`
	}
	var totalOrder TotalOrder
	 
	lo.Select("count(id) as count,sum(price) as price").Scan(&totalOrder)

	type  TypeData struct {
		Count int `json:"count"`
		Tp int `json:"type"`
	}

	var typeData []TypeData

	query3.Select("count(id) as count,`type` tp").Group("`type`").Scan(&typeData)

	levelCount :=make(map[string]interface{})

	stateCount :=make(map[string]interface{})

	OrderCount :=make(map[string]interface{})

	typeCount :=make(map[string]interface{})
	
	var prizes []int //中奖的礼物id 用于 根据等级查询
	winnerCount :=int64(0)
	
	for _, item := range headList {
		level :=0
		if item.PrizeID !=nil {
			prizeId := *item.PrizeID
			for _,levels := range item.LotteryActivityLevelPrize {
				lvp := levels.PrizeID
				if prizeId == lvp {
					level = levels.Level
					break
				}
			}
			
		}
		
		if level > 0 {
			oldCount :=0
			if levelCount["level_"+tools.ToString(level)] !=nil {
				oldCount = tools.ToInt(levelCount["level_"+tools.ToString(level)])
			}
			levelCount["level_"+tools.ToString(level)]=oldCount+int(1)

			if level == lv {
				prizeId := *item.PrizeID
				if !tools.InArray(prizeId,prizes){
					prizes = append(prizes,prizeId)	
				}
				
			}
			winnerCount++
		}
		if item.LotteryOrder.ID > 0 {
			oldCount :=0
			if OrderCount["order_"+tools.ToString(item.LotteryOrder.ID)] !=nil {
				oldCount = tools.ToInt(OrderCount["order_"+tools.ToString(item.LotteryOrder.ID)])
			}
			OrderCount["order_"+tools.ToString(item.LotteryOrder.ID)]=oldCount+int(1)
		}

		oldTypeCount :=0
		if typeCount["type_"+tools.ToString(item.Type)] !=nil {
			oldTypeCount = tools.ToInt(typeCount["type_"+tools.ToString(item.Type)])
		}
		typeCount["type_"+tools.ToString(item.Type)]=oldTypeCount+int(1)

		stateOldCount :=0
		if stateCount["state_"+tools.ToString(item.State)] !=nil {
			stateOldCount = tools.ToInt(stateCount["state_"+tools.ToString(item.State)])
		}
		stateCount["state_"+tools.ToString(item.State)]=stateOldCount+int(1)


	}
	for i := 1; i < 4; i++ {	
		if levelCount["level_"+tools.ToString(i)] ==nil {
			levelCount["level_"+tools.ToString(i)] = 0
		}
	}
	chanceUnusedCount:=int64(0)
	chanceOtherCount:=int64(0)
	for i := 1; i < 7; i++ {	
		if stateCount["state_"+tools.ToString(i)] ==nil {
			stateCount["state_"+tools.ToString(i)] = 0
		}
		if i == 1 {
			chanceUnusedCount = tools.ToInt64(stateCount["state_"+tools.ToString(i)])
		}else{
			chanceOtherCount +=tools.ToInt64(stateCount["state_"+tools.ToString(i)])
		}
	}

	
	for i := 1; i < 5; i++ {	

		exists :=false
		for _, v := range typeData {
			if v.Tp == i {
				exists = true
			}	
		}
		if !exists {
			typeData= append(typeData, TypeData{
				Tp: i,
				Count: 0,
			})
		}

	}
	
	
	
	if lv > 0  { //按照等级查询  
		query.Where("t_lottery_chance.prize_id in ?",prizes)
	}


	query.Count(&totalCount)
	query.Select("t_lottery_chance.*")
	if !export {
		query.Scopes(scopes.Page(page, limit))
	}
	query.Find(&list)

	var currentPageUsers []int

	for _, item := range list {
		currentPageUsers = append(currentPageUsers, item.UserID)
	}
	type UserChance struct {
		UserId int
		Count int
		UsedCount int
	}
	var userChances []UserChance

	if all {
		var allUserChances []UserChance //最多抽奖机会的用户 前100个
		db.Model(&list).Joins("left join t_user on t_user.id = t_lottery_chance.user_id").Select("t_lottery_chance.user_id,count(t_lottery_chance.id) count,count(IF(t_lottery_chance.state >1,1,null)) used_count").
		Group("t_lottery_chance.user_id").Order("count desc").Limit(100).Scan(&allUserChances)
	}

	db.Model(&list).Where("user_id in ?",currentPageUsers).Select("user_id,count(id) count,count(IF(state >1,1,null)) used_count").
	Group("user_id").Order("count desc").Scan(&userChances)

	winnerItemMap :=make([]map[string]interface{},0)

	for k, item := range list {
		level :=0
		if item.PrizeID !=nil {
			prizeId := *item.PrizeID
			for _,levels := range item.LotteryActivityLevelPrize {
				lvp := levels.PrizeID
				if prizeId == lvp {
					level = levels.Level
					break
				}
			}
		}
		winnerItem :=lotteryResource.LotteryWinnersItem{
			Id: item.ID,
			ActivityId: item.LotteryActivityID,
			CityName: tools.If(s.language == "zh",item.City.NameZh,item.City.NameUg),
			AreaName: tools.If(s.language == "zh",item.Area.NameZh,item.Area.NameUg),
			UserName: item.User.Name,
			UserPhone: item.User.Mobile,
			State: item.State,
			PrizeID: item.PrizeID,
			TypeName: s.langUtil.TArr("chance_type_names")[item.Type],
			StateName: s.langUtil.TArr("chance_state_names")[item.State],
			CreatedAt: item.CreatedAt.Format("2006-01-02 15:04:05"),
			Tp: item.Type,
		}
		if level > 0 {
			winnerItem.PrizeName=item.LotteryPrize.NameUg
			winnerItem.Level= fmt.Sprintf(s.langUtil.T("lottery_prize_level"),level)
			winnerItem.DrawIndex=*item.DrawIndex
			if item.PrizeOpenTime !=nil {
				winnerItem.PrizeOpenTime=item.PrizeOpenTime.Format("2006-01-02 15:04:05")
			}
		}
		for _, cc := range userChances {
			if cc.UserId == item.UserID {
				winnerItem.UsedChanceCount = cc.UsedCount
				winnerItem.AllChanceCount = cc.Count
				break
			}
		}
		
		if item.LotteryOrder.ID > 0  && item.Type == 1{
			winnerItem.OrderNo = item.LotteryOrder.OrderID
			winnerItem.OrderPrice = *item.LotteryOrder.Price
			winnerItem.OrderTime = item.LotteryOrder.CreatedAt.Format("2006-01-02 15:04:05")
		}
		if level > 0  && export{
			winnerItemMap = append(winnerItemMap, map[string]interface{}{
				
				"序号":(k+1),
				"城市":item.City.NameZh,
				"地区":item.Area.NameZh,
				"姓名":item.User.Name,
				"手机号":item.User.Mobile,
				"奖品等级":level,
				"奖品名称":item.LotteryPrize.NameZh,
				"收货地址-姓名":item.UserBuilding.Name,
				"收货地址-手机号":item.UserBuilding.Tel,
				"收货地址-详细地址":item.UserBuilding.Address,
				"抽奖时间":item.PrizeOpenTime.Format("2006-01-02 15:04:05"),
				"状态":s.langUtil.TArrZh("chance_state_names")[item.State],
			})
		}

		winners = append(winners, winnerItem)
	}


	dataMap :=make(map[string]interface{})
	dataMap["data"] = winners
	dataMap["total"] = totalCount
	dataMap["page"] = page
	dataMap["header"] = map[string]interface{}{
		"levels":levelCount,
		"buy_count":totalOrder.Count,
		"buy_price":totalOrder.Price,
		"types":typeCount,
		"type_data":typeData,
		"share_count":shareCount,
		"share_user_count":shareDistinctCount,
		"total_user_count":chanceUserCount,
		"total_chance_count":chanceCount,
		"state_count":stateCount,
		"chance_unused_count":chanceUnusedCount,
		"chance_other_count":chanceOtherCount,
		"winner_count":winnerCount,
	}
	if export {
		dataMap["data"] = winnerItemMap
	}
	
	// dataMap["top_user_chances"] = allUserChances //抽奖机会做多的用户列表
	
	return dataMap
}


//中奖详情
func (s LotteryWinnersService) Detail(id int) (map[string]interface{}) {
	item := models.LotteryChance{}
	db := tools.ReadDb1
	// db := tools.GetDB()
	query := db.Model(&item)
	item2 := models.LotteryChance{}
	db.Model(&item).Preload("LotteryActivity").Where("id = ?",id).Find(&item2)
	startTime :=item2.LotteryActivity.CreatedAt
	query.Preload("UserBuilding.Building")
	query.Preload("User")
	query.Preload("City")
	query.Preload("Area")
	query.Preload("LotteryPrize")
	query.Preload("LotteryActivity")
	query.Preload("CouponUser","created_at > ? and coupon_type = ?",startTime.Format("2006-01-02 15:04:05"),3)
	
	query.Preload("LotteryActivityLevelPrize")
	query.Where("id = ?",id)

	

	var winner lotteryResource.LotteryWinnersItemOne
  
	query.Find(&item)
	if item.ID > 0 {
		level :=0
		prizeId := *item.PrizeID
		for _,levels := range item.LotteryActivityLevelPrize {
			lvp := levels.PrizeID
			if prizeId == lvp {
				level = levels.Level
				break
			}
		}
		couponPrice :=0
		for _,cc := range item.CouponUser {
			couponPrice +=cc.Price
		}
		winner = lotteryResource.LotteryWinnersItemOne{
			Id: item.ID,
			ActivityId: item.LotteryActivityID,
			CityName: tools.If(s.language == "zh",item.City.NameZh,item.City.NameUg),
			AreaName: tools.If(s.language == "zh",item.Area.NameZh,item.Area.NameUg),
			BuildingId: item.BuildingId,
			UserName: item.User.Name,
			UserPhone: item.User.Mobile,
			PrizeName: tools.If(s.language == "zh",item.LotteryPrize.NameZh,item.LotteryPrize.NameUg),
			State: item.State,
			StateName: s.langUtil.TArr("chance_state_names")[item.State],
			Level: fmt.Sprintf(s.langUtil.T("lottery_prize_level"),level),
			PrizeOpenTime: item.PrizeOpenTime.Format("2006-01-02 15:04:05"),
			PrizeImg: tools.AddCdn(item.LotteryPrize.TitleImgUg),
			PrizePrice: item.LotteryPrize.Price,
			UserAddress: tools.If(s.language == "zh",item.UserBuilding.Building.NameZh,item.UserBuilding.Building.NameUg),
			UserAddressName: item.UserBuilding.Name,
			UserAddressTel: item.UserBuilding.Tel,
			CouponPrice: couponPrice,
		}
	
	}
	dataMap :=make(map[string]interface{})
	dataMap["data"] = winner
	return dataMap
}




//退款 
func (s LotteryWinnersService) Refund(outOrderNo string,transactionId string,force int) (bool,string) {
	
	db := tools.GetDB()
	item := models.LotteryChance{}

	var payLog models.PayLakala
	query1 :=db.Model(&models.PayLakala{})
	var lotteryOrder models.LotteryOrder
	if len(outOrderNo) > 0 {
		db.Model(&models.LotteryOrder{}).Where("order_id = ?",outOrderNo).Find(&lotteryOrder)
		if lotteryOrder.ID > 0 {
			query1.Where("object_type = ?  and order_id  = ? ","lottery_order",lotteryOrder.ID).Find(&payLog)
		}
	}
	if len(transactionId) > 0 {
		query1.Where("pay_channel_trade_no = ? ",transactionId).Find(&payLog)
	}
	
	if payLog.ID == 0 {
		return false,"order_not_found" //订单不存在
	}

	query := db.Model(&item).Preload("CouponUser","state > ?",0).Where("type_id = ?",payLog.OrderID)
	
	query.Find(&item)

	if force == 0 { //不是强制退款的 要检查 各种状态 
		if len(item.CouponUser) > 0 { //优惠券已经用过了 
			return false,"coupon_used"
		}
		if item.State > 1 { //已经抽奖了 
			return false,"lottery_used"
		}
	}

	//1.作废 抽奖获得的 优惠券 
	db.Model(&models.CouponUser{}).Where("lottery_order_id = ?",item.ID).Updates(&map[string]interface{}{
		"state":5,
	})
	
	//2.作废抽奖机会 
	db.Model(&models.LotteryChance{}).Where("id = ?",item.ID).Updates(&map[string]interface{}{
		"state":5,
	})
	//3.退款
	job := jobs.NewLakalaRefundJob()
	job.ProduceMessageToConsumer(map[string]interface{}{
		"lakala_pay_id": payLog.ID,
	})
	
	return true,""
}



//订单详情
func (s LotteryWinnersService) OrderDetail(outOrderNo string,transactionId string) (bool,string,map[string]interface{}) {
	
	db := tools.GetDB()
	item := models.LotteryChance{}

	var payLog models.PayLakala
	var lotteryOrder models.LotteryOrder
	query1 :=db.Model(&models.PayLakala{})
	if len(outOrderNo) > 0 {
		db.Model(&models.LotteryOrder{}).Where("order_id = ?",outOrderNo).Find(&lotteryOrder)
		if lotteryOrder.ID > 0 {
			query1.Where("object_type = ?  and order_id  = ?","lottery_order",lotteryOrder.ID).Find(&payLog)
		}
		
	}
	if len(transactionId) > 0 {
		query1.Where("pay_channel_trade_no = ? ",transactionId).Find(&payLog)
	}
	if payLog.ID == 0 {
		return false,"order_not_found",nil //订单不存在
	}
	query := db.Model(&item).Where("type_id = ?",payLog.OrderID)
	
	query.Preload("User")
	query.Preload("City")
	query.Preload("Area").Find(&item)

	result :=make(map[string]interface{})
	result["city_name"]=item.City.NameUg
	result["area_name"]=item.Area.NameUg
	result["user_name"]=item.User.Name
	result["user_phone"]=item.User.Mobile
	result["state_name"]=s.langUtil.TArr("chance_state_names")[item.State]
	result["price"]=payLog.PayAmount
	if payLog.PayStatus == 2003 {
		result["refund_status"]=s.langUtil.T("refunded")
	}else{
		result["refund_status"]=""
	}
	

	
	return true,"",result
}

func (s LotteryWinnersService) ListByArea(activityID int, sort string) interface{} {
	dbRead := tools.ReadDb1
	var rtn []map[string]interface{}
	query := dbRead.Table("t_lottery_chance").
		Joins("left join b_area on b_area.id = t_lottery_chance.area_id").
		Select("b_area.name_" + s.language + ",t_lottery_chance.area_id, count(t_lottery_chance.id) as total_count, "+
			"sum(if(t_lottery_chance.type=1,1,0)) as type1, "+
			"sum(if(t_lottery_chance.type=2,1,0)) as type2, "+
			"sum(if(t_lottery_chance.type=3,1,0)) as type3, "+
			"sum(if(t_lottery_chance.type=4,1,0)) as type4, "+
			"count(distinct t_lottery_chance.user_id) as user_count"). // 统计每个区域的唯一用户数
		Group("t_lottery_chance.area_id")

	if activityID > 0 {
		query = query.Where("t_lottery_chance.lottery_activity_id = ?", activityID)
	}
	if sort == "" {
		sort = "total_count desc"
	}
	query.Order(sort).Find(&rtn)
	return rtn
}




func NewLotteryWinnersService(c *gin.Context) *LotteryWinnersService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := LotteryWinnersService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &service
}