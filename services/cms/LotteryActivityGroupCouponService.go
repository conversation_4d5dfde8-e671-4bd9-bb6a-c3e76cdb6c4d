package cms

import (
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"mulazim-api/lang"
	"mulazim-api/models"
	lotteryRequest "mulazim-api/requests/cms/lottery"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"time"
)

type LotteryActivityGroupCouponService struct {
	langUtil *lang.LangUtil
	language string
}

func NewLotteryActivityGroupCouponService(c *gin.Context) *LotteryActivityGroupCouponService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := LotteryActivityGroupCouponService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &service
}

// Create
//
//  @Author: Yakup
//  @Date: 2024-09-05 17:25:34
//  @Description: 创建优惠券
//  @receiver s
//  @param admin
//  @param req
//  @return interface{}
func (s *LotteryActivityGroupCouponService) Create(admin models.Admin, req lotteryRequest.CreateLotteryActivityGroupCouponRequest) error {
	err := func(details []lotteryRequest.CouponDetail) error {
		for _, detail := range details {
			if detail.MinPrice < detail.Price {
				return errors.New("order_amount_cannot_less_than_discount_amount")
			}
		}
		return nil
	}(req.CouponDetail)
	totalPrice := 0
	for _, detail := range req.CouponDetail {
		totalPrice =detail.Price + totalPrice
	}
	if err!=nil {
		return err
	}
	couponDetail, err := json.Marshal(req.CouponDetail)
	if err != nil {
		return err
	}
	couponDetailStr := string(couponDetail)
	now := time.Now()
	coupon := models.LotteryActivityGroupCoupon{
		NameUg:       req.NameUg,
		NameZh:       req.NameZh,
		TotalPrice:   totalPrice,
		State:        req.State,
		Count:        len(req.CouponDetail),
		CouponDetail: &couponDetailStr,
		AdminID:      &admin.ID,
		CreatedAt:    &now,
	}
	if err := tools.Db.Model(models.LotteryActivityGroupCoupon{}).Create(&coupon).Error; err != nil {
		return err
	}
	return nil
}

// Update
//
//  @Author: Yakup
//  @Date: 2024-09-05 17:25:50
//  @Description: 修改优惠券
//  @receiver s
//  @param admin
//  @param req
//  @return error
func (s *LotteryActivityGroupCouponService) Update(admin models.Admin, req lotteryRequest.UpdateLotteryActivityGroupCouponRequest) error {
	db := tools.Db
	coupon := models.LotteryActivityGroupCoupon{}
	coupon.ID = req.ID
	var lotteryActivityLevelCouponCount int64
	db.Model(models.LotteryActivityLevelCoupon{}).Where("lottery_activity_group_coupon_id = ?",req.ID).Count(&lotteryActivityLevelCouponCount)
	if lotteryActivityLevelCouponCount >0 {
		return errors.New("coupon_already_used_cannot_update")
	}
	if err := db.Model(models.LotteryActivityGroupCoupon{}).First(&coupon).Error; err != nil {
		return err
	}
	if req.NameUg != nil {
		coupon.NameUg = *req.NameUg
	}
	if req.NameZh != nil {
		coupon.NameZh = *req.NameZh
	}
	if req.State != nil {
		coupon.State = *req.State
	}
	if req.CouponDetail != nil {
		couponDetail, err := json.Marshal(req.CouponDetail)
		if err != nil {
			return err
		}
		couponDetailStr := string(couponDetail)
		coupon.CouponDetail = &couponDetailStr
		coupon.Count = len(req.CouponDetail)
	}
	totalPrice := 0
	for _, detail := range req.CouponDetail {
		totalPrice =detail.Price + totalPrice
	}
	coupon.TotalPrice = totalPrice
	if err := db.Save(&coupon).Error; err != nil {
		return err
	}
	return nil
}

// Delete
//
//  @Author: Yakup
//  @Date: 2024-09-05 17:25:58
//  @Description: 删除优惠券
//  @receiver s
//  @param id
//  @return error
func (s *LotteryActivityGroupCouponService) Delete(id int) error {
	var lotteryActivityLevelCouponCount int64
	tools.Db.Model(models.LotteryActivityLevelCoupon{}).Where("lottery_activity_group_coupon_id = ?",id).Count(&lotteryActivityLevelCouponCount)
	if lotteryActivityLevelCouponCount >0 {
		return errors.New("coupon_already_used_cannot_delete")
	}
	var coupon models.LotteryActivityGroupCoupon
	if err := tools.Db.Model(models.LotteryActivityGroupCoupon{}).Where("id = ?", id).Delete(&coupon).Error; err != nil {
		return err
	}
	return nil
}

// List
//
//  @Author: Yakup
//  @Date: 2024-09-05 17:26:04
//  @Description: 优惠券列表
//  @receiver s
//  @param key
//  @return []map[string]interface{}
func (s *LotteryActivityGroupCouponService) List(key string,page int,limit int) (int64,[]map[string]interface{}) {
	lang := s.language
	db := tools.Db
	var total int64
	db.Model(models.LotteryActivityGroupCoupon{}).Count(&total)
	query := db.Model(models.LotteryActivityGroupCoupon{}).Select("id", "name_"+lang, "count", "total_price", "state", "created_at").
		Order("created_at desc")
	if key != "" {
		query = query.Where("name_"+lang+" like ?", "%"+key+"%")
	}
	var lotteryActivityGroupCouponList []models.LotteryActivityGroupCoupon
	var items []map[string]interface{}
	query.Scopes(scopes.Page(page, limit)).
		Find(&lotteryActivityGroupCouponList)
	for _, value := range lotteryActivityGroupCouponList {
		items = append(items, map[string]interface{}{
			"id":          value.ID,
			"name":        tools.GetNameByLangAndColumn(value, s.language, "Name"),
			"count":       value.Count,
			"total_price": value.TotalPrice,
			"state":       value.State,
			"created_at":  tools.TimeFormatYmdHis(value.CreatedAt),
		})
	}

	return total,items
}

// Detail
//
//  @Author: Yakup
//  @Date: 2024-09-05 17:26:13
//  @Description: 优惠券详情
//  @receiver s
//  @param id
//  @return interface{}
//  @return error
func (s *LotteryActivityGroupCouponService) Detail(id int) (interface{}, error) {
	db := tools.Db
	var data models.LotteryActivityGroupCoupon
	db.Model(models.LotteryActivityGroupCoupon{}).Where("id = ?", id).
		First(&data)
	var couponDetail interface{}
	if err := json.Unmarshal([]byte(*data.CouponDetail), &couponDetail); err != nil {
		return nil, err
	}
	result := map[string]interface{}{
		"id":            data.ID,
		"name_zh":       data.NameZh,
		"name_ug":       data.NameUg,
		"total_price":   data.TotalPrice,
		"count":         data.Count,
		"state":         data.State,
		"coupon_detail": couponDetail,
		"created_at":    tools.TimeFormatYmdHis(data.CreatedAt),
	}
	return result, nil
}
