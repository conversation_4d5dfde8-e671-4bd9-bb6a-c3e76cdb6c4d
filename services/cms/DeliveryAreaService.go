package cms

import (
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/models/shipment"
	cmsRequests "mulazim-api/requests/cms/delivery"
	"mulazim-api/resources"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type DeliveryAreaService struct {
	langUtil *lang.LangUtil
	language string
}

// NewDeliveryAreaService
//
//	@Description: 配送费/范围配置服务
//	@Author: Salam
//	@Time: 2025-06-12 16:37:32
//	@Param ctx *gin.Context
//	@return *DeliveryAreaService
func NewDeliveryAreaService(ctx *gin.Context) *DeliveryAreaService {
	if ctx == nil {
		deliveryAreaSvc := DeliveryAreaService{}
		return &deliveryAreaSvc
	}
	l, _ := ctx.Get("lang_util")
	language, _ := ctx.Get("lang")
	langUtil := l.(lang.LangUtil)
	deliveryAreaSvc := DeliveryAreaService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &deliveryAreaSvc
}

// GetDeliveryRestaurantList
//
//	@Description: 创建配送费/范围配置
//	@Author: Rixat
//	@Time: 2025-06-12 16:37:35
//	@Param ctx *gin.Context
//	@return []resources.DeliveryRestaurantList
func (svc *DeliveryAreaService) GetDeliveryRestaurantList(params cmsRequests.GetListDeliveryConfigsRequest) (int64, []resources.DeliveryRestaurantList) {
	if params.ParentID == 0 {
		runningDeliveryConfig := svc.GetRunningDeliveryConfig(params.AreaID)
		if runningDeliveryConfig.ID > 0 {
			params.ParentID = runningDeliveryConfig.ID
		}
	}
	var deliveryRestaurantList []resources.DeliveryRestaurantList
	query := tools.Db.Model(models.Restaurant{}).
		Select(fmt.Sprintf(`
			t_restaurant.id as restaurant_id,
			t_restaurant.name_%s as restaurant_name,
			t_restaurant.logo as restaurant_logo,
			t_delivery_areas.option as 'option',
			t_delivery_areas.radius as radius,
			t_delivery_areas.area_running_state as area_running_state,
			t_delivery_areas.order_add_time as order_add_time,
			t_delivery_areas.fee_state as fee_state,
			t_restaurant.lat as lat,
			t_restaurant.lng as lng,
			ST_AsText(t_delivery_areas.polygon) as polygon 
		`, svc.language)).
		Joins("left join t_delivery_areas on t_delivery_areas.restaurant_id = t_restaurant.id and t_delivery_areas.type = 2 and t_delivery_areas.parent_id = ?", params.ParentID).
		// Joins("left join t_delivery_fees on t_delivery_fees.restaurant_id = t_restaurant.id and t_delivery_fees.type = 2").
		Where("t_restaurant.state > 0")
	if params.CityID > 0 {
		query = query.Where("t_restaurant.city_id=?", params.CityID)
	}
	if params.AreaID > 0 {
		query = query.Where("t_restaurant.area_id=?", params.AreaID)
	}
	if len(params.Kw) > 0 {
		query = query.Where("(t_restaurant.name_ug like ? or t_restaurant.name_zh like ?)", "%"+params.Kw+"%", "%"+params.Kw+"%")
	}
	// 1:区域 2:餐厅
	if params.Option == 1 {
		query = query.Where("(t_delivery_areas.option is null or t_delivery_areas.option in(0,1))")
	}
	if params.Option == 2 {
		query = query.Where("t_delivery_areas.option in(2,3)")
	}
	if params.FeeState == 1 {
		query = query.Where("t_delivery_areas.fee_state = ?", params.FeeState)
	}
	if params.FeeState == 2 {
		query = query.Where("fee_state is null or fee_state in(0,2)")
	}
	// 查询结果
	var totalCount int64
	query.Count(&totalCount).
		Scopes(scopes.Page(params.Page, params.Limit)).
		Scan(&deliveryRestaurantList)

	return totalCount, deliveryRestaurantList
}

// GetDeliveryAreaList
//
//	@Description: 创建配送费/范围配置
//	@Author: Rixat
//	@Time: 2025-06-12 16:37:35
//	@Param ctx *gin.Context
//	@return []models.Area
func (svc *DeliveryAreaService) GetDeliveryAreaList(params cmsRequests.GetListDeliveryConfigsRequest) (int64, []models.Area) {
	var deliveryAreaList []models.Area
	query := tools.Db.Model(models.Area{}).
		Where("b_area.state = 1").
		Preload("City").
		Preload("DeliveryRunningConfig", func(db *gorm.DB) *gorm.DB {
			return db.Where("area_running_state = 1 and type = 1").Preload("DeliveryFees")
		}).
		Preload("DeliveryAllConfigs", "type = 1")
	if params.CityID > 0 {
		query = query.Where("b_area.city_id=?", params.CityID)
	}
	if params.AreaID > 0 {
		query = query.Where("b_area.id=?", params.AreaID)
	}
	if len(params.Kw) > 0 {
		query = query.Where("(b_area.name_ug like ? or b_area.name_zh like ?)", "%"+params.Kw+"%", "%"+params.Kw+"%")
	}
	// 查询结果
	var totalCount int64
	query.Order("id").Count(&totalCount).
		Scopes(scopes.Page(params.Page, params.Limit)).
		Find(&deliveryAreaList)
	return totalCount, deliveryAreaList
}

// GetDeliveryAreaConfigList
//
//	@Description: 创建配送费/范围配置
//	@Author: Rixat
//	@Time: 2025-06-12 16:37:35
//	@Param ctx *gin.Context
//	@return []shipment.DeliveryAreas
func (svc *DeliveryAreaService) GetDeliveryAreaConfigList(params cmsRequests.GetDeliveryConfigsRequest) (int64, []shipment.DeliveryAreas) {
	var deliveryAreaConfigList []shipment.DeliveryAreas
	var totalCount int64
	tools.Db.Model(deliveryAreaConfigList).
		Where("area_id = ? and type = 1", params.AreaID).
		Order("id desc").
		Preload("DeliveryFees").
		Count(&totalCount).
		Scopes(scopes.Page(params.Page, params.Limit)).
		Find(&deliveryAreaConfigList)
	return totalCount, deliveryAreaConfigList
}

// Create
//
//	@Description: 创建配送费/范围配置
//	@Author: Salam
//	@Time: 2025-06-12 16:37:35
//	@Param ctx *gin.Context
//	@return *shipment.DeliveryAreas
func (svc *DeliveryAreaService) Create(
	params cmsRequests.CreateDeliveryConfigsRequest, areaPolygon *shipment.Polygon, admin models.Admin,
) (*shipment.DeliveryAreas, error) {
	var (
		db   = tools.GetDB()
		area = shipment.DeliveryAreas{
			CityID:       params.CityId,
			AreaID:       params.AreaId,
			RestaurantID: &params.RestaurantId,
			ParentID:     &params.ParentID,
			Type:         params.Type,
			NameUg:       params.NameUg,
			NameZh:       params.NameZh,
			NoticeUg:     params.NoticeUg,
			NoticeZh:     params.NoticeZh,
			Option:       params.Option,
			// Polygon 字段在创建时为 nil，后续单独更新
			Radius:           params.Radius,
			AreaRunningState: 0, // 默认关闭，状态开启接口中再开启，并关闭其他
			OrderAddTime:     params.OrderAddTime,
			FeeState:         2, // 默认不实用，状态开启接口中再开启，并关闭其他
			UpdatedAdminID:   admin.ID,
		}
	)

	// 1. 如果该城市/该区域下/该餐厅下没有任何配置，那就默认开启该配置
	var totalCount int64
	totalQuery := db.Model(&shipment.DeliveryAreas{}).
		Where("city_id = ?", params.CityId).
		Where("area_id = ?", params.AreaId).
		Where("type = ?", params.Type)
	if params.ParentID > 0 && params.RestaurantId > 0 {
		totalQuery.Where("parent_id = ?", params.ParentID).
			Where("restaurant_id = ?", params.RestaurantId)
	}
	totalQuery.Count(&totalCount)

	// - 区域创建配送范围时，如果是第一个记录，默认打开
	if totalCount == 0 && params.Type == shipment.DeliveryAreasTypeArea {
		area.AreaRunningState = 1
		area.FeeState = 1
	}

	// 2. 创建记录（忽略 Polygon），GORM 会自动填充 area.ID
	if err := db.Omit("polygon").
		Create(&area).Error; err != nil {
		return nil, err
	}

	// 3. 如果有 Polygon 数据，则使用 GORM 的 Update 和 gorm.Expr 更新该字段
	if areaPolygon != nil {
		// 计算多边形的4个极值
		minLng, maxLng, minLat, maxLat := areaPolygon.GetBounds()

		updateErr := db.Model(&area).Updates(map[string]any{
			"polygon": gorm.Expr("ST_GeomFromText(?)", areaPolygon),
			"min_lng": minLng,
			"max_lng": maxLng,
			"min_lat": minLat,
			"max_lat": maxLat,
		}).Error
		if updateErr != nil {
			// 此处更新失败可能导致数据不一致，因为记录已创建
			return nil, updateErr
		}
		// 将 polygon 数据填充回返回的 area 对象中，以便上层使用
		area.Polygon = areaPolygon
	}

	return &area, nil
}

// Update
//
//	@Description: 更新配送费/范围配置
//	@Author: Salam
//	@Time: 2025-06-30 12:42:35
//	@Param ctx *gin.Context
//	@return (*shipment.DeliveryAreas
func (svc *DeliveryAreaService) Update(
	params cmsRequests.UpdateDeliveryConfigsRequest, polygon *shipment.Polygon, admin models.Admin,
) (*shipment.DeliveryAreas, error) {
	var (
		db   = tools.GetDB()
		area = shipment.DeliveryAreas{
			ID:             params.ID,
			CityID:         params.CityId,
			AreaID:         params.AreaId,
			RestaurantID:   &params.RestaurantId,
			ParentID:       &params.ParentID,
			Type:           params.Type,
			NameUg:         params.NameUg,
			NameZh:         params.NameZh,
			NoticeUg:       params.NoticeUg,
			NoticeZh:       params.NoticeZh,
			Option:         params.Option,
			Polygon:        polygon,
			Radius:         params.Radius,
			OrderAddTime:   params.OrderAddTime,
			UpdatedAdminID: admin.ID,
		}
	)

	// 写入更新前的数据日志
	existingArea, err := svc.GetDeliveryAreaById(params.ID)
	if err == nil && existingArea != nil {
		svc.RecordDeliveryAreaListLog([]shipment.DeliveryAreas{*existingArea})
	}

	// 更新
	// - 因为还没找到如何将 Polygon 类型在 Gorm 中定义并正确插入进 MySQL，暂且使用 Raw SQL 来更新该记录
	var minLng, maxLng, minLat, maxLat float64
	if polygon != nil {
		// 计算多边形的4个极值
		minLng, maxLng, minLat, maxLat = polygon.GetBounds()
	}

	execDb := db.Exec(`
		UPDATE t_delivery_areas SET
			city_id = ?,
			area_id = ?,
			`+"`type`"+` = ?,
			name_ug = ?,
			name_zh = ?,
			notice_ug = ?,
			notice_zh = ?,
			`+"`option`"+` = ?,
			`+"`polygon`"+` = ST_GeomFromText(?),
			radius = ?,
			order_add_time = ?,
			updated_admin_id = ?,
			min_lng = ?,
			max_lng = ?,
			min_lat = ?,
			max_lat = ?,
			updated_at = NOW()
		WHERE id = ?
	`,
		area.CityID, area.AreaID, area.Type,
		area.NameUg, area.NameZh, area.NoticeUg, area.NoticeZh,
		area.Option, area.Polygon, area.Radius,
		area.OrderAddTime, admin.ID,
		minLng, maxLng, minLat, maxLat,
		area.ID,
	)
	if err := execDb.Error; err != nil {
		return nil, err
	}

	return &area, nil
}

// UpdateStatus
//
//	@Description: 更新配送配置状态
//	@Author: Salam
//	@Time: 2025-07-04 16:54:35
//	@Param ctx *gin.Context
//	@return error
func (svc *DeliveryAreaService) UpdateStatus(
	params cmsRequests.UpdateDeliveryConfigStatus, admin models.Admin) error {
	var (
		db   = tools.GetDB()
		area = shipment.DeliveryAreas{}
	)

	// 写入更新前的数据日志
	existingArea, err := svc.GetDeliveryAreaById(params.ID)
	if err == nil && existingArea != nil {
		svc.RecordDeliveryAreaListLog([]shipment.DeliveryAreas{*existingArea})
	}

	// 先开启
	updateQuery := db.Model(&area).
		Where("id = ?", params.ID).
		Where("city_id = ?", params.CityId).
		Where("area_id = ?", params.AreaId)
	if params.ParentId > 0 && params.RestaurantId > 0 {
		updateQuery.Where("parent_id = ?", params.ParentId).
			Where("restaurant_id = ?", params.RestaurantId)
	}
	if err := updateQuery.Updates(map[string]any{
			"area_running_state": params.AreaRunningState,
			"fee_state":          tools.If(params.AreaRunningState == 1, 1, 2),
			"updated_admin_id":   admin.ID,
			"updated_at":         time.Now(),
		}).
		Error; err != nil {
		return err
	}

	// 如果是为了餐厅创建的 area 数据那就跳过关闭其他
	if params.ParentId > 0 && params.RestaurantId > 0 {
		return nil
	}

	// 如果开启某个区域的一个配置，那就需要关闭这个区域的其他配置
	if err := db.Model(&area).
		Where("id != ?", params.ID).
		Where("city_id = ?", params.CityId).
		Where("area_id = ?", params.AreaId).
		Where("type = ?", params.Type). // 不要区域 / 餐厅互相影响了
		Updates(map[string]any{
			"area_running_state": 0,
			"fee_state":          2,
			"updated_admin_id":   admin.ID,
			"updated_at":         time.Now(),
		}).
		Error; err != nil {
		return err
	}

	return nil
}

// UpdateNotice
//
//	@Description: 更新区域公告信息
//	@Author: Salam
//	@Time: 2025-07-09 11:06:35
//	@Param ctx *gin.Context
//	@return error
func (svc *DeliveryAreaService) UpdateNotice(
	params cmsRequests.UpdateDeliveryConfigNotice, admin models.Admin) error {
	var (
		db   = tools.GetDB()
		area = shipment.DeliveryAreas{}
	)

	// 写入更新前的数据日志
	existingArea, err := svc.GetDeliveryAreaById(params.ID)
	if err == nil && existingArea != nil {
		svc.RecordDeliveryAreaListLog([]shipment.DeliveryAreas{*existingArea})
	}

	// 先开启
	if err := db.Model(&area).
		Where("id = ?", params.ID).
		Where("city_id = ?", params.CityId).
		Where("area_id = ?", params.AreaId).
		Updates(map[string]any{
			"notice_ug":        params.NoticeUg,
			"notice_zh":        params.NoticeZh,
			"updated_admin_id": admin.ID,
			"updated_at":       time.Now(),
		}).
		Error; err != nil {
		return err
	}

	return nil
}

// UpdateMapByRestaurantIds
//
//	@Description: 根据餐厅编号更新范围配置
//	@Author: Salam
//	@Time: 2025-07-07 10:56:35
//	@Param updateMap map[string]any
//	@Param params cmsRequests.UpdateRestaurantDeliveryConfigs
//	@return error
func (svc *DeliveryAreaService) UpdateMapByRestaurantIds(
	updateMap map[string]any, params cmsRequests.UpdateRestaurantDeliveryConfigs) error {
	var (
		db   = tools.GetDB()
		area = shipment.DeliveryAreas{}
	)

	// 写入更新前的数据日志
	existingAreaList, err := svc.GetDeliveryAreaByRestaurantIds(params.ParentID, params.RestaurantIds)
	if err == nil && len(existingAreaList) > 0 {
		svc.RecordDeliveryAreaListLog(existingAreaList)
	}

	// 更新
	if err := db.Model(&area).
		Where("city_id = ?", params.CityId).
		Where("area_id = ?", params.AreaId).
		// 不存在的 restaurant_id 可以不创建，因为它原本就是跟随区域配置的
		Where("parent_id = ?", params.ParentID).
		Where("restaurant_id IN (?)", params.RestaurantIds).
		Updates(updateMap).
		Error; err != nil {
		return err
	}

	return nil
}

// GetDeliveryConfigsById
//
//	@Description: 获取配送费/范围配置详情
//	@Author: Salam
//	@Time: 2025-06-13 10:51:32
//	@Param id int
//	@return shipment.DeliveryAreas
func (svc *DeliveryAreaService) GetDeliveryConfigsById(id int) shipment.DeliveryAreas {
	var (
		db  = tools.GetDB()
		res = shipment.DeliveryAreas{}
	)
	db.Model(&shipment.DeliveryAreas{}).
		Where("id = ?", id).
		Preload("AreaInfo").
		Preload("DeliveryFees").
		Find(&res)
	return res
}

// GetDeliveryConfigsRestaurantById
//
//	@Description: 获取配送费/范围配置详情
//	@Author: Rixat
//	@Time: 2025-07-18 10:51:32
//	@Param id int
//	@return shipment.DeliveryAreas
func (svc *DeliveryAreaService) GetDeliveryConfigsRestaurantById(restaurantID int,parentID int) shipment.DeliveryAreas {
	var (
		db  = tools.GetDB()
		res = shipment.DeliveryAreas{}
	)
	db.Model(&shipment.DeliveryAreas{}).
		Where("restaurant_id = ? and parent_id = ?", restaurantID, parentID).
		Preload("AreaInfo").
		Preload("DeliveryFees").
		Find(&res)
	return res
}

func (svc *DeliveryAreaService) GetDeliveryFeeById(id int,parentID int) []shipment.DeliveryFees {
	var (
		db  = tools.GetDB()
		res = []shipment.DeliveryFees{}
	)
	db.Model(&res).
		Where("restaurant_id = ?").
		Find(&res)
	if len(res) == 0 {
		var restaurant models.Restaurant
		db.Model(&restaurant).
			Where("id = ?", id).
			Find(&restaurant)
		runningDeliveryConfig := svc.GetRunningDeliveryConfig(restaurant.AreaID)
		if runningDeliveryConfig.ID > 0 {
			db.Model(&res).
				Where("delivery_area_id = ?", runningDeliveryConfig.ID).
				Find(&res)
		}
	}
	return res
}

// GetDeliveryAreaById
//
//	@Description: 获取配送范围配置详情
//	@Author: Salam
//	@Time: 2025-07-08 10:51:32
//	@Param id int
//	@return shipment.DeliveryAreas
func (svc *DeliveryAreaService) GetDeliveryAreaById(id int) (*shipment.DeliveryAreas, error) {
	var (
		db  = tools.GetDB()
		res = shipment.DeliveryAreas{}
	)
	if err := db.Model(&shipment.DeliveryAreas{}).
		Where("id = ?", id).
		Take(&res).Error; err != nil {
		return nil, err
	}
	return &res, nil
}

// GetByRestaurantId
//
//	@Description: 根据餐厅ID获取配送配置
//	@Author: Salam
//	@Time: 2025-07-01 15:30:00
//	@Param restaurantId int
//	@return *shipment.DeliveryAreas
//	@return error
func (svc *DeliveryAreaService) GetByRestaurantId(parentId, restaurantId int) (*shipment.DeliveryAreas, error) {
	var (
		db   = tools.GetDB()
		area = shipment.DeliveryAreas{}
	)
	err := db.Model(&area).
		Where("parent_id = ?", parentId).
		Where("type = ?", shipment.DeliveryAreasTypeRestaurant).
		Where("restaurant_id = ?", restaurantId).
		Take(&area).Error
	if err != nil {
		return nil, err
	}
	return &area, nil
}

// GetDeliveryAreaByRestaurantIds
//
//	@Description: 根据餐厅ID列表获取配送配置
//	@Author: Salam
//	@Time: 2025-07-08 10:51:32
//	@Param restaurantIds []int
//	@return []shipment.DeliveryAreas
func (svc *DeliveryAreaService) GetDeliveryAreaByRestaurantIds(
	parentId int, restaurantIds []int) ([]shipment.DeliveryAreas, error) {
	var (
		db  = tools.GetDB()
		res = make([]shipment.DeliveryAreas, 0)
	)
	if err := db.Model(&res).
		Where("parent_id = ?", parentId).
		Where("type = ?", shipment.DeliveryAreasTypeRestaurant).
		Where("restaurant_id IN (?)", restaurantIds).
		Find(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}

// GetAreaDefaultPolygon
//
//	@Description: 获取配送费/范围配置详情
//	@Author: Rixat
//	@Time: 2025-06-13 10:51:32
//	@Param id int
//	@return shipment.AreaInfo
func (svc *DeliveryAreaService) GetAreaDefaultPolygon(areaID int) shipment.AreaInfo {
	var res = shipment.AreaInfo{}
	tools.GetDB().Model(&res).
		Where("id = ?", areaID).
		Find(&res)
	return res
}

func (svc *DeliveryAreaService) GetRunningDeliveryConfig(id int) shipment.DeliveryAreas {
	var (
		db  = tools.GetDB()
		res = shipment.DeliveryAreas{}
	)
	db.Model(&res).
		Where("area_id = ?", id).
		Where("type = 1").
		Where("area_running_state = 1").
		Preload("AreaInfo").
		Preload("DeliveryFees").
		Find(&res)
	return res
}

func (svc *DeliveryAreaService) GetDeliveryAreaConfigByID(id int) shipment.DeliveryAreas {
	var (
		db  = tools.GetDB()
		res = shipment.DeliveryAreas{}
	)
	db.Model(&res).
		Where("id = ?", id).
		Preload("AreaInfo").
		Preload("DeliveryFees").
		Find(&res)
	return res
}

// RecordDeliveryAreaListLog
//
//	@Description: 批量记录配送范围配置日志
//	@Author: Salam
//	@Time: 2025-07-08 18:06:32
func (svc *DeliveryAreaService) RecordDeliveryAreaListLog(deliveryAreaList []shipment.DeliveryAreas) {
	if len(deliveryAreaList) == 0 {
		return
	}

	// 构建批量插入的SQL语句
	query := `
		INSERT INTO t_delivery_areas_log (
			delivery_area_id, city_id, area_id, restaurant_id,
			parent_id, type,
			name_ug, name_zh, notice_ug, notice_zh,
			` + "`option`" + `, polygon, radius, area_running_state,
			min_lat, max_lat, min_lng, max_lng,
			order_add_time, fee_state, updated_admin_id,
			valid_begin_time, valid_end_time
		) VALUES 
	`

	// 构建参数切片
	var (
		args         []any
		valueStrings []string
		currentTime  = time.Now()
	)

	for _, dlAr := range deliveryAreaList {
		valueStrings = append(valueStrings, `(
			?, ?, ?, ?,
			?, ?,
			?, ?, ?, ?,
			?, ST_GeomFromText(?), ?, ?,
			?, ?, ?, ?,
			?, ?, ?,
			?, ?
		)`)
		args = append(args,
			dlAr.ID, dlAr.CityID, dlAr.AreaID, dlAr.RestaurantID,
			dlAr.ParentID, dlAr.Type,
			dlAr.NameUg, dlAr.NameZh, dlAr.NoticeUg, dlAr.NoticeZh,
			dlAr.Option, dlAr.Polygon, dlAr.Radius, dlAr.AreaRunningState,
			dlAr.MinLat, dlAr.MaxLat, dlAr.MinLng, dlAr.MaxLng,
			dlAr.OrderAddTime, dlAr.FeeState, dlAr.UpdatedAdminID,
			dlAr.UpdatedAt, currentTime,
		)
	}

	// 拼接完整的SQL语句
	query += strings.Join(valueStrings, ",")

	// 执行批量插入
	err := tools.Db.Exec(query, args...).Error
	if err != nil {
		tools.Logger.Error("Failed to create delivery fees log batch:", err)
		return
	}
}
