package cms

import (
	"errors"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/cms/themeActivity"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ThemeActivityService struct {
	langUtil *lang.LangUtil
	language string
}

func NewThemeActivityService(c *gin.Context) *ThemeActivityService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	return &ThemeActivityService{
		langUtil: &langUtil,
		language: language.(string),
	}
}

// Create 创建主题活动
func (s *ThemeActivityService) Create(c *gin.Context, params themeActivity.CreateRequest) (int, error) {
	var activityID int
	admin := permissions.GetAdmin(c)
	if admin.ID == 0 {
		return 0, errors.New(s.langUtil.T("unauthorized"))
	}

	// 添加事务处理
	err := tools.GetDB().Transaction(func(tx *gorm.DB) error {
		// 时间解析和验证 - 使用 Asia/Shanghai 时区
		loc, _ := time.LoadLocation("Asia/Shanghai")
		beginTime, err := time.ParseInLocation("2006-01-02 15:04:05", params.BeginTime, loc)
		if err != nil {
			return errors.New(s.langUtil.T("invalid_time_format"))
		}
		endTime, err := time.ParseInLocation("2006-01-02 15:04:05", params.EndTime, loc)
		if err != nil {
			return errors.New(s.langUtil.T("invalid_time_format"))
		}

		// 时间验证逻辑优化
		now := time.Now()
		if beginTime.Before(now) {
			return errors.New(s.langUtil.T("begin_time_must_be_after_now"))
		}
		if endTime.Before(beginTime) || endTime.Equal(beginTime) {
			return errors.New(s.langUtil.T("end_time_must_be_after_begin_time"))
		}

		// 检查活动时间冲突
		var conflictCount int64
		err = tx.Model(&models.ThemeActivity{}).
			Where("state = ? AND deleted_at IS NULL", 1).
			Where("(begin_time < ? AND end_time > ?) OR (begin_time < ? AND end_time > ?) OR (begin_time >= ? AND end_time <= ?)",
				endTime, endTime, beginTime, beginTime, beginTime, endTime).
			Count(&conflictCount).Error
		if err != nil {
			return err
		}
		if conflictCount > 0 {
			return errors.New(s.langUtil.T("theme_activity_time_conflict"))
		}

		// 创建活动
		activity := models.ThemeActivity{
			CreaterID:             admin.ID,
			NameUg:                params.NameUg,
			NameZh:                params.NameZh,
			DescUg:                params.DescUg,
			DescZh:                params.DescZh,
			BeginTime:             &beginTime,
			EndTime:               &endTime,
			DiscountPercent:       params.DiscountPercent,
			CoverUg:               params.CoverUg,
			CoverZh:               params.CoverZh,
			Color:                 params.Color,
			State:                 params.State,
			OrderType:             params.OrderType,
			ShowPreferential:      params.ShowPreferential,
			ShowSeckill:           params.ShowSeckill,
			ShowFoodType:          params.ShowFoodType,
			HasKeyword:            params.HasKeyword,
			KeywordUg:             params.KeywordUg,
			KeywordZh:             params.KeywordZh,
			ExcludeKeywordUG:      params.ExcludeKeywordUG,
			ExcludeKeywordZH:      params.ExcludeKeywordZH,
			ShareContentUg:        params.ShareContentUg,
			ShareContentZh:        params.ShareContentZh,
			ShareCoverUg:          params.ShareCoverUg,
			ShareCoverZh:          params.ShareCoverZh,
			ShareCoverToFriendUg:  params.ShareCoverToFriendUg,
			ShareCoverToFriendZh:  params.ShareCoverToFriendZh,
			CreatedAt:             &now,
			UpdatedAt:             &now,
		}

		if err := tx.Create(&activity).Error; err != nil {
			return err
		}

		activityID = activity.ID
		return nil
	})

	if err != nil {
		return 0, err
	}

	return activityID, nil
}

// Update 更新主题活动
func (s *ThemeActivityService) Update(c *gin.Context, params themeActivity.UpdateRequest) error {
	// 查找活动是否存在
	var activity models.ThemeActivity
	if err := tools.GetDB().First(&activity, params.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New(s.langUtil.T("theme_activity_not_found"))
		}
		return err
	}

	// 使用 Asia/Shanghai 时区解析时间
	loc, _ := time.LoadLocation("Asia/Shanghai")
	beginTime, err := time.ParseInLocation("2006-01-02 15:04:05", params.BeginTime, loc)
	if err != nil {
		return err
	}
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", params.EndTime, loc)
	if err != nil {
		return err
	}

	// 验证时间
	if beginTime.After(endTime) {
		return errors.New(s.langUtil.T("begin_time_must_be_before_end_time"))
	}

	// 如果修改了时间,检查是否有重叠的活动
	if !beginTime.Equal(*activity.BeginTime) || !endTime.Equal(*activity.EndTime) {
		var count int64
		err = tools.GetDB().Model(&models.ThemeActivity{}).
			Where("id != ? AND state = ? AND deleted_at IS NULL", params.ID, 1).
			Where("(begin_time <= ? AND end_time >= ?) OR (begin_time <= ? AND end_time >= ?) OR (begin_time >= ? AND end_time <= ?)",
				endTime, endTime, beginTime, beginTime, beginTime, endTime).
			Count(&count).Error
		if err != nil {
			return err
		}
		if count > 0 {
			return errors.New(s.langUtil.T("theme_activity_time_conflict"))
		}
	}

	// 更新活动信息
	activity.NameUg = params.NameUg
	activity.NameZh = params.NameZh
	activity.DescUg = params.DescUg
	activity.DescZh = params.DescZh
	activity.BeginTime = &beginTime
	activity.EndTime = &endTime
	activity.DiscountPercent = params.DiscountPercent
	activity.CoverUg = params.CoverUg
	activity.CoverZh = params.CoverZh
	activity.Color = params.Color
	activity.State = params.State
	activity.OrderType = params.OrderType
	activity.ShowPreferential = params.ShowPreferential
	activity.ShowSeckill = params.ShowSeckill
	activity.ShowFoodType = params.ShowFoodType
	activity.HasKeyword = params.HasKeyword
	activity.KeywordUg = params.KeywordUg
	activity.KeywordZh = params.KeywordZh
	activity.ExcludeKeywordUG = params.ExcludeKeywordUG
	activity.ExcludeKeywordZH = params.ExcludeKeywordZH
	activity.ShareContentUg = params.ShareContentUg
	activity.ShareContentZh = params.ShareContentZh
	activity.ShareCoverUg = params.ShareCoverUg
	activity.ShareCoverZh = params.ShareCoverZh
	activity.ShareCoverToFriendUg = params.ShareCoverToFriendUg
	activity.ShareCoverToFriendZh = params.ShareCoverToFriendZh

	return tools.GetDB().Save(&activity).Error
}

// ChangeState 修改主题活动状态
func (s *ThemeActivityService) ChangeState(c *gin.Context, params themeActivity.ChangeStateRequest) error {
	// 查找活动是否存在
	var activity models.ThemeActivity
	if err := tools.GetDB().First(&activity, params.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New(s.langUtil.T("theme_activity_not_found"))
		}
		return err
	}

	// 更新状态
	activity.State = params.State
	return tools.GetDB().Save(&activity).Error
}

// Delete 软删除主题活动
func (s *ThemeActivityService) Delete(c *gin.Context, params themeActivity.DeleteRequest) error {
	// 查找活动是否存在
	var activity models.ThemeActivity
	if err := tools.GetDB().First(&activity, params.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New(s.langUtil.T("theme_activity_not_found"))
		}
		return err
	}

	// 使用GORM的软删除功能
	return tools.GetDB().Delete(&activity).Error
}

// List 获取主题活动列表
func (s *ThemeActivityService) List(c *gin.Context, params themeActivity.ListRequest) ([]models.ThemeActivity, int64, error) {
	query := tools.GetDB().Model(&models.ThemeActivity{}).
		Preload("Creator"). // 预加载创建人信息
		Where("deleted_at IS NULL")

	// 关键词搜索
	if params.Keyword != "" {
		query = query.Where("name_ug LIKE ? OR name_zh LIKE ?", "%"+params.Keyword+"%", "%"+params.Keyword+"%")
	}

	// 状态筛选
	if params.State != nil {
		query = query.Where("state = ?", *params.State)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	var activities []models.ThemeActivity
	err := query.
		Order("created_at DESC").
		Offset((params.Page - 1) * params.Limit).
		Limit(params.Limit).
		Find(&activities).Error

	return activities, total, err
}

// Detail 获取主题活动详情
func (s *ThemeActivityService) Detail(c *gin.Context, id int) (models.ThemeActivity, error) {
	var activity models.ThemeActivity
	
	err := tools.GetDB().
		Preload("Creator"). // 预加载创建人信息
		Where("id = ?", id).
		First(&activity).Error
	
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return activity, errors.New(s.langUtil.T("theme_activity_not_found"))
		}
		return activity, err
	}

	return activity, nil
}
