package cms

import (
	"errors"
	"fmt"
	"mulazim-api/constants"
	"mulazim-api/lang"
	"mulazim-api/models"
	shipmentModels "mulazim-api/models/shipment"
	resource "mulazim-api/resources/cms/shipment"
	"mulazim-api/scopes"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ShipmentSpecialWeather struct {
	langUtil *lang.LangUtil
	language string
}

func NewShipmentSpecialWeather(c *gin.Context) *ShipmentSpecialWeather {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	ShipmentSpecialWeather := ShipmentSpecialWeather{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &ShipmentSpecialWeather
}

// / Create
//
// @Description: 创建特殊天气
// @Author: Rixat
// @Time: 2023-11-06 09:59:29
// @receiver
// @param c *gin.Context
func (s ShipmentSpecialWeather) Create(admin models.Admin, params resource.SpecialWeather) error {
	// 验证同一个时间端是否存在一样名称记录
	var weather shipmentModels.ShipperSpecialWeather
	tools.Db.Model(weather).
		Where("area_id=?", params.AreaID).
		Where("name_ug=? or name_zh=?", params.NameUg, params.NameZh).
		Where("(start_time between ? and ?) or (end_time between ? and ?)", params.StartTime, params.EndTime, params.StartTime, params.EndTime).
		Scan(&weather)
	if weather.ID > 0 {
		return errors.New("name_already_exist")
	}
	// 创建内容
	weather = shipmentModels.ShipperSpecialWeather{
		CityID:      params.CityID,
		AreaID:      params.AreaID,
		NameUg:      params.NameUg,
		NameZh:      params.NameZh,
		StartTime:   carbon.Parse(params.StartTime).Carbon2Time(),
		EndTime:     carbon.Parse(params.EndTime).Carbon2Time(),
		ShipmentFee: params.ShipmentFee,
		State:       2, // 创建时默认关闭状态
		CreatedAt:   carbon.Now().Carbon2Time(),
	}
	// 如果代理提交时，填充代理区域ID
	if params.CityID == 0 && params.AreaID == 0 {
		weather.CityID = admin.AdminCityID
		weather.AreaID = admin.AdminAreaID
	}
	err := tools.Db.Model(weather).Create(&weather).Error
	if err != nil {
		tools.Logger.Errorf("FATAL 配送员通知创建出错：%s", err.Error())
		return errors.New("failed")
	}
	return nil
}

// Update
//
// @Description: 更新特殊天气
// @Author: Rixat
// @Time: 2023-11-06 09:59:42
// @receiver
// @param c *gin.Context
func (s ShipmentSpecialWeather) Update(ID int, params resource.SpecialWeather) error {
	var weather shipmentModels.ShipperSpecialWeather
	tools.Db.Model(weather).Where("id=?", ID).First(&weather)
	if weather.ID == 0 {
		return errors.New("not_found")
	}
	// 验证统一个时间段修改后的名称是否存在
	if weather.NameUg != params.NameUg || weather.NameZh != params.NameZh {
		var weatherValid shipmentModels.ShipperSpecialWeather
		tools.Db.Model(weatherValid).
			Where("area_id=?", weather.AreaID).
			Where("id <> ?", ID).
			Where("(name_ug=? or name_zh=?)", params.NameUg, params.NameZh).
			Where("(start_time between ? and ?) or (end_time between ? and ?)", params.StartTime, params.EndTime, params.StartTime, params.EndTime).
			Scan(&weatherValid)
		if weatherValid.ID > 0 {
			return errors.New("name_already_exist")
		}
	}
	// 更新内容
	weather.NameUg = params.NameUg
	weather.NameZh = params.NameZh
	weather.StartTime = carbon.Parse(params.StartTime).Carbon2Time()
	weather.EndTime = carbon.Parse(params.EndTime).Carbon2Time()
	weather.ShipmentFee = params.ShipmentFee
	weather.UpdatedAt = carbon.Now().Carbon2Time()
	err := tools.Db.Save(&weather).Error
	if err != nil {
		tools.Logger.Errorf("FATAL 更新特殊天气出错：%s", err.Error())
		return errors.New("failed")
	}
	return nil
}

// ChangeState
//
// @Description: 修改特殊天气状态
// @Author: Rixat
// @Time: 2023-11-06 09:59:50
// @receiver
// @param c *gin.Context
func (s ShipmentSpecialWeather) ChangeState(admin models.Admin, ID int, state int) error {
	// 验证统一个时间段修改后的名称是否存在
	var weather shipmentModels.ShipperSpecialWeather
	tools.Db.Model(weather).Where("id=?", ID).First(&weather)
	if weather.ID == 0 {
		return errors.New("not_found")
	}
	if state == 1 {
		var hasWeather shipmentModels.ShipperSpecialWeather
		tools.Db.Model(weather).
			Where("id <> ?", ID).
			Where("area_id = ?", weather.AreaID).
			Where("state = 1").
			Where("start_time between ? and ? or end_time between ? and ?", weather.StartTime, weather.EndTime, weather.StartTime, weather.EndTime).
			First(&hasWeather)
		if hasWeather.ID > 0 {
			return errors.New(fmt.Sprintf(s.langUtil.T("this_time_opening_other_special_weather"), tools.GetNameByLang(hasWeather, s.language)))
		}
	}
	// 更新状态
	weather.State = state
	weather.UpdatedAt = carbon.Now().Carbon2Time()
	err := tools.Db.Save(&weather).Error
	if err != nil {
		tools.Logger.Errorf("FATAL 修改特殊天气状态出错：%s", err.Error())
		return errors.New("failed")
	}
	return nil
}

// Delete
//
// @Description: 删除特殊天气
// @Author: Rixat
// @Time: 2023-11-06 09:59:54
// @receiver
// @param c *gin.Context
func (s ShipmentSpecialWeather) Delete(ID int) error {
	// 验证统一个时间段修改后的名称是否存在
	var weather shipmentModels.ShipperSpecialWeather
	tools.Db.Model(weather).Where("id=?", ID).First(&weather)
	if weather.ID == 0 {
		return errors.New("not_found")
	}
	// 验证当前特殊天气已经有配送费记录
	var count int64
	tools.Db.Model(shipmentModels.ShipperIncome{}).
		Where("template_id=? and type=?", ID, constants.TypeSpecialWeather).
		Count(&count)
	if count > 0 {
		return errors.New("this_special_weather_already_used_unable_delete")
	}
	err := tools.Db.Delete(&shipmentModels.ShipperSpecialWeather{}, ID).Error
	if err != nil {
		tools.Logger.Errorf("FATAL 删除特殊天气出错：%s", err.Error())
		return errors.New("failed")
	}
	return nil
}

// Detail
//
// @Description: 特殊天气详情
// @Author: Rixat
// @Time: 2023-11-06 09:59:57
// @receiver
// @param c *gin.Context
func (s ShipmentSpecialWeather) Detail(ID int) (map[string]interface{}, error) {
	var weather shipmentModels.ShipperSpecialWeather
	tools.Db.Model(weather).Where("id=?", ID).First(&weather)
	if weather.ID == 0 {
		return nil, errors.New("not_found")
	}
	// 返回结果
	result := map[string]interface{}{
		"name_ug":      weather.NameUg,
		"name_zh":      weather.NameZh,
		"name":         tools.GetNameByLang(weather, s.language),
		"shipment_fee": weather.ShipmentFee,
		"start_time":   tools.TimeFormatYmdHis(&weather.StartTime),
		"end_time":     tools.TimeFormatYmdHis(&weather.EndTime),
		"state":        weather.State,
	}
	return result, nil
}

// List
//
// @Description: 特殊天气列表
// @Author: Rixat
// @Time: 2023-11-06 10:00:01
// @receiver
// @param c *gin.Context
func (s ShipmentSpecialWeather) List(page int, limit int, cityId int, areaId int, kw string, state int, sort string) map[string]interface{} {
	var weatherList []shipmentModels.ShipperSpecialWeather
	query := tools.Db.Model(weatherList)
	// 条件
	if cityId > 0 {
		query.Where("t_shipper_special_weather.city_id = ?", cityId)
	}
	if areaId > 0 {
		query.Where("t_shipper_special_weather.area_id = ?", areaId)
	}
	if len(kw) > 0 {
		query.Where("t_shipper_special_weather.name_ug like ? or t_shipper_special_weather.name_zh like ?", "%"+kw+"%", "%"+kw+"%")
	}
	if state > 0 {
		query.Where("t_shipper_special_weather.state = ?", state)
	}
	// 查询数据
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Preload("City").Preload("Area").Preload("Incomes", "type="+tools.ToString(constants.TypeSpecialWeather)).
			Group("t_shipper_special_weather.id").
			Scopes(scopes.Page(page, limit)).
			Order(sort).
			Find(&weatherList)
	}
	// 格式化结果
	var resMap []map[string]interface{}
	for _, v := range weatherList {
		resMap = append(resMap, map[string]interface{}{
			"id":                 v.ID,
			"name":               tools.GetNameByLang(v, s.language),
			"area_name":          tools.GetNameByLang(v.City, s.language),
			"city_name":          tools.GetNameByLang(v.Area, s.language),
			"start_time":         tools.TimeFormatYmdHis(&v.StartTime),
			"end_time":           tools.TimeFormatYmdHis(&v.EndTime),
			"state":              v.State,
			"shipment_fee":       v.ShipmentFee,
			"order_count":        len(v.Incomes),
			"total_shipment_fee": s.GetTotalIncomeAmount(v.Incomes),
		})
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": resMap,
	}
	return result
}

// 计算总收入
func (s ShipmentSpecialWeather) GetTotalIncomeAmount(incomes []shipmentModels.ShipperIncome) int {
	totalAmount := 0
	for _, v := range incomes {
		totalAmount += v.Amount
	}
	return totalAmount
}
