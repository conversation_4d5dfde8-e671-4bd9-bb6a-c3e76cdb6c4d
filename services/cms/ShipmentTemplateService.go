package cms

import (
	"encoding/json"
	"errors"
	"mulazim-api/lang"
	"mulazim-api/models"
	shipmentModels "mulazim-api/models/shipment"
	resource "mulazim-api/resources/cms/shipment"
	"mulazim-api/scopes"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ShipperIncomeTemplateService struct {
	langUtil *lang.LangUtil
	language string
}

func NewShipperIncomeTemplateService(c *gin.Context) *ShipperIncomeTemplateService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	shipmentTemplateService := ShipperIncomeTemplateService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &shipmentTemplateService
}

// CreateBaseInfo
//
// @Description: 创建配送员账户信息
// @Author: Rixat
// @Time: 2023-10-24 08:11:09
// @receiver
// @param c *gin.Context
func (s ShipperIncomeTemplateService) CreateBaseInfo(admin models.Admin, params resource.ShipmentBaseInfo) (map[string]interface{}, error) {
	// 验证用户名和手机号是否存在
	var template shipmentModels.ShipperIncomeTemplate
	tools.Db.Model(template).Where("name_ug=? or name_zh=?", params.NameUg, params.NameZh).Where("area_id=?", params.AreaID).Scan(&template)
	if (params.ID == 0 && template.ID > 0) || (params.ID > 0 && template.ID != params.ID) {
		return nil, errors.New("name_already_exist")
	}
	// 验证配送费规则
	ruleContent, err := s.ValidateRuleContent(params)
	if err != nil {
		return nil, err
	}
	// 验证配送费规则
	specialContent, err := s.ValidateSpecialContent(params.SpecialShipmentContent)
	if err != nil {
		return nil, err
	}
	deductionContent, err := s.ValidateDeductionContent(params)
	if err != nil {
		return nil, err
	}

	if(params.InviteUserFee < 100){
		return nil, errors.New("invite_user_fee_invalid")
	}
	if(params.OrderPercent < 200){
		return nil, errors.New("order_percent_invalid")
	}
	if(params.InviteOldUserFee < 20){
		return nil, errors.New("invite_old_user_fee_invalid")
	}
	if(params.OrderOldUserPercent < 100){
		return nil, errors.New("order_old_user_percent_invalid")
	}
	// 创建管理员信息
	template = shipmentModels.ShipperIncomeTemplate{
		OperatorID:             admin.ID,
		CityIdID:               params.CityID,
		AreaID:                 params.AreaID,
		NameUg:                 params.NameUg,
		NameZh:                 params.NameZh,
		BaseSalary:             params.BaseSalary,
		RuleType:               params.RuleType,
		RuleContent:            &ruleContent,
		RuleOrderCountType:     &params.RuleOrderCountType,
		SpecialShipmentState:   params.SpecialShipmentState,
		SpecialShipmentContent: &specialContent,
		CommentDeductionFee:    params.CommentDeductionFee,
		CommentIncrementFee:    params.CommentIncrementFee,
		ComplainDeductionFee:   params.ComplainDeductionFee,
		LateDeductionType:      params.LateDeductionType,
		LateDeductionContent:   &deductionContent,
		CreatedAt:              carbon.Now().Carbon2Time(),
		InviteUserFee:          params.InviteUserFee,
		InviteOldUserFee:          params.InviteOldUserFee,
		OrderPercent:           params.OrderPercent,
		OrderOldUserPercent:           params.OrderOldUserPercent,
	}
	// 代理创建时前端不传区域ID,通过登录信息填充属于的区域ID
	if template.CityIdID == 0 {
		template.CityIdID = admin.AdminCityID
	}
	if template.AreaID == 0 {
		template.AreaID = admin.AdminAreaID
	}
	if params.ID > 0 {
		// 验证是否使用过该模板
		var usedCount int64
		tools.Db.Model(models.Admin{}).Where("shipper_income_template_id=?", params.ID).Scan(&usedCount)
		if usedCount > 0 {
			return nil, errors.New("template_unable_edit")
		}
		template.ID = params.ID
		err := tools.Db.Save(&template).Error
		if err != nil {
			tools.Logger.Errorf("FATAL 更新配送费模板出错：%s", err.Error())
			return nil, errors.New("failed")
		}
	} else {
		err = tools.Db.Model(template).Create(&template).Error
		if err != nil {
			tools.Logger.Errorf("FATAL 创建配送费模板出错：%s", err.Error())
			return nil, errors.New("failed")
		}
	}
	// 返回结果
	result := map[string]interface{}{
		"template_id": template.ID,
	}
	return result, nil
}

// ValidateRuleContent
//
// @Description: 验证配送费规则
// @Author: Rixat
// @Time: 2023-11-03 07:52:08
// @receiver
// @param c *gin.Context
func (s ShipperIncomeTemplateService) ValidateRuleContent(params resource.ShipmentBaseInfo) (string, error) {
	ruleContent := ""
	// 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
	if params.RuleType == 1 {
		var ruleContentArr []resource.FixShipmentFeeRule
		err := json.Unmarshal([]byte(params.RuleContent), &ruleContentArr)
		if err != nil {
			return "", errors.New("base_rule_content_enable_decode")
		}
		ruleContentMap := make([]map[string]interface{}, 0)
		for _, value := range ruleContentArr {
			shipmentFee := value.FixShipmentFee
			if value.FixShipmentFee < 0 {
				return "", errors.New("invalid_price")
			}
			ruleContentMap = append(ruleContentMap, map[string]interface{}{
				"fix_shipment_fee": shipmentFee,
			})
		}
		ruleContent = tools.MapArrayToString(ruleContentMap)
	}
	if params.RuleType == 2 {
		var distanceBaseShipmentFeeRuleArr []resource.DistanceBaseShipmentFeeRule
		err := json.Unmarshal([]byte(params.RuleContent), &distanceBaseShipmentFeeRuleArr)
		if err != nil || len(distanceBaseShipmentFeeRuleArr) == 0 {
			return "", errors.New("base_rule_content_enable_decode")
		}
		ruleContentMap := make([]map[string]interface{}, 0)
		for index, value := range distanceBaseShipmentFeeRuleArr {
			distance := value.Distance
			shipment := value.ShipmentFee
			if value.Distance <= 0 {
				return "", errors.New("distance_must_greater_zero")
			}
			if shipment <= 0 {
				return "", errors.New("shipment_fee_must_greater_zero")
			}
			// 验证距离是否按顺序正确（小到大）
			if index > 0 && value.Distance <= distanceBaseShipmentFeeRuleArr[index-1].Distance {
				return "", errors.New("distance_must_asc")
			}
			// 验证距离是否正确
			if index > 0 && value.ShipmentFee <= distanceBaseShipmentFeeRuleArr[index-1].ShipmentFee {
				return "", errors.New("max_distance_shipment_must_greater_distance_min_shipment")
			}
			ruleContentMap = append(ruleContentMap, map[string]interface{}{
				"distance":     distance,
				"shipment_fee": shipment,
			})
		}
		ruleContent = tools.MapArrayToString(ruleContentMap)
	}
	if params.RuleType == 3 {
		var taxiBaseShipmentFeeRuleArr []resource.TaxiBaseShipmentFeeRule
		err := json.Unmarshal([]byte(params.RuleContent), &taxiBaseShipmentFeeRuleArr)
		if err != nil || len(taxiBaseShipmentFeeRuleArr) == 0 {
			return "", errors.New("base_rule_content_enable_decode")
		}
		ruleContentMap := make([]map[string]interface{}, 0)
		for _, value := range taxiBaseShipmentFeeRuleArr {
			distance := value.Distance
			fixed_start_fee := value.FixedStartFee
			price_per_kilometer := value.PricePerKilometer
			if value.Distance <= 0 {
				return "", errors.New("distance_must_greater_zero")
			}
			if fixed_start_fee <= 0 {
				return "", errors.New("shipment_fee_must_greater_zero")
			}
			if price_per_kilometer <= 0 {
				return "", errors.New("shipment_fee_must_greater_zero")
			}
			ruleContentMap = append(ruleContentMap, map[string]interface{}{
				"distance":            distance,
				"fixed_start_fee":     fixed_start_fee,
				"price_per_kilometer": price_per_kilometer,
			})
		}
		ruleContent = tools.MapArrayToString(ruleContentMap)
	}
	if params.RuleType == 4 {
		var orderCountBaseShipmentFeeRuleArr []resource.OrderCountBaseShipmentFeeRule
		err := json.Unmarshal([]byte(params.RuleContent), &orderCountBaseShipmentFeeRuleArr)
		if err != nil || len(orderCountBaseShipmentFeeRuleArr) == 0 {
			return "", errors.New("base_rule_content_enable_decode")
		}
		ruleContentMap := make([]map[string]interface{}, 0)
		for index, value := range orderCountBaseShipmentFeeRuleArr {
			start_order_count := value.StartOrderCount
			shipment_fee := value.ShipmentFee
			if shipment_fee <= 0 {
				return "", errors.New("shipment_fee_must_greater_zero")
			}
			if start_order_count < 0 {
				return "", errors.New("order_count_not_latter_zero")
			}
			// 验证距离是否按顺序正确（小到大）
			if index > 0 && value.StartOrderCount <= orderCountBaseShipmentFeeRuleArr[index-1].StartOrderCount {
				return "", errors.New("order_count_must_asc")
			}
			// 验证价格是否正确
			if index > 0 && value.ShipmentFee <= orderCountBaseShipmentFeeRuleArr[index-1].ShipmentFee {
				return "", errors.New("max_order_count_shipment_must_greater_min_order_count_min_shipment")
			}
			ruleContentMap = append(ruleContentMap, map[string]interface{}{
				"start_order_count": start_order_count,
				"shipment_fee":      shipment_fee,
			})
		}
		ruleContent = tools.MapArrayToString(ruleContentMap)
	}
	if params.InviteUserFee <= 0 || params.InviteUserFee >= 10000 {
		return "", errors.New("invalid_invite_user_fee")
	}
	if params.InviteOldUserFee <= 0 || params.InviteOldUserFee >= 10000 {
		return "", errors.New("invalid_invite_old_user_fee")
	}
	if params.OrderPercent <= 0 || params.OrderPercent >= 10000 { // 不能 大于 10000 表示100% 不能小于 0
		return "", errors.New("invalid_order_percent")
	}
	return ruleContent, nil
}

// ValidateSpecialContent
//
// @Description: 验证特殊配送费规则
// @Author: Rixat
// @Time: 2023-11-03 07:57:49
// @receiver
// @param c *gin.Context
func (s ShipperIncomeTemplateService) ValidateSpecialContent(specialShipmentContentStr string) (string, error) {
	var specialShipmentContentArr []resource.SpecialShipment
	err := json.Unmarshal([]byte(specialShipmentContentStr), &specialShipmentContentArr)
	if err != nil {
		return "", errors.New("special_rule_content_enable_decode")
	}
	ruleContentMap := make([]map[string]interface{}, 0)
	for index, value := range specialShipmentContentArr {
		// 验证时间段是否小到大顺序，并时间段不能交叉
		startTimeCarbon := carbon.Parse(carbon.Now().ToDateString() + " " + value.StartTime + ":00")
		endTimeCarbon := carbon.Parse(carbon.Now().ToDateString() + " " + value.EndTime + ":00")
		if endTimeCarbon.Lte(startTimeCarbon) {
			return "", errors.New("min_time_not_greater_max_time")
		}
		if index > 0 {
			frontStepEndTimeCarbon := carbon.Parse(carbon.Now().ToDateString() + " " + specialShipmentContentArr[index-1].EndTime + ":00")
			if startTimeCarbon.Lt(frontStepEndTimeCarbon) {
				return "", errors.New("time_must_asc_and_not_conflict")
			}
		}

		distanceSteps := value.DistanceStep
		for stepIndex, step := range distanceSteps {
			if stepIndex > 0 && step.Distance < distanceSteps[stepIndex-1].Distance {
				return "", errors.New("distance_must_asc")
			}
			if stepIndex > 0 && step.ShipmentFee < distanceSteps[stepIndex-1].ShipmentFee {
				return "", errors.New("max_distance_shipment_must_greater_distance_min_shipment")
			}
		}
		ruleContentMap = append(ruleContentMap, map[string]interface{}{
			"start_time":    value.StartTime,
			"end_time":      value.EndTime,
			"time_list":     value.TimeList,
			"distance_step": distanceSteps,
		})
	}
	ruleContent := tools.MapArrayToString(ruleContentMap)
	return ruleContent, nil
}

// ValidateSpecialContent
//
// @Description: 验证扣款规则
// @Author: Rixat
// @Time: 2023-11-03 07:57:49
// @receiver
// @param params resource.ShipmentDeductionInfo
func (s ShipperIncomeTemplateService) ValidateDeductionContent(info resource.ShipmentBaseInfo) (string, error) {
	ruleContentMap := make([]map[string]interface{}, 0)
	// 固定扣款
	if info.LateDeductionType == 1 {
		var lateDeductionFixArr []resource.LateDeductionFix
		err := json.Unmarshal([]byte(info.LateDeductionContent), &lateDeductionFixArr)
		if err != nil {
			return "", errors.New("late_rule_content_enable_decode")
		}
		for _, value := range lateDeductionFixArr {
			deductionFee := value.DeductionFee
			if deductionFee <= 0 {
				return "", errors.New("invalid_deduction_fee")
			}
			ruleContentMap = append(ruleContentMap, map[string]interface{}{
				"deduction_fee": deductionFee,
			})
		}

	}
	// 按迟到时间扣款
	if info.LateDeductionType == 2 {
		var lateDeductionTimeArr []resource.LateDeductionTime
		err := json.Unmarshal([]byte(info.LateDeductionContent), &lateDeductionTimeArr)
		if err != nil {
			return "", errors.New("late_rule_content_enable_decode")
		}
		for _, value := range lateDeductionTimeArr {
			minMinute := value.MinMinute
			maxMinute := value.MaxMinute
			deduction_fee := value.DeductionFee
			if deduction_fee < 0 {
				return "", errors.New("invalid_deduction_fee")
			}
			if minMinute >= maxMinute {
				return "", errors.New("min_time_not_greater_max_time")
			}
			if minMinute < 0 {
				return "", errors.New("min_time_must_greater_zero")
			}
			if maxMinute <= 0 {
				return "", errors.New("max_time_must_greater_zero")
			}
			ruleContentMap = append(ruleContentMap, map[string]interface{}{
				"min_minute":    value.MinMinute,
				"max_minute":    value.MaxMinute,
				"deduction_fee": value.DeductionFee,
			})
		}
	}
	ruleContent := tools.MapArrayToString(ruleContentMap)
	return ruleContent, nil
}

// Delete
//
// @Description: 删除配送费模板
// @Author: Rixat
// @Time: 2023-11-03 08:02:29
// @receiver
// @param c *gin.Context
func (s ShipperIncomeTemplateService) Delete(admin models.Admin, ID int) error {
	// 验证当前模板是否已经绑定过
	var count int64
	tools.Db.Model(models.Admin{}).Where("shipper_income_template_id=?", ID).Count(&count)
	if count > 0 {
		return errors.New("already_used_unable_delete")
	}
	// 验证是否配送员收入记录中使用模板
	tools.Db.Model(shipmentModels.ShipperIncome{}).Where("template_id=?", ID).Count(&count)
	if count > 0 {
		return errors.New("already_used_unable_delete")
	}
	tools.Db.Model(shipmentModels.AdminTemplateUpdate{}).Where("template_id=?", ID).Count(&count)
	if count > 0 {
		return errors.New("already_used_unable_delete")
	}
	// 删除模板
	err := tools.Db.Delete(&shipmentModels.ShipperIncomeTemplate{}, ID).Error
	if err != nil {
		tools.Logger.Errorf("FATAL 删除配送费模板出错：%s", err.Error())
		return errors.New("failed")
	}
	return nil
}

// Detail
//
// @Description: 配送费模板详情
// @Author: Rixat
// @Time: 2023-11-03 08:02:47
// @receiver
// @param c *gin.Context
func (s ShipperIncomeTemplateService) Detail(admin models.Admin, ID int) (map[string]interface{}, error) {
	// 获取数据
	var template map[string]interface{}
	tools.Db.Model(shipmentModels.ShipperIncomeTemplate{}).Where("id=?", ID).First(&template)
	if template == nil || tools.ToInt(template["id"]) == 0 {
		return nil, errors.New("not_found")
	}
	// 格式化返回结果
	result := map[string]interface{}{
		"shipment_base": map[string]interface{}{
			"city_id":               template["city_id"],
			"area_id":               template["area_id"],
			"name_ug":               template["name_ug"],
			"name_zh":               template["name_zh"],
			"base_salary":           template["base_salary"],
			"rule_type":             template["rule_type"],
			"rule_content":          template["rule_content"],
			"rule_order_count_type": template["rule_order_count_type"],
		},
		"shipment_special": map[string]interface{}{
			"special_shipment_state":   template["special_shipment_state"],
			"special_shipment_content": template["special_shipment_content"],
		},
		"shipment_deduction": map[string]interface{}{
			"late_deduction_type":    template["late_deduction_type"],
			"comment_deduction_fee":  template["comment_deduction_fee"],
			"comment_increment_fee":  template["comment_increment_fee"],
			"complain_deduction_fee": template["complain_deduction_fee"],
			"late_deduction_content": template["late_deduction_content"],
		},
		"introduce_customer": map[string]interface{}{
			"invite_user_fee": template["invite_user_fee"],
			"invite_old_user_fee": template["invite_old_user_fee"],
			"order_percent":   template["order_percent"],
			"order_old_user_percent":   template["order_old_user_percent"],
		},
	}
	return result, nil
}

// List
//
// @Description: 配送费模板列表
// @Author: Rixat
// @Time: 2023-11-03 08:03:05
// @receiver
// @param c *gin.Context
func (s ShipperIncomeTemplateService) List(page int, limit int, cityId int, areaId int, kw string, sort string) map[string]interface{} {
	// 查询条件
	query := tools.Db.Model(shipmentModels.ShipperIncomeTemplate{})
	if cityId > 0 {
		query.Where("t_shipper_income_template.city_id = ?", cityId)
	}
	if areaId > 0 {
		query.Where("t_shipper_income_template.area_id = ?", areaId)
	}
	if len(kw) > 0 {
		query = query.Where("t_shipper_income_template.name_ug like ? or t_shipper_income_template.name_zh like ?", "%"+kw+"%", "%"+kw+"%")
	}
	// 获取数据
	var totalCount int64
	query.Count(&totalCount)
	templateResMapArr := make([]map[string]interface{}, 0)
	if totalCount > 0 {
		selectRows := "t_shipper_income_template.id,"
		selectRows += "b_area.name_" + s.language + " as area_name,"
		selectRows += "b_city.name_" + s.language + " as city_name,"
		selectRows += "t_shipper_income_template.state,"
		selectRows += "t_shipper_income_template.city_id,"
		selectRows += "t_shipper_income_template.name_" + s.language + " as name,"
		selectRows += "t_shipper_income_template.base_salary,"
		selectRows += "t_shipper_income_template.rule_type,"
		selectRows += "t_shipper_income_template.special_shipment_state,"
		selectRows += "t_shipper_income_template.complain_deduction_fee,"
		selectRows += "t_shipper_income_template.late_deduction_type,"
		selectRows += "t_shipper_income_template.order_percent,"
		selectRows += "t_shipper_income_template.invite_user_fee,"
		selectRows += "t_shipper_income_template.invite_old_user_fee,"
		selectRows += "t_shipper_income_template.order_old_user_percent,"
		selectRows += "count(t_admin.id) as used_count"
		query.Select(selectRows).
			Joins("left join t_admin on t_admin.shipper_income_template_id = t_shipper_income_template.id").
			Joins("left join b_city on t_shipper_income_template.city_id = b_city.id").
			Joins("left join b_area on t_shipper_income_template.area_id = b_area.id").
			Group("t_shipper_income_template.id").
			Scopes(scopes.Page(page, limit)).
			Order(sort).
			Scan(&templateResMapArr)
	}

	// 格式化列表内容
	templateList := make([]map[string]interface{}, 0)
	for _, value := range templateResMapArr {
		templateList = append(templateList, map[string]interface{}{
			"id":                       value["id"],
			"city_name":                value["city_name"],
			"area_name":                value["area_name"],
			"name":                     value["name"],
			"state":                    value["state"],
			"base_salary":              value["base_salary"],
			"rule_type_name":           s.langUtil.TArr("RuleTypeNames")[tools.ToInt(value["rule_type"])],
			"special_shipment_state":   value["special_shipment_state"],
			"complain_deduction_fee":   value["complain_deduction_fee"],
			"late_deduction_type_name": s.langUtil.TArr("DeductionTypeNames")[tools.ToInt(value["late_deduction_type"])],
			"used_count":               value["used_count"],
			"invite_user_fee":          value["invite_user_fee"],
			"order_percent":            value["order_percent"],
			"invite_old_user_fee":            value["invite_old_user_fee"],
			"order_old_user_percent":            value["order_old_user_percent"],
		})
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": templateList,
	}
	return result
}
