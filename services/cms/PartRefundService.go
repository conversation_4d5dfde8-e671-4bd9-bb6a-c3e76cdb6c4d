package cms

import (
	"mulazim-api/errors"
	"mulazim-api/lang"
	"mulazim-api/models"
	partRefundRequest "mulazim-api/requests/cms/partRefund"
	"mulazim-api/scopes"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type PartRefundService struct {
	langUtil *lang.LangUtil
	language string
}

// RefundReasonList
//
//	@Author: YaKupJan
//	@Date: 2024-11-26 18:50:39
//	@Description: 退款原因列表
//	@receiver p
//	@param pagination
//	@return []models.Dictionary
//	@return int64
func (p PartRefundService) RefundReasonList(pagination tools.Pagination,state int) ([]models.Dictionary, int64) {
	db := tools.GetDB()
	// 字典列表
	var partRefundReasonList []models.Dictionary
	var total int64
	query := db.Model(models.Dictionary{}).
		Where("type = ?", models.DICTIONARY_TYPE_PART_REFUND_REASON)
	if state != 0 {
		query = query.Where("state = ?", state)
	}
	query.Count(&total)
	query.
		Order("weight").
		Scopes(scopes.Page(pagination.Page, pagination.Limit)).
		Find(&partRefundReasonList)
	return partRefundReasonList, total
}

// CreateRefundReason
//
//	@Author: YaKupJan
//	@Date: 2024-11-27 11:11:15
//	@Description: 添加退款原因
//	@receiver p
//	@param nameZh
//	@param nameUg
//	@param state
//	@param weight
func (p PartRefundService) CreateRefundReason(nameZh string, nameUg string, state int, weight int, subType int) {
	db := tools.GetDB()
	err := db.Model(models.Dictionary{}).Create(&models.Dictionary{
		NameUg: nameUg,
		NameZh: nameZh,
		Weight: weight,
		State:  state,
		Type:   models.DICTIONARY_TYPE_PART_REFUND_REASON,
		SubType: subType,
	}).Error
	if err != nil {
		customError := errors.CustomError{
			Msg: "create_fail",
		}
		panic(customError)
	}
}

// UpdateRefundReason
//
//  @Author: YaKupJan
//  @Date: 2024-11-27 12:42:58
//  @Description: 修改退款原因
//  @receiver p
//  @param id
//  @param nameZh
//  @param nameUg
//  @param state
//  @param weight
func (p PartRefundService) UpdateRefundReason(id int, nameZh string, nameUg string, state int, weight int, subType int) {
	db := tools.GetDB()
	var dictionary models.Dictionary
	db.Model(models.Dictionary{}).Find(&dictionary, id)
	if dictionary.ID == 0 {
		customError := errors.CustomError{
			Msg: "not_found",
		}
		panic(customError)
		return
	}
	if nameZh != "" {
		dictionary.NameZh = nameZh
	}
	if nameUg != "" {
		dictionary.NameUg = nameUg
	}
	if state != 0 {
		dictionary.State = state
	}
	if weight != 0 {
		dictionary.Weight = weight
	}
	if subType != 0{
		dictionary.SubType = subType
	}
	if updateErr := db.Save(&dictionary).Error; updateErr != nil {
		customError := errors.CustomError{
			Msg: "update_fail",
		}
		panic(customError)
	}
}

// DeleteRefundReason
//
//  @Author: YaKupJan
//  @Date: 2024-11-27 16:11:00
//  @Description: 删除退款原因
//  @receiver p
//  @param id
func (p PartRefundService) DeleteRefundReason(id int) {
	db := tools.GetDB()
	var dictionary models.Dictionary
	db.Model(models.Dictionary{}).Where("type = ?", models.DICTIONARY_TYPE_PART_REFUND_REASON).Where("id = ?", id).Find(&dictionary)
	if dictionary.ID == 0 {
		panic(errors.NewCustomError("not_found"))
	}
	// 判断这个原因是不是已经使用
	var orderPartRefund models.OrderPartRefund
	db.Model(models.OrderPartRefund{}).Where("part_refund_reason_id = ?", dictionary.ID).First(&orderPartRefund)
	if orderPartRefund.ID != 0 {
		panic(errors.NewCustomError("already_used_unable_delete"))
	}
	// 如果 没有使用 则进行删除
	if err := db.Delete(&dictionary).Error; err != nil {
		panic(errors.NewCustomError("delete_fail"))
	}
}

// RefundList
//
//  @Author: YaKupJan
//  @Date: 2024-11-29 10:47:59
//  @Description:
//  @receiver p
//  @param pagination
//  @return []models.OrderPartRefund
//  @return int64
func (p PartRefundService) RefundList(pagination tools.Pagination) ([]models.OrderPartRefund, int64) {
	db := tools.GetDB()
	var partRefundList []models.OrderPartRefund
	var total int64
	query := db.Model(models.OrderPartRefund{}).
		Preload("Restaurant").
		Preload("Creator").
		Preload("RefundReason").
		Order("created_at DESC")

	query.Count(&total)
	query.Scopes(scopes.Page(pagination.Page, pagination.Limit)).Find(&partRefundList)
	return partRefundList,total
}
func NewPartRefundService(c *gin.Context) *PartRefundService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := PartRefundService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &service
}


// GetPartRefundOrderList
//
//  @Author: Rixat
//  @Date: 2025-03-09 17:47:59
//  @Description: 获取部分退款订单列表
//  @return []models.OrderPartRefund 退款订单列表
//  @return int64 总记录数
func (p PartRefundService) GetPartRefundOrderList(params partRefundRequest.PartRefundOrderListRequest,pagination tools.Pagination) ([]models.OrderPartRefund, int64) {
	db := tools.GetDB()
	var partRefundList []models.OrderPartRefund
	var total int64
	query := db.Model(models.OrderPartRefund{})
	if params.CityID != 0 {
		query = query.Where("city_id = ?", params.CityID)
	}
	if params.AreaID != 0 {
		query = query.Where("area_id = ?", params.AreaID)
	}
	if params.State != 0 {
		query = query.Where("state = ?", params.State)
	}
	if params.RestaurantID != 0 {
		query = query.Where("store_id = ?", params.RestaurantID)
	}
	if params.PartRefundCreatorID != 0 {
		query = query.Where("part_refund_creator_id = ?", params.PartRefundCreatorID)
	}
	if params.PartRefundType != 0 {
		query = query.Where("part_refund_type = ?", params.PartRefundType)
	}
	if params.BeginDate != "" {
		query = query.Where("created_at >= ?", params.BeginDate+" 00:00:00")
	}
	if params.EndDate != "" {
		query = query.Where("created_at <= ?", params.EndDate+" 23:59:59")
	}
	if params.Kw != "" {
		query = query.Where("(mobile like ? or name like ? or order_number like ?)", "%"+params.Kw+"%", "%"+params.Kw+"%", "%"+params.Kw+"%")
	}	
	query.Preload("Restaurant").
		Preload("Creator").
		Preload("City").
		Preload("Area").
		Preload("RefundReason")
	query.Order("created_at DESC").Count(&total).Scopes(scopes.Page(pagination.Page, pagination.Limit)).Find(&partRefundList)
	return partRefundList,total
}


// GetPartRefundOrderDetail
//
//  @Author: Rixat
//  @Date: 2025-03-09 17:47:59
//  @Description: 获取部分退款订单详情
//  @param id int 部分退款订单ID
//  @return models.OrderPartRefund 退款订单详情信息,包含关联数据
func (p PartRefundService) GetPartRefundOrderDetail(id int) models.OrderPartRefund {
	db := tools.GetDB()
	var partRefund models.OrderPartRefund
	db.Model(models.OrderPartRefund{}).Where("id = ?", id).
		Preload("Creator").
		Preload("Shipper").
		Preload("Restaurant").
		Preload("UserRank").
		Preload("RefundReason").
		Preload("OrderFailReason").
		Preload("Terminal").
		Preload("OrderPartRefundDetail.RestaurantFoods.ComboFoodItems.RestaurantFood"). // 套餐美食数据
		Preload("OrderPartRefundDetail.RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions"). // 套餐子美食规格数据
		Preload("OrderPartRefundDetail.LunchBox").
		Preload("OrderPartRefundDetail.SelectedSpec.FoodSpecOptions"). // 规格美食已选规格数据
		Preload("PayTypes").
		Preload("MarketingOrderLog").

	Find(&partRefund)
	return partRefund
}

