package cms

import (
	"github.com/gin-gonic/gin"
	"mulazim-api/lang"
	"mulazim-api/models"
	lotteryRequest "mulazim-api/requests/cms/lottery"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"time"
)

type LotteryWinnerSetService struct {
	langUtil *lang.LangUtil
	language string
}

// List 抽奖列表获取
//
//	@receiver s
//	@param state
//	@param page
//	@param limit
//	@param kw
//	@param sort
//	@return int64
//	@return []models.LotteryActivity
func (s LotteryWinnerSetService) List(page int, limit int, kw string, sort string) (int64, []models.LotteryActivityLevelWinnersSet) {
	list := []models.LotteryActivityLevelWinnersSet{}
	db := tools.Db
	query := db.Model(&list)
	query.Joins("left join t_lottery_activity_level_prize on t_lottery_activity_level_winners_set.lottery_activity_level_prize_id =  t_lottery_activity_level_prize.id")
	query.Joins("left join t_lottery_prize on t_lottery_activity_level_prize.prize_id =  t_lottery_prize.id")
	query.Preload("Admin")
	query.Preload("LotteryActivity")

	if kw != "" {
		query.Where("t_lottery_prize.name_ug like ? or t_lottery_prize.name_zh like ?", "%"+kw+"%", "%"+kw+"%")
	}

	if len(sort) > 0 {
		query.Order(sort)
	}

	var totalCount int64

	query.Count(&totalCount)
	query.Select("t_lottery_activity_level_winners_set.*,t_lottery_activity_level_prize.level,t_lottery_prize.name_" + s.language + " as prize_name")
	query.Scopes(scopes.Page(page, limit)).Find(&list)

	return totalCount, list
}

// GetCurrentInfo 获取活动的抽奖信息
//
//	@receiver s
//	@param activityId
//	@return bool
//	@return string
//	@return interface{}
func (s LotteryWinnerSetService) GetCurrentInfo(activityId int) (bool, string, interface{}) {
	lotteryActivityDeail := models.LotteryActivity{}
	db := tools.Db
	db.Model(&lotteryActivityDeail).Where("id=?", activityId).
		Preload("LotteryActivityLevel.LotteryActivityLevelPrize.LotteryPrize").
		Preload("LotteryActivityLevel.LotteryActivityLevelPrize.LotteryActivityLevelWinnersSet", "state = ?", 1).
		First(&lotteryActivityDeail)
	var alreadyTakenCount int64
	db.Model(&models.LotteryChance{}).Where("lottery_activity_id=?",activityId).Where("state > 1").Count(&alreadyTakenCount)
	rtnMap := map[string]interface{}{
		"already_taken_count": alreadyTakenCount,
	}
	rtnItem := []map[string]interface{}{}

	for _, level := range lotteryActivityDeail.LotteryActivityLevel[0].LotteryActivityLevelPrize {
		oneItem := map[string]interface{}{
			"lottery_activity_id":             lotteryActivityDeail.ID,
			"lottery_activity_level_id":       lotteryActivityDeail.LotteryActivityLevel[0].ID,
			"lottery_activity_level_prize_id": level.ID,
			"lottery_prize_id":                level.LotteryPrize.ID,
			"name":                            tools.If(s.language == "ug", level.LotteryPrize.NameUg, level.LotteryPrize.NameZh),
			"count":                           level.Count,
			"left_count":					    level.Count-int(level.TakenCount),
			"level":                           level.Level,
			"taken_count":                     level.TakenCount,
			"image":                           tools.AddCdn(tools.If(s.language == "ug", level.LotteryPrize.TitleImgUg, level.LotteryPrize.TitleImgZh)),
			"min_index":                       level.LotteryActivityLevelWinnersSet.MinIndex,
			"max_index":                       level.LotteryActivityLevelWinnersSet.MaxIndex,
			"prize_count":                     level.LotteryActivityLevelWinnersSet.PrizeCount,
		}
		rtnItem = append(rtnItem, oneItem)
	}

	rtnMap["list"] = rtnItem
	return true, "msg", rtnMap
}

// PostSet 设置抽奖
//
//	@receiver s
//	@param param
//	@param adminId
//	@return bool
//	@return string
func (s LotteryWinnerSetService) PostSet(param lotteryRequest.LotteryWinnerSetRequest, adminId int) (bool, string) {

	db := tools.Db

	var alreadyTakenCount int64

	db.Model(&models.LotteryChance{}).
		Where("state = 2").
		Count(&alreadyTakenCount)
	// 取消掉之前的 抽奖活动设置
	db.Model(&models.LotteryActivityLevelWinnersSet{}).
		Where("lottery_activity_id = ?", param.ActivityID).
		Update("state", 2)
	db.Model(&models.LotteryActivityLevelWinner{}).
		Where("lottery_activity_id =? and lottery_activity_level_id =?",
			param.ActivityID, param.Prizes[0].LotteryActivityLevelID).
		Update("state", 2)

	for _, prize := range param.Prizes {

		setting := models.LotteryActivityLevelWinnersSet{
			LotteryActivityID:           param.ActivityID,
			LotteryActivityLevelID:      prize.LotteryActivityLevelID,
			LotteryActivityLevelPrizeID: prize.LotteryActivityLevelPrizeID,
			MinIndex:                    prize.MinIndex,
			MaxIndex:                    prize.MaxIndex,
			PrizeCount:                  prize.PrizeCount,
			AdminID:                     int(adminId),
			CurrentCount:                int(alreadyTakenCount),
			State:                       1,
			CreatedAt:                   time.Now(),
			UpdatedAt:                   time.Now(),
		}
		result := db.Create(&setting)
		if result.RowsAffected > 0 {
			for _, luckUserIndex := range prize.Winners {
				winner := models.LotteryActivityLevelWinner{
					LotteryActivityID:           param.ActivityID,
					LotteryActivityLevelID:      prize.LotteryActivityLevelID,
					LotteryActivityLevelPrizeID: prize.LotteryActivityLevelPrizeID,
					AdminID:                     adminId,
					LuckyUserIndex:              luckUserIndex,
					State:                       1,
					CreatedAt:                   time.Now(),
					UpdatedAt:                   time.Now(),
				}
				db.Create(&winner)
			}
		}
	}

	return true, "msg"
}

// NewLotteryWinnerSetService 抽奖活动设置服务获取
//
//	@param c
//	@return *LotteryWinnerSetService
func NewLotteryWinnerSetService(c *gin.Context) *LotteryWinnerSetService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := LotteryWinnerSetService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &service
}
