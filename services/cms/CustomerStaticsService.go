package cms

import (
	"mulazim-api/configs"
	"mulazim-api/models"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/lang"
	"mulazim-api/tools"
)

type CustomerStaticsService struct {
	langUtil *lang.LangUtil
	language string
}
// GetCustomerStaticsList 客户统计
func (s CustomerStaticsService) GetCustomerStaticsList(c *gin.Context, cityID int, areaID int,sortColumns string, sortType string,startDate string,endDate string,page int,limit int,onlyCustomer int) ([]struct{
		CityName      string `json:"city_name"  gorm:"column:city_name"`
		AreaName      string `json:"area_name"  gorm:"column:area_name"`
		CityId      int64 `json:"city_id"  gorm:"column:city_id"`
		AreaId      int64 `json:"area_id"  gorm:"column:area_id"`
		CustomerId int64 `json:"customer_id"  gorm:"column:customer_id"`
		CustomerName string `json:"customer_name"  gorm:"column:customer_name"`
		CustomerMobile string `json:"customer_mobile"  gorm:"column:customer_mobile"`
		OrderCount      int64 `json:"order_count" gorm:"column:order_count"`
		OrderPrice           int64 `json:"order_price" gorm:"column:order_price"`
		DealerProfit           int64 `json:"dealer_profit" gorm:"column:dealer_profit"`
		IsAdmin           int64 `json:"id_admin" gorm:"column:is_admin"`
		AdminType           int64 `json:"admin_type" gorm:"column:admin_type"`
		AdminTypeName           string `json:"admin_type_name" gorm:"column:admin_type_name"`
},int64) {
	db := tools.ReadDb1
	var result []struct {
		CityName      string `json:"city_name"  gorm:"column:city_name"`
		AreaName      string `json:"area_name"  gorm:"column:area_name"`
		CityId      int64 `json:"city_id"  gorm:"column:city_id"`
		AreaId      int64 `json:"area_id"  gorm:"column:area_id"`
		CustomerId int64 `json:"customer_id"  gorm:"column:customer_id"`
		CustomerName string `json:"customer_name"  gorm:"column:customer_name"`
		CustomerMobile string `json:"customer_mobile"  gorm:"column:customer_mobile"`
		OrderCount      int64 `json:"order_count" gorm:"column:order_count"`
		OrderPrice           int64 `json:"order_price" gorm:"column:order_price"`
		DealerProfit           int64 `json:"dealer_profit" gorm:"column:dealer_profit"`
		IsAdmin           int64 `json:"id_admin" gorm:"column:is_admin"`
		AdminType           int64 `json:"admin_type" gorm:"column:admin_type"`
		AdminTypeName           string `json:"admin_type_name" gorm:"column:admin_type_name"`
	}

	lg := s.language + " "
	selectSql := "b_city.name_" + lg + "as city_name, " +
			 "b_area.name_" + lg + "as area_name, "+ 
			 "t_order.city_id as city_id, "+
			 "t_order.area_id as area_id, "+
			 "t_order.user_id as customer_id, "+
			 "t_user.name as customer_name, "+
			 "t_user.mobile as customer_mobile, "+
			 "count(t_order.id) as order_count, "+
			 "sum( t_order.order_price ) AS order_price, "+
			 "sum(t_order.dealer_profit) as  dealer_profit,"+
			"(SELECT type FROM t_admin WHERE mobile = t_user.mobile LIMIT 1 ) AS admin_type,"+
			 "if(( SELECT type FROM t_admin WHERE mobile = t_user.mobile LIMIT 1 )>0,1,0) as is_admin, "+
			 "'' as admin_type_name"
			 
			  
	query := db.Table("t_order").
		Select(selectSql).
		Joins("left join t_user on t_user.id  = t_order.user_id  ").
		Joins("left join b_area on b_area.id = t_order.area_id").
		Joins("left join b_city on b_city.id = t_order.city_id")
	//  startDate需要加一天，endDate需要加2天
	startDate = carbon.Parse(startDate).AddDay().ToDateString()
	endDate = carbon.Parse(endDate).AddDays(2).ToDateString()
	query.Where("t_order.archive_date between ? and ? and t_order.state = ?", startDate,endDate, 7).
		Group("t_order.user_id")
	if cityID > 0 {
		query = query.Where("t_order.city_id = ?", cityID)
	}
	if areaID > 0 {
		query = query.Where("t_order.area_id = ?", areaID)
	}
	
	if sortColumns != ""{
		query = query.Order(sortColumns + " "+sortType)
	}
	// 0:不检查 是不是管理员 1:只检查不是管理员下的单子 2:只检查管理员一类的人员下的单子 3:只检查代理 4:店铺管理员 5:百户长，配送员
	where :=""
	switch(onlyCustomer){
		case 1:
			where = "where z.admin_type is null"
		case 2:		
		where = "where z.admin_type is not null"
		case 3:		
		where = "where z.admin_type in (3,4) "	
		case 4:		
		where = "where z.admin_type in (5,6) "
		case 5:		
		where = "where z.admin_type in (7,8,9) "
	}
	realQuery :=db.Raw(`select * from (?) z `+where+` limit 50`,query)
	realQuery.Scan(&result)
	return result,0
}



// GetCustomerStoreStaticsList 客户店铺统计
func (s CustomerStaticsService) GetCustomerStoreStaticsList(c *gin.Context, customerID int,sortColumns string, sortType string,startDate string,endDate string,page int,limit int) ([]struct{
	
	StoreId      int64 `json:"store_id"  gorm:"column:store_id"`
	StoreName     string `json:"store_name"  gorm:"column:store_name"`
	OrderCount      int64 `json:"order_count" gorm:"column:order_count"`
	OrderPrice           int64 `json:"order_price" gorm:"column:order_price"`
	DealerProfit           int64 `json:"dealer_profit" gorm:"column:dealer_profit"`
},int64) {
db := tools.ReadDb1
var result []struct {
	StoreId      int64 `json:"store_id"  gorm:"column:store_id"`
	StoreName     string `json:"store_name"  gorm:"column:store_name"`
	OrderCount      int64 `json:"order_count" gorm:"column:order_count"`
	OrderPrice           int64 `json:"order_price" gorm:"column:order_price"`
	DealerProfit           int64 `json:"dealer_profit" gorm:"column:dealer_profit"`
}

lg := s.language + " "
selectSql := "t_restaurant.id as store_id, "+
		 "t_restaurant.name_"+lg+" as store_name,"+
		 "count(t_order.id) as order_count, "+
		 "sum( t_order.order_price ) AS order_price, "+
		 "sum(t_order.dealer_profit) as  dealer_profit "

query := db.Table("t_order").
	Select(selectSql).
	Joins("left join t_restaurant on t_order.store_id = t_restaurant.id ")
//  startDate需要加一天，endDate需要加2天
startDate = carbon.Parse(startDate).AddDay().ToDateString()
endDate = carbon.Parse(endDate).AddDays(2).ToDateString()
query.Where("t_order.archive_date between ? and ? and t_order.state = ?", startDate,endDate, 7).Where("t_order.user_id = ?",customerID).
	Group("t_order.store_id")

query.Limit(10)

if sortColumns != ""{
	query = query.Order(sortColumns + " "+sortType)
}
query.Scan(&result)
return result,0
}


// GetCustomerFoodStaticsList 客户菜品统计
func (s CustomerStaticsService) GetCustomerFoodStaticsList(c *gin.Context, customerID int,sortColumns string, sortType string,startDate string,endDate string,page int,limit int) ([]struct{
	
	StoreId      int64 `json:"store_id"  gorm:"column:store_id"`
	StoreName     string `json:"store_name"  gorm:"column:store_name"`
	FoodName     string `json:"food_name"  gorm:"column:food_name"`
	OrderCount      int64 `json:"order_count" gorm:"column:order_count"`
	FoodCount      int64 `json:"food_count" gorm:"column:food_count"`
	FoodPrice           int64 `json:"food_price" gorm:"column:food_price"`
},int64) {
db := tools.ReadDb1
var result []struct {
	StoreId      int64 `json:"store_id"  gorm:"column:store_id"`
	StoreName     string `json:"store_name"  gorm:"column:store_name"`
	FoodName     string `json:"food_name"  gorm:"column:food_name"`
	OrderCount      int64 `json:"order_count" gorm:"column:order_count"`
	FoodCount      int64 `json:"food_count" gorm:"column:food_count"`
	FoodPrice           int64 `json:"food_price" gorm:"column:food_price"`
}

lg := s.language + " "
selectSql := "t_restaurant.id as store_id, "+
		 "t_restaurant.name_"+lg+" as store_name,"+
		 "t_restaurant_foods.name_ug as food_name,"+
		 "count(t_order.id) as order_count, "+
		 "sum( t_order_detail.number ) AS food_count,"+
		 "sum(t_order_detail.number*t_order_detail.price) as food_price "

query := db.Table("t_order").
	Select(selectSql).
	Joins("left join t_order_detail on t_order.id = t_order_detail.order_id").
	Joins("left join t_restaurant_foods on t_restaurant_foods.id = t_order_detail.store_foods_id").
	Joins("left join t_restaurant on t_order.store_id = t_restaurant.id ")
//  startDate需要加一天，endDate需要加2天
startDate = carbon.Parse(startDate).AddDay().ToDateString()
endDate = carbon.Parse(endDate).AddDays(2).ToDateString()
query.Where("t_order.archive_date between ? and ? and t_order.state = ?", startDate,endDate, 7).Where("t_order.user_id = ?",customerID).
	Group("t_order_detail.store_foods_id")

query.Limit(10)

if sortColumns != ""{
	query = query.Order(sortColumns + " "+sortType)
}
query.Scan(&result)
return result,0
}




//
// NewCustomerStaticsService
//  @Description: 初始化CustomerStaticsService
//  @param c
//  @return *CustomerStaticsService
//
func NewCustomerStaticsService(c *gin.Context) *CustomerStaticsService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	CustomerStaticsService := CustomerStaticsService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &CustomerStaticsService
}

type ResultItem struct {
	CityName      string `json:"city_name"  `
	AreaName      string `json:"area_name"  `
	CityId      int64 `json:"city_id"  `
	AreaId      int64 `json:"area_id"  `
	CustomerCount      int64 `json:"customer_count" `
	Date string `json:"created_at_day"`
}
// GetCustomerStaticsList 客户统计 (平台新客户)
func (s CustomerStaticsService) GetNewCustomerStaticsList(c *gin.Context, cityID int, areaID int,startDate string,endDate string,mode int) ([]ResultItem,[]ResultItem,[]ResultItem,int64,int64,int64,int64) {
	db := tools.ReadDb1

	selectSql := "count(id) as customer_count, DATE_FORMAT( t_user.created_at, '%Y-%m-%d' ) AS date,user_city_id as city_id,user_area_id as area_id"

	startTime :=""
	endTime :=""
	group :="date"
	switch(mode){
		case 21://按月 
			startDate = carbon.Parse(startDate).Format("Y-m-01")
			startTime,endTime =tools.GetMonthStartAndEndWithParam(startDate,"Y-m-d")
			selectSql = "count(id) as customer_count, DATE_FORMAT( t_user.created_at, '%Y-%m-%d' ) AS date,user_city_id as city_id,user_area_id as area_id"		
		case 22://按年
			startTime = carbon.Parse(startDate).Format("Y-01-01")
			endTime = carbon.Parse(startDate).Format("Y-12-31")
			selectSql = "count(id) as customer_count, DATE_FORMAT( t_user.created_at, '%Y-%m' ) AS date,user_city_id as city_id,user_area_id as area_id"
		case 23://全部
			selectSql = "count(id) as customer_count, DATE_FORMAT( t_user.created_at, '%Y' ) AS date,user_city_id as city_id,user_area_id as area_id"
			startTime ="2017-01-01" //平台开始时间
			endTime = carbon.Now(configs.AsiaShanghai).Format("Y-m-d")
		case 24://指定日期区间
			selectSql = "count(id) as customer_count, DATE_FORMAT( t_user.created_at, '%Y-%m-%d' ) AS date,user_city_id as city_id,user_area_id as area_id"
			startTime =startDate
			endTime = endDate
		case 11:
			startDate = carbon.Parse(startDate).Format("Y-m-01")
			startTime,endTime =tools.GetMonthStartAndEndWithParam(startDate,"Y-m-d")
			selectSql = "count(id) as customer_count, DATE_FORMAT( t_user.created_at, '%Y-%m-%d' ) AS date,user_city_id as city_id,user_area_id as area_id"		
			group = "area_id"
		case 12://按年
			startTime = carbon.Parse(startDate).Format("Y-01-01")
			endTime = carbon.Parse(startDate).Format("Y-12-31")
			selectSql = "count(id) as customer_count, DATE_FORMAT( t_user.created_at, '%Y-%m' ) AS date,user_city_id as city_id,user_area_id as area_id"	
			group = "area_id"
		case 13://全部
			selectSql = "count(id) as customer_count, DATE_FORMAT( t_user.created_at, '%Y' ) AS date,user_city_id as city_id,user_area_id as area_id"
			startTime ="2017-01-01" //平台开始时间
			endTime = carbon.Now(configs.AsiaShanghai).Format("Y-m-d")	
			group = "area_id"
		case 14://指定日期区间
			selectSql = "count(id) as customer_count, DATE_FORMAT( t_user.created_at, '%Y-%m-%d' ) AS date,user_city_id as city_id,user_area_id as area_id"
			startTime =startDate
			endTime = endDate
			group = "area_id"	

	}
	startTime = startTime+" 00:00:00"
	endTime = endTime+" 23:59:59"
	
	query := db.Table("t_user").
		Select(selectSql)
	query2 := db.Table("t_user").
		Select(selectSql)
	if cityID > 0 {
		query = query.Where("t_user.user_city_id = ?", cityID)
		query2 = query2.Where("t_user.user_city_id = ?", cityID)
	}
	if areaID > 0 {
		query = query.Where("t_user.user_area_id = ?", areaID)
		query2 = query2.Where("t_user.user_area_id = ?", areaID)
	}
	query = query.Where("t_user.created_at between ? and ? ", startTime,endTime)
	query2 = query2.Where("t_user.created_at between ? and ? ", startTime,endTime)

	areaIdMap :=make([]map[string]interface{},0)
	var areaIds []int
	query2.Select("distinct user_area_id").Scan(&areaIdMap)
	for _, v := range areaIdMap {
		areaIds = append(areaIds, tools.ToInt(v["user_area_id"]))
	}
	var area []models.Area
	db.Model(&models.Area{}).Preload("City").Where("id in ?",areaIds).Find(&area)
	total :=int64(0)
	var result []ResultItem
	var resultAll []ResultItem
	var cityResult []ResultItem
	var areaResult []ResultItem
	cityTotal :=int64(0)
	areaTotal :=int64(0)
	unknownTotal :=int64(0)
	
	query.Group(group).Scan(&result)
	if (mode == 11 || mode == 12 || mode == 13 || mode == 14){
		for k, v := range result {
			total +=v.CustomerCount
			isCity :=false
			isArea :=false
			for _, vv := range area {
				if vv.ID == int(v.AreaId) {
					if s.language == "ug" {
						result[k].AreaName = vv.NameUg
						result[k].CityName = vv.City.NameUg
					}else{
						result[k].AreaName = vv.NameZh
						result[k].CityName = vv.City.NameZh
					}
					if vv.AreaType == 1 || vv.AreaType == 2 {
						isCity =true
					}else{
						isArea =true
					}
				}
				
			}
			if v.AreaId <= 0 {
				unknownTotal +=v.CustomerCount
			}else{
				resultAll = append(resultAll, result[k])
			}
			if isCity {
				cityResult = append(cityResult, result[k])
				cityTotal +=v.CustomerCount
			}else if isArea{
				areaResult = append(areaResult, result[k])
				areaTotal +=v.CustomerCount
			}

		}
		total = cityTotal+areaTotal
	}else{ //按时间
		for k, v := range result {
			total +=v.CustomerCount
			resultAll = append(resultAll, result[k])
		}
	}
	
	return resultAll,cityResult,areaResult,total,cityTotal,areaTotal,unknownTotal
}