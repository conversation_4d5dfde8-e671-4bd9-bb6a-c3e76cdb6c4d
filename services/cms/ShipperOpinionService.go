package cms

import (
	"errors"
	"mulazim-api/lang"
	"mulazim-api/models"

	"mulazim-api/scopes"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type ShipperOpinionService struct {
	langUtil *lang.LangUtil
	language string
}

func NewShipperOpinionService(c *gin.Context) *ShipperOpinionService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	ShipperOpinionService := ShipperOpinionService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &ShipperOpinionService
}

// List
//
// @Description: 获取考勤列表
// @Author: Rixat
// @Time: 2023-11-07 07:46:13
// @receiver
// @param c *gin.Context
func (s ShipperOpinionService) List(page int, limit int, opinionType int, cityId int, areaId int, state int, ShipperID int, startDate string, endDate string, sort string) map[string]interface{} {
	var opinionList []models.ShipperOpinionBug
	query := tools.Db.Model(opinionList).Scopes(scopes.CityAreaStateDate(cityId, areaId, state, startDate, endDate))
	if opinionType > 0 {
		query.Where("type=?", opinionType)
	}
	if ShipperID > 0 {
		query.Where("shipper_id = ?", ShipperID)
	}
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.
			Preload("City").
			Preload("Area").
			Preload("Shipper").
			Scopes(scopes.Page(page, limit)).
			Order(sort).
			Find(&opinionList)
	}
	opinionListRes := make([]map[string]interface{}, 0)
	for _, value := range opinionList {
		opinionListRes = append(opinionListRes, map[string]interface{}{
			"id":         value.ID,
			"city_name":  tools.GetNameByLang(value.City, s.language),
			"area_name":  tools.GetNameByLang(value.Area, s.language),
			"shipper_id": value.ShipperID,
			"name":       value.Shipper.Name,
			"mobile":     value.Shipper.Mobile,
			"content":    value.Content,
			"images":     tools.GetImageURLs(value.Images),
			"state":      value.State,
			"type":       value.Type,
			"created_at": tools.TimeFormatYmdHis(&value.CreatedAt),
		})
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": opinionListRes,
	}
	return result
}

// Detail
//
// @Description: 请假/事故详情
// @Author: Rixat
// @Time: 2023-11-07 07:46:13
// @receiver
// @param c *gin.Context
func (s ShipperOpinionService) Detail(ID int) (map[string]interface{}, error) {
	var opinion models.ShipperOpinionBug
	tools.Db.Model(opinion).Where("id=?", ID).Preload("City").Preload("Area").Preload("Shipper").First(&opinion)
	if opinion.ID == 0 {
		return nil, errors.New("not_found")
	}
	result := map[string]interface{}{
		"id":         opinion.ID,
		"city_name":  tools.GetNameByLang(opinion.City, s.language),
		"area_name":  tools.GetNameByLang(opinion.Area, s.language),
		"shipper_id": opinion.ShipperID,
		"name":       opinion.Shipper.Name,
		"mobile":     opinion.Shipper.Mobile,
		"content":    opinion.Content,
		"images":     tools.GetImageURLs(opinion.Images),
		"state":      opinion.State,
		"type":       opinion.Type,
		"created_at": tools.TimeFormatYmdHis(&opinion.CreatedAt),
	}
	return result, nil
}
