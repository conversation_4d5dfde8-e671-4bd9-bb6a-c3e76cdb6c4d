package cms

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"time"
)

type LotteryCommentService struct {
	langUtil *lang.LangUtil
	language string
}

func NewLotteryCommentService(ctx *gin.Context) *LotteryCommentService {
	l, _ := ctx.Get("lang_util")
	language, _ := ctx.Get("lang")
	langUtil := l.(lang.LangUtil)
	lotteryCommentService := LotteryCommentService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &lotteryCommentService
}

// GetComments
//
// @Description: 获取活动评论
// @Author: Salam
// @Time: 2025-02-27 11:09:47
// @receiver
// @param c *gin.Context
func (svc *LotteryCommentService) GetComments(
	pagination tools.Pagination, activityID, cityID, areaID, commentType int, queryStr string,
	beginTime, endTime *time.Time,
) (int64, []models.LotteryComment, error) {

	db := tools.GetDB()
	var res []models.LotteryComment
	query := db.Model(&models.LotteryComment{}).Where("lottery_activity_id = ?", activityID)

	if cityID > 0 {
		query = query.Where("city_id = ?", cityID)
	}
	if areaID > 0 {
		query = query.Where("area_id = ?", areaID)
	}
	if commentType > 0 {
		query = query.Where("type = ?", commentType)
	}
	if len(queryStr) > 0 {
		query = query.Where("content LIKE ?", "%"+queryStr+"%")
	}
	if beginTime != nil && endTime != nil {
		query = query.Where("created_at BETWEEN ? AND ?",
			beginTime.Format(time.DateTime), endTime.Format(time.DateTime))
	} else {
		if beginTime != nil {
			query = query.Where("created_at >= ?", beginTime)
		}
		if endTime != nil {
			query = query.Where("created_at <= ?", endTime)
		}
	}

	var total int64
	query.Count(&total)

	if err := query.Scopes(scopes.Page(pagination.Page, pagination.Limit)).
		Find(&res).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return total, nil, err
	}
	return total, res, nil
}

// GetCommentsWithUserInfo
//
// @Description: 获取活动评论（附加用户信息）
// @Author: Salam
// @Time: 2025-02-27 11:09:47
// @receiver
// @param c *gin.Context
func (svc *LotteryCommentService) GetCommentsWithUserInfo(
	pagination tools.Pagination, activityID, cityID, areaID, commentType int, queryStr string,
	beginTime, endTime *time.Time,
) (int64, []models.LotteryComment, error) {

	db := tools.GetDB()
	var res []models.LotteryComment
	query := db.Model(&models.LotteryComment{}).Where("lottery_activity_id = ?", activityID)

	if cityID > 0 {
		query = query.Where("city_id = ?", cityID)
	}
	if areaID > 0 {
		query = query.Where("area_id = ?", areaID)
	}
	if commentType > 0 {
		query = query.Where("type = ?", commentType)
	}
	if len(queryStr) > 0 {
		query = query.Where("content LIKE ?", "%"+queryStr+"%")
	}
	if beginTime != nil && endTime != nil {
		query = query.Where("created_at BETWEEN ? AND ?",
			beginTime.Format(time.DateTime), endTime.Format(time.DateTime))
	} else {
		if beginTime != nil {
			query = query.Where("created_at >= ?", beginTime)
		}
		if endTime != nil {
			query = query.Where("created_at <= ?", endTime)
		}
	}

	var total int64
	query.Count(&total)

	if total > 0 {
		query.Preload("User")
	} else {
		return total, nil, nil
	}

	if err := query.Scopes(scopes.Page(pagination.Page, pagination.Limit)).
		Find(&res).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return total, nil, err
	}
	return total, res, nil
}
