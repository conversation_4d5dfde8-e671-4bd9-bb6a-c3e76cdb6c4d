package cms

import (
	"errors"
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	markupRequests "mulazim-api/requests/cms/markup"
	"mulazim-api/scopes"
	"mulazim-api/services"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type PriceMarkupService struct {
	langUtil *lang.LangUtil
	language string
	services.BaseService
}

func NewPriceMarkupService(c *gin.Context) *PriceMarkupService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	priceMarkupService := PriceMarkupService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &priceMarkupService
}

// GetPriceMarkupList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver
// @param c *gin.Context
func (s PriceMarkupService) GetPriceMarkupList(params markupRequests.CmsPriceMarkupList) (int64, []models.PriceMarkupFood) {
	var priceMarkupList []models.PriceMarkupFood
	query := tools.ReadDb1.Model(priceMarkupList)
	// 筛选条件
	if params.CityID > 0 {
		query = query.Where("city_id=?", params.CityID)
	}
	if params.AreaID > 0 {
		query = query.Where("area_id=?", params.AreaID)
	}
	if params.RestaurantID > 0 {
		query = query.Where("restaurant_id=?", params.RestaurantID)
	}
	if params.FoodID > 0 {
		query = query.Where("restaurant_foods_id=?", params.FoodID)
	}
	if params.State > 0 {
		query = query.Where("state = ?", params.State)
	}
	// 正在执行
	if params.RunState == 1 {
		query = query.Where("state=3 and end_date >= ?", carbon.Now().Format("Y-m-d"))
	}
	// 查询结果
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Preload("City").
			Preload("Area").
			Preload("Restaurant").
			Preload("RestaurantFoods").
			Preload("SelectedSpec.FoodSpecOptions").
			Preload("Admin").
			Preload("PriceMarkupFoodLogSum",func(db *gorm.DB) *gorm.DB{
				return db.Select("price_markup_id,sum(saled_count) as saled_count").Where("state in(1,2,3)").Group("price_markup_id")
			}).
			Order("id desc").
			Scopes(scopes.Page(params.Page, params.Limit)).
			Find(&priceMarkupList)
	}
	return totalCount, priceMarkupList
}

// GetPriceMarkupList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver
// @param c *gin.Context
func (s PriceMarkupService) Detail(ID int) models.PriceMarkupFood {
	var priceMarkup models.PriceMarkupFood
	tools.Db.Model(priceMarkup).Where("id=?", ID).Preload("Restaurant").
		Preload("Admin").
		Preload("RestaurantFoods.ComboFoodItems.RestaurantFood").
		Preload("RestaurantFoods.ComboFoodItems.SelectedSpec").
		Preload("SelectedSpec.FoodSpecOptions").
		Find(&priceMarkup)
	return priceMarkup
}

// GetPriceMarkupList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver
// @param c *gin.Context
func (s PriceMarkupService) ChangeState(ID int, State int) error {
	db := tools.Db
	var priceMarkup models.PriceMarkupFood
	db.Model(priceMarkup).Where("id = ?", ID).First(&priceMarkup)
	if priceMarkup.ID == 0 {
		return errors.New("not_found")
	}
	// 验证日期冲突
	var conflictMarkup models.PriceMarkupFood
	tools.Db.Model(conflictMarkup).
		Where("restaurant_foods_id =?", priceMarkup.RestaurantFoodsID).
		Where("(start_date >= ? and end_date <= ? ) or (start_date >= ? and end_date <= ? )", priceMarkup.StartDate, priceMarkup.StartDate, priceMarkup.EndDate, priceMarkup.EndDate).
		Where("running_state = ? and id <> ?", 1, priceMarkup.ID).
		Find(&conflictMarkup)
	if conflictMarkup.ID > 0 {
		message := fmt.Sprintf(s.langUtil.T("price_markup_conflict"), tools.TimeFormatYmd(conflictMarkup.StartDate)+"~"+tools.TimeFormatYmd(conflictMarkup.EndDate))
		return errors.New(message)
	}
	// 更新状态
	if State == 1{
		err := db.Model(models.PriceMarkupFood{}).Where("id=?", ID).UpdateColumns(map[string]interface{}{
			"running_state": State,
			"stop_time":nil,
		}).Error
		if err != nil {
			return errors.New("failed")
		}
		if priceMarkup.FoodType == models.RestaurantFoodsTypeSpec {
			if !s.CheckChangeStateSpecInfo(priceMarkup.SpecID, tools.ToInt(priceMarkup.RestaurantFoodsID)) {
				return errors.New("spec_info_has_changed")
			}
		}
	}else{
		err := db.Model(priceMarkup).Where("id=?", ID).UpdateColumns(map[string]interface{}{
			"running_state": State,
			"stop_time":time.Now(),
		}).Error
		if err != nil {
			return errors.New("failed")
		}
		// 所有有关秒杀，优惠，特价关闭
		// 秒杀
		err = db.Model(models.Seckill{}).Where("price_markup_id=?", priceMarkup.ID).Update("state",0).Error
		if err != nil {
			return errors.New("failed")
		}
		// 优惠
		err = db.Model(models.FoodsPreferential{}).Where("price_markup_id=?", priceMarkup.ID).Update("state",0).Error
		if err != nil {
			return errors.New("failed")
		}
		// 特价
		specialMarketID := []int{}
		db.Model(models.Seckill{}).Select("market_id").Where("type = 2 and price_markup_id=?", priceMarkup.ID).Scan(&specialMarketID)
		if len(specialMarketID) > 0{
			err = db.Model(models.SeckillMarket{}).Where("id in ?", specialMarketID).Update("state",0).Error
			if err != nil {
				return errors.New("failed")
			}
		}
	}
	
	return nil
}

// Create
//
// @Description: 创建美食库
// @Author: Rixat
// @Time: 2024-10-25 11:07:50
// @receiver
// @param c *gin.Context
func (s PriceMarkupService) Create(params markupRequests.CmsPriceMarkupCreate, admin models.Admin) (int,error) {
	// 验证美食信息
	var foodInfo models.RestaurantFoods
	tools.Db.Model(foodInfo).Where("id =?", params.FoodID).Preload("Restaurant").Find(&foodInfo)
	if foodInfo.ID == 0 || foodInfo.ID != params.FoodID{
		return 0,errors.New("not_found")
	}
	if foodInfo.State == 0 || foodInfo.Restaurant.State == 0 {
		return 0,errors.New("state_error")
	}
	if foodInfo.FoodType == models.RestaurantFoodsTypeSpec {
		var specPrice int64
		tools.Db.Model(models.FoodSpecOption{}).Select("sum(price) as spec_price").Where("id in ?",params.OptionIds).Scan(&specPrice)
		if tools.ToInt( specPrice) < params.InPrice {
			return 0,errors.New("in_price_must_less_food_price")
		}
	}else{
		if tools.ToInt( foodInfo.Price) < params.InPrice {
			return 0,errors.New("in_price_must_less_food_price")
		}
	}
	
	if carbon.Parse(params.EndDate).Lt(carbon.Parse(params.StartDate)) {
		return 0,errors.New("start_date_must_gt_end_date")
	}
	if carbon.Parse(params.EndDate).Lt(carbon.Now()) {
		return 0,errors.New("end_date_must_gt_now")
	}
	// 验证规格信息
	if params.FoodType == models.RestaurantFoodsTypeSpec && len(params.OptionIds) == 0 {
		return 0,errors.New("option_ids_must_not_empty")
	}
	// 验证日期冲突
	var conflictMarkups []models.PriceMarkupFood
	confilictQuery := tools.Db.Model(conflictMarkups).
		Where("restaurant_foods_id =?", params.FoodID).
		Where("running_state = ? ", 1)
	if foodInfo.FoodType == models.RestaurantFoodsTypeSpec {
		specId := s.GetSpecIdByOptions(params.FoodID, params.OptionIds)
		confilictQuery.Where("spec_id =?", specId)
	}
	confilictQuery.Find(&conflictMarkups)
	for _, conflictMarkup := range conflictMarkups {
		if tools.ConflictTimeRange(*conflictMarkup.StartDate,*conflictMarkup.EndDate,carbon.Parse(params.StartDate).Carbon2Time(),carbon.Parse(params.EndDate).Carbon2Time()) {
			message := fmt.Sprintf(s.langUtil.T("price_markup_conflict"), tools.TimeFormatYmd(conflictMarkup.StartDate)+"~"+tools.TimeFormatYmd(conflictMarkup.EndDate))
			return 0,errors.New(message)
		}
	}
	// 创建记录
	startDate := carbon.Parse(params.StartDate).Carbon2Time()
	endDate := carbon.Parse(params.EndDate).Carbon2Time()
	markup := models.PriceMarkupFood{
		CityID:            foodInfo.Restaurant.CityID,
		AreaID:            foodInfo.Restaurant.AreaID,
		RestaurantID:      foodInfo.RestaurantID,
		RestaurantFoodsID: foodInfo.ID,
		StartDate:         &startDate,
		EndDate:           &endDate,
		TotalCount:        params.TotalCount,
		State:             1,
		RunningState:      1,
		InPrice:           params.InPrice,
		CreatorID:         admin.ID,
		CreatedAt:         time.Now(),
		FoodType:          uint8(params.FoodType),
	}
	// 设置规格
	if params.FoodType == models.RestaurantFoodsTypeSpec {
		specID , err := s.SaveFoodSpec(foodInfo.RestaurantID,foodInfo.ID, params.OptionIds)
		if err != nil {
			return 0,err
		}
		markup.SpecID = specID
	}


	// 记录日志
	// markLog := tools.JsonEncode([]any{markup})
	// markup.OperationLog = &markLog
	err := tools.GetDB().Create(&markup).Error
	if err != nil {
		return 0,err
	}
	return markup.ID,nil
}

// GetPriceMarkupList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver
// @param c *gin.Context
func (s PriceMarkupService) Edit(params markupRequests.CmsPriceMarkupEdit, admin models.Admin) error {
	db := tools.Db
	var markup models.PriceMarkupFood
	db.Model(markup).Where("id=?", params.ID).Find(&markup)
	if markup.ID == 0 {
		return errors.New("not_found")
	}
	// 验证状态
	if markup.State == 5 {
		return errors.New("price_markup_stop_enable_edit")
	}
	// 已支付订单除了时间不允许修改其他信息
	if markup.State > 1 && (params.InPrice != markup.InPrice || params.TotalCount!= markup.TotalCount || params.FoodID != markup.RestaurantFoodsID) {
		return errors.New("payed_price_markup_stop_enable_edit")
	}

	var foodInfo models.RestaurantFoods
	db.Model(foodInfo).Where("id =?", params.FoodID).Preload("Restaurant").Find(&foodInfo)
	if foodInfo.ID == 0 {
		return errors.New("not_found")
	}
	if foodInfo.State == 0 || foodInfo.Restaurant.State == 0 {
		return errors.New("state_error")
	}
	// 验证日期冲突
	var conflictMarkups []models.PriceMarkupFood
	tools.Db.Model(conflictMarkups).
		Where("restaurant_foods_id =?", params.FoodID).
		Where("running_state = ? ", 1).
		Where("id <> ? ", params.ID).
		Find(&conflictMarkups)
	for _, conflictMarkup := range conflictMarkups {
		if tools.ConflictTimeRange(*conflictMarkup.StartDate,*conflictMarkup.EndDate,carbon.Parse(params.StartDate).Carbon2Time(),carbon.Parse(params.EndDate).Carbon2Time()) {
			message := fmt.Sprintf(s.langUtil.T("price_markup_conflict"), tools.TimeFormatYmd(conflictMarkup.StartDate)+"~"+tools.TimeFormatYmd(conflictMarkup.EndDate))
			return errors.New(message)
		}
	}

	// 跟新记录
	now := time.Now()
	startDate := carbon.Parse(params.StartDate).Carbon2Time()
	endDate := carbon.Parse(params.EndDate).Carbon2Time()
	err := db.Where("id=?", params.ID).UpdateColumns(&models.PriceMarkupFood{
		CityID: foodInfo.Restaurant.CityID,
		AreaID: foodInfo.Restaurant.AreaID,
		RestaurantID: foodInfo.RestaurantID,
		RestaurantFoodsID: foodInfo.ID,
		StartDate:         &startDate,
		EndDate:           &endDate,
		InPrice:           params.InPrice,
		TotalCount:        params.TotalCount,
		UpdatedAt:         &now,
		SpecID:            params.SpecID,
		FoodType:          uint8(params.FoodType),
	}).Error
	if err != nil {
		return err
	}
	return nil
}


// GetPriceMarkupList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver
// @param c *gin.Context
func (s PriceMarkupService) GetSeckillLogs(ID int, markupType int,page int,limit int) (int64,[]models.Seckill,error) {
	db := tools.Db
	var markup models.PriceMarkupFood
	db.Model(markup).Where("id=?",ID).Find(&markup)
	if markup.ID == 0 {
		return 0,nil,errors.New("not_found")
	}
	// type 1:秒杀，2：特价，3：优惠
	var seckill []models.Seckill
	var totalCount int64
	db.Model(&seckill).
		Where("price_markup_id=?",ID).
		Where("type=?",markupType).
		Preload("PriceMarkupFoodLogSum", func(db *gorm.DB) *gorm.DB {
			return db.Select("activity_id,IFNULL(sum(saled_count),0) as saled_count,IFNULL(sum(saled_count*price),0) as price").
				Where("state in(1,2,3)").
				Where("activity_type=?",markupType).
				Group("activity_id") 
		}).
		Preload("RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions").
		Preload("RestaurantFoods.ComboFoodItems.RestaurantFood").
		Preload("SeckillMarket").
		Preload("SelectedSpec.FoodSpecOptions").
		Count(&totalCount).
		Find(&seckill)
		return totalCount,seckill,nil
}

// GetSeckillLogsBySeckillID
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-10-29 17:55:32
// @receiver 
// @param c *gin.Context
func (s PriceMarkupService) GetSeckillLogsHeader(ID int) map[string]interface{}{
	// 统计秒杀，特价
	headCount := map[string]interface{}{
		"seckill_count": 0,
		"special_price_count": 0,
		"preferential_count": 0,
	}
	tools.Db.Model(models.Seckill{}).
		Select("count(if(type = 1,1,null)) as seckill_count,count(if(type = 2,1,null)) as special_price_count").
		Where("price_markup_id = ?",ID).
		Scan(&headCount)
	// 优惠
	var preCount int64
	tools.Db.Model(models.FoodsPreferential{}).
		Where("price_markup_id =?",ID).
		Count(&preCount)
	headCount["preferential_count"] = preCount
	return headCount
}


// GetPriceMarkupList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver
// @param c *gin.Context
func (s PriceMarkupService) GetPreferentialLogs(ID int, markupType int) (int64,[]models.FoodsPreferential,error) {
	db := tools.Db
	var markup models.PriceMarkupFood
	db.Model(markup).Where("id=?",ID).Find(&markup)
	if markup.ID == 0 {
		return 0,nil,errors.New("not_found")
	}
	// type 1:秒杀，2：特价，3：优惠
	var foodsPreferential []models.FoodsPreferential
	var totalCount int64
	db.Model(&foodsPreferential).
		Where("price_markup_id=?",ID).
		// Where("state = 1").
		Preload("PriceMarkupFoodLogSum", func(db *gorm.DB) *gorm.DB {
			return db.Select("price_markup_id,activity_id,IFNULL(sum(price*saled_count),0) as price,IFNULL(sum(saled_count),0) as saled_count").
			Where("state in (1,2,3) and activity_type = ?" ,markupType).
			Group("activity_id")
		}).
		Preload("RestaurantFood.ComboFoodItems.SelectedSpec.FoodSpecOptions").
		Preload("RestaurantFood.ComboFoodItems.RestaurantFood").
		Preload("SelectedSpec.FoodSpecOptions").
		Count(&totalCount).
		Find(&foodsPreferential)
	return totalCount,foodsPreferential,nil
}


// GetPriceMarkupList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver
// @param c *gin.Context
func (s PriceMarkupService) GetSeckillDetail(ID int, markupType int) (models.Seckill,error) {
	db := tools.Db
	var seckill models.Seckill
	db.Model(&seckill).
		Where("id=?",ID).
		Preload("SeckillLogGroupBySelect", func(db *gorm.DB) *gorm.DB {
			return db.Select("seckill_id,min(created_at) as start_sell_time,max(created_at) as end_sell_time,IFNULL(sum(saled_count),0) as saled_count,IFNULL(sum(saled_count*seckill_price),0) as seckill_price").
				Where("state=1").
				Where("type=?",markupType).
				Group("seckill_id") 
		}).
		Preload("PriceMarkupSeckillPriceLog.PriceMarketSeckillPriceLogSum", func(db *gorm.DB) *gorm.DB {
			return db.Select("seckill_price_log_id,IFNULL(sum(saled_count),0) as saled_count,IFNULL(sum(saled_count*price),0) as price,min(created_at) as begin_sell_time,max(created_at) as end_sell_time").
				Where("state in (1,2,3)").
				Group("seckill_price_log_id") 
		}).
		Preload("PriceMarkupFood").
		Preload("Restaurant").
		Preload("RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions").
		Preload("RestaurantFoods.ComboFoodItems.RestaurantFood").
		Preload("SelectedSpec.FoodSpecOptions").
		Find(&seckill)
	if seckill.ID == 0 {
		return seckill,errors.New("not_found")

		
	}
	return seckill,nil
}

func (s PriceMarkupService) GetPrefDetail(ID int, markupType int) (models.FoodsPreferential,error) {
	db := tools.Db
	var preferential models.FoodsPreferential
	db.Model(&preferential).
		Where("id=?",ID).
		// Where("state = 1").
		Preload("FoodsPreferentialLogGroupBySelect", func(db *gorm.DB) *gorm.DB {
			return db.Select("pref_id,IFNULL(sum(pref_price*saled_count),0) as pref_price,IFNULL(sum(saled_count),0) as saled_count,max(created_at) as end_sell_time,min(created_at) as begin_sell_time").
			Group("pref_id")
		}).
		Preload("Restaurant").
		Preload("RestaurantFood.ComboFoodItems.SelectedSpec.FoodSpecOptions").
		Preload("RestaurantFood.ComboFoodItems.RestaurantFood").
		Preload("PriceMarkupFood").
		Preload("SelectedSpec.FoodSpecOptions").
		Find(&preferential)
	if preferential.ID == 0 {
		return preferential,errors.New("not_found")
	}
	return preferential,nil
}



// GetPriceMarkupList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver
// @param c *gin.Context
func (s PriceMarkupService) GetSeckillLogsList(ID int, markupType int,page int,limit int) (int64,[]models.SeckillLog,error) {
	db := tools.Db
	// type 1:秒杀，2：特价，3：优惠
	var seckillLogs []models.SeckillLog
	var totalCount int64
	db.Model(&seckillLogs).
		Where("seckill_id=?",ID).
		Where("state = 1").
		Preload("Seckill").
		Preload("Food").
		Preload("OrderToday").
		Preload("Order").
		Count(&totalCount).
		Scopes(scopes.Page(page, limit)).
		Order("id desc").
		Find(&seckillLogs)
	return totalCount,seckillLogs,nil
}

func (s PriceMarkupService) GetPrefLogsList(ID int, markupType int,page int,limit int) (int64,[]models.FoodsPreferentialLog,error) {
	db := tools.Db
	// type 1:秒杀，2：特价，3：优惠
	var prefLogs []models.FoodsPreferentialLog
	var totalCount int64
	db.Model(&prefLogs).
		Where("pref_id=?",ID).
		Where("state = 1").
		Preload("FoodsPreferential").
		Preload("Food").
		Preload("OrderToday").
		Preload("Order").
		Count(&totalCount).
		Scopes(scopes.Page(page, limit)).
		Order("id desc").
		Find(&prefLogs)
	return totalCount,prefLogs,nil
}
