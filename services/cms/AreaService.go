package cms

import (
	"errors"
	"mulazim-api/constants"
	"mulazim-api/lang"
	"mulazim-api/models"
	cmsRequests "mulazim-api/requests/cms"
	"mulazim-api/scopes"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type AreaService struct {
	langUtil *lang.LangUtil
	language string
}

func NewAreaService(c *gin.Context) *AreaService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	seckillService := AreaService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &seckillService
}

// GetSeckillList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver 
// @param c *gin.Context
func (s AreaService) GetAreaList(params cmsRequests.AreaList) (int64,[]models.Area){
	var areaList []models.Area
	query := tools.ReadDb1.Model(areaList).Where("deleted_at is null")
	// 筛选条件
	if params.CityID > 0 {
		query = query.Where("city_id=?", params.CityID)
	}
	if params.AreaID > 0 {
		query = query.Where("id=?", params.AreaID)
	}
	if params.State != nil {
		query = query.Where("state = ?", params.State)
	}
	// 查询结果
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Preload("City").
			Preload("SelfSignImages","type=3 and doc_type in ?",[]string{"0002","1001"}).
			Order("id").
			Scopes(scopes.Page(params.Page, params.Limit)).
			Find(&areaList)
	}
	return totalCount,areaList
}
func (s AreaService) GetAreaDetail(params cmsRequests.AreaDetail) (models.Area,models.SelfSignMerchantInfo,error){
	var area models.Area
	query := tools.ReadDb1.Model(area).Where("deleted_at is null").Where("id=?",params.AreaID)
	// 筛选条件
	if params.CityID > 0 {
		query = query.Where("city_id=?", params.CityID)
	}
	if params.AreaID > 0 {
		query = query.Where("id=?", params.AreaID)
	}
	query.Find(&area)
	// 查询self_sign_info
	var selfSignInfo  models.SelfSignMerchantInfo
	tools.ReadDb1.Model(models.SelfSignMerchantInfo{}).Where("type = 3 and restaurant_id =?", params.AreaID).Preload("SelfSignImages","type=3").Find(&selfSignInfo)
	if selfSignInfo.Id == 0 {
		// 生成商家入住信息
		selfSignInfo := models.SelfSignMerchantInfo{
			Type:          constants.SELF_SIGN_TYPE_AGENT,
			CityId:        params.CityID,
			AreaId:        params.AreaID,
			RestaurantId:  params.AreaID,
			VerifyContent: "[]",
			CreatedAt:     carbon.Now().Carbon2Time(),
		}
		err := tools.Db.Model(models.SelfSignMerchantInfo{}).Create(&selfSignInfo).Error
		if err != nil {
			tools.Logger.Errorf("FATAL 配送员账户信息创建出错：%s", err.Error())
			return area,selfSignInfo, errors.New("failed")
		}
	}
	return area,selfSignInfo,nil
}
// 
//
// @Description: 营业执照上传
// @Author: Rixat
// @Time: 2024-10-17 12:37:31
// @receiver 
// @param c *gin.Context
func (s AreaService) SubmitShopLicenseInfo(params cmsRequests.AreaShopLicenseInfo) error{
	var selfSignInfo  models.SelfSignMerchantInfo
	tools.Db.Model(selfSignInfo).Where("type = 3 and restaurant_id = ?", params.AreaID).First(&selfSignInfo)
	if selfSignInfo.Id == 0 {
		return errors.New("self_sign_info_not_exist")
	}	
	if len(params.ShopLicenseImage)>0{
		var images = models.SelfSignImages{}
		tools.Db.Model(models.SelfSignImages{}).Where("type=3 and restaurant_id =? and doc_type = ?",params.AreaID,"0002").Scan(&images)
		if images.ID == 0 {
			images = models.SelfSignImages{
				Type:3,
				RestaurantID:params.AreaID,
				DocType:"0002",
				DocTypeName:"营业执照",
				MlzFilePath:params.ShopLicenseImage,
				CreatedAt:carbon.Now().Carbon2Time(),
				UpdatedAt:carbon.Now().Carbon2Time(),
			}
			err := tools.Db.Model(images).Create(&images).Error
			if err != nil {
				return errors.New("fail")
			}
		}else{
			images.MlzFilePath = params.ShopLicenseImage
			images.UpdatedAt = carbon.Now().Carbon2Time()
			err := tools.Db.Model(images).Updates(&images).Error
			if err != nil {
				return errors.New("fail")
			}
		}
	}
	if len(params.ShopPermitLicenseImage)>0{
		var images = models.SelfSignImages{}
		tools.Db.Model(models.SelfSignImages{}).Where("type=3 and restaurant_id =? and doc_type = ?",params.AreaID,"1001").Scan(&images)
		if images.ID == 0 {
			images = models.SelfSignImages{
				Type:3,
				RestaurantID:params.AreaID,
				DocType:"1001",
				DocTypeName:"食品经营许可证",
				MlzFilePath:params.ShopPermitLicenseImage,
				CreatedAt:carbon.Now().Carbon2Time(),
				UpdatedAt:carbon.Now().Carbon2Time(),
			}
			err := tools.Db.Model(images).Create(&images).Error
			if err != nil {
				return errors.New("fail")
			}
		}else{
			images.MlzFilePath = params.ShopPermitLicenseImage
			images.UpdatedAt = carbon.Now().Carbon2Time()
			err := tools.Db.Model(images).Updates(&images).Error
			if err != nil {
				return errors.New("fail")
			}
		}
	}else{
		// 删除食品经营许可证
		err := tools.Db.Model(models.SelfSignImages{}).Where("type=3 and restaurant_id =? and doc_type = ?",params.AreaID,"1001").Delete(&models.SelfSignImages{}).Error
		if err!= nil {
			return errors.New("fail")
		}
	}
	return nil
}

func (s *AreaService) ChangeBusinessTime(areaID uint, businessStartTime, businessEndTime string, businessTimeType int) error {
    var area models.Area
    err := tools.Db.First(&area, areaID).Error
    if err != nil {
        return errors.New("area_not_found")
    }
	if(businessTimeType==1){
		// 获取当前日期
		currentDate := carbon.Now().ToDateString() // 获取当前日期，格式为 "2025-02-08"
		startTimeStr := currentDate + " " + businessStartTime+":00" // 例如 "2025-02-08 10:30:00" 目前的carbon只能解析这种格式的
    	endTimeStr := currentDate + " " + businessEndTime+":00" 
		parsedStartTime := carbon.Parse(startTimeStr)
		parsedEndTime := carbon.Parse(endTimeStr)

		minutesDiff := parsedStartTime.DiffInMinutes(parsedEndTime) // 计算分钟差

		 // 判断结束时间是否早于开始时间，跨日情况
		 if minutesDiff < 0 {
			parsedEndTime = parsedEndTime.AddDay() // 跨日的情况，将结束时间加上一天
			minutesDiff = parsedStartTime.DiffInMinutes(parsedEndTime) // 重新计算分钟差
		}
	
		// 判断营业时间是否超过 8 小时
		if minutesDiff < 480 { // 8 小时 = 480 分钟
			return errors.New("area_business_time_must_be_longger_than_eight_hours")
		}
		area.BusinessStartTime = parsedStartTime.Format("H:i")
		area.BusinessEndTime = parsedEndTime.Format("H:i")
	}

	err = tools.Db.Model(&area).Updates(map[string]interface{}{
        "BusinessStartTime": area.BusinessStartTime,
        "BusinessEndTime":   area.BusinessEndTime,
        "BusinessTimeType":  businessTimeType,
    }).Error
    if err != nil {
        return err
    }

    return nil
}