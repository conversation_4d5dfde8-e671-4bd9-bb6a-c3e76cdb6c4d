package cms

import (
	"errors"
	"mulazim-api/lang"
	"mulazim-api/models"
	shipmentModels "mulazim-api/models/shipment"
	resource "mulazim-api/resources/cms/shipment"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
)

type ShipperRewardSettingService struct {
	langUtil *lang.LangUtil
	language string
}

func NewShipperRewardSettingService(c *gin.Context) *ShipperRewardSettingService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	ShipperRewardSettingService := ShipperRewardSettingService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &ShipperRewardSettingService
}

// Save
//
// @Description: 配送员奖励设置
// @Author: Rixat
// @Time: 2023-11-07 03:20:24
// @receiver
// @param c *gin.Context
func (s ShipperRewardSettingService) Save(admin models.Admin, params resource.ShipperRewardSetting) error {
	rewardSetting := shipmentModels.ShipperRewardSetting{}
	if params.ID > 0 { // 更新
		tools.Db.Model(rewardSetting).Where("id = ?", params.ID).Scan(&rewardSetting)
		rewardSetting.UpdatedAt = time.Now()
	} else { // 创建
		var count int64
		tools.Db.Model(rewardSetting).Where("area_id = ?", params.AreaID).Count(&count)
		if count > 0 {
			return errors.New("this_area_already_exists_reward_setting")
		}
		rewardSetting.CreatedAt = time.Now()
		rewardSetting.CityID = params.CityID
		rewardSetting.AreaID = params.AreaID
		if params.CityID == 0 && params.AreaID == 0 {
			rewardSetting.CityID = admin.AdminCityID
			rewardSetting.AreaID = admin.AdminAreaID
		}
	}
	rewardSetting.DailyOrderLimit = params.DailyOrderLimit
	rewardSetting.MonthRestDay = params.MonthRestDay
	rewardSetting.RewardAmount = params.RewardAmount
	rewardSetting.State = params.State
	workTime := params.WorkTime
	rewardSetting.WorkTime = workTime
	err := tools.Db.Model(rewardSetting).Save(&rewardSetting).Error
	if err != nil {
		tools.Logger.Error("FATAL 创建失败", err.Error())
		return errors.New("failed")
	}
	return nil
}

// ChangeState
//
// @Description: 修改状态
// @Author: Rixat
// @Time: 2023-11-22 02:26:47
// @receiver
// @param c *gin.Context
func (s ShipperRewardSettingService) ChangeState(admin models.Admin, ID int, State int) error {
	var rewardSetting shipmentModels.ShipperRewardSetting
	tools.Db.Model(rewardSetting).Where("id = ?", ID).First(&rewardSetting)
	if rewardSetting.ID == 0 {
		return errors.New("not_found")
	}
	rewardSetting.State = State
	err := tools.Db.Save(rewardSetting).Error
	if err != nil {
		tools.Logger.Error("FATAL 修改状态失败", err.Error())
		return errors.New("failed")
	}
	return nil
}

// 配送员奖励设置信息
//
// @Description: Detail
// @Author: Rixat
// @Time: 2023-11-17 09:14:42
// @receiver
// @param c *gin.Context
func (s ShipperRewardSettingService) List(page int, limit int, cityId int, areaId int, state int, sort string) map[string]interface{} {
	var rewardList []shipmentModels.ShipperRewardSetting
	var totalCount int64
	query := tools.Db.Model(rewardList).Scopes(scopes.CityAreaState(cityId, areaId, state))
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Preload("City").Preload("Area").
			Scopes(scopes.Page(page, limit)).
			Order(sort).
			Find(&rewardList)
	}
	items := make([]map[string]interface{}, 0)
	for _, value := range rewardList {
		items = append(items, map[string]interface{}{
			"id":                value.ID,
			"city_name":         tools.GetNameByLang(value.City, s.language),
			"area_name":         tools.GetNameByLang(value.Area, s.language),
			"city_id":           value.CityID,
			"area_id":           value.AreaID,
			"daily_order_limit": value.DailyOrderLimit,
			"month_rest_day":    value.MonthRestDay,
			"reward_amount":     value.RewardAmount,
			"work_time":         value.WorkTime,
			"state":             value.State,
			"created_at":        tools.TimeFormatYmdHis(&value.CreatedAt),
		})
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": items,
	}
	return result
}

// 配送员奖励设置信息
//
// @Description: Detail
// @Author: Rixat
// @Time: 2023-11-17 09:14:42
// @receiver
// @param c *gin.Context
func (s ShipperRewardSettingService) Detail(ID int) (map[string]interface{}, error) {
	var rewardSetting shipmentModels.ShipperRewardSetting
	tools.Db.Model(rewardSetting).Where("id=?", ID).First(&rewardSetting)
	if rewardSetting.ID == 0 {
		return nil, nil
	}
	result := map[string]interface{}{
		"id":                rewardSetting.ID,
		"city_id":           rewardSetting.CityID,
		"area_id":           rewardSetting.AreaID,
		"month_rest_day":    rewardSetting.MonthRestDay,
		"daily_order_limit": rewardSetting.DailyOrderLimit,
		"reward_amount":     rewardSetting.RewardAmount,
		"state":             rewardSetting.State,
		"work_time":         rewardSetting.WorkTime,
	}
	return result, nil
}
