package cms

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/lang"
	"mulazim-api/models"
	shipmentModels "mulazim-api/models/shipment"
	shipperRequest "mulazim-api/requests/ShipperRequest"
	shipperResource "mulazim-api/resources/cms/shipper"
	"mulazim-api/scopes"
	"mulazim-api/services"
	"mulazim-api/services/shipperv2"
	"mulazim-api/tools"
	"strings"
	"time"
)

type ShipperService struct {
	services.BaseService
	langUtil *lang.LangUtil
	language string
}

func NewShipperService(c *gin.Context) *ShipperService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	shipperService := ShipperService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &shipperService
}

// Create
//
// @Description: 创建配送员账户信息
// @Author: Rixat
// @Time: 2023-10-24 08:11:09
// @receiver
// @param c *gin.Context
func (s ShipperService) CreateAccountInfo(admin models.Admin, params shipperResource.ShipperAccountInfo) (map[string]interface{}, error) {
	// 验证用户名和手机号是否存在
	var shipper models.Admin
	tools.Db.Model(models.Admin{}).Where("name=? or mobile=?", params.Name, params.Mobile).First(&shipper)
	if shipper.ID > 0 {
		if shipper.Mobile == params.Mobile {
			return nil, errors.New("mobile_already_exist")
		} else {
			return nil, errors.New("name_already_exist")
		}
	}
	if len(params.Password) < 6 {
		return nil, errors.New("password_length_error")
	}
	if params.Age < 18 && params.Age > 75 {
		return nil, errors.New("age_error")
	}
	// 创建管理员信息
	shipperInfo := models.Admin{
		ParentID:                admin.GetParentId(),
		Type:                    constants.ADMIN_TYPE_SHIPPER,
		Avatar:                  params.Avatar,
		Name:                    params.Name,
		Mobile:                  params.Mobile,
		Age:					params.Age,
		Sex:                    params.Sex,		
		Password:                tools.MakeMd5(params.Password),
		ShipperIncomeTemplateID: params.ShipperIncomeTemplateID,
		AdminCityID:             params.AdminCityID,
		AdminAreaID:             params.AdminAreaID,
		BackOrderTimeLimit:      params.BackOrderTimeLimit,
		CreatedAt:               carbon.Now().Carbon2Time(),
	}
	// 如果前端不传，填充登录人属于的地区和县
	if shipperInfo.AdminCityID == 0 {
		shipperInfo.AdminCityID = admin.AdminCityID
	}
	if shipperInfo.AdminAreaID == 0 {
		shipperInfo.AdminAreaID = admin.AdminAreaID
	}
	tx := tools.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	err := tx.Model(models.Admin{}).Omit("area_name", "deleted_at", "login_time", "last_comment_readed").Create(&shipperInfo).Error
	if err != nil {
		tx.Rollback()
		tools.Logger.Errorf("FATAL 配送员账户信息创建出错：%s", err.Error())
		return nil, errors.New("failed")

	}
	// 生成商家入住信息
	selfSignInfo := models.SelfSignMerchantInfo{
		Type:          constants.SELF_SIGN_TYPE_SHIPPER,
		CityId:        shipperInfo.AdminCityID,
		AreaId:        shipperInfo.AdminAreaID,
		RestaurantId:  shipperInfo.ID,
		MerMobile:     params.Mobile,
		RegMerType:    constants.SELF_SIGN_REG_TYPE_MICRO_SHOP,
		VerifyContent: "[]",
		CreatedAt:     carbon.Now().Carbon2Time(),
	}
	err = tx.Model(models.SelfSignMerchantInfo{}).Create(&selfSignInfo).Error
	if err != nil {
		tx.Rollback()
		tools.Logger.Errorf("FATAL 配送员账户信息创建出错：%s", err.Error())
		return nil, errors.New("failed")
	}
	// admin_areas添加记录
	err = tx.Model(models.AdminAreas{}).Create(&models.AdminAreas{
		AdminID:   shipperInfo.ID,
		CityID:    shipperInfo.AdminCityID,
		AreaID:    shipperInfo.AdminAreaID,
		CreatedAt: carbon.Now().Carbon2Time(),
	}).Error
	if err != nil {
		tx.Rollback()
		tools.Logger.Errorf("FATAL 配送员账户信息创建出错：%s", err.Error())
		return nil, errors.New("failed")
	}
	tx.Commit()

	// 接口返回结果
	result := map[string]interface{}{
		"shipper_id":   shipperInfo.ID,
		"self_sign_id": selfSignInfo.Id,
	}
	return result, nil
}

// 更新账户信息
//
// @Description:
// @Author: Rixat
// @Time: 2023-11-04 09:56:20
// @receiver
// @param c *gin.Context
func (s ShipperService) UpdateAccountInfo(admin models.Admin, params shipperResource.ShipperAccountInfo) (map[string]interface{}, error) {
	if params.ID == 0 {
		return nil, errors.New("not_found")
	}
	// 验证用户名和手机号是否存在
	var shipper models.Admin
	tools.Db.Model(models.Admin{}).Where("id=? and type in ?", params.ID, []int{constants.ADMIN_TYPE_SHIPPER,constants.ADMIN_TYPE_SHIPPER_ADMIN}).Scan(&shipper)
	if shipper.ID == 0 {
		return nil, errors.New("not_found")
	}
	if shipper.ShipperIncomeTemplateID > 0 && params.ShipperIncomeTemplateID == 0 {
		return nil, errors.New("shipper_income_template_cant_empty")
	}
	// 如果修改名称或手机号需要验证是否已存在
	if shipper.Name != params.Name || shipper.Mobile != params.Mobile {
		var validateShipper models.Admin
		tools.Db.Model(models.Admin{}).Where("(name=? or mobile=?) and id <> ?", params.Name, params.Mobile, params.ID).Scan(&validateShipper)
		if validateShipper.ID > 0 {
			return nil, errors.New("name_already_exist")
		}
	}
	if shipper.ShipperIncomeTemplateID > 0 && shipper.ShipperIncomeTemplateID != params.ShipperIncomeTemplateID {
		var updateTemplate shipmentModels.AdminTemplateUpdate
		tools.Db.Model(updateTemplate).Where("shipper_id=?", shipper.ID).First(&updateTemplate)
		updateTemplate.OldTemplateID = shipper.ShipperIncomeTemplateID
		updateTemplate.TemplateID = params.ShipperIncomeTemplateID
		updateTemplate.ShipperID = shipper.ID
		tools.Db.Save(&updateTemplate)
	} else {
		shipper.ShipperIncomeTemplateID = params.ShipperIncomeTemplateID
	}
	// 更新信息
	if params.AdminAreaID > 0 && params.AdminCityID > 0 {
		shipper.AdminCityID = params.AdminCityID
		shipper.AdminAreaID = params.AdminAreaID
	}
	shipper.Avatar = params.Avatar
	shipper.Name = params.Name
	shipper.Mobile = params.Mobile
	shipper.Age = params.Age
	shipper.Sex = params.Sex
	shipper.BackOrderTimeLimit = params.BackOrderTimeLimit
	if len(params.Password) > 0 {
		if len(params.Password) < 6 {
			return nil, errors.New("password_length_error")
		}
		shipper.Password = tools.MakeMd5(params.Password)
	}
	shipper.UpdatedAt = carbon.Now().Carbon2Time()
	err := tools.Db.Omit("area_name", "deleted_at", "login_time", "last_comment_readed").Save(shipper).Error
	if err != nil {
		tools.Logger.Errorf("FATAL 配送员账户信息更新出错：%s", err.Error())
		return nil, errors.New("failed")
	}
	var selfSignMerchantInfo models.SelfSignMerchantInfo
	tools.Db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id=? and type = ?", shipper.ID, constants.SELF_SIGN_TYPE_SHIPPER).Scan(&selfSignMerchantInfo)
	if selfSignMerchantInfo.Id == 0 {
		// // 生成商家入住信息
		selfSignInfo := models.SelfSignMerchantInfo{
			Type:          constants.SELF_SIGN_TYPE_SHIPPER,
			CityId:        shipper.AdminCityID,
			AreaId:        shipper.AdminAreaID,
			RestaurantId:  shipper.ID,
			MerMobile:     params.Mobile,
			RegMerType:    constants.SELF_SIGN_REG_TYPE_MICRO_SHOP,
			VerifyContent: "[]",
			CreatedAt:     carbon.Now().Carbon2Time(),
		}
		err = tools.Db.Model(models.SelfSignMerchantInfo{}).Create(&selfSignInfo).Error
		if err != nil {
			tools.Logger.Errorf("FATAL 配送员账户信息创建出错：%s", err.Error())
			return nil, errors.New("failed")
		}
		selfSignMerchantInfo = selfSignInfo
	}
	//处理黑名单状态  //取消解除黑名单操作
	// if shipper.IsInsuranceBlack == 1 && tools.ToInt(params.IsInsuranceBlack) == 2{ //1为黑名单 2为解除黑名单
	// 	tools.Db.Model(models.Admin{}).Where("id=?", shipper.ID).Update("is_insurance_black", 0)
	// }
	// 接口返回结果
	result := map[string]interface{}{
		"shipper_id":   shipper.ID,
		"self_sign_id": selfSignMerchantInfo.Id,
	}
	return result, nil
}

// Create
//
// @Description: 创建配送员账户信息
// @Author: Rixat
// @Time: 2023-10-24 08:11:09
// @receiver
// @param c *gin.Context
func (s ShipperService) CreateIDCardInfo(params shipperResource.ShipperIDCardInfo) error {
	var selfSignInfo models.SelfSignMerchantInfo
	tools.Db.Model(selfSignInfo).Where("id=? and deleted_at is NULL", params.SelfSignID).Where("type = ?", constants.SELF_SIGN_TYPE_SHIPPER).First(&selfSignInfo)
	if selfSignInfo.Id == 0 {
		return errors.New("not_found")
	}
	tx := tools.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	// 保存身份证信息
	startDate := carbon.Parse(params.IDCardStartDate).Carbon2Time()
	endDate := carbon.Parse(params.IDCardEndDate).Carbon2Time()
	updateParams := models.SelfSignMerchantInfo{
		MerIdcardName:    params.RealName,
		MerIdcardNum:     params.IDCardNumber,
		MerIdcardStart:   &startDate,
		MerIdcardEnd:     &endDate,
		LegalmanHomeAddr: params.IDCardAddress,
		MerSex:           &params.Gender,
		LegalName:        params.RealName,
		UpdatedAt:        carbon.Now().Carbon2Time(),
	}
	err := tx.Model(models.SelfSignMerchantInfo{}).Omit("area_name").
		Where("id", params.SelfSignID).Updates(&updateParams).Error
	err = tx.Model(models.Admin{}).Where("id", selfSignInfo.RestaurantId).Updates(&map[string]interface{}{
		"real_name": params.RealName,
	}).Error
	if err != nil {
		tx.Rollback()
		tools.Logger.Errorf("FATAL 保存身份证信息出错：%s", err.Error())
		return errors.New("failed")
	}
	// 保存身份证照片
	err = tx.Where("restaurant_id=?", selfSignInfo.RestaurantId).Where("doc_type in ?", [3]string{"0001", "0011", "0007"}).Where("type", constants.SELF_SIGN_TYPE_SHIPPER).Delete(models.SelfSignImages{}).Error
	if err != nil {
		tx.Rollback()
		tools.Logger.Errorf("FATAL 保存身份证删除照片失败出错：%s", err.Error())
		return errors.New("failed")
	}
	docTypeNames := make([]map[string]interface{}, 0)
	tools.Db.Table("b_self_sign_doc_type").Select("name_zh,`key`").Scan(&docTypeNames)
	// 身份证正面
	imageInfo := models.SelfSignImages{
		Type:         selfSignInfo.Type,
		RestaurantID: selfSignInfo.RestaurantId,
		DocType:      "0001",
		DocTypeName:  s.GetDocTypeName(docTypeNames, "0001"),
		MlzFilePath:  params.IDCardFront,
	}
	err = tx.Model(models.SelfSignImages{}).Create(&imageInfo).Error
	if err != nil {
		tx.Rollback()
		tools.Logger.Errorf("FATAL 保存身份证照片出错：%s", err.Error())
		return errors.New("failed")
	}
	// 身份证反面
	imageInfo = models.SelfSignImages{
		Type:         selfSignInfo.Type,
		RestaurantID: selfSignInfo.RestaurantId,
		DocType:      "0011",
		DocTypeName:  s.GetDocTypeName(docTypeNames, "0011"),
		MlzFilePath:  params.IDCardBack,
	}
	err = tx.Model(models.SelfSignImages{}).Create(&imageInfo).Error
	if err != nil {
		tx.Rollback()
		tools.Logger.Errorf("FATAL 保存身份证照片出错：%s", err.Error())
		return errors.New("failed")
	}
	// 身份证手持
	imageInfo = models.SelfSignImages{
		Type:         selfSignInfo.Type,
		RestaurantID: selfSignInfo.RestaurantId,
		DocType:      "0007",
		DocTypeName:  s.GetDocTypeName(docTypeNames, "0007"),
		MlzFilePath:  params.IDCardHandel,
	}
	err = tx.Model(models.SelfSignImages{}).Create(&imageInfo).Error
	if err != nil {
		tx.Rollback()
		tools.Logger.Errorf("FATAL 保存身份证照片出错：%s", err.Error())
		return errors.New("failed")
	}
	tx.Commit()
	return nil
}
func (s ShipperService) GetDocTypeName(typeNames []map[string]interface{}, key string) string {
	for _, v := range typeNames {
		if v["key"] == key {
			return v["name_zh"].(string)
		}
	}
	return ""
}

// Create
//
// @Description: 创建配送员账户信息
// @Author: Rixat
// @Time: 2023-10-24 08:11:09
// @receiver
// @param c *gin.Context
func (s ShipperService) CreateBankCardInfo(params shipperResource.ShipperBankInfo) error {
	var selfSignInfo models.SelfSignMerchantInfo
	tools.Db.Model(selfSignInfo).Where("id=? and deleted_at is NULL", params.SelfSignID).Where("type = ?", constants.SELF_SIGN_TYPE_SHIPPER).First(&selfSignInfo)
	if selfSignInfo.Id == 0 {
		return errors.New("商家入住信息不存在")
	}
	bankAccountType := 1
	updateParams := models.SelfSignMerchantInfo{
		BankAcctType:   &bankAccountType,
		BankAcctNum:    params.BankAcctNum,
		BankId:         params.BankId,
		BankProvinceId: params.BankProvinceId,
		BankCityId:     params.BankCityId,
		BankAreaId:     params.BankAreaId,
		BankBranchCode: params.BankBranchCode,
		BankBranchName: params.BankBranchName,
		BankBindMobile: params.BankBindMobile,
		BankAcctName:   selfSignInfo.MerIdcardName,
		UpdatedAt:      carbon.Now().Carbon2Time(),
	}
	tx := tools.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	err := tx.Model(models.SelfSignMerchantInfo{}).Omit("area_name").
		Where("id", params.SelfSignID).Updates(&updateParams).Error
	if err != nil {
		tx.Rollback()
		return errors.New("创建失败" + err.Error())
	}

	// 保存身份证照片
	err = tx.Where("restaurant_id=?", selfSignInfo.RestaurantId).Where("doc_type in ?", [2]string{"0025", "0026"}).Where("type", constants.SELF_SIGN_TYPE_SHIPPER).Delete(models.SelfSignImages{}).Error
	if err != nil {
		tx.Rollback()
		return errors.New("删除照片失败" + err.Error())
	}
	docTypeNames := make([]map[string]interface{}, 0)
	tools.Db.Table("b_self_sign_doc_type").Select("name_zh,`key`").Scan(&docTypeNames)
	// 银行卡正面
	imageInfo := models.SelfSignImages{
		Type:         selfSignInfo.Type,
		RestaurantID: selfSignInfo.RestaurantId,
		DocType:      "0025",
		DocTypeName:  s.GetDocTypeName(docTypeNames, "0025"),
		MlzFilePath:  params.BankCardFrontImage,
	}
	err = tx.Model(models.SelfSignImages{}).Create(&imageInfo).Error
	if err != nil {
		tx.Rollback()
		return errors.New("创建失败" + err.Error())
	}
	// 银行卡反面
	imageInfo = models.SelfSignImages{
		Type:         selfSignInfo.Type,
		RestaurantID: selfSignInfo.RestaurantId,
		DocType:      "0026",
		DocTypeName:  s.GetDocTypeName(docTypeNames, "0026"),
		MlzFilePath:  params.BankCardBackImage,
	}
	err = tx.Model(models.SelfSignImages{}).Create(&imageInfo).Error
	if err != nil {
		tx.Rollback()
		return errors.New("创建失败" + err.Error())
	}
	tx.Commit()
	return nil
}

func (s ShipperService) CreateHealthInfo(params shipperResource.ShipperHealthInfo) error {

	tools.Db.Model(models.SelfSignStuff{}).Where("mer_info_id = ?", params.SelfSignID).Where("type", constants.SELF_SIGN_TYPE_SHIPPER).Delete(models.SelfSignStuff{})
	startDate := carbon.Parse(params.CertificateStartDate).Carbon2Time()
	endDate := carbon.Parse(params.CertificateEndDate).Carbon2Time()
	updateParams := models.SelfSignStuff{
		MerInfoId:              params.SelfSignID,
		Type:                   constants.SELF_SIGN_TYPE_SHIPPER,
		StaffName:              params.CertificateRealName,
		StaffIdCard:            params.CertificateNumber,
		HealthCertificateStart: &startDate,
		HealthCertificateEnd:   &endDate,
		HealthCertificateImage: params.CertificateImage,
		CreatedAt:              carbon.Now().Carbon2Time(),
	}
	err := tools.Db.Model(models.SelfSignStuff{}).Create(&updateParams).Error
	if err != nil {
		return errors.New("创建失败" + err.Error())
	}
	return nil
}

func (s ShipperService) GetShipperInfo(shipperID int) map[string]interface{} {
	var admin models.Admin
	err := tools.Db.Model(models.Admin{}).
		Preload("City").
		Preload("Area").
		Preload("ShipperIncomeTemplate").
		Where("id=?", shipperID).
		Find(&admin).Error
	if err != nil {
		return nil
	}

	finishStep := 1

	shipperInfo := make(map[string]interface{})
	// 账户信息
	accountInfo := map[string]interface{}{
		"city_name":             tools.GetNameByLang(admin.City, s.language),
		"area_name":             tools.GetNameByLang(admin.Area, s.language),
		"avatar":                tools.CdnUrl(admin.Avatar),
		"name":                  admin.Name,
		"mobile":                admin.Mobile,
		"age":					 admin.Age,
		"sex":					 admin.Sex,
		"template_name":         tools.GetNameByLang(admin.ShipperIncomeTemplate, s.language),
		"city_id":               admin.AdminCityID,
		"area_id":               admin.AdminAreaID,
		"template_id":           admin.ShipperIncomeTemplateID,
		"back_order_time_limit": admin.BackOrderTimeLimit,
		"is_insurance_black":admin.IsInsuranceBlack,
	}
	// 检查是否有更新的模板
	templateState := 2 // 已修改
	var updateTemplate shipmentModels.AdminTemplateUpdate
	tools.Db.Model(updateTemplate).Preload("Template").Where("shipper_id=?", shipperID).Find(&updateTemplate)
	if updateTemplate.ID > 0 {
		templateState = 1
	}
	accountInfo["template_state"] = templateState
	accountInfo["update_template_name"] = tools.GetNameByLang(updateTemplate.Template, s.language)
	accountInfo["update_template_id"] = updateTemplate.TemplateID
	shipperInfo["account_info"] = accountInfo

	var selfSignMerchantInfo models.SelfSignMerchantInfo
	tools.Db.Model(models.SelfSignMerchantInfo{}).
		Preload("Bank").
		Preload("BankProvince").
		Preload("BankCity").
		Preload("BankArea").
		Where("restaurant_id=? and type=? and deleted_at is NULL", shipperID, constants.SELF_SIGN_TYPE_SHIPPER).
		First(&selfSignMerchantInfo)
	// 身份证信息
	if len(selfSignMerchantInfo.MerIdcardNum) > 0 {
		var selfSignImages []models.SelfSignImages
		tools.Db.Model(selfSignImages).
			Where("restaurant_id = ? and type = 2 and doc_type in (0001,0011,0007)", shipperID).
			Find(&selfSignImages)

		// var imageUrls []map[string]interface{}

		finishStep = 2
		idcardInfo := map[string]interface{}{
			// "idcard_front_image":selfSignMerchantInfo.idc
			"gender":            selfSignMerchantInfo.MerSex,
			"real_name":         selfSignMerchantInfo.MerIdcardName,
			"idcard_number":     selfSignMerchantInfo.MerIdcardNum,
			"idcard_start_date": tools.TimeFormatYmd(selfSignMerchantInfo.MerIdcardStart),
			"idcard_end_date":   tools.TimeFormatYmd(selfSignMerchantInfo.MerIdcardEnd),
			"idcard_address":    selfSignMerchantInfo.LegalmanHomeAddr,
		}
		for _, value := range selfSignImages {
			image := tools.CdnUrl(value.MlzFilePath)
			if value.DocType == "0001" {
				idcardInfo["idcard_front_image"] = image
			}
			if value.DocType == "0011" {
				idcardInfo["idcard_back_image"] = image
			}
			if value.DocType == "0007" {
				idcardInfo["idcard_handel_image"] = image
			}
		}
		shipperInfo["idcard_info"] = idcardInfo

	}
	// 银行卡信息
	if len(selfSignMerchantInfo.BankAcctNum) > 0 {
		var selfSignImages []models.SelfSignImages
		tools.Db.Model(selfSignImages).Where("restaurant_id = ? and type = 2 and doc_type in (0025,0026)", selfSignMerchantInfo.RestaurantId).Find(&selfSignImages)

		finishStep = 3
		bankCardInfo := map[string]interface{}{
			"bank_acct_num":         selfSignMerchantInfo.BankAcctNum,
			"bank_name":             tools.GetNameByLang(selfSignMerchantInfo.Bank, s.language),
			"bank_id":               selfSignMerchantInfo.Bank.ID,
			"bank_province_name":    tools.GetNameByLang(selfSignMerchantInfo.BankProvince, s.language),
			"bank_province_name_zh": selfSignMerchantInfo.BankProvince.NameZh,
			"bank_province_id":      selfSignMerchantInfo.BankProvince.Code,
			"bank_city_name":        tools.GetNameByLang(selfSignMerchantInfo.BankCity, s.language),
			"bank_city_name_zh":     selfSignMerchantInfo.BankCity.NameZh,
			"bank_city_id":          selfSignMerchantInfo.BankCity.Code,
			"bank_area_name":        tools.GetNameByLang(selfSignMerchantInfo.BankArea, s.language),
			"bank_area_name_ug":     selfSignMerchantInfo.BankArea.NameZh,
			"bank_area_id":          selfSignMerchantInfo.BankArea.Code,
			"bank_branch_name":      selfSignMerchantInfo.BankBranchName,
			"bank_branch_code":      selfSignMerchantInfo.BankBranchCode,
			"bank_bind_mobile":      selfSignMerchantInfo.BankBindMobile,
			"real_name":             selfSignMerchantInfo.LegalName,
		}

		for _, value := range selfSignImages {
			image := tools.CdnUrl(value.MlzFilePath)
			if value.DocType == "0025" {
				bankCardInfo["bank_card_front_image"] = image
			}
			if value.DocType == "0026" {
				bankCardInfo["bank_card_back_image"] = image
			}
		}
		shipperInfo["bank_card_info"] = bankCardInfo
	}

	// 健康证信息
	var stuffInfo models.SelfSignStuff
	tools.Db.Model(models.SelfSignStuff{}).
		Where("type = ?", constants.SELF_SIGN_TYPE_SHIPPER).
		Where("mer_info_id=?", selfSignMerchantInfo.Id).
		Where("deleted_at is NULL").
		First(&stuffInfo)
	if stuffInfo.Id > 0 {
		finishStep = 4
		shipperInfo["health_info"] = map[string]interface{}{
			"certificate_image":      tools.CdnUrl(stuffInfo.HealthCertificateImage),
			"certificate_real_name":  stuffInfo.StaffName,
			"certificate_number":     stuffInfo.StaffIdCard,
			"certificate_start_date": tools.TimeFormatYmd(stuffInfo.HealthCertificateStart),
			"certificate_end_date":   tools.TimeFormatYmd(stuffInfo.HealthCertificateEnd),
		}
	}
	shipperInfo["finish_step"] = finishStep
	shipperInfo["shipper_id"] = admin.ID
	shipperInfo["self_sign_id"] = selfSignMerchantInfo.Id
	return shipperInfo
}

// IsChild
//
// @Description: 验证配送费是否属于当前操作人员
// @Author: Rixat
// @Time: 2023-11-03 09:30:37
// @receiver
// @param c *gin.Context
func (s ShipperService) IsChild(shipper models.Admin, operator models.Admin) bool {
	parentId := shipper.GetParentId()
	if strings.HasPrefix(shipper.ParentID, parentId) {
		return true
	}
	operatorParentID := strings.Split(operator.ParentID, ",")
	shipperParentID := strings.Split(shipper.ParentID, ",")
	for _, operatorId := range operatorParentID {
		for _, shipperId := range shipperParentID {
			if operatorId == shipperId {
				return true
			}
		}
	}
	return false
}

// PostDeleteShipper
//
// @Description: 删除配送员
// @Author: Rixat
// @Time: 2023-11-02 09:27:03
// @receiver
// @param c *gin.Context
func (s ShipperService) PostDeleteShipper(shipperID int) error {
	var shipper models.Admin
	tools.Db.Model(shipper).
		Where("id = ?", shipperID).
		Where("type in ?", []int{constants.ADMIN_TYPE_SHIPPER_ADMIN,constants.ADMIN_TYPE_SHIPPER}).
		First(&shipper)
	if shipper.ID == 0 {
		return errors.New(s.langUtil.T("not_found_shipper"))
	}
	// 操作人员是否有删除权限
	if !s.IsChild(shipper, shipper) {
		return errors.New(s.langUtil.T("no_permission"))
	}
	// 验证是否已分配餐厅
	var bindRestaurantCount int64
	tools.Db.Model(models.Admin{}).
		Joins("JOIN b_admin_store ON t_admin.id = b_admin_store.admin_id").
		Where("admin_id = ?", shipperID).
		Count(&bindRestaurantCount)
	if bindRestaurantCount > 0 {
		return errors.New(s.langUtil.T("shipper_has_restaurant"))
	}
	// 验证是否接订单过
	var takeOrderCount int64
	tools.Db.Model(models.TakeOrder{}).
		Where("admin_id=?", shipperID).
		Count(&takeOrderCount)
	if takeOrderCount > 0 {
		return errors.New(s.langUtil.T("shipper_has_take_order"))
	}
	tx := tools.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	// 删除账户信息
	err := tx.Model(models.Admin{}).
		Where("id=? and type in ?", shipperID, []int{constants.ADMIN_TYPE_SHIPPER_ADMIN,constants.ADMIN_TYPE_SHIPPER}).
		Delete(models.Admin{}).Error
	if err != nil {
		tx.Rollback()
		return errors.New(s.langUtil.T("failed_delete_shipper"))
	}
	var selfSignInfo models.SelfSignMerchantInfo
	tools.Db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id=? and type=?", shipperID, constants.SELF_SIGN_TYPE_SHIPPER).First(&selfSignInfo)
	if selfSignInfo.Id == 0 {
		tx.Rollback()
		return nil
	}
	// 删除入驻信息
	err = tx.Model(models.SelfSignMerchantInfo{}).
		Where("id=? and type=?", selfSignInfo.Id, constants.SELF_SIGN_TYPE_SHIPPER).
		UpdateColumn("deleted_at", carbon.Now()).Error
	if err != nil {
		tx.Rollback()
		return errors.New(s.langUtil.T("failed_delete_shipper"))
	}
	// 删除健康证信息
	err = tx.Model(models.SelfSignStuff{}).
		Where("mer_info_id=? and type=?", selfSignInfo.Id, constants.SELF_SIGN_TYPE_SHIPPER).
		UpdateColumn("deleted_at", carbon.Now()).Error
	if err != nil {
		tx.Rollback()
		return errors.New(s.langUtil.T("failed_delete_shipper"))
	}
	// 删除证件照片信息
	err = tx.Model(models.SelfSignImages{}).
		Where("restaurant_id=? and type=? and deleted_at is NULL", selfSignInfo.RestaurantId, constants.SELF_SIGN_TYPE_SHIPPER).
		UpdateColumn("deleted_at", carbon.Now()).Error
	if err != nil {
		tx.Rollback()
		return errors.New(s.langUtil.T("failed_delete_shipper"))
	}
	// 提交事务
	tx.Commit()
	return nil
}

// ChangeShipperState
//
// @Description: 修改配送员状态
// @Author: Rixat
// @Time: 2023-11-02 09:27:23
// @receiver
// @param c *gin.Context
func (s ShipperService) ChangeShipperState(shipperID int, state int, stateType int) error {
	// 删除账户信息
	var admin models.Admin
	tools.Db.Model(models.Admin{}).
		Where("id=? and type in (8,9)", shipperID).
		First(&admin)
	if admin.ID == 0 {
		return errors.New("not_found")
	}
	// 更新状态
	var err error
	if stateType == 1 { // 配送状态
		err = tools.Db.Model(models.Admin{}).Where("id=?", admin.ID).UpdateColumn("state", state).Error
	}
	if stateType == 2 { // 现金订单状态
		err = tools.Db.Model(models.Admin{}).Where("id=?", admin.ID).UpdateColumn("take_cash_order", state).Error
	}
	if err != nil {
		return errors.New("fail")
	}
	return nil
}

// ShipperList
//
// @Description: 配送员列表
// @Author: Rixat
// @Time: 2023-11-02 09:27:50
// @receiver
// @param c *gin.Context
func (s ShipperService) ShipperList(page, limit, cityId, areaId int, state string, templateId, nextTemplateId int, kw, sort string, c *gin.Context) map[string]interface{} {
	var adminList []models.AdminBase
	query := tools.Db.Model(adminList).Where("type in ? and t_admin.deleted_at is NULL",[]int{constants.ADMIN_TYPE_SHIPPER_ADMIN,constants.ADMIN_TYPE_SHIPPER}).
		Joins("left join t_admin_template_update t on t.shipper_id = t_admin.id and t.deleted_at is NULL")
	// 筛选条件
	if cityId > 0 {
		query = query.Where("admin_city_id=?", cityId)
	}
	if areaId > 0 {
		query = query.Where("admin_area_id=?", areaId)
	}
	if len(state) > 0 {
		query = query.Where("state=?", state)
	}
	if len(kw) > 0 {
		query.Where("real_name like ? or mobile like ?", "%"+kw+"%", "%"+kw+"%")
	}
	if templateId > 0 {
		query = query.Where("shipper_income_template_id=?", templateId)
	}
	if nextTemplateId > 0 {
		query.Where("t.template_id=?", nextTemplateId)
	}
	// 查询结果
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Preload("ShipperIncomeTemplate").
			Preload("AdminTemplateUpdate.Template").
			Preload("City").
			Preload("Area").
			Preload("ShipperRank").
			Preload("SelfSignInfo", "type = 2").
			Scopes(scopes.Page(page, limit)).
			Order(sort).
			Find(&adminList)
	}
	shipperv2Service := shipperv2.NewShipperService(c)
	// 格式化结果
	shipperList := make([]map[string]interface{}, 0)
	for _, admin := range adminList {

		nextTemplateId := 0
		nextTemplateName := ""
		if admin.AdminTemplateUpdate.TemplateID > 0 {
			nextTemplateId = admin.AdminTemplateUpdate.TemplateID
			nextTemplateName = tools.GetNameByLang(admin.AdminTemplateUpdate.Template, s.language)

		}
		inviteUserFee := 0
		inviteOldUserFee := 0
		orderPercent := 0
		if admin.ShipperIncomeTemplateID > 0 {
			inviteUserFee = admin.ShipperIncomeTemplate.InviteUserFee
			inviteOldUserFee = admin.ShipperIncomeTemplate.InviteOldUserFee
			orderPercent = admin.ShipperIncomeTemplate.OrderPercent
		}

		shipperAdmin := models.Admin{ID: admin.ID}
		newRank, _, _, _, _, _ := shipperv2Service.GetShipperRankGrowthRate(&shipperAdmin)
		shipperList = append(shipperList, map[string]interface{}{
			"id":                 admin.ID,
			"city_name":          tools.GetNameByLang(admin.City, s.language),
			"area_name":          tools.GetNameByLang(admin.Area, s.language),
			"avatar":             tools.CdnUrl(admin.Avatar),
			"name":               admin.Name,
			"real_name":          admin.RealName,
			"mobile":             admin.Mobile,
			"state":              admin.State,
			"take_cash_order":    admin.TakeCashOrder,
			"template_id":        admin.ShipperIncomeTemplateID,
			"template_name":      tools.GetNameByLang(admin.ShipperIncomeTemplate, s.language),
			"next_template_id":   nextTemplateId,
			"next_template_name": nextTemplateName,
			"base_salary":        admin.ShipperIncomeTemplate.BaseSalary,
			"self_sign_id":       admin.SelfSignInfo.Id,
			"template_invite_user_fee": inviteUserFee,
			"template_invite_old_user_fee": inviteOldUserFee,
			"template_order_percent":   orderPercent,
			"created_at":         tools.TimeFormatYmdHis(&admin.CreatedAt),
			"self_sign_state":admin.SelfSignInfo.State,//
			"rank":newRank,//分数

		})
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": shipperList,
	}
	return result
}

// BatchUpdateShipperTemplate 批量更新配送员模板
func (s ShipperService) BatchUpdateShipperTemplate(cityId, areaId, templateId int, shipperIds []int) error {
	if templateId == 0 {
		return errors.New("template_id_cant_empty")
	}
	if len(shipperIds) == 0 {
		return errors.New("shipper_not_found")
	}
	var shippers []models.AdminBase
	tools.Db.Model(models.AdminBase{}).
		Preload("AdminTemplateUpdate").
		Where("admin_city_id=?", cityId).
		Where("admin_area_id=?", areaId).
		Where("id in (?) and type in ?", shipperIds, []int{constants.ADMIN_TYPE_SHIPPER_ADMIN,constants.ADMIN_TYPE_SHIPPER}).
		Find(&shippers)
	if len(shippers) == 0 {
		return errors.New("shipper_not_found")
	}
	updateShipperIds := make([]int, 0)
	updateCurrentShipperIds := make([]int, 0)
	type InsertTempplateUpdate struct {
		ShipperId     int
		OldTemplateId int
	}
	insertShipperIds := make([]InsertTempplateUpdate, 0)
	for _, shipper := range shippers {
		// 如果该配送员送来没有设置模板直接更新模板
		if shipper.ShipperIncomeTemplateID == 0 {
			updateCurrentShipperIds = append(updateCurrentShipperIds, shipper.ID)
			continue
		}
		// 如果该配送员送来设置了模板，但是不是当前模板
		if shipper.ShipperIncomeTemplateID != templateId {
			// 如果配送员没有配置下个月的模板，直接插入下个月生效的模板
			if shipper.AdminTemplateUpdate.TemplateID == 0 {
				insertShipperIds = append(insertShipperIds, InsertTempplateUpdate{
					ShipperId:     shipper.ID,
					OldTemplateId: shipper.ShipperIncomeTemplateID,
				})
			} else if shipper.AdminTemplateUpdate.TemplateID != templateId {
				// 如果配送员配置了下个月的模板，但是不是当前要更新的模板，更新下个月生效的模板
				updateShipperIds = append(updateShipperIds, shipper.ID)
			}
		} else { // 如果配送当前模板和需要更新的模板一样
			// 确认下个月模板是否设置并且不是当前需要配置的模板相同
			if shipper.AdminTemplateUpdate.TemplateID != 0 && shipper.AdminTemplateUpdate.TemplateID != templateId {
				updateShipperIds = append(updateShipperIds, shipper.ID)
			}
		}
	}
	db := tools.GetDB()
	// 更新当前模板
	if len(updateCurrentShipperIds) > 0 {
		err := db.Model(models.Admin{}).Where("id in (?)", updateCurrentShipperIds).Update("shipper_income_template_id", templateId).Error
		if err != nil {
			msg := fmt.Sprintf("updateCurrentShipperIds:%v templateID: %d", updateCurrentShipperIds, templateId)
			tools.Logger.Errorf("FATAL 更新配送员模板失败：%s", msg)
			return errors.New("failed")
		}
	}
	// 更新下个月模板
	if len(updateShipperIds) > 0 {
		err := db.Model(shipmentModels.AdminTemplateUpdate{}).Where("shipper_id in (?)", updateShipperIds).Update("template_id", templateId).Error
		if err != nil {
			msg := fmt.Sprintf("updateShipperIds:%v templateID: %d", updateShipperIds, templateId)
			tools.Logger.Errorf("FATAL 更新配送员模板失败：%s", msg)
			return errors.New("failed")
		}
	}
	// 插入下个月模板
	if len(insertShipperIds) > 0 {
		for _, shipperTemplate := range insertShipperIds {
			templateUpdate := shipmentModels.AdminTemplateUpdate{
				OldTemplateID: shipperTemplate.OldTemplateId,
				ShipperID:     shipperTemplate.ShipperId,
				TemplateID:    templateId,
				CreatedAt:     carbon.Now().Carbon2Time(),
			}
			err := db.Model(shipmentModels.AdminTemplateUpdate{}).Create(&templateUpdate).Error
			if err != nil {
				msg := fmt.Sprintf("insertShipperIds:%v templateID: %d", insertShipperIds, templateId)
				tools.Logger.Errorf("FATAL 插入配送员模板失败：%s", msg)
				return errors.New("failed")
			}
		}
	}
	return nil
}



// 获取 是否需要 填写配送员 个人信息
func (s ShipperService) GetNeedAlert(areaId int) map[string]interface{} {
	
	db := tools.GetDB()
	var ct int64 

	//监测 该区域中有没有 没有填写 性别和年龄的配送员
	db.Model(models.Admin{}).
		Where("admin_area_id=?", areaId).
		Where("type in (8,9) and state = 1 and ((age is null or age  = 0) or (sex is null or sex = 0))").
		Count(&ct)
	
	msg :=make(map[string]interface{})
	need :=0 //是否需要弹窗
	if ct > 0 {
		need = 1
	}else{
		shippers :=make([]map[string]interface{},0)
		db.Model(models.Admin{}).
		Where("admin_area_id=?", areaId).
		Where("type in (8,9) and state = 1").
		Select("id,name,real_name,mobile,age,sex").
		Scan(&shippers)
		//是否需要一个一个监测是否合理 
		for _, v := range shippers {
			if  v["age"] == nil || (v["age"] !=nil && (tools.ToInt(v["age"]) < 18 || tools.ToInt(v["age"]) > 75)) {
				need = 1
				break
			}
		}
	}

	msg["need"] = need
	msg["title"]=s.langUtil.T("msg_title")
	msg["msg"] = s.langUtil.T("need_to_update_shipper_update_sex_and_age") //提示内容 需要更新 配送员的性别和年龄
	msg["yes_btn"]=s.langUtil.T("yes_btn")
	msg["no_btn"]=s.langUtil.T("no_btn")
	msg["link_url"]="/app/"+s.langUtil.Lang+"/shipper-submit"

	return msg


}

// 获取待更新的配送员信息
func (s ShipperService) GetShipperUpdateInfo(areaId int) []map[string]interface{} {
	shippers :=make([]map[string]interface{},0)
	db := tools.GetDB()

	db.Model(models.Admin{}).
		Where("admin_area_id=?", areaId).
		Where("type in (8,9) and state = 1").
		Where("((age is null or age  = 0) or (sex is null or sex = 0))").
		Select("id,name,real_name,mobile,age,sex").
		Scan(&shippers)
	if len(shippers) == 0 {
		return shippers
	}
	return shippers
}


// 更新的配送员信息
func (s ShipperService) PostShipperUpdateInfo(ageAndSex []shipperRequest.ShipperAgeAndSex) error {
	
	db := tools.GetDB()
	for _, v := range ageAndSex {
		if ( v.Sex !=0 && (v.Age > 18 && v.Age < 75)) {
			db.Model(models.Admin{}).
			Where("id=?", v.Id).
			Updates(&map[string]interface{}{
				"age": v.Age,
				"sex": v.Sex,
			})	
		}
	}
	return nil
}

// 配送员排行榜
func (s ShipperService)ShipperRankDetail(params shipperResource.ShipperRankDetailRequest) (models.AdminBase, shipperResource.ShipperRankWeekHistory, shipperResource.ShipperRankWeekHistory, []models.AutoDispatchHistory, int64) {

	// 初始化
	db := tools.GetDB()
	var shipper models.AdminBase
	db.Model(models.Admin{}).Preload("ShipperRank").Where("id = ? ",params.ShipperId).First(&shipper)
	// 判断参数 并填充参数
	if(params.Date == ""){
		now := carbon.Now(configs.AsiaShanghai)
		params.Date = now.ToDateTimeString()
	}
	paramsDate := tools.DateTool.ParseDateString(params.Date).ToTime()
	// 获取当前周数
	thisWeek := tools.DateTool.GetWeekOfMonthByTime(paramsDate)

	// 本周
	thisWeekParseDate := tools.DateTool.ParseDateString(params.Date)
	thisWeekStartTime , thisWeekEndTime := tools.DateTool.GetWeekStartAndEndTime(thisWeekParseDate.Year, thisWeekParseDate.Month,thisWeek)

	// 上周
	paramsLastWeekTime := paramsDate.AddDate(0, 0, -7)
	paramsLastWeekDate := tools.DateTool.ParseDateString(paramsLastWeekTime.Format(time.DateOnly))
	lastWeek := tools.DateTool.GetWeekOfMonthByTime(paramsLastWeekTime)
	lastWeekStartTime , lastWeekEndTime := tools.DateTool.GetWeekStartAndEndTime(paramsLastWeekDate.Year, paramsLastWeekDate.Month,lastWeek)

	// 基本查询
	queryShipperRankHistory := func (startTime string,endTime string,shipperRankHistory *shipperResource.ShipperRankWeekHistory) {
		db.Model(models.ShipperRankHistory{}).
		Select("base_score",
			"order_total_count",
			"positive_reviews_count",
			"positive_reviews_score",
			"delivered_on_time",
			"delivered_on_time_order_count",
			"mild_lateness_count",
			"mild_lateness_deduct",
			"moderate_lateness_count",
			"moderate_lateness_deduct",
			"severe_lateness_count",
			"severe_lateness_deduct",
			"negative_reviews_count",
			"negative_reviews_deduct",
			"complaints_count",
			"complaints_deduct",
			"early_delivery_count",
			"early_delivery_deduct",
			"final_score",
			"`rank`").
		Where("shipper_id = ?",shipper.ID).
		Where("start_time >= (?)",startTime).
		Where("end_time <= (?)",endTime).
		Scan(&shipperRankHistory)
	}
	// 查询本周和上周的配送员等级平均值
	var shipperThisWeekRankHistory shipperResource.ShipperRankWeekHistory
	var shipperLastWeekRankHistory shipperResource.ShipperRankWeekHistory

	queryShipperRankHistory(thisWeekStartTime,thisWeekEndTime,&shipperThisWeekRankHistory)
	queryShipperRankHistory(lastWeekStartTime,lastWeekEndTime,&shipperLastWeekRankHistory)

	// 查询订单信息
	var shipperRankDetailOrderList []models.AutoDispatchHistory
	var shipperRankDetailOrderListCount int64
	tx := db.Model(&models.AutoDispatchHistory{}).
		Preload("Shipper").
		Preload("Comments", func(db *gorm.DB) *gorm.DB {
			return db.Select("id","order_id") // 获取投诉订单
		}).
		Preload("ShipperIncomes", func(db *gorm.DB) *gorm.DB {
			return db.Where("type = ?",8).Select("id","order_id") // 获取投诉订单
		}).
		Preload("Order", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "store_id", "user_id", "delivery_start_time", "delivery_end_time") // 只加载 Order 的 id 和 restaurant_id
		}).
		Preload("OrderToday", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "store_id", "user_id", "delivery_start_time", "delivery_end_time") // 只加载 Order 的 id 和 restaurant_id
		}).
		Preload("Order.Restaurant", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "name_ug", "name_zh", "name")
		}).
		Preload("OrderToday.Restaurant", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "name_ug", "name_zh", "name")
		}).
		Preload("Order.User", func(db *gorm.DB) *gorm.DB {
		return db.Select("id", "name")
	}).
		Preload("OrderToday.User", func(db *gorm.DB) *gorm.DB {
		return db.Select("id",  "name")
	}).
		Where("shipper_id = ?", shipper.ID).
		Where("created_at between ? and ?", thisWeekStartTime, thisWeekEndTime)

	// 筛选
	// 基础分
	if params.TypeId == shipperResource.ShipperRankTypeBaseScore {}
	// 好评
	if params.TypeId == shipperResource.ShipperRankTypeGoodComment {
		tx = tx.Where("positive_reviews_score > 0")
	}
	// 准时订单
	if params.TypeId == shipperResource.ShipperRankTypeDiliveredOnTime {
		tx = tx.Where("delivered_on_time_score > 0").Where("order_deliver_state = ?", 0)
	}
	// 订单数量
	if params.TypeId == shipperResource.ShipperRankTypeOrderCount {
	}
	// 迟到0-5分钟
	if params.TypeId == shipperResource.ShipperRankTypeLateTime0To5 {
		tx = tx.Where("mild_lateness_deduct > 0").Where("order_deliver_state = ?", 1)
	}
	// 迟到5-10分钟
	if params.TypeId == shipperResource.ShipperRankTypeLateTime5To10 {
		tx = tx.Where("moderate_lateness_deduct > 0").Where("order_deliver_state = ?", 2)
	}
	// 迟到10分钟以上
	if params.TypeId == shipperResource.ShipperRankTypeLateTime10ToMax {
		tx = tx.Where("severe_lateness_deduct > 0").Where("order_deliver_state = ?", 3)
	}
	// 差评
	if params.TypeId == shipperResource.ShipperRankTypeBadComment {
		tx = tx.Where("negative_reviews_deduct > 0")
	}
	// 投诉
	if params.TypeId == shipperResource.ShipperRankTypeComplain {
		tx = tx.Where("complaints_deduct > 0")
	}
	// 早送
	if params.TypeId == shipperResource.ShipperRankTypeEarlyDelivery20Min {
		tx = tx.Where("early_delivery_deduct > 0").Where("order_deliver_state = ?", 4)
	}


	tx.Count(&shipperRankDetailOrderListCount)
	tx.Scopes(scopes.Page(params.Page, params.Limit)).Find(&shipperRankDetailOrderList)



	// 动态的分数
	shipperLastWeekRankHistory.FinalScore = shipperThisWeekRankHistory.FinalScore
	shipperLastWeekRankHistory.Rank = shipperThisWeekRankHistory.Rank


	return shipper,shipperThisWeekRankHistory,shipperLastWeekRankHistory,shipperRankDetailOrderList,shipperRankDetailOrderListCount
}


