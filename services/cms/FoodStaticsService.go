package cms

import (
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/lang"
	"mulazim-api/scopes"
	"mulazim-api/tools"
)

type FoodStaticsService struct {
	langUtil *lang.LangUtil
	language string
}

func (s FoodStaticsService) GetFoodStaticsList(c *gin.Context, cityID int, areaID int,storeID int, sortColumns string, sortType string,startDate string,endDate string,page int,limit int) ([]struct{
	CityName      string `json:"city_name"`
	StoreID      int64 `json:"store_id"`
	AreaName      string `json:"area_name"`
	RestaurantName string `json:"restaurant_name"`
	RestaurantFoodsName string `json:"restaurant_foods_name"`
	StoreFoodsID    uint `json:"store_foods_id"`
	TotalCount      int64 `json:"total_count" gorm:"column:total_count"`
	Price           float64 `json:"price" gorm:"column:price"`
},int64) {
	db := tools.ReadDb1
	var result []struct {
		CityName      string `json:"city_name"`
		StoreID      int64 `json:"store_id"`
		AreaName      string `json:"area_name"`
		RestaurantName string `json:"restaurant_name"`
		RestaurantFoodsName string `json:"restaurant_foods_name"`
		StoreFoodsID    uint `json:"store_foods_id"`
		TotalCount      int64 `json:"total_count" gorm:"column:total_count"`
		Price           float64 `json:"price" gorm:"column:price"`
	}

	lP := s.language + " "
	selectSql := "b_city.name_" + lP + "as CityName, " + "b_area.name_" + lP + "as AreaName, " + "t_restaurant.name_" + lP + "as RestaurantName, " + "t_restaurant_foods.name_" + lP + "as RestaurantFoodsName, " + "t_order_detail.store_foods_id, sum(t_order_detail.number) as total_count, sum(t_order_detail.number*t_order_detail.price)/100 as price,t_restaurant.id as StoreID"
	query := db.Table("t_order_detail").
		Select(selectSql).
		Joins("left join t_order on t_order.id = t_order_detail.order_id").
		Joins("left join t_restaurant_foods on t_restaurant_foods.id = t_order_detail.store_foods_id").
		Joins("left join t_restaurant on t_restaurant.id = t_restaurant_foods.restaurant_id").
		Joins("left join b_area on b_area.id = t_restaurant.area_id").
		Joins("left join b_city on b_city.id = t_restaurant.city_id")
	//  startDate需要加一天，endDate需要加2天
	startDate = carbon.Parse(startDate).AddDay().ToDateString()
	endDate = carbon.Parse(endDate).AddDays(2).ToDateString()
	query.Where("t_order.archive_date between ? and ? and t_order.state = ?", startDate,endDate, 7).
		Group("store_foods_id")
	if cityID > 0 {
		query = query.Where("t_order.city_id = ?", cityID)
	}
	if areaID > 0 {
		query = query.Where("t_order.area_id = ?", areaID)
	}
	if storeID > 0 {
		query = query.Where("t_order.store_id = ?", storeID)
	}
	var totalCount int64
	query.Count(&totalCount)

	if sortColumns != ""{
		query = query.Order(sortColumns + " "+sortType)
	}

	query.
		Scopes(scopes.Page(page, limit)).
		Scan(&result)
	return result,totalCount
}
//
// GetRestaurantStaticsList
//  @Description: 餐厅销售统计
//  @receiver s
//  @param c
//  @param cityID
//  @param areaID
//  @param storeID
//  @param sortColumns
//  @param sortType
//  @param startDate
//  @param endDate
//  @param page
//  @param limit
//  @param export
//
func (s FoodStaticsService) GetRestaurantStaticsList(c *gin.Context, cityID int, areaID int,storeID int, sortColumns string, sortType string,startDate string,endDate string,page int,limit int,export bool) ([]struct{
	CityName      string `json:"city_name"`
	StoreID      int64 `json:"store_id"`
	AreaName      string `json:"area_name"`
	RestaurantName string `json:"restaurant_name"`
	RestaurantAddress string `json:"restaurant_address"`
	TotalOrderCount      int64 `json:"total_order_count" gorm:"column:total_order_count"`
	TotalOrderPrice      float64 `json:"total_order_price" gorm:"column:total_order_price"`
	TotalMpDeliverPrice      float64 `json:"total_mp_deliver_price" gorm:"column:total_mp_deliver_price"`
	TotalSelfTakeDeliverPrice      float64 `json:"total_self_take_deliver_price" gorm:"column:total_self_take_deliver_price"`
	TotalOnlinePayPrice      float64 `json:"total_online_pay_price" gorm:"column:total_online_pay_price"`
	TotalCashPayPrice      float64 `json:"total_cash_pay_price" gorm:"column:total_cash_pay_price"`
	TotalFoodPrice      float64 `json:"total_food_price" gorm:"column:total_food_price"`
	TotalShipmentPrice      float64 `json:"total_shipment_price" gorm:"column:total_shipment_price"`
	TotalLunchBoxPrice      float64 `json:"total_lunch_box_price" gorm:"column:total_lunch_box_price"`
	TotalDealerProfit	  float64 `json:"total_dealer_profit" gorm:"column:total_dealer_profit"`
	PerOrderPrice      float64 `json:"per_order_price" gorm:"column:per_order_price"`
	PerOrderProfit      float64 `json:"per_order_profit" gorm:"column:per_order_profit"`
	//Price           float64 `json:"price" gorm:"column:price"`
},int64) {
	db := tools.ReadDb1
	var result []struct {
		CityName      string `json:"city_name"`
		StoreID      int64 `json:"store_id"`
		AreaName      string `json:"area_name"`
		RestaurantName string `json:"restaurant_name"`
		RestaurantAddress string `json:"restaurant_address"`
		TotalOrderCount      int64 `json:"total_order_count" gorm:"column:total_order_count"`
		TotalOrderPrice      float64 `json:"total_order_price" gorm:"column:total_order_price"`
		TotalMpDeliverPrice      float64 `json:"total_mp_deliver_price" gorm:"column:total_mp_deliver_price"`
		TotalSelfTakeDeliverPrice      float64 `json:"total_self_take_deliver_price" gorm:"column:total_self_take_deliver_price"`
		TotalOnlinePayPrice      float64 `json:"total_online_pay_price" gorm:"column:total_online_pay_price"`
		TotalCashPayPrice      float64 `json:"total_cash_pay_price" gorm:"column:total_cash_pay_price"`
		TotalFoodPrice      float64 `json:"total_food_price" gorm:"column:total_food_price"`
		TotalShipmentPrice      float64 `json:"total_shipment_price" gorm:"column:total_shipment_price"`
		TotalLunchBoxPrice      float64 `json:"total_lunch_box_price" gorm:"column:total_lunch_box_price"`
		TotalDealerProfit	  float64 `json:"total_dealer_profit" gorm:"column:total_dealer_profit"`
		PerOrderPrice      float64 `json:"per_order_price" gorm:"column:per_order_price"`
		PerOrderProfit      float64 `json:"per_order_profit" gorm:"column:per_order_profit"`

		//Price           float64 `json:"price" gorm:"column:price"`
	}

	lP := s.language + " "
	selectSql := "b_city.name_" + lP + "as CityName, " + "b_area.name_" + lP + "as AreaName, " + "t_restaurant.name_" + lP + "as RestaurantName, " + "t_restaurant.id as StoreID" + ",t_restaurant.address_" + lP + "as restaurant_address"
	selectSql += ",SUM(t_order.price+t_order.shipment+t_order.lunch_box_fee)/100 as total_order_price"

	selectSql += ",SUM(if(t_order.pay_type=5,t_order.price+t_order.shipment+t_order.lunch_box_fee,0))/100 as total_online_pay_price"
	selectSql += ",SUM(if(t_order.pay_type=6,t_order.price+t_order.shipment+t_order.lunch_box_fee,0))/100 as total_cash_pay_price"
	selectSql += ",SUM(if(t_order.delivery_type=1,t_order.price+t_order.shipment+t_order.lunch_box_fee,0))/100 as total_mp_deliver_price"
	selectSql += ",SUM(if(t_order.delivery_type=2,t_order.price+t_order.shipment+t_order.lunch_box_fee,0))/100 as total_self_take_deliver_price"
	selectSql += ",SUM(t_order.price)/100 as total_food_price"
	selectSql += ",SUM(t_order.shipment)/100 as total_shipment_price"
	selectSql += ",SUM(t_order.lunch_box_fee)/100 as total_lunch_box_price"
	selectSql += ",COUNT(t_order.id) as total_order_count"
	selectSql += ",SUM(if(t_order.created_at<'2020-08-11',t_order.dealer_profit+t_order.shipment*(1-t_order.mp_profit/t_order.price),t_order.dealer_profit))/100 as total_dealer_profit"
	selectSql += ",SUM(t_order.price+t_order.shipment+t_order.lunch_box_fee)/COUNT(t_order.id)/100 as per_order_price"
	selectSql += ",SUM(if(t_order.created_at<'2020-08-11',t_order.dealer_profit+t_order.shipment*(1-t_order.mp_profit/t_order.price),t_order.dealer_profit))/COUNT(t_order.id)/100 as per_order_profit"
	query := db.Table("t_order").
		Select(selectSql).
		Joins("left join t_restaurant on t_restaurant.id = t_order.store_id").
		Joins("left join b_area on b_area.id = t_restaurant.area_id").
		Joins("left join b_city on b_city.id = t_restaurant.city_id")
	//  startDate需要加一天，endDate需要加2天
	startDate = carbon.Parse(startDate).AddDay().ToDateString()
	endDate = carbon.Parse(endDate).AddDays(2).ToDateString()
	query.Where("t_order.archive_date between ? and ? and t_order.state in (7,10)", startDate,endDate).
		Group("t_order.store_id")
	if cityID > 0 {
		query = query.Where("t_order.city_id = ?", cityID)
	}
	if areaID > 0 {
		query = query.Where("t_order.area_id = ?", areaID)
	}
	if storeID > 0 {
		query = query.Where("t_order.store_id = ?", storeID)
	}
	var totalCount int64
	query.Count(&totalCount)

	if sortColumns != ""{
		query = query.Order(sortColumns + " "+sortType)
	}
	if export == false {
		query.
			Scopes(scopes.Page(page, limit)).
			Scan(&result)
	}else{
		query.
			Scan(&result)
	}

	return result,totalCount
}
//
// NewFoodStaticsService
//  @Description: 初始化FoodStaticsService
//  @param c
//  @return *FoodStaticsService
//
func NewFoodStaticsService(c *gin.Context) *FoodStaticsService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	FoodStaticsService := FoodStaticsService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &FoodStaticsService
}
