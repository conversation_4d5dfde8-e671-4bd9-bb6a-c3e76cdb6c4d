package cms

import (
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ShipperUserChangeService struct {
	langUtil *lang.LangUtil
	language string
}
// 替换个人二维码关系列表
func (s ShipperUserChangeService) GetShipperChangeList(cityId int,areaId int,startDate string,page int,limit int) (map[string]interface{}) {
	db := tools.ReadDb1
	var items []models.ShipperUserChange
	total :=int64(0)
	query :=db.Model(&models.ShipperUserChange{}).
		Preload("City").
		Preload("Area").
		Preload("OldShipper").
		Preload("NewShipper")
	if startDate != ""{
		query.Where("created_at >= ?",startDate)
	}	
	if areaId > 0{
		query.Where("area_id = ?",areaId)
	}
	if cityId > 0{
		query.Where("city_id = ?",cityId)
	}	
	query.Count(&total)
		query.Limit(limit).
		Offset((page-1)*limit).
		Order("id desc").
		Find(&items)
	result :=make(map[string]interface{})
	resultItems :=make([]map[string]interface{},0)
	for _, v := range items {
		item :=make(map[string]interface{})
		item["id"]=v.ID
		if s.language == "ug" {
			item["city_name"]=v.City.NameUg
			item["area_name"]=v.Area.NameUg
		}else{
			item["city_name"]=v.City.NameZh
			item["area_name"]=v.Area.NameZh
		}
		item["old_shipper_name"]=v.OldShipper.RealName
		item["new_shipper_name"]=v.NewShipper.RealName
		item["old_shipper_mobile"]=v.OldShipper.Mobile
		item["new_shipper_mobile"]=v.NewShipper.Mobile
		item["old_shipper_user_name"]=v.OldShipper.Name
		item["new_shipper_user_name"]=v.NewShipper.Name
		item["state"]=v.State
		item["updated_at"]=v.UpdatedAt.Format("2006-01-02 15:04:05")
		item["reason"]=v.Reason
		item["created_at"]=v.CreatedAt.Format("2006-01-02 15:04:05")
		
		resultItems = append(resultItems, item)
	}
	result["items"]=resultItems
	result["total"]=total
	result["page"]=page
	result["limit"]=limit

	return result
}


// 替换个人二维码关系 创建
// rozimamat
func (s ShipperUserChangeService) CreateShipperChange(oldShipperId int,newShipperId int,reason string) (success bool,result string) {
	db := tools.Db
	
	ct :=int64(0)
	db.Model(&models.ShipperUserChange{}).Where("old_shipper_id = ?",oldShipperId).Count(&ct)
	if ct > 0 {
		return false,"data_exists"
	}
	var oldShipper models.Admin
	db.Model(&models.Admin{}).Where("id = ?",oldShipperId).Find(&oldShipper)

	var newShipper models.Admin
	db.Model(&models.Admin{}).Where("id = ?",newShipperId).Find(&newShipper)
	if newShipper.ID == 0 { // 新的配送员不存在
		return false,"new_shipper_not_exists"
	}
	if newShipper.State != models.AdminStateOk  { // 正常状态下的配送员 不能切换二维码
		return false,"new_shipper_is_disabled"
	}

	if oldShipper.ID == 0 {
		return false,"old_shipper_not_exists"
	}
	if oldShipper.State == models.AdminStateOk  { // 正常状态下的配送员 不能切换二维码
		return false,"old_shipper_not_disabled"
	}
	item :=&models.ShipperUserChange{
		OldShipperId:oldShipperId,
		NewShipperId:newShipperId,
		State:1,
		Reason:reason,
		AreaId:oldShipper.AdminAreaID,
		CityId:oldShipper.AdminCityID,
		UpdatedAt:carbon.Now(configs.AsiaShanghai).Carbon2Time(),
		CreatedAt:carbon.Now(configs.AsiaShanghai).Carbon2Time(),
	}
	er1 :=db.Create(&item).Error
	if er1 != nil {
		tools.Logger.Error("配送员个人二维码替换失败",er1.Error())
		return false,"failed"
	}

	return true,""
}

// 删除配送员个人二维码替换记录
func (s ShipperUserChangeService) DeleteShipperChange(id int) (success bool,result string) {
	db := tools.Db
	var item models.ShipperUserChange
	db.Model(&models.ShipperUserChange{}).Where("id = ?",id).Find(&item)
	if item.ID == 0 {
		return false,"data_not_exists"
	}
	err :=db.Delete(&item).Error
	if err != nil {
		tools.Logger.Error("删除配送员变更记录失败",err.Error())
		return false,"failed"
	}
	return true,""
}
//
// NewShipperUserChangeService
//  @Description: 初始化ShipperUserChangeService
//  @param c
//  @return *ShipperUserChangeService
//
func NewShipperUserChangeService(c *gin.Context) *ShipperUserChangeService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	ShipperUserChangeService := ShipperUserChangeService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &ShipperUserChangeService
}
