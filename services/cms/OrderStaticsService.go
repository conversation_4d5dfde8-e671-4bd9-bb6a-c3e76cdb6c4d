package cms

import (
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/models/cms"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type OrderStaticsService struct {
	langUtil *lang.LangUtil
	language string
}
//
// GetOrderStaticsList
//  @Description: 获取订单统计数据
//  @receiver s
//  @param c
//  @param admin
//  @param areaId
//  @param startDate 日期
//  @return map[string]interface{}
//
func (s OrderStaticsService) GetOrderStaticsList(c *gin.Context,admin models.Admin, areaId int,startDate string) (map[string]interface{}) {
	db := tools.ReadDb1

	var areaIds []int
	db.Model(cms.AdminAreas{}).
		Where("admin_id = ?", admin.ID).
		Group("area_id").
		Pluck("area_id", &areaIds)
	tableName :="t_order_today"
	createdAtWhere := "created_at between ? and  ?" 
	startTime :=carbon.Parse(startDate,configs.AsiaShanghai).Format("Y-m-d 00:00:00")
	endTime :=carbon.Parse(startDate,configs.AsiaShanghai).Format("Y-m-d 23:59:59")

	today :=carbon.Now(configs.AsiaShanghai).Format("Y-m-d")
	if startDate != today {
		tableName ="t_order"
		createdAtWhere = "archive_date between ? and  ?" 
		startTime =carbon.Parse(startDate,configs.AsiaShanghai).AddDays(1).Format("Y-m-d 00:00:00")
		endTime =carbon.Parse(startDate,configs.AsiaShanghai).AddDays(1).Format("Y-m-d 23:59:59")
	}
	timeLinesData :=make([]map[string]interface{},0) 
	fields :="CONCAT(DATE_FORMAT(created_at, '%H:'),LPAD(15 * (FLOOR(MINUTE(created_at) / 15)), 2, '0')) AS timeLine,"
	fields +="COUNT(id) as  orderCount,"
	fields +="SUM(price + shipment + lunch_box_fee)/100 as orderAmount"

	timeLineQuery :=db.Table(tableName).
		Select(fields).
		Where("state in (3,4,5,6,7)").
		Where(createdAtWhere,startTime,endTime)
	if areaId > 0 {
		timeLineQuery = timeLineQuery.Where("area_id in(?)",areaId)
	}
	timeLineQuery.Group("timeLine").Scan(&timeLinesData)

	todayPlanData :=make([]map[string]interface{},0)
	businessFields :="city_id,"
	businessFields +="city_name,"
	businessFields +="area_id,"
	businessFields +="area_type,"
	businessFields +="name_ug,"
	businessFields +="trans_date,"
	businessFields +="target_income,"
	businessFields +="pk_target_min,"
	businessFields +="pk_target_max"
	
	db.Table("t_business_plan").Select(businessFields).
					  Where("t_business_plan.trans_date = ?",startDate).
					  Order("t_business_plan.area_id").Scan(&todayPlanData)

	list := s.GetOrderBusinessData(c,admin,areaId,areaIds,startDate)

	result :=s.FormatOrderBusinessData(admin,list,timeLinesData,todayPlanData)
	if admin.IsCaptain > 0 {
		result = s.GetRivalBusinessPlanReport(admin,result,startDate,endTime,tableName,createdAtWhere)
	}else if admin.Type == constants.ADMIN_TYPE_DEALER ||  admin.Type == constants.ADMIN_TYPE_DEALER_SUB {
		result["cityChartData"] = make([]map[string]interface{},0)
	}

	return result
}




func (s OrderStaticsService) GetOrderBusinessData(c *gin.Context,admin models.Admin,areaId int,areaIds []int,startDate string) ([]map[string]interface{}) {
	db := tools.ReadDb1
	result :=make([]map[string]interface{},0)
	tableName :="t_order_today"
	createdAtWhere := "created_at between ? and  ?"  
	startTime :=carbon.Parse(startDate,configs.AsiaShanghai).Format("Y-m-d 00:00:00")
	endTime :=carbon.Parse(startDate,configs.AsiaShanghai).Format("Y-m-d 23:59:59")

	today :=carbon.Now(configs.AsiaShanghai).Format("Y-m-d")

	if startDate != today {
		tableName ="t_order"
		createdAtWhere = "archive_date between ? and  ?" 
		startTime =carbon.Parse(startDate,configs.AsiaShanghai).AddDays(1).Format("Y-m-d 00:00:00")
		endTime =carbon.Parse(startDate,configs.AsiaShanghai).AddDays(1).Format("Y-m-d 23:59:59")
	}


	fields := "area_id,"
	fields += "sum(order_price)/100 as actual_income ,"
	fields += "SUM(if(pay_type=1,order_price+total_discount_amount,0))/100 as 'cash_pay',"
	fields += "SUM(if(pay_type=2,order_price+total_discount_amount,0))/100 as 'coin_pay',"
	fields += "SUM(if(pay_type=3,order_price+total_discount_amount,0))/100 as 'alipay',"
	fields += "SUM(if(pay_type=5,order_price+total_discount_amount,0))/100 as 'wechat_pay',"
	fields += "SUM(if(pay_type=6,order_price+total_discount_amount,0))/100 as 'agent_pay',"
	fields += "SUM(if(pay_type=7,order_price+total_discount_amount,0))/100 as 'union_pay',"
	fields += "SUM(price)/100 as 'food_income', "
	fields += "SUM(lunch_box_fee)/100 as 'lunch_box_fee', "
	fields += "SUM(shipment)/100 as total_shipment, "
	fields += "SUM(dealer_profit)/100 as dealer_profit,"
	fields += "SUM(mp_profit)/100 as mp_profit,"
	fields += "sum(res_profit)/100 as res_income,order_price_res,"
	fields += "COUNT(id) as order_count ,"
	fields += "count(if(delivery_type is null or delivery_type =1,true,null)) as order_count_mp,"
	fields += "count(if(delivery_type=2,true,null)) as order_count_self,"
	fields += "sum(if((pay_type=3 or pay_type=5 or pay_type=6) and (delivery_type is null or delivery_type =1 ) ,order_price+total_discount_amount,0))/100 AS total_money_mp,"
	fields += "sum(if((pay_type=3 or pay_type=5 or pay_type=6) and (delivery_type  = 2 ) ,order_price+total_discount_amount,0))/100 AS total_money_self,"
	fields += "(sum(order_price+total_discount_amount)/100)/COUNT(id) AS price_per_ticket,"
	fields += "(SUM(dealer_profit)/100)/COUNT(id) as profit_per_ticket"
	 
	//查询主要经营数据
	businessDataSubQuery :=db.Table(tableName).Select(fields).Where(createdAtWhere,startTime,endTime)

	if admin.IsCaptain > 0 {
		db.Table("b_area").Where("group_id = ? and team_id = ? and state = ?",admin.GroupId,admin.TeamId,1).Pluck("id",&areaIds)
	}
	if areaId > 0 {
		businessDataSubQuery = businessDataSubQuery.Where("area_id in (?)",areaIds)
	}
	businessDataSubQuery = businessDataSubQuery.Where("state in ?",[]int{3,4,5,6,7}).Group("area_id").Order("area_id")
	
	businessFields :=""
	businessFields +="t_business_plan.city_id as city_id,"
	businessFields +="t_business_plan.area_id as id,"
	businessFields +="t_business_plan.name_ug,"
	businessFields +="t_business_plan.name_"+s.language+" as name,"
	businessFields +="Z.actual_income,"
	businessFields +="Z.cash_pay,"
	businessFields +="Z.coin_pay,"
	businessFields +="Z.alipay,"
	businessFields +="Z.wechat_pay,"
	businessFields +="Z.agent_pay,"
	businessFields +="Z.union_pay,"
	businessFields +="Z.food_income,"
	businessFields +="Z.lunch_box_fee,"
	businessFields +="Z.total_shipment,"
	businessFields +="Z.dealer_profit,"
	businessFields +="Z.mp_profit,"
	businessFields +="Z.res_income,"
	businessFields +="Z.order_count,"
	businessFields +="Z.order_count_mp,"
	businessFields +="Z.order_count_self,"
	businessFields +="Z.total_money_mp,"
	businessFields +="Z.total_money_self,"
	businessFields +="Z.price_per_ticket,"
	businessFields +="Z.profit_per_ticket"

	businessQuery := db.Table("t_business_plan").Select(businessFields).Joins("left join (?) as Z on Z.area_id = t_business_plan.area_id",businessDataSubQuery)
	if areaId > 0{
		businessQuery = businessQuery.Where("t_business_plan.area_id in ?",areaIds)
	}
	
	businessData :=make([]map[string]interface{},0)
	businessQuery.Where("t_business_plan.trans_date = ?",startDate).Group("t_business_plan.area_id").Scan(&businessData)

	//获取取消订单数量和总额
	cancelOrderFields :="area_id,COUNT(id) AS canceled_order_count,sum( price + shipment + lunch_box_fee )/ 100 AS canceled_order_amount"

	cancelOrderData :=make([]map[string]interface{},0)

	cancelOrderSubQuery := db.Table(tableName).Select(cancelOrderFields).Where(createdAtWhere,startTime,endTime)
	if areaId > 0 {
		cancelOrderSubQuery = cancelOrderSubQuery.Where("area_id in ?",areaIds)
	}
	cancelOrderSubQuery = cancelOrderSubQuery.Where("state in ?",[]int{8,9}).Group("area_id").Order("area_id")

	businessFields ="t_business_plan.area_id as id,"
	businessFields +="t_business_plan.name_ug,"
	businessFields +="Z.canceled_order_count,"
	businessFields +="Z.canceled_order_amount"

	cancelOrderQuery :=db.Table("t_business_plan").Select(businessFields).Joins("left join (?) as Z on Z.area_id = t_business_plan.area_id",cancelOrderSubQuery)
	if areaId > 0{
		cancelOrderQuery =cancelOrderQuery.Where("t_business_plan.area_id in ?",areaIds)
	}
	cancelOrderQuery.Where("t_business_plan.trans_date = ?",startDate).Group("t_business_plan.area_id").Scan(&cancelOrderData)

	//获取迟到订单数和平均迟到时间
	lateOrderFields :="area_id,count(id) as late_order_count,sum(TIMESTAMPDIFF(MINUTE,booking_time,delivery_end_time))/count(id) as late_time_avg"
	lateOrderData :=make([]map[string]interface{},0)
	lateOrderSubQuery :=db.Table(tableName).Select(lateOrderFields).Where(createdAtWhere,startTime,endTime)
	if areaId > 0 {
		lateOrderSubQuery = lateOrderSubQuery.Where("area_id in ?",areaIds)
	}
	lateOrderSubQuery = lateOrderSubQuery.Where("state in ?",[]int{3,4,5,6,7}).
					Where("(TIMESTAMPDIFF(SECOND,booking_time,delivery_end_time))>60").
					Group("area_id").Order("area_id")
	businessFields	="t_business_plan.area_id as id,"				
	businessFields	+="t_business_plan.name_ug,"
	businessFields	+="Z.late_order_count,"
	businessFields	+="Z.late_time_avg"
	
	lateOrderQuery :=db.Table("t_business_plan").Select(businessFields).Joins("left join (?) as Z on Z.area_id = t_business_plan.area_id",lateOrderSubQuery)
	if areaId > 0 {
		lateOrderQuery = lateOrderQuery.Where("t_business_plan.area_id in ?",areaIds)
	}
	lateOrderQuery.Where("t_business_plan.trans_date = ?",startDate).Group("t_business_plan.area_id").Scan(&lateOrderData)

	//获取各代理区域当天上班的骑手数量
	shipperFields :="area_id,count(DISTINCT shipper_id) as shipper_count"
	shipperData :=make([]map[string]interface{},0)
	shipperSubQuery :=db.Table(tableName).Select(shipperFields).Where(createdAtWhere,startTime,endTime)
	if areaId > 0 {
		shipperSubQuery = shipperSubQuery.Where("area_id in ?",areaIds)
	}
	shipperSubQuery = shipperSubQuery.Where("state in ?",[]int{3,4,5,6,7}).Group("area_id").Order("area_id")
	
	businessFields	="t_business_plan.city_id as city_id,"				
	businessFields	+="t_business_plan.area_id,"
	businessFields	+="Z.shipper_count"
	
	shipperQuery :=db.Table("t_business_plan").Select(businessFields).Joins("left join (?) as Z on Z.area_id = t_business_plan.area_id",shipperSubQuery)
	if areaId > 0 {
		shipperQuery = shipperQuery.Where("t_business_plan.area_id in ?",areaIds)
	}
	shipperQuery.Where("t_business_plan.trans_date = ?",startDate).Group("t_business_plan.area_id").Scan(&shipperData)			  

	//各代理区域后台授权完成的订单数量统计
	finishOrderFields :="area_id,count(id) as admin_finished_order_count"
	finishOrderData :=make([]map[string]interface{},0)
	finishOrderSubQuery := db.Table(tableName).Select(finishOrderFields).Where(createdAtWhere,startTime,endTime)
	if areaId > 0 {
		finishOrderSubQuery = finishOrderSubQuery.Where("area_id in ?",areaIds)
	}
	finishOrderSubQuery = finishOrderSubQuery.Where("state in ? and shipper_complete_grant = ?",[]int{3,4,5,6,7},1).Group("area_id").Order("area_id")

	businessFields	="t_business_plan.area_id,"				
	businessFields	+="Z.admin_finished_order_count"
	
	finishOrderQuery :=db.Table("t_business_plan").Select(businessFields).Joins("left join (?) as Z on Z.area_id = t_business_plan.area_id",finishOrderSubQuery)
	if areaId > 0 {
		finishOrderQuery = finishOrderQuery.Where("t_business_plan.area_id in ?",areaIds)
	}
	finishOrderQuery.Where("t_business_plan.trans_date = ?",startDate).Group("t_business_plan.area_id").Scan(&finishOrderData)
 

	for i := 0; i < len(businessData) ;i++ {
		res := map[string]interface{}{
			"area_id" : businessData[i]["id"],
            "area_name" : businessData[i]["name"],
            "actual_income" : tools.ToRound(businessData[i]["actual_income"],2),
            "total_money_mp" : tools.ToRound(businessData[i]["total_money_mp"], 2),
            "total_money_self" : tools.ToRound(businessData[i]["total_money_self"], 2),
            "cash_income" : tools.ToRound(businessData[i]["cash_pay"], 2),
            "coin_pay" : tools.ToRound(businessData[i]["coin_pay"], 2),
            "alipay" : tools.ToRound(businessData[i]["alipay"], 2),
            "wechat_pay" : tools.ToRound(businessData[i]["wechat_pay"], 2),
            "agent_pay" : tools.ToRound(businessData[i]["agent_pay"], 2),
            "union_pay" : tools.ToRound(businessData[i]["union_pay"], 2),
            "food_income" : tools.ToRound(businessData[i]["food_income"], 2),
            "lunch_box_fee" : tools.ToRound(businessData[i]["lunch_box_fee"], 2),
            "total_shipment" : tools.ToRound(businessData[i]["total_shipment"], 2),
            "dealer_profit" : tools.ToRound(businessData[i]["dealer_profit"], 2),
            "mp_profit" : tools.ToRound(businessData[i]["mp_profit"], 2),
            "res_income" : tools.ToRound(businessData[i]["res_income"], 2),
            "order_count" : businessData[i]["order_count"],
            "order_count_mp" : businessData[i]["order_count_mp"],
            "order_count_self" : businessData[i]["order_count_self"],
            "canceled_order_count" : cancelOrderData[i]["canceled_order_count"],
            "canceled_order_amount" : tools.ToRound(cancelOrderData[i]["canceled_order_amount"], 2),
            "late_order_count" : lateOrderData[i]["late_order_count"],
            "late_time_avg" : tools.ToRound(lateOrderData[i]["late_time_avg"], 2),
            "price_per_ticket" : tools.ToRound(businessData[i]["price_per_ticket"], 2),
            "profit_per_ticket" : tools.ToRound(businessData[i]["profit_per_ticket"], 2),
            "admin_finished_order_count" : finishOrderData[i]["admin_finished_order_count"],
            "shipper_count" : shipperData[i]["shipper_count"],
		}

		to := tools.ToFloat64(businessData[i]["total_money_self"])
		if to == 0 {
			res["total_money_mp"]=businessData[i]["actual_income"]
		}
 		os := tools.ToFloat64(businessData[i]["order_count_self"])
		if os==0 {
			res["order_count_mp"]=businessData[i]["order_count"]
		}
		result = append(result, res)
	}


	 
	return result
}
//格式化今日统计 
//2024-04-04 rozimamat 
func (s OrderStaticsService) FormatOrderBusinessData(admin models.Admin,list []map[string]interface{},timeLine []map[string]interface{},todayPlan []map[string]interface{}) (map[string]interface{}) {
	report :=make(map[string]interface{})

	
	total_shipment := 0.0
	total_food_amount := 0.0
	total_lunch_box_fee := 0.0


	total_coin := 0.0
	total_wechat := 0.0
	total_alipay := 0.0
	total_agentPay := 0.0
	total_cash := 0.0

	all_restaurant_profit := 0.0
	all_dealer_profit := 0.0
	all_mulazim_profit := 0.0
 

	cityChartData := make([]map[string]interface{},0)
	townChartData := make([]map[string]interface{},0)
	cityIndex := 0
	townIndex := 0
	totalTargetIncome := 0.0
	totalPkTargetMin := 0.0
	totalPkTargetMax := 0.0
	areaType := 0
	planLength :=len(todayPlan)

	

	for _, reportArea := range list {
		 
		for j := 0; j < planLength; j++ {
			if(reportArea["area_id"] == todayPlan[j]["area_id"]){
				areaType = tools.ToInt(todayPlan[j]["area_type"])
				if tools.InArray(areaType,[]int{1,2}) {
					cityChartData=append(cityChartData,map[string]interface{}{})
					cityChartData[cityIndex]["target_income"] = tools.ToRound(todayPlan[j]["target_income"],2)
					cityChartData[cityIndex]["pk_target_min"] = tools.ToRound(todayPlan[j]["pk_target_min"],2)
					cityChartData[cityIndex]["pk_target_max"] = tools.ToRound(todayPlan[j]["pk_target_max"],2)
				}else{
					townChartData = append(townChartData,map[string]interface{}{} )
					townChartData[townIndex]["target_income"] = tools.ToRound(todayPlan[j]["target_income"],2)
					townChartData[townIndex]["pk_target_min"] = tools.ToRound(todayPlan[j]["pk_target_min"],2)
					townChartData[townIndex]["pk_target_max"] = tools.ToRound(todayPlan[j]["pk_target_max"],2)
				}
				totalTargetIncome += tools.ToFloat64(todayPlan[j]["target_income"])
				totalPkTargetMin +=  tools.ToFloat64(todayPlan[j]["pk_target_min"])
				totalPkTargetMax +=  tools.ToFloat64(todayPlan[j]["pk_target_max"])
				break
			}
		}

		if tools.InArray(areaType,[]int{1,2}) {

			cityChartData[cityIndex]["area_id"] = reportArea["area_id"]
			cityChartData[cityIndex]["area_name"] = reportArea["area_name"]
			
			cityChartData[cityIndex]["total_amount"] = tools.ToFloat64(reportArea["actual_income"])

			cityChartData[cityIndex]["total_money_mp"] = tools.ToFloat64(reportArea["total_money_mp"])
			cityChartData[cityIndex]["total_money_self"] = tools.ToFloat64(reportArea["total_money_self"])
			
			cityChartData[cityIndex]["wechat_pay"] = tools.ToFloat64(reportArea["wechat_pay"])
			cityChartData[cityIndex]["cash_pay"] = tools.ToFloat64(reportArea["cash_income"])
			cityChartData[cityIndex]["agent_pay"] = tools.ToFloat64(reportArea["agent_pay"])
			cityChartData[cityIndex]["coin_pay"] = tools.ToFloat64(reportArea["coin_pay"])
			cityChartData[cityIndex]["food_income"] = tools.ToFloat64(reportArea["food_income"])
			cityChartData[cityIndex]["shipment"] = tools.ToFloat64(reportArea["total_shipment"])
			cityChartData[cityIndex]["lunch_box_fee"] = tools.ToFloat64(reportArea["lunch_box_fee"])
			cityChartData[cityIndex]["res_income"] = tools.ToFloat64(reportArea["res_income"])
			cityChartData[cityIndex]["dealer_profit"] = tools.ToFloat64(reportArea["dealer_profit"])
			cityChartData[cityIndex]["mp_profit"] = tools.ToFloat64(reportArea["mp_profit"])
			cityChartData[cityIndex]["order_count"] = tools.ToFloat64(reportArea["order_count"])
			cityChartData[cityIndex]["order_count_mp"] = tools.ToFloat64(reportArea["order_count_mp"])
			cityChartData[cityIndex]["order_count_self"] = tools.ToFloat64(reportArea["order_count_self"])

			cityChartData[cityIndex]["per_order_price"] = tools.ToFloat64(reportArea["price_per_ticket"])
			cityChartData[cityIndex]["profit_per_ticket"] = tools.ToFloat64(reportArea["profit_per_ticket"])
			cityChartData[cityIndex]["canceled_order_count"] = tools.ToFloat64(reportArea["canceled_order_count"])
			cityChartData[cityIndex]["canceled_order_amount"] = tools.ToFloat64(reportArea["canceled_order_amount"])
			cityChartData[cityIndex]["late_order_count"] = tools.ToFloat64(reportArea["late_order_count"])
			cityChartData[cityIndex]["late_time_avg"] = tools.ToFloat64(reportArea["late_time_avg"])
			cityChartData[cityIndex]["admin_finished_order_count"] = tools.ToFloat64(reportArea["admin_finished_order_count"])
			cityChartData[cityIndex]["shipper_count"] = tools.ToFloat64(reportArea["shipper_count"])
			
			if cityChartData[cityIndex] !=nil && cityChartData[cityIndex]["target_income"] == nil {
				cityChartData[cityIndex]["target_income"] = 0
			}
			 
			cityIndex++

		}else{
			townChartData[townIndex]["area_id"] = reportArea["area_id"]
			townChartData[townIndex]["area_name"] = reportArea["area_name"]
			townChartData[townIndex]["total_amount"] = tools.ToFloat64(reportArea["actual_income"])

			townChartData[townIndex]["total_money_mp"] = tools.ToFloat64(reportArea["total_money_mp"])
			townChartData[townIndex]["total_money_self"] = tools.ToFloat64(reportArea["total_money_self"])

			
			townChartData[townIndex]["wechat_pay"] = tools.ToFloat64(reportArea["wechat_pay"])
			townChartData[townIndex]["cash_pay"] = tools.ToFloat64(reportArea["cash_income"])
			townChartData[townIndex]["agent_pay"] = tools.ToFloat64(reportArea["agent_pay"])
			townChartData[townIndex]["coin_pay"] = tools.ToFloat64(reportArea["coin_pay"])
			townChartData[townIndex]["food_income"] = tools.ToFloat64(reportArea["food_income"])
			townChartData[townIndex]["shipment"] = tools.ToFloat64(reportArea["total_shipment"])
			townChartData[townIndex]["lunch_box_fee"] = tools.ToFloat64(reportArea["lunch_box_fee"])
			townChartData[townIndex]["res_income"] = tools.ToFloat64(reportArea["res_income"])
			townChartData[townIndex]["dealer_profit"] = tools.ToFloat64(reportArea["dealer_profit"])
			townChartData[townIndex]["mp_profit"] = tools.ToFloat64(reportArea["mp_profit"])
			townChartData[townIndex]["order_count"] = tools.ToFloat64(reportArea["order_count"])
			
			townChartData[townIndex]["order_count_mp"] = tools.ToFloat64(reportArea["order_count_mp"])
			townChartData[townIndex]["order_count_self"] = tools.ToFloat64(reportArea["order_count_self"])

			townChartData[townIndex]["per_order_price"] = tools.ToFloat64(reportArea["price_per_ticket"])
			townChartData[townIndex]["profit_per_ticket"] = tools.ToFloat64(reportArea["profit_per_ticket"])
			townChartData[townIndex]["canceled_order_count"] = tools.ToFloat64(reportArea["canceled_order_count"])
			townChartData[townIndex]["canceled_order_amount"] = tools.ToFloat64(reportArea["canceled_order_amount"])
			townChartData[townIndex]["late_order_count"] = tools.ToFloat64(reportArea["late_order_count"])
			townChartData[townIndex]["late_time_avg"] = tools.ToFloat64(reportArea["late_time_avg"])
			townChartData[townIndex]["admin_finished_order_count"] = tools.ToFloat64(reportArea["admin_finished_order_count"])
			townChartData[townIndex]["shipper_count"] = tools.ToFloat64(reportArea["shipper_count"])

			if townChartData[townIndex] !=nil && townChartData[townIndex]["target_income"] == nil {
				townChartData[townIndex]["target_income"] = 0
			}
			
			townIndex++
		}

		
		total_wechat += tools.ToFloat64(reportArea["wechat_pay"])
		total_agentPay += tools.ToFloat64(reportArea["agent_pay"])
		total_alipay += 0
		total_coin += tools.ToFloat64(reportArea["coin_pay"])
		total_cash += tools.ToFloat64(reportArea["cash_income"])
		
		all_restaurant_profit += tools.ToFloat64(reportArea["res_income"])
		all_dealer_profit +=  tools.ToFloat64(reportArea["dealer_profit"])
		all_mulazim_profit += tools.ToFloat64(reportArea["mp_profit"])
		
		total_shipment +=  tools.ToFloat64(reportArea["total_shipment"])
		total_food_amount += tools.ToFloat64(reportArea["food_income"])
		total_lunch_box_fee +=  tools.ToFloat64(reportArea["lunch_box_fee"])

	}

	report["total_coin"] =tools.ToRound(total_coin,2)
	report["total_wechat"] = tools.ToRound(total_wechat,2)
	report["total_alipay"] = tools.ToRound(total_alipay,2)
	report["total_agentPay"] = tools.ToRound(total_agentPay,2)
	report["total_cash"] = tools.ToRound(total_cash,2)
	report["total_business_amount"] =  tools.ToPrice(tools.ToRound((total_coin+total_wechat+total_alipay+total_agentPay+total_cash),2))
	report["total_target_income"] = tools.ToRound(totalTargetIncome,2)
	report["total_pk_target_min"] = tools.ToRound(totalPkTargetMin,2)
	report["total_pk_target_max"] = tools.ToRound(totalPkTargetMax,2)
	report["total_food_amount"] = tools.ToRound(total_food_amount,2)
	report["total_shipment"] = tools.ToRound(total_shipment,2)
	report["total_lunch_box_fee"] = tools.ToRound(total_lunch_box_fee,2)

	report["total_restaurant_income"] = tools.ToRound(all_restaurant_profit,2)
	report["total_dealer_profit"] = tools.ToRound(all_dealer_profit,2)
	report["total_mulazim_profit"] = tools.ToRound(all_mulazim_profit,2)

	
	//序列化订单按时间段分布统计数据
	orderByHours := make([]map[string]interface{},0)
	
	hour := carbon.CreateFromTime(6, 0, 0,configs.AsiaShanghai)
	
	quarter := 15
	for k:=0;k<18;k++{
		for m := 0; m<60;m=m+quarter{
			temp :=make(map[string]interface{})
			mm := m
			if m > 0 {
				mm = quarter 
			}
			hour = hour.AddMinutes(mm)
			temp["timeLine"] = hour.Format("H:i")
			temp["orderCount"] = 0
			temp["orderAmount"] = 0
			for _, v := range timeLine {
				if hour.Format("H:i") == v["timeLine"] {
					temp["orderCount"] = tools.ToInt(v["orderCount"])
					temp["orderAmount"] = tools.ToRound(v["orderAmount"],2)
				}
			}
			orderByHours = append(orderByHours,temp)
		}
		hour = hour.AddMinutes(quarter)
	}

	
	report["orderByTimeLine"] = orderByHours
	report["cityChartData"] = cityChartData
	report["townChartData"] = townChartData
	if admin.IsCaptain > 0 {
		mixedChatData :=make([]map[string]interface{},0)
		mixedChatData = append(mixedChatData, cityChartData...)
		mixedChatData = append(mixedChatData, townChartData...)
		report["cityChartData"] = mixedChatData
	}
	mixedChatData :=make([]map[string]interface{},0)
	mixedChatData = append(mixedChatData, cityChartData...)
	mixedChatData = append(mixedChatData, townChartData...)
	report["data"] = mixedChatData

	report["msg"]="ok"
	report["status"]=200

	return report
}

//获取PK对手数据 
//2024-04-04 rozimamamt

func (s OrderStaticsService) GetRivalBusinessPlanReport(admin models.Admin,data map[string]interface{},startDate string,endDate string,tableName string,createdAtWhere string) (map[string]interface{}) {
	db := tools.ReadDb1
	var rivalAreaIds []int
	db.Table("b_area").Where("group_id = ? and team_id <> ? ",admin.GroupId,admin.TeamId).Pluck("id",&rivalAreaIds)	
	if len(rivalAreaIds) == 0 {
		return data
	}
	businessFields :="sum(target_income) as target_income,"
	businessFields +="sum(pk_target_min) as pk_target_min,"
	businessFields +="sum(pk_target_max) as pk_target_max"

	rivalPlan :=make(map[string]interface{})
	db.Table("t_business_plan").Select(businessFields).
						Where("t_business_plan.trans_date = ?",startDate).
						Where("area_id in ?",rivalAreaIds).
						Debug().Scan(&rivalPlan)

	rivalOrderByTimeLine :=make(map[string]interface{})
	db.Table(tableName).Select("sum(price + shipment + lunch_box_fee)/100 as orderAmount").
		Where("area_id in ?",rivalAreaIds).
		Where("state in ?",[]int{3,4,5,6,7}).
		Where(createdAtWhere,startDate,endDate).
		Scan(&rivalOrderByTimeLine)

	data["total_rival_business_amount"] = tools.ToFloat64(rivalOrderByTimeLine["orderAmount"])
	data["total_rival_target_income"] = tools.ToFloat64(rivalPlan["target_income"])
	data["total_rival_pk_target_min"] = tools.ToFloat64(rivalPlan["pk_target_min"])
	data["total_rival_pk_target_max"] = tools.ToFloat64(rivalPlan["pk_target_max"])

	return data
}
//
// NewOrderStaticsService
//  @Description: 初始化OrderStaticsService
//  @param c
//  @return *OrderStaticsService
//
func NewOrderStaticsService(c *gin.Context) *OrderStaticsService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	OrderStaticsService := OrderStaticsService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &OrderStaticsService
}
