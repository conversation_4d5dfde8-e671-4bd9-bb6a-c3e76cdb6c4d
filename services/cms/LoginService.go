package cms

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"io"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
	"time"

	"github.com/elliotchance/phpserialize"
	"github.com/gin-gonic/gin"
)

type LoginService struct {
	langUtil *lang.LangUtil
	language string
}

func NewLoginService(c *gin.Context) *LoginService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	seckillService := LoginService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &seckillService
}

func (c *LoginService)Login(name string,password string,tokenApi string,ctx *gin.Context)(bool, models.Admin, string,string,string){
  // 验证用户名和密码

	if tokenApi!="MMavmXJnnxzEfGNZHTAaeJEXuhsz87et" {
		return false, models.Admin{}, "", "", "asd"
	}
	success, admin, errMsg := c.loginCheck(name, password)
	if !success {
		return false, admin, "", "", errMsg
	}


	// 生成JWT token
	token, _ :=	 tools.NewJWTTools().GenerateToken(map[string]interface{}{
		"id":            admin.ID,
		"name":          admin.Name,
		"mobile":        admin.Mobile,
		"type":          admin.Type,
		"admin_city_id": admin.AdminCityID,
		"admin_area_id": admin.AdminAreaID, 
	})

	// 生成session id
	sessionId := tools.GenRandomString(40)

	// 第一步：创建关联数组形式的会话数据
	sessionData := map[string]interface{}{
		"login_dashboard_59ba36addc2b2f9401580f014c7f58ea4e30989d": int(admin.ID),
		"_token": token,
	}
	
	// 第二步：将会话数据序列化为PHP格式字符串（第一层序列化）
	serializedArrayData, err := phpserialize.Marshal(sessionData, nil)
	if err != nil {
		tools.Logger.Error("序列化session数据失败", err)
		return false, admin, "", "", "序列化session数据失败"
	}
	
	// 第三步：将第一层序列化的结果再次序列化为字符串（第二层序列化）
	// 这是Laravel风格的会话存储格式，它先将数据序列化为字符串，再将字符串序列化为存储格式
	serializedData, err := phpserialize.Marshal(string(serializedArrayData), nil)
	if err != nil {
		tools.Logger.Error("二次序列化session数据失败", err)
		return false, admin, "", "", "二次序列化session数据失败"
	}
	
	// 将最终序列化的数据存储到Redis
	redis := tools.GetRedisCmsHelper()
	sessionKey := "laravel:" + sessionId
	redis.Set(ctx, sessionKey, string(serializedData), 24*time.Hour)


	// 序列化session id
	sessionIdBytes, err := phpserialize.Marshal(sessionId, nil)
	if err != nil {
		tools.Logger.Error("序列化session id失败", err)
		return false, admin, "", "", "序列化session id失败"
	}

	// 加密session id
	key, err := base64.StdEncoding.DecodeString(configs.MyApp.CmsKey)
	if err != nil {
		tools.Logger.Error("cms key 错误", err)
		return false, admin, "", "", "cms key 错误"
	}

	// 1. 生成随机IV
	iv := make([]byte, 16)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		tools.Logger.Error("生成IV失败", err)
		return false, admin, "", "", "生成IV失败"
	}

	// 2. 创建AES加密块
	block, err := aes.NewCipher(key)
	if err != nil {
		tools.Logger.Error("创建AES加密块失败", err)
		return false, admin, "", "", "创建AES加密块失败"
	}

	// 3. 计算填充
	blockSize := aes.BlockSize
	padding := blockSize - len(sessionIdBytes)%blockSize
	if padding > 0 {
		padText := make([]byte, padding)
		for i := range padText {
			padText[i] = byte(padding)
		}
		sessionIdBytes = append(sessionIdBytes, padText...)
	}

	// 4. 加密
	ciphertext := make([]byte, len(sessionIdBytes))
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext, sessionIdBytes)

	// 5. 生成Cookie结构
	ivBase64 := base64.StdEncoding.EncodeToString(iv)
	valueBase64 := base64.StdEncoding.EncodeToString(ciphertext)

	// 计算HMAC
	hmacGenerator := hmac.New(sha256.New, key)
	hmacGenerator.Write([]byte(ivBase64 + valueBase64))
	hmacHex := hex.EncodeToString(hmacGenerator.Sum(nil))

	// 创建Cookie结构
	cookieData := struct {
		Iv    string `json:"iv"`
		Value string `json:"value"`
		Mac   string `json:"mac"`
	}{
		Iv:    ivBase64,
		Value: valueBase64,
		Mac:   hmacHex,
	}

	// 序列化为JSON并Base64编码
	cookieJson, err := json.Marshal(cookieData)
	if err != nil {
		tools.Logger.Error("序列化cookie结构失败", err)
		return false, admin, "", "", "序列化cookie结构失败"
	}

	cookieValue := base64.StdEncoding.EncodeToString(cookieJson)

	return true, admin, token,cookieValue, ""

}

// loginCheck 验证用户名和密码
func (c *LoginService) loginCheck(name string, password string) (bool, models.Admin, string) {
	db := tools.Db
	go func() {
		defer func() {
			if err := recover(); err != nil {
				tools.Logger.Error("登录日志 error", err)
			}
		}()
		tools.Log("CMS登录参数 用户名:" + name + ",密码:" + password)
	}()

	var admin models.Admin
	// 查询管理员，类型为1(OWNER)、2(ADMIN)、3(DEALER)、4(DEALER_SUB)、8(SHIPPER_ADMIN)
	db.Model(admin).Where("name = ?", name).
		Where("type in (1,2,3)").Where("state = 1").
		Where("deleted_at IS NULL").First(&admin)
	
	if admin.ID == 0 {
		db.Model(admin).Where("mobile = ?", name).
			Where("type in (1,2,3)").Where("state = 1").
			Where("deleted_at IS NULL").First(&admin)
		if admin.ID == 0 {
			return false, admin, "admin_is_not_active"
		}
	}

	attemptedHashValue := tools.PasswordToHash(password)
	if attemptedHashValue == admin.Password {
		return true, admin, ""
	}
	return false, admin, "admin_password_error"
} 