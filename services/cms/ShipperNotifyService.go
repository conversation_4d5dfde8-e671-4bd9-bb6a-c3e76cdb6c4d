package cms

import (
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/jobs"
	"mulazim-api/lang"
	"mulazim-api/models"
	shipmentModels "mulazim-api/models/shipment"
	"mulazim-api/permissions"
	resource "mulazim-api/resources/cms/shipment"
	"mulazim-api/scopes"
	"mulazim-api/tools"
)

type ShipperNotifyService struct {
	langUtil *lang.LangUtil
	language string
}

func NewShipperNotifyService(c *gin.Context) *ShipperNotifyService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	ShipperNotifyService := ShipperNotifyService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &ShipperNotifyService
}

// / Create
//
// @Description: 创建配送员通知
// @Author: Rixat
// @Time: 2023-11-07 03:20:24
// @receiver
// @param c *gin.Context
func (s ShipperNotifyService) Create(admin models.Admin, params resource.ShipperNotify) error {
	// 如果是管理员或者超级管理员  就批量创建并打开状态 并发送通知
	if (permissions.IsAdmin(admin) || permissions.IsOwner(admin)) && len(params.AreaIDs) > 0 {
		db := tools.GetDB()
		// 首先查询 area_ids and state 开着的或者重发的 改成 关闭
		db.Model(shipmentModels.ShipperNotify{}).
			Where("area_id IN ?",params.AreaIDs).
			Where("state IN ?",[]int{shipmentModels.ShipperNotifyStateSent,shipmentModels.ShipperNotifyStateResent}).
			Update("state", shipmentModels.ShipperNotifyStateClose)
		var notifys []shipmentModels.ShipperNotify
		for _, areaID := range params.AreaIDs {
			nowTime := carbon.Now(configs.AsiaShanghai).Carbon2Time()
			notify := shipmentModels.ShipperNotify{
				AreaID:    areaID,
				TitleUg:   params.TitleUg,
				TitleZh:   params.TitleZh,
				ContentUg: params.ContentUg,
				ContentZh: params.ContentZh,
				State:     shipmentModels.ShipperNotifyStateSent,
				CreatedAt: nowTime,
				UpdatedAt: nowTime,
				SendTime: 	&nowTime,
				SendCount: 1,
				CreatedBy: admin.ID,
			}
			var notifyCityId int
			db.Model(models.Area{}).Select("city_id").Where("id = ?", areaID).Scan(&notifyCityId)
			notify.CityID = notifyCityId
			notifys = append(notifys, notify)
		}
		err := db.Model(shipmentModels.ShipperNotify{}).Create(&notifys).Error
		if err != nil {
			tools.Logger.Error("FATAL 创建通知失败", err.Error())
			return errors.New("failed")
		}
		// 批量发送推送通知
		for _, notify := range notifys {
			// 获取该区域下的配送员
			var shippers []models.Admin
			tools.Db.Model(shippers).Where("admin_area_id=?", notify.AreaID).Where("type in (8,9)").Where("state = 1").Find(&shippers)
			for _, shipper := range shippers {
				pushData := jobs.PushData{
					UserType: constants.PushUserTypeShipper,
					UserId:   shipper.ID,
					Client:   models.PushDeviceClientShipper,
					PushContent: jobs.PushContent{
						TitleZh:   tools.SubString(notify.TitleUg, 40),
						TitleUg:   tools.SubString(notify.TitleZh, 40),
						ContentUg: tools.SubString(notify.ContentUg, 50),
						ContentZh: tools.SubString(notify.ContentZh, 50),
						Params: map[string]interface{}{
							"page": "home_notify",
						},
					},
					Sound: constants.SoundShipperNotification,
					ChannelType: constants.ChannelTypeShipperDefault,
					AdminId: shipper.ID,
				}
				job := jobs.NewPushJob()
				job.PushData(pushData)
			}

		}


		return nil
	}else {
		notify := shipmentModels.ShipperNotify{
			CityID:    params.CityID,
			AreaID:    params.AreaID,
			TitleUg:   params.TitleUg,
			TitleZh:   params.TitleZh,
			ContentUg: params.ContentUg,
			ContentZh: params.ContentZh,
			State:     params.State,
			CreatedAt: carbon.Now(configs.AsiaShanghai).Carbon2Time(),
			CreatedBy: admin.ID,
		}
		// 如果创建时开启状态，发送时间填充当前时间
		if permissions.IsAgent(admin) && params.CityID == 0 && params.AreaID == 0 {
			notify.CityID = admin.AdminCityID
			notify.AreaID = admin.AdminAreaID
		}
		err := tools.Db.Model(notify).Create(&notify).Error
		if err != nil {
			tools.Logger.Error("FATAL 创建通知失败", err.Error())
			return errors.New("failed")
		}
		return nil
	}
}

// List
//
// @Description: 获取配送员通知列表
// @Author: Rixat
// @Time: 2023-11-07 03:23:43
// @receiver
// @param c *gin.Context
func (s ShipperNotifyService) List(page int, limit int, cityId int, areaId int, kw string, state int, sort string) (map[string]interface{}, error) {
	var notifyList []shipmentModels.ShipperNotify
	// 添加查询条件
	query := tools.Db.Model(notifyList).Preload("Creator").Scopes(scopes.CityAreaStateDate(cityId, areaId, state, "", ""))
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Preload("City").Preload("Area").Scopes(scopes.Page(page, limit)).Order(sort).Find(&notifyList)
	}
	// 格式化列表内容
	items := make([]map[string]interface{}, 0)
	for _, value := range notifyList {
		items = append(items, map[string]interface{}{
			"id":         value.ID,
			"city_name":  tools.GetNameByLang(value.City, s.language),
			"area_name":  tools.GetNameByLang(value.Area, s.language),
			"title":      tools.GetNameByLangAndColumn(value, s.language, "Title"),
			"content":    tools.GetNameByLangAndColumn(value, s.language, "Content"),
			"send_time":  tools.TimeFormatYmdHis(value.SendTime),
			"send_count": value.SendCount,
			"state":      value.State,
			"created_at": tools.TimeFormatYmdHis(&value.UpdatedAt),
			"creator": value.Creator.Name,
		})
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": items,
	}
	return result, nil
}

// 删除
//
// @Description:
// @Author: Rixat
// @Time: 2023-11-07 03:35:31
// @receiver
// @param c *gin.Context
func (s ShipperNotifyService) Delete(ID int) error {
	var notify shipmentModels.ShipperNotify
	tools.Db.Model(notify).Where("id=?", ID).First(&notify)
	if notify.ID == 0 {
		return errors.New("not_found")
	}
	// 已发送的通知不能删除
	if notify.State != 1 {
		return errors.New("already_send_notify_unable_delete")
	}
	err := tools.Db.Delete(&shipmentModels.ShipperNotify{}, ID).Error
	if err != nil {
		tools.Logger.Error("FATAL 删除通知失败", err.Error())
		return errors.New("failed")
	}
	return nil
}

// ChangeState
//
// @Description: 修改状态
// @Author: Rixat
// @Time: 2023-11-07 03:29:05
// @receiver
// @param c *gin.Context
func (s ShipperNotifyService) ChangeState(ID int, state int) error {
	var notify shipmentModels.ShipperNotify
	tools.Db.Model(notify).Where("id=?", ID).First(&notify)
	if notify.ID == 0 {
		return errors.New("not_found")
	}
	if state > 1 {
		var openCount int64
		tools.Db.Model(notify).
			Where("area_id = ?", notify.AreaID).
			Where("state > 1").
			Where("id <> ?", ID).
			Count(&openCount)
		if openCount > 0 {
			return errors.New("this_time_opening_other_notify")
		}
		// 发送推送
		var shippers []models.Admin
		tools.Db.Model(shippers).Where("admin_area_id=?", notify.AreaID).Where("type in (8,9)").Where("state = 1").Find(&shippers)
		for _, shipper := range shippers {
			pushData := jobs.PushData{
				UserType: constants.PushUserTypeShipper,
				UserId:   shipper.ID,
				Client:   models.PushDeviceClientShipper,
				PushContent: jobs.PushContent{
					TitleZh:   tools.SubString(notify.TitleUg, 40),
					TitleUg:   tools.SubString(notify.TitleZh, 40),
					ContentUg: tools.SubString(notify.ContentUg, 50),
					ContentZh: tools.SubString(notify.ContentZh, 50),
					Params: map[string]interface{}{
						"page": "home_notify",
					},
				},
				Sound: constants.SoundShipperNotification,
				ChannelType: constants.ChannelTypeShipperDefault,
				AdminId: shipper.ID,
			}
			job := jobs.NewPushJob()
			job.PushData(pushData)
		}
	}
	sendTime := carbon.Now().Carbon2Time()
	notify.State = state
	notify.SendTime = &sendTime
	notify.UpdatedAt = carbon.Now().Carbon2Time()
	if state > 1 {
		notify.SendCount = notify.SendCount + 1
	}
	err := tools.Db.Save(notify).Error
	if err != nil {
		tools.Logger.Error("FATAL 通知修改状态错误" + err.Error())
		return errors.New("failed")
	}
	return nil
}
