package cms

import (
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/models/shipment"
	cmsRequests "mulazim-api/requests/cms/delivery"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type DeliveryFeeService struct {
	langUtil *lang.LangUtil
	language string
}

// NewDeliveryFeeService
//
//	@Description: 配送费配置服务
//	@Author: Salam
//	@Time: 2025-06-12 16:37:32
//	@Param ctx *gin.Context
//	@return *DeliveryConfigsService
func NewDeliveryFeeService(ctx *gin.Context) *DeliveryFeeService {
	l, _ := ctx.Get("lang_util")
	language, _ := ctx.Get("lang")
	langUtil := l.(lang.LangUtil)
	deliveryFeeSvc := DeliveryFeeService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &deliveryFeeSvc
}

// Create
//
//	@Description: 创建配范围配置
//	@Author: Salam
//	@Time: 2025-06-12 16:37:35
//	@Param ctx *gin.Context
//	@return *DeliveryConfigsService
func (svc *DeliveryFeeService) Create(
	params cmsRequests.CreateDeliveryConfigsRequest, admin models.Admin, deliveryAreaId int,
) ([]shipment.DeliveryFees, error) {
	if len(params.FeeConfigs) == 0 {
		return nil, nil
	}

	var (
		db      = tools.GetDB()
		feeList []shipment.DeliveryFees
	)

	for _, feeConfig := range params.FeeConfigs {
		fee := shipment.DeliveryFees{
			CityID:         params.CityId,
			AreaID:         params.AreaId,
			RestaurantID:   &params.RestaurantId,
			ParentID:       &params.ParentID,
			DeliveryAreaId: deliveryAreaId,
			Type:           params.Type,
			Option:         params.Option,
			Default:        feeConfig.Default,
			Stages:         feeConfig.Stages,
			BeginTime:      feeConfig.BeginTime,
			EndTime:        feeConfig.EndTime,
			StartFee:       feeConfig.StartFee,
			UpdatedAdminID: admin.ID,
		}

		// 从 stages 获取最小配送费
		minFee := uint(0)
		for _, stage := range feeConfig.Stages {
			if minFee == 0 {
				minFee = stage.StartFee
			}

			if stage.StartFee < minFee {
				minFee = stage.StartFee
			}
		}
		fee.StageFeeMin = minFee

		// 创建
		if err := db.Create(&fee).Error; err != nil {
			return nil, err
		}

		feeList = append(feeList, fee)
	}
	return feeList, nil
}

// Update
//
//	@Description: 更新配范围配置
//	@Author: Salam
//	@Time: 2025-06-30 12:43:35
//	@Param ctx *gin.Context
//	@return *DeliveryConfigsService
func (svc *DeliveryFeeService) Update(
	params cmsRequests.UpdateDeliveryConfigsRequest, admin models.Admin,
) ([]shipment.DeliveryFees, error) {
	if len(params.FeeConfigs) == 0 {
		return nil, nil
	}

	var (
		db      = tools.GetDB()
		feeList []shipment.DeliveryFees
	)

	// 写入更新前的数据日志
	deliveryFeeIds := make([]int, 0)
	for _, feeConfig := range params.FeeConfigs {
		if feeConfig.ID > 0 {
			deliveryFeeIds = append(deliveryFeeIds, feeConfig.ID)
		}
	}
	existingFeeList, err := svc.GetDeliveryFeeListByIds(deliveryFeeIds)
	if err == nil && len(existingFeeList) > 0 {
		svc.RecordDeliveryFeeListLog(existingFeeList)
	}

	for _, feeConfig := range params.FeeConfigs {
		// 从 stages 获取最小配送费
		minFee := uint(0)
		for _, stage := range feeConfig.Stages {
			if minFee == 0 {
				minFee = stage.StartFee
			}

			if stage.StartFee < minFee {
				minFee = stage.StartFee
			}
		}

		// 组装结构
		fee := shipment.DeliveryFees{
			ID:             feeConfig.ID,
			DeliveryAreaId: params.DeliveryAreaId,
			Option:         params.Option,
			Default:        feeConfig.Default,
			Stages:         feeConfig.Stages,
			StageFeeMin:    minFee,
			BeginTime:      feeConfig.BeginTime,
			EndTime:        feeConfig.EndTime,
			StartFee:       feeConfig.StartFee,
			UpdatedAdminID: admin.ID,
			UpdatedAt:      time.Now(),
		}

		if fee.ID == 0 {
			// 创建 - feeConfig.ID 为空的数据
			if err := db.Create(&fee).Error; err != nil {
				return nil, err
			}
		} else {
			// 更新
			if err := db.Model(&fee).
				Where("delivery_area_id = ?", fee.DeliveryAreaId).
				Where("id = ?", fee.ID).
				Updates(map[string]any{
					"parent_id":        fee.ParentID,
					"option":           fee.Option,
					"default":          fee.Default,
					"stages":           fee.Stages,
					"stage_fee_min":    fee.StageFeeMin,
					"begin_time":       fee.BeginTime,
					"end_time":         fee.EndTime,
					"start_fee":        fee.StartFee,
					"updated_admin_id": fee.UpdatedAdminID,
					"updated_at":       fee.UpdatedAt,
				}).
				Error; err != nil {
				return nil, err
			}
		}

		feeList = append(feeList, fee)
	}
	return feeList, nil
}

// UpdateMapByRestaurantIds
//
//	@Description: 根据餐厅编号更新配送费配置
//	@Author: Salam
//	@Time: 2025-07-07 11:25:35
//	@Param updateMap map[string]any
//	@Param params cmsRequests.UpdateRestaurantDeliveryConfigs
//	@return error
func (svc *DeliveryFeeService) UpdateMapByRestaurantIds(
	updateMap map[string]any, params cmsRequests.UpdateRestaurantDeliveryConfigs) error {
	var (
		db  = tools.GetDB()
		fee = shipment.DeliveryFees{}
	)

	// 写入更新前的数据日志
	deliveryFeeList, err := svc.GetDeliveryFeeListByRestaurantIds(params.ParentID, params.RestaurantIds)
	if err == nil && len(deliveryFeeList) > 0 {
		svc.RecordDeliveryFeeListLog(deliveryFeeList)
	}

	// 更新
	if err := db.Model(&fee).
		Where("city_id = ?", params.CityId).
		Where("area_id = ?", params.AreaId).
		// 不存在的 restaurant_id 可以不创建，因为它原本就是跟随区域配置的
		Where("parent_id = ?", params.ParentID).
		Where("restaurant_id IN (?)", params.RestaurantIds).
		Updates(updateMap).
		Error; err != nil {
		return err
	}

	// 更新配送范围状态
	db.Model(&shipment.DeliveryAreas{}).
		Where("parent_id = ? and restaurant_id in ?", params.ParentID, params.RestaurantIds).
		Update("fee_state", 2)

	return nil
}

// DeleteByRestaurantId
//
//	@Description: 根据餐厅ID删除配送费配置
//	@Author: Salam
//	@Time: 2025-07-02 16:19:00
//	@Param restaurantId int
//	@return error
func (svc *DeliveryFeeService) DeleteByRestaurantId(parentId, restaurantId int) error {
	db := tools.GetDB()

	// 写入更新前的数据日志
	deliveryFeeList, err := svc.GetDeliveryFeeListByRestaurantIds(parentId, []int{restaurantId})
	if err == nil && len(deliveryFeeList) > 0 {
		svc.RecordDeliveryFeeListLog(deliveryFeeList)
	}

	// 删除
	result := db.Model(&shipment.DeliveryFees{}).
		Where("parent_id = ?", parentId).
		Where("restaurant_id = ?", restaurantId).
		Where("type = ?", shipment.DeliveryFeesTypeRestaurant).
		Delete(&shipment.DeliveryFees{})
	if result.Error != nil {
		return result.Error
	}

	// 留下删除的关键信息
	if result.RowsAffected > 0 {
		tools.Logger.Info(fmt.Sprintf("删除了餐厅 %d 的配送费配置，共 %d 条", restaurantId, result.RowsAffected))
	}

	return nil
}

// GetDeliveryFeeListByIds
//
//	@Description: 根据编号列表获取配送费配置列表
//	@Author: Salam
//	@Time: 2025-07-08 17:02:32
//	@Param id int
//	@return *shipmentModels.DeliveryFees
//	@return error
func (svc *DeliveryFeeService) GetDeliveryFeeListByIds(ids []int) ([]shipment.DeliveryFees, error) {
	var (
		db  = tools.GetDB()
		res = make([]shipment.DeliveryFees, 0)
	)

	if err := db.Model(&res).
		Where("id IN (?)", ids).
		Find(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}

// GetDeliveryFeeListByRestaurantIds
//
//	@Description: 根据餐厅编号列表获取配送费配置列表
//	@Author: Salam
//	@Time: 2025-07-08 17:58:32
//	@Param id int
//	@return *shipmentModels.DeliveryFees
//	@return error
func (svc *DeliveryFeeService) GetDeliveryFeeListByRestaurantIds(parentId int, ids []int) ([]shipment.DeliveryFees, error) {
	var (
		db  = tools.GetDB()
		res = make([]shipment.DeliveryFees, 0)
	)

	if err := db.Model(&res).
		Where("parent_id = ?", parentId).
		Where("restaurant_id IN (?)", ids).
		Find(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}

// RecordDeliveryFeeListLog
//
//	@Description: 批量记录配送费配置数组日志
//	@Author: Salam
//	@Time: 2025-06-13 10:51:32
func (svc *DeliveryFeeService) RecordDeliveryFeeListLog(deliveryFees []shipment.DeliveryFees) {
	if len(deliveryFees) == 0 {
		return
	}

	// 构建批量插入的SQL语句
	query := `
		INSERT INTO t_delivery_fees_log (
			delivery_fees_id, city_id, area_id, restaurant_id,
			parent_id, delivery_area_id,
			type, ` + "`option`" + `, ` + "`default`" + `, stages,
			stage_fee_min, begin_time, end_time, start_fee,
			updated_admin_id, valid_begin_time, valid_end_time
		) VALUES 
	`

	// 构建参数切片
	var (
		args         []any
		valueStrings []string
		currentTime  = time.Now()
	)

	for _, dlFe := range deliveryFees {
		valueStrings = append(valueStrings, `(
			?, ?, ?, ?,
			?, ?,
			?, ?, ?, ?,
			?, ?, ?, ?,
			?, ?, ?
		)`)
		args = append(args,
			dlFe.ID, dlFe.CityID, dlFe.AreaID, dlFe.RestaurantID,
			dlFe.ParentID, dlFe.DeliveryAreaId,
			dlFe.Type, dlFe.Option, dlFe.Default, dlFe.Stages,
			dlFe.StageFeeMin, dlFe.BeginTime, dlFe.EndTime, dlFe.StartFee,
			dlFe.UpdatedAdminID, dlFe.UpdatedAt, currentTime,
		)
	}

	// 拼接完整的SQL语句
	query += strings.Join(valueStrings, ",")

	// 执行批量插入
	err := tools.Db.Exec(query, args...).Error
	if err != nil {
		tools.Logger.Error("Failed to create delivery fees log batch:", err)
		return
	}
}
