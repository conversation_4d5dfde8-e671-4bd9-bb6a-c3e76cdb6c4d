package cms

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
)

type LotteryChanceService struct {
	langUtil *lang.LangUtil
	language string
}

func NewLotteryChanceService(ctx *gin.Context) *LotteryChanceService {
	l, _ := ctx.Get("lang_util")
	language, _ := ctx.Get("lang")
	langUtil := l.(lang.LangUtil)
	lotteryChanceService := LotteryChanceService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &lotteryChanceService
}

// GetValidListByActivityID 获取
func (svc *LotteryChanceService) GetValidListByActivityID(
	lotActID, cityID, areaID int) ([]models.LotteryChance, error) {

	db := tools.GetDB()
	query := db.Model(&models.LotteryChance{}).
		Where("t_lottery_chance.lottery_activity_id = ?", lotActID).
		Where("t_lottery_chance.prize_open_state = ?",models.LotteryChancePrizeOpenStateDraw). // 已抽奖，已提交地址
		//Where("t_lottery_chance.prize_open_state = ?", models.LotteryChancePrizeOpenStateDraw). // 已中奖
		Where("t_lottery_chance.type = ?", models.LotteryChanceTypeActivity)                    // 活动类型

	if cityID > 0 {
		query.Where("t_lottery_chance.city_id = ?", cityID)
	}
	if areaID > 0 {
		query.Where("t_lottery_chance.area_id = ?", areaID)
	}

	query.
		Preload("LotteryPrize"). // , "t_lottery_prize.type = ?", models.LotteryPrizeTypeActivity). // 奖品信息 - 是否需要去掉无效的奖品？
		Preload("User").                                                                      // 用户信息
		Preload("Order").                                                                     // 订单信息
		Preload("OrderToday"). // 当日订单信息
		Preload("City").                                                                      // 城市信息
		Preload("Area").                                                                      // 区域信息
		Preload("UserBuilding") // 用户的收奖品的地址

	var result []models.LotteryChance
	if err := query.Find(&result).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return result, nil
}
