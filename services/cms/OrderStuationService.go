package cms

import (
	"errors"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/scopes"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type OrderSituationService struct {
	langUtil *lang.LangUtil
	language string
}

func NewOrderSituationService(c *gin.Context) *OrderSituationService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	OrderSituationService := OrderSituationService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &OrderSituationService
}

// Detail
//
// @Description: 特殊天气详情
// @Author: Rixat
// @Time: 2023-11-06 09:59:57
// @receiver
// @param c *gin.Context
func (s OrderSituationService) Detail(ID int) (map[string]interface{}, error) {
	var situation models.OrderSituation
	tools.Db.Model(situation).Where("id=?", ID).First(&situation)
	if situation.ID == 0 {
		return nil, errors.New("not_found")
	}
	// 返回结果
	result := map[string]interface{}{
		"id":           situation.ID,
		"shipper_name": situation.ShipperName,
		"order_id":     situation.OrderID,
		"order_no":     situation.OrderNo,
		"content":      situation.Content,
		"images":       tools.GetImageURLs(situation.Image),
		"created_at":   tools.TimeFormatYmdHis(&situation.CreatedAt),
	}
	return result, nil
}

// List
//
// @Description: 上报情况列表
// @Author: Rixat
// @Time: 2023-11-06 10:00:01
// @receiver
// @param c *gin.Context
func (s OrderSituationService) List(page int, limit int, cityId int, areaId int, shipperID int, startDate string, endDate string, sort string) map[string]interface{} {

	var situationList []models.OrderSituation
	query := tools.Db.Model(situationList).Scopes(scopes.CityAreaDate(cityId, areaId, startDate, endDate))
	if shipperID > 0 {
		query.Where("shipper_id=?", shipperID)
	}
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Preload("Shipper").Preload("Restaurant").
			Scopes(scopes.Page(page, limit)).
			Order(sort).
			Find(&situationList)
	}
	// 格式化列表内容
	items := make([]map[string]interface{}, 0)
	for _, value := range situationList {
		items = append(items, map[string]interface{}{
			"id":              value.ID,
			"avatar":          tools.CdnUrl(value.Shipper.Avatar),
			"mobile":          value.Shipper.Mobile,
			"shipper_name":    value.ShipperName,
			"restaurant_name": tools.GetNameByLang(value.Restaurant, s.language),
			"order_id":        value.OrderID,
			"order_no":        value.OrderNo,
			"content":         value.Content,
			"images":          tools.GetImageURLs(value.Image),
			"created_at":      tools.TimeFormatYmdHis(&value.CreatedAt),
		})
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": items,
	}
	return result
}
