package cms

import (
	"errors"
	"fmt"
	"mulazim-api/configs"
	errors2 "mulazim-api/errors"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/resources/cms"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type MiniGameActivityService struct {
	langUtil *lang.LangUtil
	language string
}

// ActivityList
//
//	@Author: YaKupJan
//	@Date: 2024-11-13 15:35:07
//	@Description: 获取游戏活动列表
//	@receiver m
//	@param pagination
//	@param search
//	@return []models.MiniGameActivity
//	@return int64
func (m MiniGameActivityService) ActivityList(pagination tools.Pagination, search string) ([]models.MiniGameActivity, int64) {
	db := tools.GetDB()
	var activities []models.MiniGameActivity
	var total int64

	query := db.Model(&models.MiniGameActivity{}).Order("id DESC")

	// 如果有搜索条件则添加查询条件
	if search != "" {
		query = query.Where("name_ug LIKE ? OR name_zh LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// 统计总记录数
	query.Count(&total)

	// 分页查询活动数据
	query.Scopes(scopes.Page(pagination.Page, pagination.Limit)).Find(&activities)

	return activities, total
}

// ToggleActivityState
//
//	@Author: YaKupJan
//	@Date: 2024-11-13 15:53:25
//	@Description: 切换游戏活动状态
//	@receiver m
//	@param id
//	@return error
func (m MiniGameActivityService) ToggleActivityState(id string) error {
	db := tools.GetDB()
	var activity models.MiniGameActivity
	if err := db.Model(&activity).Where("id = ?", id).First(&activity).Error; err != nil {
		return err
	}
	// 切换状态
	// 1 - 1 = 0     1 - 0 = 1
	activity.State = 1 - activity.State
	if err := db.Save(&activity).Error; err != nil {
		return err
	}
	return nil
}

// CreateActivity
//
//	@Author: YaKupJan
//	@Date: 2024-11-13 16:28:46
//	@Description: 创建新的游戏活动
//	@receiver m
//	@param params
//	@return error
func (m MiniGameActivityService) CreateActivity(params cms.CreateMiniGameActivityParams) error {
	db := tools.GetDB()
	// 检查时间关系，确保开始时间早于结束时间
	if params.StartTime.Time.After(params.EndTime.Time) {
		return errors.New(m.langUtil.T("start_time_must_before_end_time"))
	}

	if params.StartUseTime.Time.After(params.EndUseTime.Time) {
		return errors.New(m.langUtil.T("start_time_must_before_end_time"))
	}
	// 时间冲突检查：检查同类型活动是否存在时间交叉
	conflictingActivities := []models.MiniGameActivity{}
	if err := db.Where("type = ? AND ((start_time BETWEEN ? AND ?) OR (end_time BETWEEN ? AND ?) OR (? BETWEEN start_time AND end_time) OR (? BETWEEN start_time AND end_time))",
		params.Type, params.StartTime.Time, params.EndTime.Time, params.StartTime.Time, params.EndTime.Time,
		params.StartTime.Time, params.EndTime.Time).Find(&conflictingActivities).Error; err != nil {
		return err
	}

	if len(conflictingActivities) > 0 {
		return errors.New(m.langUtil.T("conflict_marketing_found"))
	}
	// 赋值
	activity := models.MiniGameActivity{
		NameUg:          params.NameUg,
		NameZh:          params.NameZh,
		RuleUg:          params.RuleUg,
		RuleZh:          params.RuleZh,
		GameRuleUg:      params.GameRuleUg,
		GameRuleZh:      params.GameRuleZh,
		UseAmountRuleUg: params.UseAmountRuleUg,
		UseAmountRuleZh: params.UseAmountRuleZh,
		StartTime:       params.StartTime.Time,
		EndTime:         params.EndTime.Time,
		StartUseTime:    params.StartUseTime.Time,
		EndUseTime:      params.EndUseTime.Time,
		State:           params.State,
		Type:            params.Type,
	}

	if err := db.Create(&activity).Error; err != nil {
		return err
	}

	return nil
}

// UpdateActivity
//
//  @Author: YaKupJan
//  @Date: 2024-11-13 17:07:13
//  @Description: 修改游戏活动
//  @receiver m
//  @param params
//  @return error
func (m MiniGameActivityService) UpdateActivity(params cms.UpdateMiniGameActivityParams) error {
	// 获取数据库实例
	db := tools.GetDB()
	// 确保开始时间早于结束时间
	if params.StartTime.Time.After(params.EndTime.Time) {
		return errors.New(m.langUtil.T("start_time_must_before_end_time"))
	}
	// 确保开始时间早于结束时间
	if params.StartUseTime.Time.After(params.EndUseTime.Time) {
		return errors.New(m.langUtil.T("start_time_must_before_end_time"))
	}
	// 查找活动记录
	var activity models.MiniGameActivity
	if err := db.First(&activity, params.ID).Error; err != nil {
		return err // 返回错误，如果未找到该活动
	}
	// 结束的活动不能修改
	if activity.EndTime.Before(time.Now()) {
		return errors.New(m.langUtil.T("end_active_cannot_edit"))
	}
	// 无法修改正在进行的活动的开始时间
	if activity.StartTime.Before(time.Now()) && !params.StartTime.Equal(activity.StartTime) {
		return errors.New("cannot modify start time of an ongoing activity")
	}

	// 时间冲突检查：检查同类型活动是否存在时间交叉
	conflictingActivities := []models.MiniGameActivity{}
	if err := db.Where("id != ?  AND type = ? AND ((start_time BETWEEN ? AND ?) OR (end_time BETWEEN ? AND ?) OR (? BETWEEN start_time AND end_time) OR (? BETWEEN start_time AND end_time))",
		params.ID,params.Type, params.StartTime.Time, params.EndTime.Time, params.StartTime.Time, params.EndTime.Time,
		params.StartTime.Time, params.EndTime.Time).Find(&conflictingActivities).Error; err != nil {
		return err
	}

	if len(conflictingActivities) > 0 {
		return errors.New(m.langUtil.T("conflict_marketing_found"))
	}

	// 更新活动字段
	activity.NameUg = params.NameUg
	activity.NameZh = params.NameZh
	activity.RuleUg = params.RuleUg
	activity.RuleZh = params.RuleZh
	activity.GameRuleUg = params.GameRuleUg
	activity.GameRuleZh = params.GameRuleZh
	activity.UseAmountRuleUg = params.UseAmountRuleUg
	activity.UseAmountRuleZh = params.UseAmountRuleZh
	activity.StartTime = params.StartTime.Time
	activity.EndTime = params.EndTime.Time
	activity.StartUseTime = params.StartUseTime.Time
	activity.EndUseTime = params.EndUseTime.Time
	activity.State = params.State
	activity.Type = params.Type

	// 保存更新后的活动记录
	if err := db.Save(&activity).Error; err != nil {
		return err // 如果保存失败，返回错误
	}

	return nil
}

// DeleteActivity
//
//  @Author: YaKupJan
//  @Date: 2024-11-13 17:22:34
//  @Description: 删除游戏活动
//  @receiver m
//  @param id
//  @return error
func (m MiniGameActivityService) DeleteActivity(activityID int) error {
	db := tools.GetDB()

	// 查找活动记录
	var activity models.MiniGameActivity
	if err := db.First(&activity, activityID).Error; err != nil {
		return errors.New(m.langUtil.T("marketing_not_found"))
	}

	// 判断活动状态：如果活动已开始或已结束，不允许删除
	if activity.StartTime.Before(time.Now()) {
		return errors.New(m.langUtil.T("activity_already_started_enable_delete"))
	}

	// 检查活动的参加记录，若存在则禁止删除
	var activityLogCount int64
	if err := db.Model(&models.MiniGameUserLog{}).
		Where("activity_id = ?", activityID).
		Count(&activityLogCount).Error; err != nil {
		return err
	}
	if activityLogCount > 0 {
		return errors.New(m.langUtil.T("already_used_unable_delete"))
	}

	if err := db.Delete(&activity).Error; err != nil {
		return err
	}

	return nil
}

// ActivityDetails
//
//  @Author: YaKupJan
//  @Date: 2024-11-13 18:06:36
//  @Description: 游戏活动详细
//  @receiver m
//  @param activityID
//  @return *models.MiniGameActivity
//  @return error
func (m MiniGameActivityService) ActivityDetails(activityID int) (*models.MiniGameActivity,error) {
	db := tools.GetDB()

	// 查找活动记录，确保活动存在
	var activity models.MiniGameActivity
	if err := db.First(&activity, activityID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(m.langUtil.T("not_found"))
		}
		return nil, err
	}

	return &activity, nil
}

func (m MiniGameActivityService) ActivityStatistics(pagination tools.Pagination, activityID int, cityID int, areaID int, mobile string, sortColumns string) ([]models.MiniGameActivityUserForCmsStatics, int64, int64, int64, int64, int64) {
	db := tools.ReadDb1
	userId := 0
	if mobile != "" && tools.VerifyMobileFormat(mobile) {
		var user = models.User{}
		db.Model(models.User{}).Where("mobile = ?",mobile).First(&user)
		userId = user.ID
	}
	var statisticData []models.MiniGameActivityUserForCmsStatics
	//查询列表
	query := db.Model(models.MiniGameActivityUserForCmsStatics{}).
		Preload("User").
		Preload("Area").
		Select(`*,
(select count(1) from t_mini_game_activity_user_log tmgaul where tmgaul.type = 1 and tmgaul.activity_id = ? and tmgaul.user_id = t_mini_game_activity_user.user_id) as game_count,
        (SELECT
                 -1 * sum( IF ( tmgaul.type = 2, tmgaul.amount, 0 ) ) AS spend_amount
         FROM
             t_mini_game_activity_user_log tmgaul
                 LEFT JOIN t_order_today tod ON tmgaul.order_id = tod.id
                 LEFT JOIN t_order o ON tmgaul.order_id = o.id
         WHERE
                 tmgaul.type = 2 and (o.state = 7 or tod.state = 7) and tmgaul.user_id = t_mini_game_activity_user.user_id and tmgaul.activity_id = ? ) as spend_amount,
        (SELECT
                 SUM( IF ( o.state = 7, 1, 0 ) ) + SUM( IF ( tod.state = 7, 1, 0 ) ) AS take_order_count
         FROM
             t_mini_game_activity_user_log tmgaul
                 LEFT JOIN t_order_today tod ON tmgaul.order_id = tod.id
                 LEFT JOIN t_order o ON tmgaul.order_id = o.id
		WHERE
                 tmgaul.type = 2 and tmgaul.user_id = t_mini_game_activity_user.user_id and tmgaul.activity_id = ? ) as take_order_count
`,activityID,activityID,activityID).
		Where("activity_id = ?",activityID)
	if cityID!=0 {
		query = query.Where("city_id = ?",cityID)
	}
	if areaID!=0 {
		query = query.Where("area_id = ?",areaID)
	}
	if userId != 0 {
		query = query.Where("user_id = ?",userId)
	}
	if sortColumns != ""{
		query = query.Order(sortColumns)
	}
	// 参加活动的总共人数
	var allAttendCount int64
	query.Count(&allAttendCount)
	query.Scopes(scopes.Page(pagination.Page,pagination.Limit)).Find(&statisticData)


	// 发放的总金额
	var sendAmount int64
	queryAllAmount := db.Model(&models.MiniGameActivityUserForCmsStatics{}).Where("activity_id = ?",activityID)
	if areaID != 0 {
		queryAllAmount = queryAllAmount.Where("area_id = ?", areaID)
	}
	if cityID != 0 {
		queryAllAmount = queryAllAmount.Where("city_id = ?", cityID)
	}
	if userId != 0 {
		queryAllAmount = queryAllAmount.Where("user_id = ?",userId)
	}
	queryAllAmount.Select("COALESCE(SUM(all_amount), 0)").Scan(&sendAmount)
	// 使用的金额
	var usedAmount int64
	queryUsedAmount := db.Model(&models.MiniGameUserLog{}).Where("t_mini_game_activity_user_log.activity_id = ?",activityID).Where("t_mini_game_activity_user_log.type = ?",models.MiniGameActivityUserLogTypeOutcome)
	if areaID != 0 {
		queryUsedAmount = queryUsedAmount.Where("t_mini_game_activity_user.area_id = ?", areaID)
	}
	if cityID != 0 {
		queryUsedAmount = queryUsedAmount.Where("t_mini_game_activity_user.city_id = ?", cityID)
	}
	if userId != 0 {
		queryUsedAmount = queryUsedAmount.Where("t_mini_game_activity_user.user_id = ?",userId)
	}
	queryUsedAmount.
		Joins("LEFT JOIN t_order_today tod ON t_mini_game_activity_user_log.order_id = tod.id").
		Joins("LEFT JOIN t_order o ON t_mini_game_activity_user_log.order_id = o.id").
		Joins("LEFT JOIN t_mini_game_activity_user  ON t_mini_game_activity_user.user_id = t_mini_game_activity_user_log.user_id").
		Where("(o.state = 7 or tod.state = 7)").
	Select(`ifnull((-1 * sum( t_mini_game_activity_user_log.amount)),0 )`).Scan(&usedAmount)

	// 游戏玩的次数
	var allPlayCount int64
	queryAllPlayCount := db.Model(&models.MiniGameUserLog{}).Where("activity_id = ?",activityID).Where("type = ?",models.MiniGameActivityUserLogTypeIncome)
	if areaID != 0 {
		queryAllPlayCount = queryAllPlayCount.Where("area_id = ?", areaID)
	}
	if cityID != 0 {
		queryAllPlayCount = queryAllPlayCount.Where("city_id = ?", cityID)
	}
	if userId != 0 {
		queryAllPlayCount = queryAllPlayCount.Where("user_id = ?",userId)
	}
	queryAllPlayCount.Select(`count(1)`).Scan(&allPlayCount)

	// 下单数量
	var allTakeOrderCount int64
	queryAllTakeOrderCount := db.Model(&models.MiniGameUserLog{}).Where("activity_id = ?",activityID).Where("type = ?",models.MiniGameActivityUserLogTypeOutcome)
	if areaID != 0 {
		queryAllTakeOrderCount = queryAllTakeOrderCount.Where("t_mini_game_activity_user_log.area_id = ?", areaID)
	}
	if cityID != 0 {
		queryAllTakeOrderCount = queryAllTakeOrderCount.Where("t_mini_game_activity_user_log.city_id = ?", cityID)
	}
	if userId != 0 {
		queryAllTakeOrderCount = queryAllTakeOrderCount.Where("t_mini_game_activity_user_log.user_id = ?",userId)
	}
	queryAllTakeOrderCount.Select(`IFNULL(SUM(IF(o.state = 7, 1, 0)), 0) + IFNULL(SUM(IF(tod.state = 7, 1, 0)), 0)`).
		Joins("LEFT JOIN t_order_today tod ON t_mini_game_activity_user_log.order_id = tod.id").
		Joins("LEFT JOIN t_order o ON t_mini_game_activity_user_log.order_id = o.id").
		Scan(&allTakeOrderCount)
	return statisticData,allAttendCount,sendAmount,usedAmount,allPlayCount,allTakeOrderCount
}

// CreateMiniGame
//
//  @Author: YaKupJan
//  @Date: 2024-11-15 16:26:30
//  @Description: 创建游戏
//  @receiver m
//  @param nameZh
//  @param nameUg
//  @param remarkZh
//  @param remarkUg
//  @param image
//  @param gameCategoryId
func (m MiniGameActivityService) CreateMiniGame(nameZh string, nameUg string, remarkZh string, remarkUg string, image string, gameCategoryId int) {
	db := tools.GetDB()

	// 创建 MiniGame 实例
	miniGame := models.MiniGame{
		NameZh:        nameZh,
		NameUg:        nameUg,
		RemarkZh:      remarkZh,
		RemarkUg:      remarkUg,
		Image:         image,
		GameCategoryId: gameCategoryId,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// 插入数据库
	if err := db.Model(&models.MiniGame{}).Create(&miniGame).Error; err != nil {
		customError := errors2.CustomError{Msg: m.langUtil.T("create_fail")}
		panic(customError)
	}
}

func (m MiniGameActivityService) UpdateMiniGame(id int, nameZh string, nameUg string, remarkZh string, remarkUg string, image string, gameCategoryId int) {
	db := tools.GetDB()

	// 更新 MiniGame 实例
	if err := db.Model(&models.MiniGame{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"name_zh":          nameZh,
			"name_ug":          nameUg,
			"remark_zh":        remarkZh,
			"remark_ug":        remarkUg,
			"image":            image,
			"game_category_id": gameCategoryId,
			"updated_at":       time.Now(),
		}).Error; err != nil {
		customError := errors2.CustomError{Msg: m.langUtil.T("update_fail")}
		panic(customError)
	}
}

// DeleteMiniGame
//
//  @Author: YaKupJan
//  @Date: 2024-11-15 17:31:11
//  @Description: 删除游戏
//  @receiver m
//  @param id
func (m MiniGameActivityService) DeleteMiniGame(id int) {
	db := tools.GetDB()
	var miniGameActivity models.MiniGameActivity
	db.Model(models.MiniGameActivity{}).Where("type = ?", id).First(&miniGameActivity)
	if miniGameActivity.ID != 0 {
		customError := errors2.CustomError{
			Msg: m.langUtil.T("already_used_unable_delete"),
		}
		panic(customError)
	}
	// 删除
	if err := db.Delete(&models.MiniGame{}, id).Error; err != nil {
		customError := errors2.CustomError{Msg: m.langUtil.T("delete_fail")}
		panic(customError)
	}
}

// ListMiniGame
//
//  @Author: YaKupJan
//  @Date: 2024-11-15 18:40:27
//  @Description: 游戏列表
//  @receiver m
//  @param pagination
//  @return []models.MiniGame
//  @return int64
func (m MiniGameActivityService) ListMiniGame(pagination tools.Pagination) ([]models.MiniGame, int64) {
	db := tools.GetDB()
	var miniGames []models.MiniGame
	query := db.Model(&models.MiniGame{}).Preload("MiniGameCategory")

	// 分页
	var total int64
	query.Count(&total)

	err := query.Scopes(scopes.Page(pagination.Page, pagination.Limit)).Find(&miniGames).Error
	if err != nil {
		customError := errors2.CustomError{
			Msg: "获取游戏列表失败",
		}
		panic(customError)

	}

	return miniGames,total
}

// UseDiscountStatistics
//
//  @Author: YaKupJan
//  @Date: 2024-11-20 11:22:42
//  @Description: 游戏优惠使用统计
//  @receiver m
//  @param pagination
//  @param activityID
//  @param cityID
//  @param areaID
//  @param restaurantID
//  @param startTime
//  @param endTime
//  @param sortColumns
//  @return []models.MiniGameUserLog
//  @return int64
//  @return map[string]interface{}
func (m MiniGameActivityService) UseDiscountStatistics(pagination tools.Pagination, activityID int, cityID int, areaID int, restaurantID int, startTime string, endTime string, sortColumns string) ([]models.MiniGameUserLog, cms.UseDiscountStatisticsByNumber) {
	db := tools.ReadDb1
	// 统计上面的数量
	// 发放的总金额
	var sendAmount int64
	queryAllAmount := db.Model(&models.MiniGameActivityUserForCmsStatics{}).Where("activity_id = ?",activityID).Select("COALESCE(SUM(all_amount), 0) as all_amount")

	// 使用的金额
	var usedAmount int64
	queryUsedAmount := db.Model(&models.MiniGameUserLog{}).Where("t_mini_game_activity_user_log.activity_id = ?",activityID).
		Where("t_mini_game_activity_user_log.type = ?",models.MiniGameActivityUserLogTypeOutcome).
		Joins("LEFT JOIN t_order_today tod ON t_mini_game_activity_user_log.order_id = tod.id").
		Joins("LEFT JOIN t_order o ON t_mini_game_activity_user_log.order_id = o.id").
		Joins("LEFT JOIN t_mini_game_activity_user ON t_mini_game_activity_user.user_id = t_mini_game_activity_user_log.user_id").
		Where("(o.state = 7 or tod.state = 7)").
		Select("-1 * COALESCE(SUM(IFNULL(t_mini_game_activity_user_log.amount, 0)), 0)")


	// 下单数量
	var allTakeOrderCount int64
	queryAllTakeOrderCount := db.Model(&models.MiniGameUserLog{}).Where("activity_id = ?",activityID).
		Where("type = ?",models.MiniGameActivityUserLogTypeOutcome).
		Select(`COALESCE(SUM( IF ( o.state = 7, 1, 0 ) ),0) + COALESCE(SUM( IF ( tod.state = 7, 1, 0 ) ),0)`).
		Joins("LEFT JOIN t_order_today tod ON t_mini_game_activity_user_log.order_id = tod.id").
		Joins("LEFT JOIN t_order o ON t_mini_game_activity_user_log.order_id = o.id")

	// 下单的总金额
	var orderAmount int64
	queryOrderAmount := db.Model(&models.MiniGameUserLog{}).Where("activity_id = ?",activityID).
		Where("type = ?",models.MiniGameActivityUserLogTypeOutcome).
		Joins("LEFT JOIN t_order_today tod ON t_mini_game_activity_user_log.order_id = tod.id").
		Joins("LEFT JOIN t_order o ON t_mini_game_activity_user_log.order_id = o.id").
		Where("o.state = 7 or tod.state = 7").
		Select("COALESCE(SUM( IF ( tod.id IS NULL, o.actual_paid, tod.actual_paid ) ),0)")


	// 查询列表数据
var data  []models.MiniGameUserLog
	query := db.Model(models.MiniGameUserLog{}).
		Preload("Area").
		Preload("TOrder.Restaurant").
		Preload("TOrderToday.Restaurant").
		Where("activity_id = ?", activityID).
		Where("type = ?", models.MiniGameActivityUserLogTypeOutcome).
		Joins("LEFT JOIN t_order_today tod ON t_mini_game_activity_user_log.order_id = tod.id").
		Joins("LEFT JOIN t_order o ON t_mini_game_activity_user_log.order_id = o.id").
		Where("o.state = 7 or tod.state = 7")

	sortableColumns := map[string]string{
		"created_at":    "t_mini_game_activity_user_log.created_at",
		"discount":      "t_mini_game_activity_user_log.amount",
		"order_percent": "t_mini_game_activity_user_log.order_percent",
	}

	if sortColumns != "" {
		columns := strings.Split(sortColumns, ",") // 按逗号分割多个排序字段
		validSortColumns := []string{}

		for _, column := range columns {
			parts := strings.Fields(column) // 分割字段和排序规则，例如 "created_at asc"
			if len(parts) == 2 {
				columnName, order := parts[0], strings.ToLower(parts[1])
				if field, ok := sortableColumns[columnName]; ok && (order == "asc" || order == "desc") {
					validSortColumns = append(validSortColumns, fmt.Sprintf("%s %s", field, order))
				}
			}
		}

		// 如果有合法的排序字段，应用到查询中
		if len(validSortColumns) > 0 {
			query = query.Order(strings.Join(validSortColumns, ","))
		}
	}
	if restaurantID !=0 {
		query = query.Where("o.store_id = ? or tod.store_id = ?",restaurantID,restaurantID)
		queryOrderAmount = queryOrderAmount.Where("o.store_id = ? or tod.store_id = ?",restaurantID,restaurantID)
		queryUsedAmount = queryUsedAmount.Where("o.store_id = ? or tod.store_id = ?",restaurantID,restaurantID)
		queryAllTakeOrderCount = queryAllTakeOrderCount.Where("o.store_id = ? or tod.store_id = ?",restaurantID,restaurantID)
	}


	if areaID !=0 {
		query = query.Where("t_mini_game_activity_user_log.area_id = ?",areaID)
		queryAllAmount = queryAllAmount.Where("area_id = ?", areaID)
		queryOrderAmount = queryOrderAmount.Where("t_mini_game_activity_user_log.area_id = ?", areaID)
		queryUsedAmount = queryUsedAmount.Where("t_mini_game_activity_user.area_id = ?", areaID)
		queryAllTakeOrderCount = queryAllTakeOrderCount.Where("t_mini_game_activity_user_log.area_id = ?", areaID)
	}
	if cityID !=0 {
		query = query.Where("t_mini_game_activity_user_log.city_id = ?",cityID)
		queryAllAmount = queryAllAmount.Where("city_id = ?", cityID)
		queryOrderAmount = queryOrderAmount.Where("t_mini_game_activity_user_log.city_id = ?", cityID)
		queryUsedAmount = queryUsedAmount.Where("t_mini_game_activity_user.city_id = ?", cityID)
		queryAllTakeOrderCount = queryAllTakeOrderCount.Where("t_mini_game_activity_user_log.city_id = ?", cityID)
	}
	if startTime != "" {
		startTime = startTime + " 00:00:00"
		query = query.Where("t_mini_game_activity_user_log.created_at >=  ?",startTime)
		queryAllAmount = queryAllAmount.Where("created_at >=  ?", startTime)
		queryOrderAmount = queryOrderAmount.Where("t_mini_game_activity_user_log.created_at >=  ?", startTime)
		queryUsedAmount = queryUsedAmount.Where("t_mini_game_activity_user_log.created_at >=  ?", startTime)
		queryAllTakeOrderCount = queryAllTakeOrderCount.Where("t_mini_game_activity_user_log.created_at >=  ?", startTime)
	}
	if endTime != "" {
		endTime = endTime + " 23:59:59"
		query = query.Where("t_mini_game_activity_user_log.created_at <= ?",endTime)
		queryAllAmount = queryAllAmount.Where("created_at <=  ?", endTime)
		queryOrderAmount = queryOrderAmount.Where("t_mini_game_activity_user_log.created_at <= ?", endTime)
		queryUsedAmount = queryUsedAmount.Where("t_mini_game_activity_user_log.created_at <= ?", endTime)
		queryAllTakeOrderCount = queryAllTakeOrderCount.Where("t_mini_game_activity_user_log.created_at <= ?", endTime)
	}

	queryAllAmount.Scan(&sendAmount)
	queryUsedAmount.Scan(&usedAmount)
	queryAllTakeOrderCount.Scan(&allTakeOrderCount)
	queryOrderAmount.Scan(&orderAmount)


	var total int64
	query.Count(&total)
	query.Scopes(scopes.Page(pagination.Page,pagination.Limit)).Find(&data)
	var rtnData = cms.UseDiscountStatisticsByNumber{
		SendAmount: sendAmount,
		UsedAmount: usedAmount,
		UseOrderCount: allTakeOrderCount,
		DiscountOrderAmount: orderAmount,
		Total: total,
	}

	return data,rtnData
}

func (m MiniGameActivityService) ActivityStatisticsByArea(activityID int, sortColumns string, admin models.Admin) ([]models.MiniGameActivityUserForCmsStatics, int64) {
	db := tools.ReadDb1
	var statisticData []models.MiniGameActivityUserForCmsStatics
	query := db.Model(models.MiniGameActivityUserForCmsStatics{}).
		Preload("User").
		Preload("Area").
		Preload("MiniGameActivity").
		Select(`*,
			COALESCE(SUM(all_amount), 0) as all_amount,
			COUNT(1) as game_count,
			SUM(
				(SELECT
					-1 * SUM(IF(tmgaul.type = 2, tmgaul.amount, 0)) AS spend_amount
				 FROM t_mini_game_activity_user_log tmgaul
				 LEFT JOIN t_order_today tod ON tmgaul.order_id = tod.id
				 LEFT JOIN t_order o ON tmgaul.order_id = o.id
				 WHERE tmgaul.type = 2 and tmgaul.activity_id = ?
				AND (o.state =7 or tod.state = 7)
				 AND tmgaul.user_id = t_mini_game_activity_user.user_id )
			) as spend_amount,
        SUM(
				(SELECT
					SUM(IF(o.state = 7, 1, 0)) + SUM(IF(tod.state = 7, 1, 0)) AS take_order_count
				 FROM t_mini_game_activity_user_log tmgaul
				 LEFT JOIN t_order_today tod ON tmgaul.order_id = tod.id
				 LEFT JOIN t_order o ON tmgaul.order_id = o.id
				 WHERE tmgaul.type = 2 
				 and tmgaul.activity_id = ?
				 AND tmgaul.user_id = t_mini_game_activity_user.user_id)
			) as take_order_count
`,activityID,activityID).
		Where("activity_id = ?",activityID)
	if admin.IsDealer() || admin.IsDealerSub() {
		query = query.Where("area_id = ?",admin.AdminAreaID)
	}
	query = query.Group("area_id")
	var total int64
	query.Count(&total)
	query.Find(&statisticData)
	return statisticData, total
}


// UseDiscountStatisticsByArea
//
//  @Author: YaKupJan
//  @Date: 2024-11-20 11:23:08
//  @Description: 游戏优惠使用统计 按区域
//  @receiver m
//  @param pagination
//  @param activityID
//  @param sortColumns
//  @param admin
//  @return interface{}
//  @return int64
func (m MiniGameActivityService) UseDiscountStatisticsByArea(activityID int, sortColumns string, admin models.Admin) ([]models.MiniGameActivityUserForCmsStatics, int64) {
	db := tools.ReadDb1
	var miniGameActivity models.MiniGameActivity
	db.Model(models.MiniGameActivity{}).Find(&miniGameActivity,activityID)
	startTime := miniGameActivity.StartTime.Format("2006-01-02 15:04:05")
	endTime := miniGameActivity.EndTime.Format("2006-01-02 15:04:05")
	var statisticData []models.MiniGameActivityUserForCmsStatics
	query := db.Model(models.MiniGameActivityUserForCmsStatics{}).
		Preload("User").
		Preload("Area").
		Preload("MiniGameActivity").
		Select(`*,
			COALESCE(SUM(all_amount),0) as all_amount,
			COUNT(1) as game_count,
			SUM(
				(SELECT
					-1 * SUM(ifnull(tmgaul.amount,0)) AS spend_amount
				 FROM t_mini_game_activity_user_log tmgaul
				 LEFT JOIN t_order_today tod ON tmgaul.order_id = tod.id
				 LEFT JOIN t_order o ON tmgaul.order_id = o.id
				 WHERE tmgaul.type = 2
				 AND tmgaul.activity_id = ? 
				 AND tmgaul.user_id = t_mini_game_activity_user.user_id
                 AND (o.state = 7 or tod.state =  7) )
			) as spend_amount,
        SUM(
				(SELECT
					SUM(IF(o.state = 7, 1, 0)) + SUM(IF(tod.state = 7, 1, 0)) AS take_order_count
				 FROM t_mini_game_activity_user_log tmgaul
				 LEFT JOIN t_order_today tod ON tmgaul.order_id = tod.id
				 LEFT JOIN t_order o ON tmgaul.order_id = o.id
				 WHERE tmgaul.type = 2 
				 AND tmgaul.activity_id = ? 
				 AND tmgaul.user_id = t_mini_game_activity_user.user_id )
			) as take_order_count,
	(	SELECT COUNT(1) FROM t_user u WHERE 
    		u.created_at BETWEEN ? AND ? 
    		AND u.id IN ( SELECT user_id FROM t_mini_game_activity_user WHERE activity_id = ? )
    AND u.user_area_id = t_mini_game_activity_user.area_id ) AS new_user_count
`,activityID,activityID,startTime,endTime,activityID).
		Where("activity_id = ?",activityID)
	if admin.IsDealer() || admin.IsDealerSub() {
		query = query.Where("area_id = ?",admin.AdminAreaID)
	}
	query = query.Group("area_id")
	var total int64
	query.Count(&total)
	query.Find(&statisticData)
	return statisticData, total
}


func NewMiniGameActivityService(c *gin.Context) *MiniGameActivityService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := MiniGameActivityService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &service
}


func GetMiniGameActivityService() *MiniGameActivityService {
	
	service := MiniGameActivityService{

	}
	return &service
}

//审核通过后发放优惠卷
func (m MiniGameActivityService) SendValentineCoupon(activityID int, userID int, itemId int) error {
	db := tools.GetDB()
	var activity models.MiniGameActivity
	date := carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s");
	db.Model(&models.MiniGameActivity{}).
		Where("? BETWEEN t_mini_game_activity.start_time AND t_mini_game_activity.end_time",date).
		Find(&activity,activityID)
	if activity.ID > 0 {
		var form models.MiniGameActivityUserForm
		db.Model(models.MiniGameActivityUserForm{}).
			Where("id = ? and state = ?",itemId,1).Find(&form)
		if form.ID > 0 {
			var userLog models.MiniGameUserLog
			ct :=int64(0)
			db.Model(models.MiniGameUserLog{}).Where("activity_id = ? and user_id = ? and item_id = ?",activityID,userID,itemId).Count(&ct)
			if ct == 0 {
				var user models.MiniGameActivityUser
				db.Model(models.MiniGameActivityUser{}).Where("activity_id = ? and user_id = ?",activityID,userID).Find(&user)
				
				if user.ID == 0 {
					user = models.MiniGameActivityUser{
						ActivityId:activityID,
						CityId:form.CityID,
						AreaId:form.AreaID,
						UserId:userID,
						AllAmount: activity.RewardAmount,
						LeftAmount:activity.RewardAmount,
					}
					db.Create(&user)

					userLog = models.MiniGameUserLog{
						ActivityID:activityID,
						CityID: user.CityId,
						AreaID:user.AreaId,
						UserID:userID,
						ItemId:itemId,
						Amount:int64(activity.RewardAmount),
						OriginalAmount:int64(activity.RewardAmount),
						LeftAmount:int64(activity.RewardAmount),
						OrderPercent:5,
						Type:1,//领取
					}
					db.Create(&userLog)

				}

				
			}
		}	
	}
	return nil
}

// ListContentFromUser 获取用户参与活动的列表
// @params params: 请求参数，包含查询条件如 ActivityID, AreaID, CityID 等
// @params admin: 当前管理员信息，用于权限判断
// @params pagination: 分页信息，包含当前页数和每页记录数
// 返回：活动用户表单列表、总记录数、错误（如果有）
func (m MiniGameActivityService) ListContentFromUser(params cms.MiniGameActivityListContentFromUserRequest, pagination tools.Pagination) ([]models.MiniGameActivityUserForm, int64, error) {
	// 获取数据库连接
	db := tools.GetDB()

	// 定义一个变量用于存储查询结果
	var activityUserForms []models.MiniGameActivityUserForm
	// 定义一个变量用于存储查询的总记录数
	var total int64

	// 初始化查询构建器，指定要查询的模型和需要预加载的关联数据
	tx := db.Model(models.MiniGameActivityUserForm{}).
		Preload("City").  // 预加载城市信息
		Preload("User").  // 预加载用户信息
		Preload("Activity").  // 预加载活动信息
		Preload("Admin").  // 预加载管理员信息
		Preload("Area")   // 预加载区域信息

	// 根据请求的参数构造查询条件
	if params.ActivityID > 0 {
		tx = tx.Where("activity_id = ?", params.ActivityID)  // 根据活动ID过滤
	}
	if params.AreaID > 0 {
		tx = tx.Where("area_id = ?", params.AreaID)  // 根据区域ID过滤
	}
	if params.CityID > 0 {
		tx = tx.Where("city_id = ?", params.CityID)  // 根据城市ID过滤
	}
	if params.Gender != nil {
		tx = tx.Where("gender = ?", *params.Gender) // 根据性别过滤
	}
	if params.State != nil {
		tx = tx.Where("state = ?", *params.State) // 根据状态过滤
	}
	if params.Search != "" {
		tx = tx.Where("mobile like ?", "%"+params.Search+"%")  // 根据手机号模糊查询
	}
	if params.SortColumns != "" {
		tx = tx.Order(params.SortColumns)  // 根据指定的排序字段排序
	}

	// 获取符合条件的记录总数
	countErr := tx.Count(&total).Error
	if countErr != nil {
		// 如果获取总数时出现错误，记录日志并返回错误
		tools.Logger.Error("ListContentFromUser 获取总数出现错误", countErr)
		return make([]models.MiniGameActivityUserForm, 0), 0, countErr
	}

	// 添加分页查询条件
	tx = tx.Scopes(scopes.Page(pagination.Page, pagination.Limit))

	// 执行查询操作，获取符合条件的用户活动记录
	findErr := tx.Find(&activityUserForms).Error
	if findErr != nil {
		// 如果查询过程中出现错误，记录日志并返回错误
		tools.Logger.Error("ListContentFromUser 获取列表出现错误", findErr)
		return make([]models.MiniGameActivityUserForm, 0), 0, findErr
	}

	// 返回查询结果（活动用户表单列表）和总记录数
	return activityUserForms, total, nil
}

// 操作用户的活动内容
func (m MiniGameActivityService) OperateContentFromUser(params cms.MiniGameActivityOperateContentFromUserRequest, admin models.Admin) error {

	switch params.OperateType {
	// 审核
	case "review":
		if err := m.reviewContentFromUser(params.ID,admin); err != nil {
			tools.Logger.Error("审核操作失败", err)
			return err
		}
	// 拒绝
	case "refuse":
		if err := m.refuseContentFromUser(params.ID, params.Content,admin); err != nil {
			tools.Logger.Error("拒绝操作失败", err)
			return err
		}
	// 删除
	case "delete":
		if err := m.deleteContentFromUser(params.ID, admin); err != nil {
			tools.Logger.Error("删除操作失败", err)
			return err
		}
	default:
		return errors.New("failed")
	}

	return nil
}

// 审核用户的活动内容
func (m MiniGameActivityService)reviewContentFromUser(id int, admin models.Admin) error {
	db := tools.GetDB()

	var userForm models.MiniGameActivityUserForm
	tx := db.Model(models.MiniGameActivityUserForm{}).Where("id = ?", id)

	if permissions.IsAgent(admin){
		tx = tx.Where("area_id = ?",admin.AdminAreaID,).Where("city_id = ?",admin.AdminCityID)
	}
	if err := tx.First(&userForm).Error; err != nil {
		return fmt.Errorf("not_found")
	}
	// 如果已经审核通过，直接返回 nil
	if userForm.State == models.MiniGameActivityUserFormStateApproved {
		return nil
	}
	// 修改状态为审核通过
	if err := db.Model(models.MiniGameActivityUserForm{}).Where("id = ?", id).Updates(map[string]interface{}{
		"state":      models.MiniGameActivityUserFormStateApproved,
		"admin_id":   admin.ID,
		"review_time": carbon.Now("Asia/Shanghai").ToDateTimeString(), // 确保时间格式正确
	}).Error; err != nil {
		return fmt.Errorf("update_fail")
	}
	// 发放优惠券
	if err := m.SendValentineCoupon(userForm.ActivityID, userForm.UserID, userForm.ID); err != nil {
		return fmt.Errorf("failed")
	}
	return nil
}

// 拒绝审核用户的活动内容
func (m MiniGameActivityService)refuseContentFromUser(id int,refuseContent string,admin models.Admin) error  {
	db := tools.GetDB()

	// 查询用户表单
	var userForm models.MiniGameActivityUserForm
	tx := db.Model(models.MiniGameActivityUserForm{}).Where("id = ?", id)

	if permissions.IsAgent(admin){
		tx = tx.Where("area_id = ?",admin.AdminAreaID,).Where("city_id = ?",admin.AdminCityID)
	}

	if err := tx.First(&userForm).Error; err != nil {
		return fmt.Errorf("not_found")
	}

	// 如果该表单已经被拒绝，直接返回 nil
	if userForm.State == models.MiniGameActivityUserFormStateRefused {
		return nil
	}
	// 更新表单状态为审核拒绝，并保存拒绝的理由
	if err := db.Model(models.MiniGameActivityUserForm{}).Where("id = ?", id).Updates(map[string]interface{}{
		"state":         models.MiniGameActivityUserFormStateRefused,
		"refuse_content": refuseContent,
		"admin_id":   admin.ID,
	}).Error; err != nil {
		// 如果更新失败，返回错误
		return fmt.Errorf("update_fail")
	}

	return nil
}

// 删除用户的活动内容
func (m MiniGameActivityService) deleteContentFromUser(id int, admin models.Admin) error {
	db := tools.GetDB()

	// 查询
	var userForm models.MiniGameActivityUserForm
	tx := db.Model(models.MiniGameActivityUserForm{}).Where("id = ?", id)

	if permissions.IsAgent(admin) {
		tx = tx.Where("area_id = ?", admin.AdminAreaID).Where("city_id = ?", admin.AdminCityID)
	}

	if err := tx.First(&userForm).Error; err != nil {
		return fmt.Errorf("not_found")
	}

	// 如果表单已审核通过，直接返回 nil
	//if userForm.State == models.MiniGameActivityUserFormStateApproved {
	//	return nil
	//}

	// 更新表单状态为已删除，并设置删除操作的管理员ID
	if err := db.Model(models.MiniGameActivityUserForm{}).Where("id = ?", userForm.ID).Updates(map[string]interface{}{
		"admin_id":   admin.ID,                                       // 记录执行删除操作的管理员ID
		"deleted_at": carbon.Now("Asia/Shanghai").ToDateTimeString(), // 可以添加删除时间
	}).Error; err != nil {
		return fmt.Errorf("delete_fail")
	}

	return nil
}

