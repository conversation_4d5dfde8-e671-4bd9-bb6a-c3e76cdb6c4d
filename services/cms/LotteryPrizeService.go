package cms

import (
	"errors"
	"mulazim-api/lang"
	"mulazim-api/models"
	shipmentModels "mulazim-api/models/shipment"
	lotteryRequest "mulazim-api/requests/cms/lottery"
	"mulazim-api/scopes"
	"mulazim-api/tools"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type LotteryPrizeService struct {
	langUtil *lang.LangUtil
	language string
}

func NewLotteryPrizeService(c *gin.Context) *LotteryPrizeService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	lotteryPrizeService := LotteryPrizeService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &lotteryPrizeService
}

// Create
//
// @Description: 配送员收入创建
// @Author: Rixat
// @Time: 2023-11-07 02:32:41
// @receiver
// @param c *gin.Context
func (s *LotteryPrizeService) Create(admin models.Admin, params lotteryRequest.LotteryPrizeCreateRequest) error {
	err := tools.GetDB().Model(models.LotteryPrize{}).Create(&models.LotteryPrize{
		TitleImgUg:        params.TitleImgUg,
		TitleImgZh:        params.TitleImgUg,
		SwiperImgUg:       params.SwiperImgUg,
		SwiperImgZh:       params.SwiperImgZh,
		NameUg:            params.NameUg,
		NameZh:            params.NameZh,
		Model:             params.Model,
		Price:             params.Price,
		// Type:   params.Type,
		CityID: params.CityID,
		AreaID: params.AreaID,
		State:             tools.ToInt(params.State),
		AdminID:           admin.ID,
		CreatedAt:         carbon.Now().Carbon2Time(),
	}).Error
	if err != nil {
		return errors.New("fail")
	}
	return nil
}

// Edit 
//
// @Description: 编辑
// @Author: Rixat
// @Time: 2024-09-06 10:32:01
// @receiver 
// @param c *gin.Context
func (s *LotteryPrizeService) Edit(admin models.Admin, params lotteryRequest.LotteryPrizeEditRequest) error {
	var lotteryPrize models.LotteryPrize
	tools.GetDB().Model(lotteryPrize).Where("id=?",params.ID).Find(&lotteryPrize)
	if lotteryPrize.ID == 0{
		return errors.New("not_found")
	}
	// 验证是否绑定过
	var levelPrizeCount int64
	tools.GetDB().Model(models.LotteryActivityLevelPrize{}).Where("prize_id=?",lotteryPrize.ID).Count(&levelPrizeCount)
	if levelPrizeCount > 0 {
		return errors.New("prize_already_used_enable_edit")
	}
	err := tools.GetDB().Model(models.LotteryPrize{}).Where("id = ?",params.ID).UpdateColumns(&models.LotteryPrize{
		TitleImgUg:        params.TitleImgUg,
		TitleImgZh:        params.TitleImgUg,
		SwiperImgUg:       params.SwiperImgUg,
		SwiperImgZh:       params.SwiperImgZh,
		NameUg:            params.NameUg,
		NameZh:            params.NameZh,
		Model:             params.Model,
		Price:             params.Price,
		State:             tools.ToInt(params.State),
		AdminID:           admin.ID,
		UpdatedAt: carbon.Now().Carbon2Time(),
	}).Error
	if err != nil {
		return errors.New("fail")
	}
	return nil
}

// Detail
//
// @Description: 配送员收入详情
// @Author: Rixat
// @Time: 2023-11-07 02:33:05
// @receiver
// @param c *gin.Context
func (s *LotteryPrizeService) Detail(ID int) (models.LotteryPrize, error) {
	var lotteryPrize models.LotteryPrize
	tools.Db.Model(lotteryPrize).Where("id=?", ID).
		Preload("City").
		Preload("Area").
		First(&lotteryPrize)
	if lotteryPrize.ID == 0 {
		return lotteryPrize, errors.New("not_found")
	}
	return lotteryPrize, nil
}


// Detail
//
// @Description: 配送员收入详情
// @Author: Rixat
// @Time: 2023-11-07 02:33:05
// @receiver
// @param c *gin.Context
func (s *LotteryPrizeService) PostDelete(ID int) error {
	var lotteryPrize models.LotteryPrize
	tools.Db.Model(lotteryPrize).Where("id=?", ID).First(&lotteryPrize)
	if lotteryPrize.ID == 0 {
		return  errors.New("not_found")
	}
	// 验证是否绑定过
	var levelPrizeCount int64
	tools.GetDB().Model(models.LotteryActivityLevelPrize{}).Where("prize_id=?",lotteryPrize.ID).Count(&levelPrizeCount)
	if levelPrizeCount > 0 {
		return errors.New("prize_already_used_enable_delete")
	}
	err := tools.Db.Model(&lotteryPrize).Where("id=?", ID).Delete(&lotteryPrize).Error
	if err != nil {
		return errors.New("fail")
	}
	return nil
}

// Detail
//
// @Description: 配送员收入详情
// @Author: Rixat
// @Time: 2023-11-07 02:33:05
// @receiver
// @param c *gin.Context
func (s *LotteryPrizeService) ChangeState(ID int,state int) error {
	var lotteryPrize models.LotteryPrize
	tools.Db.Model(lotteryPrize).Where("id=?", ID).First(&lotteryPrize)
	if lotteryPrize.ID == 0 {
		return  errors.New("not_found")
	}
	// 验证是否绑定过
	var levelPrizeCount int64
	tools.GetDB().Model(models.LotteryActivityLevelPrize{}).Where("prize_id=?",lotteryPrize.ID).Count(&levelPrizeCount)
	if levelPrizeCount > 0  && state == 0 {
		return errors.New("prize_already_used_enable_close")
	}
	err := tools.Db.Model(&lotteryPrize).Where("id=?", ID).UpdateColumn("state",state).Error
	if err != nil {
		return errors.New("fail")
	}
	return nil
}
// List
//
// @Description: 配送员收入列表
// @Author: Rixat
// @Time: 2023-11-07 02:33:23
// @receiver
// @param c *gin.Context
func (s *LotteryPrizeService) List(page, limit int, kw string,
	lpType uint, cityID, areaID int) (int64, []models.LotteryPrize) {
	var lotteryPrizeList []models.LotteryPrize
	query := tools.Db.Model(lotteryPrizeList)
	if len(kw) > 0 {
		query.Where("name_ug like ? or name_zh like ?", "%"+kw+"%", "%"+kw+"%")
	}
	if lpType > 0 {
		query.Where("type =?", lpType)
	}
	if cityID > 0 {
		query.Where("city_id =?", cityID)
	}
	if areaID > 0 {
		query.Where("area_id =?", areaID)
	}
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Preload("City").
			Preload("Area").
			Order("created_at desc").
			Scopes(scopes.Page(page, limit)).
			Find(&lotteryPrizeList)
	}
	return totalCount,lotteryPrizeList
}

// Header
//
// @Description: 投诉统计
// @Author: Rixat
// @Time: 2023-12-05 07:56:39
// @receiver
// @param c *gin.Context
func (s *LotteryPrizeService) Header(incomeType int, cityId int, areaId int, startDate string, endDate string) map[string]interface{} {
	var result map[string]interface{}
	selectRaw := `
		IFNULL(sum( amount ),0) as total_amount,
		COUNT( id ) as total_count,
		COUNT(IF( complaint_type = 1, 1, null )) AS customer_count,
		COUNT(IF( complaint_type = 2, 1, null )) AS restaurant_count
	`
	query := tools.Db.Model(shipmentModels.ShipperIncome{}).Select(selectRaw)
	if cityId > 0 {
		query = query.Where("city_id =?", cityId)
	}
	if areaId > 0 {
		query = query.Where("area_id =?", areaId)
	}
	if len(startDate) > 0 && len(endDate) > 0 {
		query = query.Where("created_at BETWEEN ? AND ?", startDate+" 00:00:00", endDate+" 23:59:59")
	}
	if incomeType > 0 {
		query = query.Where("type =?", incomeType)
	}
	query.Scan(&result)
	return result
}

// GetOrderList
//
// @Description: 订单列表
// @Author: Rixat
// @Time: 2023-12-05 07:57:04
// @receiver
// @param c *gin.Context
func (s *LotteryPrizeService) GetOrderList(page int, limit int, cityId int, areaId int, kw string, shipperID int, startDate string, endDate string, sort string) map[string]interface{} {
	orderItems := make([]map[string]interface{}, 0)
	var orderTodayList []models.OrderToday
	var orderList []models.Order
	var totalCount int64
	queryOrderToday := tools.Db.Model(orderTodayList).
		Select("t_order_today.id,t_order_today.order_id,t_order_today.name as customer_name,t_order_today.mobile as customer_mobile,t_order_today.shipper_id,t_admin.real_name as shipper_name,t_order_today.order_price,t_order_today.created_at,b_order_state.name_" + s.language + " as state").
		Joins("left join t_admin on t_admin.id = t_order_today.shipper_id and t_admin.type = 9").
		Joins("left join b_order_state on b_order_state.id = t_order_today.state")
	queryOrder := tools.Db.Model(orderList).
		Select("t_order.id,t_order.order_id,t_order.name as customer_name,t_order.mobile as customer_mobile,t_order.shipper_id,t_admin.real_name as shipper_name,t_order.order_price,t_order.created_at,b_order_state.name_" + s.language + " as state").
		Joins("left join t_admin on t_admin.id = t_order.shipper_id and t_admin.type = 9").
		Joins("left join b_order_state on b_order_state.id = t_order.state")
	if len(startDate) > 0 && len(endDate) > 0 {
		queryOrderToday.Where("t_order_today.created_at between ? and ?", startDate+" 00:00:00", endDate+" 23:59:59")
		queryOrder.Where("t_order.created_at between ? and ?", startDate+" 00:00:00", endDate+" 23:59:59")
	}
	if cityId > 0 {
		queryOrderToday.Where("t_order_today.city_id = ?", cityId)
		queryOrder.Where("t_order.city_id = ?", cityId)
	}
	if areaId > 0 {
		queryOrderToday.Where("t_order_today.area_id = ?", areaId)
		queryOrder.Where("t_order.area_id = ?", areaId)
	}
	if len(kw) > 0 {
		queryOrderToday.Where("t_order_today.order_id = ?", kw)
		queryOrder.Where("t_order.order_id = ?", kw)
	}
	if shipperID > 0 {
		queryOrderToday.Where("t_order_today.shipper_id = ?", shipperID)
		queryOrder.Where("t_order.shipper_id = ?", shipperID)
	}
	var todayOrderCount int64
	var hisOrderCount int64
	queryOrderToday.Count(&todayOrderCount)
	queryOrder.Count(&hisOrderCount)
	totalCount = todayOrderCount + hisOrderCount
	if totalCount > 0 {
		queryOrderToday.Order(sort).Offset((page - 1) * limit).Limit(limit)
		queryOrder.Order(sort).Offset((page - 1) * limit).Limit(limit)
		tools.Db.Raw("(?) union (?)", queryOrderToday, queryOrder).Find(&orderItems)
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": orderItems,
	}
	return result
}

// GetShipperList
//
// @Description: 获取配送员列表
// @Author: Rixat
// @Time: 2023-12-05 07:57:53
// @receiver
// @param c *gin.Context
func (s *LotteryPrizeService) GetShipperList(admin models.Admin, page int, limit int, cityId int, areaId int, kw string, sort string, disabled int) map[string]interface{} {
	var shipperList []models.Admin
	var totalCount int64

	query := tools.Db.Model(shipperList).Where("type=9")
	if cityId > 0 {
		query.Where("admin_city_id = ?", cityId)
	}
	if areaId > 0 {
		query.Where("admin_area_id = ?", areaId)
	}
	if len(kw) > 0 {
		query.Where("real_name like ? or mobile like ?", "%"+kw+"%", "%"+kw+"%")
	}
	if disabled > 0 {
		query.Where("deleted_at is not null or state = 0")
	} else {
		query.Where(" state = 1 ")
	}
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Scopes(scopes.Page(page, limit)).
			Preload("ShipperIncomeTemplate").
			Order("id desc").
			Find(&shipperList)
	}
	items := make([]map[string]interface{}, 0)
	for _, value := range shipperList {
		items = append(items, map[string]interface{}{
			"id":                      value.ID,
			"real_name":               value.RealName,
			"name":                    value.Name,
			"avatar":                  tools.CdnUrl(value.Avatar),
			"mobile":                  value.Mobile,
			"complaint_deduction_fee": value.ShipperIncomeTemplate.ComplainDeductionFee,
			"created_at":              tools.TimeFormatYmdHis(&value.CreatedAt),
		})
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": items,
	}
	return result
}

// GetValidListByIDs
//
// @Description: 使用 id 列表获取有效奖品列表
// @Author: Salam
// @Time: 2025-02-27 17:51:53
// @receiver
// @param c *gin.Context
func (svc *LotteryPrizeService) GetValidListByIDs(prizeIdList []uint) ([]models.LotteryPrize, error) {
	var result []models.LotteryPrize
	db := tools.GetDB()
	if err := db.Model(&models.LotteryPrize{}).
		Where("id in ?", prizeIdList).
		Where("state = ?", models.LotteryPrizeStateOn).
		Find(&result).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	return result, nil
}