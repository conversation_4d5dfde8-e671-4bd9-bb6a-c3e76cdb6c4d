package cms

import (
	"context"
	"errors"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	seckillRequests "mulazim-api/requests/cms/seckill"
	"mulazim-api/scopes"
	"mulazim-api/services"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type SeckillService struct {
	langUtil *lang.LangUtil
	language string
	services.BaseService
}

func NewSeckillService(c *gin.Context) *SeckillService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	seckillService := SeckillService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &seckillService
}

// GetSeckillList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver 
// @param c *gin.Context
func (s SeckillService) GetSeckillList(params seckillRequests.CmsSeckillList) (int64,[]models.Seckill){
	var seckillList []models.Seckill
	query := tools.ReadDb1.Model(seckillList).Where("type = 1")
	// 筛选条件
	if params.CityID > 0 {
		query = query.Where("city_id=?", params.CityID)
	}
	if params.AreaID > 0 {
		query = query.Where("area_id=?", params.AreaID)
	}
	if params.RestaurantID > 0 {
		query = query.Where("restaurant_id=?", params.RestaurantID)
	}
	if params.FoodID > 0 {
		query = query.Where("food_id=?", params.FoodID)
	}
	if len(params.BeginTime) > 0 {
		query = query.Where("begin_time >= ?", params.BeginTime)
	}
	if len(params.EndTime) > 0 {
		query = query.Where("begin_time <= ?", carbon.Parse(params.EndTime).SubHour())
	}
	// 餐厅美食
	if params.MarkupType == 1 {
		query = query.Where("(price_markup_id = 0 or price_markup_id is null)")
	}
	if params.MarkupType == 2 {
		query = query.Where("price_markup_id > 0")
	}
	if len(params.RunState) > 0 {
		nowTime := carbon.Now().ToDateTimeString()
		// 0:未开始
		if params.RunState == "0" {
			query = query.Where("begin_time > ?", nowTime)
		}
		// 1:执行中
		if params.RunState == "1" {
			query = query.Where("begin_time <= ? and end_time > ?", nowTime,nowTime)
		}
		// 2:已结束
		if params.RunState == "2" {
			query = query.Where("end_time < ?", nowTime)
		}
	}
	if len(params.State) > 0 {
		query = query.Where("state = ?", params.State)
	}
	// 查询结果
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Preload("City").
			Preload("Area").
			Preload("Admin").
			Preload("Restaurant").
			Preload("RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions").
			Preload("RestaurantFoods.ComboFoodItems.RestaurantFood").
			Preload("PriceMarkupFood").
			Preload("SelectedSpec.FoodSpecOptions").
			Preload("PriceMarkupSeckillPriceLog.PriceMarkupFoodLogSum", func(db *gorm.DB) *gorm.DB {
				return db.Select("seckill_price_log_id,IFNULL(sum(saled_count),0) as saled_count,IFNULL(sum(saled_count*price),0) as price").
					Where("state in(1,2,3)").
					Group("seckill_price_log_id") 
			}).
			Order("id desc").
			Scopes(scopes.Page(params.Page, params.Limit)).
			Find(&seckillList)
	}
	return totalCount,seckillList
}



// GetSeckillList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver 
// @param c *gin.Context
func (s SeckillService) GetSeckillLogList(params seckillRequests.CmsSeckillLogList) (int64,[]models.SeckillLog){
	var seckillLogList []models.SeckillLog
	query := tools.ReadDb1.Model(seckillLogList).
		Joins("left join b_seckill on b_seckill.id = b_seckill_log.seckill_id").
		Where("b_seckill_log.state=1")
	// 筛选条件
	if params.CityID > 0 {
		query = query.Where("b_seckill.city_id=?", params.CityID)
	}
	if params.AreaID > 0 {
		query = query.Where("b_seckill.area_id=?", params.AreaID)
	}
	if params.RestaurantID > 0 {
		query = query.Where("b_seckill.restaurant_id=?", params.RestaurantID)
	}
	if params.FoodID > 0 {
		query = query.Where("b_seckill_log.food_id=?", params.FoodID)
	}
	if len(params.BeginTime) > 0 {
		query = query.Where("b_seckill_log.created_at >= ?", params.BeginTime+" 00:00:00")
	}
	if len(params.EndTime) > 0 {
		query = query.Where("b_seckill_log.created_at <= ?", params.EndTime+" 23:59:59")
	}
	
	// 查询结果
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Preload("User").
			Preload("Food").
			Preload("Seckill.Area").
			Preload("Seckill.City").
			Preload("Seckill.Restaurant").
			Preload("RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions").
			Preload("RestaurantFoods.ComboFoodItems.RestaurantFood").
			Preload("SelectedSpec.FoodSpecOptions").
			Order("id desc").
			Scopes(scopes.Page(params.Page, params.Limit)).
			Find(&seckillLogList)
	}
	return totalCount,seckillLogList
}


// GetSeckillList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver 
// @param c *gin.Context
func (s SeckillService) GetSeckillLogHeader(params seckillRequests.CmsSeckillLogList) map[string]interface{}{
	var res map[string]interface{}
	sqlSelect := 	`
		count( DISTINCT b_seckill_log.order_id ) AS order_count,
		count( DISTINCT b_seckill_log.user_id ) AS user_count,
		count( DISTINCT b_seckill_log.seckill_id ) AS seckill_count,
		count( DISTINCT b_seckill_log.food_id ) AS food_count,
		sum( b_seckill_log.saled_count ) AS saled_count
	`
	query := tools.ReadDb1.Model(models.SeckillLog{}).Select(sqlSelect).
		Joins("left join b_seckill on b_seckill.id = b_seckill_log.seckill_id").
		Where("b_seckill_log.state=1")
	// 筛选条件
	if params.CityID > 0 {
		query = query.Where("b_seckill.city_id=?", params.CityID)
	}
	if params.AreaID > 0 {
		query = query.Where("b_seckill.area_id=?", params.AreaID)
	}
	if params.RestaurantID > 0 {
		query = query.Where("b_seckill.restaurant_id=?", params.RestaurantID)
	}
	if params.FoodID > 0 {
		query = query.Where("b_seckill_log.food_id=?", params.FoodID)
	}
	if len(params.BeginTime) > 0 {
		query = query.Where("b_seckill_log.created_at >= ?", params.BeginTime+" 00:00:00")

	}
	if len(params.EndTime) > 0 {
		query = query.Where("b_seckill_log.created_at <= ?", params.EndTime+" 23:59:59")

	}
	query.Scan(&res)
	return res
}

// GetSeckillList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver 
// @param c *gin.Context
func (s SeckillService) Detail(ID int) models.Seckill{
	var seckill models.Seckill
	tools.Db.Model(seckill).Where("type = 1").Where("id=?",ID).
	Preload("Restaurant").
		Preload("RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions").
		Preload("RestaurantFoods.ComboFoodItems.RestaurantFood").
		Preload("SelectedSpec.FoodSpecOptions").
		Preload("PriceMarkupFood").
	Find(&seckill)
	return seckill
}

func (s SeckillService) Delete(ID int) error{
	var seckill models.Seckill
	tools.Db.Model(seckill).Where("type = 1").Where("id=?",ID).Find(&seckill)
	if carbon.Parse(seckill.BeginTime).Lte(carbon.Now()){
		return errors.New("seckill_start_time_is_over")
	}
	// 删除
	err := tools.GetDB().Where("id=?",ID).Delete(&models.Seckill{}).Error
	if err!= nil {
		return errors.New("failed")
	}
	if seckill.PriceMarkupID > 0 && seckill.Type == 1{
		err := tools.GetDB().Where("seckill_id=?",ID).Delete(&models.PriceMarkupSeckillPriceLog{}).Error
		if err!= nil {
			return errors.New("failed")
		}
	}

	return nil
}

// GetSeckillList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver 
// @param c *gin.Context
func (s SeckillService) ChangeState(ID int,State int) error  {
	db := tools.Db
	var seckill models.Seckill
	db.Model(&models.Seckill{}).Preload("RestaurantFoods").Where("id = ?",ID).First(&seckill)
	if seckill.ID == 0 {
		return errors.New("not_found")
	}
	// 状态开启(关闭状态不需要验证，直接关闭)
	if State == 1 {
		multipleDiscount,foodName :=s.CheckFoodsMultipleDiscount(s.langUtil.Lang,int(seckill.FoodId),carbon.Parse(seckill.BeginTime,configs.AsiaShanghai).Format("Y-m-d H:i:s"),carbon.Parse(seckill.EndTime,configs.AsiaShanghai).Format("Y-m-d H:i:s"))
		if multipleDiscount.ID > 0 {
			//存在正在进行的多分打折活动 不允许创建
			return fmt.Errorf(s.langUtil.T("food_has_multiple_discount"),foodName)
		}
		if seckill.FoodType == models.RestaurantFoodsTypeSpec {
			if !s.CheckChangeStateSpecInfo(seckill.SpecID, tools.ToInt(seckill.FoodId)) {
				return errors.New("spec_info_has_changed")
			}
		}
		// 验证是否已结束
		if carbon.Parse(seckill.EndTime).Lte(carbon.Now()){
			return errors.New("seckill_end_time_is_over")
		}		
		// 验证是否存在冲突活动
		var conflictSeckill  models.Seckill
		db.Model(&models.Seckill{}).
			Where("food_id = ? and begin_time  = ? and state = 1 and id <> ? and spec_id = ?",seckill.FoodId,carbon.Parse(seckill.BeginTime).Format("Y-m-d H:i:s"),ID,seckill.SpecID).
			First(&conflictSeckill)
		// 存在时间冲突并开启或活动
		if conflictSeckill.ID > 0 {
			seckillFoodName :=conflictSeckill.RestaurantFoods.NameUg
			if s.language !="ug"{
				seckillFoodName =conflictSeckill.RestaurantFoods.NameZh
			}
			if conflictSeckill.Type == 1 {
				return  fmt.Errorf(s.langUtil.T("exist_conflict_seckill"),seckillFoodName)
			}else{
				return  fmt.Errorf(s.langUtil.T("exist_conflict_special"),seckillFoodName)
			}
		}
		// 验证加价活动库存
		if seckill.PriceMarkupID > 0 && State == 1{
			var markup models.PriceMarkupFood
			tools.Db.Model(markup).Where("id = ?", seckill.PriceMarkupID).First(&markup)
			// 如果当前秒杀活动占用库存大于加价活动目前能用到的库存时，不能修改状态
			openStateUseCount := tools.ToInt(seckill.TotalCount-seckill.SaledCount)
			if openStateUseCount > markup.CanUseCount() {
				return errors.New("open_state_out_off_price_markup_stock")
			}
		}
	}
	// 更新状态
	err := db.Model(&models.Seckill{}).Where("id=?", ID).Update("state", State).Error
	if err != nil {
		return errors.New("failed")
	}
	return nil
}



// GetSeckillList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver 
// @param c *gin.Context
func (s SeckillService) SetOrder(ID int,Order int) error  {
	db := tools.Db
	var seckill models.Seckill
	db.Model(seckill).Where("id = ?",ID).First(&seckill)
	if seckill.ID == 0 {
		return errors.New("not_found")
	}
	// 验证是否已结束
	if carbon.Parse(seckill.EndTime).Lte(carbon.Now()){
		return errors.New("seckill_end_time_is_over")
	}	
	// 更新状态
	err := db.Model(seckill).Where("id=?", ID).Update("order", Order).Error
	if err != nil {
		return errors.New("failed")
	}
	return nil
}



// GetSeckillList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver 
// @param c *gin.Context
func (s SeckillService) Create(params []seckillRequests.CmsSeckillItem,admin models.Admin) error  {
	food := s.GetFoodById(params[0].FoodID)
	// 验证加价活动
	var markup  models.PriceMarkupFood
	if params[0].PriceMarkupID > 0 {
		tools.Db.Model(markup).Where("id =?", params[0].PriceMarkupID).Find(&markup)
		if markup.ID == 0 {
			return errors.New("not_found")
		}
		if markup.State != 3 {
			return errors.New("state_error")
		}
	}
	
	for _,item := range params {
		multipleDiscount,foodName :=s.CheckFoodsMultipleDiscount(s.langUtil.Lang,item.FoodID,carbon.Parse(item.BeginTime,configs.AsiaShanghai).Format("Y-m-d H:i:s"),carbon.Parse(item.EndTime,configs.AsiaShanghai).Format("Y-m-d H:i:s"))
		if multipleDiscount.ID > 0 {
			//存在正在进行的多分打折活动 不允许创建
			return fmt.Errorf(s.langUtil.T("food_has_multiple_discount"),foodName)
		}
	}
	// var batchSeckill []models.Seckill
	for _,item := range params {
		
		if item.PriceMarkupID > 0 {
			// 验证活动时间
			if carbon.Time2Carbon(*markup.StartDate).Gt(carbon.Parse(item.BeginTime)) || carbon.Time2Carbon(*markup.EndDate).Lt(carbon.Parse(item.EndTime)){
				return errors.New("time_enable_out_price_markup_time_range")
			}
			// 验证库存数量
			if markup.CanUseCount() < item.TotalCount{
				return errors.New("total_count_over_price_markup_total_count")
			}
		}
		if item.UserMaxOrderCount == 0 {
			item.UserMaxOrderCount = item.TotalCount
		}
		sec := models.Seckill{
			PriceMarkupID:            tools.ToInt64(markup.ID),
			Type:                 1,
			AdminId:              int64(admin.ID),
			CityId:   food.Restaurant.CityID,
			AreaId:   food.Restaurant.AreaID,
			FoodId:               int64(item.FoodID),
			RestaurantId:         int64(item.RestaurantID),
			BeginTime:            item.BeginTime,
			EndTime:              item.EndTime,
			TotalCount:           int64(item.TotalCount),
			UserMaxOrderCount:    int64(item.UserMaxOrderCount),
			Price:                int64(item.Price),
			Order:                int64(item.Order),
			CreatedAt:            time.Now(),
			State:                item.State,
			ReviewState:          2,
			FoodType: uint8(item.FoodType),
			OriginalPrice:          int64(item.OriginalPrice),
		}
		// 规格
		if food.FoodType == models.RestaurantFoodsTypeSpec {
			SpecID, err := s.SaveFoodSpec(food.RestaurantID, food.ID, item.OptionIds)
			if err != nil {
				return err
			}
			sec.SpecID = SpecID
			spec := s.GetFoodSpecByID(SpecID)
			sec.OriginalPrice = tools.ToInt64(spec.Price)
		}
		err := tools.GetDB().Create(&sec).Error
		if err != nil {
			return err
		}
		// 加价处理
		if item.PriceMarkupID > 0 {
			sec.PriceMarkupID = int64(markup.ID)
			// 创建加价记录
			err = tools.Db.Create(&models.PriceMarkupSeckillPriceLog{
				PriceMarkupID:            markup.ID,
				SeckillID:            tools.ToInt(sec.ID),
				CityID:     sec.CityId,
				AreaID:                sec.AreaId,
				RestaurantID:          int(sec.RestaurantId),
				FoodID:      int(sec.FoodId),
				SpecID: sec.SpecID,
				Price: int(sec.Price),
				AdminID: admin.ID,
				Count: int(sec.TotalCount),
				CreatedAt:    time.Now(),
			}).Error
			if err != nil {
				return err
			}
		}
		// batchSeckill = append(batchSeckill,sec)
	}
	return nil
}

// GetSeckillList
//
// @Description: 根据参数获取秒杀列表
// @Author: Rixat
// @Time: 2024-08-26 19:05:47
// @receiver 
// @param c *gin.Context
func (s SeckillService) Edit(params seckillRequests.CmsSeckillEditRequest,admin models.Admin) error  {
	var  secItem models.Seckill
	tools.Db.Model(secItem).Where("id =?",params.ID).Find(&secItem)
	if secItem.ID == 0 {
		return errors.New("not_found")
	}
	if params.UserMaxOrderCount == 0 {
		params.UserMaxOrderCount = params.TotalCount
	}
	if params.State == 1 &&  secItem.FoodType == models.RestaurantFoodsTypeSpec {
		if !s.CheckChangeStateSpecInfo(secItem.SpecID, tools.ToInt(secItem.FoodId)) {
			return errors.New("spec_info_has_changed")
		}
	}
	sec := &models.Seckill{
		AdminId:              int64(admin.ID),
		FoodId:               int64(params.FoodID),
		RestaurantId:         int64(params.RestaurantID),
		BeginTime:            params.BeginTime,
		EndTime:              params.EndTime,
		TotalCount:           int64(params.TotalCount),
		UserMaxOrderCount:    int64(params.UserMaxOrderCount),
		Price:                int64(params.Price),
		Order:                int64(params.Order),
		CreatedAt:            time.Now(),
		State:                params.State,
		SpecID:               params.SpecID,
		FoodType: uint8(params.FoodType),
	}
	multipleDiscount,foodName :=s.CheckFoodsMultipleDiscount(s.langUtil.Lang,params.FoodID,carbon.Parse(params.BeginTime,configs.AsiaShanghai).Format("Y-m-d H:i:s"),carbon.Parse(params.EndTime,configs.AsiaShanghai).Format("Y-m-d H:i:s"))
	if multipleDiscount.ID > 0 {
		//存在正在进行的多分打折活动 不允许创建
		return fmt.Errorf(s.langUtil.T("food_has_multiple_discount"),foodName)
	}
	if params.PriceMarkupID > 0 {
			var markup  models.PriceMarkupFood
			tools.Db.Model(markup).Where("id = ?",params.PriceMarkupID).Find(&markup)
			if markup.ID == 0 {
				return errors.New("not_found")
			}
			if markup.State!= 3 {
				return errors.New("state_error")
			}
			// 验证活动时间
			if carbon.Time2Carbon(*markup.StartDate).Gt(carbon.Parse(sec.BeginTime)) || carbon.Time2Carbon(*markup.EndDate).Lt(carbon.Parse(sec.EndTime)){
				return errors.New("time_enable_out_price_markup_time_range")

			}
			// 验证库存数量
			if markup.CanUseCount()+int(secItem.TotalCount) < params.TotalCount {
				return errors.New("total_count_over_price_markup_total_count")
			}

			sec.PriceMarkupID = int64(markup.ID)
			// 创建加价记录
			tools.Db.Create(&models.PriceMarkupSeckillPriceLog{
				PriceMarkupID:            markup.ID,
				CityID:     secItem.CityId,
				AreaID:                secItem.AreaId,
				RestaurantID:          int(sec.RestaurantId),
				FoodID:      int(sec.FoodId),
				SpecID: sec.SpecID,
				Price: int(sec.Price),
				Count: int(sec.TotalCount),
				AdminID: admin.ID,
				CreatedAt:    time.Now(),
			})

	}
	err := tools.GetDB().Where("id=?",params.ID).UpdateColumns(sec).Error
	if err != nil {
		return err
	}
	return nil
}

// ValidSeckillSameFoods
//
// @Description: 验证同一个美食添加的秒杀
// @Author: Rixat
// @Time: 2024-08-28 10:12:28
// @receiver 
// @param c *gin.Context
func (s SeckillService) ValidSeckillSameFoods(items []seckillRequests.CmsSeckillItem) error{
	db := tools.GetDB()
	// 验证同一美食
	firstItem := items[0]
	var food models.RestaurantFoods
	db.Model(food).Where("id = ?",firstItem.FoodID).Preload("Restaurant").First(&food)
	if food.ID == 0 {
		return errors.New("food_not_exist")
	}
	if food.State == 0 {
		return errors.New("food_state_error")
	}
	if food.Restaurant.State == 0 {
		return errors.New("restaurant_state_error")
	}
	if food.RestaurantID != firstItem.RestaurantID {
		return errors.New("food_not_exist")
	}
	// 查询该美食还没结束的秒杀和特价
	for k,item := range items {
		// 规格
		if item.FoodType != food.FoodType {
			return errors.New("food_spec_type_error")
		}
		if item.FoodType == models.RestaurantFoodsTypeSpec && len(item.OptionIds) == 0 {
			return errors.New("spec_food_no_options")
		}
		// 验证秒杀价格
		if item.Price <= 0 {
			return errors.New("seckill_price_error")
		}
		if food.FoodType == models.RestaurantFoodsTypeSpec{
			var specPrice int64
			tools.Db.Model(models.FoodSpecOption{}).Select("IFNULL(SUM(price), 0) AS spec_price").Where("id in ?",item.OptionIds).Scan(&specPrice)
			// 秒杀价格必须小于美食价格
			if item.Price > tools.ToInt(specPrice) {
				return errors.New("seckill_price_error")
			}
		}else{
			// 秒杀价格必须小于美食价格
			if item.Price > tools.ToInt(food.Price) {
				return errors.New("seckill_price_error")
			}
		}
		// 秒杀时间必须美食正常营业时间范围内
		if !tools.TimeLineInTimeLine(carbon.Parse(item.BeginTime).Format("H:i:s"),carbon.Parse(item.EndTime).Format("H:i:s"),food.BeginTime,food.EndTime) {
			return errors.New("seckill_time_not_in_foods_time_range")
		}
		// 秒杀最多购买数量小于秒杀库存
		if item.UserMaxOrderCount > item.TotalCount {
			return errors.New("seckill_max_order_count_error")
		}
		// 秒杀时间必须大于当前时间
		if carbon.Parse(item.BeginTime).Lt(carbon.Now()){
			return errors.New("seckill_time_must_after_now_time")
		}
		// 秒杀开始时间不能早已当前时间
		if carbon.Parse(item.OrderTime).Lte(carbon.Parse(item.BeginTime)){
			return errors.New("seckill_order_time_must_after_end_time")
		}

		// 验证是否存在重复时间段
		for index,value := range items {
			// 通元素不对比
			if k == index{
				continue
			}
			// 存在重复时间端
			if value.BeginTime == item.BeginTime{
				return errors.New("same_time_error")
			}
		}

		
	}

	// 验证是否存在时间冲突同类活动
	var seckillList []models.Seckill
	queryConfilict := db.Model(seckillList).
		Where("food_id = ?",firstItem.FoodID).
		Where("state = ?",1).
		Where("end_time > ?",carbon.Now().Format("Y-m-d H:i:s"))
	if food.FoodType == models.RestaurantFoodsTypeSpec {
		specID := s.GetSpecIdByOptions(firstItem.FoodID, firstItem.OptionIds)
		queryConfilict.Where("spec_id = ?",specID)
	}

	queryConfilict.Scan(&seckillList)
	for _,item := range items {
		for _,seckill := range seckillList {
			// 验证是否存在同一个时间段的秒杀活动或特价活动
			if carbon.Parse(item.BeginTime).Eq(carbon.Parse(seckill.BeginTime)){
				if seckill.Type == 1 {
					msg := fmt.Sprintf(s.langUtil.T("exist_conflict_seckill"),carbon.Parse(seckill.BeginTime).Format("Y-m-d H:i:s"))
					return  errors.New(msg)
				}else{
					msg := fmt.Sprintf(s.langUtil.T("exist_conflict_special"),carbon.Parse(seckill.BeginTime).Format("Y-m-d H:i:s"))
					return  errors.New(msg)
				}
			}
		}
	}
	return nil
}

// ValidSeckillDifferentFoods
//
// @Description: 验证不同美食添加的秒杀
// @Author: Rixat
// @Time: 2024-08-28 10:12:25
// @receiver 
// @param c *gin.Context
func (s SeckillService) ValidSeckillDifferentFoods(items []seckillRequests.CmsSeckillItem) error{
	db := tools.GetDB()
	// 查询该美食还没结束的秒杀和特价
	for _,item := range items {
		// 验证同一美食
		var food models.RestaurantFoods
		db.Model(food).Where("id = ?",item.FoodID).First(&food)
		if food.ID == 0 {
			return errors.New("food_not_exist")
		}
		if food.State == 0 {
			return errors.New("food_not_exist")
		}
		if food.RestaurantID != item.RestaurantID {
			return errors.New("food_not_exist")
		}
		// 验证秒杀价格
		if item.Price <= 0 {
			return errors.New("seckill_price_error")
		}
		if food.FoodType == models.RestaurantFoodsTypeSpec {
			var specPrice int64
			tools.Db.Model(models.FoodSpecOption{}).Select("sum(price) as spec_price").Where("id in ?",item.OptionIds).Scan(&specPrice)
			// 秒杀价格必须小于美食价格
			if item.Price > tools.ToInt(specPrice) {
				return errors.New("seckill_price_error")
			}
		}else{
			// 秒杀价格必须小于美食价格
			if item.Price > tools.ToInt(food.Price) {
				return errors.New("seckill_price_error")
			}
		}
		
		// 秒杀时间必须美食正常营业时间范围内
		if !tools.TimeLineInTimeLine(carbon.Parse(item.BeginTime).Format("H:i:s"),carbon.Parse(item.EndTime).Format("H:i:s"),food.BeginTime,food.EndTime) {
			return errors.New("seckill_time_not_in_foods_time_range")
		}
		// 秒杀最多购买数量小于秒杀库存
		if item.UserMaxOrderCount <= item.TotalCount {
			return errors.New("seckill_max_order_count_error")
		}
		// 秒杀时间必须大于当前时间
		if carbon.Parse(item.BeginTime).Lt(carbon.Now()){
			return errors.New("seckill_max_order_count_error")
		}

		// 规格
		if item.FoodType != food.FoodType {
			return errors.New("food_spec_type_error")
		}
		if item.FoodType == models.RestaurantFoodsTypeSpec && len(item.OptionIds) == 0 {
			return errors.New("food_spec_type_error")
		}

		// 验证是否存在时间冲突同类活动
		var seckillList []models.Seckill
		db.Model(seckillList).
			Where("food_id = ?",item.FoodID).
			Where("state = ?",1).
			Where("end_time > ?",carbon.Now().Format("Y-m-d H:i:s")).Scan(&seckillList)
		for _,item := range items {
			for _,seckill := range seckillList {
				// 验证是否存在同一个时间段的秒杀活动或特价活动
				if carbon.Parse(item.BeginTime).Eq(carbon.Parse(seckill.BeginTime)){
					if seckill.Type == 1 {
						msg := fmt.Sprintf(s.langUtil.T("exist_conflict_seckill"),carbon.Parse(seckill.BeginTime).Format("Y-m-d H:i:s"))
						return  errors.New(msg)
					}else{
						msg := fmt.Sprintf(s.langUtil.T("exist_conflict_special"),carbon.Parse(seckill.BeginTime).Format("Y-m-d H:i:s"))
						return  errors.New(msg)
					}
				}
				
			}
		}
	}
	return nil
}




// ValidSeckillDifferentFoods
//
// @Description: 验证不同美食添加的秒杀
// @Author: Rixat
// @Time: 2024-08-28 10:12:25
// @receiver 
// @param c *gin.Context
func (s SeckillService) ValidEditSeckillParams(params seckillRequests.CmsSeckillEditRequest) error{
	db := tools.GetDB()
	// 查询该美食还没结束的秒杀和特价
	// 验证同一美食
	var food models.RestaurantFoods
	db.Model(food).Where("id = ?",params.FoodID).First(&food)
	if food.ID == 0 {
		return errors.New("food_not_exist")
	}
	if food.State == 0 {
		return errors.New("food_not_exist")
	}
	if food.RestaurantID != params.RestaurantID {
		return errors.New("food_not_exist")
	}
	// 验证秒杀价格
	if params.Price <= 0 {
		return errors.New("seckill_price_error")
	}
	// 秒杀价格必须小于美食价格
	if params.Price > tools.ToInt(food.Price) {
		return errors.New("seckill_price_error")
	}
	// 秒杀时间必须美食正常营业时间范围内
	if !tools.TimeLineInTimeLine(carbon.Parse(params.BeginTime).Format("H:i:s"),carbon.Parse(params.EndTime).Format("H:i:s"),food.BeginTime,food.EndTime) {
		return errors.New("seckill_time_not_in_foods_time_range")
	}
	// 秒杀最多购买数量小于秒杀库存
	if params.UserMaxOrderCount > params.TotalCount {
		return errors.New("seckill_max_order_count_error")
	}
	// 秒杀时间必须大于当前时间
	if carbon.Parse(params.BeginTime).Lt(carbon.Now()){
		return errors.New("seckill_time_must_after_now_time")
	}
	// 验证是否存在时间冲突同类活动
	var seckillList []models.Seckill
	db.Model(seckillList).
		Where("food_id = ?",params.FoodID).
		Where("state = ?",1).
		Where("end_time > ? and id <> ?",carbon.Now().Format("Y-m-d H:i:s"),params.ID).Scan(&seckillList)
	for _,seckill := range seckillList {
		// 验证是否存在同一个时间段的秒杀活动或特价活动
		if carbon.Parse(params.BeginTime).Eq(carbon.Parse(seckill.BeginTime)){
			if seckill.Type == 1 {
				msg := fmt.Sprintf(s.langUtil.T("exist_conflict_seckill"),carbon.Parse(seckill.BeginTime).Format("Y-m-d H:i:s"))
				return  errors.New(msg)
			}else{
				msg := fmt.Sprintf(s.langUtil.T("exist_conflict_special"),carbon.Parse(seckill.BeginTime).Format("Y-m-d H:i:s"))
				return  errors.New(msg)
			}
		}
	}
	return nil
}

// SetMarkupPrice
//
// @Description: 设置秒杀价格阶梯
// @Author: Rixat
// @Time: 2024-11-14 16:37:10
// @receiver 
// @param c *gin.Context
func (s SeckillService) SetMarkupPrice(admin models.Admin,seckillID,price,count int) error{
		var sec models.Seckill
		tools.Db.Model(sec).Where("id =?",seckillID).Find(&sec)
		if sec.ID == 0 {
			return errors.New("seckill_not_exist")
		}
		if carbon.Parse(sec.EndTime).Lte(carbon.Now()){
			return errors.New("ended_active_enable_edit")
		}
		// 加锁
		

		// 更新其他阶梯状态0
		err := tools.Db.Model(models.PriceMarkupSeckillPriceLog{}).Where("seckill_id =?",seckillID).Update("state",0).Error
		if err!= nil {
			return errors.New("fail")
	   }
		// 创建加价记录
		err = tools.Db.Create(&models.PriceMarkupSeckillPriceLog{
			PriceMarkupID:            int(sec.PriceMarkupID),
			SeckillID:            int(sec.ID),
			CityID:     sec.CityId,
			AreaID:                sec.AreaId,
			RestaurantID:          int(sec.RestaurantId),
			FoodID:      int(sec.FoodId),
			Price: price,
			Count:  count,
			AdminID: admin.ID,
			State: 1,
			CreatedAt:    time.Now(),
		}).Error
		if err!= nil {
			 return errors.New("fail")
		}
		// 更新秒杀
		err = tools.Db.Model(models.Seckill{}).Where("id =?",seckillID).UpdateColumns(
			map[string]interface{}{
				"total_count":  int64(count),
				"price": tools.ToInt64(price),
				"saled_count":0,
				"updated_at": time.Now(),
		}).Error 
		if err!= nil {
			return errors.New("fail")
	   }
	   // 删除redis锁
	   seck_kill_key_saled := "lumen_database_skill_stock_id_" + tools.ToString(sec.ID)
	   seck_kill_key_saled_key := "lumen_database_skill_stock_id_" + tools.ToString(sec.ID)
	   redisHelper := tools.GetRedisHelper()
	   redisHelper.Del(context.Background(), seck_kill_key_saled)
	   redisHelper.Del(context.Background(), seck_kill_key_saled_key)
	   return nil
}

func (s SeckillService) GetMarkupStepInfo(seckillID int)( map[string]interface{},error){
	var sec models.Seckill
	tools.Db.Model(sec).Where("id =?",seckillID).
		Preload("Area").
		Preload("City").
		Preload("Restaurant").
		Preload("RestaurantFoods").
		Preload("PriceMarkupFood").
		Preload("SelectedSpec.FoodSpecOptions").
		Preload("PriceMarkupSeckillPriceLog.PriceMarketSeckillPriceLogSum", func(db *gorm.DB) *gorm.DB {
			return db.Select("seckill_price_log_id,IFNULL(sum(saled_count),0) as saled_count,IFNULL(sum(saled_count*price),0) as price,min(created_at) as begin_sell_time,max(created_at) as end_sell_time").
				Where("state in (1,2,3)").
				Group("seckill_price_log_id") 
		}).
		Find(&sec)
	if sec.ID == 0 {
		return nil, errors.New("seckill_not_exist")
	}
	res := make(map[string]interface{})
	res["restaurant_name"]  = tools.GetNameByLang(sec.Restaurant,s.language)
	res["food_name"]  = tools.GetNameByLang(sec.RestaurantFoods,s.language)
	if sec.SpecID > 0 && sec.SelectedSpec.ID > 0 {
		res["spec_options"] = sec.SelectedSpec.GetOptions(*sec.SelectedSpec, s.language)
	}
	res["saled_count"]  = sec.SaledCount
	res["remain_count"]  = sec.TotalCount - sec.SaledCount
	res["in_price"]  = sec.PriceMarkupFood.InPrice
	res["price"]  = sec.RestaurantFoods.Price
	res["step"]  = make([]map[string]interface{},0)
	// markupSeckillPriceSteps := sec.PriceMarkupSeckillPriceLog
	for _,item := range sec.PriceMarkupSeckillPriceLog {
		step := make(map[string]interface{})
		step["price"]  = item.Price
		step["start_time"]  = carbon.Time2Carbon(item.CreatedAt).Format("Y-m-d H:i:s")
		step["end_time"]  = carbon.Time2Carbon(*item.UpdatedAt).Format("Y-m-d H:i:s")
		if item.PriceMarketSeckillPriceLogSum.SeckillPriceLogID > 0{
			step["start_time"] = tools.TimeFormatYmdHis(&item.PriceMarketSeckillPriceLogSum.BeginSellTime)
			step["end_time"] = tools.TimeFormatYmdHis(&item.PriceMarketSeckillPriceLogSum.EndSellTime)
		}
		step["in_price"] =  sec.PriceMarkupFood.InPrice
		step["profit"] =  (item.Price - sec.PriceMarkupFood.InPrice)*item.PriceMarketSeckillPriceLogSum.SaledCount
		step["count"]  = item.Count
		step["saled_count"] =  item.PriceMarketSeckillPriceLogSum.SaledCount
		res["step"] = append(res["step"].([]map[string]interface{}),step)
	}
	return res,nil
}