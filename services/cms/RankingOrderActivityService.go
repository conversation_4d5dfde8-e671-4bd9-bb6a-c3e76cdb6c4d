package cms

import (
	"errors"
	"mulazim-api/lang"
	"mulazim-api/models"
	rankingorderactivity "mulazim-api/requests/cms/rankingOrderActivity"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RankingOrderActivityService struct {
	langUtil *lang.LangUtil
	language string
}

func NewRankingOrderActivityService(c *gin.Context) *RankingOrderActivityService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := RankingOrderActivityService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &service
}

// CreateRankingOrderActivity
//
//	@Description: 创建 排行订单活动
//	@author: Captain
//	@Time: 2025-02-27 15:04:05
//	@param params rankingorderactivity.RankingOrderActivityCreateRequest
//	@param admin models.Admin
//	@param IsAgent bool
//	@return error
func (r *RankingOrderActivityService) CreateRankingOrderActivity(params rankingorderactivity.RankingOrderActivityCreateRequest, admin models.Admin, IsAgent bool) error {
	db := tools.GetDB()
	// 判断参数
	// 修复 IsPlatformActivity 类型问题
	var isPlatform int
	if IsAgent {
		isPlatform = models.LotteryActivityIsNotPlatform
	} else {
		isPlatform = models.LotteryActivityIsPlatform
	}

	// 修复 AdminID 类型问题
	adminID := admin.ID
	AdminCityID := admin.AdminCityID
	AdminAreaID := admin.AdminAreaID
	// params.AnnounceBeginTime  格式是 2006-01-02 15:04:05
	loc, _ := time.LoadLocation("Asia/Shanghai")
	announceBeginTime, _ := time.ParseInLocation(time.DateTime, params.AnnounceBeginTime, loc)
	startTime, _ := time.ParseInLocation(time.DateTime, params.StartTime, loc)
	endTime, _ := time.ParseInLocation(time.DateTime, params.EndTime, loc)
	//  endTime +  params.ResultShowEndTime 天
	resultShowEndTime := endTime.AddDate(0, 0, params.ResultShowEndTime)
	//预热时间  要小于 开始时间  结果显示时间要大于结束时间
	if announceBeginTime.After(startTime) || resultShowEndTime.Before(endTime) {
		// 预热时间要小于活动开始时间
		return errors.New("ranking_order_activity_time_error")
	}

	// 判断 奖品列表 是否正确
	indexMap := make(map[int]bool) // 用于检查所有奖品的序号是否重复

	for _, prize := range params.PrizeList {
		if prize.Indexs == "" {
			return errors.New("ranking_order_activity_prize_list_empty")
		}
		
		// 判断 奖品列表 是否正确
		prizeIndexs := strings.Split(prize.Indexs, ",")
		for _, prizeIndex := range prizeIndexs {
			index := tools.ToInt(prizeIndex)
			if index <= 0 {
				return errors.New("ranking_order_activity_prize_list_error")
			}
			
			// 检查序号是否已存在于全局map中
			if indexMap[index] {
				return errors.New("ranking_order_activity_prize_index_duplicate")
			}
			indexMap[index] = true
		}
	}
	// 判断奖品ID 是不是重复
	prizeIDMap := make(map[int]bool)
	for _, prize := range params.PrizeList {
		if prizeIDMap[prize.Id] {
			return errors.New("ranking_order_activity_prize_id_duplicate")
		}
		prizeIDMap[prize.Id] = true
	}
	// 判断活动是不是有时间交叉 有时间交叉就返回 ,平台活动可以跟地方活动时间交叉 
	var activityList []models.LotteryActivity
	tx := db.Model(&models.LotteryActivity{}).
		Where("is_platform_activity = ?", isPlatform).
		Where("type = ?", models.LotteryActivityTypeOrderRanking).
		Where("state = ?", models.LotteryActivityStateOpen).
		Where("(start_time BETWEEN ? AND ?) OR (end_time BETWEEN ? AND ?) OR (start_time <= ? AND end_time >= ?)",
			startTime, endTime, 
			startTime, endTime,
			startTime, endTime)
	if isPlatform == models.LotteryActivityIsNotPlatform {
		tx = tx.Where("city_id = ?", AdminCityID).Where("area_id = ?", AdminAreaID)
	}
	tx.First(&activityList)
	if len(activityList) > 0 {
		return errors.New("ranking_order_activity_time_cross")
	}
		// 创建  t_lottery_activity
	lotteryActivity := models.LotteryActivity{
		CityID:                  tools.If(IsAgent, &AdminCityID, nil),
		AreaID:                  tools.If(IsAgent, &AdminAreaID, nil),
		IsPlatformActivity:      &isPlatform,
		Type:                    models.LotteryActivityTypeOrderRanking, // 2: 订单排行榜活动
		NameUg:                  params.NameUg,
		NameZh:                  params.NameZh,
		AnnounceBeginTime:       &announceBeginTime,
		StartTime:               &startTime,
		EndTime:                 &endTime,
		ResultShowEndTime:       &resultShowEndTime,
		State:                   params.State,
		AdminID:                 &adminID,
		RuleUg:                  params.RuleUg,
		RuleZh:                  params.RuleZh,
		ShareCoverImages:      params.ShareCoverImages,
		ShareWinnerImageUg:      params.ShareWinnerImageUg,
		ShareWinnerImageZh:      params.ShareWinnerImageZh,
		AnnounceEntranceImageUg: params.AnnounceEntranceImageUg,
		AnnounceEntranceImageZh: params.AnnounceEntranceImageZh,
		AnnouncePageImageUg:     params.AnnouncePageImageUg,
		AnnouncePageImageZh:     params.AnnouncePageImageZh,
	}

	if err := db.Create(&lotteryActivity).Error; err != nil {
		tools.Logger.Errorf("创建  t_lottery_activity 失败: %v", err)
		return errors.New("create_fail")
	}



	// 再创建  t_lottery_activity_level
	lotteryActivityLevel := models.LotteryActivityLevel{
		LotteryActivityID: lotteryActivity.ID,
	}
	if err := db.Create(&lotteryActivityLevel).Error; err != nil {
		tools.Logger.Errorf("创建  t_lottery_activity_level 失败: %v", err)
		return errors.New("create_fail")
	}


	// 再循环创建  t_lottery_activity_level_prize
	for _, prize := range params.PrizeList {
		prizeIndexs := strings.Split(prize.Indexs, ",")
		for _, prizeIndex := range prizeIndexs {
			// 依次创建  t_lottery_activity_level_prize
			lotteryActivityLevelPrize := models.LotteryActivityLevelPrize{
				LotteryID:              lotteryActivity.ID,
				LotteryActivityLevelID: lotteryActivityLevel.ID,
				LuckyUserIndex:         tools.ToInt(prizeIndex),
				AdminID:                admin.ID,
				PrizeID:                prize.Id,
			}

			if err := db.Create(&lotteryActivityLevelPrize).Error; err != nil {
				tools.Logger.Errorf("创建  t_lottery_activity_level_prize 失败: %v", err)
				return errors.New("create_fail")
			}
		}
	}

	return nil

}

// UpdateRankingOrderActivity
//
//	@Description: 修改 排行订单活动
//	@Time: 2025-02-27 15:04:05
//	@param params rankingorderactivity.RankingOrderActivityUpdateRequest
//	@param admin models.Admin
//	@param IsAgent bool
//	@return error
func (r *RankingOrderActivityService) UpdateRankingOrderActivity(params rankingorderactivity.RankingOrderActivityUpdateRequest, admin models.Admin, IsAgent bool) error {
	db := tools.GetDB()
	
	// 获取现有活动
	var activity models.LotteryActivity
	if err := db.Where("id = ?", params.ID).Preload("LotteryActivityLevel").First(&activity).Error; err != nil {
		return errors.New("not_found")
	}

	var changeCount int64
	db.Model(models.LotteryChance{}).Where("lottery_activity_id = ?", params.ID).Count(&changeCount)
	if changeCount > 0 {
		tools.Logger.Errorf("已存在活动使用记录,不能删除 ID:%d", params.ID)
		return errors.New("activity_has_record_can_not_delete")
	}

	// 判断活动是否已经开始 并且 判断状态
	if activity.StartTime.Before(time.Now()) && activity.State == models.LotteryActivityStateOpen {
		tools.Logger.Errorf("活动已经开始 ID:%d", params.ID)
		return errors.New("activity_already_started_can_not_update")
	}

	// 时间参数处理
	loc, _ := time.LoadLocation("Asia/Shanghai")
	announceBeginTime, _ := time.ParseInLocation(time.DateTime, params.AnnounceBeginTime, loc)
	startTime, _ := time.ParseInLocation(time.DateTime, params.StartTime, loc)
	endTime, _ := time.ParseInLocation(time.DateTime, params.EndTime, loc)
	resultShowEndTime := endTime.AddDate(0, 0, params.ResultShowEndTime)

	// 时间校验
	if announceBeginTime.After(startTime) || resultShowEndTime.Before(endTime) {
		return errors.New("ranking_order_activity_time_error")
	}

	// 判断是否是平台活动
	var isPlatform int
	if IsAgent {
		isPlatform = models.LotteryActivityIsNotPlatform
	} else {
		isPlatform = models.LotteryActivityIsPlatform
	}

	// 判断是否时间交叉
	var conflictCount int64
	tx := db.Model(&models.LotteryActivity{}).
		Where("id != ?", params.ID).
		Where("is_platform_activity = ?", isPlatform).
		Where("type = ?", models.LotteryActivityTypeOrderRanking).
		Where("state = ?", models.LotteryActivityStateOpen).
		Where("(start_time BETWEEN ? AND ?) OR (end_time BETWEEN ? AND ?) OR (start_time <= ? AND end_time >= ?)",
			startTime, endTime, 
			startTime, endTime,
			startTime, endTime)

	// 判断是否是平台活动
	if isPlatform == models.LotteryActivityIsNotPlatform {
		tx = tx.Where("city_id = ? AND area_id = ?", admin.AdminCityID, admin.AdminAreaID)
	}

	// 判断是否时间交叉
	if err := tx.Count(&conflictCount).Error; err != nil || conflictCount > 0 {
		return errors.New("ranking_order_activity_time_cross")
	}


	// 修改新奖品前先检查所有序号是否有重复
	indexMap := make(map[int]bool)

	for _, prize := range params.PrizeList {
		prizeIndexs := strings.Split(prize.Indexs, ",")
		for _, prizeIndex := range prizeIndexs {
			index := tools.ToInt(prizeIndex)
			if index <= 0 {
				return errors.New("ranking_order_activity_prize_list_error")
			}

			// 检查序号是否重复
			if indexMap[index] {
				return errors.New("ranking_order_activity_prize_index_duplicate")
			}
			indexMap[index] = true
		}
	}

	// 判断奖品ID 是不是重复
	prizeIDMap := make(map[int]bool)
	for _, prize := range params.PrizeList {
		if prizeIDMap[prize.Id] {
			return errors.New("ranking_order_activity_prize_id_duplicate")
		}
		prizeIDMap[prize.Id] = true
	}
	// 更新活动主体信息
	updateData := map[string]interface{}{
		"name_ug":                   params.NameUg,
		"name_zh":                   params.NameZh,
		"announce_begin_time":       announceBeginTime,
		"start_time":                startTime,
		"end_time":                  endTime,
		"result_show_end_time":      resultShowEndTime,
		"state":                     params.State,
		"rule_ug":                   params.RuleUg,
		"rule_zh":                   params.RuleZh,
		"share_cover_images":        params.ShareCoverImages,
		"share_winner_image_ug":    params.ShareWinnerImageUg,
		"share_winner_image_zh":    params.ShareWinnerImageZh,
		"announce_entrance_image_ug": params.AnnounceEntranceImageUg,
		"announce_entrance_image_zh": params.AnnounceEntranceImageZh,
		"announce_page_image_ug":    params.AnnouncePageImageUg,
		"announce_page_image_zh":    params.AnnouncePageImageZh,
	}
	// 获取活动等级id
	lotteryActivityLevelID := activity.LotteryActivityLevel[0].ID
	// 更新主表
	if err := db.Model(&models.LotteryActivity{}).Where("id = ?", params.ID).Updates(updateData).Error; err != nil {
		tools.Logger.Errorf("更新活动失败: %v", err)
		return errors.New("update_failed")
	}

	// 删除原有奖品等级
	if err := db.Where("lottery_id = ?", params.ID).Where("lottery_activity_level_id = ?", lotteryActivityLevelID).Delete(&models.LotteryActivityLevelPrize{}).Error; err != nil {
		tools.Logger.Errorf("删除旧奖品失败: %v", err)
		return errors.New("update_failed")
	}

	

	// 序号检查通过后,再创建奖品记录
	for _, prize := range params.PrizeList {
		prizeIndexs := strings.Split(prize.Indexs, ",")
		for _, prizeIndex := range prizeIndexs {
			prizeRecord := models.LotteryActivityLevelPrize{
				LotteryID:              params.ID,
				LotteryActivityLevelID: lotteryActivityLevelID,
				LuckyUserIndex:         tools.ToInt(prizeIndex),
				AdminID:                admin.ID,
				PrizeID:                prize.Id,
			}

			if err := db.Create(&prizeRecord).Error; err != nil {
				tools.Logger.Errorf("创建奖品失败: %v", err)
				return errors.New("update_failed")
			}
		}
	}
	return nil
}


// ListRankingOrderActivity
//
//	@Description: 获取排行订单活动列表
//	@Time: 2025-02-28 10:04:05
//	@param params rankingorderactivity.RankingOrderActivityListRequest
//	@return []models.LotteryActivityOrderRanking,int64,error
func (r *RankingOrderActivityService) ListRankingOrderActivity(params rankingorderactivity.RankingOrderActivityListRequest, isAgent bool) ([]models.LotteryActivityOrderRanking, int64, error) {
	db := tools.GetDB()
	
	// 构建基础查询
	query := db.Model(&models.LotteryActivityOrderRanking{}).
		Where("t_lottery_activity.type = ?", models.LotteryActivityTypeOrderRanking).
		Preload("Admin").
		Preload("Area")

	// 条件筛选
	if isAgent {
		// 代理只能看到非平台活动
		query = query.Where("t_lottery_activity.is_platform_activity = ?", models.LotteryActivityIsNotPlatform)
	} else if params.IsPlatform != nil {		
		query = query.Where("t_lottery_activity.is_platform_activity = ?", *params.IsPlatform)
	}
	if params.State != nil && *params.State != 0 {
		query = query.Where("t_lottery_activity.state = ?", params.State)
	}
	if params.BeginTime != nil && params.EndTime != nil && *params.BeginTime != "" && *params.EndTime != "" {
		query = query.Where("t_lottery_activity.start_time >= ? AND t_lottery_activity.end_time <= ?", *params.BeginTime, *params.EndTime)
	}
	if params.Search != nil && *params.Search != "" {
		query = query.Where("t_lottery_activity.name_ug LIKE ? OR t_lottery_activity.name_zh LIKE ?", "%"+*params.Search+"%", "%"+*params.Search+"%")
	}
	if params.CityId != nil && *params.CityId != 0 {
		query = query.Where("t_lottery_activity.city_id = ?", *params.CityId)
	}
	if params.AreaId != nil && *params.AreaId != 0 {
		query = query.Where("t_lottery_activity.area_id = ?", *params.AreaId)
	}
	
	// 获取总数
	var total int64
	query.Count(&total)

	// 构建查询字段
	selectQuery := `
		t_lottery_activity.*,
		-- 参与人数
		(SELECT COUNT(DISTINCT user_id) FROM t_lottery_chance 
			WHERE t_lottery_activity.id = t_lottery_chance.lottery_activity_id 
			AND t_lottery_chance.type = ? AND t_lottery_chance.deleted_at IS NULL) as attend_count,
		-- 订单数量
		(SELECT COUNT(*) FROM t_lottery_chance 
			WHERE t_lottery_activity.id = t_lottery_chance.lottery_activity_id 
			AND t_lottery_chance.type = ? AND t_lottery_chance.deleted_at IS NULL) as order_count,
		-- 总订单金额
		(SELECT ROUND(COALESCE(SUM(user_order_price), 0) / 100.0, 2) FROM t_lottery_chance 
			WHERE t_lottery_activity.id = t_lottery_chance.lottery_activity_id 
			AND t_lottery_chance.type = ? AND t_lottery_chance.deleted_at IS NULL) as total_order_amount
	`

	// 获取列表数据
	var list []models.LotteryActivityOrderRanking
	err := query.
		Select(selectQuery, 
			models.LotteryChanceTypeActivity, 
			models.LotteryChanceTypeActivity, 
			models.LotteryChanceTypeActivity).
		Order("t_lottery_activity.created_at DESC").
		Scopes(scopes.Page(params.Page, params.Limit)).
		Find(&list).Error

	if err != nil {
		tools.Logger.Errorf("获取排行订单活动列表失败 error: %v", err)
		return nil, 0, err
	}

	return list, total, nil
}



// DetailRankingOrderActivity
//
//	@Description: 获取排行订单活动详情
//	@Time: 2025-02-27 15:04:05
//	@param id int
//	@return models.LotteryActivityOrderRanking,error
func (r *RankingOrderActivityService) DetailRankingOrderActivity(id int) (models.LotteryActivityOrderRanking, error) {
	db := tools.GetDB()
	var activity models.LotteryActivityOrderRanking

	// 构建查询字段
	selectQuery := `
		t_lottery_activity.*,
		-- 参与人数
		(SELECT COUNT(DISTINCT user_id) FROM t_lottery_chance 
			WHERE t_lottery_activity.id = t_lottery_chance.lottery_activity_id 
			AND t_lottery_chance.type = ? AND t_lottery_chance.deleted_at IS NULL) as attend_count,
		-- 订单数量
		(SELECT COUNT(*) FROM t_lottery_chance 
			WHERE t_lottery_activity.id = t_lottery_chance.lottery_activity_id 
			AND t_lottery_chance.type = ? AND t_lottery_chance.deleted_at IS NULL) as order_count,
		-- 总订单金额
		(SELECT ROUND(COALESCE(SUM(user_order_price), 0) / 100.0, 2) FROM t_lottery_chance 
			WHERE t_lottery_activity.id = t_lottery_chance.lottery_activity_id 
			AND t_lottery_chance.type = ? AND t_lottery_chance.deleted_at IS NULL) as total_order_amount,
		-- 奖品数量
		(SELECT COUNT(*) FROM t_lottery_activity_level_prize 
			WHERE t_lottery_activity.id = t_lottery_activity_level_prize.lottery_id 
			AND t_lottery_activity_level_prize.deleted_at IS NULL ) as prize_count,
		-- 已发出奖品数量
		(SELECT COUNT(*) FROM t_lottery_chance 
			WHERE t_lottery_activity.id = t_lottery_chance.lottery_activity_id 
			AND t_lottery_chance.deleted_at IS NULL  AND t_lottery_chance.type = ? AND t_lottery_chance.prize_open_state = ? ) as send_prize_count
	`

	err := db.Model(models.LotteryActivityOrderRanking{}).
		Where("id = ?", id).
		Preload("Admin").
		Select(selectQuery, 
			models.LotteryChanceTypeActivity,
			models.LotteryChanceTypeActivity,
			models.LotteryChanceTypeActivity,
			models.LotteryChanceTypeActivity,
			models.LotteryChancePrizeOpenStateDraw).
		First(&activity).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			tools.Logger.Errorf("活动不存在 ID:%d", id)
			return models.LotteryActivityOrderRanking{}, errors.New("not_found")
		}
		tools.Logger.Errorf("查询活动详情失败 ID:%d error:%v", id, err)
		return models.LotteryActivityOrderRanking{}, errors.New("query_fail")
	}

	return activity, nil
}


// GetRankingOrderActivityLikeCount
//
//	@Description: 获取排行订单活动点赞数
//	@Time: 2025-02-27 15:04:05
//	@param id int
//	@return int64,int64,error
func (r *RankingOrderActivityService) GetRankingOrderActivityLikeCount(id int) (int64,int64,error) {
	db := tools.GetDB()
	var likeCount int64
	var dislikeCount int64
	db.Model(&models.LotteryComment{}).Where("lottery_activity_id = ?", id).Where("type = ?", models.LotteryCommentTypeLike).Count(&likeCount)
	db.Model(&models.LotteryComment{}).Where("lottery_activity_id = ?", id).Where("type = ?", models.LotteryCommentTypeDislike).Count(&dislikeCount)
	return likeCount, dislikeCount, nil	
}


// WinnerList
//
//	@Description: 获取活动中奖列表
//	@Time: 2025-02-27 15:04:05
//	@param params rankingorderactivity.RankingOrderActivityWinnerListRequest
//	@return []models.LotteryChance,int64,error
func (r *RankingOrderActivityService) WinnerList(params rankingorderactivity.RankingOrderActivityWinnerListRequest) ([]models.LotteryChance,int64,error) {
	db := tools.GetDB()
	var list []models.LotteryChance
	var total int64
	query := db.Model(&models.LotteryChance{}).
		Where("lottery_activity_id = ?", params.ID).
		Where("type = ?", models.LotteryChanceTypeActivity).
		Where("prize_open_state = ?", models.LotteryChancePrizeOpenStateDraw).
		Preload("UserBuilding").
		Preload("LotteryPrize").
		Preload("City").
		Preload("Area").
		Preload("OrderToday").
		Preload("Order").
		Preload("User").
		Order("created_at DESC")
	err := query.Count(&total).Error
	if err != nil {
		tools.Logger.Errorf("获取活动中奖列表失败 error:%v", err)
		return nil, 0, err
	}
	err = query.Scopes(scopes.Page(params.Page, params.Limit)).Find(&list).Error
	if err != nil {
		tools.Logger.Errorf("获取活动中奖列表失败 error:%v", err)
		return nil, 0, err
	}
	return list, total, nil
}




// ChangeState
//
//	@Description: 修改活动状态
//	@Time: 2025-02-27 15:04:05
//	@param id int
//	@param state int
//	@return error
func (r *RankingOrderActivityService) ChangeState(id int, state int) error {
	db := tools.GetDB()
	// 检查活动是否过期
	var activity models.LotteryActivity
	err := db.Model(&models.LotteryActivity{}).Where("id = ?", id).First(&activity).Error
	if err != nil {
		tools.Logger.Errorf("查询活动失败 ID:%d error:%v", id, err)
		return errors.New("query_failed")
	}
	
	// 如果活动已过期 把状态改成已过期
	if activity.EndTime.Before(time.Now()) {
		state = models.LotteryActivityStateExpired
	}
	
	err = db.Model(&models.LotteryActivity{}).Where("id = ?", id).Update("state", state).Error
	if err != nil {
		tools.Logger.Errorf("修改活动状态失败 ID:%d error:%v", id, err)
		return errors.New("update_failed")
	}
	return nil
}


// DeleteRankingOrderActivity
//
//	@Description: 删除活动
//	@Time: 2025-02-27 15:04:05
//	@param id int
//	@return error
func (r *RankingOrderActivityService) DeleteRankingOrderActivity(id int) error {
	db := tools.GetDB()
	var activity models.LotteryActivity
	err := db.Model(models.LotteryActivity{}).Where("id = ?", id).First(&activity).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			tools.Logger.Errorf("活动不存在 ID:%d", id)
			return errors.New("not_found")
		}
		tools.Logger.Errorf("查询活动失败 ID:%d error:%v", id, err)
		return errors.New("query_failed")
	}
	// 判断活动是否已经开始 并且 判断状态
	if activity.StartTime.Before(time.Now()) {
		tools.Logger.Errorf("活动已经开始 ID:%d", id)
		return errors.New("activity_already_started_can_not_delete")
	}
	if activity.State == models.LotteryActivityStateOpen {
		tools.Logger.Errorf("活动状态为已开始 ID:%d", id)
		return errors.New("activity_state_open_can_not_delete")
	}
	var changeCount int64
	db.Model(models.LotteryChance{}).Where("lottery_activity_id = ?", id).Count(&changeCount)
	if changeCount > 0 {
		tools.Logger.Errorf("已存在活动使用记录,不能删除 ID:%d", id)
		return errors.New("activity_has_record_can_not_delete")
	}
	err = db.Model(models.LotteryActivity{}).Where("id = ?", id).Delete(&activity).Error
	if err != nil {
		tools.Logger.Errorf("删除活动失败 ID:%d error:%v", id, err)
		return errors.New("delete_failed")
	}
	return nil
}


// GetUpdateDetail
//
//	@Description: 获取修改的详细
//	@Time: 2025-02-27 15:04:05
//	@param id int
//	@return models.LotteryActivityOrderRanking,error
func (r *RankingOrderActivityService) GetUpdateDetail(id int) (models.LotteryActivityOrderRanking, error) {
	db := tools.GetDB()
	var activity models.LotteryActivityOrderRanking
	err := db.Model(&models.LotteryActivityOrderRanking{}).
		Preload("LotteryActivityLevel.LotteryActivityLevelPrize.LotteryPrize").
		Where("id = ?", id).First(&activity).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			tools.Logger.Errorf("活动不存在 ID:%d", id)
			return models.LotteryActivityOrderRanking{}, errors.New("not_found")
		}
		tools.Logger.Errorf("查询活动失败 ID:%d error:%v", id, err)
		return models.LotteryActivityOrderRanking{}, errors.New("query_failed")
	}
	return activity, nil
}