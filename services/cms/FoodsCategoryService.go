package cms

import (
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/scopes"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
)

type FoodsCategoryService struct {
	langUtil *lang.LangUtil
	language string
}

func NewFoodsCategoryService(c *gin.Context) *FoodsCategoryService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := FoodsCategoryService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &service
}

// List
//
//	@Author: YaKupJan
//	@Date: 2024-09-19 17:10:52
//	@Description: 美食分类列表
//	@receiver s
//	@param search
//	@return []map[string]interface{}
//	@return int64
func (s FoodsCategoryService) List(pagination tools.Pagination,search,sortColumns string) ([]models.FoodsCategory, int64) {
	var categoryList []models.FoodsCategory
	var total int64

	db := tools.GetDB()
	query := db.Model(models.FoodsCategory{})
	if search != ""{
		query = query.Where("name_"+s.language+" LIKE ?", "%"+search+"%")
	}
	if sortColumns!="" {
		query = query.Order(sortColumns)
	} else {
		// 默认使用权重排序
		query = query.Order("weight")
	}
	query.Count(&total)
	query.Scopes(scopes.Page(pagination.Page,pagination.Limit)).Find(&categoryList)
	return categoryList, total
}

// Create
//
//	@Author: YaKupJan
//	@Date: 2024-09-19 17:11:19
//	@Description: 新增美食分类
//	@receiver s
//	@param admin
//	@param nameZh
//	@param nameUg
//	@param image
//	@param weight
//	@param state
//	@return error
func (s FoodsCategoryService) Create(admin models.Admin, nameZh string, nameUg string, image string, weight int, state int) error {
	db := tools.Db
	// 检查是否有重复名称
	var existingCategory models.FoodsCategory
	if err := db.Where("name_zh = ? OR name_ug = ?", nameZh, nameUg).First(&existingCategory).Error; err == nil {
		// 获取重复名称
		var existingName string
		if existingCategory.NameZh == nameZh {
			existingName = nameZh
		} else if existingCategory.NameUg == nameUg {
			existingName = nameUg
		}
		// 返回错误信息
		if existingName != "" {
			if s.language == "zh" {
				return fmt.Errorf("美食分类 %s 已存在", existingName)
			} else {
				return fmt.Errorf("تاماق تۈرى %s مەۋجۇت", existingName)
			}
		}
	}
	// 创建新的分类
	newCategory := models.FoodsCategory{
		NameZh:   nameZh,
		NameUg:   nameUg,
		Image:    image,
		Weight:   weight,
		State:    state,
		CreateBy: admin.ID,
	}
	// 使用事务进行创建
	if err := db.Create(&newCategory).Error; err != nil {
		return err
	}
	return nil
}

// Edit
//
//	@Author: YaKupJan
//	@Date: 2024-09-19 17:12:18
//	@Description: 编辑美食分类
//	@receiver s
//	@param admin
//	@param id
//	@param nameZh
//	@param nameUg
//	@param image
//	@param weight
//	@param state
//	@return error
func (s FoodsCategoryService) Edit(admin models.Admin, id int, nameZh string, nameUg string, image string, weight int, state int) error {
	db := tools.Db
	// 检查要编辑的分类是否存在
	var category models.FoodsCategory
	if err := db.First(&category, id).Error; err != nil {
		return fmt.Errorf("数据不存在")
	}
	// 检查是否有重复名称
	var existingCategory models.FoodsCategory
	if err := db.Where("id <> ? AND (name_zh = ? OR name_ug = ?)", id, nameZh, nameUg).First(&existingCategory).Error; err == nil {
		// 获取重复名称
		var existingName string
		if existingCategory.NameZh == nameZh {
			existingName = nameZh
		} else if existingCategory.NameUg == nameUg {
			existingName = nameUg
		}
		// 返回错误信息
		if existingName != "" {
			if s.language == "zh" {
				return fmt.Errorf("美食分类 %s 已存在", existingName)
			} else {
				return fmt.Errorf("تاماق تۈرى %s مەۋجۇت", existingName)
			}
		}
	}
	// 更新分类信息
	category.NameZh = nameZh
	category.NameUg = nameUg
	category.Image = image
	category.Weight = weight
	category.State = state
	category.CreateBy = admin.ID // 更新者信息

	// 保存更新后的分类
	if err := db.Save(&category).Error; err != nil {
		return err
	}
	return nil
}

// Detail
//
//	@Author: YaKupJan
//	@Date: 2024-09-19 17:11:37
//	@Description: 美食分类详情
//	@receiver s
//	@param id
//	@return error
//	@return models.FoodsCategory
func (s FoodsCategoryService) Detail(id int) (error, models.FoodsCategory) {
	db := tools.Db
	var foodsCategory models.FoodsCategory
	if err := db.Model(models.FoodsCategory{}).Where("id = ?", id).First(&foodsCategory).Error; err != nil {
		return err, foodsCategory
	}
	return nil, foodsCategory
}

// FoodsList
//
//  @Author: YaKupJan
//  @Date: 2024-09-20 11:04:43
//  @Description: 获取分组内美食的列表
//  @receiver s
//  @param pagination
//  @param foodsCategoryId
//  @param search
//  @return error
//  @return []map[string]interface{}
//  @return int64
func (s FoodsCategoryService) FoodsList(c *gin.Context, pagination tools.Pagination, foodsCategoryId int, search string) (error, []models.RestaurantFoods, int64) {
	db := tools.GetDB()
	var total int64
	tx := db.Model(&models.RestaurantFoods{}).
		Preload("Restaurant").
		Preload("Restaurant.City").
		Preload("Restaurant.Area").
		Joins("LEFT JOIN t_restaurant_foods_category ON t_restaurant_foods.id = t_restaurant_foods_category.restaurant_foods_id").
		Joins("LEFT JOIN t_restaurant ON t_restaurant_foods.restaurant_id = t_restaurant.id").
		Where("t_restaurant.deleted_at IS NULL").
		Where("t_restaurant.state > 0").
		Where("t_restaurant_foods_category.foods_category_id = ?", foodsCategoryId)
	if search != ""{
		tx = tx.Where("t_restaurant_foods.name_"+s.language+" LIKE ?", "%"+search+"%")
	}
	admin := permissions.GetAdmin(c)
	if permissions.IsAdmin(admin) || permissions.IsAgent(admin){
		var  storeIds []int
		db.Model(models.AdminStore{}).Select("store_id").Where("admin_id = ?",admin.ID).Scan(&storeIds)
		tx = tx.Where("t_restaurant_foods.restaurant_id in ?",storeIds)
	}

	tx.Scopes(scopes.Page(pagination.Page, pagination.Limit))
	var restaurantFoodsList []models.RestaurantFoods
	if err := tx.Count(&total).// 使用 Find 来处理结构体而不是 Scan
		Error; err != nil {
		return err, nil, 0
	}
	if err := tx.Find(&restaurantFoodsList).// 使用 Find 来处理结构体而不是 Scan
		Error; err != nil {
		return err, nil, 0
	}
	return nil, restaurantFoodsList, total
}
