package cms

import (
	"errors"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	shipmentModels "mulazim-api/models/shipment"
	resource "mulazim-api/resources/cms/shipment"
	"mulazim-api/scopes"
	"mulazim-api/tools"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type ShipperIncomeService struct {
	langUtil *lang.LangUtil
	language string
}

func NewShipperIncomeService(c *gin.Context) *ShipperIncomeService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	ShipperIncomeService := ShipperIncomeService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &ShipperIncomeService
}

// Create
//
// @Description: 配送员收入创建
// @Author: Rixat
// @Time: 2023-11-07 02:32:41
// @receiver
// @param c *gin.Context
func (s ShipperIncomeService) Create(admin models.Admin, params resource.ShipperIncome) error {
	income := shipmentModels.ShipperIncome{
		CityID:        params.CityID,
		AreaID:        params.AreaID,
		Type:          params.Type,
		ComplaintType: params.ComplaintType,
		Amount:        params.Amount * -1,
		ShipperID:     params.ShipperID,
		OrderID:       params.OrderID,
		Remark:        params.Remark,
		OperatorID:    admin.ID,
		Date:          carbon.Now(configs.AsiaShanghai).Format("Y-m-d"),
	}

	var shipper models.Admin
	tools.Db.Model(shipper).Where("id = ?", params.ShipperID).First(&shipper)
	if shipper.ID > 0 {
		income.ShipperName = shipper.RealName
		income.ShipperMobile = shipper.Mobile
	}
	var orderToday models.OrderToday
	tools.Db.Model(orderToday).Where("id = ?", params.OrderID).First(&orderToday)
	if orderToday.ID > 0 {
		income.OrderNo = orderToday.OrderID
		income.OrderPrice = tools.ToInt(orderToday.OrderPrice)
	} else {
		var order models.Order
		tools.Db.Model(order).Where("id = ?", params.OrderID).First(&order)
		if order.ID > 0 {
			income.OrderNo = order.OrderID
			income.OrderPrice = tools.ToInt(order.OrderPrice)
		}
	}
	if income.CityID == 0 && income.AreaID == 0 {
		income.CityID = admin.AdminCityID
		income.AreaID = admin.AdminAreaID
	}
	err := tools.Db.Model(income).Create(&income).Error
	if err != nil {
		tools.Logger.Error("FATAL 创建收入记录失败" + err.Error())
		return errors.New("failed")
	}
	return nil
}

// Detail
//
// @Description: 配送员收入详情
// @Author: Rixat
// @Time: 2023-11-07 02:33:05
// @receiver
// @param c *gin.Context
func (s ShipperIncomeService) Detail(ID int) (map[string]interface{}, error) {
	var income shipmentModels.ShipperIncome
	tools.Db.Model(income).Where("id=?", ID).First(&income)
	if income.ID == 0 {
		return nil, errors.New("not_found")
	}
	result := map[string]interface{}{
		"id":          income.ID,
		"city_id":     income.CityID,
		"area_id":     income.AreaID,
		"operator_id": income.OperatorID,
		"shipper_id":  income.ShipperID,
		"order_id":    income.OrderID,
		"template_id": income.TemplateID,
		"type":        income.Type,
		"amount":      income.Amount,
		"remark":      income.Remark,
	}
	return result, nil
}

// List
//
// @Description: 配送员收入列表
// @Author: Rixat
// @Time: 2023-11-07 02:33:23
// @receiver
// @param c *gin.Context
func (s ShipperIncomeService) List(incomeType, page int, limit int, cityId int, areaId int, kw string, startDate string, endDate string, sort string) map[string]interface{} {
	var incomeList []shipmentModels.ShipperIncome
	var totalCount int64
	query := tools.Db.Model(incomeList).Scopes(scopes.CityArea(cityId, areaId))
	if incomeType > 0 {
		query.Where("type = ?", incomeType)
	}
	if len(kw) > 0 {
		query.Where("order_no like ? or shipper_name like ?", "%"+kw+"%", "%"+kw+"%")
	}
	query.Count(&totalCount)
	if totalCount > 0 {
		query.
			Preload("City").
			Preload("Area").
			Scopes(scopes.Page(page, limit)).
			Order(sort).
			Find(&incomeList)
	}
	// 格式化列表内容
	items := make([]map[string]interface{}, 0)
	for _, income := range incomeList {
		items = append(items, map[string]interface{}{
			"id":             income.ID,
			"order_no":       income.OrderNo,
			"city_name":      tools.GetNameByLang(income.City, s.language),
			"area_name":      tools.GetNameByLang(income.Area, s.language),
			"operator_id":    income.OperatorID,
			"shipper_id":     income.ShipperID,
			"shipper_name":   income.ShipperName,
			"order_id":       income.OrderID,
			"template_id":    income.TemplateID,
			"complaint_type": income.ComplaintType,
			"type":           income.Type,
			"amount":         income.Amount,
			"created_at":     tools.TimeFormatYmdHis(&income.CreatedAt),
			"remark":         income.Remark,
		})
	}
	return map[string]interface{}{
		"total": totalCount,
		"items": items,
	}
}

// Header
//
// @Description: 投诉统计
// @Author: Rixat
// @Time: 2023-12-05 07:56:39
// @receiver
// @param c *gin.Context
func (s ShipperIncomeService) Header(incomeType int, cityId int, areaId int, startDate string, endDate string) map[string]interface{} {
	var result map[string]interface{}
	selectRaw := `
		IFNULL(sum( amount ),0) as total_amount,
		COUNT( id ) as total_count,
		COUNT(IF( complaint_type = 1, 1, null )) AS customer_count,
		COUNT(IF( complaint_type = 2, 1, null )) AS restaurant_count
	`
	query := tools.Db.Model(shipmentModels.ShipperIncome{}).Select(selectRaw)
	if cityId > 0 {
		query = query.Where("city_id =?", cityId)
	}
	if areaId > 0 {
		query = query.Where("area_id =?", areaId)
	}
	if len(startDate) > 0 && len(endDate) > 0 {
		query = query.Where("created_at BETWEEN ? AND ?", startDate+" 00:00:00", endDate+" 23:59:59")
	}
	if incomeType > 0 {
		query = query.Where("type =?", incomeType)
	}
	query.Scan(&result)
	return result
}

// GetOrderList
//
// @Description: 订单列表
// @Author: Rixat
// @Time: 2023-12-05 07:57:04
// @receiver
// @param c *gin.Context
func (s ShipperIncomeService) GetOrderList(page int, limit int, cityId int, areaId int, kw string, shipperID int, startDate string, endDate string, sort string) map[string]interface{} {
	orderItems := make([]map[string]interface{}, 0)
	var orderTodayList []models.OrderToday
	var orderList []models.Order
	var totalCount int64
	queryOrderToday := tools.Db.Model(orderTodayList).
		Select("t_order_today.id,t_order_today.order_id,t_order_today.name as customer_name,t_order_today.mobile as customer_mobile,t_order_today.shipper_id,t_admin.real_name as shipper_name,t_order_today.order_price,t_order_today.created_at,b_order_state.name_" + s.language + " as state").
		Joins("left join t_admin on t_admin.id = t_order_today.shipper_id and t_admin.type = 9").
		Joins("left join b_order_state on b_order_state.id = t_order_today.state")
	queryOrder := tools.Db.Model(orderList).
		Select("t_order.id,t_order.order_id,t_order.name as customer_name,t_order.mobile as customer_mobile,t_order.shipper_id,t_admin.real_name as shipper_name,t_order.order_price,t_order.created_at,b_order_state.name_" + s.language + " as state").
		Joins("left join t_admin on t_admin.id = t_order.shipper_id and t_admin.type = 9").
		Joins("left join b_order_state on b_order_state.id = t_order.state")
	if len(startDate) > 0 && len(endDate) > 0 {
		queryOrderToday.Where("t_order_today.created_at between ? and ?", startDate+" 00:00:00", endDate+" 23:59:59")
		queryOrder.Where("t_order.created_at between ? and ?", startDate+" 00:00:00", endDate+" 23:59:59")
	}
	if cityId > 0 {
		queryOrderToday.Where("t_order_today.city_id = ?", cityId)
		queryOrder.Where("t_order.city_id = ?", cityId)
	}
	if areaId > 0 {
		queryOrderToday.Where("t_order_today.area_id = ?", areaId)
		queryOrder.Where("t_order.area_id = ?", areaId)
	}
	if len(kw) > 0 {
		queryOrderToday.Where("t_order_today.order_id = ?", kw)
		queryOrder.Where("t_order.order_id = ?", kw)
	}
	if shipperID > 0 {
		queryOrderToday.Where("t_order_today.shipper_id = ?", shipperID)
		queryOrder.Where("t_order.shipper_id = ?", shipperID)
	}
	var todayOrderCount int64
	var hisOrderCount int64
	queryOrderToday.Count(&todayOrderCount)
	queryOrder.Count(&hisOrderCount)
	totalCount = todayOrderCount + hisOrderCount
	if totalCount > 0 {
		queryOrderToday.Order(sort).Offset((page - 1) * limit).Limit(limit)
		queryOrder.Order(sort).Offset((page - 1) * limit).Limit(limit)
		tools.Db.Raw("(?) union (?)", queryOrderToday, queryOrder).Find(&orderItems)
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": orderItems,
	}
	return result
}

// GetShipperList
//
// @Description: 获取配送员列表
// @Author: Rixat
// @Time: 2023-12-05 07:57:53
// @receiver
// @param c *gin.Context
func (s ShipperIncomeService) GetShipperList(admin models.Admin, page int, limit int, cityId int, areaId int, kw string, sort string,disabled int) map[string]interface{} {
	var shipperList []models.Admin
	var totalCount int64

	query := tools.Db.Model(shipperList).Where("type=9")
	if cityId > 0 {
		query.Where("admin_city_id = ?", cityId)
	}
	if areaId > 0 {
		query.Where("admin_area_id = ?", areaId)
	}
	if len(kw) > 0 {
		query.Where("real_name like ? or mobile like ?", "%"+kw+"%", "%"+kw+"%")
	}
	if disabled > 0 {
		query.Where("deleted_at is not null or state = 0")
	}else{
		query.Where(" state = 1 ")
	}
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Scopes(scopes.Page(page, limit)).
			Preload("ShipperIncomeTemplate").
			Order("id desc").
			Find(&shipperList)
	}
	items := make([]map[string]interface{}, 0)
	for _, value := range shipperList {
		items = append(items, map[string]interface{}{
			"id":                      value.ID,
			"real_name":               value.RealName,
			"name":                    value.Name,
			"avatar":                  tools.CdnUrl(value.Avatar),
			"mobile":                  value.Mobile,
			"complaint_deduction_fee": value.ShipperIncomeTemplate.ComplainDeductionFee,
			"created_at":              tools.TimeFormatYmdHis(&value.CreatedAt),
		})
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": items,
	}
	return result
}
