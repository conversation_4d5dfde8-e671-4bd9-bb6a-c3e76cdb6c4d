package cms

import (
	"errors"
	"mulazim-api/constants"
	"mulazim-api/lang"
	"mulazim-api/models"
	shipmentModels "mulazim-api/models/shipment"
	resource "mulazim-api/resources/cms/shipment"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type ShipperAttendanceService struct {
	langUtil *lang.LangUtil
	language string
}

func NewShipperAttendanceService(c *gin.Context) *ShipperAttendanceService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	ShipperAttendanceService := ShipperAttendanceService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &ShipperAttendanceService
}

// Create
//
// @Description: 创建配送员账户信息
// @Author: Rixat
// @Time: 2023-10-24 08:11:09
// @receiver
// @param c *gin.Context
func (s ShipperAttendanceService) Create(admin models.Admin, params resource.ShipperAttendance) error {
	var shipper models.Admin
	tools.Db.Model(shipper).Where("id=?", params.ShipperID).First(&shipper)
	if shipper.ID == 0 {
		return errors.New("not_found")
	}
	if shipper.State == 0 {
		return errors.New("shipper_state_unable")
	}
	attendance := models.ShipperAttendanceLog{
		Name:      shipper.RealName,
		Mobile:    shipper.Mobile,
		AdminId:   admin.ID,
		CityId:    params.CityID,
		AreaId:    params.AreaID,
		ShipperId: params.ShipperID,
		StartTime: carbon.Parse(params.StartTime).Carbon2Time(),
		Remark:    params.Remark,
		Images:    params.Images,
		CreatedAt: time.Now(),
	}
	endTime := carbon.Parse(params.EndTime).Carbon2Time()
	if params.StateType == 1 { // 请假
		attendance.LeaveType = params.LeaveType
		attendance.State = 4
		attendance.EndTime = &endTime
	}
	if params.StateType == 2 { // 事故
		attendance.State = 5
	}
	err := tools.Db.Model(attendance).Create(&attendance).Error
	if err != nil {
		tools.Logger.Error("FATAL 创建配送员账户信息失败", err.Error())
		return errors.New("failed")
	}
	return nil
}

// List
//
// @Description: 获取考勤列表
// @Author: Rixat
// @Time: 2023-11-07 07:46:13
// @receiver
// @param c *gin.Context
func (s ShipperAttendanceService) List(admin models.Admin, page int, limit int, stateType int, cityId int, areaId int, state int, ShipperID int, sort string) map[string]interface{} {
	var attendanceList []models.ShipperAttendanceLog
	var totalCount int64
	if (admin.Type == constants.ADMIN_TYPE_DEALER || admin.Type == constants.ADMIN_TYPE_DEALER_SUB) && cityId == 0 && areaId == 0 {
		cityId = admin.AdminCityID
		areaId = admin.AdminAreaID
	}
	query := tools.Db.Model(attendanceList).Scopes(scopes.CityArea(cityId, areaId))
	if stateType == 1 {
		query.Where("state in (1,2,3)")
	}
	if stateType == 2 {
		query.Where("state = 4")
	}
	if stateType == 3 {
		query.Where("state = 5")
	}
	if ShipperID > 0 {
		query.Where("shipper_id = ?", ShipperID)
	}
	query.Count(&totalCount)
	if totalCount > 0 {
		query.
			Preload("City").
			Preload("Area").
			Preload("Shipper").
			Preload("Creator").
			Preload("Reviewer").
			Scopes(scopes.Page(page, limit)).
			Order(sort).
			Find(&attendanceList)
	}
	attendanceListRes := make([]map[string]interface{}, 0)
	for _, attendance := range attendanceList {
		attendanceListRes = append(attendanceListRes, map[string]interface{}{
			"id":              attendance.Id,
			"creator_name":    attendance.Creator.RealName,
			"reviewer_name":   attendance.Reviewer.RealName,
			"admin_id":        attendance.AdminId,
			"city_name":       tools.GetNameByLang(attendance.City, s.language),
			"area_name":       tools.GetNameByLang(attendance.Area, s.language),
			"shipper_id":      attendance.ShipperId,
			"avatar":          tools.CdnUrl(attendance.Shipper.Avatar),
			"mobile":          attendance.Mobile,
			"name":            attendance.Name,
			"lat":             attendance.Lat,
			"lng":             attendance.Lng,
			"position":        attendance.Position,
			"start_time":      tools.TimeFormatYmdHis(&attendance.StartTime),
			"end_time":        tools.TimeFormatYmdHis(attendance.EndTime),
			"images":          tools.GetImageURLs(attendance.Images),
			"leave_type":      attendance.LeaveType,
			"leave_type_name": s.langUtil.TArr("leave_types")[attendance.LeaveType],
			"state":           attendance.State,
			"review_state":    attendance.ReviewState,
			"review_remark":   attendance.ReviewRemark,
			"remark":          attendance.Remark,
			"created_at":      tools.TimeFormatYmdHis(&attendance.CreatedAt),
		})
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": attendanceListRes,
	}
	return result
}

// GetListClock
//
// @Description: 获取打卡列表
// @Author: Rixat
// @Time: 2023-12-05 08:10:42
// @receiver
// @param c *gin.Context
func (s ShipperAttendanceService) GetListClock(page int, limit int, cityId int, areaId int, state int, ShipperID int, sort string) map[string]interface{} {
	var adminList []models.Admin
	query := tools.Db.Model(adminList).Preload("ShipperAttendance", "state in (1,2,3) and created_at > '"+carbon.Now().Format("Y-m-d")+" 00:00:00'").Where("type in(8,9) and state = 1")
	if cityId > 0 {
		query.Where("admin_city_id =?", cityId)
	}
	if areaId > 0 {
		query.Where("admin_area_id =?", areaId)
	}
	if ShipperID > 0 {
		query.Where("id =?", ShipperID)
	}
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Preload("ShipperAttendance", func(d *gorm.DB) *gorm.DB {
			return d.Preload("Creator").Where("state in (1,2,3) and created_at > '" + carbon.Now().Format("Y-m-d") + " 00:00:00'")
		}).
			Preload("City").
			Preload("Area").
			Scopes(scopes.Page(page, limit)).
			Order(sort).
			Find(&adminList)
	}
	attendanceListRes := make([]map[string]interface{}, 0)
	for _, value := range adminList {
		lastState := s.FormatAttendanceInfo(value.ShipperAttendance)["lastState"]
		// 根据条件筛选
		if (state > 0 && lastState == state) || state == 0 {
			images := s.FormatAttendanceInfo(value.ShipperAttendance)["images"]
			attendanceListRes = append(attendanceListRes, map[string]interface{}{
				"city_name":  tools.GetNameByLang(value.City, s.language),
				"area_name":  tools.GetNameByLang(value.Area, s.language),
				"shipper_id": value.ID,
				"avatar":     tools.CdnUrl(value.Avatar),
				"mobile":     value.Mobile,
				"name":       value.Name,
				"start_time": s.FormatAttendanceInfo(value.ShipperAttendance)["attendanceStartTime"],
				"end_time":   s.FormatAttendanceInfo(value.ShipperAttendance)["attendanceEndTime"],
				"last_state": value.AttendanceState,
				"images":     tools.GetImageURLs(images.(string)),
				"position":   s.FormatAttendanceInfo(value.ShipperAttendance)["position"],
				"creator":    s.FormatAttendanceInfo(value.ShipperAttendance)["creator"],
				"type":       s.FormatAttendanceInfo(value.ShipperAttendance)["type"],
			})
		}
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": attendanceListRes,
	}
	return result
}

// GetClockHeader
//
// @Description: 打卡详情统计
// @Author: Rixat
// @Time: 2023-12-05 08:22:00
// @receiver
// @param c *gin.Context
func (s ShipperAttendanceService) GetClockHeader(admin models.Admin, page int, limit int, ShipperID int, startDate string, endDate string) map[string]interface{} {
	if len(startDate) == 0 || len(endDate) == 0 {
		startDate, endDate = tools.GetMonthStartAndEnd()
	}
	var shipper models.Admin
	tools.Db.Model(shipper).Where("id = ?", ShipperID).First(&shipper)
	// 总上报天数
	var workDay int64
	tools.Db.Model(shipmentModels.ShipperIncomeArchive{}).
		Where("shipper_id = ?", ShipperID).
		Where("date between ? and ?", startDate, endDate).
		Where("(success_count >0 or fail_count >0 or cancel_count > 0)").
		Count(&workDay)
	diffDay := carbon.Parse(startDate).DiffAbsInDays(carbon.Parse(endDate))+1
	// 平均配送时间
	var avgDeliveryTime float64
	tools.Db.Model(shipmentModels.ShipperIncomeArchive{}).
		Select("IFNULL(avg(order_delivery_minute),0) AS avgDeliveryTime").
		Where("shipper_id = ?", ShipperID).
		Where("date between ? and ?", startDate, endDate).
		Scan(&avgDeliveryTime)
	result := map[string]interface{}{
		"avatar":             tools.CdnUrl(shipper.Avatar),
		"name":               shipper.RealName,
		"mobile":             shipper.Mobile,
		"avg_delivery_time":  avgDeliveryTime,
		"total_work_day":     workDay,
		"total_not_work_day": diffDay - workDay,
	}
	return result
}

// GetClockDetailList
//
// @Description: 打卡详情列表
// @Author: Rixat
// @Time: 2023-12-05 08:23:36
// @receiver
// @param c *gin.Context
func (s ShipperAttendanceService) GetClockDetailList(admin models.Admin, page int, limit int, ShipperID int, startDate string, endDate string) map[string]interface{} {
	var attendanceList []models.ShipperAttendanceLog
	query := tools.Db.Model(attendanceList).Where("state in (1,2,3)")
	if ShipperID > 0 {
		query.Where("shipper_id = ?", ShipperID)
	}
	if len(startDate) > 0 && len(endDate) > 0 {
		query.Where("created_at between ? and ?", startDate+" 00:00:00", endDate+" 23:59:59")
	}
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.
			Preload("City").
			Preload("Area").
			Preload("Shipper").
			Preload("Creator").
			Scopes(scopes.Page(page, limit)).
			Order("id desc").
			Find(&attendanceList)
	}
	// 格式化返回结果
	attendanceListRes := make([]map[string]interface{}, 0)
	for _, attendance := range attendanceList {
		attendanceListRes = append(attendanceListRes, map[string]interface{}{
			"id":              attendance.Id,
			"admin_id":        attendance.AdminId,
			"city_name":       tools.GetNameByLang(admin.City, s.language),
			"area_name":       tools.GetNameByLang(admin.Area, s.language),
			"shipper_id":      attendance.ShipperId,
			"avatar":          tools.CdnUrl(attendance.Shipper.Avatar),
			"mobile":          attendance.Mobile,
			"name":            attendance.Name,
			"lat":             attendance.Lat,
			"lng":             attendance.Lng,
			"position":        attendance.Position,
			"start_time":      tools.TimeFormatYmdHis(&attendance.StartTime),
			"end_time":        tools.TimeFormatYmdHis(attendance.EndTime),
			"images":          tools.GetImageURLs(attendance.Images),
			"leave_type":      attendance.LeaveType,
			"leave_type_name": s.langUtil.TArr("leave_types")[attendance.LeaveType],
			"state":           attendance.State,
			"review_state":    attendance.ReviewState,
			"review_remark":   attendance.ReviewRemark,
			"remark":          attendance.Remark,
			"created_at":      tools.TimeFormatYmdHis(&attendance.CreatedAt),
			"creator":         attendance.Creator.Name,
			"type":            attendance.Type,
		})
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": attendanceListRes,
	}
	return result
}

// FormatAttendanceInfo
//
// @Description: 最新打卡信息
// @Author: Rixat
// @Time: 2023-12-05 08:25:16
// @receiver
// @param c *gin.Context
func (s ShipperAttendanceService) FormatAttendanceInfo(attendanceList []models.ShipperAttendanceLog) map[string]interface{} {
	var attendanceStartTime time.Time
	var attendanceEndTime time.Time
	var images string
	var position string
	lastState := 4
	var lastTime time.Time
	var creator string
	var creatorType int
	for _, attendance := range attendanceList {
		if attendance.State == 1 { // 上报
			attendanceStartTime = attendance.CreatedAt
		}
		if attendance.State == 2 { // 下班
			attendanceEndTime = attendance.CreatedAt
		}
		if attendance.CreatedAt.After(lastTime) {
			lastTime = attendance.CreatedAt
			lastState = attendance.State
			images = attendance.Images
		}
		position = attendance.Position
		creator = attendance.Creator.Name
		creatorType = attendance.Type

	}
	res := map[string]interface{}{
		"attendanceStartTime": tools.TimeFormatYmdHis(&attendanceStartTime),
		"attendanceEndTime":   tools.TimeFormatYmdHis(&attendanceEndTime),
		"lastState":           lastState,
		"images":              images,
		"position":            position,
		"creator":             creator,
		"type":                creatorType,
	}
	return res
}

// Detail
//
// @Description: 请假/事故详情
// @Author: Rixat
// @Time: 2023-11-07 07:46:13
// @receiver
// @param c *gin.Context
func (s ShipperAttendanceService) Detail(ID int) (map[string]interface{}, error) {
	var attendance models.ShipperAttendanceLog
	tools.Db.Model(attendance).Where("id=?", ID).Preload("City").Preload("Area").Preload("Shipper").First(&attendance)
	if attendance.Id == 0 {
		return nil, errors.New("not_found")
	}
	result := map[string]interface{}{
		"id":              attendance.Id,
		"admin_id":        attendance.AdminId,
		"city_name":       tools.GetNameByLang(attendance.City, s.language),
		"area_name":       tools.GetNameByLang(attendance.Area, s.language),
		"shipper_id":      attendance.ShipperId,
		"avatar":          tools.CdnUrl(attendance.Shipper.Avatar),
		"mobile":          attendance.Mobile,
		"name":            attendance.Name,
		"lat":             attendance.Lat,
		"lng":             attendance.Lng,
		"position":        attendance.Position,
		"start_time":      tools.TimeFormatYmdHis(&attendance.StartTime),
		"end_time":        tools.TimeFormatYmdHis(attendance.EndTime),
		"images":          tools.GetImageURLs(attendance.Images),
		"leave_type":      attendance.LeaveType,
		"leave_type_name": s.langUtil.TArr("leave_types")[attendance.LeaveType],
		"state":           attendance.State,
		"review_state":    attendance.ReviewState,
		"review_remark":   attendance.ReviewRemark,
		"remark":          attendance.Remark,
		"created_at":      tools.TimeFormatYmdHis(&attendance.CreatedAt),
	}
	return result, nil
}

// ChangeReviewState
//
// @Description: 修改审核状态
// @Author: Rixat
// @Time: 2023-11-07 08:00:16
// @receiver
// @param c *gin.Context
func (s ShipperAttendanceService) ChangeReviewState(admin models.Admin, ID int, reviewState int, reviewContent string) error {
	var attendance models.ShipperAttendanceLog
	tools.Db.Model(attendance).Where("id=?", ID).First(&attendance)
	if attendance.Id == 0 {
		return errors.New("not_found")
	}
	if attendance.ReviewState != 1 {
		return errors.New("unable_review")
	}
	err := tools.Db.Model(attendance).Where("id=?", ID).UpdateColumns(map[string]interface{}{
		"review_state":  reviewState,
		"reviewer_id":   admin.ID,
		"review_remark": reviewContent,
		"updated_at":    carbon.Now().Carbon2Time(),
	}).Error
	if err != nil {
		tools.Logger.Error("修改状态错误:" + err.Error())
		return errors.New("fail")
	}
	return nil
}

// SaveClock
//
// @Description: 后台打卡
// @Author: Rixat
// @Time: 2023-12-05 08:31:23
// @receiver
// @param c *gin.Context
func (s ShipperAttendanceService) SaveClock(admin models.Admin, ShipperID int, state int) error {
	var shipper models.Admin
	tools.Db.Model(shipper).Where("id=?", ShipperID).First(&shipper)
	if shipper.ID == 0 {
		return errors.New("not_found")
	}
	var attendance models.ShipperAttendanceLog
	err := tools.Db.Model(attendance).Create(map[string]interface{}{
		"admin_id":   admin.ID,
		"city_id":    admin.AdminCityID,
		"area_id":    admin.AdminAreaID,
		"shipper_id": ShipperID,
		"state":      state,
		"mobile":     shipper.Mobile,
		"name":       shipper.RealName,
		"lat":        "0",
		"lng":        "0",
		"position":   "后台",
		"type":       2,
		"created_at": carbon.Now().Carbon2Time(),
	}).Error
	if err != nil {
		tools.Logger.Error("创建打卡记录出错:" + err.Error())
		return errors.New("fail")
	}
	er2 := tools.Db.Table("t_admin").Where("id = ?", ShipperID).Update("attendance_state", state).Error
	if er2 != nil {
		tools.Logger.Error("更新配送员打卡状态出错:" + er2.Error())
		return errors.New("fail")
	}
	return nil
}
