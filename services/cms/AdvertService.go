package cms

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/requests/cms/advert/advert"
	"mulazim-api/scopes"
	"mulazim-api/services"
	"mulazim-api/tools"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type AdvertService struct {
	services.BaseService
	langUtil *lang.LangUtil
	language string
}

// GetAdvertList 获取广告列表
func (s *AdvertService) GetAdvertList(request advert.AdvertListRequest) ([]models.Advert, int64, error) {
	db := tools.GetDB()

	query := db.Model(models.Advert{}).Preload("AdvertPosition").Preload("Restaurant").Preload("RestaurantFood").Preload("Area")
	if request.CityId != 0 && request.AreaId == 0 {
		var areaIds []int
		db.Model(models.Area{}).Where("city_id = ?", request.CityId).Select("id").Scan(&areaIds)
		query = query.Where("area_id IN (?)", areaIds)
	}
	if request.AreaId != 0 {
		query = query.Where("area_id = ?", request.AreaId)
	}
	if request.LangId != 0 {
		query = query.Where("lang_id = ?", request.LangId)
	}
	if request.AdvertPositionId != 0 {
		query = query.Where("adver_position_id = ?", request.AdvertPositionId)
	}
	if request.LinkType != 0 {
		query = query.Where("link_type = ?", request.LinkType)
	}
	if request.Search != "" {
		query = query.Where("content like ?", "%"+request.Search+"%")
	}
	if request.State != nil {
		switch *request.State {
		case 0, 1, 2:
			query = query.Where("state = ?", *request.State)
		}
	}

	// 处理 is_running 参数
	if request.IsRunning == 1 {
		// 当前时间在开始时间和结束时间之间，且状态为1
		query = query.Where("state = ? AND start_time <= NOW() AND end_time >= NOW()", 1)
	}

	// 处理时间范围查询
	if request.StartDate != "" && request.EndDate != "" {
		// 查询在指定日期范围内的广告
		startDate := request.StartDate + " 00:00:00"
		endDate := request.EndDate + " 23:59:59"
		query = query.Where("(start_time <= ? AND end_time >= ?) OR (start_time >= ? AND start_time <= ?)", 
			endDate, startDate, startDate, endDate)
		
		// 如果同时指定了 is_running=1，则只返回状态为1的
		if request.IsRunning == 1 {
			query = query.Where("state = ?", 1)
		}
	}

	if request.SortColumns != "" {
		query = query.Order(request.SortColumns)
	} else {
		query = query.Order("state desc,created_at desc")
	}

	var result []models.Advert
	var total int64
	query.Count(&total)
	err := query.Scopes(scopes.Page(request.Page, request.Limit)).Find(&result).Error
	if err != nil {
		return result, 0, err
	}
	return result, total, nil
}

// CreateAdvert 创建广告
func (s *AdvertService) CreateAdvert(request advert.AdvertCreateRequest,admin models.Admin) error {
	db := tools.GetDB()
	var err error
	var adState int64

	// 如果是使用模板时，将状态设置为1  ,超级管理员应该直接1  状态 0：关闭 1:开启 2:待审核  
	if !(request.UseAutoImage || admin.Type == 1) { //如果 不是用模板生成的 或不是管理员创建的 就设置为待审核
		adState = 2
	}

	if admin.Type == 1 && request.State!=nil{
		adState = *request.State
	}
	
	// 准备广告基础信息（不包含语言ID和区域ID）
	baseAdvert := models.Advert{
		AdverPositionId:     request.AdvertPositionId,                      // 广告位ID
		Type:                1,                                             // 1表示图片广告
		Content:             request.Content,                               // 广告内容
		LinkType:            *request.LinkType,                             // 链接类型
		LinkUrl:             request.LinkUrl,                               // 链接地址
		LinkId:              *request.LinkId,                               // 链接ID
		StartTime:           carbon.Parse(request.StartDate).Carbon2Time(), // 开始日期(时间设为00:00:00)
		EndTime:             carbon.Parse(request.EndDate).Carbon2Time(),   // 结束日期(时间设为00:00:00)
		TimeBegin:           request.StartTime,                             // 开始时间(保持原样，格式HH:MM:SS)
		TimeEnd:             request.EndTime,                               // 结束时间(保持原样，格式HH:MM:SS)
		Weight:              request.Weight,                                // 权重
		State:               adState,
		StoreId:             request.StoreId,
		MiniProgramId:       request.MiniProgramId,
		MiniProgramLinkPage: request.MiniProgramLinkPage,
	}

	// 存储所有需要创建的广告记录
	var adsToCreate []models.Advert
	// 处理多语言图片
	languages := []struct {
		imageUrl string
		langId   int64
	}{
		{request.ImageUg, 1},
		{request.ImageZh, 2},
	}

	for _, lang := range languages {
		if lang.imageUrl == "" {
			continue // 跳过空图片
		}

		// 如果有指定区域，为每个区域创建记录
		if len(request.AreaIds) > 0 {
			for _, areaId := range request.AreaIds {
				ad := baseAdvert
				ad.ImageUrl = lang.imageUrl
				ad.LangId = lang.langId
				ad.AreaId = areaId
				adsToCreate = append(adsToCreate, ad)
			}
		} else {
			// 如果没有指定区域，自动获取区域
			ad := baseAdvert
			ad.ImageUrl = lang.imageUrl
			ad.LangId = lang.langId

			if request.LinkType != nil && *request.LinkType == 1 {
				var restaurantAreaId int64
				db.Model(&models.Restaurant{}).Where("id = ?", request.LinkId).Select("area_id").Scan(&restaurantAreaId)
				ad.AreaId = restaurantAreaId
			} else if request.LinkType != nil && *request.LinkType == 2 {
				var food models.RestaurantFoods
				db.Model(&models.RestaurantFoods{}).Where("id = ?", request.LinkId).Select("id,restaurant_id").
					Preload("Restaurant", func(db *gorm.DB) *gorm.DB {
						return db.Select("id, area_id")
					}).Find(&food)
				ad.AreaId = tools.ToInt64(food.Restaurant.AreaID)
			}

			adsToCreate = append(adsToCreate, ad)
		}
	}

	// 检查是否有有效广告需要创建
	if len(adsToCreate) == 0 {
		tools.Logger.Info("检查是否有有效广告需要创建 ", err)
		return fmt.Errorf("create_fail")
	}

	// 批量创建广告记录
	if err := db.Create(&adsToCreate).Error; err != nil {
		tools.Logger.Info("批量创建广告失败:", err)
		return fmt.Errorf("create_fail")
	}

	if adState == 2 {
		tools.SendDingDingMsg("代理提交新的广告，请尽快审核")
	}

	// 清除广告缓存
	go func () {
		defer func() {
			if err := recover(); err != nil {
				tools.Logger.Error("panic:", err)
			}
		}()
		s.deleteAdCache()
	}()
	

	return nil
}

// ChangeState 更改广告状态
func (s *AdvertService) ChangeState(request advert.AdvertChangeStateRequest,admin models.Admin) error {

	db := tools.GetDB()
	var adv []models.Advert
	var ids []int64
	// Parse ID as either string or number
	var id interface{}
	json.Unmarshal(request.Id, &id)
	reqId :=tools.ToString(id)
	if strings.Contains(reqId, ","){
		idItems :=strings.Split(reqId, ",")
		for _, id := range idItems {
			ids = append(ids, tools.ToInt64(id))
		}
	}else{
		ids = append(ids, tools.ToInt64(reqId))
	}
	db.Model(&models.Advert{}).Where("id in (?)", ids).Find(&adv)
	for _, v := range adv {
		if (*request.State ==1 || *request.State ==0) {
			if  v.State == 2 && !(admin.Type == 1 || admin.Type == 2 ) {//在审核中的状态下不允许更改状态，审核完了以后该怎么做就怎么做
				// tools.Logger.Info("advert_state_error,",v.ID,",old_state:",v.State,",new_state:",*request.State,",admin_type:",admin.Type)
				return errors.New(s.langUtil.T("advert_state_error"))
			}
		}
	}
	if err := db.Model(&models.Advert{}).Where("id in (?)", ids).Update("state", *request.State).Error; err != nil {
		return errors.New("update_fail")
	}
	// 清除广告缓存
	s.deleteAdCache()
	return nil
}
func (s *AdvertService) deleteAdCache() {
	redisHelper := tools.GetRedisHelper()
	ctx := context.Background()
	key := "Advert_*"
	result, _ := redisHelper.Keys(ctx, key).Result()
	redisHelper.Del(ctx, result...)
}

// EditAdvert 编辑广告
func (s *AdvertService) UpdateAdvert(request advert.AdvertEditRequest,admin models.Admin) error {
	db := tools.GetDB()

	// 查找广告记录是否存在
	var count int64
	db.Model(&models.Advert{}).Where("id = ?", request.Id).Count(&count)
	if count == 0 {
		return fmt.Errorf("not_found")
	}
	var adv models.Advert
	db.Model(&models.Advert{}).Where("id = ?", request.Id).Find(&adv)
	

	var adState int64

	// 如果是使用模板时，将状态设置为1  ,超级管理员应该直接1  状态 0：关闭 1:开启 2:待审核
	if !(request.UseAutoImage || admin.Type == 1 || admin.Type == 2) { //如果 不是用模板生成的 或不是管理员创建的 就设置为待审核
		adState = 2
	}
	if (admin.Type == 1 || admin.Type == 2) && request.State!=nil{
		adState = *request.State
	}
	// 使用map更新广告信息
	updateMap := map[string]interface{}{
		"adver_position_id":      request.AdvertPositionId,
		"content":                request.Content,
		"image_url":              request.Image,
		"link_type":              &request.LinkType,
		"link_url":               request.LinkUrl,
		"link_id":                request.LinkId,
		"store_id":               request.StoreId,
		"lang_id":                request.LangId,
		"weight":                 request.Weight,
		"start_time":             carbon.Parse(request.StartDate).Carbon2Time(),
		"end_time":               carbon.Parse(request.EndDate).Carbon2Time(),
		"time_begin":             request.StartTime,
		"time_end":               request.EndTime,
		"mini_program_id":        request.MiniProgramId,
		"mini_program_link_page": request.MiniProgramLinkPage,
		"state":                  adState,
	}

	// 更新广告记录
	if err := db.Model(&models.Advert{}).Where("id = ?", request.Id).Updates(updateMap).Error; err != nil {
		return fmt.Errorf("update_fail")
	}

	// 如果状态为2发送钉钉消息
	if adState == 2 {
		tools.SendDingDingMsg("代理提交新的广告，请尽快审核 https://cms.mulazim.com/app/ug/advert/advert-manager")
	}

	// 清除广告缓存
	s.deleteAdCache()

	return nil
}

// DeleteAdvert 删除广告
func (s *AdvertService) DeleteAdvert(ids []int64) error {
	db := tools.GetDB()

	// 删除广告记录
	if err := db.Where("id IN ?", ids).Delete(&models.Advert{}).Error; err != nil {
		return fmt.Errorf("delete_fail")
	}

	// 清除广告缓存
	s.deleteAdCache()

	return nil
}

// GetPosition 获取广告位置
func (s *AdvertService) GetPosition(adminType int) ([]advert.AdvertPositionResponse, error) {
	db := tools.GetDB()

	var fieldDescription string
	if s.language == "ug" {
		fieldDescription = "description"
	} else {
		fieldDescription = "description_zh as description"
	}

	query := db.Table("b_adver_position").
		Select("id, " + fieldDescription + ", width, height")

	// 根据管理员类型筛选不同的广告位
	if !(adminType == 1 || adminType == 2) { 
		query = query.Where("id in (?)", []int{1,8})
	}

	var positions []advert.AdvertPositionResponse
	if err := query.Find(&positions).Error; err != nil {
		return nil, fmt.Errorf("query_fail")
	}

	return positions, nil
}

func (s *AdvertService) GetCityAreaTree(cityId int, areaId int) ([]models.City, error) {
	db := tools.GetDB()
	var cities []models.City

	// 基础查询
	query := db.Model(&models.City{}).Where("state = ?", 1)

	// 城市筛选
	if cityId != 0 {
		query = query.Where("id = ?", cityId)
	}

	// 区域预加载
	if areaId != 0 {
		query = query.Preload("Areas", "id = ? and state = ?", areaId, 1)
	} else {
		query = query.Preload("Areas", "state = ?", 1)
	}

	// 执行查询
	if err := query.Find(&cities).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch city area tree: %w", err)
	}

	// 后过滤：如果指定了areaId但没有指定cityId，需要过滤掉没有匹配区域的city
	if areaId != 0 && cityId == 0 {
		filteredCities := make([]models.City, 0)
		for _, city := range cities {
			if len(city.Areas) > 0 {
				filteredCities = append(filteredCities, city)
			}
		}
		cities = filteredCities
	}

	return cities, nil
}

func (s *AdvertService) GetCanvasTemplate(pagination tools.Pagination) ([]models.PosterTemplate, int64, error) {
	db := tools.GetDB()

	baseQuery := db.Model(&models.PosterTemplate{}).
		Where("render_type = ?", models.PosterTemplateRenderTypeCanvas)

	// 查询总数
	var total int64
	if err := baseQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total == 0 {
		return nil, 0, fmt.Errorf("not_found")
	}

	// 查询分页数据
	var templates []models.PosterTemplate
	if err := baseQuery.
		Select("id", "cover_ug", "cover_zh", "width", "height", "crop").
		Scopes(scopes.Page(pagination.Page, pagination.Limit)).
		Find(&templates).Error; err != nil {
		return nil, 0, err
	}

	return templates, total, nil
}

func (s *AdvertService) GetAdvertDetail(id int64) (models.Advert, error) {
	db := tools.GetDB()

	var advert models.Advert
	if err := db.Model(models.Advert{}).
		Preload("Area").
		Preload("Restaurant").
		Preload("RestaurantFood").
		Where("id = ?", id).First(&advert).Error; err != nil {
		return advert, err
	}

	return advert, nil
}

func NewAdvertService(c *gin.Context) *AdvertService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	advertService := AdvertService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &advertService
}
