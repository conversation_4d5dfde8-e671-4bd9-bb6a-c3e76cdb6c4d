package cms

import (
	"encoding/json"
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type MapService struct {
	langUtil *lang.LangUtil
	language string
}

func (s MapService) GetShipperMapList(c *gin.Context, cityID int, areaID int, shipperID int, orderID int) ([]models.Admin, *models.OrderToday) {
	db := tools.Db
	//var shipperList []models.Admin

	shipperList := make([]models.Admin,0)
	query := db.Model(shipperList).
		Select("id,real_name,avatar,attendance_state,auto_dispatch_rank").
		Where("admin_city_id = ? AND admin_area_id = ? AND type in (?,?) AND state = 1 and t_admin.deleted_at is NULL", cityID, areaID, models.AdminTypeShipperAdmin,models.AdminTypeShipper)
	var orderToday models.OrderToday
	if orderID > 0 {
		db.Model(&orderToday).Select("id,store_id,distance,building_id,market_type,state").Preload("Building").Preload("Restaurant").Where("id = ?", orderID).First(&orderToday)
		if orderToday.StoreID>0 {
			if orderToday.MarketType == 2 {
				// 获取设置特价活动的指定配送员
				var seckillLog models.SeckillLog
				tools.Db.Model(seckillLog).Where("order_id", orderToday.ID).Preload("Seckill.SeckillMarket").Find(&seckillLog)
				shipperIdStr := seckillLog.Seckill.SeckillMarket.Shipper
				shipperIds := strings.Split(shipperIdStr, ",")
				if shipperIdStr != "" && len(shipperIds) > 0 {
					query = query.Where("id in (?)", shipperIds)
				} else {
					query = query.Where("id in (select admin_id from b_admin_store where store_id = ?)", orderToday.StoreID)
				}
			} else {
				query = query.Where("id in (select admin_id from b_admin_store where store_id = ?)", orderToday.StoreID)
			}
		}
	}
	if shipperID > 0 {
		query = query.Where("id = ?",shipperID)
	}
	query.Preload("TodayOrders", "state in (3,4,5,6)")
	query.Preload("TodayOrders.Restaurant")
	query.Preload("TodayOrders.Building")
	query.Preload("TodayOrders.User")
	query.Preload("TodayOrders.TakeOrder","t_take_order.state = 1")
	query.Preload("TodayOrders.OrderExtend")
	query.Find(&shipperList)
	var ids []string
	a := ""
	for _, v := range shipperList {
		ids = append(ids, fmt.Sprintf("shipper_%d", v.ID))
		a += fmt.Sprintf(" shipper_%d", v.ID)
	}
	redisHelper := tools.GetRedisHelper()
	posInfos := redisHelper.MGet(c,ids...).Val()
	type ShipperRedisPosInfo struct {
		AdminID string `json:"admin_id"`
		Lat string `json:"lat"`
		Lng string `json:"lng"`
		Accuracy string `json:"accuracy"`
		AreaID string `json:"area_id"`
		CityID string `json:"city_id"`
		Time string `json:"time"`
		Speed string `json:"speed"`
	}
	//打印posInfo 类型
	if posInfos != nil{
		for i,v := range posInfos{
			if v!=nil  {
				if posInfo,ok := v.(string);ok{
					var shipperRedisPosInfo ShipperRedisPosInfo
					err := json.Unmarshal([]byte(posInfo), &shipperRedisPosInfo)
					if err==nil {
						shipperList[i].Lat = tools.ToFloat64(shipperRedisPosInfo.Lat)
						shipperList[i].Lng = tools.ToFloat64(shipperRedisPosInfo.Lng)
						shipperList[i].Speed = tools.ToFloat64(shipperRedisPosInfo.Speed)*3.6
						shipperList[i].Accuracy = tools.ToFloat64(shipperRedisPosInfo.Accuracy)
						//借用这个字段
						shipperList[i].LoginTime = carbon.Parse(shipperRedisPosInfo.Time).Carbon2Time()
					}else{
						tools.Logger.Error("FATAL json.Unmarshal error",err)
					}
				}
			}
		}
	}
	return shipperList,&orderToday
}

func (s MapService) GetNewReceivedOrderList(c *gin.Context, cityID int, areaID int) []models.OrderToday {
	db := tools.Db
	var orderList []models.OrderToday
	query := db.Model(orderList).
		Select("id,state,order_type,order_id,building_id,store_id,booking_time,price,shipment,lunch_box_fee,order_address,serial_number,distance").
		Where("state in(3,4,5,6) AND city_id = ? AND area_id = ? AND shipper_id is null AND delivery_type = 1", cityID, areaID)
	query.Preload("Restaurant")
	query.Preload("Building").Order("booking_time asc")
	query.Find(&orderList)
	return orderList
}
//
// GetNewReceivedOrderListCount
//  @Description: 获取新接单列表数量
//  @receiver s
//  @param c
//  @param cityID
//  @param areaID
//  @return int64
//
func (s MapService) GetNewReceivedOrderListCount(c *gin.Context, cityID int, areaID int) int64 {
	cacheKey := fmt.Sprintf("new_received_order_list_count_%d_%d", cityID, areaID)
	countStr := tools.Remember(c, cacheKey, 1*time.Second, func() interface{}{
		db := tools.Db
		var orderList []models.OrderToday
		var count int64
		query := db.Model(orderList).
			Select("id").
			Where("state in(3,4,5,6) AND city_id = ? AND area_id = ? AND shipper_id is null AND delivery_type = 1", cityID, areaID)
		query.Count(&count)
		return count
	})
	count := tools.ToInt64(countStr)
	return count
}
func NewMapService(c *gin.Context) *MapService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	MapService := MapService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &MapService
}
