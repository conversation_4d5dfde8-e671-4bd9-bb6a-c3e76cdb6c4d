package cms

import (
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/lang"
	"mulazim-api/models"
	lotteryRequest "mulazim-api/requests/cms/lottery"
	"mulazim-api/scopes"
	"mulazim-api/tools"
)

type LotteryActivityService struct {
	langUtil *lang.LangUtil
	language string
}
// List 抽奖列表获取
//  @receiver s
//  @param state
//  @param page
//  @param limit
//  @param kw
//  @param sort
//  @return int64
//  @return []models.LotteryActivity
//
func (s LotteryActivityService) List(state *int, page int, limit int, kw string ,sort string) (int64,[]models.LotteryActivity) {
	list := []models.LotteryActivity{}
	db := tools.Db
	query := db.Model(&list)
	query.Preload("Admin")
	query.Where("type = ?", models.LotteryActivityTypeLottery) // 目前必须为抽奖活动
	if state != nil {
		query.Where("state=?", *state)
	}

	if kw != "" {
		query.Where("name_ug like ? or name_zh like ?", "%"+kw+"%", "%"+kw+"%")
	}

	if len(sort)>0 {
		query.Order(sort)
	}else{
		query.Order("created_at desc")
	}

	var totalCount int64

	query.Count(&totalCount)
	query.Scopes(scopes.Page(page, limit)).Find(&list)

	return totalCount,list
}

func (s LotteryActivityService) Detail(ID int ) models.LotteryActivity {
	var detail models.LotteryActivity
	tools.Db.Model(detail).Where("id=?",ID).Preload("LotteryActivityLevel.LotteryActivityLevelPrize.LotteryPrize").Find(&detail)
	return detail
}


func (s LotteryActivityService) ChangeState(ID int,State int ) error {
	var detail models.LotteryActivity
	tools.Db.Model(detail).Where("id=?",ID).Find(&detail)
	if detail.ID == 0 {
		return errors.New("fail")
	}
	startTimeCarbon := carbon.Time2Carbon(*detail.StartTime)
	endTimeCarbon := carbon.Time2Carbon(*detail.EndTime)
	if endTimeCarbon.Lte(carbon.Now()) && State == 1{
		return errors.New("end_active_cannot_open") 
	}
	// 验证时间冲突
	if State == 1 {
		var runningActivity  []models.LotteryActivity
		tools.GetDB().Model(runningActivity).Where("state=1 and id <> ?",detail.ID).Scan(&runningActivity)
		for _,item := range runningActivity{
			runningStartTime := *item.StartTime
			runningEndTime := *item.EndTime
			if tools.ConflictTimeRange(startTimeCarbon.Carbon2Time(),endTimeCarbon.Carbon2Time(),runningStartTime,runningEndTime){
				return errors.New("activity_time_conflict")
			}
		}
	}
	
	err := tools.Db.Model(detail).Where("id=?", ID).UpdateColumn("state", State).Error
	if err != nil {
		return errors.New("fail")
	}
	return nil
}



func (s LotteryActivityService) Delete(ID int) error {
	var activity models.LotteryActivity
	tools.Db.Model(activity).Where("id=?",ID).Find(&activity)
	if activity.ID == 0 {
		return errors.New("not_found")
	}
	startTimeCarbon := carbon.Time2Carbon(*activity.StartTime)
	if startTimeCarbon.Lte(carbon.Now()){
		return errors.New("activity_already_started_enable_delete")
	}


	tx := tools.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	// 删除LotteryActivityLevelPrize
	err := tx.Model(models.LotteryActivityLevelPrize{}).Where("lottery_id=?",ID).UpdateColumns(map[string]interface{}{
		"state":0,
		"deleted_at":carbon.Now(),
	}).Error
	if err != nil {
		tx.Rollback()
		return errors.New("fail")
	}
	// 删除LotteryActivityLevel
	err = tx.Model(models.LotteryActivityLevel{}).Where("lottery_activity_id=?",ID).Delete(&models.LotteryActivityLevel{}).Error
	if err != nil {
		tx.Rollback()
		return errors.New("fail")
	}

	// 删除coupon_level
	err = tx.Model(models.LotteryActivityLevelCoupon{}).Where("lottery_id=?",ID).UpdateColumns(map[string]interface{}{
		"state":0,
		"deleted_at":carbon.Now(),
	}).Error
	if err != nil {
		tx.Rollback()
		return errors.New("fail")
	}
	// 删除coupon
	err = tx.Model(models.Coupon{}).Where("type=3 and type_object_id=?",ID).UpdateColumns(map[string]interface{}{
		"state":0,
		"deleted_at":carbon.Now(),
	}).Error
	if err != nil {
		tx.Rollback()
		return errors.New("fail")
	}
	// 删除level
	err = tx.Model(models.LotteryActivity{}).Where("id=?",ID).UpdateColumns(map[string]interface{}{
		"state":0,
		"deleted_at":carbon.Now(),
	}).Error
	if err != nil {
		tx.Rollback()
		return errors.New("fail")
	}
	tx.Commit()
	return nil
}

func NewLotteryActivityService(c *gin.Context) *LotteryActivityService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	lotteryService := LotteryActivityService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &lotteryService
}


func (s LotteryActivityService) Create(admin models.Admin,params lotteryRequest.LotteryActivityCreateRequest)  error {
	tx := tools.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	// 创建活动基本数据
	startTime := carbon.Parse(params.StartTime).Carbon2Time()
	endTime := carbon.Parse(params.EndTime).Carbon2Time()

	// 验证是否时间冲的活动
	if tools.ToInt(params.State) == 1 {
		// 验证时间冲突
		var runningActivity  []models.LotteryActivity
		tools.GetDB().Model(runningActivity).Where("state=1").Scan(&runningActivity)
		for _,item := range runningActivity{
			runningStartTime := *item.StartTime
			runningEndTime := *item.EndTime
			if tools.ConflictTimeRange(startTime,endTime,runningStartTime,runningEndTime){
				return errors.New("activity_time_conflict")
			}
		}
	}

	couponEndTime := carbon.Parse(params.CouponEndTime).Carbon2Time()
	activity := models.LotteryActivity{
		NameUg:params.NameUg,
		NameZh:params.NameZh,
		StartTime: &startTime,
		EndTime:&endTime,
		ShowPosition: &params.ShowPosition,
		State: tools.ToInt(params.State),
		AdminID:&admin.ID,
		MinPrizeOrderPrice:params.ShareMinOrderPrice,
		MaxBuyCount:params.MaxBuyCount,
		ShareCount:params.ShareCount,
		ShareMinOrderPrice:params.ShareMinOrderPrice,
		LotteryActivityGroupID:params.CouponGroupID,
		ShareImage:params.ShareImage,
		CouponEndTimeType:params.TimeType,
		CreatedAt:         carbon.Now().Carbon2Time(),
	}
	if params.TimeType == 1{
		activity.CouponInvalidDate = params.ActiveDate
	}
	if params.TimeType == 2{
		activity.CouponEndTime = &couponEndTime
	}

	

	err := tx.Model(activity).Create(&activity).Error
	if err != nil {
		tx.Rollback()
		return errors.New("fail")
	}

	var couponGroup models.LotteryActivityGroupCoupon
	tools.GetDB().Model(&couponGroup).Where("id=?",params.CouponGroupID).Find(&couponGroup)

	
	// 创建LotteryActivityLevel
	lotteryLevels := params.LotteryActivityLevelItem
	level := models.LotteryActivityLevel{
		LotteryActivityID:activity.ID,
		LotteryActivityGroupID:params.CouponGroupID,
		Level:1,
		Price:params.Price,
		RuleUG:params.RuleUG,
		RuleZH:params.RuleZH,
	}
	err = tx.Model(models.LotteryActivityLevel{}).Create(&level).Error
	if err != nil {
		tx.Rollback()
		return errors.New("fail")
	}
	for _, item := range lotteryLevels {
		var prize models.LotteryPrize
		tx.Model(&prize).Where("id=?",item.PrizeID).Find(&prize)
		// 创建t_lottery_activity_level_prize
		levelPrize := models.LotteryActivityLevelPrize{
			LotteryID:activity.ID,
			LotteryActivityLevelID:level.ID,
			Level:item.Level,
			PrizeID:item.PrizeID,
			Count:item.Count,
			AdminID:admin.ID,
			State: 1,
		}
		err = tx.Model(&levelPrize).Create(&levelPrize).Error
		if err != nil {
			tx.Rollback()
			return errors.New("fail")
		}
		
	}
	type CouponDetailArr struct{
		Price int `json:"price"`
		MinPrice int `json:"min_price"`
	}
	var coupons []CouponDetailArr
	couponDetail := couponGroup.CouponDetail
	// 将 JSON 字符串解析到结构体数组
	err = json.Unmarshal([]byte(*couponDetail), &coupons)
	if err != nil {
		tx.Rollback()
		return nil
	}
	for _, value := range coupons {
		coupon := models.Coupon{
			NameUg:couponGroup.NameUg,
			NameZh:couponGroup.NameZh,
			Type: 3,
			TypeObjectID: activity.ID,
			AdminID: admin.ID,
			MinPrice: value.MinPrice,
			Price: value.Price,
			Count: 999999,
			State: 1,
			StartTime: *activity.StartTime,
			StartUseTime: *activity.StartTime,
			CreatedAt:carbon.Now().Carbon2Time(),
		}
		if params.TimeType == 1{
			coupon.EndTime = carbon.Time2Carbon(*activity.EndTime).AddDays(activity.CouponInvalidDate).Carbon2Time()
			coupon.EndUseTime = carbon.Time2Carbon(*activity.EndTime).AddDays(activity.CouponInvalidDate).Carbon2Time()
		}
		if params.TimeType == 2{
			coupon.EndTime = couponEndTime
			coupon.EndUseTime = couponEndTime
		}
		err = tx.Model(coupon).Create(&coupon).Error
		if err != nil {
			tx.Rollback()
			return errors.New("fail")
		}
		// 创建 t_lottery_activity_level_coupon
		activityLevelCoupon := models.LotteryActivityLevelCoupon{
			LotteryActivityGroupCouponID:params.CouponGroupID,
			LotteryID: activity.ID,
			LotteryActivityLevelID: level.ID,
			CouponID: coupon.ID,
			TimeType: params.TimeType,
			ActiveDate: params.ActiveDate,
			StartDate: carbon.Parse(params.StartTime).Carbon2Time(),
			State: 1,
			AdminID: admin.ID,
		}
		if params.TimeType == 1{
			activityLevelCoupon.EndDate = carbon.Time2Carbon(*activity.EndTime).AddDays(activity.CouponInvalidDate).Carbon2Time()
		}
		if params.TimeType == 2{
			activityLevelCoupon.EndDate = couponEndTime
		}
		err = tx.Model(&activityLevelCoupon).Create(&activityLevelCoupon).Error
		if err != nil {
			tx.Rollback()
			return errors.New("fail")
		}
	}
	tx.Commit()
	return nil
}

// Edit
//
// @Description: 修改活动
// @Author: Rixat
// @Time: 2024-09-06 10:06:45
// @receiver 
// @param c *gin.Context
func (s LotteryActivityService) Edit(admin models.Admin,params lotteryRequest.LotteryActivityEditRequest)  error {
	var activity models.LotteryActivity
	tools.GetDB().Model(&activity).Where("id=?",params.ID).Find(&activity)
	
	// 判断活动是否已开始
	startTimeCarbon := carbon.Time2Carbon(*activity.StartTime)
	endTimeCarbon := carbon.Time2Carbon(*activity.EndTime)
	if endTimeCarbon.Lte(carbon.Now()){
		return errors.New("end_active_cannot_edit") 
	}
	if startTimeCarbon.Lte(carbon.Now()){
		// 活动已开始(只能修改活动规则)
		err := tools.GetDB().Model(models.LotteryActivityLevel{}).Where("lottery_activity_id=?",params.ID).UpdateColumns(map[string]interface{}{
			"rule_ug":params.RuleUG,
			"rule_zh":params.RuleZH,
		}).Error
		if err != nil {
			return errors.New("fail")
		}
		return nil
	}else{
		// 还没开始的活动更新
		tx := tools.Db.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
		// 跟新活动基本数据
		startTime := carbon.Parse(params.StartTime).Carbon2Time()
		endTime := carbon.Parse(params.EndTime).Carbon2Time()
		couponEndTime := carbon.Parse(params.CouponEndTime).Carbon2Time()
		// 验证是否时间冲的活动
		if tools.ToInt(params.State) == 1 {
			// 验证时间冲突
			var runningActivity  []models.LotteryActivity
			tools.GetDB().Model(runningActivity).Where("state=1 and id <> ?",params.ID).Scan(&runningActivity)
			for _,item := range runningActivity{
				runningStartTime := *item.StartTime
				runningEndTime := *item.EndTime
				if tools.ConflictTimeRange(startTime,endTime,runningStartTime,runningEndTime){
					return errors.New("activity_time_conflict")
				}
			}
		}
		activityUpdate := models.LotteryActivity{
			NameUg:params.NameUg,
			NameZh:params.NameZh,
			StartTime: &startTime,
			EndTime:&endTime,
			ShowPosition: &params.ShowPosition,
			State: tools.ToInt(params.State),
			AdminID:&admin.ID,
			MinPrizeOrderPrice:params.ShareMinOrderPrice,
			MaxBuyCount:params.MaxBuyCount,
			ShareCount:params.ShareCount,
			ShareMinOrderPrice:params.ShareMinOrderPrice,
			LotteryActivityGroupID:params.CouponGroupID,
			ShareImage:params.ShareImage,
			CouponEndTimeType:params.TimeType,
			CreatedAt:         carbon.Now().Carbon2Time(),
		}
		if params.TimeType == 1{
			activity.CouponInvalidDate = params.ActiveDate
		}
		if params.TimeType == 2{
			activity.CouponEndTime = &couponEndTime
		}
		err := tx.Model(activityUpdate).Where("id=?",params.ID).UpdateColumns(&activityUpdate).Error
		if err != nil {
			tx.Rollback()
			return errors.New("fail")
		}

		// 删除关联表数据重新创建
		// 删除LotteryActivityLevelPrize
		err = tx.Model(models.LotteryActivityLevelPrize{}).Where("lottery_id=?",activity.ID).UpdateColumns(map[string]interface{}{
			"state":0,
			"deleted_at":carbon.Now(),
		}).Error
		if err != nil {
			tx.Rollback()
			return errors.New("fail")
		}
		// 删除LotteryActivityLevel
		err = tx.Model(models.LotteryActivityLevel{}).Where("lottery_activity_id=?",activity.ID).UpdateColumns(map[string]interface{}{
			"deleted_at":carbon.Now(),
		}).Error
		if err != nil {
			tx.Rollback()
			return errors.New("fail")
		}

		// 删除coupon_level
		err = tx.Model(models.LotteryActivityLevelCoupon{}).Where("lottery_id=?",activity.ID).UpdateColumns(map[string]interface{}{
			"state":0,
			"deleted_at":carbon.Now(),
		}).Error
		if err != nil {
			tx.Rollback()
			return errors.New("fail")
		}
		// 删除coupon
		err = tx.Model(models.Coupon{}).Where("type=3 and type_object_id=?",activity.ID).UpdateColumns(map[string]interface{}{
			"state":0,
			"deleted_at":carbon.Now(),
		}).Error
		if err != nil {
			tx.Rollback()
			return errors.New("fail")
		}



		var couponGroup models.LotteryActivityGroupCoupon
		tools.GetDB().Model(&couponGroup).Where("id=?",params.CouponGroupID).Find(&couponGroup)

		level := models.LotteryActivityLevel{
			LotteryActivityID:activity.ID,
			Level:1,
			Price:params.Price,
			RuleUG:params.RuleUG,
			RuleZH:params.RuleZH,
			LotteryActivityGroupID:params.CouponGroupID,
		}
		err = tx.Model(models.LotteryActivityLevel{}).Create(&level).Error
		if err != nil {
			tx.Rollback()
			return errors.New("fail")
		}
		
		// 创建LotteryActivityLevel
		lotteryLevels := params.LotteryActivityLevelItem
		for _, item := range lotteryLevels {
			var prize models.LotteryPrize
			tx.Model(&prize).Where("id=?",item.PrizeID).Find(&prize)
			// 创建t_lottery_activity_level_prize
			levelPrize := models.LotteryActivityLevelPrize{
				LotteryID:activity.ID,
				LotteryActivityLevelID:level.ID,
				Level:item.Level,
				PrizeID:item.PrizeID,
				Count:item.Count,
				AdminID:admin.ID,
				State: 1,
			}
			err = tx.Model(&levelPrize).Create(&levelPrize).Error
			if err != nil {
				tx.Rollback()
				return errors.New("fail")
			}
		}
		type CouponDetailArr struct{
			Price int `json:"price"`
			MinPrice int `json:"min_price"`
		}
		var coupons []CouponDetailArr
		couponDetail := couponGroup.CouponDetail
		// 将 JSON 字符串解析到结构体数组
		err = json.Unmarshal([]byte(*couponDetail), &coupons)
		if err != nil {
			tx.Rollback()
			return nil
		}
		for _, value := range coupons {
			coupon := models.Coupon{
				NameUg:couponGroup.NameUg,
				NameZh:couponGroup.NameZh,
				Type: 3,
				TypeObjectID: activity.ID,
				AdminID: admin.ID,
				MinPrice: value.MinPrice,
				Price: value.Price,
				Count: 999,
				State: 1,
				StartTime: *activity.StartTime,
				StartUseTime: *activity.StartTime,
				CreatedAt:carbon.Now().Carbon2Time(),
			}
			if params.TimeType == 1{
				coupon.EndTime = carbon.Time2Carbon(*activity.EndTime).AddDays(activity.CouponInvalidDate).Carbon2Time()
				coupon.EndUseTime = carbon.Time2Carbon(*activity.EndTime).AddDays(activity.CouponInvalidDate).Carbon2Time()
			}
			if params.TimeType == 2{
				coupon.EndTime = couponEndTime
				coupon.EndUseTime = couponEndTime
			}
			err = tx.Model(coupon).Create(&coupon).Error
			if err != nil {
				tx.Rollback()
				return errors.New("fail")
			}
			// 创建 t_lottery_activity_level_coupon
			activityLevelCoupon := models.LotteryActivityLevelCoupon{
				LotteryActivityGroupCouponID:params.CouponGroupID,
				LotteryID: activity.ID,
				LotteryActivityLevelID: level.ID,
				CouponID: coupon.ID,
				TimeType: params.TimeType,
				ActiveDate: params.ActiveDate,
				StartDate: carbon.Parse(params.StartTime).Carbon2Time(),
				EndDate: carbon.Parse(params.EndTime).Carbon2Time(),
				State: 1,
				AdminID: admin.ID,
			}
			if params.TimeType == 1{
				activityLevelCoupon.EndDate = carbon.Time2Carbon(*activity.EndTime).AddDays(activity.CouponInvalidDate).Carbon2Time()
			}
			if params.TimeType == 2{
				activityLevelCoupon.EndDate = couponEndTime
			}
			err = tx.Model(&activityLevelCoupon).Create(&activityLevelCoupon).Error
			if err != nil {
				tx.Rollback()
				return errors.New("fail")
			}
		}
		tx.Commit()
	}
	return nil
}

//// GetByPrizeID - 根据奖品 ID 获取所有已经开始的 LotteryActivity
//func (svc *LotteryActivityService) GetStartedActivityByPrizeID(prizeID int) ([]models.LotteryActivity, error) {
//	db := tools.GetDB()
//
//	// 获取绑定了该奖品的有效 LotteryActivityLevelPrize，并拿到所有关联的活动 ID
//	var activityIDs []int
//	if err := db.Model(&models.LotteryActivityLevelPrize{}).
//		Where("prize_id = ? and state = ?", prizeID, models.LotteryActivityLevelPrizeStateOn).
//		Pluck("lottery_id", &activityIDs).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
//		return nil, err
//	}
//
//	if activityIDs == nil || len(activityIDs) == 0 {
//		return make([]models.LotteryActivity, 0), nil
//	}
//
//	// 获取所有已开始的有效活动
//	now := time.Now()
//	var lotteryActivities []models.LotteryActivity
//	query := db.Model(&models.LotteryActivity{})
//
//	if len(activityIDs) == 1 {
//		query.Where("id = ?", activityIDs[0])
//	} else {
//		query.Where("id in ?", activityIDs)
//	}
//
//	if err := query.Where("state = ? and start_time >= ?",
//		models.LotteryActivityStateOpen, now.Format(time.DateTime)).
//		Find(&lotteryActivities).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
//		return nil, err
//	}
//	if len(lotteryActivities) == 0 {
//		return make([]models.LotteryActivity, 0), nil
//	}
//
//	return lotteryActivities, nil
//}