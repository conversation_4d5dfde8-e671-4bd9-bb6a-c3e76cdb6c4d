package cms

import (
	"encoding/json"
	"errors"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/models/cms"
	"mulazim-api/tools"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type CmsService struct {
	langUtil *lang.LangUtil
	language string
}

// GetAdminAndRole
//
//	@Description: 获取登录用户的权限和角色
//	@author: Alimjan
//	@Time: 2023-06-05 13:24:47
//	@receiver s CmsService
//	@param adminId int
//	@return models.Admin
func (s CmsService) GetAdminAndRole(c *gin.Context, adminId int) models.Admin {
	var adminCache models.Admin
	var db = tools.Db
	var cacheKey = fmt.Sprintf("g_a_r_%d", adminId)
	strCacheJson := tools.Remember(c, cacheKey, 5*time.Minute, func() interface{} {
		var admin models.Admin
		db.Table("t_admin").
			Select("id,type,level,mobile,name,real_name,grab_order_count,recommend_qrcode,state,created_at,take_cash_order,back_order_time_limit,last_comment_readed,admin_city_id,admin_area_id").
			Where("`id`=? AND `state`=? AND `deleted_at` IS NULL", adminId, models.AdminStateOk).
			Preload("Roles.Permissions").
			Preload("Areas", func(d *gorm.DB) *gorm.DB {
				return d.Where("state=?", models.AdminStateOk).Order("weight asc").Order("id asc")
			}).
			Find(&admin)
		return admin
	})
	err := json.Unmarshal([]byte(strCacheJson), &adminCache)
	if err != nil {
		println(err)
	}
	return adminCache
}

// GetMenuList
//
//	@Description: 获取平台菜单
//	@author: Alimjan
//	@Time: 2023-06-05 13:25:02
//	@receiver s CmsService
//	@return []cms.Menus
func (s CmsService) GetMenuList(c *gin.Context) []cms.Menus {
	var menusCache []cms.Menus
	var db = tools.Db
	var cacheKey = fmt.Sprintf("m_n")

	tools.GetRedisHelper().Del(c, cacheKey)
	strCacheJson := tools.Remember(c, cacheKey, 5*time.Minute, func() interface{} {
		var menus []cms.Menus
		db.Where("parent_id is null").Order("weight asc").Preload("Childs", func(d *gorm.DB) *gorm.DB {
			return d.Order("weight asc").Order("id asc")
		}).Preload("Childs.Perm").
			Find(&menus)
		return menus
	})
	err := json.Unmarshal([]byte(strCacheJson), &menusCache)
	if err != nil {
		println(err)
	}
	return menusCache
}

// NotiBadgeNumber
//
//	@Description: 获取管理员还未读取的通知列表
//	@author: Alimjan
//	@Time: 2023-06-05 13:25:18
//	@receiver s CmsService
//	@param admin models.Admin
//	@return int64
func (s CmsService) NotiBadgeNumber(c *gin.Context, admin models.Admin) int64 {
	var countCache int64
	if len(admin.Areas) > 0 {
		areaID := admin.Areas[0].ID
		var db = tools.Db

		var cacheKey = fmt.Sprintf("n_b_n_%d", areaID)
		strCacheJson := tools.Remember(c, cacheKey, 5*time.Minute, func() interface{} {
			var count int64
			db.Model(&cms.Notification{}).
				Where("area_id = ?", areaID).
				Where("state = ?", 0).
				Where("created_at BETWEEN ? AND ?",
					time.Now().AddDate(0, -1, 0).Format("2006-01-02"),
					time.Now().AddDate(0, 0, 1).Format("2006-01-02")).
				Count(&count)
			return count
		})
		countCache = tools.ToInt64(strCacheJson)
	}
	return countCache
}

// NewCmsService
//
//	@Description: 后台服务service,后期分开几个service
//	@author: Alimjan
//	@Time: 2023-06-05 13:25:32
//	@param c *gin.Context
//	@return *CmsService
func NewCmsService(c *gin.Context) *CmsService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	marketing := CmsService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &marketing
}

// 获取今日订单列表
func (c CmsService) GetToDayOrder(adminID int, ids []int, state []int) ([]map[string]interface{}, int64) {
	db := tools.Db
	orders := make([]map[string]interface{}, 0)
	selectStr := `today_order_view.id,
						today_order_view.area_id,
						today_order_view.order_id,
						today_order_view.terminal_id,
						today_order_view.category_id,
						today_order_view.store_id,
						today_order_view.user_id,
						today_order_view.mobile,
						today_order_view.state,
						today_order_view.city_id,
						today_order_view.area_id,
						today_order_view.building_id,
						today_order_view.consume_type,
						today_order_view.pay_type,
						today_order_view.original_price,
						today_order_view.price,
						today_order_view.shipment,
						today_order_view.lunch_box_fee,
						today_order_view.mp_profit,
						today_order_view.dealer_profit,
						today_order_view.cash,
						today_order_view.coin,
						today_order_view.consume,
						today_order_view.description,
						today_order_view.booking_time,
						today_order_view.printed_time,
						today_order_view.print_time,
						today_order_view.timezone,
						today_order_view.pay_time,
						today_order_view.delivery_start_time,
						today_order_view.delivery_end_time,
						today_order_view.taked,
						today_order_view.serial_number,
						today_order_view.shipper_id,
						today_order_view.order_type,
						today_order_view.delete_flag,
						today_order_view.refund_chanel,
						today_order_view.created_at,
						today_order_view.updated_at,
						today_order_view.deleted_at,
						today_order_view.building_lat,
						today_order_view.building_lng,
						today_order_view.last_query_time,
						today_order_view.name,
						today_order_view.delivery_type,
						today_order_view.json_info,
						today_order_view.pay_channel,
						today_order_view.pay_platform,
						today_order_view.self_take_number, 
						today_order_view.order_address,
						today_order_view.restaurant_lat,
						today_order_view.restaurant_lng,
						today_order_view.restaurant_name_ug,
						today_order_view.restaurant_name_zh,
						today_order_view.restaurant_name,
						today_order_view.restaurant_tag,
						today_order_view.shipper_name,
						today_order_view.terminal_name_ug,
						today_order_view.terminal_name_zh,
						today_order_view.city_id,
						today_order_view.city_name_ug,
						today_order_view.city_name_zh,
						today_order_view.area_id,
						today_order_view.area_name,
						today_order_view.area_name_ug,
						today_order_view.area_name_zh,
						today_order_view.street_id,
						today_order_view.street_name_ug,
						today_order_view.street_name_zh,
						today_order_view.building_name_ug,
						today_order_view.building_name_zh
						`

	query := db.Table("today_order_view").Select(selectStr).
		//Joins("left join t_order_detail on t_order_detail.order_id = today_order_view.id").
		Where("today_order_view.category_id = 1 and today_order_view.state in ?", state).
		Where("today_order_view.deleted_at is null")

	isAdmin := c.IsSupperAdmin(adminID)
	if !isAdmin {
		query.Where("today_order_view.store_id in ?", ids)
	}
	query.
		Order("created_at desc").Scan(&orders)
	for i := 0; i < len(orders); i++ {
		orders[i]["created_at"] = formatOrderTime(orders[i]["created_at"])
		orders[i]["booking_time"] = formatOrderTime(orders[i]["booking_time"])
		orders[i]["pay_time"] = formatOrderTime(orders[i]["pay_time"])
		orders[i]["print_time"] = formatOrderTime(orders[i]["print_time"])
		orders[i]["updated_at"] = formatOrderTime(orders[i]["updated_at"])
		orders[i]["printed_time"] = formatOrderTime(orders[i]["printed_time"])

		orders[i]["price"] = tools.ToFloat64(orders[i]["price"]) / 100
		orders[i]["lunch_box_fee"] = tools.ToFloat64(orders[i]["lunch_box_fee"]) / 100
		orders[i]["shipment"] = tools.ToFloat64(orders[i]["shipment"]) / 100
		orders[i]["dealer_profit"] = tools.ToInt64(orders[i]["dealer_profit"])
		orders[i]["original_price"] = tools.ToFloat64(orders[i]["original_price"]) / 100
	}
	// 返回订单数量
	newOrderCount := int64(len(orders))
	return orders, newOrderCount
}

// 统一格式化订单时间
func formatOrderTime(input interface{}) string {
	var output string
	if input != nil {
		output = tools.ToTime(input).Format("2006-01-02 15:04:05")
	}
	return output
}

// 安状态获取对应订单数量
func (c CmsService) OrderCountByState(adminID int, ids []int, state int) int64 {
	db := tools.Db
	var orderCount int64
	isAdmin := c.IsSupperAdmin(adminID)
	query := db.Table("today_order_view").Where("category_id = 1 and state = ?", state)
	if !isAdmin {
		query.Where("store_id in ?", ids)
	}
	query.Count(&orderCount)

	return orderCount
}

// 获取配送员取消的订单数量
func (c CmsService) ShipperCanceledOrderCount(adminID int, ids []int) int64 {
	db := tools.Db
	var canceledCount int64
	query := db.Table("shipper_canceled_order_view")
	isAdmin := c.IsSupperAdmin(adminID)
	if !isAdmin {
		query.Where("store_id in ?", ids)
	}

	query.Count(&canceledCount)
	return canceledCount
}

// 安状态获取对应订单数量
func (c CmsService) OrderCountAllState(adminId int, ids []int) (int64, int64, int64, int64, int64) {
	db := tools.Db
	var data = []map[string]interface{}{}
	query := db.Select("count(id) as order_count,state").Table("t_order_today").Where("category_id = 1 and state in (3,6,8,9,10)")
	isAdmin := c.IsSupperAdmin(adminId)
	if !isAdmin {
		query.Where("store_id in ?", ids)
	}
	query.Group("state").Scan(&data)

	var failed, canceled, refused, waiting, sending int64
	for i := 0; i < len(data); i++ {
		if tools.ToInt(data[i]["state"]) == 3 {
			waiting += tools.ToInt64(data[i]["order_count"])
		}
		if tools.ToInt(data[i]["state"]) == 6 {
			sending += tools.ToInt64(data[i]["order_count"])
		}
		if tools.ToInt(data[i]["state"]) == 8 {
			canceled += tools.ToInt64(data[i]["order_count"])
		}
		if tools.ToInt(data[i]["state"]) == 9 {
			refused += tools.ToInt64(data[i]["order_count"])
		}
		if tools.ToInt(data[i]["state"]) == 10 {
			failed += tools.ToInt64(data[i]["order_count"])
		}
	}
	return failed, canceled, refused, waiting, sending
}

// 获取管理员所分配的餐厅编号
func (s CmsService) GetRestaurantIds(c *gin.Context, adminID int) []int {
	var resIds = []int{}
	db := tools.Db

	adminResCacheKey := fmt.Sprintf("ad_r_c_%d", adminID)
	resIdsStr := tools.Remember(c, adminResCacheKey, 5*time.Minute, func() interface{} {
		db.Model(models.Restaurant{}).
			Select("id").
			Joins("INNER JOIN `b_admin_store` ON `t_restaurant`.`id` = `b_admin_store`.`store_id`").
			Where("`b_admin_store`.`admin_id` = ? AND `t_restaurant`.`deleted_at` IS NULL", adminID).
			Pluck("id", &resIds)
		return resIds
	})
	_ = json.Unmarshal([]byte(resIdsStr), &resIds)
	return resIds
}

// 按照管理员ID获取是否是超级管理员,通过type判断 ， 如果type是1，就是超级管理员
func (c CmsService) IsSupperAdmin(adminID int) bool {
	var adminType int
	db := tools.Db
	db.Model(models.Admin{}).
		Select("type").
		Where("id = ?", adminID).
		Pluck("type", &adminType)

	return adminType == 1
}

// GetCityList
//
//	@Description: 获取城市列表
//	@author: Alimjan
//	@Time: 2023-06-05 18:11:15
//	@receiver s CmsService
//	@param admin models.Admin
//	@return []cms.City
func (s CmsService) GetCityList(c *gin.Context, admin models.Admin) []cms.City {
	var cityCache []cms.City
	db := tools.Db
	var cacheKey = fmt.Sprintf("a_c_l_%d", admin.ID)
	strCacheJson := tools.Remember(c, cacheKey, 5*time.Minute, func() interface{} {
		var city []cms.City
		db.Model(&cms.City{}).Select("id, name_ug, name_zh").Scopes(cms.CityScopeOffUser(admin.Type, admin.ID)).Find(&city)
		return city
	})
	err := json.Unmarshal([]byte(strCacheJson), &cityCache)
	if err != nil {
		println(err)
	}
	return cityCache
}

// GetAreaList
//
//	@Description: 获取区域列表
//	@author: Alimjan
//	@Time: 2023-06-05 18:37:22
//	@receiver s CmsService
//	@param c *gin.Context
//	@param admin models.Admin
//	@return []models.Area
func (s CmsService) GetAreaList(c *gin.Context, admin models.Admin, cityId int) []models.Area {
	var areaCache []models.Area
	db := tools.Db
	var cacheKey = fmt.Sprintf("a_a_l_%d_%d", admin.ID, cityId)
	strCacheJson := tools.Remember(c, cacheKey, 5*time.Minute, func() interface{} {
		var areas []models.Area
		tx := db.Model(&models.Area{}).Select("id, name_ug, name_zh").Scopes(cms.CityScopeOffUser(admin.Type, admin.ID))
		if cityId > 0 {
			tx.Where("city_id = ?", cityId)
		}
		tx.Find(&areas)
		return areas
	})
	err := json.Unmarshal([]byte(strCacheJson), &areaCache)
	if err != nil {
		println(err)
	}
	return areaCache
}

// GetStateList
//
//	@Description: 获取所有状态
//	@author: Alimjan
//	@Time: 2023-06-06 15:37:22
//	@receiver s CmsService
//	@param c *gin.Context
//	@return []cms.OrderState
func (s CmsService) GetStateList(c *gin.Context) []cms.OrderState {
	var stateListCache []cms.OrderState
	db := tools.Db
	var cacheKey = fmt.Sprintf("a_s_l")
	strCacheJson := tools.Remember(c, cacheKey, 5*time.Minute, func() interface{} {
		var stateList []cms.OrderState
		tx := db.Model(&cms.OrderState{}).Select("id, name_ug, name_zh").Where("deleted_at IS NULL")
		tx.Find(&stateList)
		return stateList
	})
	err := json.Unmarshal([]byte(strCacheJson), &stateListCache)
	if err != nil {
		println(err)
	}
	return stateListCache
}

// GetCategoryList
//
//	@Description: 获取业务分类
//	@author: Alimjan
//	@Time: 2023-06-06 16:10:22
//	@receiver s CmsService
//	@param c *gin.Context
//	@return []cms.Category
func (s CmsService) GetCategoryList(c *gin.Context) []cms.Category {
	var categoryListCache []cms.Category
	db := tools.Db
	var cacheKey = fmt.Sprintf("go_c_l")
	strCacheJson := tools.Remember(c, cacheKey, 5*time.Minute, func() interface{} {
		var categoryList []cms.Category
		tx := db.Model(&cms.Category{}).Select("id, name_ug, name_zh,parent_id").Where("deleted_at IS NULL").Where("state = ?", 1).Where("parent_id = ?", 0)
		tx.Preload("Categorys")
		tx.Find(&categoryList)
		return categoryList
	})
	err := json.Unmarshal([]byte(strCacheJson), &categoryListCache)
	if err != nil {
		println(err)
	}
	return categoryListCache
}

// GetOrderCancelReasonList
//
//	@Description: 获取订单取消原因列表
//	@author: Alimjan
//	@Time: 2023-06-06 16:22:07
//	@receiver s CmsService
//	@param c *gin.Context
//	@return []cms.OrderFailReason
func (s CmsService) GetOrderCancelReasonList(c *gin.Context) []cms.OrderFailReason {
	var reasonListCache []cms.OrderFailReason
	db := tools.Db
	var cacheKey = fmt.Sprintf("go_o_c_r_l")
	strCacheJson := tools.Remember(c, cacheKey, 5*time.Minute, func() interface{} {
		var reasonList []cms.OrderFailReason
		tx := db.Model(&cms.OrderFailReason{}).Select("id, reason_ug, reason_zh").Where("deleted_at IS NULL")
		tx.Find(&reasonList)
		return reasonList
	})
	err := json.Unmarshal([]byte(strCacheJson), &reasonListCache)
	if err != nil {
		println(err)
	}
	return reasonListCache
}

// GetStreetList
//
//	@Description: 获取街道列表
//	@author: Alimjan
//	@Time: 2023-06-06 17:26:58
//	@receiver s CmsService
//	@param context *gin.Context
//	@param admin models.Admin
//	@param areaId int
//	@return []cms.Street
func (s CmsService) GetStreetList(context *gin.Context, admin models.Admin, areaId int) []cms.Street {
	db := tools.Db
	var streetList []cms.Street
	db.Model(cms.Street{}).Select("id,area_id, name_ug, name_zh").
		Where("area_id = ?", areaId).
		Where("deleted_at IS NULL").
		Where("state = ?", 1).
		Scopes(cms.StreetScoreOffUser(admin.Type, admin.ID)).Find(&streetList)
	return streetList
}

// GetRestaurantList
//
//	@Description: 获取餐厅列表
//	@author: Alimjan
//	@Time: 2023-06-06 17:55:57
//	@receiver s CmsService
//	@param context *gin.Context
//	@param admin models.Admin
//	@param cityId int
//	@param areaId int
//	@param streetId int
//	@return []models.Restaurant
func (s CmsService) GetRestaurantList(context *gin.Context, admin models.Admin, cityId int, areaId int, streetId int) []models.Restaurant {
	db := tools.Db
	var resList []models.Restaurant
	tx := db.Model(models.Restaurant{}).
		Select("t_restaurant.id, t_restaurant.logo, t_restaurant.name_ug, t_restaurant.name_zh, t_restaurant.street_id").
		Joins("LEFT JOIN b_street ON t_restaurant.street_id = b_street.id").
		Joins("LEFT JOIN b_area ON b_street.area_id = b_area.id").
		Joins("LEFT JOIN b_city ON b_area.city_id = b_city.id").
		Where("t_restaurant.deleted_at IS NULL")
	if cityId > 0 {
		tx.Where("b_city.id = ?", cityId)
	}
	if areaId > 0 {
		tx.Where("b_area.id = ?", areaId)
	}
	if streetId > 0 {
		tx.Where("b_street.id = ?", streetId)
	}
	tx.Scopes(models.RestaurantScopeOffUser(admin.Type, admin.ID))
	tx.Find(&resList)
	return resList
}

// GetShipperList
//
//	@Description:
//	@author: Alimjan
//	@Time: 2023-06-06 18:57:39
//	@receiver s CmsService
//	@param context *gin.Context
//	@param admin models.Admin
//	@param resId int
//	@return []models.Admin
func (s CmsService) GetShipperList(context *gin.Context, admin models.Admin, resId int) ([]models.Admin, []struct {
	AdminId    int
	OrderCount int
}) {

	db := tools.Db
	var shipperList []models.Admin
	tx := db.Select("id", "name", "real_name").Model(models.Admin{}).Where("deleted_at IS NULL")

	if resId > 0 {
		tx = tx.Where("EXISTS (?)", db.Model(models.Restaurant{}).
			Joins("INNER JOIN b_admin_store ON t_restaurant.id = b_admin_store.store_id").
			Where("b_admin_store.admin_id = t_admin.id AND id = ? AND t_restaurant.deleted_at IS NULL", resId).
			Select("1"))
	}
	tx = tx.Where("type = ?", models.AdminTypeShipper)
	parentId := admin.GetParentId()
	if admin.Type == models.AdminTypeDealerSub {
		tx = tx.Where("(parent_id LIKE ? OR parent_id LIKE ?)", parentId+"%", "%,"+strconv.Itoa(admin.ID)+",%")
	} else {
		tx = tx.Where("parent_id LIKE ?", parentId+"%")
	}
	tx = tx.Where("state = ?", models.AdminStateOk)
	tx = tx.Order("id asc").Find(&shipperList)

	admin_ids := make([]int, len(shipperList))

	for i, admin := range shipperList {
		admin_ids[i] = admin.ID
	}
	var result []struct {
		AdminId    int
		OrderCount int
	}
	db.Table("t_take_order").
		Select("admin_id, COUNT(id) AS order_count").
		Where("admin_id IN (?) AND state = 1 AND created_at BETWEEN ? AND ?", admin_ids, tools.Today("Asia/Urumqi").ToDateTimeString(), carbon.Tomorrow("Asia/Urumqi").ToDateString()).
		Group("admin_id").
		Scan(&result)

	return shipperList, result
}

// GrantComplete
//
//	@Description: 给配送员完成订单权限
//	@author: Alimjan
//	@Time: 2023-06-07 12:08:22
//	@receiver s CmsService
//	@param context *gin.Context
//	@param orderId int
//	@return error
func (s CmsService) GrantComplete(context *gin.Context, orderId int) error {
	db := tools.Db
	var order models.OrderToday
	db.Model(models.OrderToday{}).Where("id = ?", orderId).First(&order)
	if order.ID == 0 {
		return errors.New("order_not_exits")
	}
	if order.State != models.ORDER_STATE_IN_DELIVERY {
		return errors.New("order_state_error")
	}
	//更新order.shipper_complete_grant = 1
	db.Model(models.OrderToday{}).Where("id = ?", orderId).Update("shipper_complete_grant", 1)
	return nil
}

// SetShipper
//
//	@Description: 订单分配配送员
//	@author: Alimjan
//	@Time: 2023-06-07 12:28:31
//	@receiver s CmsService
//	@param context *gin.Context
//	@param orderId int
//	@param shipperId int
//	@return error
func (s CmsService) SetShipper(context *gin.Context, orderId int, shipperId int) error {
	db := tools.Db
	var order models.OrderToday
	db.Model(models.OrderToday{}).
		Where("deleted_at IS NULL").
		Where("state in (?)", []int{models.ORDER_STATE_WAITING_ACCEPT, models.ORDER_STATE_ACCEPTED, models.ORDER_STATE_READY}).
		Where("taked = ?", models.TakeOrderClearOff).
		Where("id = ?", orderId).First(&order)
	if order.ID == 0 {
		return errors.New("order_already_taked")
	}
	//开启事务
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	//更新订单状态
	tx.Model(models.OrderToday{}).Where("id = ?", orderId).Updates(
		map[string]interface{}{
			"shipper_id":          shipperId,
			"taked":               1,
			"delivery_taked_time": carbon.Now("Asia/Urumqi").ToDateTimeString(),
		},
	)

	takeOrder := models.TakeOrder{}
	err := tx.Where("created_at BETWEEN ? AND ? AND admin_id = ? AND state = ? AND order_id = ?",
		tools.Today("Asia/Urumqi").ToDateTimeString(), carbon.Tomorrow("Asia/Urumqi").ToDateString(),
		shipperId, models.TakeOrderStateBack,
		orderId).
		First(&takeOrder).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		tx.Rollback() // 回滚事务
		return errors.New("order_not_exits")
	}

	if takeOrder.ID != 0 {
		takeOrder.State = models.TakeOrderStateTake
		takeOrder.ReasonID = nil
	} else {
		takeOrder.OrderID = orderId
		takeOrder.AdminID = shipperId
		takeOrder.State = models.TakeOrderStateTake
	}

	takeOrder.OrderPrice = order.Price + order.Shipment

	if order.ConsumeType == 0 {
		takeOrder.ConsumeType = 0
		takeOrder.IsClearing = models.TakeOrderClearOff
	} else if order.ConsumeType == 3 {
		takeOrder.ConsumeType = uint(order.ConsumeType)
		takeOrder.IsClearing = models.TakeOrderClearOff
	} else {
		takeOrder.ConsumeType = 1
		takeOrder.IsClearing = models.TakeOrderClearOff
	}

	result := tx.Save(&takeOrder).RowsAffected
	if result == 0 {
		tx.Rollback() // 回滚事务
		return errors.New("error_happended")
	}

	tx.Commit()
	tools.SendSocket(tools.SOCKET_SHIPPER, shipperId, tools.SOCKET_ADMIN_ASSIGNED_ORDER_TO_SHIPPER)
	return nil
}

func (s CmsService) GetOrderList(context *gin.Context,
	admin models.Admin,
	cityId int,
	areaId int,
	streetId int,
	resId int,
	state int,
	timeFrom string,
	timeTo string,
	isTimeOut int,
	adminId int,
	categoryId int,
	orderType int,
	page int,
	size int,
	search string,
	col int,
	sortDir string,
	sortColName string,
	deliveryType int,
) ([]map[string]interface{}, []models.TodayOrderView) {

	db := tools.Db

	todayOrder := db.Model(&models.TodayOrderView{})
	totalOrder := db.Model(&models.TodayOrderView{}).Select("consume_type, count(id) as total, sum(price + lunch_box_fee) as price, sum(shipment) as shipment, last_query_time")

	if deliveryType == 1 || deliveryType == 2 {
		if deliveryType == 1 {
			todayOrder.Where(func(db *gorm.DB) *gorm.DB {
				return db.Where("delivery_type = ?", deliveryType).Or("delivery_type is null")
			})
			totalOrder.Where(func(db *gorm.DB) *gorm.DB {
				return db.Where("delivery_type = ?", deliveryType).Or("delivery_type is null")
			})
		} else {
			todayOrder.Where("delivery_type = ?", deliveryType)
			todayOrder.Where("delivery_type = ?", deliveryType)
		}
	}
	if orderType == 1 || orderType == 0 {
		todayOrder = todayOrder.Where("order_type = ?", orderType)
	}

	if categoryId != 0 {
		todayOrder = todayOrder.Where("category_id = ?", categoryId)
		totalOrder = totalOrder.Where("category_id = ?", categoryId)
	}
	if admin.Type != models.AdminTypeOwner {
		var resIds = admin.GetRestaurantIds(context, false)
		todayOrder = todayOrder.Where("store_id IN (?)", resIds)
		totalOrder = totalOrder.Where("store_id IN (?)", resIds)
	}
	page, _ = strconv.Atoi(context.DefaultQuery("page", "1"))
	size, _ = strconv.Atoi(context.DefaultQuery("size", "10"))

	todayOrder = todayOrder.Preload("User", func(db *gorm.DB) *gorm.DB {
		return db.Select("id", "mobile", "name")
	}).Preload("OrderState").Preload("OrderDetail").Preload("LoginTime", func(db *gorm.DB) *gorm.DB {
		return db.Select("restaurant_id", "last_query_time")
	}).Offset((page - 1) * size).Limit(size)

	// todayOrder = todayOrder.Preload("OrderDetail", func(db *gorm.DB) *gorm.DB {
	// 	return db.Select("number")
	// })

	if adminId != 0 {
		todayOrder = todayOrder.Where("shipper_id = ?", adminId)
		totalOrder = totalOrder.Where("shipper_id = ?", adminId)
	}

	todayOrder = todayOrder.Preload("OrderDetail.RestaurantFoods").
		Preload("OrderStateLog.OrderFailReason").
		Preload("MarketingList")
	queryParam := map[string]string{
		"city_id":     tools.ToString(context.Query("city_id")),
		"area_id":     tools.ToString(context.Query("area_id")),
		"street_id":   tools.ToString(context.Query("street_id")),
		"res_id":      tools.ToString(context.Query("res_id")),
		"state":       tools.ToString(context.Query("state")),
		"time_from":   tools.ToString(context.Query("time_from")),
		"time_to":     tools.ToString(context.Query("time_to")),
		"is_time_out": tools.ToString(context.Query("is_time_out")),
	}
	todayOrder = SetQueryParam(context, todayOrder, admin, queryParam)
	totalOrder = SetQueryParam(context, totalOrder, admin, queryParam)
	if search != "" {
		todayOrder = todayOrder.Where("(order_id LIKE ? OR mobile LIKE ?)", "%"+search+"%", "%"+search+"%")
		totalOrder = totalOrder.Where("(order_id LIKE ? OR mobile LIKE ?)", "%"+search+"%", "%"+search+"%")
	}

	if sortColName != "" {
		if sortColName == "shipper" {
			sortColName = "taked"
		}
		if sortColName == "order_state" {
			sortColName = "state"
		}
		todayOrder = todayOrder.Order("taked ASC, " + sortColName + " " + sortDir)
	}
	//
	var totalOrderResult []map[string]interface{}
	totalOrder = totalOrder.Group("consume_type").Scan(&totalOrderResult)
	//
	var todayOrderResult []models.TodayOrderView
	todayOrder = todayOrder.Find(&todayOrderResult)

	//
	//result["total_order"] = totalOrderResult
	//result["order_list"] = todayOrderResult
	//
	var orderIDs []int
	for _, order := range todayOrderResult {
		// orderIDs = append(orderIDs, tools.ToInt(order.OrderId))
		orderIDs = append(orderIDs, tools.ToInt(order.Id))
	}

	var lunchBoxResult []map[string]interface{}
	db.Table("t_order_detail").
		Select("t_order_detail.order_id, 0 as food_id, b_lunch_box.unit_price as original_price, b_lunch_box.unit_price as price, sum(t_order_detail.lunch_box_count) as count, b_lunch_box.name_"+s.language+" as name").
		Joins("JOIN b_lunch_box ON t_order_detail.lunch_box_id = b_lunch_box.id").
		Where("b_lunch_box.id > 0").
		Group("t_order_detail.order_id, t_order_detail.lunch_box_id").
		Where("t_order_detail.order_id IN (?)", orderIDs).
		Scan(&lunchBoxResult)

	for key, order := range todayOrderResult {
		var tmp []models.LunchBoxOrderDetail
		for _, lunchBox := range lunchBoxResult {
			if order.Id == int32(tools.ToInt(lunchBox["order_id"])) {
				var lunchBoxOrderDetail models.LunchBoxOrderDetail
				lunchBoxOrderDetail.Name = tools.ToString(lunchBox["name"])
				lunchBoxOrderDetail.Number = tools.ToInt(lunchBox["count"])
				lunchBoxOrderDetail.Price = tools.ToInt(lunchBox["price"]) / 100
				sumPrice := fmt.Sprintf("%.2f", tools.ToFloat64(lunchBox["count"])*tools.ToFloat64(lunchBox["price"])/100)
				lunchBoxOrderDetail.SumPrice, _ = strconv.ParseFloat(sumPrice, 64)
				tmp = append(tmp, lunchBoxOrderDetail)
			}
		}
		todayOrderResult[key].LunchBox = tmp
	}
	return totalOrderResult, todayOrderResult
}
func GetRestaurantListIds(c *gin.Context, admin models.Admin, cityId, areaId, streetId string) []int {
	db := tools.Db
	var ids []int

	list := db.Table("t_restaurant").Select("t_restaurant.id")

	list = list.Joins("LEFT JOIN b_street ON t_restaurant.street_id = b_street.id").
		Joins("LEFT JOIN b_area ON b_street.area_id = b_area.id").
		Joins("LEFT JOIN b_city ON b_area.city_id = b_city.id")

	if streetId != "" && streetId != "0" {
		list = list.Where("b_street.id = ?", streetId)
	} else if areaId != "" && areaId != "0" {
		list = list.Where("b_area.id = ?", areaId)
	} else if cityId != "" && cityId != "0" {
		list = list.Where("b_city.id = ?", cityId)
	}

	list = list.Where("t_restaurant.state <> ?", 0).
		Where("t_restaurant.id IN (?)", admin.GetRestaurantIds(c, false))
	list.Pluck("t_restaurant.id", &ids)

	return ids
}
func SetQueryParam(c *gin.Context, db *gorm.DB, admin models.Admin, queryParam map[string]string) *gorm.DB {
	now := time.Now()
	nowTime := now.Format("2006-01-02 15:04:05")

	resIds := GetRestaurantListIds(c, admin, queryParam["city_id"], queryParam["area_id"], queryParam["street_id"])
	fmt.Printf("res ---长度----%d\n", len(resIds))
	db.Where("store_id IN (?)", resIds)

	if queryParam["restuarant_id"] != "" {
		restuarantId, _ := strconv.Atoi(queryParam["restuarant_id"])
		if restuarantId != 0 && !admin.HasResId(c, restuarantId) {
			restuarantId = 0
		}
		db.Where("store_id = ?", restuarantId)
	}
	if queryParam["state"] != "" {
		state, _ := strconv.Atoi(queryParam["state"])
		db.Where("state = ?", state)
	} else {
		db.Where("state not in (?)", []int{models.ORDER_STATE_WAITING_FOR_PAY, models.ORDER_STATE_CANCELED, models.ORDER_STATE_RESTAURANT_REJECTED})
	}

	if queryParam["time_to"] != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", queryParam["time_to"]); err == nil {
			queryParam["time_to"] = t.Format("2006-01-02 15:04:05")
		}
	}
	if queryParam["time_from"] != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", queryParam["time_from"]); err == nil {
			queryParam["time_from"] = t.Format("2006-01-02 15:04:05")
		}
	}
	if queryParam["time_from"] != "" && queryParam["time_to"] == "" {
		db.Where("booking_time >= ?", queryParam["time_from"])
	}

	if queryParam["time_from"] != "" && queryParam["time_to"] != "" {
		if queryParam["time_from"] == queryParam["time_to"] {
			db.Where("booking_time = ?", queryParam["time_from"])
		} else {
			db.Where("booking_time >= ? AND booking_time <= ?", queryParam["time_from"], queryParam["time_to"])
		}
	}

	if queryParam["is_time_out"] != "" {
		db.Where("booking_time <= ? AND state < ? AND state <> ?", nowTime, models.ORDER_STATE_DELIVERY_COMPLETED, models.ORDER_STATE_WAITING_FOR_PAY)
	}

	return db
}

// 描述：重新分配配送员
// 作者：Qurbanjan
// 文件：CmsService.go
// 修改时间：2023/08/18 19:09
func (s CmsService) EditShipper(oldShipperId int, newShipperId int, orderId int, admin models.Admin) error {
	db := tools.Db
	var order models.OrderToday
	// 查询订单是否存在
	db.Model(models.OrderToday{}).
		Where("deleted_at IS NULL").
		Where("state in (?)", []int{models.ORDER_STATE_WAITING_ACCEPT, models.ORDER_STATE_ACCEPTED, models.ORDER_STATE_READY}).
		Where("id = ?", orderId).First(&order)
	if order.ID == 0 {
		return errors.New("order_not_found")
	}

	tx := db.Begin()

	order.ShipperID = newShipperId
	order.DeliveryTakedTime = time.Now()
	var info map[string]interface{}

	// 判断json_info 字段里面是否有数据、如果有数据则反序列化数据、否则的话创建一个新切片
	if order.JSONInfo != "" {
		err := json.Unmarshal([]byte(order.JSONInfo), &info)
		if err != nil {
			tx.Rollback()
			panic("JSON 反序列化失败")
		}
	} else {
		info = make(map[string]interface{})
	}

	// 如果json_info 字段里有shipper_edit_info的话获取里面的数据
	shipperInfo, ok := info["shipper_edit_info"].([]map[string]interface{})
	if !ok {
		shipperInfo = make([]map[string]interface{}, 0)
	}

	// 准备json_info 字段里保存的数据
	infoData := map[string]interface{}{
		"admin_id":           admin.ID,
		"be_changed_shipper": oldShipperId,
		"changed_shipper":    newShipperId,
		"admin_assign":       1,
		"admin_name":         admin.RealName,
		"updated_date":       order.DeliveryTakedTime.Format("2006-01-02 15:04:05"),
	}

	// 合并新的和旧的数据
	shipperInfo = append([]map[string]interface{}{infoData}, shipperInfo...)
	info["shipper_edit_info"] = shipperInfo

	// 序列化数据
	json_info, err := json.Marshal(info)
	if err != nil {
		panic("json 序列化失败")
	}
	// 更新t_order_today 字段、更换新的配送员ID和相关数据
	if err := tx.Model(models.OrderToday{}).Where("id = ?", orderId).Updates(map[string]interface{}{
		"shipper_id":          newShipperId,
		"delivery_taked_time": order.DeliveryTakedTime.Format("2006-01-02 15:04:05"),
		"json_info":           json_info,
	}).Error; err != nil {
		tx.Rollback()
		panic("更新失败")
	}

	takeOrder := models.TakeOrder{}
	// 更新
	if err := db.Model(&takeOrder).
		Where("created_at BETWEEN ? AND ?", tools.Today("Asia/Urumqi").ToDateTimeString(), carbon.Tomorrow("Asia/Urumqi").ToDateString()).
		Where("order_id = ?", orderId).
		Update("state", models.TakeOrderStateBack).Error; err != nil {
		tx.Rollback()
		panic("更新接单状态失败")
	}

	if err := db.Where("created_at BETWEEN ? AND ?", tools.Today("Asia/Urumqi").ToDateTimeString(), carbon.Tomorrow("Asia/Urumqi").ToDateString()).
		Where("admin_id = ?", newShipperId).
		Where("order_id = ?", orderId).
		First(&takeOrder).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			tx.Rollback()
			panic("查询接单信息失败")
		}
		takeOrder = models.TakeOrder{
			OrderID:    orderId,
			AdminID:    newShipperId,
			State:      models.TakeOrderStateTake,
			OrderPrice: order.Price + order.Shipment,
		}
	} else {
		takeOrder.State = models.TakeOrderStateBack
		takeOrder.AdminID = newShipperId
		takeOrder.ReasonID = nil
	}

	switch order.ConsumeType {
	case 0:
		takeOrder.ConsumeType = 0
		takeOrder.IsClearing = models.TakeOrderClearOff
	case 3:
		takeOrder.ConsumeType = uint(order.ConsumeType)
		takeOrder.IsClearing = models.TakeOrderClearOff
	default:
		takeOrder.ConsumeType = 1
		takeOrder.IsClearing = models.TakeOrderClearOK
	}

	if err := tx.Save(&takeOrder).Error; err != nil {
		tx.Rollback()
		panic("保存接单订单失败")
	}

	tx.Commit()
	// tools.SendSocket(tools.SOCKET_SHIPPER, newShipperId, tools.SOCKET_ADMIN_ASSIGNED_ORDER_TO_SHIPPER)

	return nil
}

func (s CmsService) OrderDetail(admin models.Admin, orderID int) models.OrderToday {
	var orderToday models.OrderToday
	tools.Db.Model(&orderToday).
		Select("t_order_today.*,sum(res_profit) as res_income,order_price_res").
		Preload("Shipper").
		Preload("OrderExtend").

		//Preload("OrderDetail.RestaurantFoods").
		Preload("OrderDetail.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取美食已选规格数据
		Preload("OrderDetail.RestaurantFoods.ComboFoodItems.RestaurantFood"). // 获取套餐中的美食数据
		Preload("OrderDetail.RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取套餐中的已选规格数据

		Preload("LunchBox", func(db *gorm.DB) *gorm.DB {
			return db.Select(`	
	0 AS food_id,
	b_lunch_box.unit_price AS original_price,
	b_lunch_box.unit_price AS price,
	sum( t_order_detail.lunch_box_count ) AS count,
	b_lunch_box.name_` + s.language + ` AS name,
	t_order_detail.order_id `).Joins("INNER JOIN b_lunch_box ON t_order_detail.lunch_box_id = b_lunch_box.id ").
				Where("t_order_detail.deleted_at is null").
				Group("t_order_detail.order_id,t_order_detail.lunch_box_id")
		}).
		Preload("Restaurant").
		Preload("OrderState").
		Preload("PayTypes").
		Preload("Terminal").
		Preload("User").
		Where("id =?", orderID).
		Group("t_order_today.id").
		Find(&orderToday)
	if orderToday.ID > 0 {
		return orderToday
	} else {
		var order models.Order
		tools.Db.Model(&order).
			Select("t_order.*,res_profit as res_income,order_price_res").
			Preload("Shipper").
			Preload("OrderExtend").

			//Preload("OrderDetail.RestaurantFoods").
			Preload("OrderDetail.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取美食已选规格数据
			Preload("OrderDetail.RestaurantFoods.ComboFoodItems.RestaurantFood"). // 获取套餐中的美食数据
			Preload("OrderDetail.RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取套餐中的已选规格数据

			Preload("LunchBox", func(db *gorm.DB) *gorm.DB {
				return db.Select(`	
		0 AS food_id,
		b_lunch_box.unit_price AS original_price,
		b_lunch_box.unit_price AS price,
		sum( t_order_detail.lunch_box_count ) AS count,
		b_lunch_box.name_` + s.language + ` AS name,
		t_order_detail.order_id `).Joins("INNER JOIN b_lunch_box ON t_order_detail.lunch_box_id = b_lunch_box.id ").
					Where("t_order_detail.deleted_at is null").
					Group("t_order_detail.order_id,t_order_detail.lunch_box_id")
			}).
			Preload("Restaurant").
			Preload("OrderState").
			Preload("PayTypes").
			Preload("Terminal").
			Preload("User").
			Where("id = ?", orderID).
			Group("t_order.id").
			Find(&order)
		// Convert Order to OrderToday
		orderToday := models.OrderToday{
			ID:            order.ID,
			OrderID:       order.OrderID,
			AreaID:        order.AreaID,
			TerminalID:    order.TerminalID,
			CategoryID:    order.CategoryID,
			StoreID:       order.StoreID,
			UserID:        order.UserID,
			Name:          order.Name,
			Mobile:        order.Mobile,
			State:         order.State,
			CityID:        order.CityID,
			BuildingID:    order.BuildingID,
			ConsumeType:   order.ConsumeType,
			PayType:       order.PayType,
			OriginalPrice: order.OriginalPrice,
			Price:         order.Price,
			Shipment:      order.Shipment,
			LunchBoxFee:   order.LunchBoxFee,
			MpProfit:      order.MpProfit,
			DealerProfit:  order.DealerProfit,
			Cash:          order.Cash,
			Coin:          order.Coin,
			Consume:       order.Consume,
			Description:   order.Description,
			BookingTime:   order.BookingTime,
			//PrintedTime:         order.PrintedTime,
			PrintTime: order.PrintTime,
			//TimeZone:            order.TimeZone,
			PayTime:           order.PayTime,
			Taked:             order.Taked,
			SerialNumber:      order.SerialNumber,
			ShipperID:         order.ShipperID,
			OrderType:         order.OrderType,
			DeleteFlag:        order.DeleteFlag,
			RefundChanel:      order.RefundChanel,
			CreatedAt:         order.CreatedAt,
			UpdatedAt:         order.UpdatedAt,
			DeletedAt:         order.DeletedAt,
			//BuildingLat:         order.BuildingLat,
			//BuildingLng:         order.BuildingLng,
			//LastQueryTime:       order.LastQueryTime,
			DeliveryType: order.DeliveryType,
			//JSONInfo:            order.JSONInfo,
			//PayChannel:          order.PayChannel,
			PayPlatform:          order.PayPlatForm, // Note: Field name mismatch PayPlatForm vs PayPlatform
			SelfTakeNumber:       order.SelfTakeNumber,
			OrderAddress:         order.OrderAddress,
			OrderDetail:          order.OrderDetail,
			User:                 order.User,
			Terminal:             order.Terminal,
			PayTypes:             order.PayTypes,
			OrderState:           order.OrderState,
			Restaurant:           order.Restaurant,
			Shipper:              order.Shipper,
			OrderExtend:          &order.OrderExtend,
			LunchBox:             order.LunchBox,
			OriginalShipment:     order.OriginalShipment,
			TotalDiscountAmount:  order.TotalDiscountAmount,
			ActualPaid:           order.ActualPaid,
			DeliveryTakedTime:    order.DeliveryTakedTime,
			ResIncome:            int(order.ResIncome),
			OrderPriceRes:        order.OrderPriceRes,
			IsAutoRefund:         order.IsAutoRefund,
			ShipperCompleteGrant: order.ShipperCompleteGrant,
		}
		if order.DeliveryStartTime !=  nil {
			orderToday.DeliveryStartTime = *order.DeliveryStartTime
		}
		if order.DeliveryEndTime !=  nil {
			orderToday.DeliveryEndTime = *order.DeliveryEndTime
		}
		return orderToday
	}
}

// 代理意愿 列表 获取
func (s CmsService) AgentForm(ctx *gin.Context,page int,limit int,orderBy string) map[string]interface{} {


	db := tools.GetDB()

	var total int64
	result :=make(map[string]interface{})
	items :=make([]map[string]interface{},0)
	query :=db.Table("t_agent_intention").
		Order(orderBy)

	query.Count(&total)

	query.Limit(limit).
		Offset((page - 1) * limit).
		Find(&items)
	for k, v := range items {
		items[k]["created_at"] = carbon.Parse(tools.ToString(v["created_at"]),configs.AsiaShanghai).Format("Y-m-d H:i:s")
		items[k]["updated_at"] = carbon.Parse(tools.ToString(v["updated_at"]),configs.AsiaShanghai).Format("Y-m-d H:i:s")
	}
	result["per_page"]=limit
	result["current_page"]=page
	result["total"]=total
	result["items"]=items


	return result

}