package cms

import (
	"encoding/json"
	"errors"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/scopes"
	"mulazim-api/tools"

	cmsResource "mulazim-api/resources/cms"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type AutoDispatchService struct {
	langUtil *lang.LangUtil
	language string
}

func NewAutoDispatchService(c *gin.Context) *AutoDispatchService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	autoDispatchService := AutoDispatchService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &autoDispatchService
}

// Detail
//
// @Description: 配送费模板详情
// @Author: Rixat
// @Time: 2023-11-03 08:02:47
// @receiver
// @param c *gin.Context
func (s AutoDispatchService) Detail(ID int) (map[string]interface{}, error) {
	var area models.Area	
	tools.Db.Model(area).Where("id=?", ID).Preload("City").Find(&area)
	if area.ID == 0 {
		return nil, errors.New("not_found")
	}
	// 格式化返回结果
	autoDispatchPeakTime := make([]map[string]string, 0)
	if area.AutoDispatchPeakTime != nil {
		json.Unmarshal([]byte(*area.AutoDispatchPeakTime), &autoDispatchPeakTime)
	}
	result := map[string]interface{}{
		"id":                       area.ID,
		"city_name":                tools.GetNameByLang(area.City,s.language),
		"area_name":                tools.GetNameByLang(area,s.language),
		"auto_dispatch_state":      area.AutoDispatchState,
		"auto_dispatch_wait":       area.AutoDispatchWait,
		"auto_dispatch_parallel_distance": area.AutoDispatchParallelDistance,
		"auto_dispatch_peak_time":  autoDispatchPeakTime,
		"auto_dispatch_peak_state": area.AutoDispatchPeakState,
		"auto_dispatch_delivery_time": area.AutoDispatchDeliveryTime,
		"auto_dispatch_take_food_time": area.AutoDispatchTakeFoodTime,
		"auto_dispatch_shipper_send_food_customer_time": area.AutoDispatchShipperSendFoodCustomerTime,
		"auto_dispatch_shipper_to_restaurant_distance": area.AutoDispatchShipperToRestaurantDistance,
		"auto_dispatch_special_order_peak_state": area.AutoDispatchSpecialOrderPeakState,
		"auto_dispatch_not_take_order_threshold_time": area.AutoDispatchNotTakeOrderThresholdTime,
		"auto_dispatch_same_building_restaurant_seckill_order": area.AutoDispatchSameBuildingRestaurantSeckillOrder,
		"auto_dispatch_same_building_restaurant_special_price_order": area.AutoDispatchSameBuildingRestaurantSpecialPriceOrder,
		"auto_dispatch_same_building_restaurant_normal_order": area.AutoDispatchSameBuildingRestaurantNormalOrder, 
		"auto_dispatch_return_order_state": area.AutoDispatchReturnOrderState, 
		"auto_dispatch_return_order_distance": area.AutoDispatchReturnOrderDistance, 
		"auto_dispatch_enable_rank": area.AutoDispatchEnableRank,
	}
	return result, nil
}

// List
//
// @Description: 智能派单参数列表
// @Author: Rixat
// @Time: 2024-08-13 18:11:20
// @receiver 
// @param c *gin.Context
func (s AutoDispatchService) List(page int, limit int, cityId int, areaId int, kw string,autoDispatchState int,autoDispatchPeakState int) map[string]interface{} {
	var areaList []models.Area
	query := tools.Db.Model(areaList).Where("state=?",1)
	if cityId > 0 {
		query.Where("city_id = ?", cityId)
	}
	if areaId > 0 {
		query.Where("id = ?", areaId)
	}
	if autoDispatchState > 0 {
		query.Where("auto_dispatch_state = ?", autoDispatchState)
	}
	if autoDispatchPeakState > 0 {
		query.Where("auto_dispatch_peak_state = ?", autoDispatchPeakState)
	}
	if len(kw) > 0 {
		query = query.Where("name_ug like ? or name_zh like ?", "%"+kw+"%", "%"+kw+"%")
	}
	// 获取数据
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Scopes(scopes.Page(page, limit)).Order("id").Preload("City").Find(&areaList)
	}
	// 格式化列表内容
	items := make([]map[string]interface{}, 0)
	for _, value := range areaList {
		autoDispatchPeakTime := make([]map[string]string, 0)
		if value.AutoDispatchPeakTime != nil {
			json.Unmarshal([]byte(*value.AutoDispatchPeakTime), &autoDispatchPeakTime)
		}
		
		items = append(items, map[string]interface{}{
			"id":                       value.ID,
			"city_name":                tools.GetNameByLang(value.City,s.language),
			"area_name":                tools.GetNameByLang(value,s.language),
			"auto_dispatch_state":      value.AutoDispatchState,
			"auto_dispatch_wait":       value.AutoDispatchWait,
			"auto_dispatch_parallel_distance": value.AutoDispatchParallelDistance,
			"auto_dispatch_peak_time":  autoDispatchPeakTime,
			"auto_dispatch_peak_state": value.AutoDispatchPeakState,
			"auto_dispatch_delivery_time": value.AutoDispatchDeliveryTime,
			"auto_dispatch_take_food_time": value.AutoDispatchTakeFoodTime,
			"auto_dispatch_shipper_send_food_customer_time": value.AutoDispatchShipperSendFoodCustomerTime,
			"auto_dispatch_shipper_to_restaurant_distance": value.AutoDispatchShipperToRestaurantDistance,
			"auto_dispatch_special_order_peak_state": value.AutoDispatchSpecialOrderPeakState,
			"auto_dispatch_enable_rank": value.AutoDispatchEnableRank,
		})
	}
	result := map[string]interface{}{
		"total": totalCount,
		"items": items,
	}
	return result
}


func (s AutoDispatchService) Edit(autoDispatchParams cmsResource.AutoDispatchParam) error {
	var area models.Area
	tools.Db.Model(area).Where("id=?",autoDispatchParams.ID).First(&area)
	if area.ID == 0 {
		return errors.New("not_found")
	}
	// 验证时间端
	peakTimes := tools.StringToMapArr(autoDispatchParams.AutoDispatchPeakTime)
	for index, value := range peakTimes {
		// 验证时间段是否小到大顺序，并时间段不能交叉
		startTimeCarbon := carbon.Parse(carbon.Now().ToDateString() + " " + value["begin_time"].(string))
		endTimeCarbon := carbon.Parse(carbon.Now().ToDateString() + " " + value["end_time"].(string))
		if endTimeCarbon.Lte(startTimeCarbon) {
			return errors.New("min_time_not_greater_max_time")
		}
		if index > 0 {
			frontStepEndTimeCarbon := carbon.Parse(carbon.Now().ToDateString() + " " + peakTimes[index-1]["end_time"].(string))
			if startTimeCarbon.Lt(frontStepEndTimeCarbon) {
				return errors.New("time_must_asc_and_not_conflict")
			}
		}
	}
	err := tools.Db.Model(area).Where("id",autoDispatchParams.ID).Updates(map[string]interface{}{
		"auto_dispatch_state": autoDispatchParams.AutoDispatchState,
		"auto_dispatch_wait": autoDispatchParams.AutoDispatchWait,
		"auto_dispatch_parallel_distance": autoDispatchParams.AutoDispatchParallelDistance,
		"auto_dispatch_peak_state": autoDispatchParams.AutoDispatchPeakState,
		"auto_dispatch_peak_time": autoDispatchParams.AutoDispatchPeakTime,
		"auto_dispatch_delivery_time": autoDispatchParams.AutoDispatchDeliveryTime,
		"auto_dispatch_take_food_time": autoDispatchParams.AutoDispatchTakeFoodTime,
		"auto_dispatch_shipper_send_food_customer_time": autoDispatchParams.AutoDispatchShipperSendFoodCustomerTime,
		"auto_dispatch_shipper_to_restaurant_distance": autoDispatchParams.AutoDispatchShipperToRestaurantDistance,
		"auto_dispatch_special_order_peak_state": autoDispatchParams.AutoDispatchSpecialOrderPeakState,
		"auto_dispatch_not_take_order_threshold_time": autoDispatchParams.AutoDispatchNotTakeOrderThresholdTime,
		"auto_dispatch_same_building_restaurant_seckill_order": autoDispatchParams.AutoDispatchSameBuildingRestaurantSeckillOrder,
		"auto_dispatch_same_building_restaurant_special_price_order": autoDispatchParams.AutoDispatchSameBuildingRestaurantSpecialPriceOrder,
		"auto_dispatch_same_building_restaurant_normal_order": autoDispatchParams.AutoDispatchSameBuildingRestaurantNormalOrder,               // 智能派单普通订单同一个餐厅和建筑分配一个配送员配置
		"auto_dispatch_return_order_state": autoDispatchParams.AutoDispatchReturnOrderState,               // 智能派单普通订单同一个餐厅和建筑分配一个配送员配置
		"auto_dispatch_return_order_distance": autoDispatchParams.AutoDispatchReturnOrderDistance,               // 智能派单普通订单同一个餐厅和建筑分配一个配送员配置
		"auto_dispatch_enable_rank": autoDispatchParams.AutoDispatchEnableRank,               // 智能派单普通订单同一个餐厅和建筑分配一个配送员配置
	}).Error
	if err != nil {
		return errors.New("update_failed")
	}
	return nil
}


// ChangeAutoDispatchState
//
// @Description: 修改智能派单状态
// @Author: Rixat
// @Time: 2024-08-16 12:25:00
// @receiver 
// @param c *gin.Context
func (s AutoDispatchService) ChangeAutoDispatchState(ID,autoDispatchState int) error {
	var area models.Area
	tools.Db.Model(area).Where("id=?",ID).First(&area)
	if area.ID == 0 {
		return errors.New("not_found")
	}
	err := tools.Db.Model(&area).Where("id=?",ID).UpdateColumn("auto_dispatch_state",autoDispatchState).Error
	if err != nil {
		return errors.New("update_failed")
	}
	return nil
}


// ChangeAutoDispatchState
//
// @Description: 修改智能派单状态
// @Author: Rixat
// @Time: 2024-08-16 12:25:00
// @receiver 
// @param c *gin.Context
func (s AutoDispatchService) ChangeAutoDispatchPeakState(ID,autoDispatchPeakState int) error {
	var area models.Area
	tools.Db.Model(area).Where("id=?",ID).First(&area)
	if area.ID == 0 {
		return errors.New("not_found")
	}
	err := tools.Db.Model(&area).Where("id=?",ID).UpdateColumn("auto_dispatch_peak_state",autoDispatchPeakState).Error
	if err != nil {
		return errors.New("update_failed")
	}
	return nil
}



// ChangeAutoDispatchState
//
// @Description: 修改智能派单状态
// @Author: Rixat
// @Time: 2024-08-16 12:25:00
// @receiver 
// @param c *gin.Context
func (s AutoDispatchService) ChangeAutoDispatchSpecialOrderPeakState(ID,autoDispatchSpecialOrderPeakState int) error {
	var area models.Area
	tools.Db.Model(area).Where("id=?",ID).First(&area)
	if area.ID == 0 {
		return errors.New("not_found")
	}
	err := tools.Db.Model(&area).Where("id=?",ID).UpdateColumn("auto_dispatch_special_order_peak_state",autoDispatchSpecialOrderPeakState).Error
	if err != nil {
		return errors.New("update_failed")
	}
	return nil
}


//派单历史记录
func (s AutoDispatchService) HistoryList(page int, limit int, cityId int, areaId int,orderNo string,shipperMobile string,startDate string,EndDate string,shipperId int) cmsResource.AutoDispatchHistoryList {
	now := carbon.Now(configs.AsiaShanghai)
	var historyList []models.AutoDispatchHistory
	query := tools.Db.Model(&models.AutoDispatchHistory{})
	if cityId > 0 {
		query.Where("city_id = ?", cityId)
	}
	if areaId > 0 {
		query.Where("area_id = ?", areaId)
	}
	if page == 0 {
		page = 1
	}
	if limit == 0 {
		limit = 10
	}
	if len(orderNo) > 0 {
		query.Where("order_no = ?", orderNo)
	}
	if len(shipperMobile) > 0 {
		var admin models.AdminBase
		tools.Db.Model(&models.AdminBase{}).Where("mobile = ? and type in (8,9)", shipperMobile).First(&admin)
		if admin.ID == 0 {
			return cmsResource.AutoDispatchHistoryList{}
		}
		shipperId := admin.ID
		query.Where("shipper_id = ?", shipperId)
	}
	if shipperId > 0 {
		query.Where("shipper_id = ?", shipperId)
	}
	if len(startDate) > 0  {
		query.Where("created_at >= ? ", startDate+" 00:00:00")
	}else{ //当前时间为空的话 要查询3个月内的记录
		query.Where("created_at >= ? ", now.AddMonths(-3).Format("Y-m-d")+" 00:00:00")
	}
	if len(EndDate) > 0 {
		query.Where("created_at < ? ", EndDate+" 23:59:59")
	}
	// 获取数据
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Scopes(scopes.Page(page, limit)).Order("id").Preload("City").Preload("Area").Preload("Shipper").Find(&historyList)
	}
	// 格式化列表内容
	var items []cmsResource.AutoDispatchHistory
	for _, value := range historyList {

		items = append(items,cmsResource.AutoDispatchHistory{
			ID: value.ID,
			// CityID: value.CityID,
			CityName: tools.GetNameByLang(value.City, s.language),
			// AreaID:value.AreaID,
			AreaName: tools.GetNameByLang(value.Area, s.language),
			OrderNo: value.OrderNo,
			OrderID: value.OrderID,
			// OrderRank: value.OrderRank,
			// OrderScore: value.OrderScore,
			Remark: value.Remark,
			ShipperID: value.ShipperID,
			ShipperName: value.Shipper.RealName,
			ShipperMobile: value.Shipper.Mobile,
			ShipperLat: value.ShipperLat,
			ShipperLng: value.ShipperLng,
			ShipperOrderInfo: value.ShipperOrderInfo,
			ShipperRank: value.ShipperRank,
			ShipperScore: value.ShipperScore,
			// UserID: value.UserID,
			// UserName: tools.GetNameByLang(value.User, s.language),
			// UserRank: value.UserRank,
			// UserScore: value.UserScore,
			CreatedAt: value.CreatedAt.Format("2006-01-02 15:04:05"),
			// UpdatedAt: value.UpdatedAt.Format("2006-01-02 15:04:05"),

		} )
	}
	if len(items) == 0 {
		items = []cmsResource.AutoDispatchHistory{}
	}
	result := cmsResource.AutoDispatchHistoryList{
		List: items,
		Total: int(totalCount),
	}
	return result
}


