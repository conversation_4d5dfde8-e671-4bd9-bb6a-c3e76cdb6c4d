package shipper

import (
	"encoding/json"
	"errors"
	"fmt"
	 
	"mulazim-api/services"

	"mulazim-api/models"
	"mulazim-api/models/shipment"
	shipmentResource "mulazim-api/resources/cms/shipment"
	"mulazim-api/tools"
	"time"

	"github.com/golang-module/carbon/v2"
)

type ShipperFeeService struct {
	services.BaseService
	ShipperIncomeTemplate          shipment.ShipperIncomeTemplate
	FixShipmentFeeRule             *shipment.FixShipmentFeeRule
	DistanceBaseShipmentFeeRules   []shipment.DistanceBaseShipmentFeeRule
	TaxiBaseShimentFeeRule         *shipment.TaxiBaseShipmentFeeRule
	OrderCountBaseShipmentFeeRules []shipment.OrderCountBaseShipmentFeeRule
}

// new shipper fee service
func NewShipperFeeService() *ShipperFeeService {
	return &ShipperFeeService{}
}

// CalculateShipperFee 运费计算
func (s *ShipperFeeService) CalculateShipperFee(shipperId int,template shipment.ShipperIncomeTemplate, order models.OrderToday, nthOrder int) (orderPrice int, specialTimePrice int, specialWeatherPrice int, err error) {
	s.ShipperIncomeTemplate = template

	price := 0
	// 如果特价活动订单，查询特价活动设置的配送员收入
	if order.MarketType == 2 {
		price = order.GetSpecialOrderShipperIncome()
	}
	// 如果没有设置收入，按普通订单计算配送员收入
	if price == 0 {
		price, err = s.calculateBaseShipmentPrice(shipperId, template, order.Distance, nthOrder)
		if err != nil {
			return 0, 0, 0, err
		}
	}
	orderPrice = price
	if s.ShipperIncomeTemplate.IsSpecialShipmentPriceActive() {
		timeStr := carbon.Parse(order.BookingTime).Format("H:i")
		if len(timeStr) == 0 {
			tools.Logger.Error("订单:", order.ID, " booktime 转换time类型错误")
			return price, specialTimePrice, specialWeatherPrice, err
		}

		specialPrice, err := s.ShipperIncomeTemplate.GetSpecialShipmentPrice(timeStr, tools.ToInt(order.Distance*1000))
		if err != nil {
			tools.Logger.Error("订单:", order.ID, " 获取特殊配送价失败", err)
			return price, specialTimePrice, specialWeatherPrice, err
		}
		specialTimePrice = specialPrice
	}
	timeStr := carbon.Parse(order.BookingTime).Format("Y-m-d H:i:s")
	if len(timeStr) == 0 {
		return price, specialTimePrice, specialWeatherPrice, err
	}

	// 特殊添加配送价计算
	specialWeather := s.getSpecialWeather(order.AreaID, carbon.Parse(order.BookingTime).Carbon2Time(),1)

	if specialWeather.ID > 0 {
		specialWeatherPrice = specialWeather.ShipmentFee
	}

	return price, specialTimePrice, specialWeatherPrice, nil
}
// CalculateShipperFee 运费计算 包括特殊天气id
func (s *ShipperFeeService) CalculateArchiveShipperFeeAndWeatherId(shipperId int,template shipment.ShipperIncomeTemplate, order models.Order, nthOrder int) (shippingPrice int, specialTimePrice int, specialWeatherPrice int,specialWeatherId int, err error) {
	s.ShipperIncomeTemplate = template
	price, err := s.calculateBaseShipmentPrice(shipperId,template,order.Distance, nthOrder)
	if err != nil {
		return 0, 0, 0, 0,err
	}
	shippingPrice = price
	if s.ShipperIncomeTemplate.IsSpecialShipmentPriceActive() {
		timeStr := carbon.Parse(order.BookingTime).Format("H:i")
		if len(timeStr) == 0 {
			tools.Logger.Error("订单:", order.ID, " booktime 转换time类型错误")
			return price, specialTimePrice, specialWeatherPrice,0, err
		}

		specialPrice, err := s.ShipperIncomeTemplate.GetSpecialShipmentPrice(timeStr, tools.ToInt(order.Distance*1000))
		if err != nil {
			tools.Logger.Error("订单:", order.ID, " 获取特殊配送价失败", err)
			return price, specialTimePrice, specialWeatherPrice,0, err
		}
		specialTimePrice = specialPrice
	}
	timeStr := carbon.Parse(order.BookingTime).Format("Y-m-d H:i:s")
	if len(timeStr) == 0 {
		return price, specialTimePrice, specialWeatherPrice,0,err
	}

	// 特殊添加配送价计算
	specialWeather := s.getSpecialWeather(order.AreaID, carbon.Parse(order.BookingTime).Carbon2Time(),0)
	weatherId :=0
	if specialWeather.ID > 0 {
		//特殊天气的数据要从 pushdetail 获取 
		db :=tools.GetDB()
		var detail models.ShipperOrderPushDetail
		db.Model(&models.ShipperOrderPushDetail{}).Where("shipper_id = ? and order_id = ?",shipperId,order.ID).Find(&detail)
		if detail.ID > 0 && detail.SpecialWeatherShippingPrice > 0{
			specialWeatherPrice = detail.SpecialWeatherShippingPrice
			weatherId = specialWeather.ID
		}
		// specialWeatherPrice = specialWeather.ShipmentFee
	}

	return price, specialTimePrice, specialWeatherPrice, weatherId, nil
}
//获取特殊天气时的价格
func (s *ShipperFeeService) getSpecialWeather(areaId int, bookingTime time.Time,state int) shipment.ShipperSpecialWeather {
	var specialWeather shipment.ShipperSpecialWeather
	db :=tools.GetDB()
		q :=db.Model(&shipment.ShipperSpecialWeather{}).Where("area_id = ?", areaId).
		Where("end_time >= ? and start_time <= ?", bookingTime, bookingTime)
		if state > 0 {//计算推荐配送的时候 要看开启的状态    归档的时候不看状态 
			q=q.Where("state = ?", shipment.SHIPPER_SPECIAL_WEATHER_SATE_ON)
		}
		q.First(&specialWeather)
	return specialWeather
}

func (s *ShipperFeeService) calculateBaseShipmentPrice(shipperId int,template shipment.ShipperIncomeTemplate,orderDistance float64, nthOrder int) (int, error) {

	// 根据配送费类型计算配送费
	switch s.ShipperIncomeTemplate.RuleType {
	case shipment.RuleTypeFixed:
		return s.getFixShipmentPrice()
	case shipment.RuleTypeDistance:
		return s.getDistanceBaseShipmentPrice(tools.ToInt(orderDistance * 1000))
	case shipment.RuleTypeCar:
		return s.getTaxiBaseShipmentPrice(tools.ToInt(orderDistance * 1000))
	case shipment.RuleTypeOrder:
		return s.getOrderCountBaseShipmentPrice(shipperId,template,nthOrder)
	default:
		msg := fmt.Sprintf("ID:%d计费模板类型%d错误", s.ShipperIncomeTemplate.ID, s.ShipperIncomeTemplate.RuleType)
		return 0, errors.New(msg)
	}
}

// getFixShipmentPrice 获取固定配送费
func (s *ShipperFeeService) getFixShipmentPrice() (int, error) {
	if s.FixShipmentFeeRule == nil {
		var err error
		s.FixShipmentFeeRule, err = s.ShipperIncomeTemplate.GetFixShipmentFeeRules()
		if err != nil {
			return 0, err
		}
	}
	return s.FixShipmentFeeRule.FixShipmentFee, nil
}

// getDistanceBaseShipmentPrice 获取按距离计算配送费
// template 配送费模板
// distance 单位米
// return 单位分
func (s *ShipperFeeService) getDistanceBaseShipmentPrice(distance int) (int, error) {
	if s.DistanceBaseShipmentFeeRules == nil {
		var err error
		s.DistanceBaseShipmentFeeRules, err = s.ShipperIncomeTemplate.GetDistanceBaseShipmentFeeRules()
		if err != nil {
			return 0, err
		}
	}
	price := 0
	for _, rule := range s.DistanceBaseShipmentFeeRules {
		if distance < rule.Distance {
			return rule.ShipmentFee, nil
		}

	}
	return price, nil
}

func (s *ShipperFeeService) getTaxiBaseShipmentPrice(distance int) (int, error) {
	if s.TaxiBaseShimentFeeRule == nil {
		var err error
		s.TaxiBaseShimentFeeRule, err = s.ShipperIncomeTemplate.GetTaxiBaseShipmentFeeRules()
		if err != nil {
			return 0, err
		}
	}
	price := s.TaxiBaseShimentFeeRule.FixedStartFee
	if distance > s.TaxiBaseShimentFeeRule.Distance {
		price += (distance - s.TaxiBaseShimentFeeRule.Distance) * s.TaxiBaseShimentFeeRule.PricePerKilometer / 1000
	}
	return price, nil
}

// getOrderCountBaseShipmentPrice 获取按订单数量计算配送费
// template 配送费模板
// admin 管理员
// nthOrder 第几单
// return 单位分
// return error
func (s *ShipperFeeService) getOrderCountBaseShipmentPrice(shipperId int,template shipment.ShipperIncomeTemplate,nthOrder int) (int, error) {
	if s.OrderCountBaseShipmentFeeRules == nil {
		var err error
		s.OrderCountBaseShipmentFeeRules, err = template.GetOrderCountBaseShipmentFeeRules()
		if err != nil {
			return 0, err
		}
	}
	price := 0
	for _, rule := range s.OrderCountBaseShipmentFeeRules {
		if nthOrder <= rule.StartOrderCount {
			return rule.ShipmentFee, nil //修复从0开始的时候的bug
		}
		price = rule.ShipmentFee
	}
	return price, nil
}

// CalculateLateOrderFee 计算迟到订单扣费
func (s *ShipperFeeService) CalculateLateOrderFee(template shipment.ShipperIncomeTemplate, order models.Order,shippingPrice int) (lateFee int) {
	// 特价活动迟到扣款逻辑
	if order.MarketType == 2{
		var seckillLog models.SeckillLog
		tools.Db.Model(seckillLog).Where("order_id", order.ID).Preload("Seckill.SeckillMarket").Find(&seckillLog)
		lateContent := seckillLog.Seckill.SeckillMarket.LateDeductRule
		var lateDeductionTimeArr []shipmentResource.LateDeductionTime
		json.Unmarshal([]byte(lateContent), &lateDeductionTimeArr)
		if len(lateDeductionTimeArr) > 0 {
			for _, la := range lateDeductionTimeArr {
				if order.LateOrders.LateMinute >= la.MinMinute && 	order.LateOrders.LateMinute <  la.MaxMinute {
					lateFee =la.DeductionFee			
				}
			}
			// 如果特价活动设置的扣款金额大于收入时，扣款替换收入
			if lateFee > shippingPrice && shippingPrice >0{
				lateFee = shippingPrice
			}
			return lateFee
		}
		
	}
	switch template.LateDeductionType {
		case 1://1:按订单数量
			lateContent :=template.LateDeductionContent		
			var lateDeductionFixArr []shipmentResource.LateDeductionFix
			json.Unmarshal([]byte(*lateContent), &lateDeductionFixArr)
			if len(lateDeductionFixArr) > 0 {
				lateFee = lateDeductionFixArr[0].DeductionFee
			}
		case 2://2:迟到时间
		if template.LateDeductionContent !=nil {
			lateContent :=template.LateDeductionContent
			var lateDeductionTimeArr []shipmentResource.LateDeductionTime
			json.Unmarshal([]byte(*lateContent), &lateDeductionTimeArr)
			if len(lateDeductionTimeArr) > 0 {

				for _, la := range lateDeductionTimeArr {
					if order.LateOrders.LateMinute >= la.MinMinute && 	order.LateOrders.LateMinute <  la.MaxMinute {
						lateFee =la.DeductionFee			
					}
				}

			}
			
		}
			
	}
	return lateFee
}




// CalculateLateOrderFee 计算迟到订单扣费
func (s *ShipperFeeService) CheckLateOrderFee(template shipment.ShipperIncomeTemplate,order models.Order,lateMinute int) (lateFee int) {

	// 特价活动迟到扣款逻辑
	if order.MarketType == 2{
		var seckillLog models.SeckillLog
		tools.Db.Model(seckillLog).Where("order_id", order.ID).Preload("Seckill.SeckillMarket").Find(&seckillLog)
		lateContent := seckillLog.Seckill.SeckillMarket.LateDeductRule
		var lateDeductionTimeArr []shipmentResource.LateDeductionTime
		json.Unmarshal([]byte(lateContent), &lateDeductionTimeArr)
		if len(lateDeductionTimeArr) > 0 {
			for _, la := range lateDeductionTimeArr {
				if order.LateOrders.LateMinute >= la.MinMinute && 	order.LateOrders.LateMinute <  la.MaxMinute {
					lateFee =la.DeductionFee			
				}
			}
			shippingPrice :=seckillLog.Seckill.SeckillMarket.ShipperIncome
			// 如果特价活动设置的扣款金额大于收入时，扣款替换收入
			if lateFee > shippingPrice && shippingPrice >0{
				lateFee = shippingPrice
			}
			return lateFee
		}
		// return lateFee
	}
	switch template.LateDeductionType {
		case 1://1:按订单数量
			lateContent :=template.LateDeductionContent		
			var lateDeductionFixArr []shipmentResource.LateDeductionFix
			json.Unmarshal([]byte(*lateContent), &lateDeductionFixArr)
			if len(lateDeductionFixArr) > 0 {
				lateFee = lateDeductionFixArr[0].DeductionFee
			}
		case 2://2:迟到时间
		if template.LateDeductionContent !=nil {
			lateContent :=template.LateDeductionContent
			var lateDeductionTimeArr []shipmentResource.LateDeductionTime
			json.Unmarshal([]byte(*lateContent), &lateDeductionTimeArr)
			if len(lateDeductionTimeArr) > 0 {

				for _, la := range lateDeductionTimeArr {
					if lateMinute >= la.MinMinute && 	lateMinute <  la.MinMinute {
						lateFee =la.DeductionFee			
					}
				}

			}
			
		}
			
	}
	return lateFee
}


