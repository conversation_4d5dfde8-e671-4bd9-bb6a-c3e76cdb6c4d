/**
@author: captain
@since: 2022/9/13
@desc: 配送客户端微信支付服务类
**/

package shipper

import (
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/pkg/xlog"
	"github.com/go-pay/gopay/wechat/v3"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type CashService struct {
	langUtil *lang.LangUtil
	language string
}

// NewCashService
//
//	@Description:  初始化配送员收款service
//	@author: Captain
//	@Time: 2022-09-13 13:40:56
//	@param c *gin.Context
//	@return *CashService
func NewCashService(c *gin.Context) *CashService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	cashService := CashService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &cashService
}

// Pay
//
//	@Description: 配送端微信支付下单函数
//	@author: Captain
//	@Time: 2022-09-18 17:59:19
//	@receiver cashService CashService
//	@param c *gin.Context 请求上下文
//	@param orderID int 订单编号
//	@param payType int	支付类型（1表示native支付、2表示app支付）
//	@return statusCode int 状态码（200表示操作成功，其他状态码表示操作失败）
//	@return msg string	native支付下单成功时返回付款二维码url，app支付下单成功返回prepay_id，下单失败返回错误提示
func (cashService CashService) Pay(c *gin.Context, orderID int, payType int) (statusCode int, payParams map[string]interface{}) {
	// tools.Logger.Info("配送端微信支付下单函数")
	return 301, map[string]interface{}{"return_msg": fmt.Sprintf(cashService.langUtil.T("shipper_app_is_old"), configs.MyApp.ShipperAppVersion)} //新客户端发布后 开启这里 旧的接口 被使用 表示 客户端是旧的 需要提示升级
	
}

// nativePay
//
//	@Description: 客户扫配送员客户端的二维码支付现金订单
//	@author: Captain
//	@Time: 2022-09-18 08:30:39
//	@receiver cashService CashService
//	@param tx *gorm.DB 数据库事务对象
//	@param c *gin.Context	请求上下文
//	@param orderID int	订单编号
//	@param param map[string]interface{} 支付参数
//	@return bool 操作成功返回true，否则返回false
//	@return interface{}
func (cashService CashService) nativePay(tx *gorm.DB, c *gin.Context, orderID int, param map[string]interface{}) (bool, map[string]interface{}) {
	wechatPayConfig := *configs.NewWechatPayConfig("wechat_mini")
	wechatPay := *tools.NewWechatPay(&wechatPayConfig)
	rs, msg := wechatPay.NativePay(param)
	if rs {
		wechat := models.Wechat{}
		wechat.OrderID = orderID
		wechat.AppID = wechatPay.AppID
		wechat.MchID = wechatPay.MchId
		wechat.OutTradeNo = param["out_trade_no"].(string)
		wechat.TotalFee = param["total"].(uint)
		wechat.ExpireTime = param["expire_time"].(time.Time)
		//fmt.Println("wechat.ExpireTime:", wechat.ExpireTime)
		err := tx.Create(&wechat).Error
		if err != nil {
			xlog.Error(err.Error())
			return false, map[string]interface{}{"return_msg": err.Error(), "code_url": ""}
		}
	}
	return true, map[string]interface{}{"return_msg": cashService.langUtil.T("msg"), "code_url": msg}
}

func (cashService CashService) lakalaJumpMiniUrl(order models.OrderToday) (bool, map[string]string) {
	return true, map[string]string{"return_msg": cashService.langUtil.T("msg"), "code_url": configs.MyApp.LakalaConfig.ShipperPayQRCodeUrl + tools.ToString(order.ID) + "&from=3&lang=" + cashService.language}
}

// appPay
//
//	@Description: 配送员通过配送客户端结账现金订单
//	@author: Captain
//	@Time: 2022-09-18 15:18:57
//	@receiver cashService CashService
//	@param tx *gorm.DB  数据库事务对象
//	@param c *gin.Context	请求上下文
//	@param orderID int	订单编号
//	@param param map[string]interface{}	支付参数
//	@return bool	操作成功返回true，否则返回false
//	@return interface{}
func (cashService CashService) appPay(tx *gorm.DB, c *gin.Context, orderID int, param map[string]interface{}) (bool, map[string]interface{}) {
	wechatPayConfig := *configs.NewWechatPayConfig("wechat_shipper_app")
	wechatPay := *tools.NewWechatPay(&wechatPayConfig)
	rs, data := wechatPay.AppPay(param)
	if rs {
		wechat := models.Wechat{}
		wechat.OrderID = orderID
		wechat.AppID = wechatPay.AppID
		wechat.MchID = wechatPay.MchId
		wechat.OutTradeNo = param["out_trade_no"].(string)
		wechat.TotalFee = param["total"].(uint)
		wechat.ExpireTime = param["expire_time"].(time.Time)
		//fmt.Println("wechat.ExpireTime:", wechat.ExpireTime)
		err := tx.Create(&wechat).Error
		if err != nil {
			xlog.Error(err.Error())
			return false, map[string]interface{}{"return_msg": err.Error()}
		}
	}
	return rs, data
}

// generateOutTradeNoForSms
//
//	@Description: 生成商户订单号
//	@author: Captain
//	@Time: 2022-09-13 19:14:26
//	@receiver cashService CashService
//	@param c *gin.Context
//	@return string
func (cashService CashService) generateOutTradeNoForSms(c *gin.Context) string {
	redisHelper := tools.GetRedisHelper()
	key := "sms_out_trade_no"
	index, _ := redisHelper.Incr(c, key).Result()
	if index > 999 {
		index = 1
		redisHelper.Set(c, key, index, 0)
	}
	indexStr := fmt.Sprintf("%03d", index)
	tmpOutTradeNo := "sms" + carbon.Now("Asia/Shanghai").Format("YmdHis", "Asia/Shanghai") + indexStr
	return tmpOutTradeNo
}

// PayQuery
//
//	@Description: 查询现金订单是否支付
//	@author: Captain
//	@Time: 2022-09-16 18:32:27
//	@receiver cashService CashService
//	@param c *gin.Context
//	@param OrderID int 订单编号
//	@return bool  订单cash_clear_state=1时返回true，0时返回false
func (cashService CashService) PayQuery(c *gin.Context, OrderID int) bool {

	var count int64
	tx := tools.Db
	tx.Model(&models.OrderToday{}).Where("id =?", OrderID).Where("cash_clear_state =?", 1).Count(&count)
	return count > 0
}

// Notify
//
//	@Description: 配送端微信支付异步通知接口（微信商户平台调用）--APIV3
//	@author: Captain
//	@Time: 2022-09-18 08:33:04
//	@receiver cashService CashService
//	@param context *gin.Context
//	@return bool
//	@return string
func (cashService CashService) Notify(context *gin.Context) (bool, string) {
	db := tools.Db
	wechatPayConfig := configs.NewWechatPayConfig("wechat_mini")
	notifyReq, err := wechat.V3ParseNotify(context.Request)
	if err != nil {
		tools.Logger.Error(err)
		return false, "接受支付通知失败"
	}
	// 普通支付通知解密
	tools.Logger.Info("APIv3Key:", wechatPayConfig.MchApiV3Key)
	result, err := notifyReq.DecryptCipherText(wechatPayConfig.MchApiV3Key)
	if err != nil {
		tools.Logger.Error(err)
		return false, "支付通知解密失败"
	}
	//使用通知里的 "微信支付订单号" 或者 "商户订单号" 去自己的数据库找到订单
	wechat := models.Wechat{}
	err = db.Model(&models.Wechat{}).Where("out_trade_no = ?", result.OutTradeNo).First(&wechat).Error
	if err != nil || wechat.Payed == 1 { // 如果订单不存在 或者 订单已经支付过了
		tools.Logger.Info("支付完成，直接退回去！")
		return true, "已接收订单支付成功通知" // 告诉微信，我已经处理完了，订单没找到，别再通知我了
	}
	// 用户是否支付成功
	todayOrder := models.OrderToday{}
	db.Model(&models.OrderToday{}).Where("id = ?", wechat.OrderID).First(&todayOrder)
	if todayOrder.PayType == 5 {
		tools.Logger.Info("---------已经付费成功了--注意*** 手动处理-----")
		err = db.Table("wechat").Where("id = ?", wechat.ID).
			Updates(map[string]interface{}{"transaction_id": result.TransactionId, "trade_type": result.TradeType, "Payed": 1, "open_id": result.Payer.Openid, "remark": "支付两次，手动退款处理"}).Error
		return true, "支付成功"
	}
	//1.更改订单支付信息，2.wechat表里插入支付信息，3.代理代付的现金订单款换到代理余额里
	//设置数据库事务所的隔离级别

	tx := tools.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			tools.Logger.Error("---------defer 出错了---", r)
		}
	}()
	todayOrder = models.OrderToday{}
	tx.Table("t_order_today").Where("id = ?", wechat.OrderID).First(&todayOrder)
	if todayOrder.PayType == 5 {
		tools.Logger.Info("事务锁成功处理")
		tx.Commit()
		return true, "支付成功"
	}
	tools.Logger.Info("---------t_order_today 锁获取成功-----")
	// 更新today_order
	tradeType := map[string]int{
		"JSAPI":  1,
		"MWEB":   2,
		"APP":    3,
		"NATIVE": 4,
	}
	orderUpdate := map[string]interface{}{
		"cash_clear_time":    carbon.Now("Asia/Shanghai").Carbon2Time(),
		"cash_clear_state":   1,
		"consume_type":       1,
		"pay_type":           5,
		"cash_clear_channel": tradeType[result.TradeType],
	}
	err = tx.Table("t_order_today").Where("id = ?", todayOrder.ID).Updates(orderUpdate).Error
	// 更新wechat
	wechatUpdate := map[string]interface{}{
		"transaction_id": result.TransactionId,
		"trade_type":     result.TradeType,
		"Payed":          1,
		"open_id":        result.Payer.Openid,
	}
	err = tx.Table("wechat").Where("id = ?", wechat.ID).Updates(wechatUpdate).Error

	tools.Logger.Infof("---------t_admin 锁获取成功---%d--", todayOrder.AreaID)
	//TODO 只有扣款才退款
	tools.Logger.Info("微信支付通知处理成功")
	//time.Sleep(time.Second * 10)
	tx.Commit()
	return true, "支付成功"
}

// GetShipperNotPayedOrders
//
//	@Description: 获取配送员未结账订单列表
//	@author: Captain
//	@Time: 2022-10-28 17:43:02
//	@receiver cashService CashService
//	@param admin models.Admin
//	@param startDate string	开始日期
//	@param endDate string		结束日期
//	@param month string		按月查询（选填）
//	@param day string			查询某一天的（选填）
//	@param page int				分页页数
//	@param limit int				每页显示的订单数量
//	@return []map[string]interface{}
func (cashService CashService) GetShipperNotPayedOrders(admin models.Admin, startDate string, endDate string, month string, day string, page int, limit int,cashClearState int) []map[string]interface{} {
	tx := tools.Db
	var myMap = make([]map[string]interface{}, 0)

	if startDate != "" && endDate != "" {
		startDate = carbon.Parse(startDate).ToDateTimeString()
		endDate = carbon.Parse(endDate).AddDays(1).ToDateTimeString()
	} else if month != "" {
		startDate = carbon.ParseByFormat(month, "Y-m").ToDateTimeString()
		endDate = carbon.ParseByFormat(month, "Y-m").AddMonths(1).ToDateTimeString()
	} else if day != "" {
		startDate = carbon.Parse(day).ToDateTimeString()
		endDate = carbon.Parse(day).AddDays(1).ToDateTimeString()
	} else {
		return nil
	}
	today := carbon.Now().Format("Y-m-d 00:00:00")
	if startDate == today {
		payType := 6
		if cashClearState == 1 {
			payType = 5
		}
		err := tx.Raw("SELECT "+
			"t_order_today.id,"+
			"t_order_today.order_id,"+
			"t_order_today.state,"+
			"t_order_today.pay_platform,"+
			"FORMAT((t_order_today.order_price)/100,2) AS price,"+
			"t_order_today.booking_time,"+
			"t_order_today.timezone,"+
			"t_order_today.cash_clear_state,"+
			"t_order_today.cash_clear_time,"+
			"t_order_today.pay_time,"+
			"Date(t_order_today.created_at) AS created_at, "+
			"'t_order_today' as  order_origin "+
			"FROM `t_order_today`  LEFT JOIN `t_take_order` ON t_order_today.id = t_take_order.order_id "+
			"WHERE t_order_today.`state`=? AND t_order_today.`pay_type`=? AND t_take_order.state in(3,2) "+
			"AND t_order_today.`shipper_id`=? AND `t_order_today`.`deleted_at` IS NULL and t_order_today.cash_clear_state = ? and  t_order_today.created_at BETWEEN ? AND ? ORDER BY t_order_today.`cash_clear_state` ASC,"+
			"t_order_today.`pay_time` ASC LIMIT ? OFFSET ?", 7, payType, admin.ID,cashClearState,startDate,endDate, limit, (page-1)*limit).
			Scan(&myMap).Error
		if err != nil {
			fmt.Println("获取配送员未结账订单列表信息时出现错误")
			fmt.Println(err.Error())
		}
	} else {
		payType := 6
		if cashClearState == 1 {
			payType = 5
		}
		err := tx.Raw(" select * from ("+
			"   (SELECT "+
			"t_order.id,"+
			"t_order.order_id,"+
			"t_order.state,"+
			"t_order.pay_platform,"+
			"format((t_order.order_price)/100,2) AS price,"+
			"t_order.booking_time,"+
			"t_order.timezone,"+
			"t_order.cash_clear_state,"+
			"t_order.cash_clear_time,"+
			"t_order.pay_time,"+
			"Date(t_order.created_at) AS created_at,"+
			"'t_order' as  order_origin "+
			"  FROM `t_order` LEFT JOIN `t_take_order` ON t_order.id = t_take_order.order_id "+
			"WHERE t_order.`state` = ? AND t_order.`pay_type` = ? AND t_order.created_at BETWEEN ? AND ? AND t_take_order.state in(3,2)  "+
			"AND t_order.`shipper_id` = ? AND `t_order`.`deleted_at` is null and t_order.cash_clear_state = ? ) "+
			"UNION "+
			"(SELECT "+
			"t_order_today.id,"+
			"t_order_today.order_id,"+
			"t_order_today.state,"+
			"t_order_today.pay_platform,"+
			"FORMAT((t_order_today.order_price)/100,2) AS price,"+
			"t_order_today.booking_time,"+
			"t_order_today.timezone,"+
			"t_order_today.cash_clear_state,"+
			"t_order_today.cash_clear_time,"+
			"t_order_today.pay_time,"+
			"Date(t_order_today.created_at) AS created_at,"+
			"'t_order_today' as  order_origin "+
			"FROM `t_order_today`  LEFT JOIN `t_take_order` ON t_order_today.id = t_take_order.order_id "+
			"WHERE t_order_today.`state`=? AND t_order_today.`pay_type`=? AND t_order_today.created_at BETWEEN ? AND ? AND t_take_order.state in(3,2)"+
			"   AND t_order_today.`shipper_id`=? AND `t_order_today`.`deleted_at` IS NULL and t_order_today.cash_clear_state = ? )"+
			") AS Z ORDER BY `Z`.`cash_clear_state` ASC , `Z`.`pay_time` ASC LIMIT ? OFFSET ?", 7, payType, startDate, endDate, admin.ID,cashClearState, 7, payType, startDate, endDate, admin.ID,cashClearState, limit, (page-1)*limit).
			Scan(&myMap).Error
		if err != nil {
			fmt.Println("获取配送员未结账订单列表信息时出现错误")
			fmt.Println(err.Error())
		}
	}
	return myMap

}

// GetShipperNotPayedOrderStatisticsHeader
//
//	@Description: 获取配送员未结账订单列表头部统计数据
//	@author: Captain
//	@Time: 2022-10-28 17:39:41
//	@receiver cashService CashService
//	@param admin models.Admin 配送员信息
//	@param startDate string	开始日期
//	@param endDate string		结束日期
//	@param month string		按月查询（选填）
//	@param day string			查询某一天的（选填）
//	@return []map[string]interface{}
func (cashService CashService) GetShipperNotPayedOrderStatisticsHeader(admin models.Admin, startDate string, endDate string, month string, day string,cashClearState int) []map[string]interface{} {
	tx := tools.Db
	var myMap = make([]map[string]interface{}, 0)
	if startDate != "" && endDate != "" {
		startDate = carbon.Parse(startDate).ToDateTimeString()
		endDate = carbon.Parse(endDate).AddDays(1).ToDateTimeString()
	} else if month != "" {
		startDate = carbon.ParseByFormat(month, "Y-m").ToDateTimeString()
		endDate = carbon.ParseByFormat(month, "Y-m").AddMonths(1).ToDateTimeString()
	} else if day != "" {
		startDate = carbon.Parse(day).ToDateTimeString()
		endDate = carbon.Parse(day).AddDays(1).ToDateTimeString()
	} else {
		return nil
	}
	today := carbon.Now().Format("Y-m-d 00:00:00")
	if startDate == today {
		rawSql := `
		SELECT
			t_order_today.cash_clear_state,
			sum( t_order_today.order_price ) AS order_price,
			count( t_order_today.id ) AS count 
		FROM
			t_order_today
			LEFT JOIN t_take_order ON t_order_today.id = t_take_order.order_id 
		WHERE
			t_order_today.state =? 
			AND t_order_today.pay_type =? 
			AND t_take_order.state IN ( 3, 2 ) 
			AND t_order_today.consume_type IN ( 0, 3 ) 
			AND t_order_today.shipper_id =? 
			AND t_order_today.deleted_at IS NULL 
			AND t_order_today.cash_clear_state = ? 
		GROUP BY
			t_order_today.cash_clear_state
		`
		err := tx.Raw(rawSql, 7, 6, admin.ID,cashClearState).Scan(&myMap).Error
		tools.Logger.Info("正在获取配送员现金订单列表")
		if err != nil {
			fmt.Println("获取配送员未结账订单列表头部信息时出现错误")
			fmt.Println(err.Error())
		}
	} else {
		err := tx.Raw(" select cash_clear_state,sum( order_price ) AS order_price,count( id ) AS count  from ("+
			"   (SELECT "+
			"t_order.id,"+
			"t_order.order_price AS order_price,"+
			"t_order.cash_clear_state "+
			"  FROM `t_order` LEFT JOIN `t_take_order` ON t_order.id = t_take_order.order_id "+
			"WHERE t_order.`state` = ? AND t_order.`pay_type` = ? AND t_order.created_at BETWEEN ? AND ? AND t_take_order.state in(3,2)  "+
			"AND t_order.consume_type IN (0,3) AND t_order.`shipper_id` = ? AND `t_order`.`deleted_at` is null and t_order.cash_clear_state = ? ) "+
			"UNION "+
			"(SELECT "+
			"t_order_today.id,"+
			"t_order_today.order_price AS order_price,"+
			"t_order_today.cash_clear_state "+
			"FROM `t_order_today`  LEFT JOIN `t_take_order` ON t_order_today.id = t_take_order.order_id "+
			"WHERE t_order_today.`state`=? AND t_order_today.`pay_type`=? AND t_order_today.created_at BETWEEN ? AND ? AND t_take_order.state in(3,2)"+
			"AND t_order_today.consume_type IN (0,3)  AND t_order_today.`shipper_id`=? AND `t_order_today`.`deleted_at` IS NULL and t_order_today.cash_clear_state = ? )"+
			") AS Z WHERE Z.id IS NOT NULL GROUP BY cash_clear_state", 7, 6, startDate, endDate, admin.ID,cashClearState, 7, 6, startDate, endDate, admin.ID,cashClearState).
			Scan(&myMap).Error
		tools.Logger.Info("正在获取配送员现金订单列表")
		if err != nil {
			fmt.Println("获取配送员未结账订单列表头部信息时出现错误")
			fmt.Println(err.Error())
		}
	}
	return myMap
}

// getAdminId
//
//	@Description: 根据订单获取代理ID
//	@author: Alimjan
//	@Time: 2023-07-25 16:43:08
//	@receiver cashService CashService
//	@param order models.OrderToday
//	@return int
func (cashService CashService) getAdminId(order models.OrderToday) int {
	agent := models.Admin{}
	err := tools.Db.Table("t_admin").Select("t_admin.id", "t_admin.balance").
		Joins("LEFT JOIN admin_areas ON t_admin.id = admin_areas.admin_id").
		Where("t_admin.type = ?", 3). //代理
		Where("t_admin.state = ?", 1).
		Where("admin_areas.area_id = ?", order.AreaID).
		First(&agent).Error
	if err != nil {
		tools.Logger.Error(err)
		return 0
	} else {
		return agent.ID
	}
}

/***
 * @Author: [rozimamat]
 * @description: 统一支付接口
 * @Date: 2023-08-26 12:16:48
 * @param {*gin.Context} c
 * @param {int} orderID
 * @param {string} payPage
 */
func (cashService CashService) UnionPay(c *gin.Context, orderID int, payPage string) (statusCode int, payParams map[string]interface{}) {
	tools.Logger.Info("配送端 统一支付 函数")
	if len(payPage) == 0 { //支付 页面未发送
		return 301, map[string]interface{}{"return_msg": cashService.langUtil.T("order_not_found"), "code_url": ""}
	}
	db := tools.GetDB()
	defer func() {
		if r := recover(); r != nil {
			tools.Logger.Error("配送端 统一支付  出错了---order_id:", orderID, r)
		}
	}()
	orderTodayObj := models.OrderToday{}
	db.Model(&models.OrderToday{}).Where("id =?", orderID).First(&orderTodayObj)

	if orderTodayObj.CashClearState == 1 {
		tools.Logger.Error("配送端 统一支付--orderTodayObj.CashClearState == 1---order_id:", orderID)
		return 301, map[string]interface{}{"return_msg": cashService.langUtil.T("order_not_found"), "code_url": ""}
	}
	if orderTodayObj.State != 6 && orderTodayObj.State != 7 {
		tools.Logger.Error("配送端 统一支付-- orderTodayObj.State != 6 && orderTodayObj.State != 7---order_id:", orderID)
		return 301, map[string]interface{}{"return_msg": cashService.langUtil.T("order_state_error")}
	}
	if orderTodayObj.PayType != 6 {
		tools.Logger.Error("配送端 统一支付-- orderTodayObj.PayType != 6---order_id:", orderID)
		return 301, map[string]interface{}{"return_msg": cashService.langUtil.T("order_cannot_repaid")}
	}

	orderTotalPrice := orderTodayObj.Price + orderTodayObj.Shipment + uint(orderTodayObj.LunchBoxFee)

	outTradeNo := cashService.generateOutTradeNoForSms(c)

	expireTime := carbon.Now("Asia/Shanghai").AddMinutes(10).Carbon2Time()

	data := make(map[string]interface{}) //总数据
	rs := false
	appData := make(map[string]interface{}) //支付有关的参数
	currentPayType := ""               //当前支付类型

	payParam := map[string]interface{}{
		"description":  "美滋来外卖平台",
		"out_trade_no": outTradeNo,
		"total":        orderTotalPrice,
		"time_expire":  expireTime.Format(time.RFC3339),
		"expire_time":  expireTime,
	}
	//order_page  订单详情页面客户要付款  cash_page  配送员现金订单 要付款
	switch payPage {
	case "order_page":
		currentPayType = "qr_code"          //订单详情页面客户要付款
		if orderTodayObj.PayPlatform == 0 { // 微信通道: 微信支付二维码
			rs, appData = cashService.nativePay(db, c, orderID, payParam)
		} else if orderTodayObj.PayPlatform == 1 { // 拉卡拉通道: 条件小程序二维码
			rs = true
			appData = map[string]interface{}{"url": configs.MyApp.LakalaConfig.ShipperPayQRCodeUrl + tools.ToString(orderID) + "&from=3&lang=" + cashService.language}
		}

		if rs {

			tools.Logger.Info("配送端 统一支付--payType == 1 ---order_id:", orderID)
			//需要格式化 data
			if orderTodayObj.PayPlatform == 0 { // 微信通道: 微信支付二维码
				appData["url"] = appData["code_url"]
			}
			data["order_id"] = orderID
			data["pay_platform"] = orderTodayObj.PayPlatform
			data["pay_type"] = 1 //客户支付
			data["current_pay_type"] = currentPayType
			data[currentPayType] = appData

			return 200, data
		} else {
			tools.Logger.Error("配送端 统一支付--payType == 1 ---order_id:", orderID)
			return 301, data
		}
	case "cash_page":
		//cash_page  配送员现金订单 要付款
		if orderTodayObj.PayPlatform == 0 { // 微信通道: 微信支付二维码
			currentPayType = "native_app" //cash_page  配送员现金订单 要付款  微信订单
			rs, appData = cashService.appPay(db, c, orderID, payParam)
		} else if orderTodayObj.PayPlatform == 1 { // 拉卡拉通道: 条件小程序二维码
			rs = true
			currentPayType = "mini_app" //cash_page  配送员现金订单 要付款  拉卡拉订单
			jumpMiniType := configs.MyApp.ShipperAppJumpEnv  //默认 release ,测试时 写入 preview
			payFrom := "3"   //配送员 APP 支付
			payerType := "2" //配送员 APP 支付
			appData = map[string]interface{}{
				"original_id": configs.MyApp.ShipperAppJumpWechatMiniOriginalId,
				"path":        configs.MyApp.ShipperAppJumpWechatMiniPage + "?order_id=" + tools.ToString(orderID) + "&lang=" + cashService.language + "&platform=app&pay_from=" + payFrom + "&payer_type=" + payerType,
				"type":        jumpMiniType,
			}
		} else {
			tools.Logger.Info("配送端 统一支付--pay-platform error---", orderID)
			// return 301, map[string]string{"return_msg": cashService.langUtil.T("client_param_error")}
			return 301, map[string]interface{}{"return_msg": cashService.langUtil.T("shipper_app_is_old")}
		}

		if rs {
			tools.Logger.Info("配送端 统一支付--payType == 2 ---", orderID)
			data["order_id"] = orderID
			data["pay_platform"] = orderTodayObj.PayPlatform
			data["pay_type"] = 2 //配送员支付
			data["current_pay_type"] = currentPayType
			data[currentPayType] = appData

			return 200, data
		} else {
			tools.Logger.Error("配送端 统一支付--payType == 2 ---", orderID)
			return 301, data
		}
	default:
		tools.Logger.Info("配送端 统一支付--pay-type error---", orderID)
		// return 301, map[string]string{"return_msg": cashService.langUtil.T("client_param_error")}
		return 301, map[string]interface{}{"return_msg": cashService.langUtil.T("shipper_app_is_old")}
	}

}
func (cashService CashService) CheckCachClearState(orderID int) bool {
	var count int64
	tools.Db.Table("t_order_today").Where("id=?", orderID).Where("cash_clear_state", 1).Where("deleted_at is NULL").Count(&count)
	return count > 0
}
