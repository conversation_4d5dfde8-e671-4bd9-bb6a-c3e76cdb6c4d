package shipper

import (
	"encoding/json"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/models"
	"mulazim-api/services"

	// "time"
	other "mulazim-api/models/other"
	"mulazim-api/models/shipment"

	"mulazim-api/tools"

	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type IncomeArchiveService struct {
	services.BaseService
}

func NewIncomeArchiveService() *IncomeArchiveService {
	return &IncomeArchiveService{}
}

// 每日归档配送员收入
func (s *IncomeArchiveService) ArchiveIncomeByDay(day string, oneShipperId int, OldTemplateId int, areaId int) error {

	if oneShipperId > 0 { //单个配送员的数据归档
		s.ArchiveIncomeByShipperId(oneShipperId, day, oneShipperId, OldTemplateId, areaId)
		return nil
	}
	shipperIds := s.getShipperIdsByDay(day, areaId)
	for _, shipperId := range shipperIds {
		s.ArchiveIncomeByShipperId(shipperId, day, 0, 0, areaId)
	}
	//计算客户拓展收入
	s.ArchiveCustomerIntroduce(day, oneShipperId, OldTemplateId)
	//保险扣费
	s.ArchiveInsurance(day, oneShipperId, OldTemplateId)
	return nil
}

// 每日归档配送员收入 计算 全勤奖
func (s *IncomeArchiveService) ArchiveFullAttendanceByDay(day string, oneShipperId int, OldTemplateId int, areaId int) error {

	shipperIds := s.getShipperIdsByDay(day, areaId)
	db := tools.GetDB()
	var shipper models.Admin

	for _, shipperId := range shipperIds {
		db.
			Preload("ShipperIncomeTemplate").
			First(&shipper, shipperId)
		if shipper.ID == 0 || shipper.ShipperIncomeTemplateID == 0 {
			continue
		}
		s.ArchiveFullAttendancePrize(shipper, day, 0, 0)

	}
	return nil
}

// 每日归档配送员收入 根据配送员id 和日期
func (s *IncomeArchiveService) ArchiveIncomeByShipperId(shipperId int, archiveDay string, oneShipperId int, OldTemplateId int, areaId int) error {
	if oneShipperId > 0 && shipperId != oneShipperId { //单个配送员的数据归档
		return nil
	}
	nthOrder := 0 //第几个订单
	db := tools.GetDB()
	var shipper models.Admin
	db.
		Preload("ShipperIncomeTemplate").
		First(&shipper, shipperId)
	if shipper.ID == 0 || shipper.ShipperIncomeTemplateID == 0 {
		return nil
	}
	if OldTemplateId > 0 { //这个月的第一天 上个月的 模板被修改的情况
		var oldTemplate shipment.ShipperIncomeTemplate
		db.Model(&shipment.ShipperIncomeTemplate{}).Where("id = ?", OldTemplateId).First(&oldTemplate)
		if oldTemplate.ID > 0 {
			shipper.ShipperIncomeTemplate = oldTemplate
		}
	}

	//取消的订单 写入记录
	s.CanceledOrderArchive(shipperId, shipper.RealName, shipper.Mobile, archiveDay)

	//配送员前天的总订单数量 因为归档在凌晨执行 所以要取2天前的
	month := carbon.Parse(archiveDay).AddDays(-1).Format("Y-m")
	
	orderCountBefore := s.GetShipperMonthOrderBeforeToday(shipperId, month, carbon.Parse(archiveDay).AddDays(-1).Format("Y-m-d"),true)
	nthOrder = orderCountBefore
	// 获取当天的订单
	var orders []models.Order
	// 每次批量处理 100 条
	db.Model(&models.Order{}).
		Preload("LateOrders").
		Where("shipper_id = ?  AND archive_date >= ? and archive_date < ? and state in ?",
			shipperId,
			archiveDay, carbon.Parse(archiveDay).AddDay().Format("Y-m-d"), []int{7, 8, 9}).
		Find(&orders)
	for _, order := range orders {

		lateMinute := 0
		if order.LateOrders.ID > 0 {
			lateMinute = order.LateOrders.LateMinute
		}
		date := carbon.Parse(archiveDay).AddDays(-1).Format("Y-m-d") //数据记录时记录昨天的日期
		// shipperIncome记录的订单类型（1:普通订单，2:特价活动订单）
		orderType := 1
		if order.MarketType == 2 {
			orderType = 2
		}
		if order.State == 7 {
			if order.MarketType == 1 {  // 只算普通订单
				nthOrder = nthOrder + 1 //订单数量加一
			}                               //成功订单
			var (
				shippingPrice       = 0
				specialTimePrice    = 0
				specialWeatherPrice = 0
				specialWeatherId    = 0
			)
			switch shipper.ShipperIncomeTemplate.RuleType {
			//规则类型:1:固定配送费,2:按距离计算,3:按出租车
			case shipment.RuleTypeFixed, shipment.RuleTypeDistance, shipment.RuleTypeCar:

				//计算  1:固定配送费,2:按距离计算,3:按出租车
				shippingPrice, specialTimePrice, specialWeatherPrice, specialWeatherId, _ = NewShipperFeeService().CalculateArchiveShipperFeeAndWeatherId(shipperId, shipper.ShipperIncomeTemplate, order, 0)

			case shipment.RuleTypeOrder:
				//计算  4:按订单数量
				//记录 不记录 配送费  后期要更新
				shippingPrice, specialTimePrice, specialWeatherPrice, specialWeatherId, _ = NewShipperFeeService().CalculateArchiveShipperFeeAndWeatherId(shipperId, shipper.ShipperIncomeTemplate, order, nthOrder)
				if *shipper.ShipperIncomeTemplate.RuleOrderCountType == shipment.RuleTypeOrderCountAll { //全部订单
					shippingPrice = 0 //后期要更新这个值
				}

			}

			// 添加特价活动逻辑
			if order.MarketType == 2{
				specialOrderShipment := order.GetSpecialOrderShipperIncome()
				if specialOrderShipment > 0{
					shippingPrice = specialOrderShipment
				}
				//特价活动 不能加入 特殊时间段和特殊天气的钱  新的规则
				tools.Logger.Info("新的规则特价活动不能加入 特殊时间段和特殊天气的钱 订单:", order.ID, " 原来的值 特殊时间费用:", specialTimePrice,",特殊天气费用:", specialWeatherPrice)
				specialTimePrice = 0
				specialWeatherPrice = 0
			}

			//类型 类型 1：订单收入，2:奖励收入，3:打赏收入，4.特殊时间，5.特殊天气，6.好评，7.差评，8.投诉，9:迟到 10:取消订单(只做记录不扣费) 11:失败订单(只做记录不扣费)
			incomeTypes := []int{constants.TypeOrder, constants.TypeSpecialTime, constants.TypeSpecialWeather, constants.TypeLate}
			var income []shipment.ShipperIncome
			db.Model(&shipment.ShipperIncome{}).
				Where("shipper_id = ? and order_id = ? ", shipperId, order.ID).
				Select("id,type,amount").
				Find(&income)
			updateItems := make([]map[string]interface{}, 0)
			var insertItems []shipment.ShipperIncome
			for _, tp := range incomeTypes {
				flag := true
				incomeId := 0
				incomeAmount := 0
				for _, inc := range income {
					if inc.Type == tp {
						flag = false
						if inc.Type == 1 {
							incomeId = inc.ID
							incomeAmount = inc.Amount
						}

					}
				}
				if flag {
					switch tp {
					case constants.TypeOrder:
						orderDeliveryMinute := 0
						if order.DeliveryStartTime != nil && order.DeliveryEndTime != nil {
							deliveryEndTime := order.DeliveryEndTime.Format("2006-01-02 15:04:05")
							deliveryStartTime := order.DeliveryStartTime.Format("2006-01-02 15:04:05")
							dst := carbon.Parse(deliveryStartTime, configs.AsiaShanghai)
							dse := carbon.Parse(deliveryEndTime, configs.AsiaShanghai)
							orderDeliveryMinute = int(dst.DiffInMinutes(dse))
							if orderDeliveryMinute < 0 {
								tools.Logger.Info("平均配送时间 负数 错误", order.ID, orderDeliveryMinute, "start_time", deliveryStartTime, "end_time", deliveryEndTime)
								orderDeliveryMinute = 0 //配送没有完成的订单怎么处理
							}
						} else if order.DeliveryStartTime != nil && order.DeliveryEndTime == nil { //配送没有完成
							tools.Logger.Info("配送完成时间为空错误 ", order.ID)
							orderDeliveryMinute = 0 //配送没有完成的订单怎么处理
						} else { //有配送员id 但是没有开始配送时间和结束配送时间
							tools.Logger.Info("配送完成时间为空错误 ", order.ID)
							orderDeliveryMinute = 0 //配送没有完成的订单怎么处理
						}

						//1,订单收入
						incomeModel := shipment.ShipperIncome{
							CityID:              order.CityID,
							AreaID:              order.AreaID,
							Date:                date,
							Type:                constants.TypeOrder, //订单收入
							Amount:              shippingPrice,
							OrderPrice:          order.OrderPrice,
							ShipperID:           shipperId,
							OrderID:             order.ID,
							OrderNo:             order.OrderID,
							ShipperName:         shipper.RealName,
							ShipperMobile:       shipper.Mobile,
							OrderDeliveryMinute: orderDeliveryMinute,
							TemplateID:          shipper.ShipperIncomeTemplate.ID,
							OrderType: orderType,
						}
						insertItems = append(insertItems, incomeModel)

					case constants.TypeSpecialTime:
						if specialTimePrice != 0 {
							//4.特殊时间
							incomeModel := shipment.ShipperIncome{
								CityID:        order.CityID,
								AreaID:        order.AreaID,
								Date:          date,
								Type:          constants.TypeSpecialTime, //特殊时间
								Amount:        specialTimePrice,
								OrderPrice:    order.OrderPrice,
								ShipperID:     shipperId,
								OrderID:       order.ID,
								OrderNo:       order.OrderID,
								ShipperName:   shipper.RealName,
								ShipperMobile: shipper.Mobile,
								TemplateID:    shipper.ShipperIncomeTemplate.ID,
								OrderType: orderType,
							}
							insertItems = append(insertItems, incomeModel)
						}
					case constants.TypeSpecialWeather:
						if specialWeatherPrice != 0 {
							//5.特殊天气
							incomeModel := shipment.ShipperIncome{
								CityID:        order.CityID,
								AreaID:        order.AreaID,
								Date:          date,
								Type:          constants.TypeSpecialWeather, //特殊天气
								Amount:        specialWeatherPrice,
								OrderPrice:    order.OrderPrice,
								ShipperID:     shipperId,
								OrderID:       order.ID,
								OrderNo:       order.OrderID,
								ShipperName:   shipper.RealName,
								ShipperMobile: shipper.Mobile,
								TemplateID:    specialWeatherId,
								OrderType: orderType,
							}
							insertItems = append(insertItems, incomeModel)
						}
					case constants.TypeLate:
						//9.迟到
						if lateMinute > 0 {
							lateFee := NewShipperFeeService().CalculateLateOrderFee(shipper.ShipperIncomeTemplate, order,shippingPrice)
							// if lateFee != 0 {
								incomeModel := shipment.ShipperIncome{
									CityID:              order.CityID,
									AreaID:              order.AreaID,
									Date:                date,
									Type:                constants.TypeLate, //迟到
									Amount:              lateFee * (-1),     //负数记录
									OrderPrice:          order.OrderPrice,
									ShipperID:           shipperId,
									OrderID:             order.ID,
									OrderNo:             order.OrderID,
									ShipperName:         shipper.RealName,
									ShipperMobile:       shipper.Mobile,
									OrderDeliveryMinute: lateMinute,
									TemplateID:          shipper.ShipperIncomeTemplate.ID,
									OrderType:           orderType,  // 订单类型（1:普通订单，2:特价活动订单）
								}
								insertItems = append(insertItems, incomeModel)
							// }
						}
					}
				} else {

					if incomeId > 0 {
						if shippingPrice != incomeAmount {	 //只更新 数据出现错的 
							updateItems = append(updateItems, map[string]interface{}{
								"id":     incomeId,
								"amount": shippingPrice,
								"old_amount": incomeAmount,
							})
						}
					}
				}
			}

			if len(insertItems) > 0 {
				//
				//写入全部记录 除了 2:奖励收入，3:打赏收入  8.投诉 全部在这里写入
				//1：订单收入，4.特殊时间，5.特殊天气，9:迟到 10:取消订单 11:失败订单

				db.Create(&insertItems)
			}
			if len(updateItems) > 0 {
				// tools.Logger.Info(fmt.Sprintf("更新 配送员工资 数据 %v", updateItems))
				for _, up := range updateItems {
					db.Table("t_shipper_income").Where("id = ?", up["id"]).Update("amount", up["amount"])
				}
			}
		} else if order.State == 8 || order.State == 9 { //失败订单
			//记录数据
			//11 失败订单
			incomeModel := shipment.ShipperIncome{
				CityID:        order.CityID,
				AreaID:        order.AreaID,
				Date:          date,
				Type:          constants.TypeFail, //失败订单
				Amount:        0,
				OrderPrice:    order.OrderPrice,
				ShipperID:     shipperId,
				OrderID:       order.ID,
				OrderNo:       order.OrderID,
				ShipperName:   shipper.RealName,
				ShipperMobile: shipper.Mobile,
				TemplateID:    shipper.ShipperIncomeTemplate.ID,
				OrderType:           orderType,  // 订单类型（1:普通订单，2:特价活动订单）
			}
			db.Create(&incomeModel)
		}
	}

	//前期 不记录 的 按 月订单 数量计算的 配送费 数据更新
	s.UpdateShipperIncomeByOrderCount(shipper, archiveDay, oneShipperId, OldTemplateId)

	//计算全勤奖
	s.ArchiveFullAttendancePrize(shipper, archiveDay, oneShipperId, OldTemplateId)
	//计算基本工资
	s.ArchiveBaseSalary(shipper, archiveDay, oneShipperId, OldTemplateId)



	return nil
}

// 根据评论 给配送员 计算收入  计算3天前的数据 因为评论审核时间是3天
func (s *IncomeArchiveService) ArchiveIncomeCommentByShipperId(shipperId int, archiveDay string, oneShipperId int, OldTemplateId int) error {
	if oneShipperId > 0 && shipperId != oneShipperId {
		return nil
	}
	db := tools.GetDB()
	var shipper models.Admin
	db.
		Preload("ShipperIncomeTemplate").
		First(&shipper, shipperId)
	if shipper.ID == 0 || shipper.ShipperIncomeTemplateID == 0 {
		return nil
	}
	if OldTemplateId > 0 { //这个月的第一天 上个月的 模板被修改的情况
		var oldTemplate shipment.ShipperIncomeTemplate
		db.Model(&shipment.ShipperIncomeTemplate{}).Where("id = ?", OldTemplateId).First(&oldTemplate)
		if oldTemplate.ID > 0 {
			shipper.ShipperIncomeTemplate = oldTemplate
		}
	}

	currentDay := carbon.Parse(archiveDay, configs.AsiaShanghai).Format("Y-m-d") + " 23:59:59"
	fourDayBefore := carbon.Parse(archiveDay, configs.AsiaShanghai).AddDays(-4).Format("Y-m-d") + " 00:00:00"
	// 获取当天的订单
	var comments []other.CommentModel
	// 每次批量处理 100 条
	db.Model(&other.CommentModel{}).
		Preload("Order", func(db *gorm.DB) *gorm.DB { //获取是否迟到
			return db.Where("t_order.shipper_id = ? and state = ?", shipperId, 7)
		}).
		Where("shipper_id = ? and type = 1 and state = 1  AND created_at between  ? and  ?", shipperId, fourDayBefore, currentDay).
		Find(&comments)

	for _, co := range comments {

		if co.Order.ID == 0 {
			continue
		}
		//类型 1：订单收入，2:奖励收入，3:打赏收入，4.特殊时间，5.特殊天气，6.好评，7.差评，8.投诉，9:迟到
		incomeTypes := []int{6, 7}
		var income []shipment.ShipperIncome
		db.Model(&shipment.ShipperIncome{}).
			Where("shipper_id = ? and order_id = ? ", shipperId, co.OrderID).
			Select("id,type").
			Find(&income)
		var insertItems []shipment.ShipperIncome
		for _, tp := range incomeTypes {
			flag := true
			for _, inc := range income {
				if inc.Type == tp {
					flag = false
				}
			}
			if flag {
				date := carbon.Parse(archiveDay, configs.AsiaShanghai).AddDays(-1).Format("Y-m-d")    //记录昨天的日期
				if co.Star >= 3 && shipper.ShipperIncomeTemplate.CommentIncrementFee > 0 && tp == 6 { //好评奖励

					incomeModel := shipment.ShipperIncome{
						CityID:        co.Order.CityID,
						AreaID:        co.Order.AreaID,
						Date:          date,
						Type:          6, //好评
						Amount:        shipper.ShipperIncomeTemplate.CommentIncrementFee,
						OrderPrice:    co.Order.OrderPrice,
						ShipperID:     shipperId,
						OrderID:       co.Order.ID,
						OrderNo:       co.Order.OrderID,
						ShipperName:   shipper.RealName,
						ShipperMobile: shipper.Mobile,
						CommentStar:   int(co.Star),
					}
					insertItems = append(insertItems, incomeModel)

				}
				if co.Star < 3 && shipper.ShipperIncomeTemplate.CommentDeductionFee > 0 && tp == 7 { //差评惩罚

					incomeModel := shipment.ShipperIncome{
						CityID:        co.Order.CityID,
						AreaID:        co.Order.AreaID,
						Date:          date,
						Type:          7,                                                        //差评
						Amount:        shipper.ShipperIncomeTemplate.CommentDeductionFee * (-1), // 惩罚 记录负数
						OrderPrice:    co.Order.OrderPrice,
						ShipperID:     shipperId,
						OrderID:       co.Order.ID,
						OrderNo:       co.Order.OrderID,
						ShipperName:   shipper.RealName,
						ShipperMobile: shipper.Mobile,
						CommentStar:   int(co.Star),
					}
					insertItems = append(insertItems, incomeModel)

				}

			}
		}

		if len(insertItems) > 0 {
			//
			//写入 6:好评 7:差评
			db.Create(&insertItems)
		}
	}

	return nil
}

// 获取今天的全部配送员id
func (s *IncomeArchiveService) getShipperIdsByDay(archiveTime string, areaId int) []int {

	startTime := carbon.Parse(archiveTime, configs.AsiaShanghai).Format("Y-m-d")
	endTime := carbon.Parse(archiveTime, configs.AsiaShanghai).AddDays(1).Format("Y-m-d")
	var shipperIds []int
	db := tools.GetDB()
	oo := db.Model(&models.Order{}).
		Where("shipper_id > 0 AND archive_date >=  ? and archive_date < ?", startTime, endTime)
	if areaId > 0 {
		oo.Where("area_id = ?", areaId)
	}
	oo.Pluck("DISTINCT shipper_id", &shipperIds)
	return shipperIds
}

// 根据评论 更新 配送员收入
func (s *IncomeArchiveService) ArchiveIncomeByComment(day string, oneShipperId int, OldTemplateId int) error {
	archiveDay := carbon.Parse(day, configs.AsiaShanghai).Format("Y-m-d")
	// firstDayOfThisMOnth :=tools.GetFirstDateOfMonth(carbon.Parse(day,configs.AsiaShanghai).Carbon2Time()).Format("2006-01-02")
	// if firstDayOfThisMOnth != archiveDay { //不是本月第一天的话减去1天 避免跨月
	// archiveDay =carbon.Parse(day,configs.AsiaShanghai).AddDays(-1).Format("Y-m-d")
	// }
	shipperIds := s.getShipperIdsByCommentDay(archiveDay)
	for _, shipperId := range shipperIds {
		s.ArchiveIncomeCommentByShipperId(shipperId, archiveDay, oneShipperId, OldTemplateId)
	}
	return nil
}

// 根据 日期找出配送员id 4天前的数据 因为评论审核通过时间是 4天
func (s *IncomeArchiveService) getShipperIdsByCommentDay(archiveTime string) []int {
	currentDay := carbon.Parse(archiveTime, configs.AsiaShanghai).Format("Y-m-d") + " 23:59:59"
	fourDayBefore := carbon.Parse(archiveTime, configs.AsiaShanghai).AddDays(-4).Format("Y-m-d") + " 00:00:00"
	var shipperIds []int
	tools.GetDB().
		Model(&other.CommentModel{}).
		Where("shipper_id > 0 and type = 1 and state = 1  AND created_at between  ? and ?", fourDayBefore, currentDay).
		Pluck("DISTINCT shipper_id", &shipperIds)
	return shipperIds
}

// 配送员一天的总收入归档
func (s *IncomeArchiveService) ArchiveIncomeAll(day string, oneShipperId int, OldTemplateId int) error {

	db := tools.GetDB()
	var shippers []int

	day = carbon.Parse(day, configs.AsiaShanghai).AddDays(-1).Format("Y-m-d") //日期记录前一天

	qq :=db.Model(&shipment.ShipperIncome{}).
		Where("date = ? ", day)
		if oneShipperId > 0 { //单个配送员
			qq.Where("shipper_id = ?", oneShipperId)
		}
		qq.Select("shipper_id").
		Group("shipper_id").
		Scan(&shippers)

	for _, sp := range shippers {
		if oneShipperId > 0 && sp != oneShipperId { //单个配送员的数据归档
			continue
		}
		var incomeData shipment.ShipperIncomeArchive
		fields := `
		city_id,
		area_id,
		shipper_id,
		shipper_name,
		shipper_mobile,
		date,
		sum(if(type in (1,4,5) ,amount,0)) as shipment,
		sum(if(type in (2,3,6) ,amount,0)) as reward,
		sum(if(type in (7,8,9) ,amount,0)) as punishment,
		0 as other,
		sum(if(type = 8 ,1,0)) as complain_count,
		sum(IF ( type = 1, 1, 0 )) AS success_count,
		sum(IF( (type = 9 ), 1, 0 )) AS late_count, 
		sum(IF( type = 10, 1, 0 )) AS cancel_count,
		sum(IF( type = 11, 1, 0 )) AS fail_count,
		sum(IF( type = 6, 1, 0 )) AS comment_good_count,

		sum(if(type = 1 ,amount,0)) as amount_order,
		sum(if(type = 2 ,amount,0)) as amount_reward,
		sum(if(type = 3 ,amount,0)) as amount_tips,
		sum(if(type = 4 ,amount,0)) as amount_special_time,
		sum(if(type = 5 ,amount,0)) as amount_special_weather,
		sum(if(type = 6 ,amount,0)) as amount_comment_good,
		sum(if(type = 7 ,amount,0)) as amount_comment_bad,
		sum(if(type = 8 ,amount,0)) as amount_complain,
		sum(if(type = 9 ,amount,0)) as amount_late,
		sum(if(type = 10 ,amount,0)) as amount_cancel,
		sum(if(type = 11 ,amount,0)) as amount_fail,

		floor(sum(order_delivery_minute)/ (sum(IF ( type = 1, 1, 0 ))+sum(IF ( type = 9, 1, 0 ))) ) as order_delivery_minute,

		floor(avg(comment_star)) as comment_star,

		sum(if(type = 7 ,1,0)) as count_comment_bad,

		
		sum(if(type = 14,amount,0)) as amount_invite_user,
		sum(if(type = 15,amount,0)) as amount_order_tips,
		sum(if(type = 8 ,1,0)) as count_complain,
		sum(IF ( type = 1 and order_type= 2, 1, 0 )) AS count_special_price_order,
		sum(IF ( type = 1 and order_type= 2 ,amount,0)) as amount_special_price_order,
		sum(if(type = 16,amount,0)) as amount_insurance
		
		
		`
		db.Model(&shipment.ShipperIncome{}).
			Where("shipper_id = ? and date = ? ", sp, day).
			Select(fields).
			Scan(&incomeData)
		if len(incomeData.Date) > 0 {

			
			var incomeArchieObject shipment.ShipperIncomeArchive
			db.Model(&shipment.ShipperIncomeArchive{}).Where("shipper_id = ? and date = ? ", sp, day).Find(&incomeArchieObject)
			if incomeArchieObject.ID == 0 {
				er := db.Create(&incomeData).Error
				if er != nil {
					tools.Logger.Errorf("FATAL 配送员收入归档失败", er.Error())
				}
				db.Model(&shipment.ShipperIncome{}).
					Where("shipper_id = ? and date = ? ", sp, day).
					Updates(&map[string]interface{}{
						"archive_id": incomeData.ID,
					})
			} else {
				//更新数据
				db.Model(&shipment.ShipperIncome{}).
					Where("shipper_id = ? and date = ? ", sp, day).
					Updates(&map[string]interface{}{
						"archive_id": incomeArchieObject.ID,
					})
				// tools.Logger.Info(fmt.Sprintf("更新 配送员工资归档 数据 archive_id %d 原来的值 %v 日期 %s  新值 %v", incomeArchieObject.ID, incomeArchieObject,day, incomeData))
				db.Model(&shipment.ShipperIncomeArchive{}).
					Where("id = ? ", incomeArchieObject.ID).
					Updates(&map[string]interface{}{
						"shipment":incomeData.Shipment,
						"reward":incomeData.Reward,
						"punishment":incomeData.Punishment,
						"other":incomeData.Other,
						"complain_count":incomeData.ComplainCount,
						"success_count":incomeData.Success,
						"late_count":incomeData.Late, 
						"cancel_count":incomeData.Cancel,
						"fail_count":incomeData.Fail,
						"comment_good_count":incomeData.CommentGoodCount,
						"amount_order":incomeData.AmountOrder,
						"amount_reward":incomeData.AmountReward,
						"amount_tips":incomeData.AmountTips,
						"amount_special_time":incomeData.AmountSpecialTime,
						"amount_special_weather":incomeData.AmountSpecialWeather,
						"amount_comment_good":incomeData.AmountCommentGood,
						"amount_comment_bad":incomeData.AmountCommentBad,
						"amount_complain":incomeData.CountComplain,
						"amount_late":incomeData.AmountLate,
						"amount_cancel":incomeData.AmountCancel,
						"amount_fail":incomeData.AmountFail,
						"order_delivery_minute":incomeData.OrderDeliveryMinute,
						"comment_star":incomeData.CommentStar,
						"count_comment_bad":incomeData.CountCommentBad,
						"amount_invite_user":incomeData.AmountInviteUser,
						"amount_order_tips":incomeData.AmountOrderTips,
						"count_complain":incomeData.CountComplain,
						"count_special_price_order":incomeData.CountSpecialPriceOrder,
						"amount_special_price_order":incomeData.AmountSpecialPriceOrder,
						"amount_insurance":incomeData.AmountInsurance,
						
					})

				

			}
		}

		//更新 一天的 总归档 数据
		var shipper models.Admin
		db.Preload("ShipperIncomeTemplate").
			Where("id = ?", sp).
			Find(&shipper)
		if OldTemplateId > 0 { //这个月的第一天 上个月的 模板被修改的情况
			var oldTemplate shipment.ShipperIncomeTemplate
			db.Model(&shipment.ShipperIncomeTemplate{}).Where("id = ?", OldTemplateId).First(&oldTemplate)
			if oldTemplate.ID > 0 {
				shipper.ShipperIncomeTemplate = oldTemplate
			}
		}
		if shipper.ID > 0 && shipper.ShipperIncomeTemplate.ID > 0 && shipper.ShipperIncomeTemplate.RuleType == shipment.RuleTypeOrder {
			//1.获取总订单数量
			//2.更新这个月的 订单 配送费数据
			if *shipper.ShipperIncomeTemplate.RuleOrderCountType == shipment.RuleTypeOrderCountAll { //全部订单
				// 获取要更新的费用
				//取总订单数量

				days := tools.GetDaysOfMonth(day)
				//前面的数据全部更新
				for _, d := range days {

					//重新计算当天的记录
					var incomeData shipment.ShipperIncomeArchive
					fields := `
					date,
					sum(if(type in (1,4,5) ,amount,0)) as shipment,
					sum(if(type = 1 ,amount,0)) as amount_order
					`
					db.Model(&shipment.ShipperIncome{}).
						Where("shipper_id = ? and date = ? ", sp, d).
						Select(fields).
						Scan(&incomeData)
					if len(incomeData.Date) > 0 {
						var incomeOriginal shipment.ShipperIncomeArchive //原始记录
						db.Model(&shipment.ShipperIncomeArchive{}).
							Where("shipper_id = ? and date = ? ", sp, d).
							Find(&incomeOriginal)

						if incomeOriginal.ID > 0 { //存在的话更新

							var logs []shipment.ChangeLog
							var newLog shipment.ChangeLog
							newLog.OriginalValue = incomeOriginal.Shipment
							newLog.NewValue = incomeData.Shipment
							newLog.OriginalDate = d
							newLog.ChangeDate = day
							newLog.Type = constants.TypeOrder

							logs = append(logs, newLog)

							newLogContent, _ := json.Marshal(logs)
							db.Model(&shipment.ShipperIncomeArchive{}).Where("id= ?", incomeOriginal.ID).
								Updates(&map[string]interface{}{
									"change_log":   string(newLogContent),
									"shipment":     incomeData.Shipment,
									"amount_order": incomeData.AmountOrder,
								})
						}
					}

				}
			}

		}

	}

	return nil
}

// 记录已经取消的订单的记录
func (s *IncomeArchiveService) CanceledOrderArchive(shipperId int, realName string, mobile string, archiveDay string) error {
	db := tools.GetDB()
	var canceledOrder []models.TakeOrder
	//该配送员昨天退掉的订单
	startTime := carbon.Parse(archiveDay).AddDays(-1).Format("Y-m-d H:i:s")
	endTime := carbon.Parse(archiveDay).Format("Y-m-d H:i:s")
	db.Model(&models.TakeOrder{}).
		Where("admin_id = ? and state = ? and created_at between ? and ?", shipperId, 0, startTime, endTime).
		Preload("Order").
		Select("id,order_id").
		Find(&canceledOrder)

	//取消的订单 写入记录
	for _, co := range canceledOrder {
		if co.Order.ID == 0 { //防止出现没有订单信息的数据
			continue
		}
		ct := int64(0)
		db.Model(&shipment.ShipperIncome{}).
			Where("shipper_id = ? and order_id = ? and type = ? ", shipperId, co.OrderID, constants.TypeCancel).
			Select("id,type").
			Count(&ct)
		if ct == 0 {
			incomeModel := shipment.ShipperIncome{
				CityID:        co.Order.CityID,
				AreaID:        co.Order.AreaID,
				Date:          carbon.Parse(archiveDay).AddDays(-1).Format("Y-m-d"),
				Type:          constants.TypeCancel, //取消的订单
				Amount:        0,
				OrderPrice:    co.Order.OrderPrice,
				ShipperID:     shipperId,
				OrderID:       co.Order.ID,
				OrderNo:       co.Order.OrderID,
				ShipperName:   realName,
				ShipperMobile: mobile,
			}
			db.Create(&incomeModel)
		}

	}
	return nil
}

// 更新 根据 订单数量 更新 以前的配送费
func (s *IncomeArchiveService) UpdateShipperIncomeByOrderCount(shipper models.Admin, archiveDay string, oneShipperId int, OldTemplateId int) error {
	if oneShipperId > 0 && shipper.ID != oneShipperId { // 单个配送员归档
		return nil
	}

	db := tools.GetDB()
	//前期 不记录 的 按 月订单 数量计算的 配送费 数据更新
	if shipper.ShipperIncomeTemplate.ID > 0 && shipper.ShipperIncomeTemplate.RuleType == shipment.RuleTypeOrder {
		//1.获取总订单数量
		//2.更新这个月的 订单 配送费数据
		if *shipper.ShipperIncomeTemplate.RuleOrderCountType == shipment.RuleTypeOrderCountAll { //全部订单
			// 获取要更新的费用
			//取总订单数量

			month := carbon.Parse(archiveDay).AddDays(-1).Format("Y-m")
			orderCount := s.GetShipperMonthOrderCount(shipper.ID, month)

			shipmentFee, err := NewShipperFeeService().getOrderCountBaseShipmentPrice(shipper.ID, shipper.ShipperIncomeTemplate, orderCount)
			if err != nil {
				tools.Logger.Errorf("FATAL 配送员工资归档失败 原因 配送员模板内容错误", err)
				return err
			}

			currentDay := carbon.Parse(archiveDay).AddDays(-1).Format("Y-m-d")

			days := tools.GetDaysOfMonth(currentDay)

				var incomeOriginal []shipment.ShipperIncome //原始记录
				db.Model(&shipment.ShipperIncome{}).
					Where("shipper_id = ? and date in(?) and type = ?", shipper.ID, days, constants.TypeOrder).
					Find(&incomeOriginal)
				for _, inc := range incomeOriginal {

					if inc.ID > 0 { //存在的话更新
						// 如果特价活动，并收入大于0时，不更新收入
						if(inc.OrderType == constants.ShipperIncomeSpecialPriceOrder && inc.Amount > 0){
							continue
						}
						var logs []shipment.ChangeLog
						var newLog shipment.ChangeLog

						newLog.OriginalValue = inc.Amount
						newLog.NewValue = shipmentFee
						newLog.OriginalDate = inc.Date
						newLog.ChangeDate = archiveDay
						newLog.Type = constants.TypeOrder
						logs = append(logs, newLog)

						newLogContent, _ := json.Marshal(logs)
						updateMap := make(map[string]interface{})
						updateMap["change_log"] = string(newLogContent)
						updateMap["amount"] = shipmentFee
						updateMap["template_id"] = shipper.ShipperIncomeTemplate.ID
						db.Model(&shipment.ShipperIncome{}).Where("id= ?", inc.ID).
							Updates(&updateMap)
					}
				}
		}

	}
	return nil
}

// 配送员收入对账
func (s *IncomeArchiveService) IncomeCheck(day string) {

	day = carbon.Parse(day).AddDays(-1).Format("Y-m-d") //数据记录时记录昨天的日期
	//1.查询这一天的收入数据
	//2.根据配送员运行对账
	db := tools.GetDB()
	errorMap := make([]map[string]interface{}, 0)
	emptyDateItems := []int{}
	db.Model(&shipment.ShipperIncome{}).
		Where("date is null or area_id =0 or city_id = 0").
		Select("shipper_id").
		Scan(&emptyDateItems)
	if len(emptyDateItems) > 0 {
		tools.Logger.Errorf("FATAL 配送员收入列表 出现没有日期的收入 请检查")
		return
	}

	shipperIds := []int{}
	db.Model(&shipment.ShipperIncome{}).Where("date = ?", day).Group("shipper_id").Select("shipper_id").Scan(&shipperIds)
	for _, shipperId := range shipperIds {
		var shipper models.Admin
		db.Preload("ShipperIncomeTemplate").
			First(&shipper, shipperId)
		//归档的记录
		var incomeArchive shipment.ShipperIncomeArchive
		db.Model(&shipment.ShipperIncomeArchive{}).
			Where("date = ? and shipper_id = ?", day, shipperId).
			Find(&incomeArchive)

		//原始记录
		var incomes []shipment.ShipperIncome
		db.Model(&shipment.ShipperIncome{}).
			Where("date = ? and shipper_id = ?", day, shipperId).
			Find(&incomes)

		//归档时的标准
		var shipmentAmount int //sum(if(type in (1,4,5) ,amount,0)) as shipment,
		var reward int         //sum(if(type in (2,3,6) ,amount,0)) as reward,
		var punishment int     //sum(if(type in (7,8,9) ,amount,0)) as punishment,
		var complainCount int  //sum(if(type = 8 ,1,0)) as complain_count,
		var successCount int   //sum(IF ( type = 1, 1, 0 )) AS success_count,
		var lateCount int      //sum(IF( (type = 9 and amount !=0), 1, 0 )) AS late_count,
		// var cancelCount int //sum(IF( type = 10, 1, 0 )) AS cancel_count,
		// var failCount int //sum(IF( type = 11, 1, 0 )) AS fail_count,
		var commentGoodCount int      //sum(IF( (type = 6 and amount !=0), 1, 0 )) AS comment_good_count,

		var amountOrder int          //sum(if(type = 1 ,amount,0)) as amount_order,
		var amountReward int         //sum(if(type = 2 ,amount,0)) as amount_reward,
		var amountTips int           //sum(if(type = 3 ,amount,0)) as amount_tips,
		var amountSpecialTime int    //sum(if(type = 4 ,amount,0)) as amount_special_time,
		var amountSpecialWeather int //sum(if(type = 5 ,amount,0)) as amount_special_weather,
		var amountCommentGood int    //sum(if(type = 6 ,amount,0)) as amount_comment_good,

		var amountCommentBad int     //sum(if(type = 7 ,amount,0)) as amount_comment_bad,
		var amountComplain int       //sum(if(type = 8 ,amount,0)) as amount_complain,
		var amountLate int           //sum(if(type = 9 ,amount,0)) as amount_late,
		// var amountCancel int //sum(if(type = 10 ,amount,0)) as amount_cancel,
		// var amountFail int //sum(if(type = 11 ,amount,0)) as amount_fail,
		var orderDeliveryMinute int //floor(sum(order_delivery_minute)/ (sum(IF ( type = 1, 1, 0 ))+sum(IF ( type = 9, 1, 0 ))) ) as order_delivery_minute,
		var orderSuccessDeliveryMinute int
		var orderLateDeliveryMinute int
		var commentStar int      //floor(avg(comment_star)) as comment_star,
		var commentStarCount int //floor(avg(comment_star)) as comment_star,
		var countCommentBad int  //sum(if(type = 7 ,1,0)) as count_comment_bad,
		// var countComplain int    //sum(if(type = 8 ,1,0)) as count_complain

		for _, inc := range incomes {
			orderDeliveryMinute += inc.OrderDeliveryMinute
			commentStarCount++
			commentStar += inc.CommentStar
			switch inc.Type {
			case constants.TypeOrder: // 订单收入
				successCount++
				orderSuccessDeliveryMinute += inc.OrderDeliveryMinute
				if inc.Amount != 0 {
					//数据存在 需要检查
					shipmentAmount += inc.Amount
					amountOrder += inc.Amount
				}
			case constants.TypeAward: //奖励收入
				if inc.Amount != 0 {
					//数据存在 需要检查
					reward += inc.Amount
					amountReward += inc.Amount
				}
			case constants.TypeTips: // 打赏收入
				if inc.Amount != 0 {
					//数据存在 需要检查
					reward += inc.Amount
					amountTips += inc.Amount
				}
			case constants.TypeSpecialTime: // 特殊时间
				if inc.Amount != 0 {
					//数据存在 需要检查
					shipmentAmount += inc.Amount
					amountSpecialTime += inc.Amount

				}
			case constants.TypeSpecialWeather: // 特殊天气
				if inc.Amount != 0 {
					//数据存在 需要检查
					shipmentAmount += inc.Amount
					amountSpecialWeather += inc.Amount

				}
			case constants.TypeGoodComment: // 好评
				if inc.Amount != 0 {
					//数据存在 需要检查
					reward += inc.Amount
					amountCommentGood += inc.Amount
					commentGoodCount++

				}
			case constants.TypeBadComment: // 差评
				if inc.Amount != 0 {
					//数据存在 需要检查
					punishment += inc.Amount
					amountCommentBad += inc.Amount
					countCommentBad++
				}
			case constants.TypeComplaint: // 投诉
				if inc.Amount != 0 {
					//数据存在 需要检查
					punishment += inc.Amount
					complainCount++
					amountComplain += inc.Amount
				}
			case constants.TypeLate: // 迟到
				// if inc.Amount != 0 {
					punishment += inc.Amount
					lateCount++
					amountLate += inc.Amount
					orderLateDeliveryMinute += inc.OrderDeliveryMinute
					//迟到的数据存在 需要检查
					var late models.LateOrders
					db.Model(&models.LateOrders{}).
						Where("shipper_id = ? and created_at > ? and created_at <= ?", shipperId, carbon.Parse(day).Format("Y-m-d")+" 00:00:00", carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s")).
						Find(&late)

					if late.ID == 0 { //迟到的数据不存在 扣除的费用 不合理
						errorMap = append(errorMap, map[string]interface{}{
							"income_id": inc.ID,
							"msg":       "迟到的数据不存在 扣除的费用 不合理",
						})
					} else {
						var order models.Order
						db.Model(&models.Order{}).
							Preload("LateOrders").
							Where("shipper_id = ?  AND id =?",
								shipperId,inc.OrderID).
							Find(&order)
						lateFee := NewShipperFeeService().CheckLateOrderFee(shipper.ShipperIncomeTemplate,order,late.LateMinute)
						if lateFee != 0 {
							lateFee = lateFee * -1
							if inc.Amount != lateFee { //迟到扣款的费用金额不正确
								errorMap = append(errorMap, map[string]interface{}{
									"income_id": inc.ID,
									"msg":       "迟到扣款的费用金额不正确",
								})
							}
						}
					}

				// }
				// case constants.TypeIntroduceCustomer: //客户拓展 奖励收入
				// if inc.Amount != 0 {
				// 	//数据存在 需要检查
				// 	reward += inc.Amount
				// 	amountReward += inc.Amount
				// }
				// case constants.TypeIntroduceCustomerOrder: //客户拓展 下单 奖励收入
				// if inc.Amount != 0 {
				// 	//数据存在 需要检查
				// 	reward += inc.Amount
				// 	amountReward += inc.Amount
				// }
				// case constants.TypeCancel://取消订单
				// case constants.TypeFail://失败订单

			}

		}
		if incomeArchive.Shipment != shipmentAmount {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "shipment 不正确",
			})
		}
		if incomeArchive.Reward != reward {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "reward 不正确",
			})
		}
		if incomeArchive.Punishment != punishment {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "punishment 不正确",
			})
		}
		if incomeArchive.ComplainCount != complainCount {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "complain_count 不正确",
			})
		}
		if incomeArchive.Success != successCount {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "success不正确",
			})
		}
		if incomeArchive.Late != lateCount {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "late 不正确",
			})
		}
		if incomeArchive.AmountOrder != amountOrder {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "amount_order不正确",
			})
		}
		if incomeArchive.AmountReward != amountReward {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "amount_reward不正确",
			})
		}
		if incomeArchive.AmountTips != amountTips {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "amount_tips不正确",
			})
		}
		if incomeArchive.AmountSpecialTime != amountSpecialTime {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "amount_special_time不正确",
			})
		}
		if incomeArchive.AmountSpecialWeather != amountSpecialWeather {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "amount_special_weather不正确",
			})
		}
		if incomeArchive.AmountCommentGood != amountCommentGood {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "amount_comment_good不正确",
			})
		}
		if incomeArchive.AmountCommentBad != amountCommentBad {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "amount_comment_bad不正确",
			})
		}
		if incomeArchive.AmountComplain != amountComplain {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "amount_complain不正确",
			})
		}
		if incomeArchive.AmountLate != amountLate {
			errorMap = append(errorMap, map[string]interface{}{
				"income_archive.id": incomeArchive.ID,
				"msg":               "amount_complain不正确",
			})
		}

	}

	if len(errorMap) > 0 {
		errs, _ := json.Marshal(errorMap)
		tools.Logger.Errorf("FATAL 配送员收入对账失败 请检查", string(errs))
	}

}

// 全勤奖计算
func (s *IncomeArchiveService) ArchiveFullAttendancePrize(shipper models.Admin, archiveDay string, oneShipperId int, OldTemplateId int) {
	if oneShipperId > 0 && shipper.ID != oneShipperId { // 单个配送员归档
		return
	}
	//全勤奖计算规则获取
	//上一个月的 考勤数据
	//上个月的最后第一天 计算上个月的全勤奖
	month := carbon.Parse(archiveDay, configs.AsiaShanghai).AddDays(-1).Format("Y-m")
	start, end := tools.GetMonthStartAndEndWithParam(month, "Y-m")
	date := carbon.Parse(archiveDay, configs.AsiaShanghai).AddDays(-1).Format("Y-m-d")

	if date == end { //这个月的第一天 计算上个月的全勤奖
		//上个月的考勤数据

		db := tools.GetDB()
		var shipperRewardSetting shipment.ShipperRewardSetting
		db.Model(&shipment.ShipperRewardSetting{}).
			Where("city_id =? and area_id = ? and state = ?", shipper.AdminCityID, shipper.AdminAreaID, 1).
			Find(&shipperRewardSetting)
		if shipperRewardSetting.ID == 0 {
			//没有设置 全勤奖的话不用计算
			return
		}

		var leaveCount int64 //请假数量
		db.Model(&models.ShipperAttendanceLog{}).
			Where("city_id =? and area_id = ?", shipper.AdminCityID, shipper.AdminAreaID).
			Where("created_at > ? and created_at <= ?", start+" 00:00:00", end+" 23:59:59").
			Where("state in ? ", []int{4, 5}).
			Count(&leaveCount)
		if leaveCount > 0 {
			//请假的话 不计算 全勤奖
			return
		}

		//总上班天数
		totalDays := tools.ToInt(tools.GetMonthLength(month)) - shipperRewardSetting.MonthRestDay

		if len(shipperRewardSetting.WorkTime) > 0 {
			//state 1 上班 2 下班 3 休息 4 请假 5 事故 6 管理员暂停配送员配送 7 管理员开启配送员配送
			var shipperAttendanceLog []models.ShipperAttendanceLog
			query := db.Model(&models.ShipperAttendanceLog{}).
				Where("city_id =? and area_id = ? and shipper_id = ?", shipper.AdminCityID, shipper.AdminAreaID, shipper.ID).
				Where("created_at <= ?", shipperRewardSetting.WorkTime).
				Where("state = ?", 1) //打卡
			query.Find(&shipperAttendanceLog)

			if len(shipperAttendanceLog) < totalDays {
				//总天数 小于 需要总天数
				return
			}
		}

		//应该完成的订单数量
		totalShouldOrderCount := tools.ToInt(shipperRewardSetting.DailyOrderLimit) * totalDays
		var successCount int64
		db.Model(&shipment.ShipperIncomeArchive{}).
			Where("shipper_id = ? and date between ? and ? ", shipper.ID, start, end).
			Select("sum(success_count) as success_count").
			Scan(&successCount)

		if tools.ToInt(successCount) < totalShouldOrderCount {
			//订单数量不达标的话 不计算 全勤奖
			return
		}
		//写入全勤奖
		if shipperRewardSetting.RewardAmount == 0 {
			//金额 为零 的话 不记录 全勤奖
			return
		}
		incomeModel := shipment.ShipperIncome{
			CityID:        shipper.AdminCityID,
			AreaID:        shipper.AdminAreaID,
			Date:          date,
			Type:          constants.TypeFullAttendance, //全勤奖
			Amount:        shipperRewardSetting.RewardAmount,
			OrderPrice:    0,
			ShipperID:     shipper.ID,
			OrderID:       0,
			OrderNo:       "",
			ShipperName:   shipper.RealName,
			ShipperMobile: shipper.Mobile,
		}
		db.Create(&incomeModel)

	}

}

// 基本工资 写入当天的收入
func (s *IncomeArchiveService) ArchiveBaseSalary(shipper models.Admin, archiveDay string, oneShipperId int, OldTemplateId int) {
	if oneShipperId > 0 && shipper.ID != oneShipperId { // 单个配送员归档
		return
	}
	month := carbon.Parse(archiveDay, configs.AsiaShanghai).AddDays(-1).Format("Y-m")
	_, end := tools.GetMonthStartAndEndWithParam(month, "Y-m")
	date := carbon.Parse(archiveDay, configs.AsiaShanghai).AddDays(-1).Format("Y-m-d")

	if date == end { //这个月的第一天 计算上个月的基本工资写入当天的收入

		if shipper.ShipperIncomeTemplate.BaseSalary > 0 {
			//TODO 基本工资计算是否还有其他条件 上班时间？ 开始配送日期？ 新进来的配送员？ 这个月结束前几天进来的配送员？
			db := tools.GetDB()
			//写入基本工资
			incomeModel := shipment.ShipperIncome{
				CityID:        shipper.AdminCityID,
				AreaID:        shipper.AdminAreaID,
				Date:          date,
				Type:          constants.TypeBaseSalary, //基本工资
				Amount:        shipper.ShipperIncomeTemplate.BaseSalary,
				OrderPrice:    0,
				ShipperID:     shipper.ID,
				OrderID:       0,
				OrderNo:       "",
				ShipperName:   shipper.RealName,
				ShipperMobile: shipper.Mobile,
			}
			db.Create(&incomeModel)

		}
	}

}


// 客户拓展收入 归档
func (s *IncomeArchiveService) ArchiveCustomerIntroduce( archiveDay string, oneShipperId int, OldTemplateId int) {

	date := carbon.Parse(archiveDay, configs.AsiaShanghai).AddDays(-1).Format("Y-m-d")

	db :=tools.GetDB()
	//查询属于该配送员的 客户拓展收入，客户下单收入

	var userFees []models.AdvertMaterialShipperDailyStatistic
	db.Model(&models.AdvertMaterialShipperDailyStatistic{}).
		Preload("Shipper").
		Where("date = ? ",date).
		Select("id,shipper_id,IF(ISNULL(sum(invite_user_fee)),0,sum(invite_user_fee)) as invite_user_fee,IF(ISNULL(sum(total_order_tips_fee)),0,sum(total_order_tips_fee)) as total_order_tips_fee").
		Group("shipper_id").
		Find(&userFees)
	for _, userFee := range userFees {
		var income []shipment.ShipperIncome
		db.Model(&shipment.ShipperIncome{}).
			Where("shipper_id = ? and date = ? and type in ?",userFee.ShipperId,date,[]int{constants.TypeIntroduceCustomer,constants.TypeIntroduceCustomerOrder}).
			Find(&income)
			shipper :=userFee.Shipper
		if len(income) == 0 {
			incomeModel := shipment.ShipperIncome{
				CityID:        shipper.AdminCityID,
				AreaID:        shipper.AdminAreaID,
				Date:          date,
				Type:          constants.TypeIntroduceCustomer, //介绍客户收入
				Amount:        userFee.InviteUserFee,
				OrderPrice:    0,
				ShipperID:     shipper.ID,
				OrderID:       0,
				OrderNo:       "",
				ShipperName:   shipper.RealName,
				ShipperMobile: shipper.Mobile,
			}
			db.Create(&incomeModel)
			incomeModel = shipment.ShipperIncome{
				CityID:        shipper.AdminCityID,
				AreaID:        shipper.AdminAreaID,
				Date:          date,
				Type:          constants.TypeIntroduceCustomerOrder, //介绍客户下单后的收入
				Amount:        userFee.TotalOrderTipsFee,
				OrderPrice:    0,
				ShipperID:     shipper.ID,
				OrderID:       0,
				OrderNo:       "",
				ShipperName:   shipper.RealName,
				ShipperMobile: shipper.Mobile,
			}
			db.Create(&incomeModel)
		}else{
			//TODO 需要更新的时候再考虑
		}

	}




}



// 保险扣费 归档
func (s *IncomeArchiveService) ArchiveInsurance( archiveDay string, oneShipperId int, OldTemplateId int) {

	date := carbon.Parse(archiveDay, configs.AsiaShanghai).AddDays(-1).Format("Y-m-d")

	db :=tools.GetDB()
	//查询属于该配送员的保险信息
	if oneShipperId > 0 {//单个配送员的数据归档
		var shipper models.Admin
		db.Model(&models.Admin{}).Where("id = ?",oneShipperId).Find(&shipper)
		var insuranceLog models.ShipperInsuranceLog
		db.Model(&models.ShipperInsuranceLog{}).
		Where("shipper_id = ?",oneShipperId).
			Where("date = ? ",date).
			Where("state = ? ",3).//保险通过 
			Find(&insuranceLog)
			insuranceAmount :=0	
		if insuranceLog.ID > 0 {
			insuranceAmount =(-1)*insuranceLog.Amount
		}
		var income []shipment.ShipperIncome
		db.Model(&shipment.ShipperIncome{}).
			Where("shipper_id = ? and date = ? and type = ?",oneShipperId,date,constants.TypeInsurance).
			Find(&income)
		if len(income) == 0 && insuranceAmount !=0 {
			
			incomeModel := shipment.ShipperIncome{
				CityID:        shipper.AdminCityID,
				AreaID:        shipper.AdminAreaID,
				Date:          date,
				Type:          constants.TypeInsurance, //保险扣费
				Amount:        insuranceAmount,
				OrderPrice:    0,
				ShipperID:     shipper.ID,
				OrderID:       0,
				OrderNo:       "",
				ShipperName:   shipper.RealName,
				ShipperMobile: shipper.Mobile,
			}
			db.Create(&incomeModel)
		}
		return
	}
	var insuranceLogs []models.ShipperInsuranceLog
	db.Model(&models.ShipperInsuranceLog{}).
		Preload("Admin").
		Where("date = ? ",date).
		Where("state = ? ",3).//保险通过 
		Find(&insuranceLogs)
	for _, log := range insuranceLogs {	
		insuranceAmount :=(-1)*log.Amount
		var income shipment.ShipperIncome
		db.Model(&shipment.ShipperIncome{}).
			Where("shipper_id = ? and date = ? and type = ?",log.ShipperID,date,constants.TypeInsurance).
			Find(&income)
		if income.ID == 0 && insuranceAmount !=0 {
			
			incomeModel := shipment.ShipperIncome{
				CityID:        log.Admin.AdminCityID,
				AreaID:        log.Admin.AdminAreaID,
				Date:          date,
				Type:          constants.TypeInsurance, //保险扣费
				Amount:        insuranceAmount,
				OrderPrice:    0,
				ShipperID:     log.ShipperID,
				OrderID:       0,
				OrderNo:       "",
				ShipperName:   log.Admin.RealName,
				ShipperMobile: log.Admin.Mobile,
			}
			db.Create(&incomeModel)
		}
	}
	

	




}