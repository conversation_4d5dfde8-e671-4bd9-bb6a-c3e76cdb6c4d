package shipperv2

import (
	"bytes"
	"context"
	"crypto/md5"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math"
	"math/rand"
	"mime/multipart"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/factory"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/models/Comments"
	"mulazim-api/models/help"
	Other "mulazim-api/models/other"
	other "mulazim-api/models/other"
	"mulazim-api/models/privacy"
	"mulazim-api/models/shipment"
	"mulazim-api/resources"
	shipperResources "mulazim-api/resources/shipper"
	"mulazim-api/scopes"
	"mulazim-api/services"
	"mulazim-api/services/sms"
	"mulazim-api/tools"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
	"unsafe"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type ShipperService struct {
	services.BaseService
	langUtil *lang.LangUtil
	language string
}

// GetList 获取配送员新订单
//
//	@Description:
//	@receiver shipper
//	@param c
//	@param admin
//	@param streetId
//	@return []models.OrderToday
func (shipper ShipperService) GetList(c *gin.Context, admin models.Admin, streetId string) []models.OrderToday {
	var resIds []int
	resIds = shipper.GetRestaurantIds(c, admin)
	var orderModel []models.OrderToday
	db := tools.GetDB()
	query := db.Select("t_order_today.id,t_order_today.area_id,t_order_today.city_id,t_order_today.order_id,t_order_today.building_id,t_order_today.timezone,t_order_today.store_id,t_order_today.serial_number,t_order_today.state,t_order_today.booking_time,t_order_today.order_address,t_order_today.order_type,t_restaurant_building.distance,t_order_today.market_type,t_order_today.printed_time,t_order_today.actual_paid").
		Joins("LEFT JOIN `t_restaurant_building` ON `t_restaurant_building`.`restaurant_id`=`t_order_today`.`store_id` AND `t_restaurant_building`.`building_id`=`t_order_today`.`building_id`").
		Where("`t_order_today`.`state` IN  ?  ", []int{2, 3, 4, 5}).
		Where(" `t_order_today`.`taked` = 0").
		Where(" `t_order_today`.`delivery_type` != 2").
		Where("`t_order_today`.`deleted_at` IS NULL").
		Where("`t_order_today`.`category_id` IN  ?  ", []int{1, 4}).
		Where("`t_order_today`.`store_id` IN  ?  ", resIds).
		Preload("AddressView", func(db *gorm.DB) *gorm.DB {
			return db.Select("address_view.city_name_" + shipper.language + " as city_name," +
				"address_view.area_name_" + shipper.language + " as area_name," +
				"address_view.building_name_" + shipper.language + " as building_name," +
				"address_view.street_name_" + shipper.language + " as street_name, " +
				"address_view.building_id as building_id,street_id")
		}).
		Preload("Restaurant", func(db *gorm.DB) *gorm.DB {
			return db.Select(
				"t_restaurant.name_" + shipper.language + " as name," +
					"t_restaurant.address_" + shipper.language + " as address," + "t_restaurant.id",
			)
		}).
		Preload("PushDetail", func(db *gorm.DB) *gorm.DB {
			return db.Where("t_shipper_order_push_detail.shipper_id = ?", admin.ID)
		}).
		Preload("OrderExtend")
	if admin.TakeCashOrder == 0 {
		query.Where("t_order_today.pay_type = 5")
	}
	if streetId != "" {
		query.Joins("left join t_restaurant on t_restaurant.id = t_order_today.store_id").
			Where("t_restaurant.street_id", streetId)
	}
	query.Order("`state` DESC,`t_order_today`.`booking_time` ASC")
	query.Find(&orderModel)
	return orderModel
}

// AutoDispatchFilterOrder
//
// @Description: 过滤压单订单
// @Author: Rixat
// @Time: 2024-08-23 16:20:52
// @receiver 
// @param c *gin.Context
func (t ShipperService) AutoDispatchFilterOrder(areaId int ,orders []models.OrderToday) []models.OrderToday {
	var area models.Area
	tools.Db.Model(area).Where("id=? and state=1 ",areaId).Scan(&area)
	if area.AutoDispatchState != 1 {
		return orders
	}
	autoDispatchWaitSecond := area.AutoDispatchWait*60
	nowTime := carbon.Now()
	// 筛选没有压单的订单
	var newOrders []models.OrderToday
	for _,order := range orders{
		printedTime := carbon.Parse(order.PrintedTime,"Asia/Shanghai")
		// 订单已压单时间 大于 压单等待时间 才给配送员显示
		if printedTime.DiffAbsInSeconds(nowTime) > tools.ToInt64(autoDispatchWaitSecond) {
			newOrders = append(newOrders,order)
		}
	}
	return newOrders
}

// GetMyOrderList
//
//	@Description: 我的订单列表
//	@author: Alimjan
//	@Time: 2022-09-12 18:40:49
//	@receiver shipper ShipperService
//	@param admin models.Admin
//	@param categoryId int
//	@return []map[string]interface{}
func (shipper ShipperService) GetMyOrderList(admin models.Admin, categoryId int) []map[string]interface{} {
	orders := shipper.getOrderListByState(admin.ID, categoryId, 1, "")
	groupPrefix := "group_"
	adminType := "_shipper_count"
	for k, o := range orders {
		msgCount := 0
		if o["order_id"] != nil {
			key := groupPrefix + tools.ToString(o["order_id"]) + adminType
			msgCount = tools.GetGroupChatMsgCount(key)
		}
		orders[k]["msg_count"] = msgCount
	}
	return orders
}

// GetFinishedOrderList
//
//	@Description: 获取配送员完成订单列表
//	@author: Alimjan
//	@Time: 2022-09-12 18:40:28
//	@receiver shipper ShipperService
//	@param admin models.Admin
//	@param categoryId int
//	@return []map[string]interface{}
func (shipper ShipperService) GetFinishedOrderList(admin models.Admin, categoryId int) []map[string]interface{} {
	orders := shipper.getOrderListByState(admin.ID, categoryId, 3, "updated_at")
	groupPrefix := "group_"
	adminType := "_shipper_count"
	for k, o := range orders {
		msgCount := 0
		if o["id"] != nil {
			key := groupPrefix + tools.ToString(o["id"]) + adminType
			msgCount = tools.GetGroupChatMsgCount(key)
		}
		orders[k]["msg_count"] = msgCount
	}
	return orders
}

// getOrderListByState
//
//	@Description: 获取我的订单和完成订单时使用
//	@author: Alimjan
//	@Time: 2022-09-12 18:41:17
//	@receiver shipper ShipperService
//	@param adminId int
//	@param catogeryId int
//	@param state int
//	@param sort string
//	@return []map[string]interface{}
func (shipper ShipperService) getOrderListByState(adminId int, catogeryId int, state int, sort string) []map[string]interface{} {
	db := tools.GetDB()
	//var orderModel []models.TakeOrder
	var result []map[string]interface{}
	sqlSelect := `t_take_order.id,
t_take_order.order_id, 
t_take_order.set_channel, 
t_order_today.id AS order_today_id,
t_order_today.state,
t_order_today.order_id as order_id_long,
t_order_today.pay_type,
t_order_today.timezone,
t_order_today.booking_time,
t_order_today.order_address,
t_order_today.booking_time,
t_order_today.building_id,
t_order_today.consume_type,
t_order_today.price,
t_order_today.shipment,
t_order_today.lunch_box_fee,
t_order_today.real_shipment,
t_order_today.deduction_fee,
t_order_today.serial_number,
t_order_today.order_type,
t_order_today.cash_clear_state,
t_order_today.cash_clear_channel,
t_order_today.mobile,
t_order_today.description,

t_order_today.actual_paid,
t_order_today.total_discount_amount,

address_view.city_id,
address_view.city_name_` + shipper.language + ` as city_name,
address_view.area_id, 
address_view.area_name_` + shipper.language + ` as area_name,
address_view.street_id,
address_view.street_name_` + shipper.language + ` as street_name,
address_view.street_name_zh,
address_view.building_name_` + shipper.language + ` as building_name,
address_view.building_lat,
address_view.building_lng,
address_view.building_name_zh,
b_pay_type.name_` + shipper.language + ` as pay_type_name
`
	query := db.Table("t_take_order").Select(sqlSelect).
		Joins("left join t_order_today on t_take_order.order_id = t_order_today.id").
		Joins("left join address_view on address_view.building_id = t_order_today.building_id").
		Joins("left join b_pay_type on t_order_today.pay_type = b_pay_type.id")
	if catogeryId == 1 || catogeryId == 4 {
		sqlSelect += `,
t_restaurant.name_` + shipper.language + ` as restaurant_name,
t_restaurant.lat as res_lat,
t_restaurant.lng as res_lng,
t_restaurant.tel as res_tel,
t_restaurant.tel as res_tel4,
t_restaurant.tel as res_tel5,
t_user.mobile as customer_original_mobile,
t_restaurant_building.distance,
t_restaurant.address_` + shipper.language + ` as restaurant_address,
t_shipper_order_push_detail.estimated_shipping_price,
t_order_extend.shipper_arrived_shop_at,
t_order_extend.shipper_take_food_at,
t_order_extend.foods_ready_time
`
		query.Select(sqlSelect).
			Joins("left join t_restaurant on t_order_today.store_id = t_restaurant.id").
			Joins("left join t_user on t_order_today.user_id = t_user.id").
			Joins("left join t_restaurant_building on t_restaurant_building.restaurant_id=t_order_today.store_id and t_restaurant_building.building_id = t_order_today.building_id").
			Joins("LEFT JOIN t_shipper_order_push_detail ON t_shipper_order_push_detail.order_id = t_order_today.id and t_shipper_order_push_detail.shipper_id = ?", adminId). //今日订单 关联  t_shipper_order_push_detail 推送时的配送价格
			Joins("left join t_order_extend on t_take_order.order_id = t_order_extend.order_id")

	}
	query.
		Where("`t_take_order`.`admin_id` = ?", adminId).
		Where("`t_take_order`.`created_at` > ?", tools.Yesterday("Asia/Shanghai")).
		Where("t_order_today.id IS NOT NULL ").
		Where("`t_take_order`.`state` = ? ", state).
		Where("`t_order_today`.`state` IN ( 3, 4, 5, 6, 7 ) ").
		Where("`t_take_order`.`deleted_at` IS NULL ")
	if len(sort) > 0 {
		query.Order("t_take_order." + sort + " desc")
	} else {
		query.Order("`t_order_today`.`booking_time` ASC")
	}
	query.
		Group("t_order_today.id").
		Find(&result)

	// 获取订单的美食详情
	for i := range result {
		orderID := result[i]["order_id"]
		//fmt.Println(orderID)
		var orderDetails []map[string]interface{}
		db.Table("t_order_detail").
			Select("t_order_detail.store_foods_id,t_order_detail.price, t_order_detail.number, t_order_detail.lunch_box_fee,t_order_detail.lunch_box_count, t_restaurant_foods.name_"+shipper.language+" as food_name").
			Joins("left join t_restaurant_foods on t_order_detail.store_foods_id = t_restaurant_foods.id").
			Where("t_order_detail.order_id = ?", orderID).
			Find(&orderDetails)

		result[i]["order_details"] = orderDetails
	}
	return result
}

func (shipper ShipperService) GetFailedOrderList(admin models.Admin, catogeryId int) []map[string]interface{} {
	db := tools.GetDB()
	//var orderModel []models.TakeOrder
	adminId := admin.ID
	var result []map[string]interface{}
	sqlSelect := `
t_take_order.id,
t_take_order.set_channel, 
t_take_order.order_id as order_today_id,
t_take_order.state,
t_order_today.booking_time,
t_order_today.order_address,
t_order_today.timezone,
t_order_today.booking_time,
b_order_fail_reason.reason_` + shipper.language + ` as reason,
t_order_today.building_id,
t_order_today.serial_number,
address_view.city_id,
address_view.city_name_` + shipper.language + ` as city_name,
address_view.area_id,
address_view.area_name_` + shipper.language + ` as area_name,
address_view.street_id,
address_view.street_name_` + shipper.language + ` as street_name,
address_view.building_name_` + shipper.language + ` as building_name,
address_view.building_name_zh`
	query := db.Table("t_take_order").Select(sqlSelect).
		Joins("left join t_order_today on t_take_order.order_id = t_order_today.id").
		Joins("LEFT JOIN b_order_fail_reason ON t_take_order.reason_id = b_order_fail_reason.id").
		Joins("left join address_view on address_view.building_id = t_order_today.building_id")
	if catogeryId == 1 || catogeryId == 4 {
		sqlSelect += `,
t_restaurant.name_` + shipper.language + ` as restaurant_name,
t_restaurant_building.distance,
t_restaurant.address_` + shipper.language + ` as restaurant_address`
		query.Select(sqlSelect).
			Joins("left join t_restaurant on t_order_today.store_id = t_restaurant.id").
			Joins("left join t_restaurant_building on t_restaurant_building.restaurant_id=t_order_today.store_id and t_restaurant_building.building_id = t_order_today.building_id")
	}
	query.
		Where("`t_take_order`.`admin_id` = ?", adminId).
		Where("`t_take_order`.`created_at` > ?", tools.Yesterday("Asia/Shanghai")).
		Where("t_order_today.id IS NOT NULL ").
		Where("t_take_order.state IN ( '0', '2' ) ").
		Where("`t_take_order`.`deleted_at` IS NULL ")
	query.Order("`t_order_today`.`booking_time` ASC")
	query.Find(&result)

	return result
}

// GetRestaurantIds 获取配送员所属餐厅
//
//	@Description:
//	@receiver shipper
//	@param admin
//	@return []int
func (shipper ShipperService) GetRestaurantIds(c *gin.Context, admin models.Admin) []int {
	var (
		resShipperKey = "s_r_"
		list          = []int{}
	)
	resShipperKey = resShipperKey + fmt.Sprintf("%d", admin.ID)
	resIdsStr := tools.Remember(c, resShipperKey, 15*time.Minute, func() interface{} {
		var resIds = []int{}
		db := tools.GetDB()
		db.Model(models.Restaurant{}).
			Select("id").
			Joins("INNER JOIN `b_admin_store` ON `t_restaurant`.`id` = `b_admin_store`.`store_id`").
			Where("`b_admin_store`.`admin_id` = ? AND `t_restaurant`.`deleted_at` IS NULL", admin.ID).
			Pluck("id", &resIds)
		return resIds
	})
	_ = json.Unmarshal([]byte(resIdsStr), &list)
	return list
}

// pushedOrderIds
//
//	@Description: 获取骑手推送订单id
//	@author: Alimjan
//	@Time: 2023-08-31 12:36:32
//	@receiver t ShipperTransformer
//	@param admin models.Admin
//	@return []int
func (t ShipperService) pushedOrderIds(admin models.Admin) []int {

	//if true {
	//	return []int{}
	//}
	db := tools.Db
	var rtn []int
	cr := carbon.Now().SubDays(1).ToDateTimeString()
	db.Model(&models.ShipperOrderPushDetail{}).Select("order_id").
		Where("created_at > ? ", cr).
		Where("shipper_id = ?", admin.ID).Pluck("t_shipper_order_push_detail.order_id", &rtn)
	return rtn
}

// isPushedShipper
//
//	@Description: 判断这个配送员是否被推送区域的
//	@author: Alimjan
//	@Time: 2023-08-31 13:08:53
//	@receiver t ShipperTransformer
//	@param list []models.OrderToday
//	@return bool
func (t ShipperService) isPushedShipper(c *gin.Context, admin models.Admin) bool {
	cacheKey := "is_push_user_" + fmt.Sprintf("%d", admin.ID)
	areaIdStr := tools.Remember(c, cacheKey, 1*time.Hour, func() interface{} {
		db := tools.GetDB()
		var areas models.AdminAreas
		db.Model(&models.AdminAreas{}).Where("admin_id = ?", admin.ID).Scan(&areas)
		// tools.Logger.Info(" areas.AreaID:", areas.AreaID)
		return areas.AreaID
	})
	areaId, _ := strconv.Atoi(areaIdStr)
	if configs.MyApp.ShipperPushAll == 1 {
		return true
	}
	return tools.InArray(areaId, configs.MyApp.ShipperPushArea)
}

// GetTabCount 获取配送员tab 红色角标 数字
//
//	@Description:
//	@receiver shipper
//	@param c
//	@param admin
//	@return map[string]int64
func (shipper ShipperService) GetTabCount(c *gin.Context, admin models.Admin) map[string]int64 {
	resIds := shipper.GetRestaurantIds(c, admin)
	isPushedShipper := shipper.isPushedShipper(c, admin)
	db := tools.GetDB()
	query := db.Model(&models.OrderToday{}).Select("id,printed_time").
		Where("`t_order_today`.`state` IN  ?  ", []int{2, 3, 4, 5}).
		Where(" `t_order_today`.`taked` = 0").
		Where(" `t_order_today`.`delivery_type` != 2").
		Where("`t_order_today`.`deleted_at` IS NULL").
		Where("`t_order_today`.`category_id` IN  ?  ", []int{1, 4}).
		Where("`t_order_today`.`store_id` IN  ?  ", resIds)
	var pushedOrdersId []int
	if isPushedShipper {
		pushedOrdersId = shipper.pushedOrderIds(admin)
		// if len(pushedOrdersId) > 0 {
		query.Where("`t_order_today`.`id` in (?)", pushedOrdersId)
		// }
	}
	if admin.TakeCashOrder == 0 {
		query.Where("t_order_today.pay_type = 5")
	}
	var newOrderCount int64
	var newOrders []models.OrderToday

	query.Count(&newOrderCount)
	var area models.Area
	tools.Db.Model(area).Where("id=? and state =1 ",admin.AdminAreaID).Scan(&area)
	if newOrderCount > 0 {
		query.Scan(&newOrders)
		// 过滤压单订单
		newOrders = shipper.AutoDispatchFilterOrder(admin.AdminAreaID,newOrders)
		//新订单数量根据 推送接口判断 开始
		newCount := int64(0)
		//满足 条件 (是推送区域的配送员 和 不在 该配送员收到的推送订单id集合里面 和 普通配送员)  的配送员看不到该订单 因为 还没开始推送,推送给该配送员后才显示
		for _, order := range newOrders {
			if (isPushedShipper && (!tools.InArray(order.ID, pushedOrdersId)) && admin.Type == 9) {
				tools.Logger.Info("isPushedShipper:",isPushedShipper,tools.InArray(order.ID, pushedOrdersId))
				//tools.Logger.Info("不显示这个订单，还没开始推送:配送员ID:",admin.ID," ,订单ID:",old.ID)
				continue
			}
			newCount++
		}
		newOrderCount = newCount
	}
	//新订单数量根据 推送接口判断 结束
	type ShipperOrderTabCount struct {
		Count int64 `gorm:"column:count"` // 城市编号
		State int64 `gorm:"column:state"` // 区域编号
	}

	var shipperOrderTabCount []ShipperOrderTabCount
	db.Select("count(t_take_order.id) AS count,t_take_order.state").
		Joins("left join t_order_today on t_take_order.order_id = t_order_today.id").
		Where("`t_take_order`.`created_at` > ? ", tools.Yesterday("Asia/Shanghai").ToDateTimeString()).
		Where("`t_take_order`.`admin_id` = ? ", admin.ID).
		Where("t_order_today.id is NOT NULL").
		Where("`t_take_order`.`state` IN ?", []int{1, 3, 2, 0}).
		Where("`t_take_order`.`deleted_at` IS NULL ").
		Table("t_take_order").
		Group("`t_take_order`.`state`").Find(&shipperOrderTabCount)
	var key = []string{"back", "myOrder", "failed", "succeed"}
	var data = map[string]int64{
		"new_list": newOrderCount,
		"back":     0,
		"myOrder":  0,
		"failed":   0,
		"succeed":  0,
	}
	for i := 0; i < len(shipperOrderTabCount); i++ {
		data[key[shipperOrderTabCount[i].State]] = shipperOrderTabCount[i].Count
	}
	var count int64
	db.Table("t_take_order").
		Select("count(1)").
		Joins("LEFT JOIN t_order_today ON t_take_order.order_id = t_order_today.id").
		Where("t_take_order.admin_id = ? AND t_take_order.created_at > ? AND t_order_today.id IS NOT NULL AND t_take_order.state = 1 AND t_order_today.state IN (3, 4, 5, 6, 7) AND t_take_order.deleted_at IS NULL", admin.ID, tools.Yesterday("Asia/Shanghai")).
		Count(&count)
	data["myOrder"] = count
	shipperCommentCacheKey := fmt.Sprintf("s_c_c_%d", admin.ID)
	commentCount := tools.Remember(c, shipperCommentCacheKey, 15*time.Minute, func() interface{} {
		if admin.LastCommentReaded.IsZero() {
			admin.LastCommentReaded = carbon.Now().SubMonths(12).Carbon2Time()
		}
		var commentCount int64
		db.Table("t_comment").
			Where("type = 1").
			Where("state = 1").
			Where("created_at > ?", admin.LastCommentReaded).
			Where("shipper_id = ?", admin.ID).
			Where("deleted_at is null").Count(&commentCount)
		return commentCount
	})
	commentCountInt, _ := strconv.Atoi(commentCount)
	data["comment_count"] = int64(commentCountInt) //新版去掉评论数量
	return data
}

// uint 代表小数位数，格式位 0.000001 如果是几位就指定为几位
func (shipper ShipperService) truncateNaive(f float64, unit float64) float64 {
	return math.Trunc(f/unit) * unit
}


// NewShipperService 初始化配送员service
//
//	@Description: 初始化配送员service
//	@param c
//	@return *MerchantService
func NewShipperService(c *gin.Context) *ShipperService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	shipperService := ShipperService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &shipperService
}

// RecommendList
// Notes: 配送员发展新客户统计
// User: Yakup
// DateTime: 2022/4/14 11:29
func (shipper ShipperService) RecommendList(admin models.Admin, startDate string, endDate string, month string, day string, limit int, page int) map[string]interface{} {
	if limit == 0 {
		limit = 30
	}
	if page == 0 {
		page = 1
	}
	db := tools.GetDB()
	var users []models.User
	query := db.Table("t_user").Select("t_user.name,b_city.name_" + shipper.language + " as city_name ,t_user.mobile,t_user.created_at")
	if startDate != "" && endDate != "" {
		startDate = carbon.Parse(startDate).ToDateTimeString()
		endDate = carbon.Parse(endDate).AddDays(1).ToDateTimeString()
	} else if month != "" {
		startDate = carbon.ParseByFormat(month, "Y-m").ToDateTimeString()
		endDate = carbon.ParseByFormat(month, "Y-m").AddMonths(1).ToDateTimeString()
	} else if day != "" {
		startDate = carbon.Parse(day).ToDateTimeString()
		endDate = carbon.Parse(day).AddDays(1).ToDateTimeString()
	} else {
		return nil
	}
	query.Where("t_user.created_at between ? and ?", startDate, endDate)
	query.Joins("left join b_city on b_city.id = t_user.city_id").Where("t_user.shipper_id", admin.ID)
	var count int64
	query.Count(&count)
	query.Limit(limit).Offset((page - 1) * limit).Find(&users) // 缺少分页

	return map[string]interface{}{
		"recommends": users,
		"count":      count,
	}
}

// SaveQrCode
//
//	@Description: 获取小程序推荐二维码，并保存在服务器（以免第二次使用时不用在请求微信公众平台）
//	@author: Captain
//	@Time: 2022-09-05 15:42:15
//	@receiver shipper ShipperService
//	@param shipperID int 配送员编号
//	@return string 二维码图片文件路径（相对路径）
func (shipper ShipperService) SaveQrCode(c *gin.Context, shipperID int) (bool, string) {
	db := tools.GetDB()
	rand.Seed(time.Now().UnixNano())
	randomStr := strconv.Itoa(rand.Intn(1000))
	fileName := strconv.FormatInt(time.Now().Unix(), 10) + randomStr + "_qr_recommend_" + strconv.Itoa(shipperID) + ".jpg"
	filePath := "upload/shipper_recommend/qrcode/" + fileName
	fullPath := configs.MyApp.UploadRootDir + filePath
	fileDir := filepath.Dir(fullPath)
	rs, err := tools.PathExists(fileDir)
	if err != nil {
		return false, err.Error()
	}
	if !rs {
		err := os.MkdirAll(fileDir, os.ModePerm)
		if err != nil {
			return false, err.Error()
		}
	}

	_, err1 := tools.GetWXMiniQrCode(c, shipperID, fullPath)
	if err1 != nil {
		return false, err1.Error()
	}
	db.Model(models.Admin{}).Where("id = ?", shipperID).Updates(map[string]interface{}{"recommend_qrcode": filePath})
	return true, filePath
}

// GetTip 配送员赞赏列表
func (shipper ShipperService) GetTip(limit int, page int, admin models.Admin) []models.ShipperTips {
	shipper_id := admin.ID
	db := tools.GetDB()
	var shipperTips []models.ShipperTips
	db.Table("t_shipper_tips").
		Select("t_shipper_tips.id,"+
			"t_shipper_tips.shipper_id,"+
			"t_shipper_tips.created_at,"+
			"t_shipper_tips.amount as amount,"+
			"t_user.name as user_name,"+
			"t_user.avatar as user_avatar").
		Joins("left join t_user on t_user.id = t_shipper_tips.user_id").
		Where("t_shipper_tips.shipper_id", shipper_id).
		Order("t_shipper_tips.created_at desc").
		Where("t_shipper_tips.state", 1).
		Limit(limit).Offset((page - 1) * limit).
		Find(&shipperTips)
	return shipperTips

}

// UpdateReadTime 更新评论读取时间
func (shipper ShipperService) UpdateReadTime(c *gin.Context, admin models.Admin) {
	shipperId := admin.ID
	shipperCommentCacheKey := fmt.Sprintf("s_c_c_%d", admin.ID)
	tools.RedisDel(c, shipperCommentCacheKey)
	dateTimeString := carbon.Now().Carbon2Time()
	db := tools.GetDB()
	db.Model(models.Admin{}).Where("id", shipperId).Updates(models.Admin{LastCommentReaded: dateTimeString})

}

// GetShipperCommentList
//
//	@Description: 获取客户对配送员的评论
//	@receiver shipper
//	@param shipperID
//	@param commentType
//	@param limit
//	@param page
//	@return map[string]interface{}
func (shipper ShipperService) GetShipperCommentList(shipperID int, commentType int, limit int, page int) map[string]interface{} {
	db := tools.GetDB()
	var count int64
	var comments []Comments.Comment
	//获取配送员评论
	query := db.Model(&Comments.Comment{}).
		Select("t_user.name as user_name,t_user.avatar as user_avatar,t_comment.shipper_id,t_comment.id,t_comment.star,t_comment.text,t_comment.is_satisfied,t_comment.is_anonymous,t_comment.created_at,t_comment.order_id").
		Joins("LEFT JOIN t_user ON t_comment.user_id = t_user.id").
		Where("t_comment.shipper_id", shipperID).
		Where("t_comment.state", 1).
		Where("t_comment.type", 1)
	//获取近一年的评论
	from := tools.GetFirstDateOfMonth(carbon.Now().SubMonths(12).Carbon2Time())
	query.Where("t_comment.created_at >?", from)
	if commentType == 2 {
		query.Where("t_comment.star = ?", 5)
	} else if commentType == 3 {
		query.Where("t_comment.star in ?", []int{3, 4})
	} else if commentType == 4 {
		query.Where("t_comment.star < ?", 3)
	}
	query.Count(&count)
	query.Preload("CommentReply", func(db *gorm.DB) *gorm.DB {
		return db.Select(
			"t_comment_reply.id,t_comment_reply.comment_id,t_comment_reply.text,t_comment_reply.type,t_comment_reply.admin_id,t_comment_reply.created_at,t_comment_reply.updated_at",
		)
	}).
		Preload("Income", func(db *gorm.DB) *gorm.DB {
			return db.Where("t_shipper_income.type in ?", []int{6, 7})
		}).
		Order("t_comment.created_at  DESC").
		Limit(limit).
		Offset((page - 1) * limit).
		Find(&comments)

	intCount := *(*int)(unsafe.Pointer(&count))

	return map[string]interface{}{
		"comments": comments,
		"page": map[string]int{
			"per_page":     limit,
			"current_page": page,
			"last_page":    int(math.Ceil(float64(intCount) / float64(limit))),
		},
		"count": intCount,
	}
}

// GetShipperInfo
//
//	@Description: 获取配送员信息
//	@receiver shipper
//	@param shippeID
//	@return models.Admin
func (shipper ShipperService) GetShipperInfo(shippeID int) models.Admin {
	db := tools.GetDB()
	var shipperInfo models.Admin
	shipperSql := db.Table("t_admin").Select("t_admin.id,t_admin.real_name,t_admin.mobile ,t_admin.avatar,b_area.name_" + shipper.language + " as area_name,t_admin.shipper_distance,t_admin.shipper_on_time_delivery_rate,t_admin.shipper_customer_rate,t_admin.shipper_delivery_avg_time")
	shipperSql.Joins("LEFT JOIN admin_areas ON t_admin.id = admin_areas.admin_id LEFT JOIN b_area ON admin_areas.area_id = b_area.id")
	shipperSql.Where("t_admin.id", shippeID).First(&shipperInfo)
	return shipperInfo
}

func (shipper ShipperService) GetShipperCommentsCountByType(c *gin.Context, shipperID int) []map[string]interface{} {
	var data = make([]map[string]interface{}, 3)
	var cacheKey = "shipper_comment_type_"
	cacheKey = cacheKey + fmt.Sprintf("%d", shipperID)
	//fmt.Println("key:", cacheKey)
	commentCountStr := tools.Remember(c, cacheKey, 5*time.Minute, func() interface{} {
		db := tools.GetDB()
		var count1 int64
		var count2 int64
		var count3 int64
		var typeCount = make([]map[string]interface{}, 4)
		//获取近一年的评论统计
		from := tools.GetFirstDateOfMonth(carbon.Now().SubMonths(12).Carbon2Time())
		db.Model(&Comments.Comment{}).Where("shipper_id=?", shipperID).
			Where("type =?", 1).
			Where("state =?", 1).
			Where("created_at >=?", from).
			Where("deleted_at is null").Count(&count1)
		typeCount[0] = make(map[string]interface{}, 4)
		typeCount[0]["name_ug"] = "ھەممىسى"
		typeCount[0]["name_zh"] = "全部"
		typeCount[0]["type"] = 1
		typeCount[0]["count"] = *(*int)(unsafe.Pointer(&count1))
		db.Model(&Comments.Comment{}).Where("shipper_id=?", shipperID).
			Where("type =?", 1).
			Where("state =?", 1).
			Where("star = ?", 5).
			Where("created_at >=?", from).
			Where("deleted_at is null").Count(&count2)
		typeCount[1] = make(map[string]interface{}, 4)
		typeCount[1]["name_ug"] = "ياخشى"
		typeCount[1]["name_zh"] = "好评"
		typeCount[1]["type"] = 2
		typeCount[1]["count"] = *(*int)(unsafe.Pointer(&count2))

		db.Model(&Comments.Comment{}).Where("shipper_id=?", shipperID).
			Where("type =?", 1).
			Where("state =?", 1).
			Where("star in ?", []int{3, 4}).
			Where("created_at >=?", from).
			Where("deleted_at is null").Count(&count2)
		typeCount[2] = make(map[string]interface{}, 4)
		typeCount[2]["name_ug"] = "ئوتتۇراھال"
		typeCount[2]["name_zh"] = "中评"
		typeCount[2]["type"] = 3
		typeCount[2]["count"] = *(*int)(unsafe.Pointer(&count2))

		db.Model(&Comments.Comment{}).Where("shipper_id=?", shipperID).
			Where("type =?", 1).
			Where("state =?", 1).
			Where("star <?", 3).
			Where("created_at >=?", from).
			Where("deleted_at is null").Count(&count3)
		typeCount[3] = make(map[string]interface{}, 4)
		typeCount[3]["name_ug"] = "ناچار"
		typeCount[3]["name_zh"] = "差评"
		typeCount[3]["type"] = 4
		typeCount[3]["count"] = *(*int)(unsafe.Pointer(&count3))
		return typeCount
	})
	//fmt.Println("value:", commentCountStr)
	_ = json.Unmarshal([]byte(commentCountStr), &data)
	return data
}

// RebindWechat
//
//	@Time 2022-09-03 15:44:10
//	<AUTHOR>
//	@Description: 配送员绑定微信号
//	@receiver ShipperService
//	@param c
//	@param admin
//	@return bool
//	@return string
func (ShipperService) RebindWechat(c *gin.Context, admin models.Admin) (bool, string) {
	mobile := admin.Mobile
	db := tools.Db
	user := models.User{}
	result := db.Model(models.User{}).Where("mobile", mobile).Find(&user)
	if user.ID == 0 || user.OpenID == "" {
		return false,admin.Mobile
	}
	if result.RowsAffected == 1 {
		db.Model(admin).Update("openid", user.OpenID)
		return true, user.Name
	} else {
		return false, admin.Mobile
	}
}

// AdminChangePassword
//
//	@Time 2022-09-03 15:43:47
//	<AUTHOR>
//	@Description: 配送员更改密码
//	@receiver shipper
//	@param admin
//	@param password
//	@param oldPassword
//	@return bool
func (shipper ShipperService) AdminChangePassword(admin models.Admin, password string, oldPassword string) bool {
	result := models.Admin{}
	tools.Db.Model(admin).Select("password").Where("id", admin.ID).First(&result)
	isTrue := checkPassword(result.Password, oldPassword)
	if isTrue {
		//密码正确
		data := []byte(password)                       //切片
		hashPassword := md5.Sum(data)                  //[16]byte
		md5Password := fmt.Sprintf("%x", hashPassword) //将[]byte转成16进制 string
		tools.Db.Model(models.Admin{}).Where("id", admin.ID).Update("password", md5Password)
		return true
	} else {
		//密码错误
		return false
	}
}

// checkPassword
// @Time 2022-09-03 15:41:21
// <AUTHOR>
// @Description: 检测密码是否正确
// @param currentPassword
// @param oldPassword
// @return bool
func checkPassword(currentPassword string, oldPassword string) bool {
	data := []byte(oldPassword)                    //切片
	hashPassword := md5.Sum(data)                  //[16]byte
	md5Password := fmt.Sprintf("%x", hashPassword) //将[]byte转成16进制 string
	if md5Password == currentPassword {
		return true
	} else {
		return false
	}

}

// TakeOrder
//
//	@Description: 配送员抢订单
//	@author: Captain
//	@Time: 2022-09-08 11:24:30
//	@receiver shipper ShipperService
//	@param c *gin.Context
//	@param admin models.Admin
//	@param orderID int
//	@return bool	抢单成功返回true,否则返回false
//	@return string 抢单成功返回成功,否则返回错误提示
func (shipper ShipperService) TakeOrder(c *gin.Context, admin models.Admin, orderID int) (bool, string) {
	return false,shipper.langUtil.T("shipper_app_is_old")
	tools.Logger.Info("配送员抢单，配送员编号：", admin.ID, "订单编号：", orderID)
	shipperInfo := models.Admin{}
	var shipperID int
	var _ error
	if admin.Type == 8 {
		shipperID, _ = strconv.Atoi(c.PostForm("order_id"))
		tools.Db.Where("id =?", shipperID).First(&shipperInfo)
		admin = shipperInfo
	}

	if admin.State == 0 {
		//log.Println("配送员在休息状态，不能抢单，配送员编号：", admin.ID)
		return false, shipper.langUtil.T("shopper_working_off")
	}
	db := tools.Db
	//能配送送的订单数是否大于实际订单数量
	var takedOrdes int64

	db.Model(&models.OrderToday{}).Where("created_at >= ? and shipper_id =? and state>=3 and state<7", tools.Today("Asia/Shanghai").ToDateTimeString("Asia/Shanghai"), admin.ID).Count(&takedOrdes)

	if int(takedOrdes) >= admin.GrabOrderCount {
		return false, fmt.Sprintf(shipper.langUtil.T("grab_order_count_exceeded"), admin.GrabOrderCount)
	}

	//获取分配该配送员的所有餐厅编号
	ResIDs := admin.GetRestaurantIds(c, false)
	//设置数据库事务所的隔离级别
	opt := &sql.TxOptions{
		Isolation: sql.LevelSerializable,
	}
	//添加redis 锁，防止订单被多人抢
	lockKey := "order_lock_" + strconv.Itoa(orderID)
	isNoLock, err := tools.GetRedisHelper().SetNX(c, lockKey, "1", 2*time.Second).Result()
	if (isNoLock == false) || (err != nil) {
		log.Println("订单已被抢 " + strconv.Itoa(orderID))
		return false, shipper.langUtil.T("already_taked")
	}

	tx := tools.Db.Begin(opt)
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			tools.Logger.Errorf("shipper_id=%d ,order_id=%d 查询订单时出现错误:%v", admin.ID, orderID, err)
		}
	}()
	Order := models.OrderToday{}
	err = tx.Set("gorm:query_option", "FOR UPDATE").Model(&models.OrderToday{}).Where("id = ?", orderID).
		Where("state IN ?", []int{4, 5}).Where("taked = ?", 0).
		Where("store_id in ?", ResIDs).
		First(&Order).Error
	if err != nil {
		tx.Rollback()
		log.Println("查询订单时出现错误:", err.Error())
		return false, shipper.langUtil.T("already_taked")
	}

	if Order.CategoryID != 1 && Order.CategoryID != 4 {
		tx.Rollback()
		return false, shipper.langUtil.T("self_taken_order")
	}
	err = tx.Model(&Order).Updates(map[string]interface{}{"DeliveryTakedTime": carbon.Now().Carbon2Time(), "Taked": 1, "ShipperID": admin.ID}).Error
	if err != nil {
		tx.Rollback()
		log.Println("订单抢单时出现错误:", err.Error())
		return false, shipper.langUtil.T("error_happend")
	}
	tools.Logger.Info("配送员抢单成功，配送员编号：", admin.ID, "订单编号：", orderID)
	tx.Commit() //数据库事务所结束了
	//开始写入t_take_order表

	TakeOrder := models.TakeOrder{}
	db.Model(&models.TakeOrder{}).Where("order_id =?", orderID).Where("created_at > ?", tools.Yesterday("Asia/Shanghai")).Where("admin_id =?", admin.ID).Where("state =?", 0).First(&TakeOrder)
	if TakeOrder.OrderID == 0 {
		NewTakeOrder := models.TakeOrder{}
		NewTakeOrder.OrderID = orderID
		NewTakeOrder.AdminID = admin.ID
		NewTakeOrder.State = 1
		NewTakeOrder.OrderPrice = Order.Price + Order.Shipment + uint(Order.LunchBoxFee)
		if Order.ConsumeType == 0 {
			NewTakeOrder.ConsumeType = 0
			NewTakeOrder.IsClearing = 0
		} else if Order.ConsumeType == 3 {
			NewTakeOrder.ConsumeType = 3
			NewTakeOrder.IsClearing = 0
		} else {
			NewTakeOrder.ConsumeType = 1
			NewTakeOrder.IsClearing = 1
		}
		err = db.Create(&NewTakeOrder).Error
		if err != nil {
			log.Printf("错误: 抢单信息写入t_take_order时出现错误: %s", err.Error())
			return false, shipper.langUtil.T("error_happend")
		}
	} else {
		Updates := map[string]interface{}{
			"ReasonID": nil,
			"State":    1,
		}
		Updates["OrderPrice"] = Order.Price + Order.Shipment + uint(Order.LunchBoxFee)
		if Order.ConsumeType == 0 {
			Updates["ConsumeType"] = 0
			Updates["IsClearing"] = 0
		} else if Order.ConsumeType == 3 {
			Updates["ConsumeType"] = 3
			Updates["IsClearing"] = 0
		} else {
			Updates["ConsumeType"] = 1
			Updates["IsClearing"] = 1
		}
		err = db.Model(&TakeOrder).Updates(Updates).Error
		//如果写入更新失败，测取消抢单操作（t_order_today数据表的更新）
		if err != nil {
			log.Printf("错误: 抢单信息::%s", err.Error())
			err = db.Model(&models.OrderToday{}).Where("id = ?", orderID).Updates(map[string]interface{}{"Taked": 0, "ShipperID": nil}).Error
			log.Printf("错误:取消抢单操作::%s", err.Error())
			return false, shipper.langUtil.T("error_happend")
		}
	}
	return true, shipper.langUtil.T("msg")
}

// GetListByStreet
//
//	@Time 2022-09-05 16:22:18
//	<AUTHOR>
//	@Description: 按街道获取订单列表
//	@receiver shipper ShipperService
//	@param admin
func (shipper ShipperService) GetListByStreet(c *gin.Context, admin models.Admin) []shipperResources.ListByStreet {
	now := carbon.Now().ToDateString()
	db := tools.GetDB()
	var restaurant []models.Restaurant
	db.Model(admin).Association("Restaurants").Find(&restaurant)
	restaurantIds := []int{}
	for _, value := range restaurant {
		restaurantIds = append(restaurantIds, value.ID)
	}
	var orderToday models.OrderToday
	var listByStreet []shipperResources.ListByStreet
	query := db.Model(orderToday).
		Select("count(t_order_today.id) as order_count,b_street.id as street_id,b_street.name_"+shipper.language+" as street_name").
		Joins("left join t_restaurant on t_order_today.store_id = t_restaurant.id").
		Joins("left join b_street on  b_street.id = t_restaurant.street_id").
		Where("t_order_today.store_id IN ?", restaurantIds).
		Where("t_order_today.state IN ?", []int{2, 3, 4, 5}).
		Where("t_order_today.taked", 0).
		Where("t_order_today.category_id", []int{1, 4})
	if shipper.isPushedShipper(c, admin) {
		pushedOrders := shipper.pushedOrderIds(admin)
		if len(pushedOrders) > 0 {
			query.Where("`t_order_today`.`id` in (?)", pushedOrders)
		}
	}
	query.Group("b_street.id").Order("order_count desc").
		Where("t_order_today.created_at > ?", now).
		Scan(&listByStreet)
	return listByStreet

}

// GetOrderDetail
//
//	@Time 2022-09-07 19:30:22
//	<AUTHOR>
//	@Description: 订单详情
//	@receiver shipper ShipperService
//	@param orderId
//	@return interface{}
func (shipper ShipperService) GetOrderDetail(orderId string) (bool, models.OrderToday) {
	db := tools.GetDB()
	tableName := "t_order_today"
	type OrderShipper struct {
		Id        int
		ShipperId int
		StoreId   int
	}
	var orderShipper OrderShipper

	db.Model(models.OrderToday{}).Select("id,shipper_id,store_id").Where("id = ?", orderId).Scan(&orderShipper)
	modelClass := db.Model(models.OrderToday{})
	if orderShipper.Id == 0 {
		tableName = "t_order"
		modelClass = db.Model(models.Order{})
		db.Model(models.Order{}).Select("id,shipper_id").Where("id = ?", orderId).Scan(&orderShipper)
	}
	var orderTodayAndOrder models.OrderToday
	modelClass.
		Select("id", "store_id", "timezone", "order_id", "state", "building_id", "booking_time", "name", "mobile",
			"order_address", "consume_type", "price", "shipment", "lunch_box_fee", "delivery_end_time", "pay_type",
			"description", "created_at", "order_type", "cash_clear_state", "cash_clear_time", "pay_time", "cash_clear_channel", "distance", "serial_number","order_price","actual_paid").
		Preload("Restaurant", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "name_ug", "name_zh", "address_ug", "address_zh", "tel", "lat", "lng", "logo","tel4","tel5")
		}).
		Preload("OrderDetail.RestaurantFoods", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "name_ug", "name_zh", "price","image","description_ug","description_zh")
		}).
		Preload("OrderDetail.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取美食已选规格数据
		Preload("OrderDetail.RestaurantFoods.ComboFoodItems.RestaurantFood"). // 获取套餐中的美食数据
		Preload("OrderDetail.RestaurantFoods.ComboFoodItems.SelectedSpec.FoodSpecOptions.FoodSpecType"). // 获取套餐中的已选规格数据
		Preload("LunchBox", func(db *gorm.DB) *gorm.DB {
			return db.Select(`	
	0 AS food_id,
	b_lunch_box.unit_price AS original_price,
	b_lunch_box.unit_price AS price,
	sum( t_order_detail.lunch_box_count ) AS count,
	b_lunch_box.name_` + shipper.language + ` AS name,
	t_order_detail.order_id `).Joins("INNER JOIN b_lunch_box ON t_order_detail.lunch_box_id = b_lunch_box.id ").
				Where("t_order_detail.deleted_at is null").
				Group("t_order_detail.order_id,t_order_detail.lunch_box_id")
		}).
		Preload("PayTypes", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "name_ug", "name_zh")
		}).
		Preload("MarketingList", func(db *gorm.DB) *gorm.DB {
			return db.Select(`
					type ,
		            name_` + shipper.language + ` AS name,
					step_reduce ,id,order_id`)
		}).
		Preload("Coupon", func(db *gorm.DB) *gorm.DB {
			return db.Select(`
			t_coupon.id,
			t_coupon_log.order_id,
			t_coupon.name_` + shipper.language + ` AS name,
			t_coupon.price
			`).Joins("left join t_coupon on t_coupon.id = t_coupon_log.coupon_id")
		}).
		Preload("AddressView").
		Preload("Comments", func(db *gorm.DB) *gorm.DB {
			return db.Where("type = ? and state = ?", 1, 1)
		}).
		Preload("PushDetail", func(db *gorm.DB) *gorm.DB {
			return db.Where("t_shipper_order_push_detail.shipper_id = ?", orderShipper.ShipperId)
		}).
		Preload("ShipperIncome", func(db *gorm.DB) *gorm.DB {
			return db.Where("t_shipper_income.shipper_id = ?", orderShipper.ShipperId)
		}).
		Preload("OrderExtend").
		Preload("OrderDelay",func(db *gorm.DB) *gorm.DB {
			return db.Where("t_order_delayed.shipper_id = ?", orderShipper.ShipperId)
		}).
		Preload("User").
		Preload("OrderStateLog").
		Preload("TakeOrder","t_take_order.admin_id=?",orderShipper.ShipperId).
		Preload("RestaurantBuilding", func(db *gorm.DB) *gorm.DB {
			return db.Where("t_restaurant_building.restaurant_id = ?", orderShipper.StoreId)
		}).
		Where("id =?", orderId).Select(tableName + ".*,\"" + tableName + "\" as order_origin").
		Find(&orderTodayAndOrder)
	if orderTodayAndOrder.OrderID == "" {
		//订单未找到
		return false, orderTodayAndOrder
	}

	return true, orderTodayAndOrder
}

// OrderStatistics
//
//	@Time 2022-09-11 00:55:57
//	<AUTHOR>
//	@Description: 配送员统计
//	@receiver shipper ShipperService
//	@param admin
//	@param startDate
//	@param endDate
//	@param month
//	@param day
//	@return interface{}
func (shipper ShipperService) OrderStatistics(admin models.Admin, startDate string, endDate string, month string, day string) []shipperResources.Statistics {
	db := tools.GetDB()
	var whereTime string
	fmt.Println(startDate, endDate, month, day)
	if startDate != "" && endDate != "" {
		fmt.Println("开始时间结束时间", startDate, endDate)
		start := carbon.Parse(startDate).ToDateTimeString()
		end := carbon.Parse(endDate).AddDay().ToDateTimeString()
		whereTime = fmt.Sprintf("created_at between '" + start + "' and '" + end + "'")
	} else if month != "" {
		start := carbon.ParseByFormat(month, "Y-m").ToDateTimeString()
		end := carbon.ParseByFormat(month, "Y-m").AddMonth().ToDateTimeString()
		whereTime = fmt.Sprintf("created_at between '" + start + "' and '" + end + "'")
	} else if day != "" {
		fmt.Println("day=", day)
		start := carbon.Parse(day).ToDateTimeString()
		end := carbon.Parse(day).AddDay().ToDateTimeString()
		whereTime = fmt.Sprintf("created_at between '" + start + "' and '" + end + "'")
	}

	orderToday := db.Model(models.OrderToday{}).
		Select("id ,state,price,shipment,lunch_box_fee,booking_time,order_address,timezone,created_at,building_id,store_id").
		Where(whereTime).
		Where("shipper_id", admin.ID)
	//fmt.Println(todayOrders)

	hisOrders := db.Model(models.Order{}).
		Select("id", "state", "price", "shipment", "lunch_box_fee", "booking_time", "order_address", "timezone", "created_at", "building_id", "store_id").
		Where(whereTime).
		Where("shipper_id", admin.ID)

	statistics := []shipperResources.Statistics{}
	union := db.Raw("(?) union (?)", hisOrders, orderToday)
	db.Model(models.TakeOrder{}).
		Joins("left join (?) all_order  on t_take_order.order_id = all_order.id", union).
		Select("t_take_order.state as state, t_take_order.consume_type, count(t_take_order.id) as order_count,round(sum(t_take_order.order_price)/100,2) as total_price,round(sum(all_order.price+all_order.shipment+all_order.lunch_box_fee)/100,2) as total_lunchbox_price").
		Where("t_take_order.admin_id", admin.ID).
		Where("t_take_order."+whereTime).
		Where("t_take_order.state In ? ", []int{0, 2, 3}).
		Group("t_take_order.state").
		Group("t_take_order.consume_type").
		Order("t_take_order.state DESC").
		Scan(&statistics)
	return statistics
}

// ChangeOrderState
//
//	@Description: 配送员改变订单状态
//	@author: Captain
//	@Time: 2022-09-12 13:50:54
//	@receiver shipper ShipperService
//	@param c *gin.Context
//	@param adminID int 配送员编号
//	@param orderID int	订单编号
//	@param adminMobile string	配送员手机号
//	@param lat float64 配送员经纬度
//	@param lng float64 配送员经纬度
//	@return bool 操作成功时返回true，则返回false
//	@return string 操作成功或失败提示
func (shipper ShipperService) ChangeOrderState(c *gin.Context, adminID int, orderID int, adminMobile string, lat float64, lng float64) (bool, string) {
	db := tools.Db
	takeOrder := models.TakeOrder{}
	orderTodayObj := models.OrderToday{}
	//if lat == 0 || lng == 0 {
	//	log.Printf("错误: 配送员定位失败，lat=【%f】     lng=【%f】", lat, lng)
	//	return false, shipper.langUtil.T("shipper_failed_to_locate_location")
	//}
	err := db.Model(&models.TakeOrder{}).Where("created_at >=?", tools.Yesterday("Asia/Shanghai").ToDateTimeString()).
		Where("order_id =?", orderID).Where("admin_id =?", adminID).Where("state =?", 1).First(&takeOrder).Error
	if err != nil {
		//打印 adminID orderId, lat ,lng
		tools.Logger.Errorf("订单状态更改时出错order_not_found: %d  %d  %f  %f %s", adminID, orderID, lat, lng, err.Error())
		return false, shipper.langUtil.T("order_not_found")
	}
	takeOrderState := takeOrder.State
	err = db.Model(&models.OrderToday{}).Preload("OrderExtend").Preload("Restaurant").Where("id =?", orderID).First(&orderTodayObj).Error
	if err != nil {
		tools.Logger.Errorf("订单状态更改时出错order_not_found: %d  %d  %f  %f %s", adminID, orderID, lat, lng, err.Error())
		return false, shipper.langUtil.T("order_not_found")
	}
	// 没有支付的现金订单不能完成
	if(orderTodayObj.State == 6 && orderTodayObj.PayType != 5 && orderTodayObj.ConsumeType != 1 && orderTodayObj.CashClearState == 0){
		return false, shipper.langUtil.T("cash_order_not_pay")
	}
	maxDistance := 10000.0 //10公里
	var resDistance float64
	var leftDistance float64
	if orderTodayObj.Restaurant.ID > 0 {
		resLat := orderTodayObj.Restaurant.Lat
		resLng := orderTodayObj.Restaurant.Lng
		//配送员和店铺的距离
		resDistance = tools.CalculateLatitudeLongitudeDistance(lng, lat, resLng, resLat)
		leftDistance = resDistance - tools.ToFloat64(configs.MyApp.ShipperArriveAtShopDistance) //剩余距离
	}
	flag := true      //是否查询到店状态   临时跳过 到店状态查询
	newClient := true //可能出现的旧客户端中途升级新客户端的情况
	//旧客户端直接点击 接口调用 配送完成的情况
	if (orderTodayObj.State == 4 || orderTodayObj.State == 5 || orderTodayObj.State == 6) && (orderTodayObj.OrderExtend == nil || (orderTodayObj.OrderExtend.ShipperArrivedShopAt == nil)) {
		newClient = false
	}
	isCheckShop := true
	//不检查到店 状态的 店铺
	if tools.InArray(orderTodayObj.StoreID, configs.MyApp.ShipperNoCheckArriveShops) {
		isCheckShop = false
	}
	if isCheckShop && newClient && flag && orderTodayObj.OrderExtend == nil {
		tools.Logger.Errorf("配送员 orderExtend 没有数据 : %d  %d  %f  %f.1 %f.1", adminID, orderID, lat, lng, resDistance, leftDistance)
		if leftDistance > 0 {
			if resDistance > maxDistance { //大于10公里
				return false, shipper.langUtil.T("shipper_app_location_disabled")
			}
			return false, fmt.Sprintf(shipper.langUtil.T("did_not_arrive_shop"), resDistance)
		}
		//自动完成 到店 操作
		ar, _ := shipper.ShipperArrivedAtShop(c, adminID, orderID, lat, lng, "come")
		if !ar {
			if resDistance > maxDistance { //大于10公里
				return false, shipper.langUtil.T("shipper_app_location_disabled")
			}
			return false, fmt.Sprintf(shipper.langUtil.T("did_not_arrive_shop"), resDistance)
		}

	}
	if isCheckShop && newClient && flag && orderTodayObj.OrderExtend.ShipperArrivedShopAt == nil {
		tools.Logger.Errorf("配送员 没有数据到达店铺 : %d  %d  %f  %f %f %f", adminID, orderID, lat, lng, resDistance, leftDistance)
		if leftDistance > 0 {
			if resDistance > maxDistance { //大于10公里
				return false, shipper.langUtil.T("shipper_app_location_disabled")
			}
			return false, fmt.Sprintf(shipper.langUtil.T("did_not_arrive_shop"), resDistance)
		}
		//自动完成 到店 操作
		ar, _ := shipper.ShipperArrivedAtShop(c, adminID, orderID, lat, lng, "come")
		if !ar {
			if resDistance > maxDistance { //大于10公里
				return false, shipper.langUtil.T("shipper_app_location_disabled")
			}
			return false, fmt.Sprintf(shipper.langUtil.T("did_not_arrive_shop"), resDistance)
		}
	}
	if isCheckShop && newClient && flag && orderTodayObj.OrderExtend.ShipperTakeFoodAt == nil {
		return false, shipper.langUtil.T("ShipperNotTakeFood") //没有取餐
	}
	var orderNewStateID int
	if orderTodayObj.State == 4 || orderTodayObj.State == 5 {
		//fmt.Println("抢过的清单改成配送中状态")
		orderNewStateID = 6
		orderTodayObj.DeliveryStartTime = carbon.Now("Asia/Shanghai").Carbon2Time()
	} else if orderTodayObj.State == 6 {
		if (lat == 0 || lng == 0) && orderTodayObj.ShipperCompleteGrant == 0 {
			tools.Logger.Errorf("配送员定位失败: %d  %d  %f  %f", adminID, orderID, lat, lng)
			return true, shipper.langUtil.T("shipper_failed_to_locate_location")
		}
		//fmt.Println("配送中状态的订单改成配送完毕")
		var building = models.Building{}
		err = db.Model(&models.Building{}).Where("id = ?", orderTodayObj.BuildingID).First(&building).Error
		if err == nil {
			//distance := tools.CalculateDistance(lat, lng, building.Lat, building.Lng)
			distance := tools.CalculateLatitudeLongitudeDistance(lng, lat, building.Lng, building.Lat)
			var orderFinishDistance float64
			area := models.Area{}
			db.Model(&models.Area{}).Select("order_finish_distance").Where("id = ?", orderTodayObj.AreaID).First(&area)
			orderFinishDistance = area.OrderFinishDistance
			if distance > orderFinishDistance {
				tools.Logger.Errorf("配送员跟客户距离: %d  %d  %f  %f %f", adminID, orderID, lat, lng, distance)
			}
			if distance > orderFinishDistance && orderTodayObj.ShipperCompleteGrant == 0 {
				tools.Logger.Errorf("没有给完成权限: %d  %d  %f  %f %f", adminID, orderID, lat, lng, distance)
				return false, fmt.Sprintf(shipper.langUtil.T("did_not_arrive_destination"), distance)
			}
		} else {
			tools.Logger.Errorf("获取楼栋信息失败: %d  %d  %f  %f %s", adminID, orderID, lat, lng, err.Error())
		}
		orderNewStateID = 7
		orderTodayObj.DeliveryEndTime = carbon.Now("Asia/Shanghai").Carbon2Time()
		tools.Logger.Info("订单编号:%s:::::完成时间:%s", orderTodayObj.OrderID, orderTodayObj.DeliveryEndTime)
		takeOrderState = 3 //表示订单完成

	} else {
		tools.Logger.Errorf("订单状态更改时出错order_not_found: %d  %d  %f  %f", adminID, orderID, lat, lng)
		return false, shipper.langUtil.T("change_state_fail")
	}
	takeOrder.State = takeOrderState
	//保存t_takeOrder
	tx := tools.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	err = tx.Table("t_take_order").Where("id = ?", takeOrder.ID).Updates(map[string]interface{}{"state": takeOrderState}).Error
	if err != nil {
		tools.Logger.Errorf("订单状态更改时出错order_not_found: %d  %d  %f  %f %s", adminID, orderID, lat, lng, err.Error())
		tx.Rollback()
		return false, shipper.langUtil.T("change_state_fail")
	}
	orderTodayObj.State = orderNewStateID
	//保存t_order_today
	if orderNewStateID == 6 {
		err = tx.Table("t_order_today").Where("id = ?", orderID).Updates(map[string]interface{}{"state": orderNewStateID, "delivery_start_time": orderTodayObj.DeliveryStartTime}).Error
	} else if orderNewStateID == 7 {
		err = tx.Table("t_order_today").Where("id = ?", orderID).Updates(map[string]interface{}{"state": orderNewStateID, "delivery_end_time": orderTodayObj.DeliveryEndTime}).Error
		//迟到订单里面标记
		mi := carbon.Parse(orderTodayObj.BookingTime, configs.AsiaShanghai).DiffInMinutes(carbon.Now(configs.AsiaShanghai))
		if mi > 0 { //迟到了
			tx.Create(&models.LateOrders{
				CityID:        orderTodayObj.CityID,
				AreaID:        orderTodayObj.AreaID,
				ShipperID:     adminID,
				Shipment:      int(orderTodayObj.Shipment),
				OrderID:       orderID,
				BookingTime:   orderTodayObj.BookingTime,
				DeliveredTime: carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
				LateMinute:    tools.ToInt(mi),
			})
		}
	}
	if err != nil {
		tools.Logger.Errorf("订单状态更改时出错order_not_found: %d  %d  %f  %f %s", adminID, orderID, lat, lng, err.Error())
		tx.Rollback()
		return false, shipper.langUtil.T("change_state_fail")
	}
	//写入t_order_State_Log
	orderStateLog := models.OrderStateLog{}
	orderStateLog.OrderID = orderTodayObj.ID
	orderStateLog.OrderStateID = orderTodayObj.State
	err = tx.Create(&orderStateLog).Error
	if err != nil {
		tools.Logger.Errorf("错误：change-order-state接口写入t_order_state_log时出现错误: %d  %d  %f  %f %s", adminID, orderID, lat, lng, err.Error())
		tx.Rollback()
		return false, shipper.langUtil.T("change_state_fail")
	}
	tx.Commit()
	if orderNewStateID == 7 && orderTodayObj.UserID >0{
		tools.HttpGetJson(fmt.Sprintf("http://%s/ug/shipper-introduce-tip-update?user_id=%d",configs.MyApp.Port,orderTodayObj.UserID),time.Second*3)

		//抽奖用户判断 
		shipper.LotteryOrderProcess(orderTodayObj)		
	}
	if orderNewStateID == 7 { 
		//更新 加价日志 
		shipper.BaseService.PriceFoodLogUpdate(orderID,3) //1:未支付，2：已支付，3:完成，4:退单
	}
	
	return true, shipper.langUtil.T("msg")

}

// BackOrder
//
//	@Description: 配送员退单
//	@author: Captain
//	@Time: 2022-09-13 12:51:30
//	@receiver shipper ShipperService
//	@param c *gin.Context
//	@param adminID int	配送员编号
//	@param orderID int	订单编号
//	@param reasonID int	退单原因编号
//	@param adminBackOrderTimeLimit int 配送员能退单时间期限
//	@return bool 操作成功返回true，否则返回false
//	@return string 操作成功返回空字符串，否则返回错误提示
func (shipper ShipperService) BackOrder(c *gin.Context, adminID int, orderID int, reasonID int, adminBackOrderTimeLimit int) (bool, string) {
	
	key1 := "grab_order_" + tools.ToString(adminID) + "_" + tools.ToString(orderID)
	sec1 := configs.MyApp.ShipperGrabOrderLimit                //请求间隔 秒
	letGo1 := 1                                                //放行的请求数量
	requestCount1 := 10                                        //请求数量
	if !tools.AccessLimit(c, key1, requestCount1, letGo1, sec1) { //每x秒的请求只能通过x次
		return false, fmt.Sprintf(shipper.langUtil.T("retry_after_second"), sec1) // "retry_after_10_second"
	}

	//限流  每x秒内发送的请求只会通过 x 个 ，其他的会提示 xx秒后重试
	key := "return_order_" + tools.ToString(adminID) + "_" + tools.ToString(orderID)
	sec := 5                //请求间隔 秒
	letGo := 1                                                //放行的请求数量
	requestCount := 10                                        //请求数量
	if !tools.AccessLimit(c, key, requestCount, letGo, sec) { //每x秒的请求只能通过x次
		return false, fmt.Sprintf(shipper.langUtil.T("retry_after_second"), sec) // "retry_after_10_second"
	}
	tx := tools.Db.Begin()
	defer func() {
		tools.RedisDel(c,key1)
		tools.RedisDel(c,key)
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	orderTodayObj := models.OrderToday{}
	err := tx.Model(&models.OrderToday{}).Where("id =?", orderID).First(&orderTodayObj).Error
	if err != nil {
		tx.Rollback()
		log.Printf("错误：配送员退但是出现错误：%s\r\n", err.Error())
		return false, shipper.langUtil.T("order_not_found")
	}
	if orderTodayObj.State == constants.ORDER_SENDING {
		tx.Rollback()
		tools.Logger.Error("订单配送中不能退单--order_id=",orderID)
		return false, shipper.langUtil.T("package_delivery_in_progress_can_not_return_order")
	}
	//判断订单是否已过能退单时间期限
	orderBackTimeLimit := carbon.Now().AddMinutes(adminBackOrderTimeLimit)
	bookingTime := carbon.Parse(orderTodayObj.BookingTime)
	diffMinutes := orderBackTimeLimit.DiffInMinutes(bookingTime)
	if diffMinutes < 0 {
		log.Println("不能退单", diffMinutes)
		tx.Rollback()
		return false, shipper.langUtil.T("overstep_time")
	}
	//判断订单是否已经退单
	takeOrderObj := models.TakeOrder{}
	err = tx.Model(&models.TakeOrder{}).Where("order_id=?", orderID).
		Where("created_at >= ?", tools.Yesterday("Asia/Shanghai").ToDateTimeString()).
		Where("admin_id=?", adminID).Where("state=?", 1).First(&takeOrderObj).Error
	if err != nil || takeOrderObj.OrderID < 1 {
		tx.Rollback()
		return false, shipper.langUtil.T("already_backed")
	}
	fmt.Println("taked:", orderTodayObj.Taked)
	if orderTodayObj.Taked == 0 {
		tx.Rollback()
		return false, shipper.langUtil.T("already_backed")
	}
	//更新t_take_order
	err = tx.Table("t_take_order").Where("id = ?", takeOrderObj.ID).Updates(map[string]interface{}{
		"state":      0,
		"reason_id":  reasonID,
		"updated_at": carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s"),
	}).Error
	if err != nil {
		log.Printf("配送员退单时出错：更新t_take_order时出现错误:%s", err.Error())
		tx.Rollback()
		return false, shipper.langUtil.T("back_order_failed")
	}
	//更新t_order_today
	err = tx.Table("t_order_today").Where("id = ?", orderID).Updates(map[string]interface{}{
		"taked":      0,
		"shipper_id": nil,
		"updated_at": carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s"),
	}).Error
	if err != nil {
		log.Printf("配送员退单时出错：更新t_order_today时出现错误:%s", err.Error())
		tx.Rollback()
		return false, shipper.langUtil.T("change_state_fail")
	}
	//更新t_order_extend
	err = tx.Table("t_order_extend").Where("order_id = ?", orderID).Updates(map[string]interface{}{
		"shipper_order_state":     0,
		"shipper_arrived_shop_at": nil,
		"shipper_take_food_at":    nil,
	}).Error
	if err != nil {
		log.Printf("配送员退单时出错：更新t_order_extend时出现错误:%s", err.Error())
		tx.Rollback()
		return false, shipper.langUtil.T("change_state_fail")
	}
	tx.Commit()
	return true, ""
}

// LoginCheck
//
//	@Description: 验证配送员账号密码
//	@author: Alimjan
//	@Time: 2022-10-12 17:56:45
//	@receiver shipper ShipperService
//	@param userName string 用户名
//	@param password string 密码
//	@param client_id string oauth 参数
//	@param client_secret string oauth 参数
//	@param grant_type string oauth 参数
//	@return bool True 则说明验证通过
//	@return models.Admin 验证的用户
//	@return string 错误信息
func (shipper ShipperService) LoginCheck(userName string, password string, client_id string, client_secret string, grant_type string) (bool, models.Admin, string) {
	db := tools.Db
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Printf("登录日志 error: %s\n", err)
			}
		}()
		//tools.Log("配送员登录参数 用户名:"+userName+",密码:"+password)
	}()
	var admin models.Admin
	db.Model(admin).Where("name = ?", userName).
		Where("type in (8,9)").Where("state = 1").
		Where("deleted_at IS NULL").First(&admin)
	if admin.ID == 0 {
		if admin.ID == 0 {
			db.Model(admin).Where("mobile = ?", userName).
				Where("type in (8,9)").Where("state = 1").
				Where("deleted_at IS NULL").First(&admin)
			if admin.ID == 0 {
				return false, admin, "admin_is_not_active"
			}
		}
	}
	attemptedHashValue := tools.PasswordToHash(password)
	if attemptedHashValue == admin.Password {
		return true, admin, ""
	}
	return false, admin, "admin_password_error"

}

// GenerateToken
//
//	@Description: 生成token 并把token 存储
//	@author: Alimjan
//	@Time: 2022-09-23 17:16:29
//	@receiver shipper ShipperService
//	@param admin models.Admin
//	@param clientId string
//	@param searialNumber string
//	@return map[string]interface{}
func (shipper ShipperService) GenerateToken(admin models.Admin, clientId string, searialNumber string) map[string]interface{} {
	accessToken := tools.RandStr(40)
	var db = tools.Db
	var oldSessionIds []int
	db.Table("oauth_sessions").
		Where("oauth_sessions.owner_id = ? ", admin.ID).
		Where("oauth_sessions.owner_type = 'shipper' ").Pluck("oauth_sessions.id", &oldSessionIds)

	db.Table("oauth_access_tokens").
		Where("oauth_access_tokens.session_id in ? ", oldSessionIds).
		Delete(models.OauthAccessTokens{})

	db.Table("oauth_sessions").
		Where("oauth_sessions.owner_id = ? ", admin.ID).
		Where("oauth_sessions.owner_type = 'shipper' ").Delete(models.OauthSessions{})

	db.Model(&admin).Updates(map[string]interface{}{
		"login_time":     carbon.Now("Asia/Shanghai").ToDateTimeString(),
		"searial_number": searialNumber,
	})
	sessions := models.OauthSessions{
		ClientID:          clientId,
		OwnerID:           fmt.Sprintf("%d", admin.ID),
		OwnerType:         "shipper",
		ClientRedirectUri: "",
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}
	db.Create(&sessions)
	tokens := models.OauthAccessTokens{
		ID:         accessToken,
		SessionID:  sessions.ID,
		ExpireTime: int(carbon.Now().AddYears(1).Timestamp()),
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}
	db.Create(&tokens)

	return map[string]interface{}{
		"access_token": accessToken,
	}

}

// GetShipperProfile
//
//	@Description: 获取配送员资料
//	@author: Alimjan
//	@Time: 2022-09-16 11:15:01
//	@receiver shipper ShipperService
//	@return interface{}
func (shipper ShipperService) GetShipperProfile(adminIn models.Admin) models.Admin {
	var db = tools.Db
	var admin models.Admin
	db.Where("id = ?", adminIn.ID).Select("id,avatar,mobile,name,real_name,openid").
		First(&admin)
	return admin
}

// SaveInfo
//
//	@Description: 更新配送员信息
//	@author: Alimjan
//	@Time: 2022-09-16 11:46:57
//	@receiver shipper ShipperService
//	@param admin models.Admin
//	@param name string
//	@param avator string
func (shipper ShipperService) SaveInfo(admin models.Admin, name string, avatar string) {
	var db = tools.Db
	updates := map[string]interface{}{}
	if len(name) > 0 {
		name = tools.FilterEmoji(name)
		updates["real_name"] = name
	}
	if len(avatar) > 0 {
		updates["avatar"] = avatar
	}
	db.Model(&admin).Updates(updates)
}

// SaveShipperAvatar
//
//	@Description: 存储配送员头像
//	@author: Alimjan
//	@Time: 2022-09-16 12:07:23
//	@receiver shipper ShipperService
//	@param file *multipart.FileHeader
func (shipper ShipperService) SaveShipperAvatar(c *gin.Context, file *multipart.FileHeader) (bool, error, string) {
	if file == nil {
		return false, nil, ""
	}
	var folder = "upload/shipper_avatar"
	var now = time.Now()
	var absoluteDir = fmt.Sprintf("%s%s/%d%02d/%02d/", configs.MyApp.UploadRootDir, folder, now.Year(), now.Month(), now.Day())

	fileDir := filepath.Dir(absoluteDir)
	rs, err := tools.PathExists(fileDir)
	println(rs)
	if err != nil {
		return false, err, ""
	}
	if !rs {
		println("-----error-----")
		err := os.MkdirAll(fileDir, os.ModePerm)
		if err != nil {
			return false, err, ""
		}
	}
	var newName = fmt.Sprintf("%s%s", tools.RandStr(24), filepath.Ext(file.Filename))
	var absoluteFilePath = fmt.Sprintf("%s%s", absoluteDir, newName)
	println(absoluteFilePath)
	c.SaveUploadedFile(file, absoluteFilePath)
	return true, nil, fmt.Sprintf("/%s/%d%02d/%02d/%s", folder, now.Year(), now.Month(), now.Day(), newName)
}

// GetRankList
//
//	@Description: 获取配送员每日订单排行榜
//	@author: Alimjan
//	@Time: 2022-10-12 17:54:37
//	@receiver shipper ShipperService
//	@param typeId int 1，今天 2，昨天 3，这个月
//	@return []map[string]interface{}
func (shipper ShipperService) GetRankList(typeId int) []map[string]interface{} {
	var (
		timeFrom carbon.Carbon
		timeTo   carbon.Carbon
	)
	switch typeId {
	case 1:
		timeFrom = tools.Today("Asia/Shanghai")
		timeTo = carbon.Now("Asia/Shanghai")

	case 2:
		timeFrom = tools.Yesterday("Asia/Shanghai")
		timeTo = tools.Today("Asia/Shanghai")

	case 3:
		timeFrom = tools.FirstOfMonth("Asia/Shanghai")
		timeTo = carbon.Now("Asia/Shanghai")

	default:

	}
	var db = tools.Db
	//data  := tools.Remember(c,cacheKey,5*time.Minute,(){
	var resultToday []map[string]interface{}
	var resultHis []map[string]interface{}
	if typeId != 2 {
		// db.Raw("call get_shipper_daily_statistics(?,?,'finished_order_count',0)", tools.Today("Asia/Shanghai").ToDateTimeString(), carbon.Now().ToDateTimeString()).Scan(&resultToday)
		shipper.GetShipperDailyStatistics(tools.Today("Asia/Shanghai").ToDateTimeString(), carbon.Now().ToDateTimeString(), "finished_order_count").Scan(&resultToday)
	}
	db.Table("t_shipper_daily_statics").Select(`
					t_shipper_daily_statics.shipper_id,
					t_shipper_daily_statics.area_id,
					t_shipper_daily_statics.area_name_ug,
					t_shipper_daily_statics.area_name_zh,
					t_shipper_daily_statics.real_name,
					sum(t_shipper_daily_statics.finished_order_count) as finished_order_count,
					sum(t_shipper_daily_statics.late_order_count) as late_order_count,
					sum(t_shipper_daily_statics.late_order_fee) as late_order_fee,
					sum(t_shipper_daily_statics.total_late_second) as total_late_second,
					sum(t_shipper_daily_statics.returned_order_count) as returned_order_count,
			sum(t_shipper_daily_statics.distance) as shipper_distance
	`).Joins("left join b_area on b_area.id=t_shipper_daily_statics.area_id").
		Where("t_shipper_daily_statics.trans_date between ? and ? and b_area.shipper_rank_state = ?", timeFrom.ToDateTimeString(), timeTo.ToDateTimeString(), 1).
		Where("t_shipper_daily_statics.shipper_id > ?", 0).
		Group("t_shipper_daily_statics.shipper_id").
		Scan(&resultHis)
	result := shipper.RankFormat(resultToday, resultHis)
	return result
}

// RankFormat
//
//	@Description: 返回数据配送员格式化
//	@author: Alimjan
//	@Time: 2022-10-12 17:55:29
//	@receiver shipper ShipperService
//	@param today []map[string]interface{} 今日订单
//	@param his []map[string]interface{} 历史订单
//	@return []map[string]interface{} 合并后的订单
func (shipper ShipperService) RankFormat(today []map[string]interface{}, his []map[string]interface{}) []map[string]interface{} {

	result := make([]map[string]interface{}, 0)
	for _, v := range his {
		result = append(result, v)
	}
	for _, todayItem := range today {
		index := -1
		for j, v := range his {
			if tools.ToInt64(v["shipper_id"]) == tools.ToInt64(todayItem["shipper_id"]) {
				index = j
				break
			}
		}
		if index == -1 {
			result = append(result, todayItem)
		} else {
			for j, v := range result {
				if tools.ToInt64(v["shipper_id"]) == tools.ToInt64(todayItem["shipper_id"]) {
					result[j]["finished_order_count"] = tools.ToInt64(todayItem["finished_order_count"]) + tools.ToInt64(v["finished_order_count"])
					result[j]["late_order_count"] = tools.ToInt64(todayItem["late_order_count"]) + tools.ToInt64(v["late_order_count"])
					result[j]["late_order_fee"] = tools.ToInt64(todayItem["late_order_fee"]) + tools.ToInt64(v["late_order_fee"])
					result[j]["total_late_second"] = tools.ToInt64(todayItem["total_late_second"]) + tools.ToInt64(v["total_late_second"])
					result[j]["returned_order_count"] = tools.ToInt64(todayItem["returned_order_count"]) + tools.ToInt64(v["returned_order_count"])
					result[j]["shipper_distance"] = tools.ToFloat64(todayItem["shipper_distance"]) + tools.ToFloat64(v["shipper_distance"])
					break
				}
			}
		}
	}
	for j := range result {
		result[j]["finished_order_count"] = tools.ToInt64(result[j]["finished_order_count"])
		result[j]["late_order_count"] = tools.ToInt64(result[j]["late_order_count"])
		result[j]["late_order_fee"] = tools.ToInt64(result[j]["late_order_feelate_order_fee"])
		result[j]["total_late_second"] = tools.ToInt64(result[j]["total_late_second"])
		result[j]["returned_order_count"] = tools.ToInt64(result[j]["returned_order_count"])
		result[j]["shipper_distance"] = tools.ToFloat64(result[j]["shipper_distance"])
	}
	resultSorted := tools.BubbleSort(result, "finished_order_count")

	return resultSorted
}

func (shipper ShipperService) GetChampion() map[string]interface{} {
	db := tools.Db
	from := tools.FirstOfMonth("Asia/Shanghai")
	//from := carbon.Parse("2022-05-23","Asia/Shanghai")
	end := carbon.Now("Asia/Shanghai")
	var oldChampion map[string]interface{}
	var newChampion map[string]interface{}
	db.Model(models.ShipperDailyStatics{}).
		Joins("left join b_area on b_area.id = t_shipper_daily_statics.area_id").
		Where("t_shipper_daily_statics.trans_date between ? and ? and b_area.shipper_rank_state = ?", from.Carbon2Time(), end.Carbon2Time(), 1).
		Where("t_shipper_daily_statics.shipper_id > ?", 0).
		Order("t_shipper_daily_statics.finished_order_count desc").First(&oldChampion)
	// db.Raw("call get_shipper_daily_statistics(?,?,'finished_order_count',0)", tools.Today("Asia/Shanghai").Carbon2Time(), carbon.Now("Asia/Shanghai")).
	// 	First(&newChampion)
	shipper.GetShipperDailyStatistics(tools.Today("Asia/Shanghai").Format("Y-m-d H:i:s"), carbon.Now("Asia/Shanghai").Format("Y-m-d H:i:s"), "finished_order_count").
		First(&newChampion)

	if oldChampion != nil && newChampion != nil {
		if tools.ToInt64(oldChampion["finished_order_count"]) > tools.ToInt64(newChampion["finished_order_count"]) {
			return oldChampion
		} else {
			return newChampion
		}
	}
	if oldChampion != nil && newChampion == nil {
		return oldChampion
	}
	if oldChampion == nil && newChampion != nil {
		return newChampion
	}
	if oldChampion == nil && newChampion == nil {
		return nil
	}
	return nil
}

// StatisticsDetail
//
//	@Time 2022-09-13 00:21:49
//	<AUTHOR>
//	@Description: 配送员统计详细
//	@receiver shipper ShipperService
//	@param admin
//	@param categoryId
//	@param startDate
//	@param endDate
//	@param month
//	@param day
//	@param state
//	@param consumeType
//	@param limit
//	@return interface{}
func (shipper ShipperService) StatisticsDetail(admin models.Admin, categoryId int, startDate string, endDate string, month string, day string, state string, consumeType string, limit int, page int) []shipperResources.StatisticsDetailTransformerBefore {
	db := tools.GetDB()
	var whereTime string
	fmt.Println(startDate, endDate, month, day)
	if startDate != "" && endDate != "" {
		start := carbon.Parse(startDate).ToDateTimeString()
		end := carbon.Parse(endDate).AddDay().ToDateTimeString()
		whereTime = fmt.Sprintf("created_at between '" + start + "' and '" + end + "'")
	} else if month != "" {
		start := carbon.ParseByFormat(month, "Y-m").ToDateTimeString()
		end := carbon.ParseByFormat(month, "Y-m").AddMonth().ToDateTimeString()
		whereTime = fmt.Sprintf("created_at between '" + start + "' and '" + end + "'")
	} else if day != "" {
		start := carbon.Parse(day).ToDateTimeString()
		end := carbon.Parse(day).AddDay().ToDateTimeString()
		whereTime = fmt.Sprintf("created_at between '" + start + "' and '" + end + "'")
	}

	orderToday := db.Model(models.OrderToday{}).
		Select("id ,state,booking_time,order_address,timezone,created_at,building_id,store_id").
		Where(whereTime).
		Where("shipper_id", admin.ID)
	//fmt.Println(todayOrders)

	hisOrders := db.Model(models.Order{}).
		Select("id ,state,booking_time,order_address,timezone,created_at,building_id,store_id").
		Where(whereTime).
		Where("shipper_id", admin.ID)
	union := db.Raw("(?) union (?)", hisOrders, orderToday)
	takeOrder := db.Model(models.TakeOrder{}).
		Select("t_take_order.id,t_take_order.order_id,t_take_order.reason_id,all_order.id as order_today_id,all_order.state,all_order.booking_time,all_order.order_address,all_order.booking_time,all_order.timezone,all_order.created_at,all_order.building_id,address_view.city_id,address_view.city_name_ug,address_view.city_name_zh,address_view.area_id,address_view.area_name_ug as area_name_ug,address_view.area_name_zh as area_name_zh,address_view.street_id,address_view.street_name_ug as street_name_ug,address_view.street_name_zh as street_name_zh,address_view.building_name_ug as building_name_ug,address_view.building_name_zh as building_name_zh,b_order_fail_reason.reason_ug as reason_ug,b_order_fail_reason.reason_zh as reason_zh").
		Joins("left join (?) all_order  on t_take_order.order_id = all_order.id", union).
		Joins("left join b_order_fail_reason on t_take_order.reason_id = b_order_fail_reason.id").
		Joins("left join address_view  on address_view.building_id = all_order.building_id")
	categoryIds := []int{1, 4}
	inArray := false
	for i := 0; i < len(categoryIds); i++ {

		if categoryIds[i] == categoryId {
			inArray = true
			break
		}
	}
	if inArray {
		takeOrder.Select("t_take_order.id,t_take_order.order_id,t_take_order.reason_id,all_order.id as order_today_id,all_order.state,all_order.booking_time,all_order.order_address,all_order.booking_time,all_order.timezone,all_order.created_at,all_order.building_id,address_view.city_id,address_view.city_name_ug,address_view.city_name_zh,address_view.area_id,address_view.area_name_ug as area_name_ug,address_view.area_name_zh as area_name_zh,address_view.street_id,address_view.street_name_ug as street_name_ug,address_view.street_name_zh as street_name_zh,address_view.building_name_ug as building_name_ug,address_view.building_name_zh as building_name_zh,b_order_fail_reason.reason_ug as reason_ug,b_order_fail_reason.reason_zh as reason_zh" +
			",t_restaurant.name_ug as restaurant_name_ug,t_restaurant.name_zh as restaurant_name_zh,t_restaurant.address_ug as restaurant_address_ug,t_restaurant.address_zh as restaurant_address_zh,t_restaurant_building.distance").
			Joins("left join t_restaurant on all_order.store_id = t_restaurant.id").
			Joins("left join t_restaurant_building on t_restaurant_building.restaurant_id = all_order.store_id and t_restaurant_building.building_id = all_order.building_id")
	}

	takeOrder = takeOrder.Where("t_take_order.admin_id= ?", admin.ID).Where("t_take_order." + whereTime)
	//takeOrder.Scan(&transformerBefore)
	if state == "" && consumeType == "" {
		takeOrder = takeOrder.Where("t_take_order.state In ?", []int{2, 3})
	} else if state == "" && consumeType != "" {
		if consumeType == "1" {
			takeOrder = takeOrder.Where("t_take_order.consume_type in (1,3)").Where("t_take_order.state In ?", []int{2, 3})
		} else {
			takeOrder = takeOrder.Where("t_take_order.consume_type = ? ", consumeType).Where("t_take_order.state In ?", []int{2, 3})
		}
	} else if state != "" && consumeType == "" {
		takeOrder = takeOrder.Where("t_take_order.state = ? ", state)
	} else if state != "" && consumeType != "" {
		if consumeType == "1" {
			takeOrder = takeOrder.Where("t_take_order.consume_type in (1,3)").Where("t_take_order.state = ? ", state)
		} else {
			takeOrder = takeOrder.Where("t_take_order.consume_type = ? ", consumeType).Where("t_take_order.state = ? ", state)
		}
	}
	transformerBefore := []shipperResources.StatisticsDetailTransformerBefore{}

	takeOrder.
		Where("t_take_order.deleted_at is null").
		Limit(limit).
		Offset((page - 1) * limit).
		Scan(&transformerBefore)
	return transformerBefore
}

// GetAdminOrderList
//
//	@Time 2022-09-14 23:10:54
//	<AUTHOR>
//	@Description: 显示配送员订单列表
//	@receiver shipper ShipperService
//	@param adminId
func (shipper ShipperService) GetAdminOrderList(adminId int) []shipperResources.AdminOrderListTransBefore {
	language := shipper.language
	db := tools.Db
	var adminOrderlist []shipperResources.AdminOrderListTransBefore
	db.Model(models.TakeOrder{}).
		Select("t_take_order.id ,  t_take_order.order_id,  t_restaurant_building.distance,  t_take_order.admin_id,  t_order_today.id as order_today_id,  t_order_today.store_id,  t_order_today.building_id,  t_order_today.order_address,  t_order_today.timezone,  t_order_today.booking_time,  t_order_today.order_type,  t_restaurant.id as restaurant_id,  t_restaurant.name_"+language+" as restaurant_name,  t_restaurant.address_"+language+" as restaurant_address,  address_view.city_id, address_view.city_name_"+language+" as city_name, address_view.area_id, address_view.area_name_"+language+" as area_name, address_view.street_id, address_view.street_name_"+language+" as street_name, address_view.building_name_"+language+" as building_name").
		Where("t_take_order.state = ?", 1).
		Where("t_take_order.admin_id = ?", adminId).
		Joins("left join t_order_today on t_order_today.id = t_take_order.order_id").
		Joins("left join t_restaurant on t_restaurant.id = t_order_today.store_id").
		Joins("left join address_view on address_view.building_id = t_order_today.building_id").
		Joins("left join t_restaurant_building on t_restaurant_building.restaurant_id = t_order_today.store_id and t_restaurant_building.building_id = t_order_today.building_id").
		Where("t_take_order.created_at > ?", tools.Yesterday("Asia/Shanghai")).
		Where("t_order_today.id is NOT NULL").
		Where("t_take_order.deleted_at IS NULL").
		Scan(&adminOrderlist)
	return adminOrderlist
}

// CloseAccount
//
//	@Time 2022-09-16 18:49:34
//	<AUTHOR>
//	@Description: 更新商家阅读评论时间
//	@receiver shipper ShipperService
//	@param admin
func (shipper ShipperService) CloseAccount(admin models.Admin) {
	db := tools.Db
	db.Model(&admin).Update("state", 0)
}

// GetCheckOut
//
//	@Time 2022-09-21 10:19:57
//	<AUTHOR>
//	@Description:
//	@receiver shipper ShipperService
//	@param date
//	@param adminId
func (shipper ShipperService) GetCheckOut(date string, adminId int) []shipperResources.CheckOut {
	now := carbon.Now().Format("Y-m-d")
	db := tools.Db
	model := db.Model(models.TakeOrder{})
	checkOut := []shipperResources.CheckOut{}
	if date == now {
		model.
			Select("t_order_today.consume_type,t_order_today.booking_time,t_take_order.id, t_take_order.order_id,t_take_order.state, (t_order_today.price +  t_order_today.shipment+ t_order_today.lunch_box_fee) as order_price , t_take_order.is_clearing,t_take_order.created_at").
			Joins("right join t_order_today on t_take_order.order_id = t_order_today.id").
			Where("t_take_order.created_at > ?", carbon.Yesterday()).
			Where("t_order_today.id is NOT NULL").
			Where("t_take_order.state IN ? ", []int{2, 3}).
			Where("t_take_order.admin_id = ?", adminId).
			Where("t_order_today.consume_type IN ?", []int{0, 3})
	} else {
		model.
			Select("t_order.consume_type,t_order.booking_time,t_take_order.id, t_take_order.order_id,t_take_order.state, (t_order.price +  t_order.shipment + t_order.lunch_box_fee) as order_price , t_take_order.is_clearing,t_take_order.created_at").
			Joins("right join t_order on t_take_order.order_id = t_order.id").
			Where("DATE(t_take_order.created_at) = ?", date).
			Where("t_take_order.state IN ?", []int{2, 3}).
			Where("t_take_order.admin_id = ?", adminId).
			Where("t_order.consume_type IN ?", []int{0, 3})
	}
	model.Find(&checkOut)
	return checkOut
}

// GetAttendanceInfo
//
//	@Time 2022-12-31 17:59:48
//	<AUTHOR>
//	@Description: 获取配送员考勤信息
//	@receiver shipper ShipperService
//	@param admin
//	@return interface{}
func (shipper ShipperService) GetAttendanceInfo(admin models.Admin) (models.AttendanceState, []map[string]interface{}, []map[string]interface{}) {

	db := tools.Db
	var state int
	var statePtr *int
	var currentState *models.AttendanceState

	result := db.Model(admin).Select("attendance_state").Scan(&statePtr)
	if result.Error != nil {
		println(result.Error)
	}
	if statePtr != nil {
		state = *statePtr
	}
	if state == 0 {
		state = 2 // 如果是Null状态 ,那就需要设置为上班状态 状态2的NextState是  上班跟请假
	}
	db.Model(models.AttendanceState{}).Select("id, name_"+shipper.language+" as name, next_state,created_at").Where("id = ?", state).Find(&currentState)
	var nextState string = currentState.NextState
	split := strings.Split(nextState, ",")
	nextStates := []map[string]interface{}{}
	db.Model(models.AttendanceState{}).Select("id,name_"+shipper.language+" as name").Where("id IN ?", split).Scan(&nextStates)

	todayHistoryStates := []map[string]interface{}{}
	db.Model(models.ShipperAttendanceLog{}).
		Joins("left join b_attendance_state on b_attendance_state.id = t_shipper_attendance_log.state").
		Select("t_shipper_attendance_log.state,b_attendance_state.name_"+shipper.language+" as name,t_shipper_attendance_log.created_at").
		Where("shipper_id = ?", admin.ID).
		Where("t_shipper_attendance_log.created_at > ?", carbon.Now().Format("Y-m-d")).
		Order("created_at asc").
		Scan(&todayHistoryStates)
	return *currentState, nextStates, todayHistoryStates
}

// PostAttendance
//
//	@Time 2022-12-31 18:03:23
//	<AUTHOR>
//	@Description: 配送员打卡
//	@receiver shipper ShipperService
//	@param admin
func (shipper ShipperService) PostAttendance(admin models.Admin, attendanceState string, lat string, lng string, position string, adminId int, images string) (bool, string, []map[string]interface{}) {
	// 判断是否在一分钟内打过卡  如果打过卡就不允许再打卡
	db := tools.Db
	now := carbon.Now()
	var lastAttendance models.ShipperAttendanceLog
	db.Model(models.ShipperAttendanceLog{}).Where("shipper_id = ?", admin.ID).
		Order("created_at desc").Limit(1).Scan(&lastAttendance)
	// 限流返回的msg
	LimitedMsg := shipper.langUtil.T("attendance-limit")
	errMsg := shipper.langUtil.T("error_happend")
	disErrMsg := shipper.langUtil.T("you_have_some_meter_to_attendence_position")
	if lastAttendance.Id != 0 {
		if carbon.Time2Carbon(lastAttendance.CreatedAt).DiffInSeconds(now) < 5 {
			return false, LimitedMsg, nil
		}
	}
	if tools.ToFloat64(lat) == 0.0 && tools.ToFloat64(lng) == 0.0 {
		return false, shipper.langUtil.T("you_must_open_gps"), nil
	}

	if len(position) == 0 {
		return false, shipper.langUtil.T("you_have_some_meter_to_attendence_position"), nil
	}

	// 验证手里是否存在没有结束的订单(下班和休息状态)
	if attendanceState == "2" || attendanceState == "3" {
		var count int64
		db.Model(models.OrderToday{}).Where("shipper_id = ? and state >= 3 and state < 7", admin.ID).Count(&count)
		if count > 0 {
			return false, shipper.langUtil.T("you_have_some_order_not_finish"), nil
		}
	}
	currentImageHash := ""
	if attendanceState == "1" {
		imageHashCheck := shipper.GetAppConfig("shipper_attendance_image_hash_check")
		if imageHashCheck == "1" {
			// 验证图片是否重复使用
			currentImagePath := configs.MyApp.UploadRootDir + images
				currentImageContent, err := os.ReadFile(currentImagePath)
				if err == nil {
					currentImageHash = tools.Md5(string(currentImageContent))
					var count int64
					db.Model(&models.ShipperAttendanceLog{}).
						Where("image_hash = ?", currentImageHash).
						Count(&count)
						if count > 0 {
							return false, shipper.langUtil.T("please_use_new_image"), nil
						}
				}
		}
		imageAiCheckKey := shipper.GetAppConfig("shipper_attendance_image_ai_check")
		if len(imageAiCheckKey) > 2 {
			// 验证图片是否重复使用
			success, msgText := shipper.ImageAiCheck(imageAiCheckKey,images,admin)
			if success {
				if msgText != "通过" {
					return false, msgText, nil
				}
			}
		}
	}


	currentState, nextState, _ := shipper.GetAttendanceInfo(admin)
	var canChange bool = false
	inAttendanceState, _ := strconv.Atoi(attendanceState)
	for _, value := range nextState {
		if tools.ToInt64(value["id"]) == int64(inAttendanceState) {
			canChange = true
		}
	}
	var adminAttendence map[string]interface{}
	err := db.Model(models.Admin{}).Where("id = ?", admin.ID).Scan(&adminAttendence).Error
	if err != nil {
		return false, errMsg, nil
	}

	if _, ok := adminAttendence["attend_lat_lng"]; ok {
		if adminAttendence["attend_lat_lng"] != nil {
			latLng := adminAttendence["attend_lat_lng"].(string)
			if latLng != "9999,9999" {
				arr := strings.Split(latLng, ",")
				if len(arr) < 2 {
					return false, errMsg, nil
				}
				latDisStr := arr[0]
				lngDisStr := arr[1]
				latDis, _ := strconv.ParseFloat(latDisStr, 64)
				lngDis, _ := strconv.ParseFloat(lngDisStr, 64)
				latFloat, _ := strconv.ParseFloat(lat, 64)
				lngFloat, _ := strconv.ParseFloat(lng, 64)

				distance := tools.CalculateLatitudeLongitudeDistance(lngDis, latDis, lngFloat, latFloat)
				attendRadius := tools.ToFloat64(adminAttendence["attend_radius"])
				if attendRadius < distance {
					return false, fmt.Sprintf(disErrMsg, distance-attendRadius), nil
				}
			}
		}
	}

	if canChange {
		//配送员的考勤状态
		admin.AttendanceState, _ = strconv.Atoi(attendanceState)
		// 更新配送员的考勤状态
		er1 := db.Table("t_admin").Where("id = ?", admin.ID).Update("attendance_state", admin.AttendanceState).Error
		if er1 != nil {
			panic(er1.Error)
		}

		// 获取配送员的城市跟区域
		var areas map[string]interface{}
		db.Table("admin_areas").Select("city_id,area_id").Where("admin_id = ?", admin.ID).Scan(&areas)

		//写到日志表
		attendanceLog := models.ShipperAttendanceLog{
			CityId:    tools.ToInt(areas["city_id"]),
			AreaId:    tools.ToInt(areas["area_id"]),
			ShipperId: admin.ID,
			Name:      admin.Name,
			Mobile:    admin.Mobile,
			Lat:       lat,
			Lng:       lng,
			Position:  position,
			State:     admin.AttendanceState,
			AdminId:   adminId,
			Images:    images,
			ImageHash: currentImageHash,
			Type:      1,
		}
		save := db.Save(&attendanceLog)

		if save.Error != nil {
			panic(save.Error)
		}

		msg := shipper.langUtil.T("success")
		return canChange, msg, nil
	} else {
		msg := currentState.Name + shipper.langUtil.T("fail")
		return canChange, msg, nextState
	}
}

// AI检查返回结果结构体
type AIResponse struct {
	Choices []struct {
		Message struct {
			Content string `json:"content"`
			Role    string `json:"role"`
		} `json:"message"`
		FinishReason string      `json:"finish_reason"`
		Index        int         `json:"index"`
		Logprobs     interface{} `json:"logprobs"`
	} `json:"choices"`
	Object            string `json:"object"`
	Usage             struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
	Created           int64       `json:"created"`
	SystemFingerprint interface{} `json:"system_fingerprint"`
	Model             string      `json:"model"`
	ID                string      `json:"id"`
}

type AIResult struct {
	Result string `json:"result"`
}

func (shipper ShipperService) ImageAiCheck(imageAiCheckKey string, imagePath string,admin models.Admin) (bool, string) {
	// 读取图片文件
	cdnUrl := tools.AddCdn(imagePath)
	
	// 构建请求体
	requestBody := map[string]interface{}{
		"model": "qwen2.5-vl-32b-instruct",
		"messages": []map[string]interface{}{
			{
				"role": "user",
				"content": []map[string]interface{}{
					{
						"type": "text",
						"text": `请检查这张照片是否符合以下要求：

骑手是否戴了头盔？
骑手的服装是否整齐且干净？（衣服为绿色，是否明显可见？）
骑手的表情是否微笑？如果带了口罩，则不要考虑表情。
骑手是否正面拍摄？（面部是否清晰可见？）
骑手是否穿戴整齐，且未有明显杂乱或脏污？
图片中必须有骑手正面照片，如果图片中没有骑手正面照片，则返回"拍摄角度不正，未显示正面"，也不能有多余的骑手，只允许有一个
如果照片不符合要求，请选择最关键的失败原因并返回给骑手，确保只返回一项原因：

"未戴头盔"
"服装不整齐或不干净"
"表情未微笑"
"拍摄角度不正，未显示正面"
如果照片符合所有要求，请标记为通过。

请返回json格式，格式如下：
{
    "result": "通过"
}`,
					},
					{
						"type": "image_url",
						"image_url": map[string]interface{}{
							"url": cdnUrl,
						},
					},
				},
			},
		},
		"stream": false,
		"response_format": map[string]interface{}{
			"type": "json_object",
		},
	}

	// 将请求体转换为JSON
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return false, "请求体序列化失败"
	}

	// 创建请求
	req, err := http.NewRequest("POST", "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return false, "创建请求失败"
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+imageAiCheckKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{
		Timeout: 15 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return false, "发送请求失败"
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, "读取响应失败"
	}

	// 解析响应
	var aiResponse AIResponse
	if err := json.Unmarshal(body, &aiResponse); err != nil {
		return false, "解析响应失败"
	}

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return false, fmt.Sprintf("API请求失败: %s", string(body))
	}

	// 解析AI返回的JSON内容
	if len(aiResponse.Choices) > 0 {
		var aiResult AIResult
		if err := json.Unmarshal([]byte(aiResponse.Choices[0].Message.Content), &aiResult); err != nil {
			return false, "解析AI结果失败"
		}
		tools.Logger.Info("解析结果: ",cdnUrl," ,ai 识别结果:",aiResult.Result,",骑手ID:",admin.ID)
		// 根据结果返回
		return true, aiResult.Result
	}else{
		tools.Logger.Info("数据解析失败")
	}

	return false, "未获取到有效结果"
}

// GetAttendanceList
//
//	@Time 2022-12-31 19:23:13
//	<AUTHOR>
//	@Description: 获取配送员考勤列表
//	@receiver shipper ShipperService
//	@param admin
//	@return interface{}
func (shipper ShipperService) GetAttendanceList(c *gin.Context, admin models.Admin) ([]map[string]interface{}, string, string) {
	db := tools.Db
	var minCreatedAts []map[string]interface{}
	var attendanceList []map[string]interface{}
	endTime := carbon.ParseByFormat(c.Query("end_time"), "Y-m-d").AddDay().Format("Y-m-d")
	err := db.Model(models.ShipperAttendanceLog{}).
		Select("created_at").
		Where("shipper_id = ?", admin.ID).
		Where("t_shipper_attendance_log.created_at < ?", endTime).
		Order("created_at desc").
		Limit(7).
		Scan(&minCreatedAts).Error

	if len(minCreatedAts) == 0 {
		return attendanceList, endTime, endTime
	}
	minCreatedAt := minCreatedAts[len(minCreatedAts)-1]
	if err != nil || minCreatedAt == nil || minCreatedAt["created_at"] == nil {
		return attendanceList, endTime, endTime
	}
	startTime := minCreatedAt["created_at"].(time.Time).Format("2006-01-02")

	db.Model(models.ShipperAttendanceLog{}).
		Select("t_shipper_attendance_log.created_at,t_shipper_attendance_log.state,"+
			" t_shipper_attendance_log.lat,t_shipper_attendance_log.lng,t_shipper_attendance_log.position,"+
			"t_shipper_attendance_log.admin_id,t_shipper_attendance_log.shipper_id,t_admin.name as admin_name,b_attendance_state.name_"+shipper.language+" as attendance_name").
		Joins("left join t_admin on t_admin.id = t_shipper_attendance_log.admin_id").
		Joins("left join b_attendance_state on t_shipper_attendance_log.state = b_attendance_state.id").
		Where("shipper_id = ?", admin.ID).
		Where("t_shipper_attendance_log.created_at BETWEEN ? AND ?", startTime, endTime).
		Order("created_at desc").
		Scan(&attendanceList)
	return attendanceList, carbon.Parse(endTime).SubDay().ToDateString(), carbon.Parse(startTime).SubDay().ToDateString()
}

// GrabOrder
//
//	@Description: 配送员抢订单 新版
//	@author: rozimamat
//	@Time: 2023-09-06 12:18:00
//	@receiver shipper ShipperService
//	@param c *gin.Context
//	@param admin models.Admin
//	@param orderID int
//	@return bool	抢单成功返回true,否则返回false
//	@return string 抢单成功返回成功,否则返回错误提示
func (shipper ShipperService) GrabOrder(c *gin.Context, admin models.Admin, orderID int,setChannel int) (int, string) {
	tools.Logger.Info("配送员抢单，配送员编号：", admin.ID, "订单编号：", orderID, "分配通道：", setChannel)
	shipperInfo := models.Admin{}
	shipperID := admin.ID
	var _ error
	if admin.Type == 8 {
		shipperID, _ = strconv.Atoi(c.PostForm("order_id"))
		tools.Db.Where("id =?", shipperID).First(&shipperInfo)
		admin = shipperInfo
	}

	if admin.State == 0 {
		//log.Println("配送员在休息状态，不能抢单，配送员编号：", admin.ID)
		return -1001, shipper.langUtil.T("shopper_working_off")
	}
	// 配送员当前考勤状态是否能配送定安
	if !shipper.ShipperAttendanceCanTakeOrder(admin.ID) {
		return -1001, shipper.langUtil.T("shipper_attendance_off")
	}
	//检查实名认证状态 
	realNameOk :=shipper.RealNameCheck(admin.ID)
	if !realNameOk{ //进行实名认证，不能抢单
		return -1001, shipper.langUtil.T("shipper_not_real_name")
	}
	//检查保险状态 
	insured :=shipper.InsuranceStatusCheck(admin.ID,admin.AdminAreaID)
	if !insured{ //未购买今天的保险，不能抢单
		return -1001, shipper.langUtil.T("shipper_not_insured")
	}
	db := tools.Db
	// 验证特价活动是否属于配送员
	var specialPriceOrder models.OrderToday
	db.Model(specialPriceOrder).Where("id=?",orderID).Find(&specialPriceOrder)
	if specialPriceOrder.ID==0{
		return -1001, shipper.langUtil.T("not_found")
	}
	// if specialPriceOrder.MarketType == 2 && !specialPriceOrder.IsShipperSpecialPriceOrder(admin){
	// 	tools.Logger.Error("配送员抢单不属于自己的特价活动订单:",admin.ID,orderID)
	// 	return false, shipper.langUtil.T("special_price_order_not_belong_shipper")
	// }
	//能配送送的订单数是否大于实际订单数量
	var takedOrdes int64


	//限流  每x秒内发送的请求只会通过 x 个 ，其他的会提示 xx秒后重试
	key := "grab_order_" + tools.ToString(shipperID) + "_" + tools.ToString(orderID)
	sec := configs.MyApp.ShipperGrabOrderLimit                //请求间隔 秒

	lockKey := "order_lock_" + strconv.Itoa(orderID)
	//添加redis 锁，防止订单被多人抢
	orderLock := tools.NewMyRedisLock(lockKey,"1",30*time.Second)
	defer orderLock.Unlock()
	if !orderLock.Lock() {
		tools.Logger.Info("订单已被抢 " + strconv.Itoa(orderID))
		return -1001, shipper.langUtil.T("already_taked")
	}

	orderGrapLock := tools.NewMyRedisLock(key,"1",30*time.Second)
	defer orderGrapLock.Unlock()
	if !orderGrapLock.Lock() {
		return -1001, fmt.Sprintf(shipper.langUtil.T("retry_after_second"), sec)
	}

	var area models.Area
	tools.Db.Model(area).Where("id = ?", admin.AdminAreaID).First(&area)
	db.Model(&models.OrderToday{}).Where("created_at >= ? and shipper_id =? and state>=3 and state<7", tools.Today("Asia/Shanghai").ToDateTimeString("Asia/Shanghai"), admin.ID).Count(&takedOrdes)
	if area.AutoDispatchState == 1 && area.AutoDispatchEnableRank == 1 &&  int(takedOrdes) >= admin.AutoDispatchRankOrderCount{
		return -1001, fmt.Sprintf(shipper.langUtil.T("grab_order_count_exceeded"), admin.AutoDispatchRankOrderCount)
	}
	if setChannel == constants.TakeOrderChannelShipper &&  int(takedOrdes) >= admin.GrabOrderCount {
		return -1001, fmt.Sprintf(shipper.langUtil.T("grab_order_count_exceeded"), admin.GrabOrderCount)
	}

	//获取分配该配送员的所有餐厅编号
	ResIDs := admin.GetRestaurantIds(c, false)
	//设置数据库事务所的隔离级别
	opt := &sql.TxOptions{
		Isolation: sql.LevelSerializable,
	}
	var err error

	tx := tools.Db.Begin(opt)
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			tools.Logger.Errorf("shipper_id=%d ,order_id=%d 查询订单时出现错误:%v", admin.ID, orderID, err)
		}
	}()
	Order := models.OrderToday{}
	err = tx.Set("gorm:query_option", "FOR UPDATE").Model(&models.OrderToday{}).Where("id = ?", orderID).
		Where("state IN ?", []int{4, 5}).Where("taked = ?", 0).
		Where("store_id in ?", ResIDs).
		First(&Order).Error
	if err != nil {
		tx.Rollback()
		if setChannel == 3 {
			var orderLog map[string]interface{}
			tools.GetDB().Model(&Order).Where("id=?", orderID).Scan(&orderLog)
			if tools.ToInt(orderLog["state"]) < 7  && orderLog["json_info"] == nil {
				tools.Logger.Error("智能派单分配订单，已抢过当前订单信息 order_id:",orderLog["id"]," shipper_id:",orderLog["shipper_id"]," state:",orderLog["state"]," 智能派单分配配送员：", admin.ID)
			}
		}
		tools.Logger.Errorf("shipper_id=%d ,order_id=%d 查询订单时出现错误:%v", admin.ID, orderID,err.Error())
		return -1000, shipper.langUtil.T("already_taked")
	}


	if Order.CategoryID != 1 && Order.CategoryID != 4 {
		tx.Rollback()
		return -1000, shipper.langUtil.T("self_taken_order")
	}
	err = tx.Model(&Order).Updates(map[string]interface{}{"DeliveryTakedTime": carbon.Now().Carbon2Time(), "Taked": 1, "ShipperID": admin.ID}).Error
	if err != nil {
		tx.Rollback()
		tools.Logger.Error("订单抢单时出现错误:", err.Error())
		return -1001, shipper.langUtil.T("error_happend")
	}
	tools.Logger.Info("配送员抢单成功，配送员编号：", admin.ID, "订单编号：", orderID)
	tx.Commit() //数据库事务所结束了
	//开始写入t_take_order表

	TakeOrder := models.TakeOrder{}
	db.Model(&models.TakeOrder{}).Where("order_id =?", orderID).Where("created_at > ?", tools.Yesterday("Asia/Shanghai")).Where("admin_id =?", admin.ID).Where("state =?", 0).First(&TakeOrder)
	if TakeOrder.OrderID == 0 {
		var pushDetail models.ShipperOrderPushDetail
		db.Model(&models.ShipperOrderPushDetail{}).Where("order_id = ? and shipper_id = ?", orderID, admin.ID).Find(&pushDetail)
		NewTakeOrder := models.TakeOrder{}
		NewTakeOrder.OrderID = orderID
		NewTakeOrder.AdminID = admin.ID
		NewTakeOrder.State = 1
		NewTakeOrder.StoreId = Order.StoreID           //店铺id
		NewTakeOrder.Distance = Order.Distance         //距离
		NewTakeOrder.BuildingId = Order.BuildingID     //楼栋id
		NewTakeOrder.OrderAddress = Order.OrderAddress //地址
		NewTakeOrder.OrderPrice = Order.Price + Order.Shipment + uint(Order.LunchBoxFee)
		NewTakeOrder.EstimatedShippingPrice = pushDetail.EstimatedShippingPrice // 推送时估计的配送费
		if Order.ConsumeType == 0 {
			NewTakeOrder.ConsumeType = 0
			NewTakeOrder.IsClearing = 0
		} else if Order.ConsumeType == 3 {
			NewTakeOrder.ConsumeType = 3
			NewTakeOrder.IsClearing = 0
		} else {
			NewTakeOrder.ConsumeType = 1
			NewTakeOrder.IsClearing = 1
		}
		NewTakeOrder.SetChannel = setChannel
		err = db.Create(&NewTakeOrder).Error
		if err != nil {
			tools.Logger.Error(fmt.Sprintf("错误: 抢单信息写入t_take_order时出现错误: %s", err.Error()))
			return -1001, shipper.langUtil.T("error_happend")
		}
	} else {
		Updates := map[string]interface{}{
			"ReasonID": nil,
			"State":    1,
		}
		Updates["OrderPrice"] = Order.Price + Order.Shipment + uint(Order.LunchBoxFee)
		if Order.ConsumeType == 0 {
			Updates["ConsumeType"] = 0
			Updates["IsClearing"] = 0
		} else if Order.ConsumeType == 3 {
			Updates["ConsumeType"] = 3
			Updates["IsClearing"] = 0
		} else {
			Updates["ConsumeType"] = 1
			Updates["IsClearing"] = 1
		}
		err = db.Model(&TakeOrder).Updates(Updates).Error
		//如果写入更新失败，测取消抢单操作（t_order_today数据表的更新）
		if err != nil {
			tools.Logger.Error(fmt.Sprintf("错误: 抢单信息::%s", err.Error()))
			err2 := db.Model(&models.OrderToday{}).Where("id = ?", orderID).Updates(map[string]interface{}{"Taked": 0, "ShipperID": nil}).Error
			if err2 != nil {
				tools.Logger.Error(fmt.Sprintf("错误:取消抢单操作::%s", err2.Error()))
			}
			return -1001, shipper.langUtil.T("error_happend")
		}
	}
	return 200, shipper.langUtil.T("msg")
}

// 描述：获取配送员取消的订单历史
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2023/09/25 17:55
func (s ShipperService) CanceledOrder(ctx *gin.Context, admin models.Admin, startTime string, endTime string) ([]models.TakeOrder, int64, int64, error) {
	var result []models.TakeOrder

	db := tools.GetDB()
	var count int64
	startTime = carbon.Parse(startTime, configs.AsiaShanghai).Format("Y-m-d")
	endTime = carbon.Parse(endTime, configs.AsiaShanghai).Format("Y-m-d") + " 23:59:59"
	pagination := scopes.Paginate(ctx)

	allReduceAmount := int64(0)
	db.Model(models.TakeOrder{}).
		Where("t_take_order.admin_id = ?", admin.ID).
		Where("t_take_order.created_at BETWEEN ? AND ?", startTime, endTime).
		Where("t_take_order.state = ?", 0).
		Where("t_take_order.store_id > ?", 0).
		Order("t_take_order.created_at desc").
		Select("IFNULL(sum( estimated_shipping_price ),0) as estimated_shipping_price").
		Scan(&allReduceAmount)

	db.Model(models.TakeOrder{}).
		Preload("Restaurant").
		Preload("AddressView").
		Where("t_take_order.admin_id = ?", admin.ID).
		Where("t_take_order.created_at BETWEEN ? AND ?", startTime, endTime).
		Where("t_take_order.state = ?", 0).
		Where("t_take_order.store_id > ?", 0).
		Order("t_take_order.created_at desc").
		Scopes(pagination).
		Find(&result)

	// 获取未应用分页限制的完整结果集计数
	db.Model(models.TakeOrder{}).Where("admin_id = ? AND state = ? AND created_at BETWEEN ? AND ? and store_id > ?", admin.ID, 0, startTime, endTime, 0).Count(&count)

	return result, count, allReduceAmount, nil
}

// 描述：验证码登录
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2023/09/27 17:11
func (shipper ShipperService) CheckSMSLogin(mobile string) (models.Admin, error) {
	db := tools.Db
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Printf("登录日志【验证码】 error: %s\n", err)
			}
		}()
	}()
	var admin models.Admin
	db.Model(admin).Where("mobile = ?", mobile).
		Where("type in (8,9)").Where("state = 1").
		Where("deleted_at IS NULL").First(&admin)
	if admin.ID == 0 {
		return admin, errors.New("admin_is_not_active")
	}
	if (admin.Type != models.AdminTypeShipperAdmin) && (admin.Type != models.AdminTypeShipper) {
		return admin, errors.New("admin_is_not_login")
	}
	return admin, nil
}

// 描述：配送员找回密码
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2023/09/28 12:29
func (shipper ShipperService) RecoverPassword(mobile string, newPassword string) error {
	langUtil := shipper.langUtil
	var err error
	result := models.Admin{}
	db := tools.GetDB()
	err = db.Model(models.Admin{}).Where("mobile", mobile).Where("type In (8,9)").Find(&result).Error
	fmt.Println(err)
	if result.ID == 0 {
		return errors.New("没有此用户")
	}
	if result.State == 0 {
		return errors.New("用户已在关闭状态、不能修改密码")
	}
	err = db.Model(models.Admin{}).Where("id", result.ID).Update("password", tools.PasswordToHash(newPassword)).Error
	if err != nil {
		return errors.New(langUtil.T("error_happend"))
	}
	return nil
}

// 描述：超时订单列表
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2023/10/08 18:34
func (shipper ShipperService) OverTimeOrders(ctx *gin.Context, admin models.Admin, beginTime string, endTime string) ([]other.LateOrders, int64, float64, error) {
	db := tools.GetDB()

	var result []other.LateOrders
	var allResult []other.LateOrders
	var count int64
	pagination := scopes.Paginate(ctx)

	start := carbon.Parse(beginTime, configs.AsiaShanghai).Format("Y-m-d") + " 00:00:00"
	end := carbon.Parse(endTime, configs.AsiaShanghai).Format("Y-m-d") + " 23:59:59"

	myModel := db.Model(&other.LateOrders{}).
		Select("id,order_id").
		Where("shipper_id = ?", admin.ID).
		Where("booking_time between ? and ?", start, end).
		Preload("ShipperIncome", func(db *gorm.DB) *gorm.DB {
			return db.Where("t_shipper_income.shipper_id = ?  and type = 9", admin.ID)
		})

	myModel.Find(&allResult)
	myModel.Count(&count)

	allReduceAmount := float64(0)
	for _, listItem := range allResult {
		if len(listItem.ShipperIncome) > 0 {

			for _, v := range listItem.ShipperIncome {
				//类型 1：订单收入，2:奖励收入，3:打赏收入，4.特殊时间，5.特殊天气，6.好评，7.差评，8.投诉，9:迟到
				if v.Type == 9 {
					allReduceAmount += tools.ToFloat64(tools.ToFloat64(v.Amount) / 100)
				}
			}
		}

	}

	if allReduceAmount != 0 {
		allReduceAmount = allReduceAmount * (-1) //前段要返回正数
	}

	myModel2 := db.Model(&other.LateOrders{}).
		Select("id,order_id, booking_time,delivered_time, shipment,shipper_id,late_minute").
		Where("shipper_id = ?", admin.ID).
		Where("booking_time between ? and ?", start, end).
		Preload("Order", func(db *gorm.DB) *gorm.DB {
			return db.Where("booking_time between ? and ?", start, end)
		}).
		Preload("OrderToday", func(db *gorm.DB) *gorm.DB {
			return db.Where("booking_time between ? and ?", start, end)
		}).
		Preload("ShipperIncome", func(db *gorm.DB) *gorm.DB {
			return db.Where("t_shipper_income.shipper_id = ?  and type = 9", admin.ID)
			// .
			// 	Select("t_shipper_income.id,t_shipper_income.type,sum(if(t_shipper_income.type in (7,8,9),amount,0)) as amount").
			// 	Group("t_shipper_income.order_id")
		})

	myModel2.Scopes(pagination).
		Find(&result)
	return result, count, allReduceAmount, nil
}

// 描述：配送端帮助列表
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2023/10/10 13:03
func (shipper ShipperService) HelpList(ctx *gin.Context) ([]help.HelpList, error) {
	db := tools.GetDB()
	lang := shipper.language
	pagination := scopes.Paginate(ctx)
	var result []help.HelpList
	if err := db.Model(help.THelp{}).Select(`id,title_` + lang + ` as title`).Scopes(pagination).Find(&result).Error; err != nil {
		return []help.HelpList{}, errors.New(shipper.langUtil.T("error_happend"))
	}

	return result, nil
}

// 描述：获取帮助详情
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2023/10/10 13:15
func (shipper ShipperService) HelpDetail(id int64) (help.HelpDetail, error) {
	db := tools.GetDB()
	lang := shipper.language
	var result help.HelpDetail
	if err := db.Model(help.THelp{}).
		Select(`id,title_`+lang+` as title, content_`+lang+` as content`).
		Where("id = (?)", id).
		Find(&result).Error; err != nil {
		return help.HelpDetail{}, errors.New(shipper.langUtil.T("error_happend"))
	}
	if result.ID == 0 {
		return help.HelpDetail{}, errors.New(shipper.langUtil.T("data_not_found"))
	}

	return result, nil
}

// 描述：隐私协议列表
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2023/10/10 18:04
func (shipper ShipperService) PrivacyList(ctx *gin.Context, terminalID int) ([]privacy.PrivacyList, error) {
	db := tools.GetDB()
	lang := shipper.language
	pagination := scopes.Paginate(ctx)
	var result []privacy.PrivacyList
	if err := db.Model(privacy.Privacy{}).
		Select(`id,url,name_`+lang+` as title`).
		Where("terminal_id = ?", terminalID).
		Where("state = ?", 1).
		Scopes(pagination).
		Where("deleted_at IS NULL").
		Find(&result).Error; err != nil {
		return []privacy.PrivacyList{}, errors.New(shipper.langUtil.T("error_happend"))
	}

	return result, nil
}

/***
 * @LastEditTime: Do not edit
 * @Author: [rozimamat]
 * @description:
 * @Date: 2023-10-20 13:03:31
 * @param {string} beginDate
 * @param {string} endDate
 * @param {string} orderBy
 */
func (shipper ShipperService) GetShipperDailyStatistics(beginDate string, endDate string, orderBy string) *gorm.DB {

	db := tools.GetDB()
	//数据库函数直接复制过来的
	sql := `

	SELECT 
						all_statics.shipper_id,
							all_statics.area_id,
							b_area.name_ug as area_name_ug,
							b_area.name_zh as area_name_zh,
							t_admin.real_name,
							all_statics.finished_order_count,
							late_static.late_order_count,
							late_static.late_order_fee,
							late_static.total_late_second,
							return_statics.returned_order_count,
							all_statics.total_price,
							cashe_order_statics.cash_total_price,
							all_statics.total_shipment,
							all_statics.shipper_distance,
							all_statics.delivery_avg_time
						FROM (
							SELECT 
							t_order.shipper_id,
							t_order.area_id,count(t_order.id) AS finished_order_count,
							sum(price+shipment+lunch_box_fee) as total_price,
							SUM(shipment) AS total_shipment,
							sum(distance) as shipper_distance,
							avg(TIMESTAMPDIFF(MINUTE,delivery_taked_time, delivery_end_time)) as delivery_avg_time
							 FROM t_order_today as t_order 
							 WHERE t_order.created_at >= ? 
							 AND t_order.created_at < ? 
							 AND t_order.state=7 
							 AND t_order.deleted_at IS NULL 
							 and shipper_id is not null 
							 and t_order.area_id in (select id from b_area where shipper_rank_state = 1)
							 GROUP BY t_order.shipper_id) AS all_statics 
						left join (SELECT t_order.shipper_id,t_order.area_id,count(t_order.id) AS finished_order_count,sum(price+shipment+lunch_box_fee) as cash_total_price FROM t_order_today as t_order WHERE t_order.created_at >= ? AND t_order.created_at < ?  AND t_order.state=7 AND  pay_type in (6,11) AND t_order.deleted_at IS NULL and shipper_id is not null GROUP BY t_order.shipper_id) as cashe_order_statics on cashe_order_statics.shipper_id = all_statics.shipper_id
						LEFT JOIN t_admin ON t_admin.id=all_statics.shipper_id 
						LEFT JOIN b_area ON all_statics.area_id=b_area.id 
						LEFT JOIN (SELECT shipper_id,count(id) AS late_order_count,sum(price+shipment+lunch_box_fee) AS late_order_fee,sum(TIME_TO_SEC(SUBTIME (time(delivery_end_time),booking_time))) AS total_late_second FROM t_order_today AS a WHERE a.state IN (7,10) AND a.created_at >= ? AND a.created_at < ?  AND (TIME_TO_SEC(SUBTIME (time(delivery_end_time),booking_time)))>60 GROUP BY shipper_id) AS late_static ON late_static.shipper_id=all_statics.shipper_id 
						LEFT JOIN (SELECT admin_id,count(admin_id) AS returned_order_count FROM t_take_order WHERE created_at >= ? AND created_at < ?  AND state=1 GROUP BY admin_id) AS return_statics ON return_statics.admin_id=all_statics.shipper_id
				union
						SELECT 
							t_admin_info.id as shipper_id,
							admin_areas.area_id,
							b_area_info.name_ug as area_name_ug,
							b_area_info.name_zh as area_name_zh,
							t_admin_info.real_name,
							all_statics.finished_order_count,
							late_static.late_order_count,
							late_static.late_order_fee,
							late_static.total_late_second,
							return_statics.returned_order_count,
							all_statics.total_price,
							cashe_order_statics.cash_total_price,
							all_statics.total_shipment,
							all_statics.shipper_distance,
							all_statics.delivery_avg_time
						FROM (
						 	SELECT 
							 t_order.shipper_id,
							 t_order.area_id,
							 count(t_order.id) AS finished_order_count,
							 sum(price+shipment+lunch_box_fee) as total_price,
							 SUM(shipment) AS total_shipment,
							 sum(distance) as shipper_distance,
							 avg(TIMESTAMPDIFF(MINUTE,delivery_taked_time, delivery_end_time)) as delivery_avg_time
							   FROM t_order_today as t_order 
							   WHERE 
							   t_order.created_at >= ? 
							   AND t_order.created_at < ?  
							   AND t_order.state=7 
							   AND t_order.deleted_at IS NULL 
							   and shipper_id is not null  
							   and t_order.area_id in (select id from b_area where shipper_rank_state = 1)
							   GROUP BY t_order.shipper_id) AS all_statics 
							left join (
								SELECT 
								t_order.shipper_id,t_order.area_id,count(t_order.id) AS finished_order_count,
								sum(price+shipment+lunch_box_fee) as cash_total_price FROM t_order_today as t_order 
								WHERE 
								t_order.created_at >= ? 
								AND t_order.created_at < ?  
								AND t_order.state=7 
								AND  pay_type in (6,11) 
								AND t_order.deleted_at IS NULL 
								and shipper_id is not null 
								and t_order.area_id in (select id from b_area where shipper_rank_state = 1)
								GROUP BY t_order.shipper_id) as cashe_order_statics on cashe_order_statics.shipper_id = all_statics.shipper_id
							LEFT JOIN t_admin ON t_admin.id=all_statics.shipper_id 
							LEFT JOIN b_area ON all_statics.area_id=b_area.id 
							LEFT JOIN (
								SELECT 
								shipper_id,count(id) AS late_order_count,sum(price+shipment+lunch_box_fee) AS late_order_fee,
								sum(TIME_TO_SEC(SUBTIME (time(delivery_end_time),booking_time))) AS total_late_second
								 FROM t_order_today AS a 
								 WHERE 
								 a.state IN (7,10) 
								 AND a.created_at >= ? 
								 AND a.created_at < ?  
								 AND (TIME_TO_SEC(SUBTIME (time(delivery_end_time),booking_time)))>60 
								 and a.area_id in (select id from b_area where shipper_rank_state = 1)
								 GROUP BY shipper_id) AS late_static ON late_static.shipper_id=all_statics.shipper_id 
							RIGHT JOIN (
								SELECT admin_id,count(admin_id) AS returned_order_count
								 FROM t_take_order
								  WHERE 
								  created_at >= ? AND created_at < ?  
								  AND state=1 GROUP BY admin_id) AS return_statics ON return_statics.admin_id=all_statics.shipper_id
							LEFT JOIN t_admin as t_admin_info ON t_admin_info.id=return_statics.admin_id 
							LEFT JOIN admin_areas  ON admin_areas.admin_id=return_statics.admin_id 
							LEFT JOIN b_area as b_area_info ON admin_areas.area_id=b_area_info.id 
							where all_statics.shipper_id is null
							and b_area_info.shipper_rank_state = 1
						order by ?

	
	`

	results := db.Raw(sql, beginDate, endDate, beginDate, endDate, beginDate, endDate, beginDate, endDate, beginDate, endDate, beginDate, endDate, beginDate, endDate, beginDate, endDate, orderBy)

	return results

}

/***
 *
 * @Author: [rozimamat]
 * @description:
 * @Date: 2023-11-14 17:03:31
 * @param {int} adminId
 * @param {int} orderId
 * @param {float64} lat
 * @param {float64} lng
 */
func (shipper ShipperService) ShipperArrivedAtShop(c *gin.Context, adminID int, orderID int, lat float64, lng float64, opType string) (bool, string) {

	db := tools.GetDB()

	orderTodayObj := models.OrderToday{}
	err := db.Model(&models.OrderToday{}).Preload("OrderExtend").Preload("Restaurant", func(db *gorm.DB) *gorm.DB {
		return db.Select(
			"t_restaurant.id,t_restaurant.lat,t_restaurant.lng",
		)
	}).Where("id =?", orderID).First(&orderTodayObj).Error
	if err != nil {
		tools.Logger.Errorf("订单状态更改时出错order_not_found: %d  %d  %f  %f %s", adminID, orderID, lat, lng, err.Error())
		return false, shipper.langUtil.T("order_not_found")
	}

	if !(orderTodayObj.State == models.ORDER_STATE_ACCEPTED || orderTodayObj.State == models.ORDER_STATE_READY) {
		return false, shipper.langUtil.T("order_state_error") //订单状态错误
	}
	distance := tools.CalculateLatitudeLongitudeDistance(lng, lat, orderTodayObj.Restaurant.Lng, orderTodayObj.Restaurant.Lat)
	now := time.Now()
	isCheckShop := true
	//不检查到店 状态的 店铺
	if tools.InArray(orderTodayObj.StoreID, configs.MyApp.ShipperNoCheckArriveShops) {
		isCheckShop = false
		//自动给他填上 数据
		if orderTodayObj.OrderExtend == nil || orderTodayObj.OrderExtend.OrderID == 0 {
			ext := models.OrderExtend{
				OrderID:              orderTodayObj.ID,
				ShipperArrivedShopAt: &now,
				ShipperTakeFoodAt:    &now,
				ShipperOrderState:    300,
			}
			db.Create(&ext)
		} else {
			db.Table("t_order_extend").Where("order_id = ?", orderID).Updates(
				&map[string]interface{}{
					"shipper_arrived_shop_at": &now,
					"shipper_take_food_at":    &now,
					"shipper_order_state":     300,
				})
		}
		return true, ""
	}
	maxDistance := 10000.0 //10公里
	if isCheckShop && distance > tools.ToFloat64(configs.MyApp.ShipperArriveAtShopDistance) {
		tools.Logger.Errorf("配送员跟店铺距离: %d  %d  %f  %f %f", adminID, orderID, lat, lng, distance)
		// leftDistance :=distance - tools.ToFloat64(configs.MyApp.ShipperArriveAtShopDistance) //剩余的距离
		if distance > maxDistance { //大于10公里
			return false, shipper.langUtil.T("shipper_app_location_disabled")
		}
		return false, fmt.Sprintf(shipper.langUtil.T("did_not_arrive_shop"), distance)
	}

	//配送员订单状态, 0:订单未抢单, 100:已抢单, 200:到餐厅, 300:已取餐, 400:完成订单, 500:餐厅未准备好美食,
	if opType == "come" { //到达店铺
		if orderTodayObj.OrderExtend != nil && orderTodayObj.OrderExtend.ShipperArrivedShopAt != nil {
			return true, ""
			// return false, shipper.langUtil.T("order_already_update_arrived_shop") //已经到达过
		}
		if orderTodayObj.OrderExtend == nil || orderTodayObj.OrderExtend.OrderID == 0 {
			ext := models.OrderExtend{
				OrderID:              orderTodayObj.ID,
				ShipperArrivedShopAt: &now,
				ShipperOrderState:    200,
			}
			db.Create(&ext)
		} else {
			db.Table("t_order_extend").Where("order_id = ?", orderID).Updates(
				&map[string]interface{}{
					"shipper_arrived_shop_at": &now,
					"shipper_order_state":     200,
				})
		}
	} else if opType == "take" { //取餐
		if orderTodayObj.OrderExtend != nil && orderTodayObj.OrderExtend.ShipperArrivedShopAt == nil {
			db.Table("t_order_extend").Where("order_id = ?", orderID).Updates(
				&map[string]interface{}{
					"shipper_arrived_shop_at": &now,
					"shipper_take_food_at":    &now,
					"shipper_order_state":     300,
				})
		} else {
			db.Table("t_order_extend").Where("order_id = ?", orderID).Updates(
				&map[string]interface{}{
					"shipper_take_food_at": &now,
					"shipper_order_state":  300,
				})
		}
	}

	return true, ""

}

// 描述：获取配送管理员手下的配送员列表
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2023/11/15 13:15
func (shipper ShipperService) GetAdminList(admin models.Admin, orderId int64) ([]map[string]interface{}, error) {
	db := tools.GetDB()
	const (
		SHIPPER_ADMIN     = 8 // 配送员管理员
		SHIPPER           = 9 // 配送员
		ORDER_STATE_TAKED = 1 // 订单是否已接单
		ADMIN_ACTIVATED   = 1 // 用户是否在正常状态
	)

	if admin.Type != 8 {
		return nil, errors.New("权限不够、您不是配送管理员")
	}
	var orderToday models.OrderToday
	result := db.Select("id, store_id, building_id").Where("id = ?", orderId).First(&orderToday)
	if result.Error != nil {
		return nil, errors.New(shipper.langUtil.T("order_not_found"))
	}

	restaurantID := orderToday.StoreID

	var shipperIDs []map[string]interface{}

	result = db.Model(models.Admin{}).
		Select("t_admin.id, t_admin.name,t_admin.real_name,t_admin.mobile,t_admin.avatar, COUNT(t_take_order.id) AS count").
		Joins("LEFT JOIN t_take_order ON t_take_order.admin_id = t_admin.id AND t_take_order.state = ? AND DATE(t_take_order.created_at) = ?",
			ORDER_STATE_TAKED, time.Now().Format("2006-01-02")).
		Where("t_admin.type = ? AND t_admin.parent_id LIKE ? AND t_admin.state = ? ", SHIPPER, "%,"+strconv.Itoa(int(admin.ID))+"%", ADMIN_ACTIVATED).
		Joins("INNER JOIN b_admin_store ON b_admin_store.admin_id = t_admin.id").
		Where("b_admin_store.store_id IN (?)", restaurantID).
		Group("t_admin.id").
		Scan(&shipperIDs)

	if result.Error != nil {
		return nil, result.Error
	}

	return shipperIDs, nil
}

// 描述：收入统计=>拼图(已完成,取消,失败,迟到),折线图(本月,本年,指定时间段),订单列表
// 作者：rozimamat
// 文件：ShipperService.go
// 修改时间：2023/11/15 12:15
func (shipper ShipperService) GetIncomeStatistic(admin models.Admin, month string) (shipperResources.IncomeStatisticsPieEntity, error) {


		db := tools.GetDB()

		startDate := carbon.Parse(carbon.ParseByFormat(month, "Y-m").Format("Y-m-d"))
		endDate := carbon.Parse(carbon.ParseByFormat(month, "Y-m").AddMonth().AddDays(-1).Format("Y-m-d"))

		var shipperIncomes []shipment.ShipperIncomeArchive
		db.Model(shipment.ShipperIncomeArchive{}).
			Where("t_shipper_income_archive.shipper_id =?", admin.ID).
			Where("t_shipper_income_archive.date between ? and ?", startDate, endDate).
			Select("t_shipper_income_archive.*,shipment+reward+punishment+other+amount_invite_user+amount_order_tips+amount_insurance as day_total").
			Scan(&shipperIncomes)
		var piEntity shipperResources.IncomeStatisticsPieEntity
		var piEntityPi []shipperResources.Pi
		var piEntityDetails []shipperResources.Details

		var piEntityHeaders []shipperResources.Details

		totalIncomeAmount := 0

		//这几天最高的记录
		detailTotalMax := 0
		//配送费合计
		shipmentAmount := 0
		//奖励合计
		rewardAmount := 0
		//惩罚合计
		punishmentAmount := 0
		//其他合计
		otherAmount := 0

		//拓展用户
		customerAmount := 0

		for _, inc := range shipperIncomes {

			piEntityDetails = append(piEntityDetails, shipperResources.Details{
				Day:       carbon.Parse(inc.Date).Format("Y-m-d"),
				Total:     inc.DayTotal,
				Shipment:  inc.Shipment,
				Reward:    inc.Reward,
				Deduction: inc.Punishment,
				Other:     inc.Other,
				Invite: inc.AmountInviteUser + inc.AmountOrderTips,
			})

			dayTotal := inc.DayTotal
			totalIncomeAmount += inc.DayTotal
			if dayTotal > detailTotalMax {
				detailTotalMax = dayTotal
			}
			//1：订单收入
			shipmentAmount += inc.Shipment

			rewardAmount += inc.Reward

			punishmentAmount += inc.Punishment

			otherAmount += inc.Other

			customerAmount +=inc.AmountInviteUser + inc.AmountOrderTips


		}
		piEntity.TotalIncomeAmount = totalIncomeAmount
		piEntity.DetailTotalMax = detailTotalMax
		piEntityPi = append(piEntityPi, shipperResources.Pi{
			Key:    "shipment", //配送费
			Title:  shipper.langUtil.TArr("ShipperStatisticsIncomeTypes")[1],
			Amount: shipmentAmount,
			Value:  shipmentAmount,
			Color:  "#4CAF50",
		})
		piEntityPi = append(piEntityPi, shipperResources.Pi{
			Key:    "other", //其他
			Title:  shipper.langUtil.TArr("ShipperStatisticsIncomeTypes")[4],
			Amount: otherAmount,
			Value:  otherAmount,
			Color:  "#2D7AFF",
		})

		piEntityPi = append(piEntityPi, shipperResources.Pi{
			Key:    "reward", //奖励
			Title:  shipper.langUtil.TArr("ShipperStatisticsIncomeTypes")[2],
			Amount: rewardAmount,
			Value:  rewardAmount,
			Color:  "#FF9800",
		})
		piEntityPi = append(piEntityPi, shipperResources.Pi{
			Key:    "deduction", //惩罚
			Title:  shipper.langUtil.TArr("ShipperStatisticsIncomeTypes")[3],
			Amount: punishmentAmount,
			Value:  punishmentAmount,
			Color:  "#F44336",
		})
		//customerAmount
		piEntityPi = append(piEntityPi, shipperResources.Pi{
			Key:    "customer", //客户拓展
			Title:  shipper.langUtil.TArr("ShipperStatisticsIncomeTypes")[5],
			Amount: customerAmount,
			Value:  customerAmount,
			Color:  "#4be790",
		})

		monthStart := carbon.Parse(carbon.ParseByFormat(month, "Y-m").Format("Y-m-d"))
		monthEnd := carbon.Parse(carbon.ParseByFormat(month, "Y-m").AddMonth().Format("Y-m-d"))

		monthLength := monthEnd.DiffAbsInDays(monthStart)

		days := []string{}

		for i := int(monthLength); i > 0; i-- {
			day := monthEnd.AddDays(-i).Format("Y-m-d")
			days = append(days, day)
		}

		if len(piEntityDetails) == 0 {

			for _, v := range days {
				piEntityHeaders = append(piEntityHeaders, shipperResources.Details{
					Day:       carbon.Parse(v).Format("Y-m-d"),
					Total:     0,
					Shipment:  0,
					Reward:    0,
					Deduction: 0,
					Other:     0,
				})
			}
		} else {
			for _, v := range days {
				flag := true
				for _, inc := range piEntityDetails {
					if inc.Day == v {
						flag = false
						inc.Day = carbon.Parse(v).Format("Y-m-d")
						piEntityHeaders = append(piEntityHeaders, inc)
					}
				}
				if flag {
					piEntityHeaders = append(piEntityHeaders, shipperResources.Details{
						Day:       carbon.Parse(v).Format("Y-m-d"),
						Total:     0,
						Shipment:  0,
						Reward:    0,
						Deduction: 0,
						Other:     0,
					})
				}
			}
		}

		piEntity.Pi = piEntityPi
		piEntity.Details = piEntityDetails
		piEntity.Headers = piEntityHeaders

		return piEntity, nil
}

// 描述：订单统计=>拼图(已完成,取消,失败,迟到),折线图(本月,本年,指定时间段),订单列表
// 作者：rozimamat
// 文件：ShipperService.go
// 修改时间：2023/11/16 12:15
func (shipper ShipperService) GetOrderStatistic(admin models.Admin, month string) (shipperResources.OrderStatisticsPieEntity, error) {

	

		db := tools.GetDB()

		startDate := carbon.Parse(carbon.ParseByFormat(month, "Y-m").Format("Y-m-d"))
		endDate := carbon.Parse(carbon.ParseByFormat(month, "Y-m").AddMonth().AddDays(-1).Format("Y-m-d"))

		var shipperOrders []shipment.ShipperIncomeArchive
		db.Model(shipment.ShipperIncomeArchive{}).
			Where("t_shipper_income_archive.shipper_id =?", admin.ID).
			Where("t_shipper_income_archive.date between ? and ?", startDate, endDate).
			Select("t_shipper_income_archive.*,success_count as day_total").
			Scan(&shipperOrders)
		var piEntity shipperResources.OrderStatisticsPieEntity
		var piEntityPi []shipperResources.OrderPi
		var piEntityDetails []shipperResources.OrderStatisticsDetails
		var piEntityHeaders []shipperResources.OrderStatisticsDetails

		//订单总数
		totalCount := 0
		//今日最大数值
		detailTotalMax := 0
		//成功数量
		successCount := 0
		//取消数量
		cancelCount := 0
		//失败数量数量
		failCount := 0
		//迟到数量
		lateCount := 0
		for _, inc := range shipperOrders {

			piEntityDetails = append(piEntityDetails, shipperResources.OrderStatisticsDetails{
				Id:          inc.ID,
				Day:         carbon.Parse(inc.Date).Format("Y-m-d"),
				Total:       tools.ToInt(inc.DayTotal),
				FinishCount: tools.ToInt(inc.Success),
				CancelCount: tools.ToInt(inc.Cancel),
				FailCount:   tools.ToInt(inc.Fail),
				LateCount:   tools.ToInt(inc.Late),
			})

			totalCount += inc.Success

			daySuccessTotal := inc.Success

			if daySuccessTotal > detailTotalMax {
				detailTotalMax = inc.Success
			}

			successCount += inc.Success

			cancelCount += inc.Cancel

			failCount += inc.Fail

			lateCount += inc.Late

		}
		piEntity.TotalOrderCount = totalCount
		piEntity.DetailTotalMax = tools.ToInt(detailTotalMax)
		piEntityPi = append(piEntityPi, shipperResources.OrderPi{
			Key:   "finish",
			Title: shipper.langUtil.TArr("ShipperStatisticsOrderTypes")[1],
			Count: successCount,
			Value: successCount,
			Color: "#4CAF50",
		})
		piEntityPi = append(piEntityPi, shipperResources.OrderPi{
			Key:   "late",
			Title: shipper.langUtil.TArr("ShipperStatisticsOrderTypes")[4],
			Count: lateCount,
			Value: lateCount,
			Color: "#FF9800",
		})
		piEntityPi = append(piEntityPi, shipperResources.OrderPi{
			Key:   "cancel",
			Title: shipper.langUtil.TArr("ShipperStatisticsOrderTypes")[2],
			Count: cancelCount,
			Value: cancelCount,
			Color: "#F44336",
		})
		piEntityPi = append(piEntityPi, shipperResources.OrderPi{
			Key:   "fail",
			Title: shipper.langUtil.TArr("ShipperStatisticsOrderTypes")[3],
			Count: failCount,
			Value: failCount,
			Color: "#BDBBBA",
		})

		monthStart := carbon.Parse(carbon.ParseByFormat(month, "Y-m").Format("Y-m-d"))
		monthEnd := carbon.Parse(carbon.ParseByFormat(month, "Y-m").AddMonth().Format("Y-m-d"))

		monthLength := monthEnd.DiffAbsInDays(monthStart)

		days := []string{}

		for i := int(monthLength); i > 0; i-- {
			day := monthEnd.AddDays(-i).Format("Y-m-d")
			days = append(days, day)
		}

		if len(piEntityDetails) == 0 {

			for _, v := range days {
				piEntityHeaders = append(piEntityHeaders, shipperResources.OrderStatisticsDetails{
					Id:          0,
					Day:         carbon.Parse(v).Format("Y-m-d"),
					Total:       0,
					FinishCount: 0,
					CancelCount: 0,
					FailCount:   0,
					LateCount:   0,
				})
			}
		} else {
			for _, v := range days {
				flag := true
				for _, inc := range piEntityDetails {
					if inc.Day == v {
						flag = false
						inc.Day = carbon.Parse(v).Format("Y-m-d")
						piEntityHeaders = append(piEntityHeaders, inc)
					}
				}
				if flag {
					piEntityHeaders = append(piEntityHeaders, shipperResources.OrderStatisticsDetails{
						Id:          0,
						Day:         carbon.Parse(v).Format("Y-m-d"),
						Total:       0,
						FinishCount: 0,
						CancelCount: 0,
						FailCount:   0,
						LateCount:   0,
					})
				}
			}
		}
		piEntity.Pi = piEntityPi
		piEntity.Details = piEntityDetails
		piEntity.Headers = piEntityHeaders

		return piEntity, nil
}

// 描述：我的页面总统计=>拼图(已完成,取消,失败,迟到),折线图(本月,本年,指定时间段),订单列表
// 作者：rozimamat
// 文件：ShipperService.go
// 修改时间：2023/11/16 12:15
func (shipper ShipperService) GetUserCenterStatistic(admin models.Admin) (shipperResources.UserCenterStatisticsPieEntity, error) {
	db := tools.GetDB()

	var piEntity shipperResources.UserCenterStatisticsPieEntity

	// 今日需要现金订单
	cashAmount := 0
	// cashStartDate :=carbon.Now(configs.AsiaShanghai).Format("Y-m-d")+" 00:00:00"
	// cashEndDate   :=carbon.Now(configs.AsiaShanghai).Format("Y-m-d")+" 23:59:59"
	var orderPrice int64
	sqlRaw := `
		SELECT
			IFNULL(sum( t_order_today.order_price),0) AS order_price
		FROM
			t_order_today
			LEFT JOIN t_take_order ON t_order_today.id = t_take_order.order_id 
		WHERE
			t_order_today.state =? 
			AND t_order_today.pay_type =? 
			AND t_take_order.state IN ( 3, 2 ) 
			AND t_order_today.consume_type IN ( 0, 3 ) 
			AND t_order_today.shipper_id =? 
			AND t_order_today.deleted_at IS NULL
			And t_order_today.cash_clear_state = 0 
	`
	db.Raw(sqlRaw, 7, 6, admin.ID).Scan(&orderPrice)
	cashAmount = tools.ToInt(orderPrice)
	//今天订单数量
	todayOrderCount := int64(0)
	db.Table("t_order_today").Select("id").
		Where("shipper_id=?", admin.ID).
		Where("`t_order_today`.`deleted_at` IS NULL").
		Where("state =?", 7).
		Count(&todayOrderCount)

	//今天退回的订单数量
	todayCancelOrderCount := 0

	now := carbon.Now(configs.AsiaShanghai)

	startDate := carbon.Now(configs.AsiaShanghai).AddDays(-7).Format("Y-m-d")
	endDate := now.Format("Y-m-d")

	var canceledOrders []models.TakeOrder
	//今天退回的订单
	db.Model(&models.TakeOrder{}).
		Preload("PushDetail", func(db *gorm.DB) *gorm.DB {
			return db.Where("t_shipper_order_push_detail.shipper_id = ?", admin.ID)
		}).
		Where("t_take_order.admin_id=?", admin.ID). //本配送员 退回的话 t_order_today 中的shipper_id 会改变
		Where("t_take_order.state  = ? ", 0).       //本配送员退回的
		Where("t_take_order.store_id > ?", 0).
		Where("t_take_order.created_at between ? and ?", now.Format("Y-m-d")+" 00:00:00", now.Format("Y-m-d")+" 23:59:59").
		Group("order_id").
		Find(&canceledOrders)

	todayCancelOrderCount = len(canceledOrders)

	//今日收入 预计
	todayIncomeAmount := 0
	//少赚多少
	todayIncomeLess := 0

	for _, canceled := range canceledOrders {
		if canceled.PushDetail.ID > 0 {
			//取消的话 算少赚
			todayIncomeLess += canceled.PushDetail.EstimatedShippingPrice
		}
	}

	//今天对比昨天的增长率
	todayIncomeRate := float64(0)

	var orderModel []models.OrderToday
	db.Table("t_order_today").
		Where("t_order_today.shipper_id=?", admin.ID).
		Where("`t_order_today`.`deleted_at` IS NULL").
		Where("t_order_today.state =?", 7).
		Preload("PushDetail", func(db *gorm.DB) *gorm.DB {
			return db.Where("t_shipper_order_push_detail.shipper_id = ?", admin.ID)
		}).
		Find(&orderModel)
	for _, o := range orderModel {
		if o.PushDetail.ID > 0 {
			todayIncomeAmount += o.PushDetail.EstimatedShippingPrice
		}
	}

	//一周内的 收入详细
	var incomeStatistic []shipperResources.UserCenterIncome
	yesterday := carbon.Now(configs.AsiaShanghai).AddDays(-1).Format("Y-m-d")
	yesterAmount := 0 //昨天赚的钱

	lateOrderCount := int64(0) //今天的迟到订单

	db.Model(&models.LateOrders{}).Where("shipper_id = ? and booking_time between ? and ?", admin.ID, now.Format("Y-m-d")+" 00:00:00", now.Format("Y-m-d")+" 23:59:59").Count(&lateOrderCount)


	

	today := carbon.Now(configs.AsiaShanghai).Format("Y-m-d")

	todayStart, todayEnd := tools.GetTodayStartAndEnd()

	type ShipperCustomerIncome struct {
		CustomerCount int64 
		TodayCustomerOrderIncome int64
	}
	var shipperCustomerIncome ShipperCustomerIncome

	db.Model(&models.AdvertMaterialShipperDailyStatistic{}).
			Where("shipper_id = ? and date = ? ", admin.ID, today).
			Select("IF(ISNULL(sum(invite_user_fee+total_order_tips_fee)),0,sum(invite_user_fee+total_order_tips_fee)) as today_customer_order_income,sum(invite_user_count) as customer_count").
			Scan(&shipperCustomerIncome)

	var todayTipAmount int64 
	db.Model(&models.ShipperTips{}).Where("shipper_id = ? and created_at between ? and ? ", admin.ID, todayStart, todayEnd).
		Where("state = ? ",1).
		Pluck("IF(ISNULL(sum(amount)),0,sum(amount)) as amount",&todayTipAmount)		

	days := []string{}
	for i := 7; i > 0; i-- {
		day := now.AddDays(-i).Format("Y-m-d")
		days = append(days, day)
	}

	var shipperIncomes []shipment.ShipperIncomeArchive
	db.Model(shipment.ShipperIncomeArchive{}).
		Where("t_shipper_income_archive.shipper_id =?", admin.ID).
		Where("t_shipper_income_archive.date between ? and ?", startDate, endDate).
		Select("t_shipper_income_archive.id,t_shipper_income_archive.date,t_shipper_income_archive.success_count,shipment+reward+punishment+other+amount_invite_user+amount_order_tips+amount_insurance as day_total").
		Find(&shipperIncomes)
	day := ""
	for _, inc := range shipperIncomes {
		day = carbon.Parse(inc.Date).Format("Y-m-d")
		incomeStatistic = append(incomeStatistic, shipperResources.UserCenterIncome{
			Day:    day,
			Amount: inc.DayTotal,
			Count:  inc.Success,
		})
		if yesterday == day { //昨天收入
			yesterAmount = inc.DayTotal
		}

	}
	if todayIncomeAmount != 0 || yesterAmount != 0 {
		todayIncomeRate = tools.GrowthRate(todayIncomeAmount, yesterAmount)
	}

	if len(incomeStatistic) == 0 {
		for _, v := range days {
			incomeStatistic = append(incomeStatistic, shipperResources.UserCenterIncome{
				Day:    carbon.Parse(v).Format("m-d"),
				Amount: 0,
				Count:  0,
			})
		}
	} else {
		var incomeStatisticCombine []shipperResources.UserCenterIncome
		for _, v := range days {
			flag := true
			for _, inc := range incomeStatistic {

				if inc.Day == v {
					flag = false
					incomeStatisticCombine = append(incomeStatisticCombine, shipperResources.UserCenterIncome{
						Day:    carbon.Parse(v).Format("m-d"),
						Amount: inc.Amount,
						Count:  inc.Count,
					})
				}

			}
			if flag {
				incomeStatisticCombine = append(incomeStatisticCombine, shipperResources.UserCenterIncome{
					Day:    carbon.Parse(v).Format("m-d"),
					Amount: 0,
					Count:  0,
				})
			}

		}
		incomeStatistic = incomeStatisticCombine
	}

	piEntity.TodayCashAmount = cashAmount
	avatar := ""
	if len(admin.Avatar) > 0 {
		avatar = configs.MyApp.CdnUrl + admin.Avatar
	}
	var adminWithOpenID models.Admin
	db.Model(&models.Admin{}).Where("id = ?", admin.ID).First(&adminWithOpenID)
	
	
	rank,levelUpRate,_,_,_,_:=shipper.GetShipperRankGrowthRate(&admin)
	

	piEntity.User = shipperResources.UserCenterUser{
		Id:       admin.ID,
		Avatar:   avatar,
		Name:     admin.Name,
		RealName: admin.RealName,
		Mobile:   admin.Mobile,
		Openid:   &adminWithOpenID.Openid,
		Rank:rank,
		LevelUpRate: levelUpRate,
	}
	println(adminWithOpenID.Openid)
	if *piEntity.User.Openid == "" {
		piEntity.User.Openid = nil
	}
	piEntity.TodayOrderCount = todayOrderCount
	piEntity.TodayCancelOrderCount = todayCancelOrderCount
	piEntity.TodayIncomeAmount = todayIncomeAmount
	piEntity.TodayIncomeLess = todayIncomeLess

	piEntity.TodayCustomerCount = shipperCustomerIncome.CustomerCount
	piEntity.TodayCustomerOrderIncome = shipperCustomerIncome.TodayCustomerOrderIncome
	piEntity.TodayTipAmount = todayTipAmount
	piEntity.ShowAdImage = int(configs.MyApp.ShowShipperIntroduceAd)
	introduceImgUrl := configs.MyApp.ShipperIntroduceAdImageUgUrl
	if shipper.language != "ug" {
		introduceImgUrl = configs.MyApp.ShipperIntroduceAdImageZhUrl
	}
	piEntity.IntroduceAdImage = tools.AddCdn(introduceImgUrl)

	piEntity.TodayIncomeRate = tools.ToInt(todayIncomeRate * 100)

	piEntity.IncomeStatistic = incomeStatistic
	piEntity.LateOrderCount = lateOrderCount

	return piEntity, nil
}

// GetAttendanceListCommon
//
// @Description: 获取考勤列表（打卡，请假，事故）
// @Author: Rixat
// @Time: 2023-11-18 07:38:52
// @receiver
// @param c *gin.Context
func (shipper ShipperService) GetAttendanceListCommon(admin models.Admin, attendanceType int, date string) []models.ShipperAttendanceLog {
	var attendanceList []models.ShipperAttendanceLog
	query := tools.Db.Model(attendanceList).Where("shipper_id=?", admin.ID)
	if attendanceType == 1 { // 打卡
		query.Where("state in (1,2,3)")
	}
	if attendanceType == 2 { // 请假
		query.Where("state = 4")
	}
	if attendanceType == 3 { // 事故
		query.Where("state = 5")
	}
	if len(date) > 0 {
		query.Where("created_at between ? and ?", carbon.Parse(date), carbon.Parse(date).AddDay().SubSecond())
	}
	query.Order("id desc").Scan(&attendanceList)
	return attendanceList
}

// GetAttendanceListCommon
//
// @Description: 获取考勤列表（打卡，请假，事故）
// @Author: Rixat
// @Time: 2023-11-18 07:38:52
// @receiver
// @param c *gin.Context
func (shipper ShipperService) GetAttendanceDetailCommon(admin models.Admin, attendanceType int, ID int) models.ShipperAttendanceLog {
	var attendance models.ShipperAttendanceLog
	tools.Db.Model(attendance).Where("shipper_id=?", admin.ID).Where("id=?", ID).First(&attendance)
	return attendance
}

// GetAllShipperList
//
//	@Description: 获取配送管理员手下配送员列表
//	@receiver shipper
//	@param admin
//	@return []shipment.ShipperAdminAll
//	@return error
func (shipper ShipperService) GetAllShipperList(admin models.Admin) ([]shipment.ShipperAdminAll, error) {

	db := tools.Db
	if err := db.Model(&admin).Association("Areas").Find(&admin.Areas); err != nil {
		return nil, err
	}
	if admin.Areas == nil {
		return nil, errors.New("您没有分配的区域")
	}
	var areaIds []int
	for _, area := range admin.Areas {
		areaIds = append(areaIds, area.ID)
	}
	var shipperList []shipment.ShipperAdminAll
	var ids = []int{}
	//先获取同一区域的配送员，优化查询，直接like 比较慢，先按区域查询
	db.Table("admin_areas").Joins("LEFT JOIN t_admin on t_admin.id = admin_areas.admin_id").
		Where("admin_areas.area_id in ?", areaIds).
		Where("t_admin.state = 1").
		Where("t_admin.deleted_at IS NULL").
		Where("t_admin.type = ?", models.AdminTypeShipper).
		Pluck("admin_id", &ids)
	//查询配送员及其手上的单子
	err := db.Model(&models.Admin{}).
		Preload("SuccessOrder", func(db *gorm.DB) *gorm.DB {
			return db.Where("t_order_today.state = ?", 7)
		}).
		Preload("CurrentOrder", func(db *gorm.DB) *gorm.DB {
			return db.Where("t_order_today.state in (3,4,5,6)")
		}).
		Where("id in ?", ids).
		Where("state = 1").
		Where("type = ?", models.AdminTypeShipper).
		Where(fmt.Sprintf("parent_id like '%%%d%%'", admin.ID)).
		Find(&shipperList).Error
	if err != nil {
		return nil, err
	}
	return shipperList, nil
}

// GetHomeNotify
//
// @Description: 获取配送端首页通知
// @Author: Rixat
// @Time: 2023-12-02 03:38:15
// @receiver
// @param c *gin.Context
func (shipper ShipperService) GetHomeNotify(admin models.Admin) map[string]interface{} {
	var notify shipment.ShipperNotify
	tools.Db.Model(notify).Where("area_id = ?", admin.AdminAreaID).Where("state > 1").First(&notify)
	if notify.ID > 0 {
		return map[string]interface{}{
			"id":         notify.ID,
			"send_count": notify.SendCount,
			"title":      tools.GetNameByLangAndColumn(notify, shipper.language, "Title"),
			"content":    tools.GetNameByLangAndColumn(notify, shipper.language, "Content"),
		}
	}
	return nil
}

// 描述：查询黑名单中的应用列表
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2024/07/16 17:47
func (shipper ShipperService) GetAppBlackList(admin models.Admin,packages string) ([]Other.BlackListApp,[]Other.BlackListApp, error) {
	// var blackList []Other.TAppBlacklist
	var blackList []Other.BlackListApp
	var existingPackages []Other.BlackListApp
	// 
	// tools.Logger.Infof("查询APP黑名单: userid :%d , 应用列表:%s", admin.ID, packages)

	// 查询状态为1的黑名单列表
	if err := tools.Db.Model(&Other.TAppBlacklist{}).Where("state = ?", 1).Where("deleted_at is null").Select("id,app_name,package_name, platform").Find(&blackList).Error; err != nil {
		return nil, nil,err
	}
	// 将提交的包名字符串按逗号分割为数组
	packageNames := strings.Split(packages, ",")

	// 遍历黑名单列表和提交的包名数组，找出匹配的黑名单项
	for _, packageName := range packageNames {
		for _, item := range blackList {
			if item.PackageName == packageName {
				existingPackages = append(existingPackages, item)
				break // 找到匹配项后跳出内层循环
			}
		}
	}
	return blackList,existingPackages, nil
}

// 描述：返回所有黑名单中的app列表
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2024/07/18 18:10
func (shipper ShipperService) GetBlackListApps() ([]Other.BlackListApp,error){
	var blackList []Other.BlackListApp
	if err := tools.Db.Model(&Other.TAppBlacklist{}).Where("state = ?", 1).Where("deleted_at is null").Select("id,app_name,package_name, platform").Find(&blackList).Error; err != nil {
		return nil, err
	}
	return blackList, nil
}

// 描述：获取收入统计详情
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2024/07/09 15:48
func (shipper ShipperService) GetIncomeDetail(ctx *gin.Context, admin models.Admin, month string, incomeType int) (int64, []shipment.ShipperIncome, error) {
	startDate, endDate := tools.GetMonthStartAndEndWithParam(month, "Y-m")
	var incomeList []shipment.ShipperIncome
	var totalCount int64
	incomeQuery := tools.Db.Model(incomeList).Where("type not in(10,11,14,15)") // 失败订单和取消订单只做记录，不扣款，所以不需要查询
	if admin.ID > 0 {
		incomeQuery.Where("shipper_id = ?", admin.ID)
	}

	if incomeType > 0 {
		incomeQuery.Where("type = ?", incomeType)
	}

	pagination := scopes.Paginate(ctx)

	if len(startDate) > 0 && len(endDate) > 0 {
		incomeQuery.Where("date between ? and ?", startDate, endDate)
	}
	incomeQuery.Count(&totalCount)
	incomeQuery.Preload("Order.Restaurant")

	incomeQuery.Scopes(pagination).Find(&incomeList)
	return totalCount, incomeList, nil
}

// 描述：获取配送员收入详情头部
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2024/07/09 17:44
func (s ShipperService) DetailHeader(ShipperID int, month string) (*shipment.ShipperIncomeSummary, error) {
	var shipper models.Admin
	tools.Db.Model(shipper).Where("id = ?", ShipperID).First(&shipper)
	if shipper.ID == 0 {
		return nil, errors.New("not_found")
	}
	// 本月和上月时间格式化
	startDate, endDate := tools.GetMonthStartAndEndWithParam(month, "Y-m")
	var shipperIncomeSummary shipment.ShipperIncomeSummary
	selectFields := `
		avg( comment_star ) as comment_star,
		avg( order_delivery_minute ) AS order_delivery_minute,
		count( id ) AS work_day,
		sum( reward + punishment + other + shipment +amount_invite_user + amount_order_tips+amount_insurance) AS total_salary,
		sum( success_count) AS total_order_count,
		sum( complain_count ) AS complain_count,
		sum( count_comment_bad ) AS count_comment_bad,
		sum( late_count ) AS late_count,
		sum( amount_reward ) AS amount_reward,
		sum( amount_tips ) AS amount_tips,
		sum( amount_special_time ) AS amount_special_time,
		sum( amount_special_weather ) AS amount_special_weather,
		sum( amount_comment_good ) AS amount_comment_good,
		sum( amount_comment_bad ) AS amount_comment_bad,
		sum( amount_order ) AS amount_order,
		sum( amount_complain ) AS amount_complain,
		sum( amount_late ) AS amount_late,
		sum( punishment ) AS punishment,
		sum(amount_invite_user) as amount_invite_user,
		sum(amount_order_tips) as amount_order_tips,
		sum( punishment ) AS punishment,
		sum(amount_special_price_order) as amount_special_price_order,
		sum(count_special_price_order) as count_special_price_order,
		sum(amount_insurance) as amount_insurance
		`
	tools.Db.Model(shipment.ShipperIncomeArchive{}).
		Select(selectFields).Where("shipper_id=?", ShipperID).
		Where("date between ? and ?", startDate, endDate).
		Scan(&shipperIncomeSummary)

	// 排名	总工资排序
	rankRes := make([]map[string]interface{}, 0)
	tools.Db.Model(shipment.ShipperIncomeArchive{}).
		Select("shipper_id,sum(reward + punishment + other + shipment +amount_invite_user + amount_order_tips+amount_insurance) salary").
		Where("area_id=?", shipper.AdminAreaID).
		Where("date between ? and ?", startDate, endDate).
		Group("shipper_id").
		Order("salary desc").
		Scan(&rankRes)
	return &shipperIncomeSummary,nil
}

func (shipper ShipperService) UpdateShowAdImageByAppConfig(c *gin.Context, s *shipperResources.UserCenterStatisticsPieEntity) {
	osTypeParam := c.GetHeader("ostype")
	versionName := c.GetHeader("version-name")
	osType := "android"
	if osTypeParam == "2"{
		osType = "ios"
	}
	s.ShowAdImage=int(tools.ToInt64(tools.NewAppConfigTool().GetConfigValue("ios_app_income_show", osType, versionName, "1")))
}
// GetCustomerAddress
//  @Description: 获取用户地址
//  @receiver shipper
//  @param orderId
//  @return []shipperResources.OrderCustomerAddResource
//  @return error
//
func (shipper ShipperService) GetCustomerAddress(orderId int64) (models.OrderToday,[]models.UserBuilding, error) {
	var orderToday models.OrderToday
	//用户历史地址
	var userBuildings []models.UserBuilding

	//  用户收货地址
	if err := tools.Db.Model(&models.OrderToday{}).
		Where("id = ?", orderId).
		Preload("Building").
		Preload("User").
		Preload("Building.Area").
		Preload("Building.City").
		Preload("OrderExtend").
		First(&orderToday).Error; err != nil {
		return orderToday,userBuildings, errors.New("order_not_found")
	}

	tools.Db.Model(&models.UserBuilding{}).Where("user_id = ?", orderToday.UserID).
		Preload("Building").
		Preload("Building.Area").
		Preload("Building.City").
		Limit(5).
		Find(&userBuildings)

	return orderToday,userBuildings,nil
}



// 获取审核状态数据失败
func (s ShipperService) CheckInsuranceState(shipper_id int, cityId int, areaId int) (data map[string]interface{}, errMsg string) {
	db := tools.Db

	var inf resources.CheckInfo
	fields := "id,reg_mer_type"
	fields += ",mer_idcard_num,bank_acct_type,bank_acct_num"
	fields += ",bank_acct_name,bank_id,verify_content,state,verify_amount,ums_reg_id"
	fields += ",bank_city_id"
	fields += ",city_id,area_id"
	fields += ",legal_name"
	fields += ",bank_id,bank_acct_name"
	fields += ",state,alter_state"
	fields += ",mer_idcard_name,mer_idcard_num,mer_idcard_start,mer_idcard_end"
	fields += ",mer_mobile,mer_sex"
	fields += ",submit_type"
	findErr := db.Model(models.SelfSignMerchantInfo{}).Select(fields).Where("restaurant_id = ? ", shipper_id).Where("type = ?",constants.SELF_SIGN_TYPE_SHIPPER).Find(&inf).Error

	if findErr != nil {
		tools.Logger.Info("获取审核状态数据失败", findErr.Error())
		errMsg = "data_not_found"
		return

	}
	var m []map[string]interface{}
	data = make(map[string]interface{})

	if inf.Id == 0 {
		//没有数据的话 给他一个 个体工商户 的数据
		lmtType := 1
		// 个体工商户
		SelfSignMerchantInfo := models.SelfSignMerchantInfo{
			Type:           		constants.SELF_SIGN_TYPE_SHIPPER,
			RestaurantId:           shipper_id, // 餐厅id
			CityId:                 cityId,
			AreaId:                 areaId,
			RegMerType:             "02", // 注册商户类型
			ShopLicenseLimitedType: &lmtType,
			VerifyContent:          "[]",
			SubmitType:             2, //拉卡拉
		}
		db.Create(&SelfSignMerchantInfo)
		db.Model(models.SelfSignMerchantInfo{}).Select(fields).Where("restaurant_id = ? ", shipper_id).Where("type = ?",constants.SELF_SIGN_TYPE_SHIPPER).Find(&inf)
	}

	err := json.Unmarshal([]byte(inf.VerifyContent), &m)
	if err != nil {
		tools.Logger.Info("获取审核状态 json 解析失败", err.Error())
		data["verify_content"] = make([]string, 0)

	} else {
		data["verify_content"] = m
	}

	data["state"] = inf.State
	data["legal_name"]= inf.LegalName
	data["mer_idcard_num"]=inf.MerIdcardNum
	data["mer_mobile"] = inf.MerMobile
	data["bank_acct_num"] = inf.BankAcctNum
	bankName := ""

	var collectionBank models.SelfSignBank
	db.Model(models.SelfSignBank{}).
		Where("id = ?", inf.BankId).
		First(&collectionBank)

	if s.langUtil.Lang == "ug" {
		bankName = collectionBank.NameUg
	} else {
		bankName = collectionBank.NameZh
	}
	data["bank_acct_name"] = bankName
	data["reg_mer_type"] = inf.RegMerType

	if inf.State == 0 {
		var zeroContent []map[string]interface{}
		zeroState := make(map[string]interface{}, 0)

		zeroState["step_num"] = 2 //身份证信息
		if len(inf.MerIdcardNum) == 0 {
			zeroState["is_done"] = 1
			zeroState["inputs"] = make([]map[string]interface{}, 0)
		} else {
			zeroState["is_done"] = 2
			zeroState["inputs"] = make([]map[string]interface{}, 0)
		}
		zeroContent = append(zeroContent, zeroState)

		zeroState = make(map[string]interface{}, 0)

		zeroState["step_num"] = 3                                                                              //结算账户信息
		if len(inf.BankAcctNum) == 0 || len(inf.BankAcctName) == 0 || inf.BankId == 0 || inf.BankCityId == 0 { //账户信息 {
			zeroState["is_done"] = 1
			zeroState["inputs"] = make([]map[string]interface{}, 0)
		} else {
			zeroState["is_done"] = 2
			zeroState["inputs"] = make([]map[string]interface{}, 0)
		}
		zeroContent = append(zeroContent, zeroState)
		if inf.RegMerType == "00" || inf.RegMerType == "01" {
			zeroState = make(map[string]interface{}, 0)
			zeroState["step_num"] = 1 //营业执照
			if len(inf.ShopName) == 0 {
				zeroState["is_done"] = 1
				zeroState["inputs"] = make([]map[string]interface{}, 0)
			} else {
				zeroState["is_done"] = 2
				zeroState["inputs"] = make([]map[string]interface{}, 0)
			}
			zeroContent = append(zeroContent, zeroState)

			zeroState = make(map[string]interface{}, 0)
			zeroState["step_num"] = 6 //许可证
			if len(inf.PermitLicNum) == 0 {
				zeroState["is_done"] = 1
				zeroState["inputs"] = make([]map[string]interface{}, 0)
			} else {
				zeroState["is_done"] = 2
				zeroState["inputs"] = make([]map[string]interface{}, 0)
			}
			zeroContent = append(zeroContent, zeroState)
		} else {
			zeroState = make(map[string]interface{}, 0)
			zeroState["step_num"] = 1 //营业执照
			if len(inf.ShopName) == 0 {
				zeroState["is_done"] = 1
				zeroState["inputs"] = make([]map[string]interface{}, 0)
			} else {
				zeroState["is_done"] = 2
				zeroState["inputs"] = make([]map[string]interface{}, 0)
			}
			zeroContent = append(zeroContent, zeroState)
		}
		zeroState = make(map[string]interface{}, 0)
		zeroState["step_num"] = 4 //商家信息
		if len(inf.ShopBusinessName) == 0 || len(inf.ShopBusinessCityID) == 0 || len(inf.ShopCategoryCode) == 0 {
			zeroState["is_done"] = 1
			zeroState["inputs"] = make([]map[string]interface{}, 0)
		} else {
			zeroState["is_done"] = 2
			zeroState["inputs"] = make([]map[string]interface{}, 0)
		}
		zeroContent = append(zeroContent, zeroState)

		data["verify_content"] = zeroContent
	}
	data["information_id"] = inf.Id
	data["submit_type"] = inf.SubmitType
	date :=carbon.Now(configs.AsiaShanghai).AddDays(1).Format("Y-m-d")
	var ins models.ShipperInsuranceLog
	db.Model(&models.ShipperInsuranceLog{}).Where("shipper_id = ?",shipper_id).Where("date = ?",date).Find(&ins)
	insuranceState :=-1
	insConfig :=s.GetInsuranceConfig()
	insuranceAmount :=insConfig.Amount
	if ins.ID > 0 {
		insuranceState = ins.State
	}
	data["insurance_state"] = insuranceState
	data["insurance_amount"] = insuranceAmount
	data["real_name_alert"] = s.langUtil.T("real_name_alert")
	
	type InsuranceNotice struct {
		Number int `json:"number"`
		Title string `json:"title"`
		Content string `json:"content"`
		OkBtn  string `json:"ok_btn"`
		CancelBtn string `json:"cancel_btn"`
	}

	realNameNoticeMap :=s.langUtil.TArrMap("real_name_alert_info")
	insuranceNoticeMap :=s.langUtil.TArrMap("insurance_alert_info")

	var insuranceItems []InsuranceNotice
	insuranceStopTime :=s.BaseService.GetAppConfig("insurance_stop_time")
	originalStopTime :=configs.MyApp.InsuranceStopTime
	newTime :=originalStopTime
	if len(insuranceStopTime) > 0 {
		newTime =insuranceStopTime
	}
	for _,v :=range insuranceNoticeMap {
		content :=tools.ToString(v["content"])
		content = strings.Replace(content,"23:00",newTime,-1)
		insuranceItems = append(insuranceItems,InsuranceNotice{
			Number:tools.ToInt(v["number"]),
			Title:tools.ToString(v["title"]),
			Content:content,
			OkBtn:tools.ToString(v["ok_btn"]),
			CancelBtn:tools.ToString(v["cancel_btn"]),
		})
	}
	insBytes,_ :=json.Marshal(insuranceItems)
	json.Unmarshal(insBytes,&insuranceNoticeMap)


	data["alert_info"]=map[string]interface{}{
		"real_name":realNameNoticeMap,
		"insurance":insuranceNoticeMap, 
	}
	areaInsured :=0
	insured :=s.AreaInsured(inf.AreaId,false)
	if insured {
		areaInsured =1
	}
	data["area_insured"]=areaInsured

	return data, errMsg
}


//获取参保信息
func (s ShipperService) GetInsuranceInfo(shipperId int) (data map[string]interface{}) {
	
	db :=tools.GetDB()
	var selfSignInfo models.SelfSignMerchantInfo
	db.Model(selfSignInfo).Where("restaurant_id=? and type = ?",shipperId,2).
	Preload("BankProvince").
	Preload("BankCity").
	Preload("BankArea").
	// Preload("City").
	// Preload("Area").
	Preload("Bank").
	Preload("SelfSignImages","type=2 and deleted_at is null").
	Find(&selfSignInfo)

	// 格式化返回内容
	stateNAme :=s.langUtil.TArr("real_name_states")[selfSignInfo.State]

	idLimitType := 0

	idcardEnd := ""
	if selfSignInfo.MerIdcardEnd !=nil {
		idcardEnd = tools.ToTime(selfSignInfo.MerIdcardEnd).Format("2006-01-02")
		if idcardEnd == "9999-12-31" {
			idLimitType = 1
			idcardEnd = "长期"
		}
	}
	data = map[string]interface{}{
		"id":selfSignInfo.Id,
		"created_at":tools.TimeFormatYmdHis(&selfSignInfo.CreatedAt),
		
		"state":selfSignInfo.State,
		"state_name":stateNAme,
		"alter_state":selfSignInfo.AlterState,
		"alter_state_name":s.langUtil.TArr("collection_merchant_alter_state")[selfSignInfo.AlterState],
		
		// 身份证信息
		"idcard_info":map[string]interface{}{
			"idcard_image_front":s.GetImageFullUrlByDocType(selfSignInfo.SelfSignImages,"0001"),
			"idcard_image_back":s.GetImageFullUrlByDocType(selfSignInfo.SelfSignImages,"0011"),
			"idcard_name":selfSignInfo.MerIdcardName,
			"idcard_num":selfSignInfo.MerIdcardNum,
			"idcard_start":tools.TimeFormatYmd(selfSignInfo.MerIdcardStart),
			"idcard_end":idcardEnd,
			"sex":selfSignInfo.MerSex,
			"address":selfSignInfo.LegalmanHomeAddr,
			"idcard_limited_type":idLimitType,
		},
		// 账户信息
		"account_info":map[string]interface{}{
			"bankcard_image_front":s.GetImageFullUrlByDocType(selfSignInfo.SelfSignImages,"0025"),
			"bankcard_image_back":s.GetImageFullUrlByDocType(selfSignInfo.SelfSignImages,"0026"),
			"bankcard_num":selfSignInfo.BankAcctNum,
			"bank_name":tools.GetNameByLang(selfSignInfo.Bank,s.language),
			"bank_name_zh":selfSignInfo.Bank.NameZh,
			"bank_branch_name":selfSignInfo.BankBranchName,
			"bank_branch_code":selfSignInfo.BankBranchCode,
			"bank_bind_mobile":selfSignInfo.BankBindMobile,
			"bank_province_name":tools.GetNameByLang(selfSignInfo.BankProvince,s.language),
			"bank_city_name":tools.GetNameByLang(selfSignInfo.BankCity,s.language),
			"bank_area_name":tools.GetNameByLang(selfSignInfo.BankArea,s.language),
			"bank_province_code":selfSignInfo.BankProvince.Code,
			"bank_province_id":selfSignInfo.BankProvinceId,
			"bank_city_id":selfSignInfo.BankCityId,
			"bank_area_id":selfSignInfo.BankAreaId,
			"bank_id":selfSignInfo.BankId,
		},
	}


	return

}
//获取身份认证
func (s ShipperService) GetIdCardInfo(shipperId int) (models.SelfSignMerchantInfo, []models.SelfSignImages) {
	db := tools.Db
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).
	    Where("type = ?",constants.SELF_SIGN_TYPE_SHIPPER).
		Where("restaurant_id = ?", shipperId).
		First(&SelfSignMerchantInfo)
	// 获取身份证照片
	var idcardsImg []models.SelfSignImages
	db.Model(models.SelfSignImages{}).Where("restaurant_id = ?", shipperId).Where("type = ?",constants.SELF_SIGN_TYPE_SHIPPER).
		Where("doc_type IN ?", []string{"0001", "0011", "0007"}).Find(&idcardsImg)
	return SelfSignMerchantInfo, idcardsImg
}
//获取实名认证审核状态
func (s ShipperService) CheckColMerInfoState(shipperId int) (bool, int) {
	//0:未提交, //1:待审核 , //2:审核未通过 , //3:后台审核通过 , //4:待提交资料 , //5:已提交资料 ,
	//6:资料未通过, //7:对公账户待确认, //8:待在线签约 , //9:待银商入网审核 , //10:银商入网成功, //11:银商入网失败,
	db := tools.Db
	var merInfo models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id = ?", shipperId).Where("type = ?",constants.SELF_SIGN_TYPE_SHIPPER).First(&merInfo)
	if merInfo.State == 0 || merInfo.State == 2 {
		return true, 0
	} else {
		return false, merInfo.State
	}

}

//存储 身份证信息
func (ums ShipperService) SaveIdCardInfo(shipperId int, IdcardImg string, MerIdcardName string, MerIdcardNum string, MerIdcardStart string, MerIdcardEnd string,  MerIdcardLimitedType string,  merSex int,merAddress string) (bool, string) {
	if MerIdcardLimitedType == "1" { // 短期
		MerIdcardEnd = "9999-12-31"
	}
	db := tools.Db
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tools.Logger.Error("FATAL","配送员实名认证信息[身份证]存储失败",r)
			tx.Rollback()
		}
	}()
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	tx.Where("restaurant_id = ?", shipperId).Where("type = ?",constants.SELF_SIGN_TYPE_SHIPPER).First(&SelfSignMerchantInfo)

	
	// 修改
	midStart := carbon.ParseByFormat(MerIdcardStart, "Y-m-d").Carbon2Time()
	midEnd := carbon.ParseByFormat(MerIdcardEnd, "Y-m-d").Carbon2Time()
	if len(MerIdcardEnd) == 0 || MerIdcardEnd == "长期"{
		midEnd = carbon.ParseByFormat("9999-12-31", "Y-m-d").Carbon2Time()
	}
	updateContent := models.SelfSignMerchantInfo{

		MerIdcardName:    MerIdcardName,
		MerIdcardNum:     MerIdcardNum,
		MerIdcardStart:   &midStart,
		MerIdcardEnd:     &midEnd,
		// MerMobile:        MerMobile,
		// LegalmanHomeAddr: legalHomeAddress,
		MerSex:           &merSex,
		// LegalName:        MerIdcardName,
		LegalmanHomeAddr: merAddress,
	}

	if SelfSignMerchantInfo.RegMerType == "02" {
		updateContent.LegalName = MerIdcardName
	}
	
	updateErr := tx.Model(&SelfSignMerchantInfo).Updates(updateContent).Error
	if updateErr != nil {
		tx.Rollback()
		tools.Logger.Info("修改商户信息失败", updateErr.Error())
		return false, "server_error"
	}
	ImageSaveErr := ums.savaImageByshipperIdAndDocType(tx, shipperId, IdcardImg)
	if ImageSaveErr != nil {
		tx.Rollback()
		tools.Logger.Info("保存身份证失败", ImageSaveErr.Error())
		return false, "server_error"
	}
	tx.Commit()
	return true, ""
}

//获取图片
func (t ShipperService) GetImageFullUrlByDocType(images []models.SelfSignImages,docType string) string {
	for _,image := range images {
		if image.DocType == docType {
			return tools.AddCdn(image.MlzFilePath)
		}
	}
	return ""
}
func (t ShipperService) SaveAccountInfo(shipperId int, AcctImg string, bankAcctType int, bankAcctNum string, bankId int, bankCityId int, bankAreaId int, bankBranchName string, bankBranchCode string, bankProvinceId int, bankBindMobile string) (bool, string) {
	//添加redis 锁，防止多次点击
	lockKey := "account_post_lock_" + strconv.Itoa(shipperId)
	isNoLock, err := tools.GetRedisHelper().SetNX(context.Background(), lockKey, "1", 120*time.Second).Result()
	if (!isNoLock) || (err != nil) {
		tools.Logger.Info("存在120秒内的实名认证发送记录 " + strconv.Itoa(shipperId))
		return false, fmt.Sprintf(t.langUtil.T("retry_after_second"), 120)
	}
	db := tools.Db
	var info models.SelfSignMerchantInfo
	db.Where("restaurant_id = ?", shipperId).Where("type = ?",constants.SELF_SIGN_TYPE_SHIPPER).First(&info)
	if len(info.MerIdcardNum)  == 0 {
		return false, "id_card_info_not_exist"
	} 
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			tools.Logger.Error("提交银行卡信息出错resID:", shipperId)
		}
	}()
	var SelfSignMerchantInfo models.SelfSignMerchantInfo
	tx.Where("restaurant_id = ?", shipperId).Where("type = ?",constants.SELF_SIGN_TYPE_SHIPPER).First(&SelfSignMerchantInfo)
	if SelfSignMerchantInfo.BankAcctType == nil {
		SelfSignMerchantInfo.BankAcctType = new(int)
	}

	updateContent := models.SelfSignMerchantInfo{

		BankAcctType:   &bankAcctType,
		BankAcctNum:    bankAcctNum,
		BankId:         bankId,
		BankProvinceId: bankProvinceId,
		BankCityId:     bankCityId,
		BankAreaId:     bankAreaId,
		BankBranchName: bankBranchName,
		BankBranchCode: bankBranchCode,
		BankAcctName:   SelfSignMerchantInfo.MerIdcardName,
		// State:      1, //0:未提交,1:待审核 ,2:审核未通过 ,3:后台审核通过 ,4:待提交资料 ,5:已提交资料 ,6:资料未通过,7:对公账户待确认 ,8:待在线签约 ,9:待银商入网审核 ,10:银商入网成功,11:银商入网失败,
	}
	if len(bankBindMobile) > 0 {
		updateContent.MerMobile = bankBindMobile
		updateContent.BankBindMobile = bankBindMobile
	}
	//监测银行卡号码是否正确
	if bankAcctType == 0 { //个人账户
		if !tools.CheckBankCardNumber(bankAcctNum) {
			tx.Rollback()
			return false, "bank_card_error"
		}
		
	}

	// 修改
	updateErr := tx.Model(&SelfSignMerchantInfo).Updates(updateContent).Error
	if updateErr != nil {
		tx.Rollback()
		tools.Logger.Info("修改商户信息失败", updateErr.Error())
		panic(updateErr.Error())
		return false, ""
	}
	ImageSaveErr := t.savaImageByshipperIdAndDocType(db, shipperId, AcctImg)
	if ImageSaveErr != nil {
		tx.Rollback()
		tools.Logger.Info("保存银行卡失败", ImageSaveErr.Error())
		return false, ""
	}
	tx.Commit()
	//实名认证信息 四要素认证
	chk, msg := tools.BankVerify(SelfSignMerchantInfo.MerIdcardName, bankAcctNum, bankBindMobile, info.MerIdcardNum)
	if !chk {
		return false,t.langUtil.TArr("bank_verify_result")[tools.ToInt(msg)]
	}
	db.Model(&models.SelfSignMerchantInfo{}).Where("id = ?",info.Id).Updates(&map[string]interface{}{
		"state":1,
	})

	return true, ""
}

func (s ShipperService) savaImageByshipperIdAndDocType(tx *gorm.DB, shipperId int, imgSrcs string) error {
	tools.Logger.Info("图片列表=====>>>>>", imgSrcs)
	type ImgSrcs struct {
		ImgSrc  string `json:"img_src" validate:"required"`
		DocType string `json:"doc_type" validate:"required"`
	}
	imgSrcsArr := make([]ImgSrcs, 0)
	jsonToStructErr := json.Unmarshal([]byte(imgSrcs), &imgSrcsArr)
	if jsonToStructErr != nil {
		tools.Logger.Info("Images json转imgSrcsstruct失败:", jsonToStructErr.Error())
		return jsonToStructErr
	}
	// 验证参数
	validate := validator.New()
	for _, imgSrc := range imgSrcsArr {
		if len(imgSrc.ImgSrc) > 0 {
			validateErr := validate.Struct(imgSrc)
			if validateErr != nil {
				return validateErr
			}
		}
	}
	for _, imgSrc := range imgSrcsArr {
		if imgSrc.DocType == "0099" {

			deleteErr := tx.Model(models.SelfSignImages{}).
				Where("restaurant_id = ? AND doc_type = ?", shipperId, "0099").
				Where("type = ?",constants.SELF_SIGN_TYPE_SHIPPER).
				Delete(models.SelfSignImages{}).Error

			if deleteErr != nil {
				tools.Logger.Info("删除管理员其他照片失败", deleteErr.Error())
				return deleteErr
			}
			break
		} else if imgSrc.DocType == "0016" || imgSrc.DocType == "0017" || imgSrc.DocType == "0018" || imgSrc.DocType == "0019" || imgSrc.DocType == "0020" {
			deleteErr := tx.Model(models.SelfSignImages{}).
				Where("restaurant_id = ? AND doc_type in ('0016','0017','0018','0019','0020')", shipperId).
				Where("type = ?",constants.SELF_SIGN_TYPE_SHIPPER).
				Delete(models.SelfSignImages{}).Error
			if deleteErr != nil {
				tools.Logger.Info("删除管理员其他照片失败", deleteErr.Error())
				return deleteErr
			}
			break
		}
	}

	tools.Logger.Info("Images=>>>>>", imgSrcsArr)
	// 保存图片
	for _, imgSrc := range imgSrcsArr {
		// 去除图片前缀
		trim := strings.Split(imgSrc.ImgSrc, configs.MyApp.CdnUrl)
		var DocTypeName string
		tx.Table("b_self_sign_doc_type").Select(" name_zh ").Where(" `key` = ? ", imgSrc.DocType).Scan(&DocTypeName)
		// 查询图片是否存在
		var img models.SelfSignImages
		tx.Model(models.SelfSignImages{}).Where("restaurant_id = ? AND doc_type = ?", shipperId, imgSrc.DocType).Where("type = ?",constants.SELF_SIGN_TYPE_SHIPPER).First(&img)
		if img.ID == 0 {
			// 不存在就创建
			img = models.SelfSignImages{
				Type: constants.SELF_SIGN_TYPE_SHIPPER,
				RestaurantID: shipperId,
				DocType:      imgSrc.DocType,
				MlzFilePath:  trim[len(trim)-1],
				DocTypeName:  DocTypeName,
			}
			createErr := tx.Create(&img).Error
			if createErr != nil {
				tools.Logger.Info("图片新建写入失败", createErr.Error())
				return createErr
			}
		} else {
			// doc_type 0099  其他资料 保存多张图片
			if imgSrc.DocType == "0099" {
				img = models.SelfSignImages{
					Type:  constants.SELF_SIGN_TYPE_SHIPPER,
					RestaurantID: shipperId,
					DocType:      imgSrc.DocType,
					MlzFilePath:  trim[len(trim)-1],
					DocTypeName:  DocTypeName,
				}
				createErr := tx.Create(&img).Error
				if createErr != nil {
					tools.Logger.Info("图片写入失败", createErr.Error())
					return createErr
				}

			} else {
				// 存在就更新
				updateErr := tx.Model(models.SelfSignImages{}).Where("restaurant_id = ? AND doc_type = ?", shipperId, imgSrc.DocType).Where("type = ?",constants.SELF_SIGN_TYPE_SHIPPER).Updates(models.SelfSignImages{MlzFilePath: trim[len(trim)-1], DocTypeName: DocTypeName}).Error
				if updateErr != nil {
					tools.Logger.Info("图片更新失败", updateErr.Error())
					return updateErr
				}
			}

		}
	}
	return nil

}



func (ums ShipperService) UploadFile(c *gin.Context, file *multipart.FileHeader, docType string, shipperId string) (fileMap map[string]string, errMsg string) {

	basePath := configs.MyApp.UploadRootDir            // 上传文件的基础路径
	filePath := "/images/self-sign/shipper/" + shipperId + "/" // 保存文件路径

	lastStr := basePath[len(basePath)-1:]
	if lastStr == `/` {
		basePath = basePath[:len(basePath)-1]
	}
	fullPath := basePath + filePath // 文件完整路径
	dots := strings.Split(file.Filename, ".")
	fileType := dots[len(dots)-1] // 文件类型

	//随机文件名
	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))
	rndStrNum := fmt.Sprintf("%06v", rnd.Int31n(1000000))
	var now = time.Now()
	rndStr := fmt.Sprintf("%d%d%d%d%d%d%s", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), rndStrNum) //随机数
	fileName := docType + "_" + rndStr + "." + fileType                                                                            // 文件名

	fileFullPath := fullPath + fileName //  文件完整路径+文件名
	// fileSize := strconv.FormatInt(file.Size, 10) //  文件大小

	fileDbName := filePath + fileName         //  文件在数据库中的路径
	err := os.MkdirAll(fullPath, os.ModePerm) // 创建文件夹
	if err != nil {
		errMsg = "创建文件夹失败"
		return
	}

	//判断文件大小 根据ocr 要求 压缩 文件

	// 获取大小的借口

	//0001,1002, 0011,1003  身份证OCR  格式为 jpg或png，宽和⾼大于 8px，⼩于等于4000px，大小不能超过1MB
	//0025 银行卡OCR  格式为 jpg或png，宽和⾼大于 8px，⼩于等于4000px，大小不能超过1MB
	//0002 营业执照OCR  格式为 jpg或png，宽和⾼⼩于等于4000px，大小不能超过1MB，图片不支持旋转
	//1001  食品经营许可证  图像数据，BASE64编码后进行URLENCODE，要求base64编码和URLENCODE后大小不超过2M，最短边至少15PX，最长边最大4096PX,支持JPG/PNG/BMP格式
	// tools.Logger.Info("压缩文件")
	//1 mb
	mb := tools.ToInt64(1024 * 1024)
	// 512 kb
	halfMb := tools.ToInt64(512 * 1024)

	fileSize := tools.ToInt64(file.Size)

	if fileSize > 15*mb {
		errMsg = "file_too_big"
		return
	}

	if fileSize > halfMb { // 超过 512 KB 就得压缩
		tools.Logger.Info("压缩文件")
		//0001,1002, 0011,1003  身份证OCR  格式为 jpg或png，宽和⾼大于 8px，⼩于等于4000px，大小不能超过1MB
		//压缩图片
		// tools.Logger.Info("fileSize", (tools.ToInt64(fileSize) / mb))

		files, fileHeader, _ := c.Request.FormFile("file")
		byteData := make([]byte, fileHeader.Size)
		files.Read(byteData)

		quality := float32(fileSize-mb) / float32(fileSize) * 100
		// tools.Logger.Info("quality", quality)

		qualityStr := strconv.FormatInt(int64(quality), 10)

		fileErr := tools.FileCreate(byteData, fileFullPath, qualityStr) //压缩的时候保存

		if fileErr != nil { // 保存营业执照失败
			// errMsg = "保存营业执照失败"
			errMsg = "save_img_fail"
			return
		}

	}

	fileExists, _ := tools.PathExists(fileFullPath) //压缩的时候保存过的 不用再次保存
	if !fileExists {
		fileSaveErr := c.SaveUploadedFile(file, fileFullPath) // 保存营业执照
		if fileSaveErr != nil {                               // 保存营业执照失败
			// errMsg = "保存营业执照失败"
			errMsg = "save_img_fail"
			return
		}
	}

	mp := map[string]string{
		"fileFullPath": fileFullPath,
		"fileUrl":      fileDbName[1:],
	}

	fileMap = mp

	return

}

//购买保险
func (s ShipperService) InsuranceBuy(shipperId int,cityId int,areaId int) error {

	db :=tools.GetDB()
	now :=carbon.Now(configs.AsiaShanghai)
	insuranceBeginTime :=s.GetAppConfig("insurance_begin_time")
	if len(insuranceBeginTime) > 0 {
		beginTime :=carbon.Parse(insuranceBeginTime,configs.AsiaShanghai)
		if now.Lt(beginTime) { //保险开始时间
			bgTime :=beginTime.Format("Y-m-d H:i:s")
			if s.language == "ug" {
				bgTime =beginTime.Format("d-m-Y H:i:s")
			}
			err := fmt.Errorf(fmt.Sprintf(s.langUtil.T("insurance_before_time_can_not_buy"),bgTime))
			return err
		}
	}
	insuranceStopTime :=s.BaseService.GetAppConfig("insurance_stop_time")
	originalStopTime :=configs.MyApp.InsuranceStopTime
	newTime :=originalStopTime
	if len(insuranceStopTime) > 0 {
		newTime =insuranceStopTime
	}
	stopTime :=carbon.Parse(now.Format("Y-m-d")+" "+newTime,configs.AsiaShanghai)
	if now.Gt(stopTime) { //保险停止时间
		//超过保险停止时间的不能创建
		return fmt.Errorf("insurance_over_time_can_not_buy")
	}
	tomorrow :=now.AddDay()
	date :=tomorrow.Format("Y-m-d")
	var insurance models.ShipperInsuranceLog
	db.Model(&models.ShipperInsuranceLog{}).Where("shipper_id = ? and date = ?",shipperId,date).Find(&insurance)
	if insurance.ID == 0 {
		insuranceMap :=s.GetInsuranceConfig()
		db.Create(&models.ShipperInsuranceLog{
			CityID: cityId,
			AreaID: areaId,
			ShipperID:      shipperId,
			InsuranceNameUG: insuranceMap.NameUg,
			InsuranceNameZH: insuranceMap.NameZh,
			Date:           date,
			StartTime: tomorrow.Format("Y-m-d 00:00:00"),
			EndTime:  tomorrow.Format("Y-m-d 23:59:59"),
			Amount: insuranceMap.Amount,
			State: 1,
		})
	}else{
		if insurance.State == 0 || insurance.State == 5 { 
			db.Model(&models.ShipperInsuranceLog{}).Where("id = ?",insurance.ID).Updates(&map[string]interface{}{
				"state": 1,
			})
		}else if insurance.State == 4 { 
			return fmt.Errorf("insurance_not_approved")
		}else{
			if insurance.State == 3 {
				return fmt.Errorf("insurance_already_buy")
			}else{ //请求已发送，等待结果
				return fmt.Errorf("insurance_request_sent")
			}
		}
	}
	return nil
}


//保险状态
func (s ShipperService) InsuranceStatus(shipperId int) (map[string]interface{},error) {

	result :=make(map[string]interface{})
	db :=tools.GetDB()
	tomorrow :=carbon.Now(configs.AsiaShanghai).AddDay()
	date :=tomorrow.Format("Y-m-d")
	var insurance models.ShipperInsuranceLog
	db.Model(&models.ShipperInsuranceLog{}).Where("shipper_id = ? and date = ?",shipperId,date).Find(&insurance)
	if insurance.ID == 0 {
		insuranceMap :=s.GetInsuranceConfig()
		insurance.ShipperID =      shipperId
		insurance.InsuranceNameUG= insuranceMap.NameUg
		insurance.InsuranceNameZH= insuranceMap.NameZh
		insurance.Date=           date
		insurance.StartTime= tomorrow.Format("Y-m-d 00:00:00")
		insurance.EndTime=  tomorrow.Format("Y-m-d 23:59:59")
		insurance.Amount= insuranceMap.Amount
		insurance.State= 0
		db.Create(&insurance)
		// return result,fmt.Errorf("insurance_not_buy")
	}
	result["insurance_name_ug"] = insurance.InsuranceNameUG
	result["insurance_name_zh"] = insurance.InsuranceNameZH
	result["insurance_state"] = insurance.InsuranceState
	result["insurance_number"] = insurance.InsuranceNumber
	result["insurance_start_time"] = carbon.Parse(insurance.StartTime,configs.AsiaShanghai).Format("Y-m-d H:i:s")
	result["insurance_end_time"] = carbon.Parse(insurance.EndTime,configs.AsiaShanghai).Format("Y-m-d H:i:s")
	result["insurance_amount"] = insurance.Amount
	result["state"] = insurance.State
	failReason :=""
	if s.language == "ug" {
		failReason = insurance.FailReasonUg
	}else{
		failReason = insurance.FailReasonZh
	}
	result["fail_reason"] = failReason
	result["state_name"] =s.langUtil.TArr("insurance_state")[insurance.State]

	
	return result,nil
}






//保险状态
func (s ShipperService) InsuranceLog(ctx *gin.Context,shipperId int,startDate string,endDate string) ([]map[string]interface{},error) {

	result :=make([]map[string]interface{},0)
	db :=tools.GetDB()
	var insurance []models.ShipperInsuranceLog
	logHead :=db.Model(&models.ShipperInsuranceLog{}).Where("shipper_id = ? ",shipperId)
	if len(startDate) > 0 {
		logHead = logHead.Where("created_at >= ?",startDate+" 00:00:00")
	}
	if len(endDate) > 0 {
		logHead = logHead.Where("created_at <= ?",endDate+" 23:59:59")
	}
	logHead.Scopes(scopes.Paginate(ctx)).Order("id desc").Find(&insurance)
	for _, item := range insurance {
		itemMap :=make(map[string]interface{})
		itemMap["insurance_name_ug"] = item.InsuranceNameUG
		itemMap["insurance_name_zh"] = item.InsuranceNameZH
		itemMap["insurance_state"] = item.InsuranceState
		itemMap["date"] =  carbon.Parse(item.Date,configs.AsiaShanghai).Format("Y-m-d")
		itemMap["insurance_start_time"] = carbon.Parse(item.StartTime,configs.AsiaShanghai).Format("Y-m-d H:i:s")

		itemMap["insurance_end_time"] = carbon.Parse(
			item.EndTime,
			configs.AsiaShanghai,
		).Format("Y-m-d H:i:s")
		itemMap["insurance_amount"] = item.Amount
		if item.UploadTime !=nil {
			itemMap["upload_time"] = item.UploadTime.Format("2006-01-02 15:04:05")
		}else{
			itemMap["upload_time"] = ""
		}
		if item.ConfirmTime !=nil {
			itemMap["confirm_time"] = item.ConfirmTime.Format("2006-01-02 15:04:05")
		}else{
			itemMap["confirm_time"] = ""
		}
		itemMap["state"] = item.State
		itemMap["state_name"] =s.langUtil.TArr("insurance_state")[item.State]
		itemMap["created_at"] = item.CreatedAt.Format("2006-01-02 15:04:05")
		result = append(result,itemMap)
	}
	return result,nil
}



//保险状态 判断
func (s ShipperService) InsuranceStatusCheck(shipperId int,shipperAreaId int) bool {
	db :=tools.GetDB()
	configVal :=s.GetAppConfig("insurance_enable")
	if tools.ToInt(configVal) == 1  {
		var area models.Area
		db.Model(&models.Area{}).Where("id = ?",shipperAreaId).Find(&area)
		if area.InsuranceEnable == 1 {
			ct :=int64(0)
			date :=carbon.Now(configs.AsiaShanghai).Format("Y-m-d")
			db.Model(&models.ShipperInsuranceLog{}).Where("shipper_id = ? and date = ? and state = 3",shipperId,date).Count(&ct)
			return ct > 0
		}
		
	}
	
	return true
}
//实名认证检查
func (s ShipperService) RealNameCheck(shipperId int) bool {
	db :=tools.GetDB()
	configVal :=s.GetAppConfig("real_name_enable")
	if tools.ToInt(configVal) == 1  {
		ct :=int64(0)
		db.Model(&models.SelfSignMerchantInfo{}).Where("restaurant_id = ? and type = ? and state = ?",shipperId,2,3).Count(&ct)
		return ct > 0
	}
	
	return true
}



//保险 取消 或 休息 
func (s ShipperService) InsuranceCancel(shipperId int,adminCityId int,adminAreaId int,reject int) (error) {

	db :=tools.GetDB()
	now :=carbon.Now(configs.AsiaShanghai)
	insuranceStopTime :=s.BaseService.GetAppConfig("insurance_stop_time")
	originalStopTime :=configs.MyApp.InsuranceStopTime
	newTime :=originalStopTime
	if len(insuranceStopTime) > 0 {
		newTime =insuranceStopTime
	}
	stopTime :=carbon.Parse(now.Format("Y-m-d")+" "+newTime,configs.AsiaShanghai)
	if now.Gt(stopTime) { //保险停止时间
		//超过保险停止时间的不能创建
		return fmt.Errorf("insurance_over_time_can_not_buy")
	}
	tomorrow :=now.AddDay()
	date :=tomorrow.Format("Y-m-d")
	var insurance models.ShipperInsuranceLog
	db.Model(&models.ShipperInsuranceLog{}).Preload("RealNameInfo","type = ? ",2).Where("shipper_id = ? and date = ?",shipperId,date).Find(&insurance)
	
	if insurance.ID == 0 {
		insuranceMap :=s.GetInsuranceConfig()
		insurance =models.ShipperInsuranceLog{
			CityID: adminCityId,
			AreaID: adminAreaId,
			ShipperID:      shipperId,
			InsuranceNameUG: insuranceMap.NameUg,
			InsuranceNameZH: insuranceMap.NameZh,
			Date:           date,
			StartTime: tomorrow.Format("Y-m-d 00:00:00"),
			EndTime:  tomorrow.Format("Y-m-d 23:59:59"),
			Amount: insuranceMap.Amount,
			State: 5,
		} 
		db.Create(&insurance)
		db.Model(&models.ShipperInsuranceLog{}).Preload("RealNameInfo","type = ? ",2).Where("id = ? ",insurance.ID).Find(&insurance)
	}
	if insurance.State >= 2 && insurance.State < 5 {
		//已提交 不能撤回 
		return fmt.Errorf("insurance_send_can_not_cancel")
	}
	redisHelper := tools.GetRedisHelper()
	cacheKey := fmt.Sprintf("enable_force_%s_%s",tools.ToString(insurance.ID),carbon.Now(configs.AsiaShanghai).Format("Y-m-d"))
	if exists, _ := redisHelper.Exists(context.Background(), cacheKey).Result(); exists == 1 {
		return fmt.Errorf("request_denied_by_admin")
	}
	db.Model(&models.ShipperInsuranceLog{}).Where("id = ? ",insurance.ID).Updates(&map[string]interface{}{
		"state":5,
	})
	//添加通知 
	ct :=int64(0)
	db.Table("t_notification").Where("type = ? and item_id = ? and state = ? and created_at > ?",9,shipperId,0,now.Format("Y-m-d 00:00:00")).Count(&ct)
	if ct ==0 {
		
		db.Table("t_notification").Create(&map[string]interface{}{
			"type":       9,
			"area_id":    adminAreaId,
			"content_ug":fmt.Sprintf(" يەتكۈزگۈچى ئەتە ئارام ئالىدۇ [%s]",insurance.RealNameInfo.MerIdcardName),
			"content_zh": fmt.Sprintf("[%s] 配送员明天休息",insurance.RealNameInfo.MerIdcardName),
			"link":       "/app/ug/shipment/insurance",
			"state":      0,
			"item_id":    shipperId,
			"created_at": time.Now(),
			"updated_at": time.Now(),
		})		
	}
	return nil
}


//处理抽奖用户的数据
func (shipper ShipperService) LotteryOrderProcess (order models.OrderToday) {
	db :=tools.GetDB()
	now :=carbon.Now(configs.AsiaShanghai)
	var activity models.LotteryActivity
	db.Model(&models.LotteryActivity{}).Preload("LotteryActivityLevel").Where("state = ? and ? between start_time  and end_time ",1,now.Format("Y-m-d H:i:s")).Find(&activity)
	if(activity.ID > 0 && int(order.OrderPrice)>=activity.MinPrizeOrderPrice && activity.MinPrizeOrderPrice >0 ) {//当前正在进行的抽奖活动
		var lotteryChance models.LotteryChance
		db.Model(&models.LotteryChance{}).Where("user_id = ? and lottery_activity_id = ? and type =? and type_id = ?",order.UserID,activity.ID,2,order.ID).Find(&lotteryChance)
		if lotteryChance.ID == 0 { 
			levelId :=0
			if len(activity.LotteryActivityLevel) > 0{
				levelId =activity.LotteryActivityLevel[0].ID
			}
			lotteryChance = models.LotteryChance{
				CityId:        order.CityID,
				AreaId:        order.AreaID,
				UserID:       order.UserID,
				LotteryActivityID: activity.ID,
				Type:         2,
				State: 1,
				TypeID:       order.ID,
				CreatedAt:    now.Carbon2Time(),
				UpdatedAt:    now.Carbon2Time(),
				LotteryActivityLevelID: levelId,
			}
			bb,_:=json.Marshal(lotteryChance)
			tools.Logger.Info("因为用户下单给他一个抽奖次数,详细数据",string(bb))
			db.Model(&models.LotteryChance{}).Create(&lotteryChance)
		}
		// TODO 判断这个用户是否来自分享
		var lotteryShareBind models.LotteryShareBind
		db.Model(&models.LotteryShareBind{}).Preload("User").Where("invited_user_id = ?",order.UserID).Find(&lotteryShareBind)
		if lotteryShareBind.ID > 0 { //确定这个用户来自分享 给他的上级分享人给他分配抽奖机会
			lotteryChance =models.LotteryChance{}
			takenCount :=int64(0)
			db.Model(&models.LotteryChance{}).Where("user_id = ? and  type =? and source_user_id = ? and lottery_activity_id = ?",lotteryShareBind.UserID,3,order.UserID,activity.ID).Count(&takenCount)
			if takenCount > 0 {//新规则 从一个用户 分享后下单后只能获得一次机会
				data :=map[string]interface{}{
					"user_id": lotteryShareBind.UserID,
					"lottery_activity_id": activity.ID,
					"source_user_id": order.UserID,
					"msg":"新规则 从一个用户 分享后下单后只能获得一次机会",
				}
				bb,_:=json.Marshal(data)
				tools.Logger.Info("新规则 从一个用户 分享后下单后只能获得一次机会",string(bb))
				return 
			}
			db.Model(&models.LotteryChance{}).Where("user_id = ? and lottery_activity_id = ? and type =? and type_id = ?",lotteryShareBind.UserID,activity.ID,3,order.ID).Find(&lotteryChance)
			if lotteryChance.ID == 0 { 
				levelId :=0
				if len(activity.LotteryActivityLevel) > 0{
					levelId =activity.LotteryActivityLevel[0].ID
				}
				lotteryChance = models.LotteryChance{
					CityId:       lotteryShareBind.User.UserCityId,
					AreaId:       lotteryShareBind.User.UserAreaId,
					UserID:       lotteryShareBind.UserID,
					LotteryActivityID: activity.ID,
					Type:         3,
					State: 1,
					TypeID:       order.ID,
					CreatedAt:    now.Carbon2Time(),
					UpdatedAt:    now.Carbon2Time(),
					SourceUserId: &order.UserID,
					LotteryActivityLevelID:levelId,
				}
				bb,_:=json.Marshal(lotteryChance)
				tools.Logger.Info("因为用户下单给他的上级分享者一个抽奖次数,详细数据",string(bb))
				db.Model(&models.LotteryChance{}).Create(&lotteryChance)
			}
		}
	}
}

// 描述：申请修改订单配送时间
// 作者：Qurbanjan
// 文件：ShipperService.go
// 修改时间：2024/08/26 18:21
func (s ShipperService) ApplyModifyBookingTime(admin models.Admin, orderId string, delayedDuration int, reasonDelay string,reasonId int) error {  
	var order models.OrderToday  
	db := tools.GetDB()  

	// 查询今天的订单  
	if err := db.Model(&models.OrderToday{}).Where("id = ?", orderId).First(&order).Error; err != nil {  
		if errors.Is(err, gorm.ErrRecordNotFound) {  
			return errors.New("order_not_found")  
		}  
		return err 
	} 
	
	if order.State == 7 {
		return errors.New("already_finish_order")  
	}
	

	maxDelayTime :=30 //最长的延长时间 
	if s.BaseService.GetAppConfig("max_delay_time") != "" {
		maxDelayTime =tools.ToInt(s.BaseService.GetAppConfig("max_delay_time"))
	}

	if delayedDuration > maxDelayTime {
		return errors.New(fmt.Sprintf(s.langUtil.T("max_duration_err"), maxDelayTime))
	}
	// 2.检查是否申请过一次延时  
	var delayedOrder models.OrderDelayed  
	err := db.Model(&models.OrderDelayed{}).  
		Where("order_id = ? AND shipper_id = ? AND user_id = ?", orderId, admin.ID, order.UserID).  
		First(&delayedOrder).Error 

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {  
		return err 
	}  
	if delayedOrder.ID != 0 {  
		return errors.New("already_send_request_once")  
	}  

	// 3.添加记录  
	delayedOrder.RandomId = tools.RandStr(6)
	delayedOrder.OrderId = tools.ToInt64(orderId)  
	delayedOrder.ShipperId = tools.ToInt64(admin.ID)  
	delayedOrder.UserId = tools.ToInt64(order.UserID)  
	delayedOrder.DelayedDuration = tools.ToInt64(delayedDuration) 
	delayedOrder.OriginalBookingTime = carbon.Parse(order.BookingTime).Carbon2Time() 
	delayedOrder.NewBookingTime = delayedOrder.OriginalBookingTime.Add(time.Minute * time.Duration(delayedDuration))
	delayedOrder.AgreeState = 1
	delayedOrder.ReasonDelay = reasonDelay
	if reasonId > 0 {
		delayedOrder.ReasonId = reasonId
	}
	if err := db.Create(&delayedOrder).Error; err != nil {  
		tools.Logger.Error("FATAL配送时间延时申请失败: " + err.Error())
		return errors.New("prolong_booking_time_failed") 
	}  
	if reasonId > 0 {
		//更新该原因词语使用次数 
		ct :=int64(0)
		db.Model(&models.CommonMsg{}).Where("id = ?",reasonId).Pluck("use_count",&ct)
		db.Model(&models.CommonMsg{}).Where("id = ?",reasonId).Updates(&map[string]interface{}{
			"use_count":ct+1,
		})
	}

	
	go func () {
		defer func() {
			if err := recover(); err != nil {
				tools.Logger.Error("配送时间延时申请失败: " + fmt.Sprint(err))
			}
		}()
		// 4.给用户发送通知（小程序推送｜短信）  
		contentMap :=map[string]interface{}{
			"shijian":delayedOrder.NewBookingTime.Format("15:04"),
			"random_id":delayedOrder.RandomId,
		}
		// 阿里云发送短信
		//cb,_:=json.Marshal(contentMap)
		// 短信发送 并记录到 短信发送记录  
		//templateId :="SMS_472295113"
		//shipper_prolong_sms :=s.GetAppConfig("shipper_prolong_sms")
		//if len(shipper_prolong_sms) > 0 {
		//	templateId =shipper_prolong_sms
		//}
		//resp,er :=tools.AliSmsSendWithResponse(order.Mobile, string(cb), "Mulazim外卖",templateId)
		//if er != nil {
		//	tools.Logger.Error(er)
		//}
		//rsp ,_:=json.Marshal(resp.Body)

		// 发送短信
		smsServiceFactory := &factory.SmsServiceFactory{}
		service := smsServiceFactory.CreateSmsService()
		smsResp, err := service.SendSMSUsingTemplate(order.Mobile,sms.ZhuTongTemplateDeliveryTimeModified, contentMap)
		if err != nil {
			tools.Logger.Infof("骑手修改配送时间发送短信失败: %v", err)
		}
		tools.Logger.Info("骑手修改配送时间发送短信发送结果: " + smsResp)
		var msgs []models.OrderSMS
		msgs= append(msgs,models.OrderSMS{
			CityID: order.CityID,
			AreaID: order.AreaID,
			OrderID: order.ID,
			Type: 3,	
			Resp: smsResp,
			From: "mulazim-api",
			CreatedAt: carbon.Now(configs.AsiaShanghai).Carbon2Time(),
		})
		if len(msgs) > 0 {
			db.Model(&models.OrderSMS{}).Create(&msgs)
		}	

	}()

	// 发送通知的方法

	return nil  
}  
// 获取 延长时间记录 
func (s ShipperService) GetModifyBookingTime(admin models.Admin, orderId string) (map[string]interface{},error) {  
	var order models.OrderToday  
	db := tools.GetDB()  

	// 查询今天的订单  
	db.Model(&models.OrderToday{}).Where("id = ?", orderId).First(&order)
	if order.ID == 0 {
		return nil,errors.New("order_not_found")  
	}
	// 2.检查是否申请过一次延时  
	var delayedOrder models.OrderDelayed  
	db.Model(&models.OrderDelayed{}).  
		Where("order_id = ? AND shipper_id = ? ", orderId, admin.ID).  
		// Where("order_id = ?  ", orderId).  
		First(&delayedOrder)
	  
	reasons :=make([]map[string]interface{},0)
	var msgs []models.CommonMsg
	db.Model(&models.CommonMsg{}).Where("type = ? and state = ?",1,1).Find(&msgs)	
	for _, v := range msgs {
		name :=v.NameUG
		if s.language != "ug" {
			name = v.NameZH
		}
		reasons = append(reasons, map[string]interface{}{
			"id":v.ID,
			"name":name,
		})
	}
	maxDelayTime :=60
	if s.BaseService.GetAppConfig("max_delay_time") != "" {
		maxDelayTime =tools.ToInt(s.BaseService.GetAppConfig("max_delay_time"))
	}
	resultMap :=map[string]interface{}{
		"max_delay_time":maxDelayTime,
		"order_id":orderId,
		"delay_id":delayedOrder.ID,
		"agree_state":delayedOrder.AgreeState,
		"original_booking_time":delayedOrder.OriginalBookingTime.Format("2006-01-02 15:04:05"),
		"new_booking_time":delayedOrder.NewBookingTime.Format("2006-01-02 15:04:05"),
		"reason_delay":delayedOrder.ReasonDelay,
		"reason_id":delayedOrder.ReasonId,
		"delayed_duration":delayedOrder.DelayedDuration,
		"reason_delay_tags":reasons,
	}

	return resultMap,nil
}  
// GetHideGpsPermissionNextTime 在华为手机上第二次不显示GPS定位，为了应付应用市场
//  @receiver shipper
//  @param c
//  @return int64
//
func (shipper ShipperService) GetHideGpsPermissionNextTime(c *gin.Context) int64 {
	osTypeParam := c.GetHeader("ostype")
	versionName := c.GetHeader("version-name")
	osType := "android"
	if osTypeParam != "2"{
		osType = "android"
	}
	return tools.ToInt64(tools.NewAppConfigTool().GetConfigValue("hide_gps_permission_next_time", osType, versionName, "0"))

}



// GetConfig 获取AppConfig的值，发送keys ，逗号区分
//  @receiver s
//  @param keys
//  @return []map[string]interface{}
//
func (shipper ShipperService) GetConfig(keys string,brand string,deviceAppVersion int64) []map[string]interface{} {
	db := tools.Db
	var rtns []models.AppConfig
	keysArr := strings.Split(keys,",")
	db.Model(&models.AppConfig{}).Where("`key` in ?",keysArr).Find(&rtns)
	rtnsMap := []map[string]interface{}{}
	for _,v := range rtns {
		value :=v.Value
		version := tools.ToInt64(tools.ReplaceString(v.Version,".",""))
		if deviceAppVersion < version {
			value = ""
		}
		if v.Key == "shipper_hide_forground_task" { //华为应用市场要求 隐藏应用
			if strings.ToUpper(brand) != "HUAWEI" {//华为的直接返回 其他的返回0
				value = "0"
			}
		}
		rtnsMap = append(rtnsMap, map[string]interface{}{
			"key":   v.Key,
			"value": value,
		})

	}
	return rtnsMap
}

// PostScanCommunityCode
//
//	@Description: 配送员进入小区扫二维码
//	@author: rixat
//	@Time: 2024-12-18 13:17:38
//	@receiver shipper ShipperController
//	@param c *gin.Context
func (shipper ShipperService) PostScanCommunityCode(admin models.Admin,code string) map[string]interface{} {
	scanSate := 1
	// 获取小区信息
	var community models.ShipperScanCommunity
	tools.Db.Model(&community).Where("code = ? and state=1", code).Find(&community)
	if community.ID == 0 {
		scanSate = 0
	}
	// 获取配送员信息
	tools.Db.Model(models.ShipperScanCommunityLog{}).Create(&models.ShipperScanCommunityLog{
		AreaID:      community.CityID,
		CityID:      community.AreaID,
		CommunityID: community.ID,
		ShipperID:   admin.ID,
		CreatedAt:   carbon.Now().Carbon2Time(),
	})
	var selfSignInfo models.SelfSignMerchantInfo
	tools.Db.Model(selfSignInfo).Where("restaurant_id=? and type = 2", admin.ID).Scan(&selfSignInfo)
	return map[string]interface{}{
		"avatar":     strings.TrimRight(configs.MyApp.CdnUrl, "/") + admin.Avatar,
		"shipper_name":     admin.RealName,
		"shipper_id_card": tools.ReplaceRange(selfSignInfo.MerIdcardNum, 5, 12),
		"shipper_mobile":    tools.ReplaceRange(admin.Mobile, 4, 10),
		"community_name":  community.NameZh,
		"scan_time":       carbon.Now().Format("Y-m-d H:i:s"),
		"scan_state":       scanSate,
	}
}

//配送员等级统计
func (shipper ShipperService) GetRankStatistic(admin models.Admin, month string,lang lang.LangUtil) (rnk shipperResources.RankStatisticsEntity,err error) {
	db :=tools.GetDB()

	// now :=carbon.Now().SetTimezone(configs.AsiaShanghai)
	//获取本月的每周的周一的日期 
	// _,days1,_ := tools.GetDaysOfMonthAndDay(month,1) // 返回参数解析  1:days 本月中的所有的日期  2:days1 本月中所有的周一 3:days2 本月中中的自然周数据

	


	
	
	newRank,rankGrowthRate,newScore,scoreGrowthRate,scoreStartTime,scoreEndTime:=shipper.GetShipperRankGrowthRate(&admin)
	
	rnk.Header = shipperResources.RankStatisticsHeaderEntity{
		Rank: newRank,
		RankGrowthRate: rankGrowthRate,
		Score: newScore,
		ScoreGrowthRate: scoreGrowthRate,
		StartTime: scoreStartTime,
		EndTime: scoreEndTime,
	}

	monthNow :=carbon.ParseByFormat(month,"Y-m").SetTimezone(configs.AsiaShanghai)
	if monthNow.Error !=nil {
		monthNow =carbon.ParseByFormat(month,"Y-m-d").SetTimezone(configs.AsiaShanghai)
	}
	
	var shipperRankHistory []models.ShipperRankHistory
    db.Model(&models.ShipperRankHistory{}).
        Where("shipper_id = ?",admin.ID).
        Where("date_month = ?",monthNow.Format("Y-m-01")).
        Find(&shipperRankHistory)
    var weekDataItem []shipperResources.RankStatisticsEntityItem
	detailItems :=lang.TArrMap("order_rank_list")
    
     
	for kk, vv := range shipperRankHistory {
		
		
		if (kk+1) == vv.DateMonthWeek {

			var details  []shipperResources.RankStatisticsEntityItemDetails
			var detailsNew  []shipperResources.RankStatisticsEntityItemDetails
			scoreSum :=float64(0)
			lateCount :=int(0)
			lateScore :=float64(0)
			lateIndex :=0
			for k, vvv := range detailItems { 
				key1 :=tools.ToString(vvv["key1"])
				value1 :=""
				if len(key1) > 0 {
					value1Str,_:=tools.GetValueByGormColumn(vv,key1)
					value1 = tools.ToString(value1Str)
				}
				if key1 == "mild_lateness_count" { 
					lateIndex = k
				}
				
				multiply :=int64(1)
				if vvv["multiply"] != nil {
					multiply = tools.ToInt64(vvv["multiply"])
				}
				key2 :=tools.ToString(vvv["key2"])
				value2 :=float64(0)
				if len(key2) > 0 {    
					value2Str,_:=tools.GetValueByGormColumn(vv,key2)
					value2 =tools.ToFloat64(value2Str)
					if value2 > 0 { 
						value2 = value2 * float64(multiply)
					}
				}
				grayBackGround :=false
				if tools.InArray(key1,[]string{"mild_lateness_count","moderate_lateness_count","severe_lateness_count"}){
					lateCount +=tools.ToInt(value1)
					lateScore += tools.ToFloat64(value2)
					grayBackGround =true
				}
				details = append(details, shipperResources.RankStatisticsEntityItemDetails{
					Name: tools.ToString(vvv["name"]),
					Count: tools.ToInt(value1),
					Score: tools.ToFloat64(value2),
					GrayBackGround: grayBackGround,
					Plus: tools.ToBool(multiply),
				})
				scoreSum += value2
			}

			for i := 0; i < lateIndex; i++ { //front
				detailsNew = append(detailsNew, details[i])
			}

			detailsNew = append(detailsNew, shipperResources.RankStatisticsEntityItemDetails{ //middle
				Name: lang.T("late"),
				Count: lateCount,
				Score: lateScore,
				GrayBackGround: false,

			})
			for i := lateIndex; i < len(details); i++ { //rear
				detailsNew = append(detailsNew, details[i])
			}

			
			detailsNew = append(detailsNew, shipperResources.RankStatisticsEntityItemDetails{
				Name: lang.T("sum"),
				Count: 0,
				Score: tools.ToFloat64(scoreSum,2),
				GrayBackGround: false,
			})

			weekDataItem = append(weekDataItem, shipperResources.RankStatisticsEntityItem{
				Name: fmt.Sprintf("%d-%s", (kk+1), lang.T("week")),
				Rank: int(vv.Rank),
				Score: vv.FinalScore,
				Details:detailsNew,
				Date:vv.Date,
				Week: (kk+1),
				StartTime: carbon.Time2Carbon(vv.StartTime).Format("m-d"),
				EndTime: carbon.Time2Carbon(vv.EndTime).Format("m-d"),
			}) 
		}
	}
     

	 rnk.Items = weekDataItem
	 if len(weekDataItem) == 0 { 
		rnk.Items = []shipperResources.RankStatisticsEntityItem{}
	 }
	 rnk.ScoreZAxis = []int{1,2,3,4,5}
	 
	 rnk.ScoreRule = shipper.BaseService.GetAppConfig("auto_dispatch_rule_url_"+lang.Lang)
	return rnk,nil
}

func (shipper ShipperService) GetShipperRankGrowthRate(admin *models.Admin) (int,float64,float64,float64,string,string) { 
	db :=tools.GetDB()
	now :=carbon.Now().SetTimezone(configs.AsiaShanghai)

	month := now.Format("Y-m")
	//获取本月的每周的周一的日期 
	_,days1,_ := tools.GetDaysOfMonthAndDay(month,1) // 返回参数解析  1:days 本月中的所有的日期  2:days1 本月中所有的周一 3:days2 本月中中的自然周数据
	
	rankGrowthRate := float64(100)
	scoreGrowthRate := float64(100)

	var ranks []models.ShipperRankHistory
	db.Model(&models.ShipperRankHistory{}).Where("shipper_id = ?", admin.ID).Order("id desc").Limit(3).Find(&ranks)
	
	oldRank :=int(0)
	oldScore :=float64(0)
	newRank :=int(0)
	newScore :=float64(0)
	startTime :=""
	endTime :=""
	switch(len(ranks)){
		case 1:
			newRank = int(ranks[0].Rank)
			newScore = ranks[0].FinalScore
			startTime = carbon.Time2Carbon(ranks[0].StartTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d")
			endTime = carbon.Time2Carbon(ranks[0].EndTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d")
		case 2:

			newRank = int(ranks[0].Rank)
			newScore = ranks[0].FinalScore
			startTime = carbon.Time2Carbon(ranks[0].StartTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d")
			endTime = carbon.Time2Carbon(ranks[1].EndTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d")
			isMonday :=false
			for _, v := range days1 {
				if v==now.Format("Y-m-d") {
					isMonday = true
				}
			}
			if isMonday {
				oldRank = int(ranks[1].Rank)
				oldScore = ranks[1].FinalScore	

			}
		case 3:
			if now.DayOfWeek() == 1 {//今天周一的 取最新的两个记录 
				newRank = int(ranks[0].Rank)
				newScore = ranks[0].FinalScore
				startTime = carbon.Time2Carbon(ranks[0].StartTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d")
				endTime = carbon.Time2Carbon(ranks[0].EndTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d")
				oldRank = int(ranks[1].Rank)
				oldScore = ranks[1].FinalScore	
			}else{//其他情况取倒数第二个开始
				newRank = int(ranks[1].Rank)
				newScore = ranks[1].FinalScore
				startTime = carbon.Time2Carbon(ranks[1].StartTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d")
				endTime = carbon.Time2Carbon(ranks[1].EndTime).SetTimezone(configs.AsiaShanghai).Format("Y-m-d")
				oldRank = int(ranks[2].Rank)
				oldScore = ranks[2].FinalScore	
			}
			


	}
	
	
	
	if oldRank != 0 && newRank != 0 { 
		rankGrowthRate=(float64(newRank-oldRank)/float64(oldRank))*100   //升级比例 = (新等级-旧等级)/旧等级
	}
	if oldScore != 0 && newScore != 0 { 
		scoreGrowthRate=(float64(newScore-oldScore)/float64(oldScore))*100   //升级比例 = (新等级-旧等级)/旧等级
	}
	return newRank,tools.ToFloat64(rankGrowthRate,2),newScore,tools.ToFloat64(scoreGrowthRate,2),startTime,endTime
}