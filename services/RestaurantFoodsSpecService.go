package services

import (
	"errors"
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	restaurantRequest "mulazim-api/requests/RestaurantRequest"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type RestaurantFoodsSpecService struct {
	langUtil *lang.LangUtil
	language string
}

func NewRestaurantFoodsSpecService(c *gin.Context) *RestaurantFoodsSpecService {
	if c == nil || c.Request == nil {
		return &RestaurantFoodsSpecService{}
	}
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	restaurantFoodsSpecService := RestaurantFoodsSpecService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &restaurantFoodsSpecService
}

// DeleteFoodsSpec
func (svc *RestaurantFoodsSpecService) DeleteFoodsSpec(foodId int) error {
	tx := tools.GetDB()
	// 更新所有相关的记录
	if err := tx.Model(&models.FoodSpecType{}).Where("food_id = ?", foodId).Update("is_deleted", 1).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Model(&models.FoodSpecOption{}).Where("food_id = ?", foodId).Update("is_deleted", 1).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Model(&models.FoodSpec{}).Where("food_id = ?", foodId).Update("is_deleted", 1).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Model(&models.FoodSpecDetail{}).Where("food_id = ?", foodId).Update("is_deleted", 1).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 更新美食类型
	if err := tx.Model(&models.RestaurantFoods{}).Where("id = ?", foodId).Update("food_type", models.RestaurantFoodsTypeNormal).Error; err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()
	return nil

}

// 美食创建规格
func (svc *RestaurantFoodsSpecService) SaveFoodSpec(food models.RestaurantFoods, specs []restaurantRequest.RestaurantFoodsSpecTypes) error {
	db := tools.Db
	var foodsSpecCount int64
	db.Model(models.FoodSpecType{}).Where("food_id = ? and is_deleted = 0", food.ID).Count(&foodsSpecCount)
	if foodsSpecCount > 6 {
		return errors.New(fmt.Sprintf("%s:%s", svc.langUtil.T("spec_type_count_max_6"), tools.GetNameByLang(food, svc.language)))
	}

	// 验证多分打折
	var multiDiscountCount int64
	db.Model(models.FoodsMultipleDiscount{}).Where("state = 1 and start_time < ? and end_time > ? and food_id = ?",carbon.Now().Format("Y-m-d H:i:s"),carbon.Now().Format("Y-m-d H:i:s"),food.ID).Count(&multiDiscountCount)
	if(multiDiscountCount > 0){
		return errors.New("multi_discount_foods_enable_create_spec")
	}

	// 默认规格数量
	var priceTypeSpecCount int64
	db.Model(models.FoodSpecType{}).Where("price_type = 1 and food_id = ?", food.ID).Count(&priceTypeSpecCount)
	
	var foodsSpecOptionsCount int64
	db.Model(models.FoodSpecOption{}).Where("food_id = ? and is_deleted = 0", food.ID).Count(&foodsSpecOptionsCount)
	// optionsArr := [][]models.FoodSpecOption{}
	// 创建规格组和选项
	for _, v := range specs {
		if v.PriceType == 1 {
			priceTypeSpecCount ++
		}
		spec := models.FoodSpecType{
			RestaurantID: food.RestaurantID,
			FoodID:       food.ID,
			NameUg:       v.NameUg,
			NameZh:       v.NameZh,
			PriceType:    v.PriceType,
			State: v.State,
			CreatedAt:    time.Now(),
		}

		if err := db.Create(&spec).Error; err != nil {
			return err
		}

		// optionsIn := []models.FoodSpecOption{}
		options := v.RestaurantFoodsSpecOption
		for index, option := range options {
			foodsSpecOptionsCount++
			specOption := models.FoodSpecOption{
				SpecTypeID:   spec.ID,
				RestaurantID: food.RestaurantID,
				FoodID:       food.ID,
				NameUg:       option.NameUg,
				NameZh:       option.NameZh,
				IsSelected:   option.IsSelected,
				Price:        option.Price,
				State: option.State,
				Sort:  tools.If(option.Sort > 0, option.Sort, index),
				CreatedAt:    time.Now(),
			}
			if err := db.Create(&specOption).Error; err != nil {
				return err
			}
			if v.PriceType == 1 && option.IsSelected == 1 {
				db.Model(&models.RestaurantFoods{}).Where("id = ?", food.ID).Update("price", option.Price)
			} 
		} 
	}
	if priceTypeSpecCount == 0 {
		spec := models.FoodSpecType{
			RestaurantID: food.RestaurantID,
			FoodID:       food.ID,
			NameUg:       "مىقدار",
			NameZh:       "量",
			PriceType:    1,
			State: 1,
			CreatedAt:    time.Now(),
		}
		if err := db.Create(&spec).Error; err != nil {
			return err
		}

		var FoodQuantity models.FoodQuantity
		tools.Db.Model(FoodQuantity).Where("id = ?",food.FoodQuantityType).Find(&FoodQuantity)
		specOption := models.FoodSpecOption{
			SpecTypeID:   spec.ID,
			RestaurantID: food.RestaurantID,
			FoodID:       food.ID,
			NameUg:       fmt.Sprintf("%d%s", tools.ToInt(food.FoodQuantity),FoodQuantity.NameUg),
			NameZh:       fmt.Sprintf("%d%s", tools.ToInt(food.FoodQuantity),FoodQuantity.NameZh),
			IsSelected:   1,
			Price:        int(food.Price),
			State: 1,
			Sort:         0,
			CreatedAt:    time.Now(),
		}
		if err := db.Create(&specOption).Error; err != nil {
			return err
		}
	} 
	// 
	if foodsSpecOptionsCount > 1 {
		db.Model(&models.RestaurantFoods{}).Where("id = ?", food.ID).Update("food_type", models.RestaurantFoodsTypeSpec)
	}
	return nil
}



// 生成所有可能的组合
func (svc *RestaurantFoodsSpecService) SaveFoodSpecAndOptions(tx *gorm.DB, options [][]models.FoodSpecOption) error {
	var result [][]models.FoodSpecOption
	// 初始化组合数组
	combination := []models.FoodSpecOption{}
	var generate func(int)
	generate = func(depth int) {
		if depth == len(options) {
			// 创建一个新的切片来存储当前组合，避免后续修改影响已保存的结果
			currentCombination := make([]models.FoodSpecOption, 0, len(options))
			currentCombination = append(currentCombination, combination...)
			result = append(result, currentCombination)
			return
		}
		for _, val := range options[depth] {
			combination = append(combination, val)
			generate(depth + 1)
			combination = combination[:len(combination)-1]
		}
	}
	
	generate(0)
	for _, item := range result {
		// 创建spec
		var spec models.FoodSpec
		spec.RestaurantID = item[0].RestaurantID
		spec.FoodID = item[0].FoodID
		err := tx.Model(spec).Create(&spec).Error
		if err != nil {
			return err
		}
		// 创建spec_detail
		for _, option := range item {
			var specDetail models.FoodSpecDetail
			specDetail.RestaurantID = option.RestaurantID
			specDetail.FoodID = option.FoodID
			specDetail.SpecID = spec.ID
			specDetail.FoodsSpecOptionID = option.ID
			err = tx.Model(specDetail).Create(&specDetail).Error
			if err != nil {
				return err
			}

		}
	}
	return nil
}


// 美食规格列表
func (svc *RestaurantFoodsSpecService) GetFoodSpecList(foodId int) []models.FoodSpecType {
	var specTypes []models.FoodSpecType
	tools.Db.Model(models.FoodSpecType{}).Where("food_id =? and is_deleted = 0", foodId).Preload("FoodSpecOptions", func(db *gorm.DB) *gorm.DB {
		return db.Where("is_deleted = 0").Order("sort asc")
	}).Order("price_type desc,sort asc").Find(&specTypes)
	return specTypes
}

// 编辑规格时，删除移除的组
func (svc *RestaurantFoodsSpecService) GetEditSpecDeleteIds(oldSpecTypes []models.FoodSpecType,newSpecTypes []restaurantRequest.RestaurantFoodsSpecTypes) []int {
	var deleteSpecTypeIds []int
	for _, oldSpecType := range oldSpecTypes {
		found := false
		for _, newSpecType := range newSpecTypes {
			if newSpecType.ID == oldSpecType.ID {
				found = true
				break
			}
		}
		if !found {
			deleteSpecTypeIds = append(deleteSpecTypeIds, oldSpecType.ID)
		}
	}
	return deleteSpecTypeIds
}

// 编辑规格时，创建新加的规格组
func (svc *RestaurantFoodsSpecService) GetEditSpecAddSpecTypes(newSpecTypes []restaurantRequest.RestaurantFoodsSpecTypes)  []restaurantRequest.RestaurantFoodsSpecTypes {
	var AddSpecTypes []restaurantRequest.RestaurantFoodsSpecTypes
	for _, specType := range newSpecTypes {
		if specType.ID == 0 {
			AddSpecTypes = append(AddSpecTypes, specType)
		}
	}
	return AddSpecTypes
}


// 编辑规格时，删除移除规格项
func (svc *RestaurantFoodsSpecService) GetEditSpecDeleteOptionItems(newSpecTypes []restaurantRequest.RestaurantFoodsSpecTypes,oldSpecTypes []models.FoodSpecType) []int {
	var addSpecOptions []int
	// 更新内容项
	var newAllOptions []restaurantRequest.RestaurantFoodsSpecOption
	for _, specType := range newSpecTypes {
		if specType.ID == 0 {
			continue	
		}
		// 更新内容项
		newOptions := specType.RestaurantFoodsSpecOption
		for _ , option := range newOptions{
			newAllOptions = append(newAllOptions, option)
			if svc.IsEditSpecOption(option, oldSpecTypes) {
				addSpecOptions = append(addSpecOptions,option.ID)
			}
		}
	}
	// 被移除项
	for _,oldType := range oldSpecTypes{
		if oldType.ID == 0 {
			continue	
		}
		for _,oldOption := range oldType.FoodSpecOptions{
			hasOption := false
			for _,newAllOption := range newAllOptions{
				if newAllOption.ID == oldOption.ID {
					hasOption = true
					break
				}
			}
			if !hasOption {
				addSpecOptions = append(addSpecOptions,oldOption.ID)
			}
		}
	}
	return addSpecOptions
}

// // 编辑规格时，根据ID删除规格组和规格项
func (svc *RestaurantFoodsSpecService) DeleteFoodsSpecBySpecTypeIds(foodID int,specTypeIds []int) error {
	tools.Db.Model(models.FoodSpecType{}).Where("id in ?",specTypeIds).Updates(map[string]interface{}{"is_deleted":1})
	tools.Db.Model(models.FoodSpecOption{}).Where("spec_type_id in ?",specTypeIds).Updates(map[string]interface{}{"is_deleted":1})
	return nil
}

// // 编辑规格时，创建新加的规格项
func (svc *RestaurantFoodsSpecService) GetEditSpecAddOptionItems(newSpecTypes []restaurantRequest.RestaurantFoodsSpecTypes,oldSpecTypes []models.FoodSpecType)  []restaurantRequest.RestaurantFoodsSpecOption {
	var addSpecOptions []restaurantRequest.RestaurantFoodsSpecOption
	for _, specType := range newSpecTypes {
		if specType.ID == 0 {
			continue	
		}
		// 新建的规格项创建
		newOptions := specType.RestaurantFoodsSpecOption
		for _ , option := range newOptions{
			if option.ID == 0 || svc.IsEditSpecOption(option, oldSpecTypes){
				// 直接创建
				addSpecOptions = append(addSpecOptions,restaurantRequest.RestaurantFoodsSpecOption{
					NameUg: option.NameUg,
					NameZh: option.NameZh,
					Price: option.Price,
					IsSelected: option.IsSelected,
					State: option.State,
					SpecTypeID: specType.ID,
				} )
			}
		}
	}
	return addSpecOptions
}

// 判断规格项是否被修改
func (svc *RestaurantFoodsSpecService) IsEditSpecOption(newOption restaurantRequest.RestaurantFoodsSpecOption, oldSpecTypes []models.FoodSpecType) bool {
	for _, oldSpecType := range oldSpecTypes {
		for _, oldOption := range oldSpecType.FoodSpecOptions {
			if  newOption.ID == oldOption.ID && newOption.NameUg == oldOption.NameUg && newOption.NameZh == oldOption.NameZh && newOption.Price == oldOption.Price {
				return false
			}
		}
	}
	return true
}

// 创建规格项
func(svc *RestaurantFoodsSpecService) SaveFoodSpecOptions(food models.RestaurantFoods,options []restaurantRequest.RestaurantFoodsSpecOption) error {
	for index, option := range options {
		specOption := models.FoodSpecOption{
			RestaurantID: food.RestaurantID,
			FoodID: food.ID,
			NameUg:     option.NameUg,
			NameZh:     option.NameZh,
			Price:      option.Price,
			IsSelected: option.IsSelected,
			State:      option.State,
			Sort:       tools.If(option.Sort > 0, option.Sort, index),
			SpecTypeID: option.SpecTypeID,
		}
		err := tools.GetDB().Create(&specOption).Error
		if err != nil {
			return err
		}
	}
	return nil
}

// 删除规格项
func(svc *RestaurantFoodsSpecService) DeleteFoodsSpecOptions(optionIds []int) error {
	tools.GetDB().Model(models.FoodSpecOption{}).Where("id in ?",optionIds).Updates(map[string]interface{}{"is_deleted":1})
	return nil
}

// 编辑规格组名称
func(svc *RestaurantFoodsSpecService) EditSpecTypesName(specTypes []restaurantRequest.RestaurantFoodsSpecTypes) error {
	for _, specType := range specTypes {
		if specType.ID == 0 {
			continue
		}
		tools.GetDB().Model(models.FoodSpecType{}).Where("id = ?", specType.ID).Updates(map[string]interface{}{"name_ug": specType.NameUg, "name_zh": specType.NameZh})
	}
	return nil
}

// 根据规格组更新美食价格
func(svc *RestaurantFoodsSpecService) UpdateFoodPriceBySpec(foodID int,specTypes []restaurantRequest.RestaurantFoodsSpecTypes) error {
	for _, specType := range specTypes {
		tools.GetDB().Model(models.FoodSpecType{}).Where("id = ?", specType.ID).Updates(map[string]interface{}{"name_ug": specType.NameUg, "name_zh": specType.NameZh,"state":specType.State})
		if specType.PriceType == 1 {
			for _,option := range specType.RestaurantFoodsSpecOption{
				if option.IsSelected == 1 {
					tools.GetDB().Model(&models.RestaurantFoods{}).Where("id = ?", foodID).Update("price", option.Price)
				}
			}
		}
	}
	return nil
}

// 更新规格项目排序
func(svc *RestaurantFoodsSpecService) UpdateFoodSpecOptionsIsSelectedAndSort(specTypes []restaurantRequest.RestaurantFoodsSpecTypes) error {
	for _, specType := range specTypes {
		for idx, option := range specType.RestaurantFoodsSpecOption {
			if option.ID == 0 {
				continue
			}
			tools.GetDB().Model(models.FoodSpecOption{}).
				Where("id = ?", option.ID).
				Updates(map[string]interface{}{"state": option.State, "is_selected": option.IsSelected, "sort": tools.If(option.Sort > 0, option.Sort, idx)})
		}
	}
	return nil
}

func(svc *RestaurantFoodsSpecService) DeleteAllSpecOptionUpdateFoodType(foodID int) error {
	var options []models.FoodSpecOption
	tools.Db.Model(&options).Where("is_deleted = 0 and food_id = ?",foodID).Scan(&options)
	if len(options) <= 1 {
		err := tools.Db.Model(models.RestaurantFoods{}).Where("id = ?",foodID).UpdateColumn("food_type",0).Error
		if err != nil {
			return errors.New("fail")
		}
		if len(options) == 1 {
			err := tools.Db.Model(models.RestaurantFoods{}).Where("id = ?",foodID).UpdateColumn("price",options[0].Price).Error
			if err != nil {
				return errors.New("fail")
			}
		}
	}else{
		err := tools.Db.Model(models.RestaurantFoods{}).Where("id = ?",foodID).UpdateColumn("food_type",1).Error
		if err != nil {
			return errors.New("fail")
		}
	}
	return nil
}
