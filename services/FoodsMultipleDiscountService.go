package services

import (
	"errors"
	"fmt"
	"math"
	"mulazim-api/configs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/permissions"
	"mulazim-api/requests/foodsMultipleDiscount"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"sort"
	"time"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

// FoodsMultipleDiscountService 多份打折活动服务
type FoodsMultipleDiscountService struct {
	BaseService
	langUtil *lang.LangUtil
	language string
}

// NewFoodsMultipleDiscountService 创建多份打折活动服务
func NewFoodsMultipleDiscountService(c *gin.Context) *FoodsMultipleDiscountService {
	l, _ := c.Get("lang_util")
	language, _ := c.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := FoodsMultipleDiscountService{
		langUtil: &langUtil,
		language: language.(string),
	}
	return &service
}

// 获取多份打折活动列表
func (f FoodsMultipleDiscountService) GetMerchantFoodsMultipleDiscountList(request foodsMultipleDiscount.FoodsMultipleDiscountListRequest, pagination tools.Pagination, restaurantId int) ([]models.FoodsMultipleDiscount, int64) {
	// 初始化db
	db := tools.GetDB()

	query := db.Model(models.FoodsMultipleDiscount{}).
		Preload("Creator").
		Preload("Food").
		Where("restaurant_id = ?", restaurantId)


	// 添加查询条件
	if request.State != nil {
		nowStr := carbon.Now(configs.AsiaShanghai).ToDateTimeString()
		switch *request.State {
		case 0: // 新建/关闭
			query = query.Where("t_foods_multiple_discount.state = ?", 0)
		case 1: // 开启且在有效时间范围内
			query = query.Where("t_foods_multiple_discount.state = ?", 1)		
		case 2: // 暂停
			query = query.Where("t_foods_multiple_discount.state = ?", 2)
		case 3: // 结束/过期（仅根据结束时间判断）
			query = query.Where("t_foods_multiple_discount.end_time < ?", nowStr)
		}
	}
	var total int64
	var list []models.FoodsMultipleDiscount
	query.Count(&total)
	query.Order("created_at DESC").Scopes(scopes.Page(pagination.Page, pagination.Limit)).Find(&list)

	return list, total
}

// 创建多份打折活动
func (f FoodsMultipleDiscountService) Create(request foodsMultipleDiscount.FoodsMultipleDiscountCreateRequest, admin models.Admin, restaurant models.Restaurant,isMerchant ...bool) error {
	db := tools.GetDB()

	// 解析开始和结束日期
	startTime, err :=  time.Parse(time.DateTime, request.StartTime)
	if err != nil {
		return errors.New("begin_date_format_error")
	}

	endTime, err := time.Parse(time.DateTime, request.EndTime)
	if err != nil {
		return errors.New("end_date_format_error")
	}

	// 检查日期有效性
	if startTime.After(endTime) {
		return errors.New("second_time_must_be_after_first")
	}


	query := db.Model(models.RestaurantFoods{}).Where("id = ?", request.FoodID)

	//判断是不是商家
	isRestaurantManager := permissions.IsRestaurantManager(admin)
	if isRestaurantManager {
		query = query.Where("restaurant_id = ?", restaurant.ID)
	}
	

	// 查询美食是否都存在
	var food models.RestaurantFoods
	if err := query.Preload("Restaurant").First(&food).Error; err != nil {
		tools.Logger.Errorf("餐厅美食不存在: %v", err)
		return errors.New("restaurant_foods_not_found")
	}

	if food.FoodType == models.FoodsComboItemFoodTypeSpec {
		// 规格美食不能创建多分打折
		return errors.New("spec_food_cannot_create_multiple_discount")
	}
	
	state :=models.FoodsMultipleDiscountStateClose // 默认新建
	if len(isMerchant)>0 && isMerchant[0] { //商家创建的时候 监测是否与其他活动冲突
		// 判断是不是有其他活动
		hasError := f.CheckHasOtherActivity(f.language, request.FoodID, request.StartTime, request.EndTime,restaurant.ID)
		if hasError != nil {
			return hasError
		}
		state = models.FoodsMultipleDiscountStateOpen
	}
	// 创建折扣记录
	discount := models.FoodsMultipleDiscount{
		CityId:           int64(food.Restaurant.CityID),
		AreaId:           int64(food.Restaurant.AreaID),
		RestaurantId:     int64(food.RestaurantID),
		CreatorId:        int64(admin.ID),
		CreatorType:      int64(admin.Type),
		NameUg:           food.NameUg,
		NameZh:           food.NameZh,
		StartTime:        carbon.Parse(request.StartTime,configs.AsiaShanghai).Carbon2Time(),
		EndTime:          carbon.Parse(request.EndTime,configs.AsiaShanghai).Carbon2Time(),
		FoodId:           int64(request.FoodID),
		Discount2Percent: request.Discount2Percent,
		Discount3Percent: request.Discount3Percent,
		Discount4Percent: request.Discount4Percent,
		Discount5Percent: request.Discount5Percent,
		State:            state, // 默认新建
	}

	// 保存到数据库
	if err := db.Create(&discount).Error; err != nil {
		return errors.New("create_fail")
	}


	var details []models.FoodsMultipleDiscountDetail

	// 创建第一个
	// 第一个折扣，固定价格无折扣
	details = append(details, models.FoodsMultipleDiscountDetail{
		ActivityId:      discount.ID,
		DiscountIndex:   1,
		DiscountPercent: 0,
		OriginalPrice:   int64(food.Price),
		DiscountPrice:   int64(food.Price),
	})


	// 使用映射和循环优化折扣详情创建
	discountMap := map[int64]float64{
		2: request.Discount2Percent,
		3: request.Discount3Percent,
		4: request.Discount4Percent,
		5: request.Discount5Percent,
	}

	// 批量创建折扣详情
	for index, percent := range discountMap {
		detail := models.FoodsMultipleDiscountDetail{
			ActivityId:      discount.ID,       // 关联到主活动表的ID
			DiscountIndex:   index,             // 折扣适用的第几份（2-5）
			DiscountPercent: percent,           // 折扣百分比，例如80表示打8折（80%）
			OriginalPrice:   int64(food.Price), // 食品原价（单位：分）
			DiscountPrice:   int64(float64(food.Price) - math.Round(float64(food.Price)*percent/100)),
		}
		details = append(details, detail)
	}

	// 排序：根据 DiscountIndex 从小到大排序
	sort.Slice(details, func(i, j int) bool {
		return details[i].DiscountIndex < details[j].DiscountIndex
	})

	// 批量创建
	if err := db.Create(&details).Error; err != nil {
		return errors.New("create_detail_fail")
	}
	return nil
}

// ChangeState 更改多份打折活动状态
func (f FoodsMultipleDiscountService) ChangeState(request foodsMultipleDiscount.FoodsMultipleDiscountChangeStateRequest, admin models.Admin) (error, int) {
	db := tools.GetDB()

	// 查询多份打折活动
	var discount models.FoodsMultipleDiscount
	if err := db.First(&discount, request.ID).Error; err != nil {
		return errors.New("not_found"), 0
	}

	// 定义状态
	var state int
	// 如果状态是开启，则改为暂停
	if discount.IsOpen() {
		state = models.FoodsMultipleDiscountStatePause
	}

	// 如果状态是暂停或者关闭，则改为开启
	if discount.IsPause() || discount.IsClose() {

		// 判断是不是有其他活动
		hasError := f.CheckHasOtherActivity(f.language, int(discount.FoodId), discount.StartTime.Format(time.DateTime), discount.EndTime.Format(time.DateTime),int(discount.RestaurantId))
		if hasError != nil {
			return hasError, 0
		}

		state = models.FoodsMultipleDiscountStateOpen
	}

	// 如果是过期 改成过期
	if discount.IsEnd(){
		state = models.FoodsMultipleDiscountStateExpired
	}

	// 修改状态
	if err := db.Model(&discount).Update("state", state).Error; err != nil {
		return errors.New("update_fail"), 0
	}

	return nil, state
}

// GetDetail 获取多份打折活动详情(商家)
func (f FoodsMultipleDiscountService) GetMerchantFoodsMultipleDiscountDetail(request foodsMultipleDiscount.FoodsMultipleDiscountDetailRequest, restaurantId int) (models.FoodsMultipleDiscountDetailQuery, error, bool) {
	db := tools.GetDB()
	var queryResult models.FoodsMultipleDiscountDetailQuery

	var _discount models.FoodsMultipleDiscount
	db.Model(models.FoodsMultipleDiscount{}).
		Preload("Creator", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "name") // 预加载创建者信息
		}).
		Preload("Food", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "name_ug", "name_zh", "price", "image") // 预加载菜品信息
		}).
		Preload("Details").
		Where("id = ?", request.ID).
		Where("restaurant_id = ?",restaurantId).
		First(&_discount)
	if _discount.ID == 0 {
		return queryResult, errors.New("not_found"), false
	}


	err := db.Model(models.FoodsMultipleDiscountDetailQuery{}).
		Select(`
            t_foods_multiple_discount.*,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 1, 1, 0)) AS discount1_saled_count,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 2, 1, 0)) AS discount2_saled_count,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 3, 1, 0)) AS discount3_saled_count,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 4, 1, 0)) AS discount4_saled_count,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 5, 1, 0)) AS discount5_saled_count,
            SUM(IF(t_foods_multiple_discount_log.discount_index IN (1,2,3,4,5), 1, 0)) AS total_saled_count,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 1, t_foods_multiple_discount_log.price, 0)) AS discount1_saled_amount,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 2, t_foods_multiple_discount_log.price, 0)) AS discount2_saled_amount,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 3, t_foods_multiple_discount_log.price, 0)) AS discount3_saled_amount,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 4, t_foods_multiple_discount_log.price, 0)) AS discount4_saled_amount,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 5, t_foods_multiple_discount_log.price, 0)) AS discount5_saled_amount,
            SUM(IF(t_foods_multiple_discount_log.discount_index IN (1,2,3,4,5), t_foods_multiple_discount_log.price, 0)) AS total_saled_amount
        `).
		Joins(`
        LEFT JOIN t_foods_multiple_discount_log ON t_foods_multiple_discount_log.activity_id = t_foods_multiple_discount.id
        LEFT JOIN t_order ON t_order.id = t_foods_multiple_discount_log.order_id AND t_order.state = 7
        LEFT JOIN t_order_today ON t_order_today.id = t_foods_multiple_discount_log.order_id AND t_order_today.state = 7
    `).
		Preload("Creator", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "name") // 预加载创建者信息
		}).
		Preload("Food", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "name_ug", "name_zh", "price", "image") // 预加载菜品信息
		}).
		Preload("Details").
		Where("t_foods_multiple_discount.id = ?", request.ID).
		Where("(t_order.state = 7 OR t_order_today.state = 7)").
		First(&queryResult).
		Error

	if err != nil {
		return models.FoodsMultipleDiscountDetailQuery{}, fmt.Errorf("查询失败: %w", err), false
	}
	// 是否有订单
	var hasOrder bool
	db.Model(models.FoodsMultipleDiscountLog{}).Where("activity_id = ?",request.ID).Select("COUNT(*) > 0").Scan(&hasOrder)

	// 如果数据为空
	if queryResult.ID == 0 {
		queryResult.ID = _discount.ID
		queryResult.FoodId = _discount.FoodId
		queryResult.CityId = _discount.CityId
		queryResult.AreaId = _discount.AreaId
		queryResult.RestaurantId = _discount.RestaurantId
		queryResult.CreatorId = _discount.CreatorId
		queryResult.CreatorType = _discount.CreatorType
		queryResult.NameUg = _discount.NameUg
		queryResult.NameZh = _discount.NameZh
		queryResult.StartTime = _discount.StartTime
		queryResult.EndTime = _discount.EndTime
		queryResult.Discount2Percent = _discount.Discount2Percent
		queryResult.Discount3Percent = _discount.Discount3Percent
		queryResult.Discount4Percent = _discount.Discount4Percent
		queryResult.Discount5Percent = _discount.Discount5Percent
		queryResult.State = _discount.State
		queryResult.CreatedAt = _discount.CreatedAt
		queryResult.UpdatedAt = _discount.UpdatedAt

		queryResult.Food = _discount.Food
		queryResult.Creator = _discount.Creator
		queryResult.Details = _discount.Details
	}
	return queryResult, nil ,hasOrder
}

// 获取多份打折活动详情(cms)
func (f FoodsMultipleDiscountService) GetCmsFoodsMultipleDiscountDetail(request foodsMultipleDiscount.FoodsMultipleDiscountDetailRequest) (models.FoodsMultipleDiscount, error) {
	db := tools.GetDB()

	// 查询多份打折活动
	var discount models.FoodsMultipleDiscount
	if err := db.Preload("Food").Preload("Restaurant").First(&discount, request.ID).Error; err != nil {
		return models.FoodsMultipleDiscount{}, errors.New("not_found")
	}

	return discount, nil
}

// 获取多份打折活动详情(cms)
func (f FoodsMultipleDiscountService) GetCmsFoodsMultipleDiscountDetailView(request foodsMultipleDiscount.FoodsMultipleDiscountDetailRequest) (models.FoodsMultipleDiscountDetailQuery, error) {
	db := tools.ReadDb1
	var queryResult models.FoodsMultipleDiscountDetailQuery

	var _discount models.FoodsMultipleDiscount
	db.Model(models.FoodsMultipleDiscount{}).
		Preload("Creator", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "name") // 预加载创建者信息
		}).
		Preload("Food", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "name_ug", "name_zh", "price", "image") // 预加载菜品信息
		}).
		Preload("Restaurant").
		Preload("Area").
		Preload("City").
		Preload("Details").
		Where("id = ?", request.ID).
		First(&_discount)
	if _discount.ID == 0 {
		return queryResult, errors.New("not_found")
	}


	err := db.Model(models.FoodsMultipleDiscountDetailQuery{}).
		Select(`
            t_foods_multiple_discount.*,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 1, 1, 0)) AS discount1_saled_count,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 2, 1, 0)) AS discount2_saled_count,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 3, 1, 0)) AS discount3_saled_count,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 4, 1, 0)) AS discount4_saled_count,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 5, 1, 0)) AS discount5_saled_count,
            SUM(IF(t_foods_multiple_discount_log.discount_index IN (1,2,3,4,5), 1, 0)) AS total_saled_count,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 1, t_foods_multiple_discount_log.price, 0)) AS discount1_saled_amount,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 2, t_foods_multiple_discount_log.price, 0)) AS discount2_saled_amount,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 3, t_foods_multiple_discount_log.price, 0)) AS discount3_saled_amount,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 4, t_foods_multiple_discount_log.price, 0)) AS discount4_saled_amount,
            SUM(IF(t_foods_multiple_discount_log.discount_index = 5, t_foods_multiple_discount_log.price, 0)) AS discount5_saled_amount,
            SUM(IF(t_foods_multiple_discount_log.discount_index IN (1,2,3,4,5), t_foods_multiple_discount_log.price, 0)) AS total_saled_amount
        `).
		Joins(`
        LEFT JOIN t_foods_multiple_discount_log ON t_foods_multiple_discount_log.activity_id = t_foods_multiple_discount.id
        LEFT JOIN t_order ON t_order.id = t_foods_multiple_discount_log.order_id AND t_order.state = 7
        LEFT JOIN t_order_today ON t_order_today.id = t_foods_multiple_discount_log.order_id AND t_order_today.state = 7
    `).
		Preload("Creator", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "name") // 预加载创建者信息
		}).
		Preload("Food", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "name_ug", "name_zh", "price", "image") // 预加载菜品信息
		}).
		Preload("Details").
		Where("t_foods_multiple_discount.id = ?", request.ID).
		Where("(t_order.state = 7 OR t_order_today.state = 7)").
		First(&queryResult).
		Error

	if err != nil {
		return queryResult, fmt.Errorf("查询失败: %w", err)
	}

	// 如果数据为空
	if queryResult.ID == 0 {
		queryResult.ID = _discount.ID
		queryResult.FoodId = _discount.FoodId
		queryResult.CityId = _discount.CityId
		queryResult.AreaId = _discount.AreaId
		queryResult.RestaurantId = _discount.RestaurantId
		queryResult.CreatorId = _discount.CreatorId
		queryResult.CreatorType = _discount.CreatorType
		queryResult.NameUg = _discount.NameUg
		queryResult.NameZh = _discount.NameZh
		queryResult.StartTime = _discount.StartTime
		queryResult.EndTime = _discount.EndTime
		queryResult.Discount2Percent = _discount.Discount2Percent
		queryResult.Discount3Percent = _discount.Discount3Percent
		queryResult.Discount4Percent = _discount.Discount4Percent
		queryResult.Discount5Percent = _discount.Discount5Percent
		queryResult.State = _discount.State
		queryResult.CreatedAt = _discount.CreatedAt
		queryResult.UpdatedAt = _discount.UpdatedAt

		queryResult.Food = _discount.Food
		queryResult.Creator = _discount.Creator
		queryResult.Details = _discount.Details
		queryResult.Restaurant = _discount.Restaurant
		queryResult.Area = _discount.Area
		queryResult.City = _discount.City
	}
	return queryResult, nil

}

// GetCmsFoodsMultipleDiscountOrderList
func (f FoodsMultipleDiscountService) GetCmsFoodsMultipleDiscountOrderList(params foodsMultipleDiscount.FoodsMultipleDiscountOrderListRequest) ([]models.FoodsMultipleDiscountOrderLog,int64) {
	db := tools.ReadDb1
	// 查询多份打折活动
	var totalCount int64
	var discountLogList []models.FoodsMultipleDiscountOrderLog
	query := db.Model(models.FoodsMultipleDiscountLog{}).
		Select("t_foods_multiple_discount_log.order_id, COUNT(*) as count,SUM(t_foods_multiple_discount_log.price) as total_price,SUM(t_foods_multiple_discount_log.original_price) as total_original_price").
		Joins("LEFT JOIN t_order ON t_order.id = t_foods_multiple_discount_log.order_id AND t_order.state IN (7,10)").
		Joins("LEFT JOIN t_order_today ON t_order_today.id = t_foods_multiple_discount_log.order_id AND t_order_today.state IN (7,10)").
		Where("t_foods_multiple_discount_log.activity_id = ?", params.ID).
		Where("(t_order.state IN (7,10) OR t_order_today.state IN (7,10))").
		Group("t_foods_multiple_discount_log.order_id").
		Preload("Order.User").
		Preload("OrderToday.User")
		if params.BeginTime != "" {
			query.Where("t_foods_multiple_discount_log.created_at >= ?", params.BeginTime+" 00:00:00")
		}
		if params.EndTime != "" {
			query.Where("t_foods_multiple_discount_log.created_at <= ?", params.EndTime+" 23:59:59")
		}
		if params.Search != "" {
			query.Where("(t_order.order_id = ? OR t_order_today.order_id = ?)",params.Search,params.Search)
		}
		query.Order("t_foods_multiple_discount_log.created_at DESC").Count(&totalCount).
		Scopes(scopes.Page(params.Page, params.Limit)).
		Find(&discountLogList)
	return discountLogList,totalCount
}







// 获取多份打折活动详情(cms)
func (f FoodsMultipleDiscountService) GetCmsFoodsMultipleDiscountDetailViewHeader(discountID int) map[string]interface{} {
		// 总订单数，总订单数量，总订单金额，总折扣金额，总折扣率
		db := tools.ReadDb1
		var res map[string]interface{}
		sql := `SELECT
					SUM(result.order_price) AS complate_order_amount,
					SUM(result.saled_count) AS saled_count,
					count(1) AS complate_order_count,
					sum(discount_amount) AS discount_amount
				FROM
					(
					SELECT
						count(1) AS saled_count,
						IFNULL( t_order_today.order_price, 0 )+ IFNULL( t_order.order_price, 0 ) AS order_price ,
						sum( log.original_price - log.price) AS discount_amount 
					FROM
						t_foods_multiple_discount_log AS log
						LEFT JOIN t_order_today ON log.order_id = t_order_today.id AND t_order_today.state IN ( 7, 10 )
						LEFT JOIN t_order ON log.order_id = t_order.id  AND t_order.state IN ( 7, 10 ) 
					WHERE
						( t_order_today.state IN ( 7, 10 ) OR t_order.state IN ( 7, 10 ) )
						AND log.activity_id = ?
					GROUP BY log.order_id
					) as result`
		db.Raw(sql, discountID).Scan(&res)
		return res
}

func (f FoodsMultipleDiscountService) GetCmsFoodsMultipleDiscountListHeader(params foodsMultipleDiscount.FoodsMultipleDiscountListHeaderRequest) map[string]interface{} {
		// 总订单数，总订单数量，总订单金额，总折扣金额，总折扣率
		db := tools.ReadDb1
		var res map[string]interface{}
		sql := `SELECT
					SUM(result.order_price) AS complate_order_amount,
					SUM(result.discount_price) AS discount_amount,
					count(1) AS complate_order_count
				FROM
					(
					SELECT
						sum( log.original_price - log.price ) AS discount_price,
						IFNULL( t_order_today.order_price, 0 )+ IFNULL( t_order.order_price, 0 ) AS order_price 
					FROM
						t_foods_multiple_discount_log AS log
						LEFT JOIN t_order_today ON log.order_id = t_order_today.id AND t_order_today.state IN ( 7, 10 )
						LEFT JOIN t_order ON log.order_id = t_order.id  AND t_order.state IN ( 7, 10 ) 
					WHERE
						( t_order_today.state IN ( 7, 10 )  OR t_order.state IN ( 7, 10 )) `

		if params.CityID > 0 {
			sql += fmt.Sprintf(" AND (t_order.city_id = %d OR t_order_today.city_id = %d)", params.CityID, params.CityID)
		}
		if params.AreaID > 0 {
			sql += fmt.Sprintf(" AND (t_order.area_id = %d OR t_order_today.area_id = %d)", params.AreaID, params.AreaID)
		}
		sql += " GROUP BY log.order_id) as result"
		db.Raw(sql).Scan(&res)
		return res
}

// 删除多份打折活动
func (f FoodsMultipleDiscountService) Delete(request foodsMultipleDiscount.FoodsMultipleDiscountDeleteRequest, admin models.Admin) error {
	// 初始化db
	db := tools.GetDB()

	// 查询多份打折活动
	var discount models.FoodsMultipleDiscount
	if err := db.First(&discount, request.ID).Error; err != nil {
		return errors.New("not_found")
	}

	// 判断能不能删除
	if !f.CanDeleteFoodsMultipleDiscount(discount, admin) {
		return errors.New("you_are_not_creator_can_not_delete")
	}

	// 判断是否使用多份打折下单
	if discount.HasMultipleDiscountOrder(db) {
		return errors.New("foods_multiple_discount_activity_has_record_can_not_delete")
	}

	// 删除多份打折活动
	if err := db.Delete(&discount).Error; err != nil {
		return errors.New("delete_fail")
	}

	// 删除多份打折活动详情
	if err := db.Where("activity_id = ?", request.ID).Delete(&models.FoodsMultipleDiscountDetail{}).Error; err != nil {
		return errors.New("delete_detail_fail")
	}
	return nil
}

// 修改多份打折活动
func (f FoodsMultipleDiscountService) Update(request foodsMultipleDiscount.FoodsMultipleDiscountUpdateRequest, admin models.Admin) error {
	db := tools.GetDB()

	// 查询多份打折活动
	var discount models.FoodsMultipleDiscount
	if err := db.First(&discount, request.ID).Error; err != nil {
		return errors.New("not_found")
	}

	// 判断能不能修改
	if !f.CanUpdateFoodsMultipleDiscount(discount, admin) {
		return errors.New("you_are_not_creator_can_not_update")
	}

	// 判断是否使用多份打折下单
	if discount.HasMultipleDiscountOrder(db) {
		return errors.New("foods_multiple_discount_activity_has_record_can_not_update")
	}

	// 判断是不是有其他活动
	// hasError := f.CheckHasOtherActivity(f.language, int(discount.FoodId), request.StartTime, request.EndTime)
	// if hasError != nil {
	// 	return hasError
	// }

	// 更新多份打折活动
	if err := db.Model(&discount).Updates(map[string]interface{}{
		"discount2_percent": request.Discount2Percent,
		"discount3_percent": request.Discount3Percent,
		"discount4_percent": request.Discount4Percent,
		"discount5_percent": request.Discount5Percent,
		"start_time":        request.StartTime,
		"end_time":          request.EndTime,
	}).Error; err != nil {
		return errors.New("update_fail")
	}


	// 删除旧的折扣详情
	if err := db.Where("activity_id = ?", discount.ID).Delete(&models.FoodsMultipleDiscountDetail{}).Error; err != nil {
		return errors.New("delete_details_fail")
	}

	// 查询美食
	food := models.RestaurantFoods{}
	db.Model(models.RestaurantFoods{}).Where("id = ?", discount.FoodId).First(&food)

	if food.FoodType == models.FoodsComboItemFoodTypeSpec {
		// 规格美食不能创建多分打折
		return errors.New("spec_food_cannot_create_multiple_discount")
	}

	var details []models.FoodsMultipleDiscountDetail
	// 第一个折扣，固定价格无折扣
	details = append(details, models.FoodsMultipleDiscountDetail{
		ActivityId:      discount.ID,
		DiscountIndex:   1,
		DiscountPercent: 0,
		OriginalPrice:   int64(food.Price),
		DiscountPrice:   int64(food.Price),
	})


	// 使用映射和循环优化折扣详情创建
	discountMap := map[int64]float64{
		2: request.Discount2Percent,
		3: request.Discount3Percent,
		4: request.Discount4Percent,
		5: request.Discount5Percent,
	}

	// 批量创建折扣详情
	for index, percent := range discountMap {
		detail := models.FoodsMultipleDiscountDetail{
			ActivityId:      discount.ID,       // 关联到主活动表的ID
			DiscountIndex:   index,             // 折扣适用的第几份（2-5）
			DiscountPercent: percent,           // 折扣百分比，例如80表示打8折（80%）
			OriginalPrice:   int64(food.Price), // 食品原价（单位：分）
			DiscountPrice:   int64(float64(food.Price) - math.Round(float64(food.Price)*percent/100)),
		}
		details = append(details, detail)
	}

	// 排序：根据 DiscountIndex 从小到大排序
	sort.Slice(details, func(i, j int) bool {
		return details[i].DiscountIndex < details[j].DiscountIndex
	})

	// 批量创建
	if err := db.Create(&details).Error; err != nil {
		return errors.New("create_detail_fail")
	}

	return nil
}

// 获取多份打折活动列表
func (f FoodsMultipleDiscountService) GetCmsFoodsMultipleDiscountListList(request foodsMultipleDiscount.FoodsMultipleDiscountListRequest) ([]models.FoodsMultipleDiscount, int64, error) {
	db := tools.GetDB()

	// 查询多份打折活动
	query := db.Model(&models.FoodsMultipleDiscount{}).
		Preload("Creator").
		Preload("City").
		Preload("Area").
		Preload("Restaurant").
		Preload("Food")

	// 添加查询条件
	if request.StartTime != "" {
		query = query.Where("t_foods_multiple_discount.start_time >= ?", request.StartTime)
	}

	// 添加查询条件
	if request.EndTime != "" {
		query = query.Where("t_foods_multiple_discount.end_time <= ?", request.EndTime)
	}

	// 添加查询条件
	if request.AreaId != 0 {
		query = query.Where("t_foods_multiple_discount.area_id = ?", request.AreaId)
	}

	// 添加查询条件
	if request.CityId != 0 {
		query = query.Where("t_foods_multiple_discount.city_id = ?", request.CityId)
	}

	// 添加查询条件
	if request.State != nil {
		nowStr := time.Now().Format(time.DateTime) // 提前计算，避免重复格式化

		switch *request.State {
		case models.FoodsMultipleDiscountStateClose,
			models.FoodsMultipleDiscountStateOpen,
			models.FoodsMultipleDiscountStatePause:
			{
				query = query.Where("t_foods_multiple_discount.state = ? AND t_foods_multiple_discount.end_time >= ?", request.State, nowStr)

			}

		case models.FoodsMultipleDiscountStateExpired:
			{
				query = query.Where("t_foods_multiple_discount.end_time < ?", nowStr)
			}
		}
	}

	// 添加查询条件
	if request.Search != "" {
		query = query.Joins("JOIN t_restaurant ON t_foods_multiple_discount.restaurant_id = t_restaurant.id").
			Where("t_restaurant.name_"+f.language+" LIKE ?", "%"+request.Search+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 添加排序
	query = query.
//		Order(`
//    CASE
//        WHEN t_foods_multiple_discount.state = 0 AND t_foods_multiple_discount.end_time >= NOW() THEN 0
//        WHEN t_foods_multiple_discount.state = 1 AND t_foods_multiple_discount.end_time >= NOW() THEN 1
//        WHEN t_foods_multiple_discount.state = 2 AND t_foods_multiple_discount.end_time >= NOW() THEN 2
//        WHEN t_foods_multiple_discount.end_time < NOW() THEN 3
//        ELSE 4
//    END ASC
//`).
		Order("created_at DESC")           // 次级排序
	// 分页查询
	var discounts []models.FoodsMultipleDiscount
	if err := query.Scopes(scopes.Page(request.Page, request.Limit)).Find(&discounts).Error; err != nil {
		return nil, 0, err
	}

	// 返回查询结果
	return discounts, total, nil

}

// 检查是否存在其他活动
func (f FoodsMultipleDiscountService) CheckHasOtherActivity(lang string, foodId int, startTime string, endTime string,resIds ...int) error {
	if len(resIds) > 0 {
		ct,resName :=f.CheckFoodsMultiDiscountCount(resIds[0], startTime, endTime,lang)
		designatedActivityCount :=configs.MyApp.StoreMultiDiscountCount
		cfg :=f.GetAppConfigInfo("store_multi_discount_count")
		if cfg.ID > 0 && cfg.Value != "" {
			designatedActivityCount = tools.ToInt(cfg.Value)
		}
		if ct >= int64(designatedActivityCount) { //存在多个正在进行的活动
			return fmt.Errorf(f.langUtil.T("food_has_multiple_discount"), fmt.Sprintf(f.langUtil.T("multi_discount_count"),resName,ct))
		}
	}
	
	_, foodName := f.CheckFoodsMultipleDiscount(lang, foodId, startTime, endTime)
	if foodName != "" {
		return fmt.Errorf(f.langUtil.T("food_has_multiple_discount"), foodName)
	}
	_, foodName,tp := f.CheckFoodsSeckill(lang, foodId, startTime, endTime)
	if foodName != "" {
		msgKey :="food_has_seckill"
		if tp == 2 {
			msgKey ="food_has_special"
		}
		return fmt.Errorf(f.langUtil.T(msgKey), foodName)
	}
	_, foodName = f.CheckFoodsPref(lang, foodId, startTime, endTime)
	if foodName != "" {
		return fmt.Errorf(f.langUtil.T("food_has_pref"), foodName)
	}
	resId :=0
	if len(resIds) > 0 {
		resId = resIds[0]
	}
	_, foodName = f.CheckFoodsMarketing(lang, foodId, resId,startTime, endTime)
	if foodName != "" {
		return fmt.Errorf(f.langUtil.T("food_has_marketing"), foodName)
	}
	return nil
}


// 判断能不能修改
func (f FoodsMultipleDiscountService) CanUpdateFoodsMultipleDiscount(discount models.FoodsMultipleDiscount, admin models.Admin) bool {
	if admin.ID == 0 {
		return false
	}
	// 如果是超级管理员 可以直接修改
	if permissions.IsOwner(admin){
		return true
	}
	if permissions.IsRestaurantManager(admin){
		restaurantID, err := permissions.GetRestaurantIdByMerchantAdmin(admin)
		if err!=nil{
			tools.Logger.Error("修改多份打折活动失败:无法根据Admin获取餐厅ID")
			return false
		}
		if discount.RestaurantId == int64(restaurantID) {
			return true
		}
	}
	if discount.CreatorId  == int64(admin.ID){
		return true
	}
	return false
}

// 判断能不能删除
func (f FoodsMultipleDiscountService) CanDeleteFoodsMultipleDiscount(discount models.FoodsMultipleDiscount, admin models.Admin) bool {
	if admin.ID == 0 {
		return false
	}
	// 如果是超级管理员 可以直接修改
	if permissions.IsOwner(admin){
		return true
	}
	if permissions.IsRestaurantManager(admin){
		restaurantID, err := permissions.GetRestaurantIdByMerchantAdmin(admin)
		if err!=nil{
			tools.Logger.Error("删除多份打折活动失败:无法根据Admin获取餐厅ID")
			return false
		}
		if discount.RestaurantId == int64(restaurantID) {
			return true
		}
	}
	if discount.CreatorId  == int64(admin.ID){
		return true
	}
	return false
}
