package services

import (
	"errors"
	"mulazim-api/lang"
	"mulazim-api/models"
	restaurantRequest "mulazim-api/requests/RestaurantRequest"
	"mulazim-api/tools"
	"time"

	"github.com/gin-gonic/gin"
)

type FoodsComboService struct {
	LangUtil *lang.LangUtil
	Lang     string
	BaseService
}

func NewFoodsComboService(ctx *gin.Context) *FoodsComboService {
	l, _ := ctx.Get("lang_util")
	language, _ := ctx.Get("lang")
	langUtil := l.(lang.LangUtil)
	service := FoodsComboService{
		LangUtil: &langUtil,
		Lang:     language.(string),
	}
	return &service
}

// 创建套餐美食 与 子美食之间的关系
func (svc *FoodsComboService) CreateFoodsComboItems(
	food models.RestaurantFoods, foodsComboItems []restaurantRequest.RestaurantFoodsComboItems,
) ([]models.FoodsComboItem, error) {

	// 套餐子美食数量不能为空
	if len(foodsComboItems) == 0 {
		return nil, errors.New("foods_combo_items_empty")
	}
	for _, foodsComboItem := range foodsComboItems {
		// 无法绑定空餐厅美食
		if foodsComboItem.FoodID == 0 {
			return nil, errors.New("foods_combo_items_food_id_empty")
		}
		// 套餐美食的子美食不能是套餐
		if foodsComboItem.FoodType == models.RestaurantFoodsTypeCombo {
			return nil, errors.New("foods_combo_items_can_not_be_combo")
		}
	}

	// 创建
	var (
		db                     = tools.GetDB()
		newFoodsComboItems = make([]models.FoodsComboItem, 0)
	)
	for _, foodsComboItem := range foodsComboItems {
		// 如果是规格子美食，则规格编号不能为空
		if foodsComboItem.FoodType == models.FoodsComboItemFoodTypeSpec && len(foodsComboItem.OptionIds) == 0 {
			return nil, errors.New("foods_combo_items_spec_id_empty")
		}
		specId ,_:= svc.SaveFoodSpec(food.RestaurantID,foodsComboItem.FoodID, foodsComboItem.OptionIds)
		_newItem := models.FoodsComboItem{
			ComboID:  food.ID,
			FoodID:   foodsComboItem.FoodID,
			FoodType: foodsComboItem.FoodType, // 美食类型 (0: 普通美食, 1: 规格美食)
			SpecID:   specId,   // 已选规格编号
			Count:    foodsComboItem.Count,
			State:    models.FoodsComboItemStateOn,
		}
		newFoodsComboItems = append(newFoodsComboItems, _newItem)
	}

	err := db.Create(&newFoodsComboItems).Error
	return newFoodsComboItems, err

}

// 创建套餐分组
func (svc *FoodsComboService) CreateFoodsComboGroup(food models.RestaurantFoods) int {
	db := tools.GetDB()
	now := time.Now()
	var group models.FoodsGroup
	db.Where("restaurant_id = ?", food.RestaurantID).Where("name_zh = ?", "套餐").First(&group)
	if group.ID == 0 {
		group = models.FoodsGroup{
			RestaurantID: food.RestaurantID,
			NameUg: "يۈرۈشلۈك",
			NameZh: "套餐",
			Weight:  -1,
			State:   1,
			ReviewState: 2,
			CreatedAt: &now,
		}
		err := db.Create(&group).Error
		if err != nil {
			tools.Logger.Error(err.Error())
			return 0
		}
	}
	db.Model(models.RestaurantFoods{}).Where("id = ?", food.ID).Update("foods_group_id", group.ID)
	return group.ID
}
