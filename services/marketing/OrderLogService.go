package marketing

import (
	"errors"
	"fmt"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/models"
	"mulazim-api/tools"
)

type OrderLogService struct {
}

// StatisticsByGroupId 统计团体活动
func (s *OrderLogService) StatisticsByGroupId(groupId int) (result models.MarketingOrderLogGroupAggs, err error) {
	err = tools.GetDB().Model(models.MarketingOrderLogGroupAggs{}).
		Select("group_id, sum(order_price) as total_order_price, "+
			"count(*) as order_count, "+
			"sum(reduction_fee) as total_reduce_price, "+
			"sum(dealer_cost) as total_dealer_cost, "+
			"sum(res_cost) as total_res_cost").
		Where("group_id = ?", groupId).
		Where("order_state = ?", models.ORDER_STATE_DELIVERY_COMPLETED).
		Group("group_id").
		Scan(&result).
		Error
	if err != nil {
		msg := fmt.Sprintf("统计团体活动失败：模板ID:%d ， %v", groupId, err)
		tools.Logger.Error(msg)
		err = errors.New("marketing_group_template_statistics_failed")
	}
	return
}

// StatisticsMerchantShipmentReduce 统计普通减配送费活动下单数据
func (s *OrderLogService) StatisticsMerchantShipmentReduce(
	cityId, areaId, restaurantId int, beginDate, endDate string, isGroupShipmentReduce bool,
	groupIds []int,
) (models.MarketingOrderLogGroupAggs, error) {
	var result = models.MarketingOrderLogGroupAggs{}
	var err error
	query := tools.GetDB().Model(models.MarketingOrderLogGroupAggs{}).
		Select("group_id, sum(order_price) as total_order_price, " +
			"count(*) as order_count, " +
			"sum(reduction_fee) as total_reduce_price, " +
			"sum(dealer_cost) as total_dealer_cost, " +
			"sum(res_cost) as total_res_cost")
	if restaurantId > 0 {
		query = query.Where("restaurant_id = ?", restaurantId)
	} else if areaId > 0 {
		query = query.Where("area_id = ?", areaId)
	} else if cityId > 0 {
		query = query.Where("city_id = ?", cityId)
	}
	if isGroupShipmentReduce {
		query.Where("group_id in ? ", groupIds)
	} else {
		query.Where("group_id is null")
	}
	err = query.Where("order_state = ?", models.ORDER_STATE_DELIVERY_COMPLETED).
		Where("created_at >= ?", beginDate+" 00:00:00").
		Where("created_at <= ?", endDate+" 23:59:59").
		Where("type = ? ", models.MarketingMarketingTypeShipmentReduce).
		Scan(&result).
		Error
	if err != nil {
		// 接受的参数保存到日志中
		msg := fmt.Sprintf(
			"统计减配送费活动下单数据失败：城市ID:%d, 区域ID:%d, 餐厅ID:%d, 开始日期:%s, 结束日期:%s, 收否团体活动:%v, 错误： %v",
			cityId, areaId, restaurantId, beginDate, endDate, isGroupShipmentReduce, err)
		tools.Logger.Error(msg)
		err = errors.New("marketing_group_template_statistics_failed")
		return result, err
	}
	return result, nil
}

func (s *OrderLogService) SyncOrderStateAndShipperId(action int) error {
	var (
		startAt carbon.Carbon
		db          = tools.GetDB()
		chunkId int = 0
	)
	switch action {
	case 1:
		startAt = carbon.Now().SubDays(1)
	case 2:
		startAt = carbon.Now().SubDays(7)
	case 3:
		startAt = carbon.Now().SubMonth()
	default:
		return errors.New("receive wrong action type")
	}
	var todayOrders []models.OrderToday
	for {
		db.Model(models.OrderToday{}).
			Where("state in ?", []int{models.ORDER_STATE_DELIVERY_COMPLETED, models.ORDER_STATE_DELIVERY_FAIL}).
			Where("id > ?", chunkId).
			Order("id asc").
			Limit(100).
			Find(&todayOrders)
		if len(todayOrders) == 0 {
			break
		}
		for _, todayOrder := range todayOrders {
			db.Model(models.MarketingOrderLog{}).
				Where("order_id = ?", todayOrder.ID).
				Where("order_state != ?", todayOrder.State).
				Updates(map[string]interface{}{
					"order_state": todayOrder.State,
					"shipper_id":  todayOrder.ShipperID,
				})
			chunkId = todayOrder.ID
		}
	}
	if startAt.Lte(carbon.Parse(carbon.Now().Format("Y-m-d"))) {
		chunkId = 0
		var orders []models.Order
		for {
			db.Model(models.Order{}).
				Where(" created_at >= ?", startAt.Format("Y-m-d H:i:s")).
				Where("state in ? ", []int{models.ORDER_STATE_DELIVERY_COMPLETED, models.ORDER_STATE_DELIVERY_FAIL}).
				Where("id > ?", chunkId).
				Order("id desc").
				Limit(100).
				Find(&orders)
			if len(orders) == 0 {
				break
			}
			for _, order := range orders {
				db.Model(models.MarketingOrderLog{}).
					Where("order_id = ?", order.ID).
					Where("order_state != ?", order.State).
					Updates(map[string]interface{}{
						"order_state": order.State,
						"shipper_id":  order.ShipperID,
					})
				chunkId = order.ID
			}
		}
	}
	return nil
}
