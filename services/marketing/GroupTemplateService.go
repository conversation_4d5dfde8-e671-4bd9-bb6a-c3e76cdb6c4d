package marketing

import (
	"encoding/json"
	"errors"
	"fmt"
	"mulazim-api/constants"
	"mulazim-api/jobs"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/requests/marketingRequest"
	"mulazim-api/requests/marketingTemplateRequest"
	"mulazim-api/resources/cms"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type GroupTemplateService struct {
}

func (s *GroupTemplateService) Create(requestData marketingTemplateRequest.CreateRequest, restaurants []models.Restaurant, admin models.Admin, area models.Area) (models.MarketingGroupTemplate, error) {
	db := tools.GetDB()
	beginDate, _ := time.Parse("2006-01-02", requestData.BeginDate)
	endDate, _ := time.Parse("2006-01-02", requestData.EndDate)
	steps, _ := json.Marshal(requestData.Steps)
	template := models.MarketingGroupTemplate{
		CreatorID:        admin.ID,
		CreatorType:      1,
		CityID:           area.CityID,
		AreaID:           area.ID,
		MarketingType:    models.MarketingMarketingTypeShipmentReduce,
		Type:             models.MarketingGroupTemplateTypeRestaurant,
		NameUg:           requestData.NameUg,
		NameZh:           requestData.NameZh,
		BeginDate:        &beginDate,
		EndDate:          &endDate,
		FullWeekState:    requestData.FullWeekState,
		Day:              requestData.Day,
		FullTimeState:    *requestData.FullTimeState,
		AutoContinue:     *requestData.AutoContinue,
		TimeCount:        len(requestData.Timelines),
		State:            models.MarketingGroupTemplateStateOpen,
		MinDeliveryPrice: requestData.MinDeliveryPrice,
		Steps:            string(steps),
	}
	for i, timeline := range requestData.Timelines {
		switch i {
		case 0:
			template.Time1Start = timeline.Start
			template.Time1End = timeline.End
		case 1:
			template.Time2Start = timeline.Start
			template.Time2End = timeline.End
		case 2:
			template.Time3Start = timeline.Start
			template.Time3End = timeline.End
		}
	}
	rs := db.Model(models.MarketingGroupTemplate{}).Create(&template)
	if rs.Error != nil {
		msg := fmt.Sprintf("创建团体活动失败: 管理员ID:%d, 错误信息: %s", admin.ID, rs.Error.Error())
		tools.Logger.Error(msg)
		return models.MarketingGroupTemplate{}, errors.New("marketing_add_group_template_failed")
	}
	go func() {
		defer func() {
			if err := recover(); err != nil {
				tools.Logger.Error("创建团体活动失败: ", err)
			}
		}()
		for _, restaurant := range restaurants {
			attendence := models.MarketingGroupTemplateAttendance{
				TemplateId:   template.ID,
				RestaurantId: restaurant.ID,
				AdminId:      admin.ID,
				SendCount:    0,
				State:        models.MarketingGroupTemplateAttendenceStateNew,
			}
			db.Model(models.MarketingGroupTemplateAttendance{}).
				Clauses(clause.OnConflict{DoNothing: true}).
				Create(&attendence)
		}
	}()
	return template, nil
}

// Find 查询团体活动
func (s *GroupTemplateService) Find(id int) (models.MarketingGroupTemplate, error) {
	db := tools.GetDB()
	var template = models.MarketingGroupTemplate{}
	rs := db.Model(models.MarketingGroupTemplate{}).Where("id = ?", id).First(&template)
	if rs.Error != nil {
		msg := fmt.Sprintf("查询团体活动失败: 活动ID:%d, 错误信息: %s", id, rs.Error.Error())
		tools.Logger.Error(msg)
		return models.MarketingGroupTemplate{}, errors.New("marketing_group_template_not_found")
	}
	return template, nil
}
func (s *GroupTemplateService) FindWithStatistics(id int) (models.MarketingGroupTemplateAggs, error) {
	db := tools.GetDB()
	var template = models.MarketingGroupTemplateAggs{}
	rs := db.Model(models.MarketingGroupTemplateAggs{}).
		Preload("Creator").
		//Joins("left join t_marketing_order_log on t_marketing_order_log.group_id = t_marketing_group_template.id AND t_marketing_order_log.order_state = ?", models.ORDER_STATE_DELIVERY_COMPLETED).
		//Joins("left join t_marketing_group_template_attendence on t_marketing_group_template_attendence.template_id = t_marketing_group_template.id").
		Select("t_marketing_group_template.*, "+
			"(select sum(t_marketing_order_log.order_price) from t_marketing_order_log where t_marketing_order_log.group_id = t_marketing_group_template.id and t_marketing_order_log.order_state in (7, 10)) as total_order_price, "+
			"(select count(t_marketing_order_log.id) from t_marketing_order_log where t_marketing_order_log.group_id = t_marketing_group_template.id and t_marketing_order_log.order_state in (7, 10)) as order_count, "+
			"(select sum(t_marketing_order_log.reduction_fee) from t_marketing_order_log where t_marketing_order_log.group_id = t_marketing_group_template.id and t_marketing_order_log.order_state in (7, 10)) as total_reduce_price, "+
			"(select sum(t_marketing_order_log.dealer_cost) from t_marketing_order_log where t_marketing_order_log.group_id = t_marketing_group_template.id and t_marketing_order_log.order_state in (7, 10)) as total_dealer_cost, "+
			"(select sum(t_marketing_order_log.res_cost) from t_marketing_order_log where t_marketing_order_log.group_id = t_marketing_group_template.id and t_marketing_order_log.order_state in (7, 10)) as total_res_cost, "+
			"(select count(t_marketing_group_template_attendence.template_id) from t_marketing_group_template_attendence where t_marketing_group_template_attendence.template_id=t_marketing_group_template.id) as invite_count, "+
			"(select count(case when t_marketing_group_template_attendence.state = 3 then t_marketing_group_template_attendence.template_id end) from t_marketing_group_template_attendence where t_marketing_group_template_attendence.template_id=t_marketing_group_template.id) as accepted_count").
		Where("t_marketing_group_template.id = ?", id).Unscoped().First(&template)
	if rs.Error != nil {
		msg := fmt.Sprintf("查询团体活动失败: 活动ID:%d, 错误信息: %s", id, rs.Error.Error())
		tools.Logger.Error(msg)
		return models.MarketingGroupTemplateAggs{}, errors.New("marketing_group_template_not_found")
	}
	return template, nil
}

// ChangeTemplateStateByAreaAndIds 查询可以改变状态的团体活动
func (s *GroupTemplateService) ChangeTemplateStateByAreaAndIds(areaId int, ids []int, state int) (bool, error) {
	db := tools.GetDB()

	query := db.Model(models.MarketingGroupTemplate{}).
		Where("id in (?)", ids).
		Where("marketing_type = ?", models.MarketingMarketingTypeShipmentReduce).
		Where("state in (?)", []int{models.MarketingGroupTemplateStateNew, models.MarketingGroupTemplateStateOpen, models.MarketingGroupTemplateStatePause})
	if areaId > 0 {
		query = query.Where("area_id = ?", areaId)
	}
	rs := query.Update("state", state)

	if rs.Error != nil {
		msg := fmt.Sprintf("更新团体活动失败: 区域ID: %d 错误信息: %s, ids: %v， 状态： %d", areaId, rs.Error.Error(), ids, state)
		tools.Logger.Error(msg)
		return false, errors.New("marketing_group_template_change_state_failed")
	}
	return true, nil
}

// Update 编辑团体减配送费活动
func (s *GroupTemplateService) Update(template models.MarketingGroupTemplate, request marketingRequest.CmsMarketingUpdateRequest) (models.MarketingGroupTemplate, error) {
	var (
		db = tools.GetDB()
	)
	var count int64
	db.Model(models.MarketingGroupTemplateAttendance{}).
		Where("template_id = ?", template.ID).
		Where("state = ?", models.MarketingGroupTemplateAttendenceStateJoined).
		Count(&count)
	if count > 0 {
		return template, errors.New("marketing_group_template_has_joined_marchant_can_not_be_updated")
	}

	steps, _ := json.Marshal(request.Steps)
	var updateObj = map[string]interface{}{
		"name_ug":            request.NameUg,
		"name_zh":            request.NameZh,
		"begin_date":         request.BeginDate,
		"end_date":           request.EndDate,
		"full_week_state":    request.FullWeekState,
		"day":                request.Day,
		"full_time_state":    *request.FullTimeState,
		"auto_continue":      *request.AutoContinue,
		"time_count":         len(request.Timelines),
		"min_delivery_price": request.MinDeliveryPrice,
		"steps":              string(steps),
	}

	for _, i := range []string{"1", "2", "3"} {
		updateObj["time"+i+"_start"] = nil
		updateObj["time"+i+"_end"] = nil
	}
	for i, timeline := range request.Timelines {
		index := tools.ToString(i + 1)
		updateObj["time"+index+"_start"] = timeline.Start
		updateObj["time"+index+"_end"] = timeline.End

	}
	rs := db.Model(&template).Updates(updateObj)

	if rs.Error != nil {
		msg := fmt.Sprintf("编辑团体活动失败: 活动ID:%d, 错误信息: %s", template.ID, rs.Error.Error())
		tools.Logger.Error(msg)
		return template, errors.New("marketing_group_template_update_failed")
	}
	return template, nil
}

// SendJoinPush  查询可以改变状态的团体活动
func (s *GroupTemplateService) SendJoinPush(areaId int, ids []int) (bool, error) {
	db := tools.GetDB()
	var templates []models.MarketingGroupTemplate = make([]models.MarketingGroupTemplate, 0)
	query := db.Model(models.MarketingGroupTemplate{}).
		Preload("MarketingGroupTemplateAttendance", func(d *gorm.DB) *gorm.DB {
			return db.Where("state <= ?", models.MarketingGroupTemplateAttendenceStateSend)
		}).
		Where("id in (?)", ids).
		Where("state = ?", models.MarketingGroupTemplateStateOpen)
	if areaId > 0 {
		query = query.Where("area_id = ?", areaId)
	}
	rs := query.Find(&templates)
	if rs.Error != nil {
		msg := fmt.Sprintf("查询团体活动失败:区域ID: %d 活动ID:%v, 错误信息: %s", areaId, ids, rs.Error.Error())
		tools.Logger.Error(msg)
		return false, errors.New("marketing_group_template_not_found")
	}
	// 开始推送消息
	go func() {
		defer func() {
			if err := recover(); err != nil {
				msg := fmt.Sprintf("发送团体活动推送失败:区域ID: %d 活动ID:%v, 错误信息: %s", areaId, ids, rs.Error.Error())
				tools.Logger.Error(msg)
			}
		}()
		for _, template := range templates {
			s.SendPushOne(template, models.MarketingGroupTemplateAttendance{})
			db.Model(models.MarketingGroupTemplate{ID: template.ID}).Update("send_count", template.SendCount+1)
		}
	}()
	return true, nil
}

// GetMarketingGroupPaginate 团体活动 列表
func (s ShipmentReduceService) GetMarketingGroupPaginate(
	cityId int,
	areaId int,
	state int,
	beginDate string,
	endDate string,
	page int,
	limit int,
	keyword string,
	sorts string,
) ([]models.MarketingTemplateStaticWithOrderLog, int64) {
	var marketings []models.MarketingTemplateStaticWithOrderLog
	db := tools.GetDB()
	query := db.Model(&models.MarketingTemplateStaticWithOrderLog{}).Where("marketing_type = 2")

	if areaId > 0 {
		query = query.Where("area_id = ?", areaId)
	} else if cityId > 0 {
		query = query.Where("city_id = ?", cityId)
	}

	if state >= 0 {
		query = query.Where("state = ?", state)
	}
	if state == 4 {
		query.Unscoped()
	}

	if len(beginDate) > 0 {
		query = query.Where("created_at >= ?", beginDate+" 00:00:00")
	}
	if len(endDate) > 0 {
		query = query.Where("created_at <= ?", endDate+" 23:59:59")
	}
	if len(keyword) > 0 {
		query = query.Where("name_ug like ? or name_zh like ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	var totalCount int64
	query.Count(&totalCount)
	if sorts != "" {
		query.Order(sorts)
	} else {
		query.Order("id desc")
	}
	if totalCount > 0 {
		query.Scopes(scopes.Page(page, limit)).Order("id desc")
	} else {
		return []models.MarketingTemplateStaticWithOrderLog{}, 0
	}
	query.
		Preload("Creator").
		Preload("City").
		Preload("Area").
		Select("t_marketing_group_template.*, (SELECT count(t_marketing_order_log.id) from t_marketing_order_log WHERE t_marketing_order_log.group_id=t_marketing_group_template.id  and t_marketing_order_log.order_state=7) as order_count," +
			"(SELECT sum(t_marketing_order_log.order_price) from t_marketing_order_log WHERE t_marketing_order_log.group_id=t_marketing_group_template.id  and t_marketing_order_log.order_state=7) as total_order_price," +
			"(SELECT sum(t_marketing_order_log.reduction_fee) from t_marketing_order_log WHERE t_marketing_order_log.group_id=t_marketing_group_template.id  and t_marketing_order_log.order_state=7) as total_reduce_price," +
			"( SELECT count( t_marketing_group_template_attendence.id ) FROM t_marketing_group_template_attendence WHERE t_marketing_group_template_attendence.template_id = t_marketing_group_template.id ) AS invite_count," +
			"( SELECT count( t_marketing_group_template_attendence.id ) FROM t_marketing_group_template_attendence WHERE t_marketing_group_template_attendence.template_id = t_marketing_group_template.id  and t_marketing_group_template_attendence.state = 3) AS accepted_count ").
		Find(&marketings)
	return marketings, totalCount

}

// GetMarketingGroupDownload 减配送费列表下载
func (s ShipmentReduceService) GetMarketingGroupDownload(
	cityId int,
	areaId int,
	restaurantId int,
	state int,
	beginDate string,
	endDate string,
	keyword string,
	langUtil lang.LangUtil,
	sorts string,
) (allExport []map[string]interface{}, cols []string, er1 error) {
	var marketings []models.MarketingTemplateStaticWithOrderLog
	db := tools.GetDB()
	query := db.Model(marketings).Where("marketing_type = 2")

	if areaId > 0 {
		query = query.Where("area_id = ?", areaId)
	} else if cityId > 0 {
		query = query.Where("city_id = ?", cityId)
	}

	if state >= 0 {
		query = query.Where("state = ?", state)
	}
	if len(beginDate) > 0 {
		query = query.Where("created_at >= ?", beginDate+" 00:00:00")
	}
	if len(endDate) > 0 {
		query = query.Where("created_at <= ?", endDate+" 23:59:59")
	}
	if len(keyword) > 0 {
		query = query.Where("name_ug like ? or name_zh like ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	if sorts != "" {
		query.Order(sorts)
	} else {
		query.Order("id desc")
	}
	excelItems := make([]map[string]interface{}, 0)
	query.
		Preload("City"). //列表输出 城市和 区域
		Preload("Area").
		Preload("Creator").
		Select("t_marketing_group_template.*, (SELECT count(t_marketing_order_log.id) from t_marketing_order_log WHERE t_marketing_order_log.group_id=t_marketing_group_template.id  and t_marketing_order_log.order_state=7) as order_count," +
			"(SELECT sum(t_marketing_order_log.order_price) from t_marketing_order_log WHERE t_marketing_order_log.group_id=t_marketing_group_template.id  and t_marketing_order_log.order_state=7) as total_order_price," +
			"(SELECT sum(t_marketing_order_log.reduction_fee) from t_marketing_order_log WHERE t_marketing_order_log.group_id=t_marketing_group_template.id  and t_marketing_order_log.order_state=7) as total_reduce_price," +
			"( SELECT count( t_marketing_group_template_attendence.id ) FROM t_marketing_group_template_attendence WHERE t_marketing_group_template_attendence.template_id = t_marketing_group_template.id ) AS invite_count," +
			"( SELECT count( t_marketing_group_template_attendence.id ) FROM t_marketing_group_template_attendence WHERE t_marketing_group_template_attendence.template_id = t_marketing_group_template.id  and t_marketing_group_template_attendence.state = 3) AS accepted_count ").
		Find(&marketings)
	cityName := ""
	areaName := ""
	for _, m := range marketings {
		if langUtil.Lang == "ug" {
			cityName = m.City.NameUg
			areaName = m.Area.NameUg
		} else {
			cityName = m.City.NameZh
			areaName = m.Area.NameZh
		}

		key := fmt.Sprintf("marketing_state_%d", m.MarketingGroupTemplate.State)
		stateName := langUtil.T(key)

		marketingName := ""
		if langUtil.Lang == "ug" {
			marketingName = m.NameUg
		} else {
			marketingName = m.NameZh
		}
		excelItems = append(excelItems, map[string]interface{}{
			"ID":     m.ID,
			"城市":     cityName,
			"区域":     areaName,
			"活动名称":   marketingName,
			"邀请数量":   m.InviteCount,
			"接收邀请数量": m.AcceptedCount,
			// 状态  0：新建，1:启动，2：暂停，3：失效，4:删除
			"状态":      stateName,
			"添加时间":    m.CreatedAt.Format("2006-01-02"),
			"活动开始时间":  m.BeginDate.Format("2006-01-02"),
			"活动结束时间":  m.EndDate.Format("2006-01-02"),
			"总交易金额":   tools.ToPrice(tools.ToFloat64(m.TotalOrderPrice) / 100),
			"总订单数量":   m.OrderCount,
			"总优惠的配送费": tools.ToPrice(tools.ToFloat64(m.TotalReducePrice) / 100),
			"创建人":     m.Creator.RealName,
		})
	}

	cols = []string{
		"ID",
		"城市",
		"区域",
		"活动名称",
		"邀请数量",
		"接收邀请数量",
		"状态",
		"添加时间",
		"活动开始时间",
		"活动结束时间",
		"总交易金额",
		"总订单数量",
		"总优惠的配送费",
		"创建人",
	}
	return excelItems, cols, nil

}

// MarketingGroupDelete 减配送费 活动删除
func (s ShipmentReduceService) MarketingGroupDelete(
	admin models.Admin,
	ids []int,
	langUtil lang.LangUtil,
) map[string]interface{} {
	rs := make(map[string]interface{})
	db := tools.GetDB()
	var marketItems []models.MarketingGroupTemplate
	successCount := 0
	reasons := make([]map[string]interface{}, 0)

	// 判断是否可以删除
	// 可以删除的 删除 不能删除的 解释原因
	db.Model(&models.MarketingGroupTemplate{}).
		Preload("MarketingGroupTemplateAttendance", func(d *gorm.DB) *gorm.DB {
			return d.Where("state = 3")
		}).
		// Preload("MarketingGroupTemplateAttendance").
		Where("marketing_type = 2").
		Where("id in (?)", ids).Find(&marketItems)

	var canDelete []int
	var canDeleteMarketItems []models.MarketingGroupTemplate
	if admin.IsOwner() {
		for _, m := range marketItems {
			if len(m.MarketingGroupTemplateAttendance) == 0 {
				canDelete = append(canDelete, m.ID)
				successCount++
				canDeleteMarketItems = append(canDeleteMarketItems, m)
			}
		}
		if len(canDelete) > 0 {
			db.Model(&models.Marketing{}).
				Where("marketing_type = 2").
				Where("group_type = 2").
				Where("id in (?)", canDelete).Updates(&map[string]interface{}{
				"state": 4,
			})
			db.Delete(&canDeleteMarketItems)
			var att []models.MarketingGroupTemplateAttendance
			db.Model(&models.MarketingGroupTemplateAttendance{}).Where("template_id in ?", canDelete).Find(&att)
			db.Delete(&att)
		}
		rs["success_count"] = successCount
		rs["fail_count"] = len(ids) - successCount
		rs["fail_reasons"] = reasons
		return rs
	}

	mName := ""

	for _, m := range marketItems {
		if langUtil.Lang == "ug" {
			mName = m.NameUg
		} else {
			mName = m.NameZh
		}
		if m.CreatorID != admin.ID {
			reasons = append(reasons, map[string]interface{}{"msg": fmt.Sprintf("[%s]", mName) + langUtil.T("created_by_not_owner")})
			continue
		}
		if len(m.MarketingGroupTemplateAttendance) > 0 {
			reasons = append(reasons, map[string]interface{}{"msg": fmt.Sprintf("[%s]", mName) + langUtil.T("market_order_exists_can_not_delete")})
		} else {
			successCount++
			canDelete = append(canDelete, m.ID)
			canDeleteMarketItems = append(canDeleteMarketItems, m)
		}
	}

	if len(canDelete) > 0 {
		db.Model(&models.MarketingGroupTemplate{}).
			Where("marketing_type = 2").
			Where("id in (?)", canDelete).Updates(&map[string]interface{}{
			"state": 4,
		})
		db.Delete(&canDeleteMarketItems)
		var att []models.MarketingGroupTemplateAttendance
		db.Model(&models.MarketingGroupTemplateAttendance{}).Where("template_id in ?", canDelete).Find(&att)
		db.Delete(&att)
	}
	rs["success_count"] = successCount
	rs["fail_count"] = len(ids) - successCount
	rs["fail_reasons"] = reasons
	return rs

}

// MarketingDetailRestaurant 减配送费 活动 详情 店铺
func (s ShipmentReduceService) MarketingDetailRestaurant(
	id int,
	beginDate string,
	endDate string,
	page int,
	limit int,
	keyword string,
	langUtils lang.LangUtil,
	sorts string,
	state int,
) *cms.MarketingDetailRestaurantResourceCollection {
	//店铺必须显示 ，开始结束日期 影响的 内容是订单
	db := tools.GetDB()

	var marketings []models.MarketingGroupTemplateAttendance

	query := db.Model(marketings).
		Where("template_id = ?", id)

	if len(beginDate) > 0 && len(endDate) > 0 {
		query = query.Preload("Marketing.Creator").
			Preload("Marketing.MarketingOrderLogForDetail", func(d *gorm.DB) *gorm.DB {
				return d.
					Select(`
						t_marketing_order_log.group_id,
						t_marketing_order_log.restaurant_id,
						count(t_marketing_order_log.id) as order_count,
						sum(t_marketing_order_log.order_price) as total_order_price,
						sum(t_marketing_order_log.reduction_fee) as total_reduce_price,
						sum(t_marketing_order_log.res_cost) as total_res_cost,
						sum(t_marketing_order_log.dealer_cost) as total_dealer_cost
						`).
					Where("t_marketing_order_log.order_state in (7,10)").
					Where("t_marketing_order_log.created_at >= ?", beginDate+" 00:00:00").
					Where("t_marketing_order_log.created_at <= ?", endDate+" 23:59:59").
					Group("t_marketing_order_log.restaurant_id")

			})
	} else {
		query = query.Preload("Marketing.Creator").
			Preload("Marketing.MarketingOrderLogForDetail", func(d *gorm.DB) *gorm.DB {
				return d.
					Select(`
						t_marketing_order_log.group_id,
						t_marketing_order_log.restaurant_id,
						count(t_marketing_order_log.id) as order_count,
						sum(t_marketing_order_log.order_price) as total_order_price,
						sum(t_marketing_order_log.reduction_fee) as total_reduce_price,
						sum(t_marketing_order_log.res_cost) as total_res_cost,
						sum(t_marketing_order_log.dealer_cost) as total_dealer_cost
						`).
					Where("t_marketing_order_log.order_state in (7,10)").
					Group("t_marketing_order_log.restaurant_id")

			})
	}

	if len(keyword) > 0 {
		var resIDs []int
		query.Pluck("restaurant_id", &resIDs)
		var searchResIDs []int
		db.Model(&models.Restaurant{}).
			Where("name_ug like ? or name_zh like ?", "%"+keyword+"%", "%"+keyword+"%").
			Where("id in ?", resIDs).
			Select("id").Pluck("id", &searchResIDs)
		query = query.Where("t_marketing_group_template_attendence.restaurant_id in ?", searchResIDs)
		query = query.Preload("Restaurant", func(d *gorm.DB) *gorm.DB {
			return d.Where("t_restaurant.id in ?", searchResIDs)
		})
	} else {
		query = query.Preload("Restaurant")
	}
	if state > 0 {
		query.Where("state = ?", state)
	}
	var totalCount int64
	query.Count(&totalCount)
	if sorts != "" {
		query.Order(sorts)
	} else {
		query.Order("state desc")
	}
	if totalCount > 0 {
		query.Scopes(scopes.Page(page, limit)).Select("t_marketing_group_template_attendence.*").Find(&marketings)
	} else {
		return cms.NewMarketingDetailRestaurantResourceCollection(
			marketings,
			totalCount,
			beginDate,
			endDate,
			page,
			limit,
			keyword,
			langUtils,
			false,
		)
	}

	result := cms.NewMarketingDetailRestaurantResourceCollection(
		marketings,
		totalCount,
		beginDate,
		endDate,
		page,
		limit,
		keyword,
		langUtils,
		false,
	)
	return result
}

// DownloadMarketingDetailRestaurant 减配送费 活动 详情 店铺
func (s ShipmentReduceService) DownloadMarketingDetailRestaurant(
	id int,
	beginDate string,
	endDate string,
	keyword string,
	langUtils lang.LangUtil,
	sorts string,
	state int,
) (allExport []map[string]interface{}, cols []string, er1 error) {

	//店铺必须显示 ，开始结束日期 影响的 内容是订单
	db := tools.GetDB()

	var marketings []models.MarketingGroupTemplateAttendance

	query := db.Model(marketings).
		Preload("Marketing.City").
		Preload("Marketing.Area").
		Where("template_id = ?", id)

	if len(beginDate) > 0 && len(endDate) > 0 {
		query = query.Preload("Marketing.Creator").
			Preload("Marketing.MarketingOrderLogForDetail", func(d *gorm.DB) *gorm.DB {
				return d.
					Select(`
						t_marketing_order_log.group_id,
						t_marketing_order_log.restaurant_id,
						count(t_marketing_order_log.id) as order_count,
						sum(t_marketing_order_log.order_price) as total_order_price,
						sum(t_marketing_order_log.reduction_fee) as total_reduce_price,
						sum(t_marketing_order_log.res_cost) as total_res_cost,
						sum(t_marketing_order_log.dealer_cost) as total_dealer_cost
						`).
					Where("t_marketing_order_log.order_state in (7,10)").
					Where("t_marketing_order_log.created_at >= ?", beginDate+" 00:00:00").
					Where("t_marketing_order_log.created_at <= ?", endDate+" 23:59:59").
					Group("t_marketing_order_log.restaurant_id")

			})
	} else {
		query = query.Preload("Marketing.Creator").
			Preload("Marketing.MarketingOrderLogForDetail", func(d *gorm.DB) *gorm.DB {
				return d.
					Select(`
						t_marketing_order_log.group_id,
						t_marketing_order_log.restaurant_id,
						count(t_marketing_order_log.id) as order_count,
						sum(t_marketing_order_log.order_price) as total_order_price,
						sum(t_marketing_order_log.reduction_fee) as total_reduce_price,
						sum(t_marketing_order_log.res_cost) as total_res_cost,
						sum(t_marketing_order_log.dealer_cost) as total_dealer_cost
						`).
					Where("t_marketing_order_log.order_state in (7,10)").
					Group("t_marketing_order_log.restaurant_id")

			})
	}

	if len(keyword) > 0 {
		var resIDs []int
		query.Pluck("restaurant_id", &resIDs)
		var searchResIDs []int
		db.Model(&models.Restaurant{}).
			Where("name_ug like ? or name_zh like ?", "%"+keyword+"%", "%"+keyword+"%").
			Where("id in ?", resIDs).
			Select("id").Pluck("id", &searchResIDs)
		query = query.Where("t_marketing_group_template_attendence.restaurant_id in ?", searchResIDs)
		query = query.Preload("Restaurant", func(d *gorm.DB) *gorm.DB {
			return d.Where("t_restaurant.id in ?", searchResIDs)
		})
	} else {
		query = query.Preload("Restaurant")
	}

	excelItems := make([]map[string]interface{}, 0)
	var totalCount int64
	query.Count(&totalCount)
	if sorts != "" {
		query.Order(sorts)
	} else {
		query.Order("state desc")
	}
	query.
		Find(&marketings)

	result := cms.NewMarketingDetailRestaurantResourceCollection(
		marketings,
		totalCount,
		beginDate,
		endDate,
		1,
		10000,
		keyword,
		langUtils,
		true,
	)
	items := result.Items
	for _, m := range items {
		excelItems = append(excelItems, map[string]interface{}{
			// "ID":     m.ID,
			"店铺名称":     m.RestaurantNameZh,
			"店铺手机号":    m.RestaurantMobile,
			"总交易金额":    tools.ToPrice(tools.ToFloat64(m.TotalOrderPrice) / 100),
			"总订单数量":    m.OrderCount,
			"总优惠的配送费":  tools.ToPrice(tools.ToFloat64(m.TotalReducePrice) / 100),
			"商家承担部分金额": tools.ToPrice(tools.ToFloat64(m.TotalCostRestaurant) / 100),
			"代理承担部分金额": tools.ToPrice(tools.ToFloat64(m.TotalCostDealer) / 100),
			"添加时间":     m.CreatedAt,
			"接手人":      m.AcceptMobile,
			"状态":       m.StateLabel,
		})

	}
	cols = []string{
		"店铺名称",
		"店铺手机号",
		"总交易金额",
		"总订单数量",
		"总优惠的配送费",
		"商家承担部分金额",
		"代理承担部分金额",
		"添加时间",
		"接手人",
		"状态",
	}
	return excelItems, cols, nil
}

// MarketingDetailOrder 减配送费 活动 详情 订单
func (s ShipmentReduceService) MarketingDetailOrder(
	id int,
	beginDate string,
	endDate string,
	page int,
	limit int,
	keyword string,
	resturantId int,
	orderState int,
	langUtils lang.LangUtil,
	sorts string,
) *cms.MarketingDetailOrderResourceCollection {
	var orderLogs []models.MarketingOrderLog
	db := tools.GetDB()
	query := db.Model(models.MarketingOrderLog{}).
		Where("group_id = ?", id).
		Preload("Restaurant").
		Preload("Order", func(d *gorm.DB) *gorm.DB {
			return d.Select("id,name,mobile,order_address,order_price,state,shipment,shipper_id").Preload("Shipper", func(d *gorm.DB) *gorm.DB {
				return d.Select("id,name,real_name")
			})
		}).
		Preload("OrderToday", func(d *gorm.DB) *gorm.DB {
			return d.Select("id,name,mobile,order_address,order_price,state,shipment,shipper_id").Preload("Shipper", func(d *gorm.DB) *gorm.DB {
				return d.Select("id,name,real_name")
			})
		})

	if len(beginDate) > 0 && len(endDate) > 0 {
		query = query.Where("t_marketing_order_log.created_at between ? and ?", beginDate+" 00:00:00", endDate+" 23:59:59")
	}
	if resturantId > 0 {
		query = query.Where("t_marketing_order_log.restaurant_id =?", resturantId)
	}
	if orderState > 0 {
		query = query.Where("t_marketing_order_log.order_state =?", orderState)
	}

	if len(keyword) > 0 { //客户手机号和姓名 和 订单号
		var orderIDs []int
		query.Pluck("order_id", &orderIDs)

		var searchIDs []int
		var searchIDsToday []int
		var searchOrderIDs []int
		db.Model(&models.Order{}).
			Where("order_id like ? or name like ? or mobile like ?", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%").
			Where("id in ?", orderIDs).
			Select("id").Pluck("id", &searchOrderIDs)
		db.Model(&models.OrderToday{}).
			Where("order_id like ? or name like ? or mobile like ?", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%").
			Where("id in ?", orderIDs).
			Select("id").Pluck("id", &searchIDsToday)
		searchIDs = append(searchIDs, searchOrderIDs...)
		searchIDs = append(searchIDs, searchIDsToday...)
		query.Where("t_marketing_order_log.order_id in ?", searchIDs)
	}
	var totalCount int64
	query.Count(&totalCount)
	if sorts != "" {
		query.Order(sorts)
	} else {
		query.Order("id desc")
	}

	if totalCount > 0 {
		query.Scopes(scopes.Page(page, limit)).Select("t_marketing_order_log.*").Find(&orderLogs)
	}

	result := cms.NewMarketingDetailOrderResourceCollection(
		orderLogs,
		totalCount,
		beginDate,
		endDate,
		page,
		limit,
		keyword,
		langUtils,
		false,
	)
	return result
}

// DownloadMarketingDetailOrder 减配送费 活动 详情 订单
func (s ShipmentReduceService) DownloadMarketingDetailOrder(
	id int,
	beginDate string,
	endDate string,
	page int,
	limit int,
	keyword string,
	resturantId int,
	orderState int,
	langUtils lang.LangUtil,
) (allExport []map[string]interface{}, cols []string, er1 error) {
	var marketings []models.MarketingOrderLog
	db := tools.GetDB()
	query := db.Model(marketings).
		Where("group_id = ?", id).
		Preload("Restaurant").
		Preload("Shipper").
		Preload("Order", func(d *gorm.DB) *gorm.DB {
			return d.Select("id,name,mobile,order_address,order_price,state,shipment,shipper_id").Preload("Shipper", func(d *gorm.DB) *gorm.DB {
				return d.Select("id,name,real_name")
			})
		}).
		Preload("OrderToday", func(d *gorm.DB) *gorm.DB {
			return d.Select("id,name,mobile,order_address,order_price,state,shipment,shipper_id").Preload("Shipper", func(d *gorm.DB) *gorm.DB {
				return d.Select("id,name,real_name")
			})
		})

	if len(beginDate) > 0 && len(endDate) > 0 {
		query = query.Where("t_marketing_order_log.created_at between ? and ?", beginDate+" 00:00:00", endDate+" 23:59:59")
	}
	if resturantId > 0 {
		query = query.Where("t_marketing_order_log.restaurant_id =?", resturantId)
	}
	if orderState > 0 {
		query = query.Where("t_marketing_order_log.order_state =?", orderState)
	}

	if len(keyword) > 0 { //客户手机号和姓名 和 订单号
		var orderIDs []int
		query.Pluck("order_id", &orderIDs)
		var searchIDs []int
		var searchIDsToday []int
		var searchOrderIDs []int
		db.Model(&models.Order{}).
			Where("order_id like ? or name like ? or mobile like ?", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%").
			Where("id in ?", orderIDs).
			Select("id").Pluck("id", &searchOrderIDs)
		db.Model(&models.OrderToday{}).
			Where("order_id like ? or name like ? or mobile like ?", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%").
			Where("id in ?", orderIDs).
			Select("id").Pluck("id", &searchIDsToday)
		searchIDs = append(searchIDs, searchOrderIDs...)
		searchIDs = append(searchIDs, searchIDsToday...)
		query = query.Where("t_marketing_order_log.order_id in ?", searchIDs)
	}
	var totalCount int64
	query.Count(&totalCount)
	query.Order("id desc")
	query.Select("t_marketing_order_log.*").Find(&marketings)
	excelItems := make([]map[string]interface{}, 0)
	result := cms.NewMarketingDetailOrderResourceCollection(
		marketings,
		totalCount,
		beginDate,
		endDate,
		page,
		limit,
		keyword,
		langUtils,
		true,
	)
	items := result.Items
	for _, m := range items {
		excelItems = append(excelItems, map[string]interface{}{
			// "ID":     m.ID,
			"店铺名称": m.RestaurantName,
			"订单号":  m.OrderNo,
			// "总交易金额":tools.ToPrice(tools.ToFloat64(m.TotalOrderPrice) / 100),
			"客户名称":   m.CustomerName,
			"客户手机号":  tools.MaskMobile(m.CustomerMobile),
			"订单金额":   tools.ToPrice(tools.ToFloat64(m.OrderPrice) / 100),
			"原始配送费":  tools.ToPrice(tools.ToFloat64(m.OriginalShipment) / 100),
			"优惠的配送费": tools.ToPrice(tools.ToFloat64(m.Shipment) / 100),
			"地址":     m.OrderAddress,
		})

	}
	cols = []string{
		"店铺名称",
		"订单号",
		"客户名称",
		"客户手机号",
		"订单金额",
		"原始配送费",
		"优惠的配送费",
		"地址",
	}
	return excelItems, cols, nil
}

func (s *GroupTemplateService) ListForStatistics(
	cityId, areaId, restaurantId int, beginDate, endDate string, page, limit int, sorts string, keyword string,
) ([]models.MarketingGroupTemplateAggsForStatistics, int64) {
	var templates = make([]models.MarketingGroupTemplateAggsForStatistics, 0)
	var total int64 = 0
	db := tools.GetDB()

	query := db.Model(models.MarketingGroupTemplateAggsForStatistics{}).
		Where("t_marketing_group_template.marketing_type = ?", models.MarketingMarketingTypeShipmentReduce)
	if restaurantId > 0 {
		query = query.Where("t_marketing_group_template.restaurant_id = ?", restaurantId)
	} else if areaId > 0 {
		query = query.Where("t_marketing_group_template.area_id = ?", areaId)
	} else if cityId > 0 {
		query = query.Where("t_marketing_group_template.city_id = ?", cityId)
	}
	if keyword != "" {
		query = query.Where("t_marketing_group_template.name_ug like ? or t_marketing_group_template.name_zh like ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	if len(beginDate) > 0 {
		query = query.Where("t_marketing_group_template.created_at >= ?", beginDate+" 00:00:00")
	}
	if len(endDate) > 0 {
		query = query.Where("t_marketing_group_template.created_at <= ?", endDate+" 23:59:59")
	}
	query.Count(&total)
	if total > 0 {
		if sorts != "" {
			query.Order("t_marketing_group_template." + sorts)
		} else {
			query.Order("t_marketing_group_template.id desc")
		}
		query.
			Preload("Creator").
			//Joins("left join t_marketing_order_log on t_marketing_order_log.group_id = t_marketing_group_template.id AND t_marketing_order_log.order_state >= ?", models.ORDER_STATE_DELIVERY_COMPLETED).
			//Joins("left join t_marketing_group_template_attendence on t_marketing_group_template_attendence.template_id = t_marketing_group_template.id").
			Select("t_marketing_group_template.*, " +
				"(select sum(case when t_marketing_order_log.order_state in (7,10) then t_marketing_order_log.order_price end) from t_marketing_order_log where t_marketing_order_log.group_id = t_marketing_group_template.id) as total_order_price, " +
				"(select count(case when t_marketing_order_log.order_state in (7,10) then t_marketing_order_log.id end) from t_marketing_order_log where t_marketing_order_log.group_id = t_marketing_group_template.id) as complated_order_count, " +
				"(select count(case when t_marketing_order_log.order_state in (8,9) then t_marketing_order_log.id end) from t_marketing_order_log where t_marketing_order_log.group_id = t_marketing_group_template.id) as rejected_order_count, " +
				"(select sum(case when t_marketing_order_log.order_state in (7,10) then t_marketing_order_log.reduction_fee end) from t_marketing_order_log where t_marketing_order_log.group_id = t_marketing_group_template.id) as total_reduce_price, " +
				"(select count(t_marketing_group_template_attendence.template_id) from t_marketing_group_template_attendence where t_marketing_group_template_attendence.template_id = t_marketing_group_template.id) as invite_count, " +
				"(select count(case when t_marketing_group_template_attendence.state = 3 then t_marketing_group_template_attendence.id end) from t_marketing_group_template_attendence where t_marketing_group_template_attendence.template_id = t_marketing_group_template.id) as accepted_count").
			Scopes(scopes.Page(page, limit)).
			Find(&templates)
	}
	return templates, total
}

// MarketingDetailRestaurantAdd 减配送费 活动 详情 店铺 添加
func (s GroupTemplateService) MarketingDetailRestaurantAdd(
	id int,
	ResIds []int,
	langUtils lang.LangUtil,
	admin models.Admin,
) (successCount int, failCount int, failReasons []string, err error) {
	//店铺必须显示 ，开始结束日期 影响的 内容是订单
	db := tools.GetDB()
	var marketings []models.MarketingGroupTemplateAttendance
	var foundResIds []int
	query := db.Model(marketings).
		Where("template_id = ?", id).
		Where("restaurant_id in ?", ResIds).
		Preload("Restaurant", func(d *gorm.DB) *gorm.DB {
			return d.Select("id,name_ug,name_zh")
		})
	// .
	// Select("id,restaurant_id")

	query.Pluck("restaurant_id", &foundResIds)
	query.Find(&marketings)
	var addResIds []int
	for _, v := range ResIds {
		if !tools.InArray(v, foundResIds) {
			successCount++
			addResIds = append(addResIds, v)
		} else {
			resName := ""
			for _, m := range marketings {
				if m.Restaurant.ID == v {
					if langUtils.Lang == "ug" {
						resName = m.Restaurant.NameUg
					} else {
						resName = m.Restaurant.NameZh
					}
					break
				}
			}
			failCount++
			msg := fmt.Sprintf(langUtils.T("marketing_group_added_current_res"), resName)
			failReasons = append(failReasons, msg)
		}
	}

	if len(addResIds) > 0 {
		var restaurants []models.Restaurant
		db.Model(&models.Restaurant{}).Where("id in ?", addResIds).Select("id,name_ug,name_zh").Find(&restaurants)
		go func() {
			defer func() {
				if err := recover(); err != nil {
					tools.Logger.Error("创建团体活动失败: ", err)
				}
			}()
			for _, restaurant := range restaurants {
				attendence := models.MarketingGroupTemplateAttendance{
					TemplateId:   id,
					RestaurantId: restaurant.ID,
					AdminId:      admin.ID,
					SendCount:    0,
					State:        models.MarketingGroupTemplateAttendenceStateNew,
				}
				db.Model(models.MarketingGroupTemplateAttendance{}).
					Clauses(clause.OnConflict{DoNothing: true}).
					Create(&attendence)
			}
		}()

	}

	return successCount, failCount, failReasons, nil
}

// SendPushOne 减配送费 活动 发送推送
func (s GroupTemplateService) SendPushOne(
	template models.MarketingGroupTemplate,
	attendanceItem models.MarketingGroupTemplateAttendance,
) (err error) {
	db := tools.GetDB()
	job := jobs.NewPushJob()
	pushData := jobs.PushData{
		UserId:   0,
		UserType: constants.PushUserTypeMerchant,
		PushContent: jobs.PushContent{
			TitleUg:   constants.TitleMerchantGroupShipmentUg,
			TitleZh:   constants.TitleMerchantGroupShipmentZh,
			ContentUg: template.NameUg,
			ContentZh: template.NameZh,
			Params: map[string]interface{}{
				"id":  tools.ToString(template.ID),
				"page": "group_marketing_detail",
			},
		},
		Client: models.PushDeviceClientMerchant,
		ChannelType: constants.ChannelTypeMerchantDefault,
	}
	if attendanceItem.ID > 0 {
		redisKey := fmt.Sprintf("group_template_push_%d_%d", template.ID, attendanceItem.RestaurantId)
		ret := tools.RedisLock(redisKey, 5*60) //5分钟内只发送一次
		if !ret {
			return errors.New("group_template_push_lock")
		}

		pushData.UserId = attendanceItem.RestaurantId
		job.PushData(pushData)
		db.Model(models.MarketingGroupTemplateAttendance{ID: attendanceItem.ID}).Updates(map[string]interface{}{
			"send_count": attendanceItem.SendCount + 1,
			"state":      models.MarketingGroupTemplateAttendenceStateSend,
		})
		return nil
	}
	for _, attendance := range template.MarketingGroupTemplateAttendance {

		redisKey := fmt.Sprintf("group_template_push_%d_%d", template.ID, attendance.RestaurantId)
		ret := tools.RedisLock(redisKey, 5*60) //5分钟内只发送一次
		if !ret {
			continue
		}

		pushData.UserId = attendance.RestaurantId
		job.PushData(pushData)
		db.Model(models.MarketingGroupTemplateAttendance{ID: attendance.ID}).Updates(map[string]interface{}{
			"send_count": attendance.SendCount + 1,
			"state":      models.MarketingGroupTemplateAttendenceStateSend,
		})
	}

	return nil
}

// SendJoinPush  查询可以改变状态的团体活动
func (s *GroupTemplateService) SendJoinPushOne(id int) (bool, error) {
	db := tools.GetDB()

	var attendance models.MarketingGroupTemplateAttendance
	query := db.Model(models.MarketingGroupTemplateAttendance{}).
		Preload("Template").
		Where("id = ?", id)
	rs := query.Find(&attendance)
	if rs.Error != nil {
		return false, errors.New("marketing_group_template_not_found")
	}
	// 开始推送消息

	err := s.SendPushOne(attendance.Template, attendance)

	return true, err
}
