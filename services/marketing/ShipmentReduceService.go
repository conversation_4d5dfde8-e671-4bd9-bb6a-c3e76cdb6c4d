package marketing

import (
	"encoding/json"
	"errors"
	"fmt"
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/observers"
	"mulazim-api/requests/marketingRequest"
	marketResource "mulazim-api/resources/marketing"
	"mulazim-api/scopes"
	"mulazim-api/tools"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm/clause"

	"github.com/golang-module/carbon/v2"
)

type ShipmentReduceService struct {
}

func NewShipmentReduceService() *ShipmentReduceService {
	shipmentTemplateService := ShipmentReduceService{}
	return &shipmentTemplateService
}

func (s ShipmentReduceService) GetShipmentReduceList(restaurantID int, page int, limit int, state int) ([]models.Marketing, int64) {
	var shipmentReduceList []models.Marketing
	query := tools.Db.Model(shipmentReduceList).Where("marketing_type = 2").Where("restaurant_id =?", restaurantID)
	if state > 0 {
		query = query.Where("state =?", state)
	}
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		query.Scopes(scopes.Page(page, limit)).Order("created_at desc").Find(&shipmentReduceList)
	}
	return shipmentReduceList, totalCount
}

func (s ShipmentReduceService) GetGroupMarketCount(restaurantID int) int64 {
	var groupMarketCount int64
	var attendance []models.MarketingGroupTemplateAttendance
	tools.Db.Model(models.MarketingGroupTemplateAttendance{}).
		Preload("Template").
		Where("state = 2"). // 已发送，没参加活动状态
		Where("restaurant_id = ?", restaurantID).
		Find(&attendance)
		for _, item := range attendance {
			// 找到结束时间
			endTime := carbon.Time2Carbon(*item.Template.EndDate).Format("Y-m-d") + " " + item.Template.Time1End + ":00"
			if item.Template.TimeCount == 2 {
				endTime = carbon.Time2Carbon(*item.Template.EndDate).Format("Y-m-d") + " " + item.Template.Time1End + ":00"
			} else if item.Template.TimeCount == 3 {
				endTime = carbon.Time2Carbon(*item.Template.EndDate).Format("Y-m-d") + " " + item.Template.Time1End + ":00"
			}
			// 活动时间已过期或状态没有启动状态，则不返回该活动
			if carbon.Parse(endTime).Lte(carbon.Now()) || item.Template.State != 1 {
				continue
			}
			groupMarketCount++
		}	
	return groupMarketCount
}

// func_name
//
// @Description:
// @Author: Rixat
// @Time: 2024-01-18 11:48:37
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) AttendanceGroupMarket(groupID int, restaurantID int) error {
	var template models.MarketingGroupTemplate
	tools.Db.Model(template).Where("id=?", groupID).First(&template)

	// 创建记录
	reduceModel := models.Marketing{
		NameUg:        template.NameUg,
		NameZh:        template.NameZh,
		BeginDate:     *template.BeginDate,
		EndDate:       *template.EndDate,
		FullWeekState: template.FullTimeState,
		FullTimeState: template.FullTimeState,
		AutoContinue:  template.AutoContinue,
		Day:           template.Day,
		Steps:         template.Steps,
		// 默认参数
		CreatorID:        template.CreatorID,
		CreatorType:      template.CreatorType, // 谁创建的 1.代理 2:商家
		CityID:           template.CityID,
		AreaID:           template.AreaID,
		MarketingType:    template.MarketingType, // 减配送活动
		RestaurantID:     restaurantID,
		GroupType:        1, // 1:团体活动，2：商家活动
		GroupId:          &template.ID,
		Type:             1, // 活动类型   1：店铺活动，2：商品定向活动
		State:            0,
		Time1Start:       template.Time1Start,
		Time1End:         template.Time1End,
		Time2Start:       template.Time2Start,
		Time2End:         template.Time2End,
		Time3Start:       template.Time3Start,
		Time3End:         template.Time3End,
		MinDeliveryPrice: template.MinDeliveryPrice,
	}
	err := tools.Db.Model(models.Marketing{}).Create(&reduceModel).Error
	if err != nil {
		return errors.New("fail")
	}
	// 更新attendance状态
	err = tools.Db.Model(models.MarketingGroupTemplateAttendance{}).Where("template_id=?", groupID).Where("restaurant_id=?", reduceModel.RestaurantID).UpdateColumns(map[string]interface{}{
		"state":        3,
		"marketing_id": reduceModel.ID,
	}).Error
	if err != nil {
		return errors.New("fail")
	}
	// 添加记录
	s.MarketingRestaurantState(reduceModel)
	return nil
}
func (s ShipmentReduceService) CreateShipmentReduce(admin models.Admin, params marketingRequest.ShipmentReduceRequest) error {
	beginDate := carbon.Parse(params.BeginDate).Carbon2Time()
	endDate := carbon.Parse(params.EndDate).Carbon2Time()
	steps, _ := json.Marshal(params.Steps)
	reduceModel := models.Marketing{
		NameUg:        params.NameUg,
		NameZh:        params.NameZh,
		BeginDate:     beginDate,
		EndDate:       endDate,
		FullWeekState: tools.ToInt(params.FullWeekState),
		FullTimeState: tools.ToInt(params.FullTimeState),
		AutoContinue:  tools.ToInt(params.AutoContinue),
		Day:           params.Day,
		Steps:         string(steps),
		// 默认参数
		CreatorID:        admin.ID,
		CreatorType:      models.MarketingCreatorTypeMerchant, // 谁创建的 1.代理 2:商家
		CityID:           admin.AdminCityID,
		AreaID:           admin.AdminAreaID,
		MarketingType:    models.MarketingMarketingTypeShipmentReduce, // 减配送活动
		RestaurantID:     params.RestaurantID,
		GroupType:        models.MarketingGroupTypeMerchant, // 1:团体活动，2：商家活动
		Type:             models.MarketingTypeShop,          // 活动类型   1：店铺活动，2：商品定向活动
		State:            models.MarketingStateNew,
		MinDeliveryPrice: params.MinDeliveryPrice,
	}
	reduceModel = s.DecodeTimeSteps(reduceModel, params.Timelines)
	error := tools.Db.Model(reduceModel).Create(&reduceModel).Error
	if error != nil {
		return errors.New("fail")
	}
	// 添加记录
	s.MarketingRestaurantState(reduceModel)
	return nil
}

// MarketingRestaurantState
//
// @Description: 餐厅活动状态更新记录
// @Author: Rixat
// @Time: 2024-01-25 10:07:42
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) MarketingRestaurantState(market models.Marketing) {
	var count int64
	tools.Db.Model(models.MarketingRestaurantState{}).Where("restaurant_id = ?", market.RestaurantID).Select("id").Count(&count)
	if count == 0 {
		tools.Db.Model(models.MarketingRestaurantState{}).Create(&models.MarketingRestaurantState{
			CityID:       market.CityID,
			AreaID:       market.AreaID,
			RestaurantID: market.RestaurantID,
			IsHaveMarket: 1,
		})
	}
}

// GetDetail
//
// @Description: 获取活动详情
// @Author: Rixat
// @Time: 2024-01-25 10:08:22
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) GetDetail(id int) models.Marketing {
	var reduceModel models.Marketing
	tools.Db.Model(&reduceModel).Where("id =?", id).First(&reduceModel)
	return reduceModel
}

// GetTemplateDetail
//
// @Description: 获取团体活动模板详情信息
// @Author: Rixat
// @Time: 2024-01-25 10:09:50
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) GetTemplateDetail(id int) models.MarketingGroupTemplate {
	var reduceModel models.MarketingGroupTemplate
	tools.Db.Model(&reduceModel).Where("id =?", id).First(&reduceModel)
	return reduceModel
}

// 获取没有参加的团体活动列表
//
// @Description:
// @Author: Rixat
// @Time: 2024-01-18 12:02:34
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) GetInvitationList(restaurantID int, langUtil lang.LangUtil) []map[string]interface{} {
	var groupAttendanceList []models.MarketingGroupTemplateAttendance
	tools.Db.Model(groupAttendanceList).Preload("Template").
		Where("state = 2").
		Where("restaurant_id = ?", restaurantID).
		Find(&groupAttendanceList)
	formatItems := make([]map[string]interface{}, 0)
	for _, item := range groupAttendanceList {
		// 找到结束时间
		endTime := carbon.Time2Carbon(*item.Template.EndDate).Format("Y-m-d") + " " + item.Template.Time1End + ":00"
		if item.Template.TimeCount == 2 {
			endTime = carbon.Time2Carbon(*item.Template.EndDate).Format("Y-m-d") + " " + item.Template.Time1End + ":00"
		} else if item.Template.TimeCount == 3 {
			endTime = carbon.Time2Carbon(*item.Template.EndDate).Format("Y-m-d") + " " + item.Template.Time1End + ":00"
		}
		// 活动时间已过期或状态没有启动状态，则不返回该活动
		if carbon.Parse(endTime).Lte(carbon.Now()) || item.Template.State != 1 {
			continue
		}
		formatItems = append(formatItems, map[string]interface{}{
			"id":         item.TemplateId,
			"name":       tools.GetNameByLang(item.Template, langUtil.Lang),
			"begin_date": item.Template.BeginDate.Format("2006-01-02"),
			"end_date":   item.Template.EndDate.Format("2006-01-02"),
			"tags": map[string]interface{}{
				"title":            langUtil.T("marketing_shipment_reduce"),
				"color":            "#FF4348",
				"background-color": "#FFF0F1",
			},
		})
	}
	return formatItems
}

// UpdateShipmentReduce
//
// @Description: 更新减配送活动
// @Author: Rixat
// @Time: 2024-01-18 12:03:05
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) UpdateShipmentReduce(params marketingRequest.ShipmentReduceRequest) error {
	var reduceModel models.Marketing
	tools.Db.Model(reduceModel).Where("id=?", params.ID).First(&reduceModel)
	if reduceModel.ID == 0 {
		return errors.New("not_found")
	}
	beginDate := carbon.Parse(params.BeginDate).Carbon2Time()
	endDate := carbon.Parse(params.EndDate).Carbon2Time()
	reduceModel.BeginDate = beginDate
	reduceModel.EndDate = endDate
	reduceModel.FullWeekState = tools.ToInt(params.FullWeekState)
	reduceModel.FullTimeState = tools.ToInt(params.FullTimeState)
	reduceModel.AutoContinue = tools.ToInt(params.AutoContinue)
	reduceModel.Day = params.Day
	// reduceModel.Steps = params.Steps
	reduceModel.MarketingType = 2 // 减配送活
	reduceModel.NameUg = params.NameUg
	reduceModel.NameZh = params.NameZh
	reduceModel.MinDeliveryPrice = params.MinDeliveryPrice
	steps, _ := json.Marshal(params.Steps)
	reduceModel.Steps = string(steps)
	reduceModel = s.DecodeTimeSteps(reduceModel, params.Timelines)
	error := tools.Db.Model(reduceModel).Save(&reduceModel).Error
	if error != nil {
		fmt.Println(error.Error())
		return errors.New("创建减配送活动失败")
	}
	return nil
}

// DecodeTimeSteps
//
// @Description:
// @Author: Rixat
// @Time: 2024-01-18 12:03:27
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) DecodeTimeSteps(reduceModel models.Marketing, timeLines []marketingRequest.Timeline) models.Marketing {
	// timeSteps := tools.StringToMapArr(timeStepsStr)
	for index, value := range timeLines {
		timeStart := value.Start
		timeEnd := value.End
		if index == 0 {
			reduceModel.Time1Start = timeStart
			reduceModel.Time1End = timeEnd
		}
		if index == 1 {
			reduceModel.Time2Start = timeStart
			reduceModel.Time2End = timeEnd
		}
		if index == 2 {
			reduceModel.Time3Start = timeStart
			reduceModel.Time3End = timeEnd
		}
	}
	return reduceModel
}

// / UpdateShipmentReduceState
//
// @Description: 修改减配送活动状态
// @Author: Rixat
// @Time: 2024-01-18 11:23:36
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) UpdateShipmentReduceState(admin models.Admin, id int, state int) error {
	var shipmentReduce models.Marketing
	tools.Db.Model(shipmentReduce).Where("id=?", id).First(&shipmentReduce)
	if shipmentReduce.ID == 0 {
		return errors.New("not_found")
	}
	// 如果团体活动，验证模板是否已停止
	if shipmentReduce.GroupType == 1 {
		var template models.MarketingGroupTemplate
		tools.Db.Model(template).Where("id=?", shipmentReduce.GroupId).First(&template)
		if template.State > 1 {
			return errors.New("this_group_marketing_stopped_enable_start")
		}
	}
	if state == 0 {
		return errors.New("marketing_enable_change")
	}

	if state == 1 {
		if shipmentReduce.State == 3 || shipmentReduce.State == 4 {
			return errors.New("marketing_not_editable")
		}
		if shipmentReduce.State == 1 {
			return nil
		}

		// 判断是否冲突
		conflictMarket, err := s.FindConflictMarketing(shipmentReduce)
		// 有关冲突活动
		if err == nil {
			if conflictMarket.GroupType == shipmentReduce.GroupType {
				return errors.New("conflict_marketing_found")
			}
			if shipmentReduce.GroupType == 2 && conflictMarket.GroupType == 1 {
				return errors.New("conflict_marketing_found")
			}
			// 关闭商家活动，并开启团体活动
			if shipmentReduce.GroupType == 1 && conflictMarket.GroupType == 2 {
				var observe = observers.MarketingChangeObserve{}
				//开始观察
				observe.Observe(conflictMarket.ID)
				tools.GetDB().Model(&models.Marketing{}).Where("id = ?", conflictMarket.ID).Update("state", models.MarketingStatePause)
				observe.SaveChanges(admin)
			}
		}
	}

	error := tools.Db.Model(shipmentReduce).Update("state", state).Error
	if error != nil {
		fmt.Println(error.Error())
		return errors.New("fail")
	}
	return nil
}

// / DeleteShipmentReduce
//
// @Description: 删除活动
// @Author: Rixat
// @Time: 2024-01-25 10:10:30
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) DeleteShipmentReduce(id int) error {
	// 没有下单的所有活动可以删除
	market := models.Marketing{}
	tools.Db.Table("t_marketing").Where("id", id).First(&market)
	if market.ID == 0 {
		return errors.New("marketing_not_found")
	}
	if market.GroupType == 1 {
		return errors.New("group_market_not_deletable")
	}
	// 有订单记录的活动不能删除
	marketLog := models.MarketingOrderLog{}
	tools.Db.Table("t_marketing_order_log").Where("marketing_id", market.ID).First(&marketLog)
	if marketLog.ID != 0 {
		return errors.New("marketing_not_deletable")
	}
	//删除前面 给一个状态
	updateMap := make(map[string]interface{})
	updateMap["state"] = 4
	updateMap["running_state"] = 0
	err1 := tools.Db.Table("t_marketing").Where("id", id).Updates(&updateMap).Error
	if err1 != nil {
		return errors.New("marketing_delet_failed")
	}
	err := tools.Db.Table("t_marketing").Where("id", id).Delete(&market).Error
	if err != nil {
		return errors.New("marketing_delet_failed")
	}
	return nil
}

// GetMerchantShipmentReduceStatistics
//
// @Description: 获取阶梯统计
// @Author: Rixat
// @Time: 2024-01-25 10:11:02
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) GetMerchantShipmentReduceStatistics(id int) map[string]interface{} {
	var orderLog models.MarketingOrderLog
	// 阶梯统计
	stepReduceMap := make(map[string]interface{}, 0)
	tools.Db.Model(orderLog).Select("name_ug,name_zh,sum(order_price) as total_price,count(id) as total_order_count,sum(reduction_fee) as total_reduce_shipment").
		Where("id=? and type=2", id).
		Group("reduction_fee,step_start_distance,step_end_distance").
		Scan(&stepReduceMap)
	return stepReduceMap
}

// GetStatisticData
//
// @Description: 获取商家端统计
// @Author: Rixat
// @Time: 2024-01-25 10:11:24
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) GetStatisticData(marketID int, startDate string, endDate string, langUtil lang.LangUtil) map[string]interface{} {
	// 汇总统计
	type HeadStatistic struct {
		AvgAmount    float64 `json:"avg_amount"`
		TotalAmount  int     `json:"total_amount"`
		TotalCount   int     `json:"total_count"`
		ReduceAmount int     `json:"reduce_amount"`
	}
	var head HeadStatistic
	headRaw := `count( id ) AS total_count,sum( order_price ) AS total_amount,sum( reduction_fee ) AS reduce_amount,avg( order_price ) AS avg_amount `
	tools.Db.Model(models.MarketingOrderLog{}).
		Select(headRaw).
		Where("marketing_id=?", marketID).
		Where("order_state=7").
		Where("created_at between ? and ?", startDate+" 00:00:00", endDate+" 23:59:59").
		Group("marketing_id").
		Scan(&head)

	var beforeHead HeadStatistic
	diffDay := carbon.Parse(startDate).DiffAbsInDays(carbon.Parse(endDate))
	beForStartDate := carbon.Parse(startDate).SubDays(tools.ToInt(diffDay)).Format("Y-m-d")
	beForEndDate := carbon.Parse(endDate).SubDays(tools.ToInt(diffDay)).Format("Y-m-d")
	tools.Db.Model(models.MarketingOrderLog{}).
		Select(headRaw).
		Where("marketing_id=?", marketID).
		Where("order_state=7").
		Where("created_at between ? and ?", beForStartDate+" 00:00:00", beForEndDate+" 23:59:59").
		Group("marketing_id").
		Scan(&beforeHead)

	// 直线图
	lineStatistic := make([]map[string]interface{}, 0)
	lineRaw := `DATE(created_at) as date,sum(order_price) as total_amount,count(id) as total_count`
	tools.Db.Model(models.MarketingOrderLog{}).
		Select(lineRaw).
		Where("marketing_id=?", marketID).
		Where("order_state=7").
		Where("created_at between ? and ?", startDate+" 00:00:00", endDate+" 23:59:59").
		Group("DATE(created_at)").
		Scan(&lineStatistic)

	res := map[string]interface{}{
		"head": map[string]interface{}{
			"total_amount":          head.TotalAmount,
			"total_amount_upgrade":  tools.GrowthRate(head.TotalAmount, beforeHead.TotalAmount),
			"total_count":           head.TotalCount,
			"total_count_upgrade":   tools.GrowthRate(head.TotalCount, beforeHead.TotalCount),
			"reduce_amount":         head.ReduceAmount,
			"reduce_amount_upgrade": tools.GrowthRate(head.ReduceAmount, beforeHead.ReduceAmount),
			"avg_amount":            head.AvgAmount,
			"avg_amount_upgrade":    tools.GrowthRate(head.AvgAmount, beforeHead.AvgAmount),
		},
		"line_amount": s.GetLineAmount(lineStatistic, startDate, endDate),
		"line_count":  s.GetLineCount(lineStatistic, startDate, endDate),
		"step":        s.GetReduceStepStatistic(marketID, langUtil),
	}
	return res
}

type Res struct {
	Name         string `json:"name"`
	TotalAmount  int    `json:"total_amount"`
	TotalCount   int    `json:"total_count"`
	ReduceAmount int    `json:"reduce_amount"`
}

func (reduce ShipmentReduceService) GetReduceStepStatistic(marketingId int, langUtil lang.LangUtil) []Res {

	var result = make([]Res, 0)
	selectRaw := `
		name_` + langUtil.Lang + ` as name,
		sum( order_price ) AS total_amount,
		count( id ) AS total_count,
		sum( reduction_fee ) AS reduce_amount
	`
	tools.GetDB().Model(models.MarketingOrderLog{}).
		Select(selectRaw).
		Where("marketing_id=?", marketingId).
		Where("order_state=7").
		Group("step_start_distance,step_end_distance").
		Scan(&result)
	if len(result) > 0 {
		return result
	}
	var market models.Marketing
	tools.Db.Model(market).Where("id=?", marketingId).First(&market)
	var steps []ShipmentReduceStep
	json.Unmarshal([]byte(market.Steps), &steps)
	for _, v := range steps {
		var item Res
		if v.DistanceEnd == 999000 {
			item.Name = fmt.Sprintf(langUtil.T("shipment_reduce_name_up"), reduce.FormatFloat(tools.ToFloat64(v.DistanceStart)/1000), reduce.FormatFloat(tools.ToFloat64(v.PriceReduce)/100))
		} else {
			item.Name = fmt.Sprintf(langUtil.T("shipment_reduce_name"), reduce.FormatFloat(tools.ToFloat64(v.DistanceStart)/1000), reduce.FormatFloat(tools.ToFloat64(v.DistanceEnd)/1000), reduce.FormatFloat(tools.ToFloat64(v.PriceReduce)/100))
		}
		item.TotalAmount = 0
		item.TotalCount = 0
		item.ReduceAmount = 0
		result = append(result, item)
	}
	return result
}

func (s ShipmentReduceService) GetLineAmount(lineList []map[string]interface{}, startDate string, endDate string) []map[string]interface{} {
	lineAmountList := make([]map[string]interface{}, 0)
	for _, v := range lineList {
		date := v["date"].(time.Time)
		lineAmountList = append(lineAmountList, map[string]interface{}{"date": tools.TimeFormatYmd(&date), "amount": tools.ToInt(v["total_amount"])})
	}

	res := make([]map[string]interface{}, 0)
	startDateCarbon := carbon.Parse(startDate)
	endDateCarbon := carbon.Parse(endDate)
	for date := startDateCarbon; date.Lte(endDateCarbon); date = date.AddDay() {
		has := false
		for _, amount := range lineAmountList {
			dateTime := amount["date"].(string)
			dateStr := date.Format("Y-m-d")
			fmt.Println(dateTime)
			if dateStr == dateTime {
				res = append(res, amount)
			}
		}
		if !has {
			res = append(res, map[string]interface{}{"date": date.Format("Y-m-d"), "amount": 0})
		}
	}
	return res
}
func (s ShipmentReduceService) GetLineCount(lineList []map[string]interface{}, startDate string, endDate string) []map[string]interface{} {
	lineCountList := make([]map[string]interface{}, 0)
	for _, v := range lineList {
		date := v["date"].(time.Time)
		lineCountList = append(lineCountList, map[string]interface{}{"date": tools.TimeFormatYmd(&date), "count": tools.ToInt(v["total_count"])})
	}
	res := make([]map[string]interface{}, 0)
	startDateCarbon := carbon.Parse(startDate)
	endDateCarbon := carbon.Parse(endDate)
	for date := startDateCarbon; date.Lte(endDateCarbon); date = date.AddDay() {
		has := false
		for _, amount := range lineCountList {
			dateTime := amount["date"].(string)
			dateStr := date.Format("Y-m-d")
			fmt.Println(dateTime)
			if dateStr == dateTime {
				res = append(res, amount)
			}
		}
		if !has {
			res = append(res, map[string]interface{}{"date": date.Format("Y-m-d"), "count": 0})
		}
	}
	return res
}

type MyMap struct {
	NewValue     interface{} `json:"NewValue"`
	OldValue     interface{} `json:"OldValue"`
	FiledName    string      `json:"FiledName"`
	RunningState string      `json:"RunningState"`
}
type ShipmentReduceStep struct {
	DistanceStart int `form:"distance_start" json:"distance_start" binding:"required"` // 开始距离
	DistanceEnd   int `form:"distance_end" json:"distance_end" binding:"required"`     // 结束距离
	PriceReduce   int `form:"price_reduce" json:"price_reduce" binding:"required"`     // 减免金额
	StoreReduce   int `form:"store_reduce" json:"store_reduce" binding:"required"`     // 商家承担金额
	DealerReduce  int `form:"dealer_reduce" json:"dealer_reduce" binding:"required"`   // 代理承担金额
}

func (s ShipmentReduceService) GetChangeFiledMap(changeFields string, langUtil lang.LangUtil) map[string]interface{} {
	var maps []MyMap
	json.Unmarshal([]byte(changeFields), &maps)
	var oldValues []string
	var newValues []string
	for _, value := range maps {
		if len(tools.ToString(value.NewValue)) > 0 {
			newValues = append(newValues, s.GetChangeValue(value, 2, langUtil))
		}
		if len(tools.ToString(value.OldValue)) > 0 {
			oldValues = append(oldValues, s.GetChangeValue(value, 1, langUtil))
		}
		if value.FiledName == "Steps" {
			var oldSteps []ShipmentReduceStep
			json.Unmarshal([]byte(value.OldValue.(string)), &oldSteps)
			for _, oldV := range oldSteps {
				distanceStart := s.FormatFloat(tools.ToFloat64(oldV.DistanceStart) / 1000)
				distanceEnd := s.FormatFloat(tools.ToFloat64(oldV.DistanceEnd) / 1000)
				priceReduce := s.FormatFloat(tools.ToFloat64(oldV.PriceReduce) / 100)
				dealerReduce := s.FormatFloat(tools.ToFloat64(oldV.DealerReduce) / 100)
				storeReduce := s.FormatFloat(tools.ToFloat64(oldV.DistanceStart) / 100)
				if oldV.DistanceEnd == 999000 {
					oldValues = append(oldValues, fmt.Sprintf(langUtil.T("shipment_reduce_step_name_up"), distanceStart, priceReduce, dealerReduce, storeReduce))
				} else {
					oldValues = append(oldValues, fmt.Sprintf(langUtil.T("shipment_reduce_step_name"), distanceStart, distanceEnd, priceReduce, dealerReduce, storeReduce))
				}
			}
			var newSteps []ShipmentReduceStep
			json.Unmarshal([]byte(value.NewValue.(string)), &newSteps)
			for _, newV := range newSteps {
				distanceStart := s.FormatFloat(tools.ToFloat64(newV.DistanceStart) / 1000)
				distanceEnd := s.FormatFloat(tools.ToFloat64(newV.DistanceEnd) / 1000)
				priceReduce := s.FormatFloat(tools.ToFloat64(newV.PriceReduce) / 100)
				dealerReduce := s.FormatFloat(tools.ToFloat64(newV.DealerReduce) / 100)
				storeReduce := s.FormatFloat(tools.ToFloat64(newV.DistanceStart) / 100)
				if newV.DistanceEnd == 999000 {
					newValues = append(newValues, fmt.Sprintf(langUtil.T("shipment_reduce_step_name_up"), distanceStart, priceReduce, dealerReduce, storeReduce))
				} else {
					newValues = append(newValues, fmt.Sprintf(langUtil.T("shipment_reduce_step_name"), distanceStart, distanceEnd, priceReduce, dealerReduce, storeReduce))
				}
			}
		}
	}
	if len(newValues) == 1 && maps[0].FiledName == "State" {
		return nil
	}
	return map[string]interface{}{
		"old_value": oldValues,
		"new_value": newValues,
	}
}

// / 格式化浮点数
//
// @Description:
// @Author: Rixat
// @Time: 2024-01-18 11:10:59
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) FormatFloat(floatValue float64) string {
	result := strconv.FormatFloat(floatValue, 'f', 4, 64)
	for strings.HasSuffix(result, "0") {
		result = strings.TrimSuffix(result, "0")
	}
	if strings.HasSuffix(result, ".") {
		result = strings.TrimSuffix(result, ".")
	}
	return result
}

// / GetChangeState
//
// @Description: 获取修改状态名称
// @Author: Rixat
// @Time: 2024-01-18 11:11:23
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) GetChangeState(changeFields string) int {
	var maps []MyMap
	json.Unmarshal([]byte(changeFields), &maps)
	if len(maps) == 1 && maps[0].FiledName == "State" {
		return tools.ToInt(maps[0].NewValue)
	}
	return 0
}

// GetMarketChangeLog
//
// @Description: 获取修改记录
// @Author: Rixat
// @Time: 2024-01-18 11:07:25
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) GetMarketChangeLog(marketID int, detail marketResource.FormatMerchantShipmentReduceDetail, langUtil lang.LangUtil) []map[string]interface{} {
	var changeLogs []map[string]interface{}
	tools.Db.Table("t_market_change_log").Where("marketing_id=?", marketID).Order("created_at desc").Scan(&changeLogs)
	// 格式化数据
	formatRes := make([]map[string]interface{}, 0)
	if len(changeLogs) > 0 {
		for _, v := range changeLogs {
			admin := make(map[string]interface{})
			tools.Db.Model(models.Admin{}).Where("id=?", v["admin_id"]).First(&admin)
			item := make(map[string]interface{})
			item["title"] = tools.If(langUtil.Lang == "ug", v["desc_ug"], v["desc_zh"])
			createdAt := v["created_at"].(time.Time)
			item["created_at"] = tools.TimeFormatYmdHis(&createdAt)
			item["operator"] = admin["real_name"]
			item["changed_fields"] = s.GetChangeFiledMap(v["changed_fields"].(string), langUtil)
			if len(s.GetChangeFiledMap(v["changed_fields"].(string), langUtil)) == 0 {
				item["current_data"] = nil
			} else {
				item["current_data"] = detail
			}
			item["state"] = s.GetChangeState(v["changed_fields"].(string))
			formatRes = append(formatRes, item)
		}
	}
	return formatRes
}

// / GetChangeValue
//
// @Description: 获取修改记录中的内容
// @Author: Rixat
// @Time: 2024-01-18 11:10:10
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) GetChangeValue(value MyMap, typeName int, langUtil lang.LangUtil) string {
	fieldName := ""
	switch value.FiledName {
	case "NameUg":
		fieldName = langUtil.T("name_ug")
	case "NameZh":
		fieldName = langUtil.T("name_zh")
	case "BeginDate":
		fieldName = langUtil.T("start_time")
	case "EndDate":
		fieldName = langUtil.T("end_time")
	case "FullWeekState":
		fieldName = langUtil.T("full_week_state")
	case "Day":
		fieldName = langUtil.T("day")
	case "FullTimeState":
		fieldName = langUtil.T("full_day_state")
	case "Time1Start":
		fieldName = langUtil.T("time")
	case "Time1End":
		fieldName = langUtil.T("time")
	case "Time2End":
		fieldName = langUtil.T("time")
	case "Time2Start":
		fieldName = langUtil.T("time")
	case "Time3Start":
		fieldName = langUtil.T("time")
	case "Time3End":
		fieldName = langUtil.T("time")
	case "AutoContinue":
		fieldName = langUtil.T("auto_continue")
	case "State":
		fieldName = langUtil.T("status")
	case "Steps":
		fieldName = langUtil.T("step")
	}
	valueStr := ""
	if typeName == 1 {
		valueStr = tools.ToString(value.OldValue)
	} else if typeName == 2 {
		valueStr = tools.ToString(value.NewValue)
	}

	switch value.FiledName {
	case "BeginDate":
		valueStr = carbon.Parse(valueStr).Format("Y-m-d")
	case "EndDate":
		valueStr = carbon.Parse(valueStr).Format("Y-m-d")
	case "Steps":
		valueStr = ""
	case "FullTimeState":
		if valueStr == "0" {
			valueStr = langUtil.T("yes")
		} else {
			valueStr = langUtil.T("no")
		}
	case "FullWeekState":
		if valueStr == "0" {
			valueStr = langUtil.T("yes")
		} else {
			valueStr = langUtil.T("no")
		}
	case "AutoContinue":
		if valueStr == "0" {
			valueStr = langUtil.T("no")
		} else {
			valueStr = langUtil.T("yes")
		}
	case "Day":
		valueStr = s.GetDayName(valueStr, langUtil)
	}

	return fieldName + ":" + valueStr
}

// 获取日期名称
//
// @Description:
// @Author: Rixat
// @Time: 2024-01-18 11:10:36
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) GetDayName(day string, langUtil lang.LangUtil) string {
	days := s.DecimalToBinaryList(tools.ToInt(day))
	dayStr := ""
	for index, value := range days {
		if value == 1 {
			dayStr = dayStr + langUtil.TArr("days")[index] + "/"
		}
	}
	lastIndex := strings.LastIndex(dayStr, "/")
	lenStr := len(dayStr)
	if lastIndex == lenStr-1 {
		dayStr = dayStr[:lastIndex]
	}
	return dayStr
}
func (s ShipmentReduceService) DecimalToBinaryList(decimal int) []int {
	// 将十进制转换为二进制字符串
	binary := strconv.FormatInt(int64(decimal), 2)
	// 在二进制字符串前面填充零，使其总长度为 7
	binary = fmt.Sprintf("%07s", binary)
	// 将字符串分解成单个字符的切片
	binaryChars := strings.Split(binary, "")
	// 将字符切片转换为整数切片
	var binaryList []int
	for _, char := range binaryChars {
		bit, _ := strconv.Atoi(char)
		binaryList = append(binaryList, bit)
	}
	return binaryList
}

// CreateRestaurantMarketingBatch 从cms请求创建活动
func (s ShipmentReduceService) CreateRestaurantMarketingBatch(request marketingRequest.CmsMarketingCreateRequest, admin models.Admin, restaurants []models.Restaurant) ([]models.Marketing, error) {
	var marketings []models.Marketing
	db := tools.GetDB()
	for _, restaurant := range restaurants {
		// 1.创建活动
		beginDate := carbon.Parse(request.BeginDate).Carbon2Time()
		endDate := carbon.Parse(request.EndDate).Carbon2Time()
		steps, _ := json.Marshal(request.Steps)

		marketing := models.Marketing{
			BeginDate:     beginDate,
			EndDate:       endDate,
			FullWeekState: request.FullWeekState,
			FullTimeState: *request.FullTimeState,
			AutoContinue:  *request.AutoContinue,
			Day:           request.Day,
			Steps:         string(steps),
			// 默认参数
			CreatorID:        admin.ID,
			CreatorType:      models.MarketingCreatorTypeDealer, // 谁创建的 1.代理 2:商家
			CityID:           restaurant.CityID,
			AreaID:           restaurant.AreaID,
			MarketingType:    models.MarketingMarketingTypeShipmentReduce, // 减配送活动
			RestaurantID:     restaurant.ID,
			GroupType:        models.MarketingGroupTypeMerchant, // 1:团体活动，2：商家活动
			Type:             models.MarketingTypeShop,          // 活动类型   1：店铺活动，2：商品定向活动
			NameUg:           request.NameUg,
			NameZh:           request.NameZh,
			MinDeliveryPrice: request.MinDeliveryPrice,
			State:            models.MarketingStateNew,
		}
		if *request.IsAllRestaurant == 1 {
			marketing.MarketingMode = models.MarketingMarketingModeAllRes
		} else {
			marketing.MarketingMode = models.MarketingMarketingModeSomeRes
		}
		for index, timeline := range request.Timelines {
			if index == 0 {
				marketing.Time1Start = timeline.Start
				marketing.Time1End = timeline.End
			}
			if index == 1 {
				marketing.Time2Start = timeline.Start
				marketing.Time2End = timeline.End
			}
			if index == 2 {
				marketing.Time3Start = timeline.Start
				marketing.Time3End = timeline.End
			}
		}
		marketings = append(marketings, marketing)
	}
	err := db.Create(&marketings).Error
	if err != nil {
		msg := fmt.Sprintf("创建普通减配送费活动失败：管理员ID: %d，%s", admin.ID, err.Error())
		tools.Logger.Error(msg)
		return marketings, errors.New("marketing_create_fail")
	}
	go func() {
		defer func() {
			if err := recover(); err != nil {
				tools.Logger.Error("创建团体活动失败: ", err)
			}
		}()
		for _, m := range marketings {
			var marketingRestaurantIsHaveShipmentReduce = models.MarketingRestaurantIsHaveShipmentReduce

			var count int64 = 0
			db.Model(models.MarketingRestaurantState{}).Where("restaurant_id = ?", m.RestaurantID).Count(&count)
			if count == 0 {
				db.Model(models.MarketingRestaurantState{}).
					Clauses(clause.OnConflict{
						Columns: []clause.Column{{Name: "restaurant_id"}},
					}).
					Create(&models.MarketingRestaurantState{
						CityID:               m.CityID,
						AreaID:               m.AreaID,
						RestaurantID:         m.RestaurantID,
						IsHaveShipmentReduce: &marketingRestaurantIsHaveShipmentReduce,
					})
			}
			if _, err := s.FindConflictMarketing(m); err != nil {
				db.Model(m).Update("state", models.MarketingStateActive)
			}
		}
	}()
	return marketings, nil
}

// FindConflictMarketing
//
// @Description: 验证冲突活动
// @Author: Rixat
// @Time: 2024-01-25 10:12:24
// @receiver
// @param c *gin.Context
func (s ShipmentReduceService) FindConflictMarketing(marketing models.Marketing) (models.Marketing, error) {
	var activeMarketings []models.Marketing
	tools.Db.Table("t_marketing").
		Where("restaurant_id = ?", marketing.RestaurantID).
		Where("marketing_type = ?", marketing.MarketingType).
		Where("state = ?", models.MarketingStateActive).
		Find(&activeMarketings)

	// 获取该餐厅正在运行的满减活动列表
	for _, m := range activeMarketings {
		if m.ID == marketing.ID {
			continue
		}
		if tools.DateTimeLineInDateTimeLine(
			marketing.BeginDate.Format("2006-01-02")+" 00:00:00",
			marketing.EndDate.Format("2006-01-02")+" 23:59:59",
			m.BeginDate.Format("2006-01-02")+" 00:00:00",
			m.EndDate.Format("2006-01-02")+" 23:59:59",
		) {
			return m, nil
		}
	}
	return models.Marketing{}, errors.New("conflict_marketing_not_found")
}

func (s ShipmentReduceService) UpdateRestaurantMarketing(marketing models.Marketing, requestData marketingRequest.CmsMarketingUpdateRequest, admin models.Admin) error {
	steps, _ := json.Marshal(requestData.Steps)
	updateData := map[string]interface{}{
		"name_ug":            requestData.NameUg,
		"name_zh":            requestData.NameZh,
		"begin_date":         requestData.BeginDate,
		"end_date":           requestData.EndDate,
		"full_week_state":    requestData.FullWeekState,
		"full_time_state":    requestData.FullTimeState,
		"auto_continue":      requestData.AutoContinue,
		"day":                requestData.Day,
		"steps":              string(steps),
		"min_delivery_price": requestData.MinDeliveryPrice,
	}

	timelinesLen := len(requestData.Timelines)
	for i := 0; i < 3; i++ {
		if i < timelinesLen {
			updateData[fmt.Sprintf("time%d_start", i+1)] = requestData.Timelines[i].Start
			updateData[fmt.Sprintf("time%d_end", i+1)] = requestData.Timelines[i].End
		} else {
			updateData[fmt.Sprintf("time%d_start", i+1)] = ""
			updateData[fmt.Sprintf("time%d_end", i+1)] = ""
		}
	}
	observe := observers.MarketingChangeObserve{}
	//开始观察
	observe.Observe(marketing.ID)
	tools.GetDB().
		Model(models.Marketing{}).
		Where("id = ?", marketing.ID).
		Updates(updateData)
	observe.SaveChanges(admin)
	return nil
}

// FindWithCreator 根据活动ID获取活动信息
func (s ShipmentReduceService) FindWithCreator(id int) models.Marketing {
	var marketing models.Marketing
	tools.Db.Model(marketing).
		Preload("Creator").
		Where("id = ?", id).
		First(&marketing)
	return marketing
}

// FindWithCreatorAndArea 根据活动ID获取活动信息
func (s ShipmentReduceService) FindWithCreatorAndAreaAndCityAndRestaurant(id int) models.Marketing {
	var marketing models.Marketing
	tools.Db.Model(marketing).
		Preload("Creator").
		Preload("Area").
		Preload("Restaurant").
		Preload("City").
		Where("id = ?", id).
		First(&marketing)
	return marketing
}

// Find 根据活动ID获取活动信息
func (s ShipmentReduceService) FindWithCreatorAndRestaurant(id int) models.Marketing {
	var marketing models.Marketing
	tools.Db.Model(marketing).
		Preload("Creator").
		Preload("Restaurant").
		Where("id = ?", id).
		First(&marketing)
	return marketing
}

// GetMarketingOrderLogAgg 获取当天订单统计
func (s ShipmentReduceService) GetMarketingOrderLogAgg(marketingId int) models.MarketingOrderLogAggs {
	var agg models.MarketingOrderLogAggs
	// 获取今日订单总数
	tools.Db.Table("t_marketing_order_log").
		//Joins(" INNER JOIN t_order_today on t_order_today.id = t_marketing_order_log.order_id AND t_order_today.state in (7,8)").
		Select("marketing_id,sum(t_marketing_order_log.order_price) as total_order_price,"+
			"count(t_marketing_order_log.id) as order_count,"+
			"sum(t_marketing_order_log.step_reduce) as total_reduce_price,"+
			"sum(t_marketing_order_log.dealer_cost) as total_dealer_cost,"+
			"sum(t_marketing_order_log.res_cost) as total_res_cost").
		Where("marketing_id = ? ", marketingId).
		Where("order_state in (7,10)").
		Scan(&agg)
	return agg
}

//// GetMarketingOrderLogAgg 获取归档订单统计
//func (s ShipmentReduceService) GetMarketingOrderLogAgg(marketingId int) models.MarketingOrderLogAggs {
//	var agg models.MarketingOrderLogAggs
//	// 获取今日订单总数
//	tools.Db.Table("t_marketing_order_log").
//		Joins("INNER JOIN t_order on t_order.id = t_marketing_order_log.order_id AND t_order.state = 7").
//		Select("marketing_id,sum(t_marketing_order_log.order_price) as total_order_price,"+
//			"count(t_marketing_order_log.id) as order_count,"+
//			"sum(t_marketing_order_log.step_reduce) as total_reduce_price,"+
//			"sum(t_marketing_order_log.dealer_cost) as total_dealer_cost,"+
//			"sum(t_marketing_order_log.res_cost) as total_res_cost").
//		Where("marketing_id = ? ", marketingId).
//		Scan(&agg)
//	return agg
//}

// GetMarketingOrdersPaginate 减配送费活动订单列表
func (s ShipmentReduceService) GetMarketingOrdersPaginate(marketing models.Marketing, request marketingRequest.MarketingOrderPageInfo) ([]models.MarketingOrderLog, int64) {
	var orders []models.MarketingOrderLog
	query := tools.Db.Model(models.MarketingOrderLog{}).
		//Joins("Order", tools.Db.Where(models.Order{State: 8})).
		//Joins("OrderToday", tools.Db.Where(models.OrderToday{State: 8})).
		Joins("left join t_order_today on t_marketing_order_log.order_id = t_order_today.id and t_order_today.state = 7").
		Joins("left join t_order on t_marketing_order_log.order_id = t_order.id and t_order.state = 7").
		Select("t_marketing_order_log.*, t_order_today.order_address as today_address, t_order.order_address as address").
		Where("marketing_id = ?", marketing.ID).
		Where("t_marketing_order_log.order_state in ?", []int{models.ORDER_STATE_DELIVERY_COMPLETED, models.ORDER_STATE_DELIVERY_FAIL})
		//Where(" (t_order.state = 7 or t_order_today.state=7) ").

	sorts := ""
	if request.SortBy != "" {
		if request.SortBy == "created_at" {
			sorts = "id "
		} else {
			sorts = request.SortBy + " "
		}
		if request.SortType == "asc" {
			sorts += "asc"
		} else {
			sorts += "desc"
		}
	}
	query.Order(sorts)
	request.Keyword = strings.Trim(request.Keyword, " ")
	if len(request.Keyword) > 0 {
		query.Where("(t_order.mobile like ? or t_order_today.mobile like ? or t_marketing_order_log.order_no like ?)",
			"%"+request.Keyword+"%",
			"%"+request.Keyword+"%",
			"%"+request.Keyword+"%")
	}
	if request.BeginDate != "" {
		beginDate, _ := time.Parse("2006-01-02", request.BeginDate)
		query.Where("t_marketing_order_log.created_at >= ?", beginDate.Format("2006-01-02")+" 00:00:00")
	}
	if request.EndDate != "" {
		endDate, _ := time.Parse("2006-01-02", request.EndDate)
		query.Where("t_marketing_order_log.created_at <= ?", endDate.Format("2006-01-02")+" 23:59:59")
	}

	var totalCount int64
	query.Count(&totalCount)

	if totalCount > 0 {
		query.
			Preload("Order.Shipper").
			Preload("OrderToday.Shipper").
			Preload("Restaurant").
			Scopes(scopes.Page(request.Page, request.Limit)).Find(&orders)
	}
	return orders, totalCount

}

func (s ShipmentReduceService) GetMarketingOrders(marketing models.Marketing, request marketingRequest.MarketingOrdersDownloadRequest) []models.MarketingOrderLog {
	var orders []models.MarketingOrderLog
	query := tools.Db.Model(models.MarketingOrderLog{}).
		//Joins("Order", tools.Db.Where(models.Order{State: 8})).
		//Joins("OrderToday", tools.Db.Where(models.OrderToday{State: 8})).
		Joins("left join t_order_today on t_marketing_order_log.order_id = t_order_today.id and t_order_today.state = 7").
		Joins("left join t_order on t_marketing_order_log.order_id = t_order.id and t_order.state = 7").
		Select("t_marketing_order_log.*, t_order_today.order_address as today_address, t_order.id, t_order.order_address as address").
		Where("marketing_id = ?", marketing.ID).
		Where(" (t_order.state = 7 or t_order_today.state=7) ").
		Order("t_marketing_order_log.id desc")
	request.Keyword = strings.Trim(request.Keyword, " ")
	if len(request.Keyword) > 0 {
		query.Where("(t_order.mobile = ? or t_order_today.mobile = ?)", request.Keyword, request.Keyword)
	}
	if request.BeginDate != "" {
		beginDate, _ := time.Parse("2006-01-02", request.BeginDate)
		query.Where("t_marketing_order_log.created_at >= ?", beginDate.Format("2006-01-02")+" 00:00:00")
	}
	if request.EndDate != "" {
		endDate, _ := time.Parse("2006-01-02", request.EndDate)
		query.Where("t_marketing_order_log.created_at <= ?", endDate.Format("2006-01-02")+" 23:59:59")
	}

	query.
		Preload("Order.Shipper").
		Preload("OrderToday.Shipper").
		Preload("Restaurant").
		Find(&orders)
	return orders
}

func (s ShipmentReduceService) Find(id int) models.Marketing {
	var marketing models.Marketing
	tools.Db.Model(marketing).Where("id = ?", id).First(&marketing)
	return marketing
}

func (s ShipmentReduceService) ChangeStatus(marketing models.Marketing, status interface{}) (models.Marketing, error) {
	if marketing.State > 2 {
		return marketing, errors.New("marketing_state_is_can_not_update")
	}
	tools.Db.Model(marketing).Update("state", status)
	return marketing, nil
}

func (s ShipmentReduceService) GetMarketingPaginate(
	cityId int,
	areaId int,
	restaurantId int,
	state int,
	beginDate string,
	endDate string,
	page int,
	limit int,
	keyword string,
	sort string,
) ([]models.MarketingStaticWithOrderLog, int64) {
	var marketings []models.MarketingStaticWithOrderLog
	db := tools.GetDB()
	query := db.Model(models.MarketingStaticWithOrderLog{}).Where("marketing_type = 2").Where("group_type = 2")

	if restaurantId > 0 {
		query.Where("restaurant_id = ?", restaurantId)
	} else if areaId > 0 {
		query.Where("area_id = ?", areaId)
	} else if cityId > 0 {
		query.Where("city_id = ?", cityId)
	}

	if state >= 0 {
		query = query.Where("state = ?", state)
	}
	if state == 4 {
		query.Unscoped()
	}

	if len(beginDate) > 0 {
		query.Where("created_at >= ?", beginDate+" 00:00:00")
	}
	if len(endDate) > 0 {
		query.Where("created_at <= ?", endDate+" 23:59:59")
	}
	if len(keyword) > 0 {
		query.Where("name_ug like ? or name_zh like ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	var totalCount int64
	query.Count(&totalCount)
	if sort != "" {
		query.Order(sort)
	} else {
		query.Order("id desc")
	}

	if totalCount > 0 {
		query.Scopes(scopes.Page(page, limit)).
			Preload("Restaurant").
			Preload("Creator").
			Preload("Area").
			Preload("City").
			Select("t_marketing.*, (SELECT count(t_marketing_order_log.id) from t_marketing_order_log WHERE t_marketing_order_log.marketing_id=t_marketing.id and t_marketing_order_log.order_state in (7, 10)) as order_count," +
				"(SELECT sum(t_marketing_order_log.order_price) from t_marketing_order_log WHERE t_marketing_order_log.marketing_id=t_marketing.id and t_marketing_order_log.order_state in (7, 10)) as total_order_price," +
				"(SELECT sum(t_marketing_order_log.reduction_fee) from t_marketing_order_log WHERE t_marketing_order_log.marketing_id=t_marketing.id and t_marketing_order_log.order_state in (7, 10)) as total_reduce_price").
			Find(&marketings)
	}

	return marketings, totalCount

}

// 减配送费列表下载
func (s ShipmentReduceService) GetMarketingDownload(
	cityId int,
	areaId int,
	restaurantId int,
	state int,
	beginDate string,
	endDate string,
	keyword string,
	langUtil lang.LangUtil,
) (allExport []map[string]interface{}, cols []string, er1 error) {
	var marketings []models.MarketingStaticWithOrderLog
	db := tools.GetDB()
	query := db.Model(marketings).Where("marketing_type = 2").Where("group_type = 2")

	if restaurantId > 0 {
		query = query.Where("restaurant_id = ?", restaurantId)
	} else if areaId > 0 {
		query = query.Where("area_id = ?", areaId)
	} else if cityId > 0 {
		query = query.Where("city_id = ?", cityId)
	}

	if state >= 0 {
		query = query.Where("state = ?", state)
	}
	if len(beginDate) > 0 {
		query = query.Where("created_at >= ?", beginDate+" 00:00:00")
	}
	if len(endDate) > 0 {
		query = query.Where("created_at <= ?", endDate+" 23:59:59")
	}
	if len(keyword) > 0 {
		query = query.Where("name_ug like ? or name_zh like ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	excelItems := make([]map[string]interface{}, 0)
	query.
		Preload("City"). //列表输出 城市和 区域
		Preload("Area").
		Preload("Restaurant").
		Preload("Creator").
		Select("t_marketing.*, (SELECT count(t_marketing_order_log.id) from t_marketing_order_log WHERE t_marketing_order_log.marketing_id=t_marketing.id and t_marketing_order_log.order_state in (7, 10)) as order_count," +
			"(SELECT sum(t_marketing_order_log.order_price) from t_marketing_order_log WHERE t_marketing_order_log.marketing_id=t_marketing.id and t_marketing_order_log.order_state in (7, 10)) as total_order_price," +
			"(SELECT sum(t_marketing_order_log.reduction_fee) from t_marketing_order_log WHERE t_marketing_order_log.marketing_id=t_marketing.id and t_marketing_order_log.order_state in (7, 10)) as total_reduce_price").
		Order("t_marketing.state asc").
		Where("state in (0,1,2,3)").
		Find(&marketings)
	cityName := ""
	areaName := ""
	for _, m := range marketings {
		if langUtil.Lang == "ug" {
			cityName = m.City.NameUg
			areaName = m.Area.NameUg
		} else {
			cityName = m.City.NameZh
			areaName = m.Area.NameZh
		}

		key := fmt.Sprintf("marketing_state_%d", m.Marketing.State)
		stateName := langUtil.T(key)
		resName := ""
		if m.Restaurant != nil {
			if langUtil.Lang == "ug" {
				resName = m.Restaurant.NameUg
			} else {
				resName = m.Restaurant.NameZh
			}
		}
		marketingName := ""
		if langUtil.Lang == "ug" {
			marketingName = m.NameUg
		} else {
			marketingName = m.NameZh
		}
		excelItems = append(excelItems, map[string]interface{}{
			"ID":   m.ID,
			"城市":   cityName,
			"区域":   areaName,
			"活动名称": marketingName,
			"店铺名称": resName,
			// 状态  0：新建，1:启动，2：暂停，3：失效，4:删除
			"状态":      stateName,
			"添加时间":    m.CreatedAt.Format("2006-01-02"),
			"活动开始时间":  m.BeginDate.Format("2006-01-02"),
			"活动结束时间":  m.EndDate.Format("2006-01-02"),
			"总交易金额":   tools.ToPrice(tools.ToFloat64(m.TotalOrderPrice) / 100),
			"总订单数量":   m.OrderCount,
			"总优惠的配送费": tools.ToPrice(tools.ToFloat64(m.TotalReducePrice) / 100),
			"创建人":     m.Creator.RealName,
		})
	}

	cols = []string{
		"ID",
		"城市",
		"区域",
		"活动名称",
		"店铺名称",
		"状态",
		"添加时间",
		"活动开始时间",
		"活动结束时间",
		"总交易金额",
		"总订单数量",
		"总优惠的配送费",
		"创建人",
	}
	return excelItems, cols, nil

}

// MarketingDelete 减配送费 活动删除
func (s ShipmentReduceService) MarketingDelete(
	admin models.Admin,
	ids []int,
	langUtil lang.LangUtil,
) (successCount, failCount int, failReasons []string, err error) {

	db := tools.GetDB()
	var marketings []models.MarketingWithOrderLogCount
	failReasons = make([]string, 0)
	// 判断是否可以删除
	// 可以删除的 删除 不能删除的 解释原因
	query := db.Model(&models.MarketingWithOrderLogCount{}).
		Select("t_marketing.*, (select count(t_marketing_order_log.id) from t_marketing_order_log  WHERE t_marketing_order_log.marketing_id=t_marketing.id) as order_log_count").
		Where("marketing_type = ?", models.MarketingMarketingTypeShipmentReduce).
		Where("group_type = ? ", models.MarketingGroupTypeMerchant).
		Where("id in (?)", ids)
	if admin.IsDealer() || admin.IsDealerSub() {
		query = query.Where("area_id = ?", admin.AdminAreaID)
	}
	query.Find(&marketings)

	for _, marketing := range marketings {
		if marketing.State == models.MarketingStateDelete {
			successCount++
			continue
		}
		if marketing.CreatorType != models.MarketingCreatorTypeDealer {
			failCount++
			msg := fmt.Sprintf(langUtil.T("marketing_create_by_merchant_not_editable"), marketing.ID)
			failReasons = append(failReasons, msg)
			continue
		}

		if marketing.OrderLogCount > 0 {
			msg := fmt.Sprintf(langUtil.T("marketing_has_order_can_not_delete"), marketing.ID)
			failReasons = append(failReasons, msg)
			failCount++
		} else {
			observe := observers.MarketingChangeObserve{}
			//开始观察
			observe.Observe(marketing.ID)
			db.Model(&models.Marketing{}).Where("id = ?", marketing.ID).Updates(map[string]interface{}{
				"state":      4,
				"deleted_at": carbon.Now().Format("Y-m-d H:i:s"),
			})
			observe.SaveChanges(admin)
			successCount++
		}
	}
	return
}

// GetStatisticsPaginate 获取减配送费统计列表
func (s ShipmentReduceService) GetStatisticsPaginate(
	cityId, areaId, restaurantId int, startDate, endDate string, page, limit int,
	sorts string,
) ([]models.MarketingStatisticsWithOrderLog, int64) {
	var marketingsWithAggs = make([]models.MarketingStatisticsWithOrderLog, 0)
	query := tools.Db.Model(&models.MarketingStatisticsWithOrderLog{}).Where("marketing_type = 2").Where("group_type = 2")

	if restaurantId > 0 {
		query = query.Where("restaurant_id = ?", restaurantId)
	} else if areaId > 0 {
		query = query.Where("area_id = ?", areaId)
	} else if cityId > 0 {
		query = query.Where("city_id = ?", cityId)
	}

	if len(startDate) > 0 {
		query = query.Where("created_at >= ?", startDate+" 00:00:00")
	}
	if len(endDate) > 0 {
		query = query.Where("created_at <= ?", endDate+" 23:59:59")
	}
	var totalCount int64
	query.Count(&totalCount)
	if totalCount > 0 {
		if sorts != "" {
			query.Order(sorts)
		} else {
			query.Order("id desc")
		}
		query.
			Preload("Restaurant").
			Preload("Creator").
			Select("t_marketing.*, (SELECT count(t_marketing_order_log.id) from t_marketing_order_log WHERE t_marketing_order_log.marketing_id=t_marketing.id and t_marketing_order_log.order_state in (7, 10)) as order_count," +
				"(SELECT count(t_marketing_order_log.id) from t_marketing_order_log WHERE t_marketing_order_log.marketing_id=t_marketing.id and t_marketing_order_log.order_state in (8, 9)) as rejected_order_count," +
				"(SELECT sum(t_marketing_order_log.order_price) from t_marketing_order_log WHERE t_marketing_order_log.marketing_id=t_marketing.id and t_marketing_order_log.order_state in (7, 10)) as total_order_price," +
				"(SELECT sum(t_marketing_order_log.reduction_fee) from t_marketing_order_log WHERE t_marketing_order_log.marketing_id=t_marketing.id and t_marketing_order_log.order_state in (7, 10)) as total_reduce_price").
			Scopes(scopes.Page(page, limit)).
			Find(&marketingsWithAggs)
	}
	return marketingsWithAggs, totalCount
}

func (s ShipmentReduceService) ChangeState(ids []int, state int, admin models.Admin, lang lang.LangUtil) (successCount int, failCount int, failReasons []string, err error) {
	failReasons = make([]string, 0)
	// 1.获取活动列表
	var marketings []models.Marketing
	query := tools.Db.Model(&models.Marketing{}).
		Where("marketing_type = 2").
		Where("group_type = 2").
		Where("id in (?)", ids)
	if admin.IsDealerSub() || admin.IsDealer() {
		query.Where("area_id = ?", admin.AdminAreaID)
	}
	query.Find(&marketings)
	if len(marketings) == 0 {
		return 0, 0, make([]string, 0), errors.New("marketing_not_found")
	}
	for _, marketing := range marketings {
		// 如果不可更新状态的活动，跳过
		if marketing.State == state {
			successCount++
			continue
		}
		if marketing.State > models.MarketingStatePause {
			state := lang.T("unkown")
			if stateText, ok := lang.TArr("marketing_state_name")[marketing.State]; ok {
				state = stateText
			}
			msg := fmt.Sprintf(lang.T("marketing_wrong_state_can_not_updated"), marketing.ID, state)
			failReasons = append(failReasons, msg)
			failCount++
			continue
		}
		if marketing.CreatorType != models.MarketingCreatorTypeDealer {
			failCount++
			msg := fmt.Sprintf(lang.T("marketing_create_by_merchant_not_editable"), marketing.ID)
			failReasons = append(failReasons, msg)
			continue
		}
		if models.MarketingStateActive == state {
			conflictMarketing, _ := s.FindConflictMarketing(marketing)
			if conflictMarketing.ID > 0 {
				//开始观察
				var observe = observers.MarketingChangeObserve{}
				observe.Observe(conflictMarketing.ID)
				tools.GetDB().Model(&models.Marketing{}).Where("id = ?", conflictMarketing.ID).Update("state", models.MarketingStatePause)
				observe.SaveChanges(admin)
				msg := fmt.Sprintf("活动 [%d](需要开启的活动)与活动[%d]（已开启的活动）时间冲突，[%d]已暂停", marketing.ID, conflictMarketing.ID, conflictMarketing.ID)
				tools.Logger.Debug(msg)
			}
		} else {
			state = models.MarketingStatePause
		}
		var observe = observers.MarketingChangeObserve{}
		observe.Observe(marketing.ID)
		tools.GetDB().Model(&models.Marketing{}).Where("id = ?", marketing.ID).Update("state", state)
		observe.SaveChanges(admin)
		successCount++
	}
	return
}
