package marketing

import (
	"fmt"

	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/services"
	"mulazim-api/tools"
	"strings"

)

type MarketingService struct {
	baseService services.BaseService
	langUtil *lang.LangUtil
	language string
}

func NewMarketingService(langUtil lang.LangUtil, language string) *MarketingService {
	marketService := MarketingService{
		langUtil: &langUtil,
		language: language,
	}
	return &marketService
}



// UpadateMarketingState
//
// @Description: 修改活动状态
// @Author: rozimamat
// @Time: 2025-04-02 16:15:40
// @receiver
// @param c *gin.Context
func (marketing MarketingService) UpadateMarketingState(idStr string, state int,admin models.Admin,langUtil lang.LangUtil) (map[string]interface{}) {
	var markets []models.Marketing
	ids :=strings.Split(idStr,",")
	db :=tools.GetDB()
	db.Model(&models.Marketing{}).Where("id in (?)", ids).Find(&markets)
	data :=map[string]interface{}{}
	successCount :=0
	failCount :=0
	notCreator :=0
	dealerCannotChange := false
	failReasons :=make([]string,0)
	for _, m := range markets {
		mName :=m.NameUg
		if langUtil.Lang != "ug"{
			mName =m.NameZh
		}
		if(m.CreatorType == 2 && (admin.Type != 1 )){
			failCount++
			notCreator++
			dealerCannotChange = true
			failReasons = append(failReasons,fmt.Sprintf("[%s] %s",mName ,langUtil.T("created_by_store")))
			continue;
		}
		if(state==1){// 要开始活动
			//监测是否可以 开启
			if(m.State==3){
				failCount++
				failReasons = append(failReasons,fmt.Sprintf("[%s] %s",mName ,langUtil.T("expired")))
				continue;
			}
			if(m.State==1){
				successCount++
				continue;
			}
			conflict :=marketing.baseService.IsMarketingTimeConflict(m,langUtil.Lang)
			if conflict == nil  {
				//监测多份打折
				canUpdate :=true
				if len(m.Foods) > 0{
					foods :=  strings.Split(m.Foods,",")
					multiDiscountConflict :=false
					multiDiscountConflictFoodName :=""

					var markets []models.Marketing
					markets = append(markets, m)
					timesMarkets :=  marketing.baseService.GetMarketArrTimeMap(markets,marketing.language)
					

					for _, ff := range foods {
						// 监测多份打折
						// 获取该餐厅正在运行的满减活动列表
						for _, inValue := range timesMarkets {
							inBeginTime := inValue["start"]
							inEndTime := inValue["end"]
							_,foodName :=marketing.baseService.CheckFoodsMultipleDiscount(marketing.language,tools.ToInt(ff),inBeginTime,inEndTime)	
							if len(foodName) >0 {
								multiDiscountConflict =true
								multiDiscountConflictFoodName =foodName
							}
						}
					}
					if multiDiscountConflict {
						failCount++
						failReasons = append(failReasons,fmt.Sprintf("[%s] %s [%s] %s",mName,langUtil.T("and"),multiDiscountConflictFoodName,langUtil.T("time_conflict_in_multi_discount")))
						canUpdate =false
						continue
					}
				}
				if canUpdate {
					db.Model(&models.Marketing{}).Where("id  = ?", m.ID).Updates(&map[string]interface{}{
						"state":1,
						"running_state":1,
					})
					successCount++
				}
				
				   
			}else{
				failReasons = append(failReasons,fmt.Sprintf("[%s] %s [%s] %s %s",mName, langUtil.T("and"),conflict.Error() ,langUtil.T("ing"),langUtil.T("time_conflict")))
				failCount++
			}

		}else if(state == 2){
			// 停止
			db.Model(&models.Marketing{}).Where("id  = ?", m.ID).Updates(&map[string]interface{}{
				"state":2,
				"running_state":0,
			})
			successCount++
		}
	}
	data["success_count"]=successCount
	data["fail_count"]=failCount
	data["not_creator"]=notCreator
	data["dealer_cannot_change"] = dealerCannotChange
	data["fail_reasons"] = failReasons
	return data

	 
}




