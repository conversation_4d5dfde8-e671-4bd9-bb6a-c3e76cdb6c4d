package services

import (
	"errors"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/models"
	"mulazim-api/tools"
	"time"
)

type PushDeviceService struct {
}

// PushDeviceServiceInst 单例
var PushDeviceServiceInst = &PushDeviceService{}

// GetByRid 根据rid获取设备信息
func (s *PushDeviceService) GetByRid(rid string, client models.PushDeviceClient) (*models.PushDevice, error) {
	var device = models.PushDevice{
		Rid:    rid,
		Client: client,
	}
	err := tools.GetDB().
		First(&device).Error
	return &device, err
}

func (s *PushDeviceService) CreateUserDevice(
	user models.User,
	rid string,
	platform models.PushDevicePlatform,
	client models.PushDeviceClient,
	lang models.PushDeviceLang,
) (models.PushDevice, error) {
	var device = models.PushDevice{
		Rid:    rid,
		Client: client,
	}
	rs := tools.GetDB().
		First(&device)
	if rs.Error != nil {
		return device, rs.Error
	}

	if rs.RowsAffected > 0 {
		if device.UserID != user.ID {
			device.UserID = user.ID
		}
		device.Lang = lang
		device.Platform = platform
		device.UpdatedAt = time.Now()
		tools.GetDB().Model(&device).Updates(device)
		return device, nil
	}
	device.Platform = platform
	tools.GetDB().Model(&user).Association("PushDevices").Append(&device)
	return device, nil
}

// CreateAdminDevice 配额宋元、商家端设备绑定
func (s *PushDeviceService) CreateAdminDevice(
	admin models.Admin,
	rid string,
	platform models.PushDevicePlatform,
	client models.PushDeviceClient,
	lang models.PushDeviceLang,
	adminId int,
	deviceAppVersion int64,
	brand string,
) (models.PushDevice, error) {
	db :=tools.GetDB()
	defer func ()  {
		if err := recover(); err != nil {
			tools.Logger.Info(fmt.Sprintf("order_push 注册设备 error: %v", err))
		}
	}()
	tools.Logger.Info("order_push_注册设备 配送端 rid:"+rid+",admin_id:"+tools.ToString(adminId)+",user_id:"+tools.ToString(adminId))
	var device models.PushDevice
	var devices []models.PushDevice
	db.Model(models.PushDevice{}).Where("rid = ? and client = ?",rid,client).Scan(&devices)
	if len(devices) > 0 {
		db.Delete(&devices)
	}

	db.Model(models.PushDevice{}).Where("admin_id = ? and client = ?",adminId,client).Scan(&devices)
	if len(devices) > 0 {
		db.Delete(&devices)
	}

	device.Rid = rid
	device.Platform = platform
	device.UserType = "t_admin"
	device.UserID = adminId
	device.AdminID=adminId
	device.Client = client
	device.Lang = lang
	device.CreatedAt = time.Now()
	device.UpdatedAt = time.Now()
	device.Brand = brand
	device.DeviceAppVersion = deviceAppVersion
	db.Create(&device)
	return device, nil
}

func (s *PushDeviceService) CreateRestaurantDevice(
	restaurant models.Restaurant,
	rid string,
	platform models.PushDevicePlatform,
	lang models.PushDeviceLang,
	adminId int,
	deviceAppVersion int64,
	brand string,
) (models.PushDevice, error) {
	
	db :=tools.GetDB()
	defer func ()  {
		if err := recover(); err != nil {
			tools.Logger.Info(fmt.Sprintf("order_push 注册设备 error: %v", err))
		}
	}()
	tools.Logger.Info("order_push_注册设备 商家端 rid:"+rid+",admin_id:"+tools.ToString(adminId)+",user_id:"+tools.ToString(restaurant.ID))
	var device models.PushDevice
	var devices []models.PushDevice
	db.Model(models.PushDevice{}).Where("rid = ? and client=?",rid,models.PushDeviceClientMerchant).Scan(&devices)
	if len(devices) > 0 {
		db.Delete(&devices)
	}
	

	db.Model(models.PushDevice{}).Where("admin_id = ? and client = ?",adminId,models.PushDeviceClientMerchant).Scan(&devices)
	if len(devices) > 0 {
		db.Delete(&devices)
	}

	device.Rid = rid
	device.Platform = platform
	device.UserType = "t_restaurant"
	device.UserID = restaurant.ID
	device.AdminID=adminId
	device.Client = models.PushDeviceClientMerchant
	device.Lang = lang
	device.CreatedAt = time.Now()
	device.UpdatedAt = time.Now()
	device.DeviceAppVersion=deviceAppVersion
	device.Brand = brand
	db.Create(&device)
	return device, nil
	
}

// DeleteAdminDevice
func (s *PushDeviceService) DeleteAdminDevice(
	rid string,
	client models.PushDeviceClient,
) error {
	db :=tools.GetDB()
	tools.Logger.Info("order_push_注销设备 rid:"+rid)
	
	var devices []models.PushDevice
	db.Model(models.PushDevice{}).Where("rid = ? and client=?",rid,client).Scan(&devices)
	if len(devices) > 0 {
		db.Delete(&devices)
	}

	return nil
}

func (s *PushDeviceService) Push(
	device *models.PushDevice,
	title string, content string,
	params map[string]interface{},
) (bool, error) {
	
	channelType := constants.ChannelTypeMerchantDefault

	sound :=""

	var config configs.JiguangConfig
	switch device.Client {
	case models.PushDeviceClientShipper:
		channelType = constants.ChannelTypeShipperDefault
		config = configs.MyApp.PushConfig.Shipper
	case models.PushDeviceClientMerchant:
		channelType = constants.ChannelTypeMerchantDefault
		config = configs.MyApp.PushConfig.Merchant
	case models.PushDeviceClientUser:
		channelType = constants.ChannelTypeUserDefault
		config = configs.MyApp.PushConfig.User
	default:
		msg := fmt.Sprintf("device %d client is invalid", device.ID)
		return false, errors.New(msg)
	}
	jpushMode :=configs.IsProduction()
	if len(configs.MyApp.JPushMode) > 0 {
		if configs.MyApp.JPushMode =="production" {
			jpushMode =true
		}else{
			jpushMode =false
		}
	}
	jpushClient := tools.NewJiguangPush(config.AppKey, config.AppSecret, jpushMode)
	
	res, err := jpushClient.Push(device.Rid, title, content, params, sound,channelType,device.AdminID)
	if err != nil {
		tools.Logger.Error("order_push_激光推送 error: ", err)
		return false, err

	}
	if error, ok := res["error"]; ok {
		msg := fmt.Sprintf("jpush push error: %s", error)
		return false, errors.New(msg)
	}
	tools.Logger.Debug("push result: ", res)
	return true, nil
}
