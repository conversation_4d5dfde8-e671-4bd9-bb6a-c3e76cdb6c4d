/**
@author: captain
@since: 2022/9/15
@desc: 微信支付下单类
**/

package tools

import (
	"context"
	"fmt"
	"io/ioutil"
	"mulazim-api/configs"
	"mulazim-api/inits"
	"time"

	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/pkg/xlog"
	"github.com/go-pay/gopay/wechat/v3"
)

type WechatPay struct {
	ctx               context.Context
	client            *wechat.ClientV3
	err               error
	AppID             string //应用ID
	MchId             string //商户ID 或者服务商模式的 sp_mchid
	APIv3Key          string //apiV3Key，商户平台获取
	SerialNo          string //商户证书的证书序列号
	PrivateKeyContent string //私钥 apiclient_key.pem 读取后的内容
	NotifyUrl         string //通知地址
}

//
// NewWechatPay
//  @Description: 初始化一个新的微信支付下单对象
//  @author: Captain
//  @Time: 2022-09-18 16:04:01
//  @param c *gin.Context
//  @param wechatPayConfig *configs.WechatPayConfig
//  @return *WechatPay	微信支付下单对象
//
func NewWechatPay(wechatPayConfig *configs.WechatPayConfig) *WechatPay {
	var privateKeyContent string

	content, err := ioutil.ReadFile(inits.ConfigFilePath+ wechatPayConfig.KeyPath)
	if err != nil {
		xlog.Error("Read key file error: ", err.Error())
		privateKeyContent = ""
	}
	privateKeyContent = string(content)
	wechatPay := WechatPay{
		ctx:               context.Background(),
		AppID:             wechatPayConfig.AppID,
		MchId:             wechatPayConfig.MchID,
		APIv3Key:          wechatPayConfig.MchApiV3Key,
		SerialNo:          wechatPayConfig.MchCertSerialID,
		PrivateKeyContent: privateKeyContent,
		NotifyUrl:         wechatPayConfig.NotifyURL,
	}
	return &wechatPay
}
//
// NativePay
//  @Description: native支付下单
//  @author: Captain
//  @Time: 2022-09-18 16:02:25
//  @receiver wechatPay WechatPay
//  @param c *gin.Context
//  @param nativePayParam map[string]interface{}	支付参数
//  @return bool	操作成功返回true，否则返回false
//  @return string	操作成功返回支付微信扫码支付二维码路径，否则返回错误提示
//
func (wechatPay WechatPay) NativePay(nativePayParam map[string]interface{}) (bool, string) {
	wechatPay.client, wechatPay.err = wechat.NewClientV3(wechatPay.MchId, wechatPay.SerialNo, wechatPay.APIv3Key, wechatPay.PrivateKeyContent)
	if wechatPay.err != nil {
		xlog.Error(wechatPay.err)
		return false, wechatPay.err.Error()
	}

	// 启用自动同步返回验签，并定时更新微信平台API证书（开启自动验签时，无需单独设置微信平台API证书和序列号）
	wechatPay.err = wechatPay.client.AutoVerifySign()
	if wechatPay.err != nil {
		xlog.Error(wechatPay.err)
		return false, wechatPay.err.Error()
	}

	// 打开Debug开关，输出日志，默认是关闭的
	wechatPay.client.DebugSwitch = gopay.DebugOff
	//expire := time.Now().Add(10 * time.Minute).Format(time.RFC3339)
	//fmt.Println("expire:", expire)
	// 初始化 BodyMap
	bm := make(gopay.BodyMap)
	bm.Set("description", nativePayParam["description"]).
		Set("appid", wechatPay.AppID).
		Set("out_trade_no", nativePayParam["out_trade_no"]).
		Set("time_expire", nativePayParam["time_expire"]).
		Set("notify_url", wechatPay.NotifyUrl).
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", nativePayParam["total"]).
				Set("currency", "CNY")
		})

	wxRsp, err := wechatPay.client.V3TransactionNative(wechatPay.ctx, bm)
	if err != nil {
		xlog.Error(err)
		return false, err.Error()
	}
	if wxRsp.Code == wechat.Success {
		//xlog.Debugf("wxRsp: %#v", wxRsp.Response)
		return true, wxRsp.Response.CodeUrl
	} else {
		xlog.Errorf("wxRsp:%s", wxRsp.Error)
		return false, wxRsp.Error
	}

}

//
// AppPay
//  @Description: app支付下单
//  @author: Captain
//  @Time: 2022-09-18 15:59:27
//  @receiver wechatPay WechatPay
//  @param c *gin.Context
//  @param param map[string]interface{} 支付参数
//  @return bool 操作成功返回true，否则返回false
//  @return interface{} 操作成功返回支付PrepayId，否则返回错误提示
//
func (wechatPay WechatPay) AppPay(param map[string]interface{}) (bool, map[string]interface{}) {
	wechatPay.client, wechatPay.err = wechat.NewClientV3(wechatPay.MchId, wechatPay.SerialNo, wechatPay.APIv3Key, wechatPay.PrivateKeyContent)
	if wechatPay.err != nil {
		xlog.Error(wechatPay.err)
		return false, map[string]interface{}{"return_msg": wechatPay.err.Error()}
	}
	// 打开Debug开关，输出日志，默认是关闭的
	wechatPay.client.DebugSwitch = gopay.DebugOn
	// 初始化 BodyMap
	bodyMap := make(gopay.BodyMap)
	bodyMap.Set("description", param["description"]).
		Set("appid", wechatPay.AppID).
		Set("out_trade_no", param["out_trade_no"]).
		Set("time_expire", param["time_expire"]).
		Set("notify_url", wechatPay.NotifyUrl).
		SetBodyMap("amount", func(bodyMap gopay.BodyMap) {
			bodyMap.Set("total", param["total"]).
				Set("currency", "CNY")
		})
	wxRsp, err := wechatPay.client.V3TransactionApp(wechatPay.ctx, bodyMap)
	fmt.Println("wechatPay.NotifyUrl",wechatPay.NotifyUrl)
	if err != nil {
		xlog.Error(err)
		return false, map[string]interface{}{"return_msg": err.Error()}
	}
	if wxRsp.Code == wechat.Success {
		payParams, _ := wechatPay.client.PaySignOfApp(wechatPay.AppID, wxRsp.Response.PrepayId)
		wechatPayParams := map[string]interface{}{
			"return_code": "SUCCESS",
			"return_msg":  "OK",
			"result_code": "SUCCESS",
			"mch_id":      payParams.Partnerid,
			"appid":       payParams.Appid,
			"nonce_str":   payParams.Noncestr,
			"sign":        payParams.Sign,
			"prepay_id":   payParams.Prepayid,
			"package":     payParams.Package,
			"time_stamp":  payParams.Timestamp,
			"trade_type":  "APP",
		}
		return true, wechatPayParams
	} else {
		xlog.Errorf("wxRsp:%s", wxRsp.Error)
		return false, map[string]interface{}{"return_msg": wxRsp.Error}
	}
}

//
// Refund
//  @Description: 微信退款
//  @author: Alimjan
//  @Time: 2023-07-22 13:15:54
//  @receiver wechatPay WechatPay
//  @param c *gin.Context
//  @param param map[string]interface{}
//  @return *wechat.RefundRsp
//  @return error
//
func (wechatPay WechatPay) Refund(param map[string]interface{}) ( *wechat.RefundRsp,  error) {
	wechatPay.client, wechatPay.err = wechat.NewClientV3(wechatPay.MchId, wechatPay.SerialNo, wechatPay.APIv3Key, wechatPay.PrivateKeyContent)
	if wechatPay.err != nil {
		return nil, wechatPay.err
	}
	// 打开Debug开关，输出日志，默认是关闭的
	wechatPay.client.DebugSwitch = gopay.DebugOff
	// 初始化 BodyMap
	bodyMap := make(gopay.BodyMap)
	bodyMap.Set("transaction_id", param["transaction_id"]).
			Set("out_refund_no", param["out_trade_no"]).
			SetBodyMap("amount", func(bodyMap gopay.BodyMap) {
				bodyMap.Set("refund", param["total"]).
					Set("total", param["amount"]).
					Set("currency", "CNY")
			})
	wxRsp, err := wechatPay.client.V3Refund(wechatPay.ctx,bodyMap)
	if err != nil {
		Logger.Error(err)
		return nil, err
	}
	return  wxRsp, nil
}

func NewWechatPayInstance(wechatPayConfig *configs.WechatPayConfig) *WechatPay {
	var privateKeyContent string

	content, err := ioutil.ReadFile(inits.ConfigFilePath+ wechatPayConfig.KeyPath)
	if err != nil {
		xlog.Error("Read key file error: ", err.Error())
		privateKeyContent = ""
	}
	privateKeyContent = string(content)
	wechatPay := WechatPay{
		ctx:               context.Background(),
		AppID:             wechatPayConfig.AppID,
		MchId:             wechatPayConfig.MchID,
		APIv3Key:          wechatPayConfig.MchApiV3Key,
		SerialNo:          wechatPayConfig.MchCertSerialID,
		PrivateKeyContent: privateKeyContent,
		NotifyUrl:         wechatPayConfig.NotifyURL,
	}
	return &wechatPay
}

//JSAPI 支付签名
func (wechatPay WechatPay) PaySignMini(prepayId string) (bool,map[string]interface{}) {
	wechatPay.client, wechatPay.err = wechat.NewClientV3(wechatPay.MchId, wechatPay.SerialNo, wechatPay.APIv3Key, wechatPay.PrivateKeyContent)
	if wechatPay.err != nil {
		xlog.Error(wechatPay.err)
		return false,nil
	}
	jsapi,_ :=wechatPay.client.PaySignOfJSAPI(wechatPay.AppID, prepayId)
	result :=make(map[string]interface{})
	result["appId"] = jsapi.AppId
	result["timeStamp"] = jsapi.TimeStamp
	result["nonceStr"] = jsapi.NonceStr
	result["package"] = jsapi.Package
	result["signType"] = jsapi.SignType
    result["paySign"] = jsapi.PaySign

	//跳转的小程序需要的参数格式 
	result["appid"]			=	jsapi.AppId
	result["nonce_str"]		=	jsapi.NonceStr
	result["package"]		=	jsapi.Package
	result["timestamp"]		=	jsapi.TimeStamp
	result["trade_type"]	=	"JSAPI"
	result["pay_sign"]		=	jsapi.PaySign
	result["sign_type"]		=	jsapi.SignType
	result["prepay_id"]		=	prepayId

	return true,result

}

//用户端APP 支付签名  旧版微信
func (wechatPay WechatPay) PaySignMiniApp(prepayId string) (bool,map[string]interface{}) {
	
	result :=make(map[string]interface{})
	result["appid"] = wechatPay.AppID
	result["partnerid"] = wechatPay.MchId
	result["prepayid"] = prepayId
	result["package"] = "Sign=WXPay"
	result["noncestr"] = RandStr(32)
	result["timestamp"] = time.Now().Unix()
	result["sign"] = "almas"

	return true,result

}


//微信 JSAPI 旧版微信
func (wechatPay WechatPay) JSAPIPay(param map[string]interface{}) (bool, map[string]interface{}) {
	wechatPay.client, wechatPay.err = wechat.NewClientV3(wechatPay.MchId, wechatPay.SerialNo, wechatPay.APIv3Key, wechatPay.PrivateKeyContent)
	if wechatPay.err != nil {
		xlog.Error(wechatPay.err)
		return false, map[string]interface{}{"return_msg": wechatPay.err.Error()}
	}
	// 打开Debug开关，输出日志，默认是关闭的
	wechatPay.client.DebugSwitch = gopay.DebugOn
	// 初始化 BodyMap
	bodyMap := make(gopay.BodyMap)
	bodyMap.Set("appid", wechatPay.AppID).
		Set("mchid", wechatPay.MchId).
		Set("description", param["description"]).
		Set("out_trade_no", param["out_trade_no"]).
		Set("time_expire", param["time_expire"]).
		Set("notify_url", wechatPay.NotifyUrl).
		SetBodyMap("amount", func(bodyMap gopay.BodyMap) {
			bodyMap.Set("total", param["total"]).
				Set("currency", "CNY")
		}).
		SetBodyMap("payer", func(bodyMap gopay.BodyMap) {
			bodyMap.Set("openid", param["openid"])
		})
	wxRsp, err := wechatPay.client.V3TransactionJsapi(wechatPay.ctx, bodyMap)
	if err != nil {
		xlog.Error(err)
		return false, map[string]interface{}{"return_msg": err.Error()}
	}
	if wxRsp.Code == wechat.Success {
		payParams, _ := wechatPay.client.PaySignOfJSAPI(wechatPay.AppID, wxRsp.Response.PrepayId)
		wechatPayParams := map[string]interface{}{
			"return_code": "SUCCESS",
			"return_msg":  "OK",
			"result_code": "SUCCESS",
			"appid":      	payParams.AppId,
			"nonceStr":   	payParams.NonceStr,
			"package":     	payParams.Package,
			"timeStamp":  	payParams.TimeStamp,
			"trade_type":  	"JSAPI",
			"paySign":     	payParams.PaySign,
			"signType":	   	payParams.SignType,
			"prepay_id":	wxRsp.Response.PrepayId,	
		}
		return true, wechatPayParams
	} else {
		xlog.Errorf("wxRsp:%s", wxRsp.Error)
		return false, map[string]interface{}{"return_msg": wxRsp.Error}
	}
}
