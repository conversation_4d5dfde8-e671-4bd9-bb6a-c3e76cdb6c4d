package tools



//define('SOCKET_ADMIN_ASSIGNED_ORDER_TO_SHIPPER',1);//管理员分配"
//define('SOCKET_ADMIN_CANCELED_ORDER',2);//管理员取消订单了"
//define('SOCKET_RESTAURANT_CANCELED_ORDER',3);//餐厅取消订单了"
//define('SOCKET_CUSTOMER_CANCELED_ORDER',4);//客户取消订单了"
//define('SOCKET_FOOD_IS_READY_BY_RESTAURANT',5);//餐厅准备好美食了"
//define('SOCKET_FOOD_TAKED_BY_SHIPPER_FROM_RESTAURANT',6);//餐厅准备好美食了"

const (
	SOCKET_ADMIN_ASSIGNED_ORDER_TO_SHIPPER       = 1
	SOCKET_ADMIN_CANCELED_ORDER                  = 2
	SOCKET_RESTAURANT_CANCELED_ORDER             = 3
	SOCKET_CUSTOMER_CANCELED_ORDER               = 4
	SOCKET_FOOD_IS_READY_BY_RESTAURANT           = 5
	SOCKET_FOOD_TAKED_BY_SHIPPER_FROM_RESTAURANT = 6
)

//define('SOCKET_SHIPPER',1);
//define('SOCKET_MERCHANT',2);

const (
	SOCKET_SHIPPER  = 1
	SOCKET_MERCHANT = 2
)

// SendSocket
//
//	@Description: 发送socket消息
//	@author: Alimjan
//	@Time: 2023-06-07 15:43:30
//	@param userType int
//	@param userId int
//	@param typeSocket int
func SendSocket(userType int, userId int, typeSocket int) {
	// url := fmt.Sprintf("%s%d/%d/%d", configs.MyApp.SocketUrl, userType, userId, typeSocket)
	// _, resp := HttpGetJson(url, 2*time.Second, nil)
	// println(resp)
}
