package tools

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"go/types"
	"math"
	"math/rand"
	"mulazim-api/configs"
	"mulazim-api/resources"
	"net/url"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	faceid "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/faceid/v20180301"

	"io/ioutil"
	"net"
	"net/http"

	"crypto/aes"
	"crypto/cipher"
	cryptoRand "crypto/rand"
	"encoding/base64"
	"io"
)

func ToInt64(v interface{}) int64 {
	var rtn int64
	switch v := v.(type) {
	case int:
		rtn = int64(v)

	case int32:
		rtn = int64(v)

	case uint32:
		rtn = int64(v)

	case uint:
		rtn = int64(v)

	case int64:
		rtn = v

	case int8:
		rtn = int64(v)

	case int16:
		rtn = int64(v)

	case float64:
		rtn = int64(v)

	case string:
		tempInt, _ := strconv.Atoi(v)
		rtn = int64(tempInt)

	case types.Nil:
		rtn = 0

	default:
		//fmt.Printf("出错了 %s\n", reflect.TypeOf(v))
		rtn = 0

	}
	return rtn
}

func ToPrice(v interface{}) string {
	if v == nil {
		return ""
	}
	var rtn string
	switch v := v.(type) {
	case float64:
		rtn = fmt.Sprintf("%.2f", v)
		for strings.HasSuffix(rtn, "0") {
			rtn = strings.TrimSuffix(rtn, "0")
		}

	case float32:
		rtn = fmt.Sprintf("%.2f", float64(v))
		for strings.HasSuffix(rtn, "0") {
			rtn = strings.TrimSuffix(rtn, "0")
		}

	case int64:
		rtn = fmt.Sprintf("%d", v)

	case string:
		rtn = v
		for strings.HasSuffix(rtn, "0") {
			rtn = strings.TrimSuffix(rtn, "0")
		}

	default:
		//fmt.Printf("出错了 %s\n", reflect.TypeOf(v))
		rtn = ""

	}
	rtn = strings.TrimSuffix(rtn, ".")

	return rtn
}
func ToFloat64(v interface{},fixedNumbers ...int) float64 {
	var rtn float64
	switch v := v.(type) {
	case float64:
		rtn = v

	case float32:
		rtn = float64(v)

	case int64:
		rtn = float64(v)

	case string:
		if strings.Contains(v, ",") {
			v = strings.Replace(v, ",", "", -1)
		}
		tempInt, _ := strconv.ParseFloat(v, 64)
		rtn = tempInt

	case uint:
		rtn = float64(v)
	case uint32:
		rtn = float64(v)
	case uint64:
		rtn = float64(v)
	case uint8:
		rtn = float64(v)
	case uint16:
		rtn = float64(v)

	case int:
		rtn = float64(v)
	default:
		// Logger.Errorf("出错了 %s\n", reflect.TypeOf(v))
		rtn = 0

	}
	
	if len(fixedNumbers) > 0 && fixedNumbers[0] > 0 {
		fixedLength := fixedNumbers[0]
		formatStr := fmt.Sprintf("%%.%df", fixedLength)
		rtnStr := fmt.Sprintf(formatStr, rtn)

		// Remove trailing zeros after decimal point
		parts := strings.Split(rtnStr, ".")
		if len(parts) == 2 {
			decimalPart := parts[1]
			decimalPart = strings.TrimRight(decimalPart, "0")
			if decimalPart == "" {
				rtnStr = parts[0]
			} else {
				rtnStr = parts[0] + "." + decimalPart
			}
		}

		rtn, _ = strconv.ParseFloat(rtnStr, 64)
	}
	return rtn
}

func ToFloat32(v interface{},fixedNumbers ...int) float32 {
	var rtn float32
	switch v := v.(type) {
	case float64:
		rtn = float32(v)

	case float32:
		rtn = float32(v)

	case int64:
		rtn = float32(v)

	case string:
		tempInt, _ := strconv.ParseFloat(v, 64)
		rtn = float32(tempInt)

	case int:
		rtn = float32(v)
	case uint:
		rtn = float32(v)

	default:
		//fmt.Printf("出错了 %s\n", reflect.TypeOf(v))
		rtn = 0

	}
	if len(fixedNumbers) > 0 && fixedNumbers[0] > 0 {
		fixedLength := fixedNumbers[0]
		formatStr := fmt.Sprintf("%%.%df", fixedLength)
		rtnStr := fmt.Sprintf(formatStr, rtn)

		// Remove trailing zeros after decimal point
		parts := strings.Split(rtnStr, ".")
		if len(parts) == 2 {
			decimalPart := parts[1]
			decimalPart = strings.TrimRight(decimalPart, "0")
			if decimalPart == "" {
				rtnStr = parts[0]
			} else {
				rtnStr = parts[0] + "." + decimalPart
			}
		}

		rtn2, _ := strconv.ParseFloat(rtnStr, 32)
		rtn = float32(rtn2)
	}
	return rtn
}

func ToBool(v interface{}) bool {
	rtn := false
	switch v := v.(type) {

	case int64:
		rtn1 := v
		if rtn1 == 1 {
			rtn = true
		}

	case string:

		if strings.ToLower(v) == "true" {
			rtn = true
		}

	case int:
		rtn1 := float32(v)
		if rtn1 == 1 {
			rtn = true
		}
	case bool:
		rtn = v
	}
	return rtn
}

func ToTime(v interface{}) time.Time {
	var rtn time.Time
	switch v := v.(type) {
	case time.Time:
		rtn = v
	case *time.Time:
		rtn = *v

	case string:

		rtn, _ = time.Parse(time.RFC3339, v)

	default:
		fmt.Printf("出错了 %s\n", reflect.TypeOf(v))
		rtn = time.Now()

	}
	return rtn
}

func ToString(v interface{}) string {
	var rtn string
	switch v := v.(type) {
	case string:
		rtn = v
	case int:
		rtn = strconv.Itoa(v)
	case int32:
		rtn = strconv.Itoa(int(v))

	case int64:
		rtn = strconv.Itoa(int(v))
	case int8:
		rtn = strconv.Itoa(int(v))
	case float64:
		rtn = strconv.FormatFloat(v, 'f', -1, 64)
	case uint:
		rtn = strconv.Itoa(int(v))
	case uint8:
		rtn = strconv.Itoa(int(v))
	case uint32:
		rtn = strconv.Itoa(int(v))
	case uint64:
		rtn = strconv.Itoa(int(v))

	case time.Time:
		rtn = v.Format("2006-01-02 15:04:05")
	case *time.Time:
		if v !=nil {
			rtn = v.Format("2006-01-02 15:04:05")	
		}
		
	default:
		//fmt.Printf("出错了 %s\n", reflect.TypeOf(v))
		rtn = ""

	}
	return rtn
}
func ToInt(v interface{}) int {
	var rtn int
	switch v := v.(type) {
	case string:
		if strings.Contains(v,"."){
			tempInt, _ := strconv.Atoi(strings.Split(v,".")[0])
			rtn = tempInt
		}else{
			rtn, _ = strconv.Atoi(v)
		}
	case int:
		rtn = v
	case float64:
		rtn = int(v)
	case int32:
		rtn = int(v)
	case float32:
		rtn = int(v)
	case int64:
		rtn = int(v)
	case int8:
		rtn = int(v)
	case uint:
		rtn = int(v)
	case uint8:
		rtn = int(v)
	case uint16:
		rtn = int(v)
	case uint32:
		rtn = int(v)
	case uint64:
		rtn = int(v)

	default:
		//fmt.Printf("出错了 %s\n", reflect.TypeOf(v))
		rtn = 0
	}
	return rtn
}

// CalculateDistance
//
//	@Description: 计算两个经纬度之间的距离
//	@author: Captain
//	@Time: 2022-09-11 14:42:35
//	@param lat1 float64
//	@param lng1 float64
//	@param lat2 float64
//	@param lng2 float64
//	@return float64
func CalculateDistance(lat1 float64, lng1 float64, lat2 float64, lng2 float64) float64 {
	earthRADIUS := 6371.0 //地区半径(KM)
	radLat1 := rad(lat1)
	fmt.Printf("radLat1:%f\r\n", radLat1)
	radLat2 := rad(lat2)
	fmt.Printf("radLat2:%f\r\n", radLat2)
	a := rad(lat1) - rad(lat2)
	fmt.Printf("a:%f\r\n", a)
	b := rad(lng1) - rad(lng2)
	fmt.Printf("b:%f\r\n", b)
	s := 2 * math.Asin(math.Sqrt(math.Pow(math.Asin(a/2), 2)+math.Acos(radLat1)*math.Acos(radLat2)*math.Pow(math.Asin(b/2), 2)))
	fmt.Printf("s:%f\r\n", s)
	s = s * earthRADIUS
	s = math.Round(s*10000) / 10
	return s
}

// 转化为弧度(rad)
func rad(d float64) (r float64) {
	r = d * math.Pi / 180.0
	return
}

// CalculateLatitudeLongitudeDistance
//
//	@Description: 计算两个经纬度之间的距离
//	@author: Captain
//	@Time: 2022-09-12 13:57:41
//	@param lon1 float64
//	@param lat1 float64
//	@param lon2 float64
//	@param lat2 float64
//	@return distance float64
func CalculateLatitudeLongitudeDistance(lon1, lat1, lon2, lat2 float64) (distance float64) {

	//fmt.Printf("配送员位置：lat:%f,lng:%f\r\n", lat1, lon1)
	//fmt.Printf("客户下单地址：lat:%f,lng:%f\r\n", lat2, lon2)
	//lat1 = 43.772244
	//lon1 = 87.616066
	//赤道半径(单位m)
	const EARTH_RADIUS = 6378137
	rad_lat1 := rad(lat1)
	rad_lon1 := rad(lon1)
	rad_lat2 := rad(lat2)
	rad_lon2 := rad(lon2)
	if rad_lat1 < 0 {
		rad_lat1 = math.Pi/2 + math.Abs(rad_lat1)
	}
	if rad_lat1 > 0 {
		rad_lat1 = math.Pi/2 - math.Abs(rad_lat1)
	}
	if rad_lon1 < 0 {
		rad_lon1 = math.Pi*2 - math.Abs(rad_lon1)
	}
	if rad_lat2 < 0 {
		rad_lat2 = math.Pi/2 + math.Abs(rad_lat2)
	}
	if rad_lat2 > 0 {
		rad_lat2 = math.Pi/2 - math.Abs(rad_lat2)
	}
	if rad_lon2 < 0 {
		rad_lon2 = math.Pi*2 - math.Abs(rad_lon2)
	}
	x1 := EARTH_RADIUS * math.Cos(rad_lon1) * math.Sin(rad_lat1)
	y1 := EARTH_RADIUS * math.Sin(rad_lon1) * math.Sin(rad_lat1)
	z1 := EARTH_RADIUS * math.Cos(rad_lat1)

	x2 := EARTH_RADIUS * math.Cos(rad_lon2) * math.Sin(rad_lat2)
	y2 := EARTH_RADIUS * math.Sin(rad_lon2) * math.Sin(rad_lat2)
	z2 := EARTH_RADIUS * math.Cos(rad_lat2)
	d := math.Sqrt((x1-x2)*(x1-x2) + (y1-y2)*(y1-y2) + (z1-z2)*(z1-z2))
	theta := math.Acos((EARTH_RADIUS*EARTH_RADIUS + EARTH_RADIUS*EARTH_RADIUS - d*d) / (2 * EARTH_RADIUS * EARTH_RADIUS))
	distance = theta * EARTH_RADIUS
	//fmt.Printf("距离：%f\n", distance)
	return distance
}

func PasswordToHash(password string) string {
	Md5Inst := md5.New()
	Md5Inst.Write([]byte(password))
	Result := Md5Inst.Sum([]byte(""))
	return fmt.Sprintf("%x", Result)
}

// BubbleSort
//
//	@Description: 冒泡排序Map
//	@author: Alimjan
//	@Time: 2022-09-18 17:29:06
//	@param slice []map[string]interface{}
//	@param key string
//	@return []map[string]interface{}
func BubbleSort(slice []map[string]interface{}, key string) []map[string]interface{} {
	for n := 0; n <= len(slice); n++ {
		for i := 1; i < len(slice)-n; i++ {
			if ToInt64(slice[i][key]) > ToInt64(slice[i-1][key]) {
				slice[i], slice[i-1] = slice[i-1], slice[i]
			}
		}
	}
	return slice
}

func If[T int | float64 | string | interface{}](condition bool, trueVal T, falseVal T) T {
	if condition {
		return trueVal
	} else {
		return falseVal
	}
}

// ReadIntArrayParam
//
//	@Description: 读取http请求表单中的数组参数
//	@author: Captain
//	@Time: 2022-11-07 17:34:45
//	@param billIds map[string]string
//	@return []int
func ReadIntArrayParam(c *gin.Context) []int {
	billIds, _ := c.GetPostFormMap("bill")
	var ids []int
	for _, v := range billIds {
		//Logger.Info(k, v)
		intV, _ := strconv.Atoi(v)
		ids = append(ids, intV)
	}
	return ids
} // TimeLineInTimeLine
// @Time 2022-12-24 11:31:43
// <AUTHOR>
// @Description: 判断一个时间是否在另一个时间段内 (17:00:00-23:00:00)=>(10:00:00-00:00:00) return true
// |time3___________time1__________________time2_____________time4|
// @param time1 string 17:00:00 H:i:s 里面开始的时间
// @param time2 string 23:00:00 H:i:s 里面结束的时间
// @param time3 string 10:00:00 H:i:s 外面开始的时间
// @param time4 string 00:00:00 H:i:s 外面结束的时间
func TimeLineInTimeLine(InStartTime, InEndTime, OutStartTime, OutEndTime string) bool {
	//inStartTime := carbon.ParseByFormat(InStartTime, "H:i:s")   // 里面开始的时间
	//inEndTime := carbon.ParseByFormat(InEndTime, "H:i:s")       // 里面结束的时间
	//outStartTime := carbon.ParseByFormat(OutStartTime, "H:i:s") //  外面开始的时间
	//outEndTime := carbon.ParseByFormat(OutEndTime, "H:i:s")     //  外面结束的时间

	inStartTime := carbon.ParseByFormat("2000-01-01 "+InStartTime, "Y-m-d H:i:s")   // 里面开始的时间
	inEndTime := carbon.ParseByFormat("2000-01-01 "+InEndTime, "Y-m-d H:i:s")       // 里面结束的时间
	outStartTime := carbon.ParseByFormat("2000-01-01 "+OutStartTime, "Y-m-d H:i:s") //  外面开始的时间
	outEndTime := carbon.ParseByFormat("2000-01-01 "+OutEndTime, "Y-m-d H:i:s")     //  外面结束的时间
	//如果外面的时间相等 直接返回true 说明在里面
	if outStartTime.Eq(outEndTime) {
		return true
	}

	//外面的时间相等(24小时) 或者 outEndTime小于outStartTime(跨天)  加一天
	// 是否小于等于
	if outEndTime.Lte(outStartTime) {
		outEndTime = outEndTime.AddDay()
	}
	// 是否小于等于
	if inEndTime.Lte(inStartTime) {
		inEndTime = inEndTime.AddDay()
	}

	// 是否在两个时间之间(包括开始时间)
	con1 := inStartTime.BetweenIncludedStart(outStartTime, outEndTime)
	con2 := inStartTime.Lte(inEndTime)
	con3 := inEndTime.BetweenIncludedEnd(outStartTime, outEndTime)

	if con1 && con2 && con3 {
		return true
	} else {
		// 如果是第二天的时间 再加一天 比如(第二天 00:00:00之后)
		inStartTime = inStartTime.AddDay()
		inEndTime = inEndTime.AddDay()
		// 是否在两个时间之间(包括开始时间)

		if inStartTime.BetweenIncludedStart(outStartTime, outEndTime) &&
			inStartTime.Lte(inEndTime) &&
			inEndTime.BetweenIncludedEnd(outStartTime, outEndTime) {
			return true
		}
	}
	return false
}

// TimelineOverlap 检查两个时间段是否交叉
// 时间格式必须为： HH:ss:ii
func TimelineOverlap(startTime1Str, endTIme1Str, startTime2Str, endTime2Str string) bool {
	var (
		start1 = carbon.ParseByFormat("2000-01-01 "+startTime1Str, "Y-m-d H:i:s") // 里面开始的时间
		end1   = carbon.ParseByFormat("2000-01-01 "+endTIme1Str, "Y-m-d H:i:s")   // 里面结束的时间
		start2 = carbon.ParseByFormat("2000-01-01 "+startTime2Str, "Y-m-d H:i:s") //  外面开始的时间
		end2   = carbon.ParseByFormat("2000-01-01 "+endTime2Str, "Y-m-d H:i:s")   //  外面结束的时间
	)
	if start1.Error != nil {
		panic(start1.Error)
	}
	if end1.Error != nil {
		panic(end1.Error)
	}
	if start2.Error != nil {
		panic(start2.Error)
	}
	if end2.Error != nil {
		panic(end2.Error)
	}
	if start1.Gte(end1) {
		end1 = end1.AddDay()
	}
	if start2.Gte(end2) {
		end2 = end2.AddDay()

	}
	return !start1.Gt(end2) && !end1.Lt(start2)
}

// 验证两个时间端是否交叉
//
// @Description:
// @Author: Rixat
// @Time: 2023-03-08 18:27:54
// @receiver
// @param c *gin.Context
func DateTimeLineInDateTimeLine(InStartTime, InEndTime, OutStartTime, OutEndTime string) bool {
	inStartTime := carbon.ParseByFormat(InStartTime, "Y-m-d H:i:s")
	inEndTime := carbon.ParseByFormat(InEndTime, "Y-m-d H:i:s")
	outStartTime := carbon.ParseByFormat(OutStartTime, "Y-m-d H:i:s")
	outEndTime := carbon.ParseByFormat(OutEndTime, "Y-m-d H:i:s")

	if inStartTime.BetweenIncludedStart(outStartTime, outEndTime) || inEndTime.BetweenIncludedEnd(outStartTime, outEndTime) {
		return true
	}
	if inStartTime.Carbon2Time().After(outStartTime.Carbon2Time()) && inEndTime.Carbon2Time().Before(outEndTime.Carbon2Time()) {
		return true
	}
	if inStartTime.Carbon2Time().Before(outStartTime.Carbon2Time()) && inEndTime.Carbon2Time().After(outEndTime.Carbon2Time()) {
		return true
	}
	return false
}

type TimeRange struct {
	Start time.Time
	End   time.Time
}

// 判断两个时间段是否交叉
func isOverlap(a, b TimeRange) bool {
	// 如果 A 的结束时间大于 B 的开始时间，并且 B 的结束时间大于 A 的开始时间，则交叉
	return a.End.After(b.Start) && b.End.After(a.Start)
}

// 判断时间段 A 是否包含时间段 B
func isContain(a, b TimeRange) bool {
	// A 包含 B 的条件是 A 的开始时间 <= B 的开始时间，且 A 的结束时间 >= B 的结束时间
	fmt.Println(a.Start.Equal(b.Start) ,a.End.Equal(b.End), a.Start.Before(b.Start) , a.End.After(b.End))
	return a.Start.Equal(b.Start) || a.End.Equal(b.End) || ( a.Start.Before(b.Start) && a.End.After(b.End) )
}

func ConflictTimeRange(startA, endA, startB, endB time.Time) bool {
	timeRangeA := TimeRange{Start: startA, End: endA}
	timeRangeB := TimeRange{Start: startB, End: endB}
	// 判断是否交叉
	if isOverlap(timeRangeA, timeRangeB) {
		return true
	}
	// 判断是否互相包含
	if isContain(timeRangeA, timeRangeB) {
		return true
	} else if isContain(timeRangeB, timeRangeA) {
		return true
	}
	return false;
}

// DateOverlap 两个日期是否在交叉
// 时间段1 InStartTime, InEndTime
// 时间段2 OutStartTime, OutEndTime
// 格式为： Y-m-d
func DateOverlap(InStartTime, InEndTime, OutStartTime, OutEndTime string) bool {
	inStartTime := carbon.ParseByFormat(InStartTime, "Y-m-d")
	inEndTime := carbon.ParseByFormat(InEndTime, "Y-m-d")
	outStartTime := carbon.ParseByFormat(OutStartTime, "Y-m-d")
	outEndTime := carbon.ParseByFormat(OutEndTime, "Y-m-d")
	if inStartTime.Error != nil {
		panic(inStartTime.Error)
	}
	if inEndTime.Error != nil {
		panic(inEndTime.Error)
	}
	if outStartTime.Error != nil {
		panic(outStartTime.Error)
	}
	if outEndTime.Error != nil {
		panic(outEndTime.Error)
	}
	return !inStartTime.Gt(outEndTime) && !inEndTime.Lt(outStartTime)
}

// email verify
func VerifyEmailFormat(email string) bool {
	//pattern := `\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*` //匹配电子邮箱
	pattern := `^[0-9a-z][_.0-9a-z-]{0,31}@([0-9a-z][0-9a-z-]{0,30}[0-9a-z]\.){1,4}[a-z]{2,4}$`

	reg := regexp.MustCompile(pattern)
	return reg.MatchString(email)
}

// mobile verify
func VerifyMobileFormat(mobileNum string) bool {
	// regular := "^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,3,5-8])|(18[0-9])|166|198|199|(147))\\d{8}$"
	regular := "^((13[0-9])|(14[0-9])|(15[0-9])|(16[0-9])|(17[0-9])|(18[0-9])|(19[0-9]))\\d{8}$"

	reg := regexp.MustCompile(regular)
	return reg.MatchString(mobileNum)
}

// SendDingDingMsg
//
// @Description: 钉钉发送通知
// @Author: Rixat
// @Time: 2023-04-11 18:38:47
// @receiver
// @param c *gin.Context
func SendDingDingMsg(content string) {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Printf("error: %s\n", err)

			}
		}()
		Url := configs.MyApp.SendDingDingMsg
		values := url.Values{
			"content": {content},
		}
		res, err := HttpPostForm(Url, values, 5*time.Second)
		Logger.Info("钉钉发送消息:", res)
		if err != nil {
			//Logger.Info(httpUrl, values)
			fmt.Printf("postForm请求失败 error: %+v", err)
		}

	}()

}

/***
 * @Author: [rozimamat]
 * @description: 校验银行卡
 * @Date: 2023-04-11 17:08:28
 * @param {string} card
 */
func CheckBankCardNumber(card string) bool {
	var sum = 0
	var nDigits = len(card)
	var parity = nDigits % 2
	for i := 0; i < nDigits; i++ {
		var digit = int(card[i] - 48)
		if i%2 == parity {
			digit *= 2
			if digit > 9 {
				digit -= 9
			}
		}
		sum += digit
	}
	return sum%10 == 0
}
func Struct2Map(obj interface{}) map[string]interface{} {
	t := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < t.NumField(); i++ {
		key := t.Field(i).Name // strings.ToLower(t.Field(i).Name)
		data[key] = v.Field(i).Interface()
	}
	return data
}

// 十进制 转 二进制
func Str2DEC(s string) (num int) {
	l := len(s)
	for i := l - 1; i >= 0; i-- {
		num += (int(s[l-i-1]) - 48) << uint8(i)
	}
	return num
}

// 二进制 转 十进制
func DecimalToBinary(num int) string {
	var binary []int

	for num != 0 {
		binary = append(binary, num%2)
		num = num / 2
	}
	binStr := ""
	if len(binary) == 0 {
		return binStr
	} else {
		for i := len(binary) - 1; i >= 0; i-- {
			binStr += fmt.Sprintf("%d", binary[i])
		}

	}
	return binStr
}

func DecimalToBinaryString(num int) string {
	var binary []int

	for num != 0 {
		binary = append(binary, num%2)
		num = num / 2
	}
	binStr := ""
	if len(binary) == 0 {
		return binStr
	} else {
		for i := len(binary) - 1; i >= 0; i-- {
			binStr += fmt.Sprintf("%d", binary[i])
		}

	}
	if len(binStr) < 7 {
		zeros := ""
		for i := 0; i < (7 - len(binStr)); i++ {
			zeros += "0"
		}
		binStr = zeros + binStr
	}
	return binStr
}


// 用map的方式 给struct 赋值
func FillStructWithMap(result interface{}, tagName string, tagMap map[string]interface{}) (interface{}, error) {

	t := reflect.TypeOf(result)
	if t.Name() != "" {
		return result, fmt.Errorf("result have to be a point")
	}
	v := reflect.ValueOf(result).Elem()
	t = v.Type()
	fieldNum := v.NumField()
	for i := 0; i < fieldNum; i++ {
		fieldInfo := t.Field(i)
		tag := fieldInfo.Tag.Get(tagName)
		if tag == "" {
			continue
		}
		if value, ok := tagMap[tag]; ok {
			if reflect.ValueOf(value).Type() == v.FieldByName(fieldInfo.Name).Type() {
				v.FieldByName(fieldInfo.Name).Set(reflect.ValueOf(value))
			}
		}
	}
	return result, nil

}

func MapArrayToString(mapVal []map[string]interface{}) string {
	str := ""
	for i := 0; i < len(mapVal); i++ {
		jsonByte, _ := json.Marshal(mapVal[i])
		jsonStr := string(jsonByte)
		str += jsonStr + ","
	}
	if len(str) > 0 {
		str = str[:len(str)-1]
		str = "[" + str + "]"
	}
	return str
}

/***
 * @Author: [rozimamat]
 * @description: 银行卡,手机号,姓名,身份证号 验证
 * @Date: 2023-04-11 12:27:06
 * @param {string} name
 * @param {string} bankCardNumber
 * @param {string} mobile
 * @param {string} idCard
 */
func BankVerify(name string, bankCardNumber string, mobile string, idCard string) (bool, string) {
	credential := common.NewCredential(
		configs.MyApp.TencentApiKey,
		configs.MyApp.TencentApiSecret,
	)
	// 实例化一个client选项，可选的，没有特殊需求可以跳过
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "faceid.tencentcloudapi.com"
	// 实例化要请求产品的client对象,clientProfile是可选的
	client, _ := faceid.NewClient(credential, "", cpf)
	Logger.Info("银行卡验证四要素:", name, bankCardNumber, mobile, idCard)
	if len(name) == 0 { //姓名为空，需要重新提交账户信息
		return false, "idcard_recommit"
	}
	// 实例化一个请求对象,每个接口都会对应一个request对象
	request := faceid.NewBankCard4EVerificationRequest()

	request.Name = common.StringPtr(name)
	request.BankCard = common.StringPtr(bankCardNumber)
	request.Phone = common.StringPtr(mobile)
	request.IdCard = common.StringPtr(idCard)

	// 返回的resp是一个BankCard4EVerificationResponse的实例，与请求对象对应
	response, err := client.BankCard4EVerification(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		fmt.Printf("银行卡四要素验证接口出现错误: %s", err.Error())
		SendDingDingMsg("银行卡四要素验证接口出现错误")
		return false, "api_error"
	}
	if err != nil {
		panic(err)
	}
	// 输出json格式的字符串回包
	fmt.Printf("%s", response.ToJsonString())
	rr := response.Response.Result
	r := ToInt(*rr)
	if r == 0 {
		return true, *rr
	}
	return false, *rr
}

/***
 * @Author: [rozimamat]
 * @description: 银行卡基础信息
 * @Date: 2023-04-17 13:43:11
 * @param {string} bankCardNumber
 */
func BankInfo(bankCardNumber string) (bool, string) {
	credential := common.NewCredential(
		configs.MyApp.TencentApiKey,
		configs.MyApp.TencentApiSecret,
	)

	// 实例化一个client选项，可选的，没有特殊需求可以跳过
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "faceid.tencentcloudapi.com"
	// 实例化要请求产品的client对象,clientProfile是可选的
	client, _ := faceid.NewClient(credential, "", cpf)

	// 实例化一个请求对象,每个接口都会对应一个request对象
	request := faceid.NewCheckBankCardInformationRequest()

	request.BankCard = common.StringPtr(bankCardNumber)

	// 返回的resp是一个CheckBankCardInformationResponse的实例，与请求对象对应
	response, err := client.CheckBankCardInformation(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		fmt.Printf("银行卡基础信息接口出现错误 An API error has returned: %s", err)
		SendDingDingMsg("银行卡基础信息接口出现错误")
		return false, "api_error"
	}
	if err != nil {
		panic(err)
	}
	// 输出json格式的字符串回包
	fmt.Printf("%s", response.ToJsonString())
	rr := response.Response.Result
	r := ToInt(*rr)
	if r == 0 {
		return true, *rr
	}
	return false, *rr
}

/***
 * @Author: [rozimamat]
 * @description: 银行卡归属地查询
 * @Date: 2023-04-17 18:46:34
 * @param {string} bankCard
 */
func BankRegion(bankCard string) (string, error) {
	return ShuMaiGetData("https://api.shumaidata.com/v4/bank_info/query", "&bankcard="+bankCard)
}

/***
 * @Author: [rozimamat]
 * @description: 数脉数据 获取支行数据
 * @Date: 2023-04-18 17:28:52
 * @param {string} params
 */
func BankRegionBranch(params string) (string, error) {
	return ShuMaiGetData("https://api.shumaidata.com/v2/lianhang/query", params)
}

/***
 * @Author: [rozimamat]
 * @description: 数脉数据 获取数据
 * @Date: 2023-04-18 17:29:17
 * @param {string} url
 * @param {string} params
 */
func ShuMaiGetData(url string, params string) (string, error) {
	// 商户分配的 appid、app_security 和 bankcard
	appid := configs.MyApp.ShumaiAppid
	app_security := configs.MyApp.ShumaiAppSecret
	// 获取当前时间的毫秒数
	timestamp := strconv.FormatInt(time.Now().UnixNano()/int64(time.Millisecond), 10)
	// 拼接字符串并进行 MD5 加密
	str := appid + "&" + timestamp + "&" + app_security
	sign := fmt.Sprintf("%x", md5.Sum([]byte(str)))

	url = url +
		"?appid=" + appid +
		"&timestamp=" + timestamp +
		"&sign=" + sign + params
	// 发送 GET 请求
	client := &http.Client{
		Timeout: time.Second * 15, // 超时时间为10秒
	}
	resp, err := client.Get(url)
	if err != nil {
		Logger.Info("请求失败：", err)
		return "", err
	}
	defer resp.Body.Close()
	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		Logger.Info("读取响应失败：", err)
		return "", err
	}
	// Logger.Info("数脉支行数据:", string(body))
	return string(body), nil
}

func GetShumaiBankData(params string, page string) ([]map[string]interface{}, int) {

	results := make([]map[string]interface{}, 0)

	banks, _ := BankRegionBranch(params + "&page=" + page)

	banksMap := make(map[string]interface{}, 0)
	json.Unmarshal([]byte(banks), &banksMap)
	totalCount := 0

	if banksMap != nil {

		bankData, _ := json.Marshal(banksMap["data"])
		resultMap := make(map[string]interface{}, 0)
		json.Unmarshal(bankData, &resultMap)

		if resultMap != nil {

			resultsStr, _ := json.Marshal(resultMap["result"])

			resultsData := make(map[string]interface{}, 0)
			json.Unmarshal(resultsStr, &resultsData)

			if resultsData != nil {

				totalCount = ToInt(resultsData["totalcount"])

				recordStr, _ := json.Marshal(resultsData["record"])

				recordsData := make([]map[string]interface{}, 0)
				json.Unmarshal(recordStr, &recordsData)

				for _, r := range recordsData {
					bankCode := ToString(r["bankcode"])
					bankResName := ToString(r["lname"])

					results = append(results, map[string]interface{}{
						"bankBranchName": bankResName,
						"code":           bankCode,
					})
				}
			}
		}
	}

	return results, totalCount

}

func GetMap(mapData map[string]interface{}, key string) interface{} {
	if _, ok := mapData[key]; ok {
		return mapData[key]
	} else {
		return nil
	}
}

// in array
func InArray(needle interface{}, haystack interface{}) bool {
	sVal := reflect.ValueOf(haystack)
	kind := sVal.Kind()
	if kind == reflect.Slice || kind == reflect.Array {
		for i := 0; i < sVal.Len(); i++ {
			if sVal.Index(i).Interface() == needle {
				return true
			}
		}

		return false
	}
	return false
}

/***
 * @Author: [rozimamat]
 * @description: 限流
 * @Date: 2023-07-27 13:07:58
 * @param {string} key  关键字
 * @param {int} limit   请求限制
 * @param {int} let_go  放行的数量
 * @param {int} tm      限制时间 秒
 */
func AccessLimit(c *gin.Context, key string, limit int, let_go int, tm int) bool {

	//根据关键字 key 存在的话 不能通过 不存在则 通过

	redisHelper := GetRedisHelper()
	cacheTime := time.Second * time.Duration(tm)
	ret := redisHelper.SetNX(c, key, 1, cacheTime)
	if err := ret.Err(); err != nil {

		return false
	}
	return ret.Val()

}

/***
 * @Author: [rozimamat]
 * @description: redis 上锁
 * @Date: 2023-11-01 13:07:58
 * @param {string} key  关键字
 * @param {int} tm      限制时间 秒
 */
func RedisLock(key string, tm int) bool {
	redisHelper := GetRedisHelper()
	cacheTime := time.Second * time.Duration(tm)
	ret := redisHelper.SetNX(context.Background(), key, 1, cacheTime)
	if err := ret.Err(); err != nil {
		return false
	}
	return ret.Val()

}

/***
 * @Author: [rozimamat]
 * @description: redis 开锁
 * @Date: 2023-11-01 13:07:58
 * @param {string} key  关键字
 */
func RedisUnlock(key string) bool {

	redisHelper := GetRedisHelper()
	ret := redisHelper.Del(context.Background(), key)
	if err := ret.Err(); err != nil {
		return false
	}
	return true

}

func GetRealIp(c *gin.Context) string {
	ip := "127.0.0.1"
	cip := c.ClientIP()
	if cip != "::1" {
		return cip
	}
	return ip
}

// GetClientIP 从HTTP请求中获取客户端IP地址
func GetClientIP(r *http.Request) uint32 {
	// 获取RemoteAddr，可能包含端口号
	ipPort := r.RemoteAddr
	// 如果IP地址带有端口号，去除端口号
	ip := strings.Split(ipPort, ":")[0]
	ipInt := IpToInt(ip)
	return ipInt
}

// ipToInt 将IP地址转换为整数
func IpToInt(ipStr string) uint32 {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return 0
	}
	ip = ip.To4() // 转换为IPv4格式
	if ip == nil {
		return 0
	}
	ipInt := uint32(ip[0])<<24 | uint32(ip[1])<<16 | uint32(ip[2])<<8 | uint32(ip[3])
	return ipInt
}

func RandInt(min int, max int) int {
	if min >= max || min == 0 || max == 0 {
		return max
	}
	return rand.Intn(max-min) + min
}

// FormatFen2TYuanPrice
//
//	@Description: 分转元
//	@author: Alimjan
//	@Time: 2023-08-23 10:36:22
//	@param priceInCents int
//	@return string
func FormatFen2TYuanPrice(priceInCents int) string {
	priceInYuan := float64(priceInCents) / 100.0
	intPart := int64(priceInYuan)
	intPartStr := strconv.FormatInt(intPart, 10)

	decimalPart := int64((priceInYuan - float64(intPart)) * 100)
	decimalPartStr := strconv.FormatInt(decimalPart, 10)

	formattedPrice := ""
	for i := len(intPartStr) - 1; i >= 0; i-- {
		formattedPrice = string(intPartStr[i]) + formattedPrice
		if (len(intPartStr)-i)%3 == 0 && i != 0 {
			formattedPrice = "," + formattedPrice
		}
	}

	formattedPrice += "." + decimalPartStr

	return formattedPrice
}

// CutChineseString
//
//	@Description: 根据给定大小截取字符串
//	@author: Alimjan
//	@Time: 2023-09-09 10:57:34
//	@param str string
//	@param targetLength int
//	@return string
func CutChineseString(str string, targetLength int) string {

	// 初始化计数器
	count := 0
	// 初始化截取位置
	cutIndex := 0

	for i := 0; i < len(str); {
		_, size := utf8.DecodeRuneInString(str[i:])
		// 如果遇到中文字符，则增加3个字节的计数
		if size > 1 {
			count += 3
		} else {
			count++
		}

		// 如果字符数达到目标长度，记录截取位置并退出循环
		if count > targetLength {
			break
		}

		i += size
		cutIndex = i
	}

	// 使用截取位置切割字符串
	result := str[:cutIndex]
	return result
}

// 生成批量更新sql
func GenerateBatchUpdateSql(tableName string, updateData []map[string]interface{}) string {

	q := "UPDATE " + tableName + " SET "
	updateItems := updateData[0]
	updateColumn := make([]string, 0, len(updateItems))
	for k := range updateItems {
		if k != "id" {
			updateColumn = append(updateColumn, k)
		}
	}
	referenceColumn := "id"
	whereIn := ""

	for _, uColumn := range updateColumn {
		q += uColumn + " = CASE "
		for _, v := range updateData {
			q += "WHEN " + referenceColumn + " = " + ToString(v[referenceColumn]) + " THEN '" + ToString(v[uColumn]) + "' "
		}
		q += "ELSE " + uColumn + " END, "
	}

	for _, v := range updateData {
		whereIn += "'" + ToString(v[referenceColumn]) + "', "
	}
	q = strings.TrimRight(q, ", ")
	ids := strings.TrimRight(whereIn, ", ")
	q += " WHERE " + referenceColumn + " IN (" + ids + ")"

	return q

}

func FormatNumber(num float64, thousandsSep string, decimalSep string, precision int) string {
	// 将数字转换为字符串，并指定精度
	numStr := strconv.FormatFloat(num, 'f', precision, 64)

	// 按小数点分割整数部分和小数部分
	parts := splitNum(numStr)

	// 添加千位分隔符
	parts[0] = addThousandsSep(parts[0], thousandsSep)

	// 组合整数部分和小数部分
	formatted := parts[0] + decimalSep + parts[1]

	return formatted
}

func splitNum(numStr string) []string {
	parts := []string{"", ""}

	// 按小数点分割整数部分和小数部分
	for i, char := range numStr {
		if char == '.' {
			parts[0] = numStr[:i]
			parts[1] = numStr[i+1:]
			break
		}
	}

	return parts
}

func addThousandsSep(numStr string, thousandsSep string) string {
	// 反转字符串
	reversed := reverseString(numStr)

	// 每隔三个数字添加一个千位分隔符
	var formatted string
	for i, char := range reversed {
		if i > 0 && i%3 == 0 {
			formatted += thousandsSep
		}
		formatted += string(char)
	}

	// 再次反转字符串
	return reverseString(formatted)
}

func reverseString(str string) string {
	runes := []rune(str)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

func IsEmpty(value interface{}) bool {
	if value == nil {
		return true
	}
	// 使用反射获取变量的类型和值
	valueType := reflect.TypeOf(value)
	valueKind := valueType.Kind()

	// 根据变量类型进行空值判断
	switch valueKind {
	case reflect.Ptr, reflect.Slice, reflect.Map, reflect.Chan:
		return reflect.ValueOf(value).IsNil()
	case reflect.Int:
		return reflect.ValueOf(value).Len() == 0
	case reflect.String, reflect.Array:
		return reflect.ValueOf(value).Len() == 0
	case reflect.Struct:
		zeroValue := reflect.Zero(valueType).Interface()
		return reflect.DeepEqual(value, zeroValue)
	}

	return false
}

// MakeMd5
//
// @Description: 生产MD5
// @Author: Rixat
// @Time: 2023-10-24 08:49:02
// @receiver
// @param c *gin.Context
func MakeMd5(str string) string {
	hash := md5.New()
	// 将字符串写入哈希对象
	hash.Write([]byte(str))
	// 计算哈希值并返回字节数组
	hashBytes := hash.Sum(nil)
	// 将字节数组转换为十六进制字符串
	strMd5 := hex.EncodeToString(hashBytes)
	return strMd5
}

// GetImageURLs
//
// @Description: 图片路径添加CDN域名返回
// @Author: Rixat
// @Time: 2023-11-17 10:55:01
// @receiver
// @param c *gin.Context
func GetImageURLs(imagesStr string) []string {
	if len(imagesStr) == 0 {
		return nil
	}
	imagesArr := strings.Split(imagesStr, ",")
	cdnImageArr := make([]string, 0)
	for _, image := range imagesArr {
		cdnImageArr = append(cdnImageArr, configs.MyApp.CdnUrl+image)
	}
	return cdnImageArr
}

// CdnUrl
//
// @Description: 图片天机CDN地址返回
// @Author: Rixat
// @Time: 2023-12-11 10:23:08
// @receiver
// @param c *gin.Context
func CdnUrl(imagePath string) string {
	if len(imagePath) == 0 {
		return GetDefaultShipperImage()
	}
	return configs.MyApp.CdnUrl + imagePath
}

// 描述：收到消息后根据 收到的用户类型 在群消息加上群消息的数量
// 作者：rozimamat
// 修改时间：2023/11/15 15:38
func GroupMessageCountUpdate(orderId int64, senderType int) int {
	//1.给其他接受者增加群消息的数量
	// redisHelper :=tools.GetRedisHelper()
	//发送信息人类型:1,用户:2,商家,3:配送员,4:管理员
	duration := configs.MyApp.ChatMsgCacheTime
	groupPrefix := "group_" + ToString(orderId)
	//给其他准备收到消息的 各方 加 未读信息数量
	switch senderType {
	case 1: //1,用户
		UpdateGroupChatCount(groupPrefix+"_shipper_count", 1, duration)
		UpdateGroupChatCount(groupPrefix+"_restaurant_count", 1, duration)
		UpdateGroupChatCount(groupPrefix+"_admin_count", 1, duration)
	case 2: //2,商家
		UpdateGroupChatCount(groupPrefix+"_shipper_count", 1, duration)
		UpdateGroupChatCount(groupPrefix+"_user_count", 1, duration)
		UpdateGroupChatCount(groupPrefix+"_admin_count", 1, duration)
	case 3: //3:配送员
		UpdateGroupChatCount(groupPrefix+"_restaurant_count", 1, duration)
		UpdateGroupChatCount(groupPrefix+"_admin_count", 1, duration)
		UpdateGroupChatCount(groupPrefix+"_user_count", 1, duration)
	case 4: //4:管理员
		UpdateGroupChatCount(groupPrefix+"_shipper_count", 1, duration)
		UpdateGroupChatCount(groupPrefix+"_restaurant_count", 1, duration)
		UpdateGroupChatCount(groupPrefix+"_user_count", 1, duration)
		// case 5: //系统
		// 	UpdateGroupChatCount(groupPrefix+"_shipper_count", 1, duration)
		// 	UpdateGroupChatCount(groupPrefix+"_restaurant_count", 1, duration)
		// 	UpdateGroupChatCount(groupPrefix+"_admin_count", 1, duration)
		// 	UpdateGroupChatCount(groupPrefix+"_user_count", 1, duration)
	}

	return 0
}

// 描述：修改 redis key 的值，不修改 过期时间
// 作者：rozimamat
// 修改时间：2023/11/20 15:38
func RedisKeyValueUpdate(key string, value interface{}) {

	redisHelper := GetRedisHelper()
	c := redisHelper.Context()
	if exists, _ := redisHelper.Exists(c, key).Result(); exists > 0 {
		expires := redisHelper.TTL(c, key).Val().Seconds()
		redisHelper.Set(c, key, value, time.Second*time.Duration(expires)).Result()
	}

}

// 描述：在群消息加上群消息的数量
// 作者：rozimamat
// 修改时间：2023/11/15 15:38
func UpdateGroupChatCount(key string, count int, duration int) int64 {

	redisHelper := GetRedisHelper()
	c := redisHelper.Context()
	if exists, _ := redisHelper.Exists(c, key).Result(); exists == 0 {
		redisHelper.SetNX(c, key, 1, time.Duration(duration)*time.Second).Result()
		return 1
	} else {
		//信息过期时间
		expires := redisHelper.TTL(c, key).Val().Seconds()
		luaScript := `
			local oldStock = tonumber(redis.call('get', KEYS[1]))
			if (oldStock==false)  then
				return 0
			end
			local newStock = oldStock+tonumber(KEYS[2])
			redis.call('set', KEYS[1] ,newStock)
			redis.call('EXPIRE', KEYS[1],KEYS[3])	
			return newStock
		`
		leftStock, _ := redisHelper.Eval(redisHelper.Context(), luaScript, []string{key, ToString(count), ToString(expires)}).Int64()
		return leftStock
	}

}

// 描述：增长率
// 作者：rozimamat
// 修改时间：2023/11/15 15:38
func GrowthRate(first interface{}, second interface{}) float64 {
	if ToFloat64(first) == 0 || ToFloat64(second) == 0 {
		return 0
	}
	rate := (ToFloat64(first) - ToFloat64(second)) / (ToFloat64(first) + ToFloat64(second))
	rateStr := fmt.Sprintf("%.3f", rate)
	rate = ToFloat64(rateStr)
	return rate
}

func GetGroupChatMsgCount(key string) int {

	redisHelper := GetRedisHelper()
	c := redisHelper.Context()
	if exists, _ := redisHelper.Exists(c, key).Result(); exists == 0 {
		return 0
	} else {
		rs, _ := redisHelper.Get(c, key).Result()
		return ToInt(rs)
	}

}

// 获取这个月的日期
func GetDaysOfThisMonth() []string {
	today := carbon.Now(configs.AsiaShanghai)
	monthStart := carbon.Parse(carbon.ParseByFormat(today.Format("Y-m"), "Y-m").Format("Y-m-d"))
	monthEnd := monthStart.AddMonth()
	dayLength := int(monthStart.DiffInDays(monthEnd))
	days := []string{}
	for i := dayLength; i > 0; i-- {
		day := monthEnd.AddDays(-i).Format("Y-m-d")
		days = append(days, day)
	}
	return days

}

func GetDaysOfMonth(month string) []string {
	today := carbon.Parse(month, configs.AsiaShanghai)
	if today.IsZero() {
		 today =carbon.ParseByFormat(month, "Y-m",configs.AsiaShanghai)
	}
	ms :=carbon.ParseByFormat(today.Format("Y-m"), "Y-m")
	if ms.Error != nil {
		ms =carbon.ParseByFormat(today.Format("Y-m-d"), "Y-m-d")
	}
	monthStart := carbon.Parse(ms.Format("Y-m-d"))
	monthEnd := monthStart.AddMonth()
	dayLength := int(monthStart.DiffInDays(monthEnd))
	days := []string{}
	for i := dayLength; i > 0; i-- {
		day := monthEnd.AddDays(-i).Format("Y-m-d")
		days = append(days, day)
	}
	return days

}

func GetDaysOfThisMonthBeforeToday() []string {
	today := carbon.Now(configs.AsiaShanghai)
	monthStart := carbon.Parse(carbon.ParseByFormat(today.Format("Y-m"), "Y-m").Format("Y-m-d"))
	monthEnd := today
	dayLength := int(monthStart.DiffInDays(monthEnd))
	days := []string{}
	for i := dayLength; i > 0; i-- {
		day := monthEnd.AddDays(-i).Format("Y-m-d")
		days = append(days, day)
	}
	return days

}

// 更新数据但保留原有的过期时间
func UpdateCountAndKeepExpireTime(key string, count int, duration int) int {

	redisHelper := GetRedisHelper()
	c := redisHelper.Context()
	if exists, _ := redisHelper.Exists(c, key).Result(); exists == 0 {
		redisHelper.SetNX(c, key, count, time.Duration(duration)*time.Second).Result()
		return count
	} else {
		//信息过期时间
		expires := redisHelper.TTL(c, key).Val().Seconds()
		luaScript := `
			local oldStock = tonumber(redis.call('get', KEYS[1]))
			if (oldStock==false)  then
				return 0
			end
			local newStock = oldStock+tonumber(KEYS[2])
			redis.call('set', KEYS[1] ,newStock)
			redis.call('EXPIRE', KEYS[1],KEYS[3])	
			return newStock
		`
		leftStock, _ := redisHelper.Eval(redisHelper.Context(), luaScript, []string{key, ToString(count), ToString(expires)}).Int()
		return leftStock
	}

}

// 配送员默认图片地址
func GetDefaultShipperImage() string {
	defaultAvatar := configs.MyApp.CdnUrl + "images/default/chat_default_shipper.png"
	return defaultAvatar
}

// 用户默认图片地址
func GetDefaultUserImage() string {
	defaultAvatar := configs.MyApp.CdnUrl + "images/default/chat_default_user.png"
	return defaultAvatar
}

// 店铺默认图片
func GetDefaultStoreImage() string {
	defaultAvatar := configs.MyApp.CdnUrl + "images/default/chat_default_store.png"
	return defaultAvatar
}

// 管理员默认图片
func GetDefaultAdminImage() string {
	defaultAvatar := configs.MyApp.CdnUrl + "images/default/chat_default_admin.png"
	return defaultAvatar
}

// 系统默认图片
func GetDefaultSystemImage() string {
	defaultAvatar := configs.MyApp.CdnUrl + "images/default/chat_default_system.png"
	return defaultAvatar
}

func GetNameByLang(obj interface{}, lang string) string {
	objMap := Struct2Map(obj)
	var name interface{}
	if lang == "zh" {
		name = GetMap(objMap, "NameZh")
		if name == nil {
			name = GetMap(objMap, "name_zh")
		}
	} else {
		name = GetMap(objMap, "NameUg")
		if name == nil {
			name = GetMap(objMap, "name_ug")
		}
	}
	if name == nil {
		return ""
	}
	return name.(string)
}

func GetNameByLangAndColumn(obj interface{}, lang string, column string) string {
	objMap := Struct2Map(obj)
	var name interface{}
	if lang == "zh" {
		name = GetMap(objMap, column+"Zh") // NameZh
		if name == nil {
			name = GetMap(objMap, ToCamelCase(column)+"_zh") // name_zh
		}
		if name == nil {
			name = GetMap(objMap, column+"_ug") // name_zh
		}
	} else {
		name = GetMap(objMap, column+"Ug") // NameUg
		if name == nil {
			name = GetMap(objMap, ToCamelCase(column)+"_ug") // name_ug
		}
		if name == nil {
			name = GetMap(objMap, column+"_ug") // name_ug
		}

	}
	if name == nil {
		return ""
	}
	return name.(string)
}

// ToCamelCase
//
// @Description: 将下划线命名转换为驼峰命名
// @Author: Rixat
// @Time: 2023-12-02 09:26:22
// @receiver
// @param c *gin.Context
func ToCamelCase(s string) string {
	underscoreString := strings.ToLower(strings.Replace(s, " ", "_", -1))
	return underscoreString
}

// 替换表情
func FilterEmoji(s string) string {
	if s == "" {
		return s
	}
	re := regexp.MustCompile(`[\p{So}]`)
	result := re.ReplaceAllString(s, "")

	re = regexp.MustCompile(`[\x{1F600}-\x{1F64F}]`)
	result = re.ReplaceAllString(result, "")

	re = regexp.MustCompile(`[\x{1F910}-\x{1F96B}\x{1F980}-\x{1F9E0}]`)
	result = re.ReplaceAllString(result, "")

	re = regexp.MustCompile(`[\x{2600}-\x{27BF}\x{2B50}\x{2B55}\x{1F300}-\x{1F5FF}\x{1F680}-\x{1F6FF}]`)
	result = re.ReplaceAllString(result, "")

	return result

}

// 判断手机号并替换中间的四位成型号
func MaskMobile(mobile string) string {
	if len(mobile) == 11 {
		return mobile[0:3] + "****" + mobile[7:11]
	} else {
		return mobile
	}
}

// 今天的开始和结束
func GetTodayStartAndEnd() (string, string) {
	now := carbon.Now(configs.AsiaShanghai)
	start := now.Format("Y-m-d") + " 00:00:00"
	end := now.Format("Y-m-d") + " 23:59:59"
	return start, end
}

func ToRound(v interface{},length int) float64 {
	floatV :=ToFloat64(v)
	rounded := strconv.FormatFloat(floatV, 'f', length, 64)
	return ToFloat64(rounded)
}

// 根据字符串的位置 前面，中间，后面等位置根据长度用*号替换指定的未知的字符串
func MaskStrByLocation(str string,maskLength int,location int) string {
	if len(str) < maskLength {
		return str
	}
	if location == 1 {
		return str[0:maskLength] + strings.Repeat("*", len(str)-maskLength)
	} else if location == 2 {
		return strings.Repeat("*", maskLength) + str[maskLength:]
	} else if location == 3 {
		return  str[:len(str)-maskLength]+strings.Repeat("*",maskLength) 
	}
	return strings.Repeat("*", len(str))
}

func ReplaceString(str string, oldStr string, newStr string) string{
	str = strings.Replace(str, oldStr, newStr, -1)
	return str
}

// 根据经纬度从高德地图获得实际距离  步行距离
func GetDistanceFromAMap(origin_lat string,origin_lng string,destination_lat string,destination_lng string) (resources.AMapEntity,error){
	origin := origin_lng+","+origin_lat
    destination := destination_lng+","+destination_lat
	url := configs.MyApp.AMapUrl+"/v3/direction/walking?" +
		"&key=" + configs.MyApp.AMapKey +
		"&origin=" + origin +
		"&destination=" + destination
	
	
	headers := map[string]string{
	}

	_, body := HttpGetJson(url, 30*time.Second,headers)
	// LoggerItem.Info(body)	
	var aMapEntity resources.AMapEntity
	json.Unmarshal([]byte(body), &aMapEntity)
	return aMapEntity,nil
}

// 根据经纬度从高德地图获得实际距离 批量获取   步行距离
func GetDistanceFromAMapMassWalking(origins string,destination_lat string,destination_lng string) (resources.AMapEntityMass,error){
	//origins = lng+","+lat+"|"	
    destination := destination_lng+","+destination_lat
	url := configs.MyApp.AMapUrl+"/v3/distance?" +
		"&key=" + configs.MyApp.AMapKey +
		"&origins=" + origins +
		"&destination=" + destination+
		"&output=json&type=3"
	headers := map[string]string{
	}

	_, body := HttpGetJson(url, 30*time.Second,headers)
	// LoggerItem.Info(body)	
	var aMapEntity resources.AMapEntityMass
	json.Unmarshal([]byte(body), &aMapEntity)
	return aMapEntity,nil

}

// 根据经纬度从高德地图获得实际距离 批量获取   骑行距离 自行车
func GetDistanceFromAMapMassRiding(origin string,destination_lat string,destination_lng string) (resources.AMapEntityRiding,error){
	//origins = lng+","+lat+"|"	
    destination := destination_lng+","+destination_lat
	url := configs.MyApp.AMapUrl+"/v4/direction/bicycling?" +
		"&key=" + configs.MyApp.AMapKey +
		"&origin=" + origin +
		"&destination=" + destination
	headers := map[string]string{
	}

	_, body := HttpGetJson(url, 30*time.Second,headers)
	//LoggerItem.Info(body)	
	var aMapEntity resources.AMapEntityRiding
	json.Unmarshal([]byte(body), &aMapEntity)
	return aMapEntity,nil

}

//转换 string to int Array 比如 a:="1,2,3" 输出 []int{1,2,3}
func StringToIntArr(strArr string,separator string) []int{
	parts := strings.Split(strArr, separator)
	result := make([]int, 0, len(parts))
	for _, part := range parts {
		num, err := strconv.Atoi(part)
		if err == nil {
			result = append(result, num)
		}
	}
	return result

}

//转换 array to string 比如 a:=[]int{1,2,3} 输出 "1,2,3"
func IntArrToString(intArr []int,separator string) string{
	strSlice := make([]string, len(intArr))
	for i, num := range intArr {
		strSlice[i] = strconv.Itoa(num)
	}
	result := strings.Join(strSlice, separator)
	return result

}

func Rad2Deg(rad float64) float64 {
	return rad * (180 / math.Pi)
}
//Deg2Rad
func Deg2Rad(deg float64) float64 {
	return deg * (math.Pi / 180)
}

//根据 情况回复 地球的 直径数据
func GetGeoDistanceMeters(key string) float64 {
	data :=map[string]float64{
		"miles" : 3959.0,
        "m" : 3959.0,
        "kilometers" : 6371.0,
        "km" : 6371.0,
        "meters" : 6371000.0,
        "feet" : 20902231.0,
        "nautical_miles" : 3440.06479,
	}
	if _, ok := data[key]; ok {
		return data[key]
	}
	return 0.0
}
//string 数组分割成指定长度的数组
func ChunkStringArray(input []string, chunkSize int) [][]string {
	var divided [][]string

	for i := 0; i < len(input); i += chunkSize {
		end := i + chunkSize

		if end > len(input) {
			end = len(input)
		}

		divided = append(divided, input[i:end])
	}

	return divided
}
//判断当前时间是否在早上，中午和晚上高峰期
func IsRushHourTime() bool{
	if configs.MyApp.EnableRushHourProtection == 0 { //不开启 高峰期保护 则返回false
		return false
	}
	now := carbon.Now(configs.AsiaShanghai)
	hour := now.Hour()
	if (hour >= 12 && hour < 16) || (hour >= 18 && hour < 21) {
		return true
	} else {
		return false
	}
	
}

//redis获取值
func RedisGetValue(key string) string{

	result :=""
	redisHelper := GetRedisHelper()
	c := redisHelper.Context()
	if exists, _ := redisHelper.Exists(c, key).Result(); exists > 0 {
		result,_=redisHelper.Get(c,key).Result()
	}
	return result

}
//set 复制
func RedisSetValue(key string, value interface{},expires int) {

	redisHelper := GetRedisHelper()
	c := redisHelper.Context()
	
	redisHelper.Set(c, key, value, time.Second*time.Duration(expires))
	

}

func RedisDel2(key string)  {
	c := redisHelper.Context()
	if exists, _ := redisHelper.Exists(c, key).Result(); exists == 1 {
		redisHelper.Del(c, key)
	}
}


//将带秒的时间转换成只带分钟的字符串，比如00:00:00 转换成00:00
func TimeToMinuteString(timeStr string) string{
	parsedTime, err := time.Parse("15:04:05", timeStr)
	if err != nil {
		fmt.Println("解析时间出错:", err)
		return ""
	}
	return parsedTime.Format("15:04")

}

//生成通用out_trade_no 
func GenerateCommonOutTradeNo(key string,prefix string) string {

	redisHelper := GetRedisHelper()
	// key := "sms_out_trade_no"
	index, _ := redisHelper.Incr(context.Background(), key).Result()
	if index > 999 {
		index = 1
		redisHelper.Set(context.Background(), key, index, 0)
	}
	indexStr := fmt.Sprintf("%03d", index)
	tmpOutTradeNo := prefix + carbon.Now("Asia/Shanghai").Format("YmdHis", "Asia/Shanghai") + indexStr
	return tmpOutTradeNo

}
// 解析时间字符串为 time.Time 类型
func parseTime(t string) time.Time {
	parsedTime, _ := time.Parse("15:04:05", t)
	return parsedTime
}
// 判断美食准备时间是否在餐厅开餐时间内
func ResTimeFoodTimeValid(openTimeStart, openTimeEnd, foodTimeStart, foodTimeEnd string) bool {
	openStart := parseTime(openTimeStart)
	openEnd := parseTime(openTimeEnd)
	foodStart := parseTime(foodTimeStart)
	foodEnd := parseTime(foodTimeEnd)

	// 特殊处理24小时营业
	if openStart.Equal(openEnd) {
		return true
	}

	// 处理餐厅营业时间跨越午夜的情况
	if openEnd.Before(openStart) {
		openEnd = openEnd.Add(24 * time.Hour)
		if foodStart.Before(openStart) {
			foodStart = foodStart.Add(24 * time.Hour)
		}
		if foodEnd.Before(openStart) {
			foodEnd = foodEnd.Add(24 * time.Hour)
		}
	}

	// 处理美食准备时间跨越午夜的情况
	if foodEnd.Before(foodStart) {
		foodEnd = foodEnd.Add(24 * time.Hour)
	}

	// 判断美食准备时间是否在开餐时间范围内
	return (foodStart.After(openStart) || foodStart.Equal(openStart)) && (foodEnd.Before(openEnd) || foodEnd.Equal(openEnd))

}
//生成餐厅新的订单序列号（现在的最大序列号+1）
func GetOrderMaxSerialNumber(storeId int) int{
	
	redisHelper := GetRedisHelper()
	today := carbon.Now("Asia/Shanghai").Format("Y-m-d")
	key := fmt.Sprintf("%s-%d-%s","order",storeId,today)
	duration :=60*60*24*time.Second
	c :=context.Background()
	exists, _ := redisHelper.Exists(c, key).Result()
	number :=1
	if exists == 0 {
		redisHelper.Set(c, key, 1, duration)
	}else{
		index, _ := redisHelper.Incr(c, key).Result()
		number = int(index)
	}
	return number

}
//生成拉卡拉订单号
func GenerateLakalaOutOrderNo(prefix string) string{

	currentTime := carbon.Now("Asia/Shanghai").Format("YmdHis")
	if configs.CurrentEnvironment != "production" {
		prefix = "DEV_" + prefix
	}
	fr := prefix + currentTime
	redisHelper := GetRedisHelper()
	key := configs.MyApp.LakalaConfig.LakalaOrderPrefix + prefix
	c :=context.Background()
	exists, _ := redisHelper.Exists(c, key).Result()
	inc := ToInt64(RandInt(1, 10)) //1,10 内随机 增加
	index, _ := redisHelper.IncrBy(c, key, inc).Result()
	if index > 9999999 {
		index = 1
		redisHelper.Set(c, key, index, 0)
	}
	if exists == 0 {
		redisHelper.Expire(c, key, 24*time.Hour)
	}
	indexStr := fmt.Sprintf("%07d", index)
	outTradeNo := fr + indexStr
	return outTradeNo


}


// Encrypt function to encrypt a plain text string with a given key
func Encrypt(plainText string, key []byte,iv []byte) (string, error) {
    block, err := aes.NewCipher(key)
    if err != nil {
        return "", err
    }

    plainTextBytes := []byte(plainText)
    cipherTextBytes := make([]byte, aes.BlockSize+len(plainTextBytes))
    iv = cipherTextBytes[:aes.BlockSize]
	fmt.Println("iv:",string(iv))
    if _, err := io.ReadFull(cryptoRand.Reader, iv); err != nil {
        return "", err
    }

    stream := cipher.NewCFBEncrypter(block, iv)
    stream.XORKeyStream(cipherTextBytes[aes.BlockSize:], plainTextBytes)

    return base64.StdEncoding.EncodeToString(cipherTextBytes), nil
}

// Decrypt function to decrypt a cipher text string with a given key
func Decrypt(cipherText string, key []byte,iv []byte) (string, error) {
    cipherTextBytes, err := base64.StdEncoding.DecodeString(cipherText)
    if err != nil {
        return "", err
    }

    block, err := aes.NewCipher(key)
    if err != nil {
        return "", err
    }

    if len(cipherTextBytes) < aes.BlockSize {
        return "", fmt.Errorf("cipher text too short")
    }
    iv = cipherTextBytes[:aes.BlockSize]
    cipherTextBytes = cipherTextBytes[aes.BlockSize:]

    stream := cipher.NewCFBDecrypter(block, iv)
    stream.XORKeyStream(cipherTextBytes, cipherTextBytes)

    return string(cipherTextBytes), nil
}

//根据指定数量 更新redis中的数量 
func UpdateCountRedis(key string, count int, duration int) int64 {

	redisHelper := GetRedisHelper()
	c := redisHelper.Context()
	if exists, _ := redisHelper.Exists(c, key).Result(); exists == 0 {
		redisHelper.SetNX(c, key, 1, time.Duration(duration)*time.Second).Result()
		return 1
	} else {
		//信息过期时间
		expires := redisHelper.TTL(c, key).Val().Seconds()
		luaScript := `
			local oldStock = tonumber(redis.call('get', KEYS[1]))
			if (oldStock==false)  then
				return 0
			end
			local newStock = oldStock+tonumber(KEYS[2])
			redis.call('set', KEYS[1] ,newStock)
			redis.call('EXPIRE', KEYS[1],KEYS[3])	
			return newStock
		`
		leftStock, _ := redisHelper.Eval(redisHelper.Context(), luaScript, []string{key, ToString(count), ToString(expires)}).Int64()
		return leftStock
	}

}

// ExtractBirthday 从18位身份证号中提取出生日期
func ExtractBirthday(idCard string) (string,error) {
	if len(idCard) != 18 {
		return "", fmt.Errorf("身份证号长度不正确")
	}

	// 提取出生日期部分
	birthdayStr := idCard[6:14]

	// 解析出生日期
	birthday, err := time.Parse("20060102", birthdayStr)
	if err != nil {
		return "", fmt.Errorf("日期解析失败: %v", err)
	}

	return birthday.Format("2006-01-02"), nil
}


// 0000000 转换成 一周中的日期
func SetBitAndConvertToDecimal(position int) (int, error) {
	binaryStr := "0000000"
	// 将字符串转换为 rune 切片以便于修改
	runes := []rune(binaryStr)

	// 检查位置是否有效
	if position < 0 || position >= len(runes) {
		return 0, fmt.Errorf("position out of range")
	}

	// 将指定位置的字符设置为 '1'
	runes[position] = '1'

	// 将 rune 切片转换回字符串
	modifiedBinaryStr := string(runes)

	// 将二进制字符串转换为十进制数
	decimalValue, err := strconv.ParseInt(modifiedBinaryStr, 2, 64)
	if err != nil {
		return 0, err
	}

	return int(decimalValue), nil
}

func MergeIntArr(slices ...[]int) []int {
	var result []int
	for _, slice := range slices {
		result = append(result, slice...)
	}
	return result
}

// Unique 去除切片中的重复元素
func UniqueIntArr(slice []int) []int {
	// 使用 map 来跟踪元素的出现情况
	seen := make(map[int]bool)
	var result []int

	for _, v := range slice {
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}

	return result
}


// GenRandomString 生成指定长度的随机字符串
func GenRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}


//获取本月中每周的某个日期的集合
func GetDaysOfMonthAndDay(month string,dayNumber int) ([]string,[]string,[][]string) {
	days :=GetDaysOfMonth(month)
	var result []string
	var weeklyGroups [][]string // 每周的日期分组
	for _, day := range days {
		dn :=carbon.Parse(day,configs.AsiaShanghai).DayOfWeek()
		if dn == dayNumber {
			result = append(result, day)
		}
		// 根据周开始/结束分组
		if len(weeklyGroups) == 0 || dn == dayNumber {
			// 开始新的一周
			weeklyGroups = append(weeklyGroups, []string{})
		}
		weeklyGroups[len(weeklyGroups)-1] = append(weeklyGroups[len(weeklyGroups)-1], day)
	
	}
	// Logger.Info("weekData",weeklyGroups)
	return days,result,weeklyGroups

}



func ParseTimeByFormat(trimValue string,firstFormat string,secondFormat string,returnFormat string) (string,error){
	// 尝试解析 "H:i:s" 格式（例如：14:30:00）
	parsedTime := carbon.ParseByFormat(trimValue, firstFormat, configs.AsiaShanghai)
	if parsedTime.Error != nil {
		// 如果失败，尝试解析 "H:i" 格式（例如：14:30）
		parsedTime = carbon.ParseByFormat(trimValue, secondFormat, configs.AsiaShanghai)
		if parsedTime.Error != nil {
			return trimValue,parsedTime.Error
		}
	}
	return parsedTime.Format(returnFormat),nil
}

func FormatScore(score float64) string {
	if score == 0 {
		return "0"
	}
	if math.Floor(score) == score {
		return strconv.FormatFloat(score, 'f', 0, 64)
	}
	return strconv.FormatFloat(score, 'f', 2, 64)
}