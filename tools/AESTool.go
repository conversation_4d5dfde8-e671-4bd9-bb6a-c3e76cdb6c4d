package tools

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"mulazim-api/configs"
)

// AESTool 结构体包含加密所需的配置
type AESTool struct {
	Key string
}

// AESTool 创建一个新的 AESTool 实例
func NewAESTool() *AESTool {
	return &AESTool{
		Key: configs.MyApp.AesKey,
	}
}

// pad 为数据添加填充，使其长度为16的倍数
func pad(data []byte) []byte {
	blockSize := aes.BlockSize
	padding := blockSize - len(data)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padtext...)
}

// unpad 去除填充
func unpad(data []byte) ([]byte, error) {
	length := len(data)
	unpadding := int(data[length-1])
	if unpadding > length || unpadding > aes.BlockSize {
		return nil, fmt.Errorf("invalid padding")
	}
	return data[:(length - unpadding)], nil
}

// Encrypt 加密方法，接收字符串类型的明文和密钥
func (ac *AESTool) Encrypt(plainTextStr string) (string, error) {
	key := []byte(ac.Key)
	if len(key) != 16 && len(key) != 24 && len(key) != 32 {
		return "", fmt.Errorf("key length must be 16, 24, or 32 bytes")
	}

	plainText := []byte(plainTextStr)

	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	plainText = pad(plainText)
	cipherText := make([]byte, aes.BlockSize+len(plainText))
	iv := cipherText[:aes.BlockSize]

	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(cipherText[aes.BlockSize:], plainText)

	return hex.EncodeToString(cipherText), nil
}

// Decrypt 解密方法，接收字符串类型的密文和密钥
func (ac *AESTool) Decrypt(cipherTextStr string) (string, error) {
	key := []byte(ac.Key)
	if len(key) != 16 && len(key) != 24 && len(key) != 32 {
		return "", fmt.Errorf("key length must be 16, 24, or 32 bytes")
	}

	cipherBytes, _ := hex.DecodeString(cipherTextStr)

	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	if len(cipherBytes) < aes.BlockSize {
		return "", fmt.Errorf("cipherText too short")
	}

	iv := cipherBytes[:aes.BlockSize]
	cipherBytes = cipherBytes[aes.BlockSize:]

	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(cipherBytes, cipherBytes)
	i, err := unpad(cipherBytes)
	return string(i),err
}

func (ac *AESTool) GenerateHash(input string) string {
	hasher := sha256.New()
	hasher.Write([]byte(input))
	return hex.EncodeToString(hasher.Sum(nil))
}