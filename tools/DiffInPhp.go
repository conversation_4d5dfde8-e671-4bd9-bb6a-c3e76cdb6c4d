package tools

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

func GetDebug(c *gin.Context) {
	fmt.Println("这是php--------")
	url := fmt.Sprintf("http://*************:8034%s", c.Request.RequestURI)
	method := "GET"
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	err := writer.Close()
	if err != nil {
		fmt.Println(err)
		return
	}

	client := &http.Client{
		Timeout: 2 * time.Second,
	}
	req, err := http.NewRequest(method, url, payload)

	if err != nil {
		fmt.Println(err)
		return
	}
	req.Header.Add("Authorization", c.<PERSON>ead<PERSON>("Authorization"))
	req.Header.Add("osType", "1")
	req.Header.Add("terminalId", "3")
	req.Header.Add("appVersion", "2.22")
	req.Header.Add("Accept-Language", "en;q=1")
	req.Header.Add("screenSize", "414*736")
	req.Header.Add("phoneBrand", "apple")
	req.Header.Add("User-Agent", "Dinner/1.0 (iPhone; iOS 9.3; Scale/3.00)")
	req.Header.Add("lang", "0")
	req.Header.Add("appBuild", "1")
	req.Header.Add("appTime", "1467103218.903170")
	req.Header.Add("searialNumber", c.GetHeader("searialNumber"))
	req.Header.Add("osVersion", "{{osVersion}}")
	req.Header.Add("parentCategoryId", "1")

	req.Header.Set("Content-Type", writer.FormDataContentType())
	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	body, _ = zhToUnicode(body)
	var out bytes.Buffer
	_ = json.Indent(&out, body, "", "\t")
	out.WriteTo(os.Stdout)
	fmt.Printf("\n")
	//fmt.Println(string(body))
}

func zhToUnicode(raw []byte) ([]byte, error) {
	str, err := strconv.Unquote(strings.Replace(strconv.Quote(string(raw)), `\\u`, `\u`, -1))
	if err != nil {
		return nil, err
	}
	return []byte(str), nil
}
func PrintDebug(data interface{}) {
	fmt.Println("这是golang--------")
	strJson, _ := json.Marshal(data)
	body, _ := zhToUnicode(strJson)
	var out bytes.Buffer
	json.Indent(&out, body, "", "\t")
	out.WriteTo(os.Stdout)
	fmt.Printf("\n")
}
