﻿package tools

import (
	"fmt"
	"os"
	"strings"
)

func GenerateModel(tableName string, filePath string) {
	resMap := make([]map[string]interface{}, 0)
	sql := `
    SELECT
        COLUMN_NAME,
        COLUMN_TYPE,
        COLUMN_COMMENT 
    FROM
        information_schema.COLUMNS 
    WHERE
        table_schema = 'mulazimpro'
        AND TABLE_NAME = ?
    `
	Db.Raw(sql, tableName).Scan(&resMap)
	contentStr := ""
	for _, v := range resMap {
		columnName := ConvertToCamelCase(v["COLUMN_NAME"].(string)) + "\t"
		columnType := ConvertSqlType(v["COLUMN_TYPE"].(string), v["COLUMN_NAME"].(string)) + "\t"
		columnComment := ConvertColumnComment(v["COLUMN_COMMENT"].(string)) + "\t"
		rule := fmt.Sprintf(`%sgorm:"column:%s"%s`, "`", v["COLUMN_NAME"].(string), "`") + "\t"
		// rule := fmt.Sprintf(`%sjson:"%s" gorm:"column:%s;%s`, "`", v["COLUMN_NAME"].(string), v["COLUMN_NAME"].(string), "`") + "\t"
		lineStr := "\t" + columnName + columnType + rule + columnComment + "\n"
		contentStr = contentStr + lineStr
	}
	fileContent := Template(tableName, contentStr)
	WriteText(fileContent, filePath)
	fmt.Println("结束")
}

func Template(tableName string, content string) string {
	fileName := ConvertToCamelCase(tableName)
	text := fmt.Sprintf(`package models
import (
    "time"

	"gorm.io/gorm"
)

type %s struct {
%s
}

func (m *%s) TableName() string {
    return "%s"
}
    `, fileName, content, fileName, tableName)
	return text
}
func WriteText(fileContent string, filePath string) {
	// 打开文件
	file, err := os.Create(filePath)
	if err != nil {
		fmt.Println("创建文件失败:", err)
		return
	}
	defer file.Close()

	// 写入内容
	_, err = file.WriteString(fileContent)
	if err != nil {
		fmt.Println("写入文件失败:", err)
		return
	}

	fmt.Println("生成模型成功，文件名：" + filePath)
}

func ConvertColumnComment(comment string) string {
	return "// " + comment
}

func ConvertSqlType(columnType string, columnName string) string {
	if columnName == "deleted_at" {
		return "gorm.DeletedAt"
	}
	typeName := ""
	if strings.Contains(columnType, "int") {
		typeName = "int"
	} else if strings.Contains(columnType, "tinyint") {
		typeName = "int"
	} else if strings.Contains(columnType, "varchar") {
		typeName = "string"
	} else if strings.Contains(columnType, "text") {
		typeName = "string"
	} else if strings.Contains(columnType, "decimal") {
		typeName = "float64"
	} else if strings.Contains(columnType, "double") {
		typeName = "float64"
	} else if strings.Contains(columnType, "datetime") {
		typeName = "time.Time"
	} else if strings.Contains(columnType, "timestamp") {
		typeName = "time.Time"
	} else {
		fmt.Println("没有匹配:" + columnType)
	}
	return typeName
}
func ConvertToCamelCase(str string) string {
	camelStr := ""
	words := strings.Split(str, "_")
	for _, key := range words {
		camelStr += strings.Title(key)
	}
	return camelStr
}
