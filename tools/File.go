/*
*
@author: captain
@since: 2022/9/5
@desc:
*
*/
package tools

import (
	"archive/zip"
	"bytes"
	"encoding/base64"
	"errors"
	"fmt"
	"image"
	"image/jpeg"
	"io"
	"io/ioutil"
	"mulazim-api/configs"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"sort"
	"strings"
	"unicode/utf8"

	// "strings"
	"github.com/xuri/excelize/v2"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

// PathExists
//
//	@Description: 判断目录是否存在
//	@author: Captain
//	@Time: 2022-09-05 18:58:01
//	@param path string 给定的路径
//	@return bool 如果目录存在返回true，否则返回false
//	@return error
func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

/**
 *@Notes: 本地图片转换base64
 *@User: rixat
 *@DateTime: 2023-01-04 11:41:49
 */
func ImageToBase64(path string, isRaw ...bool) (baseImg string, err error) {

	raw := false //是否只返回 base64 数据
	if len(isRaw) > 0 {
		raw = isRaw[0]
	}

	//获取本地文件
	file, err := os.Open(path)
	if err != nil {
		err = errors.New("获取本地图片失败")
		return
	}
	defer file.Close()
	imgByte, _ := ioutil.ReadAll(file)
	// 判断文件类型，生成一个前缀，拼接base64后可以直接粘贴到浏览器打开，不需要可以不用下面代码
	//取图片类型
	mimeType := http.DetectContentType(imgByte)
	switch mimeType {
	case "image/jpeg":
		baseImg = "data:image/jpeg;base64," + base64.StdEncoding.EncodeToString(imgByte)
	case "image/png":
		baseImg = "data:image/png;base64," + base64.StdEncoding.EncodeToString(imgByte)
	}
	if raw {
		baseImg = base64.StdEncoding.EncodeToString(imgByte)
	}
	return
}

func ReadJson(filePath string) string {

	content, err := os.ReadFile(filePath)
	if err != nil {
		Logger.Error("文件读取失败:", filePath)
		return ""
	}

	return string(content)

}

// 压缩图片
func CompressImageResource(data []byte, quality ...int) []byte {
	q := 40

	if len(quality) > 0 {
		q = quality[0]
	}

	img, _, err := image.Decode(bytes.NewReader(data))
	if err != nil {
		return data
	}
	buf := bytes.Buffer{}
	err = jpeg.Encode(&buf, img, &jpeg.Options{Quality: q})
	if err != nil {
		return data
	}
	if buf.Len() > len(data) {
		return data
	}
	return buf.Bytes()
}

// byte 文件写
func FileCreate(data []byte, dst string, options ...string) error {

	if len(options) > 0 {

		// dots := strings.Split(dst, ".")
		// fileType := dots[len(dots)-1] // 文件类型
		// newFile :=dst+"."+fileType

		err := ioutil.WriteFile(dst, data, 0666)
		if err != nil {
			return err
		}
		//do compress
		quality := options[0]

		FfmpegCompress(dst, quality, dst)

	} else {
		err := ioutil.WriteFile(dst, data, 0666)
		if err != nil {
			return err
		}
	}
	return nil
}

func FileCopy(srcFile string, destFile string) {

	// 打开原始文件
	originalFile, err := os.Open(srcFile)
	if err != nil {
		fmt.Println(err)
	}
	defer originalFile.Close()
	// 创建新的文件作为目标文件
	newFile, err := os.Create(destFile)
	if err != nil {
		fmt.Println(err)
	}
	defer newFile.Close()
	// 从源中复制字节到目标文件
	bytesWritten, err := io.Copy(newFile, originalFile)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Printf("Copied %d bytes.", bytesWritten)
	// 将文件内容flush到硬盘中
	err = newFile.Sync()
	if err != nil {
		fmt.Println(err)
	}

}

func FfmpegCompress(srcStr string, quality string, outStr string) {

	//本地模式 ffmpeg
	mode := configs.MyApp.FfmpegMode //mode 1:本地模式  2:服务器模式
	if mode == 1 {
		cmdStr := "ffmpeg -i " + srcStr + "  -vf scale=iw:ih -quality " + quality + " " + outStr
		fmt.Println(cmdStr)
		cmdArguments := []string{"-i", srcStr, "-vf", "scale=iw:ih", "-quality", quality, outStr}
		cmd := exec.Command("ffmpeg", cmdArguments...)
		var out bytes.Buffer
		cmd.Stdout = &out
		err := cmd.Run()
		if err != nil {
			fmt.Println(err)
			return
		}
		fmt.Printf("command output: %q", out.String())
	}
	if mode == 2 {
		//www/ffmpeg/convert.sh /www/ffmpeg/input.jpg /www/ffmpeg/output.jpg
		//cmdStr :="/www/ffmpeg/convert.sh "+srcStr+" "+outStr
		//fmt.Println(cmdStr)
		cmd := exec.Command("/bin/bash", "/www/ffmpeg/convert.sh", srcStr, outStr)
		fmt.Println("压缩图片-----", srcStr)
		var out bytes.Buffer
		cmd.Stdout = &out
		err := cmd.Run()
		if err != nil {
			fmt.Println(err)
			return
		}
		fmt.Printf("command output: %q", out.String())
	}

}

// 为excel 单元格查询 字母
func ExcelCols(num int) string {

	// 定义字母表
	alphabet := "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	// 定义结果字符串
	var result string
	// 计算字母表长度
	alphabetLen := len(alphabet)

	// 如果 num 大于 56，则需要在前面加 B 并继续生成
	if num > 56 {
		for num > alphabetLen {
			result += "B"
			num -= alphabetLen
		}
	} else if num > 28 {
		for num > alphabetLen {
			result += "A"
			num -= alphabetLen
		}
	}
	// 计算字母索引并拼接到结果字符串中
	result += string(alphabet[num-1])

	return result

}

// excel 生成
func ExcelExport(sheetName string, cols []string, data []map[string]interface{}, exportFileName string) *excelize.File {
	f := excelize.NewFile()
	// 创建一个工作表
	//
	f.SetSheetName("Sheet1", sheetName)
	if len(sheetName) > 0 {
		f.SetSheetName("Sheet1", sheetName)
	}
	excelMap := make(map[string]string)
	for k, v := range data {

		for kk, vv := range cols {
			letter := ExcelCols(kk + 1)
			if k == 0 {
				excelMap[letter+ToString(k+1)] = ToString(vv)
			}
			excelMap[letter+ToString(k+2)] = ToString(v[vv])
		}
	}

	for k, v := range excelMap {
		f.SetCellValue(sheetName, k, v)
	}
	// fileName :=configs.MyApp.ArchivedFilePath+"/"+exportFileName+".xlsx"
	// err := f.SaveAs(fileName)
	// // 根据指定路径保存文件
	// if  err != nil {
	//    Logger.Info("FATAL excel 生成失败",err)
	// }
	return f
}

// excel 导入
func ExcelImport(importFileName string) map[string]interface{} {
	if len(importFileName) == 0 {
		return nil
	}
	f, err := excelize.OpenFile(importFileName)
	if err != nil {
		Logger.Info(" excel 打开失败", err)
		return nil
	}
	sheets := f.GetSheetList()
	allData := make(map[string]interface{})
	for _, v := range sheets {
		// 读取工作表中的数据
		rows, err := f.GetRows(v)
		if err != nil {
			fmt.Println(err)
			continue
		}
		// 将 Excel 数据转换为数组
		var data [][]string
		for _, row := range rows {
			var rowData []string
			rowData = append(rowData, row...)
			data = append(data, rowData)
		}
		allData[v] = data
		// allData = append(allData, data)
	}
	return allData
}


func ExcelToData (importFileName string) []map[string]interface{}{
	var dataItems []map[string]interface{}
	item := ExcelImport(importFileName)
	keys :=MapKeys(item)
	if len(keys) > 0 {
		items :=item[keys[0]].([][]string)
		var cols []string
		
		for index,v := range items {
			if index == 0 {
				cols = v
			}else{
				dataItem :=map[string]interface{}{
				}
				for colIndex, vv := range cols {
					if colIndex < len(v) {
						dataItem[vv]=ToString(v[colIndex])
					}			
				}	
				dataItems = append(dataItems, dataItem)
			}
		}
	}
	return dataItems

}


//获取map的key
func MapKeys(inputMap map[string]interface{}) []string{
	// 获取 map 的所有 key
	keys := make([]string, 0, len(inputMap))
	for k := range inputMap {
		keys = append(keys, k)
	}

	// 对 key 进行升序排序
	sort.Strings(keys)

	return keys
}

func ScanDirectory(dir string, level int) ([]map[string]interface{}, error) {
	// 打开目录
	fileList :=  make([]map[string]interface{}, 0)
	files, err := os.ReadDir(dir)
	if err != nil {
		return fileList, err
	}

	// 遍历目录中的文件
	for _, file := range files {
		// 打印当前文件或文件夹的名称，并按层次缩进
		// 如果是文件夹，递归调用 scanDirectory
		if file.IsDir() {
			newDir := filepath.Join(dir, file.Name())
			list,err := ScanDirectory(newDir, level+1)
			if err != nil {
				return fileList, err
			}
			fileList = append(fileList, map[string]interface{}{
				"name": file.Name(),
				"type":"dir",
				"children": list,
			})

		}else{
			fileList = append(fileList, map[string]interface{}{
				"name": file.Name(),
				"type":"file",
				"children": []map[string]interface{}{},
			})
		}
		

	}
	return fileList,nil

}

// Unzip extracts the contents of a ZIP file to a target directory.
func Unzip(src, dest string) error {
	r, err := zip.OpenReader(src)
	if err != nil {
		return fmt.Errorf("failed to open ZIP file: %v", err)
	}
	defer r.Close()

	for _, f := range r.File {
		err := extractFile(f, dest)
		if err != nil {
			return fmt.Errorf("failed to extract file %q: %v", f.Name, err)
		}
	}

	return nil
}
// extractFile extracts a single file from the ZIP archive to the destination directory.
func extractFile(f *zip.File, dest string) error {
	rc, err := f.Open()
	if err != nil {
		return fmt.Errorf("failed to open file in ZIP: %v", err)
	}
	defer rc.Close()
	fName :=f.Name
	names :=strings.Split(fName,"/")

	decodedNameFront, _ := ConvertGBKToUTF8(names[0])

	decodedNameRear :=""
	if len(names) > 1 { 
		decodedNameRear, _ = ConvertGBKToUTF8(names[1])
	}
	

	decodedName :=decodedNameFront+"/"+decodedNameRear

	path := filepath.Join(dest, decodedName)

	// If it's a directory, create it and return early
	if f.FileInfo().IsDir() {
		os.MkdirAll(path, os.ModePerm)
		return nil
	}

	// Ensure the directory exists
	if err := os.MkdirAll(filepath.Dir(path), os.ModePerm); err != nil {
		return fmt.Errorf("failed to create directory: %v", err)
	}

	// Create the file
	dstFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
	if err != nil {
		return fmt.Errorf("failed to create file: %v", err)
	}
	defer dstFile.Close()

	// Copy the file content
	if _, err := io.Copy(dstFile, rc); err != nil {
		return fmt.Errorf("failed to copy file content: %v", err)
	}

	return nil
}

func ConvertGBKToUTF8(input string) (string, error) {
	if utf8.ValidString(input) {
        return input, nil
    }
	// 将 GBK 编码的文件名转换为 UTF-8
	decoder := simplifiedchinese.GBK.NewDecoder()
	result, _, err := transform.String(decoder, input)
	if err != nil {
		return "", err
	}
	return result, nil
}



func DownloadFile(url string, dest string) error{
	// 发送 HTTP GET 请求
	response, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("下载文件失败: %v", err)
	}
	defer response.Body.Close()

	// 创建本地文件
	file, err := os.Create(dest)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	// 将远程文件内容拷贝到本地文件
	_, err = io.Copy(file, response.Body)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	fmt.Println("文件下载完成:", dest)
	return nil
}