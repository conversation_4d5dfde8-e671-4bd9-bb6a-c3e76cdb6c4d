package tools

import (
	"encoding/json"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/models/chat"
	"time"
)

// 描述：给聊天室WebSocket服务端发送消息
// 作者：Qurbanjan
// 文件：CmsChatController.go
// 修改时间：2023/11/17 10:54
func SendSocketNotify(data chat.ChatMessage) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		Logger.Errorf("FATAL json marshal error: %v", err)
	}
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	_, body := HttpPostJson(jsonData, configs.MyApp.ChatSocketServer, 3*time.Second, headers)
	fmt.Println(body)
}
