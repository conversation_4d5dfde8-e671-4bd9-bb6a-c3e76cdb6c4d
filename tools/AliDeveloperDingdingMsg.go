package tools

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

// AliDeveloperDingdingMsg
//
//	@Description: 给开发者发送钉钉消息
//	@author: <PERSON><PERSON>jan
//	@Time: 2023-08-21 19:50:35
//	@param content string
//	@return bool
//	@return error
func AliDeveloperDingdingMsg(content string) (bool, error) {
	fmt.Println("这是异步队列")

	timestamp := strconv.FormatInt(time.Now().UnixNano()/int64(time.Millisecond), 10)
	secret := "SECa4900bf010b4ec38a732915cb7a362fb1035f314f5accafdb05d841bc0605d8e"

	stringToSign := timestamp + "\n" + secret
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write([]byte(stringToSign))
	signData := mac.Sum(nil)
	sign := url.QueryEscape(base64.StdEncoding.EncodeToString(signData))

	url := fmt.Sprintf("https://oapi.dingtalk.com/robot/send?access_token=ab00a7ff5394915a56908a5d9614a18dc308d2a51d9b0618e324173cdfef2b13&timestamp=%s&sign=%s", timestamp, sign)

	method := "POST"

	payload := strings.NewReader(`{"msgtype": "text","text": {"content":"` + content + `"}}`)

	client := &http.Client{
		Timeout: 15 * time.Second,
	}
	req, err := http.NewRequest(method, url, payload)

	if err != nil {
		fmt.Println(err)
		return false, err
	}
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return false, err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return false, err
	}
	fmt.Println(string(body))

	return true, nil
}
