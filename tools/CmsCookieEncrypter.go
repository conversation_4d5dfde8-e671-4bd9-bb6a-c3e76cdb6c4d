package tools

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
)

type Encrypter struct {
	key    []byte
	cipher string
}

// {"iv":"DqV7ItMYRyw3IN7plVz4yg==","value":"\/\/vhZlA==","mac":""}
type CmsCookie struct {
	Iv    string `json:"iv"`
	Value string `json:"value"`
	Mac   string `json:"mac"`
}

func NewEncrypter(key []byte, cipher string) *Encrypter {
	return &Encrypter{
		key:    key,
		cipher: cipher,
	}
}

func (e *Encrypter) Encrypt(value []byte) ([]byte, error) {
	block, err := e.getCipherBlock()
	if err != nil {
		return nil, err
	}

	iv := make([]byte, aes.BlockSize)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return nil, err
	}

	ciphertext := make([]byte, aes.BlockSize+len(value))
	copy(ciphertext[:aes.BlockSize], iv)
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext[aes.BlockSize:], value)

	return []byte(base64.StdEncoding.EncodeToString(ciphertext)), nil
}

func (e *Encrypter) Decrypt(value string) ([]byte, error) {
	payload, err := base64.StdEncoding.DecodeString(value)

	if err != nil {
		fmt.Println("ciphertext 解析失败")
		return nil, err
	}
	cmsCookie := CmsCookie{}
	json.Unmarshal(payload, &cmsCookie)

	if !e.invalidPayload(cmsCookie) {
		fmt.Println("cookie 结构验证")
		return nil, fmt.Errorf("cookie结构错误")
	}
	ciphertext, err := base64.StdEncoding.DecodeString(cmsCookie.Value)
	if err != nil {
		fmt.Println("解析 cmsCookie value 失败")
		return nil, err
	}

	iv, err := base64.StdEncoding.DecodeString(cmsCookie.Iv)
	if err != nil {
		fmt.Println("iv base64 解析失败")
		return nil, fmt.Errorf("mac 验证失败")
	}

	if _, err := e.verify(cmsCookie.Iv, cmsCookie.Value, cmsCookie.Mac); err != nil {
		fmt.Println("mac 验证失败")
		return nil, fmt.Errorf("mac 验证失败")
	}

	block, err := e.getCipherBlock()
	if err != nil {
		fmt.Println("获取 block 失败")
		return nil, err
	}

	if len(ciphertext) < aes.BlockSize {
		fmt.Println("ciphertext too short")
		return nil, errors.New("encrypted value is not valid")
	}

	plaintext := make([]byte, len(ciphertext))
	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(plaintext, ciphertext)
	if string(plaintext) == "" {
		return nil, errors.New("解析cookie 失败")
	}
	return plaintext, nil
}

func (e *Encrypter) invalidPayload(cookie CmsCookie) bool {
	return !(cookie.Iv == "" || cookie.Mac == "" || cookie.Value == "")
}

func (e *Encrypter) ValidMAC(iv string, value string, messageMAC string) bool {
	mac := hmac.New(sha256.New, e.key)
	mac.Write([]byte(iv + value))
	expectedMAC := mac.Sum(nil)
	return hmac.Equal([]byte(messageMAC), expectedMAC)
}
func (e *Encrypter) verify(iv string, value string, hash string) (bool, error) {
	sig, err := hex.DecodeString(hash)
	if err != nil {
		return false, err
	}

	mac := hmac.New(sha256.New, e.key)
	mac.Write([]byte(iv + value))

	return hmac.Equal(sig, mac.Sum(nil)), nil
}
func GenRandomBytes(size int) (blk []byte, err error) {
	blk = make([]byte, size)
	_, err = rand.Read(blk)
	return
}
func (e *Encrypter) getCipherBlock() (cipher.Block, error) {
	switch e.cipher {
	case "AES-128-CBC":
		return aes.NewCipher(e.key[:16])
	case "AES-256-CBC":
		return aes.NewCipher(e.key[:32])
	}
	return nil, errors.New("unsupported cipher")
}
