

package tools

import (
"log"
"time"
)

// RedisLock defines a lock structure
type MyRedisLock struct {
	key        string
	value      string
	locked     bool
	expiration time.Duration
}

//
// NewMyRedisLock
//  @Description:
//  @param key
//  @param value
//  @param expiration
//  @return *MyRedisLock
//
func NewMyRedisLock(key string, value string, expiration time.Duration) *MyRedisLock {
	return &MyRedisLock{
		key:        key,
		value:      value,
		expiration: expiration,
	}
}
//
// Lock
//  @Description:
//  @receiver lock
//  @return bool
//
func (lock *MyRedisLock) Lock() bool {
	var err error
	lock.locked, err = redisHelper.SetNX(ctx, lock.key, lock.value, lock.expiration).Result()
	if err != nil {
		log.Println("Error acquiring lock:", err)
		return false
	}
	return lock.locked
}
//
// Unlock
//  @Description:
//  @receiver lock
//
func (lock *MyRedisLock) Unlock() {
	if lock.locked{
		redisHelper.Del(ctx, lock.key)
	}
}
