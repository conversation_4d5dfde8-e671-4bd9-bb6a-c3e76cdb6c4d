package tools

/*
#cgo CFLAGS:
#cgo LDFLAGS: -lssl -lcrypto
#include <openssl/dsa.h>
#include <openssl/sha.h>
#include <openssl/err.h>
#include <openssl/pem.h>
#include <stdlib.h>
*/
import "C"
import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"io/ioutil"
	"mulazim-api/configs"
	"mulazim-api/inits"
	"os"
	"unsafe"
)

// RsaEnctypt
//
//	@Description: RSA加密
//	@author: Alimjan
//	@Time: 2023-07-11 15:54:57
//	@param data string
//	@return string
func RsaEnctypt(data string) string {
	// 使用公钥加密
	publicKeyPath := inits.ConfigFilePath + configs.MyApp.LakalaConfig.RsaPublicPubPath //  "./.vscode/cert/cloud_rsa_public_real.pub" //正式环境

	publicKeyPEM, err := os.ReadFile(publicKeyPath)
	if err != nil {
		panic(err)
	}
	block, _ := pem.Decode(publicKeyPEM)
	if block == nil {
		panic("failed to parse PEM block containing the public key")
	}
	//fmt.Println("'========='")
	//fmt.Println(len(block.Headers))
	////x509.CertPool{}.AppendCertsFromPEM()
	// 解析公钥
	pk, err := x509.ParsePKIXPublicKey(block.Bytes)

	//cert, err := x509util.CertificateFromPEM(publicKeyPEM) //.ParseCertificate(publicKeyPEM)
	if err != nil {
		panic(err)
	}

	ciphertext, err := rsa.EncryptPKCS1v15(rand.Reader, pk.(*rsa.PublicKey), []byte(data))
	if err != nil {
		panic(err)
	}
	return base64.StdEncoding.EncodeToString(ciphertext)
}

// DsaSign
//
//	@Description: lakala签名，使用DSA私钥签名，返回base64编码的签名字符串
//	@author: Alimjan
//	@Time: 2023-07-18 17:21:51
//	@param stringToBeSigned string
//	@return string
func DsaSign(stringToBeSigned string) string {
	filePath := inits.ConfigFilePath + configs.MyApp.LakalaConfig.DsaPrivateKeyPemPath
	// 将Go字符串转换为C字符串
	cMessage := C.CString(stringToBeSigned)
	defer C.free(unsafe.Pointer(cMessage))

	// 读取DSA私钥文件
	privateKeyPath := filePath
	privateKeyBytes, err := ioutil.ReadFile(privateKeyPath)
	if err != nil {
		Logger.Error("Failed to read DSA private key file: %v\n", err)
		return ""
	}

	// 将私钥文件内容转换为C字符串
	cPrivateKey := C.CString(string(privateKeyBytes))
	defer C.free(unsafe.Pointer(cPrivateKey))

	// 从C字符串中读取DSA私钥
	bio := C.BIO_new(C.BIO_s_mem())
	defer C.BIO_free(bio)

	C.BIO_puts(bio, cPrivateKey)
	dsa := C.PEM_read_bio_DSAPrivateKey(bio, nil, nil, nil)
	if dsa == nil {
		Logger.Error("Failed to read DSA private key")
		return ""
	}

	// 计算SHA1哈希值
	var hash [C.SHA_DIGEST_LENGTH]byte
	C.SHA1((*C.uchar)(unsafe.Pointer(cMessage)), C.size_t(len(stringToBeSigned)), (*C.uchar)(&hash[0]))

	// 使用DSA私钥进行签名
	signature := make([]byte, C.DSA_size(dsa))
	var sigLen C.uint
	if C.DSA_sign(C.NID_sha1, (*C.uchar)(&hash[0]), C.SHA_DIGEST_LENGTH, (*C.uchar)(&signature[0]), &sigLen, dsa) != 1 {
		Logger.Error("Failed to sign message")
		return ""
	}

	base64Value := base64.StdEncoding.EncodeToString(signature[:int(sigLen)])
	return base64Value
}
