﻿package tools

import (
	"fmt"
	"mulazim-api/inits"
	"strconv"
	"time"
	"unsafe"

	"sync"

	"github.com/casbin/casbin/v2"
	gormadapter "github.com/casbin/gorm-adapter/v3"
)

var (
    once sync.Once
    enforcer *casbin.Enforcer
)
func GetCasbinEnforcer()  *casbin.Enforcer {
    once.Do(func() {
        db := GetDB()
        adapter, err := gormadapter.NewAdapterByDBWithCustomTable(db,nil,"merchant_permission")
        if err != nil {
            panic(err)
        }
        configPath := inits.ConfigFilePath + "."
		// 带缓存的casbin 初始化
		e, err := casbin.NewEnforcer(configPath + "/rbac_model.conf", adapter)

        enforcer = e
    })
	ptr := uintptr(unsafe.Pointer(&enforcer))
	// 将指针地址转换为字符串
	ptrStr := strconv.FormatUint(uint64(ptr), 16)
	setResult, err := SetNX( fmt.Sprintf("merchant_redis_per_cache_%s",ptrStr), 1, 20*time.Second).Result()
	if err!=nil {
		Logger.Info("FATAL redis 权限缓存限流写入失败",err)
	}
	if setResult {
		if err :=  enforcer.LoadPolicy(); err != nil {
			Logger.Info("FATAL 获取权限失败",err)
		}
	}

    return enforcer
}

func GetMerchantPermissions(adminId int) []string{
	enforcer := GetCasbinEnforcer()
	permissions,  _:= enforcer.GetFilteredGroupingPolicy(0, ToString(adminId))
	perNames := make([]string,0)
	for _,v := range permissions {
		perNames = append(perNames,v[1])
	}
	return perNames
}