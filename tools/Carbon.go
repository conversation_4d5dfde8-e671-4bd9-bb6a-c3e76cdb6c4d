package tools

import (
	"mulazim-api/configs"
	"time"

	"github.com/golang-module/carbon/v2"
)

func Today(zone string) carbon.Carbon {
	now := carbon.Now(zone)
	rtn := carbon.CreateFromDateTime(now.Year(), now.Month(), now.Day(), 0, 0, 0, zone)
	return rtn
}
func Yesterday(zone string) carbon.Carbon {
	now := carbon.Yesterday(zone)
	rtn := carbon.CreateFromDateTime(now.Year(), now.Month(), now.Day(), 0, 0, 0, zone)
	return rtn
}
func FirstOfMonth(zone string) carbon.Carbon {
	now := carbon.Now(zone)
	rtn := carbon.CreateFromDateTime(now.Year(), now.Month(), 1, 0, 0, 0, zone)
	return rtn
}

// GetFirstDateOfMonth
//
//	@Description: 获取传入的时间所在月份的第一天，即某月第一天的0点。如传入time.Now()
//	@param d
//	@return 返回当前月份的第一天0点时间(time.Time)
func GetFirstDateOfMonth(d time.Time) time.Time {
	d = d.AddDate(0, 0, -d.Day()+1)
	return GetZeroTime(d)
}

// GetZeroTime
//
//	@Description: 获取某一天的0点时间
//	@param d
//	@return time.Time
func GetZeroTime(d time.Time) time.Time {
	return time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, d.Location())
}

// TimeFormatYmdHis
//
// @Description: Time类型时间格式化输出(Y-m-d H:i:s)格式字符串
// @Author: Rixat
// @Time: 2023-10-26 09:32:26
// @receiver
// @param c *gin.Context
func TimeFormatYmdHis(time *time.Time) string {
	if time == nil || time.IsZero() {
		return ""
	}
	return time.Format("2006-01-02 15:04:05")
}

func TimeFormatHis(time *time.Time) string {
	if time == nil || time.IsZero() {
		return ""
	}
	return time.Format("15:04:05")
}

// TimeFormatYmdHis
//
// @Description: Time类型时间格式化输出(Y-m-d H:i:s)格式字符串
// @Author: Rixat
// @Time: 2023-10-26 09:32:26
// @receiver
// @param c *gin.Context
func TimeFormatYmd(time *time.Time) string {
	if time == nil || time.IsZero() {
		return ""
	}
	return time.Format("2006-01-02")
}

// 获取本月的开始和结束
func GetMonthStartAndEnd() (string, string) {
	month := carbon.Now(configs.AsiaShanghai).Format("Y-m")
	monthStart := carbon.Parse(carbon.ParseByFormat(month, "Y-m").Format("Y-m-d")).Format("Y-m-d")
	monthEnd := carbon.Parse(carbon.ParseByFormat(month, "Y-m").AddMonth().AddSeconds(-1).Format("Y-m-d")).Format("Y-m-d")
	return monthStart, monthEnd

}

// 获取本月的开始和结束 带 自定义格式
func GetMonthStartAndEndWithParam(month string, format string) (string, string) {
	nowTime :=carbon.ParseByFormat(month, format)
	nextMonth :=nowTime.AddMonth().AddSeconds(-1)
	monthStart := nowTime.Format("Y-m-d")
	monthEnd := nextMonth.Format("Y-m-d")
	return monthStart, monthEnd

}

func GetMonthLength(month string) int64{

	monthStart := carbon.Parse(carbon.ParseByFormat(month, "Y-m").Format("Y-m-d"))
	monthEnd := carbon.Parse(carbon.ParseByFormat(month, "Y-m").AddMonth().Format("Y-m-d"))

	monthLength := monthEnd.DiffAbsInDays(monthStart)
	return monthLength
	

}

func IsZeroTime(tt *time.Time) bool{

	var t *time.Time = nil
	if tt == t {
		return true
	}
	if tt.IsZero() {
		return true
	}
	return false

}