package tools

import (
	"mulazim-api/configs"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v3/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/golang-module/carbon/v2"
	// "os"
)

/**
 * 使用AK&SK初始化账号Client
 * @param accessKeyId
 * @param accessKeySecret
 * @return Client
 * @throws Exception
 */
 func AliSmsClient(accessKeyId *string, accessKeySecret *string) (_result *dysmsapi20170525.Client, _err error) {
	config := &openapi.Config{
		// 必填，您的 AccessKey ID
		AccessKeyId: accessKeyId,
		// 必填，您的 AccessKey Secret
		AccessKeySecret: accessKeySecret,
	}
	// 访问的域名
	config.Endpoint = tea.String("dysmsapi.aliyuncs.com")
	_result = &dysmsapi20170525.Client{}
	_result, _err = dysmsapi20170525.NewClient(config)
	return _result, _err
}

/**
* 阿里云 短信发送
*mobile 手机号
*content 内容   "{\"code\":\"1234\"}"		
*/
func AliSmsSend(mobile string,content string,signAndTempCode ...string) (_err error) {
	// 工程代码泄露可能会导致AccessKey泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378661.html
	accessId := configs.MyApp.AliyunSmsAccessId 
	accessKeySecret :=  configs.MyApp.AliyunSmsAccessSecret 

	// SignName 		:="Mulazim" //签名名称
	// TemplateCode 	:="SMS_101255080"

	sign :="Mulazim"  //默认签名名称
	tempCode :="SMS_268530025"	//默认模板名称

	if len(signAndTempCode) >0 {
		sign=signAndTempCode[0]
		tempCode=signAndTempCode[1]
	}

	//内容 "{\"code\":\"1234\"}"

	client, _err := AliSmsClient(&accessId, &accessKeySecret)
	if _err != nil {
		return _err
	}
	sendSmsRequest := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers:  tea.String(mobile),
		SignName:      tea.String(sign),
		TemplateCode:  tea.String(tempCode),
		TemplateParam: tea.String(content),
	}
	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		_, _err = client.SendSmsWithOptions(sendSmsRequest, runtime)
		if _err != nil {
			return _err
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 如有需要，请打印 error
		_, _err = util.AssertAsString(error.Message)
		if _err != nil {
			return _err
		}
	}
	return _err
}


func AliSmsSendWithResponse(mobile string,content string,signAndTempCode ...string) (resp *dysmsapi20170525.QuerySendDetailsResponse,_err error) {
	// 工程代码泄露可能会导致AccessKey泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378661.html
	accessId := configs.MyApp.AliyunSmsAccessId 
	accessKeySecret :=  configs.MyApp.AliyunSmsAccessSecret 

	// SignName 		:="Mulazim" //签名名称
	// TemplateCode 	:="SMS_101255080"

	sign :="Mulazim"  //默认签名名称
	tempCode :="SMS_268530025"	//默认模板名称

	if len(signAndTempCode) >0 {
		sign=signAndTempCode[0]
		tempCode=signAndTempCode[1]
	}
	var sendResponse *dysmsapi20170525.SendSmsResponse
	var sendDetailResponse *dysmsapi20170525.QuerySendDetailsResponse

	//内容 "{\"code\":\"1234\"}"

	client, _err := AliSmsClient(&accessId, &accessKeySecret)
	if _err != nil {
		return sendDetailResponse,_err
	}
	sendSmsRequest := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers:  tea.String(mobile),
		SignName:      tea.String(sign),
		TemplateCode:  tea.String(tempCode),
		TemplateParam: tea.String(content),
	}
	runtime := &util.RuntimeOptions{}
	

	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		rr, _err := client.SendSmsWithOptions(sendSmsRequest, runtime)
		if _err != nil {
			return _err
		}
		sendResponse = rr
		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 如有需要，请打印 error
		_, _err = util.AssertAsString(error.Message)
		if _err != nil {
			return sendDetailResponse,_err
		}
	}
	sendDate :=carbon.Now(configs.AsiaShanghai).Format("Y-m-d")
	pageSize :=int64(100)
	currentPage :=int64(1)
	sendSmsDetail := &dysmsapi20170525.QuerySendDetailsRequest{
		BizId: sendResponse.Body.BizId,
		PhoneNumber:tea.String(mobile),
		SendDate: &sendDate,
		PageSize: &pageSize,
		CurrentPage:&currentPage,
	}
	
	detailResponse,err:=client.QuerySendDetails(sendSmsDetail)
	if err != nil {
		return sendDetailResponse,err
	}
	Logger.Info(detailResponse)
	return detailResponse,err
}
