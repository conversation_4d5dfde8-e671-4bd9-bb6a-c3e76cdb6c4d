package tools

import (
	"go.uber.org/zap/zapcore"
	"mulazim-api/inits"
	"os"
	"time"

	"go.uber.org/zap"
	"gopkg.in/natefinch/lumberjack.v2"
)

// 首字母大写，公开这两个logger实例
var LoggerItem *zap.Logger
var Logger *zap.SugaredLogger

func init() {
	LoggerItem, _ = zap.NewDevelopment()
	InitLogger()
	defer LoggerItem.Sync()
}

func Log(content string) {
	Logger.Info(content)
}

func InitLogger() {
	encoder := GetEncoder()

	core := zapcore.NewCore(encoder, zapcore.NewMultiWriteSyncer(getWriterSyncer(), zapcore.AddSync(os.Stdout)), zap.DebugLevel)
	LoggerItem = zap.New(core, zap.AddCaller()) //zap.AddCaller() 显示文件名 和 行号

	Logger = LoggerItem.Sugar()
}

// Encoder
func GetEncoder() zapcore.Encoder {
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
		enc.AppendString(t.Format("2006-01-02 15:04:05"))
	}
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	return zapcore.NewJSONEncoder(encoderConfig)
}

// core 三个参数之  日志输出路径
func getWriterSyncer() zapcore.WriteSyncer {
	logFile := inits.ConfigFilePath+ "./log/mulazim-api.log"

	lumberJackLogger := &lumberjack.Logger{
		Filename:   logFile, // 文件位置
		MaxSize:    10,      // 进行切割之前,日志文件的最大大小(MB为单位)
		MaxAge:     60,      // 保留旧文件的最大天数
		MaxBackups: 60,      // 保留旧文件的最大个数
		Compress:   false,   // 是否压缩/归档旧文件
	}
	return zapcore.AddSync(lumberJackLogger)
	//或者将上面的NewMultiWriteSyncer放到这里来，进行返回
	// return zapcore.AddSync(file)
}