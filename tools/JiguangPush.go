package tools

import (
	"encoding/json"
	"mulazim-api/configs"
	"mulazim-api/constants"
	"mulazim-api/libs/go-jpush"
	
	"runtime"
	"strings"
)

type JiguangPush struct {
	client     *jpush.Client
	production bool
}

func NewJiguangPush(appKey, masterSecret string, production bool) *JiguangPush {
	return &JiguangPush{
		client:     jpush.NewClient(appKey, masterSecret),
		production: production,
	}
}

func (p *JiguangPush) Push(
	rid string,
	title string,
	content string,
	params map[string]interface{},
	sound string,
	channelType int, // 1: new order, 2: new chat message
	adminId int,
	options ...string,
) (map[string]interface{}, error) {
	Logger.Info("order_push_激光推送开始了 channelType:",channelType,",adminId:",adminId,",sound:",sound,",rid:",rid)
	var req *jpush.PushRequest
	switch (channelType){
		case constants.ChannelTypeMerchantDefault,constants.ChannelTypeMerchantOrderReturn://商家端 默认和 被管理员退单		
			req = p.getChannelTypeMerchantDefault(rid, title, content, params, sound,adminId)
		case constants.ChannelTypeMerchantOrder://商家端 新订单
			req = p.getChannelTypeMerchantNewOrder(rid, title, content, params, sound,adminId)
		case constants.ChannelTypeShipperDefault://配送端 默认
			req = p.getChannelTypeShipperDefault(rid, title, content, params, sound,adminId)
		case constants.ChannelTypeShipperAssignOrder://配送端 管理员分配订单
			req = p.getChannelTypeShipperAssignOrder(rid, title, content, params, sound,adminId)
		case constants.ChannelTypeShipperFoodReady://配送端 美食准备好了
			req = p.getChannelTypeShipperFoodReady(rid, title, content, params, sound,adminId)
		case constants.ChannelTypeShipperOrderCancelCms:
			req = p.getChannelTypeShipperOrderCancel(rid, title, content, params, sound,adminId,"cms")
		case constants.ChannelTypeShipperOrderCancelStore:
			req = p.getChannelTypeShipperOrderCancel(rid, title, content, params, sound,adminId,"api")
		case constants.ChannelTypeShipperOrderCancelCustomer:
			req = p.getChannelTypeShipperOrderCancel(rid, title, content, params, sound,adminId,"customer")						
		case constants.ChannelTypeShipperChat://配送端 聊天室
			req = p.getChannelTypeShipperChat(rid, title, content, params, sound,adminId)		
		case constants.ChannelTypeShipperNewOrder://配送端 新订单
			req = p.getChannelTypeShipperNewOrder(rid, title, content, params, sound,adminId)	
		case constants.ChannelTypeShipperAutoDispatchAssignOrder: //智能派单极光推送
			req = p.getChannelTypeShipperAutoDispatchAssignOrder(rid, title, content, params, sound,adminId)
		case constants.ChannelTypeUserDefault://用户端	聊天室		
			req = p.getChannelTypeUserChat(rid, title, content, params, sound,adminId,options ...)

	}
	str,_ := json.Marshal(req)
	Logger.Info(string(str))
	if configs.CurrentEnvironment != "production" {
		_, file, line, ok := runtime.Caller(1)
		if !ok {
			Logger.Info("Failed to get caller info")
		}else{
			Logger.Infof("Function called from file %s, line %d\n", file, line)
		}
		_, file, line, ok = runtime.Caller(2)
		if !ok {
			Logger.Info("Failed to get caller info")
		}else{
			Logger.Infof("Function called from file %s, line %d\n", file, line)
		}
	}
	return p.client.Push(req)
}

//
// getChannelTypeMerchantNewOrder
//  @Description: 这是新订单配置，客户端会发出新订单提醒
//  @receiver p
//  @return unc
//
func (p *JiguangPush) getChannelTypeMerchantDefault(
	rid string,
	title string,
	content string,
	params map[string]interface{},
	sound string,
	adminId int,
	) *jpush.PushRequest{
	
	//SkipQuotaFalse := false
	//SkipQuotaTrue := true
	ps :=jpush.PushNotification{
		Alert: title,
		IOS: &jpush.NotificationIOS{
			Alert: &map[string]interface{}{
				"title": title,
				"body":  content,
			},
			Sound:  sound + ".caf",
			//Sound: "order_zh_tone",
			Extras: params,
		},
		Android: &jpush.NotificationAndroid{
			Alert:     content,
			Title:     "订单消息:" + title,
			BuilderId: 0,
			Category:  "",
			Priority:  0,

			AlertType: 7,
			Intent: map[string]interface{}{
				// "url": "intent:#Intent;action=android.intent.action.MAIN;end",
				"url": "",
			},

			// Sound:  "default",
			//ChannelID: "OrderGroupChat",
			Extras: params,
		},
	}
	OppoChannelId := "OrderGroupChat"
	if sound != "" && sound != "_ug" {
		if sound=="admin_canceled_order_ug" {
			sound = "admin_canceled_order"
		}
		if strings.Contains(sound, "admin_canceled_order") {
			ps.Android.ChannelID = "OrderGroupCancel"
			OppoChannelId = "OrderGroupCancel"
		}else{
			ps.Android.ChannelID = "OrderGroupChat"
		}
		ps.Android.Sound = sound
	}else{
		ps.Android.Sound = "dingdang"
		ps.Android.ChannelID = "OrderGroupChat"
	}
	req := &jpush.PushRequest{
		Platform: []jpush.Platform{
			jpush.PlatformAndroid,
			jpush.PlatformIOS,
		},
		Audience: &jpush.PushAudience{
			RegistrationId: []string{rid},
		},
		Notification: &ps,
		Message: &jpush.PushMessage{
			MsgContent:  content,
			Title:       title,
			ContentType: "text",
			Extras:      params,
		},
		Options: &jpush.PushOptions{
			TimeToLive:     60,
			ApnsProduction: p.production,
			Classification: 1,
			ThirdPartyChannel: map[string]interface{}{
				"xiaomi": &jpush.XiaomiOptions{
					ChannelId:       "118707",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
				},
				"oppo": &jpush.OppoOptions{
					ChannelId:      OppoChannelId,
					//Distribution:    "secondary_push",
					//DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
				"vivo": &jpush.VivoOptions{
					CallbackId:      "",
					Category:        "IM",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
					SkipQuota:       false,
				},
				"huawei": &jpush.HuaweiOptions{
					Category:        "EXPRESS",
					Importance: "NORMAL",
					DefaultSound: true,
					ChannelId:       "OrderGroupChat",
					Distribution:    "jpush",
					DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
			},
		},
	}
	return req
}
//
// getChannelTypeMerchantNewOrder
//  @Description: 这是新订单配置，客户端会发出新订单提醒
//  @receiver p
//  @return unc
//
func (p *JiguangPush) getChannelTypeMerchantNewOrder(
	rid string,
	title string,
	content string,
	params map[string]interface{},
	sound string,
	adminId int,	
	) *jpush.PushRequest{
	if sound == "" {
		sound = "default"
	}
	//SkipQuotaFalse := false
	//SkipQuotaTrue := true

	req := &jpush.PushRequest{
		Platform: []jpush.Platform{
			jpush.PlatformAndroid,
			jpush.PlatformIOS,
		},
		Audience: &jpush.PushAudience{
			RegistrationId: []string{rid},
		},
		Notification: &jpush.PushNotification{
			Alert: title,
			IOS: &jpush.NotificationIOS{
				Alert: &map[string]interface{}{
					"title": title,
					"body":  content,
				},
				Sound:  sound + ".caf",
				//Sound: "order_zh_tone",
				Extras: params,
			},
			Android: &jpush.NotificationAndroid{
				Alert:     content,
				Title:     "订单消息:" + title,
				BuilderId: 0,
				//Category:  "sys",
				Priority:  0,
				AlertType: 7,
				Intent: map[string]interface{}{
					"url": "intent:#Intent;action=android.intent.action.MAIN;end",
				},

				Sound:  "order_tone",
				ChannelID: "OrderGroupChannel",
				Extras: params,
			},
		},
		Message: &jpush.PushMessage{
			MsgContent:  content,
			Title:       title,
			ContentType: "text",
			Extras:      params,
		},
		Options: &jpush.PushOptions{
			TimeToLive:     60,
			ApnsProduction: p.production,
			Classification: 1,
			ThirdPartyChannel: map[string]interface{}{
				"xiaomi": &jpush.XiaomiOptions{
					ChannelId:       "113558",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
				},
				"oppo": &jpush.OppoOptions{
					ChannelId:       "OrderGroupChannel",
					//Distribution:    "secondary_push",
					//DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
				"vivo": &jpush.VivoOptions{
					CallbackId:      "",
					Category:        "IM",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
					SkipQuota:       false,
				},
				"huawei": &jpush.HuaweiOptions{
					Category:        "EXPRESS",
					Importance: "NORMAL",
					Sound: "/raw/order_tone",
					DefaultSound: false,
					//ChannelId:       "",
					//Distribution:    "secondary_push",
					//DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
			},
		},
	}
	return req
}
// 极光推送 配送员 默认通道
func (p *JiguangPush) getChannelTypeShipperDefault(
	rid string,
	title string,
	content string,
	params map[string]interface{},
	sound string,
	adminId int,	
	) *jpush.PushRequest{
	
	//SkipQuotaFalse := false
	//SkipQuotaTrue := true

	ps :=jpush.PushNotification{
		Alert: title,
		IOS: &jpush.NotificationIOS{
			Alert: &map[string]interface{}{
				"title": title,
				"body":  content,
			},
			Sound:  sound + ".caf",
			//Sound: "order_zh_tone",
			Extras: params,
		},
		Android: &jpush.NotificationAndroid{
			Alert:     content,
			Title:     "订单消息:" + title,
			BuilderId: 0,
			//Category:  "sys",
			Priority:  0,
			AlertType: 7,
			Intent: map[string]interface{}{
				"url": "intent:#Intent;action=android.intent.action.MAIN;end",
			},

			// Sound:  "default",
			ChannelID: "OrderGroupChat",
			Extras: params,
		},
	}
	if sound != "" {
		sound = strings.ReplaceAll(sound,"_ug","")
		ps.Android.Sound = sound
	}

	req := &jpush.PushRequest{
		Platform: []jpush.Platform{
			jpush.PlatformAndroid,
			jpush.PlatformIOS,
		},
		Audience: &jpush.PushAudience{
			RegistrationId: []string{rid},
		},
		Notification: &ps,
		Message: &jpush.PushMessage{
			MsgContent:  content,
			Title:       title,
			ContentType: "text",
			Extras:      params,
		},
		Options: &jpush.PushOptions{
			TimeToLive:     60,
			ApnsProduction: p.production,
			Classification: 1,
			ThirdPartyChannel: map[string]interface{}{
				"xiaomi": &jpush.XiaomiOptions{
					ChannelId:       "118707",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
				},
				"oppo": &jpush.OppoOptions{
					ChannelId:       "OrderGroupChat",
					//Distribution:    "secondary_push",
					//DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
				"vivo": &jpush.VivoOptions{
					CallbackId:      "",
					Category:        "IM",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
					SkipQuota:       false,
				},
				"huawei": &jpush.HuaweiOptions{
					Category:        "EXPRESS",
					Importance: "NORMAL",
					DefaultSound: true,
					//ChannelId:       "",
					//Distribution:    "secondary_push",
					//DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
			},
		},
	}
	return req
}

//管理员分配订单
func (p *JiguangPush) getChannelTypeShipperAssignOrder(
	rid string,
	title string,
	content string,
	params map[string]interface{},
	sound string,
	adminId int,	
	) *jpush.PushRequest{
	
	//SkipQuotaFalse := false
	//SkipQuotaTrue := true

	ps :=jpush.PushNotification{
		Alert: title,
		IOS: &jpush.NotificationIOS{
			Alert: &map[string]interface{}{
				"title": title,
				"body":  content,
			},
			Sound:  sound + ".caf",
			//Sound: "order_zh_tone",
			Extras: params,
		},
		Android: &jpush.NotificationAndroid{
			Alert:     content,
			Title:     "管理员分配订单:" + title,
			BuilderId: 0,
			//Category:  "sys",
			Priority:  0,
			AlertType: 7,
			Intent: map[string]interface{}{
				"url": "intent:#Intent;action=android.intent.action.MAIN;end",
			},

			// Sound:  "default",
			ChannelID: "AdminAssign",
			Extras: params,
		},
	}
	if sound != "" {
		sound = strings.ReplaceAll(sound,"_ug","")
		ps.Android.Sound = sound
	}

	req := &jpush.PushRequest{
		Platform: []jpush.Platform{
			jpush.PlatformAndroid,
			jpush.PlatformIOS,
		},
		Audience: &jpush.PushAudience{
			RegistrationId: []string{rid},
		},
		Notification: &ps,
		Message: &jpush.PushMessage{
			MsgContent:  content,
			Title:       title,
			ContentType: "text",
			Extras:      params,
		},
		Options: &jpush.PushOptions{
			TimeToLive:     60,
			ApnsProduction: p.production,
			Classification: 1,
			ThirdPartyChannel: map[string]interface{}{
				"xiaomi": &jpush.XiaomiOptions{
					ChannelId:       "119055",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
				},
				"oppo": &jpush.OppoOptions{
					ChannelId:       "AdminAssign",
					//Distribution:    "secondary_push",
					//DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
				"vivo": &jpush.VivoOptions{
					CallbackId:      "",
					Category:        "IM",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
					SkipQuota:       false,
				},
				"huawei": &jpush.HuaweiOptions{
					Category:        "EXPRESS",
					Importance: "NORMAL",
					DefaultSound: true,
					// ChannelId:       "Chat",
					Distribution:    "jpush",
					// DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
			},
		},
	}
	return req
}


//餐厅准备好美食
func (p *JiguangPush) getChannelTypeShipperFoodReady(
	rid string,
	title string,
	content string,
	params map[string]interface{},
	sound string,
	adminId int,	
	) *jpush.PushRequest{
	
	//SkipQuotaFalse := false
	//SkipQuotaTrue := true

	ps :=jpush.PushNotification{
		Alert: title,
		IOS: &jpush.NotificationIOS{
			Alert: &map[string]interface{}{
				"title": title,
				"body":  content,
			},
			Sound:  sound + ".caf",
			//Sound: "order_zh_tone",
			Extras: params,
		},
		Android: &jpush.NotificationAndroid{
			Alert:     content,
			Title:     "餐厅备餐完成通知:" + title,
			BuilderId: 0,
			//Category:  "sys",
			Priority:  0,
			AlertType: 7,
			Intent: map[string]interface{}{
				"url": "intent:#Intent;action=android.intent.action.MAIN;end",
			},

			Sound:  sound,
			ChannelID: "FoodReady",
			Extras: params,
		},
	}
	if sound != "" {
		sound = strings.ReplaceAll(sound,"_ug","")
		ps.Android.Sound = sound
	}

	req := &jpush.PushRequest{
		Platform: []jpush.Platform{
			jpush.PlatformAndroid,
			jpush.PlatformIOS,
		},
		Audience: &jpush.PushAudience{
			RegistrationId: []string{rid},
		},
		Notification: &ps,
		Message: &jpush.PushMessage{
			MsgContent:  content,
			Title:       title,
			ContentType: "text",
			Extras:      params,
		},
		Options: &jpush.PushOptions{
			TimeToLive:     60,
			ApnsProduction: p.production,
			Classification: 1,
			ThirdPartyChannel: map[string]interface{}{
				"xiaomi": &jpush.XiaomiOptions{
					ChannelId:       "119059",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
				},
				"oppo": &jpush.OppoOptions{
					ChannelId:       "FoodReady",
					//Distribution:    "secondary_push",
					//DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
				"vivo": &jpush.VivoOptions{
					CallbackId:      "",
					Category:        "IM",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
					SkipQuota:       false,
				},
				"huawei": &jpush.HuaweiOptions{
					Category:        "EXPRESS",
					Importance: "NORMAL",
					DefaultSound: true,
					// ChannelId:       "Chat",
					Distribution:    "jpush",
					// DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
			},
		},
	}
	return req
}


//订单退单 后台,店铺,用户
func (p *JiguangPush) getChannelTypeShipperOrderCancel(
	rid string,
	title string,
	content string,
	params map[string]interface{},
	sound string,
	adminId int,
	from string,		
	) *jpush.PushRequest{
	
	//SkipQuotaFalse := false
	//SkipQuotaTrue := true
	channelId :="ACancel"
	xiaomiChannelId :="119057" //后台取消订单
	switch from {
		case "cms":
			channelId ="ACancel"
			xiaomiChannelId ="119057"
		case "api":
			channelId ="RCancel"
			xiaomiChannelId ="119060"
		case "customer":
			channelId ="CCancel"
			xiaomiChannelId ="119058"


		
	}
	ps :=jpush.PushNotification{
		Alert: title,
		IOS: &jpush.NotificationIOS{
			Alert: &map[string]interface{}{
				"title": title,
				"body":  content,
			},
			Sound:  sound + ".caf",
			//Sound: "order_zh_tone",
			Extras: params,
		},
		Android: &jpush.NotificationAndroid{
			Alert:     content,
			Title:     "管理员取消订单通知:" + title,
			BuilderId: 0,
			//Category:  "sys",
			Priority:  0,
			AlertType: 7,
			Intent: map[string]interface{}{
				"url": "intent:#Intent;action=android.intent.action.MAIN;end",
			},

			Sound:  sound,
			ChannelID: channelId,
			Extras: params,
		},
	}
	if sound != "" {
		sound = strings.ReplaceAll(sound,"_ug","")
		ps.Android.Sound = sound
	}

	req := &jpush.PushRequest{
		Platform: []jpush.Platform{
			jpush.PlatformAndroid,
			jpush.PlatformIOS,
		},
		Audience: &jpush.PushAudience{
			RegistrationId: []string{rid},
		},
		Notification: &ps,
		Message: &jpush.PushMessage{
			MsgContent:  content,
			Title:       title,
			ContentType: "text",
			Extras:      params,
		},
		Options: &jpush.PushOptions{
			TimeToLive:     60,
			ApnsProduction: p.production,
			Classification: 1,
			ThirdPartyChannel: map[string]interface{}{
				"xiaomi": &jpush.XiaomiOptions{
					ChannelId:       xiaomiChannelId,
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
				},
				"oppo": &jpush.OppoOptions{
					ChannelId:       channelId,
					//Distribution:    "secondary_push",
					//DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
				"vivo": &jpush.VivoOptions{
					CallbackId:      "",
					Category:        "IM",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
					SkipQuota:       false,
				},
				"huawei": &jpush.HuaweiOptions{
					Category:        "EXPRESS",
					Importance: "NORMAL",
					DefaultSound: true,
					// ChannelId:       "Chat",
					Distribution:    "jpush",
					// DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
			},
		},
	}
	return req
}


//配送员聊天室
func (p *JiguangPush) getChannelTypeShipperChat(
	rid string,
	title string,
	content string,
	params map[string]interface{},
	sound string,
	adminId int,	
	) *jpush.PushRequest{
	
	//SkipQuotaFalse := false
	//SkipQuotaTrue := true

	ps :=jpush.PushNotification{
		Alert: title,
		IOS: &jpush.NotificationIOS{
			Alert: &map[string]interface{}{
				"title": title,
				"body":  content,
			},
			Sound:  sound + ".caf",
			//Sound: "order_zh_tone",
			Extras: params,
		},
		Android: &jpush.NotificationAndroid{
			Alert:     content,
			Title:     "聊天室:" + title,
			BuilderId: 0,
			//Category:  "sys",
			Priority:  0,
			AlertType: 7,
			Intent: map[string]interface{}{
				"url": "intent:#Intent;action=android.intent.action.MAIN;end",
			},

			// Sound:  "default",
			ChannelID: "Chat",
			Extras: params,
		},
	}
	if sound != "" {
		sound = strings.ReplaceAll(sound,"_ug","")
		ps.Android.Sound = sound
	}

	req := &jpush.PushRequest{
		Platform: []jpush.Platform{
			jpush.PlatformAndroid,
			jpush.PlatformIOS,
		},
		Audience: &jpush.PushAudience{
			RegistrationId: []string{rid},
		},
		Notification: &ps,
		Message: &jpush.PushMessage{
			MsgContent:  content,
			Title:       title,
			ContentType: "text",
			Extras:      params,
		},
		Options: &jpush.PushOptions{
			TimeToLive:     60,
			ApnsProduction: p.production,
			Classification: 1,
			ThirdPartyChannel: map[string]interface{}{
				"xiaomi": &jpush.XiaomiOptions{
					ChannelId:       "119061",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
				},
				"oppo": &jpush.OppoOptions{
					ChannelId:       "Chat",
					//Distribution:    "secondary_push",
					//DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
				"vivo": &jpush.VivoOptions{
					CallbackId:      "",
					Category:        "IM",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
					SkipQuota:       false,
				},
				"huawei": &jpush.HuaweiOptions{
					Category:        "EXPRESS",
					Importance: "NORMAL",
					DefaultSound: true,
					// ChannelId:       "Chat",
					Distribution:    "jpush",
					// DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
			},
		},
	}
	return req
}



// 极光推送 配送员 新订单通道
func (p *JiguangPush) getChannelTypeShipperNewOrder(
	rid string,
	title string,
	content string,
	params map[string]interface{},
	sound string,
	adminId int,
	) *jpush.PushRequest{
	if sound == "" {
		sound = "default"
	}
	//SkipQuotaFalse := false
	//SkipQuotaTrue := true

	req := &jpush.PushRequest{
		Platform: []jpush.Platform{
			jpush.PlatformAndroid,
			jpush.PlatformIOS,
		},
		Audience: &jpush.PushAudience{
			RegistrationId: []string{rid},
		},
		Notification: &jpush.PushNotification{
			Alert: title,
			IOS: &jpush.NotificationIOS{
				Alert: &map[string]interface{}{
					"title": title,
					"body":  content,
				},
				Sound:  sound + ".caf",
				//Sound: "order_zh_tone",
				Extras: params,
			},
			Android: &jpush.NotificationAndroid{
				Alert:     content,
				Title:     "订单消息:" + title,
				BuilderId: 0,
				//Category:  "sys",
				Priority:  0,
				AlertType: 7,
				Intent: map[string]interface{}{
					"url": "intent:#Intent;action=android.intent.action.MAIN;end",
				},

				Sound:  "order_tone",
				ChannelID: "OrderGroupChannel",
				Extras: params,
			},
		},
		Message: &jpush.PushMessage{
			MsgContent:  content,
			Title:       title,
			ContentType: "text",
			Extras:      params,
		},
		Options: &jpush.PushOptions{
			TimeToLive:     60,
			ApnsProduction: p.production,
			Classification: 1,
			ThirdPartyChannel: map[string]interface{}{
				"xiaomi": &jpush.XiaomiOptions{
					ChannelId:       "120000",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
				},
				"oppo": &jpush.OppoOptions{
					ChannelId:       "OrderGroupChannel",
					//Distribution:    "secondary_push",
					//DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
				"vivo": &jpush.VivoOptions{
					CallbackId:      "",
					Category:        "IM",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
					SkipQuota:       false,
				},
				"huawei": &jpush.HuaweiOptions{
					Category:        "EXPRESS",
					Importance: "NORMAL",
					Sound: "/raw/order_tone",
					DefaultSound: false,
					//ChannelId:       "",
					//Distribution:    "secondary_push",
					//DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
			},
		},
	}
	return req
}




//智能派单分配订单
func (p *JiguangPush) getChannelTypeShipperAutoDispatchAssignOrder(
	rid string,
	title string,
	content string,
	params map[string]interface{},
	sound string,
	adminId int,	
	) *jpush.PushRequest{
	
	ps :=jpush.PushNotification{
		Alert: title,
		IOS: &jpush.NotificationIOS{
			Alert: &map[string]interface{}{
				"title": title,
				"body":  content,
			},
			Sound:  sound + ".caf",
			Extras: params,
		},
		Android: &jpush.NotificationAndroid{
			Alert:     content,
			Title:     "系统分配订单:" + title,
			BuilderId: 0,
			//Category:  "sys",
			Priority:  0,
			AlertType: 7,
			Intent: map[string]interface{}{
				"url": "intent:#Intent;action=android.intent.action.MAIN;end",
			},

			// Sound:  "default",
			ChannelID: "SystemAssigned",
			Extras: params,
		},
	}
	if sound != "" {
		sound = strings.ReplaceAll(sound,"_ug","")
		ps.Android.Sound = sound
	}

	req := &jpush.PushRequest{
		Platform: []jpush.Platform{
			jpush.PlatformAndroid,
			jpush.PlatformIOS,
		},
		Audience: &jpush.PushAudience{
			RegistrationId: []string{rid},
		},
		Notification: &ps,
		Message: &jpush.PushMessage{
			MsgContent:  content,
			Title:       title,
			ContentType: "text",
			Extras:      params,
		},
		Options: &jpush.PushOptions{
			TimeToLive:     60,
			ApnsProduction: p.production,
			Classification: 1,
			ThirdPartyChannel: map[string]interface{}{
				"xiaomi": &jpush.XiaomiOptions{
					ChannelId:       "126636",//系统分配订单
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
				},
				"oppo": &jpush.OppoOptions{
					ChannelId:       "SystemAssigned",
					//Distribution:    "secondary_push",
					//DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
				"vivo": &jpush.VivoOptions{
					CallbackId:      "",
					Category:        "IM",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
					SkipQuota:       false,
				},
				"huawei": &jpush.HuaweiOptions{
					Category:        "EXPRESS",
					Importance: "NORMAL",
					DefaultSound: true,
					// ChannelId:       "Chat",
					Distribution:    "jpush",
					// DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
			},
		},
	}
	return req
}



//用户端聊天室
func (p *JiguangPush) getChannelTypeUserChat(
	rid string,
	title string,
	content string,
	params map[string]interface{},
	sound string,
	userId int,	
	options ...string,
	) *jpush.PushRequest{
	
	//SkipQuotaFalse := false
	//SkipQuotaTrue := true
	lang :="zh"
	if len(options) > 0  {
		lang = options[0]
		if params["restaurant_name_"+lang] !=nil {
			params["restaurant_name"] = params["restaurant_name_"+lang]
		}
	}	
	ps :=jpush.PushNotification{
		Alert: title,
		IOS: &jpush.NotificationIOS{
			Alert: &map[string]interface{}{
				"title": title,
				"body":  content,
			},
			Sound:  sound + ".caf",
			//Sound: "order_zh_tone",
			Extras: params,
		},
		Android: &jpush.NotificationAndroid{
			Alert:     content,
			Title:     "聊天室:" + title,
			BuilderId: 0,
			//Category:  "sys",
			Priority:  0,
			AlertType: 7,
			Intent: map[string]interface{}{
				// "url": "intent:#Intent;action=android.intent.action.MAIN;end",
				"url": "",
			},

			// Sound:  "default",
			ChannelID: "Chat",
			Extras: params,
		},
	}
	if sound != "" {
		sound = strings.ReplaceAll(sound,"_ug","")
		ps.Android.Sound = sound
	}

	req := &jpush.PushRequest{
		Platform: []jpush.Platform{
			jpush.PlatformAndroid,
			jpush.PlatformIOS,
		},
		Audience: &jpush.PushAudience{
			RegistrationId: []string{rid},
		},
		Notification: &ps,
		Message: &jpush.PushMessage{
			MsgContent:  content,
			Title:       title,
			ContentType: "text",
			Extras:      params,
		},
		Options: &jpush.PushOptions{
			TimeToLive:     60,
			ApnsProduction: p.production,
			Classification: 1,
			ThirdPartyChannel: map[string]interface{}{
				"xiaomi": &jpush.XiaomiOptions{
					ChannelId:       "119061",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
				},
				"oppo": &jpush.OppoOptions{
					ChannelId:       "Chat",
					//Distribution:    "secondary_push",
					//DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
				"vivo": &jpush.VivoOptions{
					CallbackId:      "",
					Category:        "IM",
					Distribution:    "secondary_push",
					DistributionFcm: "secondary_fcm_push",
					SkipQuota:       false,
				},
				"huawei": &jpush.HuaweiOptions{
					Category:        "EXPRESS",
					Importance: "NORMAL",
					DefaultSound: true,
					// ChannelId:       "Chat",
					Distribution:    "jpush",
					// DistributionFcm: "secondary_fcm_push",
					//SkipQuota:       false,
				},
			},
		},
	}
	return req
}