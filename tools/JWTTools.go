package tools

import (
	"fmt"
	"github.com/go-pay/gopay/pkg/jwt"
	"math/rand"
	"mulazim-api/configs"
	"time"
)

type JWTTools struct {

}

func NewJWTTools() *JWTTools{
	return &JWTTools{

	}
}
//
// GenerateToken
//  @Description: 登录时生成token
//  @receiver t
//  @param params
//  @return string
//  @return int
//
func (t JWTTools) GenerateToken(params map[string]interface{}) (string,int){
	//生成1到99999的随机数
	rand.Seed(time.Now().Unix())
	jwtSerialNumber := rand.Intn(99999)
	mapClaims := jwt.MapClaims{
		"jwt_serial_number":jwtSerialNumber,
	}
	// Create the Claims
	for k, v := range params {
		mapClaims[k] = v
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, mapClaims)

	// Sign and get the complete encoded token as a string using the secret
	key := configs.MyApp.JWTTokenKey
	tokenString, err := token.SignedString([]byte(key))
	if err!=nil {
		Logger.Error("FATAL token 生成失败")
		return "",0
	}
	return tokenString,jwtSerialNumber
}
//
// ParseTokenAndGetIDAndJWTSerialNumber
//  @Description: 解析jwt 获取admin_id 和 jwt_serial_number
//  @receiver t
//  @param tokenString
//  @return int64
//  @return int64
//  @return error
//
func (t JWTTools) ParseTokenAndGetIDAndJWTSerialNumber(tokenString string) (int64,int64,error){
	key := configs.MyApp.JWTTokenKey
	tokenParse, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Don't forget to validate the alg is what you expect:
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("FATAL Unexpected signing method: %v", token.Header["alg"])
		}

		// hmacSampleSecret is a []byte containing your secret, e.g. []byte("my_secret_key")
		return []byte(key), nil
	})
	if err != nil {
		return 0,0, fmt.Errorf("FATAL pasring: %v", err)
	}
	if claims, ok := tokenParse.Claims.(jwt.MapClaims); ok {
		return ToInt64(claims["id"]),ToInt64(claims["jwt_serial_number"]),nil
	} else {
		return 0,0,err
	}
}