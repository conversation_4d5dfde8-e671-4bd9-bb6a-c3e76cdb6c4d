package tools

import (
	"crypto/md5"
	// "encoding/json"
	"fmt"
	"mulazim-api/configs"
	"net/url"
	// "os"

	"time"
)

/**
*数脉 接口 签名
 */
func ShumaiSign(timestamp int64) (sign string) {

	str := fmt.Sprintf("%s&%d&%s", configs.MyApp.ShumaiAppid, timestamp, configs.MyApp.ShumaiAppSecret)
	data := []byte(str)      //切片
	hashStr := md5.Sum(data) //[16]byte
	sign = fmt.Sprintf("%x", hashStr)
	return
}

/**
*数脉 接口 身份证,银行卡,营业执照 OCR
* 文档地址  https://www.tianyandata.cn/productDetail/36 ,https://www.tianyandata.cn/productDetail/17, https://www.tianyandata.cn/productDetail/84,https://www.tianyandata.cn/productDetail/21
* ocr_type 1:身份证 2:银行卡 3:营业执照
*
 */
func ShumaiOcr(ocr_type int, imgPath string) (result string) {

	ocrUrl := ""
	switch ocr_type {
	case 1: //身份证
		ocrUrl = "https://api.shumaidata.com/v2/idcard/ocr"
	case 2: //银行卡
		ocrUrl = "https://api.shumaidata.com/v2/bankcard/ocr"
	case 3: //营业执照
		ocrUrl = "https://api.shumaidata.com/v2/business_license/ocr"
	}

	var cstSh, _ = time.LoadLocation("Asia/Shanghai") //上海

	timestamp := time.Now().In(cstSh).Unix() * 1000

	sign := ShumaiSign(timestamp)

	image, _ := ImageToBase64(imgPath, true)

	image = url.QueryEscape(image)

	params := fmt.Sprintf("appid=%s&timestamp=%d&sign=%s&image=%s", configs.MyApp.ShumaiAppid, timestamp, sign, image)

	headers := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
	}
	_, body := HttpPostJson([]byte(params), ocrUrl, 60*time.Second, headers)
	fmt.Println(body)
	// json.Unmarshal([]byte(body), &rs)
	result = body

	Log(body)

	return

}

/**
*数脉 接口 天眼查 营业执照 信息查询
* reg_num 营业执照号码
 */
func ShumaiCheckCompanyInfo(reg_num string) (result string) {

	ocrUrl := "https://api.shumaidata.com/v4/business4/get"

	var cstSh, _ = time.LoadLocation("Asia/Shanghai") //上海

	timestamp := time.Now().In(cstSh).Unix() * 1000

	sign := ShumaiSign(timestamp)

	fmt.Println("sign", sign)

	params := fmt.Sprintf("appid=%s&timestamp=%d&sign=%s&keyword=%s", configs.MyApp.ShumaiAppid, timestamp, sign, reg_num)

	headers := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
	}

	_, body := HttpGetJson(ocrUrl+"?"+params, 30*time.Second, headers)
	fmt.Println(body)

	Log(body)

	result = body

	return

}

func AliOcrFoodLicense(imgPath string) (rs string) {

	ocrUrl := "https://foodshop.market.alicloudapi.com/ai_market/intelligent_food_business_license_identification/v1"

	appcode := configs.MyApp.AliyunAppCode //"7bc2bf702a72425d906f23bbd83c9ef4"

	image, _ := ImageToBase64(imgPath, true)

	image = url.QueryEscape(image)

	params := fmt.Sprintf("IMAGE=%s&IMAGE_TYPE=%s", image, "0")

	headers := map[string]string{
		"Authorization": "APPCODE " + appcode,
		"Content-Type":  "application/x-www-form-urlencoded; charset=UTF-8",
	}
	_, body := HttpPostJson([]byte(params), ocrUrl, 60*time.Second, headers)
	fmt.Println(body)
	// json.Unmarshal([]byte(body), &rs)
	rs = body
	return

}
