package tools

import (
	"github.com/gin-gonic/gin"
	"strings"
)

/*
 * 该文件创建是为了统一管理版本的对比代码
 *
 * 目前商家端的 version-name 变化规则是，中/小版本都会等于小于9，大了就会升级大一级的版本。
 * 为了兼容性，我们使用假设每个版本使用最大三位数来写函数。
 * - 比如可以兼容： 2.103.356 / 2.6.9 的对比
 * - 对比过程是： 2103356 >= 2006009
 */

// PadLeft 左侧填充 0
// Creating a function to add zeroes to a string on the left side
func PadLeft(str string, length int) string {
	for len(str) < length {
		str = "0" + str
	}
	return str
}


// VersionToInt
// 解析版本字符串至数字，以后续对比
func VersionToInt(version string) int64 {
	if version == "" {
		return 0
	}

	versionArr := strings.Split(version, ".")
	if len(versionArr) != 3 {
		return 0
	}

	preVersionInt := ""
	for _, ver := range versionArr {
		preVersionInt += PadLeft(ver, 3)
	}
	return ToInt64(preVersionInt)
}

// IsVersionGTE
//
// @description Does the version name greater or equal than
// @param ctx *gin.Context
// @param versionName string 请求里的版本是否大于等于方法里给出的版本
func IsVersionGTE(ctx *gin.Context, versionName string) bool {
	headerVer := ctx.GetHeader("version-name")

	// 如果 Header 里没有 version 信息，说明是一个非常旧的版本。
	if headerVer == "" {
		return false
	}

	// 解析 version 来准备对比
	appVersion := VersionToInt(headerVer)
	compareVersion := VersionToInt(versionName)

	// 对比，并返回结果
	return appVersion >= compareVersion
}
