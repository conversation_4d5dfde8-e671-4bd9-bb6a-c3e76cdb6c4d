package tools

import "reflect"

// InArray 判断字符串是否在数组中

// InArrayInt 判断整型是否在数组中
func InArrayInt(needle int, haystack []int) bool {
	for _, item := range haystack {
		if item == needle {
			return true
		}
	}
	return false
}

// InArrayAll 判断任意类型是否在数组中
func InArrayAll(needle interface{}, haystack interface{}) bool {
	switch reflect.TypeOf(haystack).Kind() {
	case reflect.Slice:
		s := reflect.ValueOf(haystack)
		for i := 0; i < s.Len(); i++ {
			if reflect.DeepEqual(needle, s.Index(i).Interface()) {
				return true
			}
		}
	}
	return false
}

// ArrayUnique 泛型去重（支持所有可比较类型，保留原始顺序）
func ArrayUnique[T comparable](slice []T) []T {
	if slice == nil {
		return nil
	}
	m := make(map[T]bool, len(slice))
	result := make([]T, 0, len(slice))
	for _, v := range slice {
		if !m[v] {
			m[v] = true
			result = append(result, v)
		}
	}
	return result
}