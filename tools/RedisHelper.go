package tools

import (
	"context"
	"encoding/json"
	"mulazim-api/configs"
	"sync"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/go-redis/redis/v8"
)

type RedisHelper struct {
	*redis.Client
}

var redisHelper *RedisHelper
var redisCmsHelper *RedisHelper

var redisOnce sync.Once
var redisCmsOnce sync.Once

func GetRedisHelper() *RedisHelper {
	return redisHelper
}
func GetRedisCmsHelper() *RedisHelper {
	return redisCmsHelper
}
func newRedisHelper() *redis.Client {

	rdb := redis.NewClient(&redis.Options{
		Addr:         configs.MyApp.RedisIp,
		Password:     configs.MyApp.RedisPassword,
		DB:           configs.MyApp.RedisDB,
		DialTimeout:  10 * time.Second,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		PoolSize:     10,
		PoolTimeout:  30 * time.Second,
	})

	redisOnce.Do(func() {
		rdh := new(RedisHelper)
		rdh.Client = rdb
		redisHelper = rdh
	})

	return rdb
}

func newRedisCmsHelper() *redis.Client {

	rdb := redis.NewClient(&redis.Options{
		Addr:         configs.MyApp.RedisIp,
		Password:     configs.MyApp.RedisPassword,
		DB:           10,
		DialTimeout:  10 * time.Second,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		PoolSize:     10,
		PoolTimeout:  30 * time.Second,
	})

	redisCmsOnce.Do(func() {
		rdh := new(RedisHelper)
		rdh.Client = rdb
		redisCmsHelper = rdh
	})
	return rdb
}

var ctx context.Context

func init() {
	ctx = context.Background()

	rdb := newRedisHelper()
	if _, err := rdb.Ping(ctx).Result(); err != nil {
		Logger.Errorf("FATAL redis-connect-fail ---->\n\tError:%s\n", err)
	}


	rdb = newRedisCmsHelper()
	if _, err := rdb.Ping(ctx).Result(); err != nil {
		Logger.Errorf("FATAL redis-connect-fail ---->\n\tError:%s\n", err)
	}

}
func Remember(c *gin.Context, key string, expiration time.Duration, clouser func() interface{}) string {
	if exists, _ := redisHelper.Exists(c, key).Result(); exists == 0 {
		object := clouser()
		str, _ := json.Marshal(object)
		redisHelper.Set(c, key, string(str), expiration)
	}
	str, _ := redisHelper.Get(c, key).Result()
	return str
}
func RedisDel(c *gin.Context, key string) string {
	if exists, _ := redisHelper.Exists(c, key).Result(); exists == 1 {
		redisHelper.Del(c, key)
	}
	str, _ := redisHelper.Get(c, key).Result()
	return str
}

func Remember2(key string, expiration time.Duration, closure func() interface{}) string {

	if exists, _ := redisHelper.Exists(ctx, key).Result(); exists == 0 {
		object := closure()
		str, _ := json.Marshal(object)
		redisHelper.Set(ctx, key, string(str), expiration)
	}
	str, _ := redisHelper.Get(ctx, key).Result()
	return str
}
func SetNX(key string, value interface{}, expiration time.Duration) *redis.BoolCmd {
	return redisHelper.SetNX(ctx, key,value,expiration)
}