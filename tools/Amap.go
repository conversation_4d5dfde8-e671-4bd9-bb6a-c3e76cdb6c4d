package tools

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"mulazim-api/configs"
	"sort"
	"strings"
	"time"
)

//  AmapTool
//  @Description: 高德地图工具类
//
type AmapTool struct {

}

// MapToMD5 将 map 的 key 按升序排序，并计算拼接后的字符串的 MD5 值
func (t AmapTool) mapToMD5(inputMap map[string]string) string {
	if inputMap!=nil {
		inputMap["key"] = configs.MyApp.AmapWebKey
	}
	// 获取 map 的所有 key
	keys := make([]string, 0, len(inputMap))
	for k := range inputMap {
		keys = append(keys, k)
	}

	// 对 key 进行升序排序
	sort.Strings(keys)

	// 拼接排序后的 key 和对应的 value
	var concatenatedString string
	for _, k := range keys {
		concatenatedString += k +"=" + inputMap[k] +"&"
	}
	concatenatedString = strings.TrimRight(concatenatedString,"&")
	concatenatedString += configs.MyApp.AmapWebSign
	// 计算拼接后的字符串的 MD5 值
	hasher := md5.New()
	hasher.Write([]byte(concatenatedString))
	md5Hash := hex.EncodeToString(hasher.Sum(nil))

	return md5Hash
}
// GetAmapAddressTextByGpsLocation
//  @Description: 根据经纬度获取地址
//  @receiver t
//  @param lat
//  @param lng
//  @return string
//
func (t AmapTool) GetAmapAddressTextByGpsLocation(lat float64,lng float64) string {
	sig := t.mapToMD5(map[string]string{
		"location": fmt.Sprintf("%f,%f",lat,lng),
	})
	url := fmt.Sprintf("https://restapi.amap.com/v3/geocode/regeo?key=%s&location=%f,%f&sig=%s",configs.MyApp.AmapWebKey,lat,lng,sig)
	_, resp := HttpGetJson(url, 5*time.Second, nil)
	var text AmapAddressText
	err := json.Unmarshal([]byte(resp), &text)
	if  text.Status == "1" {
		rtn := text.Regeocode.FormattedAddress
		rtn = strings.TrimLeft(rtn,text.Regeocode.AddressComponent.Province)
		rtn = strings.TrimLeft(rtn,text.Regeocode.AddressComponent.City)
		if len(rtn) ==0 {
			Logger.Errorf("FATAL error from gaode get location info , %s ,lat:%f,lng:%f",resp,lat,lng)
		}
		return rtn
	}else{
		Logger.Errorf("FATAL error from gaode get location info , %s ,lat:%f,lng:%f",resp,lat,lng)
		Logger.Errorf("FATAL error from gaode get location info , %s",err.Error())
	}
	return ""
}
type AmapAddressText struct {
	Status    string `json:"status"`
	Regeocode struct {
		AddressComponent struct {
			City         string `json:"city"`
			Province     string `json:"province"`
			Country       string  `json:"country"`
			Township      string  `json:"township"`
			Citycode string `json:"citycode"`
		} `json:"addressComponent"`
		FormattedAddress string `json:"formatted_address"`
	} `json:"regeocode"`
	Info     string `json:"info"`
	Infocode string `json:"infocode"`
}