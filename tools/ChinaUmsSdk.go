package tools

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"github.com/forgoer/openssl"
)


// ChinaUmsSignSHA256
//  @Description: SHA256加密
//  <AUTHOR>
//  @Time 2023-02-02 13:06:27
//  @param str 需要加密内容
//  @return string 加密内容
func ChinaUmsSignSHA256(str string) string {
	m := sha256.New()
	m.Write([]byte(str))
	res := hex.EncodeToString(m.Sum(nil))
	return res
}


// ChinaUmsDES3Encrypt
//  @Description: 加密
//  <AUTHOR>
//  @Time 2023-02-02 13:05:52
//  @param src 需要加密内容
//  @param key 密钥
//  @return string 加密内容
func ChinaUmsDES3Encrypt(src string, key string) string {
	dst, err := openssl.Des3ECBEncrypt([]byte(src), []byte(key), openssl.PKCS5_PADDING)
	if err != nil {
		fmt.Println(err.Error())
		return err.Error()
	}
	return hex.EncodeToString(dst)
}


// ChinaUmsDES3Decrypt
//  @Description: 解密
//  <AUTHOR>
//  @Time 2023-02-02 13:05:24
//  @param src 加密内容
//  @param key 密钥
//  @return string 解密内容
func ChinaUmsDES3Decrypt(src string, key string) string {
	srcBytes, _ := hex.DecodeString(src)
	dst, _ := openssl.Des3ECBDecrypt(srcBytes, []byte(key), openssl.PKCS5_PADDING)  // 银联商务通知返回的加密内容解密算法：ZEROS_PADDING
	return string(dst)
}
