// 定义一个工具包，用来管理gorm数据库连接池的初始化工作。
package tools

import (
	"context"
	"errors"
	"fmt"
	"log"
	"mulazim-api/configs"
	"time"

	_ "github.com/jinzhu/gorm/dialects/mysql"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/utils"
	"reflect"
)

// 定义全局的db对象，我们执行数据库操作主要通过他实现。
var Db *gorm.DB
var ReadDb1 *gorm.DB

// 包初始化函数，golang特性，每个包初始化的时候会自动执行init函数，这里用来初始化gorm。
func init() {
	var err error
	// 全局模式
	mysqlLogger := MysqlLogger{}
	db, err := gorm.Open(mysql.Open(configs.MyApp.MysqlDns), &gorm.Config{
		Logger:                 mysqlLogger,
		SkipDefaultTransaction: true,
		PrepareStmt:            true,
	})

	if err != nil {
		log.Panicln("err:", err.Error())
	}
	Db = db
	sqlDB, _ := db.DB()
	sqlDB.SetMaxIdleConns(10)  //空闲连接数
	sqlDB.SetMaxOpenConns(100) //最大连接数
	sqlDB.SetConnMaxLifetime(time.Minute)


	// 读写分离模式
	if configs.MyApp.MysqlDnsReadDB1 == "" {
		configs.MyApp.MysqlDnsReadDB1 = configs.MyApp.MysqlDns
	}
	readDb1, err := gorm.Open(mysql.Open(configs.MyApp.MysqlDnsReadDB1), &gorm.Config{
		Logger:                 mysqlLogger,
		SkipDefaultTransaction: true,
		PrepareStmt:            true,
	})

	if err != nil {
		log.Panicln("err:", err.Error())
	}
	ReadDb1 = readDb1
	sqlReadDB1, _ := readDb1.DB()
	sqlReadDB1.SetMaxIdleConns(10)  //空闲连接数
	sqlReadDB1.SetMaxOpenConns(100) //最大连接数
	sqlReadDB1.SetConnMaxLifetime(time.Minute)

}

func GetDB() *gorm.DB {
	return Db
}

func DeleteDB(table string) *gorm.DB {
	return Db.Table(table).Update("deleted_at", time.Now)
}

type MysqlLogger struct {
	logger.Interface
	level logger.LogLevel
}

func (m MysqlLogger) LogMode(level logger.LogLevel) logger.Interface {

	//fmt.Printf("level:%v\n",level)
	m.level = level
	return m
}

func (m MysqlLogger) Info(ctx context.Context, s string, i ...interface{}) {

}

func (m MysqlLogger) Warn(ctx context.Context, s string, i ...interface{}) {

}

func (m MysqlLogger) Error(ctx context.Context, msg string, data ...interface{}) {

	Logger.Errorf("FATAL %s\n[error] "+msg, append([]interface{}{utils.FileWithLineNum()}, data...)...)
}

func (m MysqlLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {

	//panic("implement me")
	elapsed := time.Since(begin)
	switch {
	case err != nil && (!errors.Is(err, gorm.ErrRecordNotFound)):
		sql, rows := fc()
		if rows == -1 {
			Logger.Errorf("FATAL %s %s [%.3fms] [rows:%v] %s", utils.FileWithLineNum(), err, float64(elapsed.Nanoseconds())/1e6, "-", sql)
		} else {
			Logger.Errorf("FATAL %s %s [%.3fms] [rows:%v] %s", utils.FileWithLineNum(), err, float64(elapsed.Nanoseconds())/1e6, rows, sql)
		}
	case elapsed.Milliseconds() > 2000:
		sql, rows := fc()
		slowLog := fmt.Sprintf("SLOW SQL >= %vms ", 2000)
		if rows == -1 {
			Logger.Warnf("%s %s [%.3fms] [rows:%v] %s", utils.FileWithLineNum(), slowLog, float64(elapsed.Nanoseconds())/1e6, "-", sql)
		} else {
			Logger.Warnf("%s %s [%.3fms] [rows:%v] %s", utils.FileWithLineNum(), slowLog, float64(elapsed.Nanoseconds())/1e6, rows, sql)
		}
	case m.level == logger.Info:
		sql, rows := fc()
		if rows == -1 {
			Logger.Infow("debug",
				"type", "DEBUG",
				"line", utils.FileWithLineNum(),
				"time", float64(elapsed.Nanoseconds())/1e6,
				"sql", sql,
			)
		} else {
			Logger.Infow("debug",
				"type", "DEBUG",
				"line", fmt.Sprintf("%s", utils.FileWithLineNum()),
				"time", float64(elapsed.Nanoseconds())/1e6,
				"rows", rows,
				"sql", sql,
			)
		}
	}
}


// GetValueByGormColumn returns the value of a struct field by its gorm column name.
func GetValueByGormColumn(obj interface{}, columnName string) (interface{}, error) {
	v := reflect.ValueOf(obj)
	if v.Kind() == reflect.Ptr {
		v = v.Elem() // Dereference pointer if needed
	}
	if v.Kind() != reflect.Struct {
		return nil, fmt.Errorf("input must be a struct or pointer to struct")
	}

	t := v.Type()

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		gormTag := field.Tag.Get("gorm")
		if gormTag != "" {
			var gormCol string
			if _, err := fmt.Sscanf(gormTag, "column:%s", &gormCol); err == nil && gormCol == columnName {
				return v.Field(i).Interface(), nil
			}
		}
	}
	return nil, fmt.Errorf("column %q not found in struct fields", columnName)

}