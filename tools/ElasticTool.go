package tools

import (
	"context"
	"strings"

	esConfig "mulazim-api/configs/es"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
)

func init() {
	initEs()
}

// initEs
//
//	@Description: 初始化es
func initEs() *elasticsearch.Client {
	addresses := []string{esConfig.ElasticConfigOpt.Host}
	config := elasticsearch.Config{
		Addresses: addresses,
		Username:  "",
		Password:  "",
		CloudID:   "",
		APIKey:    "",
	}
	// new client
	es, err := elasticsearch.NewClient(config)
	if err != nil {
		Logger.Error("FATAL ERROR: %s", err.Error())
	}
	Es = es
	return Es
}

var Es *elasticsearch.Client
// 删除指定的索引
func DeleteOrderIndex(indexName string) {
	_, err := Es.Indices.Delete([]string{indexName})
	if err != nil {
		Logger.Error("FATAL ERROR: %s", err.Error())
	}
}
// 创建订单索引
func CreateOrderIndex(indexName string) {

	
	// Index creates or updates a document in an index
	exists, err := Es.Indices.Exists([]string{indexName})
	if err != nil {
		Logger.Error("FATAL ERROR: %s", err.Error())
	}
	if exists.StatusCode == 404 {

		// 定义映射
		mapping := `
    {
		 "settings":{
			"index.max_ngram_diff":2,
			"analysis":{
				"analyzer":{
					"my_analyzer":{
						"tokenizer":"my_tokenizer"
					}
				},
				"tokenizer":{
					"my_tokenizer":{
						"type":"ngram",
						"min_gram":4,
						"max_gram":6,
						"token_chars":[
							"letter",
							"digit"
						]
					}
				}
			}
		},
        "mappings": {
			"properties":{
				"building_id":{
					"type":"long"
				},
				"created_at":{
					"type":"date",
					"format": "yyyy-MM-dd HH:mm:ss||strict_date_optional_time ||epoch_millis"
				},
				
				"area_id":{
					"type":"long"
				},
				"user_mobile":{
					"type":"text",
					"analyzer":"my_analyzer",
					"fields":{
						"keyword":{
							"ignore_above":256,
							"type":"keyword"
						}
					}
				},
				"delivery_type":{
					"type":"long"
				},
				"id":{
					"type":"long"
				},
				"state":{
					"type":"long"
				},
				"terminal_id":{
					"type":"long"
				},
				"store_id":{
					"type":"long"
				},
				"shipment":{
					"type":"long"
				},
				"mobile":{
					"type":"text",
					"analyzer":"my_analyzer",
					"fields":{
						"keyword":{
							"ignore_above":256,
							"type":"keyword"
						}
					}
				},
				
				"lunch_box_fee":{
					"type":"long"
				},
				"name":{
					"type":"text",
					"fields":{
						"keyword":{
							"ignore_above":256,
							"type":"keyword"
						}
					}
				},
				"order_id":{
					"type":"text",
					"analyzer":"my_analyzer",
					"fields":{
						"keyword":{
							"ignore_above":256,
							"type":"keyword"
						}
					}
				},
				"city_id":{ 
					"type":"long"
				},
				"pay_channel_trade_no":{
					"type":"text",
					"analyzer":"my_analyzer",
					"fields":{
						"keyword":{
							"ignore_above":1024,
							"type":"keyword"
						}
					}
				},
				"wechat_pay_channel_trade_no":{
					"type":"text",
					"analyzer":"my_analyzer",
					"fields":{
						"keyword":{
							"ignore_above":1024,
							"type":"keyword"
						}
					}
				}

			}
		}
    }
    `

		req := esapi.IndicesCreateRequest{
			Index: indexName,
			Body:  strings.NewReader(mapping),
		}

		res, err := req.Do(context.Background(), Es)
		if err != nil {
			Logger.Error("FATAL ERROR: %s", err.Error())
		}
		defer res.Body.Close()
		if res.StatusCode != 200 {
			Logger.Error("FATAL ERROR: %s", "索引创建失败")
		} else {
			Logger.Infof("索引不存在，创建成功")
		}
	} else {
		Logger.Infof("索引已存在")
	}

}