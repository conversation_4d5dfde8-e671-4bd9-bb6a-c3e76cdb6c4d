package tools

import (
	"crypto/md5"
	"encoding/hex"
	"reflect"
	"strings"
	"unicode/utf8"
)

func SubString(str string, length int) string {
	if len(str) < length {
		return str
	}
	runes := []rune{}
	count := 0
	for len(runes) < length {
		r, size := utf8.DecodeRuneInString(str)
		if r == utf8.RuneError {
			break
		}
		count += size
		if count > length {
			break
		}
		runes = append(runes, r)
		str = str[size:]
	}
	return strings.Trim(string(runes), " ")
}

func ReplaceRange(str string, start, end int) string {
	if start < 0 || end > len(str) || start >= end {
		return str // Return the original string if the range is invalid
	}

	// Create the replacement string with asterisks
	replacement := strings.Repeat("*", end-start)

	// Combine the parts of the string
	return str[:start] + replacement + str[end:]
}

// Md5 计算字符串的 MD5 值
func Md5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}



// GetDefaultValue 是一个通用函数，用于获取默认值。
// 如果第一个参数为空（零值），则返回第二个参数。
func GetDefaultValue[T any](value T, defaultValue T) T {
	// 使用反射检查 value 是否为零值
	if reflect.DeepEqual(value, reflect.Zero(reflect.TypeOf(value)).Interface()) {
		return defaultValue
	}
	return value
}