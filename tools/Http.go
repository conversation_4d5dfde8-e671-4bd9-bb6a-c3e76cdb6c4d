/*
*
@author: captain
@since: 2022/9/5
@desc:
*
*/
package tools

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"reflect"
	"strings"
	"time"
)

// HttpPostJson
//
//	@Description: 发送Http Post 请求
//	@author: Captain
//	@Time: 2022-09-05 21:47:17
//	@param jsonStr []byte request body(media type json)
//	@param url string 请求Url
//
// @param timeout time.Duration 超时时间
//
//	@return int Http Status Code
//	@return string Response Body strings(Json string if status code is 200 else string otherwise)
func HttpPostJson(jsonStr []byte, url string, timeout time.Duration, headers ...map[string]string) (int, string) {
	header := map[string]string{}

	if len(headers) > 0 {
		header = headers[0]
	}
	// 先新建NewRequest，保存请求方式和内容
	// 第一个参数可选 POST或GET 或其他
	//url 为请求地址
	//第三个参数为
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		panic(err)
	}
	// 可以设置其他的Header
	//req.Header.Set("Content-Type", "application/json; charset=UTF-8")
	//调用一个http.Clien 客户端，用于对Reques 对象的处理
	// 要发送复杂的上下文 建议使用 NewRequestWithContext + Client.Do 形式
	//简单内容可以直接使用 client.Post/client.Get 方法，使用方法查看源码即可

	client := &http.Client{
		Timeout: timeout, // 设置超时时间为 5 秒
	}
	for key, value := range header {
		req.Header.Add(key, value)
	}
	resp, err := client.Do(req)
	if err != nil {
		panic(err)
	}
	// client.Do 返回的body 必须要关闭
	defer resp.Body.Close()
	statusCode := resp.StatusCode
	//hea := resp.Header
	body, _ := ioutil.ReadAll(resp.Body)
	////读取body
	//fmt.Println(string(body))
	////状态值
	//fmt.Println(statuscode)
	//fmt.Println(hea)
	return statusCode, string(body)

}

func HttpPostForm(httpUrl string, values url.Values, timeout time.Duration) (map[string]interface{}, error) {
	client := &http.Client{
		Timeout: timeout, // 设置超时时间为 5 秒
	}
	resp, err := client.PostForm(httpUrl, values)
	if err != nil {
		fmt.Printf("postForm请求失败 error: %+v", err)
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Print(err)
		panic(err.Error())
	}
	// Log("响应报文：" + string(body))
	fmt.Println(string(body))
	res, err := StringToMap(string(body))
	return res, err
}

func HttpPostFormDownFile(httpUrl string, values url.Values, timeout time.Duration) ([]byte, error) {
	client := &http.Client{
		Timeout: timeout, // 设置超时时间为 5 秒
	}
	resp, err := client.PostForm(httpUrl, values)
	if err != nil {
		fmt.Printf("postForm请求失败 error: %+v", err)
	}
	defer resp.Body.Close()
	body, _ := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println(err.Error())
		panic(err.Error())
	}
	return body, nil
}

func HttpPost(api string, params map[string]interface{}, timeout time.Duration) string {
	var values []string
	for k, v := range params {
		switch v.(type) {
		case string:
			values = append(values, fmt.Sprintf("%s=%s", k, v))
		case int:
			values = append(values, fmt.Sprintf("%s=%d", k, v))
		default:
			continue
		}
	}
	client := &http.Client{
		Timeout: timeout, // 设置超时时间为 5 秒
	}
	resp, err := client.Post(api, "application/x-www-form-urlencoded", strings.NewReader(strings.Join(values, "&")))
	if err != nil {
		fmt.Println(err.Error())
		return ""
	}

	if resp.StatusCode != 200 {
		fmt.Println("error",resp.StatusCode)
		return ""
	}
	contentBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println(err.Error())
		return ""
	}
	return string(contentBytes)
}

// HttpGetJson
//
//	@Description: 发送Http get请求
//	@author: Captain
//	@Time: 2022-09-05 21:37:01
//	@param url string 请求Url
//	@return int  Http Status Code
//	@return string Response Body strings(Json string if status code is 200 else string otherwise)
func HttpGetJson(url string, timeout time.Duration, headers ...map[string]string) (int, string) {

	header := map[string]string{}

	if len(headers) > 0 {
		header = headers[0]
	}

	client := &http.Client{
		Timeout: timeout, // 设置超时时间为 5 秒
	}

	req, err := http.NewRequest("GET", url, strings.NewReader(""))
	if err != nil {
		panic(err)
	}

	for key, value := range header {
		req.Header.Add(key, value)
	}
	resp, err := client.Do(req)

	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	statuscode := resp.StatusCode
	body, _ := ioutil.ReadAll(resp.Body)

	return statuscode, string(body)
}

// MapToString
//
// @Description: Map转换json字符串
// @Author: Rixat
// @Time: 2023-01-07 16:35:56
// @receiver
// @param c *gin.Context
func MapToString(mapVal map[string]interface{}) string {
	jsonByte, _ := json.Marshal(mapVal)
	jsonStr := string(jsonByte)
	return jsonStr
}
func StructToMap(s interface{}) map[string]interface{} {
	m := make(map[string]interface{})

	v := reflect.ValueOf(s)
	t := reflect.TypeOf(s)

	for i := 0; i < t.NumField(); i++ {
		key := t.Field(i).Name
		value := v.Field(i).Interface()
		m[key] = value
	}

	return m
}

// JsonEncode
//
// @Description: Map转换json字符串
// @Author: Rixat
// @Time: 2023-01-07 16:35:56
// @receiver
// @param c *gin.Context
func JsonEncode(v any) string {
	jsonByte, _ := json.Marshal(v)
	jsonStr := string(jsonByte)
	return jsonStr
}

// JsonDecode
//
// @Description: Map转换json字符串
// @Author: Rixat
// @Time: 2023-01-07 16:35:56
// @receiver
// @param c *gin.Context
func JsonDecode(v string) interface{} {
	res := make(map[string]interface{})
	err := json.Unmarshal([]byte(v), &res)
	if err != nil {
		fmt.Println(err.Error())
		panic(err.Error())
	}
	return res
}

// StringToMap
//
// @Description: json字符串转换Map
// @Author: Rixat
// @Time: 2023-01-07 16:35:56
// @receiver
// @param c *gin.Context
func StringToMap(jsonStr string) (map[string]interface{}, error) {
	res := make(map[string]interface{})
	err := json.Unmarshal([]byte(jsonStr), &res)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}
	return res, nil
}

// StringToMapArr
//
// @Description: json字符串转换Map
// @Author: Rixat
// @Time: 2023-01-07 16:35:56
// @receiver
// @param c *gin.Context
func StringToMapArr(jsonStr string) []map[string]interface{} {
	// 解析params.Bnf 为map
	mapArr := make([]map[string]interface{}, 0)
	err := json.Unmarshal([]byte(jsonStr), &mapArr)
	if err != nil {
		panic(err)
	}
	return mapArr
}
