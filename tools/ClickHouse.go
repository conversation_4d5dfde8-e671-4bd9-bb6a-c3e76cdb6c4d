package tools

import (
	"context"
	"fmt"
	"log"
	"mulazim-api/configs"
	"strconv"
	"sync"
	"time"

	"gorm.io/driver/clickhouse"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// ClickHouse数据库连接实例
var (
	ClickHouseDB *gorm.DB
	ckOnce       sync.Once
)

// 初始化ClickHouse数据库连接
func initClickHouseDB() {
	// 默认DSN配置
	dsn := "clickhouse://default@*************:9000/mulazim_shipper_statistic?dial_timeout=10s&read_timeout=20s"
	
	// 如果项目配置中有ClickHouse配置，优先使用配置文件中的
	// 可以在configs/Config.go中添加ClickHouseDsn字段
	if configs.MyApp.ClickHouseDsn != "" {
	    dsn = configs.MyApp.ClickHouseDsn
	}
	
	
	db, err := gorm.Open(clickhouse.Open(dsn), &gorm.Config{
		Logger:                 &ClickHouseLogger{level: logger.Error}, // 默认只显示错误日志
		SkipDefaultTransaction: true,
		PrepareStmt:            false, // ClickHouse不支持预编译语句
	})
	
	if err != nil {
		logError("ClickHouse连接失败", err, "dsn", dsn)
		log.Panicf("ClickHouse连接失败: %v, DSN: %s", err, dsn)
	}
	
	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		logError("获取ClickHouse底层连接失败", err, "dsn", dsn)
		log.Panicf("获取ClickHouse底层连接失败: %v, DSN: %s", err, dsn)
	}
	
	// ClickHouse连接池配置
	sqlDB.SetMaxIdleConns(5)                   // 空闲连接数
	sqlDB.SetMaxOpenConns(20)                  // 最大连接数  
	sqlDB.SetConnMaxLifetime(5 * time.Minute) // 连接最大生存时间
	
	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	if err := sqlDB.PingContext(ctx); err != nil {
		logError("ClickHouse连接测试失败", err, "dsn", dsn)
		log.Panicf("ClickHouse连接测试失败: %v, DSN: %s", err, dsn)
	}
	
	ClickHouseDB = db
	logInfo("ClickHouse连接初始化成功", "dsn", dsn)
}

// GetCKDB 获取ClickHouse数据库连接实例（单例模式）
func GetCKDB() *gorm.DB {
	ckOnce.Do(func() {
		initClickHouseDB()
	})
	return ClickHouseDB
}

// GetCKDBWithDebug 获取ClickHouse连接并启用调试模式
func GetCKDBWithDebug() *gorm.DB {
	db := GetCKDB()
	return db.Debug() // 启用GORM的调试模式
}

// SetClickHouseLogLevel 设置ClickHouse日志级别
func SetClickHouseLogLevel(level logger.LogLevel) {
	if ClickHouseDB != nil {
		ClickHouseDB.Logger = ClickHouseDB.Logger.LogMode(level)
	}
}

// ClickHouseLogger ClickHouse自定义日志记录器
type ClickHouseLogger struct {
	level logger.LogLevel
}

func (c *ClickHouseLogger) LogMode(level logger.LogLevel) logger.Interface {
	return &ClickHouseLogger{level: level}
}

func (c *ClickHouseLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	// GORM内部信息，通常不需要显示，只在Debug模式下才记录
	if c.level <= logger.Info {
		// 过滤掉GORM的内部回调信息
		if len(data) > 0 {
			if str, ok := data[0].(string); ok && (str == "gorm:create" || str == "gorm:update" || str == "gorm:delete" || str == "gorm:query") {
				return // 跳过GORM内部回调信息
			}
		}
		logInfo("CLICKHOUSE INFO: "+msg, "data", data, "context", getContextInfo(ctx))
	}
}

func (c *ClickHouseLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if c.level >= logger.Warn {
		logWarn("CLICKHOUSE WARN: "+msg, "data", data, "context", getContextInfo(ctx))
	}
}

func (c *ClickHouseLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	logError("CLICKHOUSE ERROR: "+msg, nil, "data", data, "context", getContextInfo(ctx))
}

func (c *ClickHouseLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()
	
	// 构建基础信息
	baseInfo := map[string]interface{}{
		"time": float64(elapsed.Nanoseconds()) / 1e6,
		"rows": formatRows(rows),
		"sql":  sql,
		"context": getContextInfo(ctx),
	}
	
	switch {
	case err != nil:
		// 详细记录错误信息
		logError("CLICKHOUSE QUERY ERROR", err, 
			"time", baseInfo["time"],
			"rows", baseInfo["rows"], 
			"sql", baseInfo["sql"],
			"context", baseInfo["context"],
			"error_type", fmt.Sprintf("%T", err),
		)
	case elapsed.Milliseconds() > 5000: // ClickHouse慢查询阈值5秒
		logWarn("CLICKHOUSE SLOW QUERY",
			"time", baseInfo["time"],
			"rows", baseInfo["rows"],
			"sql", baseInfo["sql"], 
			"context", baseInfo["context"],
			"threshold", "5000ms",
		)
	//case c.level <= logger.Info:
	//	// 只在真正的Debug模式下才显示查询信息
	//	logInfo("CLICKHOUSE QUERY DEBUG",
	//		"time", baseInfo["time"],
	//		"rows", baseInfo["rows"],
	//		"sql", baseInfo["sql"],
	//		"context", baseInfo["context"],
	//	)
	}
}

// formatRows 格式化行数显示
func formatRows(rows int64) string {
	if rows == -1 {
		return "-"
	}
	return strconv.FormatInt(rows, 10)
}

// getContextInfo 获取上下文信息
func getContextInfo(ctx context.Context) map[string]interface{} {
	info := make(map[string]interface{})
	
	if ctx == nil {
		info["status"] = "nil"
		return info
	}
	
	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		info["status"] = "cancelled"
		info["error"] = ctx.Err().Error()
	default:
		info["status"] = "active"
	}
	
	// 获取截止时间
	if deadline, ok := ctx.Deadline(); ok {
		info["deadline"] = deadline.Format("2006-01-02 15:04:05")
		info["timeout"] = time.Until(deadline).String()
	}
	
	return info
}

// 统一的日志记录函数
func logInfo(msg string, args ...interface{}) {
	if Logger != nil {
		Logger.Infow(msg, args...)
	} else {
		log.Printf("[INFO] %s %v", msg, args)
	}
}

func logWarn(msg string, args ...interface{}) {
	if Logger != nil {
		Logger.Warnw(msg, args...)
	} else {
		log.Printf("[WARN] %s %v", msg, args)
	}
}

func logError(msg string, err error, args ...interface{}) {
	if Logger != nil {
		if err != nil {
			allArgs := append([]interface{}{"error", err.Error()}, args...)
			Logger.Errorw(msg, allArgs...)
		} else {
			Logger.Errorw(msg, args...)
		}
	} else {
		if err != nil {
			log.Printf("[ERROR] %s: %v %v", msg, err, args)
		} else {
			log.Printf("[ERROR] %s %v", msg, args)
		}
	}
}
