/*
 * Copyright (c) 2021.
 */

package tools

import (
"bytes"
"fmt"
	"golang.org/x/text/unicode/bidi"
	"strings"
)

var UChar600 = [33]string{"ت", "پ", "ب", "ر", "د", "چ", "ج",
	"ز", "ف", "ق", "ك", "ش", "گ", "س", "ڭ", "ن", "م", "ۋ", "ل", "خ",
	"غ", "ژ", "ي", "ا", "ە", "و", "ۈ", "ۆ", "ۇ", "ې", "ى", "ھ", "ئ"};

var UCharExB = [33]string{"ﺗ", "ﭘ", "ﺑ", "ﺭ", "ﺩ", "ﭼ", "ﺟ",
	"ﺯ", "ﻓ", "ﻗ", "ﻛ", "ﺷ", "ﮔ", "ﺳ", "ﯕ", "ﻧ", "ﻣ", "ﯞ", "ﻟ", "ﺧ",
	"ﻏ", "ﮊ", "ﻳ", "ﺍ", "ﻩ", "ﻭ", "ﯛ", "ﯙ", "ﯗ", "ﯦ", "ﯨ", "ﮬ", "ﺋ"};

var UCharExO = [33]string{"ﺘ", "ﭙ", "ﺒ", "ﺮ", "ﺪ", "ﭽ", "ﺠ",
	"ﺰ", "ﻔ", "ﻘ", "ﻜ", "ﺸ", "ﮕ", "ﺴ", "ﯖ", "ﻨ", "ﻤ", "ﯟ", "ﻠ", "ﺨ",
	"ﻐ", "ﮋ", "ﻴ", "ﺎ", "ﻪ", "ﻮ", "ﯜ", "ﯚ", "ﯘ", "ﯧ", "ﯩ", "ﮭ", "ﺌ"};

var UCharExA = [33]string{"ﺖ", "ﭗ", "ﺐ", "ﺮ", "ﺪ", "ﭻ", "ﺞ",
	"ﺰ", "ﻒ", "ﻖ", "ﻚ", "ﺶ", "ﮓ", "ﺲ", "ﯔ", "ﻦ", "ﻢ", "ﯟ", "ﻞ", "ﺦ",
	"ﻎ", "ﮋ", "ﻲ", "ﺎ", "ﻪ", "ﻮ", "ﯜ", "ﯚ", "ﯘ", "ﯥ", "ﻰ", "ﮫ", "ﺌ"};

var UCharExY = [33]string{"ﺕ", "ﭖ", "ﺏ", "ﺭ", "ﺩ", "ﭺ", "ﺝ",
	"ﺯ", "ﻑ", "ﻕ", "ﻙ", "ﺵ", "ﮒ", "ﺱ", "ﯓ", "ﻥ", "ﻡ", "ﯞ", "ﻝ", "ﺥ",
	"ﻍ", "ﮊ", "ﻱ", "ﺍ", "ﻩ", "ﻭ", "ﯛ", "ﯙ", "ﯗ", "ﯤ", "ﻯ", "ﮪ", "ﺋ"};
var SOL_KOL = [33]string{"ت", "ئ", "خ", "چ", "ج", "پ", "ب", "س",
	"ش", "غ", "ف", "ق", "ك", "گ", "ڭ", "ل", "م", "ن", "ھ", "ې", "ى",
	"ي"};
var NUM = [10]string{"0","1","2","3","4","5","6","7","8","9"};

type UyBasesAndEx struct {

}

func (receiver UyBasesAndEx) ToEx(src string)string {
	if len(src)>0 {
		var exWord bytes.Buffer
		var indx2 int;
		var indx3 int;
		var c1 rune;
		var c2 rune;
		var c3 rune;
		var c1Ex rune;
		var c2Ex rune;

		tmp := []rune(src)

		if len(tmp)==1 {
			index := receiver.GetIndex(UChar600, tmp[0])
			if  index!= -1{
				return UCharExY[index]
			}else{
				return src
			}
		}
		if len(tmp)==2 {
			c1 = tmp[0]
			c2 = tmp[1]
			if receiver.GetIndex(SOL_KOL, c1) > -1 {
				c1Ex = []rune(receiver.GetExChar(c1, BASH))[0];
				c2Ex = []rune(receiver.GetExChar(c2, AHIR))[0];
				return ""+string(c1Ex) + string(c2Ex);
			}
		}
		c1 = tmp[0];
		c1Ex = []rune(receiver.GetExChar(c1, BASH))[0];
		exWord.WriteRune(c1Ex);
		for i := 1; i < len(tmp) -1; i++ {
			c1 = tmp[i-1]
			c2 = tmp[i]
			c3 = tmp[i+1]
			indx3 = receiver.GetIndex(UChar600, c3)
			indx2 = receiver.GetIndex(UChar600, c2)
			if indx2 > -1 {
				if receiver.GetIndex(SOL_KOL, c1) > -1 {
					if indx3 > -1 {
						c2Ex = []rune(receiver.GetExChar(c2, OTTURA))[0];
					} else {
						c2Ex = []rune(receiver.GetExChar(c2, AHIR))[0];
					}
				} else {
					if indx3 > -1 {
						c2Ex = []rune(receiver.GetExChar(c2, BASH))[0]
					} else {
						c2Ex = []rune(receiver.GetExChar(c2, YALGHUZ))[0]
					}
				}
			} else{
				c2Ex = c2;
			}
			exWord.WriteRune(c2Ex);

		}

		if receiver.GetIndex(SOL_KOL,tmp[len(tmp)-2]) > -1 {
			c2Ex = []rune(receiver.GetExChar(tmp[len(tmp)-1], AHIR))[0]
		} else {
			c2Ex = []rune(receiver.GetExChar(tmp[len(tmp)-1], YALGHUZ))[0]
		}
		exWord.WriteRune(c2Ex);
		return receiver.ProLAHAMZE(exWord.String());
	}
	return "";
}

type UyHarpType int32

const (
	BASH      UyHarpType = 0
	OTTURA      UyHarpType = 1
	AHIR      UyHarpType = 2
	YALGHUZ      UyHarpType = 3
)
func (receiver UyBasesAndEx) GetExChar( r rune, k UyHarpType) string {
	indx := receiver.GetIndex(UChar600, r);
	if indx > -1 {
		switch (k) {
		case BASH:
			return UCharExB[indx];
		case OTTURA:
			return UCharExO[indx];
		case AHIR:
			return UCharExA[indx];
		case YALGHUZ:
			return UCharExY[indx];
		}
	}
	return string(r)
}
func (receiver UyBasesAndEx) GetIndex(char600 [33]string, r rune) int {
	var s = string(r)
	for index, str := range char600 {
		if str == s {
			return index
		}
	}
	return -1;
}

func (receiver UyBasesAndEx) ProLAHAMZE(src string) string {
	tmp := []rune(src)
	var sb bytes.Buffer
	var tmpi string;
	var tmpi1 string;
	for i := 0; i < len(tmp)-1; i++ {
		tmpi = string(tmp[i])
		tmpi1 = string(tmp[i+1])
		if tmpi=="ﻟ"&&tmpi1=="ﺎ" {
			sb.WriteString("ﻻ");
			i++;
		}else if tmpi=="ﻠ"&&tmpi1=="ﺎ" {
			sb.WriteString("ﻼ");
			i++;
		}else{
			sb.WriteRune(tmp[i]);
		}
	}
	if len(tmp)<2 {
		sb.WriteString(src);
	}else{
		if string(tmp[len(tmp)-2])=="ﻟ"&&string(tmp[len(tmp)-1])=="ﺎ" {
		}else if string(tmp[len(tmp)-2])=="ﻠ"&&string(tmp[len(tmp)-1])=="ﺎ"{
		}else{
			sb.WriteRune(tmp[len(tmp)-1])
		}
	}
	return sb.String()
}
func (receiver UyBasesAndEx) IsUyghurRune( r rune) bool {
	var s = string(r)
	for i := 0; i < 33; i++ {
		if strings.Compare(s,UCharExB[i])==0 {
			return true
		}
		if strings.Compare(s,UCharExA[i])==0 {
			return true
		}
		if strings.Compare(s,UCharExO[i])==0 {
			return true
		}
		if strings.Compare(s,UCharExY[i])==0 {
			return true
		}
	}
	return false
}
func (receiver UyBasesAndEx) IsNum( r rune) bool {
	var s = string(r)
	fmt.Println(s)
	for i := 0; i < 10; i++ {
		if strings.Compare(s,NUM[i])==0 {
			return true
		}
	}
	return false
}

func (receiver UyBasesAndEx)  GetReorderedText(text string) string {
	// 创建 Bidi 对象
	bidiPara := bidi.Paragraph{}
	var lineBuffer bytes.Buffer
	// 设置要处理的文本
	bidiPara.SetString(text)

	var ordering,_ = bidiPara.Order()
	var numRuns = ordering.NumRuns()
	for i := numRuns -1; i >= 0; i-- {
		run := ordering.Run(i)
		if run.Direction()== bidi.RightToLeft {
			lineBuffer.WriteString(receiver.Reverse(run.String()))
		}else{
			lineBuffer.WriteString(run.String())
		}
	}
	return strings.Trim(lineBuffer.String()," ")
}
func (receiver UyBasesAndEx) Reverse(s string) string {
	r := []rune(s)
	for i, j := 0, len(r)-1; i < j; i, j = i+1, j-1 {
		r[i], r[j] = r[j], r[i]
	}
	return string(r)
}