/*
*
@author: captain
@since: 2022/9/5
@desc:
*
*/
package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"math"
	"mulazim-api/configs"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/silenceper/wechat/v2"
	"github.com/silenceper/wechat/v2/cache"
	"github.com/silenceper/wechat/v2/credential"
	miniConfig "github.com/silenceper/wechat/v2/miniprogram/config"
	"github.com/silenceper/wechat/v2/miniprogram/qrcode"
	offConfig "github.com/silenceper/wechat/v2/officialaccount/config"
)

//
// ReadAccessToken
//  @Description: 获取微信AccessToken
//  @author: Captain
//  @Time: 2022-09-06 12:09:35
//  @param c *gin.Context
//  @return string 操作成功返回AccessToken ，失败则返回空字符串
//  @return error	操作成功时为nil，否则返回错误信息
//

var redisOpts = &cache.RedisOpts{
	Host:        configs.MyApp.RedisIp,
	Password:    configs.MyApp.RedisPassword,
	Database:    configs.MyApp.RedisDB,
	MaxActive:   10,
	MaxIdle:     10,
	IdleTimeout: 60, //second
}
var wc *wechat.Wechat

// ReadAccessToken
//
//	@Description: 获取微信AccessToken，并把它缓存在redis（redis KEY:gowechat_officialaccount__access_token_wxdc256e0bac869579）
//	@author: Captain
//	@Time: 2022-09-06 17:24:26
//	@param c *gin.Context
//	@return string 如果操作成功返回AccessToken，否则返回空字符串
//	@return error 操作成功时为nil，否则返回错误信息
func ReadAccessToken(c *gin.Context) (string, error) {

	wc = wechat.NewWechat()
	redisCache := cache.NewRedis(c, redisOpts)
	cfg := &offConfig.Config{
		AppID:     configs.MyApp.WechatAppID,
		AppSecret: configs.MyApp.WechatAppSecret,
		Cache:     redisCache,
	}
	officialAccount := wc.GetOfficialAccount(cfg)
	accessToken, err := officialAccount.GetAccessToken()

	if err != nil {
		return "", err
	}
	return accessToken, nil
}

// GetWXMiniQrCode
//
//	@Description: 获取配送员发展新客户的小程序二维码,并把它保存在服务器
//	@author: Captain
//	@Time: 2022-09-06 17:20:39
//	@param c *gin.Context
//	@param shipperID int 配送员编号
//	@param fileFullPath string 小程序二维码图片保存路径
//	@return string 如果操作成功返回二维码图片保存路径，否则返回空字符串
//	@return error 操作成功时为nil，否则返回错误信息
func GetWXMiniQrCode(c *gin.Context, shipperID int, fileFullPath string) (string, error) {
	wc = wechat.NewWechat()
	redisCache := cache.NewRedis(c, redisOpts)

	miniCFG := &miniConfig.Config{
		AppID:     configs.MyApp.WechatAppID,
		AppSecret: configs.MyApp.WechatAppSecret,
		Cache:     redisCache,
	}
	mini := wc.GetMiniProgram(miniCFG)
	qr := mini.GetQRCode()
	//ss, _ := qr.GetAccessToken()
	//fmt.Println("qr.GetAccessToken:" + ss)
	var coderParams qrcode.QRCoder
	coderParams.Path = "pages/index/index?shipper_id=" + strconv.Itoa(shipperID)
	coderParams.Scene = "shipperId_" + strconv.Itoa(shipperID)
	coderParams.Width = 430
	response, err := RetryWechatIfAccessTokenInvalid(func() (response []byte, err error) {
		return qr.GetWXACodeUnlimit(coderParams)
	}, c, configs.MyApp.WechatAppID)

	if err != nil {
		return "", err
	}
	err1 := os.WriteFile(fileFullPath, response, 0644)
	if err1 != nil {
		log.Println(err1)
		return "", err1
	}
	return fileFullPath, nil
}

// RetryWechatIfAccessTokenInvalid
//
//	@Description: 如果微信AccessToken无效，则更新
//	@author: Alimjan
//	@Time: 2022-09-07 16:20:23
//	@param f func() (response []byte, err error)
//	@return response []byte
//	@return err error
func RetryWechatIfAccessTokenInvalid(f func() (response []byte, err error), c context.Context, appID string) (response []byte, err error) {
	for i := 0; i < 3; i++ {
		response, err = f()
		if err != nil {
			errStr := err.Error()
			if strings.Contains(errStr, "40001") {
				//AccessToken过期 要刷新
				accessTokenCacheKey := fmt.Sprintf("%s_access_token_%s", credential.CacheKeyMiniProgramPrefix, appID)
				println(accessTokenCacheKey)
				redisHelper := GetRedisHelper()
				redisHelper.Del(c, accessTokenCacheKey)
			} else {
				return response, err
			}
		} else {
			return response, err
		}
	}
	return response, err
}

// GetWXMiniQrCodeForMerchant
//
//	@Description: 获取餐厅员工绑定小程序二维码,并把它保存在服务器
//	@author: Captain
//	@Time: 2022-11-03 10:26:10
//	@param c *gin.Context
//	@param codeParam qrcode.QRCoder 生成小程序码的参数
//	@param fileFullPath string  二维码图片保存路径
//	@return string 操作成功返回二维码图片保存路径，失败返回空字符串
//	@return error 操作成功返回nil,操作失败返回错误信息
func GetWXMiniQrCodeForMerchant(c *gin.Context, codeParam qrcode.QRCoder, fileFullPath string) (string, error) {

	wc = wechat.NewWechat()
	redisCache := cache.NewRedis(c, redisOpts)

	miniCFG := &miniConfig.Config{
		AppID:     configs.MyApp.WechatAppID,
		AppSecret: configs.MyApp.WechatAppSecret,
		Cache:     redisCache,
	}
	mini := wc.GetMiniProgram(miniCFG)
	qr := mini.GetQRCode()
	//ss, _ := qr.GetAccessToken()
	//fmt.Println("qr.GetAccessToken:" + ss)
	response, err := RetryWechatIfAccessTokenInvalid(func() (response []byte, err error) {
		return qr.GetWXACodeUnlimit(codeParam)
	}, c, configs.MyApp.WechatAppID)

	if err != nil {
		return "", err
	}
	err1 := os.WriteFile(fileFullPath, response, 0644)
	if err1 != nil {
		log.Println(err1)
		return "", err1
	}
	return fileFullPath, nil
}

// 向客户发送订单取消微信通知消息
func SendCancelOrderMessage(orderId string, storeName string, totalFee int, cancelReason string, openId string) {
	go func() {

		count := 3 //重试次数

		character_string3 := make(map[string]interface{})
		character_string3["value"] = orderId

		thing1 := make(map[string]interface{})
		thing1["value"] = CutStringEndWithSpace(storeName, 40)

		amount13 := make(map[string]interface{})
		amount13["value"] = totalFee

		thing2 := make(map[string]interface{})
		thing2["value"] = "زاكاس بىكار قىلىنىدى"

		thing7 := make(map[string]interface{})
		thing7["value"] = cancelReason

		dataMap := make(map[string]interface{})
		dataMap["character_string3"] = character_string3
		dataMap["thing1"] = thing1

		dataMap["amount13"] = amount13
		dataMap["thing2"] = thing2
		dataMap["thing7"] = thing7

		data := make(map[string]interface{})

		data["touser"] = openId
		data["template_id"] = "vTnhpYUtUwRi_V0VGiTc_7Gqsf7zjJvjjp_8eUwDx40"
		data["page"] = "/pages/index/index?orderId=" + orderId
		data["miniprogram_state"] = "formal"
		data["data"] = dataMap

		for i := 0; i < count; i++ {

			access_token := GetAccessToeknForWechatMessage()
			url := "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" + access_token
			rtn := HttpPost(url, data, 5*time.Second)
			if len(rtn) == 0 {
				break
			}
			ercs := [3]int{
				40001, 40014, 42001,
			}
			var mp map[string]interface{}
			json.Unmarshal([]byte(rtn), &mp)
			errCode := mp["errcode"].(string)
			if len(errCode) > 0 {
				ec, _ := strconv.ParseFloat(errCode, 32)
				flag := false
				for j := 0; j < len(ercs); j++ {
					if ercs[i] == int(math.Abs(ec)) {
						flag = true
					}
				}
				if flag {
					break
				} else {
					//refresh token
					GetAccessToeknForWechatMessage()
				}
			}

		}
	}()
}

// 去掉多余的空位置和一些特殊符号
func CutStringEndWithSpace(inStr string, length int) string {
	//中文标点

	charCN := "。、！？：；﹑•＂…‘’“”〝〞∕¦‖—　〈〉﹞﹝「」‹›〖〗】【』『〕〔》《﹐¸﹕︰﹔！¡？¿﹖﹌﹏﹋＇´ˊˋ―﹫︳︴¯＿￣﹢﹦﹤‐­˜﹟﹩﹠﹪﹡﹨﹍﹉﹎﹊ˇ︵︶︷︸︹︿﹀︺︽︾ˉ﹁﹂﹃﹄︻︼（）"
	charEN := "().`~!@#$%^&*+=_><?\\/,}{[]|\\"

	pattern := [3]string{
		"/[" + charEN + "]/i", // 英文标点符号
		"/[" + charCN + "]/u", // 中文标点符号
		"/[ ]{2,}/",
	}
	for i := 0; i < len(pattern); i++ {
		re := regexp.MustCompile(pattern[i])
		inStr = re.ReplaceAllString(inStr, " ")
	}
	inStr = strings.Trim(inStr, " ")
	if len(inStr) > length {
		inStr = inStr[0:length]
		po := strings.IndexAny(inStr, " ")
		if po > 0 {
			inStr = inStr[0:po]
		}
	}
	return inStr
}

// 微信通知消息 获取access_token
func GetAccessToeknForWechatMessage() string {
	cacheKey := "access_token"
	accessToken := ""
	redisHelper := GetRedisHelper()
	exists, _ := redisHelper.Exists(redisHelper.Context(), cacheKey).Result()
	if exists == 0 {
		accessToken, _ = redisHelper.Get(redisHelper.Context(), cacheKey).Result()

	} else {
		url := "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + configs.MyApp.WechatAppID + "&secret=" + configs.MyApp.WechatAppSecret
		client := &http.Client{
			Timeout: 5 * time.Second,
		}
		resp, err := client.Get(url)
		if err != nil {
			fmt.Println("access_token 获取失败:", err)
		}
		defer resp.Body.Close()

		body, _ := ioutil.ReadAll(resp.Body)

		type Act struct {
			AccessToken string `json:"access_token"`
		}
		var act Act
		json.Unmarshal([]byte(string(body)), &act)
		if len(act.AccessToken) > 0 {
			accessToken = act.AccessToken
			redisHelper.Set(redisHelper.Context(), cacheKey, accessToken, 60*time.Minute)
		}
	}
	return accessToken

}
