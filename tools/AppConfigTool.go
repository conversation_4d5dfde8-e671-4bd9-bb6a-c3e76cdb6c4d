package tools

import (
	"encoding/json"
	"fmt"
	"time"
)

type AppConfigTool struct {

}

// 控制app中的某个功能是否显示
type AppConfig struct {
	ID        int       `gorm:"column:id;primary_key"`
	State        int       `gorm:"column:state"`
	Key       string    `gorm:"column:key;NOT NULL"` // 配置名称
	Value     string    `gorm:"column:value"`        // 值
	OsType    string    `gorm:"column:os_type"`      // 操作系统,android,ios,all
	Version   string    `gorm:"column:version"`      // 应用版本,v 2.3.1
	CreatedAt time.Time `gorm:"column:created_at"`   // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"`   // 修改时间
	DeletedAt time.Time `gorm:"column:deleted_at"`   // 删除时间
}

func (m *AppConfig) TableName() string {
	return "app_config"
}
//new appconfigtool
func NewAppConfigTool() *AppConfigTool {
	return &AppConfigTool{}
}
//
// GetConfigValue
//  @Description: 获取App配置,key必须全局唯一,对一些版本做特殊处理
//  @receiver receiver
//  @param key
//  @param osType
//  @param version
//  @param defaultVal
//  @return string
//
func (receiver AppConfigTool) GetConfigValue(key string,osType string,version string ,defaultVal string )string  {
	redisKey := fmt.Sprintf("app_config_%s_%s_%s",osType,version,key)
	RedisDel2(redisKey)
	rtnJson := Remember2(redisKey,time.Minute*5,func()interface{}{
		db := Db
		config := AppConfig {
		}
		db.Model(&config).Where("`key` = ? and os_type = ? and version = ? and state = 1 ",key,osType,version).First(&config)
		if config.ID == 0 {
			return defaultVal
		}
		return config.Value
	})
	var rtn string
	err := json.Unmarshal([]byte(rtnJson),&rtn)
	if err!=nil {
		return defaultVal
	}
	if rtn == "" {
		return defaultVal
	}
	return rtn
}
