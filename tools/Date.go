package tools

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

// 全局实例，方便直接使用
var DateTool = NewDateTools()

// DateTools 日期工具结构体
type DateTools struct{}

// ParsedDate 解析后的日期结构体
type ParsedDate struct {
	Year   int
	Month  int
	Day    int
	Hour   int
	Minute int
	Second int
}

// ToString 返回标准格式字符串 "YYYY-MM-DD HH:MM:SS"
func (p *ParsedDate) ToString() string {
	return fmt.Sprintf("%04d-%02d-%02d %02d:%02d:%02d", p.Year, p.Month, p.Day, p.Hour, p.Minute, p.Second)
}

// ToTime 将ParsedDate转换为time.Time类型
func (p *ParsedDate) ToTime() time.Time {
	return time.Date(p.Year, time.Month(p.Month), p.Day, p.Hour, p.Minute, p.Second, 0, time.Local)
}

// NewDateTools 创建DateTools实例
func NewDateTools() *DateTools {
	return &DateTools{}
}

// GetWeekStartAndEndTime 传递一个年月,第几周 例如: 2025-09  1  返回 这个周的开始和结束时间
// 每月第一周从该月第一个周一开始，最后一周到该月最后一个周日结束
func (d *DateTools) GetWeekStartAndEndTime(year int, month int, week int) (string, string) {
	firstDay := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
	lastDay := firstDay.AddDate(0, 1, -1)
	
	// 找到该月第一个周一
	firstMonday := firstDay
	for firstMonday.Weekday() != time.Monday && !firstMonday.After(lastDay) {
		firstMonday = firstMonday.AddDate(0, 0, 1)
	}
	if firstMonday.After(lastDay) { return "", "" }
	
	// 计算第N周的开始日期（周一）
	weekStartDate := firstMonday.AddDate(0, 0, (week-1)*7)
	if weekStartDate.After(lastDay) { return "", "" }
	
	// 计算第N周的结束日期（周日）并格式化
	weekEndDate := weekStartDate.AddDate(0, 0, 6)
	return weekStartDate.Format("2006-01-02 15:04:05"), 
		   time.Date(weekEndDate.Year(), weekEndDate.Month(), weekEndDate.Day(), 23, 59, 59, 0, time.Local).Format("2006-01-02 15:04:05")
}

// GetMonthWeeksMaxCount 获取某月的总周数
// 每月第一周从该月第一个周一开始，只要周的开始日期（周一）在当月内就算一周
func (d *DateTools) GetMonthWeeksMaxCount(year int, month int) int {
	firstDay := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
	lastDay := firstDay.AddDate(0, 1, -1)
	
	// 找到该月第一个周一
	firstMonday := firstDay
	for firstMonday.Weekday() != time.Monday && !firstMonday.After(lastDay) {
		firstMonday = firstMonday.AddDate(0, 0, 1)
	}
	if firstMonday.After(lastDay) { return 0 }
	
	// 计算周数
	weekCount := 0
	for current := firstMonday; !current.After(lastDay) && current.Month() == time.Month(month); current = current.AddDate(0, 0, 7) {
		weekCount++
	}
	
	return weekCount
}

// ParseDateString 解析不同格式的日期字符串，返回ParsedDate结构体
// 支持格式: "2025", "2025-09", "2025-09-01", "2023-02-02 12", "2023-02-02 12:09", "2023-02-02 12:01:34"
func (d *DateTools) ParseDateString(dateStr string) *ParsedDate {
	result := &ParsedDate{Year: 1970, Month: 1, Day: 1, Hour: 0, Minute: 0, Second: 0}
	dateStr = strings.TrimSpace(dateStr)
	
	// 辅助函数：解析并验证范围
	parseInt := func(s string, min, max int) int {
		if v, err := strconv.Atoi(strings.TrimSpace(s)); err == nil && v >= min && v <= max {
			return v
		}
		return -1
	}
	
	// 分离日期和时间
	parts := strings.SplitN(dateStr, " ", 2)
	
	// 解析日期部分 (年-月-日)
	if dateParts := strings.Split(parts[0], "-"); len(dateParts) > 0 {
		if v := parseInt(dateParts[0], 1, 9999); v != -1 { result.Year = v }
		if len(dateParts) > 1 { if v := parseInt(dateParts[1], 1, 12); v != -1 { result.Month = v } }
		if len(dateParts) > 2 { if v := parseInt(dateParts[2], 1, 31); v != -1 { result.Day = v } }
	}
	
	// 解析时间部分 (时:分:秒)
	if len(parts) > 1 {
		if timeParts := strings.Split(parts[1], ":"); len(timeParts) > 0 {
			if v := parseInt(timeParts[0], 0, 23); v != -1 { result.Hour = v }
			if len(timeParts) > 1 { if v := parseInt(timeParts[1], 0, 59); v != -1 { result.Minute = v } }
			if len(timeParts) > 2 { if v := parseInt(timeParts[2], 0, 59); v != -1 { result.Second = v } }
		}
	}
	
	return result
}

// GetWeekOfMonth 传递年月日，返回这个日期是当月的第几周
// 如果日期不在任何周内（比如在第一个周一之前），返回0
func (d *DateTools) GetWeekOfMonth(year int, month int, day int) int {
	targetDate := time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.Local)
	return d.GetWeekOfMonthByTime(targetDate)
}

// GetWeekOfMonthByTime 传递time.Time类型，返回这个日期是当月的第几周
// 每月第一周从该月第一个周一开始，如果日期在第一个周一之前，返回0
func (d *DateTools) GetWeekOfMonthByTime(date time.Time) int {
	year := date.Year()
	month := int(date.Month())
	
	// 获取当月第一天和最后一天
	firstDay := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
	lastDay := firstDay.AddDate(0, 1, -1)
	
	// 如果目标日期不在当月，返回0
	if date.Before(firstDay) || date.After(lastDay) {
		return 0
	}
	
	// 找到该月第一个周一
	firstMonday := firstDay
	for firstMonday.Weekday() != time.Monday && !firstMonday.After(lastDay) {
		firstMonday = firstMonday.AddDate(0, 0, 1)
	}
	
	// 如果当月没有周一，或者目标日期在第一个周一之前，返回0
	if firstMonday.After(lastDay) || date.Before(firstMonday) {
		return 0
	}
	
	// 计算目标日期到第一个周一的天数差
	daysDiff := int(date.Sub(firstMonday).Hours() / 24)
	
	// 计算是第几周（从1开始）
	weekNumber := (daysDiff / 7) + 1
	
	return weekNumber
}


