package tools

import (
	"github.com/gin-gonic/gin"
	"strconv"
)

// Pagination
// @Description: 分页信息
type Pagination struct {
	Page  int `json:"page"`
	Limit int `json:"limit"`
}

// 默认分页值
const (
	DefaultPage  = 1
	DefaultLimit = 10
	MaxLimit     = 10000
)

// GetPagination
//
//	@Author: YaKupJan
//	@Date: 2024-09-14 13:25:33
//	@Description: 从请求中提取分页参数
//	@param c
//	@return Pagination
func GetPagination(c *gin.Context) Pagination {
	page, err := strconv.Atoi(c.Query("page"))
	if err != nil || page < 1 {
		page = DefaultPage
	}

	limit, err := strconv.Atoi(c.Query("limit"))
	if err != nil || limit < 1 {
		limit = DefaultLimit
	} else if limit > MaxLimit {
		limit = MaxLimit
	}

	return Pagination{
		Page:  page,
		Limit: limit,
	}
}
