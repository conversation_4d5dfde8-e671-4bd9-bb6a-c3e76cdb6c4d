#!/bin/sh
#
# An example hook script to check the commit log message.
# Called by "git commit" with one argument, the name of the file
# that has the commit message.  The hook should exit with non-zero
# status after issuing an appropriate message if it wants to stop the
# commit.  The hook is allowed to edit the commit message file.
#
# To enable this hook, rename this file to "commit-msg".

# Uncomment the below to add a Signed-off-by line to the message.
# Doing this in a hook is a bad idea in general, but the prepare-commit-msg
# hook is more suited to it.
#
# SOB=$(git var GIT_AUTHOR_IDENT | sed -n 's/^\(.*>\).*$/Signed-off-by: \1/p')
# grep -qs "^$SOB" "$1" || echo "$SOB" >> "$1"

# This example catches duplicate Signed-off-by lines.

commit_msg_file=$1
commit_msg=$(cat $commit_msg_file)

# Define a regex pattern to match the required commit message format
COMMIT_MSG_PATTERN="^(task|fix) #DEVM-[0-9]+ .+"

# Check if the commit message matches the required format
if ! echo "$commit_msg" | grep -qE "$COMMIT_MSG_PATTERN"; then
    echo "git commit 备注格式错误、正确格式: 'task #DEVM-<number> ...' or 'fix #DEVM-<number> ...'"
    exit 1
fi
