package helper

import (
	"regexp"
	"strconv"
	"strings"
	"testing"
)

const PHP_BASE_URL = "http://localhost:80"

// DecodeUnicodeEscape 将包含Unicode转义序列的字符串转换为实际的Unicode字符
func ConvertToUTF8(t *testing.T, src []byte) string {
	// DecodeUnicodeEscape 使用正则表达式解析并解码Unicode转义序列
	input := string(src)
	// 正则表达式匹配 \u 开头的转义序列
	re := regexp.MustCompile(`\\u([0-9a-fA-F]{4})`)
	matches := re.FindAllStringSubmatchIndex(input, -1)

	var result strings.Builder
	lastIndex := 0

	for _, match := range matches {
		// 写入未匹配的部分
		result.WriteString(input[lastIndex:match[0]])
		// 提取十六进制字符串并转换为整数
		hexStr := input[match[2]:match[3]]
		r, err := strconv.ParseInt(hexStr, 16, 32)
		if err != nil {
			t.Fatalf("failed to parse hex: %v", err)
			return ""
		}
		// 将整数转换为相应的Unicode字符并写入结果
		result.WriteRune(rune(r))
		// 更新lastIndex以跳过已处理的部分
		lastIndex = match[1]
	}
	// 添加剩余部分
	result.WriteString(input[lastIndex:])
	return result.String()
}
