package helper

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"mime/multipart"
	"mulazim-api/models"
	"mulazim-api/mygin"
	"mulazim-api/tools"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
)

var CmsCookieKey = "mulazim_two_point_zero="
var CmsCookieContent = CmsCookieKey + "eyJpdiI6Iml0TUxjU21ZS2hyN2ZrbkxUZGdrcEE9PSIsInZhbHVlIjoidEl4bzJCYkdRXC9DNVlrZk9kOG1FaVBWVzVOT2lcLzh2dnUzazJTY1lua0E2aUx5SW41S1JjUTZDMzNHSzMyNURON2ZwV0lcL2Q3bDNlMnNkcjNqUExoS3c9PSIsIm1hYyI6IjIxYzA3ZTk3OTNmNzJkODVkMzE5ZDE0NTQzMzZkYTMwODYxMGNmNmNlNjE1ZDQ4N2Q2ODQ2MzYzZDljNWE4N2MifQ%3D%3D; expires=Tue, 04-Mar-2025 05:51:33 GMT; Max-Age=7200; path=/; httponly"

func CmsLogin(name string) {
	var admin models.Admin
	tools.GetDB().Where("name = ?", name).Where("type < ?", 5).First(&admin)
	redisData := fmt.Sprintf(`s:424:"a:7:{s:18:"mulazim-cms-locale";s:5:"ug_CN";s:6:"_token";s:40:"cTtDucmsEPqNiwLojSMi0GB5vNiDy2GT5nmVNGB0";s:3:"url";a:0:{}s:9:"_previous";a:1:{s:3:"url";s:62:"https://cms.spec.almas.biz/ug/address/city/list-for-select-box";}s:5:"flash";a:2:{s:3:"old";a:0:{}s:3:"new";a:0:{}}s:56:"login_dashboard_59ba36addc2b2f9401580f014c7f58ea4e30989d";i:%d;s:9:"_sf2_meta";a:3:{s:1:"u";i:**********;s:1:"c";i:**********;s:1:"l";s:1:"0";}}";`, admin.ID)
	redis := tools.GetRedisCmsHelper()
	redis.Set(context.Background(),"laravel:8414906b6a6e241fec23124f561b3bbcb0af4328", redisData,0)
}
// func SendGinPost(url string, data url.Values, token string, serial string) *httptest.ResponseRecorder {
func SendGinPost(url string, data url.Values, token string, serial string, headers map[string]interface{}) *httptest.ResponseRecorder {
	router := mygin.InitGinWeb()
	w := httptest.NewRecorder()

	b := strings.NewReader(data.Encode())

	req, _ := http.NewRequest("POST", url, b)
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("searialNumber", serial)
	req.Header.Add("Authorization", "Bearer "+token)
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("Cookie","mulazim_two_point_zero="+token)
	if tools.ToInt(data.Get("user_type")) == 1 {
		req.Header.Add("from", "mini")
	}
	if tools.ToInt(data.Get("user_type")) == 2 {
		req.Header.Add("from", "cms")
	}
	if tools.ToInt(data.Get("user_type")) == 3 {
		req.Header.Add("from", "api")
	}

	for k, v := range headers {
		req.Header.Add(k, tools.ToString(v))
	}

	router.ServeHTTP(w, req)
	return w
}

func SendGinPostJSON(url string, data interface{}, token string, serial string, headers map[string]interface{}) *httptest.ResponseRecorder {
	router := mygin.InitGinWeb()
	w := httptest.NewRecorder()

	jsonData, err := json.Marshal(data)
	if err != nil {
		tools.Logger.Error("Error marshaling JSON data: %v", err)
		return w
	}

	b := bytes.NewReader(jsonData)

	req, _ := http.NewRequest("POST", url, b)
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("serialNumber", serial) // 修正了这里的拼写错误
	req.Header.Add("Authorization", "Bearer "+token)
	req.Header.Add("Cookie","mulazim_two_point_zero="+token)
	if tools.ToInt(tools.ToString(data.(map[string]interface{})["user_type"])) == 1 {
		req.Header.Add("from", "mini")
	}
	if tools.ToInt(tools.ToString(data.(map[string]interface{})["user_type"])) == 2 {
		req.Header.Add("from", "cms")
	}
	if tools.ToInt(tools.ToString(data.(map[string]interface{})["user_type"])) == 3 {
		req.Header.Add("from", "api")
	}

	for k, v := range headers {
		req.Header.Add(k, tools.ToString(v))
	}

	router.ServeHTTP(w, req)
	return w
}
func SendGinPostWithJSON(url string, data interface{}, token string, serial string) *httptest.ResponseRecorder {
	// 初始化Gin Router
	router := mygin.InitGinWeb()
	w := httptest.NewRecorder()

	// 将数据编码为JSON
	body, err := json.Marshal(data)
	if err != nil {
		panic(err) // 错误处理，可以根据需求修改
	}

	// 创建请求
	req, _ := http.NewRequest("POST", url, bytes.NewReader(body))
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("searialNumber", serial)
	req.Header.Add("Authorization", "Bearer "+token)
	req.Header.Add("Cookie","mulazim_two_point_zero="+token)


	// 执行请求
	router.ServeHTTP(w, req)
	return w
}
func SendGinGet(url string, data url.Values, token string, serial string) *httptest.ResponseRecorder {
	router := mygin.InitGinWeb()
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", url+"?"+data.Encode(), nil)
	tools.Logger.Info(url + "?" + data.Encode())
	req.Header.Add("searialNumber", serial)
	req.Header.Add("Authorization", "Bearer "+token)
	req.Header.Add("Cookie", "mulazim_two_point_zero="+token)
	router.ServeHTTP(w, req)
	return w
}

func SendGinGetData(url string, data url.Values, token string, serial string) string {
	router := mygin.InitGinWeb()
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", url+"?"+data.Encode(), nil)
	// tools.Logger.Info(url + "?" + data.Encode())
	req.Header.Add("searialNumber", serial)
	req.Header.Add("Authorization", "Bearer "+token)
	req.Header.Add("Cookie", "mulazim_two_point_zero="+token)
	router.ServeHTTP(w, req)
	resStr := w.Body.String()
	resMap, _ := tools.StringToMap(resStr)
	goDataStr := tools.MapToString(resMap["data"].(map[string]interface{}))
	return goDataStr
}

func SendHttpPost(url string, data url.Values, token string, serial string) []byte {
	method := "POST"
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)

	for k, v := range data {
		writer.WriteField(k, v[0])
	}

	writer.Close()

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		tools.Logger.Info(err.Error())
		return nil
	}


	req.Header.Add("Authorization", "Bearer "+token)
	req.Header.Add("osType", "1")
	req.Header.Add("terminalId", "8")
	req.Header.Add("appVersion", "1.0.2")
	req.Header.Add("Accept-Language", "en;q=1")
	req.Header.Add("screenSize", "414*736")
	req.Header.Add("phoneBrand", "apple")
	req.Header.Add("User-Agent", "Dinner/1.0 (iPhone; iOS 9.3; Scale/3.00)")
	req.Header.Add("lang", "0")
	req.Header.Add("appBuild", "1")
	req.Header.Add("appTime", "1467103218.903170")
	req.Header.Add("searialNumber", "123456789")
	req.Header.Add("osVersion", "1.2")
	req.Header.Add("parentCategoryId", "1")
	req.Header.Add("Cookie","mulazim_two_point_zero="+token)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	res, err := client.Do(req)
	if err != nil {
		tools.Logger.Info(err.Error())
		return nil
	}

	defer res.Body.Close()

	body, _ := ioutil.ReadAll(res.Body)
	return body
}

func SendHttpGet(url string, data url.Values, token string, serial string) []byte {
	method := "GET"

	client := &http.Client{}
	req, _ := http.NewRequest(method, url+"?"+data.Encode(), nil)
	tools.Logger.Info(url + "?" + data.Encode())

	req.Header.Add("User-Agent", "Dinner/1.0 (iPhone; iOS 9.3; Scale/3.00)")
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/json; charset=utf-8")

	req.Header.Add("Authorization", "Bearer "+token)
	req.Header.Add("searialNumber", serial)

	res, _ := client.Do(req)
	defer res.Body.Close()

	body, _ := ioutil.ReadAll(res.Body)
	return body
}


func SendHttpGetData(url string, data url.Values, token string, serial string) string {
	method := "GET"

	client := &http.Client{}
	req, _ := http.NewRequest(method, url+"?"+data.Encode(), nil)
	tools.Logger.Info(url + "?" + data.Encode())

	req.Header.Add("User-Agent", "Dinner/1.0 (iPhone; iOS 9.3; Scale/3.00)")
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/json; charset=utf-8")

	req.Header.Add("Authorization", "Bearer "+token)
	req.Header.Add("searialNumber", serial)

	res, _ := client.Do(req)
	defer res.Body.Close()

	body, _ := ioutil.ReadAll(res.Body)
	resPHPMap, _ := tools.StringToMap(string(body))
	phpDataStr := tools.MapToString(resPHPMap["data"].(map[string]interface{}))
	return phpDataStr
}

func GetResCode(b string) int {
	var m map[string]interface{}
	err := json.Unmarshal([]byte(b), &m)
	if err != nil {
		tools.Logger.Info(err.Error())
		return 0
	}
	var rtn int
	switch m["status"].(type) {
	case int:
		rtn = m["status"].(int)
		break
	case float64:
		rtn = int(m["status"].(float64))
		break
	case float32:
		rtn = int(m["status"].(float32))
		break

	default:
		//fmt.Printf("出错了 %s\n", reflect.TypeOf(v))
		rtn = 0
		break
	}
	return rtn

}

func MapToUrlValues(params map[string]interface{}) url.Values {
	values := url.Values{}
	for k, v := range params {
		values.Set(k, tools.ToString(v))
	}
	return values
}
