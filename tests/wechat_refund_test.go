package tests

import (
	"encoding/json"
	"encoding/xml"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/resources"
	"mulazim-api/tools"
	"testing"
)

//
// TestWechatMiniRefund
//  @Description: 小程序支付退款
//  @author: Ali<PERSON>jan
//  @Time: 2023-07-22 12:56:07
//  @param t *testing.T
//
func TestWechatMiniRefund(t *testing.T) {
	if false {
		configName := "wechat_mini"
		wechatPayConfig := *configs.NewWechatPayConfig(configName)
		wechatPay := *tools.NewWechatPay(&wechatPayConfig)
		param := make(map[string]interface{})

		param["transaction_id"] = "4200002356202407033195888229"
		param["out_trade_no"] = "yardamqi000000002407031925501988"
		param["amount"] = 1000
		param["total"] =  1000
		rs, _ := wechatPay.Refund( param)
		fmt.Printf("rs:%v", rs)
		


		servicePhone := "400-1111-990"
		
		deliveryTime :=fmt.Sprintf("%s %s", "2024-07-04 18:15","[北京]")
		
		contentMap := map[string]interface{}{
			"peisongshijian" : deliveryTime,
			"jine" : tools.ToRound(1000/100,2),
			"sertel" : servicePhone,
		}
		dataType, _ := json.Marshal(contentMap)
		content := string(dataType)
		res,e :=tools.AliSmsSendWithResponse("***********", content, "Mulazim", "SMS_102525003")
		fmt.Printf("res:%v", res)
		fmt.Printf("error:%v", e)
	}

	xmlStr :=`
	<xml><appid><![CDATA[wxdc256e0bac869579]]></appid><bank_type><![CDATA[OTHERS]]></bank_type><cash_fee><![CDATA[9]]></cash_fee><fee_type><![CDATA[CNY]]></fee_type><is_subscribe><![CDATA[N]]></is_subscribe><mch_id><![CDATA[**********]]></mch_id><nonce_str><![CDATA[XfEiQSNpw5d9JI9oPhLTgXkbtu5sJ6Hw]]></nonce_str><openid><![CDATA[otf0J0UWB78rKgSHB92Q7smC12X0]]></openid><out_trade_no><![CDATA[yardamqi000000002407041625131995]]></out_trade_no><result_code><![CDATA[SUCCESS]]></result_code><return_code><![CDATA[SUCCESS]]></return_code><sign><![CDATA[0C678334EF986177998BD73B894AE4D4]]></sign><time_end><![CDATA[**************]]></time_end><total_fee>9</total_fee><trade_type><![CDATA[JSAPI]]></trade_type><transaction_id><![CDATA[4200002347202407048377148117]]></transaction_id></xml>
	`
	// 获取请求体数据
	var v2Notify resources.WechatPayResponse
	
	// 解析 XML 数据到结构体
	if err := xml.Unmarshal([]byte(xmlStr), &v2Notify); err != nil {
		tools.Logger.Error("err",err)
	}
	bts ,_:=json.Marshal(v2Notify)
	tools.Logger.Error("result",string(bts))

}
//
// TestWechatAppRefund
//  @Description: 用户端app 退款
//  @author: Alimjan
//  @Time: 2023-07-22 12:56:31
//  @param t *testing.T
//
func TestWechatAppRefund(t *testing.T) {

	configName := "wechat_api"
	wechatPayConfig := *configs.NewWechatPayConfig(configName)
	wechatPay := *tools.NewWechatPay(&wechatPayConfig)
	param := make(map[string]interface{})

	param["transaction_id"] = "4200001871202307229288164834"
	param["out_trade_no"] = "tasi0000000000002307221039351773"
	param["amount"] = 10
	param["total"] =  10
	rs, _ := wechatPay.Refund(param)
	fmt.Printf("rs:%v", rs)
}
//
// TestWechatQRCodeRefund
//  @Description: 配送员二维码支付
//  @author: Alimjan
//  @Time: 2023-07-22 12:55:52
//  @param t *testing.T
//
func TestWechatQRCodeRefund(t *testing.T) {
	configName := "wechat_shipper"
	wechatPayConfig := *configs.NewWechatPayConfig(configName)
	wechatPay := *tools.NewWechatPay(&wechatPayConfig)
	param := make(map[string]interface{})

	param["transaction_id"] = "4200001887202307226681517363"
	param["out_trade_no"] = "sms20230722104905104"
	param["amount"] = 10
	param["total"] =  10
	rs, _ := wechatPay.Refund(param)
	fmt.Printf("rs:%v", rs)
}
//
// TestWechatShipperAppRefund
//  @Description: 配送员客户端app 支付
//  @author: Alimjan
//  @Time: 2023-07-22 12:53:31
//  @param t *testing.T
//
func TestWechatShipperAppRefund(t *testing.T) {
	configName := "wechat_shipper_app"
	wechatPayConfig := *configs.NewWechatPayConfig(configName)
	wechatPay := *tools.NewWechatPay(&wechatPayConfig)
	param := make(map[string]interface{})

	param["transaction_id"] = "4200001868202307227610211673"
	param["out_trade_no"] = "sms20230722105330107"
	param["amount"] = 10
	param["total"] =  10
	rs, _ := wechatPay.Refund(param)
	fmt.Printf("rs:%v", rs)
}
//
// TestWechatShipperAppRefund
//  @Description: func h5
//  @author: Alimjan
//  @Time: 2023-07-22 13:00:50
//  @param t *testing.T
//
func TestWechatH5Refund(t *testing.T) {
	configName := "wechat_h5"
	wechatPayConfig := *configs.NewWechatPayConfig(configName)
	wechatPay := *tools.NewWechatPay(&wechatPayConfig)
	param := make(map[string]interface{})

	param["transaction_id"] = "4200001868202307227610211673"
	param["out_trade_no"] = "sms20230722105330107"
	param["amount"] = 10
	param["total"] =  10
	rs, _ := wechatPay.Refund( param)
	fmt.Printf("rs:%v", rs)
}

//
// TestWechatOfficialRefund
//  @Description: 公众号支付退款测试用例
//  @author: Alimjan
//  @Time: 2023-07-22 13:08:23
//  @param t *testing.T
//
func TestWechatOfficialRefund(t *testing.T) {
	configName := "wechat_wap"
	wechatPayConfig := *configs.NewWechatPayConfig(configName)
	wechatPay := *tools.NewWechatPay(&wechatPayConfig)
	param := make(map[string]interface{})

	param["transaction_id"] = "4200001864202307227867045710"
	param["out_trade_no"] = "sms20230722110644312"
	param["amount"] = 10
	param["total"] =  10
	rs, _ := wechatPay.Refund( param)

	tools.Logger.Error("rs:", rs)
}


