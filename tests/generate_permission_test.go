﻿package tests

import (
	"fmt"
	"mulazim-api/tools"
	"testing"
)


func TestPermission(t *testing.T) {

	// 添加策略
	// enforcer.AddPolicy("admin", "/data1", "read")
	// enforcer.AddPolicy("admin", "/data2", "write")

	// enforcer.AddPolicy("member", "/data1", "read")
	// enforcer.AddPolicy("test", "/test/test", "GET")

	// enforcer.AddGroupingPolicy("abu1","test")
	// enforcer.AddGroupingPolicy("abu1","test1")
	// enforcer.AddGroupingPolicy("abu1","test2")
	// enforcer.RemoveGroupingPolicy("abu1","test")
	permissions := tools.GetMerchantPermissions(13787)
	for _,v := range permissions {
		fmt.Println(v)
	}


	// removed, err := enforcer.RemoveFilteredGroupingPolicy(0, "abu1")
	// fmt.Println(removed, err)

	// 检查权限
    // a, err := enforcer.Enforce(tools.ToString(admin.ID), urlPath, method)
	// if (a && err==nil) {
	// 	return true
	// }
	fmt.Println("完成")
}
