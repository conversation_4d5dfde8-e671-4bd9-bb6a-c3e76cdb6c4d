package tests

import (
	"mulazim-api/models"
	"mulazim-api/tools"
	"testing"
)


func TestAddMenu(t *testing.T) {
	db :=tools.Db
	// 添加菜单
	// 主菜单
	parentMenu := models.Menus{
		NameZh:      "抽奖活动",
		NameUg:      "چەك تارتىش پائالىيىتى",
	}
	db.Create(&parentMenu)
	// 子菜单那
	parentPermissionActivity := models.Permissions{Name: "cms.v2.lottery.lottery-activity.*", DisplayNameUg: "پائالىيەت باشقۇرۇش", DisplayNameZh: "活动管理", ParentID: -1}
	db.Create(&parentPermissionActivity)
	db.Model(models.Menus{}).Create(&models.Menus{NameUg: "پائالىيەت باشقۇرۇش", NameZh: "活动管理", ParentID: parentMenu.ID,PermId: parentPermissionActivity.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.lottery-activity.list",DisplayNameUg: "تىزىملىك",DisplayNameZh: "列表",URL: "/app/:lang/cms/v2/lottery/lottery-activity/list",  ParentID: parentPermissionActivity.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.lottery-activity.create",DisplayNameUg: "قۇرۇش",DisplayNameZh: "创建",URL: "/app/:lang/cms/v2/lottery/lottery-activity/create",  ParentID: parentPermissionActivity.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.lottery-activity.edit",DisplayNameUg: "تەھرىرلەش",DisplayNameZh: "编辑",URL: "/app/:lang/cms/v2/lottery/lottery-activity/edit",  ParentID: parentPermissionActivity.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.lottery-activity.detail",DisplayNameUg: "تەپسىلاتى",DisplayNameZh: "详情",URL: "/app/:lang/cms/v2/lottery/lottery-activity/detail",  ParentID: parentPermissionActivity.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.lottery-activity.change-state",DisplayNameUg: "ھالەت ئۆزگەرتىش",DisplayNameZh: "修改状态",URL: "/app/:lang/cms/v2/lottery/lottery-activity/change-state",  ParentID: parentPermissionActivity.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.lottery-activity.delete",DisplayNameUg: "ئۆچۈرۈش",DisplayNameZh: "删除",URL: "/app/:lang/cms/v2/lottery/lottery-activity/delete",  ParentID: parentPermissionActivity.ID})
	
	parentPermissionActivityPrize := models.Permissions{Name: "cms.v2.lottery.prize.*", DisplayNameUg: "مۇكاپات", DisplayNameZh: "奖品", ParentID: -1}
	db.Create(&parentPermissionActivityPrize)
	db.Model(models.Menus{}).Create(&models.Menus{NameUg: "چەك تارتىش مۇكاپاتى", NameZh: "活动奖品", ParentID:  parentMenu.ID,PermId: parentPermissionActivityPrize.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.prize.list",DisplayNameUg: "تىزىملىك",DisplayNameZh: "列表",URL: "/app/:lang/cms/v2/lottery/prize/list",  ParentID: parentPermissionActivityPrize.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.prize.create",DisplayNameUg: "قۇرۇش",DisplayNameZh: "创建",URL: "/app/:lang/cms/v2/lottery/prize/create",  ParentID: parentPermissionActivityPrize.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.prize.edit",DisplayNameUg: "تەھرىرلەش",DisplayNameZh: "编辑",URL: "/app/:lang/cms/v2/lottery/prize/edit",  ParentID: parentPermissionActivityPrize.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.prize.detail",DisplayNameUg: "تەپسىلاتى",DisplayNameZh: "详情",URL: "/app/:lang/cms/v2/lottery/prize/detail",  ParentID: parentPermissionActivityPrize.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.prize.change-state",DisplayNameUg: "ھالەت ئۆزگەرتىش",DisplayNameZh: "修改状态",URL: "/app/:lang/cms/v2/lottery/prize/change-state",  ParentID: parentPermissionActivityPrize.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.prize.delete",DisplayNameUg: "ئۆچۈرۈش",DisplayNameZh: "删除",URL: "/app/:lang/cms/v2/lottery/prize/delete",  ParentID: parentPermissionActivityPrize.ID})
	
	parentPermissionActivityGroupCoupon := models.Permissions{Name: "cms.v2.lottery.activity-group-coupon.*", DisplayNameUg: "ئېتىبار بېلەت", DisplayNameZh:  "优惠卷", ParentID: -1}
	db.Create(&parentPermissionActivityGroupCoupon)
	db.Model(models.Menus{}).Create(&models.Menus{NameUg: "ئېتىبار بېلەت", NameZh: "优惠卷", ParentID:  parentMenu.ID,PermId: parentPermissionActivityGroupCoupon.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.activity-group-coupon.list",DisplayNameUg: "تىزىملىك",DisplayNameZh: "列表",URL: "/app/:lang/cms/v2/activity-group-coupon/prize/list",  ParentID: parentPermissionActivityGroupCoupon.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.activity-group-coupon.create",DisplayNameUg: "قۇرۇش",DisplayNameZh: "创建",URL: "/app/:lang/cms/v2/activity-group-coupon/prize/create",  ParentID: parentPermissionActivityGroupCoupon.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.activity-group-coupon.edit",DisplayNameUg: "تەھرىرلەش",DisplayNameZh: "编辑",URL: "/app/:lang/cms/v2/activity-group-coupon/prize/edit",  ParentID: parentPermissionActivityGroupCoupon.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.activity-group-coupon.detail",DisplayNameUg: "تەپسىلاتى",DisplayNameZh: "详情",URL: "/app/:lang/cms/v2/activity-group-coupon/prize/detail",  ParentID: parentPermissionActivityGroupCoupon.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.activity-group-coupon.change-state",DisplayNameUg: "ھالەت ئۆزگەرتىش",DisplayNameZh: "修改状态",URL: "/app/:lang/cms/v2/lottery/activity-group-coupon/change-state",  ParentID: parentPermissionActivityGroupCoupon.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.activity-group-coupon.delete",DisplayNameUg: "ئۆچۈرۈش",DisplayNameZh: "删除",URL: "/app/:lang/cms/v2/lottery/activity-group-coupon/delete",  ParentID: parentPermissionActivityGroupCoupon.ID})
	

	// parentPermissionActivityAnalyzeData := models.Permissions{Name: "cms.v2.lottery.activity-group-coupon.*", DisplayNameUg:  "پائالىيەت سانلىق مەلۇماتى", DisplayNameZh:  "活动数据分析", ParentID: -1}
	// db.Create(&parentPermissionActivityAnalyzeData)
	// db.Model(models.Menus{}).Create(&models.Menus{NameUg: "پائالىيەت سانلىق مەلۇماتى", NameZh: "活动数据分析", ParentID:  parentMenu.ID,PermId: parentPermissionActivityAnalyzeData.ID})

	// parentPermissionActivityAttendance := models.Permissions{Name: "cms.v2.lottery.activity-group-coupon.*", DisplayNameUg:  "پائالىيەتكە قاتناشقۇچىلار", DisplayNameZh:  "活动参与人", ParentID: -1}
	// db.Create(&parentPermissionActivityAttendance)
	// db.Model(models.Menus{}).Create(&models.Menus{NameUg: "پائالىيەتكە قاتناشقۇچىلار", NameZh: "活动参与人", ParentID: parentMenu.ID,PermId: parentPermissionActivityAttendance.ID})

	// parentPermissionActivityStatistic := models.Permissions{Name: "cms.v2.lottery.activity-group-coupon.*", DisplayNameUg: "پائالىيەت ئىستاتسىتىكىسى", DisplayNameZh:  "活动统计", ParentID: -1}
	// db.Create(&parentPermissionActivityStatistic)
	// db.Model(models.Menus{}).Create(&models.Menus{NameUg: "پائالىيەت ئىستاتسىتىكىسى", NameZh: "活动统计", ParentID: parentMenu.ID,PermId: parentPermissionActivityStatistic.ID})


}


func TestSubMenuAdd(t *testing.T) {
	db :=tools.Db
	parentPermissionActivityWinLog := models.Permissions{Name: "cms.v2.lottery.winner-set.*", DisplayNameUg: "پائالىيەت خاتىرسى", DisplayNameZh: "活动日志", ParentID: -1}
	db.Create(&parentPermissionActivityWinLog)
	db.Model(models.Menus{}).Create(&models.Menus{NameUg: "پائالىيەت خاتىرسى", NameZh: "活动日志", ParentID: 216,PermId: parentPermissionActivityWinLog.ID})
	db.Model(models.Permissions{}).Create(&models.Permissions{Name: "cms.v2.lottery.winner-set.list",DisplayNameUg: "تىزىملىك",DisplayNameZh: "列表",URL: "/app/:lang/cms/v2/lottery/winner-set/list",  ParentID: parentPermissionActivityWinLog.ID})
	
}