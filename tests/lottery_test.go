package tests

import (
	"encoding/json"
	"mulazim-api/configs"
	"mulazim-api/models"
	"mulazim-api/tools"
	"testing"

	"github.com/golang-module/carbon/v2"
)


func TestLottery(t *testing.T) {
	 

	db :=tools.GetDB()
	order :=models.OrderToday{
		ID:8814267,
		UserID :26,
		ActualPaid:2,
	}
	now :=carbon.Now(configs.AsiaShanghai)
	var activity models.LotteryActivity
	db.Model(&models.LotteryActivity{}).Where("state = ? and ? between start_time  and end_time ",1,now.Format("Y-m-d H:i:s")).Find(&activity)
	if(activity.ID > 0 && int(order.ActualPaid)>=activity.MinPrizeOrderPrice ) {//当前正在进行的抽奖活动
		var lotteryChance models.LotteryChance
		db.Model(&models.LotteryChance{}).Where("user_id = ? and lottery_activity_id = ? and type =? and type_id = ?",order.UserID,activity.ID,2,order.ID).Find(&lotteryChance)
		if lotteryChance.ID == 0 { 
			lotteryChance = models.LotteryChance{
				UserID:       order.UserID,
				LotteryActivityID: activity.ID,
				Type:         2,
				State: 1,
				TypeID:       order.ID,
				CreatedAt:    now.Carbon2Time(),
				UpdatedAt:    now.Carbon2Time(),
			}
			bb,_:=json.Marshal(lotteryChance)
			tools.Logger.Info("因为用户下单给他一个抽奖次数,详细数据",string(bb))
			db.Model(&models.LotteryChance{}).Create(&lotteryChance)
		}
		// TODO 判断这个用户是否来自分享
		var lotteryShareBind models.LotteryShareBind
		db.Model(&models.LotteryShareBind{}).Where("invited_user_id = ?",order.UserID).Find(&lotteryShareBind)
		if lotteryShareBind.ID > 0 { //确定这个用户来自分享 给他的上级分享人给他分配抽奖机会
			lotteryChance =models.LotteryChance{}
			db.Model(&models.LotteryChance{}).Where("user_id = ? and lottery_activity_id = ? and type =? and type_id = ?",lotteryShareBind.UserID,activity.ID,3,order.ID).Find(&lotteryChance)
			if lotteryChance.ID == 0 { 
				lotteryChance = models.LotteryChance{
					UserID:       lotteryShareBind.UserID,
					LotteryActivityID: activity.ID,
					Type:         3,
					State: 1,
					TypeID:       order.ID,
					CreatedAt:    now.Carbon2Time(),
					UpdatedAt:    now.Carbon2Time(),
				}
				bb,_:=json.Marshal(lotteryChance)
				tools.Logger.Info("因为用户下单给他的上级分享者一个抽奖次数,详细数据",string(bb))
				db.Model(&models.LotteryChance{}).Create(&lotteryChance)
			}
		}
	}
} 
