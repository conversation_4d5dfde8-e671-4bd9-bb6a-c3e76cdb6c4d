package tests

import (
	"fmt"
	"mulazim-api/tests/helper"
	"mulazim-api/tools"
	"net/url"
	"testing"

	"github.com/golang-module/carbon/v2"

	"github.com/go-playground/assert/v2"
)

// TestWechatUserTest
//
//	@Description: 普通的用户订单，小程序退单,微信支付
//	@author: <PERSON><PERSON><PERSON>
//	@Time: 2023-07-21 18:31:18
//	@param t *testing.T
func TestWechatMiniUserRefund(t *testing.T) {
	db := tools.Db
	db.Exec("INSERT INTO `mulazimpro`.`t_order_today` (`id`, `random_id`, `terminal_id`, `order_id`, `category_id`, `city_id`, `area_id`, `store_id`, `user_id`, `building_id`, `order_address`, `name`, `mobile`, `consume_type`, `pay_type`, `original_price`, `price`, `shipment`, `lunch_box_fee`, `full_reduction_amount`, `mp_profit`, `dealer_profit`, `cash`, `coin`, `consume`, `description`, `timezone`, `booking_time`, `pay_time`, `print_time`, `printed_time`, `delivery_taked_time`, `delivery_start_time`, `delivery_end_time`, `real_shipment`, `deduction_fee`, `taked`, `shipper_id`, `sent_sms`, `delete_flag`, `refund_type`, `refund_chanel`, `refunded`, `is_douyin`, `send_notify`, `serial_number`, `shipper_complete_grant`, `is_luck_drawed`, `is_auto_refund`, `is_commented`, `distance`, `order_type`, `called`, `pay_channel`, `state`, `created_at`, `updated_at`, `deleted_at`, `cash_clear_state`, `cash_clear_time`, `cash_clear_channel`, `cash_clear_admin`, `seckill_state`, `is_auto_fished`, `self_take_number`, `delivery_type`, `json_info`) VALUES (6521145, NULL, 8, 'lailiiii000000002307211621074418', 1, 7, 35, 5955, 340706, 84270, '大巴扎A02号', '阿孜古丽', '13779631857', 1, 5, 1800, 1800, 400, 0, NULL, 79, 627, 0, 0, 0, ' (چوكا قوشۇق 2 كىشلىك)', 6, '2023-07-21 17:04:04', '2023-07-21 16:21:17', '2023-07-21 16:21:07', NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 1, 0, 0, 0, 0, NULL, 0, 2, 0, 0, 0, 0, 0.00, 1, 0, NULL, 3, '2023-07-21 16:21:07', '2023-07-21 16:21:17', NULL, 0, NULL, NULL, NULL, 0, NULL, NULL, 1, NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10592697, 6521145, 186511, 1100, 1100, 3.6, 100, 13.4, 40, 147, 1, 0, 0, 0, NULL, '2023-07-21 16:21:07', '2023-07-21 16:21:07', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10592698, 6521145, 179553, 700, 700, 3.6, 100, 13.4, 25, 94, 1, 0, 0, 0, NULL, '2023-07-21 16:21:07', '2023-07-21 16:21:07', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34616590, 6521145, 1, NULL, 1, '2023-07-21 16:21:07', '2023-07-21 16:21:07', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34616594, 6521145, 2, NULL, 1, '2023-07-21 16:21:17', '2023-07-21 16:21:17', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34616595, 6521145, 3, NULL, 1, '2023-07-21 16:21:17', '2023-07-21 16:21:17', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`wechat` (`id`, `app_id`, `mch_id`, `open_id`, `order_id`, `out_trade_no`, `transaction_id`, `prepay_id`, `nonce_str`, `trade_type`, `total_fee`, `payed`, `number`, `remark`, `expire_time`, `created_at`, `updated_at`, `deleted_at`) VALUES (4458992, NULL, NULL, NULL, 6521145, 'lailiiii000000002307211621074418', '4200001893202307215143626445', 'wx211821113184508df0f8ce378499dd0000', 'b4ki3pezocYINC2V', 'JSAPI', 2200, 1, 0, NULL, '2023-07-21 18:21:11', '2023-07-21 16:21:11', '2023-07-21 16:21:17', NULL);")
	//
	data := url.Values{}
	data.Set("order_id", "6521145")
	data.Set("user_id", "340706")
	data.Set("terminal_id", "8")
	data.Set("user_type", "1")
	w := helper.SendGinPost("/ug/payment/v1/cancel-order", data, "", "",nil)

	fmt.Println(w.Body.String())
	assert.Equal(t, 200, getCode(w.Body.String()))

	type Order struct {
		ID       int `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
		State    int `gorm:"column:state;default:1"`               // 订单状态
		Refunded int `gorm:"column:refunded;default:0"`            // 退款状态
	}
	orderId := 6521145
	var order Order
	db.Table("t_order_today").Where("id = ?", orderId).Scan(&order)
	assert.Equal(t, true, order.State == 8 || order.State == 9)
	assert.Equal(t, true, order.Refunded == 1)

	var count int64
	db.Table("t_order_state_log").Where("order_id = ? and order_state_id in ?", orderId, [2]int{8, 9}).Count(&count)
	assert.Equal(t, true, count > 0)

	db.Table("wechat_refund").Where("order_id = ", orderId).Count(&count)
	assert.Equal(t, true, count > 0)

	deleteOrderInfo(orderId)
	//
	//

}

// TestWechatMiniUserCasheRefund
//
//	@Description: 现金订单，用户小程序退款
//	@author: Alimjan
//	@Time: 2023-07-21 19:13:54
//	@param t *testing.T
func TestWechatMiniUserCasheRefund(t *testing.T) {
	db := tools.Db
	db.Exec("INSERT INTO `mulazimpro`.`t_order_today` (`id`, `random_id`, `terminal_id`, `order_id`, `category_id`, `city_id`, `area_id`, `store_id`, `user_id`, `building_id`, `order_address`, `name`, `mobile`, `consume_type`, `pay_type`, `original_price`, `price`, `shipment`, `lunch_box_fee`, `full_reduction_amount`, `mp_profit`, `dealer_profit`, `cash`, `coin`, `consume`, `description`, `timezone`, `booking_time`, `pay_time`, `print_time`, `printed_time`, `delivery_taked_time`, `delivery_start_time`, `delivery_end_time`, `real_shipment`, `deduction_fee`, `taked`, `shipper_id`, `sent_sms`, `delete_flag`, `refund_type`, `refund_chanel`, `refunded`, `is_douyin`, `send_notify`, `serial_number`, `shipper_complete_grant`, `is_luck_drawed`, `is_auto_refund`, `is_commented`, `distance`, `order_type`, `called`, `pay_channel`, `state`, `created_at`, `updated_at`, `deleted_at`, `cash_clear_state`, `cash_clear_time`, `cash_clear_channel`, `cash_clear_admin`, `seckill_state`, `is_auto_fished`, `self_take_number`, `delivery_type`, `json_info`) VALUES (6521437, NULL, 8, 'qidir000000000002307211709245312', 1, 7, 31, 9015, 164488, 30300, '第一人民医院旁边卫生小区大门口第一个依克山超市', '啊啊', '17599264847', 0, 1, 2800, 2800, 300, 100, NULL, 115, 823, 0, 0, 0, ' (چوكا قوشۇق 4 كىشلىك)', 6, '2023-07-21 17:52:20', '2023-07-21 17:10:39', '2023-07-21 17:09:24', NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 1, 0, 0, 0, 0, NULL, 0, 3, 0, 0, 0, 0, 0.00, 1, 0, NULL, 3, '2023-07-21 17:09:24', '2023-07-21 17:10:39', NULL, 0, NULL, NULL, NULL, 0, NULL, NULL, 1, NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10593165, 6521437, 314566, 2100, 2100, 3.6, 100, 18.4, 79, 405, 1, 14, 100, 1, NULL, '2023-07-21 17:09:24', '2023-07-21 17:09:24', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10593166, 6521437, 314616, 400, 400, 3.6, 100, 18.4, 14, 74, 1, 0, 0, 0, NULL, '2023-07-21 17:09:24', '2023-07-21 17:09:24', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10593167, 6521437, 314587, 150, 150, 3.6, 100, 18.4, 5, 28, 1, 0, 0, 0, NULL, '2023-07-21 17:09:24', '2023-07-21 17:09:24', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10593168, 6521437, 314585, 150, 150, 3.6, 100, 18.4, 5, 28, 1, 0, 0, 0, NULL, '2023-07-21 17:09:24', '2023-07-21 17:09:24', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34618125, 6521437, 1, NULL, 1, '2023-07-21 17:09:24', '2023-07-21 17:09:24', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34618174, 6521437, 2, NULL, 1, '2023-07-21 17:10:39', '2023-07-21 17:10:39', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34618175, 6521437, 3, NULL, 1, '2023-07-21 17:10:39', '2023-07-21 17:10:39', NULL);")

	data := url.Values{}
	data.Set("order_id", "6521437")
	data.Set("user_id", "164488")
	data.Set("terminal_id", "8")
	data.Set("user_type", "1")
	w := helper.SendGinPost("/ug/payment/v1/cancel-order", data, "", "",nil)

	fmt.Println(w.Body.String())
	assert.Equal(t, 200, getCode(w.Body.String()))

	type Order struct {
		ID       int `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
		State    int `gorm:"column:state;default:1"`               // 订单状态
		Refunded int `gorm:"column:refunded;default:0"`            // 退款状态
	}
	orderId := 6521437
	var order Order
	db.Table("t_order_today").Where("id = ?", orderId).Scan(&order)
	assert.Equal(t, true, order.State == 8 || order.State == 9)
	assert.Equal(t, true, order.Refunded == 1)

	var count int64
	db.Table("t_order_state_log").Where("order_id = ? and order_state_id in ?", orderId, [2]int{8, 9}).Count(&count)
	assert.Equal(t, true, count > 0)

	count = 0
	db.Table("wechat_refund").Where("order_id = ?", orderId).Count(&count)
	assert.Equal(t, true, count == 0)

	//
	//
	deleteOrderInfo(orderId)
}

// TestCmsWechatRefund
//
//	@Description: 后台退已接微信订单
//	@author: Alimjan
//	@Time: 2023-07-21 19:17:43
//	@param t *testing.T
func TestCmsWechatRefund(t *testing.T) {
	db := tools.Db
	orderId := 6521764
	deleteOrderInfo(orderId)
	db.Exec("INSERT INTO `mulazimpro`.`t_order_today` (`id`, `random_id`, `terminal_id`, `order_id`, `category_id`, `city_id`, `area_id`, `store_id`, `user_id`, `building_id`, `order_address`, `name`, `mobile`, `consume_type`, `pay_type`, `original_price`, `price`, `shipment`, `lunch_box_fee`, `full_reduction_amount`, `mp_profit`, `dealer_profit`, `cash`, `coin`, `consume`, `description`, `timezone`, `booking_time`, `pay_time`, `print_time`, `printed_time`, `delivery_taked_time`, `delivery_start_time`, `delivery_end_time`, `real_shipment`, `deduction_fee`, `taked`, `shipper_id`, `sent_sms`, `delete_flag`, `refund_type`, `refund_chanel`, `refunded`, `is_douyin`, `send_notify`, `serial_number`, `shipper_complete_grant`, `is_luck_drawed`, `is_auto_refund`, `is_commented`, `distance`, `order_type`, `called`, `pay_channel`, `state`, `created_at`, `updated_at`, `deleted_at`, `cash_clear_state`, `cash_clear_time`, `cash_clear_channel`, `cash_clear_admin`, `seckill_state`, `is_auto_fished`, `self_take_number`, `delivery_type`, `json_info`) VALUES (6521764, NULL, 5, 'isak0000000000002307211800026385', 1, 7, 31, 1814, 167996, 32787, '5.بىنا1.كۇرپۇس3.قەۋەت301.ئۆي', 'مەمەتجان خالىق', '13239965501', 1, 5, 4600, 4600, 300, 200, NULL, 184, 1076, 0, 0, 0, 'تېز كەلسۇن (چوكا قوشۇق 2 كىشلىك)', 6, '2023-07-21 18:47:55', '2023-07-21 18:00:18', '2023-07-21 18:00:02', '2023-07-21 18:01:58', '2023-07-21 18:01:59', NULL, NULL, NULL, NULL, 1, 657, 1, 0, 0, 0, 0, NULL, 0, 1, 0, 0, 0, 0, 0.00, 1, 0, NULL, 4, '2023-07-21 18:00:02', '2023-07-21 18:01:59', NULL, 0, NULL, NULL, NULL, 0, NULL, NULL, 1, NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10593687, 6521764, 124848, 600, 600, 3.6, 100, 16.4, 22, 98, 1, 0, 0, 0, NULL, '2023-07-21 18:00:02', '2023-07-21 18:00:02', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10593688, 6521764, 36146, 4000, 4000, 3.6, 100, 16.4, 151, 689, 1, 138, 200, 1, NULL, '2023-07-21 18:00:02', '2023-07-21 18:00:02', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_take_order` (`id`, `admin_id`, `order_id`, `order_price`, `consume_type`, `reason_id`, `is_clearing`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (6058991, 657, 6521764, 5100, 1, NULL, 1, 1, '2023-07-21 18:01:59', '2023-07-21 18:01:59', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34619798, 6521764, 1, NULL, 1, '2023-07-21 18:00:02', '2023-07-21 18:00:02', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34619805, 6521764, 2, NULL, 1, '2023-07-21 18:00:18', '2023-07-21 18:00:18', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34619806, 6521764, 3, NULL, 1, '2023-07-21 18:00:18', '2023-07-21 18:00:18', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34619849, 6521764, 4, NULL, 1, '2023-07-21 18:01:58', '2023-07-21 18:01:58', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`wechat` (`id`, `app_id`, `mch_id`, `open_id`, `order_id`, `out_trade_no`, `transaction_id`, `prepay_id`, `nonce_str`, `trade_type`, `total_fee`, `payed`, `number`, `remark`, `expire_time`, `created_at`, `updated_at`, `deleted_at`) VALUES (4459587, NULL, NULL, NULL, 6521764, 'isak0000000000002307211800026385', '4200001872202307214883364881', 'wx21200007654278e95b2189c351540f0000', 'MwbXDdp1ngL4NK1e', 'APP', 5100, 1, 0, NULL, '2023-07-21 20:00:07', '2023-07-21 18:00:07', '2023-07-21 18:00:18', NULL);")

	data := url.Values{}
	data.Set("order_id", "6521764")
	data.Set("user_id", "1")
	data.Set("terminal_id", "8")
	data.Set("user_type", "2")
	data.Set("reason_id", "3")
	w := helper.SendGinPost("/ug/payment/v1/cancel-order", data, "", "",nil)

	fmt.Println(w.Body.String())
	assert.Equal(t, 200, getCode(w.Body.String()))

	type Order struct {
		ID       int `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
		State    int `gorm:"column:state;default:1"`               // 订单状态
		Refunded int `gorm:"column:refunded;default:0"`            // 退款状态
	}

	var order Order
	db.Table("t_order_today").Where("id = ?", orderId).Scan(&order)
	assert.Equal(t, true, order.State == 8 || order.State == 9)
	assert.Equal(t, true, order.Refunded == 1)

	var count int64
	db.Table("t_order_state_log").Where("order_id = ? and order_state_id in ?", orderId, [2]int{8, 9}).Count(&count)
	assert.Equal(t, true, count > 0)

	count = 0
	db.Table("wechat_refund").Where("order_id = ", orderId).Count(&count)
	assert.Equal(t, true, count > 0)

	//
	//
	deleteOrderInfo(orderId)
}

// TestCmsCasheRefund
//
//	@Description: 后台退已接现金订单
//	@author: Alimjan
//	@Time: 2023-07-21 19:25:04
//	@param t *testing.T
func TestCmsCasheRefund(t *testing.T) {
	db := tools.Db
	orderId := 6521281
	deleteOrderInfo(orderId)
	db.Exec("INSERT INTO `mulazimpro`.`t_order_today` (`id`, `random_id`, `terminal_id`, `order_id`, `category_id`, `city_id`, `area_id`, `store_id`, `user_id`, `building_id`, `order_address`, `name`, `mobile`, `consume_type`, `pay_type`, `original_price`, `price`, `shipment`, `lunch_box_fee`, `full_reduction_amount`, `mp_profit`, `dealer_profit`, `cash`, `coin`, `consume`, `description`, `timezone`, `booking_time`, `pay_time`, `print_time`, `printed_time`, `delivery_taked_time`, `delivery_start_time`, `delivery_end_time`, `real_shipment`, `deduction_fee`, `taked`, `shipper_id`, `sent_sms`, `delete_flag`, `refund_type`, `refund_chanel`, `refunded`, `is_douyin`, `send_notify`, `serial_number`, `shipper_complete_grant`, `is_luck_drawed`, `is_auto_refund`, `is_commented`, `distance`, `order_type`, `called`, `pay_channel`, `state`, `created_at`, `updated_at`, `deleted_at`, `cash_clear_state`, `cash_clear_time`, `cash_clear_channel`, `cash_clear_admin`, `seckill_state`, `is_auto_fished`, `self_take_number`, `delivery_type`, `json_info`) VALUES (6521281, '60ei1vou', 7, 'xarbatpolohana002307211644256298', 1, 11, 67, 6942, 573534, 78315, 'بۇلاق كەچىلىك بازىرىنىڭ يېنىغا ', 'سسسسسسسس', '18094880949', 3, 6, 6300, 3170, 400, 0, NULL, 129, 779, 0, 0, 0, '', 6, '2023-07-21 17:44:24', '2023-07-21 16:45:12', '2023-07-21 16:54:24', '2023-07-21 16:45:12', '2023-07-21 16:45:13', NULL, NULL, NULL, NULL, 1, 8848, 0, 0, 0, 0, 0, NULL, 1, 203, 0, 0, 0, 0, 0.00, 1, 0, NULL, 4, '2023-07-21 16:44:25', '2023-07-21 16:45:14', NULL, 0, NULL, NULL, NULL, 0, NULL, NULL, 1, NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10592927, 6521281, 277095, 1500, 770, 3.6, 100, 12.4, 28, 95, 1, 0, 0, 0, NULL, '2023-07-21 16:44:25', '2023-07-21 16:44:25', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10592928, 6521281, 227359, 2400, 1200, 3.6, 100, 12.4, 86, 298, 2, 0, 0, 0, NULL, '2023-07-21 16:44:25', '2023-07-21 16:44:25', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_take_order` (`id`, `admin_id`, `order_id`, `order_price`, `consume_type`, `reason_id`, `is_clearing`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (6058535, 8848, 6521281, 3570, 3, NULL, 0, 1, '2023-07-21 16:45:13', '2023-07-21 16:45:13', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34617287, 6521281, 3, NULL, 1, '2023-07-21 16:44:25', '2023-07-21 16:44:25', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34617299, 6521281, 4, NULL, 1, '2023-07-21 16:45:12', '2023-07-21 16:45:12', NULL);")
	data := url.Values{}
	data.Set("order_id", "6521281")
	data.Set("user_id", "1")
	data.Set("terminal_id", "8")
	data.Set("user_type", "1")
	data.Set("reason_id", "3")
	w := helper.SendGinPost("/ug/payment/v1/cancel-order", data, "", "",nil)

	fmt.Println(w.Body.String())
	assert.Equal(t, 200, getCode(w.Body.String()))

	type Order struct {
		ID       int `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
		State    int `gorm:"column:state;default:1"`               // 订单状态
		Refunded int `gorm:"column:refunded;default:0"`            // 退款状态
	}
	var order Order
	db.Table("t_order_today").Where("id = ?", orderId).Scan(&order)
	assert.Equal(t, true, order.State == 8 || order.State == 9)
	assert.Equal(t, true, order.Refunded == 1)

	var count int64
	db.Table("t_order_state_log").Where("order_id = ? and order_state_id in ?", orderId, [2]int{8, 9}).Count(&count)
	assert.Equal(t, true, count > 0)

	count = 0
	db.Table("t_admin_recharge_log").Where("out_trade_no = ? and type = 3", orderId).Count(&count)
	assert.Equal(t, true, count == 1)

	//
	//
	//deleteOrderInfo(orderId)
}
func TestTimer(t *testing.T) {
	fmt.Printf(carbon.Now().Format("Y-m-d H:00:00", "Asia/Shanghai"))
	fmt.Printf(carbon.Now().AddHour().Format("Y-m-d H:00:00", "Asia/Shanghai"))
}

// TestCmsCashAndWechatRefund
//
//	@Description: 后台退已完成 APP 支付的订单
//	@author: Alimjan
//	@Time: 2023-07-21 19:30:56
//	@param t *testing.T
func TestCmsCashAndWechatRefund(t *testing.T) {
	db := tools.Db
	orderId := 6516983
	deleteOrderInfo(orderId)
	db.Exec("INSERT INTO `mulazimpro`.`t_order_today` (`id`, `random_id`, `terminal_id`, `order_id`, `category_id`, `city_id`, `area_id`, `store_id`, `user_id`, `building_id`, `order_address`, `name`, `mobile`, `consume_type`, `pay_type`, `original_price`, `price`, `shipment`, `lunch_box_fee`, `full_reduction_amount`, `mp_profit`, `dealer_profit`, `cash`, `coin`, `consume`, `description`, `timezone`, `booking_time`, `pay_time`, `print_time`, `printed_time`, `delivery_taked_time`, `delivery_start_time`, `delivery_end_time`, `real_shipment`, `deduction_fee`, `taked`, `shipper_id`, `sent_sms`, `delete_flag`, `refund_type`, `refund_chanel`, `refunded`, `is_douyin`, `send_notify`, `serial_number`, `shipper_complete_grant`, `is_luck_drawed`, `is_auto_refund`, `is_commented`, `distance`, `order_type`, `called`, `pay_channel`, `state`, `created_at`, `updated_at`, `deleted_at`, `cash_clear_state`, `cash_clear_time`, `cash_clear_channel`, `cash_clear_admin`, `seckill_state`, `is_auto_fished`, `self_take_number`, `delivery_type`, `json_info`) VALUES (6516983, 'injlzcq0', 7, 'yawagbaqka0000002307211108304379', 1, 10, 53, 5109, 536611, 33865, 'خوڭيى داسا ', 'خىرىدار', '13909981819', 1, 5, 3700, 3700, 300, 0, NULL, 144, 896, 0, 0, 0, '', 6, '2023-07-21 11:56:28', '2023-07-21 11:08:36', '2023-07-21 11:18:28', '2023-07-21 11:08:36', '2023-07-21 11:08:38', '2023-07-21 11:24:08', '2023-07-21 11:26:48', NULL, NULL, 1, 5997, 0, 0, 0, 0, 0, NULL, 1, 1, 0, 0, 0, 0, 0.00, 1, 0, NULL, 7, '2023-07-21 11:08:30', '2023-07-21 13:26:48', NULL, 1, '2023-07-21 11:26:45', 4, NULL, 0, NULL, NULL, 1, NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10586709, 6516983, 145658, 3100, 3100, 3.6, 100, 16.4, 112, 508, 1, 0, 0, 0, NULL, '2023-07-21 11:08:30', '2023-07-21 11:08:30', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10586710, 6516983, 145659, 600, 600, 3.6, 100, 16.4, 22, 98, 1, 0, 0, 0, NULL, '2023-07-21 11:08:30', '2023-07-21 11:08:30', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_take_order` (`id`, `admin_id`, `order_id`, `order_price`, `consume_type`, `reason_id`, `is_clearing`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (6054304, 5997, 6516983, 4000, 3, NULL, 0, 3, '2023-07-21 11:08:38', '2023-07-21 11:08:38', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34594417, 6516983, 3, NULL, 1, '2023-07-21 11:08:30', '2023-07-21 11:08:30', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34594426, 6516983, 4, NULL, 1, '2023-07-21 11:08:36', '2023-07-21 11:08:36', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34595892, 6516983, 6, NULL, 1, '2023-07-21 11:24:08', '2023-07-21 11:24:08', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34596145, 6516983, 7, NULL, 1, '2023-07-21 11:26:48', '2023-07-21 11:26:48', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`wechat` (`id`, `app_id`, `mch_id`, `open_id`, `order_id`, `out_trade_no`, `transaction_id`, `prepay_id`, `nonce_str`, `trade_type`, `total_fee`, `payed`, `number`, `remark`, `expire_time`, `created_at`, `updated_at`, `deleted_at`) VALUES (4455637, 'wxedfd69db270adcf6', '1232338602', 'o5BguwTG-bmUXB-OWmBGxp-pqBrM', 6516983, 'sms20230721112624184', '4200001857202307218497329020', 'wx21132625107984bd530b91a219de220000', 'GhkTTVCWn3Ywtnj8', 'NATIVE', 4000, 1, 0, NULL, NULL, '2023-07-21 11:26:25', '2023-07-21 11:26:45', NULL);")
	data := url.Values{}
	data.Set("order_id", "6516983")
	data.Set("user_id", "1")
	data.Set("terminal_id", "8")
	data.Set("user_type", "1")
	data.Set("reason_id", "3")
	w := helper.SendGinPost("/ug/payment/v1/cancel-order", data, "", "",nil)

	fmt.Println(w.Body.String())
	assert.Equal(t, 200, getCode(w.Body.String()))

	type Order struct {
		ID       int `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
		State    int `gorm:"column:state;default:1"`               // 订单状态
		Refunded int `gorm:"column:refunded;default:0"`            // 退款状态
	}

	var order Order
	db.Table("t_order_today").Where("id = ?", orderId).Scan(&order)
	assert.Equal(t, true, order.State == 8 || order.State == 9)
	assert.Equal(t, true, order.Refunded == 1)

	var count int64
	db.Table("t_order_state_log").Where("order_id = ? and order_state_id in ?", orderId, [2]int{8, 9}).Count(&count)
	assert.Equal(t, true, count > 0)

	count = 0
	db.Table("wechat_refund").Where("order_id = ", orderId).Count(&count)
	assert.Equal(t, true, count > 0)

	//
	//
	deleteOrderInfo(orderId)
}

func TestCmsCashAndWechatRefundSeckillOrder(t *testing.T) {
	db := tools.Db
	orderId := 5737142
	orderDetailId := 3340729

	db.Exec("delete from t_order_today where id = ?;", orderId)
	db.Exec("delete from t_order_detail where order_id = ?;", orderId)
	db.Exec("delete from wechat where order_id = ?;", orderId)
	db.Exec("delete from wechat_refund where order_id = ?;", orderId)
	db.Exec("delete from b_seckill_log where detail_id  = ?;", orderDetailId)

	// deleteOrderInfo(orderId)
	db.Exec("INSERT INTO `mulazimpro`.`t_order_today` (`id`, `random_id`, `terminal_id`, `order_id`, `category_id`, `city_id`, `area_id`, `store_id`, `user_id`, `building_id`, `order_address`, `name`, `mobile`, `consume_type`, `pay_type`, `pay_channel`, `pay_platform`, `original_price`, `price`, `shipment`, `lunch_box_fee`, `full_reduction_amount`, `mp_profit`, `dealer_profit`, `cash`, `coin`, `consume`, `description`, `timezone`, `booking_time`, `pay_time`, `print_time`, `printed_time`, `delivery_taked_time`, `delivery_start_time`, `delivery_end_time`, `real_shipment`, `deduction_fee`, `taked`, `shipper_id`, `sent_sms`, `delete_flag`, `refund_type`, `refund_chanel`, `refunded`, `is_douyin`, `send_notify`, `serial_number`, `shipper_complete_grant`, `is_luck_drawed`, `is_auto_refund`, `is_commented`, `distance`, `order_type`, `called`, `state`, `created_at`, `updated_at`, `deleted_at`, `cash_clear_state`, `cash_clear_time`, `cash_clear_channel`, `cash_clear_admin`, `seckill_state`, `is_auto_fished`, `self_take_number`, `delivery_type`) VALUES (5737142, NULL, 8, 'tasi0000000000002307251447352209', 1, 1, 1, 6273, 243686, 1980, '1980د', 'rixat', '16699887912', 1, 5, 1, 0, 500, 10, 0, 0, NULL, 0, 1, 0, 0, 0, '', 6, '2023-07-25 15:05:34', '2023-07-25 14:47:50', '2023-07-25 14:47:35', NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 1, 0, 0, 0, 0, NULL, 0, 1, 0, 0, 0, 0, 0.00, 1, 0, 3, '2023-07-25 14:47:35', '2023-07-25 17:26:58', NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, 1);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `pref_id`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (3340729, 5737142, 193018, 500, 10, 3.6, 100, 11.4, 0, 1, 1, 53, 0, 1, NULL, 137, '2023-07-25 14:47:35', '2023-07-25 14:47:35', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`b_seckill_log` (`id`, `user_id`, `detail_id`, `seckill_id`, `food_id`, `saled_count`, `seckill_price`, `seckill_platform_service_fee`, `state`, `created_at`, `updated_at`) VALUES (137, 243686, 3340729, 67, 193018, 1, 10, 50, 1, '2023-07-25 14:47:35', '2023-07-25 14:47:35');")

	data := url.Values{}
	data.Set("order_id", tools.ToString(orderId))
	data.Set("user_id", "1")
	data.Set("terminal_id", "8")
	data.Set("user_type", "1")
	data.Set("reason_id", "3")
	w := helper.SendGinPost("/ug/payment/v1/cancel-order", data, "", "",nil)

	fmt.Println(w.Body.String())
	assert.Equal(t, 200, getCode(w.Body.String()))

	type Order struct {
		ID       int `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
		State    int `gorm:"column:state;default:1"`               // 订单状态
		Refunded int `gorm:"column:refunded;default:0"`            // 退款状态
	}

	var order Order
	db.Table("t_order_today").Where("id = ?", orderId).Scan(&order)
	assert.Equal(t, true, order.State == 8 || order.State == 9)
	assert.Equal(t, true, order.Refunded == 1)

	var count int64
	db.Table("t_order_state_log").Where("order_id = ? and order_state_id in ?", orderId, [2]int{8, 9}).Count(&count)
	assert.Equal(t, true, count > 0)

	count = 0
	db.Table("wechat_refund").Where("order_id = ", orderId).Count(&count)
	assert.Equal(t, true, count > 0)

	//
	//
	// deleteOrderInfo(orderId)
}

// select id,created_at from t_order_today where state = 7 and pay_type = 5 and taked = 1 and cash_clear_state = 1  and created_at > "2023-07-21 11:04:28" limit 10 ;
//
// set @order_id = 6516983;
//
// select * from t_order_today where id = @order_id;
// select * from t_order_detail where order_id = @order_id;
// select * from t_take_order where order_id = @order_id;
// select * from t_order_state_log where order_id = @order_id;
// select * from wechat where order_id = @order_id;
//
// select * from t_order_today where id = @order_id;
// select * from t_order_detail where order_id = @order_id;
// select * from t_take_order where order_id = @order_id;
// select * from t_order_state_log where order_id = @order_id;
// select * from wechat where order_id = @order_id;
//
// select * from t_admin;
func deleteOrderInfo(orderId int) {
	db := tools.Db
	db.Exec("delete from t_order_today where id = ?;", orderId)
	db.Exec("delete from t_order_detail where order_id = ?;", orderId)
	db.Exec("delete from t_take_order where order_id = ?;", orderId)
	db.Exec("delete from t_order_state_log where order_id = ?;", orderId)
	db.Exec("delete from wechat where order_id = ?;", orderId)
	db.Exec("delete from wechat_refund where order_id = ?;", orderId)
	db.Exec("delete from t_admin_recharge_log where type =3 and out_trade_no = ?;", orderId)
	db.Exec("delete from t_admin_packet_recharge_log where id = 11184")
}
