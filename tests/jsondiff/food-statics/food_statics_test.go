package foodlist

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	url2 "net/url"
	"testing"
)

const GO_API = "http://localhost:8085"
const PHP_API = "http://api.mulazim.cc"
const FOOD_STATICS = "/ug/merchant/v1/admin/business-statistics"
//定义数组 [211,22,33] 变量
const TOKEN = "Bearer ZQ6BJMVxJck2iUoFGIrihTT2GlenXURof28rURE6"

var resArr = []int{
	6942,
	2554,
	8466,
	3930,
	4749,
	839,
	6779,
	5593,
	3812,
	5056,
	2390,
	6054,
	5819,
	3285,
	11310,
	4364,
	1930,
	7269,
	10248,
	4998,
	3854,
	1769,
	8618,
	6163,
	9687,
	597,
	5196,
	6755,
	3945,
	11312,
	10326,
	4884,
	8234,
	3875,
	3143,
	3503,
	9624,
	2581,
	1789,
	4355,
	934,
	2553,
	2586,
	2525,
	9417,
	883,
	9512,
	3748,
	2166,
	6588,
	3948,
	4788,
	5153,
	3624,
	9431,
	4729,
	904,
	2423,
	5272,
	4428,
	8358,
	7004,
	2379,
	9923,
	6812,
	4784,
	4351,
	8136,
	11335,
	8433,
	4913,
	5326,
	3907,
	8747,
	6573,
	5007,
	11342,
	6160,
	9773,
	3645,
	9666,
	3678,
	2620,
	851,
	3837,
	8656,
	6034,
	2386,
	3763,
	5130,
	3445,
	7148,
	11245,
	7529,
	4403,
	3626,
	8167,
	5348,
	2327,
	2342,
}
func getMerchantFoodStaticJson(host string, resID int64 ,startDate string ,endDate string) string{
	//编码star date
	url := fmt.Sprintf("%s%s?restaurant_id=%d&start_date=%s&end_date=%s", host,FOOD_STATICS, resID, url2.PathEscape(startDate),url2.PathEscape(endDate))
	method := "GET"

	client := &http.Client {
	}
	req, err := http.NewRequest(method, url, nil)

	if err != nil {
		fmt.Println(err)
		return ""
	}
	req.Header.Add("Authorization", TOKEN)
	req.Header.Add("osType", "1")
	req.Header.Add("terminalId", "1")
	req.Header.Add("appVersion", "2.0")
	req.Header.Add("Accept-Language", "en;q=1")
	req.Header.Add("screenSize", "414*736")
	req.Header.Add("phoneBrand", "apple")
	req.Header.Add("User-Agent", "Dinner/1.0 (iPhone; iOS 9.3; Scale/3.00)")
	req.Header.Add("lang", "0")
	req.Header.Add("appBuild", "1")
	req.Header.Add("appTime", "1467103218.903170")
	req.Header.Add("searialNumber", "123456789")
	req.Header.Add("osVersion", "1.0")
	req.Header.Add("parentCategoryId", "1")
	req.Header.Add("Accept", "application/json")
	req.Header.Add("refresh_token", "")
	req.Header.Add("res_id", "")
	req.Header.Add("almas", "1")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return ""
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return ""
	}
	return string(body)
}
//foods-category
func TestMerchantStatics(t *testing.T) {
	for index, resID := range resArr {

		phpJson := getMerchantFoodStaticJson(GO_API, int64(resID), "2024-05-01 00:00:00","2024-06-22 23:59:59")
		goJson := getMerchantFoodStaticJson(PHP_API, int64(resID), "2024-05-01 00:00:00","2024-06-22 23:59:59")

		var phpMap map[string]interface{}
		var goMap map[string]interface{}
		json.Unmarshal([]byte(phpJson),&phpMap)
		json.Unmarshal([]byte(goJson),&goMap)

		phpDataStr,_ := json.Marshal(phpMap["data"])
		goDataStr,_ := json.Marshal(goMap["data"])
		//判断是否相等，单元测是
		if string(phpDataStr) != string(goDataStr) {
			println("phpJson:")
			println(phpJson)
			println("goJson:")
			println(goJson)

			t.Error("TestMerchantFoodCategory failed", "resID:", resID)
			return
		}
		println("index:", index, "resID:", resID, "TestMerchantFoodCategory success")
	}

	//{"data":[{"id":18,"name":"副食","state":1},{"id":19,"name":"生活服务","state":1}],"msg":"操作成功","status":200,"time":"2024-06-22 12:58:16"}

}