package foodlist

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"testing"
	"time"
)

const GO_API = "http://localhost:8085"
const PHP_API = "http://api.mulazim.cc"
const FOOD_INFO = "/zh/merchant/v1/admin/foods-information"
//定义数组 [211,22,33] 变量
const TOKEN = "Bearer ZTuuwuiQsTrIsNstmyKcqnqWJIrUPOFeSUyvNSnZ"
var foodId = []int {
	191794,
	191792,
	191788,
	191782,
	252011,
	191782,
	359394,
	359319,
	81364,
	434780,
	434711,
	185268,
	185276,
	185277,
	83093,
	83128,
	405307,
	67636,
	252046,
	252023,
	251993,
	204328,
	241274,
	241402,
	150811,
	252046,
	252023,
	251993,
	307928,
	121918,
	122659,
	122627,
	150197,
	265863,
	204313,
	130645,
	284742,
	282786,
	311456,
	392139,
	64671,
	359560,
	305210,
	305060,
	241274,
	241402,
	219368,
	354756,
	354756,
	115147,
	241274,
	241404,
	219461,
	172925,
	154493,
	323969,
	323971,
	323986,
	323975,
	324002,
	324009,
	324000,
	108907,
	64667,
	404100,
	130260,
	130261,
	241274,
	241402,
	218112,
	215725,
	208032,
	207992,
	207997,
	208050,
	208054,
	208105,
	246757,
	246757,
	305044,
	324968,
	402086,
	349307,
	64667,
	46672,
	130269,
	130261,
	150817,
	108810,
	108907,
	53865,
	53865,
	67058,
	13801,
	13796,
	427711,
	330566,
	318255,
	318253,
	318247,
}
var resArr = []int{
	6239,
	6239,
	6239,
	6239,
	7529,
	6239,
	9773,
	9773,
	3285,
	11420,
	11420,
	6100,
	6100,
	6100,
	3285,
	3285,
	10267,
	2615,
	7529,
	7529,
	7529,
	597,
	6779,
	6779,
	5216,
	7529,
	7529,
	7529,
	3285,
	4620,
	4620,
	4620,
	883,
	883,
	597,
	4788,
	8234,
	8234,
	8234,
	10375,
	2554,
	9773,
	8751,
	8751,
	6779,
	6779,
	6779,
	9428,
	9428,
	4448,
	6779,
	6779,
	6779,
	5327,
	5327,
	9232,
	9232,
	9232,
	9232,
	9232,
	9232,
	9232,
	3595,
	2554,
	7934,
	4778,
	4778,
	6779,
	6779,
	6560,
	6560,
	6560,
	6560,
	6560,
	6560,
	6560,
	6560,
	7378,
	7378,
	8751,
	8751,
	10601,
	9635,
	2554,
	2043,
	4778,
	4778,
	5216,
	3595,
	3595,
	2191,
	2191,
	597,
	597,
	597,
	11218,
	2570,
	9106,
	9106,
	9106,
}
func getMerchantFoodInfoJson(host string, resID int64 ,foodId int64) string{
	url := fmt.Sprintf("%s%s?restaurant_id=%d&id=%d", host, FOOD_INFO, resID,foodId)
	//println(url)
	method := "GET"

	client := &http.Client {
	}
	req, err := http.NewRequest(method, url, nil)

	if err != nil {
		fmt.Println(err)
		return ""
	}
	req.Header.Add("Authorization", "Bearer ZTuuwuiQsTrIsNstmyKcqnqWJIrUPOFeSUyvNSnZ")
	req.Header.Add("osType", "1")
	req.Header.Add("terminalId", "1")
	req.Header.Add("appVersion", "2.0")
	req.Header.Add("Accept-Language", "en;q=1")
	req.Header.Add("screenSize", "414*736")
	req.Header.Add("phoneBrand", "apple")
	req.Header.Add("User-Agent", "Dinner/1.0 (iPhone; iOS 9.3; Scale/3.00)")
	req.Header.Add("lang", "0")
	req.Header.Add("appBuild", "1")
	req.Header.Add("appTime", "1467103218.903170")
	req.Header.Add("searialNumber", "123456789")
	req.Header.Add("osVersion", "1.0")
	req.Header.Add("parentCategoryId", "1")
	req.Header.Add("Accept", "application/json")
	req.Header.Add("refresh_token", "")
	req.Header.Add("res_id", "")
	req.Header.Add("almas", "1")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return ""
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return ""
	}
	return string(body)
}
//foods-category
func TestMerchantFoodCategory(t *testing.T) {
	for index, resID := range resArr {

		phpJson := getMerchantFoodInfoJson(GO_API, int64(resID), int64(foodId[index]))
		goJson := getMerchantFoodInfoJson(GO_API, int64(resID), int64(foodId[index]))
		var phpMap map[string]interface{}
		var goMap map[string]interface{}
		json.Unmarshal([]byte(phpJson),&phpMap)
		json.Unmarshal([]byte(goJson),&goMap)

		phpDataStr,_ := json.Marshal(phpMap["data"])
		goDataStr,_ := json.Marshal(goMap["data"])
		//判断是否相等，单元测是
		if string(phpDataStr) != string(goDataStr) {
			print("phpJson:", phpJson,"\n")
			print("goJson:", goJson,"\n")
			t.Error("TestMerchantFoodCategory failed", "resID:", resID, "foodId:", foodId[index])
			return
		}
		println("index:", index, "resID:", resID, "TestMerchantFoodCategory success")

	}

	//{"data":[{"id":18,"name":"副食","state":1},{"id":19,"name":"生活服务","state":1}],"msg":"操作成功","status":200,"time":"2024-06-22 12:58:16"}

}
func TestTime000(t *testing.T)  {
	timeStr := "00:00:00"

	// 解析时间字符串
	parsedTime, err := time.Parse("15:04:05", timeStr)
	if err != nil {
		fmt.Println("解析时间出错:", err)
		return
	}

	// 格式化为所需格式
	formattedTime := parsedTime.Format("15:04")
	fmt.Println("格式化后的时间:", formattedTime)
}