package foodcategory

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"testing"
)

const GO_API = "http://localhost:8085"
const PHP_API = "http://api.mulazim.cc"
const FOOD_CATEGORY = "/zh/merchant/v1/admin/foods-category?restaurant_id="
//定义数组 [211,22,33] 变量
var resArr = []int{
	11310,
	3854,
	6755,
	4355,
	6054,
	8678,
	8618,
	10856,
	9431,
	6942,
	2554,
	9687,
	2570,
	3523,
	3930,
	9624,
	2586,
	11312,
	883,
	8747,
	9091,
	7269,
	934,
	3626,
	6779,
	1930,
	11245,
	2327,
	2553,
	597,
	5834,
	2423,
	3948,
	3285,
	8112,
	3129,
	4781,
	2390,
	9863,
	7529,
	8038,
	5088,
	5090,
	5390,
	11335,
	839,
	887,
	6163,
	5056,
	5593,
	8776,
	2525,
	7004,
	5326,
	9923,
	4884,
	11407,
	2386,
	10641,
	6238,
	6019,
	8388,
	4913,
	7015,
	3944,
	8034,
	3763,
	5819,
	4998,
	4428,
	10098,
	6832,
	4788,
	8445,
	6241,
	5966,
	9512,
	3891,
	3272,
	7148,
	9773,
	7434,
	923,
	4471,
	5372,
	9076,
	2138,
	11420,
	10900,
	6573,
	6257,
	6314,
	5272,
	1738,
	3503,
	5795,
	4424,
	3595,
	3624,
	3875,
}
func getMerchantFoodCategoryJson(host string, resID int64 ) string{
	url := fmt.Sprintf("%s%s%d", host, FOOD_CATEGORY, resID)
	method := "GET"

	client := &http.Client {
	}
	req, err := http.NewRequest(method, url, nil)

	if err != nil {
		fmt.Println(err)
		return ""
	}
	req.Header.Add("Authorization", "Bearer ZTuuwuiQsTrIsNstmyKcqnqWJIrUPOFeSUyvNSnZ")
	req.Header.Add("osType", "1")
	req.Header.Add("terminalId", "1")
	req.Header.Add("appVersion", "2.0")
	req.Header.Add("Accept-Language", "en;q=1")
	req.Header.Add("screenSize", "414*736")
	req.Header.Add("phoneBrand", "apple")
	req.Header.Add("User-Agent", "Dinner/1.0 (iPhone; iOS 9.3; Scale/3.00)")
	req.Header.Add("lang", "0")
	req.Header.Add("appBuild", "1")
	req.Header.Add("appTime", "1467103218.903170")
	req.Header.Add("searialNumber", "123456789")
	req.Header.Add("osVersion", "1.0")
	req.Header.Add("parentCategoryId", "1")
	req.Header.Add("Accept", "application/json")
	req.Header.Add("refresh_token", "")
	req.Header.Add("res_id", "")
	req.Header.Add("almas", "1")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return ""
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return ""
	}
	return string(body)
}
//foods-category
func TestMerchantFoodCategory(t *testing.T) {
	for index, resID := range resArr {
		phpJson := getMerchantFoodCategoryJson(PHP_API, int64(resID))
		goJson := getMerchantFoodCategoryJson(GO_API, int64(resID))
		var phpMap map[string]interface{}
		var goMap map[string]interface{}
		json.Unmarshal([]byte(phpJson),&phpMap)
		json.Unmarshal([]byte(goJson),&goMap)

		phpDataStr,_ := json.Marshal(phpMap["data"])
		goDataStr,_ := json.Marshal(goMap["data"])
		//判断是否相等，单元测是
		if string(phpDataStr) != string(goDataStr) {
			print("phpJson:", phpJson,"\n")
			print("goJson:", goJson,"\n")
			t.Error("TestMerchantFoodCategory failed")
			return
		}
		println("index:", index, "resID:", resID, "TestMerchantFoodCategory success")
		//time.Sleep(1 * time.Second)
	}

	//{"data":[{"id":18,"name":"副食","state":1},{"id":19,"name":"生活服务","state":1}],"msg":"操作成功","status":200,"time":"2024-06-22 12:58:16"}

}
