package tests

import (
	"fmt"
	"mulazim-api/tools"
	"net/url"
	"testing"
)

func TestDecryptTest (t *testing.T) {
    keyStr :="mulazimifexXhDiPeZfNBZNwnAHkbqHa" //"mulazim"+tools.RandStr(25)
    key := []byte(keyStr) // Key length must be 16, 24, or 32 bytes
    plainText := "18999371246"
    ivStr :="1234567890123456"
    encryptedText, err :=tools.Encrypt(plainText, key,[]byte(ivStr))
    if err != nil {
        fmt.Println("Encryption error:", err)
        return
    }

    fmt.Println("Encrypted text:", encryptedText,",",url.QueryEscape(encryptedText))
    

    decryptedText, err :=tools.Decrypt(encryptedText, key,[]byte(ivStr))
    if err != nil {
        fmt.Println("Decryption error:", err)
        return
    }

    fmt.Println("Decrypted text:", decryptedText)
}

