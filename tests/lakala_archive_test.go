package tests

import (
	"fmt"
	"mulazim-api/services/merchant/lakala"
	"mulazim-api/tools"
	"testing"
)

//
// TestLakalaArchive
//  @Description: 归档 因为错误原因 不能归档的数据
//  @author: <PERSON>m<PERSON>
//  @Time: 2023-07-22 12:56:07
//  @param t *testing.T
//
func TestLakalaArchive(t *testing.T) {
	db :=tools.GetDB()

	fmt.Println("unit test end")
	
	service :=lakala.GetLakalaService()
	
	lkMap := make([]map[string]interface{},0)
	fields :="t_self_sign_merchant_info.id,t_self_sign_merchant_info.restaurant_id,t_self_sign_merchant_info.member_no"
	db.Table("t_self_sign_merchant_info").
	Joins("LEFT JOIN t_self_sign_merchant_info_archive ON t_self_sign_merchant_info.member_no = t_self_sign_merchant_info_archive.member_no").
	Where("t_self_sign_merchant_info.lakala_verify_state = 2 AND t_self_sign_merchant_info_archive.id IS NULL").
	Select(fields).
	Group("t_self_sign_merchant_info.member_no").
	Scan(&lkMap)
	
	for k, v := range lkMap {
		resId :=tools.ToString(v["restaurant_id"])
		member_no :=tools.ToString(v["member_no"])
		fmt.Println(len(lkMap),k,resId,member_no)
		service.ArchiveData(resId,member_no)
		
	}
	

	fmt.Println("unit test end")
}
