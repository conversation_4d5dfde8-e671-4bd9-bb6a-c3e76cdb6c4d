package shipper

import (
	"encoding/json"
	"github.com/go-playground/assert/v2"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/models"
	"mulazim-api/models/shipment"
	"mulazim-api/services/shipper"
	"testing"
)

func genFixShipmentPriceRuleContent(price int) ([]byte, error) {
	var fixShipmentFeeRule shipment.FixShipmentFeeRule
	fixShipmentFeeRule.FixShipmentFee = 200

	return json.Marshal([]shipment.FixShipmentFeeRule{fixShipmentFeeRule})
}

func createFixShipmentPriceTemplate(price int) (shipment.ShipperIncomeTemplate, error) {
	// 创建固定配送费模板

	var template shipment.ShipperIncomeTemplate
	template.RuleType = shipment.RuleTypeFixed
	content, err := genFixShipmentPriceRuleContent(price)
	if err != nil {
		return template, err
	}
	ruleContent := string(content)
	template.RuleContent = &ruleContent
	return template, err
}

// TestFixShipmentPrice 测试固定配送费
func TestFixShipmentPrice(t *testing.T) {
	var price int = 200
	template, err := createFixShipmentPriceTemplate(price)
	assert.Equal(t, err, nil)
	var order models.OrderToday = models.OrderToday{
		BookingTime: carbon.Now("PRC").ToDateTimeString("PRC"),
	}
	var shipperFeeService = new(shipper.ShipperFeeService)
	orderPrice, specialTimePrice, specialWeatherPrice, err1 := shipperFeeService.CalculateShipperFee(10270,template, order, 0)
	assert.Equal(t, err1, nil)
	assert.Equal(t, price, orderPrice)
	assert.Equal(t, 0, specialTimePrice)
	assert.Equal(t, 0, specialWeatherPrice)
}
