package shipper

import (
	"encoding/json"
	"github.com/go-playground/assert/v2"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/models"
	"mulazim-api/models/shipment"
	"mulazim-api/services/shipper"
	"testing"
)

// TestDistanceBaseShipmentPrice 测试按距离计算配送费
func TestDistanceBaseShipmentPrice(t *testing.T) {
	type TestCase struct {
		Distance int
		Price    int
	}
	var testCases []TestCase
	testCases = append(testCases,
		TestCase{
			Distance: 500,
			Price:    200,
		},
		TestCase{
			Distance: 1500,
			Price:    300,
		},
		TestCase{
			Distance: 2500,
			Price:    400,
		},
	)
	var distanceBaseShipmentFeeRules = genDistanceBaseShipmentPriceRuleContent()
	template, err := createDIstanceBaseShipmentPriceTemplate(distanceBaseShipmentFeeRules)
	assert.Equal(t, err, nil)
	var order = models.OrderToday{
		BookingTime: carbon.Now("PRC").ToDateTimeString("PRC"),
	}
	var shipperFeeService = new(shipper.ShipperFeeService)
	for _, testCase := range testCases {
		order.Distance = float64(testCase.Distance) / 1000
		orderPrice, specialTimePrice, specialWeatherPrice, err := shipperFeeService.CalculateShipperFee(10270,template, order, 0)
		assert.Equal(t, err, nil)
		assert.Equal(t, testCase.Price, orderPrice)
		assert.Equal(t, specialWeatherPrice, 0)
		assert.Equal(t, specialTimePrice, 0)
	}
}

func createDIstanceBaseShipmentPriceTemplate(rules []shipment.DistanceBaseShipmentFeeRule) (shipment.ShipperIncomeTemplate, error) {
	// 创建按距离计算配送费模板

	var template shipment.ShipperIncomeTemplate
	template.RuleType = shipment.RuleTypeDistance
	content, err := json.Marshal(rules)
	if err != nil {
		return template, err
	}
	ruleContent := string(content)
	template.RuleContent = &ruleContent
	return template, err
}

func genDistanceBaseShipmentPriceRuleContent() []shipment.DistanceBaseShipmentFeeRule {
	var distanceBaseShipmentFeeRules []shipment.DistanceBaseShipmentFeeRule
	distanceBaseShipmentFeeRules = append(distanceBaseShipmentFeeRules, shipment.DistanceBaseShipmentFeeRule{
		Distance:    1000,
		ShipmentFee: 200,
	})
	distanceBaseShipmentFeeRules = append(distanceBaseShipmentFeeRules, shipment.DistanceBaseShipmentFeeRule{
		Distance:    2000,
		ShipmentFee: 300,
	})
	distanceBaseShipmentFeeRules = append(distanceBaseShipmentFeeRules, shipment.DistanceBaseShipmentFeeRule{
		Distance:    999000,
		ShipmentFee: 400,
	})
	return distanceBaseShipmentFeeRules
}
