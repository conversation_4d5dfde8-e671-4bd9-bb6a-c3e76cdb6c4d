package shipper

import (
	"encoding/json"
	"fmt"
	"mulazim-api/tests/helper"
	"mulazim-api/tests/login"
	"mulazim-api/tools"
	"testing"
)
//
// TestShipperAdminList
//  @Description: 测试配送员管理员获取配送员列表接口
//  @param t
//
func TestShipperAdminList(t *testing.T) {
	token,serial := login.ShipperLogin()
	tools.Logger.Infof("token:%s serial:%s",token,serial)
	data := map[string]interface{}{

	}
	rtn := helper.SendGinGet("/ug/shipper/v2/admin/all-shipper-list", helper.MapToUrlValues(data), token, serial)
	//tools.Logger.Infof("rtn:%s",rtn.Body.String())
	beatifulPrintJson(rtn.Body.String())
}
func beatifulPrintJson(body string)  {
	var m map[string]interface{}
	err := json.Unmarshal([]byte(body), &m)
	// 使用json.MarshalIndent进行JSON美化打印
	prettyJSON, err := json.MarshalIndent(m, "", "  ")
	if err != nil {
		fmt.Println("Error marshaling JSON:", err)
		return
	}

	// 打印美化后的JSON
	fmt.Println(string(prettyJSON))

}