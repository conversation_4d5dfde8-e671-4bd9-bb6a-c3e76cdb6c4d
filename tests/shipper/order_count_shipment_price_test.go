package shipper

import (
	"encoding/json"
	"github.com/go-playground/assert/v2"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/models"
	"mulazim-api/models/shipment"
	"mulazim-api/services/shipper"
	"testing"
)

func genOrderCountBaseShipmentFeeRules() []shipment.OrderCountBaseShipmentFeeRule {
	var rules []shipment.OrderCountBaseShipmentFeeRule
	rules = append(rules,
		shipment.OrderCountBaseShipmentFeeRule{
			StartOrderCount: 5,
			ShipmentFee:     400,
		},
		shipment.OrderCountBaseShipmentFeeRule{
			StartOrderCount: 10,
			ShipmentFee:     500,
		},
		shipment.OrderCountBaseShipmentFeeRule{
			StartOrderCount: 99999,
			ShipmentFee:     600,
		},
	)
	return rules
}

func createOrderCountBaseShipmentPriceTemplate(rules []shipment.OrderCountBaseShipmentFeeRule) (shipment.ShipperIncomeTemplate, error) {
	// 创建按订单数量计算配送费模板

	var template shipment.ShipperIncomeTemplate
	template.RuleType = shipment.RuleTypeOrder
	content, err := json.Marshal(rules)
	if err != nil {
		return template, err
	}
	ruleContent := string(content)
	template.RuleContent = &ruleContent
	return template, err
}

// TestOrderCountShipmentPrice 测试按订单数量计算配送费
func TestOrderCountShipmentPrice(t *testing.T) {
	type TestCase struct {
		NthOrder int
		Price    int
	}
	var testCases []TestCase
	testCases = append(testCases,
		TestCase{
			NthOrder: 0,
			Price:    400,
		},
		TestCase{
			NthOrder: 7,
			Price:    500,
		},
		TestCase{
			NthOrder: 10,
			Price:    600,
		},
		// TestCase{
		// 	NthOrder: 250,
		// 	Price:    300,
		// },
		// TestCase{
		// 	NthOrder: 500,
		// 	Price:    400,
		// },
	)
	var orderCountBaseShipmentFeeRules = genOrderCountBaseShipmentFeeRules()
	template, err := createOrderCountBaseShipmentPriceTemplate(orderCountBaseShipmentFeeRules)
	assert.Equal(t, err, nil)
	var shipperFeeService = new(shipper.ShipperFeeService)
	for _, testCase := range testCases {
		order := models.OrderToday{
			BookingTime: carbon.Now("PRC").ToDateTimeString("PRC"),
		}

		orderPrice, specialTimePrice, specialWeatherPrice, err := shipperFeeService.CalculateShipperFee(10521,template, order, testCase.NthOrder)
		assert.Equal(t, err, nil)
		assert.Equal(t, testCase.Price, orderPrice)
		assert.Equal(t, specialTimePrice, 0)
		assert.Equal(t, specialWeatherPrice, 0)
	}
}
