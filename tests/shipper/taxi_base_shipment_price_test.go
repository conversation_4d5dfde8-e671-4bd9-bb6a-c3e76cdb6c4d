package shipper

import (
	"encoding/json"
	"github.com/go-playground/assert/v2"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/models"
	"mulazim-api/models/shipment"
	"mulazim-api/services/shipper"
	"testing"
)

func TestTextBaseShipmentPrice(t *testing.T) {
	type TestCase struct {
		Distance int
		Price    int
	}
	var testCases []TestCase
	testCases = append(testCases,
		TestCase{
			Distance: 500,
			Price:    200,
		},
		TestCase{
			Distance: 2000,
			Price:    200,
		},
		TestCase{
			Distance: 2500,
			Price:    250,
		},
		TestCase{
			Distance: 3000,
			Price:    300,
		})
	template, err := genTaxiBaseShipmentPriceRuleContent()
	assert.Equal(t, err, nil)

	var shipperFeeService = new(shipper.ShipperFeeService)
	for _, testCase := range testCases {
		order := models.OrderToday{
			Distance:    float64(testCase.Distance) / 1000,
			BookingTime: carbon.Now("PRC").ToDateTimeString("PRC"),
		}
		orderPrice, specialTimePrice, specialWeatherPrice, err := shipperFeeService.CalculateShipperFee(10270,template, order, 0)
		assert.Equal(t, err, nil)
		assert.Equal(t, testCase.Price, orderPrice)
		assert.Equal(t, specialTimePrice, 0)
		assert.Equal(t, specialWeatherPrice, 0)
	}
}

func genTaxiBaseShipmentPriceRuleContent() (shipment.ShipperIncomeTemplate, error) {
	rules := []shipment.TaxiBaseShipmentFeeRule{
		shipment.TaxiBaseShipmentFeeRule{
			FixedStartFee:     200,
			Distance:          2000,
			PricePerKilometer: 100,
		},
	}
	template := shipment.ShipperIncomeTemplate{
		RuleType: shipment.RuleTypeCar,
	}
	content, err := json.Marshal(rules)
	if err != nil {
		return template, err
	}
	ruleContent := string(content)
	template.RuleContent = &ruleContent
	return template, err
}
