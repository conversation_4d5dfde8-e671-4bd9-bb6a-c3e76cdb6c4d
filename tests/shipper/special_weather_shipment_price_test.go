package shipper

import (
	"github.com/go-playground/assert/v2"
	"github.com/golang-module/carbon/v2"
	"mulazim-api/models"
	"mulazim-api/models/shipment"
	"mulazim-api/services/shipper"
	"mulazim-api/tools"
	"testing"
)

func TestSpecialWeatherShipmentPrice(t *testing.T) {
	var specialWeather = shipment.ShipperSpecialWeather{
		CityID:      1,
		AreaID:      1,
		StartTime:   carbon.Yesterday("PRC").Carbon2Time(),
		EndTime:     carbon.Tomorrow("PRC").Carbon2Time(),
		State:       shipment.SHIPPER_SPECIAL_WEATHER_SATE_ON,
		ShipmentFee: 200,
		NameUg:      "ئۇيغۇرچە نامى",
		NameZh:      "汉语名称",
	}
	rs := tools.GetDB().Create(&specialWeather)
	assert.Equal(t, int64(1), rs.RowsAffected)

	var price int = 200
	var err error
	template, err := createFixShipmentPriceTemplate(price)
	assert.Equal(t, err, nil)

	// 范围外测试
	var order models.OrderToday = models.OrderToday{
		Distance:    1,
		BookingTime: carbon.Now("PRC").ToDateTimeString(),
		AreaID:      1,
		CityID:      1,
	}
	var shipperFeeService = new(shipper.ShipperFeeService)
	orderPrice, specialTimePrice, specialWeatherPrice, err := shipperFeeService.CalculateShipperFee(10270,template, order, 0)
	assert.Equal(t, err, nil)
	assert.Equal(t, price, orderPrice)
	assert.Equal(t, 0, specialTimePrice)
	assert.Equal(t, 200, specialWeatherPrice)

	tools.GetDB().Delete(&specialWeather)

}
