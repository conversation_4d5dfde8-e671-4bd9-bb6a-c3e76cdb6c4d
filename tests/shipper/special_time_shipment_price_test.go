package shipper

import (
	"encoding/json"
	"github.com/go-playground/assert/v2"
	"mulazim-api/models"
	shipment "mulazim-api/models/shipment"
	"mulazim-api/services/shipper"
	"testing"
)

func createSpecialShipmentRule(template shipment.ShipperIncomeTemplate) shipment.ShipperIncomeTemplate {
	var distanceSteps []shipment.SpecialShipmentDistanceStep
	distanceSteps = append(
		distanceSteps,
		shipment.SpecialShipmentDistanceStep{
			Distance:    2000,
			ShipmentFee: 100,
		},
		shipment.SpecialShipmentDistanceStep{
			Distance:    3000,
			ShipmentFee: 150,
		},
		shipment.SpecialShipmentDistanceStep{
			Distance:    4000,
			ShipmentFee: 200,
		},
	)
	specialShipmentRule := shipment.SpecialShipmentFeeRule{
		StartTime:    "01:00",
		EndTime:      "06:00",
		DistanceStep: distanceSteps,
	}

	content, err := json.Marshal([]shipment.SpecialShipmentFeeRule{specialShipmentRule})
	if err != nil {

	}
	specialShipmentContent := string(content)
	template.SpecialShipmentContent = &specialShipmentContent
	template.SpecialShipmentState = shipment.SpecialShipmentStateOpen
	return template
}

func TestSpecialShipmentPrice(t *testing.T) {
	var price int = 200
	var err error
	template, err := createFixShipmentPriceTemplate(price)
	assert.Equal(t, err, nil)
	template = createSpecialShipmentRule(template)
	assert.Equal(t, err, nil)

	// 范围外测试
	var order models.OrderToday = models.OrderToday{
		Distance:    1,
		BookingTime: "2023-11-10 00:30:00",
	}
	var shipperFeeService = new(shipper.ShipperFeeService)
	var orderPrice, specialTimePrice, specialWeatherPrice int
	orderPrice, specialTimePrice, specialWeatherPrice, err = shipperFeeService.CalculateShipperFee(10270,template, order, 0)
	assert.Equal(t, err, nil)
	assert.Equal(t, price, orderPrice)
	assert.Equal(t, specialTimePrice, 0)
	assert.Equal(t, specialWeatherPrice, 0)
	// 范围内测试
	order = models.OrderToday{
		Distance:    1,
		BookingTime: "2023-11-10 01:30:00", //2023-11-22 17:49:17
	}
	shipperFeeService = new(shipper.ShipperFeeService)
	orderPrice, specialTimePrice, specialWeatherPrice, err = shipperFeeService.CalculateShipperFee(10270,template, order, 0)
	assert.Equal(t, err, nil)

	assert.Equal(t, 100, specialTimePrice)
	assert.Equal(t, specialWeatherPrice, 0)

	// 范围内测试
	order = models.OrderToday{
		Distance:    2,
		BookingTime: "2023-11-10 01:30:00",
	}
	shipperFeeService = new(shipper.ShipperFeeService)
	orderPrice, specialTimePrice, specialWeatherPrice, err = shipperFeeService.CalculateShipperFee(10270,template, order, 0)
	assert.Equal(t, err, nil)
	assert.Equal(t, price, orderPrice)
	assert.Equal(t, specialTimePrice, 100)
	assert.Equal(t, specialWeatherPrice, 0)

	// 范围内测试
	order = models.OrderToday{
		Distance:    5,
		BookingTime: "2023-11-10 01:30:00",
	}
	shipperFeeService = new(shipper.ShipperFeeService)
	orderPrice, specialTimePrice, specialWeatherPrice, err = shipperFeeService.CalculateShipperFee(10270,template, order, 0)
	assert.NotEqual(t, err, nil)
	assert.Equal(t, price, orderPrice)
	assert.Equal(t, specialTimePrice, 0)
	assert.Equal(t, specialWeatherPrice, 0)
}
