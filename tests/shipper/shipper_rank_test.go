package shipper

import (
	"encoding/json"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/models"
	"mulazim-api/tools"
	"testing"

	shipperResources "mulazim-api/resources/shipper"

	"github.com/golang-module/carbon/v2"
)
func TestShipperRank(t *testing.T) {

    month :="2025-07"
    //获取本月的每周的周一的日期 
    days,days1,_ := tools.GetDaysOfMonthAndDay(month,1)
    // tools.Logger.Info("days",days,days1)

    now := carbon.Now().SetTimezone(configs.AsiaShanghai)
    fmt.Println(now.DayOfWeek(),",",now)

    monthNow:= now.Month()
    year :=now.Year()
    firstDayOfMonth := carbon.NewCarbon().SetDate(year, monthNow, 1)

    // Calculate how many days have passed since the beginning of the month
    daysPassed := now.DiffInDays(firstDayOfMonth)

    // Calculate the week number in the month
    weekNumber := (daysPassed / 7) + 1 // Weeks start from 1

    // Get the day of the week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
    dayOfWeek := now.DayOfWeek()

    fmt.Printf("The date is in week %d of the month.\n", weekNumber)
    fmt.Printf("The day of the week is %d (0=Sunday, 1=Monday, ..., 6=Saturday).\n", dayOfWeek)

    
    
    fmt.Println(days1)
    db :=tools.GetDB()
    shipperId := 4637
    var shipperRankHistory []models.ShipperRankHistory
    db.Model(&models.ShipperRankHistory{}).
        Where("shipper_id = ?",shipperId).
        Where("created_at >= ?",days[0]).
        Where("created_at < ?",carbon.Parse(days[len(days)-1]).SetTimezone(configs.AsiaShanghai).AddDays(1).Format("Y-m-d")).
        Find(&shipperRankHistory)
    var weekDataItem []shipperResources.RankStatisticsEntityItem

    detailItems :=[]map[string]interface{}{
        {"key1":"base_score","key2":"","name_zh":"基础分","name_ug":"基础分"},
        
        {"key1":"delivered_on_time_order_count","key2":"delivered_on_time","name_zh":"准时送达","name_ug":"准时送达"},

        {"key1":"positive_reviews_count","key2":"positive_reviews_score","name_zh":"好评","name_ug":"好评"},
        
        {"key1":"mild_lateness_count","key2":"mild_lateness_deduct","name_zh":	"迟到(5分钟之内）","name_ug":"迟到(5分钟之内)"},
        
        {"key1":"moderate_lateness_count","key2":"moderate_lateness_deduct","name_zh":	"迟到(5-10分钟)","name_ug":"迟到(5-10分钟)"},
        
        {"key1":"severe_lateness_count","key2":"severe_lateness_deduct","name_zh":"严重迟到","name_ug":"严重迟到"},
        
        {"key1":"negative_reviews_count","key2":"negative_reviews_deduct","name_zh":"差评","name_ug":"差评"},
        
        {"key1":"complaints_count","key2":"complaints_deduct","name_zh":"投诉","name_ug":"投诉"},
        
        {"key1":"early_delivery_count","key2":"early_delivery_deduct","name_zh":"早送(20分钟)","name_ug":"早送(20分钟)"},

    }
    // fmt.Println(detailItems)

     for k, v := range days1 {
        for _, vv := range shipperRankHistory {
            
            
            if v == carbon.Time2Carbon(vv.CreatedAt).SetTimezone(configs.AsiaShanghai).Format("Y-m-d") {

                var details  []shipperResources.RankStatisticsEntityItemDetails
                for _, vvv := range detailItems { 
                    key1 :=tools.ToString(vvv["key1"])
                    value1,_:=tools.GetValueByGormColumn(vv,key1)
                    fmt.Println(key1,",",value1)
                    key2 :=tools.ToString(vvv["key2"])
                    value2 :=float64(0)
                    if len(key2) > 0 {    
                        value2Str,_:=tools.GetValueByGormColumn(vv,key2)
                        fmt.Println(key2,",",value2Str)
                        value2 =tools.ToFloat64(value2Str)
                    }
                    details = append(details, shipperResources.RankStatisticsEntityItemDetails{
                        Name: tools.ToString(vvv["name_zh"]),
                        Count: tools.ToInt(value1),
                        Score: tools.ToFloat64(value2),
                    })
                }
            
                
                
                weekDataItem = append(weekDataItem, shipperResources.RankStatisticsEntityItem{
                    Name: fmt.Sprintf("%d-%s", (k+1), "week"),
                    Rank: int(vv.Rank),
                    Details:details,
                    Date:v,
                }) 
            }
        }
     }
     b,_:=json.Marshal(weekDataItem)
    //  tools.Logger.Info("items",string(b)) 
     fmt.Println(string(b))


}

func TestCmsShipperRankStat(t *testing.T) {

}