package shipper

import (
	"fmt"
	"mulazim-api/constants"
	"mulazim-api/jobs"
	"mulazim-api/models"
	"mulazim-api/tools"
	"testing"
)

func TestAutoDIspatchPush(t *testing.T) {

	job := jobs.NewPushJob()
	
    channelType := 13
    sound := "system_assigned_order_to_shipper"
    contentUg := constants.ContentShipperAutoDispatchAssignOrderUg
    contentZh := constants.ContentShipperAutoDispatchAssignOrderZh
	
	job.PushData(jobs.PushData{
		UserId:   2394,
		UserType: constants.PushUserTypeShipper,
		PushContent: jobs.PushContent{
			TitleUg:   fmt.Sprintf("%s %s", tools.SubString("سىناق", 10), "سناق"),
			TitleZh:   fmt.Sprintf("%s %s", tools.SubString("测试", 10),"测试"),
			ContentUg: contentUg,
			ContentZh: contentZh,
			Params:    map[string]interface{}{},
		},
		Client:      models.PushDeviceClientShipper,
		Sound:       sound,
		ChannelType: channelType,
		AdminId:     2394,
	})

}