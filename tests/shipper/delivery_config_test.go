package shipper

import (
	"fmt"
	"mulazim-api/models/shipment"
	cmsServices "mulazim-api/services/cms"
	"mulazim-api/tools"
	"testing"
)

func TestDeliveryAreaLogs(t *testing.T) {
    _deliveryAreaSvc     := cmsServices.NewDeliveryAreaService(nil)
    var deliveryArea []shipment.DeliveryAreas
    tools.Db.Model(shipment.DeliveryAreas{}).Preload("DeliveryFees").Where("id = ?",64).Find(&deliveryArea)
    _deliveryAreaSvc.RecordDeliveryAreaListLog(deliveryArea)
    fmt.Println("finish.....")
}


func TestRecordDeliveryFeeLog(t *testing.T) {
    _deliveryAreaSvc     := cmsServices.NewDeliveryFeeService(nil)
    var deliveryFees []shipment.DeliveryFees
    tools.Db.Model(shipment.DeliveryFees{}).Where("id = ?",64).Find(&deliveryFees)
    _deliveryAreaSvc.RecordDeliveryFeeListLog(deliveryFees)
    fmt.Println("finish.....")
}