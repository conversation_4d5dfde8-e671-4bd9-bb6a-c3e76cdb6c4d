package tests

import (
	"encoding/json"
	"fmt"
	"mulazim-api/tests/helper"
	"mulazim-api/tests/login"
	"mulazim-api/tools"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/go-playground/assert/v2"
	"github.com/golang-module/carbon/v2"
)

// getCode
//
//	@Description: 解析服务器返回数据 获取Status
//	@author: Alimjan
//	@Time: 2023-03-07 17:48:28
//	@param b string
//	@return int
func getCode(b string) int {
	var m map[string]interface{}
	err := json.Unmarshal([]byte(b), &m)
	if err != nil {
		println(err.Error())
		return 0
	}
	var rtn int
	switch m["status"].(type) {
	case int:
		rtn = m["status"].(int)
		break
	case float64:
		rtn = int(m["status"].(float64))
		break
	case float32:
		rtn = int(m["status"].(float32))
		break

	default:
		//fmt.Printf("出错了 %s\n", reflect.TypeOf(v))
		rtn = 0
		break
	}
	return rtn

}

// TestPriceDiscountCreate3
//
//	@Description: 创建两个活动，判断返回数据
//	@author: Alimjan
//	@Time: 2023-03-04 19:03:21
//	@param t *testing.T
func TestPriceDiscountCreate(t *testing.T) {
	_, resId, _ := login.MerchantLogin()
	db := tools.Db
	db.Table("t_marketing").Where("restaurant_id = ?", resId).Delete(map[string]interface{}{})
	w := createOne(
		"2023-03-01",
		"2023-03-04",
		"10:00",
		"12:00",
		"13:00",
		"15:00",
		"18:00",
		"23:00",
	)
	assert.Equal(t, 200, getCode(w.Body.String()))

	w = createOne(
		"2023-03-02",
		"2023-03-03",
		"10:00",
		"12:00",
		"13:00",
		"15:00",
		"20:00",
		"23:00",
	)

	assert.Equal(t, 200, getCode(w.Body.String()))

}
func createOne(
	beginDate string,
	endDate string,
	time1_start string,
	time1_end string,
	time2_start string,
	time2_end string,
	time3_start string,
	time3_end string,
) *httptest.ResponseRecorder {
	token, resId, serial := login.MerchantLogin()

	data := url.Values{}
	data.Set("name_ug", "پائالىيەت"+tools.RandStr(6))
	data.Set("name_zh", "活动"+tools.RandStr(6))
	data.Set("restaurant_id", resId)
	data.Set("marketing_type", "1")
	data.Set("type", "2")
	data.Set("begin_date", beginDate)
	data.Set("end_date", endDate)
	data.Set("full_week_state", "1")
	data.Set("day1", "1")
	data.Set("day2", "1")
	data.Set("day3", "1")
	data.Set("day4", "1")
	data.Set("day5", "1")
	data.Set("day6", "1")
	data.Set("day7", "1")
	data.Set("full_time_state", "1")
	data.Set("time1_start", time1_start)
	data.Set("time1_end", time1_end)

	data.Set("time2_start", time2_start)
	data.Set("time2_end", time2_end)
	data.Set("time3_start", time3_start)
	data.Set("time3_end", time3_end)

	data.Set("auto_continue", "1")
	data.Set("steps", "[{\"price_start\":\"10\",\"price_reduce\":\"2\"},{\"price_start\":\"15\",\"price_reduce\":\"3\"}]")
	data.Set("foods", "18530,18531,18532,102435,102438")

	return helper.SendGinPost("/ug/merchant/v1/marketing/create", data, token, serial,nil)
}

func TestPriceDiscountList(t *testing.T) {
	_, resId, _ := login.MerchantLogin()
	db := tools.Db

	db.Table("t_marketing").Where("restaurant_id = ?", resId).Delete(map[string]interface{}{})

	w := createOne(
		"2023-03-02",
		"2023-03-03",
		"10:00",
		"12:00",
		"13:00",
		"15:00",
		"20:00",
		"23:00",
	)
	assert.Equal(t, 200, getCode(w.Body.String()))

	token, resId, serial := login.MerchantLogin()

	data := url.Values{}
	data.Set("restaurant_id", resId)
	data.Set("state", "0")
	data.Set("page", "0")
	data.Set("limit", "10")
	data.Set("marketing_type", "1")
	w = helper.SendGinGet("/ug/merchant/v1/marketing/list", data, token, serial)
	assert.Equal(t, 200, getCode(w.Body.String()))

	var m map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &m)
	assert.Equal(t, 1, len(m["data"].(map[string]interface{})["items"].([]interface{})))

}

// TestUpdateState
//
//	@Description: 修改状态 创建两个活动第一次修改成功，第二次应该失败
//	@author: Alimjan
//	@Time: 2023-03-04 19:05:30
//	@param t *testing.T
func TestUpdateState(t *testing.T) {
	_, resId, _ := login.MerchantLogin()
	db := tools.Db

	db.Table("t_marketing").Where("restaurant_id = ?", resId).Delete(map[string]interface{}{})

	w := createOne(
		"2023-03-02",
		"2023-03-03",
		"10:00",
		"12:00",
		"13:00",
		"15:00",
		"17:00",
		"18:00",
	)
	//assert.Equal(t,"200",(w.Body.String()))
	assert.Equal(t, 200, getCode(w.Body.String()))

	w = createOne(
		"2023-03-02",
		"2023-03-03",
		"12:01",
		"12:30",
		"18:00",
		"20:00",
		"22:01",
		"23:00",
	)
	assert.Equal(t, 200, getCode(w.Body.String()))

	var m []map[string]interface{}
	db.Table("t_marketing").Select("id").Where("restaurant_id = ?", resId).Scan(&m)
	id1 := int(m[0]["id"].(int32))
	id2 := int(m[1]["id"].(int32))

	token, resId, serial := login.MerchantLogin()
	data := url.Values{}
	data.Set("id", fmt.Sprintf("%d", id1))
	data.Set("state", "1")
	w = helper.SendGinPost("/ug/merchant/v1/marketing/update-state", data, token, serial,nil)

	assert.Equal(t, 200, getCode(w.Body.String()))

	data.Set("id", fmt.Sprintf("%d", id2))
	data.Set("state", "1")
	w = helper.SendGinPost("/ug/merchant/v1/marketing/update-state", data, token, serial,nil)
	//assert.Equal(t,"200",w.Body.String())
	assert.Equal(t, 200, getCode(w.Body.String()))

}

func updateState(t *testing.T) {
	token, resId, serial := login.MerchantLogin()
	db := tools.Db
	var m []map[string]interface{}
	db.Table("t_marketing").Select("id").Where("restaurant_id = ?", resId).Scan(&m)
	id1 := int(m[0]["id"].(int32))

	data := url.Values{}
	data.Set("id", fmt.Sprintf("%d", id1))
	data.Set("state", "1")

	w := helper.SendGinPost("/ug/merchant/v1/marketing/update-state", data, token, serial,nil)
	assert.Equal(t, 200, getCode(w.Body.String()))
	db.Table("t_marketing").Where("restaurant_id = ?", resId).Update("running_state", 1)

}

// TestWechatCreateOrder1
//
//	@Description: 对美食
//	@author: Alimjan
//	@Time: 2023-03-07 18:58:50
//	@param t *testing.T
func TestWechatCreateOrder1(t *testing.T) {
	_, resId, _ := login.MerchantLogin()
	db := tools.Db

	db.Table("t_marketing").Where("restaurant_id = ?", resId).Delete(map[string]interface{}{})

	token, resId, serial := login.MerchantLogin()

	data := url.Values{}
	data.Set("name_ug", "پائالىيەت"+tools.RandStr(6))
	data.Set("name_zh", "活动"+tools.RandStr(6))
	data.Set("restaurant_id", resId)
	data.Set("marketing_type", "1")
	data.Set("type", "2")
	data.Set("begin_date", tools.Today("Asia/Shanghai").ToDateString())
	data.Set("end_date", carbon.Tomorrow("Asia/Shanghai").ToDateString())
	data.Set("full_week_state", "1")
	data.Set("day1", "1")
	data.Set("day2", "1")
	data.Set("day3", "1")
	data.Set("day4", "1")
	data.Set("day5", "1")
	data.Set("day6", "1")
	data.Set("day7", "1")
	data.Set("full_time_state", "1")
	data.Set("time1_start", "08:00")
	data.Set("time1_end", "20:00")

	data.Set("time2_start", "21:00")
	data.Set("time2_end", "22:00")
	data.Set("time3_start", "23:00")
	data.Set("time3_end", "23:30")

	data.Set("auto_continue", "1")
	data.Set("steps", "[{\"price_start\":\"10\",\"price_reduce\":\"2\"},{\"price_start\":\"15\",\"price_reduce\":\"3\"}]")
	data.Set("foods", "18530,18531,18532,102435,102438")

	w := helper.SendGinPost("/ug/merchant/v1/marketing/create", data, token, serial,nil)

	//assert.Equal(t,"200",(w.Body.String()))
	assert.Equal(t, 200, getCode(w.Body.String()))

	updateState(t)

	wechatToken, _, wechatSerial := login.WechatLogin()

	data = url.Values{}
	url := "https://apiv2.almas.biz/ug/v1/order/create"

	data.Set("restaurant_id", resId)
	data.Set("address_id", "41")
	data.Set("timezone", "CST")
	data.Set("booking_time", "2022-10-24 12:08:26")
	data.Set("description", "alimjan go test")
	data.Set("shipment", "2")
	data.Set("price", "100")
	data.Set("order_type", "1")
	data.Set("lunch_box_fee", "0")
	data.Set("foods[0][id]", "18530")
	data.Set("foods[0][price]", "100")
	data.Set("foods[0][count]", "1")
	data.Set("reduction_fee", "3")
	body := helper.SendHttpPost(url, data, wechatToken, wechatSerial)

	var m map[string]interface{}
	json.Unmarshal(body, &m)
	assert.Equal(t, 200, getCode(string(body)))
}

// TestWechatCreateOrder1
//
//	@Description: 对餐厅
//	@author: Alimjan
//	@Time: 2023-03-07 18:58:50
//	@param t *testing.T
func TestWechatCreateOrder2(t *testing.T) {
	_, resId, _ := login.MerchantLogin()
	db := tools.Db

	db.Table("t_marketing").Where("restaurant_id = ?", resId).Delete(map[string]interface{}{})

	token, resId, serial := login.MerchantLogin()

	data := url.Values{}
	data.Set("name_ug", "پائالىيەت"+tools.RandStr(6))
	data.Set("name_zh", "活动"+tools.RandStr(6))
	data.Set("restaurant_id", resId)
	data.Set("marketing_type", "1")
	data.Set("type", "1")
	data.Set("begin_date", tools.Today("Asia/Shanghai").ToDateString())
	data.Set("end_date", carbon.Tomorrow("Asia/Shanghai").ToDateString())
	data.Set("full_week_state", "1")
	data.Set("day1", "1")
	data.Set("day2", "1")
	data.Set("day3", "1")
	data.Set("day4", "1")
	data.Set("day5", "1")
	data.Set("day6", "1")
	data.Set("day7", "1")
	data.Set("full_time_state", "1")
	data.Set("time1_start", "08:00")
	data.Set("time1_end", "20:00")

	data.Set("time2_start", "21:00")
	data.Set("time2_end", "22:00")
	data.Set("time3_start", "23:00")
	data.Set("time3_end", "23:30")

	data.Set("auto_continue", "1")
	data.Set("steps", "[{\"price_start\":\"20\",\"price_reduce\":\"2\"},{\"price_start\":\"50\",\"price_reduce\":\"3\"}]")
	data.Set("foods", "")

	w := helper.SendGinPost("/ug/merchant/v1/marketing/create", data, token, serial,nil)

	//assert.Equal(t,"200",(w.Body.String()))
	assert.Equal(t, 200, getCode(w.Body.String()))

	updateState(t)

	wechatToken, _, wechatSerial := login.WechatLogin()

	data = url.Values{}
	url := "https://apiv2.almas.biz/ug/v1/order/create"

	data.Set("restaurant_id", resId)
	data.Set("address_id", "41")
	data.Set("timezone", "CST")
	data.Set("booking_time", "2022-10-24 12:08:26")
	data.Set("description", "alimjan go test")
	data.Set("shipment", "2")
	data.Set("price", "45")
	data.Set("order_type", "1")
	data.Set("lunch_box_fee", "0")
	data.Set("foods[0][id]", "18532")
	data.Set("foods[0][count]", "1")
	data.Set("foods[0][price]", "45")
	data.Set("reduction_fee", "2")
	body := helper.SendHttpPost(url, data, wechatToken, wechatSerial)

	var m map[string]interface{}
	json.Unmarshal(body, &m)
	assert.Equal(t, 200, getCode(string(body)))
}

// TestWechatCreateOrder3
//
//	@Description: 对还没到时间的订单下单
//	@author: Alimjan
//	@Time: 2023-03-07 19:00:51
//	@param t *testing.T
func TestWechatCreateOrder3(t *testing.T) {
	_, resId, _ := login.MerchantLogin()
	db := tools.Db

	db.Table("t_marketing").Where("restaurant_id = ?", resId).Delete(map[string]interface{}{})

	token, resId, serial := login.MerchantLogin()

	data := url.Values{}
	data.Set("name_ug", "پائالىيەت"+tools.RandStr(6))
	data.Set("name_zh", "活动"+tools.RandStr(6))
	data.Set("restaurant_id", resId)
	data.Set("marketing_type", "1")
	data.Set("type", "1")
	data.Set("begin_date", carbon.Tomorrow("Asia/Shanghai").ToDateString())
	data.Set("end_date", carbon.Tomorrow("Asia/Shanghai").AddDay().ToDateString())
	data.Set("full_week_state", "1")
	data.Set("day1", "1")
	data.Set("day2", "1")
	data.Set("day3", "1")
	data.Set("day4", "1")
	data.Set("day5", "1")
	data.Set("day6", "1")
	data.Set("day7", "1")
	data.Set("full_time_state", "1")
	data.Set("time1_start", "08:00")
	data.Set("time1_end", "20:00")

	data.Set("time2_start", "21:00")
	data.Set("time2_end", "22:00")
	data.Set("time3_start", "23:00")
	data.Set("time3_end", "23:30")

	data.Set("auto_continue", "1")
	data.Set("steps", "[{\"price_start\":\"20\",\"price_reduce\":\"2\"},{\"price_start\":\"50\",\"price_reduce\":\"3\"}]")
	data.Set("foods", "")

	w := helper.SendGinPost("/ug/merchant/v1/marketing/create", data, token, serial,nil)

	//assert.Equal(t,"200",(w.Body.String()))
	assert.Equal(t, 200, getCode(w.Body.String()))

	updateState(t)

	wechatToken, _, wechatSerial := login.WechatLogin()

	data = url.Values{}
	url := "https://apiv2.almas.biz/ug/v1/order/create"

	data.Set("restaurant_id", resId)
	data.Set("address_id", "41")
	data.Set("timezone", "CST")
	data.Set("booking_time", "2022-10-24 12:08:26")
	data.Set("description", "alimjan go test")
	data.Set("shipment", "2")
	data.Set("price", "45")
	data.Set("order_type", "1")
	data.Set("lunch_box_fee", "0")
	data.Set("foods[0][id]", "18532")
	data.Set("foods[0][price]", "45")
	data.Set("foods[0][count]", "1")
	data.Set("reduction_fee", "2")
	body := helper.SendHttpPost(url, data, wechatToken, wechatSerial)

	var m map[string]interface{}
	json.Unmarshal(body, &m)
	assert.Equal(t, -1000, getCode(string(body)))
}
