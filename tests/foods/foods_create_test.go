package cms

import (
	"mulazim-api/lang"
	"mulazim-api/models"
	"mulazim-api/requests/RestaurantRequest"
	"mulazim-api/services"
	"mulazim-api/tools"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/assert/v2"
)

// TestPostCreate
//
//	@Author: Salam
//	@Date: 2025-04-03 18:05:45
//	@Description: 测试创建餐厅美食的接口，包含普通美食、套餐美食、规格美食和带饭盒的美食四种类型
//	@param t
//	go test -v ./tests/foods/ -run TestPostCreate
func TestPostCreate(t *testing.T) {
	// 1. 测试创建普通美食
	TestCreateNormalFood(t)
	
	// 2. 测试创建套餐美食
	TestCreateComboFood(t)

	// 3. 测试创建带饭盒的美食
	TestCreateFoodWithLunchBox(t)
}

// TestCreateNormalFood
//
//	@Author: Salam
//	@Date: 2025-04-03 18:05:45
//	@Description: 测试创建普通美食的接口，验证基本字段和分类关联是否正确创建
//	@param t
//	go test -v ./tests/foods/ -run TestCreateNormalFood
func TestCreateNormalFood(t *testing.T) {
	// 准备测试数据
	body := RestaurantRequest.RestaurantFoodsCreateBody{
		FoodsGroupID:        1,
		FoodsCategoryID:     []int{1, 2},
		RestaurantPrinterID: 1,
		RestaurantID:        2121,
		NameUg:              "تاماق",
		NameZh:              "美食",
		FoodQuantity:        1,
		FoodQuantityType:    1,
		DescriptionUg:       "تاماق تەسۋىرى",
		DescriptionZh:       "美食描述",
		State:               1,
		IsDistribution:      1,
		Image:               "/upload/foods/111.jpg",
		BeginTime:           "09:00",
		EndTime:             "22:00",
		ReadyTime:           30,
		Price:               20.00,
		Weight:              1,
		MinCount:            1,
		DistributionPercent: 20,
		YourSelfTakePercent: 20,
		FoodType:            models.RestaurantFoodsTypeNormal,
	}

	// 获取餐厅信息
	var restaurant models.Restaurant
	tools.Db.Model(&models.Restaurant{}).Where("id = ?", body.RestaurantID).First(&restaurant)
	if restaurant.ID == 0 {
		t.Fatal("餐厅不存在")
	}

	// 调用服务创建美食
	service := services.NewRestaurantFoodsService(nil)
	ok, errStr, food := service.CreateRestaurantFoods(body, restaurant)
	if !ok {
		t.Fatal(errStr)
	}

	// 验证是否创建成功
	var createdFood models.RestaurantFoods
	tools.Db.Model(&models.RestaurantFoods{}).Where("id = ?", food.ID).First(&createdFood)
	assert.Equal(t, true, createdFood.ID > 0)
	assert.Equal(t, body.NameUg, createdFood.NameUg)
	assert.Equal(t, body.NameZh, createdFood.NameZh)
	assert.Equal(t, body.DescriptionUg, createdFood.DescriptionUg)
	assert.Equal(t, body.DescriptionZh, createdFood.DescriptionZh)
	assert.Equal(t, body.State, createdFood.State)
	assert.Equal(t, body.IsDistribution, createdFood.IsDistribution)
	assert.Equal(t, body.BeginTime+":00", createdFood.BeginTime)
	assert.Equal(t, body.EndTime+":00", createdFood.EndTime)
	assert.Equal(t, int(body.ReadyTime), createdFood.ReadyTime)
	assert.Equal(t, uint(body.Price*100), createdFood.Price)
	assert.Equal(t, body.Weight, createdFood.Weight)
	assert.Equal(t, body.MinCount, createdFood.MinCount)

	// 验证分类是否创建成功
	var categories []models.RestaurantFoodsCategory
	tools.Db.Model(&models.RestaurantFoodsCategory{}).Where("restaurant_foods_id = ?", food.ID).Find(&categories)
	assert.Equal(t, len(body.FoodsCategoryID), len(categories))

	// 清理测试数据
	tools.Db.Model(&models.RestaurantFoodsCategory{}).Where("restaurant_foods_id = ?", food.ID).Delete(&models.RestaurantFoodsCategory{})
	tools.Db.Model(&models.RestaurantFoods{}).Where("id = ?", food.ID).Delete(&models.RestaurantFoods{})
}

// TestCreateComboFood
//
//	@Author: Salam
//	@Date: 2025-04-03 18:05:45
//	@Description: 测试创建套餐美食的接口，验证套餐类型和子美食关联是否正确创建
//	@param t
//	go test -v ./tests/foods/ -run TestCreateComboFood
func TestCreateComboFood(t *testing.T) {
	// 准备测试数据
	body := RestaurantRequest.RestaurantFoodsCreateBody{
		FoodsGroupID:        1,
		FoodsCategoryID:     []int{1, 2},
		RestaurantPrinterID: 1,
		RestaurantID:        2121,
		NameUg:              "تاماق كومبوسى",
		NameZh:              "套餐美食",
		FoodQuantity:        1,
		FoodQuantityType:    1,
		DescriptionUg:       "تاماق كومبوسى تەسۋىرى",
		DescriptionZh:       "套餐美食描述",
		State:               1,
		IsDistribution:      1,
		Image:               "/upload/foods/222.jpg",
		BeginTime:           "09:00",
		EndTime:             "22:00",
		ReadyTime:           30,
		Price:               50.00,
		Weight:              1,
		MinCount:            1,
		DistributionPercent: 20,
		YourSelfTakePercent: 20,
		FoodType:            models.RestaurantFoodsTypeCombo,
		ComboFoodItems: []RestaurantRequest.RestaurantFoodsComboItems{
			{
				FoodID:   1,
				FoodType: models.RestaurantFoodsTypeNormal,
				Count:    1,
			},
			{
				FoodID:   2,
				FoodType: models.RestaurantFoodsTypeNormal,
				Count:    1,
			},
		},
	}

	// 获取餐厅信息
	var restaurant models.Restaurant
	tools.Db.Model(&models.Restaurant{}).Where("id = ?", body.RestaurantID).First(&restaurant)
	if restaurant.ID == 0 {
		t.Fatal("餐厅不存在")
	}

	ctx := &gin.Context{}
	ctx.Set("lang_util", lang.LangUtil{})
	ctx.Set("lang", "ug")

	// 调用服务创建美食
	_restaurantFoodsService := services.NewRestaurantFoodsService(ctx)
	ok, errStr, food := _restaurantFoodsService.CreateRestaurantFoods(body, restaurant)
	if !ok {
		t.Fatal(errStr)
	}

	// 创建套餐美食的子美食
	_foodsComboService := services.NewFoodsComboService(ctx)
	_, err := _foodsComboService.CreateFoodsComboItems(*food, body.ComboFoodItems)
	if err != nil {
		t.Fatal(err)
	}

	// 验证是否创建成功
	var createdFood models.RestaurantFoods
	tools.Db.Model(&models.RestaurantFoods{}).Where("id = ?", food.ID).First(&createdFood)
	assert.Equal(t, true, createdFood.ID > 0)
	assert.Equal(t, body.NameUg, createdFood.NameUg)
	assert.Equal(t, body.NameZh, createdFood.NameZh)
	assert.Equal(t, body.FoodType, createdFood.FoodType)
	// - 验证套餐美食的子美食是否创建成功
	var comboItemsCount int64
	tools.Db.Model(&models.FoodsComboItem{}).Where("combo_id = ?", food.ID).Count(&comboItemsCount)
	assert.Equal(t, len(body.ComboFoodItems), tools.ToInt(comboItemsCount))
	for _, item := range body.ComboFoodItems {
		var comboItem models.FoodsComboItem
		tools.Db.Model(&models.FoodsComboItem{}).Where("combo_id = ?", food.ID).
			Where("food_id = ?", item.FoodID).Find(&comboItem)

		assert.Equal(t, item.FoodID, comboItem.FoodID)
		assert.Equal(t, item.FoodType, comboItem.FoodType)
		assert.Equal(t, item.Count, comboItem.Count)
	}

	// 清理测试数据
	tools.Db.Model(&models.FoodsComboItem{}).Where("combo_id = ?", food.ID).Delete(&models.FoodsComboItem{})
	tools.Db.Model(&models.RestaurantFoodsCategory{}).Where("restaurant_foods_id = ?", food.ID).Delete(&models.RestaurantFoodsCategory{})
	tools.Db.Model(&models.RestaurantFoods{}).Where("id = ?", food.ID).Delete(&models.RestaurantFoods{})
	tools.Db.Model(&models.FoodsComboItem{}).Where("combo_id = ?", food.ID).Delete(&models.FoodsComboItem{})
}

// TestCreateFoodWithLunchBox
//
//	@Author: Salam
//	@Date: 2025-04-03 18:05:45
//	@Description: 测试创建带饭盒的美食的接口，验证饭盒相关字段是否正确创建
//	@param t
//	go test -v ./tests/foods/ -run TestCreateFoodWithLunchBox
func TestCreateFoodWithLunchBox(t *testing.T) {
	// 准备测试数据
	body := RestaurantRequest.RestaurantFoodsCreateBody{
		FoodsGroupID:        1,
		FoodsCategoryID:     []int{1, 2},
		RestaurantPrinterID: 1,
		RestaurantID:        2121,
		NameUg:              "تاماق قاچىسى",
		NameZh:              "饭盒美食",
		FoodQuantity:        1,
		FoodQuantityType:    1,
		DescriptionUg:       "تاماق قاچىسى تەسۋىرى",
		DescriptionZh:       "饭盒美食描述",
		State:               1,
		IsDistribution:      1,
		Image:               "/upload/foods/444.jpg",
		BeginTime:           "09:00",
		EndTime:             "22:00",
		ReadyTime:           30,
		Price:               25.00,
		Weight:              1,
		MinCount:            1,
		DistributionPercent: 20,
		YourSelfTakePercent: 20,
		LunchBoxID:          1,
		LunchBoxAccommodate: 1,
	}

	// 获取餐厅信息
	var restaurant models.Restaurant
	tools.Db.Model(&models.Restaurant{}).Where("id = ?", body.RestaurantID).First(&restaurant)
	if restaurant.ID == 0 {
		t.Fatal("餐厅不存在")
	}

	// 调用服务创建美食
	service := services.NewRestaurantFoodsService(nil)
	ok, errStr, food := service.CreateRestaurantFoods(body, restaurant)
	if !ok {
		t.Fatal(errStr)
	}

	// 验证是否创建成功
	var createdFood models.RestaurantFoods
	tools.Db.Model(&models.RestaurantFoods{}).Where("id = ?", food.ID).First(&createdFood)
	assert.Equal(t, true, createdFood.ID > 0)
	assert.Equal(t, body.NameUg, createdFood.NameUg)
	assert.Equal(t, body.NameZh, createdFood.NameZh)
	assert.Equal(t, body.LunchBoxID, createdFood.LunchBoxID)
	assert.Equal(t, body.LunchBoxAccommodate, createdFood.LunchBoxAccommodate)

	// 清理测试数据
	tools.Db.Model(&models.RestaurantFoodsCategory{}).Where("restaurant_foods_id = ?", food.ID).Delete(&models.RestaurantFoodsCategory{})
	tools.Db.Model(&models.RestaurantFoods{}).Where("id = ?", food.ID).Delete(&models.RestaurantFoods{})
} 