package cms

import (
	"encoding/json"
	"fmt"
	"mulazim-api/models"
	"mulazim-api/requests/RestaurantRequest"
	"mulazim-api/services"
	"mulazim-api/tools"
	"strings"
	"testing"
)

type FoodSpecType struct {
	NameUz string `json:"name_ug"`
	NameZh string `json:"name_zh"`
}

type FoodSpecOption struct {
	NameUz string `json:"name_ug"`
	NameZh string `json:"name_zh"`
	Price  int64  `json:"price"`
	PriceType string `json:"price_type"`
	IsSelected bool `json:"is_selected"`
}

// 测试创建规格
func TestFoodsCreateSpec(t *testing.T) {
	service := services.NewRestaurantFoodsSpecService(nil)
	specsMap := []map[string]interface{}{
		{
			"name_ug": "مىقدارى",
			"name_zh": "饭量",
			"price_type": 1,
			"restaurant_foods_spec_option": []map[string]interface{}{
				{
					"name_ug": "كىچىك",
					"name_zh": "小",
					"price":   7700,
					"is_selected": 1,
				},
				{
					"name_ug": "ئوتتۇرا",
					"name_zh": "中",
					"price":   9800,
					"is_selected": 0,
				},
				{
					"name_ug": "چوڭ",
					"name_zh": "大",
					"price_type": 0,
					"is_selected": 0,
				},
			},
		},
		{
			"name_ug": "تەمى",
			"name_zh": "味道",
			"price_type": 0,
			"restaurant_foods_spec_option": []map[string]interface{}{
				{
					"name_ug": "نۇرمال",
					"name_zh": "正常",
					"price":   0,
					"is_selected": 1,
				},
				{
					"name_ug": "ئاچچىق",
					"name_zh": "中辣",
					"price":   0,
					"is_selected": 0,
				},
				{
					"name_ug": "بەك ئاچچىق",
					"name_zh": "爆辣",
					"price":   0,
					"is_selected": 0,
				},
			},
		},
		{
			"name_ug": "قۇشۇمچە",
			"name_zh": "附加",
			"price_type": 0,
			"restaurant_foods_spec_option": []map[string]interface{}{
				{
					"name_ug": "گۈرۈچ",
					"name_zh": "米饭",
					"price":   200,
					"is_selected": 0,
				},
				{
					"name_ug": "چۆپ",
					"name_zh": "皮带面",
					"price":   500,
					"is_selected": 0,
				},
			},
		},
	}
	var food models.RestaurantFoods
	tools.Db.Model(food).Where("restaurant_id = 2121 and state = 1").First(&food)


	tools.Db.Model(&models.FoodSpecType{}).Unscoped().Where("food_id = ?", food.ID).Delete(&models.FoodSpecType{})
	tools.Db.Model(&models.FoodSpecOption{}).Unscoped().Where("food_id = ?", food.ID).Delete(&models.FoodSpecOption{})
	tools.Db.Model(&models.FoodSpec{}).Unscoped().Where("food_id = ?", food.ID).Delete(&models.FoodSpec{})
	tools.Db.Model(&models.FoodSpecDetail{}).Unscoped().Where("food_id = ?", food.ID).Delete(&models.FoodSpecDetail{})

	specsMapJson, _ := json.Marshal(specsMap)

	specs := []RestaurantRequest.RestaurantFoodsSpecTypes{}
	json.Unmarshal(specsMapJson, &specs)
	// 创建规格信息
	err := service.SaveFoodSpec(food,specs)
	if err != nil {
		t.Fatal(err)
	}
	// 验证是否创建成功
	var foodsSpecTypes []models.FoodSpecType
	tools.Db.Model(foodsSpecTypes).Where("food_id = ? and is_deleted = 0", food.ID).Preload("FoodSpecOption").Find(&foodsSpecTypes)
	if len(foodsSpecTypes) != len(specs) {
		t.Fatal("specCountError")
	}
	// 验证规格选项是否创建成功
	specCount := 1
	for k, spec := range foodsSpecTypes {
		specCount *= len(spec.FoodSpecOptions)
		if len(spec.FoodSpecOptions) != len(specs[k].RestaurantFoodsSpecOption) {
			t.Fatal("specOptionCountError")
		}
		if spec.PriceType != specs[k].PriceType {
			t.Fatal("specPriceTypeError")
		}
		if spec.NameUg != specs[k].NameUg {
			t.Fatal("specNameUgError")
		}
		if spec.NameZh != specs[k].NameZh {
			t.Fatal("specNameZhError")
		}
		for i, option := range spec.FoodSpecOptions {
			if option.NameUg != specs[k].RestaurantFoodsSpecOption[i].NameUg {
				t.Fatal("optionNameUgError")
			}
			if option.NameZh != specs[k].RestaurantFoodsSpecOption[i].NameZh {
				t.Fatal("optionNameZhError")
			}
			if option.Price != specs[k].RestaurantFoodsSpecOption[i].Price {
				t.Fatal("optionPriceError")
			}
			if option.IsSelected != specs[k].RestaurantFoodsSpecOption[i].IsSelected {
				t.Fatal("optionIsSelectedError")
			}
		}
	}

	var foodSpec []models.FoodSpec
	tools.Db.Model(foodSpec).Where("food_id = ? and is_deleted = 0", food.ID).Preload("FoodSpecOptions").Find(&foodSpec)
	if len(foodSpec) != specCount {
		t.Fatal("foodSpecCountError")
	}

	// 打印规格组合
	for key, spec := range foodSpec {
		specStr := fmt.Sprintf("规格[%d]", key+1)
		for _, option := range spec.FoodSpecOptions {
			specStr += "-" + option.NameZh
		}
		fmt.Println(specStr)
	}
}

func TestFoodsBatchCreateSpec(t *testing.T) {
	service := services.NewRestaurantFoodsSpecService(nil)
	specsMap := []map[string]interface{}{
		{
			"name_ug": "مىقدارى",
			"name_zh": "饭量",
			"price_type": 1,
			"restaurant_foods_spec_option": []map[string]interface{}{
				{
					"name_ug": "كىچىك",
					"name_zh": "小",
					"price":   7700,
					"is_selected": 1,
				},
				{
					"name_ug": "ئوتتۇرا",
					"name_zh": "中",
					"price":   9800,
					"is_selected": 0,
				},
				{
					"name_ug": "چوڭ",
					"name_zh": "大",
					"price_type": 0,
					"is_selected": 0,
				},
			},
		},
	}
	var food models.RestaurantFoods
	tools.Db.Model(food).Where("restaurant_id = 2121 and state = 1").First(&food)

	specsMapJson, _ := json.Marshal(specsMap)

	specs := []RestaurantRequest.RestaurantFoodsSpecTypes{}
	json.Unmarshal(specsMapJson, &specs)
	// 创建规格信息
	err := service.SaveFoodSpec(food,specs)
	if err != nil {
		t.Fatal(err)
	}
	// 验证是否创建成功
	var foodsSpecTypes []models.FoodSpecType
	tools.Db.Model(foodsSpecTypes).Where("food_id = ? and is_deleted = 0", food.ID).Preload("FoodSpecOption").Find(&foodsSpecTypes)
	if len(foodsSpecTypes) != len(specs) {
		t.Fatal("specCountError")
	}
	// 验证规格选项是否创建成功
	specCount := 1
	for k, spec := range foodsSpecTypes {
		specCount *= len(spec.FoodSpecOptions)
		if len(spec.FoodSpecOptions) != len(specs[k].RestaurantFoodsSpecOption) {
			t.Fatal("specOptionCountError")
		}
		if spec.PriceType != specs[k].PriceType {
			t.Fatal("specPriceTypeError")
		}
		if spec.NameUg != specs[k].NameUg {
			t.Fatal("specNameUgError")
		}
		if spec.NameZh != specs[k].NameZh {
			t.Fatal("specNameZhError")
		}
		for i, option := range spec.FoodSpecOptions {
			if option.NameUg != specs[k].RestaurantFoodsSpecOption[i].NameUg {
				t.Fatal("optionNameUgError")
			}
			if option.NameZh != specs[k].RestaurantFoodsSpecOption[i].NameZh {
				t.Fatal("optionNameZhError")
			}
			if option.Price != specs[k].RestaurantFoodsSpecOption[i].Price {
				t.Fatal("optionPriceError")
			}
			if option.IsSelected != specs[k].RestaurantFoodsSpecOption[i].IsSelected {
				t.Fatal("optionIsSelectedError")
			}
		}
	}

	var foodSpec []models.FoodSpec
	tools.Db.Model(foodSpec).Where("food_id = ? and is_deleted = 0", food.ID).Preload("FoodSpecOptions").Find(&foodSpec)
	if len(foodSpec) != specCount {
		t.Fatal("foodSpecCountError")
	}

	// 打印规格组合
	for key, spec := range foodSpec {
		specStr := fmt.Sprintf("规格[%d]", key+1)
		for _, option := range spec.FoodSpecOptions {
			specStr += "-" + option.NameZh
		}
		fmt.Println(specStr)
	}
}


// 测试创建规格
func TestDeleteSpec(t *testing.T) {
	service := services.NewRestaurantFoodsSpecService(nil)
	var food models.RestaurantFoods
	tools.Db.Model(food).Where("restaurant_id = 2121 and state = 1").First(&food)
	err := service.DeleteFoodsSpec(food.ID)
	if err != nil {
		t.Fatal(err)
	}
	var foodsSpecTypes []models.FoodSpecType
	tools.Db.Model(foodsSpecTypes).Where("food_id = ? and is_deleted = 1", food.ID).Find(&foodsSpecTypes)
	if len(foodsSpecTypes) > 0 {
		t.Fatal("specCountError")
	}
	var foodSpec []models.FoodSpec
	tools.Db.Model(foodSpec).Where("food_id = ? and is_deleted = 1", food.ID).Preload("FoodSpecDetail.SpecOption").Find(&foodSpec)
	if len(foodSpec) > 0 {
		t.Fatal("foodSpecCountError")
	}
	var foodSpecDetail []models.FoodSpecDetail
	tools.Db.Model(foodSpecDetail).Where("food_id = ? and is_deleted = 1", food.ID).Find(&foodSpecDetail)
	if len(foodSpecDetail) > 0 {
		t.Fatal("foodSpecDetailCountError")
	}
	var foodSpecOption []models.FoodSpecOption
	tools.Db.Model(foodSpecOption).Where("food_id = ? and is_deleted = 1", food.ID).Find(&foodSpecOption)
	if len(foodSpecOption) > 0 {
		t.Fatal("foodSpecOptionCountError")
	}
}




func TestSpecDetail(t *testing.T) {
	items := [][]string{
		{"1", "2"},
		{"1", "2", "3"},
		{"1", "2", "3"},
	}
	var result []string
	var combination []string
	var generate func(int)
	generate = func(depth int) {
		if depth == len(items) {
			result = append(result, strings.Join(combination, ""))
			return
		}
		for _, val := range items[depth] {
			combination = append(combination, val)
			generate(depth + 1)
			combination = combination[:len(combination)-1]
		}
	}
	generate(0)
	for i, s := range result {
		fmt.Printf("%d. %s\n", i+1, s)
	}
}
