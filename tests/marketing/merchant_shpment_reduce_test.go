﻿package marketing

import (
	service "mulazim-api/services/marketing"
	"testing"
)

var (
	reduceService = service.NewShipmentReduceService()
)

func TestGetShipmentReduceList(t *testing.T) {
	// 获取数据
	// var admin models.Admin
	// tools.Db.Model(admin).Where("id = 1").First(&admin)
	// reduceList, totalCount := reduceService.GetShipmentReduceList(1, 1, 10, 1, 1)
	// // 格式化数据
	// res := reduceFormat.FormatMerchantShipmentReduceList(reduceList)
	// resData := map[string]interface{}{
	// 	"items": res,
	// 	"total": totalCount,
	// }
	// fmt.Println(tools.MapToString(resData))
}

func TestCreateShipmentReduce(t *testing.T) {
	// type params struct {
	// 	// MarketingType int    2
	// 	RestaurantID  int    `form:"restaurant_id" binding:"required"`
	// 	Type          int    `form:"type" binding:"required"`
	// 	BeginDate     string `form:"begin_date" binding:"required"` // 开始日期
	// 	EndDate       string `form:"end_date" binding:"required"`
	// 	FullWeekState int    `form:"full_week_state"  binding:"required"` // 全星期  0：否（多选星期），1：是
	// 	FullTimeState int    `form:"full_time_state"  binding:"required"` // 全天     0：否（添加时间段），1：是
	// 	AutoContinue  int    `form:"auto_continue" binding:"required"`
	// 	NameUg        string `form:"name_ug" binding:"required"`
	// 	NameZh        string `form:"name_zh" binding:"required"`
	// }
	// // 获取数据
	// var admin models.Admin
	// tools.Db.Model(admin).Where("id = 1").First(&admin)
	// reduceList, totalCount := reduceService.GetShipmentReduceList(1, 1, 10, 1, 1)
	// // 格式化数据
	// res := reduceFormat.FormatMerchantShipmentReduceList(reduceList)
	// resData := map[string]interface{}{
	// 	"items": res,
	// 	"total": totalCount,
	// }
	// fmt.Println(tools.MapToString(resData))
}

func TestUpdateShipmentReduceList(t *testing.T) {
	// // 获取数据
	// var admin models.Admin
	// tools.Db.Model(admin).Where("id = 1").First(&admin)
	// reduceList, totalCount := reduceService.GetShipmentReduceList(1, 1, 10, 1, 1)
	// // 格式化数据
	// res := reduceFormat.FormatMerchantShipmentReduceList(reduceList)
	// resData := map[string]interface{}{
	// 	"items": res,
	// 	"total": totalCount,
	// }
	// fmt.Println(tools.MapToString(resData))
}

func TestChangeStateShipmentReduceList(t *testing.T) {
	// // 获取数据
	// var admin models.Admin
	// tools.Db.Model(admin).Where("id = 1").First(&admin)
	// reduceList, totalCount := reduceService.GetShipmentReduceList(1, 1, 10, 1, 1)
	// // 格式化数据
	// res := reduceFormat.FormatMerchantShipmentReduceList(reduceList)
	// resData := map[string]interface{}{
	// 	"items": res,
	// 	"total": totalCount,
	// }
	// fmt.Println(tools.MapToString(resData))
}
