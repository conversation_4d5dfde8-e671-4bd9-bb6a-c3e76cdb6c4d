package login

import (
	"encoding/json"
	"mulazim-api/mygin"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
)

func ShipperLogin()(token string,serial string)  {
	router := mygin.InitGinWeb()
	w := httptest.NewRecorder()


	data := url.Values{}
	data.Set("name","ablimidx")
	data.Set("password","ab123123")
	data.Set("client_id","gLOdSQqVaHDcC9uaEgom")
	data.Set("client_secret","cZkWtbuwVJZ9rfi1JwQvMMqLGc1YN38M")
	data.Set("grant_type","merchant")
	b := strings.NewReader(data.Encode())

	req, _ := http.NewRequest("POST", "/ug/shipper/v1/oauth/login", b)
	req.Header.Add("Content-Type","application/x-www-form-urlencoded")
	req.Header.Add("searialNumber","123456789")
	router.ServeHTTP(w, req)
	var m map[string]interface{}
	json.Unmarshal(w.Body.Bytes(),&m)
	return m["data"].(map[string]interface{})["tokens"].(map[string]interface{})["access_token"].(string),
		"123456789"
}