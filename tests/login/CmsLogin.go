package login

import (
	"mulazim-api/tests/helper"
	"mulazim-api/tools"
	"net/url"
)

// func CmsLogin() (token string, userId string, serial string) {
func CmsLogin(name string,password string) string {
	data := url.Values{}
	data.Set("name", name)
	data.Set("password", password)
	data.Set("token", "MMavmXJnnxzEfGNZHTAaeJEXuhsz87et")
	w := helper.SendGinPost("/ug/cms/v2/login",data, "","",nil)
	resMap ,_:= tools.StringToMap(w.Body.String())
	if tools.ToInt(resMap["status"]) != 200 {
		tools.Logger.Error(resMap["msg"])
		return ""
	}
	cookie := resMap["data"].(map[string]interface{})["cookie_value"].(string) 
	return cookie
}


