package login

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"
)

func WechatLogin() (token string, userId string, serial string) {

	data := url.Values{}
	data.Set("mobile", "18129290467")
	data.Set("verify", "999999")
	data.Set("client_id", "gDw6hUnOkYXIs5NTyMju")
	data.Set("client_secret", "SgxOFzp4sLUrqITpYF4HSYgu5oIlfATu")
	data.Set("grant_type", "verify")
	b := strings.NewReader(data.Encode())

	req, _ := http.NewRequest("POST", "https://apiv2.almas.biz/ug/v1/oauth/login", b)
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("searialNumber", "123456789")
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	response, _ := client.Do(req)
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	var m map[string]interface{}
	json.Unmarshal(body, &m)
	return m["data"].(map[string]interface{})["tokens"].(map[string]interface{})["access_token"].(string),
		fmt.Sprintf("%d", int(m["data"].(map[string]interface{})["user"].(map[string]interface{})["id"].(float64))),
		"123456789"
}
