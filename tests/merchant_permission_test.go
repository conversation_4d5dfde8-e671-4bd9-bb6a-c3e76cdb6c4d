﻿package tests

import (
	"fmt"
	"testing"

	"github.com/casbin/casbin/v2"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)


 
func TestMerchantPermission(t *testing.T) {
    // 连接数据库
	dsn := "root:AlmasTest2017.@tcp(192.168.1.100:3306)/mulazimpro?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}
	// 初始化 Casbin 适配器
	adapter, err := gormadapter.NewAdapterByDBWithCustomTable(db,nil,"merchant_permission")
	if err != nil {
		panic("failed to create casbin adapter")
	}

	// 创建 Casbin Enforcer
	enforcer, err := casbin.NewEnforcer("D:/code/mulazim-api-go/.vscode/rbac_model.conf", adapter)
	if err != nil {
		panic("failed to create casbin enforcer")
	}

	// 加载策略
	if err := enforcer.LoadPolicy(); err != nil {
		panic("failed to load policy")
	}

	// 添加策略
	// enforcer.AddPolicy("admin", "/data1", "read")
	// enforcer.AddPolicy("admin", "/data2", "write")

	// enforcer.AddPolicy("member", "/data1", "read")
	// 检查权限
    a, b := enforcer.Enforce("13774", "/v2/chat/list", "GET")
		fmt.Println(a,b)
}
