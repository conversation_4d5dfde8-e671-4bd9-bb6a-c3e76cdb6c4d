package tests

import (
	"mulazim-api/tools"
	"testing"

	"github.com/go-playground/assert/v2"
)


func TestTimeInTimeLine(t *testing.T) {
	assert.Equal(t,true,tools.TimeLineInTimeLine("22:00:00", "23:00:00", "12:00:00", "23:00:00"))
	assert.Equal(t,true,tools.TimeLineInTimeLine("22:00:00", "22:59:59", "12:00:00", "23:00:00"))
	assert.Equal(t,true,tools.TimeLineInTimeLine("00:00:00", "00:00:00", "12:00:00", "12:00:00"))
	assert.Equal(t,true,tools.TimeLineInTimeLine("23:00:00", "02:00:00", "12:00:00", "12:00:00"))

	assert.Equal(t,true,tools.TimeLineInTimeLine("02:00:00", "04:00:00", "12:00:00", "12:00:00"))
	assert.Equal(t,true,tools.TimeLineInTimeLine("08:00:00", "23:00:00", "08:00:00", "23:00:00"))

	assert.Equal(t,true,tools.TimeLineInTimeLine("08:00:00", "23:00:00", "07:00:00", "23:00:00"))

	assert.Equal(t,false,tools.TimeLineInTimeLine("23:00:00", "02:00:00", "01:00:00", "02:00:00"))



	// 重叠的情况
	assert.Equal(t, false, tools.TimeLineInTimeLine("00:00:00", "01:00:00", "00:30:00", "01:30:00"))
	assert.Equal(t, false, tools.TimeLineInTimeLine("12:00:00", "15:00:00", "14:00:00", "16:00:00"))
	assert.Equal(t, false, tools.TimeLineInTimeLine("06:00:00", "08:00:00", "05:00:00", "07:00:00"))

	// 不重叠的情况
	assert.Equal(t, false, tools.TimeLineInTimeLine("09:00:00", "12:00:00", "14:00:00", "16:00:00"))
	assert.Equal(t, false, tools.TimeLineInTimeLine("01:00:00", "04:00:00", "05:00:00", "08:00:00"))
	assert.Equal(t, false, tools.TimeLineInTimeLine("17:00:00", "20:00:00", "08:00:00", "12:00:00"))

	// 边界情况
	assert.Equal(t, true, tools.TimeLineInTimeLine("12:00:00", "13:00:00", "12:00:00", "13:00:00"))
	assert.Equal(t, false, tools.TimeLineInTimeLine("00:00:00", "01:00:00", "01:00:00", "02:00:00"))
	assert.Equal(t, false, tools.TimeLineInTimeLine("23:00:00", "23:59:59", "22:00:00", "23:00:00"))

	// 与自身重叠的情况
	assert.Equal(t, true, tools.TimeLineInTimeLine("06:00:00", "12:00:00", "06:00:00", "12:00:00"))
	assert.Equal(t, true, tools.TimeLineInTimeLine("03:00:00", "04:00:00", "03:00:00", "04:00:00"))
	assert.Equal(t, true, tools.TimeLineInTimeLine("19:00:00", "22:00:00", "19:00:00", "22:00:00"))

	// 与自身不重叠的情况
	assert.Equal(t, false, tools.TimeLineInTimeLine("09:00:00", "12:00:00", "09:00:00", "10:00:00"))
	assert.Equal(t, false, tools.TimeLineInTimeLine("14:00:00", "16:00:00", "12:00:00", "14:00:00"))
	assert.Equal(t, false, tools.TimeLineInTimeLine("18:00:00", "20:00:00", "20:00:00", "22:00:00"))

	assert.Equal(t, true, tools.TimeLineInTimeLine("08:00:00", "20:00:00", "08:00:00", "06:00:00"))
	assert.Equal(t, true, tools.TimeLineInTimeLine("08:00:00", "05:00:00", "08:00:00", "06:00:00"))
	assert.Equal(t, true, tools.TimeLineInTimeLine("08:00:00", "23:00:00", "08:00:00", "06:00:00"))
	assert.Equal(t, true, tools.TimeLineInTimeLine("01:00:00", "02:00:00", "08:00:00", "06:00:00"))
	assert.Equal(t, true, tools.TimeLineInTimeLine("23:00:00", "02:00:00", "08:00:00", "06:00:00"))
	assert.Equal(t, false, tools.TimeLineInTimeLine("23:00:00", "07:00:00", "08:00:00", "06:00:00"))
	assert.Equal(t, false, tools.TimeLineInTimeLine("07:00:00", "08:00:00", "08:00:00", "06:00:00"))
	assert.Equal(t, false, tools.TimeLineInTimeLine("07:00:00", "09:00:00", "08:00:00", "06:00:00"))
	assert.Equal(t, false, tools.TimeLineInTimeLine("06:00:00", "08:00:00", "08:00:00", "06:00:00"))

	assert.Equal(t, true, tools.TimeLineInTimeLine("06:00:00", "08:00:00", "05:00:00", "08:00:00"))
	assert.Equal(t, true, tools.TimeLineInTimeLine("06:00:00", "08:00:00", "23:00:00", "08:00:00"))
	assert.Equal(t, true, tools.TimeLineInTimeLine("00:00:00", "01:00:00", "08:00:00", "01:00:00"))

}
func TestResTimeAndFoodTime(t *testing.T) {
	assert.Equal(t,false,tools.ResTimeFoodTimeValid("22:00:00", "23:00:00", "12:00:00", "23:00:00"))
	assert.Equal(t,false,tools.ResTimeFoodTimeValid("22:00:00", "22:59:59", "12:00:00", "23:00:00"))
	assert.Equal(t,true,tools.ResTimeFoodTimeValid("00:00:00", "00:00:00", "12:00:00", "12:00:00"))
	assert.Equal(t,false,tools.ResTimeFoodTimeValid("23:00:00", "02:00:00", "12:00:00", "12:00:00"))

	assert.Equal(t,true,tools.ResTimeFoodTimeValid("23:00:00", "02:00:00", "23:00:00", "02:00:00"))
	assert.Equal(t,true,tools.ResTimeFoodTimeValid("23:00:00", "00:00:00", "23:00:00", "00:00:00"))

	assert.Equal(t,true,tools.ResTimeFoodTimeValid("07:00:00", "00:00:00", "07:00:00", "09:00:00"))

	assert.Equal(t,true,tools.ResTimeFoodTimeValid("01:00:00", "08:00:00", "07:00:00", "07:30:00"))

	assert.Equal(t,false,tools.ResTimeFoodTimeValid("01:00:00", "08:00:00", "07:00:00", "09:30:00"))

	assert.Equal(t,true,tools.ResTimeFoodTimeValid("07:00:00", "01:00:00", "08:00:00", "00:30:00"))

	assert.Equal(t,false,tools.ResTimeFoodTimeValid("07:00:00", "01:00:00", "03:00:00", "00:30:00"))


}
