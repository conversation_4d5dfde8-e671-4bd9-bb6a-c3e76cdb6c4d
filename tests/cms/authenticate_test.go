package cms

import (
	"mulazim-api/middlewares"
	"mulazim-api/models"
	"mulazim-api/models/cms"
	"mulazim-api/tools"
	"testing"

	"github.com/go-playground/assert/v2"
)

func TestAuthenticate(t *testing.T) {
	cmsAuth := new(middlewares.CmsAuthenticate)
	db := tools.GetDB()
	var admin = models.Admin{
		Type:     models.AdminTypeDealerSub,
		Mobile:   "17799022300",
		Name:     "admin",
		RealName: "阿力木克然木",
	}
	var dealer = models.Admin{
		Type:     models.AdminTypeDealer,
		Mobile:   "17799022301",
		Name:     "dealer",
		RealName: "Dealer",
	}
	var dealerSub = models.Admin{
		Type:     models.AdminTypeDealerSub,
		Mobile:   "17799022302",
		Name:     "dealer_sub",
		RealName: "Dealer Sub",
	}
	db.Omit("area_name").FirstOrCreate(&admin, "name = ?", admin.Name)
	db.Omit("area_name").FirstOrCreate(&dealer, "name = ?", dealer.Name)
	db.Omit("area_name").FirstOrCreate(&dealerSub, "name = ?", dealerSub.Name)
	db.Omit("area_name").Model(&dealerSub).Update("parent_id", dealer.ID)

	var role cms.Roles
	db.First(&role, "name = ?", "dealer")

	assert.Equal(t, true, cmsAuth.CheckUserPermission(&admin, "todayOrder.list.newOrder.count", "GET"))
	assert.Equal(t, true, cmsAuth.CheckUserPermission(&dealer, "todayOrder.list.newOrder.count","GET"))
	assert.Equal(t, true, cmsAuth.CheckUserPermission(&dealerSub, "todayOrder.list.newOrder.count","GET"))
	assert.Equal(t, false, cmsAuth.CheckUserPermission(&dealer, "todayOrder.set.shipper","GET"))
	db.Model(&dealer).
		Association("Roles").
		Append(&role)
	assert.Equal(t, true, cmsAuth.CheckUserPermission(&dealer, "todayOrder.set.shipper","GET"))
	assert.Equal(t, true, cmsAuth.CheckUserPermission(&dealer, "market.packet.index","GET"))
	db.Delete(dealerSub)
	db.Model(&dealer).Association("Roles").Delete(&role)
	db.Delete(dealer)
	db.Delete(admin)
}
