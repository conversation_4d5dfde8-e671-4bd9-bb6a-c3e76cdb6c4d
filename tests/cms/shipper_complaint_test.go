package cms

// @Description: 特殊天气
// @Author: Rixat
// @Time: 2023-10-31 02:29:10
// @receiver
// @param c *gin.Context

import (
	"fmt"
	"mulazim-api/constants"
	models "mulazim-api/models/shipment"
	"mulazim-api/tests/helper"
	"mulazim-api/tools"
	"testing"

	"github.com/go-playground/assert/v2"
)

var ComplaintID = 3

// TestSpecialWeatherAll
//
// @Description: 测试全部
// @Author: Rixat
// @Time: 2023-11-06 09:52:32
// @receiver
// @param c *gin.Context
func TestShipmentComplaintAll(t *testing.T) {

	TestShipmentComplaintCreate(t) // 创建
	TestShipmentComplaintDetail(t) // 详情
	TestShipmentComplaintList(t)   // 列表
	// 结束（为了打印输出内容）
	TestEnd(t)
}

// TestShipmentComplaintCreate
//
// @Description: 创建配送员投诉
// @Author: Rixat
// @Time: 2023-11-02 10:51:58
// @receiver
// @param c *gin.Context
func TestShipmentComplaintCreate(t *testing.T) {
	// 基本信息
	params := map[string]interface{}{
		"type":           constants.TypeComplaint,
		"shipper_id":     10270,
		"order_id":       5740381,
		"complaint_type": 1, // 1:客户，2:餐厅
		"amount":         5,
		"remark":         "没有配送，服务差",
	}
	// 发送请求
	w := helper.SendGinPost("/ug/cms/v2/shipment/income/create", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var income models.ShipperIncome
	params["amount"] = 500
	tools.Db.Model(income).Where(params).First(&income)
	assert.Equal(t, true, income.ID > 0)
	ComplaintID = income.ID
}

// TestShipmentComplaintDetail
//
// @Description: 配送员投诉详情
// @Author: Rixat
// @Time: 2023-11-06 09:54:27
// @receiver
// @param c *gin.Context
func TestShipmentComplaintDetail(t *testing.T) {
	params := map[string]interface{}{
		"id": ComplaintID,
	}
	w := helper.SendGinGet("/ug/cms/v2/shipment/income/detail", helper.MapToUrlValues(params), "", "")
	fmt.Println("详情结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}

// TestShipmentComplaintList
//
// @Description: 配送员投诉列表
// @Author: Rixat
// @Time: 2023-11-06 09:54:47
// @receiver
// @param c *gin.Context
func TestShipmentComplaintList(t *testing.T) {
	params := map[string]interface{}{
		"page":  1,
		"limit": 10,
		"type":  8,
	}
	w := helper.SendGinGet("/ug/cms/v2/shipment/income/list", helper.MapToUrlValues(params), "", "")
	fmt.Println("列表结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}

func TestShipmentIncomeTypes(t *testing.T) {
	params := map[string]interface{}{
		"page":  1,
		"limit": 10,
		"type":  8,
	}
	w := helper.SendGinGet("/ug/cms/v2/shipment/income/income-types", helper.MapToUrlValues(params), "", "")
	fmt.Println("列表结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}
