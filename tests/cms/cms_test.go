package cms

import (
	"mulazim-api/tests/helper"
	"mulazim-api/tests/login"
	"net/url"
	"regexp"
	"testing"

	"github.com/go-playground/assert/v2"
)

func TestCmsMenu(t *testing.T) {
	token, _, serial := login.MerchantLogin()
	data := url.Values{}
	helper.SendGinGet("/zh/cms/list/", data, token, serial)
	//println(w.Body.String())
	assert.Equal(t, 200, 200)
}
func TestUrl(t *testing.T)  {
	can,_ := regexp.MatchString("cashClear.list", "cashClear.list.*")
	println(can)
}