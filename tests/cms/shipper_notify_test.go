package cms

// @Description: 特殊天气
// @Author: Rixat
// @Time: 2023-10-31 02:29:10
// @receiver
// @param c *gin.Context

import (
	"fmt"
	models "mulazim-api/models/shipment"
	"mulazim-api/tests/helper"
	"mulazim-api/tools"
	"testing"

	"github.com/go-playground/assert/v2"
	"github.com/golang-module/carbon/v2"
)

var NotifyID = 6
var NotifyState = 1

// TestSpecialWeatherAll
//
// @Description: 测试全部
// @Author: Rixat
// @Time: 2023-11-06 09:52:32
// @receiver
// @param c *gin.Context
func TestShipperNotifyAll(t *testing.T) {

	TestShipperNotifyCreate(t) // 创建
	TestShipperChangeState(t)  // 修改状态(状态1=>发送)
	NotifyState = 2
	TestShipperChangeState(t) // 修改状态(状态2=>重发)
	TestShipperNotifyList(t)  // 列表
	TestShipperDelete(t)      // 删除
	// 结束（为了打印输出内容）
	TestEnd(t)
}

// TestShipperNotifyCreate
//
// @Description: 创建配送员通知
// @Author: Rixat
// @Time: 2023-11-02 10:51:58
// @receiver
// @param c *gin.Context
func TestShipperNotifyCreate(t *testing.T) {
	TitleUg := "通知_" + carbon.Now().Format("mdHi")
	TitleZh := "通知_" + carbon.Now().Format("mdHi")
	// 基本信息
	params := map[string]interface{}{
		"title_ug":   TitleUg,
		"title_zh":   TitleZh,
		"content_ug": "bugun kar yagidu kelin kiyinigla",
		"content_zh": "今天下雪，穿厚点",
	}
	// 发送请求
	w := helper.SendGinPost("/ug/cms/v2/shipment/notify/create", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var notify models.ShipperNotify
	tools.Db.Model(notify).Where(notify).First(&notify)
	assert.Equal(t, true, notify.ID > 0)
	NotifyID = notify.ID
}

// TestShipperNotifyDetail
//
// @Description: 删除配送员通知
// @Author: Rixat
// @Time: 2023-11-06 09:54:27
// @receiver
// @param c *gin.Context
func TestShipperDelete(t *testing.T) {
	params := map[string]interface{}{
		"id": NotifyID,
	}
	w := helper.SendGinPost("/ug/cms/v2/shipment/notify/delete", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("删除结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
	// 验证是否删除成功
	var notify models.ShipperNotify
	tools.Db.Model(notify).Where(params).First(&notify)
	assert.Equal(t, true, notify.ID == 0)
}

// TestShipperChangeState
//
// @Description: 发送通知
// @Author: Rixat
// @Time: 2023-11-07 03:45:35
// @receiver
// @param c *gin.Context
func TestShipperChangeState(t *testing.T) {
	params := map[string]interface{}{
		"id":    NotifyID,
		"state": NotifyState,
	}
	w := helper.SendGinPost("/ug/cms/v2/shipment/notify/change-state", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("修改状态:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否更新成功
	var notify models.ShipperNotify
	tools.Db.Model(notify).Where(params).First(&notify)
	assert.Equal(t, true, notify.ID > 0)
}

// TestShipperNotifyList
//
// @Description: 获取配送员通知列表
// @Author: Rixat
// @Time: 2023-11-06 09:54:47
// @receiver
// @param c *gin.Context
func TestShipperNotifyList(t *testing.T) {
	params := map[string]interface{}{
		"page":       1,
		"limit":      10,
		"start_date": "2023-11-07",
		"end_date":   "2023-11-07",
	}
	w := helper.SendGinGet("/ug/cms/v2/shipment/notify/list", helper.MapToUrlValues(params), "", "")
	fmt.Println("列表结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}
