package cms

import (
	"fmt"
	"mulazim-api/constants"
	"mulazim-api/models"
	"mulazim-api/tests/helper"
	"mulazim-api/tools"
	"testing"

	"github.com/go-playground/assert/v2"
	"github.com/golang-module/carbon/v2"
)

var ShipperID = 10450
var SelfSignID = 6966
var ShipperName = "shipper"
var ShipperMobile = "***********"

func TestShipmentCreateInfo(t *testing.T) {
	// 创建
	TestCreateShipperAccountInfo(t) // 1.账户信息(t_admin,t_self_sign_merchant_info表添加数据)
	TestCreateShipperIDCardInfo(t)  // 2.身份证信息（更新t_self_sign_merchant_info）
	TestCreateShipperBankInfo(t)    // 3.银行卡信息（更新t_self_sign_merchant_info）
	TestCreateShipperHealthInfo(t)  // 4.健康正信息（更新t_self_sign_merchant_info）
	// 更新配送员账户信息
	TestUpdateShipperStateInfo(t) // 修改支持现金订单状态
	// 修改状态
	UpdateShipperStateInfo(t, ShipperID, 1, 1) // 修改配送员状态
	UpdateShipperStateInfo(t, ShipperID, 1, 0) // 修改配送员状态
	UpdateShipperStateInfo(t, ShipperID, 2, 1) // 修改支持现金订单状态
	UpdateShipperStateInfo(t, ShipperID, 2, 0) // 修改支持现金订单状态
	// 详情
	TestGetShipperDetailInfo(t)
	// 列表
	TestGetShipperList(t)
	// 测试删除
	// TestDeleteShipper(t)

	// 结束
	TestEnd(t)
}

// 创建账号信息(创建成功返回的ShipperID和SelfSignID赋给全局变量)
func TestCreateShipperAccountInfo(t *testing.T) {
	ShipperName = "shipper__" + carbon.Now().Format("mdHi")
	ShipperMobile = "166" + carbon.Now().Format("mdHi")
	// 获取模板列表
	templateParams := map[string]interface{}{
		"page":  1,
		"limit": 1,
	}
	tempW := helper.SendGinGet("/ug/cms/v2/shipment/template/list", helper.MapToUrlValues(templateParams), "", "")
	templateRes, _ := tools.StringToMap(tempW.Body.String())
	templateID := templateRes["data"].(map[string]interface{})["items"].([]interface{})[0].(map[string]interface{})["id"]
	if tools.ToInt(templateID) == 0 {
		fmt.Println("请首先添加配送费模板，再添加配送员")
		assert.Equal(t, false, tools.ToInt(templateID) > 0)
	}
	db := tools.Db
	params := map[string]interface{}{
		"name":                       ShipperName,
		"mobile":                     ShipperMobile,
		"avatar":                     "/upload/avatar/111.jpg",
		"password":                   "ab123123",
		"shipper_income_template_id": templateID,
	}
	// 发送请求
	w := helper.SendGinPost("/ug/cms/v2/shipper/account-info", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
	res, _ := tools.StringToMap(w.Body.String())
	ShipperID = tools.ToInt(res["data"].(map[string]interface{})["shipper_id"])
	SelfSignID = tools.ToInt(res["data"].(map[string]interface{})["self_sign_id"])
	// 验证是否成功插入
	var admin models.Admin
	params["password"] = tools.MakeMd5(params["password"].(string))
	db.Table("t_admin").Where(params).Where("id=?", ShipperID).First(&admin)
	assert.Equal(t, true, admin.ID > 0)
	// 验证入驻信息
	var selfSignInfo models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).Where("id=? and restaurant_id=? and type=?", SelfSignID, ShipperID, constants.SELF_SIGN_TYPE_SHIPPER).First(&selfSignInfo)
	assert.Equal(t, true, selfSignInfo.Id > 0)
}

// TestUpdateShipperStateInfo
//
// @Description: 更新账户信息
// @Author: Rixat
// @Time: 2023-11-04 10:19:03
// @receiver
// @param c *gin.Context
func TestUpdateShipperStateInfo(t *testing.T) {
	ShipperName = "shipper__" + carbon.Now().Format("di")
	// ShipperMobile = "166" + carbon.Now().Format("mdHi")
	params := map[string]interface{}{
		"id":                         ShipperID,
		"name":                       ShipperName,
		"mobile":                     ShipperMobile,
		"avatar":                     "/upload/avatar/22.jpg",
		"password":                   "ab123122",
		"shipper_income_template_id": 76,
	}
	// 发送请求
	w := helper.SendGinPost("/ug/cms/v2/shipper/update-account", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("更新账户信息:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
	// 验证是否成功插入
	var admin models.Admin
	params["password"] = tools.MakeMd5(params["password"].(string))
	tools.Db.Table("t_admin").Where(params).First(&admin)
	assert.Equal(t, true, admin.ID > 0)
}

// 创建身份证信息
func TestCreateShipperIDCardInfo(t *testing.T) {
	db := tools.Db
	params := map[string]interface{}{
		"shipper_id":          ShipperID,
		"self_sign_id":        SelfSignID,
		"idcard_front_image":  "/upload/avatar/111.jpg",
		"idcard_back_image":   "/upload/avatar/111.jpg",
		"idcard_handel_image": "/upload/avatar/111.jpg",
		"gender":              1,
		"real_name":           "热夏提·柯尤木",
		"idcard_number":       "653121199405152638",
		"idcard_start_date":   "2013-10-10",
		"idcard_end_date":     "2023-10-10",
		"idcard_address":      "乌鲁木齐天山区延安路",
	}
	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipper/idcard-info", data, "", "",nil)
	fmt.Println("创建身份证信息结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
	// 验证是否成功插入
	var selfSignInfo models.SelfSignMerchantInfo
	db.Model(models.SelfSignMerchantInfo{}).Where(map[string]interface{}{
		"id":                 SelfSignID,
		"type":               constants.SELF_SIGN_TYPE_SHIPPER,
		"restaurant_id":      ShipperID,
		"mer_idcard_name":    params["real_name"],
		"mer_idcard_num":     params["idcard_number"],
		"mer_idcard_start":   params["idcard_start_date"],
		"mer_idcard_end":     params["idcard_end_date"],
		"legal_home_address": params["idcard_address"],
	}).First(&selfSignInfo)
	assert.Equal(t, true, selfSignInfo.Id > 0)
}

// 创建银行卡信息
func TestCreateShipperBankInfo(t *testing.T) {
	db := tools.Db
	params := map[string]interface{}{
		"self_sign_id":          SelfSignID,
		"bank_card_front_image": "/upload/avatar/111.jpg",
		"bank_card_back_image":  "/upload/avatar/111.jpg",
		"bank_acct_num":         "620000001212100011212012",
		"bank_id":               1,
		"bank_province_id":      1,
		"bank_city_id":          1,
		"bank_area_id":          1,
		"bank_branch_name":      "天山区延安路分行",
		"bank_branch_code":      "**********",
		"bank_bind_mobile":      "***********",
	}
	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipper/bank-info", data, "", "",nil)
	fmt.Println("创建银行卡信息结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
	// 验证是否成功插入
	var selfSignInfo models.SelfSignMerchantInfo
	params["id"] = SelfSignID
	params["restaurant_id"] = ShipperID
	params["bank_acct_name"] = "热夏提·柯尤木"
	delete(params, "self_sign_id")
	delete(params, "bank_card_front_image")
	delete(params, "bank_card_back_image")
	db.Model(models.SelfSignMerchantInfo{}).Where(params).First(&selfSignInfo)
	assert.Equal(t, true, selfSignInfo.Id > 0)
}

// 创建健康证信息
func TestCreateShipperHealthInfo(t *testing.T) {
	db := tools.Db
	params := map[string]interface{}{
		"self_sign_id":           SelfSignID,
		"certificate_image":      "/upload/avatar/111.jpg",
		"certificate_real_name":  "热夏提·柯尤木",
		"certificate_number":     "620000001212100011212012",
		"certificate_start_date": "2013-10-10",
		"certificate_end_date":   "2023-10-10",
	}
	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipper/health-info", data, "", "",nil)
	fmt.Println("创建健康证信息结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
	// 验证是否成功插入
	var selfSignHealthInfo models.SelfSignStuff
	params["type"] = constants.SELF_SIGN_TYPE_SHIPPER
	params["mer_info_id"] = SelfSignID
	delete(params, "self_sign_id")
	delete(params, "bank_card_front_image")
	delete(params, "bank_card_back_image")
	db.Model(models.SelfSignStuff{}).Where(map[string]interface{}{
		"type":                     constants.SELF_SIGN_TYPE_SHIPPER,
		"mer_info_id":              SelfSignID,
		"staff_name":               params["certificate_real_name"],
		"staff_idcard":             params["certificate_number"],
		"health_certificate_start": params["certificate_start_date"],
		"health_certificate_end":   params["certificate_end_date"],
		"health_certificate_image": params["certificate_image"],
	}).First(&selfSignHealthInfo)
	assert.Equal(t, true, selfSignHealthInfo.Id > 0)
}

// 删除
func TestDeleteShipper(t *testing.T) {
	db := tools.Db
	params := map[string]interface{}{
		"shipper_id": ShipperID,
	}
	// 发送请求
	w := helper.SendGinPost("/ug/cms/v2/shipper/delete", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("删除结果", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
	// 验证配送员是否删除成功
	var count int64
	db.Model(models.Admin{}).Where("id=? and deleted_at is NULL", params["shipper_id"]).Count(&count)
	assert.Equal(t, true, count == 0)
	// 入住信息是否删除成功
	db.Model(models.SelfSignMerchantInfo{}).Where("restaurant_id=? and type = 2 and deleted_at is NULL", params["shipper_id"]).Count(&count)
	assert.Equal(t, true, count == 0)
	// 有关照片是否删除成功
	db.Model(models.SelfSignImages{}).Where("restaurant_id=? and type = 2 and deleted_at is NULL", params["shipper_id"]).Count(&count)
	assert.Equal(t, true, count == 0)
	// 健康信息是否删除成功
	db.Model(models.SelfSignStuff{}).Where("mer_info_id=? and type = 2 and deleted_at is NULL", SelfSignID).Count(&count)
	assert.Equal(t, true, count == 0)

}

// 更新配送员状态（配送状态，现金订单状态）
// func TestUpdateShipperStateInfo1(t *testing.T) {
// 	UpdateShipperStateInfo(t, 1, 1, 1)
// }

// 更新配送员状态（配送状态，现金订单状态）
func UpdateShipperStateInfo(t *testing.T, shipperID int, stateType int, state int) {
	params := map[string]interface{}{
		"shipper_id": shipperID,
		"state":      state,
		"type":       stateType, // 1.配送员状态，2.支持现金订单状态
	}

	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipper/change-state", data, "", "",nil)
	stateStr := fmt.Sprintf("修改状态结果:(%d,%d)", stateType, state)
	fmt.Println(stateStr, w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
	// 验证配送员是否删除成功
	var admin models.Admin
	tools.Db.Model(admin).Where("id=? and type=9", shipperID).First(&admin)
	if stateType == 1 { // 配送员状态
		assert.Equal(t, true, admin.State == state)
	}
	if stateType == 2 { // 现金订单状态
		assert.Equal(t, true, admin.TakeCashOrder == uint(state))
	}
}

// 更新配送员状态（配送状态，现金订单状态）
func TestGetShipperDetailInfo(t *testing.T) {
	params := map[string]interface{}{
		"shipper_id": ShipperID,
	}

	// 发送请求
	w := helper.SendGinGet("/ug/cms/v2/shipper/info", helper.MapToUrlValues(params), "", "")
	fmt.Println("详情信息：", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}

// 配送员列表
func TestGetShipperList(t *testing.T) {
	params := map[string]interface{}{
		"page":    1,
		"limit":   10,
		"city_id": 1,
	}
	// 发送请求
	w := helper.SendGinGet("/ug/cms/v2/shipper/list", helper.MapToUrlValues(params), "", "")
	fmt.Println("列表信息：", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}

func TestGetBankAreasList(t *testing.T) {
	params := map[string]interface{}{
		"level":  3,    // 1：地区，2：城市，3：区域/县
		"p_code": 6501, // 父编号
	}
	// 发送请求
	w := helper.SendGinGet("/ug/cms/v2/shipper/bank-areas", helper.MapToUrlValues(params), "", "")
	// fmt.Println("银行区域", w.Body.String())
	// w = helper.SendGinGet("/ug/cms/v2/shipper/bank-list", helper.MapToUrlValues(params), "", "")
	// fmt.Println("银行卡列表：", w.Body.String())
	// params = map[string]interface{}{
	// 	"areaCode": 6501,   // 1：地区，2：城市，3：区域/县
	// 	"key":      "交通银行", // 父编号
	// }
	// w = helper.SendGinGet("/ug/cms/v2/shipper/branch-bank", helper.MapToUrlValues(params), "", "")
	// fmt.Println("银行分支列表：", w.Body.String())

	// params = map[string]interface{}{
	// 	"mobile": "***********",
	// }
	w = helper.SendGinPost("/ug/cms/v2/shipper/send-code", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("验证码：", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}
