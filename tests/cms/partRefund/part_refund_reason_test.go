package partRefund

import (
	"fmt"
	"github.com/go-playground/assert/v2"
	partRefundRequest "mulazim-api/requests/cms/partRefund"
	"mulazim-api/tests/helper"
	"testing"
)

func TestPartRefundReasonCreate(t *testing.T) {
	request := partRefundRequest.PartRefundReasonCreateRequest{
		NameUg: "这是测试数据1",
		NameZh: "这是测试数据2",
		State: 1,
		Weight: 1,
	}

	w := helper.SendGinPostWithJSON("/ug/cms/v2/part-refund/create-refund-reason", request, "", "")
	fmt.Println("结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

}


func TestPartRefundReasonUpdate(t *testing.T) {
	request := partRefundRequest.PartRefundReasonUpdateRequest{
		ID: 1,
		NameUg: "这是测试数据1",
		NameZh: "这是测试数据2",
		State: 1,
		Weight: 1,
	}

	w := helper.SendGinPostWithJSON("/ug/cms/v2/part-refund/update-refund-reason", request, "", "")
	fmt.Println("结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

}

func TestPartRefundReasonDelete(t *testing.T) {
	type Params struct {
		ID     int    `json:"id" binding:"required"`
	}
	request := Params{
		ID:  1,
	}


	w := helper.SendGinPostWithJSON("/ug/cms/v2/part-refund/delete-refund-reason", request, "", "")
	fmt.Println("结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

}

