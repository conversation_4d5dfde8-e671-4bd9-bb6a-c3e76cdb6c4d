package cms

import (
	"fmt"
	"mulazim-api/models"
	"mulazim-api/requests/foodsMultipleDiscount"
	"mulazim-api/tests/helper"
	"mulazim-api/tools"
	"testing"

	"github.com/go-playground/assert/v2"
)

// 测试创建多分打折
func TestCreateFoodsMultipleDiscount(t *testing.T) {
	
	// 获取美食ID 
	db := tools.GetDB()
	var food models.RestaurantFoods
	db.Model(models.RestaurantFoods{}).Where("state = ?",models.RESTAURANTFOODS_STATE_OPEN).First(&food)

	t.Run("创建基本多分打折", func(t *testing.T) {
		request := foodsMultipleDiscount.FoodsMultipleDiscountCreateRequest{
			FoodID:           food.ID,
			Discount2Percent: 90,
			Discount3Percent: 80,
			Discount4Percent: 70,
			Discount5Percent: 60,
			StartTime:        "2025-10-10 10:10:10",
			EndTime:          "2025-10-20 10:10:10",
		}

		w := helper.SendGinPostWithJSON("/zh/cms/v2/foods-multiple-discount/create", request, "", "")
		fmt.Println("创建结果:", w.Body.String())
		assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
	})

	t.Run("创建时间参数错误的活动", func(t *testing.T) {
		request := foodsMultipleDiscount.FoodsMultipleDiscountCreateRequest{
			FoodID:           food.ID,
			Discount2Percent: 80,
			Discount3Percent: 70,
			Discount4Percent: 60,
			Discount5Percent: 50,
			StartTime:        "2025-09-01 08:00:00", // 时间错误
			EndTime:          "2025-06-01 20:00:00",
		}

		w := helper.SendGinPostWithJSON("/zh/cms/v2/foods-multiple-discount/create", request, "", "")
		fmt.Println("创建结果(时间错误):", w.Body.String())
		assert.Equal(t, -1000, helper.GetResCode(w.Body.String()))
	})

}

// 测试修改多分打折
func TestUpdateFoodsMultipleDiscount(t *testing.T) {
	multipleDiscountId := 10
	// 创建模拟数据
	request := foodsMultipleDiscount.FoodsMultipleDiscountUpdateRequest{
		ID:               multipleDiscountId,
		Discount2Percent: 85,
		Discount3Percent: 75,
		Discount4Percent: 65,
		Discount5Percent: 55,
		StartTime:        "2025-10-10 10:10:10",
		EndTime:          "2025-10-20 10:10:10",
	}

	w := helper.SendGinPostWithJSON("/zh/cms/v2/foods-multiple-discount/update", request, "", "")
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}


// 测试多分打折修改状态
func TestChangeStateFoodsMultipleDiscount(t *testing.T) {
	multipleDiscountId := 10
	// 创建模拟数据
	request := foodsMultipleDiscount.FoodsMultipleDiscountChangeStateRequest{
		ID:               multipleDiscountId,
	}

	w := helper.SendGinPostWithJSON("/zh/cms/v2/foods-multiple-discount/change-state", request, "", "")
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}


// 测试删除多分打折
func TestDeleteFoodsMultipleDiscount(t *testing.T) {
	multipleDiscountId := 10
	// 创建模拟数据
	request := foodsMultipleDiscount.FoodsMultipleDiscountDeleteRequest{
		ID:               multipleDiscountId,
	}

	w := helper.SendGinPostWithJSON("/zh/cms/v2/foods-multiple-discount/delete", request, "", "")
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}
