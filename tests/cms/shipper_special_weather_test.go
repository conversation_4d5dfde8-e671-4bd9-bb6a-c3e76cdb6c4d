package cms

// @Description: 特殊天气
// @Author: Rixat
// @Time: 2023-10-31 02:29:10
// @receiver
// @param c *gin.Context

import (
	"fmt"
	models "mulazim-api/models/shipment"
	"mulazim-api/tests/helper"
	"mulazim-api/tools"
	"testing"

	"github.com/go-playground/assert/v2"
	"github.com/golang-module/carbon/v2"
)

var WeatherID = 28
var WeatherNameUg = "sinak"
var WeatherNameZh = "测试"

// TestSpecialWeatherAll
//
// @Description: 测试全部
// @Author: Rixat
// @Time: 2023-11-06 09:52:32
// @receiver
// @param c *gin.Context
func TestSpecialWeatherAll(t *testing.T) {

	TestSpecialWeatherCreate(t)      // 创建
	TestSpecialWeatherUpdate(t)      // 更新（状态位0）
	TestSpecialWeatherDetail(t)      // 详情
	TestSpecialWeatherChangeState(t) // 状态改为1
	TestSpecialWeatherList(t)        // 列表
	TestSpecialWeatherDelete(t)      // 删除
	// 结束（为了打印输出内容）
	TestEnd(t)
}

// TestSpecialWeatherCreate
//
// @Description: 创建特殊天气
// @Author: Rixat
// @Time: 2023-11-02 10:51:58
// @receiver
// @param c *gin.Context
func TestSpecialWeatherCreate(t *testing.T) {
	WeatherNameUg = "天气_" + carbon.Now().Format("mdHi")
	WeatherNameZh = "天气_" + carbon.Now().Format("mdHi")
	// 基本信息
	params := map[string]interface{}{
		"name_ug":      WeatherNameUg,
		"name_zh":      WeatherNameZh,
		"shipment_fee": 2,
		"start_time":   "2023-10-01 00:00:00",
		"end_time":     "2023-10-02 00:00:00",
		"state":        1,
	}
	// 发送请求
	w := helper.SendGinPost("/ug/cms/v2/shipment/special-weather/create", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var weather models.ShipperSpecialWeather
	params["shipment_fee"] = 200
	tools.Db.Model(weather).Where(params).First(&weather)
	assert.Equal(t, true, weather.ID > 0)
	WeatherID = weather.ID
}

// TestSpecialWeatherUpdate
//
// @Description: 更新特殊天气
// @Author: Rixat
// @Time: 2023-11-06 09:53:26
// @receiver
// @param c *gin.Context
func TestSpecialWeatherUpdate(t *testing.T) {
	WeatherNameUg = "update_天气_" + carbon.Now().Format("mdHi")
	WeatherNameZh = "update_天气_" + carbon.Now().Format("mdHi")
	params := map[string]interface{}{
		"id":           WeatherID,
		"name_ug":      WeatherNameUg,
		"name_zh":      WeatherNameZh,
		"shipment_fee": 3,
		"start_time":   "2023-10-01 00:00:00",
		"end_time":     "2023-10-03 00:00:00",
		"state":        0,
	}
	// 发送请求
	w := helper.SendGinPost("/ug/cms/v2/shipment/special-weather/update", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("更新结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var weather models.ShipperSpecialWeather
	params["shipment_fee"] = 300
	tools.Db.Model(weather).Where(params).First(&weather)
	assert.Equal(t, true, weather.ID > 0)
}

// TestSpecialWeatherChangeState
//
// @Description: 修改状态
// @Author: Rixat
// @Time: 2023-11-06 09:53:46
// @receiver
// @param c *gin.Context
func TestSpecialWeatherChangeState(t *testing.T) {
	params := map[string]interface{}{
		"id":    WeatherID,
		"state": 1,
	}
	// 发送请求
	w := helper.SendGinPost("/ug/cms/v2/shipment/special-weather/change-state", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("更新状态结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var weather models.ShipperSpecialWeather
	tools.Db.Model(weather).Where(params).First(&weather)
	assert.Equal(t, true, weather.ID > 0)
}

// / TestSpecialWeatherDelete
//
// @Description: 删除特殊天气
// @Author: Rixat
// @Time: 2023-11-06 09:54:04
// @receiver
// @param c *gin.Context
func TestSpecialWeatherDelete(t *testing.T) {
	// 发送请求
	params := map[string]interface{}{
		"id": WeatherID,
	}
	w := helper.SendGinPost("/ug/cms/v2/shipment/special-weather/delete", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("删除结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
	// 验证删除结果
	var count int64
	tools.Db.Model(models.ShipperSpecialWeather{}).Where(params).Count(&count)
	assert.Equal(t, true, count == 0)
}

// / TestSpecialWeatherDetail
//
// @Description: 特殊天气详情
// @Author: Rixat
// @Time: 2023-11-06 09:54:27
// @receiver
// @param c *gin.Context
func TestSpecialWeatherDetail(t *testing.T) {
	params := map[string]interface{}{
		"id": WeatherID,
	}
	w := helper.SendGinGet("/ug/cms/v2/shipment/special-weather/detail", helper.MapToUrlValues(params), "", "")
	fmt.Println("详情结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}

// / TestSpecialWeatherList
//
// @Description: 特殊天气列表
// @Author: Rixat
// @Time: 2023-11-06 09:54:47
// @receiver
// @param c *gin.Context
func TestSpecialWeatherList(t *testing.T) {
	params := map[string]interface{}{
		"page":  1,
		"limit": 10,
	}
	w := helper.SendGinGet("/ug/cms/v2/shipment/special-weather/list", helper.MapToUrlValues(params), "", "")
	fmt.Println("列表结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}
