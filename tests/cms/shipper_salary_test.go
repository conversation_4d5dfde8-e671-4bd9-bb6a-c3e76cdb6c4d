package cms

// @Description: 特殊天气
// @Author: Rixat
// @Time: 2023-10-31 02:29:10
// @receiver
// @param c *gin.Context

import (
	"fmt"
	"mulazim-api/tests/helper"
	"testing"

	"github.com/go-playground/assert/v2"
)

// / TestSpecialWeatherList
//
// @Description: 特殊天气列表
// @Author: Rixat
// @Time: 2023-11-06 09:54:47
// @receiver
// @param c *gin.Context
func TestShipperDetail(t *testing.T) {
	params := map[string]interface{}{
		"page":  1,
		"limit": 10,
		"id":    17,
		"month": "2023-11",
	}
	w := helper.SendGinGet("/ug/cms/v2/shipment/salary/detail", helper.MapToUrlValues(params), "", "")
	fmt.Println("列表结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}
