package cms

import (
	"encoding/json"
	"fmt"
	"net/url"
	"time"

	// "net/url"
	"math"
	// "mulazim-api/configs"
	// "mulazim-api/models"

	"mulazim-api/inits"
	"mulazim-api/models"
	"mulazim-api/tests/helper"
	"mulazim-api/tools"

	"mulazim-api/resources"
	responses "mulazim-api/resources/response"

	"os/exec"
	"testing"

	"github.com/go-playground/assert/v2"
	"github.com/xuri/excelize/v2"
)

func TestPartRefund(t *testing.T) {

    for i := 1; i < 4; i++ {

        orderId :=PartRefundOrderSend(t,i,true)//秒杀 数据全部删除后发送 模拟第1次发送 
        PartRefundOrderValidate(t,orderId,true)
        time.Sleep(time.Second * 5)
        orderId =PartRefundOrderSend(t,i,false)//秒杀 数据全部删除后发送 模拟第2次发送    
        PartRefundOrderValidate(t,orderId,false)
     
    }

    //2.数据验证 

}

type OrderProfit struct {
	MpProfit     int64 `gorm:"column:mp_profit"`
	DealerProfit int64 `gorm:"column:dealer_profit"`
	ResProfit    int64 `gorm:"column:res_profit"`
	ActualPaid   int64 `gorm:"column:actual_paid"`
}

func OrderFix(t *testing.T,order_id int64,action string) bool{
	db := tools.ReadDb1
	//	select sum(price*number+lunch_box_fee*lunch_box_count) from t_order_detail where order_id = 11641855;
	var order_detail_price, original_shipment float64

	var orderProfit OrderProfit
	var order models.OrderToday
	// var order models.Order
    
	db.Table(orderTable).Where("id = ?", order_id).Find(&orderProfit)
	db.Table(orderTable).Where("id = ?", order_id).Find(&order)
    if order.ID == 0 {
        tools.Logger.Info(fmt.Sprintf("order_id:%d 不存在", order_id))
        return false
    }
	db.Model(&models.OrderDetail{}).
		Select("sum(price*number+lunch_box_fee*lunch_box_count) as total").
		Where("order_id = ?", order_id).Pluck("total",&order_detail_price)

	var mp_percent, dealer_percent,part_refund_ratio float64

	db.Model(&models.OrderDetail{}).
		Select("mp_percent").
		Where("order_id = ?", order_id).Limit(1).Pluck("mp_percent",&mp_percent)
	var orderDetails []models.OrderDetail
	db.Model(&models.OrderDetail{}).
		Where("order_id = ?", order_id).Find(&orderDetails)

	db.Model(&models.OrderDetail{}).
		Select("AVG(`dealer_percent`) as dealer_percent").
		Where("order_id = ?", order_id).Limit(1).Pluck("dealer_percent",&dealer_percent)
	db.Table(orderTable).
		Select("original_shipment").
		Where("id = ?", order_id).Pluck("original_shipment",&original_shipment)
	var snow_price int64
	db.Table("t_mini_game_activity_user_log").
		Select("-amount").
		Where("order_id = ?", order_id).Pluck("-amount",&snow_price)

    db.Table("t_order_extend").
        Select("part_refund_ratio").
		Where("order_id = ?", order_id).Pluck("part_refund_ratio",&part_refund_ratio)    

	var man_jian int64
	db.Table("t_marketing_order_log").
		Select("step_reduce").
		Where("order_id = ?", order_id).
		Where("type = 1").Pluck("step_reduce",&man_jian)

	var jian_peisongfei_res int64
	db.Table("t_marketing_order_log").
		Select("res_cost").
		Where("order_id = ?", order_id).
		Where("type = 2").Pluck("res_cost",&jian_peisongfei_res)

	var jian_peisongfei_dealer int64
	db.Table("t_marketing_order_log").
		Select("dealer_cost").
		Where("order_id = ?", order_id).
		Where("type = 2").Pluck("dealer_cost",&jian_peisongfei_dealer)

	// 代理承担的优惠券
	var coupon_price int64
	// select t_coupon.price from t_coupon_log
	// left join t_coupon on t_coupon_log.coupon_id = t_coupon.id
	// where t_coupon_log.order_id = 12817345 and t_coupon.type = 4;

	db.Table("t_coupon_log").
		Select("t_coupon.price").
		Joins("left join t_coupon on t_coupon_log.coupon_id = t_coupon.id").
		Where("t_coupon_log.order_id = ?", order_id).
		Where("t_coupon.type = 4").Pluck("t_coupon.price",&coupon_price)

	modelPath := inits.ConfigFilePath+"/xls/muban-jiajia.xlsx"
	outPath := fmt.Sprintf("%s/xls/out/%d-%s.xlsx", inits.ConfigFilePath, order_id,action)
	f, err := excelize.OpenFile(modelPath)
	if err != nil {
		fmt.Println(err)
		return false
	}
	err = f.SetCellValue("Sheet1", "D3", dealer_percent/100)
	err = f.SetCellValue("Sheet1", "D2", mp_percent/100)
	err = f.SetCellValue("Sheet1", "D9", original_shipment)
	err = f.SetCellValue("Sheet1", "D8", order_detail_price)
	err = f.SetCellValue("Sheet1", "D10", man_jian)
	err = f.SetCellValue("Sheet1", "D19", snow_price)
	err = f.SetCellValue("Sheet1", "D12", jian_peisongfei_dealer+jian_peisongfei_res)
	err = f.SetCellValue("Sheet1", "D25", jian_peisongfei_dealer)
	err = f.SetCellValue("Sheet1", "D26", jian_peisongfei_res)

	err = f.SetCellValue("Sheet1", "D53", orderProfit.ResProfit)
	err = f.SetCellValue("Sheet1", "D54", orderProfit.MpProfit)
	err = f.SetCellValue("Sheet1", "D55", orderProfit.DealerProfit)
	err = f.SetCellValue("Sheet1", "D56", orderProfit.ActualPaid)
	err = f.SetCellValue("Sheet1", "D15", coupon_price)
    err = f.SetCellValue("Sheet1", "G3", part_refund_ratio)
    

	fromIndex := 68
	//orderDetails
	for i, v := range orderDetails {
		err = f.SetCellValue("Sheet1", fmt.Sprintf("A%d", i+fromIndex), order.ID)
		err = f.SetCellValue("Sheet1", fmt.Sprintf("B%d", i+fromIndex), order.OrderID)
		err = f.SetCellValue("Sheet1", fmt.Sprintf("C%d", i+fromIndex), v.StoreFoodsID)
		err = f.SetCellValue("Sheet1", fmt.Sprintf("D%d", i+fromIndex), v.Price)
		err = f.SetCellValue("Sheet1", fmt.Sprintf("E%d", i+fromIndex), v.Number)
		err = f.SetCellValue("Sheet1", fmt.Sprintf("F%d", i+fromIndex), v.LunchBoxCount)
		err = f.SetCellValue("Sheet1", fmt.Sprintf("G%d", i+fromIndex), v.LunchBoxFee)
		err = f.SetCellValue("Sheet1", fmt.Sprintf("H%d", i+fromIndex), v.DealerPercent)
		err = f.SetCellValue("Sheet1", fmt.Sprintf("I%d", i+fromIndex), v.MpPercent)
		err = f.SetCellValue("Sheet1", fmt.Sprintf("J%d", i+fromIndex), v.ResProfit)
		err = f.SetCellValue("Sheet1", fmt.Sprintf("K%d", i+fromIndex), v.MpProfit)
		err = f.SetCellValue("Sheet1", fmt.Sprintf("L%d", i+fromIndex), v.DealerProfit)
		err = f.SetCellValue("Sheet1", fmt.Sprintf("M%d", i+fromIndex), int64(v.PriceMarkupPrice))
	}
	if err != nil {
		println("无法设置单元格值: %v", err)
	}
	f.UpdateLinkedValue()
	err = f.SaveAs(outPath)
	if err != nil {
		println("无法保存文件: %v", err)
	}

	javaProjectPath := inits.ConfigFilePath+"/xls/"
	jarFilePath := javaProjectPath + "excel-formula-1.0-SNAPSHOT.jar"

	cmd := exec.Command("java", "-jar", jarFilePath, outPath)
	cmd.Run()

	f, err = excelize.OpenFile(outPath)
	l_res_profit, err := f.GetCellValue("Sheet1", "D44", excelize.Options{RawCellValue: false})
	l_dealer_profit, err := f.GetCellValue("Sheet1", "D46", excelize.Options{RawCellValue: false})
	l_mp_profit, err := f.GetCellValue("Sheet1", "D45", excelize.Options{RawCellValue: false})
	l_actual_paid, err := f.GetCellValue("Sheet1", "D20", excelize.Options{RawCellValue: false})
	if err != nil {
		println("无法获取单元格值: %v", err)
	}
	if MyEqual(tools.ToFloat64(orderProfit.MpProfit), tools.ToFloat64(l_mp_profit)) &&
		MyEqual(tools.ToFloat64(orderProfit.DealerProfit), tools.ToFloat64(l_dealer_profit)) &&
		MyEqual(tools.ToFloat64(orderProfit.ResProfit), tools.ToFloat64(l_res_profit)) &&
		MyEqual(tools.ToFloat64(orderProfit.ActualPaid), tools.ToFloat64(l_actual_paid)) {
	} else {
		//select count(dealer_percent)  from t_order_detail where order_id = 11662782 GROUP BY dealer_percent;
		var c int64
		db.Table("t_order_detail").Where("order_id = ?", order_id).Group("dealer_percent").Count(&c)
		//if c>1 {
		//
		//}else{
		tools.Logger.Infof("出错订单------%d", order_id)
		tools.Logger.Infof("excel 计算出来的 mp_profit:%s,dealer_profit:%s,res_profit:%s,实际支付:%s", l_mp_profit, l_dealer_profit, l_res_profit, l_actual_paid)
		tools.Logger.Infof("数据库中记录的 mp_profit:%d,dealer_profit:%d,res_profit:%d,实际支付:%d", orderProfit.MpProfit, orderProfit.DealerProfit, orderProfit.ResProfit, orderProfit.ActualPaid)
        
        return false
		//}

	}
    return true
}

func MyEqual(oldPrice float64, newPrice float64) bool {
	return math.Abs((oldPrice - newPrice)) < 4
}

var orderTable = "t_order_today"

func PartRefundOrderValidate(t *testing.T,orderId int,isPartRefund bool)  {
    db :=tools.GetDB()
    if isPartRefund { //部分退款后的验证 

        //1.验证目标 
        //1.1 找出原始订单数据  找出第一次退款记录  判断退款的部分和 实际的吸入的数据是否合理 

        var partRefund models.OrderPartRefund
        //原始部分退款记录 
        db.Model(&models.OrderPartRefund{}).Where("order_id = ? and part_refund_type = ?",orderId,2).Find(&partRefund)
        //
        var originalDetails []models.OrderPartRefundDetail

        //原始记录的备份数据  原始记录和不能分退款的记录 
        db.Model(&models.OrderPartRefundDetail{}).Where("part_refund_id = ? and type=?",partRefund.ID,1).Find(&originalDetails)


        var currentRefundDetails []models.OrderPartRefundDetail
        //本次退款的部分 
        db.Model(&models.OrderPartRefundDetail{}).Where("part_refund_id = ? and type=?",partRefund.ID,2).Find(&currentRefundDetails)

        var currentDetails []models.OrderPartRefundDetail

        for _, original := range originalDetails {
            flag :=true
            for _, current := range currentRefundDetails {
                if current.OrderDetailId == original.OrderDetailId { //原始记录中 减去 部分退款的部分 来获取 现在的 订单 详细 内容 
                    currentCount :=original.Number-current.Number
                    if currentCount <= 0 {
                        flag =false
                    }
                    original.Number = currentCount
                }
            }
            if flag {
                currentDetails = append(currentDetails, original)
            }
        }
        //找出现在的订单详情数据 比较 数据 
        var nowDetails []models.OrderDetail
        db.Model(&models.OrderDetail{}).Where("order_id = ?",orderId).Find(&nowDetails)
        //现在的订单数量必须一致 
        assert.Equal(t, len(nowDetails), len(currentDetails))
        for _, detail := range nowDetails {
            for _, rf := range currentDetails {
                if detail.ID == int(rf.OrderDetailId) {
                    assert.Equal(t, detail.Number, uint(rf.Number))
                    // assert.Equal(t, detail.RefundPrice, int(rf.RefundPrice))
                    // assert.Equal(t, detail.LunchBoxRefundPrice, int(rf.LunchBoxRefundPrice))
                }
                
            }
        }
        //TODO 是否需要进行利润的验证 
    }

}

func PartRefundOrderSend(t *testing.T,number int,deleteAndCreate bool) int {
    //1.数据准备 
    orderId,foods,luncBox,refundAmount :=PartRefundOrderInsert(t,number,deleteAndCreate)//秒杀 数据全部删除后发送 模拟第一次发送 
    tools.Logger.Info("orderId",orderId)
    params :=resources.PartRefund{
        OrderId:orderId,
        RefundType:2,//2.部分退款 1.全额退款
        RefundAmount:refundAmount,
        FoodDetails: foods,
        LunchBoxDetails: luncBox,
        From: "cms",
        ReasonId: 1,
        ReasonText: "测试退款",
    }
   
    if params.RefundAmount == 0 {
        tools.Logger.Info("orderId:",orderId)
    }
    assert.NotEqual(t, params.RefundAmount, 0)
    //发送http 请求 
    bts,_:=json.Marshal(foods)
    var foodsMap []map[string]interface{}
    json.Unmarshal(bts, &foodsMap)
    bts,_=json.Marshal(luncBox)
    var lunchBoxMap []map[string]interface{}
    json.Unmarshal(bts, &lunchBoxMap)
    


    data :=map[string]interface{}{
        "order_id": orderId,
        "refund_type": params.RefundType,
        "refund_amount": params.RefundAmount,
        "reason_id": params.ReasonId,
        "from": params.From,
        "reason_text": params.ReasonText,
        "food_details":foodsMap,
        "lunch_box_details":lunchBoxMap,
    }
    

    headers :=map[string]interface{}{
        "From":"cms",
        "refund_test_user":2036,//天山区代理账号
    }
    if deleteAndCreate {
        OrderFix(t,int64(orderId),"first")    
    }
    
    res :=helper.SendGinPostJSON("/ug/payment/v2/part-refund",data,"","",headers)
    tools.Logger.Info("res:",string(res.Body.Bytes()))
    assert.Equal(t, res.Code, 200)

    if deleteAndCreate {
        OrderFix(t,int64(orderId),"part_refund")    
    }else{
        OrderFix(t,int64(orderId),"all_refund")    
    }
    var successResponse responses.SuccessResponse
    json.Unmarshal(res.Body.Bytes(), &successResponse)
    assert.Equal(t, successResponse.Status, 200)
    if successResponse.Status != 200 {
        tools.Logger.Error("参数错误:", successResponse.Msg)
    }
    //数据验证 查看退款数据的内容 
    return orderId
    
}

func PartRefundOrderInsert(t *testing.T,number int,deleteRefundRecord bool) (int,[]resources.PartRefundFoodDetail,[]resources.PartRefundLunchBoxDetails,int){
    orderId :=0
    orderDetailId :=0
    db :=tools.GetDB()
    switch number {
        case 1://秒杀  
            orderId =11786611
            orderDetailId =18563091
            if deleteRefundRecord {
                sql := fmt.Sprintf("delete from t_order_today where id = %d",orderId)
                db.Exec(sql)
                sql = "INSERT INTO `mulazimpro`.`t_order_today` (`id`, `random_id`, `terminal_id`, `order_id`, `category_id`, `city_id`, `area_id`, `store_id`, `user_id`, `building_id`, `order_address`, `name`, `mobile`, `consume_type`, `pay_type`, `original_price`, `price`, `shipment`, `original_shipment`, `lunch_box_fee`, `full_reduction_amount`, `mp_profit`, `dealer_profit`, `res_profit`, `cash`, `coin`, `consume`, `description`, `timezone`, `booking_time`, `pay_time`, `print_time`, `printed_time`, `delivery_taked_time`, `delivery_start_time`, `delivery_end_time`, `real_shipment`, `deduction_fee`, `taked`, `shipper_id`, `sent_sms`, `delete_flag`, `refund_type`, `refund_chanel`, `refunded`, `is_douyin`, `send_notify`, `serial_number`, `shipper_complete_grant`, `is_luck_drawed`, `is_auto_refund`, `is_commented`, `distance`, `order_type`, `called`, `pay_channel`, `pay_platform`, `state`, `created_at`, `updated_at`, `deleted_at`, `cash_clear_state`, `cash_clear_time`, `cash_clear_channel`, `cash_clear_admin`, `seckill_state`, `market_type`, `is_auto_fished`, `self_take_number`, `delivery_type`, `json_info`, `actual_paid`, `order_price`, `total_discount_amount`, `price_markup_in_price`, `order_price_res`, `shipper_reward`) VALUES (11786611, 'tqqkqlhw', 8, 'lafingqing0000002502261715140039', 1, 1, 1, 7978, 1096059, 1979, 'يەنئەن يولى', 'روزى مەمەت', '18097690669', 3, 6, 10600, 8580, 0, 0, 400, NULL, 413, 1382, 7184, 0, 0, 0, '(چوكا قوشۇق 8 كىشلىك)', 8, '2025-02-26 17:39:12', '2025-02-26 17:15:37', '2025-02-26 17:15:14', '2025-02-26 17:15:42', NULL, NULL, NULL, NULL, NULL, 0, NULL, 1, 0, 0, 0, 0, NULL, 0, 2, 0, 0, 0, 0, 0.00, 1, 0, NULL, 1, 4, '2025-02-26 17:15:14', '2025-02-26 17:15:43', NULL, 0, NULL, NULL, NULL, 1, 1, NULL, NULL, 1, NULL, 8980, 8980, 0, 0, 8980, 0);";
                db.Exec(sql)
                sql =fmt.Sprintf("delete from t_order_detail where order_id = %d",orderId)
                db.Exec(sql)
                sql = "INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`, `activity_type`, `activity_id`, `res_profit`, `pref_id`, `price_markup_id`, `price_markup_price`, `refund_price`, `lunch_box_refund_price`, `lunch_box_food_count`) VALUES (18563091, 11786611, 271414, 2000, 990, 4.6, 100, 15.4, 105, 351, 2, 1, 100, 3, 1450568, '2025-02-26 17:15:14', '2025-02-26 17:15:14', NULL, 3, 3461312, 1824, 0, 0, 0, 990, 100, 0);"
                db.Exec(sql)
                
                sql ="INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`, `activity_type`, `activity_id`, `res_profit`, `pref_id`, `price_markup_id`, `price_markup_price`, `refund_price`, `lunch_box_refund_price`, `lunch_box_food_count`) VALUES (18563092, 11786611, 271414, 2000, 2000, 4.6, 100, 15.4, 92, 308, 1, NULL, 0, 0, 0, '2025-02-26 17:15:14', '2025-02-26 17:15:14', NULL, 1, 0, 1600, 0, 0, 0, 2000, 0, 0);"
                db.Exec(sql)
                sql ="INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`, `activity_type`, `activity_id`, `res_profit`, `pref_id`, `price_markup_id`, `price_markup_price`, `refund_price`, `lunch_box_refund_price`, `lunch_box_food_count`) VALUES (18563093, 11786611, 271434, 3000, 3000, 4.6, 100, 15.4, 143, 477, 1, 1, 100, 1, 0, '2025-02-26 17:15:14', '2025-02-26 17:15:14', NULL, 1, 0, 2480, 0, 0, 0, 3000, 100, 0);"
                db.Exec(sql)
                sql ="INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`, `activity_type`, `activity_id`, `res_profit`, `pref_id`, `price_markup_id`, `price_markup_price`, `refund_price`, `lunch_box_refund_price`, `lunch_box_food_count`) VALUES (18563094, 11786611, 271514, 400, 400, 4.6, 100, 15.4, 74, 246, 4, 0, 0, 4, 0, '2025-02-26 17:15:14', '2025-02-26 17:15:14', NULL, 1, 0, 1280, 0, 0, 0, 400, 0, 0);";
                db.Exec(sql)
            }

            
            
        case 2://优惠
            orderId =11786612
            orderDetailId =18563095
            if deleteRefundRecord {
                sql := fmt.Sprintf("delete from t_order_today where id = %d",orderId)
                db.Exec(sql)
                sql = "INSERT INTO `mulazimpro`.`t_order_today` (`id`, `random_id`, `terminal_id`, `order_id`, `category_id`, `city_id`, `area_id`, `store_id`, `user_id`, `building_id`, `order_address`, `name`, `mobile`, `consume_type`, `pay_type`, `original_price`, `price`, `shipment`, `original_shipment`, `lunch_box_fee`, `full_reduction_amount`, `mp_profit`, `dealer_profit`, `res_profit`, `cash`, `coin`, `consume`, `description`, `timezone`, `booking_time`, `pay_time`, `print_time`, `printed_time`, `delivery_taked_time`, `delivery_start_time`, `delivery_end_time`, `real_shipment`, `deduction_fee`, `taked`, `shipper_id`, `sent_sms`, `delete_flag`, `refund_type`, `refund_chanel`, `refunded`, `is_douyin`, `send_notify`, `serial_number`, `shipper_complete_grant`, `is_luck_drawed`, `is_auto_refund`, `is_commented`, `distance`, `order_type`, `called`, `pay_channel`, `pay_platform`, `state`, `created_at`, `updated_at`, `deleted_at`, `cash_clear_state`, `cash_clear_time`, `cash_clear_channel`, `cash_clear_admin`, `seckill_state`, `market_type`, `is_auto_fished`, `self_take_number`, `delivery_type`, `json_info`, `actual_paid`, `order_price`, `total_discount_amount`, `price_markup_in_price`, `order_price_res`, `shipper_reward`) VALUES (11786612, 'l8umcmtt', 8, 'lafingqing0000002502261720440042', 1, 1, 1, 7978, 1096059, 1979, 'يەنئەن يولى', 'روزى مەمەت', '18097690669', 3, 6, 13000, 12200, 0, 0, 500, NULL, 585, 1955, 10160, 0, 0, 0, '(چوكا قوشۇق 5 كىشلىك)', 8, '2025-02-26 18:14:31', '2025-02-26 17:20:49', '2025-02-26 17:20:44', '2025-02-26 17:21:13', NULL, NULL, NULL, NULL, NULL, 0, NULL, 1, 0, 0, 0, 0, NULL, 0, 3, 0, 0, 0, 0, 0.00, 1, 0, NULL, 1, 4, '2025-02-26 17:20:44', '2025-02-26 17:21:13', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, NULL, 1, NULL, 12700, 12700, 0, 0, 12700, 0);";
                db.Exec(sql)
                sql =fmt.Sprintf("delete from t_order_detail where order_id = %d",orderId)
                db.Exec(sql)
                sql = "INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`, `activity_type`, `activity_id`, `res_profit`, `pref_id`, `price_markup_id`, `price_markup_price`, `refund_price`, `lunch_box_refund_price`, `lunch_box_food_count`) VALUES (18563095, 11786612, 271416, 2300, 1900, 4.6, 100, 15.4, 184, 616, 2, 1, 100, 2, 0, '2025-02-26 17:20:44', '2025-02-26 17:20:44', NULL, 2, 284630, 3200, 284630, 0, 0, 1900, 100, 0);"
                db.Exec(sql)
                
                sql = "INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`, `activity_type`, `activity_id`, `res_profit`, `pref_id`, `price_markup_id`, `price_markup_price`, `refund_price`, `lunch_box_refund_price`, `lunch_box_food_count`) VALUES (18563096, 11786612, 271434, 3000, 3000, 4.6, 100, 15.4, 143, 477, 1, 1, 100, 1, 0, '2025-02-26 17:20:44', '2025-02-26 17:20:44', NULL, 1, 0, 2480, 0, 0, 0, 3000, 100, 0);"
                db.Exec(sql)
                sql = "INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`, `activity_type`, `activity_id`, `res_profit`, `pref_id`, `price_markup_id`, `price_markup_price`, `refund_price`, `lunch_box_refund_price`, `lunch_box_food_count`) VALUES (18563097, 11786612, 271432, 2600, 2600, 4.6, 100, 15.4, 124, 416, 1, 1, 100, 1, 0, '2025-02-26 17:20:44', '2025-02-26 17:20:44', NULL, 1, 0, 2160, 0, 0, 0, 2600, 100, 0);"
                db.Exec(sql)
                sql = "INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`, `activity_type`, `activity_id`, `res_profit`, `pref_id`, `price_markup_id`, `price_markup_price`, `refund_price`, `lunch_box_refund_price`, `lunch_box_food_count`) VALUES (18563098, 11786612, 271422, 2800, 2800, 4.6, 100, 15.4, 133, 447, 1, 1, 100, 1, 0, '2025-02-26 17:20:44', '2025-02-26 17:20:44', NULL, 1, 0, 2320, 0, 0, 0, 2800, 100, 0);"
                db.Exec(sql)
            }
            

        case 3://满减 
            orderId =11786613
            orderDetailId =18563099
            if deleteRefundRecord {
                sql := fmt.Sprintf("delete from t_order_today where id = %d",orderId)
                db.Exec(sql)
                sql = "INSERT INTO `mulazimpro`.`t_order_today` (`id`, `random_id`, `terminal_id`, `order_id`, `category_id`, `city_id`, `area_id`, `store_id`, `user_id`, `building_id`, `order_address`, `name`, `mobile`, `consume_type`, `pay_type`, `original_price`, `price`, `shipment`, `original_shipment`, `lunch_box_fee`, `full_reduction_amount`, `mp_profit`, `dealer_profit`, `res_profit`, `cash`, `coin`, `consume`, `description`, `timezone`, `booking_time`, `pay_time`, `print_time`, `printed_time`, `delivery_taked_time`, `delivery_start_time`, `delivery_end_time`, `real_shipment`, `deduction_fee`, `taked`, `shipper_id`, `sent_sms`, `delete_flag`, `refund_type`, `refund_chanel`, `refunded`, `is_douyin`, `send_notify`, `serial_number`, `shipper_complete_grant`, `is_luck_drawed`, `is_auto_refund`, `is_commented`, `distance`, `order_type`, `called`, `pay_channel`, `pay_platform`, `state`, `created_at`, `updated_at`, `deleted_at`, `cash_clear_state`, `cash_clear_time`, `cash_clear_channel`, `cash_clear_admin`, `seckill_state`, `market_type`, `is_auto_fished`, `self_take_number`, `delivery_type`, `json_info`, `actual_paid`, `order_price`, `total_discount_amount`, `price_markup_in_price`, `order_price_res`, `shipper_reward`) VALUES (11786613, 'hk0jducp', 8, 'lafingqing0000002502261727080046', 1, 1, 1, 7978, 1096059, 1979, 'يەنئەن يولى', 'روزى مەمەت', '18097690669', 3, 6, 12500, 12100, 0, 0, 500, NULL, 580, 1940, 10080, 0, 0, 0, '(چوكا قوشۇق 5 كىشلىك)', 8, '2025-02-26 18:21:05', '2025-02-26 17:27:16', '2025-02-26 17:27:08', '2025-02-26 17:27:43', NULL, NULL, NULL, NULL, NULL, 0, NULL, 1, 0, 0, 0, 0, NULL, 0, 4, 0, 0, 0, 0, 0.00, 1, 0, NULL, 1, 4, '2025-02-26 17:27:08', '2025-02-26 17:27:44', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, NULL, 1, NULL, 12600, 12600, 0, 0, 12600, 0);";
                db.Exec(sql)
                sql =fmt.Sprintf("delete from t_order_detail where order_id = %d",orderId)
                db.Exec(sql)
                sql = "INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`, `activity_type`, `activity_id`, `res_profit`, `pref_id`, `price_markup_id`, `price_markup_price`, `refund_price`, `lunch_box_refund_price`, `lunch_box_food_count`) VALUES (18563099, 11786613, 271420, 2200, 2200, 4.6, 100, 15.4, 212, 708, 2, 1, 100, 2, 0, '2025-02-26 17:27:08', '2025-02-26 17:27:08', NULL, 1, 0, 3680, 0, 0, 0, 2200, 100, 0);"
                db.Exec(sql)
                
                sql = "INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`, `activity_type`, `activity_id`, `res_profit`, `pref_id`, `price_markup_id`, `price_markup_price`, `refund_price`, `lunch_box_refund_price`, `lunch_box_food_count`) VALUES (18563100, 11786613, 271418, 2800, 2800, 4.6, 100, 15.4, 133, 447, 1, 1, 100, 1, 0, '2025-02-26 17:27:08', '2025-02-26 17:27:08', NULL, 1, 0, 2320, 0, 0, 0, 2800, 100, 0);"
                db.Exec(sql)
                sql = "INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`, `activity_type`, `activity_id`, `res_profit`, `pref_id`, `price_markup_id`, `price_markup_price`, `refund_price`, `lunch_box_refund_price`, `lunch_box_food_count`) VALUES (18563101, 11786613, 271416, 2300, 1900, 4.6, 100, 15.4, 92, 308, 1, 1, 100, 1, 0, '2025-02-26 17:27:08', '2025-02-26 17:27:08', NULL, 2, 284630, 1600, 284630, 0, 0, 1900, 100, 0);"
                db.Exec(sql)
                sql = "INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`, `activity_type`, `activity_id`, `res_profit`, `pref_id`, `price_markup_id`, `price_markup_price`, `refund_price`, `lunch_box_refund_price`, `lunch_box_food_count`) VALUES (18563102, 11786613, 271434, 3000, 3000, 4.6, 100, 15.4, 143, 477, 1, 1, 100, 1, 0, '2025-02-26 17:27:08', '2025-02-26 17:27:08', NULL, 1, 0, 2480, 0, 0, 0, 3000, 100, 0);"
                db.Exec(sql)
            }
        
    }
    //返回第一个美食的数量的一半，和该退款的金额 包括 餐盒的该退款的部分
    var details models.OrderDetail 
    db.Model(&models.OrderDetail{}).Where("id = ?",orderDetailId).Find(&details)
    refundCount :=details.Number-1
    lunchBoxCount :=float64(0)
    lunchBoxCount = float64(details.LunchBoxFoodCount)/float64(refundCount)
    if refundCount < 1 {
        refundCount = 1
        lunchBoxCount = float64(details.LunchBoxCount)
    }
    
    lunchBOxRefundPrice := float64(details.LunchBoxRefundPrice) * float64(lunchBoxCount)
    refundPrice :=float64(details.RefundPrice) * float64(refundCount) + lunchBOxRefundPrice
    var foods []resources.PartRefundFoodDetail
    foodOne :=resources.PartRefundFoodDetail{
        DetailId: details.ID,
        FoodId: details.StoreFoodsID,
        Count: int(refundCount),
        RefundPrice: int64(refundPrice),
    }
    foods=append(foods, foodOne)
    var partRefundLunchBoxDetails []resources.PartRefundLunchBoxDetails
    if lunchBoxCount > 0 {

        partRefundLunchBoxDetail :=resources.PartRefundLunchBoxDetails{
            DetailId: details.ID,
            LunchBoxId: details.LunchBoxID,
            Count: int(lunchBoxCount),
            RefundPrice: int64(lunchBOxRefundPrice),
            

        }
        partRefundLunchBoxDetails=append(partRefundLunchBoxDetails, partRefundLunchBoxDetail)
    }
    if deleteRefundRecord {
        sql :=fmt.Sprintf("delete from t_order_part_refund where order_id = %d",orderId)
        db.Exec(sql)
        sql =fmt.Sprintf("delete from t_order_part_refund_detail where order_id = %d",orderId)
        db.Exec(sql)
    }

    return orderId,foods,partRefundLunchBoxDetails,int(refundPrice)

}

func PartRefundOrderData(id int)  (int,[]resources.PartRefundFoodDetail,[]resources.PartRefundLunchBoxDetails,int){
    db :=tools.GetDB()
    var firstDetail models.OrderDetail
    db.Model(&models.OrderDetail{}).Where("order_id = ?",id).First(&firstDetail)
    orderDetailId :=firstDetail.ID
    //返回第一个美食的数量的一半，和该退款的金额 包括 餐盒的该退款的部分
    var details models.OrderDetail 
    db.Model(&models.OrderDetail{}).Where("id = ?",orderDetailId).Find(&details)
    refundCount :=details.Number-1
    lunchBoxCount :=float64(0)
    lunchBoxCount = float64(details.LunchBoxFoodCount)/float64(refundCount)
    if refundCount < 1 {
        refundCount = 1
        lunchBoxCount = float64(details.LunchBoxCount)
    }
    
    lunchBOxRefundPrice := float64(details.LunchBoxRefundPrice) * float64(lunchBoxCount)
    refundPrice :=float64(details.RefundPrice) * float64(refundCount) + lunchBOxRefundPrice
    var foods []resources.PartRefundFoodDetail
    foodOne :=resources.PartRefundFoodDetail{
        DetailId: details.ID,
        FoodId: details.StoreFoodsID,
        Count: int(refundCount),
        RefundPrice: int64(refundPrice),
    }
    foods=append(foods, foodOne)
    var partRefundLunchBoxDetails []resources.PartRefundLunchBoxDetails
    if lunchBoxCount > 0 { 
        

        partRefundLunchBoxDetail :=resources.PartRefundLunchBoxDetails{
            DetailId: details.ID,
            LunchBoxId: details.LunchBoxID,
            Count: int(lunchBoxCount),
            RefundPrice: int64(lunchBOxRefundPrice),
            

        }
        partRefundLunchBoxDetails=append(partRefundLunchBoxDetails, partRefundLunchBoxDetail)
    }

    return id,foods,partRefundLunchBoxDetails,int(refundPrice)
}


func PartRefundSendData(t *testing.T,id int,isPartRefund bool,from string,userId int) (int,bool){
    orderId,foods,luncBox,refundAmount :=PartRefundOrderData(id)
    tools.Logger.Info("orderId",orderId)
    params :=resources.PartRefund{
        OrderId:orderId,
        RefundType:tools.If(isPartRefund,2,1),//2.部分退款 1.全额退款
        RefundAmount:refundAmount,
        FoodDetails: foods,
        LunchBoxDetails: luncBox,
        From: from,
        ReasonId: 1,
        ReasonText: "测试退款",
        UserId: userId,
    }
   
    if params.RefundAmount == 0 {
        tools.Logger.Info("orderId:",orderId)
    }
    assert.NotEqual(t, params.RefundAmount, 0)
    //发送http 请求 
    bts,_:=json.Marshal(foods)
    var foodsMap []map[string]interface{}
    json.Unmarshal(bts, &foodsMap)
    bts,_=json.Marshal(luncBox)
    var lunchBoxMap []map[string]interface{}
    json.Unmarshal(bts, &lunchBoxMap)
    


    data :=map[string]interface{}{
        "order_id": orderId,
        "refund_type": params.RefundType,
        "refund_amount": params.RefundAmount,
        "reason_id": params.ReasonId,
        "from": params.From,
        "reason_text": params.ReasonText,
        "food_details":foodsMap,
        "lunch_box_details":lunchBoxMap,
        "user_id": params.UserId,
    }
    

    headers :=map[string]interface{}{
        "From":from,
        // "refund_test_user":2036,//天山区代理账号
        "refund_test_user":userId,//天山区代理账号
    }

    
    res :=helper.SendGinPostJSON("/ug/payment/v2/part-refund",data,"","",headers)
    tools.Logger.Info("res:",string(res.Body.Bytes()))
    assert.Equal(t, res.Code, 200)

    var successResponse responses.SuccessResponse
    json.Unmarshal(res.Body.Bytes(), &successResponse)
    //数据验证 查看退款数据的内容 
    return orderId,successResponse.Status == 200
    

}


func TestRefundOrderList(t *testing.T) {
    // 参数化测试数据
    testCases := []struct {
        page       string
        limit      string
        beginDate  string
        endDate    string
        resName    string
        kw         string
        expectCode int
    }{
        {"2", "10", time.Now().Format("2006-01-02"), time.Now().Format("2006-01-02"), "", "", 200},
        {"1", "20", time.Now().Format("2006-01-02"), time.Now().Format("2006-01-02"), "test_res", "test_kw", 200},
    }

    for _, tc := range testCases {
        params := url.Values{}
        params.Add("page", tc.page)
        params.Add("limit", tc.limit)
        params.Add("begin_date", tc.beginDate)
        params.Add("end_date", tc.endDate)
        params.Add("res_name", tc.resName)
        params.Add("kw", tc.kw)

        tools.Logger.Info("Testing with params:", params.Encode())
        res := helper.SendGinGet("/ug/cms/v2/part-refund/refund-order-list", params, "", "")

        // 增加日志信息
        // tools.Logger.Info("Response Body:", res.Body.String())

        assert.Equal(t, res.Code, 200)

       
    }
}

func TestRefundOrderDetail(t *testing.T){
    var partRefund []models.OrderPartRefund
    db :=tools.GetDB()
    db.Model(&models.OrderPartRefund{}).Order("created_at DESC").Limit(10).Find(&partRefund)
    for _,v :=range partRefund {
        params := url.Values{}
        params.Add("id",tools.ToString(v.ID))
        res := helper.SendGinGet("/ug/cms/v2/part-refund/refund-order-detail", params, "", "")
        assert.Equal(t, res.Code, 200)
    }
}