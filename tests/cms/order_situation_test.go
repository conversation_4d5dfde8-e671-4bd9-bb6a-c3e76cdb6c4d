package cms

// @Description: 配送员奖励设置
// @Author: Rixat
// @Time: 2023-10-31 02:29:10
// @receiver
// @param c *gin.Context

import (
	"fmt"
	"mulazim-api/tests/helper"
	"testing"

	"github.com/go-playground/assert/v2"
)

var SituationID = 5

// TestShipperRewardSettingAll
//
// @Description: 测试全部
// @Author: Rixat
// @Time: 2023-11-06 09:52:32
// @receiver
// @param c *gin.Context
func TestOrderSituationAll(t *testing.T) {
	TestShipperRewardSettingSave(t)   // 保存
	TestShipperRewardSettingDetail(t) // 详情
}

// TestShipperRewardSettingSave
//
// @Description: 保存
// @Author: Rixat
// @Time: 2023-11-02 10:51:58
// @receiver
// @param c *gin.Context
func TestOrderSituationList(t *testing.T) {
	params := map[string]interface{}{
		"page":  1,
		"limit": 10,
	}
	w := helper.SendGinGet("/ug/cms/v2/shipment/situation/list", helper.MapToUrlValues(params), "", "")
	fmt.Println("列表结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}

// TestShipperRewardSettingDetail
//
// @Description: 详情
// @Author: Rixat
// @Time: 2023-11-06 09:54:47
// @receiver
// @param c *gin.Context
func TestOrderSituationDetail(t *testing.T) {
	params := map[string]interface{}{
		"id": SituationID,
	}
	w := helper.SendGinGet("/ug/cms/v2/shipment/situation/detail", helper.MapToUrlValues(params), "", "")
	fmt.Println("列表结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}
