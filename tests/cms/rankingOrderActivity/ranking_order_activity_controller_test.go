package rankingorderactivity

import (
	"testing"

	"github.com/go-playground/assert/v2"

	"fmt"
	rankingorderactivity "mulazim-api/requests/cms/rankingOrderActivity"
	"mulazim-api/tests/helper"
)

// 测试创建活动
func TestCreateRankingOrderActivity(t *testing.T) {

	// 创建模拟数据
	rankingOrderActivityCreateRequest := rankingorderactivity.RankingOrderActivityCreateRequest{
		NameZh:                  "测试活动25",
		NameUg:                  "22سىناق پائالىيەت",
		AnnounceBeginTime:       "2023-01-01 00:00:00",
		StartTime:               "2023-06-02 00:00:00",
		EndTime:                 "2023-06-10 00:00:00",
		ResultShowEndTime:       4, // 天
		State:                   1,
		ShareCoverImages:      "https://example.com/image1.jpg,https://example.com/image1.jpg",
		ShareWinnerImageUg:      "https://example.com/image3.jpg",
		ShareWinnerImageZh:      "https://example.com/image4.jpg",
		AnnounceEntranceImageUg: "https://example.com/image5.jpg",
		AnnounceEntranceImageZh: "https://example.com/image6.jpg",
		AnnouncePageImageUg:     "https://example.com/image7.jpg",
		AnnouncePageImageZh:     "https://example.com/image8.jpg",
		RuleUg:                  "پائالىيەت قائىدىسى",
		RuleZh:                  "活动规则",
		PrizeList: []rankingorderactivity.RankingOrderActivityPrizeRequest{
			{
				Id:     1,
				Indexs: "1,2,3",
			},
			{
				Id:     2,
				Indexs: "4,5,6",
			},
		},
	}

	w := helper.SendGinPostWithJSON("/ug/cms/v2/ranking-order-activity/create", rankingOrderActivityCreateRequest, "", "")
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

    // 验证数据库插入

	rankingOrderActivityCreateRequest = rankingorderactivity.RankingOrderActivityCreateRequest{
		NameZh:                  "测试活动25",
		NameUg:                  "22سىناق پائالىيەت",
		AnnounceBeginTime:       "2023-01-01 00:00:00",
		StartTime:               "2023-06-02 00:00:00",
		EndTime:                 "2023-06-10 00:00:00",
		ResultShowEndTime:       4, // 天
		State:                   1,
		ShareCoverImages:      "https://example.com/image1.jpg,https://example.com/image1.jpg",
		ShareWinnerImageUg:      "https://example.com/image3.jpg",
		ShareWinnerImageZh:      "https://example.com/image4.jpg",
		AnnounceEntranceImageUg: "https://example.com/image5.jpg",
		AnnounceEntranceImageZh: "https://example.com/image6.jpg",
		AnnouncePageImageUg:     "https://example.com/image7.jpg",
		AnnouncePageImageZh:     "https://example.com/image8.jpg",
		RuleUg:                  "پائالىيەت قائىدىسى",
		RuleZh:                  "活动规则",
		PrizeList: []rankingorderactivity.RankingOrderActivityPrizeRequest{
			{
				Id:     1,
				Indexs: "1,2,3",
			},
			{
				Id:     2,
				Indexs: "4,5,6",
			},
		},
	}

	w = helper.SendGinPostWithJSON("/ug/cms/v2/ranking-order-activity/create", rankingOrderActivityCreateRequest, "", "")
	assert.Equal(t, -1000, helper.GetResCode(w.Body.String()))
	

}
// 测试更新活动
func TestUpdateRankingOrderActivity(t *testing.T) {
	// 创建模拟数据
	rankingOrderActivityUpdateRequest := rankingorderactivity.RankingOrderActivityUpdateRequest{
		ID:                      10,
		NameZh:                  "测试活动25",
		NameUg:                  "22سىناق پائالىيەت",
		AnnounceBeginTime:       "2023-01-01 00:00:00",
		StartTime:               "2023-06-02 00:00:00",
		EndTime:                 "2023-06-10 00:00:00",
		ResultShowEndTime:       4, // 天
		State:                   1,
		ShareCoverImages:        "https://example.com/image1.jpg,https://example.com/image1.jpg",
		ShareWinnerImageUg:      "https://example.com/image3.jpg",
		ShareWinnerImageZh:      "https://example.com/image4.jpg",
		AnnounceEntranceImageUg: "https://example.com/image5.jpg",
		AnnounceEntranceImageZh: "https://example.com/image6.jpg",
		AnnouncePageImageUg:     "https://example.com/image7.jpg",
		AnnouncePageImageZh:     "https://example.com/image8.jpg",
		RuleUg:                  "پائالىيەت قائىدىسى",
		RuleZh:                  "活动规则",
		PrizeList: []rankingorderactivity.RankingOrderActivityPrizeRequest{
			{
				Id:     1,
				Indexs: "1,2,3",
			},
			{
				Id:     2,
				Indexs: "4,5,6",
			},
		},
	}

	w := helper.SendGinPostWithJSON("/ug/cms/v2/ranking-order-activity/update", rankingOrderActivityUpdateRequest, "", "")
	fmt.Println("更新结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 测试重复更新相同时间段的活动（应该返回错误）
	rankingOrderActivityUpdateRequest = rankingorderactivity.RankingOrderActivityUpdateRequest{
		ID:                      10,
		NameZh:                  "测试活动25",
		NameUg:                  "22سىناق پائالىيەت",
		AnnounceBeginTime:       "2023-01-01 00:00:00",
		StartTime:               "2023-06-02 00:00:00",
		EndTime:                 "2023-06-10 00:00:00",
		ResultShowEndTime:       4, // 天
		State:                   1,
		ShareCoverImages:        "https://example.com/image1.jpg,https://example.com/image1.jpg",
		ShareWinnerImageUg:      "https://example.com/image3.jpg",
		ShareWinnerImageZh:      "https://example.com/image4.jpg",
		AnnounceEntranceImageUg: "https://example.com/image5.jpg",
		AnnounceEntranceImageZh: "https://example.com/image6.jpg",
		AnnouncePageImageUg:     "https://example.com/image7.jpg",
		AnnouncePageImageZh:     "https://example.com/image8.jpg",
		RuleUg:                  "پائالىيەت قائىدىسى",
		RuleZh:                  "活动规则",
		PrizeList: []rankingorderactivity.RankingOrderActivityPrizeRequest{
			{
				Id:     1,
				Indexs: "1,2,3",
			},
			{
				Id:     2,
				Indexs: "4,5,6",
			},
		},
	}

	w = helper.SendGinPostWithJSON("/ug/cms/v2/ranking-order-activity/update", rankingOrderActivityUpdateRequest, "", "")
	assert.Equal(t, -1000, helper.GetResCode(w.Body.String()))
}



// 测试修改活动状态
func TestUpdateRankingOrderActivityState(t *testing.T) {
	// 创建模拟数据
	params := struct {
		ID    int `json:"id"`
		State int `json:"state"`
	}{
		ID:    1,
		State: 1,
	}

	w := helper.SendGinPostWithJSON("/ug/cms/v2/ranking-order-activity/state", params, "", "")
	fmt.Println("更新状态结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 测试无效状态值
	params.State = 3 // 无效的状态值
	w = helper.SendGinPostWithJSON("/ug/cms/v2/ranking-order-activity/state", params, "", "")
	assert.Equal(t, -1000, helper.GetResCode(w.Body.String()))
}


// 测试删除活动
func TestDeleteRankingOrderActivity(t *testing.T) {
	// 创建模拟数据
	params := struct {
		ID int `json:"id"`
	}{
		ID: 1,
	}

	w := helper.SendGinPostWithJSON("/ug/cms/v2/ranking-order-activity/delete", params, "", "")
	fmt.Println("删除结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 测试删除不存在的活动
	params.ID = 999 // 不存在的ID
	w = helper.SendGinPostWithJSON("/ug/cms/v2/ranking-order-activity/delete", params, "", "")
	assert.Equal(t, -1000, helper.GetResCode(w.Body.String()))
}	