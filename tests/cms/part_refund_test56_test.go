package cms

import (
	"context"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/inits"
	"mulazim-api/models"
	"mulazim-api/tests/helper"
	"mulazim-api/tools"
	"net/url"
	"os/exec"
	"testing"
	"time"

	"github.com/go-playground/assert/v2"
	"github.com/golang-module/carbon/v2"
	// "github.com/golang-module/carbon/v2"
)

// 部分退款场景测试
func TestRefundScenarios(t *testing.T) {
    testCases := []struct {
        name          string
        role          string       // 操作角色: agent/merchant/user
        paymentMethod string       // 支付方式: cash/wechat
        orderType     string       // 订单类型: premium/regular

		option   []struct{
			refundType    string       // 退款类型: partial/full
			expectSuccess bool         // 预期是否成功
		}
    }{
		{
			name: "首次部分退款第二次全额退款%s-%s-%s",
			option: []struct{refundType string; expectSuccess bool}{
				{refundType: "partial", expectSuccess: true},
				{refundType: "full", expectSuccess: true},
			},
		},
		{
			name: "首次部分退款第二次部分退款%s-%s-%s",
			option: []struct{refundType string; expectSuccess bool}{
				{refundType: "partial", expectSuccess: true},
				{refundType: "partial", expectSuccess: false},
			},
		},
		{
			name: "首次全额退款第二次全额退款%s-%s-%s",
			option: []struct{refundType string; expectSuccess bool}{
				{refundType: "full", expectSuccess: true},
				{refundType: "full", expectSuccess: false},
			},
		},
		{
			name: "首次全额退款第二次部分退款%s-%s-%s",
			option: []struct{refundType string; expectSuccess bool}{
				{refundType: "full", expectSuccess: true},
				{refundType: "partial", expectSuccess: false},
			},
		},
    }
	strRole := []string{"agent","merchant",
	// "user"
}
	strPaymentMethod := []string{"cash","wechat"}
	strOrderType := []string{"premium","regular"}



	index := 0
	for _,role := range strRole {
		for _,paymentMethod := range strPaymentMethod {
			for _,orderType := range strOrderType {
				for _,tc := range testCases {
					tc.role = role
					tc.paymentMethod = paymentMethod
					tc.orderType = orderType
					if index >=4 {
					t.Run(fmt.Sprintf(tc.name,role,paymentMethod,orderType), func(t1 *testing.T) {
						order := createOrder(t1,tc.orderType,tc.paymentMethod,tc.role,index)
						time.Sleep(5*time.Second)
						payOrder(t1,tc.paymentMethod,order)
						OrderFix(t1,order.ID,"origin")

						from,userId :=getFromAndUserId(t1,order,tc.role)
						refundTypeFirst :=tc.option[0].refundType
						refundTypeSecond :=tc.option[1].refundType
						expectSuccessFirst :=tc.option[0].expectSuccess
						expectSuccessSecond :=tc.option[1].expectSuccess

						_,success :=PartRefundSendData(t1,int(order.ID), refundTypeFirst== "partial",from,userId)

						PartRefundOrderValidate(t1,int(order.ID),refundTypeFirst == "partial")


						assert.Equal(t1,success,expectSuccessFirst)	
						
						OrderFix(t1,order.ID,refundTypeFirst)
						time.Sleep(5*time.Second)
						_,success =PartRefundSendData(t1,int(order.ID),refundTypeSecond == "partial",from,userId)
						if expectSuccessSecond {
							PartRefundOrderValidate(t1,int(order.ID),refundTypeSecond == "partial")	
						}
						assert.Equal(t1,success,expectSuccessSecond)	
					})
						
					}
					index ++
				}
			}
		}
	}

}
func getFromAndUserId(t *testing.T,order models.TOrderToday,role string) (string,int){
	db :=tools.GetDB()
	from :="cms"
	userId :=2036
	switch(role){
	case "agent":
		from = "cms"
		var admin models.Admin
		db.Model(&models.Admin{}).Where("admin_area_id = ? and type = ? and state = ?",order.AreaId,3,1).First(&admin)
		userId =admin.ID
	case "merchant":
		var admin models.AdminForSelfSign
		db.Model(models.AdminForSelfSign{}).
		Joins("INNER JOIN b_admin_store ON t_admin.id = b_admin_store.admin_id ").
		Where("b_admin_store.store_id = ? ", order.StoreId).
		Where("state = 1").
		Where("type = 5 ").
		Where("t_admin.deleted_at IS NULL").Order("type desc").First(&admin)
		userId =admin.ID
		from = "api"
	case "user":
		userId =int(order.UserId)
		from = "mini"	
	}
	return from,userId
}
func payOrder(t *testing.T,paymentMethod string,order models.TOrderToday){
	db :=tools.GetDB()
	
	//修改订单状态 
	if paymentMethod == "cash" { //现金支付 
		db.Model(&models.OrderToday{}).Where("id = ?",order.ID).Updates(&map[string]interface{}{
			"consume_type":0,
			"pay_type":1,
			"pay_time":carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			"state":4,
			"serial_number":tools.GetOrderMaxSerialNumber(int(order.StoreId)),
		})		 

		
	}else {//微信支付 
		db.Model(&models.OrderToday{}).Where("id = ?",order.ID).Updates(&map[string]interface{}{
			"consume_type":1,
			"pay_type":5,
			"pay_time":carbon.Now(configs.AsiaShanghai).Format("Y-m-d H:i:s"),
			"pay_platform":1,
			"pay_channel":1,
			"state":3,
			"serial_number":tools.GetOrderMaxSerialNumber(int(order.StoreId)),
		})
		data := url.Values{}
		data.Add("id", fmt.Sprintf("%d", order.ID))
		data.Add("job_mode",fmt.Sprintf("%d", 2))
		response := helper.SendGinGet(configs.MyApp.ApiSmartUrl+"/ug/v1/printer/order/print", data, "", "")
		resStr := response.Body.String()
		tools.Logger.Info(resStr)
	}

}
func createOrder(t *testing.T,orderType string, paymentMethod string,role string,index int) models.TOrderToday{

	var order models.TOrderToday

	minijsProjectPath := inits.ConfigFilePath + "xls/minijs/"

	scriptName := "jianpeisong.spec.js"
	if orderType == "premium" {
		scriptName = "jiajia.spec.js"
	}
	tools.Logger.Info("创建订单:", orderType, ",", paymentMethod)
	commands :=[]string{"jest","-c", minijsProjectPath+"jest.config.js", minijsProjectPath+scriptName, "--", paymentMethod}
	tools.Logger.Info("执行命令:", commands)

	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "jest", "-c", minijsProjectPath+"jest.config.js", minijsProjectPath+scriptName, "--", paymentMethod)
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Println("Error:", err)
		fmt.Println("Error Output:", string(output))
		return models.TOrderToday{}
	}

	fmt.Println("success Output:", string(output))
	db :=tools.GetDB()
	
	db.Model(&models.TOrderToday{}).Where("terminal_id = ? ",8).Order("id desc").Limit(1).Find(&order)
	
	
	
	return order
}


func TestPartRefundFixTest(t *testing.T){
	
	orderId :=210

	for i := 170; i < 220; i++ {
		orderId =i
		ok :=OrderFix(t,int64(orderId),"partial")	
		if ok {
			tools.Logger.Info("订单:",orderId,"对账成功")
		}else{
			tools.Logger.Info("订单对行失败:",orderId)

		}
	}	
	// PartRefundRestoreData(orderId)
	// PartRefundSendData(t,int(orderId), true,"cms",2036)
	// OrderFix(t,int64(orderId),"original")
	// OrderFix(t,int64(orderId),"partial")

}


func PartRefundRestoreData(orderId int) {
	// orderId :=11787261
	db :=tools.GetDB()
	var partRefundLog models.OrderPartRefund
	//查找部分退款记录 
	db.Model(&models.OrderPartRefund{}).Where("order_id = ? and part_refund_type = ?",orderId,2).Find(&partRefundLog)
	//还原订单数据
	db.Model(&models.OrderToday{}).Where("id = ?",orderId).Updates(&map[string]interface{}{
		"res_profit":partRefundLog.ResProfit,
		"dealer_profit":partRefundLog.DealerProfit,
		"mp_profit":partRefundLog.MpProfit,
		"actual_paid":partRefundLog.ActualPaid,
		"order_price":partRefundLog.OrderPrice,
		"order_price_res":partRefundLog.OrderPriceRes,
		"lunch_box_fee":partRefundLog.LunchBoxFee,
		"refunded":0,
	})

	//删除现有的detail数据
	db.Unscoped().Delete(&models.OrderDetail{},"order_id = ?",orderId)
	
	var orderDetails []models.OrderDetail

	var orderRefundDetails []models.OrderPartRefundDetail
	//找出原始记录 
	db.Model(&models.OrderPartRefundDetail{}).Where("part_refund_id = ? and type = ?",partRefundLog.ID,models.PartRefundDetailTypeOriginal).Find(&orderRefundDetails)
	
	for _, v := range orderRefundDetails {
		detail :=models.OrderDetail{				
			OrderID          :int(v.OrderId)          ,
			ActivityType     :int(v.ActivityType)     ,
			ActivityId       :int(v.ActivityId)       ,
			StoreFoodsID     :int(v.StoreFoodsId)     ,
			OriginalPrice    :uint(v.OriginalPrice)    ,
			Price            :uint(v.Price)            ,
			MpPercent        :float64(v.MpPercent)     ,
			DiscountPercent  :uint(v.DiscountPercent)  ,
			DealerPercent    :float64(v.DealerPercent) ,
			MpProfit         :uint(v.MpProfit)         ,
			DealerProfit     :int(v.DealerProfit)     ,
			ResProfit        :int(v.ResProfit)        ,
			Number           :uint(v.Number)           ,
			LunchBoxID       :int(v.LunchBoxId)       ,
			LunchBoxFee      :int(v.LunchBoxFee)      ,
			LunchBoxCount    :int(v.LunchBoxCount)    ,
			SeckillID        :int(v.SeckillId)        ,
			PrefId           :int(v.PrefId)           ,
			PriceMarkupId    :int(v.PriceMarkupId)    ,
			PriceMarkupPrice :int(v.PriceMarkupPrice) ,
			LunchBoxFoodCount: int(v.LunchBoxFoodCount),
			RefundPrice: int(v.RefundPrice),
			LunchBoxRefundPrice: v.LunchBoxRefundPrice,
			FoodType: v.FoodType,
			SpecID: v.SpecID,
			LunchBoxGroupIndex: v.LunchBoxGroupIndex,
		}
		orderDetails = append(orderDetails,detail)
	}
	if len (orderDetails) > 0 {
		err :=db.Create(&orderDetails).Error
		if err != nil {
			tools.Logger.Error("数据恢复失败",err)
			return 
		}
	}
	//删除部分退款记录
	db.Model(&models.OrderPartRefundDetail{}).Where("order_id = ? ",orderId).Unscoped().Delete(&models.OrderPartRefundDetail{})
	db.Model(&models.OrderPartRefund{}).Where("order_id = ? ",orderId).Unscoped().Delete(&models.OrderPartRefund{})

}