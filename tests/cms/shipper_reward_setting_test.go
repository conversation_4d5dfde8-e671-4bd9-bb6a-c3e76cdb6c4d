package cms

// @Description: 配送员奖励设置
// @Author: Rixat
// @Time: 2023-10-31 02:29:10
// @receiver
// @param c *gin.Context

import (
	"fmt"
	models "mulazim-api/models/shipment"
	"mulazim-api/tests/helper"
	"mulazim-api/tools"
	"testing"

	"github.com/go-playground/assert/v2"
)

var RewardSettingID = 21

// TestShipperRewardSettingAll
//
// @Description: 测试全部
// @Author: Rixat
// @Time: 2023-11-06 09:52:32
// @receiver
// @param c *gin.Context
func TestShipperRewardAll(t *testing.T) {
	TestShipperRewardSettingSave(t)   // 保存
	TestShipperRewardSettingDetail(t) // 详情
}

// TestShipperRewardSettingSave
//
// @Description: 保存
// @Author: Rixat
// @Time: 2023-11-02 10:51:58
// @receiver
// @param c *gin.Context
func TestShipperRewardSettingSave(t *testing.T) {
	// 基本信息
	params := map[string]interface{}{
		"id":                RewardSettingID,
		"area_id":           1,
		"city_id":           1,
		"month_rest_day":    30,
		"daily_order_limit": 30,
		"reward_amount":     150,
		"state":             1,
	}
	// 发送请求
	w := helper.SendGinPost("/ug/cms/v2/shipment/reward-setting/save", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var rewardSetting models.ShipperRewardSetting
	delete(params, "id")
	tools.Db.Model(rewardSetting).Where(params).First(&rewardSetting)
	assert.Equal(t, true, rewardSetting.ID > 0)
	RewardSettingID = rewardSetting.ID
}

// TestShipperRewardSettingDetail
//
// @Description: 详情
// @Author: Rixat
// @Time: 2023-11-06 09:54:47
// @receiver
// @param c *gin.Context
func TestShipperRewardSettingDetail(t *testing.T) {
	params := map[string]interface{}{
		"id": RewardSettingID,
	}
	w := helper.SendGinGet("/ug/cms/v2/shipment/reward-setting/detail", helper.MapToUrlValues(params), "", "")
	fmt.Println("列表结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}

func TestShipperRewardSettingChangeState(t *testing.T) {
	// 基本信息
	params := map[string]interface{}{
		"id":    RewardSettingID,
		"state": 1,
	}
	// 发送请求
	w := helper.SendGinPost("/ug/cms/v2/shipment/reward-setting/change-state", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}

func TestShipperRewardSettingList(t *testing.T) {
	params := map[string]interface{}{
		"page":  1,
		"limit": 10,
	}
	w := helper.SendGinGet("/ug/cms/v2/shipment/reward-setting/list", helper.MapToUrlValues(params), "", "")
	fmt.Println("列表结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}
