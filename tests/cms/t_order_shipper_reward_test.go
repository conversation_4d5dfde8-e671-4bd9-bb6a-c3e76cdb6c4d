package cms

import (
	"fmt"
	"mulazim-api/models"
	"mulazim-api/tools"
	"testing"

	"github.com/golang-module/carbon/v2"
)


func TestShipperReward(t *testing.T)  {
	var daliStatistic []models.AdvertMaterialShipperUserMonthStatistic
	tools.Db.Model(daliStatistic).Where("total_order_tips_fee>0").Preload("AdvertMaterialShipperUser").Find(&daliStatistic)
	for _, item := range daliStatistic {
		userAddTime := item.AdvertMaterialShipperUser.CreatedAt
		orderPercent := tools.ToFloat64(item.TotalOrderTipsFee) / tools.ToFloat64(item.TotalOrderPrice)
		startDay,endDay  := tools.GetMonthStartAndEndWithParam(carbon.Parse(item.Month).Format("Y-m-d"), "Y-m-d")
		startDayCarbon,endDayCarbon := carbon.Parse(startDay+" 00:00:00").AddDay().Format("Y-m-d H:i:s"),carbon.Parse(endDay+"  23:59:59").AddDay().Format("Y-m-d H:i:s")
		// percent
		var orders []models.Order
		tools.Db.Model(&models.Order{}).Where("archive_date between ? and ?", startDayCarbon,endDayCarbon).Where("user_id=? and state = 7", item.UserId).Where("archive_date>?",userAddTime).Debug().Find(&orders)
		for _,order := range orders {
			shipperReward := tools.ToInt(tools.ToFloat64(order.OrderPrice) * orderPercent)
			tools.Db.Model(&order).Update("shipper_reward",shipperReward)
		}
		fmt.Println(item,orderPercent)
	}
	print(daliStatistic)
}