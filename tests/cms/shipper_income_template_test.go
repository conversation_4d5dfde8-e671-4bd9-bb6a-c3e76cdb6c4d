package cms

// @Description: 配送费模板管理单元测试
// @Author: Rixat
// @Time: 2023-10-31 02:29:10
// @receiver
// @param c *gin.Context

import (
	"fmt"
	models "mulazim-api/models/shipment"
	
	baseModels "mulazim-api/models"

	"mulazim-api/tests/helper"
	"mulazim-api/tools"
	"testing"
	shipperService "mulazim-api/services/shipper"
	"github.com/go-playground/assert/v2"
	"github.com/golang-module/carbon/v2"
)

var ID = 119
var nameUg = "sinak"
var nameZh = "测试"

// TestAll
//
// @Description: 测试全部
// @Author: Rixat
// @Time: 2023-11-02 10:52:50
// @receiver
// @param c *gin.Context
func TestAll(t *testing.T) {
	nameUg = nameUg + "_" + carbon.Now().Format("mdHis")
	nameZh = nameZh + "_" + carbon.Now().Format("mdHis")
	// 创建基本信息（规则类型：固定配送费）  1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
	TestShipperIncomeTemplateCreateBaseRuleType1(t)
	// TestShipperIncomeTemplateCreateBaseRuleType2(t)
	// TestShipperIncomeTemplateCreateBaseRuleType3(t)
	// TestShipperIncomeTemplateCreateBaseRuleType4(t)
	// 基本配送信息
	TestShipperIncomeTemplateUpdateBaseFix(t)      // 更新基本信息（规则类型：固定配送费）
	TestShipperIncomeTemplateUpdateBaseDistance(t) // 更新基本信息（规则类型：按距离计算）
	TestShipperIncomeTemplateUpdateBaseCar(t)      // 更新基本信息（规则类型：按出租车）
	TestShipperIncomeTemplateUpdateBaseOrder(t)    // 更新基本信息（规则类型：按订单数量）
	// 更新特殊配送费
	TestShipperIncomeTemplateUpdateSpecial(t)
	// 更新扣款配送费信息
	TestShipperIncomeTemplateUpdateDeductionFix(t)  // 创建投诉/差评（迟到扣款类型：固定）
	TestShipperIncomeTemplateUpdateDeductionTime(t) // 创建投诉/差评（迟到扣款类型：迟到时间）
	// 配送费模板列表
	TestShipperIncomeTemplateList(t) // 更新投诉/差
	// 配送费模板详情
	TestShipperIncomeTemplateDetail(t) // 更新投诉/差

	// 配送费模板删除
	TestShipperIncomeTemplateDelete(t)
	// 结束（为了打印输出内容）
	TestEnd(t) // 更新投诉/差
}

// TestShipperIncomeTemplateCreate
//
// @Description: 测试创建基本信息
// @Author: Rixat
// @Time: 2023-11-02 10:51:58
// @receiver
// @param c *gin.Context
func TestShipperIncomeTemplateCreateBaseRuleType1(t *testing.T) {
	// 基本信息
	params := map[string]interface{}{
		"name_ug":     nameUg,
		"name_zh":     nameZh,
		"base_salary": 120000,
		"rule_type":   1, // 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"rule_content": tools.MapArrayToString([]map[string]interface{}{
			{
				"fix_shipment_fee": 200,
			},
		}),
	}
	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipment/template/create", data, "", "",nil)
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var shipmentTemplate models.ShipperIncomeTemplate
	db := tools.Db
	delete(params, "rule_content")
	db.Model(shipmentTemplate).Where(params).First(&shipmentTemplate)
	assert.Equal(t, true, shipmentTemplate.ID > 0)
	ID = shipmentTemplate.ID
}
func TestShipperIncomeTemplateCreateBaseRuleType2(t *testing.T) {
	// 基本信息
	params := map[string]interface{}{
		"name_ug":     nameUg,
		"name_zh":     nameZh,
		"base_salary": 120000,
		"rule_type":   2, // 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"rule_content": tools.MapArrayToString([]map[string]interface{}{
			{
				"distance":     1000,
				"shipment_fee": 300,
			},
			{
				"distance":     2000,
				"shipment_fee": 400,
			},
			{
				"distance":     3000,
				"shipment_fee": 500,
			},
			{
				"distance":     4000,
				"shipment_fee": 600,
			},
			{
				"distance":     5000,
				"shipment_fee": 700,
			},
			{
				"distance":     9999000,
				"shipment_fee": 800,
			},
		}),
	}
	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipment/template/create", data, "", "",nil)
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var shipmentTemplate models.ShipperIncomeTemplate
	db := tools.Db
	delete(params, "rule_content")
	db.Model(shipmentTemplate).Where(params).First(&shipmentTemplate)
	assert.Equal(t, true, shipmentTemplate.ID > 0)
	ID = shipmentTemplate.ID
}
func TestShipperIncomeTemplateCreateBaseRuleType3(t *testing.T) {
	// 基本信息
	params := map[string]interface{}{
		"name_ug":     nameUg,
		"name_zh":     nameZh,
		"base_salary": 120000,
		"rule_type":   3, // 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"rule_content": tools.MapArrayToString([]map[string]interface{}{
			{
				"distance":            4000,
				"fixed_start_fee":     300,
				"price_per_kilometer": 400,
			},
		}),
	}
	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipment/template/create", data, "", "",nil)
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var shipmentTemplate models.ShipperIncomeTemplate
	db := tools.Db
	delete(params, "rule_content")
	db.Model(shipmentTemplate).Where(params).First(&shipmentTemplate)
	assert.Equal(t, true, shipmentTemplate.ID > 0)
	ID = shipmentTemplate.ID
}

func TestShipperIncomeTemplateCreateBaseRuleType4(t *testing.T) {
	// 基本信息
	params := map[string]interface{}{
		"name_ug":               nameUg,
		"name_zh":               nameZh,
		"base_salary":           120000,
		"rule_type":             4, // 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"rule_order_count_type": 1, // 按订单数量计算配送费类型：1:全部订单，2:超过部分
		"rule_content": tools.MapArrayToString([]map[string]interface{}{
			{
				"start_order_count": 0,
				"shipment_fee":      300,
			},
			{
				"start_order_count": 100,
				"shipment_fee":      400,
			},
			{
				"start_order_count": 300,
				"shipment_fee":      500,
			},
			{
				"start_order_count": 400,
				"shipment_fee":      600,
			},
		}),
	}
	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipment/template/create", data, "", "",nil)
	fmt.Println("创建结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var shipmentTemplate models.ShipperIncomeTemplate
	db := tools.Db
	delete(params, "rule_content")
	delete(params, "type")
	db.Model(shipmentTemplate).Where(params).First(&shipmentTemplate)
	assert.Equal(t, true, shipmentTemplate.ID > 0)
	ID = shipmentTemplate.ID
}

func TestShipperIncomeTemplateUpdateBaseFix(t *testing.T) {
	// 基本信息
	params := map[string]interface{}{
		"id":          ID, // 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"type":        1,
		"name_ug":     nameUg,
		"name_zh":     nameZh,
		"base_salary": 120000,
		"rule_type":   1, // 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"rule_content": tools.MapArrayToString([]map[string]interface{}{
			{"fix_shipment_fee": 100},
		}),
	}
	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipment/template/update", data, "", "",nil)
	fmt.Println("更新基本信息(固定配送费):", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var shipmentTemplate models.ShipperIncomeTemplate
	db := tools.Db
	delete(params, "rule_content")
	delete(params, "type")
	db.Model(shipmentTemplate).Where(params).First(&shipmentTemplate)
	assert.Equal(t, true, shipmentTemplate.ID > 0)
	ID = shipmentTemplate.ID
}
func TestShipperIncomeTemplateUpdateBaseDistance(t *testing.T) {
	// 基本信息
	params := map[string]interface{}{
		"id":          ID, // 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"type":        1,
		"name_ug":     nameUg,
		"name_zh":     nameZh,
		"base_salary": 120000,
		"rule_type":   2, // 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"rule_content": tools.MapArrayToString([]map[string]interface{}{
			{"shipment_fee": 400, "distance": 4000}, {"shipment_fee": 700, "distance": 6000},
		}),
	}
	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipment/template/update", data, "", "",nil)
	fmt.Println("更新基本信息(按距离):", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
	// 验证是否创建成功
	var shipmentTemplate models.ShipperIncomeTemplate
	db := tools.Db
	delete(params, "rule_content")
	delete(params, "type")
	db.Model(shipmentTemplate).Where(params).First(&shipmentTemplate)
	assert.Equal(t, true, shipmentTemplate.ID > 0)
	ID = shipmentTemplate.ID
}
func TestShipperIncomeTemplateUpdateBaseCar(t *testing.T) {
	// 基本信息
	params := map[string]interface{}{
		"id":          ID, // 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"type":        1,
		"name_ug":     nameUg,
		"name_zh":     nameZh,
		"base_salary": 120000,
		"rule_type":   3, // 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"rule_content": tools.MapArrayToString([]map[string]interface{}{
			{"fixed_start_fee": 100, "distance": 1000, "price_per_kilometer": 100}, {"fixed_start_fee": 700, "distance": 6000, "price_per_kilometer": 300},
		}),
	}
	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipment/template/update", data, "", "",nil)
	fmt.Println("更新基本信息(按出租车):", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var shipmentTemplate models.ShipperIncomeTemplate
	db := tools.Db
	delete(params, "rule_content")
	delete(params, "type")
	db.Model(shipmentTemplate).Where(params).First(&shipmentTemplate)
	assert.Equal(t, true, shipmentTemplate.ID > 0)
	ID = shipmentTemplate.ID
}
func TestShipperIncomeTemplateUpdateBaseOrder(t *testing.T) {
	// 基本信息
	params := map[string]interface{}{
		"id":                    ID, // 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"type":                  1,
		"name_ug":               nameUg,
		"name_zh":               nameZh,
		"base_salary":           120000,
		"rule_type":             4, // 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"rule_order_count_type": 1,
		"rule_content": tools.MapArrayToString([]map[string]interface{}{
			{
				"start_order_count": 0,
				"shipment_fee":      400,
			},
			{
				"start_order_count": 100,
				"shipment_fee":      500,
			},
			{
				"start_order_count": 300,
				"shipment_fee":      600,
			},
			{
				"start_order_count": 400,
				"shipment_fee":      700,
			},
		}),
	}
	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipment/template/update", data, "", "",nil)
	fmt.Println("更新基本信息(按订单数量):", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var shipmentTemplate models.ShipperIncomeTemplate
	db := tools.Db
	delete(params, "rule_content")
	delete(params, "type")
	db.Model(shipmentTemplate).Where(params).First(&shipmentTemplate)
	assert.Equal(t, true, shipmentTemplate.ID > 0)
	ID = shipmentTemplate.ID
}

func TestShipperIncomeTemplateUpdateSpecial(t *testing.T) {
	// 基本信息
	distance := []map[string]interface{}{
		{"distance": 2000, "shipment_fee": 300},
		{"distance": 3000, "shipment_fee": 400},
		{"distance": 4000, "shipment_fee": 500},
		{"distance": 5000, "shipment_fee": 600},
		{"distance": 9999000, "shipment_fee": 700},
	}
	distance2 := []map[string]interface{}{
		{"distance": 2000, "shipment_fee": 300},
		{"distance": 3000, "shipment_fee": 400},
		{"distance": 4000, "shipment_fee": 500},
		{"distance": 5000, "shipment_fee": 600},
		{"distance": 9999000, "shipment_fee": 700},
	}
	content := []map[string]interface{}{
		{"start_time": "00:00", "end_time": "04:00", "distance_step": distance},
		{"start_time": "04:00", "end_time": "08:00", "distance_step": distance2},
	}
	params := map[string]interface{}{
		"id":                       ID,
		"type":                     2, // 1:固定配送费,2:按距离计算,3:按出租车,4:按订单数量
		"special_shipment_state":   1,
		"special_shipment_content": tools.MapArrayToString(content), //json stringify
	}
	// 发送请求
	data := helper.MapToUrlValues(params)

	w := helper.SendGinPost("/ug/cms/v2/shipment/template/update", data, "", "",nil)
	fmt.Println("更新特殊配送费:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var shipmentTemplate models.ShipperIncomeTemplate
	db := tools.Db
	delete(params, "special_shipment_content")
	delete(params, "type")
	db.Model(shipmentTemplate).Where(params).First(&shipmentTemplate)
	assert.Equal(t, true, shipmentTemplate.ID > 0)
	ID = shipmentTemplate.ID
}

func TestShipperIncomeTemplateUpdateDeductionFix(t *testing.T) {
	// 基本信息
	params := map[string]interface{}{
		"id":                     ID,
		"type":                   3,
		"comment_deduction_fee":  1200,
		"complain_deduction_fee": 1200,
		"late_deduction_type":    1,
		"late_deduction_content": tools.MapArrayToString([]map[string]interface{}{
			{"deduction_fee": 200},
		}),
	}
	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipment/template/update", data, "", "",nil)
	fmt.Println("更新扣款结果(固定):", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var shipmentTemplate models.ShipperIncomeTemplate
	db := tools.Db
	delete(params, "late_deduction_content")
	delete(params, "type")
	db.Model(shipmentTemplate).Where(params).First(&shipmentTemplate)
	assert.Equal(t, true, shipmentTemplate.ID > 0)
	ID = shipmentTemplate.ID
}
func TestShipperIncomeTemplateUpdateDeductionTime(t *testing.T) {
	// 基本信息
	params := map[string]interface{}{
		"id":                     ID,
		"type":                   3,
		"comment_deduction_fee":  1200,
		"complain_deduction_fee": 1200,
		"late_deduction_type":    2,
		"late_deduction_content": tools.MapArrayToString([]map[string]interface{}{
			{"min_minute": 2, "max_minute": 3, "deduction_fee": 200},
		}),
	}
	// 发送请求
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipment/template/update", data, "", "",nil)
	fmt.Println("更新扣款结果(迟到时间):", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var shipmentTemplate models.ShipperIncomeTemplate
	db := tools.Db
	delete(params, "late_deduction_content")
	delete(params, "type")
	db.Model(shipmentTemplate).Where(params).First(&shipmentTemplate)
	assert.Equal(t, true, shipmentTemplate.ID > 0)
	ID = shipmentTemplate.ID
}

// TestShipperIncomeTemplateDelete
//
// @Description: 删除模板
// @Author: Rixat
// @Time: 2023-11-02 10:52:26
// @receiver
// @param c *gin.Context
func TestShipperIncomeTemplateDelete(t *testing.T) {
	// 发送请求
	params := map[string]interface{}{
		"id": 63,
	}
	data := helper.MapToUrlValues(params)
	w := helper.SendGinPost("/ug/cms/v2/shipment/template/delete", data, "", "",nil)
	fmt.Println("删除结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
	// 验证删除结果
	var count int64
	tools.Db.Model(models.ShipperIncomeTemplate{}).Where(params).Count(&count)
	assert.Equal(t, true, count == 0)
}

// TestShipperIncomeTemplateDetail
//
// @Description: 配送费模板详情
// @Author: Rixat
// @Time: 2023-11-03 10:30:12
// @receiver
// @param c *gin.Context
func TestShipperIncomeTemplateDetail(t *testing.T) {
	params := map[string]interface{}{
		"id": ID,
	}
	data := helper.MapToUrlValues(params)
	w := helper.SendGinGet("/ug/cms/v2/shipment/template/detail", data, "", "")
	fmt.Println("详情结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}

// TestShipperIncomeTemplateList
//
// @Description: 配送费列表
// @Author: Rixat
// @Time: 2023-11-03 10:32:21
// @receiver
// @param c *gin.Context
func TestShipperIncomeTemplateList(t *testing.T) {
	params := map[string]interface{}{
		"page":  1,
		"limit": 10,
		"kw":    "20756",
	}
	data := helper.MapToUrlValues(params)
	w := helper.SendGinGet("/ug/cms/v2/shipment/template/list", data, "", "")
	fmt.Println("列表结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}

func TestEnd(t *testing.T) {
	fmt.Println("=========================================测试结束==============================================")
	assert.Equal(t, true, false)
}

func RefundTest() {

	// http://lakala.mp-rank.almas.biz
	// /test2/refund
}
func TestSeckillMarketINcomeTest(t *testing.T){
	var incomeTemplate models.ShipperIncomeTemplate
	var inc models.ShipperIncome
	var order baseModels.Order
	var late baseModels.LateOrders
	db :=tools.GetDB()
	orderId := 14858904
	db.Model(&models.ShipperIncomeTemplate{}).Where("id = ?", 274).Find(&incomeTemplate)
	db.Model(&baseModels.Order{}).Where("id =?",orderId).Find(&order)
	db.Model(&baseModels.LateOrders{}).Where("order_id =?",orderId).Find(&late)
	db.Model(&models.ShipperIncome{}).Where("order_id = ? and `type`=?",orderId,9).Find(&inc)

	lateFeeIncome := shipperService.NewShipperFeeService().CalculateLateOrderFee(incomeTemplate,order,0)
	lateFee := shipperService.NewShipperFeeService().CheckLateOrderFee(incomeTemplate,order,late.LateMinute)
	
	tools.Logger.Info("归档时的计算费用",lateFeeIncome,",对账时的费用",lateFee)
	if lateFee != 0 {
		lateFee = lateFee * -1
		if inc.Amount != lateFee { //迟到扣款的费用金额不正确
			tools.Logger.Info("迟到扣款的费用金额不正确",map[string]interface{}{
				"income_id": inc.ID,
				"msg":       "迟到扣款的费用金额不正确",
			})
		}
	}
}