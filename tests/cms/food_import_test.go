package cms

import (
	// "context"
	// "fmt"
	// "mulazim-api/configs"
	// "mulazim-api/jobs"
	// "mulazim-api/models"
	// "mulazim-api/tools"
	// "strings"
	
	"mulazim-api/lang"
	"mulazim-api/services"
	"mulazim-api/tools"

	"testing"

	// "github.com/go-playground/assert/v2"
	// "time"
	// "github.com/go-playground/assert/v2"
	// "github.com/golang-module/carbon/v2"
)

func TestFoodImport(t *testing.T) {

	tm1,err:=parseTime("09:00:00","H:i:s")
	tools.Logger.Info(tm1,err)
	tm1,err=parseTime("9:00:00","H:i:s")
	tools.Logger.Info(tm1,err)
	tm1,err=parseTime("0:00:00","H:i:s")
	tools.Logger.Info(tm1,err)
	tm1,err=parseTime("0:00","H:i:s")
	tools.Logger.Info(tm1,err)
	tm1,err=parseTime("00:00","H:i:s")
	tools.Logger.Info(tm1,err)
	
 	service :=services.GetRestaurantFoodsService("ug")   
	adminId :=2046
	resId :=14244
	insert :=false
	// filePath :="C:/Users/<USER>/Downloads/food-import/foods-import.zip"
	filePath :="C:/Users/<USER>/Downloads/food-import/foods-import.xlsx"
	
	// lg := langUtil.(lang.LangUtil)
	//  rs,err:=service.MassImportFoodProcess("",adminId,resId,filePath,insert)
	//  tools.Logger.Info(rs,err)
	//  assert.Equal(t,err,nil)
	// //  assert.Equal(t,rs["success"],true)
	 insert = false
	//  key :=tools.ToString(rs["file_key"])
	// //  rs["data"]
	key :=""
	var lg lang.LangUtil
	 rs,err :=service.MassImportFoodProcess(lg,key,adminId,resId,filePath,insert)
	 tools.Logger.Info(rs,err)
	//  assert.Equal(t,err,nil)
	//  assert.Equal(t,rs["success"],true)




}


func parseTime(trimValue string,returnFormat string) (string,error){
	// 尝试解析 "H:i:s" 格式（例如：14:30:00）
	return tools.ParseTimeByFormat(trimValue,"H:i:s","H:i",returnFormat)
}