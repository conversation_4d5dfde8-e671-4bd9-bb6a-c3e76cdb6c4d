package cms

// @Description: 考勤管理
// @Author: Rixat
// @Time: 2023-10-31 02:29:10
// @receiver
// @param c *gin.Context

import (
	"fmt"
	models "mulazim-api/models"
	"mulazim-api/tests/helper"
	"mulazim-api/tools"
	"testing"

	"github.com/go-playground/assert/v2"
	"github.com/golang-module/carbon/v2"
)

var AttendanceLeaveID = 6
var AttendanceAccidentID = 6
var AttendanceType = 4        // 1 上班  2 下班  3 休息  4 请假  5 事故   6 管理员暂停配送员配送  7 管理员开启配送员配送
var AttendanceReviewState = 2 // 1:待审核,2:审核通过,3:审核不通过,4:撤销
var StateType = 2             // 1:打卡,2:请假,3:事故

// TestSpecialWeatherAll
//
// @Description: 测试全部
// @Author: Rixat
// @Time: 2023-11-06 09:52:32
// @receiver
// @param c *gin.Context
func TestShipperAttendanceAll(t *testing.T) {

	// 创建
	AttendanceType = 4
	TestShipperAttendanceCreate(t) // 创建(请假)
	AttendanceType = 5
	TestShipperAttendanceCreate(t) // 创建(事故)

	// 审核
	AttendanceType = 4
	AttendanceReviewState = 2
	TestAttendanceReviewState(t) // 请假审核（请假）
	AttendanceType = 5
	AttendanceReviewState = 3
	TestAttendanceReviewState(t) // 事故审核拒绝

	// 详情
	AttendanceType = 4
	TestShipperAttendanceDetail(t) // 详情请假
	AttendanceType = 5
	TestShipperAttendanceDetail(t) // 详情事故

	// 列表
	StateType = 1
	TestShipperAttendanceList(t) // 列表打卡
	StateType = 2
	TestShipperAttendanceList(t) // 列表请假
	StateType = 3
	TestShipperAttendanceList(t) // 列表事故

	// 结束（为了打印输出内容）
	TestEnd(t)
}

// TestShipperAttendanceCreate
//
// @Description: 创建考勤（请假，事故）
// @Author: Rixat
// @Time: 2023-11-02 10:51:58
// @receiver
// @param c *gin.Context
func TestShipperAttendanceCreate(t *testing.T) {
	Remark := "备注：" + carbon.Now().Format("YmdHis") + tools.ToString(AttendanceType)
	// 基本信息
	var params map[string]interface{}
	if AttendanceType == 4 { // 请假
		params = map[string]interface{}{
			"shipper_id": 4313,
			"leave_type": 1, // 1:事假 2:病假 3:婚嫁 4:产假/陪产假
			"start_time": "2023-10-10 21:00:00",
			"end_time":   "2023-10-11 21:00:00",
			"remark":     Remark,
			"state_type": 1, // 1.请假，2:事故
			"images":     "/uploads/1111.png",
		}
	}
	if AttendanceType == 5 { // 事故
		params = map[string]interface{}{
			"shipper_id": 4313,
			"start_time": "2023-10-10 21:00:00",
			"remark":     Remark,
			"state_type": 2, // 1.请假，2:事故
			"images":     "/uploads/1111.png",
		}
	}
	// 发送请求
	w := helper.SendGinPost("/ug/cms/v2/shipment/attendance/create", helper.MapToUrlValues(params), "", "",nil)
	if AttendanceType == 4 {
		fmt.Println("创建请假结果:", w.Body.String())
	}
	if AttendanceType == 5 {
		fmt.Println("创建事故结果:", w.Body.String())
	}
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否创建成功
	var attendance models.ShipperAttendanceLog
	delete(params, "state_type")
	tools.Db.Model(attendance).Where(params).First(&attendance)
	assert.Equal(t, true, attendance.Id > 0)
	if AttendanceType == 4 {
		AttendanceLeaveID = attendance.Id
	}
	if AttendanceType == 5 {
		AttendanceAccidentID = attendance.Id
	}
}

// TestAttendanceReviewState
//
// @Description: 获取考勤状态
// @Author: Rixat
// @Time: 2023-11-07 08:24:50
// @receiver
// @param c *gin.Context
func TestAttendanceReviewState(t *testing.T) {
	params := map[string]interface{}{
		"review_state": AttendanceReviewState,
	}
	if AttendanceType == 4 {
		params["id"] = AttendanceLeaveID
	}
	if AttendanceType == 5 {
		params["id"] = AttendanceAccidentID
	}
	w := helper.SendGinPost("/ug/cms/v2/shipment/attendance/review", helper.MapToUrlValues(params), "", "",nil)
	if AttendanceType == 4 {
		fmt.Println("修改状态请假结果:", w.Body.String())
	}
	if AttendanceType == 5 {
		fmt.Println("修改状态事故结果:", w.Body.String())
	}
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否更新成功
	var attendance models.ShipperAttendanceLog
	tools.Db.Model(attendance).Where(params).First(&attendance)
	assert.Equal(t, true, attendance.Id > 0)
}

// TestShipperAttendanceList
//
// @Description: 获取考勤列表
// @Author: Rixat
// @Time: 2023-11-07 09:06:12
// @receiver
// @param c *gin.Context
func TestShipperAttendanceList(t *testing.T) {
	StateType = 2
	params := map[string]interface{}{
		"page":  1,
		"limit": 10,
		"test":  "test",
	}
	if StateType == 1 { // 打卡
		params["type"] = StateType
	}
	if StateType == 2 { // 请假
		params["type"] = StateType
	}
	if StateType == 3 { // 事故
		params["type"] = StateType
	}
	w := helper.SendGinGet("/ug/cms/v2/shipment/attendance/list1", helper.MapToUrlValues(params), "", "")
	if StateType == 1 {
		fmt.Println("打卡列表结果:", w.Body.String())
	}
	if StateType == 2 {
		fmt.Println("请假列表结果:", w.Body.String())
	}
	if StateType == 3 {
		fmt.Println("事故列表结果:", w.Body.String())
	}
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}

// 获取考勤详情
//
// @Description:
// @Author: Rixat
// @Time: 2023-11-07 08:24:19
// @receiver
// @param c *gin.Context
func TestShipperAttendanceDetail(t *testing.T) {
	var params map[string]interface{}
	if AttendanceType == 4 {
		params = map[string]interface{}{
			"id": AttendanceLeaveID,
		}
	}
	if AttendanceType == 5 {
		params = map[string]interface{}{
			"id": AttendanceAccidentID,
		}
	}
	w := helper.SendGinGet("/ug/cms/v2/shipment/attendance/detail", helper.MapToUrlValues(params), "", "")
	if AttendanceType == 4 {
		fmt.Println("请假详情结果:", w.Body.String())
	}
	if AttendanceType == 5 {
		fmt.Println("事故详情结果:", w.Body.String())
	}
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}

func TestShipperAttendanceTypeNames(t *testing.T) {
	var params map[string]interface{}
	w := helper.SendGinGet("/ug/cms/v2/shipment/attendance/leave-types", helper.MapToUrlValues(params), "", "")
	fmt.Println("请假详情结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))
}

func TestAttendanceChangeState(t *testing.T) {
	params := map[string]interface{}{
		"id":    69,
		"state": 2,
	}

	w := helper.SendGinPost("/ug/cms/v2/shipment/attendance/change-state", helper.MapToUrlValues(params), "", "",nil)
	fmt.Println("修改状态请假结果:", w.Body.String())
	assert.Equal(t, 200, helper.GetResCode(w.Body.String()))

	// 验证是否更新成功
	var attendance models.ShipperAttendanceLog
	tools.Db.Model(attendance).Where(params).First(&attendance)
	assert.Equal(t, true, attendance.Id > 0)
}
