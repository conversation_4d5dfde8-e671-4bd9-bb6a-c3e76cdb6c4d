package shipment_reduce

import (
	"mulazim-api/models"
	"mulazim-api/observers"
	"mulazim-api/tools"
	"testing"
	"time"
)

// TestHelloWord
//
//	@Description: 测试用例 用于测试营销活动修改观察者
//	@param t
func TestMarketingChangeObserve(t *testing.T) {
	observe := observers.MarketingChangeObserve{}
	//开始观察
	observe.Observe(844)
	db := tools.GetDB()
	var marketing models.Marketing
	db.Model(&marketing).Where("id = ?", 844).First(&marketing)
	//跟新created_at 属性为当前时间，并保存
	marketing.CreatedAt = time.Now()
	marketing.State = marketing.State + 1
	// marketing.NameUg = "测试修改"
	//保存观察到的信息
	db.Save(&marketing)
	var admin models.Admin
	tools.Db.Model(admin).First(&admin)
	observe.SaveChanges(admin)
}
