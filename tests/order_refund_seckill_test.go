package tests

import (
	"encoding/json"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/tests/helper"
	"mulazim-api/tools"
	"net/url"
	"testing"

	"github.com/go-playground/assert/v2"
	"github.com/golang-module/carbon/v2"
)

func deleteSeckillOrderInfo(orderId int, seckillId int, seckillLogId int) {
	db := tools.Db
	db.Exec("delete from t_order_today where id = ?;", orderId)
	db.Exec("delete from t_order_detail where order_id = ?;", orderId)
	db.Exec("delete from t_take_order where order_id = ?;", orderId)
	db.Exec("delete from t_order_state_log where order_id = ?;", orderId)
	db.Exec("delete from wechat where order_id = ?;", orderId)
	db.Exec("delete from wechat_refund where order_id = ?;", orderId)
	db.Exec("delete from t_admin_recharge_log where type =3 and out_trade_no = ?;", orderId)
	db.Exec("delete from b_seckill where id = ?", seckillId)
	db.Exec("delete from b_seckill_log where id = ?", seckillLogId)
	db.Exec("delete from t_admin_packet_recharge_log where out_trade_no = ?", seckillLogId)
	db.Exec("delete from t_pay_lakala where order_id = ?", orderId)
	// 清空Radis
	redisHelper := tools.GetRedisHelper()
	// redisHelper.Set(redisHelper.Context(),"lumen_database_skill_stock_id_70",10,30*time.Second)
	redisHelper.GetDel(redisHelper.Context(), "lumen_database_skill_stock_id_"+tools.ToString(seckillId))
	redisHelper.GetDel(redisHelper.Context(), "lumen_database_skill_stock_id_"+tools.ToString(seckillId)+"key")

}

const TYPE_USER = 1           // 用户退单
const TYPE_ADMIN_CMS = 2      // 后台退单
const TYPE_ADMIN_MERCHANT = 3 // 商家退单

const TERMINAL_CMS = 7
const TERMINAL_MINI = 8
const TERMINAL_MERCHANT = 9

func createInfoWechat(state int) {
	db := tools.Db
	db.Exec("INSERT INTO `mulazimpro`.`b_seckill` (`id`, `admin_id`, `city_id`, `area_id`, `restaurant_id`, `food_id`, `total_count`, `saled_count`, `price`, `order`, `state`, `review_state`, `begin_time`, `end_time`, `order_time`, `user_max_order_count`, `platform_service_fee`, `created_at`, `updated_at`, `deleted_at`, `remark`) VALUES (70, 2036, 1, 1, 6273, 193017, 100, 1, 5, NULL, 1, 2, ?, ?, NULL, 3, 50, '2023-07-27 10:09:03', '2023-07-27 10:09:03', NULL, NULL);", carbon.Now().Format("Y-m-d H:00:00", configs.AsiaShanghai), carbon.Now().AddHour().Format("Y-m-d H:00:00", configs.AsiaShanghai))
	db.Exec("INSERT INTO `mulazimpro`.`t_order_today` (`id`, `random_id`, `terminal_id`, `order_id`, `category_id`, `city_id`, `area_id`, `store_id`, `user_id`, `building_id`, `order_address`, `name`, `mobile`, `consume_type`, `pay_type`, `pay_channel`, `pay_platform`, `original_price`, `price`, `shipment`, `lunch_box_fee`, `full_reduction_amount`, `mp_profit`, `dealer_profit`, `cash`, `coin`, `consume`, `description`, `timezone`, `booking_time`, `pay_time`, `print_time`, `printed_time`, `delivery_taked_time`, `delivery_start_time`, `delivery_end_time`, `real_shipment`, `deduction_fee`, `taked`, `shipper_id`, `sent_sms`, `delete_flag`, `refund_type`, `refund_chanel`, `refunded`, `is_douyin`, `send_notify`, `serial_number`, `shipper_complete_grant`, `is_luck_drawed`, `is_auto_refund`, `is_commented`, `distance`, `order_type`, `called`, `state`, `created_at`, `updated_at`, `deleted_at`, `cash_clear_state`, `cash_clear_time`, `cash_clear_channel`, `cash_clear_admin`, `seckill_state`, `is_auto_fished`, `self_take_number`, `delivery_type`) VALUES (5737294, NULL, 5, 'tasi0000000000002307271025502757', 1, 1, 1, 6273, 243686, 1980, '1980د', 'rixat', '16699887912', 1, 5, 1, 0, 10, 5, 0, 0, NULL, 0, 1, 0, 0, 0, '', 6, '2023-07-27 10:43:45', '2023-07-27 10:26:08', '2023-07-27 10:25:50', NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 1, 0, 0, 0, 0, NULL, 0, 1, 0, 0, 0, 0, 0.00, 1, 0, " + tools.ToString(state) + ", '2023-07-27 10:25:50', '2023-07-27 10:26:08', NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, 1);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `pref_id`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10593322, 5737294, 193017, 10, 5, 3.6, 100, 11.4, 0, 1, 1, 53, 0, 1, NULL, 218, '2023-07-27 10:25:50', '2023-07-27 10:25:50', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`wechat` (`id`, `app_id`, `mch_id`, `open_id`, `order_id`, `out_trade_no`, `transaction_id`, `prepay_id`, `nonce_str`, `trade_type`, `total_fee`, `payed`, `number`, `remark`, `expire_time`, `created_at`, `updated_at`, `deleted_at`) VALUES (1285, NULL, NULL, NULL, 5737294, 'tasi0000000000002307271025502757', '4200001915202307274282032279', 'wx271225535562145f88a2e089ecebab0000', 'zfLVJcxOf6O7uAQg', 'APP', 5, 1, 0, NULL, '2023-07-27 12:25:53', '2023-07-27 10:25:53', '2023-07-27 10:26:08', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34618798, 5737294, 2, NULL, 1, '2023-07-27 10:26:08', '2023-07-27 10:26:08', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34618797, 5737294, 1, NULL, 1, '2023-07-27 10:25:50', '2023-07-27 10:25:50', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34618799, 5737294, 3, NULL, 1, '2023-07-27 10:26:08', '2023-07-27 10:26:08', NULL);")
	if state == 4 {
		db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34618800, 5737294, 4, NULL, 1, '2023-07-27 10:28:05', '2023-07-27 10:28:05', NULL);")
	}
	if state == 5 {
		db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34618800, 5737294, 5, NULL, 1, '2023-07-27 10:28:05', '2023-07-27 10:28:05', NULL);")
	}
	db.Exec("INSERT INTO `mulazimpro`.`b_seckill_log` (`id`, `user_id`, `detail_id`, `seckill_id`, `food_id`, `saled_count`, `seckill_price`, `seckill_platform_service_fee`, `state`, `created_at`, `updated_at`) VALUES (218, 243686, 10593322, 70, 193017, 1, 5, 50, 1, '2023-07-27 10:25:50', '2023-07-27 10:25:50');")
	db.Table("t_admin_packet_balance").Where("area_id=?", 1).Update("packet_balance", 10000000-50)
	db.Exec("INSERT INTO `mulazimpro`.`t_admin_packet_recharge_log` (`id`, `city_id`, `area_id`, `recharger_id`, `admin_id`, `type`, `recharge_amount`, `balance`, `out_trade_no`, `transaction_id`, `prepay_id`, `nonce_str`, `trade_type`, `payed`, `expire_time`, `created_at`, `updated_at`, `deleted_at`) VALUES (11184, 1, 1, 243686, 22036, 4, 50, 9997516, '218', '218', '', '', '', 1, NULL, '2023-07-27 10:25:50', '2023-07-27 10:25:50', NULL);")
}

func createInfoLakala(state int) {
	db := tools.Db
	db.Exec("INSERT INTO `mulazimpro`.`b_seckill` (`id`, `admin_id`, `city_id`, `area_id`, `restaurant_id`, `food_id`, `total_count`, `saled_count`, `price`, `order`, `state`, `review_state`, `begin_time`, `end_time`, `order_time`, `user_max_order_count`, `platform_service_fee`, `created_at`, `updated_at`, `deleted_at`, `remark`) VALUES (71, 2036, 1, 1, 5049, 155755, 100, 1, 10, NULL, 1, 2, ?, ?, NULL, 3, 40, '2023-07-27 10:09:56', '2023-07-27 10:09:56', NULL, NULL);", carbon.Now().Format("Y-m-d H:00:00", configs.AsiaShanghai), carbon.Now().AddHour().Format("Y-m-d H:00:00", configs.AsiaShanghai))
	db.Exec("INSERT INTO `mulazimpro`.`t_order_today` (`id`, `random_id`, `terminal_id`, `order_id`, `category_id`, `city_id`, `area_id`, `store_id`, `user_id`, `building_id`, `order_address`, `name`, `mobile`, `consume_type`, `pay_type`, `pay_channel`, `pay_platform`, `original_price`, `price`, `shipment`, `lunch_box_fee`, `full_reduction_amount`, `mp_profit`, `dealer_profit`, `cash`, `coin`, `consume`, `description`, `timezone`, `booking_time`, `pay_time`, `print_time`, `printed_time`, `delivery_taked_time`, `delivery_start_time`, `delivery_end_time`, `real_shipment`, `deduction_fee`, `taked`, `shipper_id`, `sent_sms`, `delete_flag`, `refund_type`, `refund_chanel`, `refunded`, `is_douyin`, `send_notify`, `serial_number`, `shipper_complete_grant`, `is_luck_drawed`, `is_auto_refund`, `is_commented`, `distance`, `order_type`, `called`, `state`, `created_at`, `updated_at`, `deleted_at`, `cash_clear_state`, `cash_clear_time`, `cash_clear_channel`, `cash_clear_admin`, `seckill_state`, `is_auto_fished`, `self_take_number`, `delivery_type`) VALUES (5737295, NULL, 5, 'ewirgul0000000002307271546212760', 1, 1, 1, 5049, 243686, 1980, '1980د', 'rixat', '16699887912', 1, 5, 1, 1, 1500, 10, 0, 1, NULL, 0, 2, 0, 0, 0, '', 6, '2023-07-27 21:45:00', '2023-07-27 15:46:44', '2023-07-27 21:22:00', NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 1, 0, 0, 0, 0, NULL, 0, 1, 0, 0, 0, 0, 0.00, 0, 0, " + tools.ToString(state) + ", '2023-07-27 15:46:21', '2023-07-27 15:46:44', NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, 1);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_detail` (`id`, `order_id`, `store_foods_id`, `original_price`, `price`, `mp_percent`, `discount_percent`, `dealer_percent`, `mp_profit`, `dealer_profit`, `number`, `lunch_box_id`, `lunch_box_fee`, `lunch_box_count`, `pref_id`, `seckill_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (10593323, 5737295, 155755, 1500, 10, 3.6, 100, 16.4, 0, 2, 1, 1, 1, 1, NULL, 219, '2023-07-27 15:46:21', '2023-07-27 15:46:21', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_pay_lakala` (`id`, `order_id`, `order_no`, `payer_openid`, `seller_member_no`, `out_order_no`, `amount`, `pay_amount`, `terminal_ip`, `order_name`, `split_mode`, `front_url`, `back_url`, `pay_method`, `split_rule_data`, `out_request_no`, `pay_status`, `order_status`, `error_message`, `pay_seq_no`, `split_seq_no`, `split_rule_result`, `buyer_member_no`, `pay_info`, `qzt_channel_pay_request_no`, `channel_trade_no`, `channel_seq_no`, `pay_channel_trade_no`, `third_party_payment`, `open_id`, `sub_open_id`, `pay_time`, `confirm_url`, `is_confirm`, `error_code`, `message`, `payer_type`, `pay_from`, `created_at`, `updated_at`, `deleted_at`) VALUES (189, 5737295, '7090266197651902464', 'oe68p5Sb1OxCDNl6IRErg8qbVYHA', '7076471907119063040', '230727154632295362', 11, 11, 1855490929, '订单：ewirgul0000000002307271546212760', 0, 'https://apiv2.college.almas.biz/zh/v1/payment/lakala/pay/notify', 'https://apiv2.college.almas.biz/zh/v1/payment/lakala/pay/notify', '{\"JSAPI\":{\"amount\":11,\"mch_appid\":\"wxe2fefdde08e515a6\",\"openid\":\"oe68p5Sb1OxCDNl6IRErg8qbVYHA\",\"mode\":\"wxpay\",\"front_url\":\"https:\\/\\/apiv2.college.almas.biz\\/zh\\/v1\\/payment\\/lakala\\/pay\\/notify\",\"front_fail_url\":\"https:\\/\\/apiv2.college.almas.biz\\/zh\\/v1\\/payment\\/lakala\\/pay\\/notify\",\"term_base_station\":\"00+LAC:6361+CID:58130\",\"timeout_express\":10,\"term_ip\":\"***************\"}}', '{\"split_list\": [{\"amount\": 11, \"member_no\": \"7076471907119063040\", \"special_account_no\": \"S005\"}]}', '230727154632295362', 1004, 1006, '', '7090266197937115136', '7090266245018177536', '[{\"member_id\": \"7076471907119063040\", \"total_amount\": 11}]', '', '{\"appId\": \"wxe2fefdde08e515a6\", \"package\": \"prepay_id=wx27174632909548b088a22d186cef720000\", \"pay_sign\": \"hjdfGruPpv+UWqSLFHgLj5kSTMUbDM4RvLLp4DvF8e6h5RDtpoQG95PTYDrhSJmpmrtXXfx7Ls3XBeph3d/cpl8DzQAie01gREzKwT4JNDlF/zG/7/8zhtQdqj6BroC9e9Nqszjm7mgIgyqXBHHmwVd7pGO77WFIBeCG1dND5cr5RxVBOo/HvEKjAmOrAWj2We6gwcsQmQeXs4vYIm9rZzXYpxRGhXG/EuSzMxRz/HVQWwx9N0tP13xArDU341QiKSvDMmazmvOhAt5HXsIwMjllCD4fT/5NHjt+YQEKNH+kr19dWPoAZ1PSarfmCVcMtxU0/4kaZWDMfitWsYgmeA==\", \"nonce_str\": \"7ffe773b035040f8887d0c61cd6a56ef\", \"prepay_id\": \"wx27174632909548b088a22d186cef720000\", \"sign_type\": \"RSA\", \"timestamp\": \"**********\"}', '', '202307271101191002**************', '**************', '4200001959202307279655625033', 'WECHAT', '', '', '2023-07-27 17:46:42', '', 0, '', '', 1, 4, '2023-07-27 15:46:32', '2023-07-27 15:46:44', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34618878, 5737295, 1, NULL, 1, '2023-07-27 15:46:21', '2023-07-27 15:46:21', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34618880, 5737295, 2, NULL, 1, '2023-07-27 15:46:44', '2023-07-27 15:46:44', NULL);")
	db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34618881, 5737295, 3, NULL, 1, '2023-07-27 15:46:44', '2023-07-27 15:46:44', NULL);")
	if state == 4 {
		db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34618881, 5737295, 4, NULL, 1, '2023-07-27 15:46:44', '2023-07-27 15:46:44', NULL);")
	}
	if state == 5 {
		db.Exec("INSERT INTO `mulazimpro`.`t_order_state_log` (`id`, `order_id`, `order_state_id`, `fail_reason`, `state`, `created_at`, `updated_at`, `deleted_at`) VALUES (34618881, 5737295, 5, NULL, 1, '2023-07-27 15:46:44', '2023-07-27 15:46:44', NULL);")
	}
	db.Exec("INSERT INTO `mulazimpro`.`b_seckill_log` (`id`, `user_id`, `detail_id`, `seckill_id`, `food_id`, `saled_count`, `seckill_price`, `seckill_platform_service_fee`, `state`, `created_at`, `updated_at`) VALUES (219, 243686, 10593323, 71, 155755, 1, 10, 40, 1, '2023-07-27 15:46:21', '2023-07-27 15:46:21');")
	db.Table("t_admin_packet_balance").Where("area_id=?", 1).Update("packet_balance", 10000000-40)
	db.Exec("INSERT INTO `mulazimpro`.`t_admin_packet_recharge_log` (`id`, `city_id`, `area_id`, `recharger_id`, `admin_id`, `type`, `recharge_amount`, `balance`, `out_trade_no`, `transaction_id`, `prepay_id`, `nonce_str`, `trade_type`, `payed`, `expire_time`, `created_at`, `updated_at`, `deleted_at`) VALUES (11184, 1, 1, 243686, 22036, 4, 50, 9997516, '219', '219', '', '', '', 1, NULL, '2023-07-27 10:25:50', '2023-07-27 10:25:50', NULL);")
}

func validateCancelResutl(t *testing.T, orderId int, seckillId int, seckillLogId int, refundType string) {
	type Order struct {
		ID       int `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 自增编号
		State    int `gorm:"column:state;default:1"`               // 订单状态
		Refunded int `gorm:"column:refunded;default:0"`            // 退款状态
	}

	db := tools.Db
	var order Order
	db.Table("t_order_today").Where("id = ?", orderId).Scan(&order)
	assert.Equal(t, true, order.State == 8 || order.State == 9)
	assert.Equal(t, true, order.Refunded == 1)

	var count int64
	db.Table("t_order_state_log").Where("order_id = ? and order_state_id in ?", orderId, [2]int{8, 9}).Count(&count)
	assert.Equal(t, true, count > 0)

	if refundType == "wechat" {
		count = 0
		db.Table("wechat_refund").Where("order_id = ?", orderId).Count(&count)
		assert.Equal(t, true, count > 0)
	}
	if refundType == "lakala" {
		count = 0
		lakalaPay := make(map[string]interface{})
		db.Table("t_pay_lakala").Where("order_id = ?", orderId).Scan(&lakalaPay)
		db.Table("t_pay_lakala_refund").Where("pay_lakala_id = ?", tools.ToInt(lakalaPay["id"])).Count(&count)
		assert.Equal(t, true, count > 0)
	}

	type PacketPalance struct {
		PacketBalance int `gorm:"column:packet_balance;default:0"` // 退款状态
	}
	var packetPalance PacketPalance
	db.Table("t_admin_packet_balance").Where("area_id = ?", 1).Scan(&packetPalance)
	assert.Equal(t, packetPalance.PacketBalance, 10000000)

	count = 0
	db.Table("t_admin_packet_recharge_log").Where("out_trade_no = ? and type = 5", seckillLogId).Count(&count)
	assert.Equal(t, true, count == 1)

	type Seckill struct {
		SaledCount int `gorm:"column:saled_count;default:0"` // 退款状态
	}

	var seckill Seckill
	db.Table("b_seckill").Where("id = ?", seckillId).Scan(&seckill)
	assert.Equal(t, seckill.SaledCount, 0)
	fmt.Println("测试完成！！！")
}

// 发送请求
func sendCancelOrderReq(t *testing.T, orderId int, userId int, terminalId int, userType int, reasonId int, token string, serial string) {
	data := url.Values{}
	data.Set("order_id", fmt.Sprintf("%d", orderId))
	data.Set("user_id", fmt.Sprintf("%d", userId))
	data.Set("terminal_id", fmt.Sprintf("%d", terminalId))
	data.Set("user_type", fmt.Sprintf("%d", userType))
	data.Set("reason_id", fmt.Sprintf("%d", reasonId))
	w := helper.SendGinPost("/ug/payment/v1/cancel-order", data, token, serial,nil)
	fmt.Println(w.Body.String())
	assert.Equal(t, 200, getResCode(w.Body.String()))
}

// TestCmsWechatSeckillRefund
//
//	@Description: 后台退款秒杀订单
//	@author: Alimjan
//	@Time: 2023-07-27 12:12:24
//	@param t *testing.T
func TestCmsWechatSeckillRefund(t *testing.T) {
	seckillId := 70     // 秒杀ID
	seckillLogId := 218 // 秒杀日志ID
	orderId := 5737294  // 订单ID
	userId := 2036      // 代理ID
	reasonId := 1       // 取消原因
	// 删除测试完成的数据
	deleteSeckillOrderInfo(orderId, seckillId, seckillLogId)
	// 创建下单内容
	createInfoWechat(3)
	// 发送请求取消订单
	sendCancelOrderReq(t, orderId, userId, TERMINAL_CMS, TYPE_ADMIN_CMS, reasonId, "", "")
	// 验证结果是否正确
	validateCancelResutl(t, orderId, seckillId, seckillLogId, "wechat")
}

// 小程序用户客户端取消微信秒杀订单
func TestMiniWechatSeckillRefund(t *testing.T) {
	seckillId := 70     // 秒杀ID
	seckillLogId := 218 // 秒杀日志ID
	orderId := 5737294  // 订单ID
	userId := 243686    // 客户
	reasonId := 1       // 取消原因
	// 删除测试完成的数据
	deleteSeckillOrderInfo(orderId, seckillId, seckillLogId)
	// 创建下单内容
	createInfoWechat(3)
	// 发送请求取消订单
	sendCancelOrderReq(t, orderId, userId, TERMINAL_MINI, TYPE_USER, reasonId, "umfwW0g9Prz8VuFzBBehQjP1QDw4Qjrc07JuMeGP", "")
	// 验证结果是否正确
	validateCancelResutl(t, orderId, seckillId, seckillLogId, "wechat")
}

// 小程序商家客户端取消微信秒杀订单
func TestMerchantAppWechatSeckillRefund(t *testing.T) {
	seckillId := 70     // 秒杀ID
	seckillLogId := 218 // 秒杀日志ID
	orderId := 5737294  // 订单ID
	userId := 10277     // 商家ID
	reasonId := 1       // 取消原因
	// 删除测试完成的数据
	deleteSeckillOrderInfo(orderId, seckillId, seckillLogId)
	// 创建下单内容
	createInfoWechat(4)
	// createInfoWechat(5)
	// 发送请求取消订单
	sendCancelOrderReq(t, orderId, userId, TERMINAL_MERCHANT, TYPE_ADMIN_MERCHANT, reasonId, "CLQoJrPjzz59lnbD9AZH9tfChrpoJDn5fo5c1XtJ", "123456789")
	// 验证结果是否正确
	validateCancelResutl(t, orderId, seckillId, seckillLogId, "wechat")
}

// 小程序商家客户端取消拉卡拉秒杀订单
func TestCmsLakalaSeckillRefund(t *testing.T) {
	seckillId := 71     // 秒杀ID
	seckillLogId := 219 // 秒杀日志ID
	orderId := 5737295  // 订单ID
	userId := 2036      // 代理ID
	reasonId := 1       // 取消原因
	// 删除测试完成的数据
	deleteSeckillOrderInfo(orderId, seckillId, seckillLogId)
	// 创建下单内容
	createInfoLakala(3)
	// 发送请求取消订单
	sendCancelOrderReq(t, orderId, userId, TERMINAL_CMS, TYPE_ADMIN_CMS, reasonId, "", "")
	// // 验证结果是否正确
	validateCancelResutl(t, orderId, seckillId, seckillLogId, "lakala")
}

// 小程序商家客户端取消拉卡拉秒杀订单
func TestMiniAppLakalaSeckillRefund(t *testing.T) {
	seckillId := 71     // 秒杀ID
	seckillLogId := 219 // 秒杀日志ID
	orderId := 5737295  // 订单ID
	userId := 243686    // 客户ID
	reasonId := 1       // 取消原因
	// 删除测试完成的数据
	deleteSeckillOrderInfo(orderId, seckillId, seckillLogId)
	// 创建下单内容
	createInfoLakala(3)
	// 发送请求取消订单
	sendCancelOrderReq(t, orderId, userId, TERMINAL_MINI, TYPE_USER, reasonId, "umfwW0g9Prz8VuFzBBehQjP1QDw4Qjrc07JuMeGP", "")
	// // 验证结果是否正确
	validateCancelResutl(t, orderId, seckillId, seckillLogId, "lakala")
}

// 小程序商家客户端取消拉卡拉秒杀订单
func TestMerchantAppLakalaSeckillRefund(t *testing.T) {
	seckillId := 71     // 秒杀ID
	seckillLogId := 219 // 秒杀日志ID
	orderId := 5737295  // 订单ID
	userId := 4831      // 商家ID
	reasonId := 1       // 取消原因
	// 删除测试完成的数据
	deleteSeckillOrderInfo(orderId, seckillId, seckillLogId)
	// 创建下单内容
	// createInfoLakala(4) // 新订单
	createInfoLakala(5) // 已接订单
	// 发送请求取消订单
	token := "QWoymL4RTU49pKu06XTMNu49FURNRqPl5uo7GAU7"
	serialNum := "123456789"
	sendCancelOrderReq(t, orderId, userId, TERMINAL_MERCHANT, TYPE_ADMIN_MERCHANT, reasonId, token, serialNum)
	// // 验证结果是否正确
	validateCancelResutl(t, orderId, seckillId, seckillLogId, "lakala")
}

// getCode
//
//	@Description: 解析服务器返回数据 获取Status
//	@author: Alimjan
//	@Time: 2023-03-07 17:48:28
//	@param b string
//	@return int
func getResCode(b string) int {
	var m map[string]interface{}
	err := json.Unmarshal([]byte(b), &m)
	if err != nil {
		println(err.Error())
		return 0
	}
	var rtn int
	switch m["status"].(type) {
	case int:
		rtn = m["status"].(int)
		break
	case float64:
		rtn = int(m["status"].(float64))
		break
	case float32:
		rtn = int(m["status"].(float32))
		break

	default:
		//fmt.Printf("出错了 %s\n", reflect.TypeOf(v))
		rtn = 0
		break
	}
	return rtn

}
