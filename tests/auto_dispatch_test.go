﻿package tests

import (
	"encoding/json"
	"fmt"
	"mulazim-api/configs"
	"mulazim-api/configs/mq"
	"mulazim-api/jobs"
	"mulazim-api/tools"
	"reflect"
	"testing"
	"unsafe"

	"context"

	rmq_client "github.com/apache/rocketmq-clients/golang"
)

func TestPushAutoDispatch(t *testing.T) {
    AutoDispatchJob()
}

func AutoDispatchJob(){
	// 设置HTTP协议客户端接入点，进入消息队列RocketMQ版控制台实例详情页面的接入点区域查看。
	endpoint := mq.MqConfigOpt.Endpoint
	// 请确保环境变量ALIBABA_CLOUD_ACCESS_KEY_ID、ALIBABA_CLOUD_ACCESS_KEY_SECRET已设置。
	// AccessKey ID，阿里云身份验证标识。
	accessKey := mq.MqConfigOpt.AccessKey
	// AccessKey Secret，阿里云身份验证密钥。
	secretKey := mq.MqConfigOpt.SecretKey

	topicName := "auto_dispatch_order_area_group_1"

	topic := "pro_" + topicName
	if configs.CurrentEnvironment != "production" {
		topic = "dev_" + topicName
	}
	producer := jobs.GetProducer(endpoint, accessKey, secretKey, topic)

	// b.currentReconnectCount++
	data := map[string]interface{}{
		"area_id":1,
		"order_id":123,
	}
	
	// tag := "area_type_1"
	// key := "area_id_"+tools.ToString(1)
	byteJson, _ := json.Marshal(data)
	msg := &rmq_client.Message{
		Topic: topic,
		Body:  byteJson,
	}
	// msg.GetProperties() = map[string]string{}
    // msg.GetProperties()["DELAY"] = "30"
	// per = msg.GetProperties()
	// fmt.Println(per)

	// msg.properties["DELAY"] = 30
	// fmt.Println(properties)

	// Key-value pair to add

	// Use reflection to access and modify the `properties` field
	v := reflect.ValueOf(msg).Elem()               // Get the underlying struct
	propertiesField := v.FieldByName("properties") // Get the `properties` field
	propertiesPtr := unsafe.Pointer(propertiesField.UnsafeAddr())
	realProperties := (*map[string]string)(propertiesPtr)

	// // 修改私有字段
	*realProperties = map[string]string{
		"DELAY": "30",
	}

	// 验证修改是否成功
	fmt.Println(msg.GetProperties())


	// msg.SetKeys(key)
	// msg.SetMessageGroup("AUTO_DISPATCH_NEW_ORDER")
	// 设置消息自定义属性。
	//msg.StartDeliverTime = time.Now().Add(time.Second * 20).UnixMilli()
	_, err := producer.Send(context.TODO(), msg)
	if err != nil {
		tools.Logger.Errorf("rocketmq-publish-fail ---->\n\tError:%s  topic:%s  data:%s\n", err, topic, data)
	}
}


func TestRedis(t *testing.T){
	mapArr := []map[string]interface{}{
		{"admin_id":"10100","lat":"43.778387","lng":"87.619086","accuracy":"13.297662734985352","area_id":"1","city_id":"1","time":"2024-08-15 17:01:14"},
		{"admin_id":"13408","lat":"43.770905","lng":"87.6233","accuracy":"13.297662734985352","area_id":"1","city_id":"1","time":"2024-08-15 17:01:14"},
		{"admin_id":"13700","lat":"43.778652794027","lng":"87.61638199171249","accuracy":"13.297662734985352","area_id":"1","city_id":"1","time":"2024-08-15 17:01:14"},
		{"admin_id":"14284","lat":"43.77461878989904","lng":"87.65070931331125","accuracy":"13.297662734985352","area_id":"1","city_id":"1","time":"2024-08-15 17:01:14"},
		{"admin_id":"15017","lat":"43.758185","lng":"87.627485","accuracy":"13.297662734985352","area_id":"1","city_id":"1","time":"2024-08-15 17:01:14"},
		{"admin_id":"15218","lat":"43.76523357610047","lng":"87.62565949821379","accuracy":"13.297662734985352","area_id":"1","city_id":"1","time":"2024-08-15 17:01:14"},
		{"admin_id":"15757","lat":"43.76714520455448","lng":"87.61113965270778","accuracy":"13.297662734985352","area_id":"1","city_id":"1","time":"2024-08-15 17:01:14"},
		{"admin_id":"15886","lat":"43.766795","lng":"87.611123","accuracy":"13.297662734985352","area_id":"1","city_id":"1","time":"2024-08-15 17:01:14"},
	}
	for _, value:= range mapArr{
		key := fmt.Sprintf("shipper_%s", value["admin_id"].(string))
		tools.RedisSetValue(key, tools.MapToString(value), 60*60*24*5)
	}
	fmt.Println(213232)
}

func TestRedisMaxTime(t *testing.T){
	// 过滤压单中的订单
	waitOrderKey := "auto_dispatch_wait_order_"+tools.ToString(8814198)
	waitOrder := tools.RedisGetValue(waitOrderKey)
	if len(waitOrder) != 0 {
		redisHelper := tools.GetRedisHelper()
		c := redisHelper.Context()
		expires := float64(60)
		if exists, _ := redisHelper.Exists(c, waitOrderKey).Result(); exists > 0 {
			expires = redisHelper.TTL(c, waitOrderKey).Val().Seconds()
		}
		fmt.Println(expires)
	}

}


func TestRedisBatGet(t *testing.T){
	var ids []string
	ids = append(ids, "auto_dispatch_wait_order_1")
	ids = append(ids, "auto_dispatch_wait_order_2")
	ids = append(ids, "auto_dispatch_wait_order_3")
	ids = append(ids, "auto_dispatch_wait_order_4")
	redisHelper := tools.GetRedisHelper()
	c := redisHelper.Context()
	posInfos := redisHelper.MGet(c,ids...).Val()
	fmt.Println(posInfos)
}