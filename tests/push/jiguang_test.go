package push

import (
	"mulazim-api/constants"
	"mulazim-api/jobs"
	"mulazim-api/models"
	"mulazim-api/services"
	"mulazim-api/tools"
	"testing"

	"github.com/go-playground/assert/v2"
)

func TestPushBindDevice(t *testing.T) {
	var admin models.Admin
	var admin2 models.Admin
	tools.GetDB().Model(&admin).Where("id = ?", 1).First(&admin)
	tools.GetDB().Model(&admin2).Where("id = ?", 2).First(&admin2)
	device, _ := services.PushDeviceServiceInst.CreateAdminDevice(
		admin,
		"1507bfd3f73aaf8424d",
		models.PushDevicePlatformIos,
		models.PushDeviceClientShipper,
		models.PushDeviceLangUg,
		admin.ID,
		318,
		"test",

	)
	assert.Equal(t, device.UserID, admin.ID)
	result, err := services.PushDeviceServiceInst.Push(&device, "测试标题", "测试内容", map[string]interface{}{})
	assert.Equal(t, err, nil)
	assert.Equal(t, result, true)
	device, err = services.PushDeviceServiceInst.CreateAdminDevice(admin2, "12345678", models.PushDevicePlatformIos, models.PushDeviceClientShipper, models.PushDeviceLangUg,admin.ID,318,"test")
	assert.Equal(t, device.UserID, admin2.ID)

	if err == nil {
		tools.GetDB().Model(&device).Delete(&device)
	}
}

func TestPushMessage(t *testing.T) {
	//var res map[string]interface{}
	//var err error
	//jpushClient := tools.NewJiguangPush("f8a4b77833b7b91464ae72dd", "ff42b61c06846088cfb199ae", false)
	////res, err = jpushClient.Push("161a3797c9041344cfd", "测试标题", "推送内容", map[string]interface{}{}) // Qurbanjan
	////t.Log("jpush-test", res)
	////res, err  = jpushClient.Push("171976fa8b9c5a3cd00", "测试标题", "推送内容", map[string]interface{}{}) // Alim Kirem
	////t.Log("jpush-test", res)
	////res, err  = jpushClient.Push("101d85590841bf81af3", "测试标题", "推送内容", map[string]interface{}{}) // Alimjan Captain
	////t.Log("jpush-test", res)
	//res, err = jpushClient.Push("18071adc02130e57528", "测试标题", "推送内容", map[string]interface{}{})
	//t.Log("jpush-test", res) // elshat
	//
	//// rozi
	////res, err  = jpushClient.Push("170976fa8b82dbd7f991", "测试标题", "推送内容", map[string]interface{}{})
	////t.Log("jpush-test", res)
	//// rozi memet
	////res, err = jpushClient.Push("1507bfd3f73aaf8424d", "测试标题", "推送内容", map[string]interface{}{})
	////t.Log("jpush-test", res)
	//
	//if err != nil {
	//	t.Error(err)
	//}

}

// {"page":"chat_page","order_id":5741201}
func TestPushDevice(t *testing.T) {

	pushData := jobs.PushData{
		UserType: constants.PushUserTypeShipper,
		UserId:   1,
		Client:   models.PushDeviceClientShipper,
		PushContent: jobs.PushContent{
			TitleZh:   "你好",
			TitleUg:   "ياخشىمۇ",
			ContentUg: "كەلدىڭىزما",
			ContentZh: "你好吗",
			Params:    map[string]interface{}{},
		},
		Sound: constants.SoundShipperNotification,
		ChannelType: constants.ChannelTypeShipperDefault,
		AdminId: 2046,
	}

	job := jobs.NewPushJob()
	job.PushData(pushData)
	//time.Sleep(300)
	//job.PushData(pushData)
	//time.Sleep(300)
	//job.PushData(pushData)
	//time.Sleep(300)
	//job.PushData(pushData)
	//time.Sleep(300)
	//job.PushData(pushData)
	//time.Sleep(300)
	//job.PushData(pushData)
	//time.Sleep(300)
	//job.PushData(pushData)
	//time.Sleep(300)
	//job.PushData(pushData)
	//time.Sleep(300)
	//job.PushData(pushData)
}

func TestChatPush(t *testing.T) {
	//var orderChatDetail chat.OrderChatDetail
	//tools.GetDB().Model(chat.OrderChatDetail{}).
	//	Where("id = ? ", 2794).
	//	First(&orderChatDetail)
	//var job = jobs.NewPushJob()
	//job.SendChatPush(&orderChatDetail)
}

func TestAdminAssignedPush(t *testing.T) {
	//db := tools.Db
	//var order models.OrderToday
	//var id uint = 5741702
	//db.Model(&models.OrderToday{}).Preload("Restaurant").Where("id = ?", id).Preload("OrderDetail.RestaurantFoods").Find(&order)
	//jobs.SendOrderAssignedPush(order)
}
