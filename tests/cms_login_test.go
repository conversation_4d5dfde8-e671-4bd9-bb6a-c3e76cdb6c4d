package tests

import (
	"context"
	"fmt"
	"mulazim-api/tests/helper"
	"mulazim-api/tests/login"
	"mulazim-api/tools"
	"net/url"
	"testing"

	"github.com/elliotchance/phpserialize"
)

func TestCmsLogin(t *testing.T) {
	cookie := login.CmsLogin("t<PERSON>han","ab123123")
	w := helper.SendGinGet("/ug/cms/v2/price-markup/list", url.Values{"page": {"1"}, "limit": {"10"}}, cookie, "")
	fmt.Println(w.Body.String())
}



func TestEncript(t *testing.T) {
	redisData := `s:424:"a:7:{s:18:"mulazim-cms-locale";s:5:"ug_CN";s:6:"_token";s:40:"cTtDucmsEPqNiwLojSMi0GB5vNiDy2GT5nmVNGB0";s:3:"url";a:0:{}s:9:"_previous";a:1:{s:3:"url";s:62:"https://cms.spec.almas.biz/ug/address/city/list-for-select-box";}s:5:"flash";a:2:{s:3:"old";a:0:{}s:3:"new";a:0:{}}s:56:"login_dashboard_59ba36addc2b2f9401580f014c7f58ea4e30989d";i:2036;s:9:"_sf2_meta";a:3:{s:1:"u";i:1744172199;s:1:"c";i:1744172189;s:1:"l";s:1:"0";}}";`
	redis := tools.GetRedisCmsHelper()
	redis.Set(context.Background(),"laravel:8414906b6a6e241fec23124f561b3bbcb0af4328", redisData,0)
	redisData1 := redis.Get(context.Background(),"laravel:8414906b6a6e241fec23124f561b3bbcb0af4328")
	fmt.Println(redisData1.Val())
	var sessionStr string
	if err := phpserialize.Unmarshal([]byte(redisData1.Val()), &sessionStr); err != nil {
		tools.Logger.Error("解析session data 失败")
		return
	}
	// session信息反序列化
	var sessionMap map[interface{}]interface{}
	// 这里的问题是创建了一个新的sessionMap变量，而不是使用外部声明的sessionMap变量
	// 使用 := 操作符会在当前作用域创建新变量，导致外部的sessionMap仍然为空
	var err error
	sessionMap, err = phpserialize.UnmarshalAssociativeArray([]byte(sessionStr))
	if err != nil {
		tools.Logger.Error("解析session data 失败")
		return
	}
	fmt.Println("sessionMap内容:", sessionMap)
}

