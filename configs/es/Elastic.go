package es

import (
"encoding/json"
"io/ioutil"
"mulazim-api/configs"
"mulazim-api/inits"
"strings"
)

type Config map[string]ElasticConfig
type ElasticConfigFile struct {
	Config Config `json:"config"`
}
type ElasticConfig struct {
	Host   string `json:"host"`
	CanalHost   string `json:"canal_host"`
}

var ElasticConfigOpt ElasticConfig

func init() {
	ElasticConfigOpt = LoadElasticConfig()
}
func LoadElasticConfig() ElasticConfig {
	configPath := strings.TrimRight(inits.ConfigFilePath, "/")
	content, err := ioutil.ReadFile(configPath + "/configs/es/elastic.json")
	if err != nil {
		panic(err)
	}
	var elasticConfigFile ElasticConfigFile
	json.Unmarshal(content, &elasticConfigFile)
	return elasticConfigFile.Config[configs.CurrentEnvironment]
}
