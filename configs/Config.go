/*
 * Copyright (c) 2021.
 */

package configs

import (
	"encoding/json"
	"io/ioutil"
	"mulazim-api/inits"
)

type Environment struct {
	Port                  string       `json:"port"`
	RedisIp               string       `json:"redis_ip"`
	RedisPassword         string       `json:"redis_password"`
	RedisDB               int          `json:"redis_db"`
	Concurrency           int          `json:"concurrency"`
	CdnUrl                string       `json:"cdn_url"`
	UploadRootDir         string       `json:"upload_root_dir"`
	JWTTokenKey           string       `json:"jwt_key"`
	WechatAppID           string       `json:"wechat_app_id"`
	WechatAppSecret       string       `json:"wechat_app_secret"`
	MysqlDns              string       `json:"mysql_dsn"`
	MysqlDnsReadDB1              string       `json:"mysql_dsn_read_db1"`
	
	ShumaiAppid           string       `json:"shumai_appid"`
	ShumaiAppSecret       string       `json:"shumai_appsecret"`
	AliyunAppCode         string       `json:"aliyun_app_code"`
	AliyunSmsAccessId     string       `json:"aliyun_sms_access_id" `
	AliyunSmsAccessSecret string       `json:"aliyun_sms_access_secret"`
	TencentApiKey         string       `json:"tencent_api_key"`
	TencentApiSecret      string       `json:"tencent_api_secret"`
	SendDingDingMsg       string       `json:"ding_ding_msg"`
	ArchivedFilePath      string       `json:"archived_file_path"`
	FfmpegMode            int          `json:"ffmpeg_mode"` // 1:本地模式  2:服务器模式
	LakalaConfig          LakalaConfig `json:"lakala"`
	PushConfig            PushConfig   `json:"push"` // 推送配置
	SocketUrl             string       `json:"socket_url"`
	CashOrderPostSms      string       `json:"cash_order_post_sms_url"`

	MaxCashoutAmount int `json:"max_cashout_amount"`  //一次性提现金额限制
	DayCashoutLimit  int `json:"day_cashout_limit"`   //一天 累计 提现金额
	CashOutStartTime int `json:"cash_out_start_time"` //提现开始时间 新疆时间
	CashOutEndTime   int `json:"cash_out_end_time"`   //提现结束时间 新疆时间

	CashOutNotificationUg              string `json:"cash_out_notification_ug"`                 //提现提示 维吾尔语
	CashOutNotificationZh              string `json:"cash_out_notification_zh"`                 //提现提示 汉语
	ShipperAppJumpWechatMiniOriginalId string `json:"shipper_app_jump_wechat_mini_original_id"` // 配送端 跳转 小程序 拉卡拉 配置 原始id //gh_4c15504e80b3
	ShipperAppJumpWechatMiniPage       string `json:"shipper_app_jump_wechat_mini_page"`        // 配送端 跳转 小程序 拉卡拉 配置 页面地址 //  pages/miniPay/index

	ShipperPushOrderDistance  []int `json:"shipper_push_order_distance"`   //配送端 推送订单距离
	ShipperPushOrderTimeDelta []int `json:"shipper_push_order_time_delta"` //配送端 推送订单时间间隔
	ShipperPushArea           []int `json:"shipper_push_area"`             //配送端 推送区域，下边的参数优先级高
	ShipperPushAll            int   `json:"shipper_push_area_all"`         //配送端 是否推送所有区域

	ShipperGrabOrderLimit int `json:"shipper_grab_order_limit"` //配送端 配送员抢单时间间隔

	ShipperAppVersion           string `json:"shipper_app_version"`  //配送端 最新版本
	MerchantAppVersion          string `json:"merchant_app_version"` //商家端 最新版本
	CmsKey                      string `json:"cms_key"`
	CmsCipher                   string `json:"cms_cipher"`
	CmsUrl                      string `json:"cms_url"`
	SwooleUrl      				string `json:"swoole_url"` //小程序服务端url
	SwooleOutUrl      			string `json:"swoole_out_url"` //小程序服务端 外部url
	
	ShipperArriveAtShopDistance int    `json:"shipper_arrive_at_shop_distance"` //配送员到达店铺区域距离

	ChatMsgCacheTime          int    `json:"chat_msg_cache_time"` // 聊天记录缓存时间
	ChatSocketServer          string `json:"chat_socket_server"`
	CheckForAttendance        int    `json:"check_for_attendance"`          //抢单前是否看考勤状态
	ShipperV1StopArea         []int  `json:"shipper_v1_stop_area"`          //配送端 v1 旧版停用区域
	ShipperNoCheckArriveShops []int  `json:"shipper_no_check_arrive_shops"` //配送端 不检查 到店状态
	MinDeliveryPrice          int    `json:"min_delivery_price"`            // 减配送费活动最低订单价格

	ShipperIntroduceRuleUrl    string `json:"shipper_introduce_rule_url"`     // 配送端 客户拓展规则 html 地址
	
	ShipperIntroduceAdImageUgUrl string `json:"shipper_introduce_ad_image_ug_url"` // 配送端 客户拓展 广告图片地址
	ShipperIntroduceAdImageZhUrl string `json:"shipper_introduce_ad_image_zh_url"` // 配送端 客户拓展 广告图片地址

	ShowShipperIntroduceAd     int64  `json:"show_shipper_introduce_ad"`      // 是否显示 配送端 客户拓展 广告图片
	AdvertMaterialQrcodeUrl    string `json:"advert_material_qrcode_url"`     // 宣传材料二维码地址
	ShipperIntroduceValidDays  int64    `json:"shipper_introduce_valid_days"` // 配送端 客户拓展  用户 有效期
	AdvertMaterialTakeCountMax int `json:"advert_material_take_count_max"` // 宣传材料 领取数量限制
	MerchantVoiceUrl        string  `json:"merchant_voice_url"`      //商家端提示声音 node socket 接口地址
	MerchantPushMinVersion int64 `json:"merchant_push_min_version"` //商家端开始推送的最小版本 


	ShipperVoiceUrl        string  `json:"shipper_voice_url"`      //配送端提示声音 node socket 接口地址
	ShipperPushMinVersion int64 `json:"shipper_push_min_version"` //配送端开始推送的最小版本


	//高德地图配置
	AMapKey                  string `json:"amap_key"` 
	AMapUrl                  string `json:"amap_url"`
	//是否开启 高峰期业务保护 正式环境 默认开启
	EnableRushHourProtection int `json:"enable_rush_hour_protection"` 
	
	SeckillAndPrefStopArea           []int `json:"seckill_and_pref_stop_area"`             //优惠和秒杀停用区域 仅限 商家端
	AmapWebKey string `json:"amap_web_key"`
	AmapWebSign string `json:"amap_web_sign"`

	TelecomCouponAccessKey string `json:"telecom_coupon_access_key"`
	TelecomCouponAccessIv string `json:"telecom_coupon_access_iv"`

	InsuranceConfig       InsuranceConfig `json:"insurance"`

	InsuranceStopTime           string `json:"insurance_stop_time"`                   //保险停止时间

	AesKey string `json:"aes_key"`

	Food Food `json:"food"` // 美食分成比例设定
	ApiSmartUrl string `json:"api_smart_url"`

	ShipperAppJumpEnv string `json:"shipper_app_jump_env"` // 配送端 跳转 小程序 拉卡拉 配置 原始id //gh_4c15504e80b3

	StoreMultiDiscountCount int `json:"store_multi_discount_count"` //店铺多份打折 同时进行的美食数量
	
	
	JPushMode string `json:"jpush_mode"`

	ClickHouseDsn string `json:"click_house_dsn"`

}
type LakalaConfig struct {
	AppId                  string `json:"app_id" `
	ServerUrl              string `json:"server_url"`
	RootUrl                string `json:"root_url"`
	DsaPrivateKeyPemPath   string `json:"dsa_private_key_pem_path"`
	RsaPublicPubPath       string `json:"rsa_public_pub_path"`
	SystemMerNo            string `json:"system_mer_no"`    //拉卡拉 会员号 平台
	SystemMerchantNo     string `json:"system_merchant_no"` //拉卡拉 商户号 平台
	MulazimWithdrawCartId  string `json:"mulazim_withdraw_cart_id"`
	SystemMinReserveAmount int    `json:"system_min_reserve_amount"` //佣金账户 最低储备
	WarningAdminNumber     string `json:"warning_admin_number"`
	LakalaOrderPrefix      string `json:"lakala_order_prefix"`
	ShipperPayQRCodeUrl    string `json:"shipper_pay_qrcode_url"` //配送端支付二维码地址
	AgentPayAppId	      string  `json:"agent_pay_app_id"`  //代理支付小程序appid wxe2fefdde08e515a6
	PayQRCodeUrl    string `json:"pay_qrcode_url"` //二维码支付主域名
	
	
}

type PushConfig struct {
	Shipper  JiguangConfig `json:"shipper"`  // 配送端
	Merchant JiguangConfig `json:"merchant"` // 商家端
	User     JiguangConfig `json:"user"`     // 用户端
}

type JiguangConfig struct {
	AppKey    string `json:"app_key"`
	AppSecret string `json:"app_secret"`
}

type Config map[string]Environment
type AppConfig struct {
	Environment string `json:"environment"`
	Config      Config `json:"config"`
}

type InsuranceConfig struct {
	NameUg string `json:"name_ug"`
	NameZh string `json:"name_zh"`
	Amount int    `json:"amount"`
}

type foodPercent struct {
	MPDealerMin float64 `json:"mp_dealer_min"`
	MPDealerMax float64 `json:"mp_dealer_max"`
}
type Food struct {
	Percent     foodPercent `json:"percent"`
	SelfPercent foodPercent `json:"self_percent"`
}

func LoadConfig() Environment {

	configPath := inits.ConfigFilePath + "."
	content, err := ioutil.ReadFile(configPath + "/config.json")
	if err != nil {
		panic(err)
	}
	var config AppConfig
	json.Unmarshal(content, &config)
	CurrentEnvironment = config.Environment
	cfg := config.Config[config.Environment]
	//有默认值的参数填充
	if cfg.MaxCashoutAmount == 0 {
		cfg.MaxCashoutAmount = 50000 //一次性提现金额
	}
	if cfg.DayCashoutLimit == 0 {
		cfg.DayCashoutLimit = 2000000 //商户每天累计 可提现金额
	}
	if cfg.FfmpegMode == 0 {
		cfg.FfmpegMode = 2
	}
	if len(cfg.LakalaConfig.WarningAdminNumber) == 0 { //拉卡拉 默认 提醒手机号
		cfg.LakalaConfig.WarningAdminNumber = "18129290467"
	}
	if cfg.LakalaConfig.SystemMinReserveAmount == 0 { //拉卡拉 默认 提现 最储备金额
		cfg.LakalaConfig.SystemMinReserveAmount = 100000 //1000 元
	}
	if len(cfg.LakalaConfig.LakalaOrderPrefix) == 0 { //拉卡拉 默认 订单号前缀
		cfg.LakalaConfig.LakalaOrderPrefix = "LAKALA_" //
	}
	if len(cfg.LakalaConfig.ShipperPayQRCodeUrl) == 0 { //拉卡拉 配送端支付二维码地址
		cfg.LakalaConfig.ShipperPayQRCodeUrl = "https://qr.mulazim.com/shipper-pay?id="
	}
	if len(cfg.LakalaConfig.PayQRCodeUrl) == 0 { //拉卡拉 配送端支付二维码地址
		cfg.LakalaConfig.PayQRCodeUrl = "https://qr.mulazim.com"
	}
	if cfg.CashOutStartTime == 0 {
		//提现开始时间
		cfg.CashOutStartTime = 7
	}
	if cfg.CashOutEndTime == 0 {
		//提现结束时间
		cfg.CashOutEndTime = 23
	}
	if len(cfg.CashOutNotificationUg) == 0 {
		cfg.CashOutNotificationUg = "پۇل چىقىرىش ۋاقتى توشتى، پۇل چىقىرىش ۋاقتى سەھەر سائەت توققۇزدا باشلىنىدۇ."
	}
	if len(cfg.CashOutNotificationZh) == 0 {
		cfg.CashOutNotificationZh = "提现时间过了，提现时间明天早上9点开始。"
	}
	if len(cfg.ShipperAppJumpWechatMiniOriginalId) == 0 { //
		cfg.ShipperAppJumpWechatMiniOriginalId = "gh_4c15504e80b3"
	}
	if len(cfg.ShipperAppJumpWechatMiniPage) == 0 {
		cfg.ShipperAppJumpWechatMiniPage = "pages/miniPay/index"
	}
	if cfg.ShipperGrabOrderLimit == 0 {
		cfg.ShipperGrabOrderLimit = 1 //配送员抢单一个订单的时间间隔 1 秒  1秒可以发送一次请求  超过了会提示 xx秒后重试
	}

	if len(cfg.ShipperAppVersion) == 0 { //配送端 最新版本 2.99
		cfg.ShipperAppVersion = "2.99"
	}
	if len(cfg.MerchantAppVersion) == 0 { //商家端 最新版本 2.2.7
		cfg.MerchantAppVersion = "2.2.7"
	}
	if cfg.ShipperArriveAtShopDistance == 0 {
		cfg.ShipperArriveAtShopDistance = 50 //配送员到达店铺的默认距离
	}
	if cfg.ChatMsgCacheTime == 0 {
		cfg.ChatMsgCacheTime = 60 * 60 * 24 * 14 //14天
	}
	if len(cfg.ChatSocketServer) == 0 {
		cfg.ChatSocketServer = "http://chat-socket.test.almas.biz:8002/send-message"
	}
	if cfg.ShipperIntroduceValidDays == 0 {
		cfg.ShipperIntroduceValidDays = 90 //客户拓展有效期 90天
	}
	if cfg.AdvertMaterialTakeCountMax == 0 {
		cfg.AdvertMaterialTakeCountMax = 200 //广告素材领取数量限制
	}
	if cfg.MerchantPushMinVersion == 0 {
		cfg.MerchantPushMinVersion = 242 //商家端开始推送的最小版本
	}
	if cfg.ShipperPushMinVersion == 0 {
		cfg.ShipperPushMinVersion = 317 //配送端开始推送的最小版本
	}
	if cfg.JWTTokenKey == "" {
		cfg.JWTTokenKey = "5TOcK[6x63C}xhb[llDH1N]gFl]E1JLn"
	}
	if cfg.InsuranceConfig.Amount == 0 {
		cfg.InsuranceConfig.NameUg="中国平安"
		cfg.InsuranceConfig.NameZh="中国平安"
		cfg.InsuranceConfig.Amount=300
	}
	if len(cfg.InsuranceStopTime) == 0 { //保险停止购买时间 
		cfg.InsuranceStopTime = "23:00:00"
	}

	if cfg.Food.Percent.MPDealerMin == 0 {
		cfg.Food.Percent.MPDealerMin = 7.2
		cfg.Food.Percent.MPDealerMax = 50
	}

	if cfg.Food.SelfPercent.MPDealerMin == 0 {
		cfg.Food.SelfPercent.MPDealerMin = 7.2
		cfg.Food.SelfPercent.MPDealerMax = 50
	}

	if len(cfg.ShipperAppJumpEnv) == 0 {
		cfg.ShipperAppJumpEnv = "release"
	}
	if cfg.StoreMultiDiscountCount == 0 { //多件优惠数量
		cfg.StoreMultiDiscountCount = 3
	}

	return cfg
}

var MyApp Environment
var CurrentEnvironment string
var AsiaShanghai string

func IsProduction() bool {
	return CurrentEnvironment == "production"
}
func init() {
	MyApp = LoadConfig()
	AsiaShanghai = "Asia/Shanghai"
}
