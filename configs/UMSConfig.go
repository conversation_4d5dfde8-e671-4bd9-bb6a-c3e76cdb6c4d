package configs

import (
	"encoding/json"
	"io/ioutil"
	"mulazim-api/inits"
)

type umsConfig struct {
	AccesserID string `json:"accesser_id"`
	Key        string `json:"key"`
	RequestSeq string `json:"request_seq"`
	Service    string `json:"service"`
	Services   struct {
		PicUploadService            string `json:"pic_upload_service"`
		ComplexUploadService        string `json:"complex_upload_service"`
		AgreementSignService        string `json:"agreement_sign_service"`
		ApplyQryService             string `json:"apply_qry_service"`
		DataDownloadService         string `json:"data_download_service"`
		BranchBankListService       string `json:"branch_bank_list_service"`
		CompanyAccountVerifyService string `json:"company_account_verify_service"`
		RequestAccountVerifyService string `json:"request_account_verify_service"`
		ComplexAlterAcctinfoService string `json:"complex_alter_acctinfo_service"`
		AlterSignService            string `json:"alter_sign_service"`
		AlterQryService             string `json:"alter_qry_service"`
		MerchantRegService          string `json:"merchant_reg_service"`
		ComplexAlterAcctInfo        string `json:"complex_alter_acctinfo"`
	} `json:"services"`
	SignType string `json:"sign_type"`
	Urls     struct {
		H5PageURL    string `json:"h5_page_url"`
		InterfaceURL string `json:"interface_url"`
		PcPageURL    string `json:"pc_page_url"`
	} `json:"urls"`
}

var UmsConfig umsConfig

func init() {
	configPath := inits.ConfigFilePath + "./configs"
	ums, err := ioutil.ReadFile(configPath+"/umsConfig.json")
	if err != nil {
		panic(err.Error())
	}
	json.Unmarshal(ums, &UmsConfig)
}
