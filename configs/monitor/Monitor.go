package monitor

import (
	"encoding/json"
	"io/ioutil"
	"mulazim-api/inits"
	"strings"
)

type MonitoredUrlsType struct {
	Urls []string `json:"urls"`
}
func LoadConfig() MonitoredUrlsType {

	configPath := strings.TrimRight(inits.ConfigFilePath, "/")
	content, err := ioutil.ReadFile(configPath + "/configs/monitor/monitorConfig.json")
	if err != nil {
		panic(err)
	}
	var monitoredUrls MonitoredUrlsType
	json.Unmarshal(content, &monitoredUrls)
	return monitoredUrls
}

var MonitoredUrls MonitoredUrlsType

func init() {
	MonitoredUrls = LoadConfig()
}
