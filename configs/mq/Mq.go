package mq

import (
	"encoding/json"
	"io/ioutil"
	"mulazim-api/configs"
	"mulazim-api/inits"
	"strings"
)

type Config map[string]MqConfig
type MqConfigFile struct {
	Config Config `json:"config"`
}
type MqConfig struct {
	Endpoint   string `json:"endpoint"`
	AccessKey  string `json:"accessKey"`
	SecretKey  string `json:"secretKey"`
	InstanceID string `json:"instanceId"`
}

var MqConfigOpt MqConfig

func init() {
	MqConfigOpt = LoadMqConfig()
}
func LoadMqConfig() MqConfig {
	configPath := strings.TrimRight(inits.ConfigFilePath, "/")
	content, err := ioutil.ReadFile(configPath + "/configs/mq/mq.json")
	if err != nil {
		panic(err)
	}
	var mqConfigFile MqConfigFile
	json.Unmarshal(content, &mqConfigFile)
	return mqConfigFile.Config[configs.CurrentEnvironment]
}
