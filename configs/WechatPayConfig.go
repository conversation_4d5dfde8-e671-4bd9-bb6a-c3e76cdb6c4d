/*
 * Copyright (c) 2021.
 */

package configs

import (
	"encoding/json"
	"io/ioutil"
	"mulazim-api/inits"

	// "path"
	// "runtime"
)

type WechatPayConfig struct {
	AppID           string `json:"app_id"`
	AppSecret       string `json:"app_secret"`
	MchID           string `json:"mch_id"`
	MchApiV3Key     string `json:"mch_api_v3_key"`
	MchCertSerialID string `json:"mch_cert_serial_id"`
	NotifyURL       string `json:"notify_url"`
	KeyPath         string `json:"key_path"`
	CertPath        string `json:"cert_path"`
}

func NewWechatPayConfig(configFileName string) *WechatPayConfig {
	tmpPayConfig := loadWechatPayConfigFile(configFileName)
	wechatPayConfig := WechatPayConfig{
		AppID:           tmpPayConfig.AppID,
		AppSecret:       tmpPayConfig.AppSecret,
		MchID:           tmpPayConfig.MchID,
		MchApiV3Key:     tmpPayConfig.MchApiV3Key,
		MchCertSerialID: tmpPayConfig.MchCertSerialID,
		NotifyURL:       tmpPayConfig.NotifyURL,
		KeyPath:         tmpPayConfig.KeyPath,
		CertPath:        tmpPayConfig.CertPath,
	}
	return &wechatPayConfig
}

type PayConfig map[string]WechatPayConfig

type WechatPayConfigAll struct {
	Config PayConfig `json:"config"`
}

func loadWechatPayConfigFile(configFileName string) WechatPayConfig {
	configPath := inits.ConfigFilePath+ "./configs"
	content, err := ioutil.ReadFile(configPath+"/wechat/" + configFileName + ".json")
	if err != nil {
		panic(err)
	}
	var wechatPayConfigAll WechatPayConfigAll
	json.Unmarshal(content, &wechatPayConfigAll)
	return wechatPayConfigAll.Config[CurrentEnvironment]
}
