{"config": {"captain": {"app_id": "wx100faa1c30afd37b", "app_secret": "", "mch_id": "1384352302", "mch_api_v3_key": "65ec949d6752b6a8d68441f0c3a1f0a4", "mch_cert_serial_id": "2B51C9AD9DEB36C57E7EBBB276489C929C26E1CD", "notify_url": "http://api.d.almas.biz/ug/shipper/v2/v3-pay-notify", "key_path": "./configs/certs/wechat_old/1384352302_20211208_cert/apiclient_key.pem", "cert_path": "./configs/certs/wechat_old/1384352302_20211208_cert/apiclient_cert.pem"}, "dev": {"app_id": "wx100faa1c30afd37b", "app_secret": "", "mch_id": "1384352302", "mch_api_v3_key": "65ec949d6752b6a8d68441f0c3a1f0a4", "mch_cert_serial_id": "2B51C9AD9DEB36C57E7EBBB276489C929C26E1CD", "notify_url": "http://api.d.almas.biz/ug/shipper/v2/v3-pay-notify", "key_path": "./configs/certs/wechat_old/1384352302_20211208_cert/apiclient_key.pem", "cert_path": "./configs/certs/wechat_old/1384352302_20211208_cert/apiclient_cert.pem"}, "production": {"app_id": "wx100faa1c30afd37b", "app_secret": "", "mch_id": "1232338602", "mch_api_v3_key": "e7b76a3cda5d2fdce49e77e948d266f9", "mch_cert_serial_id": "452A05807605D8F267FE2125D8FB54D2CE2211C5", "notify_url": "http://api.mulazim.com/ug/shipper/v2/v3-pay-notify", "key_path": "./configs/certs/wechat_old/1232338602_cert/apiclient_key.pem", "cert_path": "./configs/certs/wechat_old/1232338602_cert/apiclient_cert.pem"}}}